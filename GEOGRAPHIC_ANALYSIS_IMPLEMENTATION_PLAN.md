# Geographic Analysis Implementation Plan

## Current Situation
- Structure model has `gps_lat` and `gps_lon` fields but no `province` field
- Service analytics needs geographic grouping for cost analysis
- Need efficient, accurate way to determine province/state from GPS coordinates

## Proposed Solutions

### Option 1: Reverse Geocoding with Smart Caching ⭐ **RECOMMENDED**

#### Overview
Use reverse geocoding to determine province/state from GPS coordinates with intelligent caching to minimize API calls and costs.

#### Implementation Details

**1. Database Schema Changes**
```sql
-- Add new fields to Structure table
ALTER TABLE structures ADD COLUMN province_id INTEGER;
ALTER TABLE structures ADD COLUMN province_name VARCHAR(100);
ALTER TABLE structures ADD COLUMN state_code VARCHAR(10);
ALTER TABLE structures ADD COLUMN geocoding_status VARCHAR(20) DEFAULT 'pending';
ALTER TABLE structures ADD COLUMN geocoding_updated_at TIMESTAMP;

-- Create province lookup table for caching
CREATE TABLE provinces (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    state_code VARCHAR(10),
    country_code VARCHAR(3),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create geocoding cache table
CREATE TABLE geocoding_cache (
    id SERIAL PRIMARY KEY,
    lat_rounded DECIMAL(8,5), -- Rounded to ~100m precision
    lon_rounded DECIMAL(8,5),
    province_id INTEGER REFERENCES provinces(id),
    country_code VARCHAR(3),
    locality VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(lat_rounded, lon_rounded)
);
```

**2. Core Geocoding Service**
```python
# app/services/geocoding.py
from typing import Optional, Tuple
import httpx
from decimal import Decimal
from app.db.database import get_ijack_db

class GeocodingService:
    def __init__(self):
        self.cache_precision = 3  # Round to ~100m precision for caching
        
    def round_coordinates(self, lat: float, lon: float) -> Tuple[Decimal, Decimal]:
        """Round coordinates for cache lookup"""
        return (
            round(Decimal(str(lat)), self.cache_precision),
            round(Decimal(str(lon)), self.cache_precision)
        )
    
    async def get_province_from_coordinates(
        self, lat: float, lon: float
    ) -> Optional[dict]:
        """Get province info from coordinates with caching"""
        
        # Check cache first
        lat_rounded, lon_rounded = self.round_coordinates(lat, lon)
        cached_result = await self._get_from_cache(lat_rounded, lon_rounded)
        if cached_result:
            return cached_result
            
        # Use Nominatim (free OpenStreetMap service)
        result = await self._reverse_geocode_nominatim(lat, lon)
        if result:
            await self._save_to_cache(lat_rounded, lon_rounded, result)
            
        return result
    
    async def _reverse_geocode_nominatim(self, lat: float, lon: float) -> Optional[dict]:
        """Use Nominatim for reverse geocoding"""
        url = f"https://nominatim.openstreetmap.org/reverse"
        params = {
            "lat": lat,
            "lon": lon,
            "format": "json",
            "addressdetails": 1,
            "zoom": 10  # State/province level
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, params=params, timeout=5.0)
                if response.status_code == 200:
                    data = response.json()
                    return self._extract_province_info(data)
            except Exception as e:
                print(f"Geocoding error: {e}")
                
        return None
    
    def _extract_province_info(self, nominatim_data: dict) -> dict:
        """Extract province/state info from Nominatim response"""
        address = nominatim_data.get("address", {})
        
        return {
            "province_name": (
                address.get("state") or 
                address.get("province") or
                address.get("region") or
                "Unknown"
            ),
            "state_code": address.get("ISO3166-2-lvl4", "").split("-")[-1] if "ISO3166-2-lvl4" in address else None,
            "country_code": address.get("country_code", "").upper(),
            "locality": address.get("city") or address.get("town") or address.get("village")
        }
```

**3. Background Job for Batch Processing**
```python
# app/tasks/geocoding_tasks.py
from app.services.geocoding import GeocodingService
import asyncio

async def update_missing_provinces():
    """Background task to populate missing province data"""
    geocoding_service = GeocodingService()
    
    # Get structures without province data
    query = """
        SELECT id, gps_lat, gps_lon 
        FROM structures 
        WHERE province_id IS NULL 
        AND gps_lat IS NOT NULL 
        AND gps_lon IS NOT NULL
        AND geocoding_status != 'failed'
        LIMIT 100
    """
    
    async for db in get_ijack_db():
        structures = await db.execute(text(query))
        
        for structure in structures:
            try:
                province_info = await geocoding_service.get_province_from_coordinates(
                    structure.gps_lat, structure.gps_lon
                )
                
                if province_info:
                    # Update structure with province info
                    await update_structure_province(db, structure.id, province_info)
                else:
                    # Mark as failed to avoid retrying
                    await mark_geocoding_failed(db, structure.id)
                    
                # Rate limiting - be nice to free services
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"Error processing structure {structure.id}: {e}")
```

**4. Fallback Clustering by Major Cities**
```python
# app/services/city_clustering.py
MAJOR_CITIES = {
    "CA": [  # Canada
        {"name": "Calgary", "province": "Alberta", "lat": 51.0447, "lon": -114.0719},
        {"name": "Edmonton", "province": "Alberta", "lat": 53.5461, "lon": -113.4938},
        {"name": "Vancouver", "province": "British Columbia", "lat": 49.2827, "lon": -123.1207},
        {"name": "Toronto", "province": "Ontario", "lat": 43.6532, "lon": -79.3832},
        # Add more cities as needed
    ],
    "US": [  # United States
        {"name": "Houston", "province": "Texas", "lat": 29.7604, "lon": -95.3698},
        {"name": "Denver", "province": "Colorado", "lat": 39.7392, "lon": -104.9903},
        # Add more cities as needed
    ]
}

def find_nearest_city(lat: float, lon: float, country_code: str = "CA") -> Optional[dict]:
    """Find nearest major city as fallback for province determination"""
    cities = MAJOR_CITIES.get(country_code, [])
    
    min_distance = float('inf')
    nearest_city = None
    
    for city in cities:
        distance = calculate_distance(lat, lon, city["lat"], city["lon"])
        if distance < min_distance:
            min_distance = distance
            nearest_city = city
    
    return nearest_city

def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate distance between two points using Haversine formula"""
    from math import radians, cos, sin, asin, sqrt
    
    # Convert to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371  # Radius of earth in kilometers
    
    return c * r
```

**5. Updated Geographic Analysis Query**
```python
# Enhanced geographic analysis using province data
def get_geographic_analysis_enhanced():
    query = select(
        Province.name.label("region_name"),
        Structure.country.label("country_name"),
        func.count(WorkOrder.id).label("total_services"),
        func.avg(WorkOrderPart.cost_before_tax * Currency.fx_rate_cad_per).label("avg_cost_per_service"),
        func.count(func.distinct(Structure.id)).label("unique_structures"),
        func.avg(Structure.gps_lat).label("avg_latitude"),
        func.avg(Structure.gps_lon).label("avg_longitude")
    ).select_from(WorkOrder
    ).join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id
    ).join(Structure, WorkOrderPart.structure_id == Structure.id
    ).outerjoin(Province, Structure.province_id == Province.id
    ).group_by(Province.name, Structure.country
    ).having(func.count(WorkOrder.id) >= 3)
```

#### Advantages
- ✅ Accurate province/state determination
- ✅ Intelligent caching reduces API costs
- ✅ Graceful fallback to city clustering
- ✅ Handles international locations
- ✅ Background processing doesn't block user queries

#### Implementation Timeline
- **Week 1**: Database schema changes and core geocoding service
- **Week 2**: Background job implementation and testing
- **Week 3**: Enhanced geographic analysis queries
- **Week 4**: Testing and optimization

---

### Option 2: Pure Geographic Clustering by Distance

#### Overview
Group structures by geographic proximity without relying on administrative boundaries.

#### Implementation
```python
# app/services/geographic_clustering.py
from sklearn.cluster import DBSCAN
import numpy as np

def cluster_structures_geographically(structures, max_distance_km=50):
    """Cluster structures by geographic proximity"""
    
    # Prepare coordinates
    coords = np.array([[s.gps_lat, s.gps_lon] for s in structures])
    
    # Convert km to approximate degrees (rough approximation)
    eps_degrees = max_distance_km / 111.0  # 1 degree ≈ 111 km
    
    # Apply DBSCAN clustering
    clustering = DBSCAN(eps=eps_degrees, min_samples=2).fit(coords)
    
    # Group structures by cluster
    clusters = {}
    for i, cluster_id in enumerate(clustering.labels_):
        if cluster_id not in clusters:
            clusters[cluster_id] = []
        clusters[cluster_id].append(structures[i])
    
    return clusters

def get_cluster_center_name(cluster_structures):
    """Generate a meaningful name for a geographic cluster"""
    # Use nearest major city or geographic description
    avg_lat = sum(s.gps_lat for s in cluster_structures) / len(cluster_structures)
    avg_lon = sum(s.gps_lon for s in cluster_structures) / len(cluster_structures)
    
    nearest_city = find_nearest_city(avg_lat, avg_lon)
    if nearest_city:
        return f"Near {nearest_city['name']}"
    else:
        return f"Region ({avg_lat:.2f}, {avg_lon:.2f})"
```

#### Advantages
- ✅ No external API dependencies
- ✅ Flexible clustering based on actual service density
- ✅ Works anywhere in the world

#### Disadvantages
- ❌ Less intuitive than province/state names
- ❌ Clusters may not align with business territories

---

### Option 3: Hybrid Approach ⭐ **ULTIMATE SOLUTION**

#### Overview
Combine reverse geocoding with clustering as a comprehensive solution.

#### Implementation Strategy
1. **Primary**: Use cached reverse geocoding for province/state
2. **Secondary**: Fall back to nearest major city
3. **Tertiary**: Use geographic clustering for detailed analysis
4. **Always**: Provide raw coordinates for custom analysis

```python
# app/services/hybrid_geographic.py
class HybridGeographicService:
    def __init__(self):
        self.geocoding_service = GeocodingService()
        
    async def get_geographic_info(self, lat: float, lon: float) -> dict:
        """Get comprehensive geographic information"""
        
        # Try reverse geocoding first
        province_info = await self.geocoding_service.get_province_from_coordinates(lat, lon)
        
        # Fall back to city clustering
        if not province_info:
            nearest_city = find_nearest_city(lat, lon)
            province_info = {
                "province_name": f"Near {nearest_city['name']}" if nearest_city else "Unknown",
                "state_code": None,
                "country_code": "CA",  # Default assumption
                "locality": nearest_city['name'] if nearest_city else None
            }
        
        return {
            "province_name": province_info["province_name"],
            "state_code": province_info["state_code"],
            "country_code": province_info["country_code"],
            "locality": province_info["locality"],
            "coordinates": {"lat": lat, "lon": lon},
            "data_source": "geocoding" if province_info else "clustering"
        }
        
    def get_flexible_grouping_options(self, structures):
        """Provide multiple grouping options for analysis"""
        return {
            "by_province": self.group_by_province(structures),
            "by_major_city": self.group_by_nearest_city(structures),
            "by_geographic_cluster": self.group_by_distance_cluster(structures),
            "by_custom_regions": self.group_by_custom_regions(structures)
        }
```

## Implementation Recommendation

**Start with Option 1 (Reverse Geocoding with Caching)** as it provides the most business-relevant geographic groupings while being cost-effective through intelligent caching.

### Phase 1: Core Implementation
1. Add province fields to Structure model
2. Implement geocoding service with Nominatim
3. Create background job for populating existing data
4. Update geographic analysis queries

### Phase 2: Enhanced Features
1. Add city clustering as fallback
2. Implement custom region definitions
3. Add geographic performance analytics
4. Create management interface for geocoding status

### Phase 3: Advanced Analytics
1. Distance-based service efficiency analysis
2. Geographic cost trend prediction
3. Territory optimization recommendations
4. Travel time and cost analysis

## Cost and Maintenance Considerations

- **Nominatim**: Free but rate-limited (1 req/sec)
- **Google Geocoding**: Paid but more accurate and faster
- **Caching**: Reduces ongoing costs to near zero
- **Background processing**: Doesn't impact user experience
- **Maintenance**: Minimal once implemented

This approach provides accurate, cost-effective geographic analysis while maintaining flexibility for future enhancements.