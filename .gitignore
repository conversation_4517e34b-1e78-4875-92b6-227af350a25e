# .gitignore for Python projects
.DS_Store
*.pyc
.env
# env-configmap.yaml
env-configmap.yaml
# secret.yaml
secret*
tls*
*/.venv
.venv_wsl
# instance/
dist/
example/
# Appears after copying files from Windows to WSL sometimes
*.Identifier
certs/

# Certificates
certs/
cert.pem
key.pem
acme.json
!local_certs/*
local_certs/localCA.key

# Next.js
.next/
out/
node_modules/
build/
.pnpm-store/
# frontend/

data/certbot/conf/
# logs/
# reports/

build/
instance/config.py
report.xml
pytest.xml
myijack.log*
celery.log

.webassets-cache/
app/static/dist/
dist/
# _archive/

# flask-static-assets
*-[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f].*

# The machine learning stuff needs Gzip files
# *.gz
cache_manifest.json

auth.toml
.local/
.vscode-server/

test.html
test_response.html
.pypirc
celerybeat-schedule

# ignore personal git config
.gitconfig
# Some Bash scripts need line endings to be LF,
# like "scripts/lint_apply.sh" and "scripts/pytest_run_all.sh",
# and this .gitattributes file is used to enforce that.
# .gitattributes

# Ignore screenshots from tests
*screenshot.png

# ignore cache and logs
_cacache/
_logs/

kompose
*.log
build-log-prod.json
/rcom

!alembic/*
!migrations/*
/postgres-data/
/migrations/versions/
.coverage*
coverage.xml

# Playwright stuff
playwright----*
auth.json
/tests/dash_rcom/videos
/tests/dash_rcom/screenshots
/tests/dash_rcom/html
/tests/dash_rcom/traces
webpack-build-log-dev.json

# HTML prints when errors occur in development
test_error_400.html
test_error_403.html
test_error_404.html
test_error_500.html
test_error_csrf.html

# build artifacts
**/*.egg-info/*

# VS Code settings
mcp.json
.aider*
session_cookies.json
login_success.png
