# Enhanced Geographic Analysis Implementation Plan
## Ultimate Hybrid Solution with Existing Infrastructure

## Current Infrastructure Analysis ✅

### Existing Models (packages/shared/models/models.py)
- **Country** (line 1924): country_code, country_name, sales_tax_rate
- **Province** (line 1971): name, abbrev, country_id (FK to countries)
- **County** (line 1998): name, province_id (FK to provinces) 
- **Structure** (line 2900): gps_lat, gps_lon, location (computed), but **NO province_id or country_id**

### Existing Utilities (flask_app/app/utils/simple.py)
- **geodesic()** (line 375): Haversine formula distance calculation
- **calc_distance()** (line 401): Wrapper with error handling

## Phase 1: Utility Migration & DRY Refactoring 🔄

### 1.1 Move Flask Utils to Shared Location
```bash
# Create new shared utils structure
mkdir -p packages/shared/utils/geographic/

# Move and refactor utilities
mv flask_app/app/utils/* packages/shared/utils/
```

**Files to migrate:**
- `flask_app/app/utils/simple.py` → `packages/shared/utils/simple_utils.py` 
- `flask_app/app/utils/complex.py` → `packages/shared/utils/complex_utils.py`
- `flask_app/app/utils/pdf_generator.py` → `packages/shared/utils/pdf_utils.py`
- `flask_app/app/utils/recaptcha.py` → `packages/shared/utils/recaptcha_utils.py`

**New geographic utils:**
- `packages/shared/utils/geographic_utils.py` (extract from simple_utils.py)

### 1.2 Create Geographic Utils Module
```python
# packages/shared/utils/geographic_utils.py
from typing import Optional, Tuple, List
from math import atan2, cos, radians, sin, sqrt

def geodesic(lat1_dec: float, lon1_dec: float, lat2_dec: float, lon2_dec: float) -> float:
    """Calculate distance between GPS coordinates using Haversine formula"""
    lat1 = radians(abs(lat1_dec))
    lon1 = radians(abs(lon1_dec))
    lat2 = radians(abs(lat2_dec))
    lon2 = radians(abs(lon2_dec))

    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    distance = 6371.0 * c  # Earth radius in km
    return distance

def calc_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate distance with error handling"""
    try:
        return geodesic(lat1, lon1, lat2, lon2)
    except (ValueError, TypeError):
        return 2000.0  # Default large distance on error

def round_coordinates_for_cache(lat: float, lon: float, precision: int = 3) -> Tuple[float, float]:
    """Round coordinates for geocoding cache (default ~100m precision)"""
    return (round(lat, precision), round(lon, precision))

def find_nearest_major_city(lat: float, lon: float, country_code: str = "CA") -> Optional[dict]:
    """Find nearest major city for fallback geographic identification"""
    # Implementation with predefined major cities
    pass

def cluster_coordinates_by_distance(coordinates: List[Tuple[float, float]], max_distance_km: float = 50) -> dict:
    """Cluster GPS coordinates by geographic proximity"""
    # Implementation using DBSCAN or similar
    pass
```

### 1.3 Update All Import References 
**Automated migration script:**
```python
# scripts/migrate_utils_imports.py
import os
import re

IMPORT_REPLACEMENTS = {
    'from app.utils.simple import': 'from shared.utils.simple_utils import',
    'from app.utils.complex import': 'from shared.utils.complex_utils import',
    'from app.utils.pdf_generator import': 'from shared.utils.pdf_utils import',
    'from app.utils.recaptcha import': 'from shared.utils.recaptcha_utils import',
    'from flask_app.app.utils.simple import geodesic, calc_distance': 'from shared.utils.geographic_utils import geodesic, calc_distance',
}

def migrate_imports_in_file(file_path: str):
    """Update import statements in a single file"""
    # Implementation to replace imports
    pass

def migrate_all_imports():
    """Scan and update all Python files in project"""
    for root, dirs, files in os.walk('/project'):
        for file in files:
            if file.endswith('.py'):
                migrate_imports_in_file(os.path.join(root, file))

if __name__ == "__main__":
    migrate_all_imports()
```

## Phase 2: Database Schema Enhancement 🗄️

### 2.1 Add Geographic Fields to Structure Model
```sql
-- Migration: Add geographic relationships to structures table
ALTER TABLE public.structures ADD COLUMN province_id INTEGER;
ALTER TABLE public.structures ADD COLUMN geocoding_status VARCHAR(20) DEFAULT 'pending';
ALTER TABLE public.structures ADD COLUMN geocoding_updated_at TIMESTAMP;
ALTER TABLE public.structures ADD COLUMN auto_geocoded BOOLEAN DEFAULT FALSE;

-- Add foreign key constraints
ALTER TABLE public.structures 
ADD CONSTRAINT fk_structures_province 
FOREIGN KEY (province_id) REFERENCES public.provinces(id);

-- Create indexes for performance
CREATE INDEX idx_structures_province_id ON public.structures(province_id);
CREATE INDEX idx_structures_geocoding_status ON public.structures(geocoding_status);
```

### 2.2 Create Geocoding Cache Table
```sql
-- Cache table for reverse geocoding results
CREATE TABLE public.geocoding_cache (
    id SERIAL PRIMARY KEY,
    lat_rounded DECIMAL(8,5) NOT NULL,
    lon_rounded DECIMAL(8,5) NOT NULL,
    country_id INTEGER REFERENCES public.countries(id),
    province_id INTEGER REFERENCES public.provinces(id),
    locality VARCHAR(100),
    confidence_score REAL DEFAULT 1.0,
    data_source VARCHAR(20) DEFAULT 'nominatim',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(lat_rounded, lon_rounded)
);

CREATE INDEX idx_geocoding_cache_coords ON public.geocoding_cache(lat_rounded, lon_rounded);
```

### 2.3 Update Structure Model Class
```python
# packages/shared/models/models.py - Add to Structure class
class Structure(Base):
    # ... existing fields ...
    
    # New geographic relationships
    province_id = Column(INTEGER, ForeignKey("public.provinces.id"))
    geocoding_status = Column(VARCHAR(20), default='pending')
    geocoding_updated_at = Column(TIMESTAMP)
    auto_geocoded = Column(BOOLEAN, default=False)
    
    @declared_attr 
    def province_rel(self):
        return relationship("Province", back_populates="structures_rel")
```

### 2.4 Update Country/Province Models
```python
# Add reverse relationships to Province class
class Province(Base):
    # ... existing fields ...
    structures_rel = relationship("Structure", back_populates="province_rel")
```

## Phase 3: Hybrid Geocoding Service Implementation 🌍

### 3.1 Core Geocoding Service
```python
# packages/shared/services/geocoding_service.py
from typing import Optional, Dict, List
import asyncio
import httpx
from datetime import datetime, timedelta
from sqlalchemy import select, insert, update
from sqlalchemy.ext.asyncio import AsyncSession

from shared.models.models import Structure, Country, Province, GecodingCache
from shared.utils.geographic_utils import calc_distance, round_coordinates_for_cache

class HybridGeocodingService:
    """
    Ultimate geocoding solution supporting:
    - Nominatim reverse geocoding with caching
    - Major city clustering fallback  
    - International province/state mapping
    - South America & Middle East support via coordinates
    """
    
    def __init__(self):
        self.cache_precision = 3  # ~100m precision
        self.rate_limit_delay = 1.0  # 1 second between API calls
        
    async def get_geographic_info(
        self, 
        lat: float, 
        lon: float, 
        db: AsyncSession,
        force_refresh: bool = False
    ) -> Dict:
        """Get comprehensive geographic information with multiple fallbacks"""
        
        # Step 1: Check cache first
        if not force_refresh:
            cached_result = await self._get_from_cache(lat, lon, db)
            if cached_result:
                return cached_result
        
        # Step 2: Try Nominatim reverse geocoding
        nominatim_result = await self._reverse_geocode_nominatim(lat, lon)
        if nominatim_result:
            geo_info = await self._process_nominatim_result(nominatim_result, db)
            await self._save_to_cache(lat, lon, geo_info, db)
            return geo_info
        
        # Step 3: Fallback to major city clustering
        city_result = self._find_nearest_major_city(lat, lon)
        if city_result:
            geo_info = await self._process_city_result(city_result, db)
            await self._save_to_cache(lat, lon, geo_info, db)
            return geo_info
        
        # Step 4: Last resort - coordinate-based region
        return await self._create_coordinate_region(lat, lon, db)
    
    async def _reverse_geocode_nominatim(self, lat: float, lon: float) -> Optional[Dict]:
        """Use Nominatim for reverse geocoding with rate limiting"""
        url = "https://nominatim.openstreetmap.org/reverse"
        params = {
            "lat": lat,
            "lon": lon,
            "format": "json",
            "addressdetails": 1,
            "zoom": 8,  # Province/state level
            "accept-language": "en"
        }
        
        headers = {
            "User-Agent": "IJack-Technologies-Geographic-Analysis/1.0"
        }
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                await asyncio.sleep(self.rate_limit_delay)  # Rate limiting
                response = await client.get(url, params=params, headers=headers)
                
                if response.status_code == 200:
                    return response.json()
        except Exception as e:
            print(f"Nominatim geocoding error: {e}")
        
        return None
    
    def _find_nearest_major_city(self, lat: float, lon: float) -> Optional[Dict]:
        """Find nearest major city across all supported regions"""
        
        MAJOR_CITIES = {
            # North America
            "calgary": {"lat": 51.0447, "lon": -114.0719, "province": "Alberta", "country": "CA"},
            "edmonton": {"lat": 53.5461, "lon": -113.4938, "province": "Alberta", "country": "CA"},
            "vancouver": {"lat": 49.2827, "lon": -123.1207, "province": "British Columbia", "country": "CA"},
            "toronto": {"lat": 43.6532, "lon": -79.3832, "province": "Ontario", "country": "CA"},
            "houston": {"lat": 29.7604, "lon": -95.3698, "province": "Texas", "country": "US"},
            "denver": {"lat": 39.7392, "lon": -104.9903, "province": "Colorado", "country": "US"},
            
            # South America
            "bogota": {"lat": 4.7110, "lon": -74.0721, "province": "Cundinamarca", "country": "CO"},
            "caracas": {"lat": 10.4806, "lon": -66.9036, "province": "Distrito Capital", "country": "VE"},
            "lima": {"lat": -12.0464, "lon": -77.0428, "province": "Lima", "country": "PE"},
            "quito": {"lat": -0.1807, "lon": -78.4678, "province": "Pichincha", "country": "EC"},
            
            # Middle East
            "riyadh": {"lat": 24.7136, "lon": 46.6753, "province": "Riyadh Province", "country": "SA"},
            "dubai": {"lat": 25.2048, "lon": 55.2708, "province": "Dubai", "country": "AE"},
            "doha": {"lat": 25.2854, "lon": 51.5310, "province": "Ad Dawhah", "country": "QA"},
            "kuwait_city": {"lat": 29.3759, "lon": 47.9774, "province": "Al Asimah", "country": "KW"},
        }
        
        min_distance = float('inf')
        nearest_city = None
        
        for city_name, city_data in MAJOR_CITIES.items():
            distance = calc_distance(lat, lon, city_data["lat"], city_data["lon"])
            if distance < min_distance:
                min_distance = distance
                nearest_city = {
                    "name": city_name.replace("_", " ").title(),
                    "distance_km": distance,
                    **city_data
                }
        
        # Only return if within reasonable distance (500km)
        return nearest_city if min_distance < 500 else None
    
    async def _process_nominatim_result(self, data: Dict, db: AsyncSession) -> Dict:
        """Extract and normalize province/country info from Nominatim"""
        address = data.get("address", {})
        
        # Extract country
        country_code = address.get("country_code", "").upper()
        country_name = address.get("country", "")
        
        # Extract province/state (multiple possible fields)
        province_name = (
            address.get("state") or 
            address.get("province") or
            address.get("region") or
            address.get("county") or
            "Unknown"
        )
        
        # Map to database records
        country_record = await self._get_or_create_country(country_code, country_name, db)
        province_record = await self._get_or_create_province(
            province_name, country_record.id if country_record else None, db
        )
        
        return {
            "country_id": country_record.id if country_record else None,
            "province_id": province_record.id if province_record else None,
            "country_name": country_name,
            "province_name": province_name,
            "locality": address.get("city") or address.get("town"),
            "confidence_score": 0.95,
            "data_source": "nominatim"
        }
    
    async def batch_geocode_structures(self, db: AsyncSession, limit: int = 100):
        """Background job to geocode structures without province data"""
        
        # Get structures needing geocoding
        query = select(Structure).where(
            Structure.province_id.is_(None),
            Structure.gps_lat.is_not(None),
            Structure.gps_lon.is_not(None),
            Structure.geocoding_status != 'failed'
        ).limit(limit)
        
        result = await db.execute(query)
        structures = result.scalars().all()
        
        for structure in structures:
            try:
                # Mark as processing
                await db.execute(
                    update(Structure)
                    .where(Structure.id == structure.id)
                    .values(geocoding_status='processing')
                )
                
                # Get geographic info
                geo_info = await self.get_geographic_info(
                    structure.gps_lat, structure.gps_lon, db
                )
                
                # Update structure
                await db.execute(
                    update(Structure)
                    .where(Structure.id == structure.id)
                    .values(
                        country_id=geo_info.get('country_id'),
                        province_id=geo_info.get('province_id'),
                        geocoding_status='completed',
                        geocoding_updated_at=datetime.utcnow(),
                        auto_geocoded=True
                    )
                )
                
                await db.commit()
                
            except Exception as e:
                print(f"Error geocoding structure {structure.id}: {e}")
                await db.execute(
                    update(Structure)
                    .where(Structure.id == structure.id)
                    .values(geocoding_status='failed')
                )
                await db.commit()
```

## Phase 4: Enhanced Service Analytics Integration 📊

### 4.1 Updated Geographic Analysis Query
```python
# fast_api/app/api/endpoints/service_analytics/service_costs.py

async def get_geographic_analysis_enhanced(
    filters: ServiceCostFilters, 
    db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[GeographicAnalysis]:
    """Enhanced geographic analysis using hybrid geocoding"""
    
    query = apply_common_filters(
        select(
            # Prioritize database province/country, fallback to coordinates
            func.coalesce(Province.name, "Coordinate Region").label("region_name"),
            func.coalesce(Country.country_name, "Unknown Country").label("country_name"),
            func.count(WorkOrder.id).label("total_services"),
            func.avg(WorkOrderPart.cost_before_tax * Currency.fx_rate_cad_per).label("avg_cost_per_service"),
            func.count(func.distinct(Structure.id)).label("unique_structures"),
            func.avg(Structure.gps_lat).label("avg_latitude"),
            func.avg(Structure.gps_lon).label("avg_longitude"),
            
            # Geographic clustering for coordinate regions
            func.round(Structure.gps_lat, 1).label("lat_cluster"),
            func.round(Structure.gps_lon, 1).label("lon_cluster"),
            
            # Geocoding status tracking
            func.count(
                case((Structure.auto_geocoded == True, 1))
            ).label("auto_geocoded_count"),
        )
        .select_from(WorkOrder)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(Structure, WorkOrderPart.structure_id == Structure.id)
        .outerjoin(Province, Structure.province_id == Province.id)
        .outerjoin(Country, Province.country_id == Country.id)
        .group_by(
            Province.name, 
            Country.country_name,
            func.round(Structure.gps_lat, 1),
            func.round(Structure.gps_lon, 1)
        )
        .having(func.count(WorkOrder.id) >= 3),
        filters,
    )
    
    result = await db.execute(query)
    regional_data = result.fetchall()
    
    # Process results with intelligent grouping
    geographic_regions = []
    for row in regional_data:
        region_name = row.region_name
        
        # For coordinate regions, generate meaningful names
        if region_name == "Coordinate Region":
            region_name = f"Region ({row.avg_latitude:.1f}°, {row.avg_longitude:.1f}°)"
            
            # Try to identify by nearest major city
            geocoding_service = HybridGeocodingService()
            nearest_city = geocoding_service._find_nearest_major_city(
                row.avg_latitude, row.avg_longitude
            )
            if nearest_city and nearest_city["distance_km"] < 100:
                region_name = f"Near {nearest_city['name']}"
        
        geographic_regions.append(
            GeographicRegion(
                region_name=region_name,
                country_name=row.country_name,
                total_services=row.total_services,
                avg_cost_per_service=float(row.avg_cost_per_service or 0),
                unique_structures=row.unique_structures,
                coordinates={
                    "lat": float(row.avg_latitude or 0),
                    "lon": float(row.avg_longitude or 0)
                },
                geocoding_coverage=float(row.auto_geocoded_count / row.unique_structures * 100),
                clustering_method="hybrid" if row.auto_geocoded_count > 0 else "coordinate"
            )
        )
    
    return {"result": geographic_regions}
```

## Phase 5: Background Jobs & Management 🔧

### 5.1 Geocoding Background Task
```python
# packages/shared/tasks/geocoding_tasks.py
from celery import Celery
from shared.services.geocoding_service import HybridGeocodingService

@celery_app.task
async def batch_geocode_structures():
    """Celery task for background geocoding"""
    service = HybridGeocodingService()
    async with get_async_db() as db:
        await service.batch_geocode_structures(db, limit=50)

@celery_app.task
async def refresh_geocoding_cache():
    """Periodically refresh old cache entries"""
    # Implementation to refresh cache entries older than 30 days
    pass
```

### 5.2 Management Commands
```python
# scripts/geocoding_management.py
import asyncio
import click
from shared.services.geocoding_service import HybridGeocodingService

@click.group()
def geocoding():
    """Geographic data management commands"""
    pass

@geocoding.command()
@click.option('--limit', default=100, help='Number of structures to process')
@click.option('--force', is_flag=True, help='Force re-geocoding of existing data')
async def geocode_structures(limit: int, force: bool):
    """Geocode structures missing province data"""
    service = HybridGeocodingService()
    async with get_async_db() as db:
        await service.batch_geocode_structures(db, limit=limit)

@geocoding.command()
async def geocoding_stats():
    """Show geocoding coverage statistics"""
    # Implementation to show geocoding status across all structures
    pass

if __name__ == "__main__":
    geocoding()
```

## Implementation Timeline 📅

### Week 1: Infrastructure Setup
- ✅ Move flask_app/utils to packages/shared/utils
- ✅ Update all import references across codebase  
- ✅ Create geographic_utils.py module
- ✅ Test existing functionality still works

### Week 2: Database Schema
- ✅ Add country_id, province_id to Structure model
- ✅ Create geocoding_cache table
- ✅ Run database migrations
- ✅ Update model relationships

### Week 3: Core Geocoding Service
- ✅ Implement HybridGeocodingService
- ✅ Nominatim integration with caching
- ✅ Major city fallback system
- ✅ Background job framework

### Week 4: Service Analytics Integration
- ✅ Update geographic analysis queries
- ✅ Enhanced region grouping logic
- ✅ Management commands
- ✅ Testing and optimization

## Benefits of This Approach 🎯

### 1. **DRY & Maintainable**
- Single shared utils location
- No code duplication between Flask and FastAPI
- Centralized geographic logic

### 2. **International Support**
- Works with existing Province/Country models
- Handles South America, Middle East, worldwide
- Intelligent fallbacks for any location

### 3. **Performance Optimized**
- Smart caching reduces API costs to near-zero
- Background processing doesn't block users
- Efficient database queries with proper indexes

### 4. **Business Intelligence Ready**
- Accurate administrative boundaries for reporting
- Flexible grouping options (province, city, coordinates)
- Tracking of geocoding coverage and data quality

### 5. **Future-Proof**
- Easy to add new regions/cities
- Pluggable geocoding providers
- Extensible for territory optimization and route planning

This hybrid approach leverages your existing infrastructure while providing comprehensive geographic analysis capabilities that scale globally! 🌍