#!/usr/bin/env python3
"""
Automated login script for IJACK development environment.

This script automates the login process:
1. Navigate to localhost:4999/login
2. Enter email: <EMAIL>
3. Click to get verification code
4. Wait for auto-filled code (dev mode)
5. Click "just log in" button
6. Navigate to service dashboard

Requirements:
    pip install playwright
    playwright install chromium

Usage:
    python scripts/auto_login.py
    python scripts/auto_login.py --headless  # Run without browser UI
    python scripts/auto_login.py --url /services/overview  # Navigate to specific page after login
"""

import argparse
import asyncio
import sys

from playwright.async_api import async_playwright


async def auto_login(
    base_url="http://localhost:4999", target_url="/services/overview", headless=True
):
    """
    Automate the login process for IJACK development environment.

    Args:
        base_url: Base URL of the application (default: http://localhost:4999)
        target_url: URL to navigate to after successful login (default: /services/overview)
        headless: Run browser in headless mode (default: True)
    """

    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(
            headless=headless,
            args=[
                "--no-sandbox",
                "--disable-dev-shm-usage",
            ],  # For WSL/Docker compatibility
        )

        try:
            # Create new page
            page = await browser.new_page()

            print(f"🚀 Starting automated login to {base_url}")

            # Navigate to login page
            login_url = f"{base_url}/login"
            print(f"📱 Navigating to {login_url}")
            await page.goto(login_url)

            # Wait for login form to load
            print("⏳ Waiting for login form...")
            await page.wait_for_load_state("networkidle")

            # Find and fill email field
            print("📧 Entering email address...")
            email_field = page.locator(
                'input[type="email"], input[name="email"], input[placeholder*="email" i]'
            )
            await email_field.fill("<EMAIL>")

            # Helper function to close any modals that might appear
            async def close_modals():
                try:
                    # Look for common modal close buttons
                    modal_close_selectors = [
                        'button[aria-label="Close"]',
                        "button.modal-close",
                        '[data-dismiss="modal"]',
                        ".modal-header button",
                        'button:has-text("×")',
                        'button:has-text("Close")',
                        '[role="dialog"] button[aria-label="Close"]',
                    ]

                    for selector in modal_close_selectors:
                        close_buttons = page.locator(selector)
                        count = await close_buttons.count()
                        for i in range(count):
                            try:
                                await close_buttons.nth(i).click(timeout=1000)
                                print(f"🔄 Closed modal using selector: {selector}")
                                await page.wait_for_timeout(500)
                            except Exception:
                                continue

                    # Also try pressing Escape key to close modals
                    await page.keyboard.press("Escape")

                except Exception as e:
                    print(f"⚠️  Modal close attempt: {e}")

            # Find and click the "get code" button
            print("🔐 Clicking 'get verification code' button...")
            get_code_button = page.locator(
                'button:has-text("send"), button:has-text("code"), button:has-text("get"), input[type="submit"]'
            ).first
            await get_code_button.click()

            # Close any modals that might have appeared
            print("🔄 Checking for modals to close...")
            await close_modals()

            # Wait for code field to appear and auto-fill (dev mode)
            print("⏳ Waiting for verification code to auto-fill...")
            await page.wait_for_timeout(3000)  # Give time for dev auto-fill

            # Close modals again before next step
            await close_modals()

            # Click "Verify code" button
            print("✅ Clicking 'Verify code' button...")
            verify_button = page.locator(
                'button:has-text("verify"), button:has-text("Verify code"), button[type="submit"]'
            )
            await verify_button.click()

            # Close any modals that appeared after verify
            print("🔄 Checking for modals after verify...")
            await close_modals()

            # Wait for next step
            await page.wait_for_timeout(2000)

            # Close any final modals before the last step
            print("🔄 Final modal check before sign in...")
            await close_modals()
            await page.wait_for_timeout(1000)

            # Click "Just sign in, no PassKey" button using specific ID
            print("🚪 Clicking 'Just sign in, no PassKey' button...")
            signin_button = page.locator("#sign_in_no_passkey_btn")
            await signin_button.click()

            # Final modal check
            await close_modals()

            # Wait for navigation after login with longer timeout
            print("⏳ Waiting for login to complete...")
            try:
                await page.wait_for_url(lambda url: "/login" not in url, timeout=10000)
                print("✅ URL changed - login appears successful!")
            except Exception:
                print(
                    "⚠️  URL didn't change from login page, checking other indicators..."
                )

            await page.wait_for_load_state("networkidle")

            # Get current state for debugging
            current_url = page.url
            page_title = await page.title()
            print(f"🔍 Current URL: {current_url}")
            print(f"🔍 Page title: {page_title}")

            # Check multiple indicators of successful login
            login_success_indicators = [
                "dashboard",
                "profile",
                "logout",
                "account",
                "welcome",
                "service",
                "sales",
                "navigation",
                "breadcrumb",
            ]

            page_content = await page.content()
            success_indicators_found = []
            for indicator in login_success_indicators:
                if indicator.lower() in page_content.lower():
                    success_indicators_found.append(indicator)

            if success_indicators_found:
                print(f"✅ Success indicators found: {success_indicators_found}")

            # Check if we're successfully logged in
            if "/login" not in current_url or success_indicators_found:
                print("✅ Login successful!")

                # Navigate to target URL if specified
                if target_url and target_url != "/":
                    final_url = f"{base_url}{target_url}"
                    print(f"🎯 Navigating to {final_url}")
                    await page.goto(final_url)
                    await page.wait_for_load_state("networkidle")
                    print(f"📍 Now at: {page.url}")

                # Take success screenshot
                await page.screenshot(path="login_success.png", full_page=True)
                print("📸 Success screenshot saved as login_success.png")

                # Save session cookies for future use
                cookies = await page.context.cookies()
                import json

                with open("session_cookies.json", "w") as f:
                    json.dump(cookies, f, indent=2)
                print("🍪 Session cookies saved to session_cookies.json")

                # Keep browser open for a moment to see the result
                if not headless:
                    print(
                        "🎉 Login completed! Browser will stay open for 30 seconds..."
                    )
                    await page.wait_for_timeout(30000)
                else:
                    print("🎉 Login completed!")

            else:
                print("❌ Login failed - still on login page")
                # Take screenshot for debugging
                await page.screenshot(path="login_failure.png")
                print(f"📸 Screenshot saved as login_failure.png in {page.url}")

                # Try to find any error messages on the page
                error_selectors = [
                    ".error",
                    ".alert-danger",
                    ".text-danger",
                    '[role="alert"]',
                    ".notification.error",
                ]

                for selector in error_selectors:
                    error_elements = page.locator(selector)
                    count = await error_elements.count()
                    for i in range(count):
                        try:
                            error_text = await error_elements.nth(i).text_content()
                            if error_text and error_text.strip():
                                print(f"🚨 Error found: {error_text.strip()}")
                        except Exception:
                            continue

        except Exception as e:
            print(f"💥 Error during login: {str(e)}")
            # Take screenshot for debugging
            await page.screenshot(path="login_error.png")
            print("📸 Error screenshot saved as login_error.png")
            raise

        finally:
            await browser.close()


def main():
    parser = argparse.ArgumentParser(description="Automate login for IJACK development")
    parser.add_argument(
        "--base-url",
        default="http://localhost:4999",
        help="Base URL of the application (default: http://localhost:4999)",
    )
    parser.add_argument(
        "--url",
        default="/services/overview",
        help="URL to navigate to after login (default: /services/overview)",
    )
    parser.add_argument(
        "--show-browser",
        action="store_true",
        help="Show browser UI for debugging (default: run headless)",
    )

    args = parser.parse_args()

    try:
        asyncio.run(
            auto_login(
                base_url=args.base_url,
                target_url=args.url,
                headless=not args.show_browser,
            )
        )
    except KeyboardInterrupt:
        print("\n🛑 Login cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"💥 Failed to complete login: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
