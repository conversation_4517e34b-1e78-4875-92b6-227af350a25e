#!/usr/bin/env python
"""
Management commands for geocoding operations

This script provides CLI commands for managing the geocoding system.
"""

import asyncio
import os
import sys

import click

# Add project root to path
sys.path.insert(0, "/project")

# Import concrete Structure model
from fast_api.app.models.models import Structure
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine

from shared.jobs.geocoding_job_async import AsyncGeocodingJob
from shared.models.models import Country, GeocodingCache, Province


def get_db_url():
    """Get database URL from environment or default."""
    # In development, use environment variables to connect directly to RDS
    # bypassing pgbouncer which doesn't support asyncpg
    from dotenv import load_dotenv

    # Load .env file
    load_dotenv("/project/.env")

    # Build URL from environment variables
    user = os.environ.get("USER_IJ")
    password = os.environ.get("PASS_IJ")
    host = os.environ.get("HOST_IJ")
    port = os.environ.get("PORT_IJ")
    db = os.environ.get("DB_IJ")

    # Direct connection to RDS for psycopg
    url = f"postgresql+psycopg://{user}:{password}@{host}:{port}/{db}"

    return url


@click.group()
def cli():
    """Geocoding management commands."""
    pass


@cli.command()
@click.option("--limit", default=100, help="Number of structures to process")
@click.option("--batch-size", default=50, help="Batch size for processing")
def run(limit, batch_size):
    """Run geocoding job once."""
    click.echo(f"Running geocoding job (limit={limit}, batch_size={batch_size})...")

    async def _run():
        job = AsyncGeocodingJob(db_url=get_db_url(), batch_size=batch_size)
        results = await job.run_job(limit=limit)
        return results

    results = asyncio.run(_run())

    click.echo("\nResults:")
    click.echo(f"  Processed: {results['total_processed']}")
    click.echo(f"  Success: {results['total_success']}")
    click.echo(f"  Errors: {results['total_errors']}")


@cli.command()
def status():
    """Show geocoding status statistics."""

    async def _get_status():
        engine = create_async_engine(get_db_url())

        async with AsyncSession(engine) as session:
            # Total structures with GPS
            total_query = select(func.count(Structure.id)).where(
                Structure.gps_lat.isnot(None), Structure.gps_lon.isnot(None)
            )
            total_result = await session.execute(total_query)
            total_with_gps = total_result.scalar() or 0

            # Structures with province data
            with_province_query = select(func.count(Structure.id)).where(
                Structure.gps_lat.isnot(None),
                Structure.gps_lon.isnot(None),
                Structure.province_id.isnot(None),
            )
            with_province_result = await session.execute(with_province_query)
            with_province = with_province_result.scalar() or 0

            # Status breakdown
            status_query = (
                select(Structure.geocoding_status, func.count(Structure.id))
                .where(Structure.gps_lat.isnot(None), Structure.gps_lon.isnot(None))
                .group_by(Structure.geocoding_status)
            )

            status_result = await session.execute(status_query)
            status_breakdown = {row[0] or "unknown": row[1] for row in status_result}

            # Cache statistics
            cache_count_query = select(func.count(GeocodingCache.id))
            cache_result = await session.execute(cache_count_query)
            cache_count = cache_result.scalar() or 0

            # Countries and provinces covered
            countries_query = (
                select(func.count(func.distinct(Province.country_id)))
                .select_from(Structure)
                .join(Province, Structure.province_id == Province.id)
            )
            countries_result = await session.execute(countries_query)
            countries_count = countries_result.scalar() or 0

            provinces_query = select(
                func.count(func.distinct(Structure.province_id))
            ).where(Structure.province_id.isnot(None))
            provinces_result = await session.execute(provinces_query)
            provinces_count = provinces_result.scalar() or 0

        await engine.dispose()

        return {
            "total_with_gps": total_with_gps,
            "with_province": with_province,
            "without_province": total_with_gps - with_province,
            "coverage_percent": (with_province / total_with_gps * 100)
            if total_with_gps > 0
            else 0,
            "status_breakdown": status_breakdown,
            "cache_entries": cache_count,
            "countries": countries_count,
            "provinces": provinces_count,
        }

    stats = asyncio.run(_get_status())

    click.echo("\nGeocoding Status Report")
    click.echo("=" * 50)
    click.echo(f"Total structures with GPS coordinates: {stats['total_with_gps']:,}")
    click.echo(
        f"Structures with province data: {stats['with_province']:,} ({stats['coverage_percent']:.1f}%)"
    )
    click.echo(f"Structures without province data: {stats['without_province']:,}")
    click.echo("\nGeographic coverage:")
    click.echo(f"  Countries: {stats['countries']}")
    click.echo(f"  Provinces/States: {stats['provinces']}")
    click.echo("\nGeocoding status breakdown:")
    for status, count in sorted(stats["status_breakdown"].items()):
        click.echo(f"  {status}: {count:,}")
    click.echo(f"\nCache entries: {stats['cache_entries']:,}")


@cli.command()
@click.option("--days", default=30, help="Delete cache entries older than N days")
def clean_cache(days):
    """Clean up old geocoding cache entries."""
    click.echo(f"Cleaning cache entries older than {days} days...")

    async def _clean():
        job = AsyncGeocodingJob(db_url=get_db_url())
        await job.cleanup_old_cache(days=days)

    asyncio.run(_clean())
    click.echo("Cache cleanup completed!")


@cli.command()
@click.option("--structure-id", type=int, help="Structure ID to geocode")
def geocode_single(structure_id):
    """Geocode a single structure by ID."""
    if not structure_id:
        click.echo("Error: --structure-id is required")
        return

    async def _geocode_single():
        engine = create_async_engine(get_db_url())

        async with AsyncSession(engine) as session:
            # Get the structure
            query = select(Structure).where(Structure.id == structure_id)
            result = await session.execute(query)
            structure = result.scalar_one_or_none()

            if not structure:
                click.echo(f"Structure {structure_id} not found")
                return

            if not structure.gps_lat or not structure.gps_lon:
                click.echo(f"Structure {structure_id} has no GPS coordinates")
                return

            click.echo(f"Structure: {structure.structure_str or 'Unnamed'}")
            click.echo(f"Current province_id: {structure.province_id}")
            click.echo(f"GPS: {structure.gps_lat}, {structure.gps_lon}")
            click.echo(f"Geocoding status: {structure.geocoding_status}")

            # Run geocoding
            job = AsyncGeocodingJob(db_url=get_db_url())
            result = await job.process_structure(session, structure)

            # Get updated structure
            await session.refresh(structure)

            if structure.province_id:
                # Get province and country info
                province_query = select(Province).where(
                    Province.id == structure.province_id
                )
                province_result = await session.execute(province_query)
                province = province_result.scalar_one_or_none()

                if province:
                    country_query = select(Country).where(
                        Country.id == province.country_id
                    )
                    country_result = await session.execute(country_query)
                    country = country_result.scalar_one_or_none()

                    click.echo("\nGeocoding result:")
                    click.echo(f"  Province: {province.name}")
                    click.echo(
                        f"  Country: {country.country_name if country else 'Unknown'}"
                    )
            else:
                click.echo("\nNo province mapping found")

            click.echo(f"  Status: {result['status']}")
            if result["status"] == "error":
                click.echo(f"  Error: {result.get('error')}")

        await engine.dispose()

    asyncio.run(_geocode_single())


@cli.command()
def reset_failed():
    """Reset failed geocoding attempts to pending."""

    async def _reset():
        engine = create_async_engine(get_db_url())

        async with AsyncSession(engine) as session:
            from sqlalchemy import update

            # Count failed
            count_query = select(func.count(Structure.id)).where(
                Structure.geocoding_status == "failed"
            )
            count_result = await session.execute(count_query)
            failed_count = count_result.scalar() or 0

            if failed_count == 0:
                click.echo("No failed geocoding attempts found")
                return

            # Reset to pending
            update_stmt = (
                update(Structure)
                .where(Structure.geocoding_status == "failed")
                .values(geocoding_status="pending")
            )

            await session.execute(update_stmt)
            await session.commit()

            click.echo(f"Reset {failed_count} failed structures to pending status")

        await engine.dispose()

    asyncio.run(_reset())


if __name__ == "__main__":
    cli()
