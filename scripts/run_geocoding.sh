#!/bin/bash
# <PERSON><PERSON>t to run the geocoding job

# Set the database URL for the container environment
export DATABASE_URL="*******************************************************/ijack"

echo "Running geocoding job..."
echo "========================"

# Parse command line arguments
BATCH_SIZE=50
LIMIT=""
CLEANUP_CACHE=""

while [[ $# -gt 0 ]]; do
  case $1 in
    --batch-size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    --limit)
      LIMIT="--limit $2"
      shift 2
      ;;
    --cleanup-cache)
      CLEANUP_CACHE="--cleanup-cache"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Run the geocoding job
cd /project
python -m packages.shared.jobs.geocoding_job_async \
  --batch-size $BATCH_SIZE \
  $LIMIT \
  $CLEANUP_CACHE

echo "Geocoding job completed!"