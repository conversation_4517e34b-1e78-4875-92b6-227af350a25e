#!/bin/bash
# Setup script for automated login tools

set -e

echo "🔧 Setting up automated login tools for IJACK development..."

# Check if we're in a virtual environment
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ Virtual environment detected: $VIRTUAL_ENV"
else
    echo "⚠️  No virtual environment detected. Creating one..."

    # Create virtual environment if it doesn't exist
    if [ ! -d ".venv" ]; then
        python3 -m venv .venv
        echo "✅ Created .venv virtual environment"
    fi

    echo "🔄 Activating virtual environment..."
    source .venv/bin/activate
fi

# Install Playwright
echo "📦 Installing Playwright..."
pip install playwright

# Install browser binaries
echo "🌐 Installing browser binaries..."
playwright install chromium

# Install additional dependencies
echo "🔧 Installing additional dependencies..."
playwright install-deps

# Make scripts executable
chmod +x scripts/auto_login.py

echo "✅ Setup complete!"
echo ""
echo "Usage examples:"
echo "  # Run with visible browser (recommended for first time)"
echo "  python scripts/auto_login.py"
echo ""
echo "  # Run in headless mode (faster)"
echo "  python scripts/auto_login.py --headless"
echo ""
echo "  # Navigate to specific page after login"
echo "  python scripts/auto_login.py --url /sales"
echo ""
echo "  # Use different base URL"
echo "  python scripts/auto_login.py --base-url http://localhost:5000"
echo ""

# Create a quick test
echo "🧪 Testing basic setup..."
python -c "from playwright.async_api import async_playwright; print('✅ Playwright import successful')"

echo "🎉 All ready! You can now use the automated login script."
