# Using Session Cookies for Authenticated Requests

Now that the login script works perfectly, <PERSON> can use the saved session cookies to make authenticated requests without going through the login process each time.

## How It Works

1. **Login script saves cookies**: When `auto_login.py` succeeds, it saves cookies to `session_cookies.json`
2. **Future requests use cookies**: Other scripts load these cookies to make authenticated requests
3. **Session persistence**: Cookies remain valid until the session expires

## Available Tools

### 1. Basic Authenticated Requests

**Purpose**: Make simple authenticated HTTP requests to protected pages

```bash
# Check if a protected page loads
python scripts/authenticated_request.py --url /services/overview

# Get JSON data from API endpoints  
python scripts/authenticated_request.py --url /api/auth/me --json

# Save screenshot of protected page
python scripts/authenticated_request.py --url /sales --save-screenshot

# Use different base URL
python scripts/authenticated_request.py --url /services --base-url http://localhost:5000
```

**What it returns**:
- ✅ Page status and authentication check
- 📍 Final URL (detects redirects to login)
- 📝 Page title
- 📋 JSON data (for API endpoints)
- 📸 Screenshots (optional)

### 2. Comprehensive Page Debugging

**Purpose**: Deep analysis of protected pages for troubleshooting

```bash
# Basic debugging
python scripts/debug_authenticated_page.py --url /services/overview

# Include API call monitoring
python scripts/debug_authenticated_page.py --url /services/overview --check-api-calls

# Full debugging with console logs and HTML dump
python scripts/debug_authenticated_page.py --url /services/overview --full-debug
```

**What it analyzes**:
- 🏗️ **Page Structure**: Checks for navbar, sidebar, main content, charts, tables
- 🚨 **Error Detection**: Looks for error messages, alerts, failures
- 📡 **API Calls**: Monitors all API requests and their status codes
- 📝 **Console Logs**: Captures JavaScript errors and warnings
- 📸 **Screenshots**: Saves debugging screenshots
- 📄 **HTML Dump**: Saves complete page HTML for analysis

## Workflow for Claude Code

### Initial Setup (Once)
```bash
# 1. Login and save session
python scripts/auto_login.py --headless

# This creates:
# - login_success.png (screenshot)
# - session_cookies.json (authentication cookies)
```

### Regular Usage

**Check if page loads correctly**:
```bash
python scripts/authenticated_request.py --url /services/overview --save-screenshot
```

**Debug missing content**:
```bash
python scripts/debug_authenticated_page.py --url /services/overview --check-api-calls
```

**Troubleshoot API issues**:
```bash
python scripts/debug_authenticated_page.py --url /services/overview --full-debug
```

**Test specific API endpoints**:
```bash
python scripts/authenticated_request.py --url /v1/service-analytics/cost-overview --json
```

## Session Management

### Check if session is still valid
```bash
python scripts/authenticated_request.py --url /services/overview
```

If you see: `🔒 Session appears to have expired - redirected to login`

### Refresh session
```bash
python scripts/auto_login.py --headless  # Creates new session_cookies.json
```

### Session lifespan
- Flask sessions typically last 24 hours
- Development sessions may be shorter
- Script will detect expired sessions automatically

## Integration Examples

### For Claude Code: Check Service Dashboard Issues

```bash
# 1. First, ensure we have a valid session
python scripts/auto_login.py --headless

# 2. Debug the service dashboard
python scripts/debug_authenticated_page.py --url /services/overview --full-debug

# This will show:
# - Whether charts/tables are present in DOM
# - If API calls are failing (404, 500 errors)  
# - JavaScript console errors
# - Missing content indicators
```

### For Claude Code: Test API Endpoints

```bash
# Test if specific service analytics endpoints work
python scripts/authenticated_request.py --url /v1/service-analytics/cost-overview --json
python scripts/authenticated_request.py --url /v1/service-analytics/available-years --json
```

### For Claude Code: Compare Pages

```bash
# Test multiple pages to see which work
python scripts/authenticated_request.py --url /sales --save-screenshot
python scripts/authenticated_request.py --url /services/overview --save-screenshot  
python scripts/authenticated_request.py --url /units --save-screenshot

# Compare screenshots to see differences
```

## Troubleshooting Common Issues

### 1. "Charts not loading after refresh"
```bash
python scripts/debug_authenticated_page.py --url /services/overview --check-api-calls
```
Look for:
- Failed API calls (404, 500 errors)
- Missing DOM elements (charts, tables)
- JavaScript errors in console

### 2. "Blank page after login"
```bash
python scripts/authenticated_request.py --url /services --save-screenshot
python scripts/authenticated_request.py --url /services/overview --save-screenshot
```
Compare to see if there's a routing issue.

### 3. "API returning empty data"
```bash
python scripts/authenticated_request.py --url /v1/service-analytics/cost-overview --json
```
Check if the API endpoint returns expected data structure.

## Security Notes

- 🔒 **Development only**: These tools are for local development
- 🍪 **Cookie storage**: `session_cookies.json` contains authentication data
- 🚫 **Don't commit**: Add `session_cookies.json` to `.gitignore`
- ⏰ **Time-limited**: Sessions expire and need renewal
- 🌐 **Localhost only**: Only use with development servers

## Files Created by Scripts

| File | Purpose | When Created |
|------|---------|-------------|
| `session_cookies.json` | Authentication cookies | After successful login |
| `login_success.png` | Success screenshot | After successful login |
| `authenticated_*.png` | Page screenshots | When using `--save-screenshot` |
| `debug_*.png` | Debug screenshots | During debugging |
| `debug_*.html` | Full page HTML | When using `--full-debug` |

## Advanced Usage

### Custom Cookie Files
```bash
# Use different cookie file for different users
python scripts/auto_login.py --headless  # Save to session_cookies.json
mv session_cookies.json admin_cookies.json

python scripts/authenticated_request.py --url /admin --cookies-file admin_cookies.json
```

### Automation Scripts
```bash
#!/bin/bash
# daily_check.sh - Check all main pages work

echo "🔄 Refreshing session..."
python scripts/auto_login.py --headless

echo "🧪 Testing main pages..."
python scripts/authenticated_request.py --url /services/overview --save-screenshot
python scripts/authenticated_request.py --url /sales --save-screenshot
python scripts/authenticated_request.py --url /units --save-screenshot

echo "✅ All pages tested - check screenshots for results"
```

This setup gives Claude Code powerful tools to troubleshoot authentication-protected pages without manually going through the login process each time!