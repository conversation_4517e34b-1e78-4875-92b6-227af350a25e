#!/usr/bin/env python3
"""
Debug authenticated pages for Claude Code troubleshooting.

This script provides detailed debugging information about protected pages,
including DOM inspection, error detection, and network monitoring.

Usage:
    python scripts/debug_authenticated_page.py --url /services/overview
    python scripts/debug_authenticated_page.py --url /services/overview --check-api-calls
    python scripts/debug_authenticated_page.py --url /services/overview --full-debug
"""

import argparse
import asyncio
import json
import sys
from pathlib import Path

from playwright.async_api import async_playwright


async def debug_authenticated_page(
    url_path: str,
    base_url: str = "http://localhost:4999",
    check_api_calls: bool = False,
    full_debug: bool = False,
    cookies_file: str = "session_cookies.json",
):
    """
    Comprehensive debugging of an authenticated page.
    """

    # Check if cookies file exists
    if not Path(cookies_file).exists():
        print(f"❌ Cookies file not found: {cookies_file}")
        print("💡 Run the login script first: python scripts/auto_login.py")
        return False

    # Load saved cookies
    with open(cookies_file, "r") as f:
        cookies = json.load(f)

    full_url = f"{base_url}{url_path}"
    print(f"🔍 Debugging authenticated page: {full_url}")

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)

        try:
            # Create context and add cookies
            context = await browser.new_context()
            await context.add_cookies(cookies)

            page = await context.new_page()

            # Network monitoring for API calls
            api_calls = []
            if check_api_calls or full_debug:

                def handle_request(request):
                    if "/api/" in request.url or "/v1/" in request.url:
                        api_calls.append(
                            {
                                "url": request.url,
                                "method": request.method,
                                "headers": dict(request.headers),
                            }
                        )

                def handle_response(response):
                    if "/api/" in response.url or "/v1/" in response.url:
                        for call in api_calls:
                            if call["url"] == response.url and "status" not in call:
                                call["status"] = response.status
                                call["response_headers"] = dict(response.headers)

                page.on("request", handle_request)
                page.on("response", handle_response)

            # Console logging
            console_logs = []
            if full_debug:

                def handle_console(msg):
                    console_logs.append(
                        {"type": msg.type, "text": msg.text, "location": msg.location}
                    )

                page.on("console", handle_console)

            # Make the request
            print("📡 Loading page...")
            response = await page.goto(full_url)
            await page.wait_for_load_state("networkidle")

            print(f"📊 Response status: {response.status}")
            print(f"📍 Final URL: {page.url}")

            # Check authentication
            if "/login" in page.url and "/login" not in url_path:
                print("🔒 ❌ Session expired - redirected to login")
                return False

            print("✅ Authentication successful")

            # Basic page info
            title = await page.title()
            print(f"📝 Page title: {title}")

            # Check for main content areas
            print("\n🏗️  Page Structure Analysis:")

            # Check for common containers
            containers_to_check = [
                ("Navbar", "nav, .navbar, .navigation"),
                ("Sidebar", ".sidebar, .side-nav, aside"),
                ("Main Content", "main, .main-content, .content"),
                ("Cards", ".card, .service-cost-cards"),
                ("Charts", '.chart, canvas, svg[class*="chart"]'),
                ("Tables", "table, .ag-grid, .data-table"),
                ("Filters", ".filter, .sidebar-menu"),
                ("Loading Elements", ".loading, .spinner, .skeleton"),
            ]

            for name, selector in containers_to_check:
                elements = page.locator(selector)
                count = await elements.count()
                if count > 0:
                    print(f"  ✅ {name}: {count} element(s) found")
                    if name == "Loading Elements" and count > 0:
                        print(
                            "    ⚠️  Loading elements still present - content may not be fully loaded"
                        )
                else:
                    print(f"  ❌ {name}: Not found")

            # Check for errors
            print("\n🚨 Error Detection:")

            error_selectors = [
                (".error", "General errors"),
                (".alert-danger, .text-danger", "Danger alerts"),
                ('[role="alert"]', "ARIA alerts"),
                (".notification.error", "Error notifications"),
                ('div:has-text("Error"), div:has-text("Failed")', "Error text"),
                (
                    'div:has-text("No data"), div:has-text("No results")',
                    "Empty state messages",
                ),
            ]

            errors_found = []
            for selector, description in error_selectors:
                elements = page.locator(selector)
                count = await elements.count()
                if count > 0:
                    for i in range(min(count, 3)):  # Check first 3 elements
                        try:
                            text = await elements.nth(i).text_content()
                            if text and text.strip():
                                errors_found.append(f"{description}: {text.strip()}")
                        except Exception:
                            continue

            if errors_found:
                print("  ❌ Errors found:")
                for error in errors_found:
                    print(f"    - {error}")
            else:
                print("  ✅ No errors detected")

            # API Calls Analysis
            if api_calls:
                print(f"\n📡 API Calls Detected ({len(api_calls)}):")
                for call in api_calls:
                    status = call.get("status", "pending")
                    status_emoji = (
                        "✅"
                        if str(status).startswith("2")
                        else "❌"
                        if str(status).startswith("4") or str(status).startswith("5")
                        else "⏳"
                    )
                    print(f"  {status_emoji} {call['method']} {call['url']} - {status}")

                    if str(status).startswith("4") or str(status).startswith("5"):
                        print(
                            "    ⚠️  API call failed - this might explain missing content"
                        )

            # Console Logs
            if console_logs:
                error_logs = [
                    log for log in console_logs if log["type"] in ["error", "warning"]
                ]
                if error_logs:
                    print(f"\n📝 Console Errors/Warnings ({len(error_logs)}):")
                    for log in error_logs[:5]:  # Show first 5
                        print(f"  {log['type'].upper()}: {log['text']}")

            # Take debugging screenshot
            screenshot_name = f"debug_{url_path.replace('/', '_').strip('_')}.png"
            await page.screenshot(path=screenshot_name, full_page=True)
            print(f"\n📸 Debug screenshot saved: {screenshot_name}")

            # Save page HTML for analysis
            if full_debug:
                html_content = await page.content()
                html_filename = f"debug_{url_path.replace('/', '_').strip('_')}.html"
                with open(html_filename, "w", encoding="utf-8") as f:
                    f.write(html_content)
                print(f"📄 Page HTML saved: {html_filename}")

            return True

        except Exception as e:
            print(f"💥 Error during debugging: {e}")
            return False

        finally:
            await browser.close()


def main():
    parser = argparse.ArgumentParser(
        description="Debug authenticated pages for troubleshooting"
    )
    parser.add_argument(
        "--url", required=True, help="URL path to debug (e.g., /services/overview)"
    )
    parser.add_argument("--base-url", default="http://localhost:4999", help="Base URL")
    parser.add_argument(
        "--check-api-calls", action="store_true", help="Monitor API calls"
    )
    parser.add_argument(
        "--full-debug",
        action="store_true",
        help="Enable full debugging (includes console logs and HTML dump)",
    )
    parser.add_argument(
        "--cookies-file", default="session_cookies.json", help="Path to cookies file"
    )

    args = parser.parse_args()

    try:
        success = asyncio.run(
            debug_authenticated_page(
                url_path=args.url,
                base_url=args.base_url,
                check_api_calls=args.check_api_calls,
                full_debug=args.full_debug,
                cookies_file=args.cookies_file,
            )
        )

        if success:
            print("\n✅ Debugging completed successfully")
        else:
            print("\n❌ Debugging failed")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n🛑 Debugging cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Failed to complete debugging: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
