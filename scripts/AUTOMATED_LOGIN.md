# Automated Login for IJACK Development

This directory contains automated login tools to streamline development workflow by eliminating the need to manually complete the email verification login process in Docker containers.

## The Problem

When refreshing the browser at `http://localhost:4999/dashboards/services` in a Docker container environment, you need to:

1. Navigate to `/login`
2. Enter `<EMAIL>`
3. Click "send verification code"
4. Wait for auto-fill (dev mode)
5. Click "Verify code"
6. Click "Just sign in, no PassKey"
7. Navigate back to your intended page

This becomes tedious during development when you frequently refresh or restart sessions, especially in containerized environments.

## Root Cause of Missing Dashboard Content

**Fixed**: The main issue was a missing index route. When navigating to `/services`, there was no index route to show content - only the layout with filters was displayed. 

**Solution**: Created `/routes/dashboards/services/index.tsx` that redirects to `/services/overview` where the actual charts and tables are located.

## Automated Solutions

### 1. Main Login Script (Recommended)

**Perfect for Docker containers** - runs headless by default, with optional browser display for debugging.

```bash
# Setup (one-time)
./scripts/setup_auto_login.sh

# Daily usage - runs headless by default (perfect for Docker)
python scripts/auto_login.py                              # Headless login, go to /services/overview
python scripts/auto_login.py --url /sales                 # Headless login, go to sales dashboard
python scripts/auto_login.py --show-browser               # Show browser for debugging/first-time setup
python scripts/auto_login.py --base-url http://localhost:5000  # Different port
```

**Features**:
- ✅ **Docker-optimized**: Runs headless by default (no display required)
- ✅ **Container-friendly**: Works in CI/CD and automated environments
- ✅ **Visual debugging**: Use `--show-browser` when you need to see what's happening
- ✅ **Session persistence**: Saves cookies for future authenticated requests
- ✅ **Success confirmation**: Takes screenshots and saves session data
- ✅ **Cross-platform**: Works in Linux containers, WSL, macOS, Windows

### 2. Authenticated Request Tools

Once logged in, use these tools for testing and troubleshooting protected pages:

```bash
# Check if a protected page loads correctly
python scripts/authenticated_request.py --url /services/overview --save-screenshot

# Debug missing content issues  
python scripts/debug_authenticated_page.py --url /services/overview --check-api-calls

# Test API endpoints directly
python scripts/authenticated_request.py --url /v1/service-analytics/cost-overview --json
```

## Installation Requirements

### Playwright (Main Solution)
```bash
# Quick setup
./scripts/setup_auto_login.sh

# Manual setup
pip install playwright
playwright install chromium
playwright install-deps  # For Linux containers
```

## Why This Design?

### Docker Container Optimized

| Scenario | Command | Why |
|----------|---------|-----|
| **Daily development** | `python scripts/auto_login.py` | Headless by default - works in containers |
| **Debugging login issues** | `python scripts/auto_login.py --show-browser` | Shows browser only when needed |
| **CI/CD automation** | `python scripts/auto_login.py` | No display required |
| **Testing different pages** | `python scripts/auto_login.py --url /sales` | Direct navigation after login |

### Complete Workflow Integration

```bash
# 1. Login and save session (headless - perfect for Docker)
python scripts/auto_login.py

# 2. Test protected pages using saved session
python scripts/authenticated_request.py --url /services/overview --save-screenshot

# 3. Debug issues with detailed analysis
python scripts/debug_authenticated_page.py --url /services/overview --full-debug
```

## Development Workflow

### Initial Setup (Once per environment)
```bash
# 1. Install tools
./scripts/setup_auto_login.sh

# 2. Test with browser visible (first time)
python scripts/auto_login.py --show-browser

# 3. Verify session works
python scripts/authenticated_request.py --url /services/overview
```

### Daily Usage (Container-friendly)
```bash
# Quick login (headless)
python scripts/auto_login.py

# Login and go to specific page
python scripts/auto_login.py --url /sales

# Debug page issues
python scripts/debug_authenticated_page.py --url /services/overview --check-api-calls
```

### Troubleshooting Workflow
```bash
# 1. Check if login worked
python scripts/authenticated_request.py --url /services/overview --save-screenshot

# 2. Analyze missing content
python scripts/debug_authenticated_page.py --url /services/overview --full-debug

# 3. Test API endpoints
python scripts/authenticated_request.py --url /v1/service-analytics/cost-overview --json
```

## Files Created by Scripts

| File | Purpose | When Created |
|------|---------|-------------|
| `session_cookies.json` | Authentication cookies for future requests | After successful login |
| `login_success.png` | Screenshot of successful login destination | After successful login |
| `authenticated_*.png` | Screenshots of protected pages | When using `--save-screenshot` |
| `debug_*.png` | Debug screenshots for troubleshooting | During debugging |
| `debug_*.html` | Full page HTML for analysis | When using `--full-debug` |

## Troubleshooting

### Common Issues

1. **"Charts not loading after refresh"**
   ```bash
   python scripts/debug_authenticated_page.py --url /services/overview --check-api-calls
   ```
   Look for failed API calls or missing DOM elements.

2. **"Login script fails in Docker"**
   ```bash
   # Ensure deps are installed
   playwright install-deps
   
   # Run with error debugging
   python scripts/auto_login.py --show-browser  # If you have display
   ```

3. **"Session expired"**
   ```bash
   # Refresh session
   python scripts/auto_login.py
   ```

4. **"Permission denied on scripts"**
   ```bash
   chmod +x scripts/*.sh scripts/*.py
   ```

### Debug Mode

```bash
# Visual debugging (requires display)
python scripts/auto_login.py --show-browser

# Comprehensive page debugging
python scripts/debug_authenticated_page.py --url /services/overview --full-debug

# API troubleshooting
python scripts/debug_authenticated_page.py --url /services/overview --check-api-calls
```

## Integration Examples

### For Docker Development
```bash
# In your Dockerfile or docker-compose
RUN pip install playwright && playwright install chromium chromium-deps

# In your container startup script
python scripts/auto_login.py  # Runs headless by default
```

### For Development Aliases
```bash
# Add to your .bashrc or .zshrc
alias dev-login="python scripts/auto_login.py"
alias dev-services="python scripts/auto_login.py --url /services/overview"
alias dev-sales="python scripts/auto_login.py --url /sales"
alias debug-page="python scripts/debug_authenticated_page.py"
```

### For CI/CD
```bash
#!/bin/bash
# test_dashboard.sh
echo "🔄 Logging in..."
python scripts/auto_login.py

echo "🧪 Testing service dashboard..."
python scripts/authenticated_request.py --url /services/overview --save-screenshot

echo "✅ Dashboard test complete"
```

## Security Considerations

- ⚠️ **Development only**: These scripts are for local development
- ⚠️ **Credentials**: Uses hardcoded dev email `<EMAIL>`
- ⚠️ **Environment**: Only use on `localhost` development servers
- ✅ **Session management**: Creates temporary browser sessions
- ✅ **No password storage**: Relies on dev auto-fill mechanism
- ✅ **Container isolation**: Safe in containerized development environments

## Advanced Usage

### Session Management
```bash
# Check session validity
python scripts/authenticated_request.py --url /services/overview

# Force refresh session
python scripts/auto_login.py  # Creates new session_cookies.json

# Use custom session file
python scripts/authenticated_request.py --url /admin --cookies-file admin_cookies.json
```

### Multi-Environment Testing
```bash
# Test different environments
python scripts/auto_login.py --base-url http://localhost:5000
python scripts/auto_login.py --base-url http://staging.localhost:4999
```

### Automated Health Checks
```bash
#!/bin/bash
# health_check.sh
echo "🏥 Health checking all dashboards..."

python scripts/auto_login.py
python scripts/authenticated_request.py --url /services/overview --save-screenshot
python scripts/authenticated_request.py --url /sales --save-screenshot
python scripts/authenticated_request.py --url /units --save-screenshot

echo "✅ Health check complete - review screenshots"
```

This setup provides a robust, container-friendly authentication system that works seamlessly in Docker environments while still allowing visual debugging when needed!