#!/usr/bin/env python3
"""
Authenticated request helper for <PERSON> to access protected pages using saved session cookies.

This script allows <PERSON> to make authenticated HTTP requests to the IJACK app
without needing to go through the login process each time.

Usage:
    python scripts/authenticated_request.py --url /services/overview
    python scripts/authenticated_request.py --url /api/auth/me --json
    python scripts/authenticated_request.py --url /sales --save-screenshot
"""

import argparse
import asyncio
import json
import sys
from pathlib import Path

from playwright.async_api import async_playwright


async def make_authenticated_request(
    url_path: str,
    base_url: str = "http://localhost:4999",
    save_screenshot: bool = False,
    return_json: bool = False,
    cookies_file: str = "session_cookies.json",
):
    """
    Make an authenticated request using saved session cookies.

    Args:
        url_path: Path to request (e.g., "/services/overview")
        base_url: Base URL of the application
        save_screenshot: Whether to save a screenshot of the page
        return_json: Whether to treat response as JSON and parse it
        cookies_file: Path to the saved cookies file
    """

    # Check if cookies file exists
    if not Path(cookies_file).exists():
        print(f"❌ Cookies file not found: {cookies_file}")
        print("💡 Run the login script first: python scripts/auto_login.py")
        return None

    # Load saved cookies
    with open(cookies_file, "r") as f:
        cookies = json.load(f)

    full_url = f"{base_url}{url_path}"
    print(f"🌐 Making authenticated request to: {full_url}")

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)

        try:
            # Create context and add cookies
            context = await browser.new_context()
            await context.add_cookies(cookies)

            page = await context.new_page()

            # Make the request
            response = await page.goto(full_url)
            await page.wait_for_load_state("networkidle")

            print(f"📊 Response status: {response.status}")
            print(f"📍 Final URL: {page.url}")

            # Check if we were redirected to login (session expired)
            if "/login" in page.url and "/login" not in url_path:
                print("🔒 Session appears to have expired - redirected to login")
                print("💡 Run the login script again: python scripts/auto_login.py")
                return None

            # Get page title for context
            title = await page.title()
            print(f"📝 Page title: {title}")

            # Save screenshot if requested
            if save_screenshot:
                screenshot_name = (
                    f"authenticated_{url_path.replace('/', '_').strip('_')}.png"
                )
                await page.screenshot(path=screenshot_name, full_page=True)
                print(f"📸 Screenshot saved: {screenshot_name}")

            # Return JSON response if it's an API endpoint
            if return_json or url_path.startswith("/api/"):
                try:
                    content = await page.content()
                    # Try to extract JSON from the page
                    if "application/json" in response.headers.get("content-type", ""):
                        # Pure JSON response
                        json_text = await page.locator("pre").text_content()
                        result = json.loads(json_text)
                        print("📋 JSON Response:")
                        print(json.dumps(result, indent=2))
                        return result
                    else:
                        # Look for JSON in script tags or pre elements
                        json_elements = await page.locator(
                            'script[type="application/json"], pre'
                        ).all()
                        for element in json_elements:
                            try:
                                json_text = await element.text_content()
                                if json_text and json_text.strip().startswith("{"):
                                    result = json.loads(json_text)
                                    print("📋 Found JSON data:")
                                    print(json.dumps(result, indent=2))
                                    return result
                            except json.JSONDecodeError as e:
                                print(f"⚠️  Could not decode JSON from element: {e}")
                                continue

                        print("⚠️  No JSON data found in response")
                        return None

                except Exception as e:
                    print(f"⚠️  Could not parse JSON: {e}")
                    return None

            # For regular pages, return basic info
            result = {
                "url": page.url,
                "title": title,
                "status": response.status,
                "authenticated": "/login" not in page.url or "/login" in url_path,
            }

            # Check for common success indicators
            content = await page.content()
            success_indicators = ["dashboard", "logout", "profile", "navigation"]
            found_indicators = [
                ind for ind in success_indicators if ind in content.lower()
            ]

            if found_indicators:
                result["success_indicators"] = found_indicators
                print(
                    f"✅ Page loaded successfully with indicators: {found_indicators}"
                )

            return result

        except Exception as e:
            print(f"💥 Error making request: {e}")
            return None

        finally:
            await browser.close()


def main():
    parser = argparse.ArgumentParser(
        description="Make authenticated requests using saved session cookies"
    )
    parser.add_argument(
        "--url", required=True, help="URL path to request (e.g., /services/overview)"
    )
    parser.add_argument("--base-url", default="http://localhost:4999", help="Base URL")
    parser.add_argument(
        "--json", action="store_true", help="Parse and display JSON response"
    )
    parser.add_argument(
        "--save-screenshot", action="store_true", help="Save screenshot of the page"
    )
    parser.add_argument(
        "--cookies-file", default="session_cookies.json", help="Path to cookies file"
    )

    args = parser.parse_args()

    try:
        result = asyncio.run(
            make_authenticated_request(
                url_path=args.url,
                base_url=args.base_url,
                save_screenshot=args.save_screenshot,
                return_json=args.json,
                cookies_file=args.cookies_file,
            )
        )

        if result:
            print("✅ Request completed successfully")
        else:
            print("❌ Request failed")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n🛑 Request cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"💥 Failed to complete request: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
