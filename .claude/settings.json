{"allowedTools": ["Edit", "Shell", "Python", "<PERSON><PERSON><PERSON>", "Search"], "permissions": {"editFiles": true, "runShellCommands": true, "runPython": true, "runPytest": true, "searchFiles": true, "accessEnvironmentVariables": true, "allow": ["Bash(ls:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)", "Bash(python -m pytest:*)", "Bash(grep:*)", "Bash(find:*)", "<PERSON><PERSON>(read:*)", "Bash(rm:*)", "Bash(rg:*)", "Bash(python -m pytest tests/unit/inventory/test_flask_inventory_migrated.py::test_save_inventory_modal_creates_movement_and_ledger -xvs)", "Bash(python -m pytest tests/unit/inventory/test_inventory_with_ledger.py -xvs)", "Bash(uv pip install:*)", "Bash(python -m pytest tests/unit/inventory/test_inventory_with_ledger.py::test_save_inventory_modal_creates_movement_and_ledger -xvs)", "Bash(python -m pytest tests/unit/inventory/test_flask_inventory_management.py -xvs)", "Bash(python -m pytest tests/unit/inventory/test_work_order_inventory.py -xvs -k \"test_manual_adjustment\")", "Bash(python -m pytest tests/unit/inventory/test_work_order_inventory.py::test_new_approved_work_order_with_inventory_operations -xvs)", "Bash(python -m pytest tests/unit/inventory/test_work_order_inventory.py::test_negative_stock_policy -xvs)"], "deny": ["Bash(git:commit*)", "Bash(git:push*)", "Bash(git:pull*)", "Bash(git:checkout*)", "<PERSON><PERSON>(docker:*)"]}}