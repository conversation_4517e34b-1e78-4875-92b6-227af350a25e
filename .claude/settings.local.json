{"permissions": {"allow": ["Bash(find:*)", "Bash(pytest:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(rg:*)", "<PERSON><PERSON>(python:*)", "Bash(rg:*)", "Bash(pnpm install:*)", "<PERSON><PERSON>(sed:*)", "Bash(pnpm tsc:*)", "Bash(ls:*)", "Bash(pnpm lint:*)", "Bash(ruff check:*)", "Bash(pnpm run:*)", "Bash(pnpm tsc:*)", "Bash(pnpm vite build:*)", "Bash(pnpm run:*)", "<PERSON><PERSON>(mv:*)", "Bash(rg:*)", "Bash(pnpm run:*)", "Bash(node:*)", "<PERSON><PERSON>(sed:*)", "Bash(rm:*)", "Bash(npx tsc:*)", "Bash(pnpm build:*)", "Bash(pnpm test:*)", "Bash(timeout 180 pytest tests/unit/test_config_gunicorn.py tests/unit/inventory/test_inventory_with_ledger.py tests/unit/inventory/test_dash_inventory_tab.py -v -n auto --tb=short)", "Bash(timeout 180 pytest tests/unit/test_config_gunicorn.py tests/unit/inventory/test_inventory_with_ledger.py tests/unit/inventory/test_dash_inventory_tab.py -v -n auto --tb=line)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm add:*)", "Bash(for i in {1..3})", "Bash(do echo \"=== Run $i ===\")", "<PERSON><PERSON>(break)", "Bash(done)", "<PERSON><PERSON>(mv:*)", "Bash(pnpx shadcn:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm view:*)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"^[A-Z_]+ = \\d+\" /project/flask_app/app/config.py)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"^[A-Z_]+ = \" /project/flask_app/app/config.py)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"from.*config import\" /project/flask_app/tests/ --type py)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"from.*config import\" /project/flask_app/tests/ --type py -l)", "Bash(pnpm remove:*)", "Bash(pnpm ls:*)", "Bash(pnpm install:*)", "Bash(awk:*)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"719[12]\" /project/flask_app/tests/unit/inventory/test_work_order_inventory.py -n -C 3)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"7192\" /project/flask_app/tests/unit/inventory/test_work_order_inventory.py -n -C 3)", "<PERSON><PERSON>(mkdir:*)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"part_num.*[0-9]{4}\" /project/flask_app/tests/unit/inventory/test_work_order_inventory.py -n -C 2)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"part_id.*[0-9]{4}\" /project/flask_app/tests/unit/inventory/test_work_order_inventory.py -n -C 2)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"ensure_parts_exist\" /project/flask_app/tests/unit/inventory/test_work_order_inventory.py -n -C 2)", "<PERSON><PERSON>(python:*)", "Bash(/project/flask_app/node_modules/.pnpm/@anthropic-ai+claude-code@1.0.17/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"PART_ID_PART_0\" /project/flask_app/tests/conftest.py -n -C 2)", "Bash(npx tsc:*)", "Bash(rm:*)", "WebFetch(domain:github.com)", "Bash(npx eslint:*)", "Bash(npx tsc:*)", "Bash(vite build)", "Bash(. .venv/bin/activate)", "<PERSON><PERSON>(python:*)", "Bash(npx tsc:*)", "Bash(npm run build:*)", "Bash(npm run typecheck:*)", "<PERSON><PERSON>(uv run:*)", "Bash(npm run build:*)", "Bash(pnpm exec tsc:*)", "WebFetch(domain:fred.stlouisfed.org)", "WebFetch(domain:www.bankofcanada.ca)", "Bash(pnpm exec tsc:*)", "Bash(git fetch:*)", "Bash(ruff format:*)", "Bash(./scripts/lint_apply.sh:*)"], "deny": ["Bash(git:commit*)", "Bash(git:push*)", "Bash(git:pull*)", "Bash(git:checkout*)", "<PERSON><PERSON>(docker:*)"]}}