# Claude AI Assistant Instructions

## Standard workflow
1. Think through the problem, read the codebase for relevant files, and write a plan to projectplan.md
2. The plan should have a list of to do items that you can check off as you complete them.
3. Before you begin working, check in with me and I will verify the plan.
4. Begin working on the to do items, marking them complete as you go.
5. Every step of the way, give me a high level explanation of what changes you made.
6. Make every task and code change you do as simple and DRY as possible. Who want to avoid making any massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity.
7. Finally, add a review section to the projectplan.md file with a summary of the changes you made, and any other relevant information.

## Included files

<!-- The instructions Github Copilot uses -->
@.github/copilot-instructions.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a multi-service web application with:
- **Flask** (main web framework) - handles web UI, authentication, and Dash apps
- **FastAPI** (API backend) - provides REST API endpoints with OpenAPI spec
- **React** (frontend) - Inertia.js + TanStack Router for SPAs
- **Shared package** - common models/utils between Flask and FastAPI
- **Docker Compose** - orchestrates all services including Redis, PostgreSQL, TimescaleDB

### Service URLs (Development)
- Flask app: https://app.localhost:443
- FastAPI: https://web-api.app.localhost:443  
- Vite HMR: https://hmr.app.localhost:443
- Traefik dashboard: http://localhost:8080

## Commands

### Development Setup
```bash
# Start all services
docker compose -f docker-compose.dev.yml up -d

# Enter development container
docker exec -it dev-rcom-1 bash

# Inside container - install dependencies
cd /project/flask_app && uv pip install -e . -e /project/packages
cd /project/fast_api && uv pip install -e . -e /project/packages
```

### Running Services
```bash
# Inside container
cd /project/flask_app
python wsgi.py  # Flask on port 4999

cd /project/fast_api  
hypercorn app.main:app --config hypercorn.toml  # FastAPI on port 8000

cd /project/flask_app
pnpm --filter inertia run dev  # Vite HMR on port 5173
```

### Testing
```bash
# Flask tests (inside container)
cd /project/flask_app
pytest tests/unit/test_example.py -xvs  # Single test
pytest tests/ -xvs --exitfirst  # All tests
pytest tests/ -xvs --lf  # Last failed

# FastAPI tests
cd /project/fast_api
pytest tests/
```

### Linting & Formatting
```bash
# Python (uses Ruff)
cd /project/flask_app
ruff check app tests --fix
ruff format app tests

# TypeScript/React
cd /project/flask_app/app/inertia/react
pnpm run lint
```

### Database Migrations

Database migrations are done manually with SQL.

### Type Generation
```bash
# Auto-generates TypeScript types from FastAPI OpenAPI spec
cd /project/flask_app
pnpm --filter inertia run generate-types
```

## Key Architecture Patterns

### 1. Multi-Page/Single-Page Hybrid
- Flask serves initial Inertia.js pages (MPA behavior)
- Each Inertia page becomes a TanStack Router SPA
- Routes like `/dashboards/*` delegate to React client-side routing
- Example: `/dashboards/inventory/*` handled by React after initial load

### 2. Database Architecture
- **PostgreSQL** (via pgbouncer-rds) - main application data
- **TimescaleDB** (via pgbouncer-ts) - time-series data
- Models defined in `/packages/shared/models/` using SQLAlchemy
- Shared between Flask and FastAPI via editable install

### 3. API Standards
- All FastAPI endpoints return `ApiResponse[T]` format: `{result: data}`
- Endpoints use Pydantic models for request/response validation
- OpenAPI spec at `/openapi.json` drives TypeScript type generation
- API client uses `openapi-fetch` with `openapi-react-query`

### 4. Frontend State Management
- **TanStack Query** for server state (API data)
- **XState Store** for client state (filters, UI state)
- AG-Grid for data tables with server-side operations
- All grids use `InfiniteRowModel` pagination format

### 5. Authentication Flow
- Flask handles login/sessions (stored in Redis)
- Session cookie sent with API requests
- FastAPI validates sessions against Redis
- Role-based access control (RBAC) via dependencies

## Project Structure

```
/project/
├── flask_app/          # Main Flask application
│   ├── app/
│   │   ├── dashapp/    # Dash applications
│   │   ├── inertia/    # Inertia.js + React frontend
│   │   │   └── react/
│   │   │       ├── src/routes/      # TanStack Router pages
│   │   │       ├── src/components/  # React components
│   │   │       └── src/stores/      # XState stores
│   │   └── models/     # Flask-specific models
│   └── tests/
├── fast_api/           # FastAPI REST API
│   ├── app/
│   │   ├── api/endpoints/  # API routes
│   │   └── schemas/        # Pydantic models
│   └── tests/
├── packages/           # Shared code
│   └── shared/
│       ├── models/     # SQLAlchemy models
│       └── utils/      # Common utilities
└── docker-compose.dev.yml
```

## Important Considerations

### React Development
- Path alias `@/` maps to `src/` in React code
- Run `npm run generate-types` after API changes
- New route trees need Vite restart (stop/start debugger)
- Use `/fast_api/app/core/openapi_ts.py` auto-generates types in dev

### Database Connections
- Use dependency injection: `db: Session = Depends(get_db)`
- Transactions auto-commit on successful requests
- Connection pooling via pgbouncer (transaction mode)

### Testing Approach
- Flask uses pytest with custom fixtures for DB setup
- Test database created in tmpfs for speed
- Use `conftest_fast_db.py` pattern for fast DB tests
- AG-Grid endpoints tested with fixture data
- Please write a high quality, general purpose solution. Implement a solution that works correctly for all valid inputs, not just the test cases. Do not hard-code values or create solutions that only work for specific test inputs. Instead, implement the actual logic that solves the problem generally.
- Focus on understanding the problem requirements and implementing the correct algorithm. Tests are there to verify correctness, not to define the solution. Provide a principled implementation that follows best practices and software design principles.
- If the task is unreasonable or infeasible, or if any of the tests are incorrect, please tell me. The solution should be robust, maintainable, and extendable.

### Error Handling
- FastAPI uses custom exceptions in `api/exceptions/`
- Frontend shows errors via toast notifications (Sonner)
- API errors follow consistent format with status codes

### Leverage thinking & interleaved thinking capabilities
After receiving tool results, carefully reflect on their quality and determine optimal next steps before proceeding. Use your thinking to plan and iterate based on this new information, and then take the best next action.

### Optimize parallel tool calling
For maximum efficiency, whenever you need to perform multiple independent operations, invoke all relevant tools simultaneously rather than sequentially.

### Reduce file creation in agentic coding
If you create any temporary new files, scripts, or helper files for iteration, clean up these files by removing them at the end of the task.

### Think hard always
Think step-by-step through the problem and show your complete reasoning. Try different approaches if your first approach doesn't work.

## Virtual Environment Management

### Overview
This project uses multiple virtual environments for different components. Always ensure you're using the correct virtual environment based on the code you're working with.

### Python Virtual Environments

#### Flask Application (flask_app)
When working with Flask, Dash, or related code, ALWAYS activate the virtual environment before running python or pytest commands:

```bash
# Activate virtual environment
cd /project/flask_app
source .venv/bin/activate  # Linux/Mac
# or
.venv\Scripts\activate  # Windows

# Package management with uv
uv pip install -r pyproject.toml  # Install all dependencies
uv pip install package_name       # Add new package
uv lock                          # Update lock file
```

#### FastAPI Application (fast_api)
When working with FastAPI or API-related code, ALWAYS activate the virtual environment before running python or pytest commands:
```bash
# Activate virtual environment
cd /project/fast_api
source .venv/bin/activate  # Linux/Mac
# or
.venv\Scripts\activate  # Windows

# Package management with uv
uv pip install -r pyproject.toml
```

#### Shared Packages (packages)
When working with shared models or utilities, ALWAYS activate the virtual environment before running python or pytest commands:
```bash
# Use Flask app's virtual environment
cd /project/flask_app
source .venv/bin/activate

# Or ensure packages is installed as editable
cd /project/packages
pip install -e .
```

### JavaScript/Node Environments

#### React/Inertia Frontend
When working with React components or TypeScript:
```bash
cd /project/flask_app/app/inertia/react
pnpm install        # Install dependencies
pnpm dev           # Start dev server
pnpm build         # Build for production
pnpm type-check    # Run TypeScript checks
```

#### Flask App Frontend Assets
When working with Flask app's JavaScript/CSS:
```bash
cd /project/flask_app
pnpm install
```

## Testing Guidelines

### Running Tests by Directory

#### Flask App Tests
```bash
cd /project/flask_app
source .venv/bin/activate
pytest tests/                    # Run all tests
pytest tests/unit/              # Run unit tests only
pytest tests/dash_rcom/         # Run Dash tests only
pytest -k "test_name"           # Run specific test
```

#### FastAPI Tests
```bash
cd /project/fast_api
source .venv/bin/activate
pytest tests/
```

#### Shared Package Tests
```bash
cd /project/flask_app  # Use Flask venv
source .venv/bin/activate
cd /project/packages
pytest tests/
```

## Important Conventions

### Database Operations
- Always use async sessions with SQLAlchemy
- Use `get_dataframe()` for pandas integration
- Apply concurrent operations with `asyncio.gather()`

### API Development
- Use `$api.useQuery()` for data fetching in React
- Follow the established response schema patterns
- Implement proper role-based access control

### Code Style
- Python: Follow PEP 8, use type hints
- TypeScript: Use strong typing, avoid `any`
- Always run linters before committing:
  ```bash
  # Python
  ruff check .
  ruff format .
  
  # TypeScript
  pnpm lint
  pnpm format
  ```

## Environment Variables
- Flask app: `/project/flask_app/.env`
- FastAPI: `/project/fast_api/.env`
- React: `/project/flask_app/app/inertia/react/.env`

## Quick Reference

### Determine Which Environment to Use
- Working on `flask_app/` → Use Flask venv
- Working on `fast_api/` → Use FastAPI venv
- Working on `packages/` → Use Flask venv
- Working on React components → Use pnpm in react directory
- Working on shared models → Use Flask venv

### Common Commands
```bash
# Check current environment
which python
echo $VIRTUAL_ENV

# Deactivate current environment
deactivate

# Install shared packages in editable mode
pip install -e /project/packages

# Run type checking
cd /project/flask_app && pyright
cd /project/fast_api && pyright

# Run all linters and formatters
cd /project/flask_app && ruff check . && ruff format .
cd /project/flask_app/app/inertia/react && pnpm lint && pnpm format
```

## Docker Development
When working with Docker:
```bash
# Use docker-compose for local development
docker-compose -f docker-compose.local.yml up

# Rebuild after dependency changes
docker-compose -f docker-compose.local.yml build --no-cache
```

Remember: Always verify you're in the correct virtual environment before installing packages or running code!