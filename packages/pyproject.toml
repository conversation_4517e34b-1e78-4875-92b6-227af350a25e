[project]
name = "shared"
version = "0.1.0"
description = "Shared packages for IJACK applications"
requires-python = ">=3.11,<3.14"
dependencies = ["pytz>=2025.2", "sqlalchemy>=2.0.40", "sqlalchemy[asyncio]"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["shared"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = ["-v", "--tb=short", "--strict-markers", "--disable-warnings"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "smoke: marks tests as smoke tests",
]
