"""
Async Background Geocoding Job for IJACK Technologies

This job processes structures with GPS coordinates and populates
the geographic fields using the hybrid geocoding service.

Connects to the actual database and updates structures with province/country data.
"""

import asyncio
import sys
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional

# Add project root to path for imports
sys.path.append("/project")

from sqlalchemy import and_, or_, select, update, NullPool
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from shared.models.models import Country, GeocodingCache, Province
from shared.services.geocoding_service import HybridGeocodingService
from shared.utils.geographic_utils import round_coordinates_for_cache

# Import concrete Structure model - must use the app-specific one, not the abstract base
try:
    # For FastAPI context
    from app.models.models import Structure
except ImportError:
    # For standalone script execution
    from fast_api.app.models.models import Structure


class AsyncGeocodingJob:
    """
    Async background job to geocode structures and populate geographic fields.

    This job processes structures in batches to avoid overwhelming the system
    and provides intelligent geographic identification without external APIs.
    """

    def __init__(self, db_url: str, batch_size: int = 50):
        self.batch_size = batch_size
        self.geocoding_service = HybridGeocodingService()

        # Create async engine and session
        # Use NullPool for pgbouncer compatibility
        self.engine = create_async_engine(
            db_url, echo=False, poolclass=NullPool, connect_args={"autocommit": False}
        )
        self.async_session = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autocommit=False,
            autoflush=False,
        )

    async def get_structures_needing_geocoding(
        self, session: AsyncSession, limit: int
    ) -> List[Structure]:
        """Get structures that need geocoding."""

        query = (
            select(Structure)
            .where(
                and_(
                    Structure.gps_lat.isnot(None),
                    Structure.gps_lon.isnot(None),
                    or_(
                        Structure.province_id.is_(None),
                        Structure.geocoding_status.in_(["pending", None]),
                    ),
                )
            )
            .limit(limit)
        )

        result = await session.execute(query)
        return result.scalars().all()

    async def get_or_create_province(
        self, session: AsyncSession, province_name: str, country_name: str
    ) -> Optional[int]:
        """Get or create province record."""

        # First try to find the country
        country_query = select(Country).where(Country.country_name == country_name)
        country_result = await session.execute(country_query)
        country = country_result.scalar_one_or_none()

        if not country:
            # Create country if it doesn't exist
            # In production, you might want to handle this differently
            return None

        # Find or create province
        province_query = select(Province).where(
            and_(Province.name == province_name, Province.country_id == country.id)
        )
        province_result = await session.execute(province_query)
        province = province_result.scalar_one_or_none()

        if province:
            return province.id

        # Create province if it doesn't exist
        # In production, you might want to handle this differently
        return None

    async def check_cache(
        self, session: AsyncSession, lat: float, lon: float
    ) -> Optional[Dict]:
        """Check geocoding cache for coordinates."""

        lat_rounded, lon_rounded = round_coordinates_for_cache(lat, lon)

        query = select(GeocodingCache).where(
            and_(
                GeocodingCache.lat_rounded == lat_rounded,
                GeocodingCache.lon_rounded == lon_rounded,
            )
        )

        result = await session.execute(query)
        cache_entry = result.scalar_one_or_none()

        if cache_entry:
            return {
                "province_id": cache_entry.province_id,
                "country_id": cache_entry.country_id,
                "locality": cache_entry.locality,
                "confidence_score": cache_entry.confidence_score,
                "data_source": cache_entry.data_source,
            }

        return None

    async def save_to_cache(
        self, session: AsyncSession, lat: float, lon: float, geo_info: Dict
    ):
        """Save geocoding result to cache."""

        lat_rounded, lon_rounded = round_coordinates_for_cache(lat, lon)

        cache_entry = GeocodingCache(
            lat_rounded=lat_rounded,
            lon_rounded=lon_rounded,
            province_id=geo_info.get("province_id"),
            country_id=geo_info.get("country_id"),
            locality=geo_info.get("locality"),
            confidence_score=geo_info.get("confidence_score", 1.0),
            data_source=geo_info.get("data_source", "internal"),
        )

        session.add(cache_entry)

    async def process_structure(
        self, session: AsyncSession, structure: Structure
    ) -> Dict:
        """Process a single structure for geocoding."""

        try:
            # Mark as processing
            await session.execute(
                update(Structure)
                .where(Structure.id == structure.id)
                .values(geocoding_status="processing")
            )
            await session.commit()

            # Check cache first
            cached_result = await self.check_cache(
                session, structure.gps_lat, structure.gps_lon
            )

            if cached_result:
                # Use cached result
                province_id = cached_result.get("province_id")
                update_values = {
                    "province_id": province_id,
                    "geocoding_status": "completed",
                    "geocoding_updated_at": datetime.now(timezone.utc),
                    "auto_geocoded": True,
                }
            else:
                # Get geographic info from service
                geo_info = self.geocoding_service.get_geographic_info_sync(
                    structure.gps_lat, structure.gps_lon
                )

                # Try to get province ID from database
                province_id = None
                if geo_info.get("province_name") and geo_info.get("country_name"):
                    province_id = await self.get_or_create_province(
                        session, geo_info["province_name"], geo_info["country_name"]
                    )

                # Save to cache
                cache_info = {
                    "province_id": province_id,
                    "locality": geo_info.get("locality"),
                    "confidence_score": geo_info.get("confidence_score", 1.0),
                    "data_source": geo_info.get("data_source", "internal"),
                }
                await self.save_to_cache(
                    session, structure.gps_lat, structure.gps_lon, cache_info
                )

                update_values = {
                    "province_id": province_id,
                    "geocoding_status": "completed",
                    "geocoding_updated_at": datetime.utcnow(),
                    "auto_geocoded": True,
                }

            # Update structure
            await session.execute(
                update(Structure)
                .where(Structure.id == structure.id)
                .values(**update_values)
            )
            await session.commit()

            return {
                "status": "success",
                "structure_id": structure.id,
                "province_id": province_id,
            }

        except Exception as e:
            # Mark as failed
            await session.execute(
                update(Structure)
                .where(Structure.id == structure.id)
                .values(
                    geocoding_status="failed", geocoding_updated_at=datetime.utcnow()
                )
            )
            await session.commit()

            return {"status": "error", "structure_id": structure.id, "error": str(e)}

    async def process_batch(
        self, session: AsyncSession, structures: List[Structure]
    ) -> Dict:
        """Process a batch of structures."""

        results = {"processed": 0, "success": 0, "errors": 0, "details": []}

        for structure in structures:
            result = await self.process_structure(session, structure)
            results["processed"] += 1
            results["details"].append(result)

            if result["status"] == "success":
                results["success"] += 1
            else:
                results["errors"] += 1

        return results

    async def run_job(self, limit: Optional[int] = None):
        """Run the geocoding job."""

        print(f"Starting async geocoding job at {datetime.utcnow()}")
        print(f"Batch size: {self.batch_size}")

        total_processed = 0
        total_success = 0
        total_errors = 0

        async with self.async_session() as session:
            # Get structures needing geocoding
            structures = await self.get_structures_needing_geocoding(
                session, limit or 1000
            )

            print(f"Found {len(structures)} structures needing geocoding")

            # Process in batches
            for i in range(0, len(structures), self.batch_size):
                batch = structures[i : i + self.batch_size]
                print(
                    f"\nProcessing batch {i // self.batch_size + 1}: {len(batch)} structures"
                )

                results = await self.process_batch(session, batch)

                total_processed += results["processed"]
                total_success += results["success"]
                total_errors += results["errors"]

                print(
                    f"Batch results: {results['success']} success, {results['errors']} errors"
                )

                # Small delay between batches
                await asyncio.sleep(0.1)

        print(f"\nGeocoding job completed at {datetime.utcnow()}")
        print(f"Total processed: {total_processed}")
        print(f"Total success: {total_success}")
        print(f"Total errors: {total_errors}")

        return {
            "total_processed": total_processed,
            "total_success": total_success,
            "total_errors": total_errors,
            "completion_time": datetime.utcnow(),
        }

    async def cleanup_old_cache(self, days: int = 30):
        """Clean up old cache entries."""

        cutoff_date = datetime.utcnow() - timedelta(days=days)

        async with self.async_session() as session:
            # Delete old cache entries
            query = select(GeocodingCache).where(
                GeocodingCache.created_at < cutoff_date
            )

            result = await session.execute(query)
            old_entries = result.scalars().all()

            for entry in old_entries:
                await session.delete(entry)

            await session.commit()

            print(f"Cleaned up {len(old_entries)} old cache entries")


async def main():
    """CLI entry point for the async geocoding job"""
    import argparse
    import os

    parser = argparse.ArgumentParser(
        description="Run async geocoding job for structures"
    )
    parser.add_argument(
        "--batch-size", type=int, default=50, help="Batch size for processing"
    )
    parser.add_argument(
        "--limit", type=int, help="Limit number of structures to process"
    )
    parser.add_argument(
        "--cleanup-cache", action="store_true", help="Clean up old cache entries"
    )
    parser.add_argument(
        "--db-url", help="Database URL (defaults to environment variable DATABASE_URL)"
    )

    args = parser.parse_args()

    # Get database URL
    db_url = args.db_url or os.environ.get("DATABASE_URL")
    if not db_url:
        print(
            "Error: Database URL not provided. Use --db-url or set DATABASE_URL environment variable"
        )
        return

    # Convert to async URL if needed
    if db_url.startswith("postgresql://"):
        db_url = db_url.replace("postgresql://", "postgresql+psycopg://")

    print("IJack Technologies - Async Geocoding Job")
    print("========================================")

    job = AsyncGeocodingJob(db_url=db_url, batch_size=args.batch_size)

    if args.cleanup_cache:
        print("\nCleaning up old cache entries...")
        await job.cleanup_old_cache()

    results = await job.run_job(limit=args.limit)

    return results


if __name__ == "__main__":
    asyncio.run(main())
