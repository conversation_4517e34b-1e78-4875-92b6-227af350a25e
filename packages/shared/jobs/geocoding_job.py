"""
Background Geocoding Job for IJack Technologies

This job processes structures with GPS coordinates and populates
the geographic fields using the hybrid geocoding service.

Phase 4 implementation - keeping it DRY and simple.
"""

import sys
import time
from typing import List, Optional

# Add project root to path for imports
sys.path.append("/project")

from shared.services.geocoding_service import get_geocoding_service
from shared.utils.datetime_utils import utcnow_naive


class GeocodingJob:
    """
    Background job to geocode structures and populate geographic fields.

    This job processes structures in batches to avoid overwhelming the system
    and provides intelligent geographic identification without external APIs.
    """

    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
        self.geocoding_service = get_geocoding_service()

    def process_structures_batch(self, structures: List[dict]) -> dict:
        """
        Process a batch of structures for geocoding.

        Args:
            structures: List of structure dicts with id, lat, lon, etc.

        Returns:
            Dict with processing results and statistics
        """
        processed = 0
        updated = 0
        errors = 0

        for structure in structures:
            try:
                # Skip if already processed recently
                if self._is_recently_geocoded(structure):
                    continue

                # Get geographic information
                lat, lon = structure.get("lat"), structure.get("lon")
                if not lat or not lon:
                    continue

                region_info = self.geocoding_service.get_geographic_region_name(
                    lat, lon
                )

                # Update structure with geographic data
                update_data = {
                    "geocoding_status": "completed",
                    "geocoding_updated_at": utcnow_naive(),
                    "auto_geocoded": True,
                }

                # Add province ID if we can determine it (country access via province join)
                province_id = self._get_province_id_from_region(region_info)

                if province_id:
                    update_data["province_id"] = province_id

                # This would normally update the database
                # For now, just simulate the update
                print(f"Would update structure {structure['id']} with: {update_data}")
                print(f"Region: {region_info['region_name']}")

                updated += 1
                processed += 1

            except Exception as e:
                print(
                    f"Error processing structure {structure.get('id', 'unknown')}: {e}"
                )
                errors += 1
                processed += 1

        return {
            "processed": processed,
            "updated": updated,
            "errors": errors,
            "batch_size": len(structures),
        }

    def _is_recently_geocoded(self, structure: dict) -> bool:
        """Check if structure was geocoded recently (within 30 days)"""
        last_geocoded = structure.get("geocoding_updated_at")
        if not last_geocoded:
            return False

        # In a real implementation, we'd parse the timestamp
        # For now, just return False to process all structures
        return False

    def _get_province_id_from_region(self, region_info: dict) -> Optional[int]:
        """
        Map region info to database province ID.

        This would normally query the provinces table to find matching province
        and country combination. The Structure->Province->Country join handles
        country access automatically.
        """
        # Simplified mapping for demonstration - in real implementation,
        # this would query: SELECT id FROM provinces WHERE name = ? AND country_id = ?
        province_map = {
            ("Alberta", "Canada"): 1,
            ("Saskatchewan", "Canada"): 2,
            ("British Columbia", "Canada"): 3,
            ("Ontario", "Canada"): 4,
            ("Manitoba", "Canada"): 5,
            ("Texas", "United States"): 6,
            ("Colorado", "United States"): 7,
            ("Oklahoma", "United States"): 8,
            ("Cundinamarca", "Colombia"): 9,
            ("Atlantico", "Colombia"): 10,
            ("Distrito Capital", "Venezuela"): 11,
            ("Zulia", "Venezuela"): 12,
            ("Lima", "Peru"): 13,
            ("Pichincha", "Ecuador"): 14,
            ("Riyadh Province", "Saudi Arabia"): 15,
            ("Dubai", "United Arab Emirates"): 16,
            ("Abu Dhabi", "United Arab Emirates"): 17,
            ("Ad Dawhah", "Qatar"): 18,
            ("Al Asimah", "Kuwait"): 19,
            ("Capital", "Bahrain"): 20,
        }

        province_name = region_info.get("province_name")
        country_name = region_info.get("country_name")

        if province_name and country_name:
            return province_map.get((province_name, country_name))

        return None

    def run_job(self, limit: Optional[int] = None):
        """
        Run the geocoding job.

        Args:
            limit: Optional limit on number of structures to process
        """
        print(f"Starting geocoding job at {utcnow_naive()}")
        print(f"Batch size: {self.batch_size}")

        # This would normally query the database for structures needing geocoding
        # For now, simulate with sample data
        sample_structures = [
            {"id": 1, "lat": 51.0447, "lon": -114.0719, "name": "Calgary Test Site"},
            {"id": 2, "lat": 53.5461, "lon": -113.4938, "name": "Edmonton Test Site"},
            {"id": 3, "lat": 29.7604, "lon": -95.3698, "name": "Houston Test Site"},
            {"id": 4, "lat": 25.2048, "lon": 55.2708, "name": "Dubai Test Site"},
            {"id": 5, "lat": 4.7110, "lon": -74.0721, "name": "Bogota Test Site"},
        ]

        # Apply limit if specified
        if limit:
            sample_structures = sample_structures[:limit]

        # Process in batches
        total_processed = 0
        total_updated = 0
        total_errors = 0

        for i in range(0, len(sample_structures), self.batch_size):
            batch = sample_structures[i : i + self.batch_size]
            print(
                f"\nProcessing batch {i // self.batch_size + 1}: {len(batch)} structures"
            )

            results = self.process_structures_batch(batch)

            total_processed += results["processed"]
            total_updated += results["updated"]
            total_errors += results["errors"]

            print(f"Batch results: {results}")

            # Small delay between batches to be nice to the system
            time.sleep(0.1)

        print(f"\nGeocoding job completed at {utcnow_naive()}")
        print(f"Total processed: {total_processed}")
        print(f"Total updated: {total_updated}")
        print(f"Total errors: {total_errors}")

        return {
            "total_processed": total_processed,
            "total_updated": total_updated,
            "total_errors": total_errors,
            "completion_time": utcnow_naive(),
        }


def main():
    """CLI entry point for the geocoding job"""
    import argparse

    parser = argparse.ArgumentParser(description="Run geocoding job for structures")
    parser.add_argument(
        "--batch-size", type=int, default=100, help="Batch size for processing"
    )
    parser.add_argument(
        "--limit", type=int, help="Limit number of structures to process"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in dry-run mode (no database updates)",
    )

    args = parser.parse_args()

    print("IJack Technologies - Geocoding Job")
    print("==================================")

    if args.dry_run:
        print("DRY RUN MODE - No database updates will be performed")

    job = GeocodingJob(batch_size=args.batch_size)
    results = job.run_job(limit=args.limit)

    return results


if __name__ == "__main__":
    main()
