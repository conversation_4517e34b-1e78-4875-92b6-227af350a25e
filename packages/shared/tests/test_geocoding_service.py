"""
Unit tests for the hybrid geocoding service.

Tests the internal logic of the geocoding service including:
- Province/country lookup
- Major city clustering
- Coordinate-based region generation
- Cache operations
"""

import pytest
from unittest.mock import Mock, AsyncMock

from shared.services.geocoding_service import (
    HybridGeocodingService,
    get_geocoding_service,
)
from sqlalchemy.ext.asyncio import AsyncSession


class TestHybridGeocodingService:
    """Test suite for the HybridGeocodingService class."""

    @pytest.fixture
    def service(self):
        """Create a geocoding service instance for testing."""
        return HybridGeocodingService()

    def test_find_nearest_major_city(self, service):
        """Test finding the nearest major city."""
        # Test coordinates near Edmonton, Alberta
        result = service._find_nearest_major_city(53.5461, -113.4938)

        assert result is not None
        assert result["name"] == "Edmonton"
        assert result["province"] == "Alberta"
        assert result["country"] == "Canada"
        assert result["distance_km"] < 10  # Should be very close

        # Test coordinates in remote area
        result = service._find_nearest_major_city(65.0, -135.0)  # Northern Canada

        assert result is not None
        assert result["distance_km"] > 100  # Should be far from any major city

    def test_find_nearest_major_city_us_location(self, service):
        """Test finding nearest major city for US locations."""
        # Test coordinates near Houston, Texas
        result = service._find_nearest_major_city(29.7604, -95.3698)

        assert result is not None
        assert result["name"] == "Houston"
        assert result["province"] == "Texas"
        assert result["country"] == "United States"
        assert result["distance_km"] < 10

    def test_generate_coordinate_region_name(self, service):
        """Test generation of coordinate-based region names."""
        # Test various coordinate combinations
        test_cases = [
            ((53.5, -113.5), "Region (53.5°, -113.5°)"),
            ((0.0, 0.0), "Region (0.0°, 0.0°)"),
            ((-45.123, 170.456), "Region (-45.1°, 170.5°)"),
        ]

        for coords, expected in test_cases:
            result = service._generate_coordinate_region_name(*coords)
            assert result == expected

    @pytest.mark.asyncio
    async def test_geocode_structure_with_province(self, service):
        """Test geocoding a structure that already has province data."""
        # Mock structure with province
        structure = Mock(
            id=1,
            gps_lat=53.5461,
            gps_lon=-113.4938,
            province_id=1,
            province=Mock(name="Alberta", country=Mock(country_name="Canada")),
        )

        # Mock database session
        db = AsyncMock(spec=AsyncSession)

        result = await service.geocode_structure(structure, db)

        assert result is not None
        assert result["region_name"] == "Alberta"
        assert result["country_name"] == "Canada"
        assert result["clustering_method"] == "database"
        assert result["confidence_score"] == 1.0

        # Should not make any database queries for geocoding
        db.execute.assert_not_called()

    @pytest.mark.asyncio
    async def test_geocode_structure_near_major_city(self, service):
        """Test geocoding a structure near a major city."""
        # Mock structure without province, near Calgary
        structure = Mock(
            id=2, gps_lat=51.0447, gps_lon=-114.0719, province_id=None, province=None
        )

        # Mock database session
        db = AsyncMock(spec=AsyncSession)

        result = await service.geocode_structure(structure, db)

        assert result is not None
        assert result["region_name"] == "Near Calgary"
        assert result["country_name"] == "Canada"
        assert result["clustering_method"] == "major_city"
        assert result["distance_from_major_city"] < 20
        assert 0.7 <= result["confidence_score"] <= 0.9

    @pytest.mark.asyncio
    async def test_geocode_structure_remote_location(self, service):
        """Test geocoding a structure in a remote location."""
        # Mock structure in remote northern location
        structure = Mock(
            id=3, gps_lat=68.0, gps_lon=-135.0, province_id=None, province=None
        )

        # Mock database session
        db = AsyncMock(spec=AsyncSession)

        result = await service.geocode_structure(structure, db)

        assert result is not None
        assert result["region_name"] == "Region (68.0°, -135.0°)"
        assert result["country_name"] == "Unknown"
        assert result["clustering_method"] == "coordinate"
        assert result["confidence_score"] < 0.7

    @pytest.mark.asyncio
    async def test_geocode_structure_no_gps(self, service):
        """Test geocoding a structure without GPS coordinates."""
        # Mock structure without GPS
        structure = Mock(
            id=4, gps_lat=None, gps_lon=None, province_id=None, province=None
        )

        # Mock database session
        db = AsyncMock(spec=AsyncSession)

        result = await service.geocode_structure(structure, db)

        assert result is None

    @pytest.mark.asyncio
    async def test_check_cache_hit(self, service):
        """Test checking geocoding cache for existing data."""
        # Mock database session with cache hit
        db = AsyncMock(spec=AsyncSession)

        # Mock cache result
        cache_result = Mock(
            province_id=1,
            province_name="Alberta",
            country_id=1,
            country_name="Canada",
            locality="Calgary",
            confidence_score=0.95,
        )

        db.execute.return_value.scalar_one_or_none.return_value = cache_result

        result = await service._check_cache(53.5, -113.5, db)

        assert result is not None
        assert result["region_name"] == "Calgary, Alberta"
        assert result["country_name"] == "Canada"
        assert result["confidence_score"] == 0.95
        assert result["from_cache"] is True

    @pytest.mark.asyncio
    async def test_check_cache_miss(self, service):
        """Test checking geocoding cache with no results."""
        # Mock database session with cache miss
        db = AsyncMock(spec=AsyncSession)
        db.execute.return_value.scalar_one_or_none.return_value = None

        result = await service._check_cache(45.0, -75.0, db)

        assert result is None

    @pytest.mark.asyncio
    async def test_update_cache(self, service):
        """Test updating the geocoding cache."""
        # Mock database session
        db = AsyncMock(spec=AsyncSession)

        # Geocoding result to cache
        geocoding_result = {
            "region_name": "Alberta",
            "country_name": "Canada",
            "clustering_method": "database",
            "confidence_score": 1.0,
        }

        # Mock Province and Country queries
        province_result = Mock(id=1)
        country_result = Mock(id=1)

        db.execute.return_value.scalar_one_or_none.side_effect = [
            province_result,
            country_result,
        ]

        await service._update_cache(53.5461, -113.4938, geocoding_result, db)

        # Should have made insert/update query
        assert db.execute.call_count >= 3  # Province query, Country query, Insert
        db.commit.assert_called_once()


class TestGeocodingServiceFactory:
    """Test the geocoding service factory function."""

    def test_get_geocoding_service_singleton(self):
        """Test that get_geocoding_service returns singleton instance."""
        service1 = get_geocoding_service()
        service2 = get_geocoding_service()

        assert service1 is service2
        assert isinstance(service1, HybridGeocodingService)

    def test_get_geocoding_service_with_custom_config(self):
        """Test creating service with custom configuration."""
        config = {
            "max_city_distance_km": 200,
            "cache_ttl_days": 60,
        }

        service = get_geocoding_service(config)

        assert isinstance(service, HybridGeocodingService)
        # Note: Would need to expose config attributes to fully test this


class TestGeographicUtilities:
    """Test geographic utility functions used by the service."""

    def test_haversine_distance_calculation(self):
        """Test distance calculation between coordinates."""
        from shared.utils.geographic_utils import calc_distance

        # Test known distances
        test_cases = [
            # Edmonton to Calgary (approximately 300 km)
            ((53.5461, -113.4938), (51.0447, -114.0719), 280, 320),
            # Same location
            ((45.0, -75.0), (45.0, -75.0), 0, 1),
            # Antipodal points (maximum distance ~20,000 km)
            ((0, 0), (0, 180), 19900, 20100),
        ]

        for coord1, coord2, min_dist, max_dist in test_cases:
            distance = calc_distance(coord1[0], coord1[1], coord2[0], coord2[1])
            assert min_dist <= distance <= max_dist

    def test_coordinate_rounding(self):
        """Test coordinate rounding for cache keys."""
        test_cases = [
            (53.54612, -113.49384, 53.54610, -113.49380),
            (45.123456, -75.654321, 45.12346, -75.65432),
            (-45.999999, 170.111111, -46.00000, 170.11111),
        ]

        for lat, lon, expected_lat, expected_lon in test_cases:
            # This would need a method to expose rounding logic
            rounded_lat = round(lat, 5)
            rounded_lon = round(lon, 5)

            assert abs(rounded_lat - expected_lat) < 0.00001
            assert abs(rounded_lon - expected_lon) < 0.00001
