"""
Integration tests for the asynchronous geocoding job.

Tests the geocoding job's ability to:
- Process structures in batches
- Handle errors gracefully
- Update geocoding status
- Clean up old cache entries
"""

from unittest.mock import AsyncMock, Mock, patch

import pytest

from shared.jobs.geocoding_job_async import AsyncGeocodingJob


@pytest.mark.asyncio
class TestAsyncGeocodingJob:
    """Test suite for AsyncGeocodingJob."""

    @pytest.fixture
    async def job(self, tmpdir):
        """Create a geocoding job instance with test database."""
        # Use SQLite for testing
        db_url = f"sqlite+aiosqlite:///{tmpdir}/test.db"

        job = AsyncGeocodingJob(db_url=db_url, batch_size=10, max_retries=2)

        # Create tables
        async with job.engine.begin():
            # Mock table creation
            pass

        yield job

        # Cleanup
        await job.engine.dispose()

    @patch("shared.jobs.geocoding_job_async.AsyncGeocodingJob._get_pending_structures")
    @patch("shared.jobs.geocoding_job_async.HybridGeocodingService")
    async def test_run_job_success(self, mock_service_class, mock_get_pending, job):
        """Test successful execution of geocoding job."""
        # Mock structures to geocode
        mock_structures = [
            Mock(
                id=1,
                gps_lat=53.5461,
                gps_lon=-113.4938,
                geocoding_status="pending",
                retry_count=0,
            ),
            Mock(
                id=2,
                gps_lat=51.0447,
                gps_lon=-114.0719,
                geocoding_status="pending",
                retry_count=0,
            ),
        ]

        mock_get_pending.return_value = mock_structures

        # Mock geocoding service
        mock_service = mock_service_class.return_value
        mock_service.geocode_structure = AsyncMock(
            return_value={
                "region_name": "Alberta",
                "country_name": "Canada",
                "clustering_method": "database",
                "confidence_score": 1.0,
            }
        )

        # Run the job
        results = await job.run_job(limit=10)

        assert results["total_processed"] == 2
        assert results["total_success"] == 2
        assert results["total_errors"] == 0
        assert results["duration_seconds"] > 0

        # Verify geocoding was called for each structure
        assert mock_service.geocode_structure.call_count == 2

    @patch("shared.jobs.geocoding_job_async.AsyncGeocodingJob._get_pending_structures")
    @patch("shared.jobs.geocoding_job_async.HybridGeocodingService")
    async def test_run_job_with_errors(self, mock_service_class, mock_get_pending, job):
        """Test job execution with some geocoding errors."""
        # Mock structures
        mock_structures = [
            Mock(
                id=1,
                gps_lat=53.5461,
                gps_lon=-113.4938,
                geocoding_status="pending",
                retry_count=0,
            ),
            Mock(
                id=2,
                gps_lat=None,  # Invalid GPS
                gps_lon=None,
                geocoding_status="pending",
                retry_count=0,
            ),
        ]

        mock_get_pending.return_value = mock_structures

        # Mock geocoding service
        mock_service = mock_service_class.return_value

        # First structure succeeds, second returns None
        mock_service.geocode_structure = AsyncMock(
            side_effect=[
                {
                    "region_name": "Alberta",
                    "country_name": "Canada",
                    "clustering_method": "database",
                    "confidence_score": 1.0,
                },
                None,  # Failed geocoding
            ]
        )

        # Run the job
        results = await job.run_job(limit=10)

        assert results["total_processed"] == 2
        assert results["total_success"] == 1
        assert results["total_errors"] == 1

    @patch("shared.jobs.geocoding_job_async.AsyncGeocodingJob._get_pending_structures")
    async def test_run_job_no_pending(self, mock_get_pending, job):
        """Test job execution with no pending structures."""
        mock_get_pending.return_value = []

        results = await job.run_job(limit=10)

        assert results["total_processed"] == 0
        assert results["total_success"] == 0
        assert results["total_errors"] == 0

    @patch("shared.jobs.geocoding_job_async.AsyncGeocodingJob._process_structure")
    async def test_batch_processing(self, mock_process, job):
        """Test that structures are processed in correct batch sizes."""
        # Mock many structures
        mock_structures = [
            Mock(
                id=i,
                gps_lat=50.0 + i * 0.1,
                gps_lon=-110.0 + i * 0.1,
                geocoding_status="pending",
                retry_count=0,
            )
            for i in range(25)
        ]

        # Mock successful processing
        mock_process.return_value = True

        with patch.object(job, "_get_pending_structures", return_value=mock_structures):
            results = await job.run_job(limit=30)

        # Should process all 25 structures
        assert results["total_processed"] == 25

        # Check batch processing (batch_size=10)
        # Should have been called 25 times (once per structure)
        assert mock_process.call_count == 25

    async def test_retry_mechanism(self, job):
        """Test that failed structures are retried correctly."""
        # Create a mock structure with retry tracking
        structure = Mock(
            id=1,
            gps_lat=53.5461,
            gps_lon=-113.4938,
            geocoding_status="pending",
            retry_count=0,
        )

        # Mock session
        session = AsyncMock()

        # First attempt fails
        with patch.object(
            job.geocoding_service,
            "geocode_structure",
            side_effect=Exception("Network error"),
        ):
            success = await job._process_structure(structure, session)

        assert not success
        assert structure.geocoding_status == "failed"
        assert structure.retry_count == 1

        # Reset for retry
        structure.geocoding_status = "pending"

        # Second attempt succeeds
        with patch.object(
            job.geocoding_service,
            "geocode_structure",
            return_value={"region_name": "Test"},
        ):
            success = await job._process_structure(structure, session)

        assert success
        assert structure.geocoding_status == "success"

    async def test_max_retries_exceeded(self, job):
        """Test that structures exceeding max retries are marked appropriately."""
        # Should not process structures that exceeded retries
        # This depends on implementation details
        # The job should filter these out in _get_pending_structures

    @patch("shared.jobs.geocoding_job_async.select")
    async def test_cleanup_old_cache(self, mock_select, job):
        """Test cleanup of old geocoding cache entries."""
        # Mock database session
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.rowcount = 10

        mock_session.execute.return_value = mock_result

        with patch.object(job, "_get_session", return_value=mock_session):
            deleted_count = await job.cleanup_old_cache(days=30)

        assert deleted_count == 10
        mock_session.commit.assert_called_once()

    async def test_concurrent_job_execution(self, job):
        """Test that multiple job instances don't interfere."""
        # This would test job locking/coordination in a real system
        # For now, we'll test that the job handles concurrent execution gracefully

        import asyncio

        # Mock to track concurrent executions
        concurrent_count = 0
        max_concurrent = 0

        async def mock_process_structure(structure, session):
            nonlocal concurrent_count, max_concurrent
            concurrent_count += 1
            max_concurrent = max(max_concurrent, concurrent_count)

            # Simulate processing time
            await asyncio.sleep(0.01)

            concurrent_count -= 1
            return True

        # Create multiple structures
        structures = [
            Mock(
                id=i,
                gps_lat=50.0 + i * 0.1,
                gps_lon=-110.0 + i * 0.1,
                geocoding_status="pending",
                retry_count=0,
            )
            for i in range(20)
        ]

        with patch.object(job, "_get_pending_structures", return_value=structures):
            with patch.object(
                job, "_process_structure", side_effect=mock_process_structure
            ):
                results = await job.run_job(limit=20)

        assert results["total_processed"] == 20
        # Check that batching limited concurrency
        assert max_concurrent <= job.batch_size


class TestGeocodingJobConfiguration:
    """Test geocoding job configuration and initialization."""

    def test_job_initialization_with_defaults(self):
        """Test job initialization with default parameters."""
        job = AsyncGeocodingJob(db_url="sqlite+aiosqlite:///test.db")

        assert job.batch_size == 50
        assert job.max_retries == 3
        assert job.geocoding_service is not None

    def test_job_initialization_with_custom_params(self):
        """Test job initialization with custom parameters."""
        job = AsyncGeocodingJob(
            db_url="sqlite+aiosqlite:///test.db", batch_size=100, max_retries=5
        )

        assert job.batch_size == 100
        assert job.max_retries == 5

    def test_invalid_database_url(self):
        """Test job initialization with invalid database URL."""
        with pytest.raises(ValueError):
            AsyncGeocodingJob(db_url="invalid://url")


@pytest.mark.asyncio
class TestGeocodingJobMonitoring:
    """Test monitoring and statistics for geocoding job."""

    async def test_job_statistics_tracking(self, job):
        """Test that job tracks statistics correctly."""
        # Mock structures with various outcomes
        mock_structures = [
            Mock(
                id=1,
                gps_lat=53.5,
                gps_lon=-113.5,
                geocoding_status="pending",
                retry_count=0,
            ),
            Mock(
                id=2,
                gps_lat=None,
                gps_lon=None,
                geocoding_status="pending",
                retry_count=0,
            ),
            Mock(
                id=3,
                gps_lat=51.0,
                gps_lon=-114.0,
                geocoding_status="pending",
                retry_count=1,
            ),
        ]

        with patch.object(job, "_get_pending_structures", return_value=mock_structures):
            with patch.object(
                job.geocoding_service,
                "geocode_structure",
                side_effect=[
                    {"region_name": "Test1"},  # Success
                    None,  # Failure
                    {"region_name": "Test2"},  # Success after retry
                ],
            ):
                results = await job.run_job()

        # Verify statistics
        assert results["total_processed"] == 3
        assert results["total_success"] == 2
        assert results["total_errors"] == 1
        assert "duration_seconds" in results
        assert results["duration_seconds"] >= 0

    async def test_job_progress_callback(self, job):
        """Test progress callback functionality if implemented."""
        progress_updates = []

        def progress_callback(current, total):
            progress_updates.append((current, total))

        # This would require the job to support progress callbacks
        # For now, we'll test the concept
        structures = [Mock(id=i) for i in range(10)]

        # Simulate progress updates
        for i, structure in enumerate(structures):
            progress_callback(i + 1, len(structures))

        assert len(progress_updates) == 10
        assert progress_updates[-1] == (10, 10)
