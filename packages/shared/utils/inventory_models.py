"""
Inventory Models - Domain models for inventory management.

This module contains value objects and domain models used throughout
the inventory management system.
"""

from dataclasses import dataclass, field
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional


class WorkOrderState(Enum):
    """Enumeration of work order states for inventory management"""

    QUOTE = "quote"
    WORK_ORDER_NOT_APPROVED = "work_order_not_approved"
    WORK_ORDER_APPROVED = "work_order_approved"


@dataclass
class InventoryRequirement:
    """
    Represents an inventory requirement for a work order.
    This is a value object that describes what inventory is needed.
    """

    part_id: int
    warehouse_id: int
    quantity: Decimal
    source_type: str  # "order_item" or "repair_item"
    source_id: int
    priority: int = 0
    warehouse_to_id: Optional[int] = None  # For transfers
    is_transfer_destination: bool = False  # True for the receiving end of a transfer
    linked_requirement_key: Optional[tuple] = (
        None  # Links source and destination requirements
    )

    def __hash__(self):
        """Make hashable for use in sets"""
        return hash((self.part_id, self.warehouse_id))

    def __eq__(self, other):
        """Equality based on part and warehouse"""
        if not isinstance(other, InventoryRequirement):
            return False
        return self.part_id == other.part_id and self.warehouse_id == other.warehouse_id


@dataclass
class StateTransition:
    """Represents a state transition"""

    from_state: Optional[WorkOrderState]
    to_state: WorkOrderState

    def __str__(self):
        return f"{self.from_state or 'NEW'} -> {self.to_state}"


@dataclass
class ValidationResult:
    """Result of a validation operation"""

    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

    @classmethod
    def combine(cls, results: List["ValidationResult"]) -> "ValidationResult":
        """Combine multiple validation results"""
        combined = cls(is_valid=True)
        for result in results:
            if not result.is_valid:
                combined.is_valid = False
            combined.errors.extend(result.errors)
            combined.warnings.extend(result.warnings)
        return combined


@dataclass
class InventoryResult:
    """
    Result of an inventory operation.
    Contains success status, affected entities, and any messages.
    """

    success: bool = True
    message: str = ""
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

    # Affected entities
    requirements: List[InventoryRequirement] = field(default_factory=list)
    reservations: List[Any] = field(
        default_factory=list
    )  # InventoryReservation objects
    movements: List[Any] = field(default_factory=list)  # InventoryMovement objects
    deleted_reservations: List[Any] = field(default_factory=list)
    deleted_movements: List[Any] = field(default_factory=list)

    # Additional context
    transition: Optional[StateTransition] = None
    reservation: Optional[Any] = None  # Single reservation for some operations
    movement: Optional[Any] = None  # Single movement for some operations

    def merge(self, other: "InventoryResult") -> None:
        """Merge another result into this one"""
        if not other.success:
            self.success = False

        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)

        # Merge entity lists
        self.requirements.extend(other.requirements)
        self.reservations.extend(other.reservations)
        self.movements.extend(other.movements)
        self.deleted_reservations.extend(other.deleted_reservations)
        self.deleted_movements.extend(other.deleted_movements)

        # Append messages if both have content
        if self.message and other.message:
            self.message = f"{self.message}; {other.message}"
        elif other.message:
            self.message = other.message

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "success": self.success,
            "message": self.message,
            "errors": self.errors,
            "warnings": self.warnings,
            "created_reservations": [r for r in self.reservations if hasattr(r, "id")],
            "created_movements": [m for m in self.movements if hasattr(m, "id")],
            "deleted_reservations": self.deleted_reservations,
            "deleted_movements": self.deleted_movements,
            "transition": str(self.transition) if self.transition else None,
        }
