# Historical Currency Exchange Rates System

This system provides **FREE** historical USD/CAD exchange rate data using official Bank of Canada sources.

## 🎯 What You Get

- **FREE historical data** back to ~1950
- **Official Bank of Canada rates** (most authoritative source)
- **Daily exchange rates** for USD/CAD
- **No API limits** or rate restrictions
- **CSV format** downloads

## 🚀 Quick Start

### 1. Create the Database Table
Run this SQL in pgAdmin:
```bash
psql -f /project/create_currencies_rates_table.sql
```

### 2. Download Historical Data

**Get last 30 days:**
```bash
python packages/shared/utils/bank_of_canada_historical_updater.py --days-back 30
```

**Get specific date range:**
```bash
python packages/shared/utils/bank_of_canada_historical_updater.py \
  --start-date 2020-01-01 \
  --end-date 2024-12-31
```

**Get ALL available history (1950+):**
```bash
python packages/shared/utils/bank_of_canada_historical_updater.py --all-history
```

**Note**: The script automatically uses your AWS RDS database from Flask app environment variables (USER_IJ, PASS_IJ, DB_IJ, etc.). You can override with `--database-url` if needed.

### 3. Set Up Daily Updates (Optional)
Add to cron for daily updates:
```bash
# Daily at 5 PM (after Bank of Canada publishes at 4:30 PM)
0 17 * * 1-5 python /path/to/bank_of_canada_historical_updater.py --days-back 1
```

## 📊 Data Sources

### Primary: Bank of Canada Valet API
- **URL**: `https://www.bankofcanada.ca/valet/observations/FXUSDCAD/csv`
- **Cost**: 100% FREE
- **History**: ~1950 to present
- **Frequency**: Daily (business days)
- **Authority**: Official central bank rates

### Alternative: FRED (Federal Reserve)
- **Series**: DEXCAUS (Canadian Dollars to U.S. Dollar Spot Exchange Rate)
- **URL**: `https://fred.stlouisfed.org/series/DEXCAUS`
- **History**: 1971 to present
- **Requires**: Free API key registration

## 🗄️ Database Schema

```sql
CREATE TABLE public.currencies_rates (
    id SERIAL PRIMARY KEY,
    currency_id INTEGER NOT NULL,          -- FK to currencies table
    rate_date DATE NOT NULL,               -- Date of exchange rate
    fx_rate_cad_per NUMERIC(10, 6) NOT NULL, -- CAD per unit of currency
    source VARCHAR(50) NOT NULL,           -- Data source
    UNIQUE (currency_id, rate_date)        -- One rate per currency per day
);
```

## 📈 Usage Examples

### Query Recent Rates
```sql
SELECT 
    c.name as currency,
    cr.rate_date,
    cr.fx_rate_cad_per as rate,
    cr.source
FROM currencies_rates cr
JOIN currencies c ON c.id = cr.currency_id
WHERE cr.rate_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY cr.rate_date DESC, c.name;
```

### Get Rate for Specific Date
```sql
SELECT fx_rate_cad_per 
FROM currencies_rates cr
JOIN currencies c ON c.id = cr.currency_id
WHERE c.name = 'USD' 
  AND cr.rate_date = '2024-01-15';
```

### Historical Rate Analysis
```sql
SELECT 
    DATE_TRUNC('month', rate_date) as month,
    AVG(fx_rate_cad_per) as avg_rate,
    MIN(fx_rate_cad_per) as min_rate,
    MAX(fx_rate_cad_per) as max_rate
FROM currencies_rates cr
JOIN currencies c ON c.id = cr.currency_id
WHERE c.name = 'USD'
  AND rate_date >= '2023-01-01'
GROUP BY DATE_TRUNC('month', rate_date)
ORDER BY month;
```

## 🔧 Technical Details

### Bank of Canada API Response Format
```csv
"TERMS AND CONDITIONS"
"https://www.bankofcanada.ca/terms/"

"SERIES"
"id","label","description"
"FXUSDCAD","USD/CAD","US dollar to Canadian dollar daily exchange rate"

"OBSERVATIONS"
"date","FXUSDCAD"
"2024-01-02","1.3316"
"2024-01-03","1.3288"
```

### Rate Interpretation
- **USD rate of 1.3500**: 1 USD = 1.35 CAD
- **CAD rate**: Always 1.0000 (base currency)
- **Business days only**: No weekend rates
- **Publication time**: 4:30 PM ET daily

## 🎯 Why This Approach?

✅ **FREE**: No API costs or limits  
✅ **AUTHORITATIVE**: Official central bank data  
✅ **HISTORICAL**: Complete data back to 1950s  
✅ **RELIABLE**: Government-maintained service  
✅ **SIMPLE**: Direct CSV download, no authentication  

## 🚨 Important Notes

1. **Business days only**: Rates not published on weekends/holidays
2. **CAD base**: All rates stored as CAD per unit of foreign currency  
3. **Daily updates**: Bank of Canada publishes new rates at 4:30 PM ET
4. **Terms of use**: Check Bank of Canada terms at https://www.bankofcanada.ca/terms/

## 🔄 Migration from API Services

If migrating from paid APIs like exchangerate-api.com:

1. Run the historical updater to backfill data
2. Update your application to query the local database instead of API
3. Set up daily cron job for ongoing updates
4. Cancel paid API subscriptions

This gives you better performance, no rate limits, and zero ongoing costs!