"""
Legacy adapter for old inventory management functions.

This module provides backward compatibility for code that still uses
the old inventory management functions. It adapts the calls to use
the new inventory manager.
"""

import logging
from typing import Dict, Any

from sqlalchemy.orm import Session

from shared.models.models_work_order import WorkOrder
from shared.utils.inventory_manager import (
    InventoryManager,
    get_work_order_state,
    WorkOrderState,
)

logger = logging.getLogger(__name__)


class InventoryError(Exception):
    """Legacy exception for inventory operations"""

    pass


def delete_work_order_movements(
    db_session: Session, work_order: WorkOrder, user_id: int, commit: bool = True
) -> Dict[str, Any]:
    """
    Legacy function to delete work order movements.
    Adapts to the new inventory manager.
    """
    logger.warning(
        "Using legacy delete_work_order_movements - consider updating to new inventory manager"
    )

    try:
        manager = InventoryManager(db_session)
        movement_manager = manager.strategies[
            WorkOrderState.WORK_ORDER_APPROVED
        ].movement_manager

        movements = movement_manager.get_work_order_movements(work_order.id)
        deleted_count = 0

        for movement in movements:
            result = movement_manager.delete_movement(movement, user_id)
            if result.success:
                deleted_count += 1

        if commit:
            db_session.commit()

        return {
            "success": True,
            "deleted_movements": deleted_count,
            "message": f"Deleted {deleted_count} movements",
        }

    except Exception as e:
        logger.exception("Error deleting work order movements")
        if commit:
            db_session.rollback()
        return {"success": False, "errors": [str(e)], "deleted_movements": 0}


def delete_work_order_reservations(
    db_session: Session, work_order: WorkOrder, user_id: int, commit: bool = True
) -> Dict[str, Any]:
    """
    Legacy function to delete work order reservations.
    Adapts to the new inventory manager.
    """
    logger.warning(
        "Using legacy delete_work_order_reservations - consider updating to new inventory manager"
    )

    try:
        manager = InventoryManager(db_session)
        reservation_manager = manager.strategies[
            WorkOrderState.WORK_ORDER_NOT_APPROVED
        ].reservation_manager

        reservations = reservation_manager.get_work_order_reservations(work_order.id)
        deleted_count = 0

        for reservation in reservations:
            result = reservation_manager.delete_reservation(reservation, user_id)
            if result.success:
                deleted_count += 1

        if commit:
            db_session.commit()

        return {
            "success": True,
            "deleted_reservations": deleted_count,
            "message": f"Deleted {deleted_count} reservations",
        }

    except Exception as e:
        logger.exception("Error deleting work order reservations")
        if commit:
            db_session.rollback()
        return {"success": False, "errors": [str(e)], "deleted_reservations": 0}


def handle_work_order_inventory_simple(
    db_session: Session,
    work_order: WorkOrder,
    old_is_quote: bool = None,
    old_invoice_approval_req: bool = None,
    new_is_quote: bool = None,
    new_invoice_approval_req: bool = None,
    user_id: int = None,
    commit: bool = True,
) -> Dict[str, Any]:
    """
    Legacy function for inventory operations.
    Adapts to the new inventory manager.
    """
    logger.warning(
        "Using legacy handle_work_order_inventory_simple - consider updating to new inventory manager"
    )

    try:
        manager = InventoryManager(db_session)

        # Determine states
        old_state = None
        if old_is_quote is not None:

            class OldWorkOrder:
                def __init__(self, is_quote, invoice_approval_req):
                    self.is_quote = is_quote
                    self.invoice_approval_req = invoice_approval_req

            old_wo = OldWorkOrder(old_is_quote, old_invoice_approval_req)
            old_state = get_work_order_state(old_wo)

        # Update work order
        if new_is_quote is not None:
            work_order.is_quote = new_is_quote
        if new_invoice_approval_req is not None:
            work_order.invoice_approval_req = new_invoice_approval_req

        new_state = get_work_order_state(work_order)

        # Handle state change
        result = manager.handle_state_change(
            work_order=work_order,
            old_state=old_state,
            new_state=new_state,
            user_id=user_id,
        )

        if commit and result.success:
            db_session.commit()
        elif commit and not result.success:
            db_session.rollback()

        # Convert to legacy format
        return result.to_dict()

    except Exception as e:
        logger.exception("Error in legacy inventory handler")
        if commit:
            db_session.rollback()
        return {"success": False, "errors": [str(e)]}
