from datetime import datetime, timedelta, timezone

# NOTE the below datetime functions are adapted from this <PERSON> blog post:
# https://blog.miguelgrinberg.com/post/it-s-time-for-a-change-datetime-utcnow-is-now-deprecated


def utcnow_aware() -> datetime:
    """Get the current time in UTC, with timezone info attached"""
    return datetime.now(timezone.utc)


def utcnow_naive() -> datetime:
    """Get the current time in UTC, without timezone info"""
    return utcnow_aware().replace(tzinfo=None)


def utcfromtimestamp_aware(timestamp: float) -> datetime:
    """Convert a timestamp to a UTC datetime object with timezone info attached"""
    return datetime.fromtimestamp(timestamp, timezone.utc)


def utcfromtimestamp_naive(timestamp: float) -> datetime:
    """Convert a timestamp to a UTC datetime object without timezone info"""
    return utcfromtimestamp_aware(timestamp).replace(tzinfo=None)


class FriendlyTime:
    """A class to represent a friendly time since an event."""

    def __init__(self, seconds: float):
        """Initialize the FriendlyTime object with the time since the event."""
        self.seconds = max(0.0, float(seconds))
        self.mins = round(self.seconds / 60, 1)
        self.hours = round(self.mins / 60, 1)
        self.days = round(self.hours / 24, 1)

    @property
    def elapsed_time(self) -> str:
        """Return a friendly string representation of the time since the event."""
        if self.seconds < 60:
            return f"{self.seconds} secs"
        elif self.mins < 60:
            return f"{self.mins} mins"
        elif self.hours < 24:
            return f"{self.hours} hours"
        else:
            return f"{self.days} days"

    @property
    def color_time_since(self) -> str:
        """Return the color for the time since the event."""
        if self.seconds < 60:
            return "success"
        elif self.mins < 16:
            return "warning"
        else:
            return "danger"

    @property
    def datetime_future(self) -> str:
        """Return the future datetime as a string."""
        return (utcnow_aware() + timedelta(seconds=self.seconds)).strftime(
            "%Y-%m-%d %H:%M:%S %Z"
        )
