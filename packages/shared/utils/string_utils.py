"""
Shared string utility functions.

Pure string manipulation functions that can be used across Flask and FastAPI applications.
"""

import random
import string
from typing import List


def generate_random_string(
    length: int = 8,
    characters: List[str] = [
        string.ascii_letters,
        string.ascii_uppercase,
        string.digits,
        # string.punctuation,
        r"[]()-— -{};:=<>_+^#$@!%*?&",
    ],
) -> str:
    """
    Generate a random string of letters and digits.

    Args:
        length: Length of the random string to generate
        characters: List of character sets to cycle through

    Returns:
        Random string of specified length
    """
    # Generate the random string
    counter: int = 0
    random_string: str = ""
    for _ in range(length):
        random_string += random.choice(characters[counter])
        counter += 1
        if counter == len(characters):
            counter = 0

    return random_string


def generate_simple_random_string(length: int = 8) -> str:
    """
    Generate a simple random string with just letters and digits.

    Args:
        length: Length of the random string

    Returns:
        Random alphanumeric string
    """
    characters = string.ascii_letters + string.digits
    return "".join(random.choice(characters) for _ in range(length))


def generate_confirmation_code(length: int = 6) -> str:
    """
    Generate a numeric confirmation code.

    Args:
        length: Length of the confirmation code

    Returns:
        Random numeric string
    """
    return "".join(random.choice(string.digits) for _ in range(length))


def clean_phone_number(phone: str) -> str:
    """
    Clean a phone number by removing all non-digit characters.

    Args:
        phone: Raw phone number string

    Returns:
        Cleaned phone number with only digits
    """
    return "".join(filter(str.isdigit, phone))


def format_phone_number(phone: str, format_type: str = "north_american") -> str:
    """
    Format a phone number according to specified format.

    Args:
        phone: Raw phone number string
        format_type: Format type ("north_american", "international", etc.)

    Returns:
        Formatted phone number
    """
    cleaned = clean_phone_number(phone)

    if format_type == "north_american" and len(cleaned) == 10:
        return f"({cleaned[:3]}) {cleaned[3:6]}-{cleaned[6:]}"
    elif format_type == "north_american" and len(cleaned) == 11:
        return f"+{cleaned[0]} ({cleaned[1:4]}) {cleaned[4:7]}-{cleaned[7:]}"
    else:
        return cleaned


def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """
    Truncate a string to a maximum length with optional suffix.

    Args:
        text: String to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to append to truncated string

    Returns:
        Truncated string with suffix if needed
    """
    if len(text) <= max_length:
        return text

    return text[: max_length - len(suffix)] + suffix


def camel_to_snake_case(camel_str: str) -> str:
    """
    Convert camelCase to snake_case.

    Args:
        camel_str: String in camelCase

    Returns:
        String in snake_case
    """
    import re

    # Insert an underscore before any uppercase letter that follows a lowercase letter
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", camel_str)
    # Insert an underscore before any uppercase letter that follows a lowercase letter or digit
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()


def snake_to_camel_case(snake_str: str) -> str:
    """
    Convert snake_case to camelCase.

    Args:
        snake_str: String in snake_case

    Returns:
        String in camelCase
    """
    components = snake_str.split("_")
    return components[0] + "".join(word.capitalize() for word in components[1:])


def extract_numbers_from_string(text: str) -> List[float]:
    """
    Extract all numbers from a string.

    Args:
        text: String containing numbers

    Returns:
        List of numbers found in the string
    """
    import re

    number_pattern = r"-?\d+\.?\d*"
    matches = re.findall(number_pattern, text)
    return [float(match) for match in matches if match]


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename by removing/replacing invalid characters.

    Args:
        filename: Raw filename

    Returns:
        Sanitized filename safe for filesystem use
    """
    import re

    # Replace invalid characters with underscores
    sanitized = re.sub(r'[<>:"/\\|?*]', "_", filename)

    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip(". ")

    # Ensure filename is not empty
    if not sanitized:
        sanitized = "unnamed_file"

    return sanitized
