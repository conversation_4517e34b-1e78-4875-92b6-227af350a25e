"""
Shared datetime utility functions.

Pure datetime functions that can be used across Flask and FastAPI applications.
These functions replace deprecated datetime.utcnow() calls.

NOTE: Adapted from <PERSON> blog post:
https://blog.miguelgrinberg.com/post/it-s-time-for-a-change-datetime-utcnow-is-now-deprecated
"""

from datetime import datetime, timedelta, timezone


def utcnow_aware() -> datetime:
    """Get the current time in UTC, with timezone info attached"""
    return datetime.now(timezone.utc)


def utcnow_naive() -> datetime:
    """Get the current time in UTC, without timezone info"""
    return utcnow_aware().replace(tzinfo=None)


def utcfromtimestamp_aware(timestamp: float) -> datetime:
    """Convert a timestamp to a UTC datetime object with timezone info attached"""
    return datetime.fromtimestamp(timestamp, timezone.utc)


def utcfromtimestamp_naive(timestamp: float) -> datetime:
    """Convert a timestamp to a UTC datetime object without timezone info"""
    return utcfromtimestamp_aware(timestamp).replace(tzinfo=None)


class FriendlyTime:
    """A class to represent a friendly time since an event."""

    def __init__(self, seconds: float):
        """Initialize the FriendlyTime object with the time since the event."""
        self.seconds = max(0.0, float(seconds))
        self.mins = round(self.seconds / 60, 1)
        self.hours = round(self.mins / 60, 1)
        self.days = round(self.hours / 24, 1)

    @property
    def elapsed_time(self) -> str:
        """Return a friendly string representation of the time since the event."""
        if self.seconds < 60:
            return f"{self.seconds} secs"
        elif self.mins < 60:
            return f"{self.mins} mins"
        elif self.hours < 24:
            return f"{self.hours} hours"
        else:
            return f"{self.days} days"

    @property
    def color_time_since(self) -> str:
        """Return the color for the time since the event."""
        if self.seconds < 60:
            return "success"
        elif self.mins < 16:
            return "warning"
        else:
            return "danger"

    @property
    def datetime_future(self) -> str:
        """Return the future datetime as a string."""
        return (utcnow_aware() + timedelta(seconds=self.seconds)).strftime(
            "%Y-%m-%d %H:%M:%S %Z"
        )

    def __str__(self) -> str:
        """Return a string representation of the FriendlyTime object."""
        return self.elapsed_time


def is_within_business_hours(
    dt: datetime, start_hour: int = 8, end_hour: int = 17
) -> bool:
    """
    Check if a datetime falls within business hours.

    Args:
        dt: The datetime to check
        start_hour: Business start hour (default 8 AM)
        end_hour: Business end hour (default 5 PM)

    Returns:
        True if within business hours, False otherwise
    """
    return start_hour <= dt.hour < end_hour and dt.weekday() < 5  # Mon-Fri


def get_time_until_next_business_day(dt: datetime) -> timedelta:
    """
    Get the time until the next business day (Mon-Fri).

    Args:
        dt: The current datetime

    Returns:
        Timedelta until next business day
    """
    # If it's Friday after hours, wait until Monday
    if dt.weekday() == 4 and dt.hour >= 17:  # Friday
        days_until_monday = 3
        next_business = dt.replace(
            hour=8, minute=0, second=0, microsecond=0
        ) + timedelta(days=days_until_monday)

    # If it's weekend, wait until Monday
    elif dt.weekday() >= 5:  # Saturday or Sunday
        days_until_monday = 7 - dt.weekday()
        next_business = dt.replace(
            hour=8, minute=0, second=0, microsecond=0
        ) + timedelta(days=days_until_monday)

    # If it's a weekday but after hours, wait until tomorrow
    elif dt.hour >= 17:
        next_business = dt.replace(
            hour=8, minute=0, second=0, microsecond=0
        ) + timedelta(days=1)

    # If it's a weekday during business hours, return zero
    else:
        return timedelta(0)

    return next_business - dt
