#!/usr/bin/env python3
"""
Currency Exchange Rate Updater

A DRY and simple utility to download historical currency exchange rates
and store them in the database.

Usage:
    # Update rates for today
    python currency_rate_updater.py

    # Update rates for a specific date (stores current rates with that date)
    python currency_rate_updater.py --date 2024-01-01

    # Use different base currency
    python currency_rate_updater.py --base-currency EUR

API: exchangerate-api.com (1,500 free requests/month)
Endpoint: https://api.exchangerate-api.com/v4/latest/{base_currency}
Note: Free tier only supports current rates. Historical data requires paid plan.
"""

import argparse
import logging
import sys
from datetime import date, timedelta
from typing import Dict, List, Optional

import requests
from sqlalchemy import create_engine
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, sessionmaker

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CurrencyRateAPI:
    """Handles API communication with exchangerate-api.com"""

    BASE_URL = "https://api.exchangerate-api.com/v4"

    def __init__(self, timeout: int = 10):
        self.timeout = timeout
        self.session = requests.Session()
        # Set user agent to be polite
        self.session.headers.update(
            {"User-Agent": "IJack-Currency-Updater/1.0 (<EMAIL>)"}
        )

    def get_current_rates(self, base_currency: str) -> Optional[Dict]:
        """
        Fetch current exchange rates.

        Args:
            base_currency: The base currency code (e.g., 'USD', 'EUR')

        Returns:
            Dict with rates and date or None if failed
        """
        url = f"{self.BASE_URL}/latest/{base_currency}"

        try:
            logger.debug(f"Fetching current rates for {base_currency}")
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()

            data = response.json()

            # Validate response structure
            if "rates" not in data:
                logger.error(f"Invalid API response for {base_currency}: {data}")
                return None

            return {
                "rates": data.get("rates", {}),
                "date": data.get("date", date.today().isoformat()),
            }

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch rates for {base_currency}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching rates for {base_currency}: {e}")
            return None


class CurrencyRateManager:
    """Handles database operations for currency rates"""

    def __init__(self, session: Session):
        self.session = session

    def get_currencies(self) -> List:
        """Get all currencies from the database"""
        from shared.models.models import Currency

        return self.session.query(Currency).all()

    def store_rates(
        self,
        currency_id: int,
        target_date: date,
        cad_rate: float,
        source: str = "exchangerate-api.com",
    ) -> bool:
        """
        Store a currency rate in the database.

        Args:
            currency_id: ID of the currency
            target_date: Date of the rate
            cad_rate: Exchange rate (CAD per currency unit)
            source: Data source name

        Returns:
            True if stored successfully, False otherwise
        """
        from shared.models.models import CurrencyRate

        try:
            # Check if rate already exists
            existing = (
                self.session.query(CurrencyRate)
                .filter_by(currency_id=currency_id, rate_date=target_date)
                .first()
            )

            if existing:
                # Update existing rate
                existing.fx_rate_cad_per = cad_rate
                existing.source = source
                logger.debug(
                    f"Updated existing rate for currency {currency_id} on {target_date}"
                )
            else:
                # Create new rate
                new_rate = CurrencyRate(
                    currency_id=currency_id,
                    rate_date=target_date,
                    fx_rate_cad_per=cad_rate,
                    source=source,
                )
                self.session.add(new_rate)
                logger.debug(
                    f"Added new rate for currency {currency_id} on {target_date}"
                )

            self.session.commit()
            return True

        except IntegrityError as e:
            logger.error(f"Database integrity error storing rate: {e}")
            self.session.rollback()
            return False
        except Exception as e:
            logger.error(f"Unexpected error storing rate: {e}")
            self.session.rollback()
            return False

    def get_missing_dates(
        self, currency_id: int, start_date: date, end_date: date
    ) -> List[date]:
        """Get dates that are missing rates for a currency"""
        from shared.models.models import CurrencyRate

        existing_dates = set(
            row[0]
            for row in self.session.query(CurrencyRate.rate_date)
            .filter_by(currency_id=currency_id)
            .filter(CurrencyRate.rate_date.between(start_date, end_date))
            .all()
        )

        all_dates = set()
        current_date = start_date
        while current_date <= end_date:
            all_dates.add(current_date)
            current_date += timedelta(days=1)

        return sorted(all_dates - existing_dates)


def calculate_cad_rate(base_currency: str, rates: Dict[str, float]) -> Optional[float]:
    """
    Calculate CAD rate from API response.

    The API returns rates where 1 unit of base_currency = X units of target currency.
    We need CAD per unit of the currency we're storing.

    Args:
        base_currency: The base currency of the API response
        rates: Dictionary of currency rates from API

    Returns:
        CAD rate or None if can't calculate
    """
    if "CAD" not in rates:
        logger.warning(f"CAD rate not found in response for base {base_currency}")
        return None

    cad_rate = rates["CAD"]

    # If the base currency is CAD, we need the inverse
    if base_currency == "CAD":
        return 1.0

    # For other currencies, the rate is already CAD per unit of base currency
    return float(cad_rate)


def update_currency_rates(
    database_url: str, rate_date: Optional[date] = None, base_currency: str = "USD"
) -> None:
    """
    Main function to update currency rates.

    Note: Free tier API only supports current rates, not historical data.

    Args:
        database_url: Database connection string
        rate_date: Date to store the rates for (default: today)
        base_currency: Base currency for API calls (default: USD)
    """
    if rate_date is None:
        rate_date = date.today()

    logger.info(f"Updating currency rates for {rate_date}")

    # Initialize components
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(bind=engine)
    session = SessionLocal()

    api = CurrencyRateAPI()
    manager = CurrencyRateManager(session)

    try:
        # Get all currencies
        currencies = manager.get_currencies()
        logger.info(f"Found {len(currencies)} currencies to update")

        total_updated = 0
        total_errors = 0

        # Fetch current rates
        logger.info("Fetching current rates")
        rate_data = api.get_current_rates(base_currency)
        if not rate_data:
            logger.error("Failed to fetch current rates")
            return

        rates = rate_data["rates"]
        api_date = rate_data["date"]
        logger.info(f"Received rates for {api_date} (storing as {rate_date})")

        # Process each currency
        for currency in currencies:
            # Calculate CAD rate for this currency
            cad_rate = None

            if currency.name == "CAD":
                cad_rate = 1.0
            elif currency.name == base_currency:
                # Base currency: calculate from CAD rate
                if "CAD" in rates:
                    cad_rate = rates["CAD"]
                else:
                    logger.warning("CAD rate not found in API response")
                    continue
            elif currency.name in rates:
                # Other currency: convert from base currency to CAD per currency unit
                if base_currency == "CAD":
                    # If base is CAD, rate is direct CAD per currency unit
                    cad_rate = 1.0 / rates[currency.name]
                else:
                    # If base is not CAD, we need to cross-calculate
                    if "CAD" in rates:
                        currency_per_base = rates[currency.name]
                        cad_per_base = rates["CAD"]
                        cad_rate = cad_per_base / currency_per_base
                    else:
                        logger.warning("CAD rate not found for cross-calculation")
                        continue
            else:
                logger.warning(f"Currency {currency.name} not found in API response")
                continue

            if cad_rate is not None:
                if manager.store_rates(currency.id, rate_date, cad_rate):
                    total_updated += 1
                    logger.debug(f"Stored rate for {currency.name}: {cad_rate} CAD")
                else:
                    total_errors += 1
            else:
                logger.warning(f"Could not calculate CAD rate for {currency.name}")
                total_errors += 1

        logger.info(
            f"Update complete. Updated: {total_updated}, Errors: {total_errors}"
        )

    except Exception as e:
        logger.error(f"Fatal error during update: {e}")
        raise
    finally:
        session.close()


def main():
    """Command line interface"""
    parser = argparse.ArgumentParser(
        description="Update currency exchange rates (current rates only)"
    )
    parser.add_argument(
        "--date",
        type=date.fromisoformat,
        help="Date to store rates for (YYYY-MM-DD, default: today)",
    )
    parser.add_argument(
        "--database-url", help="Database URL (default: from environment)"
    )
    parser.add_argument(
        "--base-currency",
        default="USD",
        help="Base currency for API calls (default: USD)",
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Get database URL
    database_url = args.database_url
    if not database_url:
        import os

        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            logger.error(
                "Database URL not provided. Use --database-url or set DATABASE_URL environment variable"
            )
            sys.exit(1)

    try:
        update_currency_rates(
            database_url=database_url,
            rate_date=args.date,
            base_currency=args.base_currency,
        )
    except Exception as e:
        logger.error(f"Update failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
