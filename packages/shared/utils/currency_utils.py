"""
Currency conversion utilities for service analytics.

This module provides reusable functions to convert costs from various currencies
to CAD using average monthly exchange rates from the currency_rates table.
"""

from sqlalchemy import case, func, select

from shared.config import CURRENCY_ID_CAD
from shared.models.models import Currency, CurrencyRate


def get_monthly_avg_exchange_rate_cte():
    """
    Returns a CTE that calculates average monthly exchange rates.

    This CTE groups currency rates by currency_id and month, calculating
    the average exchange rate for each currency per month. This provides
    more accurate conversion rates than using static rates.

    Returns:
        CTE with columns:
        - currency_id: ID of the currency
        - month: Month (truncated to first of month)
        - avg_monthly_rate: Average exchange rate for that month
    """
    month_trunc = func.date_trunc("month", CurrencyRate.rate_date).label("month")

    return (
        select(
            CurrencyRate.currency_id,
            month_trunc,
            func.avg(CurrencyRate.fx_rate_cad_per).label("avg_monthly_rate"),
        )
        .select_from(CurrencyRate)
        .group_by(CurrencyRate.currency_id, month_trunc)
    ).cte("monthly_rates")


def get_currency_conversion_expression(
    cost_field, work_order_date, currency_id, monthly_rates_cte=None
):
    """
    Returns a SQL expression that converts any cost field to CAD.

    The conversion logic:
    1. If currency is CAD (CURRENCY_ID_CAD), return cost as-is
    2. Otherwise, multiply cost by exchange rate:
       - Prefer average monthly rate from monthly_rates_cte if available
       - Fall back to static rate from Currency.fx_rate_cad_per

    Args:
        cost_field: The cost column/expression to convert (e.g., WorkOrderPart.cost_before_tax)
        work_order_date: The date field to match against currency rates (e.g., WorkOrder.date_service)
        currency_id: The currency ID field (e.g., WorkOrder.currency_id)
        monthly_rates_cte: Optional CTE from get_monthly_avg_exchange_rate_cte()

    Returns:
        SQLAlchemy expression that converts the cost to CAD
    """
    if monthly_rates_cte is not None:
        # Use monthly average rate if available
        conversion_rate = func.coalesce(
            monthly_rates_cte.c.avg_monthly_rate,  # Prefer monthly average
            Currency.fx_rate_cad_per,  # Fallback to static rate
        )
    else:
        # Use static rate from Currency table
        conversion_rate = Currency.fx_rate_cad_per

    return case(
        # If CAD currency, no conversion needed
        (currency_id == CURRENCY_ID_CAD, cost_field),
        # Otherwise, convert using exchange rate
        else_=(cost_field * conversion_rate),
    )


def add_currency_conversion_joins(query, monthly_rates_cte=None):
    """
    Adds necessary joins for currency conversion to an existing query.

    This function assumes the query already has WorkOrder joined and adds:
    1. Currency table join
    2. Optional monthly rates CTE join

    Args:
        query: SQLAlchemy query object
        monthly_rates_cte: Optional CTE from get_monthly_avg_exchange_rate_cte()

    Returns:
        Modified query with currency conversion joins added
    """
    # Join Currency table
    query = query.join(
        Currency,
        Currency.id == func.coalesce(Currency.id, CURRENCY_ID_CAD),
        isouter=True,
    )

    if monthly_rates_cte is not None:
        # Join monthly rates CTE on currency_id and month match
        query = query.join(
            monthly_rates_cte,
            (monthly_rates_cte.c.currency_id == Currency.id)
            & (
                monthly_rates_cte.c.month == func.date_trunc("month", Currency.id)
            ),  # This will be overridden in actual usage
            isouter=True,
        )

    return query


def convert_cost_to_cad_simple(cost_field, currency_id):
    """
    Simple currency conversion using static rates only.

    This is a simpler version that doesn't require date-based monthly averages.
    Use this when you only need basic currency conversion or when work order
    dates are not available in the query.

    Args:
        cost_field: The cost column/expression to convert
        currency_id: The currency ID field

    Returns:
        SQLAlchemy expression that converts the cost to CAD using static rates
    """
    return case(
        # If CAD currency, no conversion needed
        (currency_id == CURRENCY_ID_CAD, cost_field),
        # Otherwise, convert using static rate from Currency table
        else_=(cost_field * Currency.fx_rate_cad_per),
    )
