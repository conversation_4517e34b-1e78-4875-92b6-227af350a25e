"""
Shared email utility for both Flask and FastAPI applications.
Provides email sending functionality using Mailgun API.
"""

import os
from datetime import datetime
from typing import Any, Dict, List

import requests


class EmailService:
    """Shared email service that can be used by both Flask and FastAPI"""

    def __init__(
        self, mailgun_api_key: str = None, mailgun_domain: str = "myijack.com"
    ):
        self.mailgun_api_key = mailgun_api_key or os.environ.get("MAILGUN_API_KEY")
        self.mailgun_domain = mailgun_domain
        self.mailgun_url = f"https://api.mailgun.net/v3/{mailgun_domain}/messages"

        # Default admin emails - can be overridden
        self.admin_emails = [
            "<EMAIL>",
            # "<EMAIL>",
            # "<EMAIL>"
        ]

    def send_email(
        self,
        subject: str,
        text_body: str,
        to_emails: List[str] = None,
        sender: str = "RCOM Website <<EMAIL>>",
        cc_emails: List[str] = None,
        html_body: str = None,
        files_list: List[tuple] = None,
        testing: bool = False,
        send_all_together: bool = False,
    ) -> requests.Response:
        """
        Send email using Mailgun API

        Args:
            subject: Email subject
            text_body: Plain text email body
            to_emails: List of recipient emails, defaults to admin emails
            sender: From email address
            cc_emails: List of CC emails
            html_body: HTML email body
            files_list: List of file attachments in format [("attachment", (filename, file_binary))]
            testing: If True, only send to development emails
            send_all_together: If True, send one email to all recipients; if False, send individual emails

        Returns:
            requests.Response from Mailgun API
        """

        if not self.mailgun_api_key:
            raise ValueError("MAILGUN_API_KEY environment variable not set")

        # Use admin emails as default
        if to_emails is None:
            to_emails = self.admin_emails.copy()

        # In development/testing, override recipients
        environment = os.getenv("FLASK_CONFIG", "development")
        if testing or environment in ("development", "testing", "wsl"):
            to_emails = ["<EMAIL>"]
            cc_emails = []

        # Initialize cc_emails if None
        if cc_emails is None:
            cc_emails = []

        data = {
            "h:sender": sender,
            "from": sender,
            "subject": subject,
            "text": text_body,
        }

        # Add HTML body if provided
        if html_body:
            data["html"] = html_body

        response = None

        if send_all_together:
            # Send a single email to all recipients
            data["to"] = to_emails
            if cc_emails:
                data["cc"] = cc_emails

            response = requests.post(
                self.mailgun_url,
                auth=("api", self.mailgun_api_key),
                files=files_list,
                data=data,
            )
        else:
            # Send individual emails to each recipient
            all_emails = list(to_emails) + list(cc_emails)
            for recipient in all_emails:
                response = requests.post(
                    self.mailgun_url,
                    auth=("api", self.mailgun_api_key),
                    files=files_list,
                    data={
                        "h:sender": sender,
                        "from": sender,
                        "to": recipient,
                        "subject": subject,
                        "text": text_body,
                        "html": html_body,
                    },
                )

        return response

    def send_error_report_email(
        self,
        error_details: Dict[str, Any],
        user_info: Dict[str, Any] = None,
        additional_context: Dict[str, Any] = None,
    ) -> requests.Response:
        """
        Send an error report email to IJACK support with detailed error information

        Args:
            error_details: Dictionary containing error information (message, stack, etc.)
            user_info: User information if available
            additional_context: Additional context like URL, user agent, etc.

        Returns:
            requests.Response from Mailgun API
        """

        timestamp = datetime.now().isoformat()

        # Create subject
        error_type = error_details.get("name", "Unknown Error")
        subject = f"🚨 Production Error Report: {error_type} - {timestamp}"

        # Create detailed text body for support
        text_body = f"""
IJACK Application Error Report
=============================

Timestamp: {timestamp}
Environment: {os.getenv("FLASK_CONFIG", "unknown")}

ERROR DETAILS:
--------------
Type: {error_details.get("name", "Unknown")}
Message: {error_details.get("message", "No message")}

Stack Trace:
{error_details.get("stack", "No stack trace available")}

USER INFORMATION:
-----------------
"""

        if user_info:
            text_body += f"""
User ID: {user_info.get("id", "Unknown")}
User Email: {user_info.get("email", "Unknown")}
User Name: {user_info.get("name", "Unknown")}
User Roles: {user_info.get("roles", "Unknown")}
"""
        else:
            text_body += "No user information available (user not logged in)"

        text_body += """

ADDITIONAL CONTEXT:
------------------
"""

        if additional_context:
            for key, value in additional_context.items():
                text_body += f"{key}: {value}\n"
        else:
            text_body += "No additional context provided"

        text_body += """

RECOMMENDED ACTIONS:
-------------------
1. Check application logs for related errors
2. Review the stack trace for the root cause
3. Test the specific functionality that caused the error
4. Consider if this requires immediate attention based on error frequency

This error report was automatically generated by the application's error handling system.
"""

        # Create HTML body (optional, formatted version)
        html_body = f"""
<html>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 800px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #d32f2f; border-bottom: 2px solid #d32f2f; padding-bottom: 10px;">
            🚨 IJACK Application Error Report
        </h1>
        
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <strong>Timestamp:</strong> {timestamp}<br>
            <strong>Environment:</strong> {os.getenv("FLASK_CONFIG", "unknown")}<br>
        </div>
        
        <h2 style="color: #1976d2;">Error Details</h2>
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;">
            <strong>Type:</strong> {error_details.get("name", "Unknown")}<br>
            <strong>Message:</strong> {error_details.get("message", "No message")}<br>
        </div>
        
        <h3>Stack Trace:</h3>
        <pre style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;">
{error_details.get("stack", "No stack trace available")}
        </pre>
        
        <h2 style="color: #1976d2;">User Information</h2>
        <div style="background-color: #e3f2fd; border: 1px solid #90caf9; padding: 15px; border-radius: 5px;">
"""

        if user_info:
            html_body += f"""
            <strong>User ID:</strong> {user_info.get("id", "Unknown")}<br>
            <strong>Email:</strong> {user_info.get("email", "Unknown")}<br>
            <strong>Name:</strong> {user_info.get("name", "Unknown")}<br>
            <strong>Roles:</strong> {user_info.get("roles", "Unknown")}<br>
"""
        else:
            html_body += "No user information available (user not logged in)"

        html_body += """
        </div>
        
        <h2 style="color: #1976d2;">Additional Context</h2>
        <div style="background-color: #f3e5f5; border: 1px solid #ce93d8; padding: 15px; border-radius: 5px;">
"""

        if additional_context:
            for key, value in additional_context.items():
                html_body += f"<strong>{key}:</strong> {value}<br>"
        else:
            html_body += "No additional context provided"

        html_body += """
        </div>
        
        <h2 style="color: #1976d2;">Recommended Actions</h2>
        <ol style="background-color: #e8f5e8; border: 1px solid #a5d6a7; padding: 15px; border-radius: 5px;">
            <li>Check application logs for related errors</li>
            <li>Review the stack trace for the root cause</li>
            <li>Test the specific functionality that caused the error</li>
            <li>Consider if this requires immediate attention based on error frequency</li>
        </ol>
        
        <p style="font-style: italic; color: #666; margin-top: 30px;">
            This error report was automatically generated by the application's error handling system.
        </p>
    </div>
</body>
</html>
"""

        return self.send_email(
            subject=subject,
            text_body=text_body,
            html_body=html_body,
            to_emails=self.admin_emails,
            sender="IJACK Error Reporter <<EMAIL>>",
            send_all_together=True,
        )


# Global instance
email_service = EmailService()


# Convenience functions for backward compatibility
def send_email(*args, **kwargs) -> requests.Response:
    """Backward compatibility function"""
    return email_service.send_email(*args, **kwargs)


def send_error_report_email(*args, **kwargs) -> requests.Response:
    """Send error report email to support"""
    return email_service.send_error_report_email(*args, **kwargs)
