"""
Inventory Manager - Core orchestrator for all inventory operations.

This module implements a clean, state-based approach to inventory management
using strategy pattern and domain-driven design principles.
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from shared.models.models_bom import (
    InventoryLedger,
    InventoryLedgerOperationType,
    InventoryMovement,
    InventoryReservation,
    MovementType,
    ReservationStatus,
    Warehouse,
    WarehousePart,
)
from shared.models.models_work_order import WorkOrder
from shared.utils.inventory_ledger_manager import InventoryLedgerManager
from shared.utils.inventory_models import (
    InventoryRequirement,
    InventoryResult,
    StateTransition,
    ValidationResult,
    WorkOrderState,
)

logger = logging.getLogger(__name__)


class InventoryError(Exception):
    """Base exception for inventory operations"""

    pass


class InvalidStateTransition(InventoryError):
    """Raised when an invalid state transition is attempted"""

    pass


class InventoryNotAvailable(InventoryError):
    """Raised when required inventory is not available"""

    pass


class InventoryManager:
    """
    Central orchestrator for all inventory operations.
    Uses strategy pattern for different operation types.
    """

    def __init__(self, session: Session):
        self.session = session
        self.ledger = InventoryLedgerManager()
        self.strategies = self._initialize_strategies()
        self.validator = StateTransitionValidator()

    def _initialize_strategies(self) -> Dict[WorkOrderState, "InventoryStrategy"]:
        """Initialize strategies for each work order state"""
        return {
            WorkOrderState.QUOTE: QuoteInventoryStrategy(self.session, self.ledger),
            WorkOrderState.WORK_ORDER_NOT_APPROVED: ReservationOnlyStrategy(
                self.session, self.ledger
            ),
            WorkOrderState.WORK_ORDER_APPROVED: ReservationAndMovementStrategy(
                self.session, self.ledger
            ),
        }

    def handle_state_change(
        self,
        work_order: WorkOrder,
        old_state: Optional[WorkOrderState],
        new_state: WorkOrderState,
        user_id: int,
    ) -> InventoryResult:
        """
        Main entry point for all inventory operations.
        Handles state transitions and delegates to appropriate strategy.
        """
        logger.info(
            f"Handling inventory state change for WO {work_order.id}: {old_state} -> {new_state}"
        )

        # Create transition object
        transition = StateTransition(old_state, new_state)

        # Validate transition
        if not self.validator.validate_transition(old_state, new_state):
            return InventoryResult(
                success=False,
                errors=[f"Invalid state transition: {old_state} -> {new_state}"],
            )

        try:
            # Don't use nested transactions - let the caller manage the transaction
            # 1. Clean up old state if exists and state is changing
            if old_state and old_state != new_state:
                cleanup_result = self._cleanup_old_state(work_order, old_state, user_id)
                if not cleanup_result.success:
                    return cleanup_result

            # 2. Execute strategy for new state
            strategy = self.strategies[new_state]
            result = strategy.execute(work_order, user_id)

            # 3. Validate end state
            validation = self.validator.validate_end_state(work_order, new_state)
            if not validation.is_valid:
                result.success = False
                result.errors.extend(validation.errors)
                return result

            # 4. Record state transition in result
            result.transition = transition

            logger.info(
                f"Successfully completed inventory state change for WO {work_order.id}"
            )
            return result

        except Exception as e:
            logger.exception(
                f"Error handling inventory state change for WO {work_order.id}"
            )
            return InventoryResult(
                success=False,
                errors=[f"Unexpected error: {str(e)}"],
                transition=transition,
            )

    def _cleanup_old_state(
        self, work_order: WorkOrder, old_state: WorkOrderState, user_id: int
    ) -> InventoryResult:
        """Clean up inventory from old state before transitioning"""
        logger.debug(f"Cleaning up old state {old_state} for WO {work_order.id}")

        # Get cleanup strategy for old state
        cleanup_strategy = self.strategies.get(old_state)
        if cleanup_strategy:
            return cleanup_strategy.cleanup(work_order, user_id)

        return InventoryResult(success=True)

    def create_manual_adjustment(
        self,
        part_id: int,
        warehouse_id: int,
        new_quantity: Decimal,
        user_id: int,
        reason: Optional[str] = None,
        adjustment_type: Optional[str] = None,
    ) -> InventoryResult:
        """
        Create a manual inventory adjustment with proper tracking.

        This method handles the common case of manually adjusting inventory levels,
        creating all necessary records for audit trail and ledger tracking.

        Args:
            part_id: ID of the part to adjust
            warehouse_id: ID of the warehouse
            new_quantity: The new total quantity (not a delta)
            user_id: ID of the user making the adjustment
            reason: Optional reason for the adjustment
            adjustment_type: Optional type (e.g., 'cycle_count', 'correction', 'damage')

        Returns:
            InventoryResult with success status and created records
        """
        result = InventoryResult(success=True)

        try:
            # Get current warehouse part or create if doesn't exist
            warehouse_part = (
                self.session.query(WarehousePart)
                .filter(
                    WarehousePart.warehouse_id == warehouse_id,
                    WarehousePart.part_id == part_id,
                )
                .first()
            )

            if not warehouse_part:
                # Create warehouse part if it doesn't exist
                warehouse_part = WarehousePart(
                    warehouse_id=warehouse_id,
                    part_id=part_id,
                    quantity=Decimal("0"),
                    quantity_reserved=Decimal("0"),
                )
                self.session.add(warehouse_part)
                self.session.flush()

            # Calculate the adjustment delta
            current_quantity = warehouse_part.quantity
            adjustment_delta = new_quantity - current_quantity

            if adjustment_delta == 0:
                result.message = "No adjustment needed - quantity unchanged"
                return result

            # Check warehouse negative stock policy if the adjustment would result in negative quantity
            if new_quantity < 0:
                allows_negative = warehouse_allows_negative_stock(
                    self.session, warehouse_id
                )
                if not allows_negative:
                    result.success = False
                    result.errors.append(
                        f"Manual adjustment would result in negative inventory ({new_quantity}) "
                        f"for part {part_id} in warehouse {warehouse_id}, "
                        f"but warehouse does not allow negative stock"
                    )
                    return result
                else:
                    result.warnings.append(
                        f"Manual adjustment creating negative inventory ({new_quantity}) "
                        f"for part {part_id} in warehouse {warehouse_id} "
                        f"(warehouse allows negative stock)"
                    )

            # Create the movement record using MovementManager
            movement_manager = MovementManager(self.session, self.ledger)
            movement = InventoryMovement(
                movement_number=movement_manager._generate_movement_number(),
                movement_type=MovementType.ADJUSTMENT,
                part_id=part_id,
                from_warehouse_id=warehouse_id if adjustment_delta < 0 else None,
                to_warehouse_id=warehouse_id if adjustment_delta > 0 else None,
                quantity=abs(adjustment_delta),
                reference_type="manual_adjustment",
                reference_id=None,  # No specific reference for manual adjustments
                created_by_id=user_id,
                notes=f"Manual adjustment: {reason or 'No reason provided'}. "
                f"Type: {adjustment_type or 'general'}. "
                f"Changed from {current_quantity} to {new_quantity}",
                unit_cost=Decimal("0"),  # Could be enhanced to track cost
                total_cost=Decimal("0"),
            )
            self.session.add(movement)
            self.session.flush()

            # Record in ledger - use ADJUST operation type for all manual adjustments
            ledger_entry = InventoryLedger(
                operation_type=InventoryLedgerOperationType.ADJUST,
                part_id=part_id,
                warehouse_id=warehouse_id,
                quantity=adjustment_delta,  # Can be positive or negative
                reference_type="movement",
                reference_id=movement.id,
                created_by_id=user_id,
                notes=f"Manual adjustment: {'increased' if adjustment_delta > 0 else 'decreased'} by {abs(adjustment_delta)}",
            )
            self.session.add(ledger_entry)

            # Update the warehouse part quantity
            warehouse_part.quantity = new_quantity

            # Set success result
            result.success = True
            result.movement = movement
            result.message = (
                f"Successfully adjusted inventory from {current_quantity} to {new_quantity} "
                f"(delta: {adjustment_delta:+})"
            )

            logger.info(
                f"Manual adjustment created: part {part_id}, warehouse {warehouse_id}, "
                f"from {current_quantity} to {new_quantity}, movement {movement.movement_number}"
            )

        except Exception as e:
            logger.exception(f"Error creating manual adjustment: {e}")
            result.success = False
            result.errors.append(f"Failed to create adjustment: {str(e)}")

        return result

    # Removed _transaction method - letting caller manage transactions
    # to avoid "transaction is closed" errors from nested savepoints


class InventoryStrategy(ABC):
    """Base strategy for inventory operations"""

    def __init__(self, session: Session, ledger: InventoryLedgerManager):
        self.session = session
        self.ledger = ledger
        self.requirement_calculator = InventoryRequirementCalculator()
        self.reservation_manager = ReservationManager(session, ledger)
        self.movement_manager = MovementManager(session, ledger)

    @abstractmethod
    def execute(self, work_order: WorkOrder, user_id: int) -> InventoryResult:
        """Execute inventory operations for this state"""
        pass

    @abstractmethod
    def cleanup(self, work_order: WorkOrder, user_id: int) -> InventoryResult:
        """Clean up inventory when leaving this state"""
        pass

    @abstractmethod
    def validate(self, work_order: WorkOrder) -> ValidationResult:
        """Validate inventory state"""
        pass


class QuoteInventoryStrategy(InventoryStrategy):
    """Quotes have no inventory impact"""

    def execute(self, work_order: WorkOrder, user_id: int) -> InventoryResult:
        """Ensure quote has no inventory"""
        logger.debug(f"Executing quote inventory strategy for WO {work_order.id}")

        # Quotes should have no inventory - cleanup any that exists
        cleanup_result = self.cleanup(work_order, user_id)

        return InventoryResult(
            success=cleanup_result.success,
            message="Quote has no inventory impact",
            errors=cleanup_result.errors,
            warnings=cleanup_result.warnings,
        )

    def cleanup(self, work_order: WorkOrder, user_id: int) -> InventoryResult:
        """Remove all inventory for quote"""
        result = InventoryResult(success=True)

        # Delete all reservations
        reservations = self.reservation_manager.get_work_order_reservations(
            work_order.id
        )
        for reservation in reservations:
            delete_result = self.reservation_manager.delete_reservation(
                reservation, user_id
            )
            if not delete_result.success:
                result.merge(delete_result)

        # Delete all movements
        movements = self.movement_manager.get_work_order_movements(work_order.id)
        for movement in movements:
            delete_result = self.movement_manager.delete_movement(movement, user_id)
            if not delete_result.success:
                result.merge(delete_result)

        if result.success:
            result.message = f"Cleaned up {len(reservations)} reservations and {len(movements)} movements"

        return result

    def validate(self, work_order: WorkOrder) -> ValidationResult:
        """Validate quote has no inventory"""
        errors = []

        # Check for reservations
        reservations = self.reservation_manager.get_work_order_reservations(
            work_order.id
        )
        if reservations:
            errors.append(
                f"Quote has {len(reservations)} reservations but should have none"
            )

        # Check for movements
        movements = self.movement_manager.get_work_order_movements(work_order.id)
        if movements:
            errors.append(f"Quote has {len(movements)} movements but should have none")

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)


class ReservationOnlyStrategy(InventoryStrategy):
    """Non-approved work orders only have reservations"""

    def execute(self, work_order: WorkOrder, user_id: int) -> InventoryResult:
        """Create/update reservations for work order"""
        logger.debug(f"Executing reservation-only strategy for WO {work_order.id}")

        result = InventoryResult(success=True)

        # 1. Calculate requirements
        requirements = self.requirement_calculator.calculate_requirements(work_order)
        result.requirements = requirements

        logger.debug(
            f"Calculated {len(requirements)} requirements for WO {work_order.id}"
        )
        for req in requirements:
            logger.debug(
                f"  - Part {req.part_id}, WH {req.warehouse_id}, Qty {req.quantity}, Transfer: {req.is_transfer_destination}"
            )

        # 2. Ensure movements are deleted (shouldn't have any)
        movements = self.movement_manager.get_work_order_movements(work_order.id)
        for movement in movements:
            delete_result = self.movement_manager.delete_movement(movement, user_id)
            if not delete_result.success:
                result.merge(delete_result)

        # 3. Create or update reservations
        for requirement in requirements:
            reservation_result = self.reservation_manager.ensure_reservation(
                requirement, work_order, user_id
            )
            if reservation_result.success:
                result.reservations.append(reservation_result.reservation)
            else:
                result.merge(reservation_result)

        # 4. Clean up obsolete reservations
        cleanup_result = self._cleanup_obsolete_reservations(
            work_order, requirements, user_id
        )
        result.merge(cleanup_result)

        if result.success:
            result.message = f"Managed {len(result.reservations)} reservations"

        return result

    def cleanup(self, work_order: WorkOrder, user_id: int) -> InventoryResult:
        """Clean up reservations when leaving this state"""
        # When leaving non-approved state, we keep reservations
        # They will be fulfilled or deleted by the next state
        # Parameters work_order and user_id are required by interface but not used here
        _ = (work_order, user_id)
        return InventoryResult(
            success=True, message="Reservations preserved for transition"
        )

    def validate(self, work_order: WorkOrder) -> ValidationResult:
        """Validate non-approved work order has only reservations"""
        errors = []
        warnings = []

        # Check for movements (shouldn't have any)
        movements = self.movement_manager.get_work_order_movements(work_order.id)
        if movements:
            errors.append(
                f"Non-approved work order has {len(movements)} movements but should have none"
            )

        # Check reservations match requirements
        requirements = self.requirement_calculator.calculate_requirements(work_order)
        reservations = self.reservation_manager.get_work_order_reservations(
            work_order.id
        )

        if len(reservations) != len(requirements):
            warnings.append(
                f"Expected {len(requirements)} reservations but found {len(reservations)}"
            )

        return ValidationResult(
            is_valid=len(errors) == 0, errors=errors, warnings=warnings
        )

    def _cleanup_obsolete_reservations(
        self,
        work_order: WorkOrder,
        current_requirements: List[InventoryRequirement],
        user_id: int,
    ) -> InventoryResult:
        """Remove reservations that are no longer needed"""
        result = InventoryResult(success=True)

        # Get all existing reservations
        existing_reservations = self.reservation_manager.get_work_order_reservations(
            work_order.id
        )

        # Build a map of requirements for easier lookup
        requirement_map = {}
        for req in current_requirements:
            # Skip zero quantity requirements - they shouldn't have reservations
            if req.quantity != 0:
                # Include warehouse_to_id for positive reservations to distinguish transfers
                if req.quantity > 0:
                    key = (
                        int(req.part_id),  # Ensure integer type
                        int(req.warehouse_id),  # Ensure integer type
                        req.quantity < 0,
                        int(req.warehouse_to_id)
                        if req.warehouse_to_id is not None
                        else None,
                    )
                else:
                    # Negative reservations don't have warehouse_to_id
                    key = (
                        int(req.part_id),
                        int(req.warehouse_id),
                        req.quantity < 0,
                        None,
                    )
                requirement_map[key] = req

        # Check each existing reservation
        processed_keys = set()
        for reservation in existing_reservations:
            # Build key matching the requirement map structure
            if reservation.quantity_reserved > 0:
                key = (
                    int(reservation.part_id),  # Ensure integer type
                    int(reservation.warehouse_id),  # Ensure integer type
                    False,
                    int(reservation.warehouse_to_id)
                    if reservation.warehouse_to_id is not None
                    else None,
                )
            else:
                key = (
                    int(reservation.part_id),
                    int(reservation.warehouse_id),
                    True,
                    None,
                )

            if key not in requirement_map:
                # This reservation no longer has a matching requirement - delete it
                delete_result = self.reservation_manager.delete_reservation(
                    reservation, user_id
                )
                result.merge(delete_result)
                if delete_result.success:
                    result.deleted_reservations.append(reservation)
                    logger.debug(
                        f"Deleted obsolete reservation {reservation.id} for part {reservation.part_id} "
                        f"at warehouse {reservation.warehouse_id} "
                        f"(warehouse_to: {reservation.warehouse_to_id})"
                    )
            else:
                # Mark this requirement as processed
                processed_keys.add(key)

        # Log if any requirements weren't matched to existing reservations
        # (These will be created by ensure_reservation)
        unmatched_requirements = set(requirement_map.keys()) - processed_keys
        if unmatched_requirements:
            logger.debug(
                f"Found {len(unmatched_requirements)} requirements without existing reservations"
            )

        return result


class ReservationAndMovementStrategy(InventoryStrategy):
    """Approved work orders have fulfilled reservations and movements"""

    def execute(self, work_order: WorkOrder, user_id: int) -> InventoryResult:
        """Create reservations and movements, fulfilling reservations"""
        logger.debug(f"Executing reservation+movement strategy for WO {work_order.id}")

        # 1. First ensure reservations exist (without deleting movements)
        result = InventoryResult(success=True)

        # Calculate requirements
        requirements = self.requirement_calculator.calculate_requirements(work_order)
        result.requirements = requirements

        logger.debug(
            f"Calculated {len(requirements)} requirements for WO {work_order.id}"
        )
        for req in requirements:
            logger.debug(
                f"  - Part {req.part_id}, WH {req.warehouse_id}, Qty {req.quantity}, Transfer: {req.is_transfer_destination}"
            )

        # Create or update reservations (but don't delete movements like ReservationOnlyStrategy would)
        for requirement in requirements:
            logger.info(
                f"Processing requirement: part_id={requirement.part_id}, "
                f"warehouse_id={requirement.warehouse_id}, quantity={requirement.quantity}, "
                f"warehouse_to_id={getattr(requirement, 'warehouse_to_id', None)}"
            )
            reservation_result = self.reservation_manager.ensure_reservation(
                requirement, work_order, user_id
            )
            if reservation_result.success:
                result.reservations.append(reservation_result.reservation)
                logger.info(
                    f"Reservation result: {reservation_result.message}, "
                    f"reservation_id={reservation_result.reservation.id if reservation_result.reservation else 'None'}"
                )
            else:
                result.merge(reservation_result)

        # Clean up obsolete reservations
        reservation_strategy = ReservationOnlyStrategy(self.session, self.ledger)
        cleanup_result = reservation_strategy._cleanup_obsolete_reservations(
            work_order, requirements, user_id
        )
        result.merge(cleanup_result)

        if not result.success:
            return result

        # 2. Handle movements with delta calculation for existing movements
        # Flush any pending changes to ensure movements are visible in the session
        self.session.flush()

        # Get existing movements to check if this is an update
        existing_movements = self.movement_manager.get_work_order_movements(
            work_order.id
        )
        logger.info(
            f"Found {len(existing_movements)} existing movements for WO {work_order.id}"
        )

        # Debug: Log the current requirements to understand what we're trying to process
        logger.info(
            f"Processing {len(requirements)} requirements for approved WO {work_order.id}:"
        )
        for req in requirements:
            logger.info(
                f"  - Part {req.part_id}, WH {req.warehouse_id}, Qty {req.quantity}"
            )

        # Build a map of existing movements by part/warehouse for easy lookup
        existing_movement_map = {}
        for movement in existing_movements:
            # Key for transfers includes both source and destination warehouses
            if movement.movement_type == MovementType.TRANSFER:
                key = (
                    movement.part_id,
                    movement.from_warehouse_id,
                    movement.to_warehouse_id,
                )
            else:
                key = (movement.part_id, movement.from_warehouse_id, None)
            existing_movement_map[key] = movement
            logger.debug(
                f"Found existing movement: {key} -> {movement.movement_number} (qty: {movement.quantity})"
            )

        # Process positive reservations first (they create movements and fulfill negative ones)
        positive_reservations = [
            r for r in result.reservations if r.quantity_reserved > 0
        ]
        negative_reservations = [
            r for r in result.reservations if r.quantity_reserved < 0
        ]

        # Track which movements we've processed
        processed_movements = set()

        # Process positive reservations first
        for reservation in positive_reservations:
            # Check if we have an existing movement for this reservation
            if reservation.warehouse_to_id:
                movement_key = (
                    reservation.part_id,
                    reservation.warehouse_id,
                    reservation.warehouse_to_id,
                )
            else:
                movement_key = (reservation.part_id, reservation.warehouse_id, None)

            logger.debug(f"Looking for movement with key: {movement_key}")
            logger.info(
                f"Processing reservation {reservation.id}: part_id={reservation.part_id}, "
                f"warehouse_id={reservation.warehouse_id}, warehouse_to_id={reservation.warehouse_to_id}, "
                f"quantity={reservation.quantity_reserved}"
            )
            existing_movement = existing_movement_map.get(movement_key)

            if existing_movement:
                # This is an update - calculate delta
                processed_movements.add(movement_key)

                old_quantity = existing_movement.quantity
                new_quantity = abs(reservation.quantity_reserved)
                logger.info(
                    f"Checking movement update: reservation {reservation.id} has quantity {reservation.quantity_reserved}, "
                    f"movement {existing_movement.id} has quantity {old_quantity}"
                )

                if old_quantity != new_quantity:
                    # Quantity changed - update the movement
                    logger.info(
                        f"Movement quantity change detected: {old_quantity} -> {new_quantity}"
                    )
                    update_result = self.movement_manager.update_movement_quantity(
                        existing_movement, new_quantity, user_id
                    )
                    if update_result.success:
                        result.movements.append(existing_movement)
                        logger.info(
                            f"Updated movement {existing_movement.movement_number} "
                            f"quantity from {old_quantity} to {new_quantity}"
                        )
                        # Merge any warnings from movement update
                        result.warnings.extend(update_result.warnings)
                    else:
                        result.merge(update_result)
                else:
                    # Quantity unchanged - just add to result
                    result.movements.append(existing_movement)
                    logger.debug(
                        f"Movement {existing_movement.movement_number} unchanged"
                    )

                # Ensure reservation is marked as fulfilled
                if reservation.status != ReservationStatus.FULFILLED:
                    reservation.status = ReservationStatus.FULFILLED
                    reservation.quantity_fulfilled = reservation.quantity_reserved
                    reservation.fulfilled_at = datetime.now(timezone.utc)

                # For transfers, also ensure the destination reservation is fulfilled
                if reservation.warehouse_to_id:
                    # Find the corresponding negative reservation at the destination
                    dest_reservation = next(
                        (
                            r
                            for r in negative_reservations
                            if r.part_id == reservation.part_id
                            and r.warehouse_id == reservation.warehouse_to_id
                        ),
                        None,
                    )
                    if dest_reservation:
                        logger.info(
                            f"Found destination reservation {dest_reservation.id} with status {dest_reservation.status}"
                        )
                        if dest_reservation.status != ReservationStatus.FULFILLED:
                            dest_reservation.status = ReservationStatus.FULFILLED
                            dest_reservation.quantity_fulfilled = (
                                dest_reservation.quantity_reserved
                            )
                            dest_reservation.fulfilled_at = datetime.now(timezone.utc)
                            logger.info(
                                f"Re-fulfilled destination reservation {dest_reservation.id}"
                            )
                        else:
                            logger.info(
                                f"Destination reservation {dest_reservation.id} already fulfilled"
                            )
            else:
                # New movement needed
                logger.info(
                    f"No existing movement found for key {movement_key}, creating new movement"
                )
                # Skip if already fulfilled (shouldn't happen but just in case)
                if reservation.status == ReservationStatus.FULFILLED:
                    logger.warning(
                        f"Reservation {reservation.id} already fulfilled, skipping movement creation"
                    )
                    continue

                movement_result = (
                    self.movement_manager.create_movement_from_reservation(
                        reservation, work_order, user_id
                    )
                )

                if movement_result.success and movement_result.movement:
                    result.movements.append(movement_result.movement)
                    logger.info(
                        f"Created new movement {movement_result.movement.movement_number}"
                    )
                    # Merge any warnings from movement creation
                    result.warnings.extend(movement_result.warnings)
                    # The movement creation automatically fulfills the reservation
                else:
                    result.merge(movement_result)

        # Delete any movements that no longer have corresponding reservations
        for movement_key, movement in existing_movement_map.items():
            if movement_key not in processed_movements:
                # This movement is no longer needed
                delete_result = self.movement_manager.delete_movement(movement, user_id)
                if delete_result.success:
                    logger.info(f"Deleted obsolete movement {movement.movement_number}")
                else:
                    result.merge(delete_result)

        # Check if any negative reservations are still unfulfilled
        # (This shouldn't happen if transfers are properly linked, but just in case)
        for reservation in negative_reservations:
            if reservation.status != ReservationStatus.FULFILLED:
                # This negative reservation wasn't fulfilled by a transfer
                # Log a warning but don't fail the operation
                logger.warning(
                    f"Negative reservation {reservation.id} for part {reservation.part_id} "
                    f"at warehouse {reservation.warehouse_id} was not fulfilled by a transfer"
                )

        if result.success:
            result.message = f"Managed {len(result.movements)} movements and fulfilled {len(result.reservations)} reservations"

        return result

    def cleanup(self, work_order: WorkOrder, user_id: int) -> InventoryResult:
        """Clean up when leaving approved state"""
        # When leaving approved state, we need to reverse movements
        # but keep reservations (they'll be unfulfilled)
        result = InventoryResult(success=True)

        movements = self.movement_manager.get_work_order_movements(work_order.id)
        for movement in movements:
            # Reverse the movement and unfulfill the reservation
            reverse_result = self.movement_manager.reverse_movement(movement, user_id)
            result.merge(reverse_result)

        if result.success:
            result.message = f"Reversed {len(movements)} movements"

        return result

    def validate(self, work_order: WorkOrder) -> ValidationResult:
        """Validate approved work order has fulfilled reservations and movements"""
        errors = []
        warnings = []

        # Get all reservations and movements
        reservations = self.reservation_manager.get_work_order_reservations(
            work_order.id
        )
        movements = self.movement_manager.get_work_order_movements(work_order.id)

        # All reservations should be fulfilled
        unfulfilled = [
            r for r in reservations if r.status != ReservationStatus.FULFILLED
        ]
        if unfulfilled:
            errors.append(
                f"Approved work order has {len(unfulfilled)} unfulfilled reservations"
            )

        # Should have movements for all reservations
        if len(movements) != len(reservations):
            warnings.append(
                f"Expected {len(reservations)} movements but found {len(movements)}"
            )

        return ValidationResult(
            is_valid=len(errors) == 0, errors=errors, warnings=warnings
        )


class InventoryRequirementCalculator:
    """
    Calculates inventory requirements from work order data.
    Single responsibility: determine what inventory is needed.
    """

    @staticmethod
    def calculate_requirements(work_order: WorkOrder) -> List[InventoryRequirement]:
        """
        Calculate all inventory requirements for a work order.
        Returns a list of requirements that can be compared against existing state.
        For transfers, creates TWO requirements: source (positive) and destination (negative).
        """
        requirements = []

        # From order items (work_order_parts_rel in the model)
        for item in work_order.work_order_parts_rel:
            if item.part_id and item.quantity > 0:
                # Source requirement (always positive)
                source_req = InventoryRequirement(
                    part_id=item.part_id,
                    warehouse_id=item.warehouse_id
                    or getattr(work_order, "from_warehouse_id", None),
                    quantity=Decimal(str(item.quantity)),
                    source_type="order_item",
                    source_id=item.id,
                    priority=getattr(item, "priority", 0),
                    warehouse_to_id=getattr(item, "warehouse_to_id", None),
                    is_transfer_destination=False,
                )
                requirements.append(source_req)

                # If this is a transfer, create destination requirement (negative)
                warehouse_to_id = getattr(item, "warehouse_to_id", None)
                if warehouse_to_id:
                    # Create a linked key for both requirements
                    linked_key = (
                        item.part_id,
                        item.warehouse_id,
                        warehouse_to_id,
                        item.id,
                    )
                    source_req.linked_requirement_key = linked_key

                    dest_req = InventoryRequirement(
                        part_id=item.part_id,
                        warehouse_id=warehouse_to_id,  # Destination warehouse
                        quantity=-Decimal(str(item.quantity)),  # Negative for incoming
                        source_type="order_item",
                        source_id=item.id,
                        priority=getattr(item, "priority", 0),
                        warehouse_to_id=None,  # No further transfer
                        is_transfer_destination=True,
                        linked_requirement_key=linked_key,
                    )
                    requirements.append(dest_req)

        # From repair items (if they exist)
        # Note: The current WorkOrder model doesn't have a separate repair items relationship
        # This is kept for future compatibility
        repair_items = getattr(work_order, "workorder_repair_items", [])
        for repair in repair_items:
            if repair.part_id and repair.quantity > 0:
                # Source requirement
                source_req = InventoryRequirement(
                    part_id=repair.part_id,
                    warehouse_id=repair.warehouse_id
                    or getattr(work_order, "from_warehouse_id", None),
                    quantity=Decimal(str(repair.quantity)),
                    source_type="repair_item",
                    source_id=repair.id,
                    priority=1,  # Repairs might have higher priority
                    warehouse_to_id=getattr(repair, "warehouse_to_id", None),
                    is_transfer_destination=False,
                )
                requirements.append(source_req)

                # If this is a transfer, create destination requirement
                warehouse_to_id = getattr(repair, "warehouse_to_id", None)
                if warehouse_to_id:
                    linked_key = (
                        repair.part_id,
                        repair.warehouse_id,
                        warehouse_to_id,
                        repair.id,
                    )
                    source_req.linked_requirement_key = linked_key

                    dest_req = InventoryRequirement(
                        part_id=repair.part_id,
                        warehouse_id=warehouse_to_id,
                        quantity=-Decimal(str(repair.quantity)),
                        source_type="repair_item",
                        source_id=repair.id,
                        priority=1,
                        warehouse_to_id=None,
                        is_transfer_destination=True,
                        linked_requirement_key=linked_key,
                    )
                    requirements.append(dest_req)

        # Aggregate by part/warehouse (but preserve transfer relationships)
        return InventoryRequirementCalculator._aggregate_requirements(requirements)

    @staticmethod
    def _aggregate_requirements(
        requirements: List[InventoryRequirement],
    ) -> List[InventoryRequirement]:
        """
        Aggregate requirements by part and warehouse.
        Special handling for transfer requirements to preserve their relationships.
        """
        # Separate transfer and non-transfer requirements
        transfer_requirements = []
        non_transfer_requirements = []

        for req in requirements:
            if req.linked_requirement_key:
                transfer_requirements.append(req)
            else:
                non_transfer_requirements.append(req)

        # Aggregate non-transfer requirements normally
        aggregated = {}
        for req in non_transfer_requirements:
            key = (req.part_id, req.warehouse_id)
            if key in aggregated:
                # Add quantities and keep highest priority
                existing = aggregated[key]
                existing.quantity += req.quantity
                existing.priority = max(existing.priority, req.priority)
                # Track all sources
                if not hasattr(existing, "all_sources"):
                    existing.all_sources = [(existing.source_type, existing.source_id)]
                existing.all_sources.append((req.source_type, req.source_id))
            else:
                aggregated[key] = req

        # Add transfer requirements without aggregation to preserve relationships
        result = list(aggregated.values())
        result.extend(transfer_requirements)

        return result


class ReservationManager:
    """
    Manages reservations with idempotency and consistency.
    All operations are safe to retry.
    """

    def __init__(self, session: Session, ledger: InventoryLedgerManager):
        self.session = session
        self.ledger = ledger

    def ensure_reservation(
        self, requirement: InventoryRequirement, work_order: WorkOrder, user_id: int
    ) -> InventoryResult:
        """
        Ensure a reservation exists for the requirement.
        Updates if exists, creates if not, deletes if quantity is zero.
        This method is idempotent - calling it multiple times produces the same result.
        """
        result = InventoryResult(success=True)

        # Find existing reservation - also check if it matches the sign (positive/negative)
        # and warehouse_to_id for transfers
        existing = self.find_reservation_with_sign(
            work_order_id=work_order.id,
            part_id=requirement.part_id,
            warehouse_id=requirement.warehouse_id,
            is_negative=requirement.quantity < 0,
            warehouse_to_id=requirement.warehouse_to_id
            if hasattr(requirement, "warehouse_to_id")
            else None,
        )

        # Handle zero quantity - delete reservation if it exists
        if requirement.quantity == 0:
            if existing:
                delete_result = self.delete_reservation(existing, user_id)
                result.merge(delete_result)
                result.message = "Deleted reservation for zero quantity requirement"
            else:
                result.message = (
                    "No reservation to delete for zero quantity requirement"
                )
            return result

        if existing:
            # Refresh the reservation to ensure we have the latest data
            self.session.refresh(existing)

            # If reservation is already fulfilled, we still need to check if quantity changed
            if existing.status == ReservationStatus.FULFILLED:
                if existing.quantity_reserved != requirement.quantity:
                    # For fulfilled reservations, we need to update BOTH the reservation quantity
                    # AND the warehouse quantities to maintain consistency
                    old_quantity = existing.quantity_reserved
                    delta = requirement.quantity - old_quantity

                    logger.info(
                        f"Updating fulfilled reservation {existing.id} quantity from "
                        f"{old_quantity} to {requirement.quantity}, delta={delta}"
                    )

                    # Record in ledger for audit trail BEFORE updating
                    self.ledger.record_reservation_updated(
                        session=self.session,
                        reservation=existing,
                        old_quantity=old_quantity,
                        new_quantity=requirement.quantity,
                        user_id=user_id,
                        notes="Updated fulfilled reservation quantity",
                    )

                    # Update the reservation quantities
                    existing.quantity_reserved = requirement.quantity
                    existing.quantity_fulfilled = requirement.quantity
                    existing.updated_at = datetime.now(timezone.utc)

                    # CRITICAL FIX: Also update warehouse quantities for fulfilled reservations
                    # This was the missing piece causing the bug!
                    warehouse_part = (
                        self.session.query(WarehousePart)
                        .filter(
                            WarehousePart.warehouse_id == existing.warehouse_id,
                            WarehousePart.part_id == existing.part_id,
                        )
                        .with_for_update()
                        .first()
                    )
                    if warehouse_part:
                        # Only adjust quantity_reserved for non-approved work orders
                        # For approved work orders, movements handle the warehouse quantity changes
                        if not work_order.invoice_approval_req:
                            warehouse_part.quantity_reserved += delta

                        # Flush to ensure consistency
                        self.session.flush()

                    result.reservation = existing
                    result.message = "Fulfilled reservation quantity updated with warehouse adjustment"
                else:
                    result.reservation = existing
                    result.message = "Reservation already fulfilled"
                return result

            # Update if quantity changed (for non-fulfilled reservations)
            if existing.quantity_reserved != requirement.quantity:
                # Store the old quantity before any updates
                old_quantity = existing.quantity_reserved
                update_result = self.update_reservation_quantity(
                    existing, requirement.quantity, user_id
                )
                result.merge(update_result)
                result.reservation = existing
                result.message = f"Updated reservation quantity from {old_quantity} to {requirement.quantity}"
            else:
                result.reservation = existing
                result.message = "Reservation already up to date"
        else:
            # Create new reservation
            create_result = self.create_reservation(requirement, work_order, user_id)
            result.merge(create_result)
            if create_result.success:
                result.reservation = create_result.reservation

        return result

    def find_reservation(
        self, work_order_id: int, part_id: int, warehouse_id: int
    ) -> Optional[InventoryReservation]:
        """Find existing reservation for work order/part/warehouse"""
        return (
            self.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_type == "work_order",
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.part_id == part_id,
                InventoryReservation.warehouse_id == warehouse_id,
                InventoryReservation.status != ReservationStatus.CANCELLED,
            )
            .first()
        )

    def find_reservation_with_sign(
        self,
        work_order_id: int,
        part_id: int,
        warehouse_id: int,
        is_negative: bool,
        warehouse_to_id: Optional[int] = None,
    ) -> Optional[InventoryReservation]:
        """Find existing reservation for work order/part/warehouse with matching sign and destination"""
        query = self.session.query(InventoryReservation).filter(
            InventoryReservation.reference_type == "work_order",
            InventoryReservation.reference_id == work_order_id,
            InventoryReservation.part_id == part_id,
            InventoryReservation.warehouse_id == warehouse_id,
            InventoryReservation.status != ReservationStatus.CANCELLED,
        )

        if is_negative:
            query = query.filter(InventoryReservation.quantity_reserved < 0)
        else:
            query = query.filter(InventoryReservation.quantity_reserved > 0)
            # For positive reservations, also match warehouse_to_id to distinguish
            # between consumed parts (warehouse_to_id=None) and transferred parts
            if warehouse_to_id is None:
                query = query.filter(InventoryReservation.warehouse_to_id.is_(None))
            else:
                query = query.filter(
                    InventoryReservation.warehouse_to_id == warehouse_to_id
                )

        return query.first()

    def get_work_order_reservations(
        self, work_order_id: int
    ) -> List[InventoryReservation]:
        """Get all reservations for a work order"""
        return (
            self.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_type == "work_order",
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.status != ReservationStatus.CANCELLED,
            )
            .all()
        )

    def create_reservation(
        self, requirement: InventoryRequirement, work_order: WorkOrder, user_id: int
    ) -> InventoryResult:
        """Create new reservation (supports negative quantities for transfer destinations)"""
        result = InventoryResult(success=True)

        # For transfer destinations (negative quantities), create warehouse part if needed
        if requirement.is_transfer_destination:
            warehouse_part = (
                self.session.query(WarehousePart)
                .filter(
                    WarehousePart.warehouse_id == requirement.warehouse_id,
                    WarehousePart.part_id == requirement.part_id,
                )
                .first()
            )

            if not warehouse_part:
                # Create warehouse part for destination
                warehouse_part = WarehousePart(
                    warehouse_id=requirement.warehouse_id,
                    part_id=requirement.part_id,
                    quantity=Decimal("0"),
                    quantity_reserved=Decimal("0"),
                )
                self.session.add(warehouse_part)
                self.session.flush()
        else:
            # For normal reservations, check availability
            warehouse_part = (
                self.session.query(WarehousePart)
                .filter(
                    WarehousePart.warehouse_id == requirement.warehouse_id,
                    WarehousePart.part_id == requirement.part_id,
                )
                .first()
            )

            if not warehouse_part:
                result.success = False
                result.errors.append(
                    f"Part {requirement.part_id} not found in warehouse {requirement.warehouse_id}"
                )
                return result

            # Use warehouse-specific validation for negative stock policy
            available = warehouse_part.quantity - warehouse_part.quantity_reserved
            validation_result = validate_inventory_availability(
                session=self.session,
                warehouse_id=requirement.warehouse_id,
                part_id=requirement.part_id,
                required_quantity=requirement.quantity,
                current_available=available,
            )

            if not validation_result.is_valid:
                result.success = False
                result.errors.extend(validation_result.errors)
                return result

            # Add any warnings about negative stock
            result.warnings.extend(validation_result.warnings)

        # Create reservation
        reservation = InventoryReservation(
            reservation_number=self._generate_reservation_number(),
            status=ReservationStatus.CONFIRMED,
            part_id=requirement.part_id,
            warehouse_id=requirement.warehouse_id,
            warehouse_to_id=requirement.warehouse_to_id,
            quantity_reserved=requirement.quantity,
            quantity_fulfilled=Decimal(0),
            reference_type="work_order",
            reference_id=work_order.id,
            priority=requirement.priority,
            created_by_id=user_id,
            notes=f"{'Transfer destination' if requirement.is_transfer_destination else 'Reserved'} for work order {work_order.id}",
        )

        self.session.add(reservation)
        self.session.flush()

        # Record in ledger
        self.ledger.record_reservation_created(
            session=self.session,
            reservation=reservation,
            user_id=user_id,
            notes=f"Created reservation {reservation.reservation_number}",
        )

        # Update warehouse quantities
        warehouse_part.quantity_reserved += reservation.quantity_reserved

        # Flush to ensure the reservation is persisted
        self.session.flush()

        result.reservation = reservation
        result.message = f"Created reservation {reservation.reservation_number}"

        return result

    def update_reservation_quantity(
        self, reservation: InventoryReservation, new_quantity: Decimal, user_id: int
    ) -> InventoryResult:
        """Update reservation quantity using ledger events"""
        result = InventoryResult(success=True)

        # Ensure we're working with fresh data
        self.session.refresh(reservation)

        old_quantity = reservation.quantity_reserved
        delta = new_quantity - old_quantity

        logger.debug(
            f"Update reservation {reservation.id}: old_qty={old_quantity}, "
            f"new_qty={new_quantity}, delta={delta}"
        )

        if delta == 0:
            result.message = "No quantity change needed"
            return result

        # Prevent double-processing
        if reservation.quantity_reserved == new_quantity:
            result.message = "Reservation already has the target quantity"
            return result

        # Determine if this is a transfer destination (negative reservation)
        is_transfer_destination = reservation.quantity_reserved < 0

        # Check availability for increase (only for positive reservations)
        if delta > 0 and not is_transfer_destination:
            warehouse_part = (
                self.session.query(WarehousePart)
                .filter(
                    WarehousePart.warehouse_id == reservation.warehouse_id,
                    WarehousePart.part_id == reservation.part_id,
                )
                .first()
            )

            if warehouse_part:
                # Refresh to ensure we have latest data
                self.session.refresh(warehouse_part)
                available = warehouse_part.quantity - warehouse_part.quantity_reserved

                # Use warehouse-specific validation for negative stock policy
                validation_result = validate_inventory_availability(
                    session=self.session,
                    warehouse_id=reservation.warehouse_id,
                    part_id=reservation.part_id,
                    required_quantity=delta,
                    current_available=available,
                )

                if not validation_result.is_valid:
                    result.success = False
                    result.errors.extend(validation_result.errors)
                    return result

                # Add any warnings about negative stock
                result.warnings.extend(validation_result.warnings)

        # Update reservation record FIRST before recording in ledger
        # This ensures the reservation has the correct quantity when ledger is processed
        reservation.quantity_reserved = new_quantity
        reservation.updated_at = datetime.now(timezone.utc)

        # Record in ledger AFTER updating the reservation
        self.ledger.record_reservation_updated(
            session=self.session,
            reservation=reservation,
            old_quantity=old_quantity,
            new_quantity=new_quantity,
            user_id=user_id,
            notes=f"Updated reservation quantity from {old_quantity} to {new_quantity}",
        )

        # Update warehouse quantities
        warehouse_part = (
            self.session.query(WarehousePart)
            .filter(
                WarehousePart.warehouse_id == reservation.warehouse_id,
                WarehousePart.part_id == reservation.part_id,
            )
            .with_for_update()  # Lock the row to prevent concurrent updates
            .first()
        )
        if warehouse_part:
            # Don't refresh here - we already have the locked row
            # self.session.refresh(warehouse_part)

            # Store the old value for safety
            old_reserved = Decimal(str(warehouse_part.quantity_reserved))
            new_reserved_expected = old_reserved + delta

            # Apply the delta
            warehouse_part.quantity_reserved = new_reserved_expected

            # Sanity check - quantity_reserved should never be negative for normal parts
            if warehouse_part.quantity_reserved < 0 and not is_transfer_destination:
                logger.error(
                    f"ERROR: quantity_reserved is negative ({warehouse_part.quantity_reserved}) "
                    f"for non-transfer part!"
                )
                # Fix it
                warehouse_part.quantity_reserved = Decimal("0")
                logger.error("ERROR: FIXED quantity_reserved to 0")

            # Flush to ensure database consistency
            self.session.flush()

        result.message = f"Updated reservation quantity to {new_quantity}"
        return result

    def delete_reservation(
        self, reservation: InventoryReservation, user_id: int
    ) -> InventoryResult:
        """Delete reservation and release quantities"""
        result = InventoryResult(success=True)

        # Release any reserved quantity
        # For positive reservations, release if not fully fulfilled
        # For negative reservations (transfer destinations), always release
        if reservation.quantity_reserved > 0:
            # Positive reservation - release unfulfilled quantity
            if reservation.quantity_reserved > reservation.quantity_fulfilled:
                release_quantity = (
                    reservation.quantity_reserved - reservation.quantity_fulfilled
                )

                # Record release in ledger
                self.ledger.record_reservation_deleted(
                    session=self.session,
                    reservation=reservation,
                    user_id=user_id,
                    notes=f"Released {release_quantity} on reservation deletion",
                )

                # Update warehouse quantities
                warehouse_part = (
                    self.session.query(WarehousePart)
                    .filter(
                        WarehousePart.warehouse_id == reservation.warehouse_id,
                        WarehousePart.part_id == reservation.part_id,
                    )
                    .first()
                )
                if warehouse_part:
                    warehouse_part.quantity_reserved -= release_quantity
        else:
            # Negative reservation (transfer destination) - only adjust if not fulfilled
            if reservation.status != ReservationStatus.FULFILLED:
                # Record release in ledger
                self.ledger.record_reservation_deleted(
                    session=self.session,
                    reservation=reservation,
                    user_id=user_id,
                    notes=f"Released negative reservation {reservation.quantity_reserved} on deletion",
                )

                # Update warehouse quantities - subtract the negative to make it positive
                warehouse_part = (
                    self.session.query(WarehousePart)
                    .filter(
                        WarehousePart.warehouse_id == reservation.warehouse_id,
                        WarehousePart.part_id == reservation.part_id,
                    )
                    .first()
                )
                if warehouse_part:
                    warehouse_part.quantity_reserved -= reservation.quantity_reserved
            else:
                # Fulfilled negative reservation - just record deletion, don't adjust quantities
                self.ledger.record_reservation_deleted(
                    session=self.session,
                    reservation=reservation,
                    user_id=user_id,
                    notes=f"Deleted fulfilled negative reservation {reservation.reservation_number}",
                )

        # Mark as cancelled
        reservation.status = ReservationStatus.CANCELLED
        reservation.updated_at = datetime.now(timezone.utc)

        result.message = f"Deleted reservation {reservation.reservation_number}"
        return result

    def _generate_reservation_number(self) -> str:
        """Generate unique reservation number"""
        import uuid

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
        return f"RES-{timestamp}-{str(uuid.uuid4())[:8].upper()}"


class MovementManager:
    """
    Manages inventory movements with automatic reservation fulfillment.
    Ensures consistency between reservations and movements.
    """

    def __init__(self, session: Session, ledger: InventoryLedgerManager):
        self.session = session
        self.ledger = ledger

    def create_movement_from_reservation(
        self, reservation: InventoryReservation, work_order: WorkOrder, user_id: int
    ) -> InventoryResult:
        """Create movement and fulfill reservation atomically"""
        result = InventoryResult(success=True)

        # Skip negative reservations (transfer destinations) - they don't create movements directly
        # But they should be marked as fulfilled when their corresponding transfer movement is created
        if reservation.quantity_reserved < 0:
            # These will be fulfilled when the corresponding positive reservation creates a transfer movement
            result.message = "Transfer destination reservations are fulfilled by their corresponding transfer"
            result.success = True
            return result

        # Check if already has movement
        existing_movement = self.find_movement_for_reservation(reservation.id)
        if existing_movement:
            result.movement = existing_movement
            result.message = "Movement already exists for reservation"
            return result

        # Create movement - transfer if warehouse_to_id is set, otherwise issue
        is_transfer = reservation.warehouse_to_id is not None
        movement = InventoryMovement(
            movement_number=self._generate_movement_number(),
            movement_type=MovementType.TRANSFER if is_transfer else MovementType.ISSUE,
            part_id=reservation.part_id,
            from_warehouse_id=reservation.warehouse_id,
            to_warehouse_id=reservation.warehouse_to_id if is_transfer else None,
            quantity=abs(
                reservation.quantity_reserved
            ),  # Always positive for movements
            reference_type="work_order",
            reference_id=work_order.id,
            created_by_id=user_id,
            notes=f"{'Transferred' if is_transfer else 'Issued'} for work order {work_order.id} (reservation {reservation.id})",
            # Store unit cost as 0 for now - should be calculated from part cost
            unit_cost=Decimal("0"),
            total_cost=Decimal("0"),
        )
        self.session.add(movement)
        self.session.flush()

        # Record in ledger
        self.ledger.record_movement_consumption(
            session=self.session,
            movement=movement,
            user_id=user_id,
            notes=f"Movement {movement.movement_number} fulfilling reservation",
        )

        # Update warehouse quantities
        # For source warehouse
        warehouse_part_from = (
            self.session.query(WarehousePart)
            .filter(
                WarehousePart.warehouse_id == movement.from_warehouse_id,
                WarehousePart.part_id == movement.part_id,
            )
            .first()
        )
        if warehouse_part_from:
            # Safety check: ensure we respect warehouse negative stock policy
            new_quantity = warehouse_part_from.quantity - movement.quantity
            if new_quantity < 0:
                allows_negative = warehouse_allows_negative_stock(
                    self.session, movement.from_warehouse_id
                )
                if not allows_negative:
                    result.success = False
                    result.errors.append(
                        f"Movement would result in negative inventory for part {movement.part_id} "
                        f"in warehouse {movement.from_warehouse_id}, but warehouse does not allow negative stock. "
                        f"Current: {warehouse_part_from.quantity}, attempting to consume: {movement.quantity}"
                    )
                    return result
                else:
                    logger.info(
                        f"Creating negative inventory in warehouse {movement.from_warehouse_id} "
                        f"for part {movement.part_id}: {warehouse_part_from.quantity} - {movement.quantity} = {new_quantity}"
                    )
                    result.warnings.append(
                        f"Creating negative inventory in warehouse {movement.from_warehouse_id} "
                        f"for part {movement.part_id}: quantity will be {new_quantity} "
                        f"(warehouse allows negative stock)"
                    )

            warehouse_part_from.quantity -= movement.quantity
            # Clear the reservation - use the actual reserved quantity, not movement quantity
            warehouse_part_from.quantity_reserved -= reservation.quantity_reserved

        # For transfer, also update destination warehouse
        if is_transfer and movement.to_warehouse_id:
            warehouse_part_to = (
                self.session.query(WarehousePart)
                .filter(
                    WarehousePart.warehouse_id == movement.to_warehouse_id,
                    WarehousePart.part_id == movement.part_id,
                )
                .first()
            )
            if not warehouse_part_to:
                # Create warehouse part if it doesn't exist
                warehouse_part_to = WarehousePart(
                    warehouse_id=movement.to_warehouse_id,
                    part_id=movement.part_id,
                    quantity=Decimal("0"),
                    quantity_reserved=Decimal("0"),
                )
                self.session.add(warehouse_part_to)
            # Add to destination warehouse
            warehouse_part_to.quantity += movement.quantity

        # Update reservation status
        reservation.status = ReservationStatus.FULFILLED
        reservation.quantity_fulfilled = reservation.quantity_reserved
        reservation.fulfilled_at = datetime.now(timezone.utc)

        # For transfers, also fulfill the destination reservation
        if is_transfer and movement.to_warehouse_id:
            # Find the corresponding negative reservation at the destination
            dest_reservation = (
                self.session.query(InventoryReservation)
                .filter(
                    InventoryReservation.reference_type == "work_order",
                    InventoryReservation.reference_id == work_order.id,
                    InventoryReservation.part_id == reservation.part_id,
                    InventoryReservation.warehouse_id == movement.to_warehouse_id,
                    InventoryReservation.quantity_reserved < 0,  # Negative reservation
                    InventoryReservation.status != ReservationStatus.CANCELLED,
                )
                .first()
            )

            if dest_reservation:
                dest_reservation.status = ReservationStatus.FULFILLED
                dest_reservation.quantity_fulfilled = dest_reservation.quantity_reserved
                dest_reservation.fulfilled_at = datetime.now(timezone.utc)
                # Update the destination warehouse's reserved quantity
                warehouse_part_to = (
                    self.session.query(WarehousePart)
                    .filter(
                        WarehousePart.warehouse_id == movement.to_warehouse_id,
                        WarehousePart.part_id == movement.part_id,
                    )
                    .first()
                )
                if warehouse_part_to:
                    # Remove the negative reservation from quantity_reserved
                    warehouse_part_to.quantity_reserved -= (
                        dest_reservation.quantity_reserved
                    )

        result.movement = movement
        result.message = f"Created movement {movement.movement_number}"

        return result

    def find_movement_for_reservation(
        self, reservation_id: int
    ) -> Optional[InventoryMovement]:
        """Find movement created for a reservation"""
        # We store reservation id in the notes field for now
        # In a production system, we'd add a proper reservation_id field to InventoryMovement
        return (
            self.session.query(InventoryMovement)
            .filter(InventoryMovement.notes.like(f"%reservation {reservation_id}%"))
            .first()
        )

    def get_work_order_movements(self, work_order_id: int) -> List[InventoryMovement]:
        """Get all movements for a work order"""
        # Flush to ensure all pending changes are visible
        self.session.flush()

        movements = (
            self.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_type == "work_order",
                InventoryMovement.reference_id == work_order_id,
            )
            .all()
        )
        logger.info(
            f"Query found {len(movements)} movements for work_order_id={work_order_id}"
        )
        return movements

    def delete_movement(
        self, movement: InventoryMovement, user_id: int
    ) -> InventoryResult:
        """Delete movement and reverse its effects"""
        return self.reverse_movement(movement, user_id)

    def update_movement_quantity(
        self, movement: InventoryMovement, new_quantity: Decimal, user_id: int
    ) -> InventoryResult:
        """Update movement quantity with delta calculation"""
        result = InventoryResult(success=True)

        old_quantity = movement.quantity
        delta = new_quantity - old_quantity

        if delta == 0:
            result.message = "No quantity change needed"
            return result

        # Update warehouse quantities based on delta
        # For source warehouse
        warehouse_part_from = (
            self.session.query(WarehousePart)
            .filter(
                WarehousePart.warehouse_id == movement.from_warehouse_id,
                WarehousePart.part_id == movement.part_id,
            )
            .first()
        )

        if warehouse_part_from:
            # Check if we have enough inventory for increase
            if delta > 0:
                # Use warehouse-specific validation for negative stock policy
                validation_result = validate_inventory_availability(
                    session=self.session,
                    warehouse_id=movement.from_warehouse_id,
                    part_id=movement.part_id,
                    required_quantity=delta,
                    current_available=warehouse_part_from.quantity,
                )

                if not validation_result.is_valid:
                    result.success = False
                    result.errors.extend(validation_result.errors)
                    return result

                # Add any warnings about negative stock
                result.warnings.extend(validation_result.warnings)

            # Apply delta to source warehouse
            warehouse_part_from.quantity -= delta

        # For transfer, also update destination warehouse
        if movement.movement_type == MovementType.TRANSFER and movement.to_warehouse_id:
            warehouse_part_to = (
                self.session.query(WarehousePart)
                .filter(
                    WarehousePart.warehouse_id == movement.to_warehouse_id,
                    WarehousePart.part_id == movement.part_id,
                )
                .first()
            )
            if warehouse_part_to:
                # Apply delta to destination warehouse
                warehouse_part_to.quantity += delta

        # Update movement record
        movement.quantity = new_quantity
        movement.updated_at = datetime.now(timezone.utc)

        # Record in ledger
        self.ledger.record_movement_updated(
            session=self.session,
            movement=movement,
            old_quantity=old_quantity,
            new_quantity=new_quantity,
            user_id=user_id,
            notes=f"Updated movement quantity from {old_quantity} to {new_quantity}",
        )

        # Update related reservation quantities if needed
        # Extract reservation ID from notes if present
        import re

        match = re.search(r"reservation (\d+)", movement.notes or "")
        if match:
            reservation_id = int(match.group(1))
            reservation = self.session.query(InventoryReservation).get(reservation_id)
            if reservation and reservation.quantity_fulfilled != new_quantity:
                reservation.quantity_fulfilled = new_quantity

        result.message = f"Updated movement quantity to {new_quantity}"
        return result

    def reverse_movement(
        self, movement: InventoryMovement, user_id: int
    ) -> InventoryResult:
        """Reverse a movement and unfulfill associated reservation"""
        result = InventoryResult(success=True)

        # Record reversal in ledger
        self.ledger.record_movement_deleted(
            session=self.session,
            movement=movement,
            user_id=user_id,
            notes=f"Reversed movement {movement.movement_number}",
        )

        # Update warehouse quantities
        # For source warehouse - add quantity back
        warehouse_part_from = (
            self.session.query(WarehousePart)
            .filter(
                WarehousePart.warehouse_id == movement.from_warehouse_id,
                WarehousePart.part_id == movement.part_id,
            )
            .first()
        )
        if warehouse_part_from:
            warehouse_part_from.quantity += movement.quantity
            # Only restore reserved quantity if we'll actually unfulfill the reservation

        # For transfer movements, also reverse destination warehouse
        if movement.movement_type == MovementType.TRANSFER and movement.to_warehouse_id:
            warehouse_part_to = (
                self.session.query(WarehousePart)
                .filter(
                    WarehousePart.warehouse_id == movement.to_warehouse_id,
                    WarehousePart.part_id == movement.part_id,
                )
                .first()
            )
            if warehouse_part_to:
                # Remove quantity from destination warehouse
                warehouse_part_to.quantity -= movement.quantity

        # Unfulfill reservation if exists
        # Extract reservation ID from notes if present
        import re

        match = re.search(r"reservation (\d+)", movement.notes or "")
        if match:
            reservation_id = int(match.group(1))
            reservation = self.session.query(InventoryReservation).get(reservation_id)
            if reservation and reservation.status == ReservationStatus.FULFILLED:
                reservation.status = ReservationStatus.CONFIRMED
                reservation.quantity_fulfilled = Decimal(0)
                reservation.fulfilled_at = None
                # Now restore the reserved quantity for the source
                if warehouse_part_from:
                    warehouse_part_from.quantity_reserved += (
                        reservation.quantity_reserved
                    )

        # For transfers, also unfulfill the destination reservation
        if movement.movement_type == MovementType.TRANSFER and movement.to_warehouse_id:
            # Find the corresponding negative reservation at the destination
            dest_reservation = (
                self.session.query(InventoryReservation)
                .filter(
                    InventoryReservation.reference_type == movement.reference_type,
                    InventoryReservation.reference_id == movement.reference_id,
                    InventoryReservation.part_id == movement.part_id,
                    InventoryReservation.warehouse_id == movement.to_warehouse_id,
                    InventoryReservation.quantity_reserved < 0,  # Negative reservation
                    InventoryReservation.status == ReservationStatus.FULFILLED,
                )
                .first()
            )

            if (
                dest_reservation
                and dest_reservation.status == ReservationStatus.FULFILLED
            ):
                dest_reservation.status = ReservationStatus.CONFIRMED
                dest_reservation.quantity_fulfilled = Decimal(0)
                dest_reservation.fulfilled_at = None
                # Also restore the reserved quantity at destination
                if warehouse_part_to:
                    warehouse_part_to.quantity_reserved += (
                        dest_reservation.quantity_reserved
                    )

        # Delete the movement
        self.session.delete(movement)

        result.message = f"Reversed movement {movement.movement_number}"
        return result

    def _generate_movement_number(self) -> str:
        """Generate unique movement number"""
        import uuid

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
        return f"MOV-{timestamp}-{str(uuid.uuid4())[:8].upper()}"


class StateTransitionValidator:
    """Validates state transitions and ensures consistency"""

    # Valid transitions matrix
    VALID_TRANSITIONS = {
        (None, WorkOrderState.QUOTE): True,
        (None, WorkOrderState.WORK_ORDER_NOT_APPROVED): True,
        (None, WorkOrderState.WORK_ORDER_APPROVED): True,
        (WorkOrderState.QUOTE, WorkOrderState.WORK_ORDER_NOT_APPROVED): True,
        (WorkOrderState.QUOTE, WorkOrderState.WORK_ORDER_APPROVED): True,
        (WorkOrderState.WORK_ORDER_NOT_APPROVED, WorkOrderState.QUOTE): True,
        (
            WorkOrderState.WORK_ORDER_NOT_APPROVED,
            WorkOrderState.WORK_ORDER_APPROVED,
        ): True,
        (
            WorkOrderState.WORK_ORDER_APPROVED,
            WorkOrderState.WORK_ORDER_NOT_APPROVED,
        ): True,
        (WorkOrderState.WORK_ORDER_APPROVED, WorkOrderState.QUOTE): True,
        # Same state transitions are valid (for updates)
        (WorkOrderState.QUOTE, WorkOrderState.QUOTE): True,
        (
            WorkOrderState.WORK_ORDER_NOT_APPROVED,
            WorkOrderState.WORK_ORDER_NOT_APPROVED,
        ): True,
        (WorkOrderState.WORK_ORDER_APPROVED, WorkOrderState.WORK_ORDER_APPROVED): True,
    }

    def validate_transition(
        self, old_state: Optional[WorkOrderState], new_state: WorkOrderState
    ) -> bool:
        """Check if state transition is valid"""
        return self.VALID_TRANSITIONS.get((old_state, new_state), False)

    def validate_end_state(
        self, work_order: WorkOrder, expected_state: WorkOrderState
    ) -> ValidationResult:
        """Validate that inventory state matches expected state"""
        # Get appropriate strategy for validation
        session = Session.object_session(work_order)
        ledger = InventoryLedgerManager()

        strategies = {
            WorkOrderState.QUOTE: QuoteInventoryStrategy(session, ledger),
            WorkOrderState.WORK_ORDER_NOT_APPROVED: ReservationOnlyStrategy(
                session, ledger
            ),
            WorkOrderState.WORK_ORDER_APPROVED: ReservationAndMovementStrategy(
                session, ledger
            ),
        }

        strategy = strategies.get(expected_state)
        if strategy:
            return strategy.validate(work_order)

        return ValidationResult(
            is_valid=False, errors=[f"Unknown state: {expected_state}"]
        )


def get_work_order_state(work_order: WorkOrder) -> WorkOrderState:
    """Determine the current inventory state of a work order"""
    if work_order.is_quote:
        return WorkOrderState.QUOTE
    elif work_order.invoice_approval_req:
        return WorkOrderState.WORK_ORDER_APPROVED
    else:
        return WorkOrderState.WORK_ORDER_NOT_APPROVED


def warehouse_allows_negative_stock(session: Session, warehouse_id: int) -> bool:
    """
    Check if a warehouse allows negative stock quantities.

    Args:
        session: Database session
        warehouse_id: ID of the warehouse to check

    Returns:
        bool: True if warehouse allows negative stock, False otherwise

    Raises:
        ValueError: If warehouse is not found
    """
    warehouse = session.get(Warehouse, warehouse_id)
    if not warehouse:
        raise ValueError(f"Warehouse {warehouse_id} not found")

    return warehouse.allows_negative_stock


def validate_inventory_availability(
    session: Session,
    warehouse_id: int,
    part_id: int,
    required_quantity: Decimal,
    current_available: Decimal,
) -> ValidationResult:
    """
    Validate if inventory is available considering warehouse negative stock policy.

    Args:
        session: Database session
        warehouse_id: ID of the warehouse
        part_id: ID of the part
        required_quantity: Quantity needed
        current_available: Currently available quantity

    Returns:
        ValidationResult: Result of the validation
    """
    result = ValidationResult(is_valid=True)

    # If we have enough inventory, always allow
    if current_available >= required_quantity:
        return result

    # Check if warehouse allows negative stock
    try:
        allows_negative = warehouse_allows_negative_stock(session, warehouse_id)
        if allows_negative:
            # Warehouse allows negative stock, so this is acceptable
            shortage = required_quantity - current_available
            result.warnings.append(
                f"Warehouse allows negative stock: creating shortage of {shortage} "
                f"for part {part_id} in warehouse {warehouse_id}"
            )
            logger.info(
                f"Allowing negative stock in warehouse {warehouse_id}: "
                f"part {part_id}, shortage {shortage}"
            )
            return result
        else:
            # Warehouse doesn't allow negative stock, this is an error
            shortage = required_quantity - current_available
            result.is_valid = False
            result.errors.append(
                f"Insufficient inventory for part {part_id} in warehouse {warehouse_id}: "
                f"needed {required_quantity}, available {current_available}, "
                f"shortage {shortage} (warehouse does not allow negative stock)"
            )
            return result

    except ValueError as e:
        # Warehouse not found
        result.is_valid = False
        result.errors.append(str(e))
        return result
