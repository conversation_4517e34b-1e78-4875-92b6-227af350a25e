"""
Geographic utility functions for distance calculations and coordinate operations.

This module provides DRY utilities for geographic operations that can be shared
across Flask and FastAPI applications.
"""

from math import atan2, cos, radians, sin, sqrt
from typing import Optional, Tuple, List


def geodesic(
    lat1_dec: float, lon1_dec: float, lat2_dec: float, lon2_dec: float
) -> float:
    """
    Calculate the distance between two GPS coordinates.
    The incoming lat and lon values are in decimal format.
    They need to be converted to radians before using the Haversine formula.
    The radians function needs positive values, so we use the abs() function.
    https://stackoverflow.com/a/19412565/3385948
    """
    lat1 = radians(abs(lat1_dec))
    lon1 = radians(abs(lon1_dec))
    lat2 = radians(abs(lat2_dec))
    lon2 = radians(abs(lon2_dec))

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    # Radius of Earth in kilometers = 6371.0
    distance = 6371.0 * c

    return distance


def calc_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    Calculate the distance between two GPS coordinates
    https://stackoverflow.com/a/43211266/3385948
    """
    try:
        km = geodesic(lat1, lon1, lat2, lon2)
    except (ValueError, TypeError):
        km = 2000.0

    return km


def round_coordinates_for_cache(
    lat: float, lon: float, precision: int = 3
) -> Tuple[float, float]:
    """
    Round coordinates for geocoding cache (default ~100m precision).

    Args:
        lat: Latitude in decimal degrees
        lon: Longitude in decimal degrees
        precision: Number of decimal places (3 = ~100m, 4 = ~10m)

    Returns:
        Tuple of rounded (lat, lon)
    """
    return (round(lat, precision), round(lon, precision))


def find_nearest_coordinate(
    target_lat: float, target_lon: float, coordinates: List[Tuple[float, float]]
) -> Optional[Tuple[float, float, float]]:
    """
    Find the nearest coordinate from a list of coordinates.

    Args:
        target_lat: Target latitude
        target_lon: Target longitude
        coordinates: List of (lat, lon) tuples to search

    Returns:
        Tuple of (nearest_lat, nearest_lon, distance_km) or None if coordinates empty
    """
    if not coordinates:
        return None

    min_distance = float("inf")
    nearest_coord = None

    for lat, lon in coordinates:
        distance = calc_distance(target_lat, target_lon, lat, lon)
        if distance < min_distance:
            min_distance = distance
            nearest_coord = (lat, lon, distance)

    return nearest_coord


def are_coordinates_nearby(
    lat1: float, lon1: float, lat2: float, lon2: float, threshold_km: float = 5.0
) -> bool:
    """
    Check if two coordinates are within a threshold distance.

    Args:
        lat1, lon1: First coordinate
        lat2, lon2: Second coordinate
        threshold_km: Maximum distance in kilometers

    Returns:
        True if coordinates are within threshold distance
    """
    distance = calc_distance(lat1, lon1, lat2, lon2)
    return distance <= threshold_km


def get_coordinate_bounds(coordinates: List[Tuple[float, float]]) -> Optional[dict]:
    """
    Get the bounding box of a list of coordinates.

    Args:
        coordinates: List of (lat, lon) tuples

    Returns:
        Dict with min_lat, max_lat, min_lon, max_lon or None if coordinates empty
    """
    if not coordinates:
        return None

    lats = [coord[0] for coord in coordinates]
    lons = [coord[1] for coord in coordinates]

    return {
        "min_lat": min(lats),
        "max_lat": max(lats),
        "min_lon": min(lons),
        "max_lon": max(lons),
    }
