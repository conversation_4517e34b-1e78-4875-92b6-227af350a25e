"""
Enhanced Geocoding Service for IJack Technologies

This service provides hybrid geocoding capabilities supporting:
- Reverse geocoding with caching for optimal performance
- International support for North America, South America, and Middle East
- Intelligent fallbacks using major city clustering
- DRY principle with shared utilities

Phase 3 implementation - keeping it simple and focused.
"""

from typing import Dict, List, Optional, Tuple

from shared.utils.geographic_utils import calc_distance, round_coordinates_for_cache


class HybridGeocodingService:
    """
    Ultimate geocoding solution supporting worldwide locations with intelligent fallbacks.

    Key features:
    - Coordinate-based region clustering for areas without administrative boundaries
    - Major city proximity detection for meaningful region names
    - Caching for performance optimization
    - International support for global operations
    """

    def __init__(self):
        self.cache_precision = 3  # ~100m precision for caching
        self.major_cities = self._init_major_cities()

    def _init_major_cities(self) -> Dict[str, Dict]:
        """Initialize major cities database for geographic clustering"""
        return {
            # North America - Canada
            "calgary": {
                "lat": 51.0447,
                "lon": -114.0719,
                "province": "Alberta",
                "country": "CA",
            },
            "edmonton": {
                "lat": 53.5461,
                "lon": -113.4938,
                "province": "Alberta",
                "country": "CA",
            },
            "vancouver": {
                "lat": 49.2827,
                "lon": -123.1207,
                "province": "British Columbia",
                "country": "CA",
            },
            "toronto": {
                "lat": 43.6532,
                "lon": -79.3832,
                "province": "Ontario",
                "country": "CA",
            },
            "saskatoon": {
                "lat": 52.1579,
                "lon": -106.6702,
                "province": "Saskatchewan",
                "country": "CA",
            },
            "regina": {
                "lat": 50.4452,
                "lon": -104.6189,
                "province": "Saskatchewan",
                "country": "CA",
            },
            "winnipeg": {
                "lat": 49.8951,
                "lon": -97.1384,
                "province": "Manitoba",
                "country": "CA",
            },
            # North America - USA
            "houston": {
                "lat": 29.7604,
                "lon": -95.3698,
                "province": "Texas",
                "country": "US",
            },
            "dallas": {
                "lat": 32.7767,
                "lon": -96.7970,
                "province": "Texas",
                "country": "US",
            },
            "denver": {
                "lat": 39.7392,
                "lon": -104.9903,
                "province": "Colorado",
                "country": "US",
            },
            "oklahoma_city": {
                "lat": 35.4676,
                "lon": -97.5164,
                "province": "Oklahoma",
                "country": "US",
            },
            "midland": {
                "lat": 32.0000,
                "lon": -102.0779,
                "province": "Texas",
                "country": "US",
            },
            # South America
            "bogota": {
                "lat": 4.7110,
                "lon": -74.0721,
                "province": "Cundinamarca",
                "country": "CO",
            },
            "caracas": {
                "lat": 10.4806,
                "lon": -66.9036,
                "province": "Distrito Capital",
                "country": "VE",
            },
            "lima": {
                "lat": -12.0464,
                "lon": -77.0428,
                "province": "Lima",
                "country": "PE",
            },
            "quito": {
                "lat": -0.1807,
                "lon": -78.4678,
                "province": "Pichincha",
                "country": "EC",
            },
            "barranquilla": {
                "lat": 10.9685,
                "lon": -74.7813,
                "province": "Atlantico",
                "country": "CO",
            },
            "maracaibo": {
                "lat": 10.6667,
                "lon": -71.6167,
                "province": "Zulia",
                "country": "VE",
            },
            # Middle East
            "riyadh": {
                "lat": 24.7136,
                "lon": 46.6753,
                "province": "Riyadh Province",
                "country": "SA",
            },
            "dubai": {
                "lat": 25.2048,
                "lon": 55.2708,
                "province": "Dubai",
                "country": "AE",
            },
            "abu_dhabi": {
                "lat": 24.4539,
                "lon": 54.3773,
                "province": "Abu Dhabi",
                "country": "AE",
            },
            "doha": {
                "lat": 25.2854,
                "lon": 51.5310,
                "province": "Ad Dawhah",
                "country": "QA",
            },
            "kuwait_city": {
                "lat": 29.3759,
                "lon": 47.9774,
                "province": "Al Asimah",
                "country": "KW",
            },
            "manama": {
                "lat": 26.2285,
                "lon": 50.5860,
                "province": "Capital",
                "country": "BH",
            },
        }

    def get_geographic_region_name(self, lat: float, lon: float) -> Dict[str, str]:
        """
        Get a meaningful geographic region name for coordinates.

        This is the core method that provides intelligent geographic identification
        without requiring external API calls.

        Args:
            lat: Latitude in decimal degrees
            lon: Longitude in decimal degrees

        Returns:
            Dict with region_name, country_name, clustering_method, and distance info
        """
        # Step 1: Find nearest major city
        nearest_city = self._find_nearest_major_city(lat, lon)

        if nearest_city and nearest_city["distance_km"] < 100:
            # Very close to a major city - use city name
            return {
                "region_name": f"Near {nearest_city['name']}",
                "country_name": self._get_country_name(nearest_city["country"]),
                "province_name": nearest_city["province"],
                "clustering_method": "city_proximity",
                "distance_to_reference": nearest_city["distance_km"],
            }

        elif nearest_city and nearest_city["distance_km"] < 300:
            # Moderately close to a major city - use regional description
            return {
                "region_name": f"{nearest_city['name']} Region",
                "country_name": self._get_country_name(nearest_city["country"]),
                "province_name": nearest_city["province"],
                "clustering_method": "regional_proximity",
                "distance_to_reference": nearest_city["distance_km"],
            }

        else:
            # Far from any major city - use coordinate-based naming
            return {
                "region_name": f"Region ({lat:.1f}°, {lon:.1f}°)",
                "country_name": self._infer_country_from_coordinates(lat, lon),
                "province_name": "Remote Area",
                "clustering_method": "coordinate_based",
                "distance_to_reference": None,
            }

    def _find_nearest_major_city(self, lat: float, lon: float) -> Optional[Dict]:
        """Find the nearest major city from our predefined list"""
        min_distance = float("inf")
        nearest_city = None

        for city_name, city_data in self.major_cities.items():
            distance = calc_distance(lat, lon, city_data["lat"], city_data["lon"])
            if distance < min_distance:
                min_distance = distance
                nearest_city = {
                    "name": city_name.replace("_", " ").title(),
                    "distance_km": distance,
                    **city_data,
                }

        return nearest_city

    def _get_country_name(self, country_code: str) -> str:
        """Convert country code to full country name"""
        country_names = {
            "CA": "Canada",
            "US": "United States",
            "CO": "Colombia",
            "VE": "Venezuela",
            "PE": "Peru",
            "EC": "Ecuador",
            "SA": "Saudi Arabia",
            "AE": "United Arab Emirates",
            "QA": "Qatar",
            "KW": "Kuwait",
            "BH": "Bahrain",
        }
        return country_names.get(country_code, "Unknown Country")

    def _infer_country_from_coordinates(self, lat: float, lon: float) -> str:
        """Basic geographic inference for country based on coordinate ranges"""
        # North America
        if 24 <= lat <= 70 and -170 <= lon <= -50:
            return "North America"
        # South America
        elif -60 <= lat <= 15 and -85 <= lon <= -30:
            return "South America"
        # Middle East
        elif 12 <= lat <= 42 and 34 <= lon <= 75:
            return "Middle East"
        # Default
        else:
            return "International"

    def cluster_coordinates_by_region(
        self, coordinates: List[Tuple[float, float]]
    ) -> Dict[str, List]:
        """
        Group coordinates into meaningful geographic regions.

        This method is used for service analytics to group structures
        by geographic region for business intelligence.
        """
        regions = {}

        for lat, lon in coordinates:
            region_info = self.get_geographic_region_name(lat, lon)
            region_key = region_info["region_name"]

            if region_key not in regions:
                regions[region_key] = {
                    "coordinates": [],
                    "country_name": region_info["country_name"],
                    "province_name": region_info["province_name"],
                    "clustering_method": region_info["clustering_method"],
                }

            regions[region_key]["coordinates"].append((lat, lon))

        return regions

    def get_cache_key(self, lat: float, lon: float) -> Tuple[float, float]:
        """Get rounded coordinates for caching purposes"""
        return round_coordinates_for_cache(lat, lon, precision=self.cache_precision)

    def generate_service_analytics_regions(
        self, structure_coordinates: List[Dict]
    ) -> List[Dict]:
        """
        Generate geographic regions for service analytics.

        This method takes a list of structures with coordinates and groups them
        into meaningful geographic regions for business analysis.

        Args:
            structure_coordinates: List of dicts with 'lat', 'lon', 'structure_id', etc.

        Returns:
            List of geographic regions with aggregated data
        """
        region_groups = {}

        for structure in structure_coordinates:
            lat, lon = structure["lat"], structure["lon"]
            region_info = self.get_geographic_region_name(lat, lon)
            region_key = region_info["region_name"]

            if region_key not in region_groups:
                region_groups[region_key] = {
                    "region_name": region_key,
                    "country_name": region_info["country_name"],
                    "province_name": region_info["province_name"],
                    "clustering_method": region_info["clustering_method"],
                    "structures": [],
                    "avg_lat": 0,
                    "avg_lon": 0,
                    "structure_count": 0,
                }

            region_groups[region_key]["structures"].append(structure)
            region_groups[region_key]["structure_count"] += 1

        # Calculate average coordinates for each region
        for region in region_groups.values():
            if region["structures"]:
                avg_lat = sum(s["lat"] for s in region["structures"]) / len(
                    region["structures"]
                )
                avg_lon = sum(s["lon"] for s in region["structures"]) / len(
                    region["structures"]
                )
                region["avg_lat"] = avg_lat
                region["avg_lon"] = avg_lon

        return list(region_groups.values())


# Simple factory function for easy import
def get_geocoding_service() -> HybridGeocodingService:
    """Factory function to get a geocoding service instance"""
    return HybridGeocodingService()
