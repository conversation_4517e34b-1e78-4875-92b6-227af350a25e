"""
Background Task Scheduler using APScheduler

Lightweight alternative to Celery for running periodic tasks like geocoding.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

from shared.jobs.geocoding_job_async import AsyncGeocodingJob

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TaskScheduler:
    """Manages scheduled background tasks for the application."""

    def __init__(self, db_url: Optional[str] = None):
        self.scheduler = AsyncIOScheduler()
        self.db_url = db_url or os.environ.get("DATABASE_URL")

        if not self.db_url:
            raise ValueError("Database URL not provided")

        # Convert to async URL if needed
        if self.db_url.startswith("postgresql://"):
            self.db_url = self.db_url.replace("postgresql://", "postgresql+psycopg://")

    async def geocoding_task(self):
        """Run the geocoding job."""
        logger.info(f"Starting scheduled geocoding job at {datetime.utcnow()}")

        try:
            job = AsyncGeocodingJob(db_url=self.db_url, batch_size=100)
            results = await job.run_job(
                limit=500
            )  # Process up to 500 structures per run

            logger.info(
                f"Geocoding job completed: {results['total_success']} success, "
                f"{results['total_errors']} errors"
            )
        except Exception as e:
            logger.error(f"Error in geocoding job: {e}", exc_info=True)

    async def cache_cleanup_task(self):
        """Clean up old cache entries."""
        logger.info(f"Starting cache cleanup at {datetime.utcnow()}")

        try:
            job = AsyncGeocodingJob(db_url=self.db_url)
            await job.cleanup_old_cache(days=30)
            logger.info("Cache cleanup completed")
        except Exception as e:
            logger.error(f"Error in cache cleanup: {e}", exc_info=True)

    def setup_jobs(self):
        """Configure all scheduled jobs."""

        # Geocoding job - runs every 6 hours
        self.scheduler.add_job(
            self.geocoding_task,
            trigger=IntervalTrigger(hours=6),
            id="geocoding_job",
            name="Geocode structures without province data",
            replace_existing=True,
            max_instances=1,  # Prevent overlapping runs
        )

        # Cache cleanup - runs daily at 2 AM
        self.scheduler.add_job(
            self.cache_cleanup_task,
            trigger=CronTrigger(hour=2, minute=0),
            id="cache_cleanup",
            name="Clean up old geocoding cache entries",
            replace_existing=True,
            max_instances=1,
        )

        logger.info("Scheduled jobs configured:")
        for job in self.scheduler.get_jobs():
            logger.info(f"  - {job.name}: {job.trigger}")

    def start(self):
        """Start the scheduler."""
        self.setup_jobs()
        self.scheduler.start()
        logger.info("Task scheduler started")

    def stop(self):
        """Stop the scheduler."""
        self.scheduler.shutdown(wait=True)
        logger.info("Task scheduler stopped")


# Global scheduler instance
_scheduler: Optional[TaskScheduler] = None


def get_scheduler(db_url: Optional[str] = None) -> TaskScheduler:
    """Get or create the global scheduler instance."""
    global _scheduler

    if _scheduler is None:
        _scheduler = TaskScheduler(db_url)

    return _scheduler


async def run_geocoding_once(db_url: Optional[str] = None, limit: int = 100):
    """Run geocoding job once (useful for testing or manual runs)."""
    url = db_url or os.environ.get("DATABASE_URL")
    if not url:
        raise ValueError("Database URL not provided")

    if url.startswith("postgresql://"):
        url = url.replace("postgresql://", "postgresql+psycopg://")

    job = AsyncGeocodingJob(db_url=url, batch_size=50)
    results = await job.run_job(limit=limit)

    return results


# CLI interface
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Task scheduler management")
    parser.add_argument(
        "command", choices=["start", "run-once", "list-jobs"], help="Command to execute"
    )
    parser.add_argument(
        "--db-url", help="Database URL (defaults to DATABASE_URL env var)"
    )
    parser.add_argument(
        "--limit", type=int, default=100, help="Limit for run-once command"
    )

    args = parser.parse_args()

    if args.command == "start":
        # Start the scheduler and keep it running
        scheduler = get_scheduler(args.db_url)
        scheduler.start()

        try:
            # Keep the script running
            asyncio.get_event_loop().run_forever()
        except KeyboardInterrupt:
            logger.info("Shutting down scheduler...")
            scheduler.stop()

    elif args.command == "run-once":
        # Run geocoding once
        results = asyncio.run(run_geocoding_once(args.db_url, args.limit))
        print(f"Geocoding results: {results}")

    elif args.command == "list-jobs":
        # List scheduled jobs
        scheduler = get_scheduler(args.db_url)
        scheduler.setup_jobs()

        print("Scheduled jobs:")
        for job in scheduler.scheduler.get_jobs():
            print(f"  - {job.id}: {job.name}")
            print(f"    Trigger: {job.trigger}")
            print(f"    Next run: {job.next_run_time}")
