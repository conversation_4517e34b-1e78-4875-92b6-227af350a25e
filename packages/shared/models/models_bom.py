"""
Inventory Management System Database Models

This module defines all SQLAlchemy models for the inventory management system,
following best practices for warehouse and parts management including:
- Multi-warehouse inventory tracking
- Reservation system with quantity management
- Stock movement audit trails
- Cycle counting support
- Reorder point management
"""

import logging
from enum import Enum

from sqlalchemy import (
    Column,
    Computed,
    ForeignKey,
    Index,
    UniqueConstraint,
    func,
)
from sqlalchemy.dialects.postgresql import (
    BIGINT,
    BOOLEAN,
    BYTEA,
    DATE,
    DOUBLE_PRECISION,
    INTEGER,
    JSONB,
    NUMERIC,
    REAL,
    SMALLINT,
    TEXT,
    TIMESTAMP,
    VARCHAR,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from shared.config import USER_ID_SEAN
from shared.models.base import Base
from shared.models.models_work_order import work_order_model_type_rel
from shared.utils.date_utils import utcnow_naive

logger = logging.getLogger(__name__)


class BOMPricingModelType(Base):
    """
    Many-to-many relationship between ModelType and BOMPricing classes/tables
    """

    __tablename__ = "bom_pricing_model_type_rel"
    __table_args__ = (
        # Part ID and pump top ID must be unique when combined
        UniqueConstraint(
            "finished_good_id",
            "model_type_id",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    finished_good_id = Column(
        INTEGER,
        ForeignKey("public.bom_pricing.id"),
        nullable=False,
    )
    finished_good_rel = relationship("BOMPricing", back_populates="bom_pricing_rel")

    model_type_id = Column(
        INTEGER,
        ForeignKey("public.model_types.id", ondelete="CASCADE"),
        nullable=False,
    )
    model_type_rel = relationship("ModelType", back_populates="bom_pricing_rel")

    # This could be feet, or an integer
    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return f"{self.finished_good_rel} ({self.quantity})"


class ModelTypePart(Base):
    """Many-to-many relationship between ModelType and Part classes/tables"""

    __tablename__ = "model_types_parts_rel"
    __table_args__ = (
        # part_id and model_type_id must be unique when combined
        UniqueConstraint(
            "model_type_id",
            "part_id",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    model_type_id = Column(
        INTEGER,
        ForeignKey("public.model_types.id"),
        nullable=False,
    )
    model_type_rel = relationship("ModelType", back_populates="parts_rel")

    part_id = Column(
        INTEGER,
        ForeignKey("public.parts.id"),
        nullable=False,
    )
    part_rel = relationship("Part", back_populates="model_types_rel")

    # This could be feet, or an integer
    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return (
            f"{self.part_rel.part_num} - {self.part_rel.description} ({self.quantity})"
        )


class ModelTypePartPMSealKit(Base):
    """
    Many-to-many relationship between ModelType and Part classes/tables,
    for specifying how many of each PM Seal Kit is required for each ModelType.
    """

    __tablename__ = "model_types_parts_pm_seal_kits_rel"
    __table_args__ = (
        # part_id and model_type_id must be unique when combined
        UniqueConstraint(
            "model_type_id",
            "part_id",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    model_type_id = Column(
        INTEGER,
        ForeignKey("public.model_types.id"),
        nullable=False,
    )
    model_type_rel = relationship("ModelType", back_populates="pm_seal_kits_rel")

    part_id = Column(
        INTEGER,
        ForeignKey("public.parts.id"),
        nullable=False,
    )
    part_rel = relationship("Part", back_populates="model_types_pm_seal_kits_rel")

    # This could be feet, or an integer
    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return (
            f"{self.part_rel.part_num} - {self.part_rel.description} ({self.quantity})"
        )


class PowerUnitTypePart(Base):
    """
    Many-to-many relationship between PowerUnitType and Part classes/tables.
    The "part" is hydraulic oil, in this case.
    """

    __tablename__ = "power_unit_types_parts_rel"
    __table_args__ = (
        # part_id and power_unit_type_id must be unique when combined
        UniqueConstraint(
            "power_unit_type_id",
            "part_id",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    power_unit_type_id = Column(
        INTEGER,
        ForeignKey("public.power_unit_types.id"),
        nullable=False,
    )
    power_unit_type_rel = relationship("PowerUnitType", back_populates="parts_rel")

    # Hydraulic oil
    part_id = Column(
        INTEGER,
        ForeignKey("public.parts.id"),
        nullable=False,
    )
    part_rel = relationship("Part", back_populates="power_unit_types_rel")

    # This could be feet, or an integer
    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return (
            f"{self.part_rel.part_num} - {self.part_rel.description} ({self.quantity})"
        )


class PowerUnitTypePartFilter(Base):
    """
    Many-to-many relationship between PowerUnitType and PartFilter classes/tables
    """

    __tablename__ = "power_unit_types_filters_rel"
    __table_args__ = (
        # Part ID and part filter ID must be unique when combined
        UniqueConstraint(
            "power_unit_type_id",
            "part_filter_id",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    power_unit_type_id = Column(
        INTEGER,
        ForeignKey("public.power_unit_types.id", ondelete="CASCADE"),
        nullable=False,
    )
    power_unit_type_rel = relationship("PowerUnitType", back_populates="filters_rel")

    part_filter_id = Column(
        INTEGER,
        ForeignKey("public.part_filters.id", ondelete="CASCADE"),
        nullable=False,
    )
    part_filter_rel = relationship("PartFilter", back_populates="power_unit_types_rel")

    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return f"{self.part_filter_rel} ({self.quantity})"


class BOMPricingPart(Base):
    """
    Many-to-many relationship between Part and BOMPricing classes/tables
    """

    __tablename__ = "bom_pricing_part_rel"
    __table_args__ = (
        # Part ID and pump top ID must be unique when combined
        UniqueConstraint(
            "finished_good_id",
            "part_id",
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    finished_good_id = Column(
        INTEGER,
        ForeignKey("public.bom_pricing.id", ondelete="CASCADE"),
        nullable=False,
    )
    finished_good_rel = relationship(
        "BOMPricing", back_populates="bom_pricing_parts_rel"
    )
    # finished_good_name = association_proxy("finished_good_rel", "name")

    part_id = Column(
        INTEGER, ForeignKey("public.parts.id", ondelete="CASCADE"), nullable=False
    )
    part_rel = relationship("Part", back_populates="bom_pricing_parts_rel")
    # part_num = association_proxy("part_rel", "part_num")

    # This could be feet, or an integer
    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return f"{self.part_rel} - {self.finished_good_rel} ({self.quantity})"


class BOMBasePowerUnitPartRel(Base):
    """
    Model representing the many-to-many relationship between BOM base power units and parts
    """

    __tablename__ = "bom_base_powerunit_part_rel"
    __table_args__ = (
        UniqueConstraint("finished_good_id", "part_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True, nullable=False)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    finished_good_id = Column(
        INTEGER, ForeignKey("public.bom_base_powerunit.id"), nullable=False
    )
    finished_good_rel = relationship(
        "BOMBasePowerUnit", back_populates="bom_base_powerunit_part_rel"
    )

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    part_rel = relationship("Part", back_populates="bom_base_powerunit_part_rel")

    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return f"{self.finished_good_rel} - {self.part_rel} ({self.quantity})"


class BOMPowerUnitPartRel(Base):
    """
    Model representing the many-to-many relationship between BOM power units and parts
    """

    __tablename__ = "bom_powerunit_part_rel"
    __table_args__ = (
        UniqueConstraint("finished_good_id", "part_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True, nullable=False)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    finished_good_id = Column(
        INTEGER, ForeignKey("public.bom_powerunit.id"), nullable=False
    )
    finished_good_rel = relationship(
        "BOMPowerUnit", back_populates="bom_powerunit_part_rel"
    )

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    part_rel = relationship("Part", back_populates="bom_powerunit_part_rel")

    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return f"{self.finished_good_rel} - {self.part_rel} ({self.quantity})"


class BOMDGasPartRel(Base):
    """
    Model representing the many-to-many relationship between BOM DGas and parts
    """

    __tablename__ = "bom_dgas_part_rel"
    __table_args__ = (
        UniqueConstraint("finished_good_id", "part_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True, nullable=False)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    finished_good_id = Column(INTEGER, ForeignKey("public.bom_dgas.id"), nullable=False)
    finished_good_rel = relationship("BOMDGAS", back_populates="bom_dgas_part_rel")

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    part_rel = relationship("Part", back_populates="bom_dgas_part_rel")

    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return f"{self.finished_good_rel} - {self.part_rel} ({self.quantity})"


class BOMPumpTopPartRel(Base):
    """
    Model representing the many-to-many relationship between BOM pump tops and parts
    """

    __tablename__ = "bom_pump_top_part_rel"
    __table_args__ = (
        UniqueConstraint("finished_good_id", "part_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True, nullable=False)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    finished_good_id = Column(
        INTEGER, ForeignKey("public.bom_pump_top.id"), nullable=False
    )
    finished_good_rel = relationship(
        "BOMPumpTop", back_populates="bom_pump_top_part_rel"
    )

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    part_rel = relationship("Part", back_populates="bom_pump_top_part_rel")

    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        return f"{self.finished_good_rel} - {self.part_rel} ({self.quantity})"


class BOMStructurePartRel(Base):
    """
    Model representing the many-to-many relationship between BOM structures and parts
    """

    # Set the table name and schema
    __tablename__ = "bom_structure_part_rel"
    __table_args__ = (
        # Create a unique constraint on the combination of finished good and part
        UniqueConstraint("finished_good_id", "part_id"),
        {"schema": "public"},
    )

    # Primary key column
    id = Column(INTEGER, primary_key=True, nullable=False)

    # Automatically track creation and update timestamps
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    # Foreign key to the finished good (BOM structure)
    finished_good_id = Column(
        INTEGER, ForeignKey("public.bom_structure.id"), nullable=False
    )
    # Relationship to the BOM structure
    finished_good_rel = relationship("BOMStructure", back_populates="bom_structure_rel")

    # Foreign key to the part
    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    # Relationship to the part
    part_rel = relationship("Part", back_populates="bom_structure_rel")

    # Quantity of parts needed
    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        """String representation of the relationship"""
        return f"{self.finished_good_rel} - {self.part_rel} ({self.quantity})"


class BOMStructureModelType(Base):
    """
    Create a many-to-many model for the Bill of Materials (BOM) structures and model types
    """

    __tablename__ = "bom_structure_model_type_rel"
    __table_args__ = (
        UniqueConstraint("finished_good_id", "model_type_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    model_type_id = Column(INTEGER, ForeignKey("public.model_types.id"), nullable=False)
    model_type_rel = relationship("ModelType", back_populates="bom_structure_rel")

    finished_good_id = Column(
        INTEGER, ForeignKey("public.bom_structure.id"), nullable=False
    )
    finished_good_rel = relationship("BOMStructure", back_populates="model_type_rel")

    quantity = Column(NUMERIC, nullable=False, default=1.0)

    def __repr__(self):
        # return f"{self.model_type_rel} - {self.finished_good_rel} ({self.quantity})"
        return f"{self.finished_good_rel} ({self.quantity})"


class BOMPumpTopModelType(Base):
    """
    Create a many-to-many model for the Bill of Materials (BOM) pump tops and model types
    """

    __tablename__ = "bom_pump_top_model_type_rel"
    __table_args__ = (
        UniqueConstraint("finished_good_id", "model_type_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    model_type_id = Column(INTEGER, ForeignKey("public.model_types.id"), nullable=False)
    model_type_rel = relationship("ModelType", back_populates="bom_pump_top_rel")

    finished_good_id = Column(
        INTEGER, ForeignKey("public.bom_pump_top.id"), nullable=False
    )
    finished_good_rel = relationship("BOMPumpTop", back_populates="model_type_rel")

    quantity = Column(NUMERIC, nullable=False, default=1.0)

    def __repr__(self):
        # return f"{self.model_type_rel} - {self.finished_good_rel} ({self.quantity})"
        return f"{self.finished_good_rel} ({self.quantity})"


class BOMDGASModelType(Base):
    """
    Create a many-to-many model for the Bill of Materials (BOM) DGAS and model types
    """

    __tablename__ = "bom_dgas_model_type_rel"
    __table_args__ = (
        UniqueConstraint("finished_good_id", "model_type_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    model_type_id = Column(INTEGER, ForeignKey("public.model_types.id"), nullable=False)
    model_type_rel = relationship("ModelType", back_populates="bom_dgas_rel")

    finished_good_id = Column(INTEGER, ForeignKey("public.bom_dgas.id"), nullable=False)
    finished_good_rel = relationship("BOMDGAS", back_populates="model_type_rel")

    quantity = Column(NUMERIC, nullable=False, default=1.0)

    def __repr__(self):
        # return f"{self.model_type_rel} - {self.finished_good_rel} ({self.quantity})"
        return f"{self.finished_good_rel} ({self.quantity})"


class BOMBasePowerUnitPowerUnitType(Base):
    """
    Create a many-to-many relationship table representation
    """

    __tablename__ = "bom_base_powerunit_power_unit_type_rel"
    __table_args__ = (
        UniqueConstraint("finished_good_id", "power_unit_type_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    power_unit_type_id = Column(
        INTEGER, ForeignKey("public.power_unit_types.id"), nullable=False
    )
    power_unit_type_rel = relationship(
        "PowerUnitType", back_populates="bom_base_powerunit_rel"
    )

    finished_good_id = Column(
        INTEGER, ForeignKey("public.bom_base_powerunit.id"), nullable=False
    )
    finished_good_rel = relationship(
        "BOMBasePowerUnit", back_populates="power_unit_type_rel"
    )

    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        # return (
        #     f"{self.power_unit_type_rel} - {self.finished_good_rel} ({self.quantity})"
        # )
        return f"{self.finished_good_rel} ({self.quantity})"


class BOMPowerUnitPowerUnitType(Base):
    """
    Create a many-to-many relationship table representation
    """

    __tablename__ = "bom_powerunit_power_unit_type_rel"
    __table_args__ = (
        UniqueConstraint("power_unit_type_id", "finished_good_id"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    power_unit_type_id = Column(
        INTEGER, ForeignKey("public.power_unit_types.id"), nullable=False
    )
    power_unit_type_rel = relationship(
        "PowerUnitType", back_populates="bom_powerunit_rel"
    )

    finished_good_id = Column(
        INTEGER, ForeignKey("public.bom_powerunit.id"), nullable=False
    )
    finished_good_rel = relationship(
        "BOMPowerUnit", back_populates="power_unit_type_rel"
    )

    quantity = Column(NUMERIC, nullable=False, default=1)

    def __repr__(self):
        # return (
        #     f"{self.power_unit_type_rel} - {self.finished_good_rel} ({self.quantity})"
        # )
        return f"{self.finished_good_rel} ({self.quantity})"


class ModelType(Base):
    """
    Create a model for IJACK structure models such as XFER 2270, EGAS 1035, etc
    """

    __tablename__ = "model_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    part_id = Column(
        INTEGER,
        ForeignKey("public.parts.id"),
        nullable=True,
    )
    part_rel = relationship("Part", back_populates="model_type_rel")

    # NOTE: change nullable=True to nullable=False at some point once they're filled in
    part_num = Column(VARCHAR, unique=True, nullable=True)
    # Generated, calculated field for high-level part name not including the revision number
    part_name = Column(
        VARCHAR,
        Computed(r"regexp_replace(part_num, 'r\d*$'::text, ''::text)"),
        # nullable=False,
    )
    # Generated, calculated field for the revision number
    part_rev = Column(
        DOUBLE_PRECISION,
        Computed(
            r"case when substring(part_num FROM 'r(\d+)'::text)::double precision is null then 0::double precision else substring(part_num FROM 'r(\d+)'::text)::double precision end"
        ),
        # nullable=False,
    )

    model = Column(VARCHAR, unique=True, nullable=False)
    description = Column(TEXT)
    can_show_to_customers = Column(BOOLEAN, default=True, nullable=False)
    color = Column(VARCHAR)
    max_delta_p = Column(INTEGER)

    # Enter the unit_type ID from the 'unit_types' table
    unit_type_id = Column(INTEGER, ForeignKey("public.unit_types.id"), nullable=False)
    unit_types_rel = relationship("UnitType", back_populates="models_rel")

    # pump_top_id = Column(INTEGER, ForeignKey("public.pump_tops.id"))
    # pump_top_rel = relationship("PumpTop", back_populates="model_types_rel")

    structures_rel = relationship(
        "Structure",
        back_populates="model_types_rel",
        foreign_keys="Structure.model_type_id",
    )

    calculators_rel = relationship("Calculator", back_populates="model_types_rel")

    # work_orders_rel = relationship("WorkOrder", back_populates="model_rel")
    work_orders_rel = relationship(
        "WorkOrder",
        secondary=work_order_model_type_rel,
        back_populates="model_types_rel",
    )

    parts_rel = relationship("ModelTypePart", back_populates="model_type_rel")
    pm_seal_kits_rel = relationship(
        "ModelTypePartPMSealKit", back_populates="model_type_rel"
    )

    bom_pricing_rel = relationship(
        "BOMPricingModelType", back_populates="model_type_rel"
    )
    # bom_structure_rel = relationship(
    #     "BOMStructure",
    #     back_populates="model_type_rel",
    #     secondary="public.bom_structure_model_type_rel",
    # )
    bom_structure_rel = relationship(
        "BOMStructureModelType", back_populates="model_type_rel"
    )
    # bom_pump_top_rel = relationship(
    #     "BOMPumpTop",
    #     back_populates="model_type_rel",
    #     secondary="public.bom_pump_top_model_type_rel",
    # )
    bom_pump_top_rel = relationship(
        "BOMPumpTopModelType", back_populates="model_type_rel"
    )
    # bom_dgas_rel = relationship(
    #     "BOMDGAS",
    #     back_populates="model_type_rel",
    #     secondary="public.bom_dgas_model_type_rel",
    # )
    bom_dgas_rel = relationship("BOMDGASModelType", back_populates="model_type_rel")

    def __repr__(self):
        return str(self.model)


class PowerUnitType(Base):
    """
    Create a public.power_unit_types table representation
    """

    __tablename__ = "power_unit_types"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    part_id = Column(
        INTEGER,
        ForeignKey("public.parts.id"),
        nullable=True,
    )
    part_rel = relationship("Part", back_populates="power_unit_type_rel")

    # NOTE: change nullable=True to nullable=False at some point once they're filled in
    part_num = Column(VARCHAR, unique=True, nullable=True)
    # Generated, calculated field for high-level part name not including the revision number
    part_name = Column(
        VARCHAR,
        Computed(r"regexp_replace(part_num, 'r\d*$'::text, ''::text)"),
        # nullable=False,
    )
    # Generated, calculated field for the revision number
    part_rev = Column(
        DOUBLE_PRECISION,
        Computed(
            r"case when substring(part_num FROM 'r(\d+)'::text)::double precision is null then 0::double precision else substring(part_num FROM 'r(\d+)'::text)::double precision end"
        ),
        # nullable=False,
    )

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT, unique=False)

    power_units_rel = relationship("PowerUnit", back_populates="power_unit_type_rel")
    calculators_rel = relationship("Calculator", back_populates="power_unit_type_rel")

    # bom_base_powerunit_rel = relationship(
    #     "BOMBasePowerUnit",
    #     back_populates="power_unit_type_rel",
    #     secondary="public.bom_base_powerunit_power_unit_type_rel",
    # )
    bom_base_powerunit_rel = relationship(
        "BOMBasePowerUnitPowerUnitType", back_populates="power_unit_type_rel"
    )

    # bom_powerunit_rel = relationship(
    #     "BOMPowerUnit",
    #     back_populates="power_unit_type_rel",
    #     # secondary="public.bom_powerunit_power_unit_type_rel",
    # )
    bom_powerunit_rel = relationship(
        "BOMPowerUnitPowerUnitType", back_populates="power_unit_type_rel"
    )

    parts_rel = relationship("PowerUnitTypePart", back_populates="power_unit_type_rel")

    filters_rel = relationship(
        "PowerUnitTypePartFilter", back_populates="power_unit_type_rel"
    )

    def __repr__(self):
        return str(self.name)

    def __str__(self):
        return str(self.name)


class Warehouse(Base):
    """
    Warehouse/location master table.
    Supports multi-warehouse inventory management.
    """

    __tablename__ = "warehouses"
    __table_args__ = {
        "schema": "public",
        "comment": "Master table for warehouses and storage locations",
    }

    id = Column(INTEGER, primary_key=True)

    # Audit fields
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)
    can_show_to_customers = Column(BOOLEAN, default=True, nullable=False)

    # Warehouse settings
    is_active = Column(BOOLEAN, nullable=False, default=True)
    is_main_warehouse = Column(BOOLEAN, nullable=False, default=False)
    # Currently (2025-06-06) all warehouses allow negative stock.
    # This is to allow for work orders to be created without having to have stock on hand.
    # This will be changed in the future to allow for negative stock only on certain warehouses.
    allows_negative_stock = Column(BOOLEAN, nullable=False, default=False)

    gps_lat = Column(REAL)
    gps_lon = Column(REAL)

    address = Column(TEXT)
    city = Column(VARCHAR)
    zip_code = Column(VARCHAR)

    province_id = Column(INTEGER, ForeignKey("public.provinces.id"))
    province_rel = relationship("Province", back_populates="warehouses_rel")

    country_id = Column(INTEGER, ForeignKey("public.countries.id"))
    country_rel = relationship("Country", back_populates="warehouses_rel")

    time_zone_id = Column(INTEGER, ForeignKey("public.time_zones.id"))
    time_zone_rel = relationship("TimeZone", back_populates="warehouses_rel")

    structures_rel = relationship("Structure", back_populates="warehouse_rel")
    service_clock_rel = relationship("ServiceClock", back_populates="warehouse_rel")
    parts_rel = relationship(
        "WarehousePart", back_populates="warehouse_rel", cascade="all, delete-orphan"
    )
    work_order_parts_rel = relationship(
        "WorkOrderPart",
        back_populates="warehouses_rel",
        foreign_keys="WorkOrderPart.warehouse_id",
    )
    work_orders_rel = relationship("WorkOrder", back_populates="warehouse_rel")
    locations_rel = relationship(
        "WarehouseLocation",
        back_populates="warehouse_rel",
        cascade="all, delete-orphan",
    )
    movements_from_rel = relationship(
        "InventoryMovement",
        foreign_keys="InventoryMovement.from_warehouse_id",
        back_populates="from_warehouse_rel",
    )
    movements_to_rel = relationship(
        "InventoryMovement",
        foreign_keys="InventoryMovement.to_warehouse_id",
        back_populates="to_warehouse_rel",
    )
    ledger_entries_rel = relationship("InventoryLedger", back_populates="warehouse_rel")

    def __repr__(self):
        return str(self.name)


class MovementType(str, Enum):
    """Types of inventory movements"""

    RECEIPT = "receipt"  # Incoming from supplier
    ISSUE = "issue"  # Outgoing to work order
    TRANSFER = "transfer"  # Between warehouses
    ADJUSTMENT = "adjustment"  # Manual adjustment
    CYCLE_COUNT = "cycle_count"  # From cycle counting
    RETURN = "return"  # Customer/supplier return
    DAMAGE = "damage"  # Damaged goods write-off
    CANCELLATION = "cancellation"  # Cancelled orders or movements
    REVERSAL = "reversal"  # Reversal of previous movements


class ReservationStatus(str, Enum):
    """Status of inventory reservations"""

    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    FULFILLED = "fulfilled"


class CycleCountStatus(str, Enum):
    """Status of cycle count tasks"""

    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class WarehouseLocation(Base):
    """
    Storage locations within warehouses (bins, shelves, zones).
    Supports hierarchical location structures.
    """

    __tablename__ = "warehouse_locations"
    __table_args__ = (
        UniqueConstraint("warehouse_id", "location_code", name="uq_warehouse_location"),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)
    warehouse_id = Column(INTEGER, ForeignKey("public.warehouses.id"), nullable=False)
    warehouse_rel = relationship("Warehouse", back_populates="locations_rel")

    # Location identifiers
    location_code = Column(VARCHAR(50), nullable=False, index=True)
    location_name = Column(VARCHAR(200))

    # Hierarchical structure
    parent_location_id = Column(INTEGER, ForeignKey("public.warehouse_locations.id"))
    parent_location_rel = relationship("WarehouseLocation", remote_side=[id])

    # Location type (zone, aisle, rack, shelf, bin)
    location_type = Column(VARCHAR(50))

    # Physical attributes
    max_weight_kg = Column(NUMERIC(15, 2))
    max_volume_m3 = Column(NUMERIC(15, 2))

    # Status
    is_active = Column(BOOLEAN, nullable=False, default=True)
    is_pickable = Column(BOOLEAN, nullable=False, default=True)
    is_receivable = Column(BOOLEAN, nullable=False, default=True)

    # Audit fields
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    def __repr__(self):
        return f"<Location({self.warehouse_rel.code}-{self.location_code})>"


class WarehousePart(Base):
    """
    Junction table tracking inventory quantities by warehouse and part.
    This is the core inventory tracking table.
    """

    __tablename__ = "warehouses_parts_rel"
    __table_args__ = (
        UniqueConstraint("warehouse_id", "part_id", name="uq_warehouse_part"),
        # CheckConstraint("quantity >= 0", name="check_quantity_positive"),
        # CheckConstraint(
        #     "quantity_reserved <= quantity", name="check_reserved_not_exceed"
        # ),
        Index(
            "idx_warehouse_part_available",
            "warehouse_id",
            "part_id",
            "quantity_available",
        ),
        {"schema": "public", "comment": "Core inventory tracking table"},
    )

    id = Column(INTEGER, primary_key=True)

    # Foreign keys
    warehouse_id = Column(INTEGER, ForeignKey("public.warehouses.id"), nullable=False)
    warehouse_rel = relationship("Warehouse", back_populates="parts_rel")

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    part_rel = relationship("Part", back_populates="warehouses_rel")

    # Core inventory quantities
    quantity = Column(
        NUMERIC, nullable=False, default=0, comment="Total quantity on hand"
    )
    quantity_reserved = Column(
        NUMERIC,
        nullable=False,
        default=0,
        comment="Quantity reserved for orders",
    )
    # Computed available quantity
    quantity_available = Column(
        NUMERIC,
        Computed(
            "quantity - quantity_reserved",
            persisted=True,  # Persisted for performance
        ),
        # nullable=False,
        # server_default="0",
        comment="Available quantity (computed: quantity - quantity_reserved)",
    )
    # Re-order quantity
    quantity_desired = Column(
        NUMERIC,
        nullable=False,
        default=0,
        comment="Desired quantity for re-ordering",
    )

    # Location within warehouse (optional, for bin-level tracking)
    default_location_id = Column(INTEGER, ForeignKey("public.warehouse_locations.id"))
    default_location_rel = relationship("WarehouseLocation")

    # Warehouse-specific reorder settings (overrides part defaults)
    # TODO: shouldn't these be in the warehouse table?
    warehouse_min_stock = Column(NUMERIC(15, 2))
    warehouse_max_stock = Column(NUMERIC(15, 2))
    warehouse_reorder_point = Column(NUMERIC(15, 2))
    warehouse_reorder_quantity = Column(NUMERIC(15, 2))

    # Inventory valuation
    average_cost = Column(NUMERIC(15, 2), nullable=False, default=0)
    last_cost = Column(NUMERIC(15, 2), nullable=False, default=0)

    # Last activity dates
    last_receipt_date = Column(DATE)
    last_issue_date = Column(DATE)
    last_count_date = Column(DATE)

    # Audit fields
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive, onupdate=utcnow_naive
    )

    @hybrid_property
    def reorder_point(self):
        """Get effective reorder point (warehouse override or part default)"""
        return self.warehouse_reorder_point or self.part_rel.reorder_point

    @hybrid_property
    def is_below_reorder_point(self):
        """Check if inventory is below reorder point"""
        if self.reorder_point is None:
            return False
        return self.quantity_available < self.reorder_point

    def __repr__(self):
        return f"<WarehousePart({self.warehouse_rel}-{self.part_rel.part_num}: {self.quantity})>"


class InventoryReservation(Base):
    """
    Tracks inventory reservations for orders.
    Implements soft allocation to prevent overcommitment.
    """

    __tablename__ = "inventory_reservations"
    __table_args__ = (
        Index("idx_reservation_status", "status"),
        Index("idx_reservation_expiry", "expiry_date"),
        {
            "schema": "public",
            "comment": "Inventory reservations for preventing overcommitment",
        },
    )

    id = Column(INTEGER, primary_key=True)

    # Reservation details
    reservation_number = Column(VARCHAR(50), unique=True, nullable=False, index=True)
    status = Column(
        VARCHAR(20), nullable=False, default=ReservationStatus.PENDING.value
    )

    # work_order_part_id = Column(
    #     INTEGER, ForeignKey("public.work_orders_parts.id"), nullable=False
    # )
    # work_order_part_rel = relationship(
    #     "WorkOrderPart", back_populates="reservations_rel"
    # )

    # What is being reserved
    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    part_rel = relationship("Part", back_populates="reservations_rel")

    warehouse_id = Column(INTEGER, ForeignKey("public.warehouses.id"), nullable=False)
    warehouse_rel = relationship("Warehouse", foreign_keys=[warehouse_id])

    # Optional, for transfers (e.g. from one warehouse to another)
    warehouse_to_id = Column(INTEGER, ForeignKey("public.warehouses.id"), nullable=True)
    warehouse_to_rel = relationship("Warehouse", foreign_keys=[warehouse_to_id])

    # Quantity and dates
    quantity_reserved = Column(NUMERIC(15, 2), nullable=False)
    quantity_fulfilled = Column(NUMERIC(15, 2), nullable=False, default=0)
    # Calculate remaining quantity to fulfill
    quantity_remaining = Column(
        NUMERIC(15, 2),
        Computed("quantity_reserved - quantity_fulfilled", persisted=True),
    )
    reservation_date = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    expiry_date = Column(TIMESTAMP)  # Auto-release if not fulfilled

    # Reference to source document
    reference_type = Column(
        VARCHAR(50), nullable=False
    )  # e.g., 'work_order', 'sales_order'
    reference_id = Column(INTEGER, nullable=False)
    reference_number = Column(VARCHAR(100))

    # Priority for allocation
    priority = Column(INTEGER, nullable=False, default=0)

    # Notes
    notes = Column(TEXT)

    # Audit fields
    created_by_id = Column(INTEGER, ForeignKey("public.users.id"), nullable=False)
    cancelled_by_id = Column(INTEGER, ForeignKey("public.users.id"))
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive, onupdate=utcnow_naive
    )

    def __repr__(self):
        return f"<Reservation({self.reservation_number}: {self.quantity_reserved} {self.part_rel.part_num})>"


class InventoryMovement(Base):
    """
    Tracks all inventory movements (receipts, issues, transfers, adjustments).
    This provides the complete audit trail for inventory changes.
    """

    __tablename__ = "inventory_movements"
    __table_args__ = (
        Index("idx_movement_date", "movement_date"),
        Index(
            "idx_movement_part_warehouse",
            "part_id",
            "from_warehouse_id",
            "to_warehouse_id",
        ),
        {
            "schema": "public",
            "comment": "Complete audit trail of all inventory movements",
        },
    )

    id = Column(BIGINT, primary_key=True)

    # Movement identification
    movement_number = Column(VARCHAR(50), unique=True, nullable=False, index=True)
    movement_type = Column(VARCHAR(20), nullable=False)  # Uses MovementType enum
    movement_date = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    # work_order_part_id = Column(
    #     INTEGER, ForeignKey("public.work_orders_parts.id"), nullable=False
    # )
    # work_order_part_rel = relationship("WorkOrderPart", back_populates="movements_rel")

    # Part being moved
    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    part_rel = relationship("Part", back_populates="movements_rel")

    # Quantity
    quantity = Column(NUMERIC(15, 2), nullable=False)
    unit_cost = Column(NUMERIC(15, 2), nullable=False)
    total_cost = Column(NUMERIC(15, 2), nullable=False)

    # From/To warehouses (one or both may be null depending on movement type)
    from_warehouse_id = Column(INTEGER, ForeignKey("public.warehouses.id"))
    from_warehouse_rel = relationship(
        "Warehouse",
        foreign_keys=[from_warehouse_id],
        back_populates="movements_from_rel",
    )
    from_location_id = Column(INTEGER, ForeignKey("public.warehouse_locations.id"))

    to_warehouse_id = Column(INTEGER, ForeignKey("public.warehouses.id"))
    to_warehouse_rel = relationship(
        "Warehouse", foreign_keys=[to_warehouse_id], back_populates="movements_to_rel"
    )
    to_location_id = Column(INTEGER, ForeignKey("public.warehouse_locations.id"))

    # Reference to source document
    reference_type = Column(
        VARCHAR(50)
    )  # e.g., 'work_order', 'purchase_order', 'transfer_order'
    reference_id = Column(INTEGER)
    reference_number = Column(VARCHAR(100))

    # Optional lot/serial tracking
    lot_number = Column(VARCHAR(100))
    serial_numbers = Column(JSONB)  # Array of serial numbers if multiple items

    # Notes and reason codes
    notes = Column(TEXT)
    reason_code = Column(VARCHAR(50))

    # Audit fields
    created_by_id = Column(INTEGER, ForeignKey("public.users.id"), nullable=False)
    created_by_rel = relationship("User", foreign_keys=[created_by_id])
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive, onupdate=utcnow_naive
    )

    # Reversal tracking (for corrections)
    is_reversed = Column(BOOLEAN, nullable=False, default=False)
    reversal_movement_id = Column(BIGINT, ForeignKey("public.inventory_movements.id"))
    reversal_movement_rel = relationship("InventoryMovement", remote_side=[id])

    def __repr__(self):
        return f"<Movement({self.movement_number}: {self.movement_type} {self.quantity} {self.part_rel.part_num})>"


class CycleCount(Base):
    """
    Cycle count headers for periodic inventory verification.
    Supports ABC analysis and targeted counting strategies.
    """

    __tablename__ = "cycle_counts"
    __table_args__ = {
        "schema": "public",
        "comment": "Cycle count sessions for inventory accuracy",
    }

    id = Column(INTEGER, primary_key=True)

    # Count identification
    count_number = Column(VARCHAR(50), unique=True, nullable=False, index=True)
    count_name = Column(VARCHAR(200))
    status = Column(
        VARCHAR(20), nullable=False, default=CycleCountStatus.SCHEDULED.value
    )

    # Count scope
    warehouse_id = Column(INTEGER, ForeignKey("public.warehouses.id"), nullable=False)
    warehouse_rel = relationship("Warehouse")
    count_type = Column(VARCHAR(50))  # 'ABC', 'RANDOM', 'CATEGORY', 'LOCATION'

    # Scheduling
    scheduled_date = Column(DATE, nullable=False)
    started_date = Column(TIMESTAMP)
    completed_date = Column(TIMESTAMP)

    # Count parameters
    abc_codes = Column(JSONB)  # ['A', 'B'] for ABC counting
    category_ids = Column(JSONB)  # List of category IDs
    location_ids = Column(JSONB)  # List of location IDs

    # Results summary
    total_items = Column(INTEGER, default=0)
    counted_items = Column(INTEGER, default=0)
    variance_items = Column(INTEGER, default=0)
    total_variance_value = Column(NUMERIC(15, 2), default=0)

    # Audit fields
    created_by_id = Column(INTEGER, ForeignKey("public.users.id"), nullable=False)
    created_by_rel = relationship("User", foreign_keys=[created_by_id])
    assigned_to_id = Column(INTEGER, ForeignKey("public.users.id"))
    assigned_to_rel = relationship("User", foreign_keys=[assigned_to_id])
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive, onupdate=utcnow_naive
    )

    # Relationships
    items_rel = relationship(
        "CycleCountItem", back_populates="cycle_count_rel", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<CycleCount({self.count_number}: {self.status})>"


class CycleCountItem(Base):
    """
    Individual items within a cycle count session.
    Tracks expected vs actual quantities and variances.
    """

    __tablename__ = "cycle_count_items"
    __table_args__ = (
        UniqueConstraint(
            "cycle_count_id", "part_id", "location_id", name="uq_count_part_location"
        ),
        {"schema": "public"},
    )

    id = Column(INTEGER, primary_key=True)

    # Parent count
    cycle_count_id = Column(
        INTEGER, ForeignKey("public.cycle_counts.id"), nullable=False
    )
    cycle_count_rel = relationship("CycleCount", back_populates="items_rel")

    # Item being counted
    part_id = Column(INTEGER, ForeignKey("public.parts.id"), nullable=False)
    part_rel = relationship("Part", back_populates="cycle_count_items_rel")
    location_id = Column(INTEGER, ForeignKey("public.warehouse_locations.id"))

    # Quantities
    system_quantity = Column(NUMERIC(15, 2), nullable=False)
    counted_quantity = Column(NUMERIC(15, 2))
    variance_quantity = Column(NUMERIC(15, 2))

    # Financial impact
    unit_cost = Column(NUMERIC(15, 2), nullable=False)
    variance_value = Column(NUMERIC(15, 2))

    # Count status
    is_counted = Column(BOOLEAN, nullable=False, default=False)
    count_date = Column(TIMESTAMP)
    counted_by_id = Column(INTEGER, ForeignKey("public.users.id"))

    # Variance handling
    variance_reason = Column(VARCHAR(200))
    adjustment_approved = Column(BOOLEAN, default=False)
    adjustment_movement_id = Column(BIGINT, ForeignKey("public.inventory_movements.id"))

    # Notes
    notes = Column(TEXT)

    # Audit fields
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive, onupdate=utcnow_naive
    )

    def __repr__(self):
        return f"<CountItem({self.part_rel.part_num}: {self.system_quantity} vs {self.counted_quantity})>"


class BOMPricing(Base):
    """Unique pricing for each BOM item in the 'Pricing' worksheet. These are finished goods"""

    __tablename__ = "bom_pricing"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)

    # parts_rel = relationship(
    #     "Part",
    #     back_populates="bom_pricing_rel",
    #     secondary="public.bom_pricing_part_rel",
    #     # foreign_keys="[public.bom_pricing_part_rel.part_id]",
    # )
    bom_pricing_parts_rel = relationship(
        "BOMPricingPart", back_populates="finished_good_rel"
    )

    bom_pricing_rel = relationship(
        "BOMPricingModelType", back_populates="finished_good_rel"
    )

    def __repr__(self):
        return str(self.name)


class BOMBasePowerUnit(Base):
    """Bill of Materials for 'Base Powerunit' worksheet"""

    __tablename__ = "bom_base_powerunit"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)

    # parts_rel = relationship(
    #     "Part",
    #     back_populates="bom_base_powerunit_rel",
    #     secondary="public.bom_base_powerunit_part_rel",
    # )

    # power_unit_type_rel = relationship(
    #     "PowerUnitType",
    #     back_populates="bom_base_powerunit_rel",
    #     secondary="public.bom_base_powerunit_power_unit_type_rel",
    # )
    power_unit_type_rel = relationship(
        "BOMBasePowerUnitPowerUnitType", back_populates="finished_good_rel"
    )
    bom_base_powerunit_part_rel = relationship(
        "BOMBasePowerUnitPartRel", back_populates="finished_good_rel"
    )

    def __repr__(self):
        return str(self.name)


class BOMPowerUnit(Base):
    """Bill of Materials for 'Powerunit' worksheet"""

    __tablename__ = "bom_powerunit"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)

    # TODO: I think this is a duplicate of below 'bom_powerunit_part_rel'
    # parts_rel = relationship(
    #     "Part",
    #     back_populates="bom_powerunit_rel",
    #     secondary="public.bom_powerunit_part_rel",
    # )

    # power_unit_type_rel = relationship(
    #     "PowerUnitType",
    #     back_populates="bom_powerunit_rel",
    #     secondary="public.bom_powerunit_power_unit_type_rel",
    # )
    power_unit_type_rel = relationship(
        "BOMPowerUnitPowerUnitType", back_populates="finished_good_rel"
    )

    bom_powerunit_part_rel = relationship(
        "BOMPowerUnitPartRel", back_populates="finished_good_rel"
    )

    def __repr__(self):
        return str(self.name)


class BOMStructure(Base):
    """Bill of Materials for 'Structure' worksheet"""

    __tablename__ = "bom_structure"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)

    # parts_rel = relationship(
    #     "Part",
    #     back_populates="bom_structure_rel",
    #     secondary="public.bom_structure_part_rel",
    # )

    # model_type_rel = relationship(
    #     "ModelType",
    #     back_populates="bom_structure_rel",
    #     secondary="public.bom_structure_model_type_rel",
    # )
    model_type_rel = relationship(
        "BOMStructureModelType", back_populates="finished_good_rel"
    )
    bom_structure_rel = relationship(
        "BOMStructurePartRel", back_populates="finished_good_rel"
    )

    def __repr__(self):
        return str(self.name)


class BOMPumpTop(Base):
    """Bill of Materials for 'Pump Top' worksheet"""

    __tablename__ = "bom_pump_top"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)

    # parts_rel = relationship(
    #     "Part",
    #     back_populates="bom_pump_top_rel",
    #     secondary="public.bom_pump_top_part_rel",
    # )

    # model_type_rel = relationship(
    #     "ModelType",
    #     back_populates="bom_pump_top_rel",
    #     secondary="public.bom_pump_top_model_type_rel",
    # )
    model_type_rel = relationship(
        "BOMPumpTopModelType", back_populates="finished_good_rel"
    )
    bom_pump_top_part_rel = relationship(
        "BOMPumpTopPartRel", back_populates="finished_good_rel"
    )

    def __repr__(self):
        return str(self.name)


class BOMDGAS(Base):
    """Bill of Materials for 'DGAS' worksheet"""

    __tablename__ = "bom_dgas"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    name = Column(VARCHAR, nullable=False, unique=True)
    description = Column(TEXT)

    # parts_rel = relationship(
    #     "Part",
    #     back_populates="bom_dgas_rel",
    #     secondary="public.bom_dgas_part_rel",
    # )
    bom_dgas_part_rel = relationship(
        "BOMDGasPartRel", back_populates="finished_good_rel"
    )

    # model_type_rel = relationship(
    #     "ModelType",
    #     back_populates="bom_dgas_rel",
    #     secondary="public.bom_dgas_model_type_rel",
    # )
    model_type_rel = relationship(
        "BOMDGASModelType", back_populates="finished_good_rel"
    )

    def __repr__(self):
        return str(self.name)


class Part(Base):
    """
    Master part/item table containing all trackable inventory items.
    Includes both purchased parts and manufactured assemblies.
    """

    __tablename__ = "parts"
    __table_args__ = {
        "schema": "public",
        "comment": "Master table for all inventory parts and items",
    }

    id = Column(INTEGER, primary_key=True)
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    part_num = Column(VARCHAR, nullable=False, unique=True, index=True)

    # Generated, calculated field for high-level part name not including the revision number
    part_name = Column(
        VARCHAR,
        Computed(r"regexp_replace(part_num, 'r\d*$'::text, ''::text)"),
        nullable=False,
    )
    # Generated, calculated field for the revision number
    part_rev = Column(
        DOUBLE_PRECISION,
        Computed(
            r"case when substring(part_num FROM 'r(\d+)'::text)::double precision is null then 0::double precision else substring(part_num FROM 'r(\d+)'::text)::double precision end"
        ),
        nullable=False,
    )
    description = Column(VARCHAR)

    # Picture of the part
    part_image = Column(BYTEA)

    # This indicates whether the part should remain in the database even if it's
    # no longer used in the BOM Master spreadsheet. Things like "miscellaneous"
    # for the work order part line items, should be "true" for "no_delete"
    no_delete = Column(BOOLEAN)
    # This indicates that the part is no longer in the BOM Master spreadsheet, so we could delete it
    flagged_for_deletion = Column(BOOLEAN, default=False, nullable=False)

    worksheet = Column(VARCHAR)
    ws_row = Column(SMALLINT)

    cost_cad = Column(NUMERIC(12, 2), nullable=False)
    cost_usd = Column(NUMERIC(12, 2), nullable=False)
    msrp_mult_cad = Column(REAL, nullable=False)
    msrp_mult_usd = Column(REAL, nullable=False)
    msrp_cad = Column(NUMERIC(12, 2), nullable=False)
    msrp_usd = Column(NUMERIC(12, 2), nullable=False)

    transfer_mult_cad_dealer = Column(REAL, nullable=False)
    transfer_mult_usd_dealer = Column(REAL, nullable=False)
    transfer_mult_inc_to_corp = Column(REAL, nullable=False)
    warehouse_mult = Column(REAL, nullable=False)

    dealer_cost_cad = Column(NUMERIC(12, 2), nullable=False)
    dealer_cost_usd = Column(NUMERIC(12, 2), nullable=False)

    ijack_corp_cost = Column(NUMERIC, nullable=False)
    # Generated columns, new to PostgreSQL 12
    ijack_corp_cost_usd = Column(
        NUMERIC(12, 2), Computed("msrp_usd * transfer_mult_inc_to_corp"), nullable=False
    )
    ijack_corp_cost_cad = Column(
        NUMERIC(12, 2), Computed("msrp_cad * transfer_mult_inc_to_corp"), nullable=False
    )

    is_usd = Column(BOOLEAN, nullable=False)
    cad_per_usd = Column(REAL)
    # Is this a soft part for preventative maintenance?
    # This is going to be the PM automatic reset flag
    is_soft_part = Column(BOOLEAN, nullable=False, default=False)
    is_hard_part = Column(BOOLEAN, nullable=False, default=False)

    harmonization_code = Column(VARCHAR)
    country_of_origin = Column(VARCHAR)

    weight = Column(VARCHAR)
    # "mass" is a calculated field that converts the weight to a float, so we can do math on it
    mass = Column(
        REAL,
        Computed(
            r"CASE WHEN ((weight)::text ~ '^[0-9]+\.?[0-9]*$'::text) THEN (weight)::real ELSE NULL::real END"
        ),
        # nullable=False,
    )
    lead_time = Column(REAL)

    work_order_parts_rel = relationship("WorkOrderPart", back_populates="parts_rel")

    # Many-to-many relationships with Bill of Materials Main spreadsheet
    model_type_rel = relationship("ModelType", back_populates="part_rel")
    power_unit_type_rel = relationship("PowerUnitType", back_populates="part_rel")

    model_types_rel = relationship("ModelTypePart", back_populates="part_rel")
    model_types_pm_seal_kits_rel = relationship(
        "ModelTypePartPMSealKit", back_populates="part_rel"
    )

    bom_pricing_parts_rel = relationship("BOMPricingPart", back_populates="part_rel")

    # New relationships 2024-12-17
    bom_base_powerunit_part_rel = relationship(
        "BOMBasePowerUnitPartRel", back_populates="part_rel"
    )
    bom_powerunit_part_rel = relationship(
        "BOMPowerUnitPartRel", back_populates="part_rel"
    )
    bom_dgas_part_rel = relationship("BOMDGasPartRel", back_populates="part_rel")
    bom_pump_top_part_rel = relationship("BOMPumpTopPartRel", back_populates="part_rel")
    bom_structure_rel = relationship("BOMStructurePartRel", back_populates="part_rel")

    power_unit_types_rel = relationship("PowerUnitTypePart", back_populates="part_rel")
    warehouses_rel = relationship("WarehousePart", back_populates="part_rel")

    # Pricing options for parts
    model_type_option_rel = relationship("ModelTypeOption", back_populates="part_rel")
    power_unit_type_option_rel = relationship(
        "PowerUnitTypeOption", back_populates="part_rel"
    )
    power_unit_speed_rel = relationship("PowerUnitTypeSpeed", back_populates="part_rel")
    power_unit_power_rel = relationship("PowerUnitTypePower", back_populates="part_rel")
    power_unit_voltage_rel = relationship(
        "PowerUnitTypeVoltage", back_populates="part_rel"
    )

    # Calculated part number group
    part_num_group = Column(
        VARCHAR,
        Computed(
            r"""CASE
            WHEN part_num ~~ '%%050-%%'::text THEN '050'::text
            WHEN part_num ~~ '%%060-%%'::text THEN '060'::text
            WHEN part_num ~~ '%%070-%%'::text THEN '070'::text
            WHEN part_num = '0'::text THEN '0'::text
            ELSE 'Other'::text
        END"""
        ),
        # nullable=False,
        # index=True,
    )

    # Part categorization
    category_id = Column(INTEGER, ForeignKey("public.part_categories.id"))
    category_rel = relationship("PartCategory", back_populates="parts_rel")

    # Unit of measure
    unit_of_measure = Column(VARCHAR(20), nullable=False, default="EACH")

    # Inventory ledger relationship
    ledger_entries_rel = relationship("InventoryLedger", back_populates="part_rel")

    # Financial data
    unit_cost = Column(
        NUMERIC(15, 2),
        #    nullable=False,
        #    default=0
        # Computed("CASE WHEN is_usd THEN cost_usd ELSE cost_cad END"),
        Computed("cost_cad"),
    )
    unit_price = Column(
        NUMERIC(15, 2),
        # nullable=False,
        # default=0
        # Computed("CASE WHEN is_usd THEN msrp_usd ELSE msrp_cad END"),
        Computed("msrp_cad"),
    )

    # Inventory tracking settings
    is_serialized = Column(BOOLEAN, nullable=False, default=False)
    is_lot_tracked = Column(BOOLEAN, nullable=False, default=False)

    # Reorder settings (default values, can be overridden at warehouse level)
    min_stock_level = Column(NUMERIC(15, 2), nullable=True)
    max_stock_level = Column(NUMERIC(15, 2), nullable=True)
    reorder_point = Column(NUMERIC(15, 2), nullable=True)
    reorder_quantity = Column(NUMERIC(15, 2), nullable=True)

    # Lead time in days
    lead_time_days = Column(INTEGER, nullable=True)

    # Status flags
    is_active = Column(BOOLEAN, nullable=False, default=True)
    is_purchasable = Column(BOOLEAN, nullable=False, default=True)
    is_sellable = Column(BOOLEAN, nullable=False, default=True)

    # Audit fields
    created_by_id = Column(
        INTEGER, ForeignKey("public.users.id"), nullable=False, default=USER_ID_SEAN
    )
    updated_by_id = Column(
        INTEGER, ForeignKey("public.users.id"), nullable=False, default=USER_ID_SEAN
    )

    # Relationships
    movements_rel = relationship("InventoryMovement", back_populates="part_rel")
    reservations_rel = relationship("InventoryReservation", back_populates="part_rel")
    cycle_count_items_rel = relationship("CycleCountItem", back_populates="part_rel")

    def __repr__(self):
        return str(self.part_num)


class PartCategory(Base):
    """
    Categories for organizing parts (hierarchical structure supported).
    Used for reporting and cycle counting strategies.
    """

    __tablename__ = "part_categories"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)
    code = Column(VARCHAR(50), nullable=False, unique=True, index=True)
    name = Column(VARCHAR(200), nullable=False)

    # Hierarchical structure
    parent_category_id = Column(INTEGER, ForeignKey("public.part_categories.id"))
    parent_category_rel = relationship("PartCategory", remote_side=[id])

    # ABC classification for cycle counting
    abc_code = Column(VARCHAR(1))  # A, B, or C

    # Count frequency (days between counts)
    count_frequency_days = Column(INTEGER)

    # Status
    is_active = Column(BOOLEAN, nullable=False, default=True)

    # Audit fields
    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive)

    # Relationships
    parts_rel = relationship("Part", back_populates="category_rel")

    def __repr__(self):
        return f"<Category({self.code}: {self.name})>"


class PartFilter(Base):
    """
    Model for oil filter types used in each power unit type.
    This used to be related to the ModelType class, but now
    it's related to the PowerUnitType class, which makes more sense.
    """

    __tablename__ = "part_filters"
    __table_args__ = {"schema": "public"}

    id = Column(INTEGER, primary_key=True)

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())
    timestamp_utc_updated = Column(
        TIMESTAMP, nullable=False, default=utcnow_naive(), onupdate=utcnow_naive()
    )

    part_num = Column(VARCHAR, unique=True, nullable=False)
    description = Column(TEXT)

    power_unit_types_rel = relationship(
        "PowerUnitTypePartFilter", back_populates="part_filter_rel"
    )

    def __repr__(self):
        return str(self.part_num)


class ModelTypeOption(Base):
    """
    Create a model for the model type options
    This is used to define the options available for each model type
    """

    __tablename__ = "model_types_options"
    __table_args__ = {"schema": "public"}

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), primary_key=True)
    part_rel = relationship("Part", back_populates="model_type_option_rel")

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())

    def __repr__(self):
        return str(self.part_rel.part_name)


class PowerUnitTypeOption(Base):
    """
    Create a model for the power unit type options
    This is used to define the options available for each power unit type
    """

    __tablename__ = "power_unit_types_options"
    __table_args__ = {"schema": "public"}

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), primary_key=True)
    part_rel = relationship("Part", back_populates="power_unit_type_option_rel")

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())

    def __repr__(self):
        return str(self.part_rel.part_name)


class PowerUnitTypeSpeed(Base):
    """
    Create a model for the power unit type speeds
    This is used to define the speed of the power unit types
    """

    __tablename__ = "power_unit_types_speeds"
    __table_args__ = {"schema": "public"}

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), primary_key=True)
    part_rel = relationship("Part", back_populates="power_unit_speed_rel")

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())

    def __repr__(self):
        return str(self.part_rel.part_name)


class PowerUnitTypePower(Base):
    """
    Create a model for the power unit type power
    This is used to define the power of the power unit types
    """

    __tablename__ = "power_unit_types_power"
    __table_args__ = {"schema": "public"}

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), primary_key=True)
    part_rel = relationship("Part", back_populates="power_unit_power_rel")

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())

    def __repr__(self):
        return str(self.part_rel.part_name)


class PowerUnitTypeVoltage(Base):
    """
    Create a model for the power unit type voltage
    This is used to define the voltage of the power unit types
    """

    __tablename__ = "power_unit_types_voltage"
    __table_args__ = {"schema": "public"}

    part_id = Column(INTEGER, ForeignKey("public.parts.id"), primary_key=True)
    part_rel = relationship("Part", back_populates="power_unit_voltage_rel")

    timestamp_utc_inserted = Column(TIMESTAMP, nullable=False, default=utcnow_naive())

    def __repr__(self):
        return str(self.part_rel.part_name)


class InventoryLedgerOperationType(str, Enum):
    """Types of operations tracked in the inventory ledger"""

    RESERVE = "RESERVE"  # Reserve inventory for future use
    RELEASE = "RELEASE"  # Release previously reserved inventory
    CONSUME = "CONSUME"  # Consume reserved inventory (e.g., by movement)
    RESTORE = "RESTORE"  # Restore previously consumed inventory
    ADJUST = "ADJUST"  # Manual adjustment

    @classmethod
    def get_reversal(cls, operation_type: str) -> str:
        """Get the reversal operation for a given operation type"""
        reversal_map = {
            cls.RESERVE: cls.RELEASE,
            cls.RELEASE: cls.RESERVE,
            cls.CONSUME: cls.RESTORE,
            cls.RESTORE: cls.CONSUME,
            cls.ADJUST: cls.ADJUST,  # Adjustments reverse to opposite sign
        }
        return reversal_map.get(operation_type, cls.ADJUST)


class InventoryLedger(Base):
    """
    Immutable ledger of all inventory operations for audit and reconciliation.

    This table provides a complete audit trail of all inventory quantity changes,
    allowing reconstruction of inventory state at any point in time and ensuring
    proper handling of reserved quantities during state transitions.

    Key principles:
    - Append-only (no updates or deletes)
    - Every operation is reversible
    - Reserved quantities are calculated from the ledger
    - Complete audit trail with user and timestamp
    """

    __tablename__ = "inventory_ledger"
    __table_args__ = (
        Index("idx_ledger_warehouse_part", "warehouse_id", "part_id"),
        Index("idx_ledger_reference", "reference_type", "reference_id"),
        Index("idx_ledger_created", "created_at"),
        Index("idx_ledger_operation", "operation_type"),
        {
            "schema": "public",
            "comment": "Immutable ledger for inventory operations audit trail",
        },
    )

    id = Column(BIGINT, primary_key=True)

    # Operation details
    operation_type = Column(
        VARCHAR(20),
        nullable=False,
        comment="Type of operation: RESERVE, RELEASE, CONSUME, RESTORE, ADJUST",
    )

    # What is being affected
    part_id = Column(
        INTEGER,
        ForeignKey("public.parts.id"),
        nullable=False,
        comment="Part being affected",
    )
    part_rel = relationship("Part", back_populates="ledger_entries_rel")

    warehouse_id = Column(
        INTEGER,
        ForeignKey("public.warehouses.id"),
        nullable=False,
        comment="Warehouse being affected",
    )
    warehouse_rel = relationship("Warehouse", back_populates="ledger_entries_rel")

    # Quantity change (positive or negative based on operation)
    quantity = Column(
        NUMERIC(15, 2),
        nullable=False,
        comment="Quantity change (always positive, operation_type determines effect)",
    )

    # Reference to the source of this operation
    reference_type = Column(
        VARCHAR(50),
        nullable=False,
        comment="Type of reference: reservation, movement, manual_adjustment",
    )
    reference_id = Column(INTEGER, nullable=True, comment="ID of the referenced record")

    # Reversal tracking
    reversal_of_id = Column(
        BIGINT,
        ForeignKey("public.inventory_ledger.id"),
        nullable=True,
        comment="If this is a reversal, reference to the original entry",
    )
    reversal_of_rel = relationship(
        "InventoryLedger", remote_side=[id], backref="reversals"
    )

    # Audit fields
    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="When this entry was created",
    )
    created_by_id = Column(
        INTEGER,
        ForeignKey("public.users.id"),
        nullable=False,
        comment="User who created this entry",
    )
    created_by_rel = relationship("User", back_populates="ledger_entries_rel")

    # Additional context
    notes = Column(
        TEXT, nullable=True, comment="Additional notes or context for this operation"
    )

    # Computed field for running balance (can be calculated in queries)
    # This is NOT stored, just shown as an example of how to query
    # running_balance = Column(
    #     NUMERIC(15, 2),
    #     Computed("SUM(CASE WHEN operation_type IN ('RESERVE', 'RESTORE') THEN quantity "
    #              "WHEN operation_type IN ('RELEASE', 'CONSUME') THEN -quantity "
    #              "ELSE 0 END) OVER (PARTITION BY warehouse_id, part_id ORDER BY created_at)",
    #              persisted=False)
    # )
