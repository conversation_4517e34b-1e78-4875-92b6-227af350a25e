````instructions
# GitHub Copilot Instructions

## Project Overview
This project consists of multiple Python web applications using Flask, FastAPI, and Dash, with React and TypeScript for frontend development. The applications are deployed on AWS EC2 using Docker Swarm, with GitHub Actions for CI/CD.

## Technology Stack Preferences

### Backend
- **Python Frameworks**:
  - **Flask**: Used for the main web application with inertia.js integration
  - **FastAPI**: Used for building high-performance APIs
  - **Dash**: Used for data visualization and interactive dashboards
- **Database**:
  - SQLAlchemy ORM with async sessions (AsyncSession)
  - Manual migrations in PGAdmin or similar - we don't use Alembic
  - PostgreSQL for primary data storage
  - Redis for caching and session management
  - Pandas integration via get_dataframe() for analytics
  - Concurrent database operations using asyncio.gather()
  - PgBouncer connection pooling with NullPool

### Frontend
- **React**: Used for building interactive UI components
- **TypeScript**: Preferred over JavaScript for type safety
- **Tailwind CSS**: For styling components
- **Tanstack React Router**: For client-side routing in React applications
- **Inertia.js**: For building single-page applications without building an API
- **React Query ($api)**: For API data fetching and caching via the $api client
- **XState Store**: For global state management (filters, selections)
- **Recharts**: For data visualization and charts
- **AG-Grid**: For complex data tables with infinite scrolling
- **shadcn/ui**: UI component library with customizable components

### Testing
- **Pytest**: Primary testing framework
  - Ensure tests run quickly (aim for < 1s per test) and are self-contained in a single module with no fixtures from other modules, where possible
  - Use mocking and patching when appropriate to isolate tests
  - Use playwright for end-to-end tests when necessary
  - Patches should be applied as decorators (@patch) above test functions
  - Use pytest.mark.asyncio for async test functions
- **Coverage**: Aim for high test coverage, with a focus on core business logic

### Deployment
- **Docker Swarm**: For container orchestration
- **AWS EC2**: Primary hosting environment
- **GitHub Actions**: For CI/CD pipeline automation
- **Docker**: All applications are containerized for consistent deployment

## Coding Style Preferences

### General
- **Extensive Comments**: Add detailed comments explaining complex logic, business rules, and architectural decisions
- **Scalability**: Design with scalability in mind - both in terms of code organization and performance
- **Modular Design**: Prefer small, focused modules with clear responsibilities
- **Type Hints**: Use Python type hints consistently
- **Imported packages**: Import packages at the top of the file instead of in the functions, where possible

### Python Specific
- **PEP 8**: Follow PEP 8 style guide
- **Documentation**: Add docstrings to all functions, classes, and modules
- **Error Handling**: Be explicit about error handling and logging
- **Configuration**: Use environment variables and config classes for settings

### JavaScript/TypeScript Specific
- **Strong Typing**: Leverage TypeScript's type system fully
- **Component Structure**: Organize React components with clear separation of concerns
- **State Management**: Use XState store for global state, React Query for server state
- **API Integration**: Always use $api.useQuery() for data fetching from FastAPI
- **Error Handling**: Implement proper loading, success, and error states for all API calls
- **Filter Patterns**: Use reusable filter components that connect to global state

## Workflow Preferences

### Development Process
- **Test-Driven Development**: Write tests before or alongside implementation
- **Code Reviews**: Be thorough, with comments explaining design choices
- **Documentation**: Keep documentation up-to-date as code changes

### After Code Changes
1. **Test Existing Functionality**: 
   - Search for existing tests relevant to the changes
   - Run tests to ensure they still pass
   - If tests fail, address the failures before proceeding

2. **Add New Tests**:
   - Write unit tests for new functions, modules, or classes
   - Ensure tests are comprehensive and cover edge cases
   - Focus on making tests run quickly to avoid slowing down the test suite
   - Python patches should be like @patch above the test function definition rather than inside the test function
   - Use pytest fixtures for database setup and teardown
   - Mock external dependencies and API calls for unit tests

3. **Documentation**:
   - Update any relevant documentation to reflect changes
   - Add comments explaining complex logic or business rules
   - Update docstrings for modified functions and classes

## Virtual Environments

### Python Virtual Environments
This project uses **uv** as the Python package manager (modern replacement for pip/poetry).

#### Flask Application (flask_app directory)
- **Virtual Environment**: `/project/flask_app/.venv`
- **Activation**: 
  ```bash
  cd /project/flask_app
  source .venv/bin/activate  # Linux/Mac
  # or
  .venv\Scripts\activate  # Windows
  ```
- **Package Management**: 
  ```bash
  uv pip install -r pyproject.toml  # Install dependencies
  uv pip install package_name       # Add new package
  uv pip freeze > requirements.txt  # Export dependencies
  ```
- **Tests Location**: `/project/flask_app/tests`

#### FastAPI Application (fast_api directory)
- **Virtual Environment**: `/project/fast_api/.venv`
- **Activation**: 
  ```bash
  cd /project/fast_api
  source .venv/bin/activate  # Linux/Mac
  # or
  .venv\Scripts\activate  # Windows
  ```
- **Package Management**: Same as Flask app using `uv`
- **Tests Location**: `/project/fast_api/tests`

#### Shared Packages (packages directory)
- **Virtual Environment**: Uses Flask app's venv (`/project/flask_app/.venv`)
- **Installation**: Installed as editable package in both Flask and FastAPI venvs
  ```bash
  # From flask_app or fast_api directory with venv activated:
  pip install -e ../packages
  ```
- **Tests Location**: `/project/packages/tests`
- **Note**: The packages directory contains shared models and utilities used by both Flask and FastAPI

### JavaScript/Node Environments
This project uses **pnpm** for JavaScript package management.

#### React/Inertia Frontend (flask_app/app/inertia/react directory)
- **Node Modules**: Local to the react directory
- **Package Management**:
  ```bash
  cd /project/flask_app/app/inertia/react
  pnpm install        # Install dependencies
  pnpm add package    # Add new package
  pnpm dev           # Start development server
  pnpm build         # Build for production
  ```

#### Flask App JavaScript (flask_app directory)
- **Node Modules**: `/project/flask_app/node_modules`
- **Package Management**: Same as React app using `pnpm`

### Environment Setup Best Practices
1. Always activate the appropriate virtual environment before working
2. Use `uv` for Python package management instead of pip
3. Use `pnpm` for JavaScript package management instead of npm/yarn
4. The shared packages should be installed as editable (-e) in both Flask and FastAPI environments
5. Run tests from the appropriate virtual environment

## Shared Packages and Models
- **Shared Models**: Located in `/project/packages/shared/models/`
  - `models.py`: Core user and system models
  - `models_work_order.py`: Work order and service type models
  - `models_bom.py`: Bill of materials and inventory models
  - All models use SQLAlchemy declarative base
- **Shared Configuration**: Located in `/project/packages/shared/config.py`
  - Contains role IDs, constants, and shared configuration
  - Import like: `from shared.config import ROLE_ID_IJACK_SALES`
- **Shared Utils**: Located in `/project/packages/shared/utils/`
  - Common utility functions for datetime, formatting, etc.

### API Client Pattern

```typescript
/**
 * API Client Configuration (Real Pattern)
 * 
 * This shows the actual $api client setup used throughout the project
 * for consistent API communication with FastAPI backend.
 */
import createClient from "openapi-fetch";
import createFetchClient from "openapi-react-query";
import { components, paths } from "@/types/schema";

// Create OpenAPI fetch client
export const fetchClient = createClient<paths>({
  baseUrl: "/api",
  headers: {
    "Content-Type": "application/json",
  },
});

// Create React Query client with proper typing
export const $api = createFetchClient(fetchClient);

// Type exports for consistent usage
export type ApiPaths = paths;
export type ApiComponents = components;

/**
 * Usage Examples:
 * 
 * GET Request:
 * const { data, isSuccess, isLoading, isError } = $api.useQuery(
 *   "get",
 *   "/v1/customer/"
 * );
 * 
 * POST Request with body:
 * const { data, isSuccess, isLoading, isError } = $api.useQuery(
 *   "post",
 *   "/v1/sales-analytics/total-revenue",
 *   {
 *     body: {
 *       customers: [1, 2, 3],
 *       service_types: [1, 2],
 *       models: [1],
 *       service_dates: {
 *         from: "2024-01-01T00:00:00.000Z",
 *         to: "2024-12-31T23:59:59.999Z",
 *       },
 *     },
 *   }
 * );
 * 
 * Mutations (for POST/PUT/DELETE operations that modify data):
 * const mutation = $api.useMutation("post", "/v1/work-order/create");
 * mutation.mutate({
 *   body: {
 *     customer_id: 1,
 *     service_type_id: 2,
 *     // ... other fields
 *   }
 * });
 */
```

```tsx
/**
 * Sales Cards Component (Real Pattern)
 * 
 * This example shows the actual pattern used in the sales dashboard
 * for displaying key metrics using data from FastAPI backend.
 */
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import { useSalesSelector } from "@/stores/sales/hooks";
import { Skeleton } from "@/components/ui/skeleton";

export const TotalRevenue: React.FC = () => {
  // Get current filter state from XState store
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector((state) => state.context.service_types);
  const model_types = useSalesSelector((state) => state.context.model_types);
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);

  // Fetch data from FastAPI backend using React Query
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/sales-analytics/total-revenue",
    {
      body: {
        customers,
        service_types,
        models: model_types,
        service_dates: {
          from: from?.toISOString() ?? null,
          to: to?.toISOString() ?? null,
        },
      },
    }
  );

  return (
    <Card className="gap-2 rounded-r-none p-2 md:gap-6 md:p-4 lg:rounded-xl lg:p-6">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
      </CardHeader>
      <CardContent>
        {isSuccess ? (
          <>
            <div className="text-xl font-bold md:text-2xl">
              ${data.result?.total_revenue?.toLocaleString() || "0"}
            </div>
            <div className="text-sm text-muted-foreground">
              {data.result?.period || "Selected period"}
            </div>
          </>
        ) : isLoading ? (
          <div className="flex flex-col space-y-1 md:space-y-2">
            <Skeleton className="h-4 w-1/2 md:h-6" />
            <Skeleton className="h-3 w-1/3 md:h-4" />
          </div>
        ) : isError ? (
          <div className="text-xl font-bold md:text-2xl">Error</div>
        ) : null}
      </CardContent>
    </Card>
  );
};

/**
 * Customer Filter Component (Real Pattern)
 * 
 * This shows the actual filter pattern used throughout the application.
 * Fetches options from API and manages state with XState store.
 */
import { FilterMenu } from "@/components/filters/filter-menu";
import { Users } from "lucide-react";

export const CustomerFilter: React.FC = () => {
  // Fetch customer data from FastAPI
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "get",
    "/v1/customer/"
  );

  // Get current filter state and store instance
  const selectedKeys = useSalesSelector((state) => state.context.customers);
  const store = useSalesStore();

  // Handler to update filter state
  const setKeys = (keys: number[]) => {
    store.send({ type: "applyFilter", customers: keys });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      {isSuccess ? (
        <FilterMenu
          options={data.result}
          keyGetter={(item) => item.id}
          labelGetter={(item) => item.customer}
          selectedKeys={selectedKeys}
          setSelectedKeys={setKeys}
          placeholder="Search customers..."
          allSelectedText="All customers"
          noneSelectedText="All customers"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} customers selected`
          }
          emptyWhenAllSelected={true}
          icon={<Users strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        "Error loading customers"
      ) : null}
    </div>
  );
};

/**
 * Chart Component (Real Pattern)
 * 
 * This shows the actual pattern for creating charts that fetch data from FastAPI
 * and respect the current filter state from XState store.
 */
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

export const MonthlyRevenueChart: React.FC = () => {
  // Get filter state from global store
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector((state) => state.context.service_types);
  const model_types = useSalesSelector((state) => state.context.model_types);
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);

  // Fetch chart data from FastAPI with current filters
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/sales-analytics/monthly-revenue-chart",
    {
      body: {
        customers,
        service_types,
        models: model_types,
        service_dates: {
          from: from?.toISOString() ?? null,
          to: to?.toISOString() ?? null,
        },
      },
    }
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Revenue</CardTitle>
      </CardHeader>
      <CardContent>
        {isSuccess && data?.result ? (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={data.result}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="month" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
              />
              <Tooltip 
                formatter={(value: number) => [`$${value.toLocaleString()}`, "Revenue"]}
                labelFormatter={(label) => `Month: ${label}`}
              />
              <Bar dataKey="revenue" fill="#3b82f6" />
            </BarChart>
          </ResponsiveContainer>
        ) : isLoading ? (
          <Skeleton className="h-[400px] w-full" />
        ) : isError ? (
          <div className="flex h-[400px] items-center justify-center">
            <p className="text-muted-foreground text-sm">
              Error loading chart data. Please try again.
            </p>
          </div>
        ) : null}
      </CardContent>
    </Card>
  );
};

/**
 * Work Orders Grid (Real Pattern)
 * 
 * This shows the actual AG-Grid integration with infinite scrolling 
 * and server-side data fetching used in the project.
 */
import { AgGridReact } from "ag-grid-react";
import { ColDef, GridReadyEvent, IDatasource } from "ag-grid-community";
import { GripBar } from "@/components/ui/grip-bar";
import { fetchClient } from "@/api/web-api";
import { toast } from "sonner";
import nProgress from "nprogress";

export const WorkOrdersGrid: React.FC = () => {
  // Get filter state from XState store
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector((state) => state.context.service_types);
  const model_types = useSalesSelector((state) => state.context.model_types);
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);

  // Define AG-Grid column definitions
  const [colDefs] = React.useState<ColDef[]>([
    {
      field: "id",
      headerName: "Work Order",
      minWidth: 120,
      maxWidth: 140,
      cellRenderer: "ExternalLinkRenderer",
    },
    {
      field: "customer",
      headerName: "Customer",
      minWidth: 200,
      filter: "SetFilter",
    },
    {
      field: "date_service",
      headerName: "Service Date",
      minWidth: 150,
      filter: "agDateColumnFilter",
      cellRenderer: "DateRenderer",
    },
    {
      field: "total_sales",
      headerName: "Total Sales",
      minWidth: 120,
      cellRenderer: "NumericRenderer",
    },
  ]);

  // Configure data source for infinite scrolling
  const onGridReady = React.useCallback((params: GridReadyEvent) => {
    const dataSource: IDatasource = {
      rowCount: 0,
      getRows: async ({
        startRow,
        endRow,
        filterModel,
        sortModel,
        successCallback,
      }) => {
        try {
          nProgress.start();
          const response = await fetchClient.POST(
            "/v1/sales-analytics/work-order-grid",
            {
              body: {
                startRow,
                endRow,
                filterModel,
                sortModel,
                // Include global filters
                customers,
                service_types,
                model_types,
                service_dates: {
                  from: from?.toISOString() ?? null,
                  to: to?.toISOString() ?? null,
                },
              },
            }
          );
          nProgress.done();
          
          if (response.data?.rowData) {
            successCallback(response.data.rowData, response.data.rowCount ?? 0);
          } else {
            successCallback([], 0);
          }
        } catch (error) {
          nProgress.done();
          toast.error("Failed to load work orders data.");
          successCallback([], 0);
        }
      },
    };
    params.api.setGridOption("datasource", dataSource);
  }, [customers, service_types, model_types, from, to]);

  return (
    <div className="flex h-fit max-h-full flex-col">
      <GripBar maxHeight={window.innerHeight - 100} className="border-border">
        {(resizeRef) => (
          <div
            ref={resizeRef}
            className="ag-theme-ijack h-[350px] max-h-full min-h-[350px] w-full overflow-auto"
          >
            <AgGridReact
              columnDefs={colDefs}
              rowBuffer={20}
              rowModelType={"infinite"}
              onGridReady={onGridReady}
              enableCellTextSelection
              suppressRowClickSelection
            />
          </div>
        )}
      </GripBar>
    </div>
  );
};

/**
 * Sales Layout (Real Pattern)
 * 
 * This shows the actual layout pattern with sidebar filters and main content area.
 * Uses XState store provider for global state management.
 */
import { Outlet } from "@tanstack/react-router";
import { SalesStoreProvider } from "@/stores/sales/provider";
import { SidebarProvider } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/layout/dashboard-sidebar";
import { SiteNavigation } from "@/components/layout/site-navigation";

export function SalesLayout() {
  return (
    <SalesStoreProvider>
      <div className="relative flex h-full w-full flex-col">
        <SidebarProvider className="flex flex-col">
          <div className="sticky top-1 flex flex-1">
            <div className="sticky top-0 h-screen">
              <DashboardSidebar variant="sidebar">
                <div className="flex flex-col gap-2 p-2">
                  <CustomerFilter />
                  <ServiceTypeFilter />
                  <ModelTypeFilter />
                  <ServiceDateFilter />
                  <ResetFilters />
                </div>
              </DashboardSidebar>
            </div>
            <div className="flex flex-1 flex-col">
              <SiteNavigation
                breadcrumbs={{
                  text: "Dashboards",
                  url: "/",
                  children: [
                    {
                      is_current_path: true,
                      text: "Sales",
                      url: "/sales",
                      children: [],
                    },
                  ],
                  is_current_path: false,
                }}
              />
              <div className="flex flex-1 flex-col overflow-hidden">
                <Outlet />
              </div>
            </div>
          </div>
        </SidebarProvider>
      </div>
    </SalesStoreProvider>
  );
}

/**
 * Date Filter Component (Real Pattern)
 * 
 * This shows the actual date range filtering pattern with presets and custom selection.
 */
import { CalendarFilterMenu } from "@/components/filters/calendar-filter-menu";

export function ServiceDateFilter() {
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);
  const store = useSalesStore();

  const setServiceDates = (dateRange: {
    from: Date | null;
    to: Date | null;
  }) => {
    store.send({
      type: "applyFilter",
      service_date_from: dateRange.from,
      service_date_to: dateRange.to,
    });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <CalendarFilterMenu
        onChange={(range) => setServiceDates(range)}
        range={{ from, to }}
        noneSelectedText="All service dates"
        icon={<Calendar className="h-4 w-4" />}
      />
    </div>
  );
}

/**
 * Reset Filters Component (Real Pattern)
 * 
 * This shows how to implement a reset filters button that clears all filter state.
 */
import { Button } from "@/components/ui/button";
import { RotateCcw } from "lucide-react";

export function ResetFilters() {
  const store = useSalesStore();

  const handleReset = () => {
    store.send({ type: "resetFilters" });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Button 
        variant="outline" 
        size="sm" 
        onClick={handleReset}
        className="w-full justify-start"
      >
        <RotateCcw className="mr-2 h-4 w-4" />
        Reset Filters
      </Button>
    </div>
  );
}
```

### XState Store Pattern

```typescript
/**
 * XState Store Configuration (Real Pattern)
 * 
 * This shows the actual XState store setup used for global state management
 * in the sales dashboard and throughout the application.
 */

// stores/sales/hooks.ts - Core store definition
import React from "react";
import { createStore, SnapshotFromStore } from "@xstate/store";
import { useSelector } from "@xstate/store/react";

export const store = createStore({
  context: { 
    customers: [] as number[], 
    service_types: [] as number[], 
    service_date_from: null as Date | null, 
    service_date_to: null as Date | null, 
    model_types: [] as (number | null)[] 
  },
  on: {
    applyFilter: (context, event: { 
      customers?: number[], 
      service_types?: number[], 
      service_date_from?: Date | null, 
      service_date_to?: Date | null, 
      model_types?: (number | null)[] 
    }) => ({
      ...context,
      customers: event?.customers ?? context.customers,
      service_types: event?.service_types ?? context.service_types,
      service_date_from: event?.service_date_from === undefined ? context.service_date_from : event.service_date_from,
      service_date_to: event?.service_date_to === undefined ? context.service_date_to : event.service_date_to,
      model_types: event?.model_types ?? context.model_types,
    }),
    resetFilters: (context) => ({
      ...context,
      customers: [],
      service_types: [],
      service_date_from: null,
      service_date_to: null,
      model_types: [],
    }),
  },
});

type SalesStore = typeof store;

export const SalesStoreContext = React.createContext<SalesStore | null>(null);

export const useSalesSelector = <T>(selector: (state: SnapshotFromStore<SalesStore>) => T) => {
  const store = React.useContext(SalesStoreContext)
  if (!store) {
    throw new Error('Missing SalesStoreProvider')
  }
  return useSelector(store, selector)
}

export const useSalesStore = () => {
  const store = React.useContext(SalesStoreContext)
  if (!store) {
    throw new Error('Missing SalesStoreProvider')
  }
  return store
}

// stores/sales/store.tsx - Provider component
import { SalesStoreContext, store } from "./hooks";
import React from "react";

export const SalesStoreProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [stateState] = React.useState(store);
  return (
    <SalesStoreContext.Provider value={stateState}>
      {children}
    </SalesStoreContext.Provider>
  );
};

/**
 * Usage Examples:
 * 
 * Reading from store:
 * const customers = useSalesSelector((state) => state.context.customers);
 * const service_types = useSalesSelector((state) => state.context.service_types);
 * 
 * Writing to store:
 * const store = useSalesStore();
 * store.send({ type: "applyFilter", customers: [1, 2, 3] });
 * 
 * Resetting all filters:
 * store.send({ type: "resetFilters" });
 * 
 * Partial filter updates:
 * store.send({ 
 *   type: "applyFilter", 
 *   service_date_from: new Date(),
 *   service_date_to: new Date()
 * });
 */
```

## FastAPI Example

```python
from typing import Optional, List
from decimal import Decimal
from fastapi import APIRouter, Depends, Security
from redis.asyncio import Redis
from sqlalchemy import func, select, case, or_, exists
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV
from shared.models.models import Customer
from shared.models.models_work_order import (
    WorkOrder, WorkOrderStatus, ServiceType, work_order_model_type_rel
)
from shared.models.models_bom import ModelType

from app.db.database import get_ijack_db
from app.db.redis import cache_memoize, get_async_redis
from app.auth.session import roles_required
from app.schemas.response import ApiResponse
from app.schemas.ag_grid import InfiniteApiResponse, InifiniteRowModel
from app.models.models import WorkOrderPart

from .schemas import (
    SalesAnalyticsFilters, TopCustomer, TopYear, 
    ServiceTypeValue, ModelTypeValue, WorkOrderGridSchema
)

# Router with role-based security matching React component usage
router = APIRouter(
    prefix="/sales-analytics",
    tags=["sales", "analytics"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)

@router.post("/total-revenue")
async def get_total_revenue(
    filters: SalesAnalyticsFilters, 
    db=Depends(get_ijack_db)
) -> ApiResponse[dict]:
    """
    Calculate total revenue based on applied filters.
    
    This endpoint matches the React pattern:
    $api.useQuery("post", "/v1/sales-analytics/total-revenue", { body: filters })
    
    Parameters:
    -----------
    filters : SalesAnalyticsFilters
        Filters including customers, service_types, models, and service_dates
    db : AsyncSession
        Database session dependency
        
    Returns:
    --------
    ApiResponse[dict]
        Response containing total_revenue and period description
    """
    # Get non-voided work order status IDs
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )
    
    # Build the main query
    query = (
        select(
            func.trunc(func.sum(WorkOrderPart.cost_before_tax), 2).label("total_revenue")
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(
            WorkOrder.status_id.in_(status_subquery), 
            Customer.id > 3  # Exclude test customers
        )
    )
    
    # Apply date filters if provided
    if (filters.service_dates is not None 
        and filters.service_dates.from_date is not None):
        query = query.where(WorkOrder.date_service >= filters.service_dates.from_date)
    if (filters.service_dates is not None 
        and filters.service_dates.to_date is not None):
        query = query.where(WorkOrder.date_service <= filters.service_dates.to_date)
    
    # Apply customer filters
    if filters.customers is not None and len(filters.customers) > 0:
        query = query.where(Customer.id.in_(filters.customers))
        
    # Apply service type filters
    if filters.service_types is not None and len(filters.service_types) > 0:
        query = query.where(WorkOrder.service_type_id.in_(filters.service_types))
    
    # Apply model filters (handle nullable model relationships)
    if filters.models is not None and len(filters.models) > 0:
        non_null_values = [x for x in filters.models if x is not None]
        null_values = [x for x in filters.models if x is None]
        
        conditions = []
        if non_null_values:
            in_query = WorkOrder.id.in_(
                select(work_order_model_type_rel.c.work_order_id).where(
                    work_order_model_type_rel.c.model_type_id.in_(non_null_values)
                )
            )
            conditions.append(in_query)
            
        if null_values:
            not_in_query = ~exists(
                select(1).where(
                    work_order_model_type_rel.c.work_order_id == WorkOrder.id
                )
            )
            conditions.append(not_in_query)
            
        if conditions:
            query = query.where(or_(*conditions))
    
    # Execute query
    result = await db.execute(query)
    total_revenue = result.scalar_one() or 0
    
    # Generate period description
    period = "All time"
    if (filters.service_dates is not None 
        and filters.service_dates.from_date is not None 
        and filters.service_dates.to_date is not None):
        period = f"{filters.service_dates.from_date.strftime('%Y-%m-%d')} to {filters.service_dates.to_date.strftime('%Y-%m-%d')}"
    
    return {
        "result": {
            "total_revenue": total_revenue,
            "period": period
        }
    }

@router.post("/top-customer")
async def get_top_customer(
    filters: SalesAnalyticsFilters, 
    db=Depends(get_ijack_db)
) -> ApiResponse[Optional[TopCustomer]]:
    """
    Get the customer with the highest total sales based on filters.
    
    Demonstrates complex filtering with nullable relationships and 
    aggregation functions for sales analytics.
    """
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )
    
    query = (
        select(
            Customer.id,
            Customer.customer,
            func.trunc(func.sum(WorkOrderPart.cost_before_tax), 2).label("total_cost"),
        )
        .join(WorkOrder, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(WorkOrder.status_id.in_(status_subquery), Customer.id > 3)
    )
    
    # Apply all filters (same pattern as total_revenue)
    # ... filter application code ...
    
    query = (
        query.group_by(Customer.customer, Customer.id)
        .order_by(func.sum(WorkOrderPart.cost_before_tax).desc())
        .limit(1)
    )
    
    response = await db.execute(query)
    result = response.first()
    return {"result": result}

@router.post("/monthly-service-type-chart")
async def monthly_service_type_chart(
    filters: SalesAnalyticsFilters,
    db=Depends(get_ijack_db),
) -> ApiResponse[List[ServiceTypeValue]]:
    """
    Get monthly revenue data grouped by service type for chart visualization.
    
    This endpoint matches the React chart pattern:
    $api.useQuery("post", "/v1/sales-analytics/monthly-service-type-chart", { body: filters })
    
    Demonstrates:
    - Complex aggregation with CASE statements for pivot-like behavior
    - Window functions for running totals
    - Date truncation for monthly grouping
    """
    # Build query with CASE statements to pivot service types
    query = (
        select(
            func.max(func.date_trunc("month", WorkOrder.date_service)).label("month"),
            case(
                (WorkOrder.service_type_id == 1, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("new_installation_sum"),
            case(
                (WorkOrder.service_type_id == 2, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("repair_sum"),
            case(
                (WorkOrder.service_type_id == 3, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("parts_sum"),
            case(
                (WorkOrder.service_type_id == 4, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("sale_sum"),
            func.sum(WorkOrderPart.cost_before_tax).label("monthly_total"),
        )
        .select_from(WorkOrder)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Customer, Customer.id == WorkOrder.customer_id)
        .where(WorkOrder.customer_id > 3, WorkOrder.status_id != 14)
    )
    
    # Apply filters (same pattern as other endpoints)
    # ... filter application code ...
    
    # Group by month and service type for the CTE
    query = query.group_by(
        func.date_trunc("month", WorkOrder.date_service),
        WorkOrder.service_type_id,
    ).cte("query")

    # Create final query with running totals using window functions
    result_query = (
        select(
            query.c.month,
            func.trunc(func.sum(query.c.new_installation_sum), 0).label("new_installation"),
            func.trunc(func.sum(query.c.parts_sum), 0).label("parts"),
            func.trunc(func.sum(query.c.repair_sum), 0).label("repair"),
            func.trunc(func.sum(query.c.sale_sum), 0).label("sale"),
            # Calculate running total using window function
            func.trunc(
                func.sum(func.sum(query.c.monthly_total)).over(order_by=query.c.month),
                0,
            ).label("total"),
        )
        .select_from(query)
        .group_by(query.c.month)
        .order_by(query.c.month)
    )

    response = await db.execute(result_query)
    results = response.all()
    return {"result": results}

@router.post("/work-order-grid")
async def get_infinite_sales_grid(
    server_request: InifiniteRowModel,
    ijack_db=Depends(get_ijack_db),
) -> InfiniteApiResponse[WorkOrderGridSchema]:
    """
    Get infinite scroll grid data for work orders with server-side operations.
    
    This endpoint supports the AG-Grid infinite scrolling pattern used in React:
    fetchClient.POST("/v1/sales-analytics/work-order-grid", { body: gridRequest })
    
    Demonstrates:
    - Server-side row model (SSRM) for AG-Grid
    - Complex filtering with multiple table joins
    - Sorting and pagination
    - CTE usage for complex aggregations
    """
    # Create CTE for equipment serviced aggregation
    equipment_serviced_cte = (
        select(
            work_order_model_type_rel.c.work_order_id,
            func.string_agg(
                ModelType.model,
                ", "
            ).label("equipment_serviced"),
        )
        .select_from(ModelType)
        .join(
            work_order_model_type_rel,
            work_order_model_type_rel.c.model_type_id == ModelType.id,
        )
        .group_by(work_order_model_type_rel.c.work_order_id)
        .cte("equipment_serviced_cte")
    )
    
    # Build main query with all necessary joins
    order_total = func.sum(WorkOrderPart.cost_before_tax)
    query = (
        select(
            WorkOrder.id,
            WorkOrder.service_required,
            Customer.customer,
            WorkOrderStatus.name.label("status"),
            ServiceType.name.label("service_type"),
            WorkOrder.date_service,
            order_total.label("order_total"),
            equipment_serviced_cte.c.equipment_serviced,
        )
        .join(Customer, Customer.id == WorkOrder.customer_id, isouter=True)
        .join(equipment_serviced_cte, 
              equipment_serviced_cte.c.work_order_id == WorkOrder.id, 
              isouter=True)
        .join(WorkOrderStatus, WorkOrderStatus.id == WorkOrder.status_id, isouter=True)
        .join(ServiceType, ServiceType.id == WorkOrder.service_type_id, isouter=True)
        .join(WorkOrderPart, WorkOrderPart.work_order_id == WorkOrder.id)
        .where(
            Customer.id > 3,
            WorkOrderStatus.name != "VOID",
        )
    )
    
    # Apply AG-Grid server-side operations (filtering, sorting, pagination)
    # This would use helper functions for SSRM operations
    # ... SSRM operations code ...
    
    # Group by all non-aggregate columns
    query = query.group_by(
        WorkOrder.id,
        WorkOrder.service_required,
        Customer.customer,
        WorkOrderStatus.name,
        ServiceType.name,
        WorkOrder.date_service,
        equipment_serviced_cte.c.equipment_serviced,
    )
    
    # Execute paginated query and get count
    result = await ijack_db.execute(query)
    db_res = result.all()
    # db_count = await get_row_count(ijack_db, query)  # Helper function
    
    return {
        "rowData": db_res,
        "rowCount": len(db_res),  # Would be actual count in real implementation
    }
```

### Database Patterns

```python
# Database session dependency with proper async handling
async def get_ijack_db():
    """
    Returns an async session for the IJACK database.
    Handles commit/rollback automatically.
    """
    db = SessionLocalIjack()
    try:
        yield db
        await db.commit()  # Commit only if no exceptions
    except:
        await db.rollback()  # Rollback on exception
        raise
    finally:
        await db.close()

# Using get_dataframe for pandas integration
async def get_sales_data() -> pd.DataFrame:
    """
    Execute a query and return results as a pandas DataFrame.
    Supports both SQLAlchemy queries and raw SQL strings.
    """
    query = select(WorkOrder.id, WorkOrder.total_cost).where(
        WorkOrder.is_active == True
    )
    
    # Convert SQLAlchemy query to DataFrame
    df = await get_dataframe(query, db_name="ijack")
    return df

# Concurrent database operations for better performance
async def get_dashboard_data(ijack_db=Depends(get_ijack_db)):
    """
    Execute multiple queries concurrently using asyncio.gather
    """
    sales_query = select(func.sum(WorkOrder.total_cost))
    count_query = select(func.count(WorkOrder.id))
    avg_query = select(func.avg(WorkOrder.total_cost))
    
    # Execute all queries concurrently
    sales_result, count_result, avg_result = await gather(
        ijack_db.execute(sales_query),
        ijack_db.execute(count_query),
        ijack_db.execute(avg_query)
    )
    
    return {
        "total_sales": sales_result.scalar_one(),
        "total_orders": count_result.scalar_one(),
        "average_order": avg_result.scalar_one(),
    }
```

### Authentication and Security Patterns

```python
from fastapi import Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV
from app.auth.session import roles_required, login_required

# Role-based access control at router level
router = APIRouter(
    prefix="/sales",
    tags=["sales"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)

# Role-based access control at endpoint level
@router.get("/sensitive-data")
@Security(roles_required([ROLE_ID_IJACK_SOFTWARE_DEV]))  # Only software devs
async def get_sensitive_data():
    """Endpoint requiring specific role access"""
    return {"data": "sensitive_information"}

# Basic authentication requirement
@router.get("/user-data")
async def get_user_data(user_id: int = Depends(login_required)):
    """Endpoint requiring any authenticated user"""
    return {"user_id": user_id}

# Custom role checking within endpoint
async def check_customer_access(
    customer_id: int,
    user_id: int = Depends(get_user_id),
    ijack_db=Depends(get_ijack_db)
):
    """
    Check if user has access to specific customer data.
    Custom business logic for data access control.
    """
    access_query = select(user_customer_rel).where(
        user_customer_rel.c.user_id == user_id,
        user_customer_rel.c.customer_id == customer_id
    )
    
    result = await ijack_db.execute(access_query)
    if not result.first():
        raise ForbiddenException("Access denied to customer data")
    
    return True
```

### Caching Patterns

```python
from app.db.redis import cache_memoize

# Simple caching with timeout
@cache_memoize(timeout=300)  # 5 minutes
async def get_slow_calculation(
    param1: str,
    redis_client: Redis = Depends(get_async_redis)
):
    """
    Cache expensive calculations.
    Redis client is automatically injected from dependencies.
    """
    # Expensive operation here
    result = perform_complex_calculation(param1)
    return result

# Caching database queries
@cache_memoize(timeout=3600)  # 1 hour
async def get_reference_data(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis)
):
    """
    Cache reference data that doesn't change often.
    """
    query = select(ServiceType.id, ServiceType.name).order_by(ServiceType.name)
    result = await ijack_db.execute(query)
    return result.all()
```

### Pydantic Schema Patterns

```python
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Generic, TypeVar

DataType = TypeVar("DataType")
PaginationType = TypeVar("PaginationType")

class WorkOrderSchema(BaseModel):
    """
    Schema for work order data.
    Uses ConfigDict for SQLAlchemy model compatibility.
    """
    model_config = ConfigDict(from_attributes=True)

    id: int
    service_required: Optional[str]

class SalesByYearSchema(BaseModel):
    """Schema for aggregated sales data by year"""
    model_config = ConfigDict(from_attributes=True)

    year: int
    total_sales: float

class ApiResponse(BaseModel, Generic[DataType]):
    """
    Generic API response wrapper.
    Provides consistent response structure across all endpoints.
    """
    model_config = ConfigDict(from_attributes=True)

    result: DataType

class PaginatedApiResponse(BaseModel, Generic[DataType, PaginationType]):
    """
    Generic paginated API response.
    Includes both data and pagination metadata.
    """
    data: DataType
    info: PaginationType

class LimitOffsetPaginationInfo(BaseModel):
    """Pagination information for limit-offset based pagination."""
    limit: int = Field(description="Number of items per page", default=20)
    page: int = Field(description="Page number", default=1)
```

### Router Organization Patterns

```python
# Main router file (router.py) - combines multiple sub-routers
from fastapi import APIRouter, Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV
from app.auth.session import roles_required

from .service_type import router as service_type_router
from .work_order import router as work_order_router

router = APIRouter(
    prefix="/work-order",
    tags=["work_orders", "sales"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)

# Include sub-routers
router.include_router(service_type_router)
router.include_router(work_order_router)

# Individual endpoint file (service_type.py) - focused functionality
from fastapi import APIRouter, Depends, Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV
from shared.models.models_work_order import ServiceType
from sqlalchemy import select

from app.api.responses.auth import AUTHZ_RESPONSES
from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.db.redis import cache_memoize
from app.schemas.response import ApiResponse

from .schemas import ServiceTypeSchema

router = APIRouter(
    tags=["service type"],
)

@router.get(
    "/service-type",
    tags=["internal"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
    responses=AUTHZ_RESPONSES,
)
@cache_memoize()
async def read_service_types(
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[ServiceTypeSchema]]:
    """
    Get a list of service types and their details.
    """
    query = select(ServiceType.id, ServiceType.name).order_by(ServiceType.name)
    result = await ijack_db.execute(query)
    return {"result": result.all()}
```

### Python Unit Test Example

```python
import pytest
from unittest.mock import patch

from shared.models.models import Alert
from app.config import POWER_UNIT_ID_TEINE_200432, USER_ID_TEINE_USER
from tests.conftest import ijack_context


@patch("app.dashapp.callbacks.operators.get_id_triggered")
def test_main_flask_route(all_ijack):
    """
    Very simple Flask test to see if the main
    Flask route is accessible
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Make an alertee with default settings, and a remote control user
        new_alert = Alert(
            user_id=USER_ID_TEINE_USER,
            power_unit_id=POWER_UNIT_ID_TEINE_200432,
        )
        db.session.add(new_alert)
        db.session.commit()
    
    response = flask_test_client.get("/")
    assert response.status_code == 200
    assert (
        b"proudly helped energy companies produce oil and gas in the most reliable"
        in response.data
    )
```

### FastAPI Unit Test Example

```python
import pytest
from unittest.mock import MagicMock, patch
import pandas as pd

from app.db.database import get_dataframe


@pytest.mark.asyncio
@patch("app.db.database.SessionLocalIjack")
async def test_get_dataframe(mock_session_local):
    """
    Test that get_dataframe properly executes SQL query and returns a pandas DataFrame.
    
    This example shows:
    - Using @pytest.mark.asyncio for async test functions
    - Patching database sessions for testing
    - Using MagicMock for async operations
    - Testing pandas DataFrame results
    """
    # Set up mock
    mock_session = MagicMock()
    mock_session_local.return_value = mock_session

    # Mock DataFrame result
    expected_df = pd.DataFrame({"id": [1, 2, 3], "name": ["test1", "test2", "test3"]})

    # Configure mock to return expected DataFrame when run_sync is called
    mock_session.run_sync.return_value = expected_df

    # Execute the function
    query = "SELECT * FROM test_table"
    result_df = await get_dataframe(query)

    # Verify the mock was called with a function that would pass the query
    mock_session.run_sync.assert_called_once()

    # Verify the result is what we expect
    pd.testing.assert_frame_equal(result_df, expected_df)

    # Verify session was closed
    mock_session.close.assert_called_once()
```

### Python Example

```python
def calculate_well_efficiency(well_data: dict, time_period: str = "daily") -> float:
    """
    Calculate the efficiency of a well based on production data.
    
    Parameters:
    -----------
    well_data : dict
        Dictionary containing well production data
    time_period : str, optional
        Time period for calculation ("daily", "weekly", "monthly")
        Default is "daily"
        
    Returns:
    --------
    float
        Efficiency score between 0.0 and 1.0
        
    Raises:
    -------
    ValueError
        If well_data is missing required fields or time_period is invalid
    """
    # Validate input parameters
    if not well_data or not isinstance(well_data, dict):
        raise ValueError("Well data must be a non-empty dictionary")
        
    valid_periods = ["daily", "weekly", "monthly"]
    if time_period not in valid_periods:
        raise ValueError(f"Time period must be one of: {', '.join(valid_periods)}")
    
    # Extract required data
    actual_production = well_data.get("actual_production")
    theoretical_max = well_data.get("theoretical_max")
    
    if actual_production is None or theoretical_max is None:
        raise ValueError("Well data missing required production values")
    
    # Calculate efficiency score
    # Avoid division by zero
    if theoretical_max == 0:
        return 0.0
        
    efficiency = actual_production / theoretical_max
    
    # Cap efficiency at 1.0 (100%)
    return min(efficiency, 1.0)
```

### Dash Example

```python
def create_well_monitoring_dashboard(well_id: str) -> Dash:
    """
    Create a Dash application for real-time well monitoring.
    
    Parameters:
    -----------
    well_id : str
        The unique identifier for the well to monitor
        
    Returns:
    --------
    Dash
        Configured Dash application with interactive components
    """
    # Initialize the Dash app with required CSS
    app = Dash(
        __name__,
        external_stylesheets=[dbc.themes.BOOTSTRAP],
        suppress_callback_exceptions=True,
    )
    
    # Create layout with multiple interactive components
    app.layout = dbc.Container([
        # Header section with title and well info
        dbc.Row([
            dbc.Col([
                html.H1("Well Performance Dashboard", className="mt-4 mb-4"),
                html.H5(f"Well ID: {well_id}", className="text-secondary mb-4")
            ])
        ]),
        
        # Controls for time range and metrics selection
        dbc.Row([
            dbc.Col([
                html.Label("Select Time Range:"),
                dcc.DatePickerRange(
                    id="date-range",
                    start_date=datetime.now() - timedelta(days=30),
                    end_date=datetime.now(),
                    max_date_allowed=datetime.now(),
                ),
            ], width=6),
            dbc.Col([
                html.Label("Select Metrics:"),
                dcc.Checklist(
                    id="metrics-selection",
                    options=[
                        {"label": "Production Rate", "value": "production"},
                        {"label": "Pressure", "value": "pressure"},
                        {"label": "Temperature", "value": "temperature"},
                        {"label": "Efficiency", "value": "efficiency"},
                    ],
                    value=["production", "efficiency"],
                    inline=True,
                ),
            ], width=6),
        ], class_name="mb-4"),
        
        # Main chart area
        dbc.Row([
            dbc.Col([
                dcc.Graph(id="main-timeseries"),
            ]),
        ], class_name="mb-4"),
        
        # Detailed metrics cards
        dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Current Production"),
                    dbc.CardBody([
                        html.H3(id="current-production"),
                        html.P("barrels per day"),
                    ]),
                ]),
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Average Pressure"),
                    dbc.CardBody([
                        html.H3(id="average-pressure"),
                        html.P("PSI"),
                    ]),
                ]),
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Peak Temperature"),
                    dbc.CardBody([
                        html.H3(id="peak-temperature"),
                        html.P("°F"),
                    ]),
                ]),
            ], width=3),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader("Overall Efficiency"),
                    dbc.CardBody([
                        html.H3(id="overall-efficiency"),
                        html.P("percent"),
                    ]),
                ]),
            ], width=3),
        ]),
        
        # Hidden data store for sharing data between callbacks
        dcc.Store(id="well-data-store"),
        
        # Interval component for real-time updates
        dcc.Interval(
            id="interval-component",
            interval=60000,  # Update every minute
            n_intervals=0,
        ),
    ])
    
    # Register callbacks for interactive components
    register_dashboard_callbacks(app)
    
    return app


def register_dashboard_callbacks(app: Dash) -> None:
    """
    Register all callbacks for the well monitoring dashboard.
    
    Parameters:
    -----------
    app : Dash
        The Dash application to register callbacks for
    """
    @app.callback(
        Output("well-data-store", "data"),
        Input("interval-component", "n_intervals"),
        Input("date-range", "start_date"),
        Input("date-range", "end_date"),
        prevent_initial_call=True,
    )
    def update_well_data(n_intervals, start_date, end_date):
        """Fetch updated well data and store it"""
        # Convert string dates to datetime objects
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        # Fetch data from database or API
        # This is a placeholder for actual data fetching logic
        df = fetch_well_data(well_id, start_dt, end_dt)
        
        # Return data in JSON serializable format
        return df.to_dict("records")
    
    @app.callback(
        Output("main-timeseries", "figure"),
        Input("well-data-store", "data"),
        Input("metrics-selection", "value"),
        prevent_initial_call=True,
    )
    def update_timeseries(data, selected_metrics):
        """Update the main time series chart"""
        if not data or not selected_metrics:
            return go.Figure()
        
        # Convert data back to dataframe
        df = pd.DataFrame(data)
        
        # Create figure
        fig = go.Figure()
        
        # Add traces for each selected metric
        for metric in selected_metrics:
            fig.add_trace(
                go.Scatter(
                    x=df["timestamp"],
                    y=df[metric],
                    mode="lines",
                    name=metric.capitalize(),
                )
            )
        
        # Update layout
        fig.update_layout(
            title="Well Performance Over Time",
            xaxis_title="Date",
            yaxis_title="Value",
            legend_title="Metrics",
            height=500,
        )
        
        return fig
    
    # Additional callbacks for metric cards and other components...
```