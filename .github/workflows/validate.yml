name: Validate code to be merged

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [main, staging, staging2]

jobs:
  typecheck:
    name: TypeScript Compilation
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 10
          run_install: false

      - name: Install Node.js
        uses: useblacksmith/setup-node@v5
        with:
          node-version: 22
          cache: "pnpm"
          cache-dependency-path: "flask_app/pnpm-lock.yaml"

      - name: Install dependencies
        run: pnpm install
        working-directory: flask_app

      - name: TypeScript Build
        run: pnpm exec tsc --build app/inertia/react
        working-directory: flask_app

  lint-flask-app:
    name: <PERSON> Linting (ruff)
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/ruff-action@v3

      - name: Ruff Check
        run: ruff check flask_app

  lint-fast-api:
    name: <PERSON> (ruff)
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/ruff-action@v3

      - name: Ruff Check
        run: ruff check fast_api

  lint-packages:
    name: Python Linting (ruff)
    runs-on: blacksmith-4vcpu-ubuntu-2404
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/ruff-action@v3

      - name: Ruff Check
        run: ruff check packages

  check-branch-commits:
    name: Branch is up-to-date
    runs-on: blacksmith-4vcpu-ubuntu-2404

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set branch variables
        id: set-vars
        run: |
          echo "SOURCE_BRANCH=${{ github.event.pull_request.head.ref }}" >> $GITHUB_ENV
          echo "DESTINATION_BRANCH=${{ github.event.pull_request.base.ref }}" >> $GITHUB_ENV

      - name: Check if branch is updated with destination
        run: |
          echo "SOURCE_BRANCH: ${{ env.SOURCE_BRANCH }}"
          echo "DESTINATION_BRANCH: ${{ env.DESTINATION_BRANCH }}"

          git remote prune origin
          git fetch origin ${{ env.DESTINATION_BRANCH }}
          git fetch origin ${{ env.SOURCE_BRANCH }}

          DIFF=$(git log --oneline --cherry origin/${{ env.DESTINATION_BRANCH }}...origin/${{ env.SOURCE_BRANCH }})

          if [[ -z $DIFF ]]; then
            echo "Your PR branch is not up-to-date with origin/${{ env.DESTINATION_BRANCH }}."
            echo "Use git rebase origin/${{ env.DESTINATION_BRANCH }} to update then git push -f after rebase is completed."
            exit 1
          fi

          echo "Commit Diff: $DIFF"
