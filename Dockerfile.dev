FROM nikolaik/python-nodejs:python3.12-nodejs22-slim AS base

ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV ENVIRONMENT=development

SHELL ["/bin/bash", "-c"]

# Configure apt and install packages
# I had to add --insecure since curl didn't work...
RUN --mount=target=/var/lib/apt/lists,type=cache,sharing=locked \
    --mount=target=/var/cache/apt,type=cache,sharing=locked \
    apt-get update && \
    apt-get install -y --no-install-recommends docker.io docker-compose apt-utils build-essential dialog \
    curl netcat-traditional iputils-ping unzip dos2unix gcc 2>&1 && \
    # Verify git, process tools, lsb-release (common in install instructions for CLIs) installed
    apt-get install -y --no-install-recommends sudo git redis-server libpq-dev sass \
    procps iproute2 lsb-release gnupg apt-transport-https ripgrep \
    # For display testing
    # x11-apps \
    # xvfb xauth libxext6 libxrender1 libxtst6 \
    # x11vnc novnc \
    g++ protobuf-compiler libprotobuf-dev \
    # For Cairo SVG rendering
    cairosvg libffi-dev && \
    apt-get install -y openssh-client

# ------ packages setup begins ------
FROM base as packages
ENV ENVIRONMENT=development
WORKDIR /project/packages
COPY packages/pyproject.toml .
RUN uv lock
RUN uv venv .venv
RUN source .venv/bin/activate
RUN --mount=type=cache,target=/root/.cache/uv,id=packages_uv \
    uv sync --no-install-project

#  ------ flask_app setup begins ------
FROM packages AS flask
ENV ENVIRONMENT=development
WORKDIR /project/flask_app
# Copy installation files for flask_app uv only
COPY flask_app/pyproject.toml ./
RUN --mount=type=cache,target=/root/.cache/uv,id=flask_uv \
    uv sync --no-install-project --group dev
# These Playwright dependencies are required for the Playwright package,
# but they take forever to install...
# RUN source .venv/bin/activate && \
#     playwright install-deps && \
#     playwright install
# Install Node.js dependencies
RUN npm install --global corepack@latest typescript pnpm
# Add node_modules/.bin to PATH for both build-time and runtime
ENV PATH="/project/flask_app/node_modules/.bin:$PATH"
RUN echo 'export PATH="/project/flask_app/node_modules/.bin:$PATH"' >> /etc/profile && \
    echo 'export PATH="/project/flask_app/node_modules/.bin:$PATH"' >> /root/.bashrc
RUN corepack enable pnpm
# Copy installation files for pnpm
COPY flask_app/package.json flask_app/pnpm-lock.yaml flask_app/pnpm-workspace.yaml ./
COPY flask_app/app/inertia/react/package.json flask_app/app/inertia/react/pnpm-*.yaml ./app/inertia/react/
RUN --mount=type=cache,target=/root/.pnpm-store,id=flask_pnpm \
    pnpm install
# Place executables in the environment at the front of the path
#  ------ flask_app setup ends ------

# ----- fast_api setup begings -----
FROM packages AS fast
ENV ENVIRONMENT=development
WORKDIR /project/fast_api
COPY fast_api/pyproject.toml ./
RUN --mount=type=cache,target=/root/.cache/uv,id=fast_uv \
    uv sync --no-install-project --group dev
# ----- fast_api setup ends -----

FROM base AS final
ENV ENVIRONMENT=development
COPY . /project
COPY --chmod=755 --from=packages /project/packages/.venv /project/packages/.venv
COPY --chmod=755 --from=flask /project/flask_app/.venv /project/flask_app/.venv
COPY --chmod=755 --from=flask /project/flask_app/node_modules /project/flask_app/node_modules
COPY --chmod=755 --from=flask /project/flask_app/app/inertia/react/node_modules /project/flask_app/app/inertia/react/node_modules
COPY --chmod=755 --from=fast /project/fast_api/.venv /project/fast_api/.venv
# Playwright doesn't work and it's slow to install, so we will not install it in the dev container.
# COPY --from=flask /root/.cache/ms-playwright /root/.cache/ms-playwright
# ENV PLAYWRIGHT_BROWSERS_PATH=/root/.cache/ms-playwright
ENV PATH="/project/flask_app/.venv/bin:$PATH"
ENV PATH="/project/packages/.venv/bin:$PATH"
ENV PATH="/project/fast_api/.venv/bin:$PATH"
ENV PATH="/project/flask_app/node_modules/.bin:$PATH"
RUN echo 'export PATH="/project/flask_app/node_modules/.bin:$PATH"' >> /etc/profile && \
    echo 'export PATH="/project/flask_app/node_modules/.bin:$PATH"' >> /root/.bashrc

WORKDIR /project/fast_api
RUN source ./.venv/bin/activate && uv pip install -e /project/packages --link-mode=copy

WORKDIR /project/flask_app
RUN source ./.venv/bin/activate && uv pip install -e /project/packages --link-mode=copy

WORKDIR /project
# Copy my preferred .bashrc to /root/ so that it's automatically "sourced" when the container starts
COPY .bashrc /root/
