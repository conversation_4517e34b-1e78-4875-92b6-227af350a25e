[project]
name = "fast-api"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11,<3.14"
dependencies = [
    "apscheduler>=3.11.0",
    "fastapi[standard]>=0.115.12",
    "hypercorn[h3]>=0.17.3",
    "itsdangerous>=2.2.0",
    "msgpack>=1.1.0",
    "numpy>=2.2.5",
    "pandas>=2.2.3",
    "psycopg[binary]>=3.2.7",
    "pydantic-extra-types>=2.10.4",
    "pydantic-settings>=2.9.1",
    "redis>=6.0.0",
    "requests>=2.32.4",
    "sqlalchemy>=2.0.40",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-asyncio>=0.25.0",
    "ruff>=0.11.8",
]

[tool.pytest.ini_options]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[tool.ruff]
exclude = [
    "app/api/endpoints/admin_resources/*",
]
