from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.encoders import jsonable_encoder
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import HTMLR<PERSON>ponse, JSONResponse

from app.api.api import api_router
from app.api.exceptions.base import ApiException
from app.core.config import settings
from app.core.logging import logger
from app.core.openapi_ts import generate_openapi_types

# Log application startup information
logger.info("Starting FastAPI application")
logger.info(f"Environment: {settings.ENVIRONMENT}")
logger.info(f"Debug mode: {settings.DEBUG}")
logger.info(f"Session cookie name: {settings.SESSION_COOKIE_NAME}")


app = FastAPI(
    title="IJACK Technologies",
    docs_url=None,
    redoc_url=None,
    openapi_url="/openapi.json",
)

# app = ProxyFixMiddleware(fast_app, mode="legacy", trusted_hops=1)

# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=[str(origin) for origin in settings.CORS_ORIGINS],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Type", "X-CSRFToken"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=[str(origin) for origin in settings.TRUSTED_ORIGINS],
)

app.add_event_handler("startup", generate_openapi_types)


@app.exception_handler(ApiException)
async def api_exception_handler(request: Request, exc: ApiException):
    """Handle custom API exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        headers=exc.headers,
        content=jsonable_encoder(
            {"detail": exc.detail, "error_code": exc.headers.get("X-Error-Code")}
        ),
    )


app.include_router(api_router, prefix="")


@app.get("/healthcheck", response_class=HTMLResponse)
async def healthcheck():
    return "OK"


@app.get("/docs", response_class=HTMLResponse)
async def custom_rapidoc():
    return """
    <!doctype html>
    <html>
        <head>
        <meta charset="utf-8">
        <script type="module" src="https://unpkg.com/rapidoc/dist/rapidoc-min.js"></script>
    </head>
        <body>
            <rapi-doc
            spec-url="/openapi.json"
            primary-color = "#C1D72E"
            theme="dark"
            render-style="focused"
            use-path-in-nav-bar="true"
            nav-item-spacing="compact"
            show-header="false"
            schema-style="table"
            show-method-in-nav-bar="as-colored-text"
            sort-tags="true"
            >
            <img
            slot="nav-logo"
            width="150"
            style="margin: 0 auto;"
            src="http://localhost:4999/static/img/icons/ijack-logo-512x233.png"
            />
            </rapi-doc>
        </body>
    </html>
    """
