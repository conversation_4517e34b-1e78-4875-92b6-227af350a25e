import pickle
from functools import wraps
from typing import Any, Callable, TypeVar, cast

from redis.asyncio import Redis
from redis.exceptions import ConnectionError, TimeoutError

from app.core.config import settings

# Create an async Redis client instance
async_redis_client = Redis.from_url(
    settings.REDIS_URL,
    socket_timeout=5,
    socket_connect_timeout=5,
    retry_on_timeout=True,
    decode_responses=False,  # Automatically decode responses to Python strings
)


async def get_async_redis() -> Redis:
    """
    Returns an async Redis client instance.
    Used as a FastAPI dependency.
    """
    try:
        # Test the connection with a ping
        await async_redis_client.ping()
        return async_redis_client
    except (ConnectionError, TimeoutError) as e:
        # Log the error and re-raise
        print(f"Redis connection error: {e}")
        raise


T = TypeVar("T")


# Cache decorator for FastAPI functions
def cache_memoize(timeout: int = 3600):
    """
    Cache the result of a function call using Redis.

    Parameters:
    -----------
    timeout : int, optional
        Cache timeout in seconds, default is 1 hour
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # Skip caching in debug mode for easier development
            if settings.DEBUG or True:
                return await func(*args, **kwargs)

            # Get Redis client from kwargs if available, otherwise use None
            redis_client = kwargs.get("redis_client")

            # Generate a unique cache key
            cache_key = f"cache:{func.__module__}:{func.__name__}"

            # Try to get cached result
            if redis_client:
                cached_result = await redis_client.get(cache_key)
                if cached_result:
                    try:
                        return pickle.loads(cached_result)
                    except (pickle.PickleError, TypeError):
                        # If unpickling fails, just continue to compute the result
                        pass

            # If no cached result or unpickling failed, compute the result
            result = await func(*args, **kwargs)

            # Cache the result if we have a redis client
            if redis_client:
                try:
                    await redis_client.set(cache_key, pickle.dumps(result), ex=timeout)
                except (pickle.PickleError, TypeError):
                    # If pickling fails, just return the result without caching
                    pass

            return cast(T, result)

        return wrapper

    return decorator
