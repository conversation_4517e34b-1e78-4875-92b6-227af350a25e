import pandas as pd
from sqlalchemy import URL, NullPool
from sqlalchemy.ext.asyncio import async_sessionmaker, create_async_engine

from app.core.config import settings

url = URL.create(
    drivername="postgresql+psycopg",
    username=settings.USER_IJ,
    password=settings.PASS_IJ,
    host=settings.HOST_IJ,
    port=int(settings.PORT_IJ),
    database=settings.DB_IJ,
)
url_ts = URL.create(
    drivername="postgresql+psycopg",
    username=settings.USER_TS,
    password=settings.PASS_TS,
    host=settings.HOST_TS,
    port=int(settings.PORT_TS),
    database=settings.DB_TS,
)
# engine_ijack = create_async_engine(url, echo=True, connect_args={"connect_timeout": 5})
engine_ijack = create_async_engine(
    url,
    echo=settings.ECHO,
    poolclass=NullPool,  # Important: Use NullPool with PgBouncer
    connect_args={
        # Important for PgBouncer transaction pooling mode, but we commit manually if the transaction succeeds in get_ijack_db and get_timescale_db
        "autocommit": False,
    },
    # Set execution options at the engine level
    execution_options={
        # Further ensures no transactions are kept open
        # "isolation_level": "AUTOCOMMIT"
    },
)
engine_timescale = create_async_engine(
    url_ts, echo=settings.ECHO, poolclass=NullPool, connect_args={"connect_timeout": 5}
)
# Fix: Use async_sessionmaker for async engines
SessionLocalIjack = async_sessionmaker(
    # Newest version of FastAPI does not support autocommit=True
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
    bind=engine_ijack,
)
SessionLocalTimescale = async_sessionmaker(
    autocommit=False, autoflush=False, bind=engine_timescale
)


# Synchronous function to run inside run_sync
def pandas_query(sync_session, query):
    """
    Run a SQL query and return a DataFrame.
    This function is run in a synchronous context using run_sync.
    """
    sync_conn = sync_session.connection()
    return pd.read_sql_query(query, sync_conn)


# Async function to fetch DataFrame from either database
async def get_dataframe(query, db_name: str = "ijack") -> pd.DataFrame:
    """
    Execute a SQL query and return the results as a pandas DataFrame.
    This function is run in an async context.

    Parameters:
    -----------
    query : str or SQLAlchemy query
        SQL query to execute
    db_name : str
        Database to query: "ijack" or "timescale"

    Returns:
    --------
    pd.DataFrame
        Results of the query as a pandas DataFrame

    Raises:
    -------
    ValueError
        If db_name is not "ijack" or "timescale"
    """
    if db_name not in ["ijack", "timescale"]:
        raise ValueError('db_name must be "ijack" or "timescale"')

    # Select the appropriate session based on db_name
    if db_name == "ijack":
        session = SessionLocalIjack()
    else:
        session = SessionLocalTimescale()

    # Handle both string queries and SQLAlchemy queries
    try:
        # For both string queries and SQLAlchemy queries, use session.run_sync
        # This ensures we're using a synchronous connection for pandas
        df = await session.run_sync(
            lambda sync_session: pandas_query(sync_session, query)
        )
        return df
    finally:
        await session.close()


async def get_ijack_db():
    """
    Returns an async session for the IJACK database.
    Used as a FastAPI dependency.
    """
    db = SessionLocalIjack()
    try:
        yield db
        await db.commit()  # Commit only if no exceptions
    except:
        await db.rollback()  # Rollback on exception
        raise
    finally:
        await db.close()


async def get_timescale_db():
    """
    Returns an async session for the TimescaleDB database.
    Used as a FastAPI dependency.
    """
    db = SessionLocalTimescale()
    try:
        yield db
        await db.commit()  # Commit only if no exceptions
    except:
        await db.rollback()  # Rollback on exception
        raise
    finally:
        await db.close()
