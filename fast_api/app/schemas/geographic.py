"""
Geographic analysis schemas for service analytics.

These schemas support the hybrid geocoding system that combines:
- Database lookups for known provinces/countries
- Major city clustering for approximate locations
- Coordinate-based regions for unmapped areas
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class GeographicCoordinates(BaseModel):
    """Geographic coordinate pair."""

    lat: float = Field(..., description="Latitude in decimal degrees")
    lon: float = Field(..., description="Longitude in decimal degrees")


class GeographicRegion(BaseModel):
    """
    Represents a geographic region with service analytics data.

    Regions can be identified by:
    - Known administrative boundaries (provinces/states)
    - Proximity to major cities
    - Coordinate clusters for remote areas
    """

    region_name: str = Field(
        ...,
        description="Name of the region (province, near city, or coordinate region)",
    )
    country_name: str = Field(
        ..., description="Country name or 'Unknown' for unmapped regions"
    )
    total_services: int = Field(
        ..., description="Total number of services performed in this region"
    )
    avg_cost_per_service: float = Field(
        ..., description="Average cost per service in CAD"
    )
    unique_structures: int = Field(
        ..., description="Number of unique structures serviced"
    )
    coordinates: GeographicCoordinates = Field(
        ..., description="Average coordinates of the region"
    )
    geocoding_coverage: float = Field(
        ...,
        ge=0,
        le=100,
        description="Percentage of structures with province/country data (0-100)",
    )
    clustering_method: str = Field(
        ...,
        description="Method used to identify region: 'database', 'major_city', or 'coordinate'",
    )

    # Optional fields for enhanced analytics
    distance_from_major_city: Optional[float] = Field(
        None, description="Distance in km from nearest major city (if applicable)"
    )
    service_frequency: Optional[float] = Field(
        None, description="Average services per structure"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "region_name": "Alberta",
                "country_name": "Canada",
                "total_services": 150,
                "avg_cost_per_service": 2500.50,
                "unique_structures": 45,
                "coordinates": {"lat": 53.5461, "lon": -113.4938},
                "geocoding_coverage": 95.5,
                "clustering_method": "database",
                "distance_from_major_city": None,
                "service_frequency": 3.33,
            }
        }


class GeographicAnalysisSummary(BaseModel):
    """Summary statistics for geographic analysis."""

    total_regions: int = Field(
        ..., description="Total number of geographic regions identified"
    )
    avg_geocoding_coverage: float = Field(
        ..., ge=0, le=100, description="Average geocoding coverage across all regions"
    )
    unmapped_structures: int = Field(
        ..., description="Number of structures without province/country data"
    )
    total_structures: int = Field(
        ..., description="Total number of structures analyzed"
    )
    dominant_country: Optional[str] = Field(
        None, description="Country with most services"
    )
    dominant_region: Optional[str] = Field(
        None, description="Region with most services"
    )


class GeographicAnalysis(BaseModel):
    """Complete geographic analysis response."""

    regions: List[GeographicRegion] = Field(
        ..., description="List of geographic regions with analytics"
    )
    summary: GeographicAnalysisSummary = Field(..., description="Summary statistics")

    # Metadata about the analysis
    analysis_method: str = Field(
        default="hybrid",
        description="Analysis method used: 'hybrid', 'database_only', or 'coordinate_only'",
    )
    cache_hit_rate: Optional[float] = Field(
        None, ge=0, le=100, description="Percentage of lookups served from cache"
    )

    class Config:
        json_schema_extra = {
            "example": {
                "regions": [
                    {
                        "region_name": "Alberta",
                        "country_name": "Canada",
                        "total_services": 150,
                        "avg_cost_per_service": 2500.50,
                        "unique_structures": 45,
                        "coordinates": {"lat": 53.5461, "lon": -113.4938},
                        "geocoding_coverage": 95.5,
                        "clustering_method": "database",
                    },
                    {
                        "region_name": "Near Houston",
                        "country_name": "United States",
                        "total_services": 85,
                        "avg_cost_per_service": 3200.00,
                        "unique_structures": 23,
                        "coordinates": {"lat": 29.7604, "lon": -95.3698},
                        "geocoding_coverage": 60.0,
                        "clustering_method": "major_city",
                        "distance_from_major_city": 45.2,
                    },
                ],
                "summary": {
                    "total_regions": 12,
                    "avg_geocoding_coverage": 78.5,
                    "unmapped_structures": 34,
                    "total_structures": 156,
                    "dominant_country": "Canada",
                    "dominant_region": "Alberta",
                },
                "analysis_method": "hybrid",
                "cache_hit_rate": 85.5,
            }
        }


class GeographicFilter(BaseModel):
    """Filters for geographic queries."""

    country_codes: Optional[List[str]] = Field(
        None, description="Filter by country codes (e.g., ['CA', 'US'])"
    )
    region_names: Optional[List[str]] = Field(
        None, description="Filter by region/province names"
    )
    min_services: Optional[int] = Field(
        None, ge=0, description="Minimum number of services in region"
    )
    max_distance_from_city: Optional[float] = Field(
        None, gt=0, description="Maximum distance from major city in km"
    )
    include_unmapped: bool = Field(
        True, description="Include regions without province/country mapping"
    )
