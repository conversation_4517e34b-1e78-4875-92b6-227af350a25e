from enum import Enum
from typing import Any, Dict, Generic, Optional, TypeVar, Union

from pydantic import BaseModel

RowSchemaType = TypeVar("RowSchemaType", bound=BaseModel)


class ColumnVO(BaseModel):
    id: str
    displayName: str
    field: Optional[str] = None
    aggFunc: Optional[str] = None


class SortEnum(str, Enum):
    asc = "asc"
    desc = "desc"


class FilterEnum(str, Enum):
    # implements:
    # https://www.ag-grid.com/react-data-grid/filter-text/#text-filter-model
    text = "text"
    number = "number"
    date = "date"
    set = "set"


class SimpleFilterModelType(str, Enum):
    empty = "empty"
    equals = "equals"
    notEqual = "notEqual"
    lessThan = "lessThan"
    lessThanOrEqual = "lessThanOrEqual"
    greaterThan = "greaterThan"
    greaterThanOrEqual = "greaterThanOrEqual"
    inRange = "inRange"
    contains = "contains"
    notContains = "notContains"
    startsWith = "startsWith"
    endsWith = "endsWith"
    blank = "blank"
    notBlank = "notBlank"


class FilterModel(BaseModel):
    filter: Union[str, int, None] = None
    filterType: FilterEnum
    dateFrom: Optional[str] = None
    dateTo: Optional[str] = None
    type: SimpleFilterModelType
    filterTo: Optional[str] = None


class SetFilterModel(BaseModel):
    filterType: FilterEnum
    values: list[str | int | None] = []


class SortModelItem(BaseModel):
    colId: str
    sort: SortEnum


class InifiniteRowModel(BaseModel):
    startRow: Optional[int] = None
    endRow: Optional[int] = None
    filterModel: Optional[Dict[str, Union[FilterModel, SetFilterModel]]]
    sortModel: list[SortModelItem]


class ServerSideRowModel(InifiniteRowModel):
    rowGroupCols: list[ColumnVO]
    valueCols: list[ColumnVO]
    pivotCols: list[ColumnVO]
    pivotMode: bool
    groupKeys: list[str]


class InfiniteApiResponse(BaseModel, Generic[RowSchemaType]):
    rowData: list[RowSchemaType] = []
    rowCount: Optional[int] = 0


class SsrmApiResponse(InifiniteRowModel):
    groupLevelInfo: Optional[Any] = None
    pivotResultField: Optional[list[str]] = None
