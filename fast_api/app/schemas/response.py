from typing import Generic, TypeVar

from pydantic import BaseModel, ConfigDict

DataType = TypeVar("DataType")


class ApiResponse(BaseModel, Generic[DataType]):
    model_config = ConfigDict(from_attributes=True)

    result: DataType


class PaginatedData(BaseModel, Generic[DataType]):
    """Paginated data container"""

    data: DataType
    total: int
    page: int
    per_page: int
    pages: int


class PaginatedResponse(BaseModel, Generic[DataType]):
    """Response wrapper for paginated data"""

    model_config = ConfigDict(from_attributes=True)

    result: PaginatedData[DataType]
