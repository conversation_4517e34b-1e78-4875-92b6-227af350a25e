from typing import Generic, Optional, TypeVar

from pydantic import BaseModel, ConfigDict, Field

T = TypeVar("T")
P = TypeVar("P")


class LimitOffsetPaginationInfo(BaseModel):
    """Pagination information for limit-offset based pagination."""

    limit: int = Field(description="Number of items per page", default=20)
    page: int = Field(description="Page number", default=1)


class CursorPaginationInfo(BaseModel):
    """Pagination information for cursor-based pagination."""

    cursor: str = Field(..., description="Cursor for the next page")


class PaginationLimitOffset(BaseModel):
    """Container for limit-offset pagination navigation."""

    next: Optional[LimitOffsetPaginationInfo] = Field(
        None, description="Parameters for the next page"
    )
    prev: Optional[LimitOffsetPaginationInfo] = Field(
        None, description="Parameters for the previous page"
    )


class PaginationCursor(BaseModel):
    """Container for cursor-based pagination navigation."""

    next: Optional[CursorPaginationInfo] = Field(
        None, description="Parameters for the next page"
    )
    prev: Optional[CursorPaginationInfo] = Field(
        None, description="Parameters for the previous page"
    )


class PaginationInfo(BaseModel, Generic[P]):
    """Generic pagination information for API responses."""

    count: int = Field(..., description="Total number of items")
    pages: int = Field(..., description="Total number of pages")
    next: Optional[P] = Field(None, description="Parameters for the next page")
    prev: Optional[P] = Field(None, description="Parameters for the previous page")


class PaginatedApiResponse(BaseModel, Generic[T, P]):
    """Generic paginated API response container."""

    model_config = ConfigDict(from_attributes=True)

    data: T
    info: Optional[PaginationInfo[P]] = Field(
        None, description="Pagination information"
    )
