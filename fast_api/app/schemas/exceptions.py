from pydantic import BaseModel, Field


class ApiExceptionSchema(BaseModel):
    detail: str = Field(description="Error message")
    error_code: str = Field(description="Application error code")


class ForbiddenExceptionSchema(ApiExceptionSchema):
    detail: str = Field(default="ERR: Forbidden")
    error_code: str = Field(default="FORBIDDEN")


class UnauthorizedExceptionSchema(ApiExceptionSchema):
    detail: str = Field(default="ERR: Unauthorized")
    error_code: str = Field(default="UNAUTHORIZED")


class InternalServerErrorExceptionSchema(ApiExceptionSchema):
    detail: str = Field(default="ERR: Internal Server Error")
    error_code: str = Field(default="INTERNAL_SERVER_ERROR")


class BadRequestExceptionSchema(ApiExceptionSchema):
    detail: str = Field(default="ERR: Bad Request")
    error_code: str = Field(default="BAD_REQUEST")
