"""
Geographic analysis endpoint for service analytics.

Provides geographic distribution of services with hybrid geocoding support:
- Uses existing province/country data when available
- Falls back to major city clustering for approximate locations
- Groups by coordinates for remote/unmapped areas
"""

from datetime import datetime
from typing import Dict

from fastapi import APIRouter, Depends, Security
from shared.models.models import Country, Currency, Province
from shared.models.models_work_order import WorkOrder, WorkOrderPart
from shared.services.geocoding_service import HybridGeocodingService
from sqlalchemy import case, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.models.models import Structure, User
from app.schemas.geographic import (
    GeographicAnalysis,
    GeographicAnalysisSummary,
    GeographicCoordinates,
    GeographicRegion,
)
from app.schemas.response import ApiResponse

from .schemas import ServiceCostFilters

router = APIRouter(
    prefix="/geographic",
    tags=["service-analytics-geographic"],
)


@router.post("/analysis", response_model=ApiResponse[GeographicAnalysis])
async def get_geographic_analysis(
    filters: ServiceCostFilters,
    db: AsyncSession = Depends(get_ijack_db),
    current_user: User = Security(roles_required, scopes=["admin", "service", "user"]),
) -> ApiResponse[GeographicAnalysis]:
    """
    Get geographic analysis of service costs with hybrid geocoding support.

    Features:
    - Groups services by province/country when available
    - Uses major city clustering for approximate locations
    - Falls back to coordinate-based regions for unmapped areas
    - Provides cost analytics and service frequency by region
    """
    from .service_costs import apply_common_filters

    # Build the main query with geographic grouping
    query = select(
        # Geographic identifiers with fallbacks
        func.coalesce(Province.name, "Coordinate Region").label("region_name"),
        func.coalesce(Country.country_name, "Unknown Country").label("country_name"),
        # Service metrics
        func.count(WorkOrder.id).label("total_services"),
        func.avg(
            WorkOrderPart.cost_before_tax
            * func.coalesce(func.nullif(Currency.fx_rate_cad_per, 0), 1.0)
        ).label("avg_cost_per_service"),
        func.count(func.distinct(Structure.id)).label("unique_structures"),
        # Geographic coordinates for mapping
        func.avg(Structure.gps_lat).label("avg_latitude"),
        func.avg(Structure.gps_lon).label("avg_longitude"),
        # Coordinate clustering for unmapped regions
        func.round(Structure.gps_lat, 1).label("lat_cluster"),
        func.round(Structure.gps_lon, 1).label("lon_cluster"),
        # Geocoding coverage metrics
        func.sum(case((Structure.province_id.isnot(None), 1), else_=0)).label(
            "geocoded_count"
        ),
        # Track which structures were auto-geocoded
        func.sum(case((Structure.auto_geocoded, 1), else_=0)).label(
            "auto_geocoded_count"
        ),
        # Service frequency
        func.cast(func.count(WorkOrder.id), func.Float)
        / func.nullif(func.count(func.distinct(Structure.id)), 0).label(
            "service_frequency"
        ),
    )

    # Add joins
    query = query.select_from(WorkOrder)
    query = query.join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
    query = query.join(Currency, WorkOrder.currency_id == Currency.id)
    query = query.join(Structure, WorkOrderPart.structure_id == Structure.id)
    query = query.outerjoin(Province, Structure.province_id == Province.id)
    query = query.outerjoin(Country, Province.country_id == Country.id)

    # Group by geographic identifiers
    query = query.group_by(
        Province.name,
        Country.country_name,
        func.round(Structure.gps_lat, 1),
        func.round(Structure.gps_lon, 1),
    )

    # Only include regions with meaningful data
    query = query.having(func.count(WorkOrder.id) >= 3)

    # Apply common filters
    query = apply_common_filters(query, filters)

    # Execute query
    result = await db.execute(query)
    regional_data = result.fetchall()

    # Process results with intelligent region naming
    geocoding_service = HybridGeocodingService()
    geographic_regions = []
    total_structures = 0
    total_geocoded = 0

    # Apply post-processing filters
    for row in regional_data:
        region_name = row.region_name
        clustering_method = "database"
        distance_from_city = None

        # For coordinate regions, generate meaningful names
        if (
            region_name == "Coordinate Region"
            and row.avg_latitude
            and row.avg_longitude
        ):
            # Try to identify by nearest major city
            nearest_city = geocoding_service._find_nearest_major_city(
                float(row.avg_latitude), float(row.avg_longitude)
            )

            if nearest_city and nearest_city["distance_km"] < 150:
                region_name = f"Near {nearest_city['name']}"
                clustering_method = "major_city"
                distance_from_city = round(nearest_city["distance_km"], 1)
            else:
                # Use coordinate-based name for remote regions
                region_name = (
                    f"Region ({row.avg_latitude:.1f}°, {row.avg_longitude:.1f}°)"
                )
                clustering_method = "coordinate"

        # Calculate geocoding coverage
        geocoding_coverage = 0.0
        if row.unique_structures > 0:
            geocoding_coverage = (row.geocoded_count or 0) / row.unique_structures * 100

        # Apply geographic filters before adding to results

        # Country filter
        if filters.country_codes:
            country_names = []
            for code in filters.country_codes:
                if code == "CA":
                    country_names.append("Canada")
                elif code == "US":
                    country_names.append("United States")
                else:
                    country_names.append(code)

            if (
                row.country_name not in country_names
                and row.country_name != "Unknown Country"
            ):
                continue

        # Region filter
        if filters.region_names:
            if region_name not in filters.region_names:
                continue

        # Min services filter
        if filters.min_services is not None:
            if row.total_services < filters.min_services:
                continue

        # Max distance from city filter
        if filters.max_distance_from_city is not None and distance_from_city:
            if distance_from_city > filters.max_distance_from_city:
                continue

        # Include unmapped filter
        if not filters.include_unmapped:
            if clustering_method in ["coordinate", "major_city"]:
                continue

        # Track totals for summary
        total_structures += row.unique_structures
        total_geocoded += row.geocoded_count or 0

        geographic_regions.append(
            GeographicRegion(
                region_name=region_name,
                country_name=row.country_name,
                total_services=row.total_services,
                avg_cost_per_service=float(row.avg_cost_per_service or 0),
                unique_structures=row.unique_structures,
                coordinates=GeographicCoordinates(
                    lat=float(row.avg_latitude or 0), lon=float(row.avg_longitude or 0)
                ),
                geocoding_coverage=round(geocoding_coverage, 1),
                clustering_method=clustering_method,
                distance_from_major_city=distance_from_city,
                service_frequency=round(float(row.service_frequency or 0), 2),
            )
        )

    # Sort regions by total services (descending)
    geographic_regions.sort(key=lambda x: x.total_services, reverse=True)

    # Calculate summary statistics
    avg_geocoding_coverage = 0.0
    if total_structures > 0:
        avg_geocoding_coverage = total_geocoded / total_structures * 100

    unmapped_structures = total_structures - total_geocoded

    # Find dominant country and region
    dominant_country = None
    dominant_region = None
    if geographic_regions:
        # Group by country for dominant country
        country_services = {}
        for region in geographic_regions:
            country = region.country_name
            if country not in country_services:
                country_services[country] = 0
            country_services[country] += region.total_services

        if country_services:
            dominant_country = max(country_services, key=country_services.get)

        # Dominant region is simply the one with most services
        dominant_region = geographic_regions[0].region_name

    # Create summary
    summary = GeographicAnalysisSummary(
        total_regions=len(geographic_regions),
        avg_geocoding_coverage=round(avg_geocoding_coverage, 1),
        unmapped_structures=unmapped_structures,
        total_structures=total_structures,
        dominant_country=dominant_country,
        dominant_region=dominant_region,
    )

    # Determine analysis method based on what was used
    analysis_method = "hybrid"
    if all(r.clustering_method == "database" for r in geographic_regions):
        analysis_method = "database_only"
    elif all(
        r.clustering_method in ["coordinate", "major_city"] for r in geographic_regions
    ):
        analysis_method = "coordinate_only"

    return ApiResponse(
        result=GeographicAnalysis(
            regions=geographic_regions,
            summary=summary,
            analysis_method=analysis_method,
            # Cache hit rate would be calculated if we implement cache tracking
            cache_hit_rate=None,
        )
    )


@router.post("/geocoding-status")
async def get_geocoding_status(
    db: AsyncSession = Depends(get_ijack_db),
    current_user: User = Security(roles_required, scopes=["admin", "service"]),
) -> ApiResponse[Dict]:
    """
    Get the current status of geocoding coverage across all structures.

    Returns statistics about how many structures have been geocoded,
    are pending, or have failed.
    """

    # Query geocoding status counts
    query = select(
        Structure.geocoding_status, func.count(Structure.id).label("count")
    ).group_by(Structure.geocoding_status)

    result = await db.execute(query)
    status_counts = {row.geocoding_status or "unknown": row.count for row in result}

    # Get total structures with GPS coordinates
    total_query = select(func.count(Structure.id)).where(
        Structure.gps_lat.isnot(None), Structure.gps_lon.isnot(None)
    )
    total_result = await db.execute(total_query)
    total_with_gps = total_result.scalar() or 0

    # Calculate percentages
    percentages = {}
    if total_with_gps > 0:
        for status, count in status_counts.items():
            percentages[status] = round(count / total_with_gps * 100, 1)

    return ApiResponse(
        result={
            "status_counts": status_counts,
            "percentages": percentages,
            "total_structures_with_gps": total_with_gps,
            "last_updated": datetime.utcnow().isoformat(),
        }
    )
