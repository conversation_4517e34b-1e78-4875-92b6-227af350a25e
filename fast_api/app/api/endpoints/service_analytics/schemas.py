from datetime import date
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


class DateRange(BaseModel):
    from_date: Optional[date] = Field(None, alias="from")
    to_date: Optional[date] = Field(None, alias="to")


class ServiceCostFilters(BaseModel):
    service_dates: Optional[DateRange] = None
    selected_years: Optional[list[int]] = Field(
        default=None,
        description="Filter by specific years (takes precedence over service_dates)",
    )
    customers: Optional[list[int]] = None
    service_types: Optional[list[int]] = None
    models: Optional[list[int | None]] = None
    unit_types: Optional[list[int]] = None
    technicians: Optional[list[int]] = None
    part_categories: Optional[list[str]] = None
    structure_id: Optional[int] = None
    work_order_id: Optional[int] = None
    include_ijack: Optional[bool] = Field(
        default=False, description="Include IJACK customer IDs (1, 3) in results"
    )
    include_sales_parts: Optional[bool] = Field(
        default=False, description="Include sales parts (050-) in service cost analysis"
    )


class ServiceCostOverview(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    total_costs: Decimal = Field(..., description="Total service costs")
    labor_costs: Decimal = Field(..., description="Total labor costs")
    parts_costs: Decimal = Field(..., description="Total parts costs")
    average_cost_per_order: Decimal
    work_order_count: int
    cost_trend_percentage: float
    period_comparison: str = Field(..., description="vs last period")


class CostByModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    model_type_id: int
    model_name: str
    total_cost: Decimal
    work_order_count: int
    average_cost_per_unit: Decimal  # Changed from average_cost for clarity
    unit_count: int  # Number of unique units/structures for this model
    # Part category breakdown
    sales_cost: Decimal = Field(default=0, description="050 Sales parts cost")
    pm_cost: Decimal = Field(default=0, description="060 PM parts cost")
    labor_cost: Decimal = Field(default=0, description="070 Labor/Service parts cost")
    other_cost: Decimal = Field(default=0, description="Other parts cost")


class TechnicianPerformance(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    technician_id: int
    technician_name: str
    total_orders: int
    total_cost: Decimal
    average_order_cost: Decimal
    efficiency_score: float


class HighCostUnit(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    power_unit: Optional[str]
    model_name: Optional[str]
    customer_name: Optional[str]
    total_cost: Decimal
    work_order_count: int
    avg_cost_per_service: Decimal
    last_service_date: Optional[date]
    cost_trend: str


class CostTrend(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    month: date
    total_cost: Decimal
    labor_cost: Decimal
    parts_cost: Decimal
    work_order_count: int


class CostDriver(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    category: str  # 'model', 'unit', 'part', 'service_type'
    name: str
    total_cost: Decimal
    percentage: float
    work_order_count: int


class StructureWorkOrder(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    work_order_id: int
    date_service: Optional[date]
    service_type: Optional[str]
    technician_name: Optional[str]
    labor_hours: Optional[float]
    labor_cost: Decimal
    parts_cost: Decimal
    total_cost: Decimal
    status: Optional[str]
    work_description: Optional[str]


class WorkOrderPartDetail(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    part_id: int
    part_name: Optional[str]
    description: Optional[str]
    quantity: float
    price: Decimal
    total_cost: Decimal
    warehouse: Optional[str]
    category: Optional[str]


class AvailableYears(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    years: list[int] = Field(
        ...,
        description="List of available years with work order data, sorted descending",
    )


# Phase 1 Enhanced Analytics Schemas


class CostEfficiencyMetrics(BaseModel):
    """Enhanced cost efficiency KPI metrics"""

    model_config = ConfigDict(from_attributes=True)

    # Core efficiency metrics
    total_service_hours: float = Field(
        ..., description="Total labor hours across all services"
    )
    billable_hours_ratio: float = Field(
        ..., description="Ratio of billable to total hours"
    )
    cost_per_billable_hour: Decimal = Field(..., description="Cost per billable hour")

    # Service efficiency
    avg_service_duration: float = Field(
        ..., description="Average service duration in hours"
    )
    emergency_service_rate: float = Field(
        ..., description="Percentage of emergency vs planned services"
    )

    # Parts efficiency
    parts_markup_ratio: float = Field(..., description="Average parts markup ratio")
    emergency_parts_cost_impact: Decimal = Field(
        ..., description="Additional cost from emergency parts procurement"
    )


class ServiceQualityMetrics(BaseModel):
    """Service quality indicators"""

    model_config = ConfigDict(from_attributes=True)

    # Quality indicators
    repeat_service_rate: float = Field(
        ..., description="Percentage of units requiring repeat service within 30 days"
    )
    avg_days_between_services: float = Field(
        ..., description="Average days between services per unit"
    )
    warranty_work_rate: float = Field(
        ..., description="Percentage of work that is warranty vs billable"
    )

    # Service frequency analysis
    service_frequency_score: float = Field(
        ..., description="Service frequency vs industry benchmark (1.0 = average)"
    )
    units_over_serviced: int = Field(
        ..., description="Number of units being serviced too frequently"
    )
    units_under_serviced: int = Field(
        ..., description="Number of units potentially under-serviced"
    )


class BasicOutlierDetection(BaseModel):
    """Basic outlier detection results"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Outlier metrics
    outlier_type: str = Field(
        ..., description="Type of outlier: cost, frequency, or efficiency"
    )
    severity: str = Field(..., description="Severity: low, medium, high, critical")
    z_score: float = Field(..., description="Standard deviations from mean")

    # Current metrics
    current_cost_per_service: Decimal
    current_service_frequency: float = Field(..., description="Services per month")

    # Benchmark comparison
    benchmark_cost_per_service: Decimal
    benchmark_service_frequency: float

    # Financial impact
    annual_excess_cost: Decimal = Field(..., description="Estimated annual excess cost")
    recommendation: str = Field(..., description="Recommended action")


class TechnicianEfficiencyMetrics(BaseModel):
    """Technician performance and efficiency metrics"""

    model_config = ConfigDict(from_attributes=True)

    technician_id: int
    technician_name: str

    # Efficiency metrics
    labor_efficiency_ratio: float = Field(
        ..., description="Billable hours / total hours"
    )
    avg_service_duration: float = Field(
        ..., description="Average service duration in hours"
    )
    services_per_day: float = Field(
        ..., description="Average services completed per day"
    )

    # Quality metrics
    repeat_service_rate: float = Field(
        ..., description="Percentage of services requiring return visits"
    )
    cost_per_service: Decimal = Field(..., description="Average cost per service")

    # Volume metrics
    total_services: int = Field(..., description="Total services in period")
    total_billable_hours: float = Field(..., description="Total billable hours")

    # Benchmarking
    efficiency_rank: int = Field(
        ..., description="Rank among all technicians (1 = most efficient)"
    )
    vs_team_average: float = Field(
        ..., description="Performance vs team average (1.0 = average)"
    )


class CustomerProfitabilityMetrics(BaseModel):
    """Customer profitability analysis metrics"""

    model_config = ConfigDict(from_attributes=True)

    customer_id: int
    customer_name: str

    # Financial metrics
    total_revenue: Decimal = Field(
        ..., description="Total service revenue from customer"
    )
    total_service_costs: Decimal = Field(
        ..., description="Total service costs (parts + labor)"
    )
    gross_margin: Decimal = Field(..., description="Revenue minus service costs")
    margin_percentage: float = Field(..., description="Gross margin as percentage")

    # Efficiency metrics
    avg_cost_per_unit: Decimal = Field(..., description="Average service cost per unit")
    service_frequency_vs_benchmark: float = Field(
        ..., description="Service frequency vs industry average"
    )

    # Volume metrics
    total_units: int = Field(..., description="Number of units serviced")
    total_services: int = Field(..., description="Total services performed")
    avg_services_per_unit: float = Field(..., description="Average services per unit")

    # Profitability ranking
    profitability_rank: int = Field(
        ..., description="Rank among all customers (1 = most profitable)"
    )


# Phase 2 Advanced Analytics Schemas


class TrendDataPoint(BaseModel):
    """Single data point for trend analysis"""

    model_config = ConfigDict(from_attributes=True)

    date: date
    value: Decimal
    category: Optional[str] = None


class CostDriverBreakdown(BaseModel):
    """Cost driver breakdown for unit analysis"""

    model_config = ConfigDict(from_attributes=True)

    driver_type: str = Field(
        ..., description="Type: parts, labor, service_type, technician"
    )
    driver_name: str
    total_cost: Decimal
    percentage_of_total: float
    service_count: int


class ServicePatternAnalysis(BaseModel):
    """Service pattern analysis for a unit"""

    model_config = ConfigDict(from_attributes=True)

    avg_service_interval: float = Field(
        ..., description="Average days between services"
    )
    service_frequency_per_month: float
    seasonal_pattern_detected: bool = Field(
        ..., description="Whether seasonal patterns are detected"
    )
    most_common_service_types: list[str] = Field(
        ..., description="Most frequent service types"
    )
    repeat_service_rate: float = Field(
        ..., description="Percentage of services followed by another within 30 days"
    )


class FailureAnalysis(BaseModel):
    """Failure analysis for a unit"""

    model_config = ConfigDict(from_attributes=True)

    common_failure_parts: list[dict] = Field(
        ..., description="Most frequently replaced parts"
    )
    failure_modes: list[str] = Field(..., description="Common failure patterns")
    mtbf_days: Optional[float] = Field(
        None, description="Mean time between failures in days"
    )
    warranty_failure_rate: float = Field(
        ..., description="Percentage of failures under warranty"
    )


class BenchmarkComparison(BaseModel):
    """Benchmark comparison metrics"""

    model_config = ConfigDict(from_attributes=True)

    vs_model_type_average: float = Field(
        ..., description="Performance vs same model type (1.0 = average)"
    )
    vs_customer_fleet_average: float = Field(
        ..., description="Performance vs customer's other units"
    )
    vs_top_performers: float = Field(
        ..., description="Performance vs top 10% of similar units"
    )

    model_type_rank: int = Field(..., description="Rank among same model type units")
    customer_fleet_rank: int = Field(..., description="Rank within customer's fleet")


class UnitDeepDiveAnalysis(BaseModel):
    """Comprehensive unit analysis for deep-dive investigation"""

    model_config = ConfigDict(from_attributes=True)

    # Unit identification
    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]
    power_unit: Optional[str]

    # Cost analysis
    total_lifetime_cost: Decimal
    cost_per_month: Decimal = Field(..., description="Average monthly service cost")
    cost_trend_data: list[TrendDataPoint] = Field(
        ..., description="Historical cost trends"
    )
    parts_vs_labor_ratio: float = Field(
        ..., description="Ratio of parts to labor costs"
    )
    top_cost_drivers: list[CostDriverBreakdown] = Field(
        ..., description="Top 5 cost drivers"
    )

    # Service patterns
    service_patterns: ServicePatternAnalysis

    # Failure analysis
    failure_analysis: FailureAnalysis

    # Benchmarking
    benchmark_comparison: BenchmarkComparison

    # Metadata
    analysis_period_start: date
    analysis_period_end: date
    total_services: int
    first_service_date: Optional[date]
    last_service_date: Optional[date]


class ServiceIntervalOptimization(BaseModel):
    """Service interval optimization recommendations"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Current patterns
    current_avg_interval: float = Field(
        ..., description="Current average interval in days"
    )
    current_cost_per_service: Decimal
    current_failure_rate: float = Field(
        ..., description="Failure rate with current interval"
    )

    # Optimization analysis
    recommended_interval: float = Field(..., description="Recommended interval in days")
    confidence_level: float = Field(
        ..., description="Confidence in recommendation (0-1)"
    )
    potential_cost_savings: Decimal = Field(..., description="Annual potential savings")
    risk_assessment: str = Field(..., description="Risk level: low, medium, high")

    # Implementation details
    rationale: str = Field(..., description="Explanation for recommendation")
    monitoring_metrics: list[str] = Field(
        ..., description="Metrics to monitor during implementation"
    )


class RootCauseAnalysisResult(BaseModel):
    """Root cause analysis results for problem units"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Problem identification
    primary_issue: str = Field(..., description="Primary identified issue")
    secondary_issues: list[str] = Field(..., description="Contributing factors")

    # Root cause analysis
    likely_root_causes: list[str] = Field(..., description="Most likely root causes")
    evidence_summary: str = Field(..., description="Evidence supporting the analysis")

    # Impact assessment
    cost_impact: Decimal = Field(..., description="Annual cost impact of the issue")
    frequency_impact: float = Field(..., description="Impact on service frequency")

    # Recommendations
    immediate_actions: list[str] = Field(
        ..., description="Immediate recommended actions"
    )
    long_term_solutions: list[str] = Field(
        ..., description="Long-term prevention strategies"
    )
    expected_improvement: str = Field(
        ..., description="Expected improvement from implementing recommendations"
    )

    # Analysis metadata
    confidence_score: float = Field(..., description="Confidence in analysis (0-1)")
    analysis_date: date
