from datetime import date
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


class DateRange(BaseModel):
    from_date: Optional[date] = Field(None, alias="from")
    to_date: Optional[date] = Field(None, alias="to")


class ServiceCostFilters(BaseModel):
    service_dates: Optional[DateRange] = None
    selected_years: Optional[list[int]] = Field(
        default=None,
        description="Filter by specific years (takes precedence over service_dates)",
    )
    customers: Optional[list[int]] = None
    service_types: Optional[list[int]] = None
    models: Optional[list[int | None]] = None
    unit_types: Optional[list[int]] = None
    technicians: Optional[list[int]] = None
    part_categories: Optional[list[str]] = None
    structure_id: Optional[int] = None
    work_order_id: Optional[int] = None
    include_ijack: Optional[bool] = Field(
        default=False, description="Include IJACK customer IDs (1, 3) in results"
    )
    include_sales_parts: Optional[bool] = Field(
        default=False, description="Include sales parts (050-) in service cost analysis"
    )
    # Geographic filters
    country_codes: Optional[list[str]] = Field(
        default=None, description="Filter by country codes (e.g., ['CA', 'US'])"
    )
    region_names: Optional[list[str]] = Field(
        default=None, description="Filter by region/province names"
    )
    min_services: Optional[int] = Field(
        default=None, ge=0, description="Minimum number of services in region"
    )
    max_distance_from_city: Optional[float] = Field(
        default=None, gt=0, description="Maximum distance from major city in km"
    )
    include_unmapped: Optional[bool] = Field(
        default=True, description="Include regions without province/country mapping"
    )


class ServiceCostOverview(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    total_costs: Decimal = Field(..., description="Total service costs")
    labor_costs: Decimal = Field(..., description="Total labor costs")
    parts_costs: Decimal = Field(..., description="Total parts costs")
    average_cost_per_order: Decimal
    work_order_count: int
    cost_trend_percentage: float
    period_comparison: str = Field(..., description="vs last period")


class CostByModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    model_type_id: int
    model_name: str
    total_cost: Decimal
    work_order_count: int
    average_cost_per_unit: Decimal  # Changed from average_cost for clarity
    unit_count: int  # Number of unique units/structures for this model
    # Part category breakdown
    sales_cost: Decimal = Field(default=0, description="050 Sales parts cost")
    pm_cost: Decimal = Field(default=0, description="060 PM parts cost")
    labor_cost: Decimal = Field(default=0, description="070 Labor/Service parts cost")
    other_cost: Decimal = Field(default=0, description="Other parts cost")


class TechnicianPerformance(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    technician_id: int
    technician_name: str
    total_orders: int
    total_cost: Decimal
    average_order_cost: Decimal
    efficiency_score: float


class HighCostUnit(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    power_unit: Optional[str]
    model_name: Optional[str]
    customer_name: Optional[str]
    total_cost: Decimal
    work_order_count: int
    avg_cost_per_service: Decimal
    last_service_date: Optional[date]
    cost_trend: str


class CostTrend(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    month: date
    total_cost: Decimal
    labor_cost: Decimal
    parts_cost: Decimal
    work_order_count: int


class CostDriver(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    category: str  # 'model', 'unit', 'part', 'service_type'
    name: str
    total_cost: Decimal
    percentage: float
    work_order_count: int


class StructureWorkOrder(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    work_order_id: int
    date_service: Optional[date]
    service_type: Optional[str]
    technician_name: Optional[str]
    labor_hours: Optional[float]
    labor_cost: Decimal
    parts_cost: Decimal
    total_cost: Decimal
    status: Optional[str]
    work_description: Optional[str]


class WorkOrderPartDetail(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    part_id: int
    part_name: Optional[str]
    description: Optional[str]
    quantity: float
    price: Decimal
    total_cost: Decimal
    warehouse: Optional[str]
    category: Optional[str]


class AvailableYears(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    years: list[int] = Field(
        ...,
        description="List of available years with work order data, sorted descending",
    )


# Phase 1 Enhanced Analytics Schemas


class CostEfficiencyMetrics(BaseModel):
    """Enhanced cost efficiency KPI metrics"""

    model_config = ConfigDict(from_attributes=True)

    # Core efficiency metrics
    total_service_hours: float = Field(
        ..., description="Total labor hours across all services"
    )
    billable_hours_ratio: float = Field(
        ..., description="Ratio of billable to total hours"
    )
    cost_per_billable_hour: Decimal = Field(..., description="Cost per billable hour")

    # Service efficiency
    avg_service_duration: float = Field(
        ..., description="Average service duration in hours"
    )
    emergency_service_rate: float = Field(
        ..., description="Percentage of emergency vs planned services"
    )

    # Parts efficiency
    parts_markup_ratio: float = Field(..., description="Average parts markup ratio")
    emergency_parts_cost_impact: Decimal = Field(
        ..., description="Additional cost from emergency parts procurement"
    )


class ServiceQualityMetrics(BaseModel):
    """Service quality indicators"""

    model_config = ConfigDict(from_attributes=True)

    # Quality indicators
    repeat_service_rate: float = Field(
        ..., description="Percentage of units requiring repeat service within 30 days"
    )
    avg_days_between_services: float = Field(
        ..., description="Average days between services per unit"
    )
    warranty_work_rate: float = Field(
        ..., description="Percentage of work that is warranty vs billable"
    )

    # Service frequency analysis
    service_frequency_score: float = Field(
        ..., description="Service frequency vs industry benchmark (1.0 = average)"
    )
    units_over_serviced: int = Field(
        ..., description="Number of units being serviced too frequently"
    )
    units_under_serviced: int = Field(
        ..., description="Number of units potentially under-serviced"
    )


class BasicOutlierDetection(BaseModel):
    """Basic outlier detection results"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    power_unit_str: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Outlier metrics
    outlier_type: str = Field(
        ..., description="Type of outlier: cost, frequency, or efficiency"
    )
    severity: str = Field(..., description="Severity: low, medium, high, critical")
    z_score: float = Field(..., description="Standard deviations from mean")

    # Current metrics
    current_cost_per_service: Decimal
    current_service_frequency: float = Field(..., description="Services per month")

    # Benchmark comparison
    benchmark_cost_per_service: Decimal
    benchmark_service_frequency: float

    # Financial impact
    annual_excess_cost: Decimal = Field(..., description="Estimated annual excess cost")
    recommendation: str = Field(..., description="Recommended action")


class TechnicianEfficiencyMetrics(BaseModel):
    """Technician performance and efficiency metrics"""

    model_config = ConfigDict(from_attributes=True)

    technician_id: int
    technician_name: str

    # Efficiency metrics
    labor_efficiency_ratio: float = Field(
        ..., description="Billable hours / total hours"
    )
    avg_service_duration: float = Field(
        ..., description="Average service duration in hours"
    )
    services_per_day: float = Field(
        ..., description="Average services completed per day"
    )

    # Quality metrics
    repeat_service_rate: float = Field(
        ..., description="Percentage of services requiring return visits"
    )
    cost_per_service: Decimal = Field(..., description="Average cost per service")

    # Volume metrics
    total_services: int = Field(..., description="Total services in period")
    total_billable_hours: float = Field(..., description="Total billable hours")

    # Benchmarking
    efficiency_rank: int = Field(
        ..., description="Rank among all technicians (1 = most efficient)"
    )
    vs_team_average: float = Field(
        ..., description="Performance vs team average (1.0 = average)"
    )


class CustomerProfitabilityMetrics(BaseModel):
    """Customer profitability analysis metrics"""

    model_config = ConfigDict(from_attributes=True)

    customer_id: int
    customer_name: str

    # Financial metrics
    total_revenue: Decimal = Field(
        ..., description="Total service revenue from customer"
    )
    total_service_costs: Decimal = Field(
        ..., description="Total service costs (parts + labor)"
    )
    gross_margin: Decimal = Field(..., description="Revenue minus service costs")
    margin_percentage: float = Field(..., description="Gross margin as percentage")

    # Efficiency metrics
    avg_cost_per_unit: Decimal = Field(..., description="Average service cost per unit")
    service_frequency_vs_benchmark: float = Field(
        ..., description="Service frequency vs industry average"
    )

    # Volume metrics
    total_units: int = Field(..., description="Number of units serviced")
    total_services: int = Field(..., description="Total services performed")
    avg_services_per_unit: float = Field(..., description="Average services per unit")

    # Profitability ranking
    profitability_rank: int = Field(
        ..., description="Rank among all customers (1 = most profitable)"
    )


# Phase 2 Advanced Analytics Schemas


class TrendDataPoint(BaseModel):
    """Single data point for trend analysis"""

    model_config = ConfigDict(from_attributes=True)

    date: date
    value: Decimal
    category: Optional[str] = None


class CostDriverBreakdown(BaseModel):
    """Cost driver breakdown for unit analysis"""

    model_config = ConfigDict(from_attributes=True)

    driver_type: str = Field(
        ..., description="Type: parts, labor, service_type, technician"
    )
    driver_name: str
    total_cost: Decimal
    percentage_of_total: float
    service_count: int


class ServicePatternAnalysis(BaseModel):
    """Service pattern analysis for a unit"""

    model_config = ConfigDict(from_attributes=True)

    avg_service_interval: float = Field(
        ..., description="Average days between services"
    )
    service_frequency_per_month: float
    seasonal_pattern_detected: bool = Field(
        ..., description="Whether seasonal patterns are detected"
    )
    most_common_service_types: list[str] = Field(
        ..., description="Most frequent service types"
    )
    repeat_service_rate: float = Field(
        ..., description="Percentage of services followed by another within 30 days"
    )


class FailureAnalysis(BaseModel):
    """Failure analysis for a unit"""

    model_config = ConfigDict(from_attributes=True)

    common_failure_parts: list[dict] = Field(
        ..., description="Most frequently replaced parts"
    )
    failure_modes: list[str] = Field(..., description="Common failure patterns")
    mtbf_days: Optional[float] = Field(
        None, description="Mean time between failures in days"
    )
    warranty_failure_rate: float = Field(
        ..., description="Percentage of failures under warranty"
    )


class BenchmarkComparison(BaseModel):
    """Benchmark comparison metrics"""

    model_config = ConfigDict(from_attributes=True)

    vs_model_type_average: float = Field(
        ..., description="Performance vs same model type (1.0 = average)"
    )
    vs_customer_fleet_average: float = Field(
        ..., description="Performance vs customer's other units"
    )
    vs_top_performers: float = Field(
        ..., description="Performance vs top 10% of similar units"
    )

    model_type_rank: int = Field(..., description="Rank among same model type units")
    customer_fleet_rank: int = Field(..., description="Rank within customer's fleet")


class UnitDeepDiveAnalysis(BaseModel):
    """Comprehensive unit analysis for deep-dive investigation"""

    model_config = ConfigDict(from_attributes=True)

    # Unit identification
    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]
    power_unit: Optional[str]

    # Cost analysis
    total_lifetime_cost: Decimal
    cost_per_month: Decimal = Field(..., description="Average monthly service cost")
    cost_trend_data: list[TrendDataPoint] = Field(
        ..., description="Historical cost trends"
    )
    parts_vs_labor_ratio: float = Field(
        ..., description="Ratio of parts to labor costs"
    )
    top_cost_drivers: list[CostDriverBreakdown] = Field(
        ..., description="Top 5 cost drivers"
    )

    # Service patterns
    service_patterns: ServicePatternAnalysis

    # Failure analysis
    failure_analysis: FailureAnalysis

    # Benchmarking
    benchmark_comparison: BenchmarkComparison

    # Metadata
    analysis_period_start: date
    analysis_period_end: date
    total_services: int
    first_service_date: Optional[date]
    last_service_date: Optional[date]


class ServiceIntervalOptimization(BaseModel):
    """Service interval optimization recommendations"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Current patterns
    current_avg_interval: float = Field(
        ..., description="Current average interval in days"
    )
    current_cost_per_service: Decimal
    current_failure_rate: float = Field(
        ..., description="Failure rate with current interval"
    )

    # Optimization analysis
    recommended_interval: float = Field(..., description="Recommended interval in days")
    confidence_level: float = Field(
        ..., description="Confidence in recommendation (0-1)"
    )
    potential_cost_savings: Decimal = Field(..., description="Annual potential savings")
    risk_assessment: str = Field(..., description="Risk level: low, medium, high")

    # Implementation details
    rationale: str = Field(..., description="Explanation for recommendation")
    monitoring_metrics: list[str] = Field(
        ..., description="Metrics to monitor during implementation"
    )


class RootCauseAnalysisResult(BaseModel):
    """Root cause analysis results for problem units"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Problem identification
    primary_issue: str = Field(..., description="Primary identified issue")
    secondary_issues: list[str] = Field(..., description="Contributing factors")

    # Root cause analysis
    likely_root_causes: list[str] = Field(..., description="Most likely root causes")
    evidence_summary: str = Field(..., description="Evidence supporting the analysis")

    # Impact assessment
    cost_impact: Decimal = Field(..., description="Annual cost impact of the issue")
    frequency_impact: float = Field(..., description="Impact on service frequency")

    # Recommendations
    immediate_actions: list[str] = Field(
        ..., description="Immediate recommended actions"
    )
    long_term_solutions: list[str] = Field(
        ..., description="Long-term prevention strategies"
    )
    expected_improvement: str = Field(
        ..., description="Expected improvement from implementing recommendations"
    )

    # Analysis metadata
    confidence_score: float = Field(..., description="Confidence in analysis (0-1)")
    analysis_date: date


# Phase 3 Predictive Analytics Schemas


class PredictiveCostModel(BaseModel):
    """Predictive cost modeling results"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Current state
    current_monthly_cost: Decimal = Field(
        ..., description="Current average monthly cost"
    )
    historical_cost_trend: list[TrendDataPoint] = Field(
        ..., description="Historical cost data points"
    )

    # Predictions
    predicted_next_service_cost: Decimal = Field(
        ..., description="Predicted cost of next service"
    )
    predicted_next_service_date: date = Field(
        ..., description="Predicted date of next service"
    )
    confidence_interval_low: Decimal = Field(
        ..., description="Lower bound of cost prediction"
    )
    confidence_interval_high: Decimal = Field(
        ..., description="Upper bound of cost prediction"
    )

    # 12-month forecast
    monthly_cost_forecast: list[TrendDataPoint] = Field(
        ..., description="12-month cost forecast"
    )
    annual_cost_prediction: Decimal = Field(
        ..., description="Predicted total annual cost"
    )

    # Risk factors
    cost_volatility_score: float = Field(
        ..., description="Cost predictability score (0-1, higher = more volatile)"
    )
    risk_factors: list[str] = Field(..., description="Identified cost risk factors")

    # Model metadata
    prediction_confidence: float = Field(
        ..., description="Overall prediction confidence (0-1)"
    )
    model_accuracy: float = Field(..., description="Historical model accuracy")
    last_updated: date = Field(..., description="When predictions were generated")


class FailurePredictionResult(BaseModel):
    """Parts failure prediction results"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # High-risk parts
    high_risk_parts: list[dict] = Field(
        ..., description="Parts with high failure probability"
    )

    # Failure timeline
    predicted_failures: list[dict] = Field(
        ..., description="Predicted failure events with timing"
    )

    # Service interval analysis
    optimal_service_interval: float = Field(
        ..., description="ML-recommended service interval in days"
    )
    current_vs_optimal_ratio: float = Field(
        ..., description="Current interval vs optimal (1.0 = optimal)"
    )

    # Risk assessment
    overall_failure_risk: str = Field(
        ..., description="Overall failure risk: low, medium, high, critical"
    )
    imminent_failure_probability: float = Field(
        ..., description="Probability of failure in next 30 days"
    )

    # Recommendations
    recommended_actions: list[str] = Field(
        ..., description="Recommended preventive actions"
    )
    priority_score: float = Field(
        ..., description="Priority score for maintenance scheduling"
    )

    # Model metadata
    prediction_horizon_days: int = Field(
        ..., description="How many days ahead predictions are valid"
    )
    model_confidence: float = Field(..., description="Model confidence score")


class AdvancedRootCauseAnalysis(BaseModel):
    """Advanced ML-powered root cause analysis"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Problem classification
    problem_category: str = Field(..., description="ML-classified problem category")
    problem_severity: str = Field(
        ..., description="Severity: low, medium, high, critical"
    )
    problem_description: str = Field(
        ..., description="Auto-generated problem description"
    )

    # Root cause analysis
    primary_root_cause: str = Field(..., description="Most likely root cause")
    contributing_factors: list[dict] = Field(
        ..., description="Contributing factors with impact scores"
    )
    correlation_analysis: list[dict] = Field(
        ..., description="Statistical correlations found"
    )

    # Pattern analysis
    similar_cases: list[dict] = Field(
        ..., description="Similar historical cases and outcomes"
    )
    recurring_pattern: bool = Field(
        ..., description="Whether this is part of a recurring pattern"
    )
    pattern_frequency: Optional[float] = Field(
        None, description="Frequency of this pattern (occurrences per year)"
    )

    # Cost impact analysis
    direct_cost_impact: Decimal = Field(..., description="Direct cost impact")
    indirect_cost_impact: Decimal = Field(..., description="Estimated indirect costs")
    total_cost_impact: Decimal = Field(..., description="Total estimated cost impact")

    # Solution recommendations
    immediate_fixes: list[dict] = Field(
        ..., description="Immediate corrective actions with effort estimates"
    )
    preventive_measures: list[dict] = Field(
        ..., description="Preventive measures with cost-benefit analysis"
    )
    long_term_solutions: list[dict] = Field(
        ..., description="Long-term strategic solutions"
    )

    # Success probability
    solution_success_probability: float = Field(
        ..., description="Probability that recommended solutions will work"
    )
    expected_cost_reduction: Decimal = Field(
        ..., description="Expected cost reduction from implementing solutions"
    )

    # Analysis metadata
    analysis_algorithm: str = Field(..., description="ML algorithm used for analysis")
    confidence_score: float = Field(..., description="Overall analysis confidence")
    data_quality_score: float = Field(
        ..., description="Quality of data used for analysis"
    )
    analysis_timestamp: date = Field(..., description="When analysis was performed")


class IntelligentRecommendation(BaseModel):
    """AI-generated intelligent recommendations"""

    model_config = ConfigDict(from_attributes=True)

    # Recommendation identification
    recommendation_id: str = Field(..., description="Unique recommendation identifier")
    recommendation_type: str = Field(
        ..., description="Type: maintenance, parts, process, training"
    )
    priority: str = Field(..., description="Priority: low, medium, high, urgent")

    # Target scope
    target_structures: list[int] = Field(
        ..., description="Structure IDs affected by recommendation"
    )
    target_customers: list[int] = Field(
        ..., description="Customer IDs affected by recommendation"
    )
    target_model_types: list[int] = Field(
        ..., description="Model type IDs affected by recommendation"
    )

    # Recommendation details
    title: str = Field(..., description="Short recommendation title")
    description: str = Field(..., description="Detailed recommendation description")
    rationale: str = Field(..., description="Why this recommendation was generated")

    # Implementation details
    implementation_steps: list[str] = Field(
        ..., description="Step-by-step implementation guide"
    )
    estimated_effort_hours: float = Field(
        ..., description="Estimated implementation effort"
    )
    estimated_cost: Decimal = Field(..., description="Estimated implementation cost")

    # Expected outcomes
    expected_cost_savings: Decimal = Field(
        ..., description="Expected annual cost savings"
    )
    expected_failure_reduction: float = Field(
        ..., description="Expected reduction in failure rate (0-1)"
    )
    payback_period_months: float = Field(
        ..., description="Expected payback period in months"
    )

    # Risk assessment
    implementation_risk: str = Field(
        ..., description="Implementation risk: low, medium, high"
    )
    risk_factors: list[str] = Field(..., description="Potential implementation risks")

    # Validation
    supporting_evidence: list[str] = Field(
        ..., description="Evidence supporting this recommendation"
    )
    confidence_score: float = Field(
        ..., description="Confidence in recommendation success"
    )

    # Metadata
    generated_by_algorithm: str = Field(
        ..., description="AI algorithm that generated recommendation"
    )
    generated_date: date = Field(..., description="When recommendation was generated")
    expires_date: Optional[date] = Field(
        None, description="When recommendation expires (if applicable)"
    )


# Phase 4 Enhanced Temporal and Operational Insights Schemas


class SeasonalPattern(BaseModel):
    """Individual seasonal pattern data point"""

    model_config = ConfigDict(from_attributes=True)

    month: int = Field(..., description="Month number (1-12)")
    month_name: str = Field(..., description="Month name")
    average_cost: Decimal = Field(
        ..., description="Average service cost for this month"
    )
    service_count: int = Field(..., description="Number of services in this month")
    cost_variance: float = Field(..., description="Variance from annual average")
    failure_rate: float = Field(..., description="Failure rate for this month")


class SeasonalAnalysis(BaseModel):
    """Comprehensive seasonal cost and failure analysis"""

    model_config = ConfigDict(from_attributes=True)

    # Overall seasonal metrics
    seasonal_cost_variation: float = Field(
        ..., description="Coefficient of variation across seasons"
    )
    highest_cost_season: str = Field(
        ..., description="Season with highest average costs"
    )
    lowest_cost_season: str = Field(..., description="Season with lowest average costs")
    peak_failure_months: list[str] = Field(
        ..., description="Months with highest failure rates"
    )

    # Monthly breakdown
    monthly_patterns: list[SeasonalPattern] = Field(
        ..., description="Month-by-month analysis"
    )

    # Seasonal forecasting
    next_seasonal_peak: date = Field(..., description="Predicted next cost peak date")
    recommended_prep_actions: list[str] = Field(
        ..., description="Recommended preparation actions"
    )

    # Weather correlation insights
    weather_impact_score: float = Field(
        ..., description="Estimated weather impact on costs (0-1)"
    )
    climate_risk_factors: list[str] = Field(
        ..., description="Identified climate-related risk factors"
    )


class WarrantyAnalysis(BaseModel):
    """Warranty vs non-warranty service cost analysis"""

    model_config = ConfigDict(from_attributes=True)

    # Cost breakdown
    warranty_costs: Decimal = Field(..., description="Total warranty service costs")
    non_warranty_costs: Decimal = Field(
        ..., description="Total non-warranty service costs"
    )
    warranty_percentage: float = Field(
        ..., description="Percentage of total costs that are warranty"
    )

    # Efficiency metrics
    warranty_avg_cost_per_service: Decimal = Field(
        ..., description="Average cost per warranty service"
    )
    non_warranty_avg_cost_per_service: Decimal = Field(
        ..., description="Average cost per non-warranty service"
    )
    warranty_efficiency_ratio: float = Field(
        ..., description="Warranty vs non-warranty efficiency"
    )

    # Temporal analysis
    warranty_trend: str = Field(..., description="Trend in warranty costs over time")
    warranty_frequency_by_age: list[dict] = Field(
        ..., description="Warranty frequency by equipment age"
    )

    # Risk assessment
    high_warranty_models: list[dict] = Field(
        ..., description="Models with high warranty costs"
    )
    warranty_cost_drivers: list[str] = Field(
        ..., description="Main drivers of warranty costs"
    )


class GeographicCostMetrics(BaseModel):
    """Geographic cost variation analysis"""

    model_config = ConfigDict(from_attributes=True)

    region_id: int = Field(..., description="Region identifier (province/state)")
    region_name: str = Field(..., description="Region name")
    country_name: str = Field(..., description="Country name")

    # Cost metrics
    average_cost_per_service: Decimal = Field(
        ..., description="Average service cost in region"
    )
    total_services: int = Field(..., description="Total services in region")
    cost_vs_national_average: float = Field(
        ..., description="Ratio vs national average"
    )

    # Service density and efficiency
    service_density: float = Field(..., description="Services per geographic area")
    travel_efficiency_score: float = Field(
        ..., description="Estimated travel efficiency"
    )

    # Regional characteristics
    climate_zone: Optional[str] = Field(None, description="Climate classification")
    cost_drivers: list[str] = Field(..., description="Main cost drivers in this region")


class ServiceUrgencyAnalysis(BaseModel):
    """Service urgency classification and emergency cost analysis"""

    model_config = ConfigDict(from_attributes=True)

    # Urgency classification
    emergency_services: int = Field(..., description="Number of emergency services")
    planned_services: int = Field(..., description="Number of planned services")
    emergency_percentage: float = Field(
        ..., description="Percentage of services that are emergency"
    )

    # Cost impact
    emergency_cost_premium: float = Field(
        ..., description="Cost premium for emergency services"
    )
    emergency_total_cost: Decimal = Field(
        ..., description="Total cost of emergency services"
    )
    planned_total_cost: Decimal = Field(
        ..., description="Total cost of planned services"
    )

    # Time analysis
    emergency_response_time: float = Field(
        ..., description="Average emergency response time"
    )
    planned_scheduling_efficiency: float = Field(
        ..., description="Planned service scheduling efficiency"
    )

    # Prevention opportunities
    preventable_emergencies: int = Field(
        ..., description="Estimated preventable emergency services"
    )
    potential_savings: Decimal = Field(
        ..., description="Potential savings from prevention"
    )


class CompositeRiskScore(BaseModel):
    """Unified risk scoring combining multiple factors"""

    model_config = ConfigDict(from_attributes=True)

    structure_id: int
    structure_number: Optional[str]
    customer_name: Optional[str]
    model_name: Optional[str]

    # Overall risk score (0-100)
    composite_risk_score: float = Field(..., description="Overall risk score (0-100)")
    risk_category: str = Field(
        ..., description="Risk category: low, medium, high, critical"
    )

    # Component scores
    cost_risk_score: float = Field(..., description="Cost escalation risk (0-100)")
    failure_risk_score: float = Field(
        ..., description="Failure probability risk (0-100)"
    )
    operational_risk_score: float = Field(
        ..., description="Operational disruption risk (0-100)"
    )
    financial_risk_score: float = Field(
        ..., description="Financial impact risk (0-100)"
    )

    # Risk drivers
    primary_risk_factors: list[str] = Field(..., description="Main risk contributors")
    risk_trend: str = Field(
        ..., description="Risk trend: increasing, stable, decreasing"
    )

    # Recommendations
    immediate_actions: list[str] = Field(
        ..., description="Immediate risk mitigation actions"
    )
    monitoring_metrics: list[str] = Field(..., description="Key metrics to monitor")
    escalation_threshold: float = Field(..., description="Score at which to escalate")

    # Temporal analysis
    risk_forecast_30_days: float = Field(
        ..., description="Risk score forecast for 30 days"
    )
    risk_forecast_90_days: float = Field(
        ..., description="Risk score forecast for 90 days"
    )


class TemporalInsightsSummary(BaseModel):
    """Summary of all temporal insights for dashboard"""

    model_config = ConfigDict(from_attributes=True)

    # Key insights
    seasonal_cost_impact: Decimal = Field(
        ..., description="Annual cost impact of seasonality"
    )
    warranty_cost_opportunity: Decimal = Field(
        ..., description="Warranty cost optimization opportunity"
    )
    emergency_reduction_potential: Decimal = Field(
        ..., description="Emergency cost reduction potential"
    )
    geographic_optimization_savings: Decimal = Field(
        ..., description="Geographic optimization savings"
    )

    # Alerts and recommendations
    seasonal_alerts: list[str] = Field(..., description="Upcoming seasonal concerns")
    warranty_alerts: list[str] = Field(..., description="Warranty-related alerts")
    geographic_alerts: list[str] = Field(
        ..., description="Geographic efficiency alerts"
    )
    urgency_alerts: list[str] = Field(..., description="Service urgency alerts")

    # Performance metrics
    temporal_efficiency_score: float = Field(
        ..., description="Overall temporal efficiency (0-100)"
    )
    operational_efficiency_score: float = Field(
        ..., description="Overall operational efficiency (0-100)"
    )
    cost_optimization_score: float = Field(
        ..., description="Cost optimization effectiveness (0-100)"
    )
