import statistics
from datetime import date
from decimal import Decimal
from typing import List, Optional

from fastapi import APIRouter, Depends, Security
from shared.config import (
    IJACK_CUST_IDS_LIST,
    ROLE_ID_IJACK_SALES,
    ROLE_ID_IJACK_SOFTWARE_DEV,
)
from shared.models.models import <PERSON>ur<PERSON>cy, Customer, PowerUnit
from shared.models.models_bom import ModelType, Part, Warehouse
from shared.models.models_work_order import (
    ServiceType,
    WorkOrder,
    WorkOrderPart,
    WorkOrderStatus,
    work_order_model_type_rel,
    work_order_user_rel,
)
from shared.utils.currency_utils import (
    get_currency_conversion_expression,
    get_monthly_avg_exchange_rate_cte,
)
from sqlalchemy import case, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.models.models import Structure, User
from app.schemas.response import ApiResponse

from .schemas import (
    AvailableYears,
    BasicOutlierDetection,
    BenchmarkComparison,
    CostByModel,
    CostDriver,
    CostDriverBreakdown,
    CostEfficiencyMetrics,
    CostTrend,
    CustomerProfitabilityMetrics,
    FailureAnalysis,
    HighCostUnit,
    RootCauseAnalysisResult,
    ServiceCostFilters,
    ServiceCostOverview,
    ServiceIntervalOptimization,
    ServicePatternAnalysis,
    ServiceQualityMetrics,
    StructureWorkOrder,
    TechnicianEfficiencyMetrics,
    TrendDataPoint,
    UnitDeepDiveAnalysis,
    WorkOrderPartDetail,
)


def get_part_category_filter_conditions(part_categories: Optional[List[str]]):
    """Get SQL filter conditions for part categories - DRY helper function"""
    if part_categories is None or len(part_categories) == 0:
        return []

    part_category_conditions = []
    for category in part_categories:
        if category == "050 (Sales)":
            part_category_conditions.append(Part.part_num.like("050-%"))
        elif category == "060 (PM Parts)":
            part_category_conditions.append(Part.part_num.like("060-%"))
        elif category == "070 (Service)":
            part_category_conditions.append(Part.part_num.like("070-%"))
        elif category == "0":
            part_category_conditions.append(Part.part_num == "0")
        elif category == "Other":
            part_category_conditions.append(
                ~Part.part_num.like("050-%")
                & ~Part.part_num.like("060-%")
                & ~Part.part_num.like("070-%")
                & (Part.part_num != "0")
            )
    return part_category_conditions


def should_include_part_python(
    part_num: str, part_categories: Optional[List[str]]
) -> bool:
    """Python helper to check if a part matches selected categories - DRY helper function"""
    if part_categories is None or len(part_categories) == 0:
        return True

    for category in part_categories:
        if category == "050 (Sales)" and part_num.startswith("050-"):
            return True
        elif category == "060 (PM Parts)" and part_num.startswith("060-"):
            return True
        elif category == "070 (Service)" and part_num.startswith("070-"):
            return True
        elif category == "0" and part_num == "0":
            return True
        elif (
            category == "Other"
            and not part_num.startswith("050-")
            and not part_num.startswith("060-")
            and not part_num.startswith("070-")
            and part_num != "0"
        ):
            return True
    return False


def apply_common_filters(query, filters: ServiceCostFilters):
    """Apply common filters to a query that joins WorkOrder, Customer, and WorkOrderPart"""
    # Date filters - selected_years takes precedence over service_dates
    if filters.selected_years is not None and len(filters.selected_years) > 0:
        # Filter by specific years
        query = query.where(
            func.extract("year", WorkOrder.date_service).in_(filters.selected_years)
        )
    else:
        # Use regular date range filtering if no specific years selected
        if (
            filters.service_dates is not None
            and filters.service_dates.from_date is not None
        ):
            query = query.where(
                WorkOrder.date_service >= filters.service_dates.from_date
            )
        if (
            filters.service_dates is not None
            and filters.service_dates.to_date is not None
        ):
            query = query.where(WorkOrder.date_service <= filters.service_dates.to_date)

    # Customer filter
    if filters.customers is not None and len(filters.customers) > 0:
        query = query.where(Customer.id.in_(filters.customers))

    # Service type filter
    if filters.service_types is not None and len(filters.service_types) > 0:
        query = query.where(WorkOrder.service_type_id.in_(filters.service_types))

    # Technician filter
    if filters.technicians is not None and len(filters.technicians) > 0:
        technician_work_orders = select(work_order_user_rel.c.work_order_id).where(
            work_order_user_rel.c.user_id.in_(filters.technicians)
        )
        query = query.where(WorkOrder.id.in_(technician_work_orders))

    # Part categories filter
    if filters.part_categories is not None and len(filters.part_categories) > 0:
        part_category_conditions = []
        for category in filters.part_categories:
            if category == "050 (Sales)":
                part_category_conditions.append(Part.part_num.like("%050-%"))
            elif category == "060 (PM Parts)":
                part_category_conditions.append(Part.part_num.like("%060-%"))
            elif category == "070 (Service)":
                part_category_conditions.append(Part.part_num.like("%070-%"))
            elif category == "0":
                part_category_conditions.append(Part.part_num == "0")
            elif category == "Other":
                part_category_conditions.append(
                    ~Part.part_num.like("%050-%")
                    & ~Part.part_num.like("%060-%")
                    & ~Part.part_num.like("%070-%")
                    & (Part.part_num != "0")
                )

        if part_category_conditions:
            # We need to join with Part table and filter work orders that have parts in specified categories
            part_filtered_work_orders = (
                select(WorkOrderPart.work_order_id.distinct())
                .select_from(WorkOrderPart)
                .join(Part, WorkOrderPart.part_id == Part.id)
                .where(or_(*part_category_conditions))
            )
            query = query.where(WorkOrder.id.in_(part_filtered_work_orders))

    # IJACK customers filter - when include_ijack is False, exclude IJACK customer IDs
    if filters.include_ijack is False:
        query = query.where(Customer.id.notin_(IJACK_CUST_IDS_LIST))

    # Sales parts filter - when include_sales_parts is False, exclude sales parts (050-)
    if filters.include_sales_parts is False:
        sales_parts_work_orders = (
            select(WorkOrderPart.work_order_id.distinct())
            .select_from(WorkOrderPart)
            .join(Part, WorkOrderPart.part_id == Part.id)
            .where(Part.part_num.like("050-%"))
        )
        query = query.where(WorkOrder.id.notin_(sales_parts_work_orders))

    # Unit types filter - filter work orders based on structure unit types
    if filters.unit_types is not None and len(filters.unit_types) > 0:
        unit_type_work_orders = (
            select(WorkOrderPart.work_order_id.distinct())
            .select_from(WorkOrderPart)
            .join(Structure, WorkOrderPart.structure_id == Structure.id)
            .where(Structure.unit_type_id.in_(filters.unit_types))
        )
        query = query.where(WorkOrder.id.in_(unit_type_work_orders))

    # Models filter - filter work orders based on model types
    if filters.models is not None and len(filters.models) > 0:
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        if len(non_null_values) > 0:
            model_work_orders = select(work_order_model_type_rel.c.work_order_id).where(
                work_order_model_type_rel.c.model_type_id.in_(non_null_values)
            )
            query = query.where(WorkOrder.id.in_(model_work_orders))

    return query


router = APIRouter(
    tags=["service", "analytics"],
    prefix="/service-analytics",
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)


@router.post("/cost-overview")
async def get_cost_overview(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[Optional[ServiceCostOverview]]:
    """Get overview of service costs for date range"""
    # Get non-voided work order status IDs
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Create monthly average exchange rate CTE
    monthly_rates = get_monthly_avg_exchange_rate_cte()

    # Build the main query with currency conversion
    query = (
        select(
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_costs"),
            func.count(WorkOrder.id.distinct()).label("work_order_count"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)  # Add Part join for filtering
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply part category filtering at the part level using helper function
    part_category_conditions = get_part_category_filter_conditions(
        filters.part_categories
    )
    if part_category_conditions:
        query = query.where(or_(*part_category_conditions))

    # Apply other common filters but exclude part categories (we handled them above)
    filters_without_parts = ServiceCostFilters(
        service_dates=filters.service_dates,
        selected_years=filters.selected_years,
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        unit_types=filters.unit_types,
        technicians=filters.technicians,
        part_categories=None,  # Already handled above
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    query = apply_common_filters(query, filters_without_parts)

    # Execute query
    response = await db.execute(query)
    result = response.first()

    if not result or result.total_costs is None:
        return {"result": None}

    # Calculate average cost per order
    average_cost_per_order = (
        result.total_costs / result.work_order_count
        if result.work_order_count > 0
        else 0
    )

    # Calculate period comparison (simplified for now)
    # TODO: Add proper period comparison logic
    cost_trend_percentage = 0.0
    period_comparison = "vs last period"

    # For now, we'll set labor/parts costs to half each (TODO: implement proper split)
    labor_costs = result.total_costs / 2
    parts_costs = result.total_costs / 2

    overview = ServiceCostOverview(
        total_costs=result.total_costs,
        labor_costs=labor_costs,
        parts_costs=parts_costs,
        average_cost_per_order=average_cost_per_order,
        work_order_count=result.work_order_count,
        cost_trend_percentage=cost_trend_percentage,
        period_comparison=period_comparison,
    )

    return {"result": overview}


@router.post("/cost-by-model")
async def get_cost_by_model(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[CostByModel]]:
    """Get average yearly service costs per unit grouped by model type with part category breakdown"""

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Create monthly average exchange rate CTE
    monthly_rates = get_monthly_avg_exchange_rate_cte()

    # First, get all model types with their costs broken down by category (with currency conversion)
    base_query = (
        select(
            ModelType.id.label("model_type_id"),
            ModelType.model.label("model_name"),
            WorkOrder.id.label("work_order_id"),
            WorkOrder.date_service,
            WorkOrderPart.structure_id,
            get_currency_conversion_expression(
                WorkOrderPart.cost_before_tax,
                WorkOrder.date_service,
                WorkOrder.currency_id,
                monthly_rates,
            ).label("cost_before_tax_cad"),
            Part.part_num,
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .join(
            work_order_model_type_rel,
            WorkOrder.id == work_order_model_type_rel.c.work_order_id,
        )
        .join(
            ModelType,
            work_order_model_type_rel.c.model_type_id == ModelType.id,
        )
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply common filters but exclude part categories (we'll handle that at the part level)
    filters_without_parts = ServiceCostFilters(
        service_dates=filters.service_dates,
        selected_years=filters.selected_years,
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        unit_types=filters.unit_types,
        technicians=filters.technicians,
        part_categories=None,  # Don't filter by part categories at work order level
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    base_query = apply_common_filters(base_query, filters_without_parts)

    response = await db.execute(base_query)
    raw_results = response.all()

    # Use the DRY helper function for part category checking

    # Process results to calculate costs by model and category
    model_data = {}

    for row in raw_results:
        model_id = row.model_type_id
        model_name = row.model_name

        if model_id not in model_data:
            model_data[model_id] = {
                "model_name": model_name,
                "work_orders": set(),
                "structures": set(),
                "service_dates": [],
                "sales_cost": Decimal("0"),
                "pm_cost": Decimal("0"),
                "labor_cost": Decimal("0"),
                "other_cost": Decimal("0"),
                "total_parts_cost": Decimal("0"),
            }

        data = model_data[model_id]
        data["work_orders"].add(row.work_order_id)
        if row.structure_id:
            data["structures"].add(row.structure_id)
        if row.date_service:
            data["service_dates"].append(row.date_service)

        # Check if this part should be included based on part category filters
        part_num = row.part_num or ""
        if not should_include_part_python(part_num, filters.part_categories):
            continue

        # Categorize part costs (now in CAD)
        part_cost = Decimal(str(row.cost_before_tax_cad or 0))

        if part_num.startswith("050-"):
            data["sales_cost"] += part_cost
        elif part_num.startswith("060-"):
            data["pm_cost"] += part_cost
        elif part_num.startswith("070-"):
            data["labor_cost"] += part_cost  # 070- parts are labor costs
        else:
            data["other_cost"] += part_cost

        data["total_parts_cost"] += part_cost

    # Build final results
    cost_by_model_results = []
    for model_id, data in model_data.items():
        unit_count = len(data["structures"])
        total_cost = data["total_parts_cost"] + data["labor_cost"]

        # Calculate time span in years to get yearly average
        if data["service_dates"]:
            min_date = min(data["service_dates"])
            max_date = max(data["service_dates"])
            days_span = (max_date - min_date).days
            years_span = max(
                days_span / 365.25, 1.0
            )  # Minimum 1 year to avoid division issues
        else:
            years_span = 1.0

        # Calculate average yearly cost per unit
        average_cost_per_unit = (
            (total_cost / unit_count / Decimal(str(years_span)))
            if unit_count > 0
            else Decimal("0")
        )

        cost_by_model_results.append(
            CostByModel(
                model_type_id=model_id,
                model_name=data["model_name"],
                total_cost=total_cost,
                work_order_count=len(data["work_orders"]),
                average_cost_per_unit=average_cost_per_unit,
                unit_count=unit_count,
                sales_cost=data["sales_cost"],
                pm_cost=data["pm_cost"],
                labor_cost=data["labor_cost"],
                other_cost=data["other_cost"],
            )
        )

    # Sort by average cost per unit (descending) and limit to top 10
    cost_by_model_results.sort(key=lambda x: x.average_cost_per_unit, reverse=True)
    cost_by_model_results = cost_by_model_results[:10]

    return {"result": cost_by_model_results}


@router.post("/cost-trends")
async def get_cost_trends(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[CostTrend]]:
    """Get monthly cost trends"""
    # Create the month expression once
    month_expr = func.date_trunc("month", WorkOrder.date_service)

    # Create monthly average exchange rate CTE
    monthly_rates = get_monthly_avg_exchange_rate_cte()

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    query = (
        select(
            month_expr.label("month"),  # Use the same expression
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_cost"),
            func.count(WorkOrder.id.distinct()).label("work_order_count"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)  # Add Part join for filtering
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply part category filtering at the part level using helper function
    part_category_conditions = get_part_category_filter_conditions(
        filters.part_categories
    )
    if part_category_conditions:
        query = query.where(or_(*part_category_conditions))

    # Apply other common filters but exclude part categories (we handled them above)
    filters_without_parts = ServiceCostFilters(
        service_dates=filters.service_dates,
        selected_years=filters.selected_years,
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        unit_types=filters.unit_types,
        technicians=filters.technicians,
        part_categories=None,  # Already handled above
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    query = apply_common_filters(query, filters_without_parts)

    # Use the same expression here
    query = query.group_by(month_expr).order_by(month_expr)

    response = await db.execute(query)
    results = response.all()

    # Convert to schema objects
    cost_trends = []
    for result in results:
        # TODO: Split labor and parts costs properly
        labor_cost = result.total_cost / 2
        parts_cost = result.total_cost / 2

        cost_trends.append(
            CostTrend(
                month=result.month.date() if result.month else None,
                total_cost=result.total_cost,
                labor_cost=labor_cost,
                parts_cost=parts_cost,
                work_order_count=result.work_order_count,
            )
        )

    return {"result": cost_trends}


@router.post("/high-cost-units")
async def get_high_cost_units(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[HighCostUnit]]:
    """Get units with highest service costs"""
    # Create monthly average exchange rate CTE
    monthly_rates = get_monthly_avg_exchange_rate_cte()

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    query = (
        select(
            WorkOrderPart.structure_id,
            Structure.structure_str.label("structure_number"),
            PowerUnit.power_unit_str.label("power_unit"),
            ModelType.model.label("model_name"),
            Customer.customer.label("customer_name"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_cost"),
            func.count(WorkOrder.id.distinct()).label("work_order_count"),
            func.max(WorkOrder.date_service).label("last_service_date"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)  # Add Part join for filtering
        .join(Structure, WorkOrderPart.structure_id == Structure.id, isouter=True)
        .join(ModelType, Structure.model_type_id == ModelType.id, isouter=True)
        .join(PowerUnit, Structure.power_unit_id == PowerUnit.id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id.is_not(None),
        )
    )

    # Apply part category filtering at the part level using helper function
    part_category_conditions = get_part_category_filter_conditions(
        filters.part_categories
    )
    if part_category_conditions:
        query = query.where(or_(*part_category_conditions))

    # Apply other common filters but exclude part categories (we handled them above)
    filters_without_parts = ServiceCostFilters(
        service_dates=filters.service_dates,
        selected_years=filters.selected_years,
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        unit_types=filters.unit_types,
        technicians=filters.technicians,
        part_categories=None,  # Already handled above
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    query = apply_common_filters(query, filters_without_parts)

    # Special handling for high-cost units since it joins ModelType directly
    if filters.models is not None and len(filters.models) > 0:
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        if len(non_null_values) > 0:
            query = query.where(ModelType.id.in_(non_null_values))

    query = (
        query.group_by(
            WorkOrderPart.structure_id,
            Structure.structure_str,
            PowerUnit.power_unit_str,
            ModelType.model,
            Customer.customer,
        )
        .order_by(func.sum(WorkOrderPart.cost_before_tax).desc())
        .limit(50)
    )

    response = await db.execute(query)
    results = response.all()

    # Convert to schema objects
    high_cost_units = []
    for result in results:
        avg_cost_per_service = (
            result.total_cost / result.work_order_count
            if result.work_order_count > 0
            else 0
        )

        # TODO: Calculate actual cost trend
        cost_trend = "stable"

        high_cost_units.append(
            HighCostUnit(
                structure_id=result.structure_id,
                structure_number=result.structure_number,
                power_unit=result.power_unit,
                model_name=result.model_name,
                customer_name=result.customer_name,
                total_cost=result.total_cost,
                work_order_count=result.work_order_count,
                avg_cost_per_service=avg_cost_per_service,
                last_service_date=result.last_service_date,
                cost_trend=cost_trend,
            )
        )

    return {"result": high_cost_units}


@router.post("/top-cost-drivers")
async def get_top_cost_drivers(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[CostDriver]]:
    """Get top cost drivers across different categories"""
    # For now, return model-based cost drivers
    # TODO: Expand to include parts, service types, etc.

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Create monthly average exchange rate CTE
    monthly_rates = get_monthly_avg_exchange_rate_cte()

    # Calculate total costs for percentage calculation (with currency conversion)
    total_query = (
        select(
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            )
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply same filters to total query
    total_query = apply_common_filters(total_query, filters)

    total_response = await db.execute(total_query)
    total_costs = total_response.scalar() or Decimal(0)

    # Get model-based cost drivers (with currency conversion)
    model_query = (
        select(
            ModelType.model.label("name"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_cost"),
            func.count(WorkOrder.id.distinct()).label("work_order_count"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(
            work_order_model_type_rel,
            WorkOrder.id == work_order_model_type_rel.c.work_order_id,
        )
        .join(
            ModelType,
            work_order_model_type_rel.c.model_type_id == ModelType.id,
        )
        .where(WorkOrder.status_id.in_(status_subquery))
    )

    # Apply same filters
    model_query = apply_common_filters(model_query, filters)

    model_query = (
        model_query.group_by(ModelType.model)
        .order_by(func.sum(WorkOrderPart.cost_before_tax).desc())
        .limit(5)
    )

    response = await db.execute(model_query)
    results = response.all()

    cost_drivers = []
    for result in results:
        percentage = (
            float(result.total_cost / total_costs * 100) if total_costs > 0 else 0
        )

        cost_drivers.append(
            CostDriver(
                category="model",
                name=result.name,
                total_cost=result.total_cost,
                percentage=percentage,
                work_order_count=result.work_order_count,
            )
        )

    return {"result": cost_drivers}


@router.post("/structure-work-orders")
async def get_structure_work_orders(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[StructureWorkOrder]]:
    """Get all work orders for a specific structure"""

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Create monthly average exchange rate CTE
    monthly_rates = get_monthly_avg_exchange_rate_cte()

    # Calculate labor hours and costs from 070 (Service) parts (with currency conversion)
    labor_subquery = (
        select(
            WorkOrder.id.label("wo_id"),
            func.sum(WorkOrderPart.quantity).label("labor_hours"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("labor_cost"),
        )
        .select_from(WorkOrder)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id == filters.structure_id,
            Part.part_num.like("%070-%"),  # Service labor parts
        )
        .group_by(WorkOrder.id)
    ).alias("labor_data")

    # Calculate non-labor parts costs (with currency conversion)
    parts_subquery = (
        select(
            WorkOrder.id.label("wo_id"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("parts_cost"),
        )
        .select_from(WorkOrder)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id == filters.structure_id,
            ~Part.part_num.like("%070-%"),  # Non-service parts
        )
        .group_by(WorkOrder.id)
    ).alias("parts_data")

    # Get technician names for work orders (can be multiple)
    from shared.models.models_work_order import work_order_user_rel

    technician_subquery = (
        select(
            work_order_user_rel.c.work_order_id.label("wo_id"),
            func.string_agg(User.full_name, ", ").label("technician_names"),
        )
        .select_from(work_order_user_rel)
        .join(User, work_order_user_rel.c.user_id == User.id)
        .group_by(work_order_user_rel.c.work_order_id)
    ).alias("technician_data")

    query = (
        select(
            WorkOrder.id.label("work_order_id"),
            WorkOrder.date_service,
            ServiceType.name.label("service_type"),
            func.coalesce(technician_subquery.c.technician_names, "Unassigned").label(
                "technician_name"
            ),
            func.coalesce(labor_subquery.c.labor_hours, 0).label("labor_hours"),
            func.coalesce(labor_subquery.c.labor_cost, 0).label("labor_cost"),
            func.coalesce(parts_subquery.c.parts_cost, 0).label("parts_cost"),
            (
                func.coalesce(labor_subquery.c.labor_cost, 0)
                + func.coalesce(parts_subquery.c.parts_cost, 0)
            ).label("total_cost"),
            WorkOrderStatus.name.label("status"),
            WorkOrder.work_done.label("work_description"),
        )
        .select_from(WorkOrder)
        .join(
            Customer, WorkOrder.customer_id == Customer.id
        )  # Add Customer join for common filters
        .join(WorkOrderStatus, WorkOrder.status_id == WorkOrderStatus.id, isouter=True)
        .join(ServiceType, WorkOrder.service_type_id == ServiceType.id, isouter=True)
        .join(
            technician_subquery,
            WorkOrder.id == technician_subquery.c.wo_id,
            isouter=True,
        )
        .join(labor_subquery, WorkOrder.id == labor_subquery.c.wo_id, isouter=True)
        .join(parts_subquery, WorkOrder.id == parts_subquery.c.wo_id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            # Only include work orders that have parts for this structure
            WorkOrder.id.in_(
                select(WorkOrderPart.work_order_id).where(
                    WorkOrderPart.structure_id == filters.structure_id
                )
            ),
        )
        .order_by(WorkOrder.date_service.desc())
    )

    # Apply common filters (including date filters and Include IJACK filter)
    query = apply_common_filters(query, filters)

    response = await db.execute(query)
    results = response.all()

    structure_work_orders = []
    for result in results:
        structure_work_orders.append(
            StructureWorkOrder(
                work_order_id=result.work_order_id,
                date_service=result.date_service,
                service_type=result.service_type,
                technician_name=result.technician_name,
                labor_hours=float(result.labor_hours or 0),
                labor_cost=result.labor_cost or Decimal(0),
                parts_cost=result.parts_cost or Decimal(0),
                total_cost=result.total_cost or Decimal(0),
                status=result.status,
                work_description=result.work_description,
            )
        )

    return {"result": structure_work_orders}


@router.post("/work-order-parts")
async def get_work_order_parts(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[WorkOrderPartDetail]]:
    """Get all parts and costs for a specific work order"""

    # Create monthly average exchange rate CTE
    monthly_rates = get_monthly_avg_exchange_rate_cte()

    query = (
        select(
            WorkOrderPart.id.label("part_id"),
            Part.part_name,
            Part.description,
            WorkOrderPart.quantity,
            WorkOrderPart.price,
            get_currency_conversion_expression(
                WorkOrderPart.cost_before_tax,
                WorkOrder.date_service,
                WorkOrder.currency_id,
                monthly_rates,
            ).label("total_cost_cad"),
            Warehouse.name.label("warehouse"),
            case(
                (Part.part_num.like("%050-%"), "050 (Sales)"),
                (Part.part_num.like("%060-%"), "060 (PM Parts)"),
                (Part.part_num.like("%070-%"), "070 (Service)"),
                (Part.part_num == "0", "0"),
                else_="Other",
            ).label("category"),
        )
        .select_from(WorkOrderPart)
        .join(WorkOrder, WorkOrderPart.work_order_id == WorkOrder.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(Part, WorkOrderPart.part_id == Part.id, isouter=True)
        .join(Warehouse, WorkOrderPart.warehouse_id == Warehouse.id, isouter=True)
        .where(WorkOrderPart.work_order_id == filters.work_order_id)
        .order_by(
            get_currency_conversion_expression(
                WorkOrderPart.cost_before_tax,
                WorkOrder.date_service,
                WorkOrder.currency_id,
                monthly_rates,
            ).desc()
        )
    )

    response = await db.execute(query)
    results = response.all()

    work_order_parts = []
    for result in results:
        work_order_parts.append(
            WorkOrderPartDetail(
                part_id=result.part_id,
                part_name=result.part_name,
                description=result.description,
                quantity=result.quantity or 0,
                price=result.price or Decimal(0),
                total_cost=result.total_cost_cad or Decimal(0),
                warehouse=result.warehouse,
                category=result.category,
            )
        )

    return {"result": work_order_parts}


@router.post("/available-years")
async def get_available_years(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[AvailableYears]:
    """Get all available years with work order data, respecting filters except date filters"""

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Build query to get distinct years (add Currency join for consistency)
    query = (
        select(func.extract("year", WorkOrder.date_service).distinct().label("year"))
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrder.date_service.is_not(None),
        )
    )

    # Apply part category filtering at the part level using helper function
    part_category_conditions = get_part_category_filter_conditions(
        filters.part_categories
    )
    if part_category_conditions:
        query = query.where(or_(*part_category_conditions))

    # Apply other common filters but exclude date and year filters (we want all years)
    filters_without_dates = ServiceCostFilters(
        service_dates=None,  # Don't filter by dates - we want all years
        selected_years=None,  # Don't filter by years - we want all years
        customers=filters.customers,
        service_types=filters.service_types,
        models=filters.models,
        unit_types=filters.unit_types,
        technicians=filters.technicians,
        part_categories=None,  # Already handled above
        structure_id=filters.structure_id,
        work_order_id=filters.work_order_id,
        include_ijack=filters.include_ijack,
        include_sales_parts=filters.include_sales_parts,
    )
    query = apply_common_filters(query, filters_without_dates)

    # Order by year descending
    query = query.order_by(func.extract("year", WorkOrder.date_service).desc())

    response = await db.execute(query)
    results = response.all()

    # Extract years and convert to integers
    years = [int(result.year) for result in results if result.year is not None]

    return {"result": AvailableYears(years=years)}


# Phase 1 Enhanced Analytics Endpoints


@router.post("/cost-efficiency-metrics")
async def get_cost_efficiency_metrics(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[CostEfficiencyMetrics]:
    """Get cost efficiency KPI metrics"""
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    monthly_rates = get_monthly_avg_exchange_rate_cte()

    # Calculate total service hours from 070 parts (service labor)
    labor_query = (
        select(
            func.sum(WorkOrderPart.quantity).label("total_service_hours"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_labor_costs"),
            func.count(WorkOrder.id.distinct()).label("total_services"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            Part.part_num.like("%070-%"),  # Service labor parts
        )
    )

    labor_query = apply_common_filters(labor_query, filters)
    labor_result = await db.execute(labor_query)
    labor_data = labor_result.first()

    # Calculate parts costs and emergency procurement impact
    parts_query = (
        select(
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_parts_costs"),
            func.avg(
                case(
                    (Part.unit_cost > 0, WorkOrderPart.price / Part.unit_cost),
                    else_=1.0,
                )
            ).label("avg_markup_ratio"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            ~Part.part_num.like("%070-%"),  # Non-labor parts
        )
    )

    parts_query = apply_common_filters(parts_query, filters)
    parts_result = await db.execute(parts_query)
    parts_data = parts_result.first()

    # Calculate metrics with safe defaults
    total_hours = float(labor_data.total_service_hours or 0)
    total_labor_costs = labor_data.total_labor_costs or Decimal(0)
    total_services = labor_data.total_services or 1
    total_parts_costs = parts_data.total_parts_costs or Decimal(0)
    avg_markup = float(parts_data.avg_markup_ratio or 1.0)

    # Calculate derived metrics
    billable_hours_ratio = 0.85  # Placeholder - would calculate from ServiceClock data
    cost_per_billable_hour = total_labor_costs / Decimal(
        str(max(total_hours * billable_hours_ratio, 1))
    )
    avg_service_duration = total_hours / max(total_services, 1)
    emergency_service_rate = 0.15  # Placeholder - would calculate from service types
    emergency_parts_impact = total_parts_costs * Decimal(
        "0.20"
    )  # Estimated 20% premium

    metrics = CostEfficiencyMetrics(
        total_service_hours=total_hours,
        billable_hours_ratio=billable_hours_ratio,
        cost_per_billable_hour=cost_per_billable_hour,
        avg_service_duration=avg_service_duration,
        emergency_service_rate=emergency_service_rate,
        parts_markup_ratio=avg_markup,
        emergency_parts_cost_impact=emergency_parts_impact,
    )

    return {"result": metrics}


@router.post("/service-quality-metrics")
async def get_service_quality_metrics(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[ServiceQualityMetrics]:
    """Get service quality indicators"""
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Calculate repeat service rate using CTE to separate window functions from aggregates
    # Apply filters within the CTE to avoid Cartesian products later
    services_base_query = (
        select(
            WorkOrder.id.label("work_order_id"),
            WorkOrder.date_service,
            WorkOrder.is_warranty,
            WorkOrderPart.structure_id,
            func.lag(WorkOrder.date_service)
            .over(
                partition_by=WorkOrderPart.structure_id, order_by=WorkOrder.date_service
            )
            .label("prev_service_date"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id.is_not(None),
        )
    )

    # Apply common filters to the base query before creating CTE
    services_base_query = apply_common_filters(services_base_query, filters)
    services_with_lag_cte = services_base_query.cte("services_with_lag")

    repeat_services_query = select(
        func.count().label("total_services"),
        func.sum(
            case(
                (
                    (services_with_lag_cte.c.prev_service_date.is_not(None))
                    & (
                        services_with_lag_cte.c.date_service
                        - services_with_lag_cte.c.prev_service_date
                        <= 30
                    ),
                    1,
                ),
                else_=0,
            )
        ).label("repeat_services"),
        func.avg(
            services_with_lag_cte.c.date_service
            - services_with_lag_cte.c.prev_service_date
        ).label("avg_days_between_services"),
        func.sum(case((services_with_lag_cte.c.is_warranty == True, 1), else_=0)).label(  # noqa: E712
            "warranty_services"
        ),
    ).select_from(services_with_lag_cte)

    # Don't apply common filters to CTE-based queries to avoid Cartesian products
    # The filtering was already applied when creating the services_with_lag_cte
    repeat_result = await db.execute(repeat_services_query)
    repeat_data = repeat_result.first()

    # Calculate service frequency analysis - simplified to avoid PostgreSQL type casting issues
    frequency_base_query = (
        select(
            WorkOrderPart.structure_id,
            func.count().label("service_count"),
            func.max(WorkOrder.date_service).label("max_date"),
            func.min(WorkOrder.date_service).label("min_date"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id.is_not(None),
        )
        .group_by(WorkOrderPart.structure_id)
        .having(func.count() >= 2)  # Need at least 2 services to calculate interval
    )

    # Apply common filters to the base query before creating CTE
    frequency_base_query = apply_common_filters(frequency_base_query, filters)
    frequency_cte = frequency_base_query.cte("frequency_base")

    # Don't apply common filters to CTE-based frequency query to avoid Cartesian products
    # The filtering was already applied when creating the frequency_cte
    frequency_query = select(
        frequency_cte.c.structure_id,
        frequency_cte.c.service_count,
        frequency_cte.c.max_date,
        frequency_cte.c.min_date,
    ).select_from(frequency_cte)

    frequency_result = await db.execute(frequency_query)
    frequency_data = frequency_result.all()

    # Calculate metrics with safe defaults
    total_services = repeat_data.total_services or 1
    repeat_services = repeat_data.repeat_services or 0
    warranty_services = repeat_data.warranty_services or 0
    avg_days_between = float(repeat_data.avg_days_between_services or 90)

    repeat_rate = (repeat_services / total_services) * 100
    warranty_rate = (warranty_services / total_services) * 100

    # Calculate service intervals in Python to avoid PostgreSQL type casting issues
    service_intervals = []
    for row in frequency_data:
        if row.max_date and row.min_date and row.service_count > 1:
            days_span = (row.max_date - row.min_date).days
            avg_interval = (
                days_span / row.service_count if row.service_count > 0 else 90
            )
            service_intervals.append(avg_interval)

    avg_interval = statistics.mean(service_intervals) if service_intervals else 90

    # Industry benchmark assumption: 90 days average
    industry_benchmark = 90.0
    frequency_score = avg_interval / industry_benchmark

    # Count units with unusual service patterns
    units_over_serviced = len(
        [i for i in service_intervals if i < industry_benchmark * 0.5]
    )
    units_under_serviced = len(
        [i for i in service_intervals if i > industry_benchmark * 2.0]
    )

    metrics = ServiceQualityMetrics(
        repeat_service_rate=repeat_rate,
        avg_days_between_services=avg_days_between,
        warranty_work_rate=warranty_rate,
        service_frequency_score=frequency_score,
        units_over_serviced=units_over_serviced,
        units_under_serviced=units_under_serviced,
    )

    return {"result": metrics}


@router.post("/basic-outlier-detection")
async def get_basic_outlier_detection(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[BasicOutlierDetection]]:
    """Get basic outlier detection results using simplified approach"""
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    monthly_rates = get_monthly_avg_exchange_rate_cte()

    # Simplified approach: Get basic aggregated data per structure first
    unit_stats_query = (
        select(
            WorkOrderPart.structure_id,
            Structure.structure_str,
            Customer.customer.label("customer_name"),
            ModelType.model.label("model_name"),
            func.count().label("service_count"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_cost"),
            func.avg(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("avg_cost_per_service"),
            func.min(WorkOrder.date_service).label("first_service"),
            func.max(WorkOrder.date_service).label("last_service"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Structure, WorkOrderPart.structure_id == Structure.id, isouter=True)
        .join(ModelType, Structure.model_type_id == ModelType.id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id.is_not(None),
        )
        .group_by(
            WorkOrderPart.structure_id,
            Structure.structure_str,
            Customer.customer,
            ModelType.model,
        )
        .having(func.count() >= 3)  # Minimum service history for meaningful analysis
    )

    unit_stats_query = apply_common_filters(unit_stats_query, filters)

    # Execute the query and process results in Python
    unit_result = await db.execute(unit_stats_query)
    unit_data = unit_result.all()

    # Calculate metrics in Python to avoid complex SQL type casting
    unit_metrics = []
    for unit in unit_data:
        service_count = unit.service_count
        avg_cost = float(unit.avg_cost_per_service or 0)
        first_service = unit.first_service
        last_service = unit.last_service

        # Calculate service frequency (services per month) in Python
        if first_service and last_service and service_count > 1:
            days_span = (last_service - first_service).days
            months_span = max(days_span / 30.44, 1.0)  # Avoid division by zero
            services_per_month = service_count / months_span
        else:
            services_per_month = 1.0  # Default for single service

        unit_metrics.append(
            {
                "structure_id": unit.structure_id,
                "structure_str": unit.structure_str,
                "customer_name": unit.customer_name,
                "model_name": unit.model_name,
                "service_count": service_count,
                "avg_cost_per_service": avg_cost,
                "services_per_month": services_per_month,
                "total_cost": float(unit.total_cost or 0),
            }
        )

    if not unit_metrics:
        return {"result": []}

    # Calculate global statistics in Python
    costs = [m["avg_cost_per_service"] for m in unit_metrics]
    frequencies = [m["services_per_month"] for m in unit_metrics]

    global_avg_cost = statistics.mean(costs) if costs else 0
    global_stddev_cost = statistics.stdev(costs) if len(costs) > 1 else 1
    global_avg_freq = statistics.mean(frequencies) if frequencies else 0
    global_stddev_freq = statistics.stdev(frequencies) if len(frequencies) > 1 else 1

    outliers = []

    for unit in unit_metrics:
        cost_per_service = unit["avg_cost_per_service"]
        frequency = unit["services_per_month"]

        # Calculate z-scores
        cost_z_score = (
            (cost_per_service - global_avg_cost) / global_stddev_cost
            if global_stddev_cost > 0
            else 0
        )
        freq_z_score = (
            (frequency - global_avg_freq) / global_stddev_freq
            if global_stddev_freq > 0
            else 0
        )

        # Determine if unit is an outlier (>2 standard deviations)
        if abs(cost_z_score) > 2 or abs(freq_z_score) > 2:
            # Determine outlier type and severity
            if abs(cost_z_score) > abs(freq_z_score):
                outlier_type = "cost"
                z_score = cost_z_score
            else:
                outlier_type = "frequency"
                z_score = freq_z_score

            severity = (
                "critical"
                if abs(z_score) > 3
                else "high"
                if abs(z_score) > 2.5
                else "medium"
            )

            # Calculate annual excess cost
            annual_excess = Decimal(
                str(max(0, (cost_per_service - global_avg_cost) * frequency * 12))
            )

            # Generate recommendation
            if outlier_type == "cost" and z_score > 0:
                recommendation = "Review high service costs - check for recurring issues or inefficient repairs"
            elif outlier_type == "frequency" and z_score > 0:
                recommendation = "Unit serviced too frequently - investigate root causes or optimize intervals"
            else:
                recommendation = "Monitor for potential under-maintenance or efficiency opportunities"

            outlier = BasicOutlierDetection(
                structure_id=unit["structure_id"],
                structure_number=unit["structure_str"],
                customer_name=unit["customer_name"],
                model_name=unit["model_name"],
                outlier_type=outlier_type,
                severity=severity,
                z_score=z_score,
                current_cost_per_service=Decimal(str(cost_per_service)),
                current_service_frequency=frequency,
                benchmark_cost_per_service=Decimal(str(global_avg_cost)),
                benchmark_service_frequency=global_avg_freq,
                annual_excess_cost=annual_excess,
                recommendation=recommendation,
            )
            outliers.append(outlier)

    # Sort by severity and z-score
    severity_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
    outliers.sort(
        key=lambda x: (severity_order.get(x.severity, 0), abs(x.z_score)), reverse=True
    )

    return {"result": outliers[:20]}  # Return top 20 outliers


@router.post("/technician-efficiency-metrics")
async def get_technician_efficiency_metrics(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[TechnicianEfficiencyMetrics]]:
    """Get technician performance and efficiency metrics"""
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Create CTE for technician services with lag to avoid window functions in aggregates
    # Apply filters to the base query before creating CTE to avoid Cartesian products
    technician_services_base = (
        select(
            WorkOrder.id.label("work_order_id"),
            WorkOrder.date_service,
            WorkOrder.currency_id,
            WorkOrderPart.cost_before_tax,
            WorkOrderPart.quantity,
            WorkOrderPart.structure_id,
            User.id.label("technician_id"),
            User.full_name.label("technician_name"),
            Part.part_num,
            func.lag(WorkOrder.date_service)
            .over(
                partition_by=[User.id, WorkOrderPart.structure_id],
                order_by=WorkOrder.date_service,
            )
            .label("prev_service_date"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(Part, WorkOrderPart.part_id == Part.id)
        .join(work_order_user_rel, WorkOrder.id == work_order_user_rel.c.work_order_id)
        .join(User, work_order_user_rel.c.user_id == User.id)
        .where(WorkOrder.status_id.in_(status_subquery), User.id.is_not(None))
    )

    # Apply common filters to the base query before creating CTE
    technician_services_base = apply_common_filters(technician_services_base, filters)
    technician_services_cte = technician_services_base.cte("technician_services")

    # Calculate technician statistics from work order assignments
    # Note: Using simplified cost aggregation without currency conversion to avoid Cartesian products
    # The CTE already includes costs from base query with proper currency joins
    # For precise currency conversion in the future, consider moving currency logic to Python post-processing
    technician_query = (
        select(
            technician_services_cte.c.technician_id,
            technician_services_cte.c.technician_name,
            func.count(technician_services_cte.c.work_order_id.distinct()).label(
                "total_services"
            ),
            # Use simple cost aggregation - currency conversion will be handled in Python if needed
            func.sum(technician_services_cte.c.cost_before_tax).label("total_cost"),
            func.sum(
                case(
                    (
                        technician_services_cte.c.part_num.like("%070-%"),
                        technician_services_cte.c.quantity,
                    ),
                    else_=0,
                )
            ).label("total_billable_hours"),
            func.avg(
                case(
                    (
                        technician_services_cte.c.part_num.like("%070-%"),
                        technician_services_cte.c.quantity,
                    ),
                    else_=0,
                )
            ).label("avg_service_duration"),
            # Count repeat services (simplified - services to same structure within 30 days)
            func.sum(
                case(
                    (
                        (technician_services_cte.c.prev_service_date.is_not(None))
                        & (
                            technician_services_cte.c.date_service
                            - technician_services_cte.c.prev_service_date
                            <= 30
                        ),
                        1,
                    ),
                    else_=0,
                )
            ).label("repeat_services"),
        )
        .select_from(technician_services_cte)
        .group_by(
            technician_services_cte.c.technician_id,
            technician_services_cte.c.technician_name,
        )
        .having(
            func.count(technician_services_cte.c.work_order_id.distinct()) >= 5
        )  # Minimum 5 services for meaningful analysis
    )

    # Don't apply common filters to CTE-based technician query to avoid Cartesian products
    # The filtering was already applied when creating the technician_services_cte
    result = await db.execute(technician_query)
    technician_data = result.all()

    # Calculate global averages for benchmarking
    if technician_data:
        total_services_all = sum(tech.total_services for tech in technician_data)
        total_cost_all = sum(float(tech.total_cost or 0) for tech in technician_data)
        total_hours_all = sum(
            float(tech.total_billable_hours or 0) for tech in technician_data
        )

        global_avg_cost_per_service = (
            total_cost_all / total_services_all if total_services_all > 0 else 0
        )
        global_avg_hours_per_service = (
            total_hours_all / total_services_all if total_services_all > 0 else 0
        )
    else:
        global_avg_cost_per_service = 0
        global_avg_hours_per_service = 0  # noqa: F841

    technician_metrics = []

    for idx, tech in enumerate(technician_data, 1):
        total_services = tech.total_services or 1
        total_cost = float(tech.total_cost or 0)
        total_hours = float(tech.total_billable_hours or 0)
        avg_duration = float(tech.avg_service_duration or 0)
        repeat_services = tech.repeat_services or 0

        # Calculate metrics
        cost_per_service = total_cost / total_services if total_services > 0 else 0
        labor_efficiency_ratio = (
            0.85  # Placeholder - would calculate from ServiceClock data
        )
        services_per_day = total_services / 30.0  # Assuming 30-day period
        repeat_rate = (
            (repeat_services / total_services) * 100 if total_services > 0 else 0
        )

        # Benchmarking
        vs_team_average = (
            cost_per_service / global_avg_cost_per_service
            if global_avg_cost_per_service > 0
            else 1.0
        )

        metric = TechnicianEfficiencyMetrics(
            technician_id=tech.technician_id,
            technician_name=tech.technician_name,
            labor_efficiency_ratio=labor_efficiency_ratio,
            avg_service_duration=avg_duration,
            services_per_day=services_per_day,
            repeat_service_rate=repeat_rate,
            cost_per_service=Decimal(str(cost_per_service)),
            total_services=total_services,
            total_billable_hours=total_hours,
            efficiency_rank=idx,  # Simplified ranking
            vs_team_average=vs_team_average,
        )
        technician_metrics.append(metric)

    # Sort by cost efficiency (lower cost per service is better)
    technician_metrics.sort(key=lambda x: float(x.cost_per_service))

    # Update rankings after sorting
    for idx, metric in enumerate(technician_metrics, 1):
        metric.efficiency_rank = idx

    return {"result": technician_metrics}


@router.post("/customer-profitability-metrics")
async def get_customer_profitability_metrics(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[CustomerProfitabilityMetrics]]:
    """Get customer profitability analysis metrics"""
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Create CTE for customer services with date ranges to avoid window functions in aggregates
    # Apply filters to the base query before creating CTE to avoid Cartesian products
    customer_services_base = (
        select(
            Customer.id.label("customer_id"),
            Customer.customer.label("customer_name"),
            WorkOrder.id.label("work_order_id"),
            WorkOrder.total,
            WorkOrder.date_service,
            WorkOrder.currency_id,
            WorkOrderPart.cost_before_tax,
            WorkOrderPart.structure_id,
            func.count()
            .over(partition_by=WorkOrderPart.structure_id)
            .label("services_per_unit"),
            func.max(WorkOrder.date_service)
            .over(partition_by=WorkOrderPart.structure_id)
            .label("max_service_date"),
            func.min(WorkOrder.date_service)
            .over(partition_by=WorkOrderPart.structure_id)
            .label("min_service_date"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id.is_not(None),
            Customer.id.not_in(IJACK_CUST_IDS_LIST),
            WorkOrder.id.not_in(
                select(WorkOrderPart.work_order_id.distinct())
                .select_from(WorkOrderPart)
                .join(Part, WorkOrderPart.part_id == Part.id)
                .where(Part.part_num.like("050-%"))
            ),
        )
    )

    # Apply common filters to the base query before creating CTE
    customer_services_base = apply_common_filters(customer_services_base, filters)
    customer_services_cte = customer_services_base.cte("customer_services")

    # Calculate customer profitability metrics
    # Note: Using simplified cost aggregation without currency conversion to avoid Cartesian products
    # The CTE already includes costs from base query with proper currency joins
    # For precise currency conversion in the future, consider moving currency logic to Python post-processing
    customer_query = (
        select(
            customer_services_cte.c.customer_id,
            customer_services_cte.c.customer_name,
            # Revenue calculation (total service amounts) - simplified
            func.sum(customer_services_cte.c.total).label("total_revenue"),
            # Service costs (parts + labor) - simplified
            func.sum(customer_services_cte.c.cost_before_tax).label(
                "total_service_costs"
            ),
            # Volume metrics
            func.count(customer_services_cte.c.work_order_id.distinct()).label(
                "total_services"
            ),
            func.count(customer_services_cte.c.structure_id.distinct()).label(
                "total_units"
            ),
            # Service frequency calculation - simplified to avoid complex nesting
            # We'll calculate this in Python code instead of SQL to avoid nesting issues
            func.avg(customer_services_cte.c.services_per_unit).label(
                "avg_services_per_structure"
            ),
        )
        .select_from(customer_services_cte)
        .group_by(
            customer_services_cte.c.customer_id, customer_services_cte.c.customer_name
        )
        .having(
            func.count(customer_services_cte.c.work_order_id.distinct()) >= 3
        )  # Minimum 3 services for analysis
    )

    # Don't apply common filters to CTE-based customer query to avoid Cartesian products
    # The filtering was already applied when creating the customer_services_cte
    result = await db.execute(customer_query)
    customer_data = result.all()

    customer_metrics = []

    # Industry benchmark for service frequency (services per month per unit)
    industry_benchmark_frequency = 0.33  # Approximately once every 3 months

    for customer in customer_data:
        total_revenue = float(customer.total_revenue or 0)
        total_costs = float(customer.total_service_costs or 0)
        total_services = customer.total_services or 1
        total_units = customer.total_units or 1
        avg_services_per_structure = float(customer.avg_services_per_structure or 0)
        # Calculate frequency manually - simplified approach
        avg_frequency = avg_services_per_structure / 12.0  # Rough monthly frequency

        # Calculate profitability metrics
        gross_margin = total_revenue - total_costs
        margin_percentage = (
            (gross_margin / total_revenue * 100) if total_revenue > 0 else 0
        )
        avg_cost_per_unit = total_costs / total_units if total_units > 0 else 0
        avg_services_per_unit = total_services / total_units if total_units > 0 else 0
        frequency_vs_benchmark = (
            avg_frequency / industry_benchmark_frequency
            if industry_benchmark_frequency > 0
            else 1.0
        )

        metric = CustomerProfitabilityMetrics(
            customer_id=customer.customer_id,
            customer_name=customer.customer_name,
            total_revenue=Decimal(str(total_revenue)),
            total_service_costs=Decimal(str(total_costs)),
            gross_margin=Decimal(str(gross_margin)),
            margin_percentage=margin_percentage,
            avg_cost_per_unit=Decimal(str(avg_cost_per_unit)),
            service_frequency_vs_benchmark=frequency_vs_benchmark,
            total_units=total_units,
            total_services=total_services,
            avg_services_per_unit=avg_services_per_unit,
            profitability_rank=0,  # Will be set after sorting
        )
        customer_metrics.append(metric)

    # Sort by profitability (highest margin first)
    customer_metrics.sort(key=lambda x: float(x.gross_margin), reverse=True)

    # Update rankings after sorting
    for idx, metric in enumerate(customer_metrics, 1):
        metric.profitability_rank = idx

    return {"result": customer_metrics}


# Phase 2 Enhanced Analytics Endpoints - Unit Deep-Dive Analysis


@router.post("/unit-deep-dive-analysis")
async def get_unit_deep_dive_analysis(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[Optional[UnitDeepDiveAnalysis]]:
    """Get comprehensive deep-dive analysis for a specific unit"""
    if not filters.structure_id:
        return {"result": None}

    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    monthly_rates = get_monthly_avg_exchange_rate_cte()

    # Get basic unit information and cost summary
    unit_info_query = (
        select(
            Structure.id.label("structure_id"),
            Structure.structure_str.label("structure_number"),
            Customer.customer.label("customer_name"),
            ModelType.model.label("model_name"),
            PowerUnit.power_unit_str.label("power_unit"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_cost"),
            func.count(WorkOrder.id.distinct()).label("total_services"),
            func.min(WorkOrder.date_service).label("first_service"),
            func.max(WorkOrder.date_service).label("last_service"),
            func.avg(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("avg_cost_per_service"),
        )
        .select_from(WorkOrderPart)
        .join(WorkOrder, WorkOrderPart.work_order_id == WorkOrder.id)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(Structure, WorkOrderPart.structure_id == Structure.id)
        .join(ModelType, Structure.model_type_id == ModelType.id, isouter=True)
        .join(PowerUnit, Structure.power_unit_id == PowerUnit.id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id == filters.structure_id,
        )
        .group_by(
            Structure.id,
            Structure.structure_str,
            Customer.customer,
            ModelType.model,
            PowerUnit.power_unit_str,
        )
    )

    unit_result = await db.execute(unit_info_query)
    unit_data = unit_result.first()

    if not unit_data:
        return {"result": None}

    # Get cost trends over time (monthly aggregation)
    month_trunc = func.date_trunc("month", WorkOrder.date_service)
    cost_trend_query = (
        select(
            month_trunc.label("month"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("monthly_cost"),
        )
        .select_from(WorkOrderPart)
        .join(WorkOrder, WorkOrderPart.work_order_id == WorkOrder.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (monthly_rates.c.month == month_trunc),
            isouter=True,
        )
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id == filters.structure_id,
        )
        .group_by(month_trunc)
        .order_by(month_trunc)
    )

    trend_result = await db.execute(cost_trend_query)
    trend_data = trend_result.all()

    # Get cost drivers (top parts, service types, technicians)
    cost_drivers_query = (
        select(
            Part.part_num.label("driver_name"),
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).label("total_cost"),
            func.count().label("service_count"),
        )
        .select_from(WorkOrderPart)
        .join(WorkOrder, WorkOrderPart.work_order_id == WorkOrder.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(Part, WorkOrderPart.part_id == Part.id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id == filters.structure_id,
        )
        .group_by(Part.part_num)
        .order_by(
            func.sum(
                get_currency_conversion_expression(
                    WorkOrderPart.cost_before_tax,
                    WorkOrder.date_service,
                    WorkOrder.currency_id,
                    monthly_rates,
                )
            ).desc()
        )
        .limit(5)
    )

    drivers_result = await db.execute(cost_drivers_query)
    drivers_data = drivers_result.all()

    # Get service patterns
    service_intervals_query = (
        select(
            WorkOrder.date_service,
            ServiceType.name.label("service_type"),
            WorkOrder.is_warranty,
            func.lag(WorkOrder.date_service)
            .over(order_by=WorkOrder.date_service)
            .label("prev_service_date"),
        )
        .select_from(WorkOrderPart)
        .join(WorkOrder, WorkOrderPart.work_order_id == WorkOrder.id)
        .join(ServiceType, WorkOrder.service_type_id == ServiceType.id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id == filters.structure_id,
        )
        .order_by(WorkOrder.date_service)
    )

    intervals_result = await db.execute(service_intervals_query)
    intervals_data = intervals_result.all()

    # Calculate service patterns in Python
    if intervals_data:
        service_types = [row.service_type for row in intervals_data if row.service_type]
        intervals_days = []

        for i, row in enumerate(intervals_data):
            if row.prev_service_date and row.date_service:
                interval = (row.date_service - row.prev_service_date).days
                intervals_days.append(interval)

        avg_interval = statistics.mean(intervals_days) if intervals_days else 90
        frequency_per_month = 30.44 / avg_interval if avg_interval > 0 else 0

        # Count repeat services (within 30 days)
        repeat_services = len([i for i in intervals_days if i <= 30])
        repeat_rate = (
            (repeat_services / len(intervals_data)) * 100 if intervals_data else 0
        )

        # Most common service types
        from collections import Counter

        service_type_counts = Counter(service_types)
        common_service_types = [
            stype for stype, count in service_type_counts.most_common(3)
        ]
    else:
        avg_interval = 90
        frequency_per_month = 0
        repeat_rate = 0
        common_service_types = []

    # Build trend data points
    cost_trend_points = []
    for trend in trend_data:
        if trend.month:
            cost_trend_points.append(
                TrendDataPoint(
                    date=trend.month.date(),
                    value=trend.monthly_cost or Decimal(0),
                    category="monthly_cost",
                )
            )

    # Build cost drivers breakdown
    total_unit_cost = float(unit_data.total_cost or 0)
    cost_drivers_breakdown = []
    for driver in drivers_data:
        driver_cost = float(driver.total_cost or 0)
        percentage = (driver_cost / total_unit_cost * 100) if total_unit_cost > 0 else 0
        cost_drivers_breakdown.append(
            CostDriverBreakdown(
                driver_type="parts",
                driver_name=driver.driver_name or "Unknown",
                total_cost=Decimal(str(driver_cost)),
                percentage_of_total=percentage,
                service_count=driver.service_count or 0,
            )
        )

    # Calculate parts vs labor ratio (simplified - assuming 070 parts are labor)
    labor_cost = sum(
        float(d.total_cost)
        for d in drivers_data
        if d.driver_name and d.driver_name.startswith("070")
    )
    parts_cost = total_unit_cost - labor_cost
    parts_vs_labor_ratio = parts_cost / labor_cost if labor_cost > 0 else 1.0

    # Calculate time span for monthly cost
    first_service = unit_data.first_service
    last_service = unit_data.last_service
    if first_service and last_service:
        days_span = (last_service - first_service).days
        months_span = max(days_span / 30.44, 1)
        cost_per_month = total_unit_cost / months_span
        analysis_start = first_service
        analysis_end = last_service
    else:
        cost_per_month = 0
        analysis_start = last_service or date.today()
        analysis_end = date.today()

    # Build service patterns
    service_patterns = ServicePatternAnalysis(
        avg_service_interval=avg_interval,
        service_frequency_per_month=frequency_per_month,
        seasonal_pattern_detected=False,  # Simplified for now
        most_common_service_types=common_service_types,
        repeat_service_rate=repeat_rate,
    )

    # Build failure analysis (simplified)
    warranty_services = len([row for row in intervals_data if row.is_warranty])
    warranty_rate = (
        (warranty_services / len(intervals_data)) * 100 if intervals_data else 0
    )

    failure_analysis = FailureAnalysis(
        common_failure_parts=[
            {"part_num": d.driver_name, "frequency": d.service_count}
            for d in drivers_data[:3]
            if d.driver_name
        ],
        failure_modes=[
            "High wear parts replacement",
            "Preventive maintenance",
            "Emergency repair",
        ],
        mtbf_days=avg_interval if avg_interval > 0 else None,
        warranty_failure_rate=warranty_rate,
    )

    # Build benchmark comparison (simplified - would need peer data for accurate comparison)
    benchmark_comparison = BenchmarkComparison(
        vs_model_type_average=1.0,  # Placeholder
        vs_customer_fleet_average=1.0,  # Placeholder
        vs_top_performers=1.2,  # Placeholder - indicating above average cost
        model_type_rank=0,  # Would calculate from peer data
        customer_fleet_rank=0,  # Would calculate from customer's other units
    )

    # Build the complete analysis
    analysis = UnitDeepDiveAnalysis(
        structure_id=unit_data.structure_id,
        structure_number=unit_data.structure_number,
        customer_name=unit_data.customer_name,
        model_name=unit_data.model_name,
        power_unit=unit_data.power_unit,
        total_lifetime_cost=Decimal(str(total_unit_cost)),
        cost_per_month=Decimal(str(cost_per_month)),
        cost_trend_data=cost_trend_points,
        parts_vs_labor_ratio=parts_vs_labor_ratio,
        top_cost_drivers=cost_drivers_breakdown,
        service_patterns=service_patterns,
        failure_analysis=failure_analysis,
        benchmark_comparison=benchmark_comparison,
        analysis_period_start=analysis_start,
        analysis_period_end=analysis_end,
        total_services=unit_data.total_services or 0,
        first_service_date=unit_data.first_service,
        last_service_date=unit_data.last_service,
    )

    return {"result": analysis}


@router.post("/service-interval-optimization")
async def get_service_interval_optimization(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[ServiceIntervalOptimization]]:
    """Get service interval optimization recommendations for units"""
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    monthly_rates = get_monthly_avg_exchange_rate_cte()

    # Get units with service interval analysis
    intervals_base_query = (
        select(
            WorkOrderPart.structure_id,
            Structure.structure_str,
            Customer.customer.label("customer_name"),
            ModelType.model.label("model_name"),
            WorkOrder.date_service,
            get_currency_conversion_expression(
                WorkOrderPart.cost_before_tax,
                WorkOrder.date_service,
                WorkOrder.currency_id,
                monthly_rates,
            ).label("cost_cad"),
            func.lag(WorkOrder.date_service)
            .over(
                partition_by=WorkOrderPart.structure_id,
                order_by=WorkOrder.date_service,
            )
            .label("prev_service_date"),
        )
        .select_from(WorkOrderPart)
        .join(WorkOrder, WorkOrderPart.work_order_id == WorkOrder.id)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(Currency, WorkOrder.currency_id == Currency.id)
        .join(
            monthly_rates,
            (monthly_rates.c.currency_id == WorkOrder.currency_id)
            & (
                monthly_rates.c.month
                == func.date_trunc("month", WorkOrder.date_service)
            ),
            isouter=True,
        )
        .join(Structure, WorkOrderPart.structure_id == Structure.id)
        .join(ModelType, Structure.model_type_id == ModelType.id, isouter=True)
        .where(
            WorkOrder.status_id.in_(status_subquery),
            WorkOrderPart.structure_id.is_not(None),
        )
        .order_by(WorkOrderPart.structure_id, WorkOrder.date_service)
    )

    intervals_base_query = apply_common_filters(intervals_base_query, filters)

    result = await db.execute(intervals_base_query)
    raw_data = result.all()

    # Process in Python to calculate intervals and optimization
    unit_data = {}
    for row in raw_data:
        structure_id = row.structure_id

        if structure_id not in unit_data:
            unit_data[structure_id] = {
                "structure_str": row.structure_str,
                "customer_name": row.customer_name,
                "model_name": row.model_name,
                "services": [],
                "intervals": [],
                "costs": [],
            }

        unit_data[structure_id]["services"].append(row.date_service)
        unit_data[structure_id]["costs"].append(float(row.cost_cad or 0))

        if row.prev_service_date and row.date_service:
            interval = (row.date_service - row.prev_service_date).days
            unit_data[structure_id]["intervals"].append(interval)

    # Generate optimization recommendations
    optimization_results = []

    for structure_id, data in unit_data.items():
        if len(data["intervals"]) < 3:  # Need minimum service history
            continue

        intervals = data["intervals"]
        costs = data["costs"]

        # Calculate current patterns
        current_avg_interval = statistics.mean(intervals) if intervals else 90
        current_cost_per_service = statistics.mean(costs) if costs else 0

        # Simple optimization logic - look for patterns
        # If intervals are very short (< 60 days), suggest increasing
        # If intervals are very long (> 180 days), suggest decreasing

        if current_avg_interval < 60:
            recommended_interval = current_avg_interval * 1.5  # Increase by 50%
            potential_savings = (
                current_cost_per_service * 0.3 * 12
            )  # 30% cost reduction, annualized
            risk_assessment = "low"
            rationale = "Current service frequency is higher than typical. Extending intervals could reduce costs while maintaining reliability."
        elif current_avg_interval > 180:
            recommended_interval = current_avg_interval * 0.8  # Decrease by 20%
            potential_savings = (
                -current_cost_per_service * 0.2 * 12
            )  # May increase costs short-term
            risk_assessment = "medium"
            rationale = "Current service intervals may be too long, potentially leading to larger failures. More frequent PM could prevent costly breakdowns."
        else:
            recommended_interval = current_avg_interval  # No change recommended
            potential_savings = 0
            risk_assessment = "low"
            rationale = (
                "Current service intervals appear appropriate for this unit type."
            )

        # Calculate confidence based on data consistency
        interval_stddev = statistics.stdev(intervals) if len(intervals) > 1 else 0
        confidence = (
            max(0.5, 1.0 - (interval_stddev / current_avg_interval))
            if current_avg_interval > 0
            else 0.5
        )

        optimization = ServiceIntervalOptimization(
            structure_id=structure_id,
            structure_number=data["structure_str"],
            customer_name=data["customer_name"],
            model_name=data["model_name"],
            current_avg_interval=current_avg_interval,
            current_cost_per_service=Decimal(str(current_cost_per_service)),
            current_failure_rate=0.1,  # Placeholder
            recommended_interval=recommended_interval,
            confidence_level=confidence,
            potential_cost_savings=Decimal(str(potential_savings)),
            risk_assessment=risk_assessment,
            rationale=rationale,
            monitoring_metrics=[
                "Service cost per interval",
                "Failure rate",
                "Emergency service frequency",
            ],
        )

        optimization_results.append(optimization)

    # Sort by potential savings (highest first)
    optimization_results.sort(
        key=lambda x: float(x.potential_cost_savings), reverse=True
    )

    return {"result": optimization_results[:20]}  # Return top 20 opportunities


@router.post("/root-cause-analysis")
async def get_root_cause_analysis(
    filters: ServiceCostFilters, db: AsyncSession = Depends(get_ijack_db)
) -> ApiResponse[List[RootCauseAnalysisResult]]:
    """Get root cause analysis for problem units"""

    # First get outlier units to focus root cause analysis on
    outliers_data = await get_basic_outlier_detection(filters, db)
    outlier_units = outliers_data.get("result", [])

    if not outlier_units:
        return {"result": []}

    # Take top 10 outliers for detailed root cause analysis
    top_outliers = outlier_units[:10]

    root_cause_results = []

    for outlier in top_outliers:
        # Analyze this specific unit
        unit_filters = ServiceCostFilters(
            structure_id=outlier.structure_id,
            service_dates=filters.service_dates,
            selected_years=filters.selected_years,
            customers=filters.customers,
            service_types=filters.service_types,
            models=filters.models,
            unit_types=filters.unit_types,
            technicians=filters.technicians,
            part_categories=filters.part_categories,
            include_ijack=filters.include_ijack,
            include_sales_parts=filters.include_sales_parts,
        )

        # Get detailed analysis for this unit
        unit_analysis_data = await get_unit_deep_dive_analysis(unit_filters, db)
        unit_analysis = unit_analysis_data.get("result")

        if not unit_analysis:
            continue

        # Generate root cause analysis based on patterns
        primary_issue = f"{outlier.outlier_type.title()} outlier"
        secondary_issues = []
        likely_root_causes = []
        immediate_actions = []
        long_term_solutions = []

        # Analyze based on outlier type
        if outlier.outlier_type == "cost":
            if (
                outlier.current_cost_per_service
                > outlier.benchmark_cost_per_service * 2
            ):
                primary_issue = "Excessive service costs per visit"
                secondary_issues = [
                    "High parts costs",
                    "Extended labor time",
                    "Frequent emergency services",
                ]
                likely_root_causes = [
                    "Underlying mechanical issues causing cascading failures",
                    "Inadequate preventive maintenance leading to major repairs",
                    "Parts quality issues requiring frequent replacement",
                    "Technician efficiency issues or training gaps",
                ]
                immediate_actions = [
                    "Conduct thorough mechanical inspection",
                    "Review service history for recurring part failures",
                    "Assess technician performance on this unit",
                ]
                long_term_solutions = [
                    "Implement condition-based monitoring",
                    "Upgrade to higher-quality parts where cost-effective",
                    "Establish dedicated technician for complex units",
                ]

        elif outlier.outlier_type == "frequency":
            if (
                outlier.current_service_frequency
                > outlier.benchmark_service_frequency * 2
            ):
                primary_issue = "Excessive service frequency"
                secondary_issues = [
                    "Short intervals between failures",
                    "Preventive maintenance ineffectiveness",
                ]
                likely_root_causes = [
                    "Operating conditions exceeding design parameters",
                    "Poor maintenance practices or incomplete repairs",
                    "Environmental factors accelerating wear",
                ]
                immediate_actions = [
                    "Review operating conditions and usage patterns",
                    "Audit recent service quality and completeness",
                    "Check environmental factors (contamination, temperature, etc.)",
                ]
                long_term_solutions = [
                    "Optimize operating parameters",
                    "Implement enhanced PM procedures",
                    "Consider equipment upgrade or modification",
                ]

        # Generate evidence summary
        evidence_parts = []
        if unit_analysis.service_patterns.repeat_service_rate > 20:
            evidence_parts.append(
                f"High repeat service rate ({unit_analysis.service_patterns.repeat_service_rate:.1f}%)"
            )
        if unit_analysis.parts_vs_labor_ratio > 3:
            evidence_parts.append(
                f"High parts-to-labor ratio ({unit_analysis.parts_vs_labor_ratio:.1f})"
            )
        if len(unit_analysis.top_cost_drivers) > 0:
            top_driver = unit_analysis.top_cost_drivers[0]
            evidence_parts.append(
                f"Top cost driver: {top_driver.driver_name} ({top_driver.percentage_of_total:.1f}% of costs)"
            )

        evidence_summary = (
            "; ".join(evidence_parts)
            if evidence_parts
            else "Limited historical data available"
        )

        # Calculate impacts
        cost_impact = outlier.annual_excess_cost
        frequency_impact = (
            outlier.current_service_frequency - outlier.benchmark_service_frequency
        )

        # Expected improvement
        if cost_impact > 5000:
            expected_improvement = (
                "20-40% cost reduction through root cause elimination"
            )
        elif cost_impact > 2000:
            expected_improvement = (
                "15-25% cost reduction through targeted interventions"
            )
        else:
            expected_improvement = "10-15% improvement through optimization"

        # Confidence score based on data quality
        confidence = 0.8 if len(unit_analysis.cost_trend_data) > 6 else 0.6

        root_cause = RootCauseAnalysisResult(
            structure_id=outlier.structure_id,
            structure_number=outlier.structure_number,
            customer_name=outlier.customer_name,
            model_name=outlier.model_name,
            primary_issue=primary_issue,
            secondary_issues=secondary_issues,
            likely_root_causes=likely_root_causes,
            evidence_summary=evidence_summary,
            cost_impact=cost_impact,
            frequency_impact=frequency_impact,
            immediate_actions=immediate_actions,
            long_term_solutions=long_term_solutions,
            expected_improvement=expected_improvement,
            confidence_score=confidence,
            analysis_date=date.today(),
        )

        root_cause_results.append(root_cause)

    return {"result": root_cause_results}
