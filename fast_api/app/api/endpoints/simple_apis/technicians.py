from fastapi import APIRouter, Depends, Security
from shared.config import (
    CUSTOMER_ID_IJACK_CORP,
    CUSTOMER_ID_IJACK_INC,
    ROLE_ID_IJACK_SALES,
    ROLE_ID_IJACK_SOFTWARE_DEV,
)
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.models.models import User
from app.schemas.response import ApiResponse

from .schemas import TechnicianSchema

router = APIRouter(
    prefix="/technicians",
    tags=["technicians"],
)


@router.get("/", response_model=ApiResponse[list[TechnicianSchema]])
async def get_all_technicians(
    ijack_db: AsyncSession = Depends(get_ijack_db),
    _: int = Security(
        roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV])
    ),
):
    """
    Get active IJACK technicians for service cost analysis
    Returns a list of active users from IJACK Inc and IJACK Corp
    """
    query = (
        select(User.id, User.full_name)
        .where(User.is_active == True)  # noqa: E712
        .where(User.customer_id.in_([CUSTOMER_ID_IJACK_INC, CUSTOMER_ID_IJACK_CORP]))
        .order_by(User.first_name, User.last_name)
    )
    result = await ijack_db.execute(query)
    return {"result": result.all()}
