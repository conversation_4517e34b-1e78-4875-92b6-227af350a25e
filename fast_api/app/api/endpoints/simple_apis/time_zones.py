from typing import List

from fastapi import APIRouter, Depends, HTTPException, Security
from shared.config import ROLE_ID_IJACK_ADMIN
from shared.models.models import TimeZone
from sqlalchemy import select, text
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import TimeZoneSchema

router = APIRouter(
    prefix="/time_zones",
    tags=["time_zones"],
)


@router.get("/", response_model=ApiResponse[List[TimeZoneSchema]])
async def get_all_time_zones(ijack_db: AsyncSession = Depends(get_ijack_db)):
    """
    Get all time zones - migrated from Flask time_zones API
    Returns a list of all TimeZone records
    """
    query = select(TimeZone)
    result = await ijack_db.execute(query)
    time_zones = result.scalars().all()

    # Convert to dict for JSON serialization
    data = []
    for tz in time_zones:
        tz_dict = {
            "id": tz.id,
            "time_zone": tz.time_zone,
            "description": tz.description,
            "country_code": tz.country_code,
        }
        data.append(tz_dict)

    return {"result": data}


@router.get(
    "/by_country/{country_id}", response_model=ApiResponse[List[TimeZoneSchema]]
)
async def get_time_zones_by_country(
    country_id: str, ijack_db: AsyncSession = Depends(get_ijack_db)
):
    """
    Get time zones by country ID - migrated from Flask time_zones API
    Returns a list of all time zones for a given country code
    """
    if country_id:
        query = (
            select(TimeZone)
            .where(TimeZone.country_id == country_id)
            .order_by(TimeZone.time_zone)
        )
        result = await ijack_db.execute(query)
        time_zones = result.scalars().all()

        if not time_zones:
            raise HTTPException(
                status_code=404, detail=f"No records for that country ID '{country_id}'"
            )

        # Convert to dict for JSON serialization
        data = []
        for tz in time_zones:
            tz_dict = {
                "id": tz.id,
                "time_zone": tz.time_zone,
                "description": tz.description,
                "country_code": tz.country_code,
            }
            data.append(tz_dict)

        return {"result": data}
    else:
        # Return all time zones if no country_id provided
        return await get_all_time_zones(ijack_db)


@router.get("/{time_zone_id}", response_model=ApiResponse[TimeZoneSchema])
async def get_time_zone_by_id(
    time_zone_id: int, ijack_db: AsyncSession = Depends(get_ijack_db)
):
    """
    Get time zone by ID - migrated from Flask time_zones API
    Returns a single TimeZone record based on the time zone ID
    """
    query = select(TimeZone).where(TimeZone.id == time_zone_id)
    result = await ijack_db.execute(query)
    time_zone = result.scalar_one_or_none()

    if not time_zone:
        raise HTTPException(status_code=404, detail="Time zone not found")

    tz_dict = {
        "id": time_zone.id,
        "time_zone": time_zone.time_zone,
        "description": time_zone.description,
        "country_code": time_zone.country_code,
    }

    return {"result": tz_dict}


@router.get("/by_gateway/{gateway}", response_model=ApiResponse[TimeZoneSchema])
async def get_time_zone_by_gateway(
    gateway: str,
    ijack_db: AsyncSession = Depends(get_ijack_db),
    _: int = Security(roles_required([ROLE_ID_IJACK_ADMIN])),
):
    """
    Get time zone by gateway - migrated from Flask time_zones API
    Returns a single time zone record based on the gateway (admin only)
    """
    # Raw SQL query as in Flask version
    sql = text("""
        SELECT time_zone_id
        FROM public.vw_structures_joined_filtered
        WHERE gateway = :gw
            AND unit_type_id IS NOT NULL
            AND customer_id IS DISTINCT FROM 21 -- demo customer
        LIMIT 1
    """).bindparams(gw=gateway)

    result = await ijack_db.execute(sql)
    row = result.fetchone()

    if not row:
        raise HTTPException(status_code=404, detail="Time zone not found")

    time_zone_id = row[0]

    # Get time zone by ID
    query = select(TimeZone).where(TimeZone.id == time_zone_id)
    result = await ijack_db.execute(query)
    time_zone = result.scalar_one_or_none()

    if not time_zone:
        raise HTTPException(status_code=404, detail="Time zone not found")

    tz_dict = {
        "id": time_zone.id,
        "time_zone": time_zone.time_zone,
        "description": time_zone.description,
        "country_code": time_zone.country_code,
    }

    return {"result": tz_dict}
