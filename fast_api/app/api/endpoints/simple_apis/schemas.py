from typing import List, Optional

from pydantic import BaseModel, ConfigDict, Field


class TechnicianSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    full_name: str = Field(..., description="Full name of the technician")


class TimeZoneSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    time_zone: str = Field(..., description="Time zone name")
    description: Optional[str] = Field(None, description="Time zone description")
    country_code: Optional[str] = Field(None, description="Country code")


class ModelTypeSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    model: str = Field(..., description="Model name")
    description: Optional[str] = Field(None, description="Model description")
    unit_type_id: int = Field(..., description="Unit type ID")
    color: Optional[str] = Field(None, description="Color value")


class ModelTypeCreateSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    model: str = Field(..., description="Model name")
    description: Optional[str] = Field(None, description="Model description")
    unit_type_id: int = Field(..., description="Unit type ID")
    color: Optional[str] = Field(None, description="Color value")


class CompressionSurfaceSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    compression_or_surface: str = Field(
        ..., description="Type: compression, surface, or both"
    )


class StructureSlavesSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    structure_slaves: List[str] = Field(..., description="List of structure slave IDs")
