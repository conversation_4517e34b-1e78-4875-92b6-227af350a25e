from typing import Dict, List

from fastapi import APIRouter, Depends, Security
from shared.config import ROLE_ID_IJACK_ADMIN
from shared.models.models_bom import ModelType
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import ModelTypeCreateSchema, ModelTypeSchema

router = APIRouter(
    prefix="/model_types",
    tags=["model_types"],
)


@router.get("/", response_model=ApiResponse[List[ModelTypeSchema]])
async def get_all_model_types(
    ijack_db: AsyncSession = Depends(get_ijack_db),
    _: int = Security(roles_required([ROLE_ID_IJACK_ADMIN])),
):
    """
    Get all model types - migrated from Flask model_types API
    Returns a list of all ModelType records (admin only)
    """
    query = select(ModelType)
    result = await ijack_db.execute(query)
    model_types = result.scalars().all()

    # Convert to dict for JSON serialization
    data = []
    for mt in model_types:
        mt_dict = {
            "id": mt.id,
            "model": mt.model,
            "description": mt.description,
            "unit_type_id": mt.unit_type_id,
            "color": mt.color,
        }
        data.append(mt_dict)

    return {"result": data}


@router.post("/", response_model=ApiResponse[Dict[str, str]])
async def create_or_update_model_type(
    model_type: ModelTypeCreateSchema,
    ijack_db: AsyncSession = Depends(get_ijack_db),
    _: int = Security(roles_required([ROLE_ID_IJACK_ADMIN])),
):
    """
    Create or update model type record - migrated from Flask model_types API
    Creates a single record. If record exists, it updates it. (admin only)
    """
    # Check if record exists
    query = select(ModelType).where(ModelType.id == model_type.id)
    result = await ijack_db.execute(query)
    existing_record = result.scalar_one_or_none()

    if existing_record is None:
        # Create a new record
        new_record = ModelType(
            id=model_type.id,
            model=model_type.model,
            description=model_type.description,
            unit_type_id=model_type.unit_type_id,
            color=model_type.color,
        )
        ijack_db.add(new_record)
        await ijack_db.commit()
        return {"result": {"message": "Record was created!"}}
    else:
        # Update existing record - in Flask version only color was updated
        existing_record.color = model_type.color
        await ijack_db.commit()
        return {"result": {"message": "Record was updated!"}}
