from fastapi import APIRouter

from .time_zones import router as time_zones_router
from .model_types import router as model_types_router
from .compression_surface import router as compression_surface_router
from .structure_slaves import router as structure_slaves_router
from .technicians import router as technicians_router

router = APIRouter(
    prefix="/api",
    tags=["api", "simple"],
)

# Include all simple API routers
router.include_router(time_zones_router)
router.include_router(model_types_router)
router.include_router(compression_surface_router)
router.include_router(structure_slaves_router)
router.include_router(technicians_router)
