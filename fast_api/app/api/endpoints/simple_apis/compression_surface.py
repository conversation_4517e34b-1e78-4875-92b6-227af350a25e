from fastapi import APIRouter, Depends, HTTPException, Security
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse
from app.auth.session import roles_required
from shared.config import ROLE_ID_IJACK_ADMIN

from .schemas import CompressionSurfaceSchema

router = APIRouter(
    prefix="/compression_or_surface",
    tags=["compression_surface"],
)


@router.get("/{gateway}", response_model=ApiResponse[CompressionSurfaceSchema])
async def get_compression_or_surface_by_gateway(
    gateway: str,
    ijack_db: AsyncSession = Depends(get_ijack_db),
    _: int = Security(roles_required([ROLE_ID_IJACK_ADMIN])),
):
    """
    Get compression or surface type by gateway - migrated from Flask compression_or_surface API
    Returns the unit type classification for a gateway (admin only)
    """
    # Raw SQL query as in Flask version
    sql = text("""
        SELECT unit_type_id
        FROM public.vw_structures_joined_filtered
        WHERE gateway = :gw
            AND unit_type_id IS NOT NULL
            AND customer_id IS DISTINCT FROM 21 -- demo customer
        LIMIT 1
    """).bindparams(gw=gateway)

    result = await ijack_db.execute(sql)
    row = result.fetchone()

    if not row:
        raise HTTPException(
            status_code=404,
            detail=f"Compression or surface not found for gateway {gateway}",
        )

    unit_type_id = row[0]

    # Determine compression_or_surface based on unit_type_id
    compression_or_surface = "both"  # UNOGAS
    if unit_type_id == 1:  # UNO
        compression_or_surface = "surface"
    elif unit_type_id in (2, 3, 6, 7):  # EGAS, XFER, SHOP, BOOST
        compression_or_surface = "compression"

    result_data = {"compression_or_surface": compression_or_surface}

    return {"result": result_data}
