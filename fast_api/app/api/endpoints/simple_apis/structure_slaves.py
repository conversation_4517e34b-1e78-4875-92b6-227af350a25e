from fastapi import APIRouter, Depends, Security
from shared.config import ROLE_ID_IJACK_ADMIN
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import StructureSlavesSchema

router = APIRouter(
    prefix="/structure_slaves",
    tags=["structure_slaves"],
)


@router.get("/", response_model=ApiResponse[StructureSlavesSchema])
async def get_all_structure_slaves(
    ijack_db: AsyncSession = Depends(get_ijack_db),
    _: int = Security(roles_required([ROLE_ID_IJACK_ADMIN])),
):
    """
    Get all structure slaves - migrated from Flask structure_slaves API
    Returns a list of all structure slave IDs (admin only)
    """
    # Raw SQL query as in Flask version
    sql = text("""
        SELECT id
        FROM public.structures
        WHERE id IN (
            SELECT structure_slave_id
            FROM public.structures t1
            WHERE structure_slave_id IS NOT NULL
        )
    """)

    result = await ijack_db.execute(sql)
    rows = result.fetchall()

    # Convert to list of string IDs as in Flask version
    structure_slaves = [str(row[0]) for row in rows]

    result_data = {"structure_slaves": structure_slaves}

    return {"result": result_data}
