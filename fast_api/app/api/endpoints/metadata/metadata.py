from typing import Dict, List

from fastapi import APIRouter, Depends
from shared.models.models import MetaDataTbl
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import MetaDataCreateSchema, MetaDataSchema

router = APIRouter()


@router.get("/", response_model=ApiResponse[List[MetaDataSchema]])
async def get_all_metadata(ijack_db: AsyncSession = Depends(get_ijack_db)):
    """
    Get all metadata records - migrated from Flask meta_data API
    Returns a list of all MetaDataTbl records (admin only via router dependencies)
    """
    query = select(MetaDataTbl)
    result = await ijack_db.execute(query)
    metadata_records = result.scalars().all()

    # Convert to dict for JSON serialization
    data = []
    for record in metadata_records:
        record_dict = {
            "id": record.id,
            "id_cell": record.id_cell,
            "element": record.element,
            "color": record.color,
        }
        data.append(record_dict)

    return {"result": data}


@router.post("/", response_model=ApiResponse[Dict[str, str]])
async def create_or_update_metadata(
    metadata: MetaDataCreateSchema, ijack_db: AsyncSession = Depends(get_ijack_db)
):
    """
    Create or update metadata record - migrated from Flask meta_data API
    Creates a single record. If record exists, it updates it.
    """
    # Check if record exists
    query = select(MetaDataTbl).where(
        MetaDataTbl.id_cell == metadata.id_cell, MetaDataTbl.element == metadata.element
    )
    result = await ijack_db.execute(query)
    existing_record = result.scalar_one_or_none()

    if existing_record is None:
        # Create a new record
        new_record = MetaDataTbl(
            id_cell=metadata.id_cell, element=metadata.element, color=metadata.color
        )
        ijack_db.add(new_record)
        await ijack_db.commit()
        return {"result": {"message": "Record was created!"}}
    else:
        # Update existing record
        if metadata.color == "":
            # If the user changes the color to empty, remove the formatting
            await ijack_db.delete(existing_record)
            await ijack_db.commit()
            return {"result": {"message": "Record was deleted!"}}
        else:
            existing_record.color = metadata.color  # type: ignore
            await ijack_db.commit()
            return {"result": {"message": "Record was updated!"}}
