from fastapi import APIRouter, Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV

from app.auth.session import roles_required

from .customer import router as customer_router

router = APIRouter(
    prefix="/customer",
    tags=["customers", "sales"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)

router.include_router(customer_router)
