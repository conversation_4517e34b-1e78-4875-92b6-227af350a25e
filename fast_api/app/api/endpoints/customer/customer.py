from fastapi import APIRouter, Depends
from shared.models.models import Customer
from sqlalchemy import select

from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import CustomerSchema

router = APIRouter()


@router.get(
    "/",
)
async def read_customers(
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[CustomerSchema]]:
    """
    Get a list of customers and their details.
    """
    query = select(Customer.id, Customer.customer).order_by(Customer.customer)
    result = await ijack_db.execute(query)
    return {"result": result.all()}
