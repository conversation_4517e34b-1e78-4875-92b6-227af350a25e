from typing import Optional

from fastapi import APIRouter, Depends
from shared.models.models import UnitType
from shared.models.models_bom import (
    Part,
    PowerUnitTypePower,
)
from sqlalchemy import select

from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import PowerUnitPricing

router = APIRouter(
    tags=["power_unit"],
    prefix="/power-unit",
    dependencies=[],
)


@router.get(
    "/",
    summary="Get the power_unit pricing",
)
async def get_pump_top_pricing(
    unit_type: Optional[str] = None,
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[PowerUnitPricing]]:
    """
    Retrieve the top pricing for pumps.
    Returns a list of pump top pricing details.
    """
    query = (
        select(
            PowerUnitTypePower.part_id.label("power_unit_id"),
            Part.msrp_cad,
            Part.msrp_usd,
            Part.description,
        )
        .select_from(PowerUnitTypePower)
        .join(Part, PowerUnitTypePower.part_id == Part.id)
    )
    if unit_type:
        query = query.where(UnitType.unit_type == unit_type)
    query = query.order_by(Part.description.desc())
    result = await ijack_db.execute(query)
    pricing_data = result.all()
    return {"result": pricing_data}


@router.get(
    "/{power_unit_id}",
    summary="Get the power unit pricing by power unit ID",
)
async def get_power_unit_pricing_by_id(
    power_unit_id: int,
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[PowerUnitPricing]:
    """
    Retrieve the voltage pricing by site voltage ID.
    Returns the voltage pricing details for the specified site voltage ID.
    """
    query = (
        select(
            PowerUnitTypePower.part_id.label("power_unit_id"),
            Part.msrp_cad,
            Part.msrp_usd,
            Part.description,
        )
        .select_from(PowerUnitTypePower)
        .join(Part, PowerUnitTypePower.part_id == Part.id)
        .where(PowerUnitTypePower.part_id == power_unit_id)
    )
    result = await ijack_db.execute(query)
    pricing_data = result.first()
    return {"result": pricing_data}
