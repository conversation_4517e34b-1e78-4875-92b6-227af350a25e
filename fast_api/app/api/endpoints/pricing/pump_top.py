from typing import Optional

from fastapi import APIRouter, Depends
from shared.models.models import UnitType
from shared.models.models_bom import ModelType, ModelTypeOption, Part
from sqlalchemy import select

from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import PumpTopPricing

router = APIRouter(
    tags=["pump-top"],
    prefix="/pump-top",
    dependencies=[],
)


@router.get(
    "/",
    summary="Get the top pricing for pumps",
)
async def get_pump_top_pricing(
    unit_type: Optional[str] = None,
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[PumpTopPricing]]:
    """
    Retrieve the top pricing for pumps.
    Returns a list of pump top pricing details.
    """
    query = (
        select(
            ModelTypeOption.part_id.label("pump_top_id"),
            Part.msrp_cad,
            Part.msrp_usd,
            UnitType.unit_type,
            Part.description,
        )
        .select_from(ModelTypeOption)
        .join(Part, ModelTypeOption.part_id == Part.id)
        .join(ModelType, Part.id == ModelType.part_id)
        .join(UnitType, ModelType.unit_type_id == UnitType.id)
    )
    if unit_type:
        query = query.where(UnitType.unit_type == unit_type)
    query = query.order_by(Part.description)
    result = await ijack_db.execute(query)
    pricing_data = result.all()
    return {"result": pricing_data}


@router.get(
    "/{part_id}",
    summary="Get specific pump top pricing by part ID",
)
async def get_specific_pump_top_pricing(
    part_id: int,
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[PumpTopPricing]:
    """
    Retrieve specific pump top pricing by part ID.
    """
    query = (
        select(
            ModelTypeOption.part_id.label("pump_top_id"),
            Part.msrp_cad,
            Part.msrp_usd,
            UnitType.unit_type,
            Part.description,
        )
        .select_from(ModelTypeOption)
        .join(Part, ModelTypeOption.part_id == Part.id)
        .join(ModelType, Part.id == ModelType.part_id)
        .join(UnitType, ModelType.unit_type_id == UnitType.id)
        .where(ModelTypeOption.part_id == part_id)
    )
    result = await ijack_db.execute(query)
    pricing_data = result.one_or_none()
    return {"result": pricing_data}
