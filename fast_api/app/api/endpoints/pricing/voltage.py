from typing import Optional

from fastapi import APIRouter, Depends
from shared.models.models import UnitType
from shared.models.models_bom import (
    Part,
    PowerUnitTypeVoltage,
)
from sqlalchemy import select

from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import VoltagePricing

router = APIRouter(
    tags=["voltage"],
    prefix="/voltage",
    dependencies=[],
)


@router.get(
    "/",
    summary="Get the voltage pricing",
)
async def get_pump_top_pricing(
    unit_type: Optional[str] = None,
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[VoltagePricing]]:
    """
    Retrieve the top pricing for pumps.
    Returns a list of pump top pricing details.
    """
    query = (
        select(
            PowerUnitTypeVoltage.part_id.label("site_voltage_id"),
            Part.msrp_cad,
            Part.msrp_usd,
            Part.description,
        )
        .select_from(PowerUnitTypeVoltage)
        .join(Part, PowerUnitTypeVoltage.part_id == Part.id)
    )
    if unit_type:
        query = query.where(UnitType.unit_type == unit_type)
    query = query.order_by(Part.description.desc())
    result = await ijack_db.execute(query)
    pricing_data = result.all()
    return {"result": pricing_data}


@router.get(
    "/{site_voltage_id}",
    summary="Get the voltage pricing by site voltage ID",
)
async def get_voltage_pricing_by_id(
    site_voltage_id: int,
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[VoltagePricing]:
    """
    Retrieve the voltage pricing by site voltage ID.
    Returns the voltage pricing details for the specified site voltage ID.
    """
    query = (
        select(
            PowerUnitTypeVoltage.part_id.label("site_voltage_id"),
            Part.msrp_cad,
            Part.msrp_usd,
            Part.description,
        )
        .select_from(PowerUnitTypeVoltage)
        .join(Part, PowerUnitTypeVoltage.part_id == Part.id)
        .where(PowerUnitTypeVoltage.part_id == site_voltage_id)
    )
    result = await ijack_db.execute(query)
    pricing_data = result.first()
    return {"result": pricing_data}
