from decimal import Decimal

from pydantic import BaseModel, ConfigDict


class PumpTopPricing(BaseModel):
    """Schema for the top pricing of pumps."""

    pump_top_id: int
    unit_type: str
    msrp_cad: Decimal
    msrp_usd: Decimal
    description: str
    model_config = ConfigDict(from_attributes=True)


class VoltagePricing(BaseModel):
    """Schema for the voltage pricing of pumps."""

    site_voltage_id: int
    msrp_cad: Decimal
    msrp_usd: Decimal
    description: str
    model_config = ConfigDict(from_attributes=True)


class PowerUnitPricing(BaseModel):
    """Schema for the power unit pricing."""

    power_unit_id: int
    msrp_cad: Decimal
    msrp_usd: Decimal
    description: str
    model_config = ConfigDict(from_attributes=True)
