"""
Dynamic admin router that creates endpoints for all models in the registry
Replaces Flask admin_api.py with better functionality and DRY principles
"""

from fastapi import APIRouter

from .model_registry import create_crud_router, get_all_model_configs

# Create the main admin router
admin_router = APIRouter(
    prefix="/admin",
    tags=["admin"],
)

# Track created routers to avoid duplicates
_created_routers = {}


def setup_admin_routes():
    """
    Dynamically create CRUD routes for all models in the registry
    This replaces the Flask admin_api.py with a single, maintainable solution
    """
    global _created_routers

    model_configs = get_all_model_configs()

    for table_name, config in model_configs.items():
        if table_name not in _created_routers:
            try:
                # Create CRUD router for this model
                crud_router = create_crud_router(config)

                # Include the router in the admin router
                admin_router.include_router(
                    crud_router.router,
                    prefix=f"/{table_name}",
                    tags=[table_name, "admin", config.model_class.__name__],
                )

                _created_routers[table_name] = crud_router

            except Exception as e:
                # Log error but don't fail the entire setup
                print(f"Warning: Failed to create router for {table_name}: {e}")
                continue


# Additional admin endpoints that don't fit the CRUD pattern
@admin_router.get("/models")
async def list_available_models():
    """List all available models for admin interface"""
    model_configs = get_all_model_configs()

    models_info = {}
    for table_name, config in model_configs.items():
        models_info[table_name] = {
            "model_class": config.model_class.__name__,
            "table_name": config.table_name,
            "searchable_fields": config.searchable_fields,
            "filterable_fields": config.filterable_fields,
            "relationships": config.relationships,
            "readonly": config.readonly,
        }

    return {"result": models_info}


@admin_router.get("/health")
async def admin_health_check():
    """Health check endpoint for admin interface"""
    return {
        "result": {
            "status": "healthy",
            "models_available": len(get_all_model_configs()),
            "routers_created": len(_created_routers),
        }
    }


# Initialize routes when module is imported
setup_admin_routes()
