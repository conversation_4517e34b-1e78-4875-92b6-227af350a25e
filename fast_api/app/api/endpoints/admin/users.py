from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import Depends, HTTPException
from pydantic import BaseModel, EmailStr
from shared.config import ROLE_ID_IJACK_ADMIN
from shared.models.models import User
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.database import get_ijack_db

from .generic_crud import GenericCRUDRouter


# Pydantic schema for User admin operations
class UserAdminSchema(BaseModel):
    id: Optional[int] = None
    first_name: str
    last_name: str
    email: EmailStr
    phone: Optional[str] = None
    is_active: bool = True
    customer_id: Optional[int] = None
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True


# Create generic CRUD router for users
users_crud = GenericCRUDRouter(
    model_class=User,
    schema_class=UserAdminSchema,
    table_name="users",
    allowed_roles=[ROLE_ID_IJACK_ADMIN],
    relationships=["roles_rel", "customers_rel"],
    searchable_fields=["first_name", "last_name", "email", "phone"],
    filterable_fields=["is_active", "customer_id", "created_at"],
)

# Get the router and add custom endpoints
router = users_crud.router


@router.post("/{id}/reset_password")
async def reset_user_password(
    id: int, db: AsyncSession = Depends(get_ijack_db)
) -> Dict[str, str]:
    """Reset user password (admin only)"""

    try:
        # Get user
        query = select(User).where(User.id == id)
        result = await db.execute(query)
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Generate new temporary password
        import secrets
        import string

        temp_password = "".join(
            secrets.choice(string.ascii_letters + string.digits) for _ in range(12)
        )

        # Update user password (assuming there's a method to set password)
        # user.password = temp_password  # This would need proper password hashing

        await db.commit()

        # In production, send email with temporary password
        # For now, return it (NOT recommended for production)
        return {
            "message": "Password reset successfully",
            "temporary_password": temp_password,  # Remove this in production
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{id}/toggle_active")
async def toggle_user_active(
    id: int, db: AsyncSession = Depends(get_ijack_db)
) -> Dict[str, Any]:
    """Toggle user active status"""

    try:
        # Get user
        query = select(User).where(User.id == id)
        result = await db.execute(query)
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Toggle active status
        user.is_active = not user.is_active
        await db.commit()

        return {
            "message": f"User {'activated' if user.is_active else 'deactivated'} successfully",
            "is_active": user.is_active,
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
