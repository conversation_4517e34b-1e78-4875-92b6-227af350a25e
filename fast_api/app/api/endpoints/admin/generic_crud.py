from datetime import datetime
from typing import Any, Dict, List, Optional, Type

from fastapi import APIRouter, Depends, HTTPException, Query, Security
from pydantic import BaseModel
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse, PaginatedResponse


class GenericCRUDRouter:
    """
    Generic CRUD router that provides standardized endpoints for admin tables
    Mirrors Flask-Admin functionality with role-based access control
    """

    def __init__(
        self,
        model_class: Type,
        schema_class: Type[BaseModel],
        table_name: str,
        allowed_roles: List[int],
        rejected_roles: Optional[List[int]] = None,
        relationships: Optional[List[str]] = None,
        searchable_fields: Optional[List[str]] = None,
        filterable_fields: Optional[List[str]] = None,
    ):
        self.model_class = model_class
        self.schema_class = schema_class
        self.table_name = table_name
        self.allowed_roles = allowed_roles
        self.rejected_roles = rejected_roles or []
        self.relationships = relationships or []
        self.searchable_fields = searchable_fields or []
        self.filterable_fields = filterable_fields or []

        self.router = APIRouter(
            prefix=f"/{table_name}",
            tags=[table_name, "admin"],
            dependencies=[Security(roles_required(allowed_roles, rejected_roles))],
        )

        self._setup_routes()

    def _apply_relationships(self, query):
        """Apply relationship loading to query"""
        for rel in self.relationships:
            if hasattr(self.model_class, rel):
                query = query.options(selectinload(getattr(self.model_class, rel)))
        return query

    def _apply_filters(self, query, filters: Dict[str, Any]):
        """Apply filters to query"""
        for field, value in filters.items():
            if hasattr(self.model_class, field) and value is not None:
                attr = getattr(self.model_class, field)

                # Handle different filter types
                if isinstance(value, str):
                    if field in self.searchable_fields:
                        # For searchable fields, use ILIKE for partial matching
                        query = query.where(attr.ilike(f"%{value}%"))
                    else:
                        query = query.where(attr == value)
                else:
                    query = query.where(attr == value)

        return query

    def _apply_search(self, query, search_term: str):
        """Apply search across searchable fields"""
        if not search_term or not self.searchable_fields:
            return query

        conditions = []
        for field in self.searchable_fields:
            if hasattr(self.model_class, field):
                attr = getattr(self.model_class, field)
                conditions.append(attr.ilike(f"%{search_term}%"))

        if conditions:
            from sqlalchemy import or_

            query = query.where(or_(*conditions))

        return query

    def _apply_sorting(self, query, sort_field: str, sort_order: str = "asc"):
        """Apply sorting to query"""
        if sort_field and hasattr(self.model_class, sort_field):
            attr = getattr(self.model_class, sort_field)
            if sort_order.lower() == "desc":
                query = query.order_by(attr.desc())
            else:
                query = query.order_by(attr.asc())

        return query

    def _setup_routes(self):
        """Set up all CRUD routes"""

        @self.router.get("/")
        async def get_list(
            page: int = Query(1, ge=1),
            per_page: int = Query(25, ge=1, le=100),
            sort: Optional[str] = None,
            order: Optional[str] = Query("asc"),
            q: Optional[str] = None,  # Search query
            db: AsyncSession = Depends(get_ijack_db),
            **filters,
        ) -> PaginatedResponse[List[Dict[str, Any]]]:
            """Get paginated list of records with filtering and search"""

            try:
                # Base query
                query = select(self.model_class)

                # Apply relationships
                query = self._apply_relationships(query)

                # Apply search
                if q:
                    query = self._apply_search(query, q)

                # Apply filters (extract from query params with filter[] prefix)
                parsed_filters = {}
                for key, value in filters.items():
                    if key.startswith("filter_") and value:
                        field_name = key.replace("filter_", "")
                        parsed_filters[field_name] = value

                query = self._apply_filters(query, parsed_filters)

                # Get total count before pagination
                count_query = select(func.count()).select_from(query.subquery())
                result = await db.execute(count_query)
                total = result.scalar() or 0

                # Apply sorting
                if sort:
                    query = self._apply_sorting(query, sort, order)

                # Apply pagination
                offset = (page - 1) * per_page
                query = query.offset(offset).limit(per_page)

                # Execute query
                result = await db.execute(query)
                records = result.scalars().all()

                # Convert to dict for JSON serialization
                data = []
                for record in records:
                    record_dict = {}
                    for column in record.__table__.columns:
                        value = getattr(record, column.name)
                        if isinstance(value, datetime):
                            value = value.isoformat()
                        record_dict[column.name] = value
                    data.append(record_dict)

                return {
                    "result": {
                        "data": data,
                        "total": total,
                        "page": page,
                        "per_page": per_page,
                        "pages": (total + per_page - 1) // per_page if total > 0 else 0,
                    }
                }

            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.router.get("/{id}")
        async def get_one(
            id: int, db: AsyncSession = Depends(get_ijack_db)
        ) -> ApiResponse[Dict[str, Any]]:
            """Get single record by ID"""

            try:
                query = select(self.model_class).where(self.model_class.id == id)
                query = self._apply_relationships(query)

                result = await db.execute(query)
                record = result.scalar_one_or_none()

                if not record:
                    raise HTTPException(status_code=404, detail="Record not found")

                # Convert to dict
                record_dict = {}
                for column in record.__table__.columns:
                    value = getattr(record, column.name)
                    if isinstance(value, datetime):
                        value = value.isoformat()
                    record_dict[column.name] = value

                return {"result": record_dict}

            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.router.post("/")
        async def create(
            data: Dict[str, Any], db: AsyncSession = Depends(get_ijack_db)
        ) -> ApiResponse[Dict[str, Any]]:
            """Create new record"""

            try:
                # Create new instance
                record = self.model_class(**data)
                db.add(record)
                await db.commit()
                await db.refresh(record)

                # Convert to dict
                record_dict = {}
                for column in record.__table__.columns:
                    value = getattr(record, column.name)
                    if isinstance(value, datetime):
                        value = value.isoformat()
                    record_dict[column.name] = value

                return {"result": record_dict}

            except Exception as e:
                await db.rollback()
                raise HTTPException(status_code=400, detail=str(e))

        @self.router.put("/{id}")
        async def update_record(
            id: int, data: Dict[str, Any], db: AsyncSession = Depends(get_ijack_db)
        ) -> ApiResponse[Dict[str, Any]]:
            """Update existing record"""

            try:
                # Get existing record
                query = select(self.model_class).where(self.model_class.id == id)
                result = await db.execute(query)
                record = result.scalar_one_or_none()

                if not record:
                    raise HTTPException(status_code=404, detail="Record not found")

                # Update fields
                for key, value in data.items():
                    if hasattr(record, key):
                        setattr(record, key, value)

                await db.commit()
                await db.refresh(record)

                # Convert to dict
                record_dict = {}
                for column in record.__table__.columns:
                    value = getattr(record, column.name)
                    if isinstance(value, datetime):
                        value = value.isoformat()
                    record_dict[column.name] = value

                return {"result": record_dict}

            except HTTPException:
                raise
            except Exception as e:
                await db.rollback()
                raise HTTPException(status_code=400, detail=str(e))

        @self.router.delete("/{id}")
        async def delete_record(
            id: int, db: AsyncSession = Depends(get_ijack_db)
        ) -> ApiResponse[Dict[str, str]]:
            """Delete record"""

            try:
                # Check if record exists
                query = select(self.model_class).where(self.model_class.id == id)
                result = await db.execute(query)
                record = result.scalar_one_or_none()

                if not record:
                    raise HTTPException(status_code=404, detail="Record not found")

                # Delete record
                await db.delete(record)
                await db.commit()

                return {"result": {"message": "Record deleted successfully"}}

            except HTTPException:
                raise
            except Exception as e:
                await db.rollback()
                raise HTTPException(status_code=500, detail=str(e))

        @self.router.get("/batch")
        async def get_many(
            ids: List[int] = Query(...), db: AsyncSession = Depends(get_ijack_db)
        ) -> ApiResponse[List[Dict[str, Any]]]:
            """Get multiple records by IDs"""

            try:
                query = select(self.model_class).where(self.model_class.id.in_(ids))
                query = self._apply_relationships(query)

                result = await db.execute(query)
                records = result.scalars().all()

                # Convert to dict
                data = []
                for record in records:
                    record_dict = {}
                    for column in record.__table__.columns:
                        value = getattr(record, column.name)
                        if isinstance(value, datetime):
                            value = value.isoformat()
                        record_dict[column.name] = value
                    data.append(record_dict)

                return {"result": data}

            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
