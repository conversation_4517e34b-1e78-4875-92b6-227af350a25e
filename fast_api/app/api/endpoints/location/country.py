from fastapi import APIRouter, Depends
from shared.models.models import Country
from sqlalchemy import select

from app.api.responses.auth import AUTHZ_RESPONSES
from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import CountrySchema

router = APIRouter(
    prefix="/country",
    tags=["country"],
)


@router.get(
    "/",
    tags=["country"],
    responses=AUTHZ_RESPONSES,
)
async def read_countries(
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[CountrySchema]]:
    """
    Get a list of countries and their details.
    """
    query = select(Country.id, Country.country_name)
    result = await ijack_db.execute(query)
    return {"result": result.all()}
