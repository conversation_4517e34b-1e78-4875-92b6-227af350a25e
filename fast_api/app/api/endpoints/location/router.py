from fastapi import APIRouter, Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV

from app.auth.session import roles_required

from .country import router as country_router
from .state_province import router as state_province_router

router = APIRouter(
    prefix="/locations",
    tags=["locations", "sales"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)

router.include_router(country_router)
router.include_router(state_province_router)
