from fastapi import APIRouter, Depends
from shared.models.models import Country, Province
from sqlalchemy import select

from app.api.responses.auth import AUTHZ_RESPONSES
from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import ProvinceSchema

router = APIRouter(
    tags=["state province"],
)


@router.get(
    "/province",
    tags=["CA", "internal"],
    responses=AUTHZ_RESPONSES,
)
async def read_provinces(
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[ProvinceSchema]]:
    query = select(Province.id, Province.name).filter(
        Province.country_id
        == select(Country.id).where(Country.country_name == "Canada")
    )
    result = await ijack_db.execute(query)
    return {"result": result.all()}


@router.get(
    "/state",
    tags=["US", "internal"],
    responses=AUTHZ_RESPONSES,
)
async def read_states(
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[ProvinceSchema]]:
    query = select(Province.id, Province.name).where(
        Province.country_id
        == select(Country.id).where(Country.country_name == "United States")
    )
    result = await ijack_db.execute(query)
    return {"result": result.all()}
