# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""  # noqa: F841
FastAPI Router for Machine Learning resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841
  # noqa: F841
from typing import List, Optional  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841
from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841
from sqlalchemy import desc, asc  # noqa: F841
  # noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841
  # noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
  # noqa: F841
  # noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/machine learning", tags=["Machine Learning"])  # noqa: F841
  # noqa: F841
# Pydantic Schemas  # noqa: F841
class CompressionImageSchema(BaseModel):  # noqa: F841
    """Pydantic schema for CompressionImage"""  # noqa: F841
    image: Optional[str] = Field(None, description="Image field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description Rel.Description field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description Rel field")  # noqa: F841
    pattern_id: Optional[int] = Field(None, description="Pattern Id field")  # noqa: F841
    num_in_cluster: Optional[str] = Field(None, description="Num In Cluster field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Description Rel.Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Description Rel.Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Description Rel.Explanation field")  # noqa: F841
    ml_version: Optional[str] = Field(None, description="Ml Version field")  # noqa: F841
    cluster_id: Optional[int] = Field(None, description="Cluster Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CompressionImageCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating CompressionImage"""  # noqa: F841
    image: Optional[str] = Field(None, description="Image field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description Rel.Description field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description Rel field")  # noqa: F841
    pattern_id: Optional[int] = Field(None, description="Pattern Id field")  # noqa: F841
    num_in_cluster: Optional[str] = Field(None, description="Num In Cluster field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Description Rel.Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Description Rel.Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Description Rel.Explanation field")  # noqa: F841
    ml_version: Optional[str] = Field(None, description="Ml Version field")  # noqa: F841
    cluster_id: Optional[int] = Field(None, description="Cluster Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CompressionImageUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating CompressionImage"""  # noqa: F841
    image: Optional[str] = Field(None, description="Image field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description Rel.Description field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description Rel field")  # noqa: F841
    pattern_id: Optional[int] = Field(None, description="Pattern Id field")  # noqa: F841
    num_in_cluster: Optional[str] = Field(None, description="Num In Cluster field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Description Rel.Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Description Rel.Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Description Rel.Explanation field")  # noqa: F841
    ml_version: Optional[str] = Field(None, description="Ml Version field")  # noqa: F841
    cluster_id: Optional[int] = Field(None, description="Cluster Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SurfaceImageSchema(BaseModel):  # noqa: F841
    """Pydantic schema for SurfaceImage"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SurfaceImageCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating SurfaceImage"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SurfaceImageUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating SurfaceImage"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CompressionPatternSchema(BaseModel):  # noqa: F841
    """Pydantic schema for CompressionPattern"""  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CompressionPatternCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating CompressionPattern"""  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CompressionPatternUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating CompressionPattern"""  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SurfacePatternSchema(BaseModel):  # noqa: F841
    """Pydantic schema for SurfacePattern"""  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SurfacePatternCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating SurfacePattern"""  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SurfacePatternUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating SurfacePattern"""  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    send_alert: Optional[str] = Field(None, description="Send Alert field")  # noqa: F841
    solution: Optional[str] = Field(None, description="Solution field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
  # noqa: F841
# CRUD Endpoints  # noqa: F841
  # noqa: F841
@router.get("/compression_images", response_model=ApiResponse[List[CompressionImageSchema]])  # noqa: F841
async def get_compression_images(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CompressionImage records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(CompressionImage)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(CompressionImage, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(CompressionImage, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(CompressionImage, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching compression_images: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/compression_images/{item_id}", response_model=ApiResponse[CompressionImageSchema])  # noqa: F841
async def get_compression_images_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CompressionImage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CompressionImage).filter(CompressionImage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CompressionImage not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching CompressionImage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/compression_images", response_model=ApiResponse[CompressionImageSchema])  # noqa: F841
async def create_compression_images(  # noqa: F841
    item_data: CompressionImageCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new CompressionImage"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = CompressionImage(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating CompressionImage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/compression_images/{item_id}", response_model=ApiResponse[CompressionImageSchema])  # noqa: F841
async def update_compression_images(  # noqa: F841
    item_id: int,
    item_data: CompressionImageUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update CompressionImage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CompressionImage).filter(CompressionImage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CompressionImage not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating CompressionImage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/compression_images/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_compression_images(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete CompressionImage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CompressionImage).filter(CompressionImage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CompressionImage not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "CompressionImage deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting CompressionImage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/surface_images", response_model=ApiResponse[List[SurfaceImageSchema]])  # noqa: F841
async def get_surface_images(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get SurfaceImage records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(SurfaceImage)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(SurfaceImage, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(SurfaceImage, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(SurfaceImage, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching surface_images: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/surface_images/{item_id}", response_model=ApiResponse[SurfaceImageSchema])  # noqa: F841
async def get_surface_images_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get SurfaceImage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SurfaceImage).filter(SurfaceImage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SurfaceImage not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching SurfaceImage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/surface_images", response_model=ApiResponse[SurfaceImageSchema])  # noqa: F841
async def create_surface_images(  # noqa: F841
    item_data: SurfaceImageCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new SurfaceImage"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = SurfaceImage(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating SurfaceImage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/surface_images/{item_id}", response_model=ApiResponse[SurfaceImageSchema])  # noqa: F841
async def update_surface_images(  # noqa: F841
    item_id: int,
    item_data: SurfaceImageUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update SurfaceImage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SurfaceImage).filter(SurfaceImage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SurfaceImage not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating SurfaceImage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/surface_images/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_surface_images(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete SurfaceImage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SurfaceImage).filter(SurfaceImage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SurfaceImage not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "SurfaceImage deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting SurfaceImage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/compression_patterns", response_model=ApiResponse[List[CompressionPatternSchema]])  # noqa: F841
async def get_compression_patterns(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CompressionPattern records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(CompressionPattern)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(CompressionPattern, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(CompressionPattern, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(CompressionPattern, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching compression_patterns: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/compression_patterns/{item_id}", response_model=ApiResponse[CompressionPatternSchema])  # noqa: F841
async def get_compression_patterns_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CompressionPattern by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CompressionPattern).filter(CompressionPattern.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CompressionPattern not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching CompressionPattern: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/compression_patterns", response_model=ApiResponse[CompressionPatternSchema])  # noqa: F841
async def create_compression_patterns(  # noqa: F841
    item_data: CompressionPatternCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new CompressionPattern"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = CompressionPattern(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating CompressionPattern: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/compression_patterns/{item_id}", response_model=ApiResponse[CompressionPatternSchema])  # noqa: F841
async def update_compression_patterns(  # noqa: F841
    item_id: int,
    item_data: CompressionPatternUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update CompressionPattern by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CompressionPattern).filter(CompressionPattern.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CompressionPattern not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating CompressionPattern: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/compression_patterns/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_compression_patterns(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete CompressionPattern by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CompressionPattern).filter(CompressionPattern.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CompressionPattern not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "CompressionPattern deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting CompressionPattern: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/surface_patterns", response_model=ApiResponse[List[SurfacePatternSchema]])  # noqa: F841
async def get_surface_patterns(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get SurfacePattern records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(SurfacePattern)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(SurfacePattern, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(SurfacePattern, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(SurfacePattern, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching surface_patterns: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/surface_patterns/{item_id}", response_model=ApiResponse[SurfacePatternSchema])  # noqa: F841
async def get_surface_patterns_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get SurfacePattern by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SurfacePattern).filter(SurfacePattern.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SurfacePattern not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching SurfacePattern: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/surface_patterns", response_model=ApiResponse[SurfacePatternSchema])  # noqa: F841
async def create_surface_patterns(  # noqa: F841
    item_data: SurfacePatternCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new SurfacePattern"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = SurfacePattern(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating SurfacePattern: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/surface_patterns/{item_id}", response_model=ApiResponse[SurfacePatternSchema])  # noqa: F841
async def update_surface_patterns(  # noqa: F841
    item_id: int,
    item_data: SurfacePatternUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update SurfacePattern by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SurfacePattern).filter(SurfacePattern.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SurfacePattern not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating SurfacePattern: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/surface_patterns/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_surface_patterns(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete SurfacePattern by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SurfacePattern).filter(SurfacePattern.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SurfacePattern not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "SurfacePattern deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting SurfacePattern: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841