# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""  # noqa: F841
FastAPI Router for Inventory resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841
  # noqa: F841
from datetime import datetime  # noqa: F841
from decimal import Decimal  # noqa: F841
from typing import List, Optional  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841
from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841
from sqlalchemy import desc, asc  # noqa: F841
  # noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841
  # noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
  # noqa: F841
  # noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/inventory", tags=["Inventory"])  # noqa: F841
  # noqa: F841
# Pydantic Schemas  # noqa: F841
class WarehouseSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Warehouse"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
    allows_negative_stock: Optional[str] = Field(None, description="Allows Negative Stock field")  # noqa: F841
    is_main_warehouse: Optional[bool] = Field(None, description="Is Main Warehouse field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
    address: Optional[str] = Field(None, description="Address field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    zip_code: Optional[str] = Field(None, description="Zip Code field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
    time_zone: Optional[str] = Field(None, description="Time Zone Rel.Time Zone field")  # noqa: F841
    province: Optional[str] = Field(None, description="Province Rel field")  # noqa: F841
    country: Optional[int] = Field(None, description="Country Rel field")  # noqa: F841
    time_zone: Optional[str] = Field(None, description="Time Zone Rel field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WarehouseCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Warehouse"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
    allows_negative_stock: Optional[str] = Field(None, description="Allows Negative Stock field")  # noqa: F841
    is_main_warehouse: Optional[bool] = Field(None, description="Is Main Warehouse field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
    address: Optional[str] = Field(None, description="Address field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    zip_code: Optional[str] = Field(None, description="Zip Code field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
    time_zone: Optional[str] = Field(None, description="Time Zone Rel.Time Zone field")  # noqa: F841
    province: Optional[str] = Field(None, description="Province Rel field")  # noqa: F841
    country: Optional[int] = Field(None, description="Country Rel field")  # noqa: F841
    time_zone: Optional[str] = Field(None, description="Time Zone Rel field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WarehouseUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Warehouse"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
    allows_negative_stock: Optional[str] = Field(None, description="Allows Negative Stock field")  # noqa: F841
    is_main_warehouse: Optional[bool] = Field(None, description="Is Main Warehouse field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
    address: Optional[str] = Field(None, description="Address field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    zip_code: Optional[str] = Field(None, description="Zip Code field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
    time_zone: Optional[str] = Field(None, description="Time Zone Rel.Time Zone field")  # noqa: F841
    province: Optional[str] = Field(None, description="Province Rel field")  # noqa: F841
    country: Optional[int] = Field(None, description="Country Rel field")  # noqa: F841
    time_zone: Optional[str] = Field(None, description="Time Zone Rel field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WarehouseLocationSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WarehouseLocation"""  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    location_code: Optional[str] = Field(None, description="Location Code field")  # noqa: F841
    location_name: Optional[str] = Field(None, description="Location Name field")  # noqa: F841
    location_type: Optional[str] = Field(None, description="Location Type field")  # noqa: F841
    location_code: Optional[str] = Field(None, description="Parent Location Rel.Location Code field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
    is_pickable: Optional[bool] = Field(None, description="Is Pickable field")  # noqa: F841
    is_receivable: Optional[bool] = Field(None, description="Is Receivable field")  # noqa: F841
    max_weight_kg: Optional[str] = Field(None, description="Max Weight Kg field")  # noqa: F841
    max_volume_m3: Optional[str] = Field(None, description="Max Volume M3 field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WarehouseLocationCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WarehouseLocation"""  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    location_code: Optional[str] = Field(None, description="Location Code field")  # noqa: F841
    location_name: Optional[str] = Field(None, description="Location Name field")  # noqa: F841
    location_type: Optional[str] = Field(None, description="Location Type field")  # noqa: F841
    location_code: Optional[str] = Field(None, description="Parent Location Rel.Location Code field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
    is_pickable: Optional[bool] = Field(None, description="Is Pickable field")  # noqa: F841
    is_receivable: Optional[bool] = Field(None, description="Is Receivable field")  # noqa: F841
    max_weight_kg: Optional[str] = Field(None, description="Max Weight Kg field")  # noqa: F841
    max_volume_m3: Optional[str] = Field(None, description="Max Volume M3 field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WarehouseLocationUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WarehouseLocation"""  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    location_code: Optional[str] = Field(None, description="Location Code field")  # noqa: F841
    location_name: Optional[str] = Field(None, description="Location Name field")  # noqa: F841
    location_type: Optional[str] = Field(None, description="Location Type field")  # noqa: F841
    location_code: Optional[str] = Field(None, description="Parent Location Rel.Location Code field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
    is_pickable: Optional[bool] = Field(None, description="Is Pickable field")  # noqa: F841
    is_receivable: Optional[bool] = Field(None, description="Is Receivable field")  # noqa: F841
    max_weight_kg: Optional[str] = Field(None, description="Max Weight Kg field")  # noqa: F841
    max_volume_m3: Optional[str] = Field(None, description="Max Volume M3 field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WarehousePartSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WarehousePart"""  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    quantity_reserved: Optional[int] = Field(None, description="Quantity Reserved field")  # noqa: F841
    quantity_available: Optional[int] = Field(None, description="Quantity Available field")  # noqa: F841
    quantity_desired: Optional[int] = Field(None, description="Quantity Desired field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WarehousePartCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WarehousePart"""  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    quantity_reserved: Optional[int] = Field(None, description="Quantity Reserved field")  # noqa: F841
    quantity_available: Optional[int] = Field(None, description="Quantity Available field")  # noqa: F841
    quantity_desired: Optional[int] = Field(None, description="Quantity Desired field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WarehousePartUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WarehousePart"""  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    quantity_reserved: Optional[int] = Field(None, description="Quantity Reserved field")  # noqa: F841
    quantity_available: Optional[int] = Field(None, description="Quantity Available field")  # noqa: F841
    quantity_desired: Optional[int] = Field(None, description="Quantity Desired field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class InventoryMovementSchema(BaseModel):  # noqa: F841
    """Pydantic schema for InventoryMovement"""  # noqa: F841
    movement_number: Optional[str] = Field(None, description="Movement Number field")  # noqa: F841
    movement_type: Optional[str] = Field(None, description="Movement Type field")  # noqa: F841
    movement_date: Optional[datetime] = Field(None, description="Movement Date field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    unit_cost: Optional[Decimal] = Field(None, description="Unit Cost field")  # noqa: F841
    total_cost: Optional[Decimal] = Field(None, description="Total Cost field")  # noqa: F841
    from_warehouse_rel_name: Optional[str] = Field(None, description="From Warehouse Rel.Name field")  # noqa: F841
    to_warehouse_rel_name: Optional[str] = Field(None, description="To Warehouse Rel.Name field")  # noqa: F841
    reference_type: Optional[str] = Field(None, description="Reference Type field")  # noqa: F841
    reference_number: Optional[str] = Field(None, description="Reference Number field")  # noqa: F841
    created_by: Optional[str] = Field(None, description="Created By Rel field")  # noqa: F841
    is_reversed: Optional[bool] = Field(None, description="Is Reversed field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class InventoryMovementCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating InventoryMovement"""  # noqa: F841
    movement_number: Optional[str] = Field(None, description="Movement Number field")  # noqa: F841
    movement_type: Optional[str] = Field(None, description="Movement Type field")  # noqa: F841
    movement_date: Optional[datetime] = Field(None, description="Movement Date field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    unit_cost: Optional[Decimal] = Field(None, description="Unit Cost field")  # noqa: F841
    total_cost: Optional[Decimal] = Field(None, description="Total Cost field")  # noqa: F841
    from_warehouse_rel_name: Optional[str] = Field(None, description="From Warehouse Rel.Name field")  # noqa: F841
    to_warehouse_rel_name: Optional[str] = Field(None, description="To Warehouse Rel.Name field")  # noqa: F841
    reference_type: Optional[str] = Field(None, description="Reference Type field")  # noqa: F841
    reference_number: Optional[str] = Field(None, description="Reference Number field")  # noqa: F841
    created_by: Optional[str] = Field(None, description="Created By Rel field")  # noqa: F841
    is_reversed: Optional[bool] = Field(None, description="Is Reversed field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class InventoryMovementUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating InventoryMovement"""  # noqa: F841
    movement_number: Optional[str] = Field(None, description="Movement Number field")  # noqa: F841
    movement_type: Optional[str] = Field(None, description="Movement Type field")  # noqa: F841
    movement_date: Optional[datetime] = Field(None, description="Movement Date field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    unit_cost: Optional[Decimal] = Field(None, description="Unit Cost field")  # noqa: F841
    total_cost: Optional[Decimal] = Field(None, description="Total Cost field")  # noqa: F841
    from_warehouse_rel_name: Optional[str] = Field(None, description="From Warehouse Rel.Name field")  # noqa: F841
    to_warehouse_rel_name: Optional[str] = Field(None, description="To Warehouse Rel.Name field")  # noqa: F841
    reference_type: Optional[str] = Field(None, description="Reference Type field")  # noqa: F841
    reference_number: Optional[str] = Field(None, description="Reference Number field")  # noqa: F841
    created_by: Optional[str] = Field(None, description="Created By Rel field")  # noqa: F841
    is_reversed: Optional[bool] = Field(None, description="Is Reversed field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class InventoryReservationSchema(BaseModel):  # noqa: F841
    """Pydantic schema for InventoryReservation"""  # noqa: F841
    reservation_number: Optional[str] = Field(None, description="Reservation Number field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    quantity_reserved: Optional[int] = Field(None, description="Quantity Reserved field")  # noqa: F841
    quantity_fulfilled: Optional[int] = Field(None, description="Quantity Fulfilled field")  # noqa: F841
    quantity_remaining: Optional[int] = Field(None, description="Quantity Remaining field")  # noqa: F841
    reservation_date: Optional[datetime] = Field(None, description="Reservation Date field")  # noqa: F841
    expiry_date: Optional[datetime] = Field(None, description="Expiry Date field")  # noqa: F841
    reference_type: Optional[str] = Field(None, description="Reference Type field")  # noqa: F841
    reference_number: Optional[str] = Field(None, description="Reference Number field")  # noqa: F841
    priority: Optional[str] = Field(None, description="Priority field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class InventoryReservationCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating InventoryReservation"""  # noqa: F841
    reservation_number: Optional[str] = Field(None, description="Reservation Number field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    quantity_reserved: Optional[int] = Field(None, description="Quantity Reserved field")  # noqa: F841
    quantity_fulfilled: Optional[int] = Field(None, description="Quantity Fulfilled field")  # noqa: F841
    quantity_remaining: Optional[int] = Field(None, description="Quantity Remaining field")  # noqa: F841
    reservation_date: Optional[datetime] = Field(None, description="Reservation Date field")  # noqa: F841
    expiry_date: Optional[datetime] = Field(None, description="Expiry Date field")  # noqa: F841
    reference_type: Optional[str] = Field(None, description="Reference Type field")  # noqa: F841
    reference_number: Optional[str] = Field(None, description="Reference Number field")  # noqa: F841
    priority: Optional[str] = Field(None, description="Priority field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class InventoryReservationUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating InventoryReservation"""  # noqa: F841
    reservation_number: Optional[str] = Field(None, description="Reservation Number field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    quantity_reserved: Optional[int] = Field(None, description="Quantity Reserved field")  # noqa: F841
    quantity_fulfilled: Optional[int] = Field(None, description="Quantity Fulfilled field")  # noqa: F841
    quantity_remaining: Optional[int] = Field(None, description="Quantity Remaining field")  # noqa: F841
    reservation_date: Optional[datetime] = Field(None, description="Reservation Date field")  # noqa: F841
    expiry_date: Optional[datetime] = Field(None, description="Expiry Date field")  # noqa: F841
    reference_type: Optional[str] = Field(None, description="Reference Type field")  # noqa: F841
    reference_number: Optional[str] = Field(None, description="Reference Number field")  # noqa: F841
    priority: Optional[str] = Field(None, description="Priority field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CycleCountSchema(BaseModel):  # noqa: F841
    """Pydantic schema for CycleCount"""  # noqa: F841
    count_number: Optional[int] = Field(None, description="Count Number field")  # noqa: F841
    count_name: Optional[int] = Field(None, description="Count Name field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status field")  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    count_type: Optional[int] = Field(None, description="Count Type field")  # noqa: F841
    scheduled_date: Optional[datetime] = Field(None, description="Scheduled Date field")  # noqa: F841
    started_date: Optional[datetime] = Field(None, description="Started Date field")  # noqa: F841
    completed_date: Optional[datetime] = Field(None, description="Completed Date field")  # noqa: F841
    total_items: Optional[str] = Field(None, description="Total Items field")  # noqa: F841
    counted_items: Optional[int] = Field(None, description="Counted Items field")  # noqa: F841
    variance_items: Optional[str] = Field(None, description="Variance Items field")  # noqa: F841
    total_variance_value: Optional[str] = Field(None, description="Total Variance Value field")  # noqa: F841
    created_by: Optional[str] = Field(None, description="Created By Rel field")  # noqa: F841
    assigned_to: Optional[str] = Field(None, description="Assigned To Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CycleCountCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating CycleCount"""  # noqa: F841
    count_number: Optional[int] = Field(None, description="Count Number field")  # noqa: F841
    count_name: Optional[int] = Field(None, description="Count Name field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status field")  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    count_type: Optional[int] = Field(None, description="Count Type field")  # noqa: F841
    scheduled_date: Optional[datetime] = Field(None, description="Scheduled Date field")  # noqa: F841
    started_date: Optional[datetime] = Field(None, description="Started Date field")  # noqa: F841
    completed_date: Optional[datetime] = Field(None, description="Completed Date field")  # noqa: F841
    total_items: Optional[str] = Field(None, description="Total Items field")  # noqa: F841
    counted_items: Optional[int] = Field(None, description="Counted Items field")  # noqa: F841
    variance_items: Optional[str] = Field(None, description="Variance Items field")  # noqa: F841
    total_variance_value: Optional[str] = Field(None, description="Total Variance Value field")  # noqa: F841
    created_by: Optional[str] = Field(None, description="Created By Rel field")  # noqa: F841
    assigned_to: Optional[str] = Field(None, description="Assigned To Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CycleCountUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating CycleCount"""  # noqa: F841
    count_number: Optional[int] = Field(None, description="Count Number field")  # noqa: F841
    count_name: Optional[int] = Field(None, description="Count Name field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status field")  # noqa: F841
    warehouse_rel_name: Optional[str] = Field(None, description="Warehouse Rel.Name field")  # noqa: F841
    count_type: Optional[int] = Field(None, description="Count Type field")  # noqa: F841
    scheduled_date: Optional[datetime] = Field(None, description="Scheduled Date field")  # noqa: F841
    started_date: Optional[datetime] = Field(None, description="Started Date field")  # noqa: F841
    completed_date: Optional[datetime] = Field(None, description="Completed Date field")  # noqa: F841
    total_items: Optional[str] = Field(None, description="Total Items field")  # noqa: F841
    counted_items: Optional[int] = Field(None, description="Counted Items field")  # noqa: F841
    variance_items: Optional[str] = Field(None, description="Variance Items field")  # noqa: F841
    total_variance_value: Optional[str] = Field(None, description="Total Variance Value field")  # noqa: F841
    created_by: Optional[str] = Field(None, description="Created By Rel field")  # noqa: F841
    assigned_to: Optional[str] = Field(None, description="Assigned To Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CycleCountItemSchema(BaseModel):  # noqa: F841
    """Pydantic schema for CycleCountItem"""  # noqa: F841
    count_number: Optional[int] = Field(None, description="Cycle Count Rel.Count Number field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    system_quantity: Optional[int] = Field(None, description="System Quantity field")  # noqa: F841
    counted_quantity: Optional[int] = Field(None, description="Counted Quantity field")  # noqa: F841
    variance_quantity: Optional[int] = Field(None, description="Variance Quantity field")  # noqa: F841
    unit_cost: Optional[Decimal] = Field(None, description="Unit Cost field")  # noqa: F841
    variance_value: Optional[str] = Field(None, description="Variance Value field")  # noqa: F841
    is_counted: Optional[bool] = Field(None, description="Is Counted field")  # noqa: F841
    count_date: Optional[datetime] = Field(None, description="Count Date field")  # noqa: F841
    counted_by: Optional[int] = Field(None, description="Counted By Rel field")  # noqa: F841
    variance_reason: Optional[str] = Field(None, description="Variance Reason field")  # noqa: F841
    adjustment_approved: Optional[str] = Field(None, description="Adjustment Approved field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CycleCountItemCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating CycleCountItem"""  # noqa: F841
    count_number: Optional[int] = Field(None, description="Cycle Count Rel.Count Number field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    system_quantity: Optional[int] = Field(None, description="System Quantity field")  # noqa: F841
    counted_quantity: Optional[int] = Field(None, description="Counted Quantity field")  # noqa: F841
    variance_quantity: Optional[int] = Field(None, description="Variance Quantity field")  # noqa: F841
    unit_cost: Optional[Decimal] = Field(None, description="Unit Cost field")  # noqa: F841
    variance_value: Optional[str] = Field(None, description="Variance Value field")  # noqa: F841
    is_counted: Optional[bool] = Field(None, description="Is Counted field")  # noqa: F841
    count_date: Optional[datetime] = Field(None, description="Count Date field")  # noqa: F841
    counted_by: Optional[int] = Field(None, description="Counted By Rel field")  # noqa: F841
    variance_reason: Optional[str] = Field(None, description="Variance Reason field")  # noqa: F841
    adjustment_approved: Optional[str] = Field(None, description="Adjustment Approved field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CycleCountItemUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating CycleCountItem"""  # noqa: F841
    count_number: Optional[int] = Field(None, description="Cycle Count Rel.Count Number field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Part Rel.Description field")  # noqa: F841
    system_quantity: Optional[int] = Field(None, description="System Quantity field")  # noqa: F841
    counted_quantity: Optional[int] = Field(None, description="Counted Quantity field")  # noqa: F841
    variance_quantity: Optional[int] = Field(None, description="Variance Quantity field")  # noqa: F841
    unit_cost: Optional[Decimal] = Field(None, description="Unit Cost field")  # noqa: F841
    variance_value: Optional[str] = Field(None, description="Variance Value field")  # noqa: F841
    is_counted: Optional[bool] = Field(None, description="Is Counted field")  # noqa: F841
    count_date: Optional[datetime] = Field(None, description="Count Date field")  # noqa: F841
    counted_by: Optional[int] = Field(None, description="Counted By Rel field")  # noqa: F841
    variance_reason: Optional[str] = Field(None, description="Variance Reason field")  # noqa: F841
    adjustment_approved: Optional[str] = Field(None, description="Adjustment Approved field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartCategorySchema(BaseModel):  # noqa: F841
    """Pydantic schema for PartCategory"""  # noqa: F841
    code: Optional[str] = Field(None, description="Code field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    code: Optional[str] = Field(None, description="Parent Category Rel.Code field")  # noqa: F841
    abc_code: Optional[str] = Field(None, description="Abc Code field")  # noqa: F841
    count_frequency_days: Optional[int] = Field(None, description="Count Frequency Days field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartCategoryCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PartCategory"""  # noqa: F841
    code: Optional[str] = Field(None, description="Code field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    code: Optional[str] = Field(None, description="Parent Category Rel.Code field")  # noqa: F841
    abc_code: Optional[str] = Field(None, description="Abc Code field")  # noqa: F841
    count_frequency_days: Optional[int] = Field(None, description="Count Frequency Days field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartCategoryUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PartCategory"""  # noqa: F841
    code: Optional[str] = Field(None, description="Code field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    code: Optional[str] = Field(None, description="Parent Category Rel.Code field")  # noqa: F841
    abc_code: Optional[str] = Field(None, description="Abc Code field")  # noqa: F841
    count_frequency_days: Optional[int] = Field(None, description="Count Frequency Days field")  # noqa: F841
    is_active: Optional[bool] = Field(None, description="Is Active field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
  # noqa: F841
# CRUD Endpoints  # noqa: F841
  # noqa: F841
@router.get("/inventory_warehouses", response_model=ApiResponse[List[WarehouseSchema]])  # noqa: F841
async def get_inventory_warehouses(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Warehouse records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Warehouse)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Warehouse, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Warehouse, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Warehouse, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching inventory_warehouses: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_warehouses/{item_id}", response_model=ApiResponse[WarehouseSchema])  # noqa: F841
async def get_inventory_warehouses_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Warehouse by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Warehouse).filter(Warehouse.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Warehouse not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Warehouse: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/inventory_warehouses", response_model=ApiResponse[WarehouseSchema])  # noqa: F841
async def create_inventory_warehouses(  # noqa: F841
    item_data: WarehouseCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Warehouse"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Warehouse(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Warehouse: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/inventory_warehouses/{item_id}", response_model=ApiResponse[WarehouseSchema])  # noqa: F841
async def update_inventory_warehouses(  # noqa: F841
    item_id: int,
    item_data: WarehouseUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Warehouse by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Warehouse).filter(Warehouse.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Warehouse not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Warehouse: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/inventory_warehouses/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_inventory_warehouses(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Warehouse by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Warehouse).filter(Warehouse.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Warehouse not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Warehouse deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Warehouse: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/warehouse_locations", response_model=ApiResponse[List[WarehouseLocationSchema]])  # noqa: F841
async def get_warehouse_locations(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WarehouseLocation records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WarehouseLocation)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WarehouseLocation, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WarehouseLocation, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WarehouseLocation, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching warehouse_locations: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/warehouse_locations/{item_id}", response_model=ApiResponse[WarehouseLocationSchema])  # noqa: F841
async def get_warehouse_locations_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WarehouseLocation by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WarehouseLocation).filter(WarehouseLocation.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WarehouseLocation not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching WarehouseLocation: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/warehouse_locations", response_model=ApiResponse[WarehouseLocationSchema])  # noqa: F841
async def create_warehouse_locations(  # noqa: F841
    item_data: WarehouseLocationCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WarehouseLocation"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WarehouseLocation(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating WarehouseLocation: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/warehouse_locations/{item_id}", response_model=ApiResponse[WarehouseLocationSchema])  # noqa: F841
async def update_warehouse_locations(  # noqa: F841
    item_id: int,
    item_data: WarehouseLocationUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WarehouseLocation by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WarehouseLocation).filter(WarehouseLocation.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WarehouseLocation not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating WarehouseLocation: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/warehouse_locations/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_warehouse_locations(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WarehouseLocation by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WarehouseLocation).filter(WarehouseLocation.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WarehouseLocation not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "WarehouseLocation deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting WarehouseLocation: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_warehouse_parts", response_model=ApiResponse[List[WarehousePartSchema]])  # noqa: F841
async def get_inventory_warehouse_parts(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WarehousePart records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WarehousePart)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WarehousePart, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WarehousePart, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WarehousePart, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching inventory_warehouse_parts: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_warehouse_parts/{item_id}", response_model=ApiResponse[WarehousePartSchema])  # noqa: F841
async def get_inventory_warehouse_parts_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WarehousePart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WarehousePart).filter(WarehousePart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WarehousePart not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching WarehousePart: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/inventory_warehouse_parts", response_model=ApiResponse[WarehousePartSchema])  # noqa: F841
async def create_inventory_warehouse_parts(  # noqa: F841
    item_data: WarehousePartCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WarehousePart"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WarehousePart(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating WarehousePart: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/inventory_warehouse_parts/{item_id}", response_model=ApiResponse[WarehousePartSchema])  # noqa: F841
async def update_inventory_warehouse_parts(  # noqa: F841
    item_id: int,
    item_data: WarehousePartUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WarehousePart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WarehousePart).filter(WarehousePart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WarehousePart not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating WarehousePart: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/inventory_warehouse_parts/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_inventory_warehouse_parts(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WarehousePart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WarehousePart).filter(WarehousePart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WarehousePart not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "WarehousePart deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting WarehousePart: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_movements", response_model=ApiResponse[List[InventoryMovementSchema]])  # noqa: F841
async def get_inventory_movements(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get InventoryMovement records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(InventoryMovement)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(InventoryMovement, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(InventoryMovement, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(InventoryMovement, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching inventory_movements: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_movements/{item_id}", response_model=ApiResponse[InventoryMovementSchema])  # noqa: F841
async def get_inventory_movements_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get InventoryMovement by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(InventoryMovement).filter(InventoryMovement.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="InventoryMovement not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching InventoryMovement: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/inventory_movements", response_model=ApiResponse[InventoryMovementSchema])  # noqa: F841
async def create_inventory_movements(  # noqa: F841
    item_data: InventoryMovementCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new InventoryMovement"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = InventoryMovement(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating InventoryMovement: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/inventory_movements/{item_id}", response_model=ApiResponse[InventoryMovementSchema])  # noqa: F841
async def update_inventory_movements(  # noqa: F841
    item_id: int,
    item_data: InventoryMovementUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update InventoryMovement by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(InventoryMovement).filter(InventoryMovement.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="InventoryMovement not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating InventoryMovement: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/inventory_movements/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_inventory_movements(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete InventoryMovement by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(InventoryMovement).filter(InventoryMovement.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="InventoryMovement not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "InventoryMovement deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting InventoryMovement: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_reservations", response_model=ApiResponse[List[InventoryReservationSchema]])  # noqa: F841
async def get_inventory_reservations(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get InventoryReservation records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(InventoryReservation)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(InventoryReservation, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(InventoryReservation, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(InventoryReservation, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching inventory_reservations: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_reservations/{item_id}", response_model=ApiResponse[InventoryReservationSchema])  # noqa: F841
async def get_inventory_reservations_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get InventoryReservation by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(InventoryReservation).filter(InventoryReservation.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="InventoryReservation not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching InventoryReservation: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/inventory_reservations", response_model=ApiResponse[InventoryReservationSchema])  # noqa: F841
async def create_inventory_reservations(  # noqa: F841
    item_data: InventoryReservationCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new InventoryReservation"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = InventoryReservation(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating InventoryReservation: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/inventory_reservations/{item_id}", response_model=ApiResponse[InventoryReservationSchema])  # noqa: F841
async def update_inventory_reservations(  # noqa: F841
    item_id: int,
    item_data: InventoryReservationUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update InventoryReservation by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(InventoryReservation).filter(InventoryReservation.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="InventoryReservation not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating InventoryReservation: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/inventory_reservations/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_inventory_reservations(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete InventoryReservation by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(InventoryReservation).filter(InventoryReservation.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="InventoryReservation not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "InventoryReservation deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting InventoryReservation: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/cycle_counts", response_model=ApiResponse[List[CycleCountSchema]])  # noqa: F841
async def get_cycle_counts(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CycleCount records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(CycleCount)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(CycleCount, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(CycleCount, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(CycleCount, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching cycle_counts: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/cycle_counts/{item_id}", response_model=ApiResponse[CycleCountSchema])  # noqa: F841
async def get_cycle_counts_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CycleCount by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CycleCount).filter(CycleCount.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CycleCount not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching CycleCount: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/cycle_counts", response_model=ApiResponse[CycleCountSchema])  # noqa: F841
async def create_cycle_counts(  # noqa: F841
    item_data: CycleCountCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new CycleCount"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = CycleCount(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating CycleCount: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/cycle_counts/{item_id}", response_model=ApiResponse[CycleCountSchema])  # noqa: F841
async def update_cycle_counts(  # noqa: F841
    item_id: int,
    item_data: CycleCountUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update CycleCount by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CycleCount).filter(CycleCount.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CycleCount not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating CycleCount: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/cycle_counts/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_cycle_counts(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete CycleCount by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CycleCount).filter(CycleCount.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CycleCount not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "CycleCount deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting CycleCount: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/cycle_count_items", response_model=ApiResponse[List[CycleCountItemSchema]])  # noqa: F841
async def get_cycle_count_items(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CycleCountItem records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(CycleCountItem)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(CycleCountItem, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(CycleCountItem, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(CycleCountItem, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching cycle_count_items: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/cycle_count_items/{item_id}", response_model=ApiResponse[CycleCountItemSchema])  # noqa: F841
async def get_cycle_count_items_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CycleCountItem by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CycleCountItem).filter(CycleCountItem.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CycleCountItem not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching CycleCountItem: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/cycle_count_items", response_model=ApiResponse[CycleCountItemSchema])  # noqa: F841
async def create_cycle_count_items(  # noqa: F841
    item_data: CycleCountItemCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new CycleCountItem"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = CycleCountItem(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating CycleCountItem: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/cycle_count_items/{item_id}", response_model=ApiResponse[CycleCountItemSchema])  # noqa: F841
async def update_cycle_count_items(  # noqa: F841
    item_id: int,
    item_data: CycleCountItemUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update CycleCountItem by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CycleCountItem).filter(CycleCountItem.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CycleCountItem not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating CycleCountItem: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/cycle_count_items/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_cycle_count_items(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete CycleCountItem by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CycleCountItem).filter(CycleCountItem.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CycleCountItem not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "CycleCountItem deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting CycleCountItem: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/part_categories", response_model=ApiResponse[List[PartCategorySchema]])  # noqa: F841
async def get_part_categories(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PartCategory records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PartCategory)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PartCategory, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PartCategory, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PartCategory, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching part_categories: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/part_categories/{item_id}", response_model=ApiResponse[PartCategorySchema])  # noqa: F841
async def get_part_categories_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PartCategory by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PartCategory).filter(PartCategory.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PartCategory not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching PartCategory: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/part_categories", response_model=ApiResponse[PartCategorySchema])  # noqa: F841
async def create_part_categories(  # noqa: F841
    item_data: PartCategoryCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PartCategory"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PartCategory(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating PartCategory: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/part_categories/{item_id}", response_model=ApiResponse[PartCategorySchema])  # noqa: F841
async def update_part_categories(  # noqa: F841
    item_id: int,
    item_data: PartCategoryUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PartCategory by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PartCategory).filter(PartCategory.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PartCategory not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating PartCategory: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/part_categories/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_part_categories(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PartCategory by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PartCategory).filter(PartCategory.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PartCategory not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "PartCategory deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting PartCategory: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841