# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""# noqa: F841
FastAPI Router for Service resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841

# noqa: F841
from datetime import datetime  # noqa: F841
from typing import List, Optional  # noqa: F841

from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841

# noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
from sqlalchemy import asc, desc  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841

# noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841

# noqa: F841
# noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/service", tags=["Service"])  # noqa: F841


# noqa: F841
# Pydantic Schemas  # noqa: F841
class WorkOrderSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WorkOrder"""  # noqa: F841

    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    creator: Optional[str] = Field(None, description="Creator Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    users_sales: Optional[str] = Field(None, description="Users Sales Rel field")  # noqa: F841
    creator_company: Optional[str] = Field(
        None, description="Creator Company Rel field"
    )  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    invoice_approval_req: Optional[str] = Field(
        None, description="Invoice Approval Req field"
    )  # noqa: F841
    date_sent_for_approval: Optional[datetime] = Field(
        None, description="Date Sent For Approval field"
    )  # noqa: F841
    approved_by: Optional[str] = Field(None, description="Approved By Rel field")  # noqa: F841
    is_quote: Optional[bool] = Field(None, description="Is Quote field")  # noqa: F841
    quickbooks_num: Optional[str] = Field(None, description="Quickbooks Num field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status Rel field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    service_type: Optional[str] = Field(None, description="Service Type Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    currency_rel_name: Optional[str] = Field(
        None, description="Currency Rel.Name field"
    )  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WorkOrder"""  # noqa: F841

    creator: Optional[str] = Field(None, description="Creator Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    users_sales: Optional[str] = Field(None, description="Users Sales Rel field")  # noqa: F841
    creator_company: Optional[str] = Field(
        None, description="Creator Company Rel field"
    )  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    invoice_approval_req: Optional[str] = Field(
        None, description="Invoice Approval Req field"
    )  # noqa: F841
    date_sent_for_approval: Optional[datetime] = Field(
        None, description="Date Sent For Approval field"
    )  # noqa: F841
    approved_by: Optional[str] = Field(None, description="Approved By Rel field")  # noqa: F841
    is_quote: Optional[bool] = Field(None, description="Is Quote field")  # noqa: F841
    quickbooks_num: Optional[str] = Field(None, description="Quickbooks Num field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status Rel field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    service_type: Optional[str] = Field(None, description="Service Type Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    currency_rel_name: Optional[str] = Field(
        None, description="Currency Rel.Name field"
    )  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WorkOrder"""  # noqa: F841

    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    creator: Optional[str] = Field(None, description="Creator Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    users_sales: Optional[str] = Field(None, description="Users Sales Rel field")  # noqa: F841
    creator_company: Optional[str] = Field(
        None, description="Creator Company Rel field"
    )  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    invoice_approval_req: Optional[str] = Field(
        None, description="Invoice Approval Req field"
    )  # noqa: F841
    date_sent_for_approval: Optional[datetime] = Field(
        None, description="Date Sent For Approval field"
    )  # noqa: F841
    approved_by: Optional[str] = Field(None, description="Approved By Rel field")  # noqa: F841
    is_quote: Optional[bool] = Field(None, description="Is Quote field")  # noqa: F841
    quickbooks_num: Optional[str] = Field(None, description="Quickbooks Num field")  # noqa: F841
    status: Optional[str] = Field(None, description="Status Rel field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    service_type: Optional[str] = Field(None, description="Service Type Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    currency_rel_name: Optional[str] = Field(
        None, description="Currency Rel.Name field"
    )  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WorkOrder"""  # noqa: F841

    # noqa: F841
    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WorkOrder"""  # noqa: F841

    pass  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WorkOrder"""  # noqa: F841

    # noqa: F841
    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WorkOrder"""  # noqa: F841

    # noqa: F841
    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WorkOrder"""  # noqa: F841

    pass  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WorkOrder"""  # noqa: F841

    # noqa: F841
    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WorkOrder"""  # noqa: F841

    # noqa: F841
    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WorkOrder"""  # noqa: F841

    pass  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WorkOrder"""  # noqa: F841

    # noqa: F841
    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrdersByUnitSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WorkOrdersByUnit"""  # noqa: F841

    work_order_id: Optional[int] = Field(None, description="Work Order Id field")  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Type field")  # noqa: F841
    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structure field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Downhole field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    service_required: Optional[str] = Field(None, description="Service Required field")  # noqa: F841
    work_done: Optional[str] = Field(None, description="Work Done field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    is_warranty_reason: Optional[bool] = Field(
        None, description="Is Warranty Reason field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrdersByUnitCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WorkOrdersByUnit"""  # noqa: F841

    work_order_id: Optional[int] = Field(None, description="Work Order Id field")  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Type field")  # noqa: F841
    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structure field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Downhole field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    service_required: Optional[str] = Field(None, description="Service Required field")  # noqa: F841
    work_done: Optional[str] = Field(None, description="Work Done field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    is_warranty_reason: Optional[bool] = Field(
        None, description="Is Warranty Reason field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrdersByUnitUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WorkOrdersByUnit"""  # noqa: F841

    work_order_id: Optional[int] = Field(None, description="Work Order Id field")  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Type field")  # noqa: F841
    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structure field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Downhole field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    service_required: Optional[str] = Field(None, description="Service Required field")  # noqa: F841
    work_done: Optional[str] = Field(None, description="Work Done field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    is_warranty_reason: Optional[bool] = Field(
        None, description="Is Warranty Reason field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderPartSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WorkOrderPart"""  # noqa: F841

    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    work_orders: Optional[str] = Field(None, description="Work Orders Rel field")  # noqa: F841
    quickbooks_num: Optional[str] = Field(None, description="Quickbooks Num field")  # noqa: F841
    status_rel: Optional[str] = Field(
        None, description="Work Orders Rel.Status Rel field"
    )  # noqa: F841
    status_rel_name: Optional[str] = Field(
        None, description="Work Orders Rel.Status Rel.Name field"
    )  # noqa: F841
    invoice_approval_req: Optional[str] = Field(
        None, description="Invoice Approval Req field"
    )  # noqa: F841
    approved_by: Optional[str] = Field(None, description="Approved By Rel field")  # noqa: F841
    creator: Optional[str] = Field(None, description="Creator Rel field")  # noqa: F841
    field_tech: Optional[str] = Field(None, description="Field Tech Rel field")  # noqa: F841
    sales_person: Optional[str] = Field(None, description="Sales Person Rel field")  # noqa: F841
    customers: Optional[str] = Field(
        None, description="Work Orders Rel.Customers Rel.Customer field"
    )  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    invoice_summary: Optional[str] = Field(None, description="Invoice Summary field")  # noqa: F841
    work_done: Optional[str] = Field(
        None, description="Work Orders Rel.Work Done field"
    )  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_id: Optional[int] = Field(None, description="Part Id field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    structure_str: Optional[str] = Field(
        None, description="Structures Rel.Structure Str field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderPartCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WorkOrderPart"""  # noqa: F841

    work_orders: Optional[str] = Field(None, description="Work Orders Rel field")  # noqa: F841
    quickbooks_num: Optional[str] = Field(None, description="Quickbooks Num field")  # noqa: F841
    status_rel: Optional[str] = Field(
        None, description="Work Orders Rel.Status Rel field"
    )  # noqa: F841
    status_rel_name: Optional[str] = Field(
        None, description="Work Orders Rel.Status Rel.Name field"
    )  # noqa: F841
    invoice_approval_req: Optional[str] = Field(
        None, description="Invoice Approval Req field"
    )  # noqa: F841
    approved_by: Optional[str] = Field(None, description="Approved By Rel field")  # noqa: F841
    creator: Optional[str] = Field(None, description="Creator Rel field")  # noqa: F841
    field_tech: Optional[str] = Field(None, description="Field Tech Rel field")  # noqa: F841
    sales_person: Optional[str] = Field(None, description="Sales Person Rel field")  # noqa: F841
    customers: Optional[str] = Field(
        None, description="Work Orders Rel.Customers Rel.Customer field"
    )  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    invoice_summary: Optional[str] = Field(None, description="Invoice Summary field")  # noqa: F841
    work_done: Optional[str] = Field(
        None, description="Work Orders Rel.Work Done field"
    )  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_id: Optional[int] = Field(None, description="Part Id field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    structure_str: Optional[str] = Field(
        None, description="Structures Rel.Structure Str field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderPartUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WorkOrderPart"""  # noqa: F841

    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    work_orders: Optional[str] = Field(None, description="Work Orders Rel field")  # noqa: F841
    quickbooks_num: Optional[str] = Field(None, description="Quickbooks Num field")  # noqa: F841
    status_rel: Optional[str] = Field(
        None, description="Work Orders Rel.Status Rel field"
    )  # noqa: F841
    status_rel_name: Optional[str] = Field(
        None, description="Work Orders Rel.Status Rel.Name field"
    )  # noqa: F841
    invoice_approval_req: Optional[str] = Field(
        None, description="Invoice Approval Req field"
    )  # noqa: F841
    approved_by: Optional[str] = Field(None, description="Approved By Rel field")  # noqa: F841
    creator: Optional[str] = Field(None, description="Creator Rel field")  # noqa: F841
    field_tech: Optional[str] = Field(None, description="Field Tech Rel field")  # noqa: F841
    sales_person: Optional[str] = Field(None, description="Sales Person Rel field")  # noqa: F841
    customers: Optional[str] = Field(
        None, description="Work Orders Rel.Customers Rel.Customer field"
    )  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    invoice_summary: Optional[str] = Field(None, description="Invoice Summary field")  # noqa: F841
    work_done: Optional[str] = Field(
        None, description="Work Orders Rel.Work Done field"
    )  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_id: Optional[int] = Field(None, description="Part Id field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    structure_str: Optional[str] = Field(
        None, description="Structures Rel.Structure Str field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ContactFormSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ContactForm"""  # noqa: F841

    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    message: Optional[str] = Field(None, description="Message field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Last Name field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Phone field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ContactFormCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ContactForm"""  # noqa: F841

    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    message: Optional[str] = Field(None, description="Message field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Last Name field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Phone field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ContactFormUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ContactForm"""  # noqa: F841

    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    message: Optional[str] = Field(None, description="Message field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Last Name field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Phone field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Service"""  # noqa: F841

    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    priority: Optional[str] = Field(None, description="Priority field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_resolved: Optional[bool] = Field(None, description="Is Resolved field")  # noqa: F841
    resolution: Optional[str] = Field(None, description="Resolution field")  # noqa: F841
    operator_name: Optional[str] = Field(None, description="Operator Name field")  # noqa: F841
    operator_phone: Optional[str] = Field(None, description="Operator Phone field")  # noqa: F841
    operator_email: Optional[str] = Field(None, description="Operator Email field")  # noqa: F841
    service_type_rel_name: Optional[str] = Field(
        None, description="Service Type Rel.Name field"
    )  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Structures Rel.Power Unit Str field"
    )  # noqa: F841
    work_orders_rel_id: Optional[str] = Field(
        None, description="Work Orders Rel.Id field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Service"""  # noqa: F841

    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    priority: Optional[str] = Field(None, description="Priority field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_resolved: Optional[bool] = Field(None, description="Is Resolved field")  # noqa: F841
    resolution: Optional[str] = Field(None, description="Resolution field")  # noqa: F841
    operator_name: Optional[str] = Field(None, description="Operator Name field")  # noqa: F841
    operator_phone: Optional[str] = Field(None, description="Operator Phone field")  # noqa: F841
    operator_email: Optional[str] = Field(None, description="Operator Email field")  # noqa: F841
    service_type_rel_name: Optional[str] = Field(
        None, description="Service Type Rel.Name field"
    )  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Structures Rel.Power Unit Str field"
    )  # noqa: F841
    work_orders_rel_id: Optional[str] = Field(
        None, description="Work Orders Rel.Id field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Service"""  # noqa: F841

    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    priority: Optional[str] = Field(None, description="Priority field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_resolved: Optional[bool] = Field(None, description="Is Resolved field")  # noqa: F841
    resolution: Optional[str] = Field(None, description="Resolution field")  # noqa: F841
    operator_name: Optional[str] = Field(None, description="Operator Name field")  # noqa: F841
    operator_phone: Optional[str] = Field(None, description="Operator Phone field")  # noqa: F841
    operator_email: Optional[str] = Field(None, description="Operator Email field")  # noqa: F841
    service_type_rel_name: Optional[str] = Field(
        None, description="Service Type Rel.Name field"
    )  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Structures Rel.Power Unit Str field"
    )  # noqa: F841
    work_orders_rel_id: Optional[str] = Field(
        None, description="Work Orders Rel.Id field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceEmaileeSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ServiceEmailee"""  # noqa: F841

    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceEmaileeCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ServiceEmailee"""  # noqa: F841

    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceEmaileeUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ServiceEmailee"""  # noqa: F841

    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MaintenanceSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Maintenance"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Structures Rel.Power Unit Str field"
    )  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    op_hours: Optional[str] = Field(None, description="Op Hours field")  # noqa: F841
    op_months: Optional[str] = Field(None, description="Op Months field")  # noqa: F841
    maintenance_type_rel_name: Optional[str] = Field(
        None, description="Maintenance Type Rel.Name field"
    )  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MaintenanceCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Maintenance"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Structures Rel.Power Unit Str field"
    )  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    op_hours: Optional[str] = Field(None, description="Op Hours field")  # noqa: F841
    op_months: Optional[str] = Field(None, description="Op Months field")  # noqa: F841
    maintenance_type_rel_name: Optional[str] = Field(
        None, description="Maintenance Type Rel.Name field"
    )  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MaintenanceUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Maintenance"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Structures Rel.Power Unit Str field"
    )  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    op_hours: Optional[str] = Field(None, description="Op Hours field")  # noqa: F841
    op_months: Optional[str] = Field(None, description="Op Months field")  # noqa: F841
    maintenance_type_rel_name: Optional[str] = Field(
        None, description="Maintenance Type Rel.Name field"
    )  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MaintenanceTypeSchema(BaseModel):  # noqa: F841
    """Pydantic schema for MaintenanceType"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MaintenanceTypeCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating MaintenanceType"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MaintenanceTypeUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating MaintenanceType"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceClockSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ServiceClock"""  # noqa: F841

    employee: Optional[str] = Field(None, description="Employee Rel field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structure Rel field")  # noqa: F841
    warehouse: Optional[str] = Field(None, description="Warehouse Rel field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Structure Rel.Power Unit Str field"
    )  # noqa: F841
    surface: Optional[str] = Field(None, description="Structure Rel.Surface field")  # noqa: F841
    timestamp_utc_in: Optional[datetime] = Field(
        None, description="Timestamp Utc In field"
    )  # noqa: F841
    timestamp_utc_out: Optional[datetime] = Field(
        None, description="Timestamp Utc Out field"
    )  # noqa: F841
    time_zone_in: Optional[str] = Field(None, description="Time Zone In Rel field")  # noqa: F841
    time_zone_out: Optional[str] = Field(None, description="Time Zone Out Rel field")  # noqa: F841
    total_hours_worked: Optional[str] = Field(
        None, description="Total Hours Worked field"
    )  # noqa: F841
    gps_lat_in: Optional[str] = Field(None, description="Gps Lat In field")  # noqa: F841
    gps_lon_in: Optional[str] = Field(None, description="Gps Lon In field")  # noqa: F841
    gps_lat_out: Optional[str] = Field(None, description="Gps Lat Out field")  # noqa: F841
    gps_lon_out: Optional[str] = Field(None, description="Gps Lon Out field")  # noqa: F841
    notes_in: Optional[str] = Field(None, description="Notes In field")  # noqa: F841
    notes_out: Optional[str] = Field(None, description="Notes Out field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceClockCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ServiceClock"""  # noqa: F841

    employee: Optional[str] = Field(None, description="Employee Rel field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structure Rel field")  # noqa: F841
    warehouse: Optional[str] = Field(None, description="Warehouse Rel field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Structure Rel.Power Unit Str field"
    )  # noqa: F841
    surface: Optional[str] = Field(None, description="Structure Rel.Surface field")  # noqa: F841
    timestamp_utc_in: Optional[datetime] = Field(
        None, description="Timestamp Utc In field"
    )  # noqa: F841
    timestamp_utc_out: Optional[datetime] = Field(
        None, description="Timestamp Utc Out field"
    )  # noqa: F841
    time_zone_in: Optional[str] = Field(None, description="Time Zone In Rel field")  # noqa: F841
    time_zone_out: Optional[str] = Field(None, description="Time Zone Out Rel field")  # noqa: F841
    total_hours_worked: Optional[str] = Field(
        None, description="Total Hours Worked field"
    )  # noqa: F841
    gps_lat_in: Optional[str] = Field(None, description="Gps Lat In field")  # noqa: F841
    gps_lon_in: Optional[str] = Field(None, description="Gps Lon In field")  # noqa: F841
    gps_lat_out: Optional[str] = Field(None, description="Gps Lat Out field")  # noqa: F841
    gps_lon_out: Optional[str] = Field(None, description="Gps Lon Out field")  # noqa: F841
    notes_in: Optional[str] = Field(None, description="Notes In field")  # noqa: F841
    notes_out: Optional[str] = Field(None, description="Notes Out field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceClockUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ServiceClock"""  # noqa: F841

    employee: Optional[str] = Field(None, description="Employee Rel field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structure Rel field")  # noqa: F841
    warehouse: Optional[str] = Field(None, description="Warehouse Rel field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Structure Rel.Power Unit Str field"
    )  # noqa: F841
    surface: Optional[str] = Field(None, description="Structure Rel.Surface field")  # noqa: F841
    timestamp_utc_in: Optional[datetime] = Field(
        None, description="Timestamp Utc In field"
    )  # noqa: F841
    timestamp_utc_out: Optional[datetime] = Field(
        None, description="Timestamp Utc Out field"
    )  # noqa: F841
    time_zone_in: Optional[str] = Field(None, description="Time Zone In Rel field")  # noqa: F841
    time_zone_out: Optional[str] = Field(None, description="Time Zone Out Rel field")  # noqa: F841
    total_hours_worked: Optional[str] = Field(
        None, description="Total Hours Worked field"
    )  # noqa: F841
    gps_lat_in: Optional[str] = Field(None, description="Gps Lat In field")  # noqa: F841
    gps_lon_in: Optional[str] = Field(None, description="Gps Lon In field")  # noqa: F841
    gps_lat_out: Optional[str] = Field(None, description="Gps Lat Out field")  # noqa: F841
    gps_lon_out: Optional[str] = Field(None, description="Gps Lon Out field")  # noqa: F841
    notes_in: Optional[str] = Field(None, description="Notes In field")  # noqa: F841
    notes_out: Optional[str] = Field(None, description="Notes Out field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceTypeSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ServiceType"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceTypeCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ServiceType"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ServiceTypeUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ServiceType"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderStatusSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WorkOrderStatus"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderStatusCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WorkOrderStatus"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class WorkOrderStatusUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WorkOrderStatus"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class CurrencySchema(BaseModel):  # noqa: F841
    """Pydantic schema for Currency"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    fx_rate_cad_per: Optional[str] = Field(None, description="Fx Rate Cad Per field")  # noqa: F841
    country_name: Optional[int] = Field(
        None, description="Country Rel.Country Name field"
    )  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class CurrencyCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Currency"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    fx_rate_cad_per: Optional[str] = Field(None, description="Fx Rate Cad Per field")  # noqa: F841
    country_name: Optional[int] = Field(
        None, description="Country Rel.Country Name field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class CurrencyUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Currency"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    fx_rate_cad_per: Optional[str] = Field(None, description="Fx Rate Cad Per field")  # noqa: F841
    country_name: Optional[int] = Field(
        None, description="Country Rel.Country Name field"
    )  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
# noqa: F841
# CRUD Endpoints  # noqa: F841
# noqa: F841
@router.get("/work_orders", response_model=ApiResponse[List[WorkOrderSchema]])  # noqa: F841
async def get_work_orders(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get WorkOrder records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WorkOrder)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WorkOrder, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WorkOrder, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WorkOrder, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching work_orders: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/work_orders/{item_id}", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def get_work_orders_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/work_orders", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def create_work_orders(  # noqa: F841
    item_data: WorkOrderCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WorkOrder"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WorkOrder(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/work_orders/{item_id}", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def update_work_orders(  # noqa: F841
    item_id: int, item_data: WorkOrderUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/work_orders/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_orders(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "WorkOrder deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/work_orders_corp", response_model=ApiResponse[List[WorkOrderSchema]])  # noqa: F841
async def get_work_orders_corp(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get WorkOrder records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WorkOrder)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WorkOrder, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WorkOrder, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WorkOrder, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching work_orders_corp: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/work_orders_corp/{item_id}", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def get_work_orders_corp_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/work_orders_corp", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def create_work_orders_corp(  # noqa: F841
    item_data: WorkOrderCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WorkOrder"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WorkOrder(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/work_orders_corp/{item_id}", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def update_work_orders_corp(  # noqa: F841
    item_id: int, item_data: WorkOrderUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/work_orders_corp/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_orders_corp(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "WorkOrder deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/work_order_quotes", response_model=ApiResponse[List[WorkOrderSchema]])  # noqa: F841
async def get_work_order_quotes(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get WorkOrder records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WorkOrder)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WorkOrder, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WorkOrder, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WorkOrder, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching work_order_quotes: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/work_order_quotes/{item_id}", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def get_work_order_quotes_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/work_order_quotes", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def create_work_order_quotes(  # noqa: F841
    item_data: WorkOrderCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WorkOrder"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WorkOrder(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/work_order_quotes/{item_id}", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def update_work_order_quotes(  # noqa: F841
    item_id: int, item_data: WorkOrderUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/work_order_quotes/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_order_quotes(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "WorkOrder deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_order_quotes_corp", response_model=ApiResponse[List[WorkOrderSchema]]
)  # noqa: F841
async def get_work_order_quotes_corp(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get WorkOrder records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WorkOrder)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WorkOrder, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WorkOrder, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WorkOrder, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching work_order_quotes_corp: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_order_quotes_corp/{item_id}", response_model=ApiResponse[WorkOrderSchema]
)  # noqa: F841
async def get_work_order_quotes_corp_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/work_order_quotes_corp", response_model=ApiResponse[WorkOrderSchema])  # noqa: F841
async def create_work_order_quotes_corp(  # noqa: F841
    item_data: WorkOrderCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WorkOrder"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WorkOrder(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/work_order_quotes_corp/{item_id}", response_model=ApiResponse[WorkOrderSchema]
)  # noqa: F841
async def update_work_order_quotes_corp(  # noqa: F841
    item_id: int, item_data: WorkOrderUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/work_order_quotes_corp/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_order_quotes_corp(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrder).filter(WorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrder not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "WorkOrder deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting WorkOrder: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_orders_by_unit", response_model=ApiResponse[List[WorkOrdersByUnitSchema]]
)  # noqa: F841
async def get_work_orders_by_unit(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get WorkOrdersByUnit records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WorkOrdersByUnit)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WorkOrdersByUnit, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WorkOrdersByUnit, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WorkOrdersByUnit, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching work_orders_by_unit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_orders_by_unit/{item_id}", response_model=ApiResponse[WorkOrdersByUnitSchema]
)  # noqa: F841
async def get_work_orders_by_unit_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WorkOrdersByUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrdersByUnit).filter(WorkOrdersByUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrdersByUnit not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching WorkOrdersByUnit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/work_orders_by_unit", response_model=ApiResponse[WorkOrdersByUnitSchema])  # noqa: F841
async def create_work_orders_by_unit(  # noqa: F841
    item_data: WorkOrdersByUnitCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WorkOrdersByUnit"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WorkOrdersByUnit(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating WorkOrdersByUnit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/work_orders_by_unit/{item_id}", response_model=ApiResponse[WorkOrdersByUnitSchema]
)  # noqa: F841
async def update_work_orders_by_unit(  # noqa: F841
    item_id: int,
    item_data: WorkOrdersByUnitUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update WorkOrdersByUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrdersByUnit).filter(WorkOrdersByUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrdersByUnit not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating WorkOrdersByUnit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/work_orders_by_unit/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_orders_by_unit(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WorkOrdersByUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrdersByUnit).filter(WorkOrdersByUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrdersByUnit not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {
                "message": "WorkOrdersByUnit deleted successfully",
                "id": item_id,
            }
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting WorkOrdersByUnit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/work_order_parts", response_model=ApiResponse[List[WorkOrderPartSchema]])  # noqa: F841
async def get_work_order_parts(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get WorkOrderPart records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WorkOrderPart)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WorkOrderPart, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WorkOrderPart, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WorkOrderPart, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching work_order_parts: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_order_parts/{item_id}", response_model=ApiResponse[WorkOrderPartSchema]
)  # noqa: F841
async def get_work_order_parts_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WorkOrderPart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrderPart).filter(WorkOrderPart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrderPart not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching WorkOrderPart: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/work_order_parts", response_model=ApiResponse[WorkOrderPartSchema])  # noqa: F841
async def create_work_order_parts(  # noqa: F841
    item_data: WorkOrderPartCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WorkOrderPart"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WorkOrderPart(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating WorkOrderPart: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/work_order_parts/{item_id}", response_model=ApiResponse[WorkOrderPartSchema]
)  # noqa: F841
async def update_work_order_parts(  # noqa: F841
    item_id: int,
    item_data: WorkOrderPartUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update WorkOrderPart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrderPart).filter(WorkOrderPart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrderPart not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating WorkOrderPart: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/work_order_parts/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_order_parts(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WorkOrderPart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrderPart).filter(WorkOrderPart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrderPart not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "WorkOrderPart deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting WorkOrderPart: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/contact_form", response_model=ApiResponse[List[ContactFormSchema]])  # noqa: F841
async def get_contact_form(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get ContactForm records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ContactForm)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ContactForm, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ContactForm, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ContactForm, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching contact_form: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/contact_form/{item_id}", response_model=ApiResponse[ContactFormSchema])  # noqa: F841
async def get_contact_form_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ContactForm by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ContactForm).filter(ContactForm.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ContactForm not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching ContactForm: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/contact_form", response_model=ApiResponse[ContactFormSchema])  # noqa: F841
async def create_contact_form(  # noqa: F841
    item_data: ContactFormCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ContactForm"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ContactForm(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating ContactForm: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/contact_form/{item_id}", response_model=ApiResponse[ContactFormSchema])  # noqa: F841
async def update_contact_form(  # noqa: F841
    item_id: int,
    item_data: ContactFormUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update ContactForm by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ContactForm).filter(ContactForm.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ContactForm not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating ContactForm: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/contact_form/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_contact_form(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ContactForm by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ContactForm).filter(ContactForm.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ContactForm not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "ContactForm deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting ContactForm: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/service_requests", response_model=ApiResponse[List[ServiceSchema]])  # noqa: F841
async def get_service_requests(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get Service records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Service)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Service, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Service, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Service, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching service_requests: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/service_requests/{item_id}", response_model=ApiResponse[ServiceSchema])  # noqa: F841
async def get_service_requests_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Service by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Service).filter(Service.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Service not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Service: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/service_requests", response_model=ApiResponse[ServiceSchema])  # noqa: F841
async def create_service_requests(  # noqa: F841
    item_data: ServiceCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Service"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Service(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Service: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/service_requests/{item_id}", response_model=ApiResponse[ServiceSchema])  # noqa: F841
async def update_service_requests(  # noqa: F841
    item_id: int, item_data: ServiceUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Service by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Service).filter(Service.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Service not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Service: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/service_requests/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_service_requests(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Service by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Service).filter(Service.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Service not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "Service deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Service: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/service_request_email_recipients",
    response_model=ApiResponse[List[ServiceEmaileeSchema]],
)  # noqa: F841
async def get_service_request_email_recipients(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get ServiceEmailee records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ServiceEmailee)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ServiceEmailee, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ServiceEmailee, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ServiceEmailee, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching service_request_email_recipients: {str(e)}",
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/service_request_email_recipients/{item_id}",
    response_model=ApiResponse[ServiceEmaileeSchema],
)  # noqa: F841
async def get_service_request_email_recipients_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ServiceEmailee by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceEmailee).filter(ServiceEmailee.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceEmailee not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching ServiceEmailee: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post(
    "/service_request_email_recipients",
    response_model=ApiResponse[ServiceEmaileeSchema],
)  # noqa: F841
async def create_service_request_email_recipients(  # noqa: F841
    item_data: ServiceEmaileeCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ServiceEmailee"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ServiceEmailee(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating ServiceEmailee: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/service_request_email_recipients/{item_id}",
    response_model=ApiResponse[ServiceEmaileeSchema],
)  # noqa: F841
async def update_service_request_email_recipients(  # noqa: F841
    item_id: int,
    item_data: ServiceEmaileeUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update ServiceEmailee by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceEmailee).filter(ServiceEmailee.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceEmailee not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating ServiceEmailee: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete(
    "/service_request_email_recipients/{item_id}", response_model=ApiResponse[dict]
)  # noqa: F841
async def delete_service_request_email_recipients(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ServiceEmailee by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceEmailee).filter(ServiceEmailee.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceEmailee not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "ServiceEmailee deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting ServiceEmailee: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/maintenance", response_model=ApiResponse[List[MaintenanceSchema]])  # noqa: F841
async def get_maintenance(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get Maintenance records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Maintenance)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Maintenance, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Maintenance, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Maintenance, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching maintenance: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/maintenance/{item_id}", response_model=ApiResponse[MaintenanceSchema])  # noqa: F841
async def get_maintenance_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Maintenance by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Maintenance).filter(Maintenance.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Maintenance not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching Maintenance: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/maintenance", response_model=ApiResponse[MaintenanceSchema])  # noqa: F841
async def create_maintenance(  # noqa: F841
    item_data: MaintenanceCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Maintenance"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Maintenance(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating Maintenance: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/maintenance/{item_id}", response_model=ApiResponse[MaintenanceSchema])  # noqa: F841
async def update_maintenance(  # noqa: F841
    item_id: int,
    item_data: MaintenanceUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update Maintenance by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Maintenance).filter(Maintenance.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Maintenance not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating Maintenance: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/maintenance/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_maintenance(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Maintenance by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Maintenance).filter(Maintenance.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Maintenance not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "Maintenance deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting Maintenance: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/maintenance_types", response_model=ApiResponse[List[MaintenanceTypeSchema]]
)  # noqa: F841
async def get_maintenance_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get MaintenanceType records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(MaintenanceType)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(MaintenanceType, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(MaintenanceType, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(MaintenanceType, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching maintenance_types: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/maintenance_types/{item_id}", response_model=ApiResponse[MaintenanceTypeSchema]
)  # noqa: F841
async def get_maintenance_types_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get MaintenanceType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MaintenanceType).filter(MaintenanceType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MaintenanceType not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching MaintenanceType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/maintenance_types", response_model=ApiResponse[MaintenanceTypeSchema])  # noqa: F841
async def create_maintenance_types(  # noqa: F841
    item_data: MaintenanceTypeCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new MaintenanceType"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = MaintenanceType(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating MaintenanceType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/maintenance_types/{item_id}", response_model=ApiResponse[MaintenanceTypeSchema]
)  # noqa: F841
async def update_maintenance_types(  # noqa: F841
    item_id: int,
    item_data: MaintenanceTypeUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update MaintenanceType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MaintenanceType).filter(MaintenanceType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MaintenanceType not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating MaintenanceType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/maintenance_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_maintenance_types(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete MaintenanceType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MaintenanceType).filter(MaintenanceType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MaintenanceType not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "MaintenanceType deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting MaintenanceType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/service_clock", response_model=ApiResponse[List[ServiceClockSchema]])  # noqa: F841
async def get_service_clock(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Field description"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get ServiceClock records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ServiceClock)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ServiceClock, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ServiceClock, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ServiceClock, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching service_clock: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/service_clock/{item_id}", response_model=ApiResponse[ServiceClockSchema])  # noqa: F841
async def get_service_clock_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ServiceClock by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceClock).filter(ServiceClock.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceClock not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching ServiceClock: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/service_clock", response_model=ApiResponse[ServiceClockSchema])  # noqa: F841
async def create_service_clock(  # noqa: F841
    item_data: ServiceClockCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ServiceClock"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ServiceClock(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating ServiceClock: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/service_clock/{item_id}", response_model=ApiResponse[ServiceClockSchema])  # noqa: F841
async def update_service_clock(  # noqa: F841
    item_id: int,
    item_data: ServiceClockUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update ServiceClock by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceClock).filter(ServiceClock.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceClock not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating ServiceClock: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/service_clock/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_service_clock(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ServiceClock by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceClock).filter(ServiceClock.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceClock not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "ServiceClock deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting ServiceClock: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_order_service_type", response_model=ApiResponse[List[ServiceTypeSchema]]
)  # noqa: F841
async def get_work_order_service_type(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get ServiceType records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ServiceType)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ServiceType, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ServiceType, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ServiceType, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching work_order_service_type: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_order_service_type/{item_id}", response_model=ApiResponse[ServiceTypeSchema]
)  # noqa: F841
async def get_work_order_service_type_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ServiceType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceType).filter(ServiceType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceType not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching ServiceType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/work_order_service_type", response_model=ApiResponse[ServiceTypeSchema])  # noqa: F841
async def create_work_order_service_type(  # noqa: F841
    item_data: ServiceTypeCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ServiceType"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ServiceType(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating ServiceType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/work_order_service_type/{item_id}", response_model=ApiResponse[ServiceTypeSchema]
)  # noqa: F841
async def update_work_order_service_type(  # noqa: F841
    item_id: int,
    item_data: ServiceTypeUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update ServiceType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceType).filter(ServiceType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceType not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating ServiceType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/work_order_service_type/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_order_service_type(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ServiceType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ServiceType).filter(ServiceType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ServiceType not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "ServiceType deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting ServiceType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_order_status", response_model=ApiResponse[List[WorkOrderStatusSchema]]
)  # noqa: F841
async def get_work_order_status(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get WorkOrderStatus records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WorkOrderStatus)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WorkOrderStatus, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WorkOrderStatus, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WorkOrderStatus, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching work_order_status: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/work_order_status/{item_id}", response_model=ApiResponse[WorkOrderStatusSchema]
)  # noqa: F841
async def get_work_order_status_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WorkOrderStatus by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrderStatus).filter(WorkOrderStatus.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrderStatus not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching WorkOrderStatus: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/work_order_status", response_model=ApiResponse[WorkOrderStatusSchema])  # noqa: F841
async def create_work_order_status(  # noqa: F841
    item_data: WorkOrderStatusCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WorkOrderStatus"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WorkOrderStatus(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating WorkOrderStatus: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/work_order_status/{item_id}", response_model=ApiResponse[WorkOrderStatusSchema]
)  # noqa: F841
async def update_work_order_status(  # noqa: F841
    item_id: int,
    item_data: WorkOrderStatusUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update WorkOrderStatus by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrderStatus).filter(WorkOrderStatus.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrderStatus not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating WorkOrderStatus: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/work_order_status/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_order_status(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WorkOrderStatus by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WorkOrderStatus).filter(WorkOrderStatus.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WorkOrderStatus not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "WorkOrderStatus deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting WorkOrderStatus: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/currencies", response_model=ApiResponse[List[CurrencySchema]])  # noqa: F841
async def get_currencies(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get Currency records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Currency)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Currency, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Currency, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Currency, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching currencies: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/currencies/{item_id}", response_model=ApiResponse[CurrencySchema])  # noqa: F841
async def get_currencies_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Currency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Currency).filter(Currency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Currency not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching Currency: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/currencies", response_model=ApiResponse[CurrencySchema])  # noqa: F841
async def create_currencies(  # noqa: F841
    item_data: CurrencyCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Currency"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Currency(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating Currency: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/currencies/{item_id}", response_model=ApiResponse[CurrencySchema])  # noqa: F841
async def update_currencies(  # noqa: F841
    item_id: int, item_data: CurrencyUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Currency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Currency).filter(Currency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Currency not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating Currency: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/currencies/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_currencies(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Currency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Currency).filter(Currency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Currency not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "Currency deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting Currency: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
