# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""  # noqa: F841
FastAPI Router for Build resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841
  # noqa: F841
from typing import List  # noqa: F841
from pydantic import BaseModel  # noqa: F841
from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841
from sqlalchemy import desc, asc  # noqa: F841
  # noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841
  # noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
  # noqa: F841
  # noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/build", tags=["Build"])  # noqa: F841
  # noqa: F841
# Pydantic Schemas  # noqa: F841
class ModelTypeOptionSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ModelTypeOption"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ModelTypeOptionCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ModelTypeOption"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ModelTypeOptionUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ModelTypeOption"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeOptionSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PowerUnitTypeOption"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeOptionCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PowerUnitTypeOption"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeOptionUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PowerUnitTypeOption"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeSpeedSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PowerUnitTypeSpeed"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeSpeedCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PowerUnitTypeSpeed"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeSpeedUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PowerUnitTypeSpeed"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypePowerSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PowerUnitTypePower"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypePowerCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PowerUnitTypePower"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypePowerUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PowerUnitTypePower"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeVoltageSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PowerUnitTypeVoltage"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeVoltageCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PowerUnitTypeVoltage"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeVoltageUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PowerUnitTypeVoltage"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
  # noqa: F841
# CRUD Endpoints  # noqa: F841
  # noqa: F841
@router.get("/pump_top_options", response_model=ApiResponse[List[ModelTypeOptionSchema]])  # noqa: F841
async def get_pump_top_options(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ModelTypeOption records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ModelTypeOption)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ModelTypeOption, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ModelTypeOption, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ModelTypeOption, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching pump_top_options: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/pump_top_options/{item_id}", response_model=ApiResponse[ModelTypeOptionSchema])  # noqa: F841
async def get_pump_top_options_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ModelTypeOption by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ModelTypeOption).filter(ModelTypeOption.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ModelTypeOption not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ModelTypeOption: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/pump_top_options", response_model=ApiResponse[ModelTypeOptionSchema])  # noqa: F841
async def create_pump_top_options(  # noqa: F841
    item_data: ModelTypeOptionCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ModelTypeOption"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ModelTypeOption(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ModelTypeOption: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/pump_top_options/{item_id}", response_model=ApiResponse[ModelTypeOptionSchema])  # noqa: F841
async def update_pump_top_options(  # noqa: F841
    item_id: int,
    item_data: ModelTypeOptionUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ModelTypeOption by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ModelTypeOption).filter(ModelTypeOption.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ModelTypeOption not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ModelTypeOption: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/pump_top_options/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_pump_top_options(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ModelTypeOption by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ModelTypeOption).filter(ModelTypeOption.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ModelTypeOption not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ModelTypeOption deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ModelTypeOption: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_options", response_model=ApiResponse[List[PowerUnitTypeOptionSchema]])  # noqa: F841
async def get_power_unit_options(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitTypeOption records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PowerUnitTypeOption)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PowerUnitTypeOption, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PowerUnitTypeOption, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PowerUnitTypeOption, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching power_unit_options: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_options/{item_id}", response_model=ApiResponse[PowerUnitTypeOptionSchema])  # noqa: F841
async def get_power_unit_options_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitTypeOption by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeOption).filter(PowerUnitTypeOption.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeOption not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching PowerUnitTypeOption: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/power_unit_options", response_model=ApiResponse[PowerUnitTypeOptionSchema])  # noqa: F841
async def create_power_unit_options(  # noqa: F841
    item_data: PowerUnitTypeOptionCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PowerUnitTypeOption"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PowerUnitTypeOption(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating PowerUnitTypeOption: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/power_unit_options/{item_id}", response_model=ApiResponse[PowerUnitTypeOptionSchema])  # noqa: F841
async def update_power_unit_options(  # noqa: F841
    item_id: int,
    item_data: PowerUnitTypeOptionUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PowerUnitTypeOption by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeOption).filter(PowerUnitTypeOption.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeOption not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating PowerUnitTypeOption: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/power_unit_options/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_power_unit_options(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PowerUnitTypeOption by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeOption).filter(PowerUnitTypeOption.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeOption not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "PowerUnitTypeOption deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting PowerUnitTypeOption: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_speeds", response_model=ApiResponse[List[PowerUnitTypeSpeedSchema]])  # noqa: F841
async def get_power_unit_speeds(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitTypeSpeed records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PowerUnitTypeSpeed)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PowerUnitTypeSpeed, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PowerUnitTypeSpeed, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PowerUnitTypeSpeed, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching power_unit_speeds: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_speeds/{item_id}", response_model=ApiResponse[PowerUnitTypeSpeedSchema])  # noqa: F841
async def get_power_unit_speeds_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitTypeSpeed by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeSpeed).filter(PowerUnitTypeSpeed.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeSpeed not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching PowerUnitTypeSpeed: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/power_unit_speeds", response_model=ApiResponse[PowerUnitTypeSpeedSchema])  # noqa: F841
async def create_power_unit_speeds(  # noqa: F841
    item_data: PowerUnitTypeSpeedCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PowerUnitTypeSpeed"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PowerUnitTypeSpeed(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating PowerUnitTypeSpeed: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/power_unit_speeds/{item_id}", response_model=ApiResponse[PowerUnitTypeSpeedSchema])  # noqa: F841
async def update_power_unit_speeds(  # noqa: F841
    item_id: int,
    item_data: PowerUnitTypeSpeedUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PowerUnitTypeSpeed by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeSpeed).filter(PowerUnitTypeSpeed.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeSpeed not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating PowerUnitTypeSpeed: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/power_unit_speeds/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_power_unit_speeds(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PowerUnitTypeSpeed by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeSpeed).filter(PowerUnitTypeSpeed.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeSpeed not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "PowerUnitTypeSpeed deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting PowerUnitTypeSpeed: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_power", response_model=ApiResponse[List[PowerUnitTypePowerSchema]])  # noqa: F841
async def get_power_unit_power(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitTypePower records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PowerUnitTypePower)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PowerUnitTypePower, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PowerUnitTypePower, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PowerUnitTypePower, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching power_unit_power: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_power/{item_id}", response_model=ApiResponse[PowerUnitTypePowerSchema])  # noqa: F841
async def get_power_unit_power_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitTypePower by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypePower).filter(PowerUnitTypePower.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypePower not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching PowerUnitTypePower: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/power_unit_power", response_model=ApiResponse[PowerUnitTypePowerSchema])  # noqa: F841
async def create_power_unit_power(  # noqa: F841
    item_data: PowerUnitTypePowerCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PowerUnitTypePower"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PowerUnitTypePower(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating PowerUnitTypePower: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/power_unit_power/{item_id}", response_model=ApiResponse[PowerUnitTypePowerSchema])  # noqa: F841
async def update_power_unit_power(  # noqa: F841
    item_id: int,
    item_data: PowerUnitTypePowerUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PowerUnitTypePower by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypePower).filter(PowerUnitTypePower.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypePower not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating PowerUnitTypePower: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/power_unit_power/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_power_unit_power(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PowerUnitTypePower by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypePower).filter(PowerUnitTypePower.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypePower not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "PowerUnitTypePower deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting PowerUnitTypePower: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_voltage", response_model=ApiResponse[List[PowerUnitTypeVoltageSchema]])  # noqa: F841
async def get_power_unit_voltage(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitTypeVoltage records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PowerUnitTypeVoltage)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PowerUnitTypeVoltage, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PowerUnitTypeVoltage, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PowerUnitTypeVoltage, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching power_unit_voltage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_voltage/{item_id}", response_model=ApiResponse[PowerUnitTypeVoltageSchema])  # noqa: F841
async def get_power_unit_voltage_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitTypeVoltage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeVoltage).filter(PowerUnitTypeVoltage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeVoltage not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching PowerUnitTypeVoltage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/power_unit_voltage", response_model=ApiResponse[PowerUnitTypeVoltageSchema])  # noqa: F841
async def create_power_unit_voltage(  # noqa: F841
    item_data: PowerUnitTypeVoltageCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PowerUnitTypeVoltage"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PowerUnitTypeVoltage(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating PowerUnitTypeVoltage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/power_unit_voltage/{item_id}", response_model=ApiResponse[PowerUnitTypeVoltageSchema])  # noqa: F841
async def update_power_unit_voltage(  # noqa: F841
    item_id: int,
    item_data: PowerUnitTypeVoltageUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PowerUnitTypeVoltage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeVoltage).filter(PowerUnitTypeVoltage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeVoltage not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating PowerUnitTypeVoltage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/power_unit_voltage/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_power_unit_voltage(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PowerUnitTypeVoltage by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitTypeVoltage).filter(PowerUnitTypeVoltage.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitTypeVoltage not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "PowerUnitTypeVoltage deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting PowerUnitTypeVoltage: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841