# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""# noqa: F841
FastAPI Router for Units resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841

# noqa: F841
from datetime import datetime  # noqa: F841
from typing import List, Optional  # noqa: F841

from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841

# noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
from sqlalchemy import asc, desc  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841

# noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841

# noqa: F841
# noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/units", tags=["Units"])  # noqa: F841


# noqa: F841
# Pydantic Schemas  # noqa: F841
class StructureSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Structure"""  # noqa: F841

    structure: Optional[str] = Field(None, description="Structure field")  # noqa: F841
    power_units: Optional[str] = Field(None, description="Power Units Rel field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Str field"
    )  # noqa: F841
    qb_sale: Optional[str] = Field(None, description="Qb Sale field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model Types Rel.Model field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Types Rel.Unit Type field")  # noqa: F841
    power_unit_type_rel_name: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Type Rel.Name field"
    )  # noqa: F841
    op_months_interval: Optional[str] = Field(
        None, description="Op Months Interval field"
    )  # noqa: F841
    run_mfg_date: Optional[datetime] = Field(None, description="Run Mfg Date field")  # noqa: F841
    structure_install_date: Optional[datetime] = Field(
        None, description="Structure Install Date field"
    )  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    cust_sub_groups: Optional[str] = Field(
        None, description="Cust Sub Groups Rel field"
    )  # noqa: F841
    warehouse: Optional[str] = Field(None, description="Warehouse Rel field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Downhole field")  # noqa: F841
    time_zones: Optional[str] = Field(None, description="Time Zones Rel field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class StructureCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Structure"""  # noqa: F841

    structure: Optional[str] = Field(None, description="Structure field")  # noqa: F841
    power_units: Optional[str] = Field(None, description="Power Units Rel field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Str field"
    )  # noqa: F841
    qb_sale: Optional[str] = Field(None, description="Qb Sale field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model Types Rel.Model field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Types Rel.Unit Type field")  # noqa: F841
    power_unit_type_rel_name: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Type Rel.Name field"
    )  # noqa: F841
    op_months_interval: Optional[str] = Field(
        None, description="Op Months Interval field"
    )  # noqa: F841
    run_mfg_date: Optional[datetime] = Field(None, description="Run Mfg Date field")  # noqa: F841
    structure_install_date: Optional[datetime] = Field(
        None, description="Structure Install Date field"
    )  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    cust_sub_groups: Optional[str] = Field(
        None, description="Cust Sub Groups Rel field"
    )  # noqa: F841
    warehouse: Optional[str] = Field(None, description="Warehouse Rel field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Downhole field")  # noqa: F841
    time_zones: Optional[str] = Field(None, description="Time Zones Rel field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class StructureUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Structure"""  # noqa: F841

    structure: Optional[str] = Field(None, description="Structure field")  # noqa: F841
    power_units: Optional[str] = Field(None, description="Power Units Rel field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Str field"
    )  # noqa: F841
    qb_sale: Optional[str] = Field(None, description="Qb Sale field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model Types Rel.Model field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Types Rel.Unit Type field")  # noqa: F841
    power_unit_type_rel_name: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Type Rel.Name field"
    )  # noqa: F841
    op_months_interval: Optional[str] = Field(
        None, description="Op Months Interval field"
    )  # noqa: F841
    run_mfg_date: Optional[datetime] = Field(None, description="Run Mfg Date field")  # noqa: F841
    structure_install_date: Optional[datetime] = Field(
        None, description="Structure Install Date field"
    )  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    cust_sub_groups: Optional[str] = Field(
        None, description="Cust Sub Groups Rel field"
    )  # noqa: F841
    warehouse: Optional[str] = Field(None, description="Warehouse Rel field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Downhole field")  # noqa: F841
    time_zones: Optional[str] = Field(None, description="Time Zones Rel field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class StructureByModelSchema(BaseModel):  # noqa: F841
    """Pydantic schema for StructureByModel"""  # noqa: F841

    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    most_recent_install_date: Optional[datetime] = Field(
        None, description="Most Recent Install Date field"
    )  # noqa: F841
    total_units: Optional[str] = Field(None, description="Total Units field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class StructureByModelCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating StructureByModel"""  # noqa: F841

    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    most_recent_install_date: Optional[datetime] = Field(
        None, description="Most Recent Install Date field"
    )  # noqa: F841
    total_units: Optional[str] = Field(None, description="Total Units field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class StructureByModelUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating StructureByModel"""  # noqa: F841

    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    most_recent_install_date: Optional[datetime] = Field(
        None, description="Most Recent Install Date field"
    )  # noqa: F841
    total_units: Optional[str] = Field(None, description="Total Units field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PowerUnit"""  # noqa: F841

    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    power_unit_type_rel_name: Optional[str] = Field(
        None, description="Power Unit Type Rel.Name field"
    )  # noqa: F841
    run_mfg_date: Optional[datetime] = Field(None, description="Run Mfg Date field")  # noqa: F841
    website_card_msg: Optional[str] = Field(None, description="Website Card Msg field")  # noqa: F841
    notes_1: Optional[str] = Field(None, description="Structures Rel.Notes 1 field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    model_types: Optional[str] = Field(
        None, description="Structures Rel.Model Types Rel.Model field"
    )  # noqa: F841
    structures: Optional[str] = Field(None, description="Structures Rel field")  # noqa: F841
    gateways: Optional[str] = Field(None, description="Gateways Rel field")  # noqa: F841
    modbus_networks: Optional[str] = Field(
        None, description="Modbus Networks Rel field"
    )  # noqa: F841
    fixed_ip_networks: Optional[str] = Field(
        None, description="Fixed Ip Networks Rel field"
    )  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PowerUnit"""  # noqa: F841

    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    power_unit_type_rel_name: Optional[str] = Field(
        None, description="Power Unit Type Rel.Name field"
    )  # noqa: F841
    run_mfg_date: Optional[datetime] = Field(None, description="Run Mfg Date field")  # noqa: F841
    website_card_msg: Optional[str] = Field(None, description="Website Card Msg field")  # noqa: F841
    notes_1: Optional[str] = Field(None, description="Structures Rel.Notes 1 field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    model_types: Optional[str] = Field(
        None, description="Structures Rel.Model Types Rel.Model field"
    )  # noqa: F841
    structures: Optional[str] = Field(None, description="Structures Rel field")  # noqa: F841
    gateways: Optional[str] = Field(None, description="Gateways Rel field")  # noqa: F841
    modbus_networks: Optional[str] = Field(
        None, description="Modbus Networks Rel field"
    )  # noqa: F841
    fixed_ip_networks: Optional[str] = Field(
        None, description="Fixed Ip Networks Rel field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PowerUnit"""  # noqa: F841

    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    power_unit_type_rel_name: Optional[str] = Field(
        None, description="Power Unit Type Rel.Name field"
    )  # noqa: F841
    run_mfg_date: Optional[datetime] = Field(None, description="Run Mfg Date field")  # noqa: F841
    website_card_msg: Optional[str] = Field(None, description="Website Card Msg field")  # noqa: F841
    notes_1: Optional[str] = Field(None, description="Structures Rel.Notes 1 field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    model_types: Optional[str] = Field(
        None, description="Structures Rel.Model Types Rel.Model field"
    )  # noqa: F841
    structures: Optional[str] = Field(None, description="Structures Rel field")  # noqa: F841
    gateways: Optional[str] = Field(None, description="Gateways Rel field")  # noqa: F841
    modbus_networks: Optional[str] = Field(
        None, description="Modbus Networks Rel field"
    )  # noqa: F841
    fixed_ip_networks: Optional[str] = Field(
        None, description="Fixed Ip Networks Rel field"
    )  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitModbusNetworkSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PowerUnitModbusNetwork"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Power Unit Rel.Power Unit Str field"
    )  # noqa: F841
    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    ip_address: Optional[str] = Field(None, description="Ip Address field")  # noqa: F841
    subnet: Optional[str] = Field(None, description="Subnet field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    never_default: Optional[str] = Field(None, description="Never Default field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitModbusNetworkCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PowerUnitModbusNetwork"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Power Unit Rel.Power Unit Str field"
    )  # noqa: F841
    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    ip_address: Optional[str] = Field(None, description="Ip Address field")  # noqa: F841
    subnet: Optional[str] = Field(None, description="Subnet field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    never_default: Optional[str] = Field(None, description="Never Default field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitModbusNetworkUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PowerUnitModbusNetwork"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Power Unit Rel.Power Unit Str field"
    )  # noqa: F841
    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    ip_address: Optional[str] = Field(None, description="Ip Address field")  # noqa: F841
    subnet: Optional[str] = Field(None, description="Subnet field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    never_default: Optional[str] = Field(None, description="Never Default field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitFixedIPNetworkSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PowerUnitFixedIPNetwork"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Power Unit Rel.Power Unit Str field"
    )  # noqa: F841
    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    ip_address: Optional[str] = Field(None, description="Ip Address field")  # noqa: F841
    subnet: Optional[str] = Field(None, description="Subnet field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    never_default: Optional[str] = Field(None, description="Never Default field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitFixedIPNetworkCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PowerUnitFixedIPNetwork"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Power Unit Rel.Power Unit Str field"
    )  # noqa: F841
    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    ip_address: Optional[str] = Field(None, description="Ip Address field")  # noqa: F841
    subnet: Optional[str] = Field(None, description="Subnet field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    never_default: Optional[str] = Field(None, description="Never Default field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class PowerUnitFixedIPNetworkUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PowerUnitFixedIPNetwork"""  # noqa: F841

    power_unit_str: Optional[str] = Field(
        None, description="Power Unit Rel.Power Unit Str field"
    )  # noqa: F841
    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    ip_address: Optional[str] = Field(None, description="Ip Address field")  # noqa: F841
    subnet: Optional[str] = Field(None, description="Subnet field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    never_default: Optional[str] = Field(None, description="Never Default field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Gw"""  # noqa: F841

    gateway_types_rel_name: Optional[str] = Field(
        None, description="Gateway Types Rel.Name field"
    )  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    cust_sub_groups_rel_name: Optional[str] = Field(
        None, description="Cust Sub Groups Rel.Name field"
    )  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    serial_gw: Optional[str] = Field(None, description="Serial Gw field")  # noqa: F841
    model_gw: Optional[str] = Field(None, description="Model Gw field")  # noqa: F841
    imei: Optional[str] = Field(None, description="Imei field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Str field"
    )  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Structures Rel.Downhole field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Structures Rel.Surface field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    test_can_bus: Optional[str] = Field(None, description="Test Can Bus field")  # noqa: F841
    test_cellular: Optional[str] = Field(None, description="Test Cellular field")  # noqa: F841
    test_cellular_user: Optional[str] = Field(
        None, description="Test Cellular User Rel field"
    )  # noqa: F841
    ready_and_working: Optional[str] = Field(
        None, description="Ready And Working field"
    )  # noqa: F841
    location_gw: Optional[str] = Field(None, description="Location Gw field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Gw"""  # noqa: F841

    gateway_types_rel_name: Optional[str] = Field(
        None, description="Gateway Types Rel.Name field"
    )  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    cust_sub_groups_rel_name: Optional[str] = Field(
        None, description="Cust Sub Groups Rel.Name field"
    )  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    serial_gw: Optional[str] = Field(None, description="Serial Gw field")  # noqa: F841
    model_gw: Optional[str] = Field(None, description="Model Gw field")  # noqa: F841
    imei: Optional[str] = Field(None, description="Imei field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Str field"
    )  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Structures Rel.Downhole field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Structures Rel.Surface field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    test_can_bus: Optional[str] = Field(None, description="Test Can Bus field")  # noqa: F841
    test_cellular: Optional[str] = Field(None, description="Test Cellular field")  # noqa: F841
    test_cellular_user: Optional[str] = Field(
        None, description="Test Cellular User Rel field"
    )  # noqa: F841
    ready_and_working: Optional[str] = Field(
        None, description="Ready And Working field"
    )  # noqa: F841
    location_gw: Optional[str] = Field(None, description="Location Gw field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Gw"""  # noqa: F841

    gateway_types_rel_name: Optional[str] = Field(
        None, description="Gateway Types Rel.Name field"
    )  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(
        None, description="Timestamp Utc Inserted field"
    )  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    cust_sub_groups_rel_name: Optional[str] = Field(
        None, description="Cust Sub Groups Rel.Name field"
    )  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateway field")  # noqa: F841
    serial_gw: Optional[str] = Field(None, description="Serial Gw field")  # noqa: F841
    model_gw: Optional[str] = Field(None, description="Model Gw field")  # noqa: F841
    imei: Optional[str] = Field(None, description="Imei field")  # noqa: F841
    power_unit_str: Optional[str] = Field(
        None, description="Power Units Rel.Power Unit Str field"
    )  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    downhole: Optional[str] = Field(None, description="Structures Rel.Downhole field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Structures Rel.Surface field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    test_can_bus: Optional[str] = Field(None, description="Test Can Bus field")  # noqa: F841
    test_cellular: Optional[str] = Field(None, description="Test Cellular field")  # noqa: F841
    test_cellular_user: Optional[str] = Field(
        None, description="Test Cellular User Rel field"
    )  # noqa: F841
    ready_and_working: Optional[str] = Field(
        None, description="Ready And Working field"
    )  # noqa: F841
    location_gw: Optional[str] = Field(None, description="Location Gw field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwInfoSchema(BaseModel):  # noqa: F841
    """Pydantic schema for GwInfo"""  # noqa: F841

    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Unit Str field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    aws_thing: Optional[str] = Field(None, description="Aws Thing field")  # noqa: F841
    time_since_reported: Optional[str] = Field(
        None, description="Time Since Reported field"
    )  # noqa: F841
    connected: Optional[str] = Field(None, description="Connected field")  # noqa: F841
    hyd: Optional[str] = Field(None, description="Hyd field")  # noqa: F841
    warn1: Optional[str] = Field(None, description="Warn1 field")  # noqa: F841
    warn2: Optional[str] = Field(None, description="Warn2 field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours field")  # noqa: F841
    gateway_types_rel_name: Optional[str] = Field(
        None, description="Gateway Types Rel.Name field"
    )  # noqa: F841
    swv_canpy: Optional[str] = Field(None, description="Swv Canpy field")  # noqa: F841
    swv_plc: Optional[str] = Field(None, description="Swv Plc field")  # noqa: F841
    suction_range: Optional[str] = Field(None, description="Suction Range field")  # noqa: F841
    has_slave: Optional[str] = Field(None, description="Has Slave field")  # noqa: F841
    modem_model: Optional[str] = Field(None, description="Modem Model field")  # noqa: F841
    modem_firmware_rev: Optional[str] = Field(
        None, description="Modem Firmware Rev field"
    )  # noqa: F841
    modem_drivers: Optional[str] = Field(None, description="Modem Drivers field")  # noqa: F841
    sim_operator: Optional[str] = Field(None, description="Sim Operator field")  # noqa: F841
    os_name: Optional[str] = Field(None, description="Os Name field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwInfoCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating GwInfo"""  # noqa: F841

    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Unit Str field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    aws_thing: Optional[str] = Field(None, description="Aws Thing field")  # noqa: F841
    time_since_reported: Optional[str] = Field(
        None, description="Time Since Reported field"
    )  # noqa: F841
    connected: Optional[str] = Field(None, description="Connected field")  # noqa: F841
    hyd: Optional[str] = Field(None, description="Hyd field")  # noqa: F841
    warn1: Optional[str] = Field(None, description="Warn1 field")  # noqa: F841
    warn2: Optional[str] = Field(None, description="Warn2 field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours field")  # noqa: F841
    gateway_types_rel_name: Optional[str] = Field(
        None, description="Gateway Types Rel.Name field"
    )  # noqa: F841
    swv_canpy: Optional[str] = Field(None, description="Swv Canpy field")  # noqa: F841
    swv_plc: Optional[str] = Field(None, description="Swv Plc field")  # noqa: F841
    suction_range: Optional[str] = Field(None, description="Suction Range field")  # noqa: F841
    has_slave: Optional[str] = Field(None, description="Has Slave field")  # noqa: F841
    modem_model: Optional[str] = Field(None, description="Modem Model field")  # noqa: F841
    modem_firmware_rev: Optional[str] = Field(
        None, description="Modem Firmware Rev field"
    )  # noqa: F841
    modem_drivers: Optional[str] = Field(None, description="Modem Drivers field")  # noqa: F841
    sim_operator: Optional[str] = Field(None, description="Sim Operator field")  # noqa: F841
    os_name: Optional[str] = Field(None, description="Os Name field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwInfoUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating GwInfo"""  # noqa: F841

    customers_rel: Optional[str] = Field(
        None, description="Power Unit Rel.Customers Rel field"
    )  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Unit Str field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    aws_thing: Optional[str] = Field(None, description="Aws Thing field")  # noqa: F841
    time_since_reported: Optional[str] = Field(
        None, description="Time Since Reported field"
    )  # noqa: F841
    connected: Optional[str] = Field(None, description="Connected field")  # noqa: F841
    hyd: Optional[str] = Field(None, description="Hyd field")  # noqa: F841
    warn1: Optional[str] = Field(None, description="Warn1 field")  # noqa: F841
    warn2: Optional[str] = Field(None, description="Warn2 field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours field")  # noqa: F841
    gateway_types_rel_name: Optional[str] = Field(
        None, description="Gateway Types Rel.Name field"
    )  # noqa: F841
    swv_canpy: Optional[str] = Field(None, description="Swv Canpy field")  # noqa: F841
    swv_plc: Optional[str] = Field(None, description="Swv Plc field")  # noqa: F841
    suction_range: Optional[str] = Field(None, description="Suction Range field")  # noqa: F841
    has_slave: Optional[str] = Field(None, description="Has Slave field")  # noqa: F841
    modem_model: Optional[str] = Field(None, description="Modem Model field")  # noqa: F841
    modem_firmware_rev: Optional[str] = Field(
        None, description="Modem Firmware Rev field"
    )  # noqa: F841
    modem_drivers: Optional[str] = Field(None, description="Modem Drivers field")  # noqa: F841
    sim_operator: Optional[str] = Field(None, description="Sim Operator field")  # noqa: F841
    os_name: Optional[str] = Field(None, description="Os Name field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwNotConnectedDontWorrySchema(BaseModel):  # noqa: F841
    """Pydantic schema for GwNotConnectedDontWorry"""  # noqa: F841

    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    gateway_types_rel: Optional[str] = Field(
        None, description="Gateways Rel.Gateway Types Rel field"
    )  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    am_i_worried: Optional[str] = Field(None, description="Am I Worried field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    test_cellular_user: Optional[str] = Field(
        None, description="Test Cellular User Rel field"
    )  # noqa: F841
    gateway_info_days_since_reported: Optional[str] = Field(
        None, description="Gateway Info Days Since Reported field"
    )  # noqa: F841
    operators_contacted: Optional[str] = Field(
        None, description="Operators Contacted field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwNotConnectedDontWorryCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating GwNotConnectedDontWorry"""  # noqa: F841

    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    gateway_types_rel: Optional[str] = Field(
        None, description="Gateways Rel.Gateway Types Rel field"
    )  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    am_i_worried: Optional[str] = Field(None, description="Am I Worried field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    test_cellular_user: Optional[str] = Field(
        None, description="Test Cellular User Rel field"
    )  # noqa: F841
    gateway_info_days_since_reported: Optional[str] = Field(
        None, description="Gateway Info Days Since Reported field"
    )  # noqa: F841
    operators_contacted: Optional[str] = Field(
        None, description="Operators Contacted field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GwNotConnectedDontWorryUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating GwNotConnectedDontWorry"""  # noqa: F841

    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    gateway_types_rel: Optional[str] = Field(
        None, description="Gateways Rel.Gateway Types Rel field"
    )  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    surface: Optional[str] = Field(None, description="Surface field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    am_i_worried: Optional[str] = Field(None, description="Am I Worried field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    test_cellular_user: Optional[str] = Field(
        None, description="Test Cellular User Rel field"
    )  # noqa: F841
    gateway_info_days_since_reported: Optional[str] = Field(
        None, description="Gateway Info Days Since Reported field"
    )  # noqa: F841
    operators_contacted: Optional[str] = Field(
        None, description="Operators Contacted field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ReleaseNoteSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ReleaseNote"""  # noqa: F841

    version: Optional[str] = Field(None, description="Version field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_stable: Optional[bool] = Field(None, description="Is Stable field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ReleaseNoteCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ReleaseNote"""  # noqa: F841

    version: Optional[str] = Field(None, description="Version field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_stable: Optional[bool] = Field(None, description="Is Stable field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ReleaseNoteUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ReleaseNote"""  # noqa: F841

    version: Optional[str] = Field(None, description="Version field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    is_stable: Optional[bool] = Field(None, description="Is Stable field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MapAbbrevItemSchema(BaseModel):  # noqa: F841
    """Pydantic schema for MapAbbrevItem"""  # noqa: F841

    cob: Optional[str] = Field(None, description="Cob field")  # noqa: F841
    byte_start_index: Optional[str] = Field(None, description="Byte Start Index field")  # noqa: F841
    num_bytes: Optional[str] = Field(None, description="Num Bytes field")  # noqa: F841
    signed: Optional[str] = Field(None, description="Signed field")  # noqa: F841
    data_type: Optional[str] = Field(None, description="Data Type field")  # noqa: F841
    decimals: Optional[str] = Field(None, description="Decimals field")  # noqa: F841
    resolution: Optional[str] = Field(None, description="Resolution field")  # noqa: F841
    offset_: Optional[str] = Field(None, description="Offset  field")  # noqa: F841
    bit_start_index: Optional[str] = Field(None, description="Bit Start Index field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    plus1_variable: Optional[str] = Field(None, description="Plus1 Variable field")  # noqa: F841
    plus1_program: Optional[str] = Field(None, description="Plus1 Program field")  # noqa: F841
    rcom_name: Optional[str] = Field(None, description="Rcom Name field")  # noqa: F841
    rcom_tab: Optional[str] = Field(None, description="Rcom Tab field")  # noqa: F841
    controller_version: Optional[str] = Field(
        None, description="Controller Version field"
    )  # noqa: F841
    machine: Optional[str] = Field(None, description="Machine field")  # noqa: F841
    item: Optional[str] = Field(None, description="Item field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    control_num: Optional[str] = Field(None, description="Control Num field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MapAbbrevItemCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating MapAbbrevItem"""  # noqa: F841

    cob: Optional[str] = Field(None, description="Cob field")  # noqa: F841
    byte_start_index: Optional[str] = Field(None, description="Byte Start Index field")  # noqa: F841
    num_bytes: Optional[str] = Field(None, description="Num Bytes field")  # noqa: F841
    signed: Optional[str] = Field(None, description="Signed field")  # noqa: F841
    data_type: Optional[str] = Field(None, description="Data Type field")  # noqa: F841
    decimals: Optional[str] = Field(None, description="Decimals field")  # noqa: F841
    resolution: Optional[str] = Field(None, description="Resolution field")  # noqa: F841
    offset_: Optional[str] = Field(None, description="Offset  field")  # noqa: F841
    bit_start_index: Optional[str] = Field(None, description="Bit Start Index field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    plus1_variable: Optional[str] = Field(None, description="Plus1 Variable field")  # noqa: F841
    plus1_program: Optional[str] = Field(None, description="Plus1 Program field")  # noqa: F841
    rcom_name: Optional[str] = Field(None, description="Rcom Name field")  # noqa: F841
    rcom_tab: Optional[str] = Field(None, description="Rcom Tab field")  # noqa: F841
    controller_version: Optional[str] = Field(
        None, description="Controller Version field"
    )  # noqa: F841
    machine: Optional[str] = Field(None, description="Machine field")  # noqa: F841
    item: Optional[str] = Field(None, description="Item field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    control_num: Optional[str] = Field(None, description="Control Num field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class MapAbbrevItemUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating MapAbbrevItem"""  # noqa: F841

    cob: Optional[str] = Field(None, description="Cob field")  # noqa: F841
    byte_start_index: Optional[str] = Field(None, description="Byte Start Index field")  # noqa: F841
    num_bytes: Optional[str] = Field(None, description="Num Bytes field")  # noqa: F841
    signed: Optional[str] = Field(None, description="Signed field")  # noqa: F841
    data_type: Optional[str] = Field(None, description="Data Type field")  # noqa: F841
    decimals: Optional[str] = Field(None, description="Decimals field")  # noqa: F841
    resolution: Optional[str] = Field(None, description="Resolution field")  # noqa: F841
    offset_: Optional[str] = Field(None, description="Offset  field")  # noqa: F841
    bit_start_index: Optional[str] = Field(None, description="Bit Start Index field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    plus1_variable: Optional[str] = Field(None, description="Plus1 Variable field")  # noqa: F841
    plus1_program: Optional[str] = Field(None, description="Plus1 Program field")  # noqa: F841
    rcom_name: Optional[str] = Field(None, description="Rcom Name field")  # noqa: F841
    rcom_tab: Optional[str] = Field(None, description="Rcom Tab field")  # noqa: F841
    controller_version: Optional[str] = Field(
        None, description="Controller Version field"
    )  # noqa: F841
    machine: Optional[str] = Field(None, description="Machine field")  # noqa: F841
    item: Optional[str] = Field(None, description="Item field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    control_num: Optional[str] = Field(None, description="Control Num field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ModbusHoldingRegisterSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ModbusHoldingRegister"""  # noqa: F841

    abbrev: Optional[str] = Field(None, description="Abbrev Rel.Abbrev field")  # noqa: F841
    description: Optional[str] = Field(None, description="Abbrev Rel.Description field")  # noqa: F841
    address: Optional[str] = Field(None, description="Address field")  # noqa: F841
    n_registers: Optional[str] = Field(None, description="N Registers field")  # noqa: F841
    holding_reg_40k: Optional[str] = Field(None, description="Holding Reg 40K field")  # noqa: F841
    in_web_api: Optional[str] = Field(None, description="In Web Api field")  # noqa: F841
    writable_modbus: Optional[str] = Field(None, description="Writable Modbus field")  # noqa: F841
    writable_web_api: Optional[str] = Field(None, description="Writable Web Api field")  # noqa: F841
    min_val: Optional[str] = Field(None, description="Min Val field")  # noqa: F841
    max_val: Optional[str] = Field(None, description="Max Val field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ModbusHoldingRegisterCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ModbusHoldingRegister"""  # noqa: F841

    abbrev: Optional[str] = Field(None, description="Abbrev Rel.Abbrev field")  # noqa: F841
    description: Optional[str] = Field(None, description="Abbrev Rel.Description field")  # noqa: F841
    address: Optional[str] = Field(None, description="Address field")  # noqa: F841
    n_registers: Optional[str] = Field(None, description="N Registers field")  # noqa: F841
    holding_reg_40k: Optional[str] = Field(None, description="Holding Reg 40K field")  # noqa: F841
    in_web_api: Optional[str] = Field(None, description="In Web Api field")  # noqa: F841
    writable_modbus: Optional[str] = Field(None, description="Writable Modbus field")  # noqa: F841
    writable_web_api: Optional[str] = Field(None, description="Writable Web Api field")  # noqa: F841
    min_val: Optional[str] = Field(None, description="Min Val field")  # noqa: F841
    max_val: Optional[str] = Field(None, description="Max Val field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class ModbusHoldingRegisterUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ModbusHoldingRegister"""  # noqa: F841

    abbrev: Optional[str] = Field(None, description="Abbrev Rel.Abbrev field")  # noqa: F841
    description: Optional[str] = Field(None, description="Abbrev Rel.Description field")  # noqa: F841
    address: Optional[str] = Field(None, description="Address field")  # noqa: F841
    n_registers: Optional[str] = Field(None, description="N Registers field")  # noqa: F841
    holding_reg_40k: Optional[str] = Field(None, description="Holding Reg 40K field")  # noqa: F841
    in_web_api: Optional[str] = Field(None, description="In Web Api field")  # noqa: F841
    writable_modbus: Optional[str] = Field(None, description="Writable Modbus field")  # noqa: F841
    writable_web_api: Optional[str] = Field(None, description="Writable Web Api field")  # noqa: F841
    min_val: Optional[str] = Field(None, description="Min Val field")  # noqa: F841
    max_val: Optional[str] = Field(None, description="Max Val field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class AlarmLogMetricSchema(BaseModel):  # noqa: F841
    """Pydantic schema for AlarmLogMetric"""  # noqa: F841

    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    index: Optional[str] = Field(None, description="Index field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class AlarmLogMetricCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating AlarmLogMetric"""  # noqa: F841

    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    index: Optional[str] = Field(None, description="Index field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class AlarmLogMetricUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating AlarmLogMetric"""  # noqa: F841

    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    index: Optional[str] = Field(None, description="Index field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class DiagnosticMetricSchema(BaseModel):  # noqa: F841
    """Pydantic schema for DiagnosticMetric"""  # noqa: F841

    diag_num: Optional[str] = Field(None, description="Diag Num field")  # noqa: F841
    decimals: Optional[str] = Field(None, description="Decimals field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    timestamp_utc_modified: Optional[datetime] = Field(
        None, description="Timestamp Utc Modified field"
    )  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class DiagnosticMetricCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating DiagnosticMetric"""  # noqa: F841

    diag_num: Optional[str] = Field(None, description="Diag Num field")  # noqa: F841
    decimals: Optional[str] = Field(None, description="Decimals field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    timestamp_utc_modified: Optional[datetime] = Field(
        None, description="Timestamp Utc Modified field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class DiagnosticMetricUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating DiagnosticMetric"""  # noqa: F841

    diag_num: Optional[str] = Field(None, description="Diag Num field")  # noqa: F841
    decimals: Optional[str] = Field(None, description="Decimals field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    timestamp_utc_modified: Optional[datetime] = Field(
        None, description="Timestamp Utc Modified field"
    )  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class DiagnosticSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Diagnostic"""  # noqa: F841

    power_unit_str: Optional[str] = Field(None, description="Power Unit Str field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    timestamp_utc_modified: Optional[datetime] = Field(
        None, description="Timestamp Utc Modified field"
    )  # noqa: F841
    is_main: Optional[bool] = Field(None, description="Is Main field")  # noqa: F841
    msg_type: Optional[str] = Field(None, description="Msg Type field")  # noqa: F841
    diag_num: Optional[str] = Field(None, description="Diag Num field")  # noqa: F841
    value: Optional[str] = Field(None, description="Value field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class DiagnosticCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Diagnostic"""  # noqa: F841

    power_unit_str: Optional[str] = Field(None, description="Power Unit Str field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    timestamp_utc_modified: Optional[datetime] = Field(
        None, description="Timestamp Utc Modified field"
    )  # noqa: F841
    is_main: Optional[bool] = Field(None, description="Is Main field")  # noqa: F841
    msg_type: Optional[str] = Field(None, description="Msg Type field")  # noqa: F841
    diag_num: Optional[str] = Field(None, description="Diag Num field")  # noqa: F841
    value: Optional[str] = Field(None, description="Value field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class DiagnosticUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Diagnostic"""  # noqa: F841

    power_unit_str: Optional[str] = Field(None, description="Power Unit Str field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    timestamp_utc_modified: Optional[datetime] = Field(
        None, description="Timestamp Utc Modified field"
    )  # noqa: F841
    is_main: Optional[bool] = Field(None, description="Is Main field")  # noqa: F841
    msg_type: Optional[str] = Field(None, description="Msg Type field")  # noqa: F841
    diag_num: Optional[str] = Field(None, description="Diag Num field")  # noqa: F841
    value: Optional[str] = Field(None, description="Value field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class SIMCardSchema(BaseModel):  # noqa: F841
    """Pydantic schema for SIMCard"""  # noqa: F841

    sim_card: Optional[str] = Field(None, description="Sim Card field")  # noqa: F841
    sim_card_num: Optional[str] = Field(None, description="Sim Card Num field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    sim_card_activated: Optional[str] = Field(
        None, description="Sim Card Activated field"
    )  # noqa: F841
    sim_card_phone: Optional[str] = Field(None, description="Sim Card Phone field")  # noqa: F841
    cell_provider: Optional[str] = Field(None, description="Cell Provider field")  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class SIMCardCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating SIMCard"""  # noqa: F841

    sim_card: Optional[str] = Field(None, description="Sim Card field")  # noqa: F841
    sim_card_num: Optional[str] = Field(None, description="Sim Card Num field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    sim_card_activated: Optional[str] = Field(
        None, description="Sim Card Activated field"
    )  # noqa: F841
    sim_card_phone: Optional[str] = Field(None, description="Sim Card Phone field")  # noqa: F841
    cell_provider: Optional[str] = Field(None, description="Cell Provider field")  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class SIMCardUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating SIMCard"""  # noqa: F841

    sim_card: Optional[str] = Field(None, description="Sim Card field")  # noqa: F841
    sim_card_num: Optional[str] = Field(None, description="Sim Card Num field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    gateway: Optional[str] = Field(None, description="Gateways Rel.Gateway field")  # noqa: F841
    sim_card_activated: Optional[str] = Field(
        None, description="Sim Card Activated field"
    )  # noqa: F841
    sim_card_phone: Optional[str] = Field(None, description="Sim Card Phone field")  # noqa: F841
    cell_provider: Optional[str] = Field(None, description="Cell Provider field")  # noqa: F841
    location: Optional[str] = Field(None, description="Location field")  # noqa: F841
    notes: Optional[str] = Field(None, description="Notes field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class CalculatorSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Calculator"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    unit_types_rel: Optional[str] = Field(
        None, description="Model Types Rel.Unit Types Rel field"
    )  # noqa: F841
    power_unit_type: Optional[str] = Field(
        None, description="Power Unit Type Rel field"
    )  # noqa: F841
    diameter: Optional[str] = Field(None, description="Diameter field")  # noqa: F841
    area: Optional[str] = Field(None, description="Area field")  # noqa: F841
    stroke: Optional[str] = Field(None, description="Stroke field")  # noqa: F841
    max_spm: Optional[str] = Field(None, description="Max Spm field")  # noqa: F841
    max_delta_p: Optional[str] = Field(None, description="Max Delta P field")  # noqa: F841
    mawp: Optional[str] = Field(None, description="Mawp field")  # noqa: F841
    rod_size: Optional[str] = Field(None, description="Rod Size field")  # noqa: F841
    motor_hp: Optional[str] = Field(None, description="Motor Hp field")  # noqa: F841
    hyds: Optional[str] = Field(None, description="Hyds field")  # noqa: F841
    max_liquid_m3pd: Optional[str] = Field(None, description="Max Liquid M3Pd field")  # noqa: F841
    min_liquid_m3pd_10pct: Optional[str] = Field(
        None, description="Min Liquid M3Pd 10Pct field"
    )  # noqa: F841
    hyd_size_inch: Optional[str] = Field(None, description="Hyd Size Inch field")  # noqa: F841
    single_loss_cu_inch: Optional[str] = Field(
        None, description="Single Loss Cu Inch field"
    )  # noqa: F841
    port_area_sq_inch: Optional[str] = Field(
        None, description="Port Area Sq Inch field"
    )  # noqa: F841
    single_port_gas_level: Optional[str] = Field(
        None, description="Single Port Gas Level field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class CalculatorCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Calculator"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    unit_types_rel: Optional[str] = Field(
        None, description="Model Types Rel.Unit Types Rel field"
    )  # noqa: F841
    power_unit_type: Optional[str] = Field(
        None, description="Power Unit Type Rel field"
    )  # noqa: F841
    diameter: Optional[str] = Field(None, description="Diameter field")  # noqa: F841
    area: Optional[str] = Field(None, description="Area field")  # noqa: F841
    stroke: Optional[str] = Field(None, description="Stroke field")  # noqa: F841
    max_spm: Optional[str] = Field(None, description="Max Spm field")  # noqa: F841
    max_delta_p: Optional[str] = Field(None, description="Max Delta P field")  # noqa: F841
    mawp: Optional[str] = Field(None, description="Mawp field")  # noqa: F841
    rod_size: Optional[str] = Field(None, description="Rod Size field")  # noqa: F841
    motor_hp: Optional[str] = Field(None, description="Motor Hp field")  # noqa: F841
    hyds: Optional[str] = Field(None, description="Hyds field")  # noqa: F841
    max_liquid_m3pd: Optional[str] = Field(None, description="Max Liquid M3Pd field")  # noqa: F841
    min_liquid_m3pd_10pct: Optional[str] = Field(
        None, description="Min Liquid M3Pd 10Pct field"
    )  # noqa: F841
    hyd_size_inch: Optional[str] = Field(None, description="Hyd Size Inch field")  # noqa: F841
    single_loss_cu_inch: Optional[str] = Field(
        None, description="Single Loss Cu Inch field"
    )  # noqa: F841
    port_area_sq_inch: Optional[str] = Field(
        None, description="Port Area Sq Inch field"
    )  # noqa: F841
    single_port_gas_level: Optional[str] = Field(
        None, description="Single Port Gas Level field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class CalculatorUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Calculator"""  # noqa: F841

    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    unit_types_rel: Optional[str] = Field(
        None, description="Model Types Rel.Unit Types Rel field"
    )  # noqa: F841
    power_unit_type: Optional[str] = Field(
        None, description="Power Unit Type Rel field"
    )  # noqa: F841
    diameter: Optional[str] = Field(None, description="Diameter field")  # noqa: F841
    area: Optional[str] = Field(None, description="Area field")  # noqa: F841
    stroke: Optional[str] = Field(None, description="Stroke field")  # noqa: F841
    max_spm: Optional[str] = Field(None, description="Max Spm field")  # noqa: F841
    max_delta_p: Optional[str] = Field(None, description="Max Delta P field")  # noqa: F841
    mawp: Optional[str] = Field(None, description="Mawp field")  # noqa: F841
    rod_size: Optional[str] = Field(None, description="Rod Size field")  # noqa: F841
    motor_hp: Optional[str] = Field(None, description="Motor Hp field")  # noqa: F841
    hyds: Optional[str] = Field(None, description="Hyds field")  # noqa: F841
    max_liquid_m3pd: Optional[str] = Field(None, description="Max Liquid M3Pd field")  # noqa: F841
    min_liquid_m3pd_10pct: Optional[str] = Field(
        None, description="Min Liquid M3Pd 10Pct field"
    )  # noqa: F841
    hyd_size_inch: Optional[str] = Field(None, description="Hyd Size Inch field")  # noqa: F841
    single_loss_cu_inch: Optional[str] = Field(
        None, description="Single Loss Cu Inch field"
    )  # noqa: F841
    port_area_sq_inch: Optional[str] = Field(
        None, description="Port Area Sq Inch field"
    )  # noqa: F841
    single_port_gas_level: Optional[str] = Field(
        None, description="Single Port Gas Level field"
    )  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GatewayTypeSchema(BaseModel):  # noqa: F841
    """Pydantic schema for GatewayType"""  # noqa: F841

    # noqa: F841
    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GatewayTypeCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating GatewayType"""  # noqa: F841

    pass  # noqa: F841

    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
class GatewayTypeUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating GatewayType"""  # noqa: F841

    # noqa: F841
    # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841


# noqa: F841
# noqa: F841
# CRUD Endpoints  # noqa: F841
# noqa: F841
@router.get("/structures", response_model=ApiResponse[List[StructureSchema]])  # noqa: F841
async def get_structures(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get Structure records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Structure)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Structure, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Structure, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Structure, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching structures: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/structures/{item_id}", response_model=ApiResponse[StructureSchema])  # noqa: F841
async def get_structures_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Structure by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Structure).filter(Structure.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Structure not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching Structure: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/structures", response_model=ApiResponse[StructureSchema])  # noqa: F841
async def create_structures(  # noqa: F841
    item_data: StructureCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Structure"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Structure(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating Structure: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/structures/{item_id}", response_model=ApiResponse[StructureSchema])  # noqa: F841
async def update_structures(  # noqa: F841
    item_id: int, item_data: StructureUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Structure by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Structure).filter(Structure.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Structure not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating Structure: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/structures/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_structures(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Structure by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Structure).filter(Structure.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Structure not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "Structure deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting Structure: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/structures_by_model", response_model=ApiResponse[List[StructureByModelSchema]]
)  # noqa: F841
async def get_structures_by_model(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get StructureByModel records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(StructureByModel)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(StructureByModel, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(StructureByModel, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(StructureByModel, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching structures_by_model: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/structures_by_model/{item_id}", response_model=ApiResponse[StructureByModelSchema]
)  # noqa: F841
async def get_structures_by_model_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get StructureByModel by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(StructureByModel).filter(StructureByModel.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="StructureByModel not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching StructureByModel: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/structures_by_model", response_model=ApiResponse[StructureByModelSchema])  # noqa: F841
async def create_structures_by_model(  # noqa: F841
    item_data: StructureByModelCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new StructureByModel"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = StructureByModel(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating StructureByModel: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/structures_by_model/{item_id}", response_model=ApiResponse[StructureByModelSchema]
)  # noqa: F841
async def update_structures_by_model(  # noqa: F841
    item_id: int,
    item_data: StructureByModelUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update StructureByModel by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(StructureByModel).filter(StructureByModel.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="StructureByModel not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating StructureByModel: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/structures_by_model/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_structures_by_model(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete StructureByModel by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(StructureByModel).filter(StructureByModel.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="StructureByModel not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {
                "message": "StructureByModel deleted successfully",
                "id": item_id,
            }
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting StructureByModel: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/power_units", response_model=ApiResponse[List[PowerUnitSchema]])  # noqa: F841
async def get_power_units(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get PowerUnit records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PowerUnit)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PowerUnit, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PowerUnit, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PowerUnit, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching power_units: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/power_units/{item_id}", response_model=ApiResponse[PowerUnitSchema])  # noqa: F841
async def get_power_units_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnit).filter(PowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnit not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching PowerUnit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/power_units", response_model=ApiResponse[PowerUnitSchema])  # noqa: F841
async def create_power_units(  # noqa: F841
    item_data: PowerUnitCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PowerUnit"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PowerUnit(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating PowerUnit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/power_units/{item_id}", response_model=ApiResponse[PowerUnitSchema])  # noqa: F841
async def update_power_units(  # noqa: F841
    item_id: int, item_data: PowerUnitUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnit).filter(PowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnit not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating PowerUnit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/power_units/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_power_units(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnit).filter(PowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnit not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "PowerUnit deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting PowerUnit: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/power_unit_modbus_network",
    response_model=ApiResponse[List[PowerUnitModbusNetworkSchema]],
)  # noqa: F841
async def get_power_unit_modbus_network(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get PowerUnitModbusNetwork records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PowerUnitModbusNetwork)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PowerUnitModbusNetwork, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PowerUnitModbusNetwork, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PowerUnitModbusNetwork, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching power_unit_modbus_network: {str(e)}",
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/power_unit_modbus_network/{item_id}",
    response_model=ApiResponse[PowerUnitModbusNetworkSchema],
)  # noqa: F841
async def get_power_unit_modbus_network_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitModbusNetwork by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(PowerUnitModbusNetwork)
            .filter(PowerUnitModbusNetwork.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="PowerUnitModbusNetwork not found"
            )  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching PowerUnitModbusNetwork: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post(
    "/power_unit_modbus_network",
    response_model=ApiResponse[PowerUnitModbusNetworkSchema],
)  # noqa: F841
async def create_power_unit_modbus_network(  # noqa: F841
    item_data: PowerUnitModbusNetworkCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PowerUnitModbusNetwork"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PowerUnitModbusNetwork(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating PowerUnitModbusNetwork: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/power_unit_modbus_network/{item_id}",
    response_model=ApiResponse[PowerUnitModbusNetworkSchema],
)  # noqa: F841
async def update_power_unit_modbus_network(  # noqa: F841
    item_id: int,
    item_data: PowerUnitModbusNetworkUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update PowerUnitModbusNetwork by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(PowerUnitModbusNetwork)
            .filter(PowerUnitModbusNetwork.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="PowerUnitModbusNetwork not found"
            )  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating PowerUnitModbusNetwork: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/power_unit_modbus_network/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_power_unit_modbus_network(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PowerUnitModbusNetwork by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(PowerUnitModbusNetwork)
            .filter(PowerUnitModbusNetwork.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="PowerUnitModbusNetwork not found"
            )  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {
                "message": "PowerUnitModbusNetwork deleted successfully",
                "id": item_id,
            }
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting PowerUnitModbusNetwork: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/power_unit_fixed_ip_network",
    response_model=ApiResponse[List[PowerUnitFixedIPNetworkSchema]],
)  # noqa: F841
async def get_power_unit_fixed_ip_network(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get PowerUnitFixedIPNetwork records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PowerUnitFixedIPNetwork)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PowerUnitFixedIPNetwork, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PowerUnitFixedIPNetwork, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PowerUnitFixedIPNetwork, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching power_unit_fixed_ip_network: {str(e)}",
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/power_unit_fixed_ip_network/{item_id}",
    response_model=ApiResponse[PowerUnitFixedIPNetworkSchema],
)  # noqa: F841
async def get_power_unit_fixed_ip_network_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitFixedIPNetwork by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(PowerUnitFixedIPNetwork)
            .filter(PowerUnitFixedIPNetwork.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="PowerUnitFixedIPNetwork not found"
            )  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching PowerUnitFixedIPNetwork: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post(
    "/power_unit_fixed_ip_network",
    response_model=ApiResponse[PowerUnitFixedIPNetworkSchema],
)  # noqa: F841
async def create_power_unit_fixed_ip_network(  # noqa: F841
    item_data: PowerUnitFixedIPNetworkCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PowerUnitFixedIPNetwork"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PowerUnitFixedIPNetwork(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating PowerUnitFixedIPNetwork: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/power_unit_fixed_ip_network/{item_id}",
    response_model=ApiResponse[PowerUnitFixedIPNetworkSchema],
)  # noqa: F841
async def update_power_unit_fixed_ip_network(  # noqa: F841
    item_id: int,
    item_data: PowerUnitFixedIPNetworkUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update PowerUnitFixedIPNetwork by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(PowerUnitFixedIPNetwork)
            .filter(PowerUnitFixedIPNetwork.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="PowerUnitFixedIPNetwork not found"
            )  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating PowerUnitFixedIPNetwork: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete(
    "/power_unit_fixed_ip_network/{item_id}", response_model=ApiResponse[dict]
)  # noqa: F841
async def delete_power_unit_fixed_ip_network(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PowerUnitFixedIPNetwork by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(PowerUnitFixedIPNetwork)
            .filter(PowerUnitFixedIPNetwork.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="PowerUnitFixedIPNetwork not found"
            )  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {
                "message": "PowerUnitFixedIPNetwork deleted successfully",
                "id": item_id,
            }
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting PowerUnitFixedIPNetwork: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/gateways", response_model=ApiResponse[List[GwSchema]])  # noqa: F841
async def get_gateways(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get Gw records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Gw)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Gw, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Gw, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Gw, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching gateways: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/gateways/{item_id}", response_model=ApiResponse[GwSchema])  # noqa: F841
async def get_gateways_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Gw by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Gw).filter(Gw.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Gw not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Gw: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/gateways", response_model=ApiResponse[GwSchema])  # noqa: F841
async def create_gateways(  # noqa: F841
    item_data: GwCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Gw"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Gw(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Gw: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/gateways/{item_id}", response_model=ApiResponse[GwSchema])  # noqa: F841
async def update_gateways(  # noqa: F841
    item_id: int, item_data: GwUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Gw by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Gw).filter(Gw.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Gw not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Gw: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/gateways/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_gateways(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Gw by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Gw).filter(Gw.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Gw not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "Gw deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Gw: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/gateway_info", response_model=ApiResponse[List[GwInfoSchema]])  # noqa: F841
async def get_gateway_info(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Field description"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get GwInfo records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(GwInfo)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(GwInfo, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(GwInfo, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(GwInfo, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching gateway_info: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/gateway_info/{item_id}", response_model=ApiResponse[GwInfoSchema])  # noqa: F841
async def get_gateway_info_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get GwInfo by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(GwInfo).filter(GwInfo.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="GwInfo not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching GwInfo: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/gateway_info", response_model=ApiResponse[GwInfoSchema])  # noqa: F841
async def create_gateway_info(  # noqa: F841
    item_data: GwInfoCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new GwInfo"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = GwInfo(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating GwInfo: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/gateway_info/{item_id}", response_model=ApiResponse[GwInfoSchema])  # noqa: F841
async def update_gateway_info(  # noqa: F841
    item_id: int, item_data: GwInfoUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update GwInfo by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(GwInfo).filter(GwInfo.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="GwInfo not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating GwInfo: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/gateway_info/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_gateway_info(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete GwInfo by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(GwInfo).filter(GwInfo.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="GwInfo not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "GwInfo deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting GwInfo: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/gateways_not_connected",
    response_model=ApiResponse[List[GwNotConnectedDontWorrySchema]],
)  # noqa: F841
async def get_gateways_not_connected(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get GwNotConnectedDontWorry records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(GwNotConnectedDontWorry)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(GwNotConnectedDontWorry, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(GwNotConnectedDontWorry, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(GwNotConnectedDontWorry, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching gateways_not_connected: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/gateways_not_connected/{item_id}",
    response_model=ApiResponse[GwNotConnectedDontWorrySchema],
)  # noqa: F841
async def get_gateways_not_connected_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get GwNotConnectedDontWorry by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(GwNotConnectedDontWorry)
            .filter(GwNotConnectedDontWorry.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="GwNotConnectedDontWorry not found"
            )  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching GwNotConnectedDontWorry: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post(
    "/gateways_not_connected", response_model=ApiResponse[GwNotConnectedDontWorrySchema]
)  # noqa: F841
async def create_gateways_not_connected(  # noqa: F841
    item_data: GwNotConnectedDontWorryCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new GwNotConnectedDontWorry"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = GwNotConnectedDontWorry(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating GwNotConnectedDontWorry: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/gateways_not_connected/{item_id}",
    response_model=ApiResponse[GwNotConnectedDontWorrySchema],
)  # noqa: F841
async def update_gateways_not_connected(  # noqa: F841
    item_id: int,
    item_data: GwNotConnectedDontWorryUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update GwNotConnectedDontWorry by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(GwNotConnectedDontWorry)
            .filter(GwNotConnectedDontWorry.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="GwNotConnectedDontWorry not found"
            )  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating GwNotConnectedDontWorry: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/gateways_not_connected/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_gateways_not_connected(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete GwNotConnectedDontWorry by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(GwNotConnectedDontWorry)
            .filter(GwNotConnectedDontWorry.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="GwNotConnectedDontWorry not found"
            )  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {
                "message": "GwNotConnectedDontWorry deleted successfully",
                "id": item_id,
            }
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting GwNotConnectedDontWorry: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/release_notes", response_model=ApiResponse[List[ReleaseNoteSchema]])  # noqa: F841
async def get_release_notes(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get ReleaseNote records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ReleaseNote)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ReleaseNote, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ReleaseNote, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ReleaseNote, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching release_notes: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/release_notes/{item_id}", response_model=ApiResponse[ReleaseNoteSchema])  # noqa: F841
async def get_release_notes_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReleaseNote by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReleaseNote).filter(ReleaseNote.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReleaseNote not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching ReleaseNote: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/release_notes", response_model=ApiResponse[ReleaseNoteSchema])  # noqa: F841
async def create_release_notes(  # noqa: F841
    item_data: ReleaseNoteCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ReleaseNote"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ReleaseNote(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating ReleaseNote: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/release_notes/{item_id}", response_model=ApiResponse[ReleaseNoteSchema])  # noqa: F841
async def update_release_notes(  # noqa: F841
    item_id: int,
    item_data: ReleaseNoteUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update ReleaseNote by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReleaseNote).filter(ReleaseNote.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReleaseNote not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating ReleaseNote: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/release_notes/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_release_notes(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ReleaseNote by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReleaseNote).filter(ReleaseNote.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReleaseNote not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "ReleaseNote deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting ReleaseNote: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/can_bus_cob_map", response_model=ApiResponse[List[MapAbbrevItemSchema]])  # noqa: F841
async def get_can_bus_cob_map(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get MapAbbrevItem records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(MapAbbrevItem)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(MapAbbrevItem, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(MapAbbrevItem, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(MapAbbrevItem, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching can_bus_cob_map: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/can_bus_cob_map/{item_id}", response_model=ApiResponse[MapAbbrevItemSchema]
)  # noqa: F841
async def get_can_bus_cob_map_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get MapAbbrevItem by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MapAbbrevItem).filter(MapAbbrevItem.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MapAbbrevItem not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching MapAbbrevItem: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/can_bus_cob_map", response_model=ApiResponse[MapAbbrevItemSchema])  # noqa: F841
async def create_can_bus_cob_map(  # noqa: F841
    item_data: MapAbbrevItemCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new MapAbbrevItem"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = MapAbbrevItem(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating MapAbbrevItem: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/can_bus_cob_map/{item_id}", response_model=ApiResponse[MapAbbrevItemSchema]
)  # noqa: F841
async def update_can_bus_cob_map(  # noqa: F841
    item_id: int,
    item_data: MapAbbrevItemUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update MapAbbrevItem by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MapAbbrevItem).filter(MapAbbrevItem.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MapAbbrevItem not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating MapAbbrevItem: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/can_bus_cob_map/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_can_bus_cob_map(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete MapAbbrevItem by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MapAbbrevItem).filter(MapAbbrevItem.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MapAbbrevItem not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "MapAbbrevItem deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting MapAbbrevItem: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/modbus_holding_registers",
    response_model=ApiResponse[List[ModbusHoldingRegisterSchema]],
)  # noqa: F841
async def get_modbus_holding_registers(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    sort_by: Optional[str] = Query(None, description="Sort by field: c, o, l, u, m"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get ModbusHoldingRegister records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ModbusHoldingRegister)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ModbusHoldingRegister, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ModbusHoldingRegister, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ModbusHoldingRegister, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching modbus_holding_registers: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/modbus_holding_registers/{item_id}",
    response_model=ApiResponse[ModbusHoldingRegisterSchema],
)  # noqa: F841
async def get_modbus_holding_registers_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ModbusHoldingRegister by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(ModbusHoldingRegister)
            .filter(ModbusHoldingRegister.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="ModbusHoldingRegister not found"
            )  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching ModbusHoldingRegister: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post(
    "/modbus_holding_registers", response_model=ApiResponse[ModbusHoldingRegisterSchema]
)  # noqa: F841
async def create_modbus_holding_registers(  # noqa: F841
    item_data: ModbusHoldingRegisterCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ModbusHoldingRegister"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ModbusHoldingRegister(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating ModbusHoldingRegister: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/modbus_holding_registers/{item_id}",
    response_model=ApiResponse[ModbusHoldingRegisterSchema],
)  # noqa: F841
async def update_modbus_holding_registers(  # noqa: F841
    item_id: int,
    item_data: ModbusHoldingRegisterUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update ModbusHoldingRegister by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(ModbusHoldingRegister)
            .filter(ModbusHoldingRegister.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="ModbusHoldingRegister not found"
            )  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating ModbusHoldingRegister: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/modbus_holding_registers/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_modbus_holding_registers(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ModbusHoldingRegister by ID"""  # noqa: F841
    try:  # noqa: F841
        item = (
            db.query(ModbusHoldingRegister)
            .filter(ModbusHoldingRegister.id == item_id)
            .first()
        )  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(
                status_code=404, detail="ModbusHoldingRegister not found"
            )  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {
                "message": "ModbusHoldingRegister deleted successfully",
                "id": item_id,
            }
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting ModbusHoldingRegister: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/alarm_log_metrics", response_model=ApiResponse[List[AlarmLogMetricSchema]]
)  # noqa: F841
async def get_alarm_log_metrics(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get AlarmLogMetric records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(AlarmLogMetric)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(AlarmLogMetric, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(AlarmLogMetric, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(AlarmLogMetric, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching alarm_log_metrics: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/alarm_log_metrics/{item_id}", response_model=ApiResponse[AlarmLogMetricSchema]
)  # noqa: F841
async def get_alarm_log_metrics_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlarmLogMetric by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlarmLogMetric).filter(AlarmLogMetric.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlarmLogMetric not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching AlarmLogMetric: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/alarm_log_metrics", response_model=ApiResponse[AlarmLogMetricSchema])  # noqa: F841
async def create_alarm_log_metrics(  # noqa: F841
    item_data: AlarmLogMetricCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new AlarmLogMetric"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = AlarmLogMetric(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating AlarmLogMetric: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/alarm_log_metrics/{item_id}", response_model=ApiResponse[AlarmLogMetricSchema]
)  # noqa: F841
async def update_alarm_log_metrics(  # noqa: F841
    item_id: int,
    item_data: AlarmLogMetricUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update AlarmLogMetric by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlarmLogMetric).filter(AlarmLogMetric.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlarmLogMetric not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating AlarmLogMetric: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/alarm_log_metrics/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_alarm_log_metrics(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete AlarmLogMetric by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlarmLogMetric).filter(AlarmLogMetric.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlarmLogMetric not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "AlarmLogMetric deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting AlarmLogMetric: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/diagnostic_metrics", response_model=ApiResponse[List[DiagnosticMetricSchema]]
)  # noqa: F841
async def get_diagnostic_metrics(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get DiagnosticMetric records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(DiagnosticMetric)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(DiagnosticMetric, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(DiagnosticMetric, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(DiagnosticMetric, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching diagnostic_metrics: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get(
    "/diagnostic_metrics/{item_id}", response_model=ApiResponse[DiagnosticMetricSchema]
)  # noqa: F841
async def get_diagnostic_metrics_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get DiagnosticMetric by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(DiagnosticMetric).filter(DiagnosticMetric.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="DiagnosticMetric not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching DiagnosticMetric: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/diagnostic_metrics", response_model=ApiResponse[DiagnosticMetricSchema])  # noqa: F841
async def create_diagnostic_metrics(  # noqa: F841
    item_data: DiagnosticMetricCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new DiagnosticMetric"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = DiagnosticMetric(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating DiagnosticMetric: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put(
    "/diagnostic_metrics/{item_id}", response_model=ApiResponse[DiagnosticMetricSchema]
)  # noqa: F841
async def update_diagnostic_metrics(  # noqa: F841
    item_id: int,
    item_data: DiagnosticMetricUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update DiagnosticMetric by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(DiagnosticMetric).filter(DiagnosticMetric.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="DiagnosticMetric not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating DiagnosticMetric: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/diagnostic_metrics/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_diagnostic_metrics(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete DiagnosticMetric by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(DiagnosticMetric).filter(DiagnosticMetric.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="DiagnosticMetric not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {
                "message": "DiagnosticMetric deleted successfully",
                "id": item_id,
            }
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting DiagnosticMetric: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/diagnostic_data", response_model=ApiResponse[List[DiagnosticSchema]])  # noqa: F841
async def get_diagnostic_data(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get Diagnostic records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Diagnostic)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Diagnostic, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Diagnostic, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Diagnostic, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching diagnostic_data: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/diagnostic_data/{item_id}", response_model=ApiResponse[DiagnosticSchema])  # noqa: F841
async def get_diagnostic_data_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Diagnostic by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Diagnostic).filter(Diagnostic.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Diagnostic not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching Diagnostic: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/diagnostic_data", response_model=ApiResponse[DiagnosticSchema])  # noqa: F841
async def create_diagnostic_data(  # noqa: F841
    item_data: DiagnosticCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Diagnostic"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Diagnostic(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating Diagnostic: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/diagnostic_data/{item_id}", response_model=ApiResponse[DiagnosticSchema])  # noqa: F841
async def update_diagnostic_data(  # noqa: F841
    item_id: int, item_data: DiagnosticUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Diagnostic by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Diagnostic).filter(Diagnostic.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Diagnostic not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating Diagnostic: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/diagnostic_data/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_diagnostic_data(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Diagnostic by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Diagnostic).filter(Diagnostic.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Diagnostic not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "Diagnostic deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting Diagnostic: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/sim_cards", response_model=ApiResponse[List[SIMCardSchema]])  # noqa: F841
async def get_sim_cards(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get SIMCard records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(SIMCard)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(SIMCard, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(SIMCard, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(SIMCard, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching sim_cards: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/sim_cards/{item_id}", response_model=ApiResponse[SIMCardSchema])  # noqa: F841
async def get_sim_cards_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get SIMCard by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SIMCard).filter(SIMCard.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SIMCard not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching SIMCard: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/sim_cards", response_model=ApiResponse[SIMCardSchema])  # noqa: F841
async def create_sim_cards(  # noqa: F841
    item_data: SIMCardCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new SIMCard"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = SIMCard(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating SIMCard: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/sim_cards/{item_id}", response_model=ApiResponse[SIMCardSchema])  # noqa: F841
async def update_sim_cards(  # noqa: F841
    item_id: int, item_data: SIMCardUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update SIMCard by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SIMCard).filter(SIMCard.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SIMCard not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating SIMCard: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/sim_cards/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_sim_cards(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete SIMCard by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SIMCard).filter(SIMCard.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SIMCard not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "SIMCard deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting SIMCard: {str(e)}")  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/calculator", response_model=ApiResponse[List[CalculatorSchema]])  # noqa: F841
async def get_calculator(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get Calculator records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Calculator)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Calculator, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Calculator, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Calculator, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching calculator: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/calculator/{item_id}", response_model=ApiResponse[CalculatorSchema])  # noqa: F841
async def get_calculator_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Calculator by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Calculator).filter(Calculator.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Calculator not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching Calculator: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/calculator", response_model=ApiResponse[CalculatorSchema])  # noqa: F841
async def create_calculator(  # noqa: F841
    item_data: CalculatorCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Calculator"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Calculator(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating Calculator: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/calculator/{item_id}", response_model=ApiResponse[CalculatorSchema])  # noqa: F841
async def update_calculator(  # noqa: F841
    item_id: int, item_data: CalculatorUpdateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Calculator by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Calculator).filter(Calculator.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Calculator not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating Calculator: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/calculator/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_calculator(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Calculator by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Calculator).filter(Calculator.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Calculator not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {"result": {"message": "Calculator deleted successfully", "id": item_id}}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting Calculator: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/gateway_types", response_model=ApiResponse[List[GatewayTypeSchema]])  # noqa: F841
async def get_gateway_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(
        50, ge=1, le=1000, description="Number of records to return"
    ),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get GatewayType records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(GatewayType)  # noqa: F841
        # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
            # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(GatewayType, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(GatewayType, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(GatewayType, sort_by)))  # noqa: F841
                # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
        # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching gateway_types: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.get("/gateway_types/{item_id}", response_model=ApiResponse[GatewayTypeSchema])  # noqa: F841
async def get_gateway_types_by_id(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get GatewayType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(GatewayType).filter(GatewayType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="GatewayType not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error fetching GatewayType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.post("/gateway_types", response_model=ApiResponse[GatewayTypeSchema])  # noqa: F841
async def create_gateway_types(  # noqa: F841
    item_data: GatewayTypeCreateSchema, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new GatewayType"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = GatewayType(**item_dict)  # noqa: F841
        # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
        # noqa: F841
        return {"result": new_item}  # noqa: F841
    # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error creating GatewayType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.put("/gateway_types/{item_id}", response_model=ApiResponse[GatewayTypeSchema])  # noqa: F841
async def update_gateway_types(  # noqa: F841
    item_id: int,
    item_data: GatewayTypeUpdateSchema,
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Update GatewayType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(GatewayType).filter(GatewayType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="GatewayType not found")  # noqa: F841
            # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
        # noqa: F841
        return {"result": item}  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error updating GatewayType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
@router.delete("/gateway_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_gateway_types(  # noqa: F841
    item_id: int, db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete GatewayType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(GatewayType).filter(GatewayType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="GatewayType not found")  # noqa: F841
            # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
        # noqa: F841
        return {
            "result": {"message": "GatewayType deleted successfully", "id": item_id}
        }  # noqa: F841
    # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(
            status_code=500, detail=f"Error deleting GatewayType: {str(e)}"
        )  # noqa: F841


# noqa: F841
# noqa: F841
