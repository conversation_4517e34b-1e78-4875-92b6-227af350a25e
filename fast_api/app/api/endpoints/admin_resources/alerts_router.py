# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""  # noqa: F841
FastAPI Router for Alerts resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841
  # noqa: F841
from datetime import datetime  # noqa: F841
from typing import List, Optional  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841
from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841
from sqlalchemy import desc, asc  # noqa: F841
  # noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841
  # noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
  # noqa: F841
  # noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/alerts", tags=["Alerts"])  # noqa: F841
  # noqa: F841
# Pydantic Schemas  # noqa: F841
class AlertSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Alert"""  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    power_units: Optional[str] = Field(None, description="Power Units Rel field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Users Rel.Phone field")  # noqa: F841
    wants_sms: Optional[str] = Field(None, description="Wants Sms field")  # noqa: F841
    sms_stop_all: Optional[str] = Field(None, description="Users Rel.Sms Stop All field")  # noqa: F841
    wants_email: Optional[str] = Field(None, description="Wants Email field")  # noqa: F841
    wants_phone: Optional[str] = Field(None, description="Wants Phone field")  # noqa: F841
    wants_whatsapp: Optional[str] = Field(None, description="Wants Whatsapp field")  # noqa: F841
    whatsapp_stop_all: Optional[str] = Field(None, description="Users Rel.Whatsapp Stop All field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Alert"""  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    power_units: Optional[str] = Field(None, description="Power Units Rel field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Users Rel.Phone field")  # noqa: F841
    wants_sms: Optional[str] = Field(None, description="Wants Sms field")  # noqa: F841
    sms_stop_all: Optional[str] = Field(None, description="Users Rel.Sms Stop All field")  # noqa: F841
    wants_email: Optional[str] = Field(None, description="Wants Email field")  # noqa: F841
    wants_phone: Optional[str] = Field(None, description="Wants Phone field")  # noqa: F841
    wants_whatsapp: Optional[str] = Field(None, description="Wants Whatsapp field")  # noqa: F841
    whatsapp_stop_all: Optional[str] = Field(None, description="Users Rel.Whatsapp Stop All field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Alert"""  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    power_units: Optional[str] = Field(None, description="Power Units Rel field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Users Rel.Phone field")  # noqa: F841
    wants_sms: Optional[str] = Field(None, description="Wants Sms field")  # noqa: F841
    sms_stop_all: Optional[str] = Field(None, description="Users Rel.Sms Stop All field")  # noqa: F841
    wants_email: Optional[str] = Field(None, description="Wants Email field")  # noqa: F841
    wants_phone: Optional[str] = Field(None, description="Wants Phone field")  # noqa: F841
    wants_whatsapp: Optional[str] = Field(None, description="Wants Whatsapp field")  # noqa: F841
    whatsapp_stop_all: Optional[str] = Field(None, description="Users Rel.Whatsapp Stop All field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentSchema(BaseModel):  # noqa: F841
    """Pydantic schema for AlertsSent"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    warning_msg: Optional[str] = Field(None, description="Warning Msg field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
    program: Optional[str] = Field(None, description="Program field")  # noqa: F841
    function: Optional[str] = Field(None, description="Function field")  # noqa: F841
    alerts_sent_users: Optional[str] = Field(None, description="Alerts Sent Users Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating AlertsSent"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    warning_msg: Optional[str] = Field(None, description="Warning Msg field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
    program: Optional[str] = Field(None, description="Program field")  # noqa: F841
    function: Optional[str] = Field(None, description="Function field")  # noqa: F841
    alerts_sent_users: Optional[str] = Field(None, description="Alerts Sent Users Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating AlertsSent"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    power_unit: Optional[str] = Field(None, description="Power Unit field")  # noqa: F841
    warning_msg: Optional[str] = Field(None, description="Warning Msg field")  # noqa: F841
    explanation: Optional[str] = Field(None, description="Explanation field")  # noqa: F841
    program: Optional[str] = Field(None, description="Program field")  # noqa: F841
    function: Optional[str] = Field(None, description="Function field")  # noqa: F841
    alerts_sent_users: Optional[str] = Field(None, description="Alerts Sent Users Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertCustomSchema(BaseModel):  # noqa: F841
    """Pydantic schema for AlertCustom"""  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    months: Optional[str] = Field(None, description="Months Rel field")  # noqa: F841
    days: Optional[str] = Field(None, description="Days Rel field")  # noqa: F841
    hour_end: Optional[str] = Field(None, description="Hour End Rel field")  # noqa: F841
    time_zones: Optional[str] = Field(None, description="Time Zones Rel field")  # noqa: F841
    want_email: Optional[str] = Field(None, description="Want Email field")  # noqa: F841
    want_sms: Optional[str] = Field(None, description="Want Sms field")  # noqa: F841
    structures: Optional[str] = Field(None, description="Structures Rel field")  # noqa: F841
    subject: Optional[str] = Field(None, description="Subject field")  # noqa: F841
    body: Optional[str] = Field(None, description="Body field")  # noqa: F841
    images: Optional[str] = Field(None, description="Images Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertCustomCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating AlertCustom"""  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    months: Optional[str] = Field(None, description="Months Rel field")  # noqa: F841
    days: Optional[str] = Field(None, description="Days Rel field")  # noqa: F841
    hour_end: Optional[str] = Field(None, description="Hour End Rel field")  # noqa: F841
    time_zones: Optional[str] = Field(None, description="Time Zones Rel field")  # noqa: F841
    want_email: Optional[str] = Field(None, description="Want Email field")  # noqa: F841
    want_sms: Optional[str] = Field(None, description="Want Sms field")  # noqa: F841
    structures: Optional[str] = Field(None, description="Structures Rel field")  # noqa: F841
    subject: Optional[str] = Field(None, description="Subject field")  # noqa: F841
    body: Optional[str] = Field(None, description="Body field")  # noqa: F841
    images: Optional[str] = Field(None, description="Images Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertCustomUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating AlertCustom"""  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    months: Optional[str] = Field(None, description="Months Rel field")  # noqa: F841
    days: Optional[str] = Field(None, description="Days Rel field")  # noqa: F841
    hour_end: Optional[str] = Field(None, description="Hour End Rel field")  # noqa: F841
    time_zones: Optional[str] = Field(None, description="Time Zones Rel field")  # noqa: F841
    want_email: Optional[str] = Field(None, description="Want Email field")  # noqa: F841
    want_sms: Optional[str] = Field(None, description="Want Sms field")  # noqa: F841
    structures: Optional[str] = Field(None, description="Structures Rel field")  # noqa: F841
    subject: Optional[str] = Field(None, description="Subject field")  # noqa: F841
    body: Optional[str] = Field(None, description="Body field")  # noqa: F841
    images: Optional[str] = Field(None, description="Images Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailHourlySchema(BaseModel):  # noqa: F841
    """Pydantic schema for ReportEmailHourly"""  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours Rel field")  # noqa: F841
    days_of_week: Optional[str] = Field(None, description="Days Of Week Rel field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailHourlyCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ReportEmailHourly"""  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours Rel field")  # noqa: F841
    days_of_week: Optional[str] = Field(None, description="Days Of Week Rel field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailHourlyUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ReportEmailHourly"""  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours Rel field")  # noqa: F841
    days_of_week: Optional[str] = Field(None, description="Days Of Week Rel field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailDeratesSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ReportEmailDerates"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailDeratesCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ReportEmailDerates"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailDeratesUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ReportEmailDerates"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailOpHoursSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ReportEmailOpHours"""  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    types: Optional[str] = Field(None, description="Types Rel field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailOpHoursCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ReportEmailOpHours"""  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    types: Optional[str] = Field(None, description="Types Rel field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailOpHoursUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ReportEmailOpHours"""  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    types: Optional[str] = Field(None, description="Types Rel field")  # noqa: F841
    unit_types: Optional[str] = Field(None, description="Unit Types Rel field")  # noqa: F841
    model_types: Optional[str] = Field(None, description="Model Types Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailInventorySchema(BaseModel):  # noqa: F841
    """Pydantic schema for ReportEmailInventory"""  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    days_of_week: Optional[str] = Field(None, description="Days Of Week Rel field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailInventoryCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ReportEmailInventory"""  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    days_of_week: Optional[str] = Field(None, description="Days Of Week Rel field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ReportEmailInventoryUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ReportEmailInventory"""  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    warehouses: Optional[str] = Field(None, description="Warehouses Rel field")  # noqa: F841
    days_of_week: Optional[str] = Field(None, description="Days Of Week Rel field")  # noqa: F841
    hours: Optional[str] = Field(None, description="Hours Rel field")  # noqa: F841
    max_distance_km: Optional[str] = Field(None, description="Max Distance Km field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentUserSchema(BaseModel):  # noqa: F841
    """Pydantic schema for AlertsSentUser"""  # noqa: F841
    field_324: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_325: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_326: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_327: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_328: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_329: Optional[str] = Field(None, description=" field")  # noqa: F841
    alerts_sent_id: Optional[int] = Field(None, description="Alerts Sent Id field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    msg_type: Optional[str] = Field(None, description="Msg Type field")  # noqa: F841
    twilio_sid: Optional[str] = Field(None, description="Twilio Sid field")  # noqa: F841
    mailgun_id: Optional[int] = Field(None, description="Mailgun Id field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentUserCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating AlertsSentUser"""  # noqa: F841
    field_342: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_343: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_344: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_345: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_346: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_347: Optional[str] = Field(None, description=" field")  # noqa: F841
    alerts_sent_id: Optional[int] = Field(None, description="Alerts Sent Id field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    msg_type: Optional[str] = Field(None, description="Msg Type field")  # noqa: F841
    twilio_sid: Optional[str] = Field(None, description="Twilio Sid field")  # noqa: F841
    mailgun_id: Optional[int] = Field(None, description="Mailgun Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentUserUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating AlertsSentUser"""  # noqa: F841
    field_359: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_360: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_361: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_362: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_363: Optional[str] = Field(None, description=" field")  # noqa: F841
    field_364: Optional[str] = Field(None, description=" field")  # noqa: F841
    alerts_sent_id: Optional[int] = Field(None, description="Alerts Sent Id field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    msg_type: Optional[str] = Field(None, description="Msg Type field")  # noqa: F841
    twilio_sid: Optional[str] = Field(None, description="Twilio Sid field")  # noqa: F841
    mailgun_id: Optional[int] = Field(None, description="Mailgun Id field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RemoteControlSchema(BaseModel):  # noqa: F841
    """Pydantic schema for RemoteControl"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Unit Rel.Power Unit Str field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    action: Optional[str] = Field(None, description="Action field")  # noqa: F841
    metric: Optional[str] = Field(None, description="Metric field")  # noqa: F841
    value_wanted: Optional[str] = Field(None, description="Value Wanted field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RemoteControlCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating RemoteControl"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Unit Rel.Power Unit Str field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    action: Optional[str] = Field(None, description="Action field")  # noqa: F841
    metric: Optional[str] = Field(None, description="Metric field")  # noqa: F841
    value_wanted: Optional[str] = Field(None, description="Value Wanted field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RemoteControlUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating RemoteControl"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Unit Rel.Power Unit Str field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    action: Optional[str] = Field(None, description="Action field")  # noqa: F841
    metric: Optional[str] = Field(None, description="Metric field")  # noqa: F841
    value_wanted: Optional[str] = Field(None, description="Value Wanted field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentMaintSchema(BaseModel):  # noqa: F841
    """Pydantic schema for AlertsSentMaint"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    alerts_sent_maint_email_types: Optional[str] = Field(None, description="Alerts Sent Maint Email Types Rel field")  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentMaintCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating AlertsSentMaint"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    alerts_sent_maint_email_types: Optional[str] = Field(None, description="Alerts Sent Maint Email Types Rel field")  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentMaintUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating AlertsSentMaint"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    alerts_sent_maint_email_types: Optional[str] = Field(None, description="Alerts Sent Maint Email Types Rel field")  # noqa: F841
    customers: Optional[str] = Field(None, description="Customers Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentMaintUserSchema(BaseModel):  # noqa: F841
    """Pydantic schema for AlertsSentMaintUser"""  # noqa: F841
    alerts_sent_maint: Optional[str] = Field(None, description="Alerts Sent Maint Rel field")  # noqa: F841
    alerts_sent_maint_timestamp_utc_proxy: Optional[datetime] = Field(None, description="Alerts Sent Maint Rel Timestamp Utc Proxy field")  # noqa: F841
    email_types_rel_name_proxy: Optional[str] = Field(None, description="Alerts Sent Maint Rel.Email Types Rel Name Proxy field")  # noqa: F841
    customers_rel_customer_proxy: Optional[str] = Field(None, description="Alerts Sent Maint Rel.Customers Rel Customer Proxy field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentMaintUserCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating AlertsSentMaintUser"""  # noqa: F841
    alerts_sent_maint: Optional[str] = Field(None, description="Alerts Sent Maint Rel field")  # noqa: F841
    alerts_sent_maint_timestamp_utc_proxy: Optional[datetime] = Field(None, description="Alerts Sent Maint Rel Timestamp Utc Proxy field")  # noqa: F841
    email_types_rel_name_proxy: Optional[str] = Field(None, description="Alerts Sent Maint Rel.Email Types Rel Name Proxy field")  # noqa: F841
    customers_rel_customer_proxy: Optional[str] = Field(None, description="Alerts Sent Maint Rel.Customers Rel Customer Proxy field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class AlertsSentMaintUserUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating AlertsSentMaintUser"""  # noqa: F841
    alerts_sent_maint: Optional[str] = Field(None, description="Alerts Sent Maint Rel field")  # noqa: F841
    alerts_sent_maint_timestamp_utc_proxy: Optional[datetime] = Field(None, description="Alerts Sent Maint Rel Timestamp Utc Proxy field")  # noqa: F841
    email_types_rel_name_proxy: Optional[str] = Field(None, description="Alerts Sent Maint Rel.Email Types Rel Name Proxy field")  # noqa: F841
    customers_rel_customer_proxy: Optional[str] = Field(None, description="Alerts Sent Maint Rel.Customers Rel Customer Proxy field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
  # noqa: F841
# CRUD Endpoints  # noqa: F841
  # noqa: F841
@router.get("/alerts", response_model=ApiResponse[List[AlertSchema]])  # noqa: F841
async def get_alerts(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Alert records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Alert)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Alert, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Alert, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Alert, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching alerts: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts/{item_id}", response_model=ApiResponse[AlertSchema])  # noqa: F841
async def get_alerts_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Alert by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Alert).filter(Alert.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Alert not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Alert: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/alerts", response_model=ApiResponse[AlertSchema])  # noqa: F841
async def create_alerts(  # noqa: F841
    item_data: AlertCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Alert"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Alert(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Alert: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/alerts/{item_id}", response_model=ApiResponse[AlertSchema])  # noqa: F841
async def update_alerts(  # noqa: F841
    item_id: int,
    item_data: AlertUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Alert by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Alert).filter(Alert.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Alert not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Alert: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/alerts/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_alerts(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Alert by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Alert).filter(Alert.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Alert not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Alert deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Alert: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_sent", response_model=ApiResponse[List[AlertsSentSchema]])  # noqa: F841
async def get_alerts_sent(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertsSent records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(AlertsSent)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(AlertsSent, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(AlertsSent, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(AlertsSent, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching alerts_sent: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_sent/{item_id}", response_model=ApiResponse[AlertsSentSchema])  # noqa: F841
async def get_alerts_sent_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertsSent by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSent).filter(AlertsSent.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSent not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching AlertsSent: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/alerts_sent", response_model=ApiResponse[AlertsSentSchema])  # noqa: F841
async def create_alerts_sent(  # noqa: F841
    item_data: AlertsSentCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new AlertsSent"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = AlertsSent(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating AlertsSent: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/alerts_sent/{item_id}", response_model=ApiResponse[AlertsSentSchema])  # noqa: F841
async def update_alerts_sent(  # noqa: F841
    item_id: int,
    item_data: AlertsSentUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update AlertsSent by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSent).filter(AlertsSent.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSent not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating AlertsSent: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/alerts_sent/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_alerts_sent(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete AlertsSent by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSent).filter(AlertsSent.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSent not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "AlertsSent deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting AlertsSent: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_custom", response_model=ApiResponse[List[AlertCustomSchema]])  # noqa: F841
async def get_alerts_custom(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertCustom records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(AlertCustom)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(AlertCustom, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(AlertCustom, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(AlertCustom, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching alerts_custom: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_custom/{item_id}", response_model=ApiResponse[AlertCustomSchema])  # noqa: F841
async def get_alerts_custom_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertCustom by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertCustom).filter(AlertCustom.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertCustom not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching AlertCustom: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/alerts_custom", response_model=ApiResponse[AlertCustomSchema])  # noqa: F841
async def create_alerts_custom(  # noqa: F841
    item_data: AlertCustomCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new AlertCustom"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = AlertCustom(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating AlertCustom: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/alerts_custom/{item_id}", response_model=ApiResponse[AlertCustomSchema])  # noqa: F841
async def update_alerts_custom(  # noqa: F841
    item_id: int,
    item_data: AlertCustomUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update AlertCustom by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertCustom).filter(AlertCustom.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertCustom not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating AlertCustom: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/alerts_custom/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_alerts_custom(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete AlertCustom by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertCustom).filter(AlertCustom.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertCustom not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "AlertCustom deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting AlertCustom: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hourly_update_report", response_model=ApiResponse[List[ReportEmailHourlySchema]])  # noqa: F841
async def get_hourly_update_report(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReportEmailHourly records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ReportEmailHourly)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ReportEmailHourly, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ReportEmailHourly, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ReportEmailHourly, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching hourly_update_report: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hourly_update_report/{item_id}", response_model=ApiResponse[ReportEmailHourlySchema])  # noqa: F841
async def get_hourly_update_report_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReportEmailHourly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailHourly).filter(ReportEmailHourly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailHourly not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ReportEmailHourly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/hourly_update_report", response_model=ApiResponse[ReportEmailHourlySchema])  # noqa: F841
async def create_hourly_update_report(  # noqa: F841
    item_data: ReportEmailHourlyCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ReportEmailHourly"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ReportEmailHourly(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ReportEmailHourly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/hourly_update_report/{item_id}", response_model=ApiResponse[ReportEmailHourlySchema])  # noqa: F841
async def update_hourly_update_report(  # noqa: F841
    item_id: int,
    item_data: ReportEmailHourlyUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ReportEmailHourly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailHourly).filter(ReportEmailHourly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailHourly not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ReportEmailHourly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/hourly_update_report/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_hourly_update_report(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ReportEmailHourly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailHourly).filter(ReportEmailHourly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailHourly not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ReportEmailHourly deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ReportEmailHourly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/derates_report", response_model=ApiResponse[List[ReportEmailDeratesSchema]])  # noqa: F841
async def get_derates_report(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReportEmailDerates records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ReportEmailDerates)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ReportEmailDerates, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ReportEmailDerates, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ReportEmailDerates, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching derates_report: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/derates_report/{item_id}", response_model=ApiResponse[ReportEmailDeratesSchema])  # noqa: F841
async def get_derates_report_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReportEmailDerates by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailDerates).filter(ReportEmailDerates.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailDerates not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ReportEmailDerates: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/derates_report", response_model=ApiResponse[ReportEmailDeratesSchema])  # noqa: F841
async def create_derates_report(  # noqa: F841
    item_data: ReportEmailDeratesCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ReportEmailDerates"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ReportEmailDerates(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ReportEmailDerates: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/derates_report/{item_id}", response_model=ApiResponse[ReportEmailDeratesSchema])  # noqa: F841
async def update_derates_report(  # noqa: F841
    item_id: int,
    item_data: ReportEmailDeratesUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ReportEmailDerates by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailDerates).filter(ReportEmailDerates.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailDerates not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ReportEmailDerates: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/derates_report/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_derates_report(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ReportEmailDerates by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailDerates).filter(ReportEmailDerates.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailDerates not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ReportEmailDerates deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ReportEmailDerates: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/op_hours_report", response_model=ApiResponse[List[ReportEmailOpHoursSchema]])  # noqa: F841
async def get_op_hours_report(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReportEmailOpHours records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ReportEmailOpHours)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ReportEmailOpHours, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ReportEmailOpHours, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ReportEmailOpHours, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching op_hours_report: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/op_hours_report/{item_id}", response_model=ApiResponse[ReportEmailOpHoursSchema])  # noqa: F841
async def get_op_hours_report_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReportEmailOpHours by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailOpHours).filter(ReportEmailOpHours.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailOpHours not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ReportEmailOpHours: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/op_hours_report", response_model=ApiResponse[ReportEmailOpHoursSchema])  # noqa: F841
async def create_op_hours_report(  # noqa: F841
    item_data: ReportEmailOpHoursCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ReportEmailOpHours"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ReportEmailOpHours(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ReportEmailOpHours: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/op_hours_report/{item_id}", response_model=ApiResponse[ReportEmailOpHoursSchema])  # noqa: F841
async def update_op_hours_report(  # noqa: F841
    item_id: int,
    item_data: ReportEmailOpHoursUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ReportEmailOpHours by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailOpHours).filter(ReportEmailOpHours.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailOpHours not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ReportEmailOpHours: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/op_hours_report/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_op_hours_report(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ReportEmailOpHours by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailOpHours).filter(ReportEmailOpHours.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailOpHours not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ReportEmailOpHours deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ReportEmailOpHours: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_report", response_model=ApiResponse[List[ReportEmailInventorySchema]])  # noqa: F841
async def get_inventory_report(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReportEmailInventory records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ReportEmailInventory)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ReportEmailInventory, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ReportEmailInventory, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ReportEmailInventory, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching inventory_report: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/inventory_report/{item_id}", response_model=ApiResponse[ReportEmailInventorySchema])  # noqa: F841
async def get_inventory_report_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ReportEmailInventory by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailInventory).filter(ReportEmailInventory.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailInventory not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ReportEmailInventory: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/inventory_report", response_model=ApiResponse[ReportEmailInventorySchema])  # noqa: F841
async def create_inventory_report(  # noqa: F841
    item_data: ReportEmailInventoryCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ReportEmailInventory"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ReportEmailInventory(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ReportEmailInventory: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/inventory_report/{item_id}", response_model=ApiResponse[ReportEmailInventorySchema])  # noqa: F841
async def update_inventory_report(  # noqa: F841
    item_id: int,
    item_data: ReportEmailInventoryUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ReportEmailInventory by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailInventory).filter(ReportEmailInventory.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailInventory not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ReportEmailInventory: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/inventory_report/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_inventory_report(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ReportEmailInventory by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ReportEmailInventory).filter(ReportEmailInventory.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ReportEmailInventory not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ReportEmailInventory deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ReportEmailInventory: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_sent_users", response_model=ApiResponse[List[AlertsSentUserSchema]])  # noqa: F841
async def get_alerts_sent_users(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertsSentUser records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(AlertsSentUser)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(AlertsSentUser, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(AlertsSentUser, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(AlertsSentUser, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching alerts_sent_users: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_sent_users/{item_id}", response_model=ApiResponse[AlertsSentUserSchema])  # noqa: F841
async def get_alerts_sent_users_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertsSentUser by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentUser).filter(AlertsSentUser.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentUser not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching AlertsSentUser: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/alerts_sent_users", response_model=ApiResponse[AlertsSentUserSchema])  # noqa: F841
async def create_alerts_sent_users(  # noqa: F841
    item_data: AlertsSentUserCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new AlertsSentUser"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = AlertsSentUser(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating AlertsSentUser: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/alerts_sent_users/{item_id}", response_model=ApiResponse[AlertsSentUserSchema])  # noqa: F841
async def update_alerts_sent_users(  # noqa: F841
    item_id: int,
    item_data: AlertsSentUserUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update AlertsSentUser by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentUser).filter(AlertsSentUser.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentUser not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating AlertsSentUser: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/alerts_sent_users/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_alerts_sent_users(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete AlertsSentUser by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentUser).filter(AlertsSentUser.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentUser not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "AlertsSentUser deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting AlertsSentUser: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/remote_control", response_model=ApiResponse[List[RemoteControlSchema]])  # noqa: F841
async def get_remote_control(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get RemoteControl records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(RemoteControl)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(RemoteControl, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(RemoteControl, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(RemoteControl, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching remote_control: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/remote_control/{item_id}", response_model=ApiResponse[RemoteControlSchema])  # noqa: F841
async def get_remote_control_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get RemoteControl by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(RemoteControl).filter(RemoteControl.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="RemoteControl not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching RemoteControl: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/remote_control", response_model=ApiResponse[RemoteControlSchema])  # noqa: F841
async def create_remote_control(  # noqa: F841
    item_data: RemoteControlCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new RemoteControl"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = RemoteControl(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating RemoteControl: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/remote_control/{item_id}", response_model=ApiResponse[RemoteControlSchema])  # noqa: F841
async def update_remote_control(  # noqa: F841
    item_id: int,
    item_data: RemoteControlUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update RemoteControl by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(RemoteControl).filter(RemoteControl.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="RemoteControl not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating RemoteControl: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/remote_control/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_remote_control(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete RemoteControl by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(RemoteControl).filter(RemoteControl.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="RemoteControl not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "RemoteControl deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting RemoteControl: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_sent_maintenance", response_model=ApiResponse[List[AlertsSentMaintSchema]])  # noqa: F841
async def get_alerts_sent_maintenance(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertsSentMaint records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(AlertsSentMaint)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(AlertsSentMaint, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(AlertsSentMaint, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(AlertsSentMaint, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching alerts_sent_maintenance: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_sent_maintenance/{item_id}", response_model=ApiResponse[AlertsSentMaintSchema])  # noqa: F841
async def get_alerts_sent_maintenance_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertsSentMaint by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentMaint).filter(AlertsSentMaint.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentMaint not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching AlertsSentMaint: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/alerts_sent_maintenance", response_model=ApiResponse[AlertsSentMaintSchema])  # noqa: F841
async def create_alerts_sent_maintenance(  # noqa: F841
    item_data: AlertsSentMaintCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new AlertsSentMaint"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = AlertsSentMaint(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating AlertsSentMaint: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/alerts_sent_maintenance/{item_id}", response_model=ApiResponse[AlertsSentMaintSchema])  # noqa: F841
async def update_alerts_sent_maintenance(  # noqa: F841
    item_id: int,
    item_data: AlertsSentMaintUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update AlertsSentMaint by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentMaint).filter(AlertsSentMaint.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentMaint not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating AlertsSentMaint: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/alerts_sent_maintenance/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_alerts_sent_maintenance(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete AlertsSentMaint by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentMaint).filter(AlertsSentMaint.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentMaint not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "AlertsSentMaint deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting AlertsSentMaint: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_sent_maintenance_users", response_model=ApiResponse[List[AlertsSentMaintUserSchema]])  # noqa: F841
async def get_alerts_sent_maintenance_users(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertsSentMaintUser records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(AlertsSentMaintUser)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(AlertsSentMaintUser, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(AlertsSentMaintUser, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(AlertsSentMaintUser, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching alerts_sent_maintenance_users: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/alerts_sent_maintenance_users/{item_id}", response_model=ApiResponse[AlertsSentMaintUserSchema])  # noqa: F841
async def get_alerts_sent_maintenance_users_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get AlertsSentMaintUser by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentMaintUser).filter(AlertsSentMaintUser.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentMaintUser not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching AlertsSentMaintUser: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/alerts_sent_maintenance_users", response_model=ApiResponse[AlertsSentMaintUserSchema])  # noqa: F841
async def create_alerts_sent_maintenance_users(  # noqa: F841
    item_data: AlertsSentMaintUserCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new AlertsSentMaintUser"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = AlertsSentMaintUser(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating AlertsSentMaintUser: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/alerts_sent_maintenance_users/{item_id}", response_model=ApiResponse[AlertsSentMaintUserSchema])  # noqa: F841
async def update_alerts_sent_maintenance_users(  # noqa: F841
    item_id: int,
    item_data: AlertsSentMaintUserUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update AlertsSentMaintUser by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentMaintUser).filter(AlertsSentMaintUser.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentMaintUser not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating AlertsSentMaintUser: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/alerts_sent_maintenance_users/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_alerts_sent_maintenance_users(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete AlertsSentMaintUser by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(AlertsSentMaintUser).filter(AlertsSentMaintUser.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="AlertsSentMaintUser not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "AlertsSentMaintUser deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting AlertsSentMaintUser: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841