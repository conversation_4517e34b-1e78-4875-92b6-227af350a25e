# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""  # noqa: F841
FastAPI Router for Customers resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841
  # noqa: F841
from datetime import datetime  # noqa: F841
from typing import List, Optional  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841
from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841
from sqlalchemy import desc, asc  # noqa: F841
  # noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841
  # noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
  # noqa: F841
  # noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/customers", tags=["Customers"])  # noqa: F841
  # noqa: F841
# Pydantic Schemas  # noqa: F841
class ApplicationSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Application"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Application"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Application"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Application"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Application"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Application"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ApplicationUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Application"""  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    application_types_rel_name: Optional[str] = Field(None, description="Application Types Rel.Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer Rel.Customer field")  # noqa: F841
    company_name: Optional[str] = Field(None, description="Company Name field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Countries Rel.Country Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="User Rel.Email field")  # noqa: F841
    contact_name: Optional[str] = Field(None, description="Contact Name field")  # noqa: F841
    contact_phone: Optional[str] = Field(None, description="Contact Phone field")  # noqa: F841
    contact_email: Optional[str] = Field(None, description="Contact Email field")  # noqa: F841
    install_location: Optional[str] = Field(None, description="Install Location field")  # noqa: F841
    field_contact_name: Optional[str] = Field(None, description="Field Contact Name field")  # noqa: F841
    field_contact_phone: Optional[str] = Field(None, description="Field Contact Phone field")  # noqa: F841
    field_contact_email: Optional[str] = Field(None, description="Field Contact Email field")  # noqa: F841
    project_objective: Optional[str] = Field(None, description="Project Objective field")  # noqa: F841
    current_process_equipment: Optional[str] = Field(None, description="Current Process Equipment field")  # noqa: F841
    current_process_issues: Optional[str] = Field(None, description="Current Process Issues field")  # noqa: F841
    when_need_equipment: Optional[str] = Field(None, description="When Need Equipment field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CareerApplicationSchema(BaseModel):  # noqa: F841
    """Pydantic schema for CareerApplication"""  # noqa: F841
    timestamp_inserted_utc: Optional[datetime] = Field(None, description="Timestamp Inserted Utc field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    job_type: Optional[str] = Field(None, description="Job Type field")  # noqa: F841
    message: Optional[str] = Field(None, description="Message field")  # noqa: F841
    career_files: Optional[str] = Field(None, description="Career Files Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CareerApplicationCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating CareerApplication"""  # noqa: F841
    timestamp_inserted_utc: Optional[datetime] = Field(None, description="Timestamp Inserted Utc field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    job_type: Optional[str] = Field(None, description="Job Type field")  # noqa: F841
    message: Optional[str] = Field(None, description="Message field")  # noqa: F841
    career_files: Optional[str] = Field(None, description="Career Files Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CareerApplicationUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating CareerApplication"""  # noqa: F841
    timestamp_inserted_utc: Optional[datetime] = Field(None, description="Timestamp Inserted Utc field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    job_type: Optional[str] = Field(None, description="Job Type field")  # noqa: F841
    message: Optional[str] = Field(None, description="Message field")  # noqa: F841
    career_files: Optional[str] = Field(None, description="Career Files Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class UserAPITokenSchema(BaseModel):  # noqa: F841
    """Pydantic schema for UserAPIToken"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    expires: Optional[str] = Field(None, description="Expires field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    token: Optional[str] = Field(None, description="Token field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class UserAPITokenCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating UserAPIToken"""  # noqa: F841
    expires: Optional[str] = Field(None, description="Expires field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    token: Optional[str] = Field(None, description="Token field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class UserAPITokenUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating UserAPIToken"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    expires: Optional[str] = Field(None, description="Expires field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    token: Optional[str] = Field(None, description="Token field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RoleSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Role"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RoleCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Role"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RoleUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Role"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwUserRoleSchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwUserRole"""  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841
    job_title: Optional[str] = Field(None, description="Job Title field")  # noqa: F841
    roles: Optional[str] = Field(None, description="Roles field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwUserRoleCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwUserRole"""  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841
    job_title: Optional[str] = Field(None, description="Job Title field")  # noqa: F841
    roles: Optional[str] = Field(None, description="Roles field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwUserRoleUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwUserRole"""  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841
    job_title: Optional[str] = Field(None, description="Job Title field")  # noqa: F841
    roles: Optional[str] = Field(None, description="Roles field")  # noqa: F841
    units: Optional[str] = Field(None, description="Units field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CustomerSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Customer"""  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
    formal: Optional[str] = Field(None, description="Formal field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    country: Optional[int] = Field(None, description="Country field")  # noqa: F841
    mqtt_topic: Optional[str] = Field(None, description="Mqtt Topic field")  # noqa: F841
    cust_sub_groups_rel_name: Optional[str] = Field(None, description="Cust Sub Groups Rel.Name field")  # noqa: F841
    is_tax_exempt: Optional[bool] = Field(None, description="Is Tax Exempt field")  # noqa: F841
    unit: Optional[str] = Field(None, description="Unit field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    state: Optional[str] = Field(None, description="State field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    accounting_contact: Optional[int] = Field(None, description="Accounting Contact Rel field")  # noqa: F841
    gst_hst_number: Optional[str] = Field(None, description="Gst Hst Number field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CustomerCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Customer"""  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
    formal: Optional[str] = Field(None, description="Formal field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    country: Optional[int] = Field(None, description="Country field")  # noqa: F841
    mqtt_topic: Optional[str] = Field(None, description="Mqtt Topic field")  # noqa: F841
    cust_sub_groups_rel_name: Optional[str] = Field(None, description="Cust Sub Groups Rel.Name field")  # noqa: F841
    is_tax_exempt: Optional[bool] = Field(None, description="Is Tax Exempt field")  # noqa: F841
    unit: Optional[str] = Field(None, description="Unit field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    state: Optional[str] = Field(None, description="State field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    accounting_contact: Optional[int] = Field(None, description="Accounting Contact Rel field")  # noqa: F841
    gst_hst_number: Optional[str] = Field(None, description="Gst Hst Number field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CustomerUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Customer"""  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
    formal: Optional[str] = Field(None, description="Formal field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    country: Optional[int] = Field(None, description="Country field")  # noqa: F841
    mqtt_topic: Optional[str] = Field(None, description="Mqtt Topic field")  # noqa: F841
    cust_sub_groups_rel_name: Optional[str] = Field(None, description="Cust Sub Groups Rel.Name field")  # noqa: F841
    is_tax_exempt: Optional[bool] = Field(None, description="Is Tax Exempt field")  # noqa: F841
    unit: Optional[str] = Field(None, description="Unit field")  # noqa: F841
    street: Optional[str] = Field(None, description="Street field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    state: Optional[str] = Field(None, description="State field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    accounting_contact: Optional[int] = Field(None, description="Accounting Contact Rel field")  # noqa: F841
    gst_hst_number: Optional[str] = Field(None, description="Gst Hst Number field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CustSubGroupSchema(BaseModel):  # noqa: F841
    """Pydantic schema for CustSubGroup"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CustSubGroupCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating CustSubGroup"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CustSubGroupUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating CustSubGroup"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customers Rel.Customer field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CountrySchema(BaseModel):  # noqa: F841
    """Pydantic schema for Country"""  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    sales_tax_rate: Optional[str] = Field(None, description="Sales Tax Rate field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CountryCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Country"""  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    sales_tax_rate: Optional[str] = Field(None, description="Sales Tax Rate field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CountryUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Country"""  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    sales_tax_rate: Optional[str] = Field(None, description="Sales Tax Rate field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ProvinceSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Province"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ProvinceCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Province"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ProvinceUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Province"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    abbrev: Optional[str] = Field(None, description="Abbrev field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Rel.Country Name field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CountySchema(BaseModel):  # noqa: F841
    """Pydantic schema for County"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_code: Optional[int] = Field(None, description="County Code field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CountyCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating County"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_code: Optional[int] = Field(None, description="County Code field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CountyUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating County"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_code: Optional[int] = Field(None, description="County Code field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CitySchema(BaseModel):  # noqa: F841
    """Pydantic schema for City"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CityCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating City"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CityUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating City"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ZipCodeSalesTaxSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ZipCodeSalesTax"""  # noqa: F841
    zip_code: Optional[str] = Field(None, description="Zip Code Rel.Zip Code field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
    city_rel_name: Optional[str] = Field(None, description="City Rel.Name field")  # noqa: F841
    state_plus_county: Optional[int] = Field(None, description="State Plus County field")  # noqa: F841
    combined_rate_est: Optional[str] = Field(None, description="Combined Rate Est field")  # noqa: F841
    state_rate: Optional[str] = Field(None, description="State Rate field")  # noqa: F841
    county_rate: Optional[int] = Field(None, description="County Rate field")  # noqa: F841
    city_rate: Optional[str] = Field(None, description="City Rate field")  # noqa: F841
    special_rate: Optional[str] = Field(None, description="Special Rate field")  # noqa: F841
    risk_level: Optional[str] = Field(None, description="Risk Level field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ZipCodeSalesTaxCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ZipCodeSalesTax"""  # noqa: F841
    zip_code: Optional[str] = Field(None, description="Zip Code Rel.Zip Code field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
    city_rel_name: Optional[str] = Field(None, description="City Rel.Name field")  # noqa: F841
    state_plus_county: Optional[int] = Field(None, description="State Plus County field")  # noqa: F841
    combined_rate_est: Optional[str] = Field(None, description="Combined Rate Est field")  # noqa: F841
    state_rate: Optional[str] = Field(None, description="State Rate field")  # noqa: F841
    county_rate: Optional[int] = Field(None, description="County Rate field")  # noqa: F841
    city_rate: Optional[str] = Field(None, description="City Rate field")  # noqa: F841
    special_rate: Optional[str] = Field(None, description="Special Rate field")  # noqa: F841
    risk_level: Optional[str] = Field(None, description="Risk Level field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ZipCodeSalesTaxUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ZipCodeSalesTax"""  # noqa: F841
    zip_code: Optional[str] = Field(None, description="Zip Code Rel.Zip Code field")  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    county_rel_name: Optional[int] = Field(None, description="County Rel.Name field")  # noqa: F841
    city_rel_name: Optional[str] = Field(None, description="City Rel.Name field")  # noqa: F841
    state_plus_county: Optional[int] = Field(None, description="State Plus County field")  # noqa: F841
    combined_rate_est: Optional[str] = Field(None, description="Combined Rate Est field")  # noqa: F841
    state_rate: Optional[str] = Field(None, description="State Rate field")  # noqa: F841
    county_rate: Optional[int] = Field(None, description="County Rate field")  # noqa: F841
    city_rate: Optional[str] = Field(None, description="City Rate field")  # noqa: F841
    special_rate: Optional[str] = Field(None, description="Special Rate field")  # noqa: F841
    risk_level: Optional[str] = Field(None, description="Risk Level field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SalesTaxSchema(BaseModel):  # noqa: F841
    """Pydantic schema for SalesTax"""  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    rate: Optional[str] = Field(None, description="Rate field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SalesTaxCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating SalesTax"""  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    rate: Optional[str] = Field(None, description="Rate field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class SalesTaxUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating SalesTax"""  # noqa: F841
    province_rel_name: Optional[str] = Field(None, description="Province Rel.Name field")  # noqa: F841
    rate: Optional[str] = Field(None, description="Rate field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class UserSchema(BaseModel):  # noqa: F841
    """Pydantic schema for User"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class UserCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating User"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class UserUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating User"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
  # noqa: F841
# CRUD Endpoints  # noqa: F841
  # noqa: F841
@router.get("/applications", response_model=ApiResponse[List[ApplicationSchema]])  # noqa: F841
async def get_applications(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Application)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Application, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Application, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Application, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching applications: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def get_applications_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/applications", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def create_applications(  # noqa: F841
    item_data: ApplicationCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Application"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Application(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/applications/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def update_applications(  # noqa: F841
    item_id: int,
    item_data: ApplicationUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/applications/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_applications(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Application deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications_xfer", response_model=ApiResponse[List[ApplicationSchema]])  # noqa: F841
async def get_applications_xfer(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db),
):  # noqa: F841
    """Get Application records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Application)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Application, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Application, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Application, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching applications_xfer: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications_xfer/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def get_applications_xfer_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/applications_xfer", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def create_applications_xfer(  # noqa: F841
    item_data: ApplicationCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Application"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Application(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/applications_xfer/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def update_applications_xfer(  # noqa: F841
    item_id: int,
    item_data: ApplicationUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/applications_xfer/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_applications_xfer(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Application deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications_vru", response_model=ApiResponse[List[ApplicationSchema]])  # noqa: F841
async def get_applications_vru(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Application)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Application, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Application, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Application, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching applications_vru: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications_vru/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def get_applications_vru_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/applications_vru", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def create_applications_vru(  # noqa: F841
    item_data: ApplicationCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Application"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Application(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/applications_vru/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def update_applications_vru(  # noqa: F841
    item_id: int,
    item_data: ApplicationUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/applications_vru/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_applications_vru(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Application deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications_egas", response_model=ApiResponse[List[ApplicationSchema]])  # noqa: F841
async def get_applications_egas(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Application)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Application, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Application, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Application, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching applications_egas: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications_egas/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def get_applications_egas_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/applications_egas", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def create_applications_egas(  # noqa: F841
    item_data: ApplicationCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Application"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Application(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/applications_egas/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def update_applications_egas(  # noqa: F841
    item_id: int,
    item_data: ApplicationUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/applications_egas/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_applications_egas(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Application deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications_dgas", response_model=ApiResponse[List[ApplicationSchema]])  # noqa: F841
async def get_applications_dgas(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Application)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Application, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Application, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Application, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching applications_dgas: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/applications_dgas/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def get_applications_dgas_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/applications_dgas", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def create_applications_dgas(  # noqa: F841
    item_data: ApplicationCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Application"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Application(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/applications_dgas/{item_id}", response_model=ApiResponse[ApplicationSchema])  # noqa: F841
async def update_applications_dgas(  # noqa: F841
    item_id: int,
    item_data: ApplicationUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/applications_dgas/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_applications_dgas(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Application by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Application).filter(Application.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Application not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Application deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Application: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/career_applications", response_model=ApiResponse[List[CareerApplicationSchema]])  # noqa: F841
async def get_career_applications(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CareerApplication records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(CareerApplication)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(CareerApplication, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(CareerApplication, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(CareerApplication, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching career_applications: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/career_applications/{item_id}", response_model=ApiResponse[CareerApplicationSchema])  # noqa: F841
async def get_career_applications_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CareerApplication by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CareerApplication).filter(CareerApplication.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CareerApplication not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching CareerApplication: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/career_applications", response_model=ApiResponse[CareerApplicationSchema])  # noqa: F841
async def create_career_applications(  # noqa: F841
    item_data: CareerApplicationCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new CareerApplication"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = CareerApplication(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating CareerApplication: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/career_applications/{item_id}", response_model=ApiResponse[CareerApplicationSchema])  # noqa: F841
async def update_career_applications(  # noqa: F841
    item_id: int,
    item_data: CareerApplicationUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update CareerApplication by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CareerApplication).filter(CareerApplication.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CareerApplication not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating CareerApplication: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/career_applications/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_career_applications(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete CareerApplication by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CareerApplication).filter(CareerApplication.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CareerApplication not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "CareerApplication deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting CareerApplication: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/api_tokens", response_model=ApiResponse[List[UserAPITokenSchema]])  # noqa: F841
async def get_api_tokens(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    sort_by: Optional[str] = Query(None, description="Sort by field"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get UserAPIToken records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(UserAPIToken)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(UserAPIToken, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(UserAPIToken, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(UserAPIToken, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching api_tokens: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/api_tokens/{item_id}", response_model=ApiResponse[UserAPITokenSchema])  # noqa: F841
async def get_api_tokens_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get UserAPIToken by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(UserAPIToken).filter(UserAPIToken.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="UserAPIToken not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching UserAPIToken: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/api_tokens", response_model=ApiResponse[UserAPITokenSchema])  # noqa: F841
async def create_api_tokens(  # noqa: F841
    item_data: UserAPITokenCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new UserAPIToken"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = UserAPIToken(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating UserAPIToken: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/api_tokens/{item_id}", response_model=ApiResponse[UserAPITokenSchema])  # noqa: F841
async def update_api_tokens(  # noqa: F841
    item_id: int,
    item_data: UserAPITokenUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update UserAPIToken by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(UserAPIToken).filter(UserAPIToken.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="UserAPIToken not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating UserAPIToken: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/api_tokens/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_api_tokens(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete UserAPIToken by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(UserAPIToken).filter(UserAPIToken.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="UserAPIToken not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "UserAPIToken deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting UserAPIToken: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/roles", response_model=ApiResponse[List[RoleSchema]])  # noqa: F841
async def get_roles(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Role records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Role)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Role, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Role, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Role, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching roles: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/roles/{item_id}", response_model=ApiResponse[RoleSchema])  # noqa: F841
async def get_roles_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Role by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Role).filter(Role.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Role not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Role: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/roles", response_model=ApiResponse[RoleSchema])  # noqa: F841
async def create_roles(  # noqa: F841
    item_data: RoleCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Role"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Role(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Role: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/roles/{item_id}", response_model=ApiResponse[RoleSchema])  # noqa: F841
async def update_roles(  # noqa: F841
    item_id: int,
    item_data: RoleUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Role by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Role).filter(Role.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Role not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Role: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/roles/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_roles(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Role by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Role).filter(Role.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Role not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Role deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Role: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/users_roles", response_model=ApiResponse[List[VwUserRoleSchema]])  # noqa: F841
async def get_users_roles(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    sort_by: Optional[str] = Query(None, description="Sort by field: c, o, l, u, m"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwUserRole records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwUserRole)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwUserRole, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwUserRole, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwUserRole, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching users_roles: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/users_roles/{item_id}", response_model=ApiResponse[VwUserRoleSchema])  # noqa: F841
async def get_users_roles_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwUserRole by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwUserRole).filter(VwUserRole.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwUserRole not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwUserRole: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/users_roles", response_model=ApiResponse[VwUserRoleSchema])  # noqa: F841
async def create_users_roles(  # noqa: F841
    item_data: VwUserRoleCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwUserRole"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwUserRole(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwUserRole: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/users_roles/{item_id}", response_model=ApiResponse[VwUserRoleSchema])  # noqa: F841
async def update_users_roles(  # noqa: F841
    item_id: int,
    item_data: VwUserRoleUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwUserRole by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwUserRole).filter(VwUserRole.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwUserRole not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwUserRole: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/users_roles/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_users_roles(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwUserRole by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwUserRole).filter(VwUserRole.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwUserRole not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwUserRole deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwUserRole: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/customers", response_model=ApiResponse[List[CustomerSchema]])  # noqa: F841
async def get_customers(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Customer records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Customer)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Customer, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Customer, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Customer, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching customers: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/customers/{item_id}", response_model=ApiResponse[CustomerSchema])  # noqa: F841
async def get_customers_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Customer by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Customer).filter(Customer.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Customer not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Customer: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/customers", response_model=ApiResponse[CustomerSchema])  # noqa: F841
async def create_customers(  # noqa: F841
    item_data: CustomerCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Customer"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Customer(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Customer: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/customers/{item_id}", response_model=ApiResponse[CustomerSchema])  # noqa: F841
async def update_customers(  # noqa: F841
    item_id: int,
    item_data: CustomerUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Customer by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Customer).filter(Customer.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Customer not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Customer: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/customers/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_customers(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Customer by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Customer).filter(Customer.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Customer not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Customer deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Customer: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/cust_sub_groups", response_model=ApiResponse[List[CustSubGroupSchema]])  # noqa: F841
async def get_cust_sub_groups(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CustSubGroup records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(CustSubGroup)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(CustSubGroup, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(CustSubGroup, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(CustSubGroup, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching cust_sub_groups: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/cust_sub_groups/{item_id}", response_model=ApiResponse[CustSubGroupSchema])  # noqa: F841
async def get_cust_sub_groups_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CustSubGroup by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CustSubGroup).filter(CustSubGroup.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CustSubGroup not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching CustSubGroup: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/cust_sub_groups", response_model=ApiResponse[CustSubGroupSchema])  # noqa: F841
async def create_cust_sub_groups(  # noqa: F841
    item_data: CustSubGroupCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new CustSubGroup"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = CustSubGroup(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating CustSubGroup: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/cust_sub_groups/{item_id}", response_model=ApiResponse[CustSubGroupSchema])  # noqa: F841
async def update_cust_sub_groups(  # noqa: F841
    item_id: int,
    item_data: CustSubGroupUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update CustSubGroup by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CustSubGroup).filter(CustSubGroup.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CustSubGroup not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating CustSubGroup: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/cust_sub_groups/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_cust_sub_groups(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete CustSubGroup by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CustSubGroup).filter(CustSubGroup.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CustSubGroup not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "CustSubGroup deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting CustSubGroup: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/countries", response_model=ApiResponse[List[CountrySchema]])  # noqa: F841
async def get_countries(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Country records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Country)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Country, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Country, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Country, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching countries: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/countries/{item_id}", response_model=ApiResponse[CountrySchema])  # noqa: F841
async def get_countries_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Country by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Country).filter(Country.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Country not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Country: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/countries", response_model=ApiResponse[CountrySchema])  # noqa: F841
async def create_countries(  # noqa: F841
    item_data: CountryCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Country"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Country(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Country: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/countries/{item_id}", response_model=ApiResponse[CountrySchema])  # noqa: F841
async def update_countries(  # noqa: F841
    item_id: int,
    item_data: CountryUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Country by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Country).filter(Country.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Country not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Country: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/countries/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_countries(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Country by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Country).filter(Country.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Country not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Country deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Country: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/provinces_states", response_model=ApiResponse[List[ProvinceSchema]])  # noqa: F841
async def get_provinces_states(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Province records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Province)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Province, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Province, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Province, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching provinces_states: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/provinces_states/{item_id}", response_model=ApiResponse[ProvinceSchema])  # noqa: F841
async def get_provinces_states_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Province by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Province).filter(Province.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Province not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Province: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/provinces_states", response_model=ApiResponse[ProvinceSchema])  # noqa: F841
async def create_provinces_states(  # noqa: F841
    item_data: ProvinceCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Province"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Province(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Province: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/provinces_states/{item_id}", response_model=ApiResponse[ProvinceSchema])  # noqa: F841
async def update_provinces_states(  # noqa: F841
    item_id: int,
    item_data: ProvinceUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Province by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Province).filter(Province.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Province not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Province: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/provinces_states/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_provinces_states(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Province by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Province).filter(Province.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Province not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Province deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Province: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/counties", response_model=ApiResponse[List[CountySchema]])  # noqa: F841
async def get_counties(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get County records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(County)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(County, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(County, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(County, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching counties: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/counties/{item_id}", response_model=ApiResponse[CountySchema])  # noqa: F841
async def get_counties_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get County by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(County).filter(County.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="County not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching County: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/counties", response_model=ApiResponse[CountySchema])  # noqa: F841
async def create_counties(  # noqa: F841
    item_data: CountyCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new County"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = County(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating County: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/counties/{item_id}", response_model=ApiResponse[CountySchema])  # noqa: F841
async def update_counties(  # noqa: F841
    item_id: int,
    item_data: CountyUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update County by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(County).filter(County.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="County not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating County: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/counties/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_counties(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete County by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(County).filter(County.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="County not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "County deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting County: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/cities", response_model=ApiResponse[List[CitySchema]])  # noqa: F841
async def get_cities(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get City records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(City)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(City, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(City, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(City, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching cities: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/cities/{item_id}", response_model=ApiResponse[CitySchema])  # noqa: F841
async def get_cities_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get City by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(City).filter(City.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="City not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching City: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/cities", response_model=ApiResponse[CitySchema])  # noqa: F841
async def create_cities(  # noqa: F841
    item_data: CityCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new City"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = City(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating City: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/cities/{item_id}", response_model=ApiResponse[CitySchema])  # noqa: F841
async def update_cities(  # noqa: F841
    item_id: int,
    item_data: CityUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update City by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(City).filter(City.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="City not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating City: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/cities/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_cities(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete City by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(City).filter(City.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="City not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "City deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting City: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/zip_code_sales_tax", response_model=ApiResponse[List[ZipCodeSalesTaxSchema]])  # noqa: F841
async def get_zip_code_sales_tax(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ZipCodeSalesTax records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ZipCodeSalesTax)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ZipCodeSalesTax, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ZipCodeSalesTax, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ZipCodeSalesTax, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching zip_code_sales_tax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/zip_code_sales_tax/{item_id}", response_model=ApiResponse[ZipCodeSalesTaxSchema])  # noqa: F841
async def get_zip_code_sales_tax_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ZipCodeSalesTax by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ZipCodeSalesTax).filter(ZipCodeSalesTax.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ZipCodeSalesTax not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ZipCodeSalesTax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/zip_code_sales_tax", response_model=ApiResponse[ZipCodeSalesTaxSchema])  # noqa: F841
async def create_zip_code_sales_tax(  # noqa: F841
    item_data: ZipCodeSalesTaxCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ZipCodeSalesTax"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ZipCodeSalesTax(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ZipCodeSalesTax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/zip_code_sales_tax/{item_id}", response_model=ApiResponse[ZipCodeSalesTaxSchema])  # noqa: F841
async def update_zip_code_sales_tax(  # noqa: F841
    item_id: int,
    item_data: ZipCodeSalesTaxUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ZipCodeSalesTax by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ZipCodeSalesTax).filter(ZipCodeSalesTax.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ZipCodeSalesTax not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ZipCodeSalesTax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/zip_code_sales_tax/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_zip_code_sales_tax(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ZipCodeSalesTax by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ZipCodeSalesTax).filter(ZipCodeSalesTax.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ZipCodeSalesTax not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ZipCodeSalesTax deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ZipCodeSalesTax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/work_order_sales_tax", response_model=ApiResponse[List[SalesTaxSchema]])  # noqa: F841
async def get_work_order_sales_tax(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get SalesTax records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(SalesTax)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(SalesTax, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(SalesTax, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(SalesTax, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching work_order_sales_tax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/work_order_sales_tax/{item_id}", response_model=ApiResponse[SalesTaxSchema])  # noqa: F841
async def get_work_order_sales_tax_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get SalesTax by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SalesTax).filter(SalesTax.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SalesTax not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching SalesTax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/work_order_sales_tax", response_model=ApiResponse[SalesTaxSchema])  # noqa: F841
async def create_work_order_sales_tax(  # noqa: F841
    item_data: SalesTaxCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new SalesTax"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = SalesTax(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating SalesTax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/work_order_sales_tax/{item_id}", response_model=ApiResponse[SalesTaxSchema])  # noqa: F841
async def update_work_order_sales_tax(  # noqa: F841
    item_id: int,
    item_data: SalesTaxUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update SalesTax by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SalesTax).filter(SalesTax.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SalesTax not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating SalesTax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/work_order_sales_tax/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_work_order_sales_tax(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete SalesTax by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(SalesTax).filter(SalesTax.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="SalesTax not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "SalesTax deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting SalesTax: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/users", response_model=ApiResponse[List[UserSchema]])  # noqa: F841
async def get_users(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get User records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(User)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(User, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(User, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(User, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching users: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/users/{item_id}", response_model=ApiResponse[UserSchema])  # noqa: F841
async def get_users_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get User by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(User).filter(User.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="User not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching User: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/users", response_model=ApiResponse[UserSchema])  # noqa: F841
async def create_users(  # noqa: F841
    item_data: UserCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new User"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = User(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating User: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/users/{item_id}", response_model=ApiResponse[UserSchema])  # noqa: F841
async def update_users(  # noqa: F841
    item_id: int,
    item_data: UserUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update User by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(User).filter(User.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="User not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating User: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/users/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_users(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete User by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(User).filter(User.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="User not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "User deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting User: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841