# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""  # noqa: F841
FastAPI Router for BoM resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841
  # noqa: F841
from datetime import datetime  # noqa: F841
from decimal import Decimal  # noqa: F841
from typing import List, Optional  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841
from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841
from sqlalchemy import desc, asc  # noqa: F841
  # noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841
  # noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
  # noqa: F841
  # noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/bom", tags=["BoM"])  # noqa: F841
  # noqa: F841
# Pydantic Schemas  # noqa: F841
class UnitTypeSchema(BaseModel):  # noqa: F841
    """Pydantic schema for UnitType"""  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Type field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    model: Optional[str] = Field(None, description="Models Rel.Model field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class UnitTypeCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating UnitType"""  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Type field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    model: Optional[str] = Field(None, description="Models Rel.Model field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class UnitTypeUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating UnitType"""  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Type field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    model: Optional[str] = Field(None, description="Models Rel.Model field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ModelTypeSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ModelType"""  # noqa: F841
    part: Optional[str] = Field(None, description="Part Rel field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    max_delta_p: Optional[str] = Field(None, description="Max Delta P field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
    bom_structure_rel_name: Optional[str] = Field(None, description="Bom Structure Rel.Name field")  # noqa: F841
    bom_pump_top_rel_name: Optional[str] = Field(None, description="Bom Pump Top Rel.Name field")  # noqa: F841
    bom_dgas_rel_name: Optional[str] = Field(None, description="Bom Dgas Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Pm Seal Kits Rel.Part Num field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Types Rel.Unit Type field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ModelTypeCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ModelType"""  # noqa: F841
    part: Optional[str] = Field(None, description="Part Rel field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    max_delta_p: Optional[str] = Field(None, description="Max Delta P field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
    bom_structure_rel_name: Optional[str] = Field(None, description="Bom Structure Rel.Name field")  # noqa: F841
    bom_pump_top_rel_name: Optional[str] = Field(None, description="Bom Pump Top Rel.Name field")  # noqa: F841
    bom_dgas_rel_name: Optional[str] = Field(None, description="Bom Dgas Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Pm Seal Kits Rel.Part Num field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Types Rel.Unit Type field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ModelTypeUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ModelType"""  # noqa: F841
    part: Optional[str] = Field(None, description="Part Rel field")  # noqa: F841
    model: Optional[str] = Field(None, description="Model field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    max_delta_p: Optional[str] = Field(None, description="Max Delta P field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
    bom_structure_rel_name: Optional[str] = Field(None, description="Bom Structure Rel.Name field")  # noqa: F841
    bom_pump_top_rel_name: Optional[str] = Field(None, description="Bom Pump Top Rel.Name field")  # noqa: F841
    bom_dgas_rel_name: Optional[str] = Field(None, description="Bom Dgas Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Pm Seal Kits Rel.Part Num field")  # noqa: F841
    unit_type: Optional[str] = Field(None, description="Unit Types Rel.Unit Type field")  # noqa: F841
    structure: Optional[str] = Field(None, description="Structures Rel.Structure field")  # noqa: F841
    can_show_to_customers: Optional[str] = Field(None, description="Can Show To Customers field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PowerUnitType"""  # noqa: F841
    part: Optional[str] = Field(None, description="Part Rel field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    bom_base_powerunit_rel_name: Optional[str] = Field(None, description="Bom Base Powerunit Rel.Name field")  # noqa: F841
    bom_powerunit_rel_name: Optional[str] = Field(None, description="Bom Powerunit Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Filters Rel.Part Num field")  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Units Rel.Power Unit Str field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PowerUnitType"""  # noqa: F841
    part: Optional[str] = Field(None, description="Part Rel field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    bom_base_powerunit_rel_name: Optional[str] = Field(None, description="Bom Base Powerunit Rel.Name field")  # noqa: F841
    bom_powerunit_rel_name: Optional[str] = Field(None, description="Bom Powerunit Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Filters Rel.Part Num field")  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Units Rel.Power Unit Str field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PowerUnitTypeUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PowerUnitType"""  # noqa: F841
    part: Optional[str] = Field(None, description="Part Rel field")  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    bom_base_powerunit_rel_name: Optional[str] = Field(None, description="Bom Base Powerunit Rel.Name field")  # noqa: F841
    bom_powerunit_rel_name: Optional[str] = Field(None, description="Bom Powerunit Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Parts Rel.Part Num field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Filters Rel.Part Num field")  # noqa: F841
    power_unit_str: Optional[str] = Field(None, description="Power Units Rel.Power Unit Str field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPricingSchema(BaseModel):  # noqa: F841
    """Pydantic schema for BOMPricing"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    bom_pricing: Optional[str] = Field(None, description="Bom Pricing Rel field")  # noqa: F841
    bom_pricing_parts: Optional[str] = Field(None, description="Bom Pricing Parts Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPricingCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating BOMPricing"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    bom_pricing: Optional[str] = Field(None, description="Bom Pricing Rel field")  # noqa: F841
    bom_pricing_parts: Optional[str] = Field(None, description="Bom Pricing Parts Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPricingUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating BOMPricing"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    bom_pricing: Optional[str] = Field(None, description="Bom Pricing Rel field")  # noqa: F841
    bom_pricing_parts: Optional[str] = Field(None, description="Bom Pricing Parts Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPricingPartSchema(BaseModel):  # noqa: F841
    """Pydantic schema for BOMPricingPart"""  # noqa: F841
    finished_good_rel_name: Optional[str] = Field(None, description="Finished Good Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPricingPartCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating BOMPricingPart"""  # noqa: F841
    finished_good_rel_name: Optional[str] = Field(None, description="Finished Good Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPricingPartUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating BOMPricingPart"""  # noqa: F841
    finished_good_rel_name: Optional[str] = Field(None, description="Finished Good Rel.Name field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Rel.Part Num field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMBasePowerUnitSchema(BaseModel):  # noqa: F841
    """Pydantic schema for BOMBasePowerUnit"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_base_powerunit_part: Optional[str] = Field(None, description="Bom Base Powerunit Part Rel field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMBasePowerUnitCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating BOMBasePowerUnit"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_base_powerunit_part: Optional[str] = Field(None, description="Bom Base Powerunit Part Rel field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMBasePowerUnitUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating BOMBasePowerUnit"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_base_powerunit_part: Optional[str] = Field(None, description="Bom Base Powerunit Part Rel field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPowerUnitSchema(BaseModel):  # noqa: F841
    """Pydantic schema for BOMPowerUnit"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_powerunit_part: Optional[str] = Field(None, description="Bom Powerunit Part Rel field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPowerUnitCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating BOMPowerUnit"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_powerunit_part: Optional[str] = Field(None, description="Bom Powerunit Part Rel field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPowerUnitUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating BOMPowerUnit"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_powerunit_part: Optional[str] = Field(None, description="Bom Powerunit Part Rel field")  # noqa: F841
    power_unit_type: Optional[str] = Field(None, description="Power Unit Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMStructureSchema(BaseModel):  # noqa: F841
    """Pydantic schema for BOMStructure"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_structure: Optional[str] = Field(None, description="Bom Structure Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMStructureCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating BOMStructure"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_structure: Optional[str] = Field(None, description="Bom Structure Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMStructureUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating BOMStructure"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_structure: Optional[str] = Field(None, description="Bom Structure Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPumpTopSchema(BaseModel):  # noqa: F841
    """Pydantic schema for BOMPumpTop"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_pump_top_part: Optional[str] = Field(None, description="Bom Pump Top Part Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPumpTopCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating BOMPumpTop"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_pump_top_part: Optional[str] = Field(None, description="Bom Pump Top Part Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMPumpTopUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating BOMPumpTop"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_pump_top_part: Optional[str] = Field(None, description="Bom Pump Top Part Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMDGASSchema(BaseModel):  # noqa: F841
    """Pydantic schema for BOMDGAS"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_dgas_part: Optional[str] = Field(None, description="Bom Dgas Part Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMDGASCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating BOMDGAS"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_dgas_part: Optional[str] = Field(None, description="Bom Dgas Part Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BOMDGASUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating BOMDGAS"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    quantity: Optional[int] = Field(None, description="Quantity field")  # noqa: F841
    bom_dgas_part: Optional[str] = Field(None, description="Bom Dgas Part Rel field")  # noqa: F841
    model_type: Optional[str] = Field(None, description="Model Type Rel field")  # noqa: F841
    timestamp_utc_inserted: Optional[datetime] = Field(None, description="Timestamp Utc Inserted field")  # noqa: F841
    timestamp_utc_updated: Optional[datetime] = Field(None, description="Timestamp Utc Updated field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Part"""  # noqa: F841
    worksheet: Optional[str] = Field(None, description="Worksheet field")  # noqa: F841
    ws_row: Optional[str] = Field(None, description="Ws Row field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    part_image: Optional[str] = Field(None, description="Part Image field")  # noqa: F841
    part_image: Optional[str] = Field(None, description="Part Image Rel field")  # noqa: F841
    msrp_mult_cad: Optional[str] = Field(None, description="Msrp Mult Cad field")  # noqa: F841
    transfer_mult_cad_dealer: Optional[str] = Field(None, description="Transfer Mult Cad Dealer field")  # noqa: F841
    msrp_mult_usd: Optional[str] = Field(None, description="Msrp Mult Usd field")  # noqa: F841
    transfer_mult_inc_to_corp: Optional[str] = Field(None, description="Transfer Mult Inc To Corp field")  # noqa: F841
    transfer_mult_usd_dealer: Optional[str] = Field(None, description="Transfer Mult Usd Dealer field")  # noqa: F841
    warehouse_mult: Optional[str] = Field(None, description="Warehouse Mult field")  # noqa: F841
    cost_cad: Optional[Decimal] = Field(None, description="Cost Cad field")  # noqa: F841
    msrp_cad: Optional[str] = Field(None, description="Msrp Cad field")  # noqa: F841
    dealer_cost_cad: Optional[Decimal] = Field(None, description="Dealer Cost Cad field")  # noqa: F841
    cost_usd: Optional[Decimal] = Field(None, description="Cost Usd field")  # noqa: F841
    msrp_usd: Optional[str] = Field(None, description="Msrp Usd field")  # noqa: F841
    dealer_cost_usd: Optional[Decimal] = Field(None, description="Dealer Cost Usd field")  # noqa: F841
    is_usd: Optional[bool] = Field(None, description="Is Usd field")  # noqa: F841
    cad_per_usd: Optional[str] = Field(None, description="Cad Per Usd field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Part"""  # noqa: F841
    worksheet: Optional[str] = Field(None, description="Worksheet field")  # noqa: F841
    ws_row: Optional[str] = Field(None, description="Ws Row field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    part_image: Optional[str] = Field(None, description="Part Image field")  # noqa: F841
    part_image: Optional[str] = Field(None, description="Part Image Rel field")  # noqa: F841
    msrp_mult_cad: Optional[str] = Field(None, description="Msrp Mult Cad field")  # noqa: F841
    transfer_mult_cad_dealer: Optional[str] = Field(None, description="Transfer Mult Cad Dealer field")  # noqa: F841
    msrp_mult_usd: Optional[str] = Field(None, description="Msrp Mult Usd field")  # noqa: F841
    transfer_mult_inc_to_corp: Optional[str] = Field(None, description="Transfer Mult Inc To Corp field")  # noqa: F841
    transfer_mult_usd_dealer: Optional[str] = Field(None, description="Transfer Mult Usd Dealer field")  # noqa: F841
    warehouse_mult: Optional[str] = Field(None, description="Warehouse Mult field")  # noqa: F841
    cost_cad: Optional[Decimal] = Field(None, description="Cost Cad field")  # noqa: F841
    msrp_cad: Optional[str] = Field(None, description="Msrp Cad field")  # noqa: F841
    dealer_cost_cad: Optional[Decimal] = Field(None, description="Dealer Cost Cad field")  # noqa: F841
    cost_usd: Optional[Decimal] = Field(None, description="Cost Usd field")  # noqa: F841
    msrp_usd: Optional[str] = Field(None, description="Msrp Usd field")  # noqa: F841
    dealer_cost_usd: Optional[Decimal] = Field(None, description="Dealer Cost Usd field")  # noqa: F841
    is_usd: Optional[bool] = Field(None, description="Is Usd field")  # noqa: F841
    cad_per_usd: Optional[str] = Field(None, description="Cad Per Usd field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Part"""  # noqa: F841
    worksheet: Optional[str] = Field(None, description="Worksheet field")  # noqa: F841
    ws_row: Optional[str] = Field(None, description="Ws Row field")  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    part_image: Optional[str] = Field(None, description="Part Image field")  # noqa: F841
    part_image: Optional[str] = Field(None, description="Part Image Rel field")  # noqa: F841
    msrp_mult_cad: Optional[str] = Field(None, description="Msrp Mult Cad field")  # noqa: F841
    transfer_mult_cad_dealer: Optional[str] = Field(None, description="Transfer Mult Cad Dealer field")  # noqa: F841
    msrp_mult_usd: Optional[str] = Field(None, description="Msrp Mult Usd field")  # noqa: F841
    transfer_mult_inc_to_corp: Optional[str] = Field(None, description="Transfer Mult Inc To Corp field")  # noqa: F841
    transfer_mult_usd_dealer: Optional[str] = Field(None, description="Transfer Mult Usd Dealer field")  # noqa: F841
    warehouse_mult: Optional[str] = Field(None, description="Warehouse Mult field")  # noqa: F841
    cost_cad: Optional[Decimal] = Field(None, description="Cost Cad field")  # noqa: F841
    msrp_cad: Optional[str] = Field(None, description="Msrp Cad field")  # noqa: F841
    dealer_cost_cad: Optional[Decimal] = Field(None, description="Dealer Cost Cad field")  # noqa: F841
    cost_usd: Optional[Decimal] = Field(None, description="Cost Usd field")  # noqa: F841
    msrp_usd: Optional[str] = Field(None, description="Msrp Usd field")  # noqa: F841
    dealer_cost_usd: Optional[Decimal] = Field(None, description="Dealer Cost Usd field")  # noqa: F841
    is_usd: Optional[bool] = Field(None, description="Is Usd field")  # noqa: F841
    cad_per_usd: Optional[str] = Field(None, description="Cad Per Usd field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartFilterSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PartFilter"""  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartFilterCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PartFilter"""  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PartFilterUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PartFilter"""  # noqa: F841
    part_num: Optional[str] = Field(None, description="Part Num field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RodSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Rod"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RodCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Rod"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class RodUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Rod"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BarrelSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Barrel"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BarrelCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Barrel"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class BarrelUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Barrel"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ShuttleValveSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ShuttleValve"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ShuttleValveCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ShuttleValve"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ShuttleValveUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ShuttleValve"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CheckValveSchema(BaseModel):  # noqa: F841
    """Pydantic schema for CheckValve"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CheckValveCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating CheckValve"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class CheckValveUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating CheckValve"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PackingGlandSchema(BaseModel):  # noqa: F841
    """Pydantic schema for PackingGland"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PackingGlandCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating PackingGland"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class PackingGlandUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating PackingGland"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HydPistonTypeSchema(BaseModel):  # noqa: F841
    """Pydantic schema for HydPistonType"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HydPistonTypeCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating HydPistonType"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HydPistonTypeUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating HydPistonType"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
  # noqa: F841
# CRUD Endpoints  # noqa: F841
  # noqa: F841
@router.get("/unit_types", response_model=ApiResponse[List[UnitTypeSchema]])  # noqa: F841
async def get_unit_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get UnitType records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(UnitType)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(UnitType, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(UnitType, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(UnitType, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching unit_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/unit_types/{item_id}", response_model=ApiResponse[UnitTypeSchema])  # noqa: F841
async def get_unit_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get UnitType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(UnitType).filter(UnitType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="UnitType not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching UnitType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/unit_types", response_model=ApiResponse[UnitTypeSchema])  # noqa: F841
async def create_unit_types(  # noqa: F841
    item_data: UnitTypeCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new UnitType"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = UnitType(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating UnitType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/unit_types/{item_id}", response_model=ApiResponse[UnitTypeSchema])  # noqa: F841
async def update_unit_types(  # noqa: F841
    item_id: int,
    item_data: UnitTypeUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update UnitType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(UnitType).filter(UnitType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="UnitType not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating UnitType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/unit_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_unit_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete UnitType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(UnitType).filter(UnitType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="UnitType not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "UnitType deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting UnitType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/structure_types", response_model=ApiResponse[List[ModelTypeSchema]])  # noqa: F841
async def get_structure_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ModelType records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ModelType)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ModelType, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ModelType, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ModelType, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching structure_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/structure_types/{item_id}", response_model=ApiResponse[ModelTypeSchema])  # noqa: F841
async def get_structure_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ModelType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ModelType).filter(ModelType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ModelType not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ModelType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/structure_types", response_model=ApiResponse[ModelTypeSchema])  # noqa: F841
async def create_structure_types(  # noqa: F841
    item_data: ModelTypeCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ModelType"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ModelType(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ModelType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/structure_types/{item_id}", response_model=ApiResponse[ModelTypeSchema])  # noqa: F841
async def update_structure_types(  # noqa: F841
    item_id: int,
    item_data: ModelTypeUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ModelType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ModelType).filter(ModelType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ModelType not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ModelType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/structure_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_structure_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ModelType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ModelType).filter(ModelType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ModelType not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ModelType deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ModelType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_types", response_model=ApiResponse[List[PowerUnitTypeSchema]])  # noqa: F841
async def get_power_unit_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Field description"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitType records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PowerUnitType)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PowerUnitType, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PowerUnitType, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PowerUnitType, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching power_unit_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/power_unit_types/{item_id}", response_model=ApiResponse[PowerUnitTypeSchema])  # noqa: F841
async def get_power_unit_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PowerUnitType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitType).filter(PowerUnitType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitType not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching PowerUnitType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/power_unit_types", response_model=ApiResponse[PowerUnitTypeSchema])  # noqa: F841
async def create_power_unit_types(  # noqa: F841
    item_data: PowerUnitTypeCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PowerUnitType"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PowerUnitType(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating PowerUnitType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/power_unit_types/{item_id}", response_model=ApiResponse[PowerUnitTypeSchema])  # noqa: F841
async def update_power_unit_types(  # noqa: F841
    item_id: int,
    item_data: PowerUnitTypeUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PowerUnitType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitType).filter(PowerUnitType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitType not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating PowerUnitType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/power_unit_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_power_unit_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PowerUnitType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PowerUnitType).filter(PowerUnitType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PowerUnitType not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "PowerUnitType deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting PowerUnitType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_pricing", response_model=ApiResponse[List[BOMPricingSchema]])  # noqa: F841
async def get_bom_pricing(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMPricing records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(BOMPricing)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(BOMPricing, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(BOMPricing, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(BOMPricing, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching bom_pricing: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_pricing/{item_id}", response_model=ApiResponse[BOMPricingSchema])  # noqa: F841
async def get_bom_pricing_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMPricing by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPricing).filter(BOMPricing.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPricing not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching BOMPricing: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/bom_pricing", response_model=ApiResponse[BOMPricingSchema])  # noqa: F841
async def create_bom_pricing(  # noqa: F841
    item_data: BOMPricingCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new BOMPricing"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = BOMPricing(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating BOMPricing: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/bom_pricing/{item_id}", response_model=ApiResponse[BOMPricingSchema])  # noqa: F841
async def update_bom_pricing(  # noqa: F841
    item_id: int,
    item_data: BOMPricingUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update BOMPricing by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPricing).filter(BOMPricing.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPricing not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating BOMPricing: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/bom_pricing/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_bom_pricing(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete BOMPricing by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPricing).filter(BOMPricing.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPricing not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "BOMPricing deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting BOMPricing: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_pricing_parts", response_model=ApiResponse[List[BOMPricingPartSchema]])  # noqa: F841
async def get_bom_pricing_parts(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMPricingPart records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(BOMPricingPart)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(BOMPricingPart, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(BOMPricingPart, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(BOMPricingPart, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching bom_pricing_parts: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_pricing_parts/{item_id}", response_model=ApiResponse[BOMPricingPartSchema])  # noqa: F841
async def get_bom_pricing_parts_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMPricingPart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPricingPart).filter(BOMPricingPart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPricingPart not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching BOMPricingPart: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/bom_pricing_parts", response_model=ApiResponse[BOMPricingPartSchema])  # noqa: F841
async def create_bom_pricing_parts(  # noqa: F841
    item_data: BOMPricingPartCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new BOMPricingPart"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = BOMPricingPart(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating BOMPricingPart: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/bom_pricing_parts/{item_id}", response_model=ApiResponse[BOMPricingPartSchema])  # noqa: F841
async def update_bom_pricing_parts(  # noqa: F841
    item_id: int,
    item_data: BOMPricingPartUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update BOMPricingPart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPricingPart).filter(BOMPricingPart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPricingPart not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating BOMPricingPart: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/bom_pricing_parts/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_bom_pricing_parts(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete BOMPricingPart by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPricingPart).filter(BOMPricingPart.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPricingPart not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "BOMPricingPart deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting BOMPricingPart: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_base_powerunit", response_model=ApiResponse[List[BOMBasePowerUnitSchema]])  # noqa: F841
async def get_bom_base_powerunit(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMBasePowerUnit records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(BOMBasePowerUnit)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(BOMBasePowerUnit, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(BOMBasePowerUnit, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(BOMBasePowerUnit, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching bom_base_powerunit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_base_powerunit/{item_id}", response_model=ApiResponse[BOMBasePowerUnitSchema])  # noqa: F841
async def get_bom_base_powerunit_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMBasePowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMBasePowerUnit).filter(BOMBasePowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMBasePowerUnit not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching BOMBasePowerUnit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/bom_base_powerunit", response_model=ApiResponse[BOMBasePowerUnitSchema])  # noqa: F841
async def create_bom_base_powerunit(  # noqa: F841
    item_data: BOMBasePowerUnitCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new BOMBasePowerUnit"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = BOMBasePowerUnit(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating BOMBasePowerUnit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/bom_base_powerunit/{item_id}", response_model=ApiResponse[BOMBasePowerUnitSchema])  # noqa: F841
async def update_bom_base_powerunit(  # noqa: F841
    item_id: int,
    item_data: BOMBasePowerUnitUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update BOMBasePowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMBasePowerUnit).filter(BOMBasePowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMBasePowerUnit not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating BOMBasePowerUnit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/bom_base_powerunit/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_bom_base_powerunit(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete BOMBasePowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMBasePowerUnit).filter(BOMBasePowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMBasePowerUnit not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "BOMBasePowerUnit deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting BOMBasePowerUnit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_powerunit", response_model=ApiResponse[List[BOMPowerUnitSchema]])  # noqa: F841
async def get_bom_powerunit(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMPowerUnit records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(BOMPowerUnit)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(BOMPowerUnit, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(BOMPowerUnit, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(BOMPowerUnit, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching bom_powerunit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_powerunit/{item_id}", response_model=ApiResponse[BOMPowerUnitSchema])  # noqa: F841
async def get_bom_powerunit_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMPowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPowerUnit).filter(BOMPowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPowerUnit not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching BOMPowerUnit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/bom_powerunit", response_model=ApiResponse[BOMPowerUnitSchema])  # noqa: F841
async def create_bom_powerunit(  # noqa: F841
    item_data: BOMPowerUnitCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new BOMPowerUnit"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = BOMPowerUnit(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating BOMPowerUnit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/bom_powerunit/{item_id}", response_model=ApiResponse[BOMPowerUnitSchema])  # noqa: F841
async def update_bom_powerunit(  # noqa: F841
    item_id: int,
    item_data: BOMPowerUnitUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update BOMPowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPowerUnit).filter(BOMPowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPowerUnit not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating BOMPowerUnit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/bom_powerunit/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_bom_powerunit(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete BOMPowerUnit by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPowerUnit).filter(BOMPowerUnit.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPowerUnit not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "BOMPowerUnit deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting BOMPowerUnit: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_structure", response_model=ApiResponse[List[BOMStructureSchema]])  # noqa: F841
async def get_bom_structure(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMStructure records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(BOMStructure)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(BOMStructure, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(BOMStructure, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(BOMStructure, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching bom_structure: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_structure/{item_id}", response_model=ApiResponse[BOMStructureSchema])  # noqa: F841
async def get_bom_structure_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMStructure by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMStructure).filter(BOMStructure.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMStructure not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching BOMStructure: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/bom_structure", response_model=ApiResponse[BOMStructureSchema])  # noqa: F841
async def create_bom_structure(  # noqa: F841
    item_data: BOMStructureCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new BOMStructure"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = BOMStructure(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating BOMStructure: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/bom_structure/{item_id}", response_model=ApiResponse[BOMStructureSchema])  # noqa: F841
async def update_bom_structure(  # noqa: F841
    item_id: int,
    item_data: BOMStructureUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update BOMStructure by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMStructure).filter(BOMStructure.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMStructure not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating BOMStructure: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/bom_structure/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_bom_structure(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete BOMStructure by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMStructure).filter(BOMStructure.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMStructure not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "BOMStructure deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting BOMStructure: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_pump_top", response_model=ApiResponse[List[BOMPumpTopSchema]])  # noqa: F841
async def get_bom_pump_top(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMPumpTop records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(BOMPumpTop)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(BOMPumpTop, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(BOMPumpTop, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(BOMPumpTop, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching bom_pump_top: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_pump_top/{item_id}", response_model=ApiResponse[BOMPumpTopSchema])  # noqa: F841
async def get_bom_pump_top_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMPumpTop by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPumpTop).filter(BOMPumpTop.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPumpTop not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching BOMPumpTop: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/bom_pump_top", response_model=ApiResponse[BOMPumpTopSchema])  # noqa: F841
async def create_bom_pump_top(  # noqa: F841
    item_data: BOMPumpTopCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new BOMPumpTop"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = BOMPumpTop(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating BOMPumpTop: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/bom_pump_top/{item_id}", response_model=ApiResponse[BOMPumpTopSchema])  # noqa: F841
async def update_bom_pump_top(  # noqa: F841
    item_id: int,
    item_data: BOMPumpTopUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update BOMPumpTop by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPumpTop).filter(BOMPumpTop.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPumpTop not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating BOMPumpTop: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/bom_pump_top/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_bom_pump_top(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete BOMPumpTop by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMPumpTop).filter(BOMPumpTop.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMPumpTop not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "BOMPumpTop deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting BOMPumpTop: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_dgas", response_model=ApiResponse[List[BOMDGASSchema]])  # noqa: F841
async def get_bom_dgas(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMDGAS records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(BOMDGAS)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(BOMDGAS, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(BOMDGAS, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(BOMDGAS, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching bom_dgas: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/bom_dgas/{item_id}", response_model=ApiResponse[BOMDGASSchema])  # noqa: F841
async def get_bom_dgas_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get BOMDGAS by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMDGAS).filter(BOMDGAS.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMDGAS not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching BOMDGAS: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/bom_dgas", response_model=ApiResponse[BOMDGASSchema])  # noqa: F841
async def create_bom_dgas(  # noqa: F841
    item_data: BOMDGASCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new BOMDGAS"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = BOMDGAS(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating BOMDGAS: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/bom_dgas/{item_id}", response_model=ApiResponse[BOMDGASSchema])  # noqa: F841
async def update_bom_dgas(  # noqa: F841
    item_id: int,
    item_data: BOMDGASUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update BOMDGAS by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMDGAS).filter(BOMDGAS.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMDGAS not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating BOMDGAS: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/bom_dgas/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_bom_dgas(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete BOMDGAS by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(BOMDGAS).filter(BOMDGAS.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="BOMDGAS not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "BOMDGAS deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting BOMDGAS: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/parts", response_model=ApiResponse[List[PartSchema]])  # noqa: F841
async def get_parts(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Part records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Part)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Part, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Part, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Part, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching parts: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/parts/{item_id}", response_model=ApiResponse[PartSchema])  # noqa: F841
async def get_parts_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Part by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Part).filter(Part.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Part not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Part: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/parts", response_model=ApiResponse[PartSchema])  # noqa: F841
async def create_parts(  # noqa: F841
    item_data: PartCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Part"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Part(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Part: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/parts/{item_id}", response_model=ApiResponse[PartSchema])  # noqa: F841
async def update_parts(  # noqa: F841
    item_id: int,
    item_data: PartUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Part by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Part).filter(Part.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Part not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Part: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/parts/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_parts(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Part by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Part).filter(Part.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Part not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Part deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Part: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/filters", response_model=ApiResponse[List[PartFilterSchema]])  # noqa: F841
async def get_filters(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PartFilter records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PartFilter)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PartFilter, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PartFilter, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PartFilter, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching filters: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/filters/{item_id}", response_model=ApiResponse[PartFilterSchema])  # noqa: F841
async def get_filters_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PartFilter by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PartFilter).filter(PartFilter.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PartFilter not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching PartFilter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/filters", response_model=ApiResponse[PartFilterSchema])  # noqa: F841
async def create_filters(  # noqa: F841
    item_data: PartFilterCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PartFilter"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PartFilter(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating PartFilter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/filters/{item_id}", response_model=ApiResponse[PartFilterSchema])  # noqa: F841
async def update_filters(  # noqa: F841
    item_id: int,
    item_data: PartFilterUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PartFilter by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PartFilter).filter(PartFilter.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PartFilter not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating PartFilter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/filters/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_filters(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PartFilter by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PartFilter).filter(PartFilter.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PartFilter not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "PartFilter deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting PartFilter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/rod_types", response_model=ApiResponse[List[RodSchema]])  # noqa: F841
async def get_rod_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Rod records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Rod)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Rod, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Rod, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Rod, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching rod_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/rod_types/{item_id}", response_model=ApiResponse[RodSchema])  # noqa: F841
async def get_rod_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Rod by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Rod).filter(Rod.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Rod not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Rod: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/rod_types", response_model=ApiResponse[RodSchema])  # noqa: F841
async def create_rod_types(  # noqa: F841
    item_data: RodCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Rod"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Rod(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Rod: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/rod_types/{item_id}", response_model=ApiResponse[RodSchema])  # noqa: F841
async def update_rod_types(  # noqa: F841
    item_id: int,
    item_data: RodUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Rod by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Rod).filter(Rod.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Rod not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Rod: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/rod_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_rod_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Rod by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Rod).filter(Rod.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Rod not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Rod deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Rod: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/barrel_types", response_model=ApiResponse[List[BarrelSchema]])  # noqa: F841
async def get_barrel_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Barrel records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Barrel)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Barrel, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Barrel, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Barrel, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching barrel_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/barrel_types/{item_id}", response_model=ApiResponse[BarrelSchema])  # noqa: F841
async def get_barrel_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Barrel by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Barrel).filter(Barrel.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Barrel not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Barrel: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/barrel_types", response_model=ApiResponse[BarrelSchema])  # noqa: F841
async def create_barrel_types(  # noqa: F841
    item_data: BarrelCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Barrel"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Barrel(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Barrel: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/barrel_types/{item_id}", response_model=ApiResponse[BarrelSchema])  # noqa: F841
async def update_barrel_types(  # noqa: F841
    item_id: int,
    item_data: BarrelUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Barrel by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Barrel).filter(Barrel.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Barrel not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Barrel: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/barrel_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_barrel_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Barrel by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Barrel).filter(Barrel.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Barrel not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Barrel deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Barrel: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/shuttle_valve_types", response_model=ApiResponse[List[ShuttleValveSchema]])  # noqa: F841
async def get_shuttle_valve_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ShuttleValve records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ShuttleValve)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ShuttleValve, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ShuttleValve, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ShuttleValve, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching shuttle_valve_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/shuttle_valve_types/{item_id}", response_model=ApiResponse[ShuttleValveSchema])  # noqa: F841
async def get_shuttle_valve_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ShuttleValve by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ShuttleValve).filter(ShuttleValve.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ShuttleValve not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ShuttleValve: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/shuttle_valve_types", response_model=ApiResponse[ShuttleValveSchema])  # noqa: F841
async def create_shuttle_valve_types(  # noqa: F841
    item_data: ShuttleValveCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ShuttleValve"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ShuttleValve(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ShuttleValve: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/shuttle_valve_types/{item_id}", response_model=ApiResponse[ShuttleValveSchema])  # noqa: F841
async def update_shuttle_valve_types(  # noqa: F841
    item_id: int,
    item_data: ShuttleValveUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ShuttleValve by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ShuttleValve).filter(ShuttleValve.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ShuttleValve not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ShuttleValve: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/shuttle_valve_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_shuttle_valve_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ShuttleValve by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ShuttleValve).filter(ShuttleValve.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ShuttleValve not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ShuttleValve deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ShuttleValve: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/check_valve_types", response_model=ApiResponse[List[CheckValveSchema]])  # noqa: F841
async def get_check_valve_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CheckValve records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(CheckValve)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(CheckValve, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(CheckValve, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(CheckValve, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching check_valve_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/check_valve_types/{item_id}", response_model=ApiResponse[CheckValveSchema])  # noqa: F841
async def get_check_valve_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get CheckValve by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CheckValve).filter(CheckValve.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CheckValve not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching CheckValve: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/check_valve_types", response_model=ApiResponse[CheckValveSchema])  # noqa: F841
async def create_check_valve_types(  # noqa: F841
    item_data: CheckValveCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new CheckValve"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = CheckValve(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating CheckValve: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/check_valve_types/{item_id}", response_model=ApiResponse[CheckValveSchema])  # noqa: F841
async def update_check_valve_types(  # noqa: F841
    item_id: int,
    item_data: CheckValveUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update CheckValve by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CheckValve).filter(CheckValve.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CheckValve not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating CheckValve: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/check_valve_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_check_valve_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete CheckValve by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(CheckValve).filter(CheckValve.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="CheckValve not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "CheckValve deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting CheckValve: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/packing_gland_types", response_model=ApiResponse[List[PackingGlandSchema]])  # noqa: F841
async def get_packing_gland_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PackingGland records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(PackingGland)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(PackingGland, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(PackingGland, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(PackingGland, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching packing_gland_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/packing_gland_types/{item_id}", response_model=ApiResponse[PackingGlandSchema])  # noqa: F841
async def get_packing_gland_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get PackingGland by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PackingGland).filter(PackingGland.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PackingGland not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching PackingGland: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/packing_gland_types", response_model=ApiResponse[PackingGlandSchema])  # noqa: F841
async def create_packing_gland_types(  # noqa: F841
    item_data: PackingGlandCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new PackingGland"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = PackingGland(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating PackingGland: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/packing_gland_types/{item_id}", response_model=ApiResponse[PackingGlandSchema])  # noqa: F841
async def update_packing_gland_types(  # noqa: F841
    item_id: int,
    item_data: PackingGlandUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update PackingGland by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PackingGland).filter(PackingGland.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PackingGland not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating PackingGland: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/packing_gland_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_packing_gland_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete PackingGland by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(PackingGland).filter(PackingGland.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="PackingGland not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "PackingGland deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting PackingGland: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hydraulic_piston_types", response_model=ApiResponse[List[HydPistonTypeSchema]])  # noqa: F841
async def get_hydraulic_piston_types(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get HydPistonType records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(HydPistonType)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(HydPistonType, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(HydPistonType, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(HydPistonType, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching hydraulic_piston_types: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hydraulic_piston_types/{item_id}", response_model=ApiResponse[HydPistonTypeSchema])  # noqa: F841
async def get_hydraulic_piston_types_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get HydPistonType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HydPistonType).filter(HydPistonType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HydPistonType not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching HydPistonType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/hydraulic_piston_types", response_model=ApiResponse[HydPistonTypeSchema])  # noqa: F841
async def create_hydraulic_piston_types(  # noqa: F841
    item_data: HydPistonTypeCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new HydPistonType"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = HydPistonType(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating HydPistonType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/hydraulic_piston_types/{item_id}", response_model=ApiResponse[HydPistonTypeSchema])  # noqa: F841
async def update_hydraulic_piston_types(  # noqa: F841
    item_id: int,
    item_data: HydPistonTypeUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update HydPistonType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HydPistonType).filter(HydPistonType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HydPistonType not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating HydPistonType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/hydraulic_piston_types/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_hydraulic_piston_types(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete HydPistonType by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HydPistonType).filter(HydPistonType.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HydPistonType not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "HydPistonType deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting HydPistonType: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841