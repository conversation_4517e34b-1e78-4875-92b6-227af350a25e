# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""  # noqa: F841
FastAPI Router for Reports resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841
  # noqa: F841
from datetime import datetime  # noqa: F841
from typing import List, Optional  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841
from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841
from sqlalchemy import desc, asc  # noqa: F841
  # noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841
  # noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
  # noqa: F841
  # noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/reports", tags=["Reports"])  # noqa: F841
  # noqa: F841
# Pydantic Schemas  # noqa: F841
class VwSalesByPersonYearSchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwSalesByPersonYear"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwSalesByPersonYearCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwSalesByPersonYear"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwSalesByPersonYearUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwSalesByPersonYear"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwSalesByPersonQuarterSchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwSalesByPersonQuarter"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_quarter: Optional[str] = Field(None, description="Service Quarter field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwSalesByPersonQuarterCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwSalesByPersonQuarter"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_quarter: Optional[str] = Field(None, description="Service Quarter field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwSalesByPersonQuarterUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwSalesByPersonQuarter"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_quarter: Optional[str] = Field(None, description="Service Quarter field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwSalesByPersonMonthSchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwSalesByPersonMonth"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwSalesByPersonMonthCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwSalesByPersonMonth"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwSalesByPersonMonthUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwSalesByPersonMonth"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    sales_warranty: Optional[str] = Field(None, description="Sales Warranty field")  # noqa: F841
    sales_total: Optional[str] = Field(None, description="Sales Total field")  # noqa: F841
    sales_labour: Optional[str] = Field(None, description="Sales Labour field")  # noqa: F841
    sales_parts: Optional[str] = Field(None, description="Sales Parts field")  # noqa: F841
    type_sales: Optional[str] = Field(None, description="Type Sales field")  # noqa: F841
    type_rentals: Optional[str] = Field(None, description="Type Rentals field")  # noqa: F841
    type_new_installs: Optional[str] = Field(None, description="Type New Installs field")  # noqa: F841
    type_repairs: Optional[str] = Field(None, description="Type Repairs field")  # noqa: F841
    type_parts: Optional[str] = Field(None, description="Type Parts field")  # noqa: F841
    type_prevent_maint: Optional[str] = Field(None, description="Type Prevent Maint field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HoursBilledMonthlyEfficiencySchema(BaseModel):  # noqa: F841
    """Pydantic schema for HoursBilledMonthlyEfficiency"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    billed_per_hour_worked: Optional[str] = Field(None, description="Billed Per Hour Worked field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HoursBilledMonthlyEfficiencyCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating HoursBilledMonthlyEfficiency"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    billed_per_hour_worked: Optional[str] = Field(None, description="Billed Per Hour Worked field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HoursBilledMonthlyEfficiencyUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating HoursBilledMonthlyEfficiency"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    billed_per_hour_worked: Optional[str] = Field(None, description="Billed Per Hour Worked field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HoursBilledByFieldTechMonthlyEfficiencySchema(BaseModel):  # noqa: F841
    """Pydantic schema for HoursBilledByFieldTechMonthlyEfficiency"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    full_name: Optional[str] = Field(None, description="Full Name field")  # noqa: F841
    billed_per_hour_worked: Optional[str] = Field(None, description="Billed Per Hour Worked field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HoursBilledByFieldTechMonthlyEfficiencyCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating HoursBilledByFieldTechMonthlyEfficiency"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    full_name: Optional[str] = Field(None, description="Full Name field")  # noqa: F841
    billed_per_hour_worked: Optional[str] = Field(None, description="Billed Per Hour Worked field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HoursBilledByFieldTechMonthlyEfficiencyUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating HoursBilledByFieldTechMonthlyEfficiency"""  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    full_name: Optional[str] = Field(None, description="Full Name field")  # noqa: F841
    billed_per_hour_worked: Optional[str] = Field(None, description="Billed Per Hour Worked field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwHoursBilledByFieldTechByWorkOrderSchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwHoursBilledByFieldTechByWorkOrder"""  # noqa: F841
    work_order_id: Optional[int] = Field(None, description="Work Order Id field")  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwHoursBilledByFieldTechByWorkOrderCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwHoursBilledByFieldTechByWorkOrder"""  # noqa: F841
    work_order_id: Optional[int] = Field(None, description="Work Order Id field")  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwHoursBilledByFieldTechByWorkOrderUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwHoursBilledByFieldTechByWorkOrder"""  # noqa: F841
    work_order_id: Optional[int] = Field(None, description="Work Order Id field")  # noqa: F841
    service_year: Optional[str] = Field(None, description="Service Year field")  # noqa: F841
    service_month: Optional[str] = Field(None, description="Service Month field")  # noqa: F841
    is_warranty: Optional[bool] = Field(None, description="Is Warranty field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    date_service: Optional[datetime] = Field(None, description="Date Service field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    monthly_hours_worked_clock: Optional[str] = Field(None, description="Monthly Hours Worked Clock field")  # noqa: F841
    quantity_hours_billed: Optional[int] = Field(None, description="Quantity Hours Billed field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursDailySchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwServiceClockHoursDaily"""  # noqa: F841
    year_: Optional[str] = Field(None, description="Year  field")  # noqa: F841
    month_: Optional[str] = Field(None, description="Month  field")  # noqa: F841
    day_: Optional[str] = Field(None, description="Day  field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    time_records: Optional[str] = Field(None, description="Time Records field")  # noqa: F841
    days_worked: Optional[str] = Field(None, description="Days Worked field")  # noqa: F841
    hours_worked: Optional[str] = Field(None, description="Hours Worked field")  # noqa: F841
    total_hours: Optional[str] = Field(None, description="Total Hours field")  # noqa: F841
    service_hours: Optional[str] = Field(None, description="Service Hours field")  # noqa: F841
    travel_hours: Optional[str] = Field(None, description="Travel Hours field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursDailyCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwServiceClockHoursDaily"""  # noqa: F841
    year_: Optional[str] = Field(None, description="Year  field")  # noqa: F841
    month_: Optional[str] = Field(None, description="Month  field")  # noqa: F841
    day_: Optional[str] = Field(None, description="Day  field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    time_records: Optional[str] = Field(None, description="Time Records field")  # noqa: F841
    days_worked: Optional[str] = Field(None, description="Days Worked field")  # noqa: F841
    hours_worked: Optional[str] = Field(None, description="Hours Worked field")  # noqa: F841
    total_hours: Optional[str] = Field(None, description="Total Hours field")  # noqa: F841
    service_hours: Optional[str] = Field(None, description="Service Hours field")  # noqa: F841
    travel_hours: Optional[str] = Field(None, description="Travel Hours field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursDailyUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwServiceClockHoursDaily"""  # noqa: F841
    year_: Optional[str] = Field(None, description="Year  field")  # noqa: F841
    month_: Optional[str] = Field(None, description="Month  field")  # noqa: F841
    day_: Optional[str] = Field(None, description="Day  field")  # noqa: F841
    name_: Optional[str] = Field(None, description="Name  field")  # noqa: F841
    time_records: Optional[str] = Field(None, description="Time Records field")  # noqa: F841
    days_worked: Optional[str] = Field(None, description="Days Worked field")  # noqa: F841
    hours_worked: Optional[str] = Field(None, description="Hours Worked field")  # noqa: F841
    total_hours: Optional[str] = Field(None, description="Total Hours field")  # noqa: F841
    service_hours: Optional[str] = Field(None, description="Service Hours field")  # noqa: F841
    travel_hours: Optional[str] = Field(None, description="Travel Hours field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursMonthlySchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwServiceClockHoursMonthly"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursMonthlyCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwServiceClockHoursMonthly"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursMonthlyUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwServiceClockHoursMonthly"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursYearlySchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwServiceClockHoursYearly"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursYearlyCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwServiceClockHoursYearly"""  # noqa: F841
    pass  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwServiceClockHoursYearlyUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwServiceClockHoursYearly"""  # noqa: F841
  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
  # noqa: F841
# CRUD Endpoints  # noqa: F841
  # noqa: F841
@router.get("/sales_by_person_year", response_model=ApiResponse[List[VwSalesByPersonYearSchema]])  # noqa: F841
async def get_sales_by_person_year(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwSalesByPersonYear records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwSalesByPersonYear)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwSalesByPersonYear, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwSalesByPersonYear, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwSalesByPersonYear, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching sales_by_person_year: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/sales_by_person_year/{item_id}", response_model=ApiResponse[VwSalesByPersonYearSchema])  # noqa: F841
async def get_sales_by_person_year_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwSalesByPersonYear by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonYear).filter(VwSalesByPersonYear.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonYear not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwSalesByPersonYear: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/sales_by_person_year", response_model=ApiResponse[VwSalesByPersonYearSchema])  # noqa: F841
async def create_sales_by_person_year(  # noqa: F841
    item_data: VwSalesByPersonYearCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwSalesByPersonYear"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwSalesByPersonYear(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwSalesByPersonYear: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/sales_by_person_year/{item_id}", response_model=ApiResponse[VwSalesByPersonYearSchema])  # noqa: F841
async def update_sales_by_person_year(  # noqa: F841
    item_id: int,
    item_data: VwSalesByPersonYearUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwSalesByPersonYear by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonYear).filter(VwSalesByPersonYear.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonYear not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwSalesByPersonYear: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/sales_by_person_year/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_sales_by_person_year(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwSalesByPersonYear by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonYear).filter(VwSalesByPersonYear.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonYear not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwSalesByPersonYear deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwSalesByPersonYear: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/sales_by_person_quarter", response_model=ApiResponse[List[VwSalesByPersonQuarterSchema]])  # noqa: F841
async def get_sales_by_person_quarter(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwSalesByPersonQuarter records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwSalesByPersonQuarter)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwSalesByPersonQuarter, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwSalesByPersonQuarter, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwSalesByPersonQuarter, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching sales_by_person_quarter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/sales_by_person_quarter/{item_id}", response_model=ApiResponse[VwSalesByPersonQuarterSchema])  # noqa: F841
async def get_sales_by_person_quarter_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwSalesByPersonQuarter by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonQuarter).filter(VwSalesByPersonQuarter.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonQuarter not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwSalesByPersonQuarter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/sales_by_person_quarter", response_model=ApiResponse[VwSalesByPersonQuarterSchema])  # noqa: F841
async def create_sales_by_person_quarter(  # noqa: F841
    item_data: VwSalesByPersonQuarterCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwSalesByPersonQuarter"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwSalesByPersonQuarter(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwSalesByPersonQuarter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/sales_by_person_quarter/{item_id}", response_model=ApiResponse[VwSalesByPersonQuarterSchema])  # noqa: F841
async def update_sales_by_person_quarter(  # noqa: F841
    item_id: int,
    item_data: VwSalesByPersonQuarterUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwSalesByPersonQuarter by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonQuarter).filter(VwSalesByPersonQuarter.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonQuarter not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwSalesByPersonQuarter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/sales_by_person_quarter/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_sales_by_person_quarter(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwSalesByPersonQuarter by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonQuarter).filter(VwSalesByPersonQuarter.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonQuarter not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwSalesByPersonQuarter deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwSalesByPersonQuarter: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/sales_by_person_month", response_model=ApiResponse[List[VwSalesByPersonMonthSchema]])  # noqa: F841
async def get_sales_by_person_month(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwSalesByPersonMonth records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwSalesByPersonMonth)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwSalesByPersonMonth, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwSalesByPersonMonth, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwSalesByPersonMonth, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching sales_by_person_month: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/sales_by_person_month/{item_id}", response_model=ApiResponse[VwSalesByPersonMonthSchema])  # noqa: F841
async def get_sales_by_person_month_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwSalesByPersonMonth by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonMonth).filter(VwSalesByPersonMonth.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonMonth not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwSalesByPersonMonth: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/sales_by_person_month", response_model=ApiResponse[VwSalesByPersonMonthSchema])  # noqa: F841
async def create_sales_by_person_month(  # noqa: F841
    item_data: VwSalesByPersonMonthCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwSalesByPersonMonth"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwSalesByPersonMonth(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwSalesByPersonMonth: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/sales_by_person_month/{item_id}", response_model=ApiResponse[VwSalesByPersonMonthSchema])  # noqa: F841
async def update_sales_by_person_month(  # noqa: F841
    item_id: int,
    item_data: VwSalesByPersonMonthUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwSalesByPersonMonth by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonMonth).filter(VwSalesByPersonMonth.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonMonth not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwSalesByPersonMonth: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/sales_by_person_month/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_sales_by_person_month(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwSalesByPersonMonth by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwSalesByPersonMonth).filter(VwSalesByPersonMonth.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwSalesByPersonMonth not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwSalesByPersonMonth deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwSalesByPersonMonth: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hours_billed_monthly_efficiency", response_model=ApiResponse[List[HoursBilledMonthlyEfficiencySchema]])  # noqa: F841
async def get_hours_billed_monthly_efficiency(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get HoursBilledMonthlyEfficiency records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(HoursBilledMonthlyEfficiency)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(HoursBilledMonthlyEfficiency, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(HoursBilledMonthlyEfficiency, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(HoursBilledMonthlyEfficiency, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching hours_billed_monthly_efficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hours_billed_monthly_efficiency/{item_id}", response_model=ApiResponse[HoursBilledMonthlyEfficiencySchema])  # noqa: F841
async def get_hours_billed_monthly_efficiency_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get HoursBilledMonthlyEfficiency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HoursBilledMonthlyEfficiency).filter(HoursBilledMonthlyEfficiency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HoursBilledMonthlyEfficiency not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching HoursBilledMonthlyEfficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/hours_billed_monthly_efficiency", response_model=ApiResponse[HoursBilledMonthlyEfficiencySchema])  # noqa: F841
async def create_hours_billed_monthly_efficiency(  # noqa: F841
    item_data: HoursBilledMonthlyEfficiencyCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new HoursBilledMonthlyEfficiency"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = HoursBilledMonthlyEfficiency(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating HoursBilledMonthlyEfficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/hours_billed_monthly_efficiency/{item_id}", response_model=ApiResponse[HoursBilledMonthlyEfficiencySchema])  # noqa: F841
async def update_hours_billed_monthly_efficiency(  # noqa: F841
    item_id: int,
    item_data: HoursBilledMonthlyEfficiencyUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update HoursBilledMonthlyEfficiency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HoursBilledMonthlyEfficiency).filter(HoursBilledMonthlyEfficiency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HoursBilledMonthlyEfficiency not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating HoursBilledMonthlyEfficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/hours_billed_monthly_efficiency/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_hours_billed_monthly_efficiency(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete HoursBilledMonthlyEfficiency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HoursBilledMonthlyEfficiency).filter(HoursBilledMonthlyEfficiency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HoursBilledMonthlyEfficiency not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "HoursBilledMonthlyEfficiency deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting HoursBilledMonthlyEfficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hours_billed_by_field_tech_monthly_efficiency", response_model=ApiResponse[List[HoursBilledByFieldTechMonthlyEfficiencySchema]])  # noqa: F841
async def get_hours_billed_by_field_tech_monthly_efficiency(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get HoursBilledByFieldTechMonthlyEfficiency records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(HoursBilledByFieldTechMonthlyEfficiency)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(HoursBilledByFieldTechMonthlyEfficiency, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(HoursBilledByFieldTechMonthlyEfficiency, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(HoursBilledByFieldTechMonthlyEfficiency, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching hours_billed_by_field_tech_monthly_efficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hours_billed_by_field_tech_monthly_efficiency/{item_id}", response_model=ApiResponse[HoursBilledByFieldTechMonthlyEfficiencySchema])  # noqa: F841
async def get_hours_billed_by_field_tech_monthly_efficiency_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get HoursBilledByFieldTechMonthlyEfficiency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HoursBilledByFieldTechMonthlyEfficiency).filter(HoursBilledByFieldTechMonthlyEfficiency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HoursBilledByFieldTechMonthlyEfficiency not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching HoursBilledByFieldTechMonthlyEfficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/hours_billed_by_field_tech_monthly_efficiency", response_model=ApiResponse[HoursBilledByFieldTechMonthlyEfficiencySchema])  # noqa: F841
async def create_hours_billed_by_field_tech_monthly_efficiency(  # noqa: F841
    item_data: HoursBilledByFieldTechMonthlyEfficiencyCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new HoursBilledByFieldTechMonthlyEfficiency"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = HoursBilledByFieldTechMonthlyEfficiency(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating HoursBilledByFieldTechMonthlyEfficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/hours_billed_by_field_tech_monthly_efficiency/{item_id}", response_model=ApiResponse[HoursBilledByFieldTechMonthlyEfficiencySchema])  # noqa: F841
async def update_hours_billed_by_field_tech_monthly_efficiency(  # noqa: F841
    item_id: int,
    item_data: HoursBilledByFieldTechMonthlyEfficiencyUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update HoursBilledByFieldTechMonthlyEfficiency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HoursBilledByFieldTechMonthlyEfficiency).filter(HoursBilledByFieldTechMonthlyEfficiency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HoursBilledByFieldTechMonthlyEfficiency not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating HoursBilledByFieldTechMonthlyEfficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/hours_billed_by_field_tech_monthly_efficiency/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_hours_billed_by_field_tech_monthly_efficiency(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete HoursBilledByFieldTechMonthlyEfficiency by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(HoursBilledByFieldTechMonthlyEfficiency).filter(HoursBilledByFieldTechMonthlyEfficiency.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="HoursBilledByFieldTechMonthlyEfficiency not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "HoursBilledByFieldTechMonthlyEfficiency deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting HoursBilledByFieldTechMonthlyEfficiency: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hours_billed_by_field_tech_by_work_order", response_model=ApiResponse[List[VwHoursBilledByFieldTechByWorkOrderSchema]])  # noqa: F841
async def get_hours_billed_by_field_tech_by_work_order(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwHoursBilledByFieldTechByWorkOrder records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwHoursBilledByFieldTechByWorkOrder)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwHoursBilledByFieldTechByWorkOrder, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwHoursBilledByFieldTechByWorkOrder, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwHoursBilledByFieldTechByWorkOrder, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching hours_billed_by_field_tech_by_work_order: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hours_billed_by_field_tech_by_work_order/{item_id}", response_model=ApiResponse[VwHoursBilledByFieldTechByWorkOrderSchema])  # noqa: F841
async def get_hours_billed_by_field_tech_by_work_order_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwHoursBilledByFieldTechByWorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwHoursBilledByFieldTechByWorkOrder).filter(VwHoursBilledByFieldTechByWorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwHoursBilledByFieldTechByWorkOrder not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwHoursBilledByFieldTechByWorkOrder: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/hours_billed_by_field_tech_by_work_order", response_model=ApiResponse[VwHoursBilledByFieldTechByWorkOrderSchema])  # noqa: F841
async def create_hours_billed_by_field_tech_by_work_order(  # noqa: F841
    item_data: VwHoursBilledByFieldTechByWorkOrderCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwHoursBilledByFieldTechByWorkOrder"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwHoursBilledByFieldTechByWorkOrder(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwHoursBilledByFieldTechByWorkOrder: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/hours_billed_by_field_tech_by_work_order/{item_id}", response_model=ApiResponse[VwHoursBilledByFieldTechByWorkOrderSchema])  # noqa: F841
async def update_hours_billed_by_field_tech_by_work_order(  # noqa: F841
    item_id: int,
    item_data: VwHoursBilledByFieldTechByWorkOrderUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwHoursBilledByFieldTechByWorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwHoursBilledByFieldTechByWorkOrder).filter(VwHoursBilledByFieldTechByWorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwHoursBilledByFieldTechByWorkOrder not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwHoursBilledByFieldTechByWorkOrder: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/hours_billed_by_field_tech_by_work_order/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_hours_billed_by_field_tech_by_work_order(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwHoursBilledByFieldTechByWorkOrder by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwHoursBilledByFieldTechByWorkOrder).filter(VwHoursBilledByFieldTechByWorkOrder.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwHoursBilledByFieldTechByWorkOrder not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwHoursBilledByFieldTechByWorkOrder deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwHoursBilledByFieldTechByWorkOrder: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/service_clock_daily", response_model=ApiResponse[List[VwServiceClockHoursDailySchema]])  # noqa: F841
async def get_service_clock_daily(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwServiceClockHoursDaily records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwServiceClockHoursDaily)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwServiceClockHoursDaily, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwServiceClockHoursDaily, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwServiceClockHoursDaily, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching service_clock_daily: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/service_clock_daily/{item_id}", response_model=ApiResponse[VwServiceClockHoursDailySchema])  # noqa: F841
async def get_service_clock_daily_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwServiceClockHoursDaily by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursDaily).filter(VwServiceClockHoursDaily.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursDaily not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwServiceClockHoursDaily: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/service_clock_daily", response_model=ApiResponse[VwServiceClockHoursDailySchema])  # noqa: F841
async def create_service_clock_daily(  # noqa: F841
    item_data: VwServiceClockHoursDailyCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwServiceClockHoursDaily"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwServiceClockHoursDaily(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwServiceClockHoursDaily: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/service_clock_daily/{item_id}", response_model=ApiResponse[VwServiceClockHoursDailySchema])  # noqa: F841
async def update_service_clock_daily(  # noqa: F841
    item_id: int,
    item_data: VwServiceClockHoursDailyUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwServiceClockHoursDaily by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursDaily).filter(VwServiceClockHoursDaily.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursDaily not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwServiceClockHoursDaily: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/service_clock_daily/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_service_clock_daily(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwServiceClockHoursDaily by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursDaily).filter(VwServiceClockHoursDaily.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursDaily not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwServiceClockHoursDaily deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwServiceClockHoursDaily: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/service_clock_monthly", response_model=ApiResponse[List[VwServiceClockHoursMonthlySchema]])  # noqa: F841
async def get_service_clock_monthly(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwServiceClockHoursMonthly records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwServiceClockHoursMonthly)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwServiceClockHoursMonthly, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwServiceClockHoursMonthly, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwServiceClockHoursMonthly, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching service_clock_monthly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/service_clock_monthly/{item_id}", response_model=ApiResponse[VwServiceClockHoursMonthlySchema])  # noqa: F841
async def get_service_clock_monthly_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwServiceClockHoursMonthly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursMonthly).filter(VwServiceClockHoursMonthly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursMonthly not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwServiceClockHoursMonthly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/service_clock_monthly", response_model=ApiResponse[VwServiceClockHoursMonthlySchema])  # noqa: F841
async def create_service_clock_monthly(  # noqa: F841
    item_data: VwServiceClockHoursMonthlyCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwServiceClockHoursMonthly"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwServiceClockHoursMonthly(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwServiceClockHoursMonthly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/service_clock_monthly/{item_id}", response_model=ApiResponse[VwServiceClockHoursMonthlySchema])  # noqa: F841
async def update_service_clock_monthly(  # noqa: F841
    item_id: int,
    item_data: VwServiceClockHoursMonthlyUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwServiceClockHoursMonthly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursMonthly).filter(VwServiceClockHoursMonthly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursMonthly not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwServiceClockHoursMonthly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/service_clock_monthly/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_service_clock_monthly(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwServiceClockHoursMonthly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursMonthly).filter(VwServiceClockHoursMonthly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursMonthly not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwServiceClockHoursMonthly deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwServiceClockHoursMonthly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/service_clock_yearly", response_model=ApiResponse[List[VwServiceClockHoursYearlySchema]])  # noqa: F841
async def get_service_clock_yearly(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwServiceClockHoursYearly records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwServiceClockHoursYearly)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwServiceClockHoursYearly, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwServiceClockHoursYearly, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwServiceClockHoursYearly, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching service_clock_yearly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/service_clock_yearly/{item_id}", response_model=ApiResponse[VwServiceClockHoursYearlySchema])  # noqa: F841
async def get_service_clock_yearly_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwServiceClockHoursYearly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursYearly).filter(VwServiceClockHoursYearly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursYearly not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwServiceClockHoursYearly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/service_clock_yearly", response_model=ApiResponse[VwServiceClockHoursYearlySchema])  # noqa: F841
async def create_service_clock_yearly(  # noqa: F841
    item_data: VwServiceClockHoursYearlyCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwServiceClockHoursYearly"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwServiceClockHoursYearly(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwServiceClockHoursYearly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/service_clock_yearly/{item_id}", response_model=ApiResponse[VwServiceClockHoursYearlySchema])  # noqa: F841
async def update_service_clock_yearly(  # noqa: F841
    item_id: int,
    item_data: VwServiceClockHoursYearlyUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwServiceClockHoursYearly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursYearly).filter(VwServiceClockHoursYearly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursYearly not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwServiceClockHoursYearly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/service_clock_yearly/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_service_clock_yearly(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwServiceClockHoursYearly by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwServiceClockHoursYearly).filter(VwServiceClockHoursYearly.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwServiceClockHoursYearly not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwServiceClockHoursYearly deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwServiceClockHoursYearly: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841