"""
Main router index for all Flask Admin resources
Auto-generated comprehensive FastAPI endpoints
"""

from fastapi import APIRouter

# Import all category routers
from .customers_router import router as customers_router
from .units_router import router as units_router
from .bom_router import router as bom_router
from .build_router import router as build_router
from .other_router import router as other_router
from .machine_learning_router import router as machine_learning_router
from .alerts_router import router as alerts_router
from .reports_router import router as reports_router
from .service_router import router as service_router
from .inventory_router import router as inventory_router

# Create main admin router
admin_router = APIRouter(prefix="/admin", tags=["Admin Resources"])

admin_router.include_router(customers_router)
admin_router.include_router(units_router)
admin_router.include_router(bom_router)
admin_router.include_router(build_router)
admin_router.include_router(other_router)
admin_router.include_router(machine_learning_router)
admin_router.include_router(alerts_router)
admin_router.include_router(reports_router)
admin_router.include_router(service_router)
admin_router.include_router(inventory_router)
