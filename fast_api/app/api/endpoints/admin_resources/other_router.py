# ruff: noqa: F403, F405
# Disable warnings for star imports and model usage in generated FastAPI routers

"""  # noqa: F841
FastAPI Router for Other resources  # noqa: F841
Auto-generated from Flask Admin extraction  # noqa: F841
"""  # noqa: F841
  # noqa: F841
from datetime import datetime  # noqa: F841
from typing import List, Optional  # noqa: F841
from pydantic import BaseModel, Field  # noqa: F841
from fastapi import APIRouter, Depends, HTTPException, Query  # noqa: F841
from sqlalchemy.orm import Session  # noqa: F841
from sqlalchemy import desc, asc  # noqa: F841
  # noqa: F841
from app.db.database import get_ijack_db  # noqa: F841
from app.schemas.response import ApiResponse  # noqa: F841
  # noqa: F841
# Import all models (you may need to adjust these imports based on your model structure)  # noqa: F841
from shared.models.models import *  # noqa: F403
from shared.models.models_bom import *  # noqa: F403
from shared.models.models_work_order import *  # noqa: F403
  # noqa: F841
  # noqa: F841
# Create router  # noqa: F841
router = APIRouter(prefix="/other", tags=["Other"])  # noqa: F841
  # noqa: F841
# Pydantic Schemas  # noqa: F841
# Skipped view without model: Redis DB Command Line Interface  # noqa: F841
class MetaDataTblSchema(BaseModel):  # noqa: F841
    """Pydantic schema for MetaDataTbl"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    id_cell: Optional[str] = Field(None, description="Id Cell field")  # noqa: F841
    element: Optional[str] = Field(None, description="Element field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class MetaDataTblCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating MetaDataTbl"""  # noqa: F841
    id_cell: Optional[str] = Field(None, description="Id Cell field")  # noqa: F841
    element: Optional[str] = Field(None, description="Element field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class MetaDataTblUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating MetaDataTbl"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    id_cell: Optional[str] = Field(None, description="Id Cell field")  # noqa: F841
    element: Optional[str] = Field(None, description="Element field")  # noqa: F841
    color: Optional[str] = Field(None, description="Color field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HourSchema(BaseModel):  # noqa: F841
    """Pydantic schema for Hour"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    hour: Optional[str] = Field(None, description="Hour field")  # noqa: F841
    hour_ending: Optional[str] = Field(None, description="Hour Ending field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HourCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating Hour"""  # noqa: F841
    hour: Optional[str] = Field(None, description="Hour field")  # noqa: F841
    hour_ending: Optional[str] = Field(None, description="Hour Ending field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class HourUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating Hour"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    hour: Optional[str] = Field(None, description="Hour field")  # noqa: F841
    hour_ending: Optional[str] = Field(None, description="Hour Ending field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ErrorLogSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ErrorLog"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    error_type: Optional[str] = Field(None, description="Error Type field")  # noqa: F841
    error_message: Optional[str] = Field(None, description="Error Message field")  # noqa: F841
    request_url: Optional[str] = Field(None, description="Request Url field")  # noqa: F841
    environment: Optional[str] = Field(None, description="Environment field")  # noqa: F841
    status_code: Optional[str] = Field(None, description="Status Code field")  # noqa: F841
    resolved: Optional[str] = Field(None, description="Resolved field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ErrorLogCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ErrorLog"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    error_type: Optional[str] = Field(None, description="Error Type field")  # noqa: F841
    error_message: Optional[str] = Field(None, description="Error Message field")  # noqa: F841
    request_url: Optional[str] = Field(None, description="Request Url field")  # noqa: F841
    environment: Optional[str] = Field(None, description="Environment field")  # noqa: F841
    status_code: Optional[str] = Field(None, description="Status Code field")  # noqa: F841
    resolved: Optional[str] = Field(None, description="Resolved field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ErrorLogUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ErrorLog"""  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    user: Optional[str] = Field(None, description="User Rel field")  # noqa: F841
    error_type: Optional[str] = Field(None, description="Error Type field")  # noqa: F841
    error_message: Optional[str] = Field(None, description="Error Message field")  # noqa: F841
    request_url: Optional[str] = Field(None, description="Request Url field")  # noqa: F841
    environment: Optional[str] = Field(None, description="Environment field")  # noqa: F841
    status_code: Optional[str] = Field(None, description="Status Code field")  # noqa: F841
    resolved: Optional[str] = Field(None, description="Resolved field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwProfilerSchema(BaseModel):  # noqa: F841
    """Pydantic schema for VwProfiler"""  # noqa: F841
    full_name: Optional[str] = Field(None, description="Full Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    status_code: Optional[str] = Field(None, description="Status Code field")  # noqa: F841
    timestamp_utc_started: Optional[datetime] = Field(None, description="Timestamp Utc Started field")  # noqa: F841
    timestamp_utc_ended: Optional[datetime] = Field(None, description="Timestamp Utc Ended field")  # noqa: F841
    elapsed: Optional[str] = Field(None, description="Elapsed field")  # noqa: F841
    cpu_start: Optional[str] = Field(None, description="Cpu Start field")  # noqa: F841
    cpu_end: Optional[str] = Field(None, description="Cpu End field")  # noqa: F841
    endpoint_name: Optional[str] = Field(None, description="Endpoint Name field")  # noqa: F841
    referrer: Optional[str] = Field(None, description="Referrer field")  # noqa: F841
    method: Optional[str] = Field(None, description="Method field")  # noqa: F841
    args: Optional[str] = Field(None, description="Args field")  # noqa: F841
    kwargs: Optional[str] = Field(None, description="Kwargs field")  # noqa: F841
    query_string: Optional[str] = Field(None, description="Query String field")  # noqa: F841
    form: Optional[str] = Field(None, description="Form field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    files: Optional[str] = Field(None, description="Files field")  # noqa: F841
    path: Optional[str] = Field(None, description="Path field")  # noqa: F841
    request_args: Optional[str] = Field(None, description="Request Args field")  # noqa: F841
    scheme: Optional[str] = Field(None, description="Scheme field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwProfilerCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating VwProfiler"""  # noqa: F841
    full_name: Optional[str] = Field(None, description="Full Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    status_code: Optional[str] = Field(None, description="Status Code field")  # noqa: F841
    timestamp_utc_started: Optional[datetime] = Field(None, description="Timestamp Utc Started field")  # noqa: F841
    timestamp_utc_ended: Optional[datetime] = Field(None, description="Timestamp Utc Ended field")  # noqa: F841
    elapsed: Optional[str] = Field(None, description="Elapsed field")  # noqa: F841
    cpu_start: Optional[str] = Field(None, description="Cpu Start field")  # noqa: F841
    cpu_end: Optional[str] = Field(None, description="Cpu End field")  # noqa: F841
    endpoint_name: Optional[str] = Field(None, description="Endpoint Name field")  # noqa: F841
    referrer: Optional[str] = Field(None, description="Referrer field")  # noqa: F841
    method: Optional[str] = Field(None, description="Method field")  # noqa: F841
    args: Optional[str] = Field(None, description="Args field")  # noqa: F841
    kwargs: Optional[str] = Field(None, description="Kwargs field")  # noqa: F841
    query_string: Optional[str] = Field(None, description="Query String field")  # noqa: F841
    form: Optional[str] = Field(None, description="Form field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    files: Optional[str] = Field(None, description="Files field")  # noqa: F841
    path: Optional[str] = Field(None, description="Path field")  # noqa: F841
    request_args: Optional[str] = Field(None, description="Request Args field")  # noqa: F841
    scheme: Optional[str] = Field(None, description="Scheme field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class VwProfilerUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating VwProfiler"""  # noqa: F841
    full_name: Optional[str] = Field(None, description="Full Name field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    status_code: Optional[str] = Field(None, description="Status Code field")  # noqa: F841
    timestamp_utc_started: Optional[datetime] = Field(None, description="Timestamp Utc Started field")  # noqa: F841
    timestamp_utc_ended: Optional[datetime] = Field(None, description="Timestamp Utc Ended field")  # noqa: F841
    elapsed: Optional[str] = Field(None, description="Elapsed field")  # noqa: F841
    cpu_start: Optional[str] = Field(None, description="Cpu Start field")  # noqa: F841
    cpu_end: Optional[str] = Field(None, description="Cpu End field")  # noqa: F841
    endpoint_name: Optional[str] = Field(None, description="Endpoint Name field")  # noqa: F841
    referrer: Optional[str] = Field(None, description="Referrer field")  # noqa: F841
    method: Optional[str] = Field(None, description="Method field")  # noqa: F841
    args: Optional[str] = Field(None, description="Args field")  # noqa: F841
    kwargs: Optional[str] = Field(None, description="Kwargs field")  # noqa: F841
    query_string: Optional[str] = Field(None, description="Query String field")  # noqa: F841
    form: Optional[str] = Field(None, description="Form field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    files: Optional[str] = Field(None, description="Files field")  # noqa: F841
    path: Optional[str] = Field(None, description="Path field")  # noqa: F841
    request_args: Optional[str] = Field(None, description="Request Args field")  # noqa: F841
    scheme: Optional[str] = Field(None, description="Scheme field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WebsiteView"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    company: Optional[str] = Field(None, description="Company field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    region: Optional[str] = Field(None, description="Region field")  # noqa: F841
    timezone: Optional[str] = Field(None, description="Timezone field")  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WebsiteView"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    company: Optional[str] = Field(None, description="Company field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    region: Optional[str] = Field(None, description="Region field")  # noqa: F841
    timezone: Optional[str] = Field(None, description="Timezone field")  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WebsiteView"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    company: Optional[str] = Field(None, description="Company field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    region: Optional[str] = Field(None, description="Region field")  # noqa: F841
    timezone: Optional[str] = Field(None, description="Timezone field")  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewMostActiveSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WebsiteViewMostActive"""  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    count_: Optional[int] = Field(None, description="Count  field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Last Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Phone field")  # noqa: F841
    earliest_date_in_sample: Optional[datetime] = Field(None, description="Earliest Date In Sample field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewMostActiveCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WebsiteViewMostActive"""  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    count_: Optional[int] = Field(None, description="Count  field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Last Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Phone field")  # noqa: F841
    earliest_date_in_sample: Optional[datetime] = Field(None, description="Earliest Date In Sample field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewMostActiveUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WebsiteViewMostActive"""  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    count_: Optional[int] = Field(None, description="Count  field")  # noqa: F841
    customer: Optional[str] = Field(None, description="Customer field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Last Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Email field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Phone field")  # noqa: F841
    earliest_date_in_sample: Optional[datetime] = Field(None, description="Earliest Date In Sample field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewSchema(BaseModel):  # noqa: F841
    """Pydantic schema for WebsiteView"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    count_records: Optional[int] = Field(None, description="Count Records field")  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    customers_rel: Optional[str] = Field(None, description="Users Rel.Customers Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Users Rel.Phone field")  # noqa: F841
    company: Optional[str] = Field(None, description="Company field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    region: Optional[str] = Field(None, description="Region field")  # noqa: F841
    timezone: Optional[str] = Field(None, description="Timezone field")  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating WebsiteView"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    count_records: Optional[int] = Field(None, description="Count Records field")  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    customers_rel: Optional[str] = Field(None, description="Users Rel.Customers Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Users Rel.Phone field")  # noqa: F841
    company: Optional[str] = Field(None, description="Company field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    region: Optional[str] = Field(None, description="Region field")  # noqa: F841
    timezone: Optional[str] = Field(None, description="Timezone field")  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class WebsiteViewUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating WebsiteView"""  # noqa: F841
    timestamp_utc: Optional[datetime] = Field(None, description="Timestamp Utc field")  # noqa: F841
    count_records: Optional[int] = Field(None, description="Count Records field")  # noqa: F841
    page: Optional[str] = Field(None, description="Page field")  # noqa: F841
    customers_rel: Optional[str] = Field(None, description="Users Rel.Customers Rel field")  # noqa: F841
    users: Optional[str] = Field(None, description="Users Rel field")  # noqa: F841
    first_name: Optional[str] = Field(None, description="Users Rel.First Name field")  # noqa: F841
    last_name: Optional[str] = Field(None, description="Users Rel.Last Name field")  # noqa: F841
    email: Optional[str] = Field(None, description="Users Rel.Email field")  # noqa: F841
    phone: Optional[str] = Field(None, description="Users Rel.Phone field")  # noqa: F841
    company: Optional[str] = Field(None, description="Company field")  # noqa: F841
    city: Optional[str] = Field(None, description="City field")  # noqa: F841
    region: Optional[str] = Field(None, description="Region field")  # noqa: F841
    timezone: Optional[str] = Field(None, description="Timezone field")  # noqa: F841
    country_code: Optional[int] = Field(None, description="Country Code field")  # noqa: F841
    country_name: Optional[int] = Field(None, description="Country Name field")  # noqa: F841
    ip: Optional[str] = Field(None, description="Ip field")  # noqa: F841
    postal: Optional[str] = Field(None, description="Postal field")  # noqa: F841
    gps_lat: Optional[str] = Field(None, description="Gps Lat field")  # noqa: F841
    gps_lon: Optional[str] = Field(None, description="Gps Lon field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ImageFieldSchema(BaseModel):  # noqa: F841
    """Pydantic schema for ImageField"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    image: Optional[str] = Field(None, description="Image field")  # noqa: F841
    format: Optional[str] = Field(None, description="Format field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ImageFieldCreateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for creating ImageField"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    image: Optional[str] = Field(None, description="Image field")  # noqa: F841
    format: Optional[str] = Field(None, description="Format field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
class ImageFieldUpdateSchema(BaseModel):  # noqa: F841
    """Pydantic schema for updating ImageField"""  # noqa: F841
    name: Optional[str] = Field(None, description="Name field")  # noqa: F841
    description: Optional[str] = Field(None, description="Description field")  # noqa: F841
    image: Optional[str] = Field(None, description="Image field")  # noqa: F841
    format: Optional[str] = Field(None, description="Format field")  # noqa: F841
    id: Optional[str] = Field(None, description="Id field")  # noqa: F841
      # noqa: F841
    class Config:  # noqa: F841
        from_attributes = True  # noqa: F841
  # noqa: F841
  # noqa: F841
# CRUD Endpoints  # noqa: F841
# Skipped endpoints for view without model: Redis DB Command Line Interface  # noqa: F841
  # noqa: F841
@router.get("/meta_data", response_model=ApiResponse[List[MetaDataTblSchema]])  # noqa: F841
async def get_meta_data(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    sort_by: Optional[str] = Query(None, description="Sort by field: c, o, l, u, m"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get MetaDataTbl records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(MetaDataTbl)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(MetaDataTbl, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(MetaDataTbl, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(MetaDataTbl, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching meta_data: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/meta_data/{item_id}", response_model=ApiResponse[MetaDataTblSchema])  # noqa: F841
async def get_meta_data_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get MetaDataTbl by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MetaDataTbl).filter(MetaDataTbl.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MetaDataTbl not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching MetaDataTbl: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/meta_data", response_model=ApiResponse[MetaDataTblSchema])  # noqa: F841
async def create_meta_data(  # noqa: F841
    item_data: MetaDataTblCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new MetaDataTbl"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = MetaDataTbl(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating MetaDataTbl: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/meta_data/{item_id}", response_model=ApiResponse[MetaDataTblSchema])  # noqa: F841
async def update_meta_data(  # noqa: F841
    item_id: int,
    item_data: MetaDataTblUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update MetaDataTbl by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MetaDataTbl).filter(MetaDataTbl.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MetaDataTbl not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating MetaDataTbl: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/meta_data/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_meta_data(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete MetaDataTbl by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(MetaDataTbl).filter(MetaDataTbl.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="MetaDataTbl not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "MetaDataTbl deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting MetaDataTbl: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hours", response_model=ApiResponse[List[HourSchema]])  # noqa: F841
async def get_hours(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Hour records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(Hour)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(Hour, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(Hour, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(Hour, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching hours: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/hours/{item_id}", response_model=ApiResponse[HourSchema])  # noqa: F841
async def get_hours_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get Hour by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Hour).filter(Hour.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Hour not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching Hour: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/hours", response_model=ApiResponse[HourSchema])  # noqa: F841
async def create_hours(  # noqa: F841
    item_data: HourCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new Hour"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = Hour(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating Hour: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/hours/{item_id}", response_model=ApiResponse[HourSchema])  # noqa: F841
async def update_hours(  # noqa: F841
    item_id: int,
    item_data: HourUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update Hour by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Hour).filter(Hour.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Hour not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating Hour: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/hours/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_hours(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete Hour by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(Hour).filter(Hour.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="Hour not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "Hour deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting Hour: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/error_logs", response_model=ApiResponse[List[ErrorLogSchema]])  # noqa: F841
async def get_error_logs(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: "),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ErrorLog records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ErrorLog)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ErrorLog, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ErrorLog, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ErrorLog, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching error_logs: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/error_logs/{item_id}", response_model=ApiResponse[ErrorLogSchema])  # noqa: F841
async def get_error_logs_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ErrorLog by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ErrorLog).filter(ErrorLog.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ErrorLog not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ErrorLog: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/error_logs", response_model=ApiResponse[ErrorLogSchema])  # noqa: F841
async def create_error_logs(  # noqa: F841
    item_data: ErrorLogCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ErrorLog"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ErrorLog(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ErrorLog: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/error_logs/{item_id}", response_model=ApiResponse[ErrorLogSchema])  # noqa: F841
async def update_error_logs(  # noqa: F841
    item_id: int,
    item_data: ErrorLogUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ErrorLog by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ErrorLog).filter(ErrorLog.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ErrorLog not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ErrorLog: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/error_logs/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_error_logs(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ErrorLog by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ErrorLog).filter(ErrorLog.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ErrorLog not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ErrorLog deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ErrorLog: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/slow_requests", response_model=ApiResponse[List[VwProfilerSchema]])  # noqa: F841
async def get_slow_requests(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    sort_by: Optional[str] = Query(None, description="Sort by field: c, o, l, u, m"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwProfiler records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(VwProfiler)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(VwProfiler, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(VwProfiler, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(VwProfiler, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching slow_requests: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/slow_requests/{item_id}", response_model=ApiResponse[VwProfilerSchema])  # noqa: F841
async def get_slow_requests_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get VwProfiler by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwProfiler).filter(VwProfiler.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwProfiler not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching VwProfiler: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/slow_requests", response_model=ApiResponse[VwProfilerSchema])  # noqa: F841
async def create_slow_requests(  # noqa: F841
    item_data: VwProfilerCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new VwProfiler"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = VwProfiler(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating VwProfiler: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/slow_requests/{item_id}", response_model=ApiResponse[VwProfilerSchema])  # noqa: F841
async def update_slow_requests(  # noqa: F841
    item_id: int,
    item_data: VwProfilerUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update VwProfiler by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwProfiler).filter(VwProfiler.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwProfiler not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating VwProfiler: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/slow_requests/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_slow_requests(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete VwProfiler by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(VwProfiler).filter(VwProfiler.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="VwProfiler not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "VwProfiler deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting VwProfiler: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/website_views", response_model=ApiResponse[List[WebsiteViewSchema]])  # noqa: F841
async def get_website_views(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WebsiteView records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WebsiteView)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WebsiteView, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WebsiteView, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WebsiteView, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching website_views: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/website_views/{item_id}", response_model=ApiResponse[WebsiteViewSchema])  # noqa: F841
async def get_website_views_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WebsiteView by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteView).filter(WebsiteView.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteView not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching WebsiteView: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/website_views", response_model=ApiResponse[WebsiteViewSchema])  # noqa: F841
async def create_website_views(  # noqa: F841
    item_data: WebsiteViewCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WebsiteView"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WebsiteView(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating WebsiteView: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/website_views/{item_id}", response_model=ApiResponse[WebsiteViewSchema])  # noqa: F841
async def update_website_views(  # noqa: F841
    item_id: int,
    item_data: WebsiteViewUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WebsiteView by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteView).filter(WebsiteView.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteView not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating WebsiteView: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/website_views/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_website_views(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WebsiteView by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteView).filter(WebsiteView.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteView not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "WebsiteView deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting WebsiteView: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/website_most_active_users", response_model=ApiResponse[List[WebsiteViewMostActiveSchema]])  # noqa: F841
async def get_website_most_active_users(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WebsiteViewMostActive records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WebsiteViewMostActive)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WebsiteViewMostActive, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WebsiteViewMostActive, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WebsiteViewMostActive, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching website_most_active_users: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/website_most_active_users/{item_id}", response_model=ApiResponse[WebsiteViewMostActiveSchema])  # noqa: F841
async def get_website_most_active_users_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WebsiteViewMostActive by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteViewMostActive).filter(WebsiteViewMostActive.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteViewMostActive not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching WebsiteViewMostActive: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/website_most_active_users", response_model=ApiResponse[WebsiteViewMostActiveSchema])  # noqa: F841
async def create_website_most_active_users(  # noqa: F841
    item_data: WebsiteViewMostActiveCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WebsiteViewMostActive"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WebsiteViewMostActive(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating WebsiteViewMostActive: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/website_most_active_users/{item_id}", response_model=ApiResponse[WebsiteViewMostActiveSchema])  # noqa: F841
async def update_website_most_active_users(  # noqa: F841
    item_id: int,
    item_data: WebsiteViewMostActiveUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WebsiteViewMostActive by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteViewMostActive).filter(WebsiteViewMostActive.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteViewMostActive not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating WebsiteViewMostActive: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/website_most_active_users/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_website_most_active_users(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WebsiteViewMostActive by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteViewMostActive).filter(WebsiteViewMostActive.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteViewMostActive not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "WebsiteViewMostActive deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting WebsiteViewMostActive: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/website_most_active_users_filterable", response_model=ApiResponse[List[WebsiteViewSchema]])  # noqa: F841
async def get_website_most_active_users_filterable(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WebsiteView records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(WebsiteView)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(WebsiteView, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(WebsiteView, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(WebsiteView, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching website_most_active_users_filterable: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/website_most_active_users_filterable/{item_id}", response_model=ApiResponse[WebsiteViewSchema])  # noqa: F841
async def get_website_most_active_users_filterable_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get WebsiteView by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteView).filter(WebsiteView.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteView not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching WebsiteView: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/website_most_active_users_filterable", response_model=ApiResponse[WebsiteViewSchema])  # noqa: F841
async def create_website_most_active_users_filterable(  # noqa: F841
    item_data: WebsiteViewCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new WebsiteView"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = WebsiteView(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating WebsiteView: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/website_most_active_users_filterable/{item_id}", response_model=ApiResponse[WebsiteViewSchema])  # noqa: F841
async def update_website_most_active_users_filterable(  # noqa: F841
    item_id: int,
    item_data: WebsiteViewUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update WebsiteView by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteView).filter(WebsiteView.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteView not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating WebsiteView: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/website_most_active_users_filterable/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_website_most_active_users_filterable(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete WebsiteView by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(WebsiteView).filter(WebsiteView.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="WebsiteView not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "WebsiteView deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting WebsiteView: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/images", response_model=ApiResponse[List[ImageFieldSchema]])  # noqa: F841
async def get_images(  # noqa: F841
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    page_size: int = Query(50, ge=1, le=1000, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search across searchable fields"),
    sort_by: Optional[str] = Query(None, description="Sort by field: c, o, l, u, m"),
    sort_order: Optional[str] = Query("asc", description="Sort order: asc or desc"),
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ImageField records with pagination, search, and sorting"""  # noqa: F841
    try:  # noqa: F841
        query = db.query(ImageField)  # noqa: F841
          # noqa: F841
        # Apply search if provided  # noqa: F841
        if search:  # noqa: F841
            # Add search logic for searchable fields  # noqa: F841
            pass  # TODO: Implement search logic  # noqa: F841
              # noqa: F841
        # Apply sorting if provided  # noqa: F841
        if sort_by and hasattr(ImageField, sort_by):  # noqa: F841
            if sort_order == "desc":  # noqa: F841
                query = query.order_by(desc(getattr(ImageField, sort_by)))  # noqa: F841
            else:  # noqa: F841
                query = query.order_by(asc(getattr(ImageField, sort_by)))  # noqa: F841
                  # noqa: F841
        # Apply pagination  # noqa: F841
        total = query.count()  # noqa: F841
        items = query.offset(skip).limit(page_size).all()  # noqa: F841
          # noqa: F841
        return {"result": items, "total": total}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching images: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.get("/images/{item_id}", response_model=ApiResponse[ImageFieldSchema])  # noqa: F841
async def get_images_by_id(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Get ImageField by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ImageField).filter(ImageField.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ImageField not found")  # noqa: F841
        return {"result": item}  # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error fetching ImageField: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.post("/images", response_model=ApiResponse[ImageFieldSchema])  # noqa: F841
async def create_images(  # noqa: F841
    item_data: ImageFieldCreateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Create new ImageField"""  # noqa: F841
    try:  # noqa: F841
        # Convert Pydantic model to SQLAlchemy model  # noqa: F841
        item_dict = item_data.model_dump(exclude_unset=True)  # noqa: F841
        new_item = ImageField(**item_dict)  # noqa: F841
          # noqa: F841
        db.add(new_item)  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(new_item)  # noqa: F841
          # noqa: F841
        return {"result": new_item}  # noqa: F841
          # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error creating ImageField: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.put("/images/{item_id}", response_model=ApiResponse[ImageFieldSchema])  # noqa: F841
async def update_images(  # noqa: F841
    item_id: int,
    item_data: ImageFieldUpdateSchema,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Update ImageField by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ImageField).filter(ImageField.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ImageField not found")  # noqa: F841
              # noqa: F841
        # Update only provided fields  # noqa: F841
        update_data = item_data.model_dump(exclude_unset=True, exclude_none=True)  # noqa: F841
        for field, value in update_data.items():  # noqa: F841
            if hasattr(item, field):  # noqa: F841
                setattr(item, field, value)  # noqa: F841
                  # noqa: F841
        db.commit()  # noqa: F841
        db.refresh(item)  # noqa: F841
          # noqa: F841
        return {"result": item}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error updating ImageField: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841
@router.delete("/images/{item_id}", response_model=ApiResponse[dict])  # noqa: F841
async def delete_images(  # noqa: F841
    item_id: int,
    db: Session = Depends(get_ijack_db)
):  # noqa: F841
    """Delete ImageField by ID"""  # noqa: F841
    try:  # noqa: F841
        item = db.query(ImageField).filter(ImageField.id == item_id).first()  # noqa: F841
        if not item:  # noqa: F841
            raise HTTPException(status_code=404, detail="ImageField not found")  # noqa: F841
              # noqa: F841
        db.delete(item)  # noqa: F841
        db.commit()  # noqa: F841
          # noqa: F841
        return {"result": {"message": "ImageField deleted successfully", "id": item_id}}  # noqa: F841
          # noqa: F841
    except HTTPException:  # noqa: F841
        raise  # noqa: F841
    except Exception as e:  # noqa: F841
        db.rollback()  # noqa: F841
        raise HTTPException(status_code=500, detail=f"Error deleting ImageField: {str(e)}")  # noqa: F841
  # noqa: F841
  # noqa: F841