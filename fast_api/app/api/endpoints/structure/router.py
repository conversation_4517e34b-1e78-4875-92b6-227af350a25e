from fastapi import APIRouter, Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV

from app.auth.session import roles_required

from .models import router as models_router
from .structure import router as structure_router

router = APIRouter(
    prefix="/structure",
    tags=["structures", "sales"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)

router.include_router(models_router)
router.include_router(structure_router)
