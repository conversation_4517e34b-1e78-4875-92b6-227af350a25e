from fastapi import APIRouter, Depends
from shared.models.models import UnitType
from shared.models.models_bom import ModelType
from sqlalchemy import select

from app.db.database import get_ijack_db
from app.schemas.response import ApiResponse

from .schemas import ModelSchema, UnitTypeSchema

router = APIRouter(tags=["models"])


@router.get("/model")
async def get_models(
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[ModelSchema]]:
    """Get all models from the database"""
    query = (
        select(ModelType.id, ModelType.model, UnitType.unit_type)
        .join(UnitType, ModelType.unit_type_id == UnitType.id)
        .order_by(ModelType.unit_type_id, ModelType.model)
    )
    result = await ijack_db.execute(query)
    records = result.all()
    return {"result": records}


@router.get("/unit-type")
async def get_unit_types(
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[UnitTypeSchema]]:
    """Get all unit types from the database"""
    query = (
        select(UnitType.id, UnitType.unit_type, UnitType.description)
        .where(UnitType.can_show_to_customers == True)  # noqa: E712
        .order_by(UnitType.unit_type)
    )
    result = await ijack_db.execute(query)
    records = result.all()
    return {"result": records}
