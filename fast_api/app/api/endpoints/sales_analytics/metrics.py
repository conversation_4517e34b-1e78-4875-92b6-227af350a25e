from decimal import Decimal
from typing import Optional

from fastapi import APIRouter, Depends, Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV
from shared.models.models import Customer
from shared.models.models_bom import ModelType
from shared.models.models_work_order import (
    ServiceType,
    WorkOrder,
    WorkOrderPart,
    WorkOrderStatus,
    work_order_model_type_rel,
)
from sqlalchemy import case, exists, func, or_, select, text

from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.schemas.ag_grid import InfiniteApiResponse, InifiniteRowModel
from app.schemas.response import ApiResponse
from app.services.ag_grid import ssrm_count, ssrm_filter, ssrm_paginate, ssrm_sort

from .schemas import (
    ModelTypeValue,
    SalesAnalyticsFilters,
    ServiceTypeValue,
    TopCustomer,
    TopYear,
    WorkOrderGridSchema,
)

router = APIRouter(
    tags=["sales", "analytics"],
    prefix="/sales-analytics",
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)


@router.post("/top-customer")
async def get_top_customer(
    filters: SalesAnalyticsFilters, db=Depends(get_ijack_db)
) -> ApiResponse[Optional[TopCustomer]]:
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )
    query = (
        select(
            Customer.id,
            Customer.customer,
            func.trunc(func.sum(WorkOrderPart.cost_before_tax), 2).label("total_cost"),
        )
        .join(WorkOrder, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(WorkOrder.status_id.in_(status_subquery), Customer.id > 3)
    )
    if (
        filters.service_dates is not None
        and filters.service_dates.from_date is not None
    ):
        query = query.where(WorkOrder.date_service >= filters.service_dates.from_date)
    if filters.service_dates is not None and filters.service_dates.to_date is not None:
        query = query.where(WorkOrder.date_service <= filters.service_dates.to_date)
    if filters.customers is not None and len(filters.customers) > 0:
        query = query.where(Customer.id.in_(filters.customers))
    if filters.service_types is not None and len(filters.service_types) > 0:
        query = query.where(WorkOrder.service_type_id.in_(filters.service_types))
    if filters.models is not None and len(filters.models) > 0:
        not_in_query = False
        in_query = False
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        null_values = list(filter(lambda x: x is None, filters.models))
        if len(non_null_values) > 0:
            in_query = WorkOrder.id.in_(
                select(work_order_model_type_rel.c.work_order_id).where(
                    work_order_model_type_rel.c.model_type_id.in_(non_null_values)
                )
            )
        if len(null_values) > 0:
            not_in_query = ~exists(
                select(1).where(
                    work_order_model_type_rel.c.work_order_id == WorkOrder.id
                )
            )
        query = query.where(or_(in_query, not_in_query))
    query = (
        query.group_by(Customer.customer, Customer.id)
        .order_by(func.sum(WorkOrderPart.cost_before_tax).desc())
        .limit(1)
    )
    response = await db.execute(query)
    result = response.first()
    return {"result": result}


@router.post("/top-year")
async def get_top_year(
    filters: SalesAnalyticsFilters, db=Depends(get_ijack_db)
) -> ApiResponse[Optional[TopYear]]:
    """
    Calculate and return the year with the highest total sales amount.

    Parameters:
    -----------
    filters : SalesAnalyticsFilters
        Filters to apply to the sales data analysis
    db : AsyncSession
        Database session dependency

    Returns:
    --------
    ApiResponse[Optional[TopYear]]
        Response containing the year with highest sales and the total amount
    """
    # Get non-voided work order status IDs
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # Build the query starting with WorkOrder as the from table
    query = (
        select(
            func.extract("year", WorkOrder.date_service).label("year"),
            func.trunc(func.sum(WorkOrderPart.cost_before_tax), 2).label("total_cost"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(WorkOrder.status_id.in_(status_subquery), Customer.id > 3)
    )

    # Apply filters if provided
    if (
        filters.service_dates is not None
        and filters.service_dates.from_date is not None
    ):
        query = query.where(WorkOrder.date_service >= filters.service_dates.from_date)
    if filters.service_dates is not None and filters.service_dates.to_date is not None:
        query = query.where(WorkOrder.date_service <= filters.service_dates.to_date)
    if filters.customers is not None and len(filters.customers) > 0:
        query = query.where(Customer.id.in_(filters.customers))
    if filters.service_types is not None and len(filters.service_types) > 0:
        query = query.where(WorkOrder.service_type_id.in_(filters.service_types))
    if filters.models is not None and len(filters.models) > 0:
        not_in_query = False
        in_query = False
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        null_values = list(filter(lambda x: x is None, filters.models))
        if len(non_null_values) > 0:
            in_query = WorkOrder.id.in_(
                select(work_order_model_type_rel.c.work_order_id).where(
                    work_order_model_type_rel.c.model_type_id.in_(non_null_values)
                )
            )
        if len(null_values) > 0:
            not_in_query = ~exists(
                select(1).where(
                    work_order_model_type_rel.c.work_order_id == WorkOrder.id
                )
            )
        query = query.where(or_(in_query, not_in_query))

    # Group and order the results
    query = (
        query.group_by(func.extract("year", WorkOrder.date_service))
        .order_by(func.sum(WorkOrderPart.cost_before_tax).desc())
        .limit(1)
    )

    # Execute query and return result
    response = await db.execute(query)
    result = response.first()
    return {"result": result}


@router.post("/average-order-value")
async def get_average_order_value(
    filters: SalesAnalyticsFilters, db=Depends(get_ijack_db)
) -> ApiResponse[Optional[Decimal]]:
    """
    Calculate and return the average order value for the specified filters.

    This calculates the sum of all parts for each work order first,
    then averages these sums to get the true average order value.

    Parameters:
    -----------
    filters : SalesAnalyticsFilters
        Filters to apply to the sales data analysis
    db : AsyncSession
        Database session dependency

    Returns:
    --------
    ApiResponse[Optional[float]]
        Response containing the average order value
    """
    # Get non-voided work order status IDs
    status_subquery = (
        select(WorkOrderStatus.id)
        .where(WorkOrderStatus.name != "VOID")
        .scalar_subquery()
    )

    # First, create a subquery to sum the costs for each work order
    query = (
        select(
            WorkOrder.id.label("work_order_id"),
            func.sum(WorkOrderPart.cost_before_tax).label("order_total"),
        )
        .select_from(WorkOrder)
        .join(Customer, WorkOrder.customer_id == Customer.id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(WorkOrder.status_id.in_(status_subquery), Customer.id > 3)
    )

    # Apply filters to the subquery
    if (
        filters.service_dates is not None
        and filters.service_dates.from_date is not None
    ):
        query = query.where(WorkOrder.date_service >= filters.service_dates.from_date)
    if filters.service_dates is not None and filters.service_dates.to_date is not None:
        query = query.where(WorkOrder.date_service <= filters.service_dates.to_date)
    if filters.customers is not None and len(filters.customers) > 0:
        query = query.where(Customer.id.in_(filters.customers))
    if filters.service_types is not None and len(filters.service_types) > 0:
        query = query.where(WorkOrder.service_type_id.in_(filters.service_types))
    # work_order_model_type_rel is a many-to-many relationship table
    # between WorkOrder and ModelType, so we need to join it
    # to filter by models if provided.
    # Therefore, to filter by work orders that have no related model_types we need to use not exists
    if filters.models is not None and len(filters.models) > 0:
        not_in_query = False
        in_query = False
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        null_values = list(filter(lambda x: x is None, filters.models))
        if len(non_null_values) > 0:
            in_query = WorkOrder.id.in_(
                select(work_order_model_type_rel.c.work_order_id).where(
                    work_order_model_type_rel.c.model_type_id.in_(non_null_values)
                )
            )
        if len(null_values) > 0:
            not_in_query = ~exists(
                select(1).where(
                    work_order_model_type_rel.c.work_order_id == WorkOrder.id
                )
            )
        query = query.where(or_(in_query, not_in_query))

    # Group by work order ID
    query = query.group_by(WorkOrder.id).subquery()

    # Now calculate the average of these work order totals
    query = select(
        func.trunc(func.avg(query.c.order_total), 2).label("average_order_value")
    )

    # Execute query and return result
    response = await db.execute(query)
    result = response.scalar()
    return {"result": result}


@router.post("/work-order-grid")
async def get_infinite_sales_grid(
    server_request: InifiniteRowModel,
    ijack_db=Depends(get_ijack_db),
) -> InfiniteApiResponse[WorkOrderGridSchema]:
    """
    Get an infinite scroll grid of work order sales data.
    This endpoint is used for the sales dashboard to fetch data in chunks.
    """
    model_types_sort = next(
        (
            sort.sort.value
            for sort in server_request.sortModel
            if sort.colId == "models"
        ),
        "asc",
    )
    equipment_serviced_cte = (
        select(
            work_order_model_type_rel.c.work_order_id,
            func.string_agg(
                ModelType.model,
                text(f"', ' ORDER BY model_types.model {model_types_sort} "),
            ).label("equipment_serviced"),
        )
        .select_from(ModelType)
        .join(
            work_order_model_type_rel,
            work_order_model_type_rel.c.model_type_id == ModelType.id,
        )
        .group_by(work_order_model_type_rel.c.work_order_id)
        .cte("equipment_serviced_cte")
    )
    order_total = func.sum(WorkOrderPart.cost_before_tax)
    query = (
        select(
            WorkOrder.id,
            WorkOrder.service_required,
            WorkOrder.work_done,
            WorkOrder.invoice_summary,
            WorkOrder.creator_company_id,
            Customer.customer,
            WorkOrderStatus.name.label("status"),
            ServiceType.name.label("service_type"),
            WorkOrder.date_service,
            order_total.label("order_total"),
            equipment_serviced_cte.c.equipment_serviced,
        )
        .join(Customer, Customer.id == WorkOrder.customer_id, isouter=True)
        .join(
            equipment_serviced_cte,
            equipment_serviced_cte.c.work_order_id == WorkOrder.id,
            isouter=True,
        )
        .join(WorkOrderStatus, WorkOrderStatus.id == WorkOrder.status_id, isouter=True)
        .join(
            work_order_model_type_rel,
            work_order_model_type_rel.c.work_order_id == WorkOrder.id,
            isouter=True,
        )
        .join(
            ModelType,
            ModelType.id == work_order_model_type_rel.c.model_type_id,
            isouter=True,
        )
        .join(
            ServiceType,
            ServiceType.id == WorkOrder.service_type_id,
            isouter=True,
        )
        .join(
            WorkOrderPart,
            WorkOrderPart.work_order_id == WorkOrder.id,
        )
        .where(
            Customer.id > 3,
            WorkOrderStatus.name != "VOID",
        )
    )

    def equipment_serviced_filter(filter_model):
        if len(getattr(filter_model, "values", [])) > 0:
            not_in_query = False
            in_query = False
            non_null_values = list(filter(lambda x: x is not None, filter_model.values))
            null_values = list(filter(lambda x: x is None, filter_model.values))
            if len(non_null_values) > 0:
                in_query = WorkOrder.id.in_(
                    select(work_order_model_type_rel.c.work_order_id).where(
                        work_order_model_type_rel.c.model_type_id.in_(non_null_values)
                    )
                )
            if len(null_values) > 0:
                not_in_query = ~exists(
                    select(1)
                    .where(work_order_model_type_rel.c.work_order_id == WorkOrder.id)
                    .correlate(WorkOrder)
                )
            return or_(in_query, not_in_query)
        return True

    criteria = ssrm_filter(
        server_request=server_request,
        table=WorkOrder,
        filter_criteria=[],
        overrides={
            "customer": lambda filter_model: Customer.id.in_(filter_model.values)
            if len(getattr(filter_model, "values", [])) > 0
            else True,
            "service_type": lambda filter_model: ServiceType.id.in_(filter_model.values)
            if len(getattr(filter_model, "values", [])) > 0
            else True,
            "equipment_serviced": equipment_serviced_filter,
        },
    )
    # apply filters
    query = query.filter(*criteria)
    # ssrm sorting
    sorting = ssrm_sort(
        server_request=server_request,
        table=WorkOrder,
        sort_criteria=[],
        overrides={
            "customer": lambda sort_model: getattr(
                Customer.customer, sort_model.sort
            )(),
            "service_type": lambda sort_model: getattr(
                ServiceType.name, sort_model.sort
            )(),
            "status": lambda sort_model: getattr(
                WorkOrderStatus.name, sort_model.sort
            )(),
            "equipment_serviced": lambda sort_model: getattr(
                equipment_serviced_cte.c.equipment_serviced, sort_model.sort
            )(),
            "order_total": lambda sort_model: getattr(order_total, sort_model.sort)(),
        },
    )
    query = query.group_by(
        WorkOrder.id,
        WorkOrder.service_required,
        WorkOrder.work_done,
        WorkOrder.invoice_summary,
        Customer.customer,
        WorkOrderStatus.name,
        ServiceType.name,
        WorkOrder.date_service,
        equipment_serviced_cte.c.equipment_serviced,
    )
    # apply sort
    query = query.order_by(*sorting)
    # ssrm paginate
    paginated_query = ssrm_paginate(server_request=server_request, query=query)
    result = await ijack_db.execute(paginated_query)
    db_res = result.all()
    # count rows so we know where the end is for ssrm
    db_count = await ssrm_count(ijack_db, query)
    return {
        "rowData": db_res,
        "rowCount": db_count,
    }


@router.post("/monthly-service-type-chart")
async def monthly_service_type_chart(
    filters: SalesAnalyticsFilters,
    db=Depends(get_ijack_db),
) -> ApiResponse[list[ServiceTypeValue]]:
    """
    Get the service type values by month, including customer, new installation, parts,
    preventative maintenance, rental, repair, sale, and total costs.
    This endpoint aggregates the costs for each service type and returns a list of
    ServiceTypeValue objects, each representing a month with the corresponding costs.
    """
    # pivot the service type values and sum the cost_before_tax for each service type
    query = (
        select(
            func.max(func.date_trunc("month", WorkOrder.date_service)).label("month"),
            case(
                (
                    WorkOrder.service_type_id == 1,
                    func.sum(WorkOrderPart.cost_before_tax),
                ),
                else_=0,
            ).label("new_installation_sum"),
            case(
                (
                    WorkOrder.service_type_id == 2,
                    func.sum(WorkOrderPart.cost_before_tax),
                ),
                else_=0,
            ).label("repair_sum"),
            case(
                (
                    WorkOrder.service_type_id == 3,
                    func.sum(WorkOrderPart.cost_before_tax),
                ),
                else_=0,
            ).label("parts_sum"),
            case(
                (
                    WorkOrder.service_type_id == 4,
                    func.sum(WorkOrderPart.cost_before_tax),
                ),
                else_=0,
            ).label("sale_sum"),
            case(
                (
                    WorkOrder.service_type_id == 5,
                    func.sum(WorkOrderPart.cost_before_tax),
                ),
                else_=0,
            ).label("preventative_maintenance_sum"),
            case(
                (
                    WorkOrder.service_type_id == 7,
                    func.sum(WorkOrderPart.cost_before_tax),
                ),
                else_=0,
            ).label("rental_sum"),
            case(
                (
                    WorkOrder.service_type_id == 8,
                    func.sum(WorkOrderPart.cost_before_tax),
                ),
                else_=0,
            ).label("customer_sum"),
            func.sum(WorkOrderPart.cost_before_tax).label("monthly_total"),
        )
        .select_from(WorkOrder)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .where(WorkOrder.customer_id > 3, WorkOrder.status_id != 14)
        .join(Customer, Customer.id == WorkOrder.customer_id)
    )
    if (
        filters.service_dates is not None
        and filters.service_dates.from_date is not None
    ):
        query = query.where(WorkOrder.date_service >= filters.service_dates.from_date)
    if filters.service_dates is not None and filters.service_dates.to_date is not None:
        query = query.where(WorkOrder.date_service <= filters.service_dates.to_date)
    if filters.customers is not None and len(filters.customers) > 0:
        query = query.where(Customer.id.in_(filters.customers))
    if filters.service_types is not None and len(filters.service_types) > 0:
        query = query.where(WorkOrder.service_type_id.in_(filters.service_types))
    if filters.models is not None and len(filters.models) > 0:
        not_in_query = False
        in_query = False
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        null_values = list(filter(lambda x: x is None, filters.models))
        if len(non_null_values) > 0:
            in_query = WorkOrder.id.in_(
                select(work_order_model_type_rel.c.work_order_id).where(
                    work_order_model_type_rel.c.model_type_id.in_(non_null_values)
                )
            )
        if len(null_values) > 0:
            not_in_query = ~exists(
                select(1).where(
                    work_order_model_type_rel.c.work_order_id == WorkOrder.id
                )
            )
        query = query.where(or_(in_query, not_in_query))
    query = query.group_by(
        func.date_trunc("month", WorkOrder.date_service),
        WorkOrder.service_type_id,
    )
    query = query.cte("query")

    # Now create the main query with running total using window function
    result_query = (
        select(
            query.c.month,
            func.trunc(func.sum(query.c.customer_sum), 0).label("customer"),
            func.trunc(func.sum(query.c.new_installation_sum), 0).label(
                "new_installation"
            ),
            func.trunc(func.sum(query.c.parts_sum), 0).label("parts"),
            func.trunc(func.sum(query.c.preventative_maintenance_sum), 0).label(
                "preventative_maintenance"
            ),
            func.trunc(func.sum(query.c.rental_sum), 0).label("rental"),
            func.trunc(func.sum(query.c.repair_sum), 0).label("repair"),
            func.trunc(func.sum(query.c.sale_sum), 0).label("sale"),
            # Calculate running total using window function
            func.trunc(
                func.sum(func.sum(query.c.monthly_total)).over(order_by=query.c.month),
                0,
            ).label("total"),
        )
        .select_from(query)
        .group_by(query.c.month)
    )

    # Execute the query and return results
    response = await db.execute(result_query)
    results = response.all()

    return {"result": results}


@router.post("/monthly-model-type-revenue")
async def monthly_model_type_revenue(
    filters: SalesAnalyticsFilters,
    db=Depends(get_ijack_db),
) -> ApiResponse[list[ModelTypeValue]]:
    """
    Get monthly revenue data grouped by model type unit types.

    This endpoint aggregates the costs for each unit type (uno, xfer, egas, etc.)
    by month and includes a running total across all months.

    Parameters:
    -----------
    filters : SalesAnalyticsFilters
        Filters to apply to the sales data analysis
    db : AsyncSession
        Database session dependency

    Returns:
    --------
    ApiResponse[list[ModelTypeValue]]
        Response containing monthly revenue data by model type
    """
    # Create the CTE that groups by unit_type_id and month
    cte_query = (
        select(
            case(
                (ModelType.unit_type_id == 1, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("uno"),
            case(
                (ModelType.unit_type_id == 2, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("xfer"),
            case(
                (ModelType.unit_type_id == 3, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("egas"),
            case(
                (ModelType.unit_type_id == 4, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("unogas"),
            case(
                (ModelType.unit_type_id == 5, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("dgas"),
            case(
                (ModelType.unit_type_id == 7, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("boost"),
            case(
                (ModelType.unit_type_id == 9, func.sum(WorkOrderPart.cost_before_tax)),
                else_=0,
            ).label("vru"),
            func.sum(WorkOrderPart.cost_before_tax).label("total"),
            func.max(func.date_trunc("month", WorkOrder.date_service)).label("month"),
        )
        .select_from(WorkOrder)
        .join(Customer, Customer.id == WorkOrder.customer_id)
        .join(WorkOrderPart, WorkOrder.id == WorkOrderPart.work_order_id)
        .join(
            work_order_model_type_rel,
            WorkOrder.id == work_order_model_type_rel.c.work_order_id,
        )
        .join(
            ModelType,
            work_order_model_type_rel.c.model_type_id == ModelType.id,
        )
        .where(WorkOrder.customer_id > 3, WorkOrder.status_id != 14)
    )

    # Apply filters to the CTE query
    if (
        filters.service_dates is not None
        and filters.service_dates.from_date is not None
    ):
        cte_query = cte_query.where(
            WorkOrder.date_service >= filters.service_dates.from_date
        )
    if filters.service_dates is not None and filters.service_dates.to_date is not None:
        cte_query = cte_query.where(
            WorkOrder.date_service <= filters.service_dates.to_date
        )
    if filters.customers is not None and len(filters.customers) > 0:
        cte_query = cte_query.where(WorkOrder.customer_id.in_(filters.customers))
    if filters.service_types is not None and len(filters.service_types) > 0:
        cte_query = cte_query.where(
            WorkOrder.service_type_id.in_(filters.service_types)
        )
    if filters.models is not None and len(filters.models) > 0:
        not_in_query = False
        in_query = False
        non_null_values = list(filter(lambda x: x is not None, filters.models))
        null_values = list(filter(lambda x: x is None, filters.models))
        if len(non_null_values) > 0:
            in_query = ModelType.id.in_(non_null_values)
        if len(null_values) > 0:
            # For null values, we need work orders that don't have any model types
            not_in_query = ~exists(
                select(1).where(
                    work_order_model_type_rel.c.work_order_id == WorkOrder.id
                )
            )
        if not_in_query is not False and in_query is not False:
            cte_query = cte_query.where(or_(in_query, not_in_query))
        elif in_query is not False:
            cte_query = cte_query.where(in_query)
        elif not_in_query is not False:
            cte_query = cte_query.where(not_in_query)

    # Group by unit_type_id and month for the CTE
    cte_query = cte_query.group_by(
        ModelType.unit_type_id,
        func.date_trunc("month", WorkOrder.date_service),
    ).cte("my_cte")

    # Main query that aggregates the CTE results by month
    result_query = (
        select(
            cte_query.c.month,
            func.trunc(func.sum(cte_query.c.uno), 0).label("uno"),
            func.trunc(func.sum(cte_query.c.xfer), 0).label("xfer"),
            func.trunc(func.sum(cte_query.c.egas), 0).label("egas"),
            func.trunc(func.sum(cte_query.c.unogas), 0).label("unogas"),
            func.trunc(func.sum(cte_query.c.dgas), 0).label("dgas"),
            func.trunc(func.sum(cte_query.c.boost), 0).label("boost"),
            func.trunc(func.sum(cte_query.c.vru), 0).label("vru"),
            # Calculate running total using window function
            func.trunc(
                func.sum(func.sum(cte_query.c.total)).over(order_by=cte_query.c.month),
                0,
            ).label("total"),
        )
        .select_from(cte_query)
        .group_by(cte_query.c.month)
        .order_by(cte_query.c.month)
    )

    # Execute the query and return results
    response = await db.execute(result_query)
    results = response.all()

    return {"result": results}
