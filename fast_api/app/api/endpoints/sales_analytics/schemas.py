from datetime import date
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


class DateRange(BaseModel):
    from_date: Optional[date] = Field(None, alias="from")
    to_date: Optional[date] = Field(None, alias="to")


class SalesAnalyticsFilters(BaseModel):
    service_dates: Optional[DateRange] = None
    customers: Optional[list[int]] = None
    service_types: Optional[list[int]] = None
    models: Optional[list[int | None]] = None


class TopCustomer(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    customer: str
    total_cost: Decimal


class TopYear(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    year: int
    total_cost: Decimal


class WorkOrderGridSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    service_required: Optional[str]
    work_done: Optional[str]
    invoice_summary: Optional[str]
    customer: Optional[str]
    service_type: Optional[str]
    date_service: Optional[date]
    equipment_serviced: Optional[str]
    order_total: Optional[Decimal]
    creator_company_id: int


class ServiceTypeValue(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    month: date
    customer: Optional[int]
    new_installation: Optional[int]
    parts: Optional[int]
    preventative_maintenance: Optional[int]
    rental: Optional[int]
    repair: Optional[int]
    sale: Optional[int]
    total: int


class ModelTypeValue(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    month: date
    uno: Optional[int]
    xfer: Optional[int]
    egas: Optional[int]
    unogas: Optional[int]
    dgas: Optional[int]
    boost: Optional[int]
    vru: Optional[int]
    total: int
