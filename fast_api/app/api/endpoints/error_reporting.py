"""
Error reporting API endpoints for handling client-side errors
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, Field
from shared.utils.email_utils import send_error_report_email

logger = logging.getLogger(__name__)

router = APIRouter()


class ErrorReportRequest(BaseModel):
    """Request model for error reporting"""

    # Error details
    error_name: str = Field(
        ..., description="Error type/name (e.g., TypeError, ReferenceError)"
    )
    error_message: str = Field(..., description="Error message")
    error_stack: Optional[str] = Field(None, description="Error stack trace")

    # User context
    user_info: Optional[Dict[str, Any]] = Field(
        None, description="User information if available"
    )

    # Browser/environment context
    user_agent: Optional[str] = Field(None, description="Browser user agent")
    url: Optional[str] = Field(None, description="URL where error occurred")
    timestamp: Optional[str] = Field(
        None, description="Client timestamp when error occurred"
    )

    # Additional context
    component_stack: Optional[str] = Field(
        None, description="React component stack trace"
    )
    additional_info: Optional[Dict[str, Any]] = Field(
        None, description="Any additional context"
    )


class ErrorReportResponse(BaseModel):
    """Response model for error reporting"""

    success: bool = Field(
        ..., description="Whether the error report was successfully processed"
    )
    message: str = Field(..., description="Response message")
    error_id: Optional[str] = Field(
        None, description="Unique identifier for this error report"
    )


@router.post("/report-error", response_model=ErrorReportResponse)
async def report_error(
    error_request: ErrorReportRequest, request: Request
) -> ErrorReportResponse:
    """
    Report a client-side error to IJACK support via email

    This endpoint receives error reports from the React frontend and sends
    detailed error information to IJACK support via email, while returning
    a generic success response to the client.
    """

    try:
        # Generate unique error ID for tracking
        error_id = f"ERR-{datetime.now().strftime('%Y%m%d-%H%M%S')}-{id(error_request)}"

        # Prepare error details
        error_details = {
            "name": error_request.error_name,
            "message": error_request.error_message,
            "stack": error_request.error_stack or "No stack trace provided",
        }

        # Prepare additional context
        additional_context = {
            "error_id": error_id,
            "url": error_request.url or "Unknown",
            "user_agent": error_request.user_agent or "Unknown",
            "client_timestamp": error_request.timestamp or "Unknown",
            "server_timestamp": datetime.now().isoformat(),
            "client_ip": request.client.host,
            "component_stack": error_request.component_stack or "Not provided",
        }

        # Add any additional info provided
        if error_request.additional_info:
            additional_context.update(error_request.additional_info)

        # Log the error for server-side monitoring
        logger.error(
            f"Client error reported [ID: {error_id}]: {error_request.error_name} - {error_request.error_message}",
            extra={
                "error_id": error_id,
                "error_details": error_details,
                "user_info": error_request.user_info,
                "additional_context": additional_context,
            },
        )

        # Send error report email to IJACK support
        try:
            response = send_error_report_email(
                error_details=error_details,
                user_info=error_request.user_info,
                additional_context=additional_context,
            )

            if response.status_code == 200:
                logger.info(
                    f"Error report email sent successfully for error ID: {error_id}"
                )
            else:
                logger.warning(
                    f"Error report email may have failed for error ID: {error_id}. "
                    f"Mailgun response: {response.status_code} - {response.text}"
                )

        except Exception as email_error:
            # Log email sending failure but don't fail the entire request
            logger.error(
                f"Failed to send error report email for error ID: {error_id}. "
                f"Email error: {str(email_error)}"
            )

        return ErrorReportResponse(
            success=True,
            message="Error report received and forwarded to support team",
            error_id=error_id,
        )

    except Exception as e:
        logger.error(f"Failed to process error report: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process error report")


@router.get("/health", response_model=Dict[str, str])
async def health_check() -> Dict[str, str]:
    """Health check endpoint for error reporting service"""
    return {
        "status": "healthy",
        "service": "error-reporting",
        "timestamp": datetime.now().isoformat(),
    }
