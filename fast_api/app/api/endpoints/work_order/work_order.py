import math
from asyncio import gather
from typing import Annotated, List

import numpy as np
import pandas as pd
from fastapi import APIRouter, Depends, Query
from redis.asyncio import Redis
from shared.models.models_work_order import (
    WorkOrder,
    WorkOrderPartsJoined,
)
from sqlalchemy import func, select

from app.db.database import get_dataframe, get_ijack_db
from app.db.redis import cache_memoize, get_async_redis
from app.schemas.pagination import LimitOffsetPaginationInfo, PaginatedApiResponse
from app.schemas.response import ApiResponse

from .schemas import (
    SalesByModelSchema,
    SalesByUnitTypeSchema,
    SalesByYearAndCustomerSchema,
    SalesByYearAndProductSchema,
    SalesByYearSchema,
    SalesDashboardAnalysisSchema,
    SalesFilterOptionsSchema,
    SalesSummarySchema,
    WorkOrderSchema,
)
from .service_type import router as service_type_router

router = APIRouter(
    tags=["work order"],
)
router.include_router(service_type_router)


@router.get("/")
async def read_work_orders(
    ijack_db=Depends(get_ijack_db),
    pagination: Annotated[
        LimitOffsetPaginationInfo, Query()
    ] = LimitOffsetPaginationInfo(page=1, limit=20),
) -> PaginatedApiResponse[list[WorkOrderSchema], LimitOffsetPaginationInfo]:
    """
    Get a list of work orders and their details.
    """
    query = select(WorkOrder.id, WorkOrder.service_required)
    offset = (pagination.page - 1) * pagination.limit
    query = query.limit(pagination.limit).offset(offset).order_by(WorkOrder.id)
    count = ijack_db.execute(select(func.count()).select_from(WorkOrder))
    result = ijack_db.execute(query)
    count, result = await gather(count, result)
    count = count.scalar_one()
    return {
        "data": result.all(),
        "info": {
            "count": count,
            "pages": math.ceil(count / pagination.limit),
            "next": {
                "limit": pagination.limit,
                "page": pagination.page + 1,
            }
            if pagination.page * pagination.limit < count
            else None,
            "prev": {
                "limit": pagination.limit,
                "page": pagination.page - 1,
            }
            if pagination.page > 1
            else None,
        },
    }


@cache_memoize(timeout=300)  # Cache for 5 minutes
async def get_work_order_parts_joined_cached(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> pd.DataFrame:
    """
    Get the WorkOrderPartsJoined model with caching.
    This function is used to cache the result of the query for performance.

    Parameters:
    -----------
    ijack_db : AsyncSession
        Database session for executing queries
    redis_client : Redis
        Redis client for caching

    Returns:
    --------
    pd.DataFrame
        DataFrame containing WorkOrderPartsJoined records
    """
    # Build query to select WorkOrderPartsJoined records
    # and filter out unwanted model types, unit types, and void records
    query = select(WorkOrderPartsJoined).where(
        ~WorkOrderPartsJoined.is_void,
        WorkOrderPartsJoined.model_type_id.isnot(None),
        WorkOrderPartsJoined.unit_type_id.isnot(None),
        ~WorkOrderPartsJoined.model_type_id.in_([63, 41, 23, 37, 38]),
        ~WorkOrderPartsJoined.unit_type_id.in_(
            [6, 8, 10]
        ),  # Gateway, SHOP, and TEST are excluded
    )

    # Execute the query and convert results directly to a DataFrame
    df = await get_dataframe(query, db_name="ijack")

    # If DataFrame is empty, return an empty DataFrame with the expected columns
    if df.empty:
        return pd.DataFrame(
            columns=[column.name for column in WorkOrderPartsJoined.__table__.columns]
        )

    # Fill NA values
    df["unit_type"] = df["unit_type"].fillna("Unknown")
    df["model"] = df["model"].fillna("Unknown")
    df["part_num_group"] = df["part_num_group"].fillna("Unknown")
    df["service_type"] = df["service_type"].fillna("Unknown")
    df["customer"] = df["customer"].fillna("Unknown")
    df["creator_company"] = df["creator_company"].fillna("Unknown")
    df["currency"] = df["currency"].fillna("Unknown")
    df["year"] = df["year"].fillna(0)

    # Calculate cost based on whether the work order is in USD or not
    df["cost"] = np.where(
        df["is_void"] == True,  # If this is a void record...  # noqa: E712
        0,  # ...then cost is 0
        df["quantity"]  # Otherwise, calculate cost as quantity times appropriate cost
        * np.where(
            df["is_usd_work_order"] == True,  # noqa: E712
            df["cost_usd"],  # If USD work order, use USD cost
            df["cost_cad"],  # Otherwise use CAD cost
        ),
    )

    # Calculate total sales (excluding void records)
    df["total_sales"] = np.where(
        df["is_void"] == True,  # noqa: E712
        0,  # Void records have 0 sales
        df["cost_before_tax"],  # Otherwise use cost_before_tax as the sales value
    )

    # Convert sales to CAD
    df["total_sales_cad"] = np.where(
        df["is_usd_work_order"] == True,  # noqa: E712
        df["cad_per_usd"] * df["total_sales"],  # Convert USD to CAD
        df["total_sales"],  # Already in CAD, use as is
    )

    # Convert sales to USD
    df["total_sales_usd"] = np.where(
        df["is_usd_work_order"] == True,  # noqa: E712
        df["total_sales"],  # Already in USD, use as is
        df["total_sales"] / df["cad_per_usd"],  # Convert CAD to USD
    )

    # Calculate margins (profit)
    df["margin"] = df["total_sales"] - df["cost"]
    df["margin_cad"] = df["total_sales_cad"] - df["cost_cad"]
    df["margin_usd"] = df["total_sales_usd"] - df["cost_usd"]

    # Flag records where cost equals revenue (these are usually labor costs)
    df["same_cost_and_revenue"] = np.where(
        df["cost"] == df["total_sales"],
        True,
        False,
    )

    # Make the columns floats before multiplying
    df["cost"] = df["cost"].astype(float)
    df["cost_cad"] = df["cost_cad"].astype(float)
    df["cost_usd"] = df["cost_usd"].astype(float)
    df["total_sales"] = df["total_sales"].astype(float)
    df["total_sales_cad"] = df["total_sales_cad"].astype(float)
    df["total_sales_usd"] = df["total_sales_usd"].astype(float)
    df["margin"] = df["margin"].astype(float)
    df["margin_cad"] = df["margin_cad"].astype(float)
    df["margin_usd"] = df["margin_usd"].astype(float)
    df["same_cost_and_revenue"] = df["same_cost_and_revenue"].astype(bool)
    df["is_void"] = df["is_void"].astype(bool)
    df["is_usd_work_order"] = df["is_usd_work_order"].astype(bool)

    # For labor costs (where cost equals revenue), adjust cost down by a multiplier
    # This represents the company's markup on labor
    multiplier: float = 2.0  # labour cost multiplier

    # Adjust cost based on the same_cost_and_revenue flag
    df["cost_adj"] = np.where(
        df["same_cost_and_revenue"],
        df["cost"] / multiplier,  # For labor, reduce cost by multiplier
        df["cost"],  # For parts, use actual cost
    )
    df["cost_adj_cad"] = np.where(
        df["same_cost_and_revenue"],
        df["cost_cad"] / multiplier,
        df["cost_cad"],
    )
    df["cost_adj_usd"] = np.where(
        df["same_cost_and_revenue"],
        df["cost_usd"] / multiplier,
        df["cost_usd"],
    )

    # Recalculate margins with adjusted costs
    df["margin"] = df["total_sales"] - df["cost_adj"]
    df["margin_adj_cad"] = df["total_sales_cad"] - df["cost_adj_cad"]
    df["margin_adj_usd"] = df["total_sales_usd"] - df["cost_adj_usd"]

    return df


@router.get("/sales-by-year")
async def get_sales_by_year(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[List[SalesByYearSchema]]:
    """
    Get sales data aggregated by year for charting purposes.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Aggregate the data by year using pandas
    # This is more efficient with the DataFrame
    sales_by_year_df = (
        all_work_order_parts.groupby("year")["total_sales"].sum().reset_index()
    )

    # Sort by year and format the data
    formatted_data = [
        {"year": int(row["year"]), "total_sales": float(row["total_sales"])}
        for _, row in sales_by_year_df.sort_values("year").iterrows()
    ]

    return {"result": formatted_data}


@router.get("/sales-by-year-and-service-type")
async def get_sales_by_service_type_by_year(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[List[dict]]:
    """
    Get sales data aggregated by service type and year for charting purposes.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Aggregate the data by service type and year using pandas
    grouped_df = (
        all_work_order_parts.groupby(["service_type", "year"])["total_sales"]
        .sum()
        .reset_index()
    )

    # Sort by year, then service_type
    sorted_df = grouped_df.sort_values(by=["year", "service_type"])

    # Format the data for the response
    formatted_data = [
        {
            "service_type": row["service_type"],
            "year": int(row["year"]),
            "total_sales": float(row["total_sales"]),
        }
        for _, row in sorted_df.iterrows()
    ]

    return {"result": formatted_data}


@router.get("/sales-by-year-and-customer")
async def get_sales_by_customer_by_year(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[List[dict]]:
    """
    Get sales data aggregated by customer and year for charting purposes.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Aggregate the data by customer and year using pandas
    grouped_df = (
        all_work_order_parts.groupby(["customer", "year"])["total_sales"]
        .sum()
        .reset_index()
    )

    # Sort by year, then customer
    sorted_df = grouped_df.sort_values(by=["year", "customer"])

    # Format the data for the response
    formatted_data = [
        {
            "customer": row["customer"],
            "year": int(row["year"]),
            "total_sales": float(row["total_sales"]),
        }
        for _, row in sorted_df.iterrows()
    ]

    return {"result": formatted_data}


@router.get("/sales-summary")
async def get_sales_summary(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[SalesSummarySchema]:
    """
    Get summary metrics for the sales dashboard.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Calculate total sales and count using pandas
    total_sales = all_work_order_parts["total_sales"].sum()
    total_count = len(all_work_order_parts)

    # Calculate average sale
    average_sale = total_sales / total_count if total_count > 0 else 0.0

    # Get unique customers count
    customer_count = all_work_order_parts["customer"].nunique()

    # Find top customer
    customer_sales_df = (
        all_work_order_parts.groupby("customer")["total_sales"].sum().reset_index()
    )

    if len(customer_sales_df) > 0:
        top_customer_row = customer_sales_df.loc[
            customer_sales_df["total_sales"].idxmax()
        ]
        top_customer = top_customer_row["customer"]
        top_customer_sales = float(top_customer_row["total_sales"])
    else:
        top_customer = "None"
        top_customer_sales = 0.0

    # Format the data
    summary = {
        "total_sales": float(total_sales),
        "total_count": total_count,
        "average_sale": float(average_sale),
        "customer_count": customer_count,
        "top_customer": top_customer,
        "top_customer_sales": top_customer_sales,
    }

    return {"result": summary}


@router.get("/sales-filter-options")
async def get_sales_filter_options(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[SalesFilterOptionsSchema]:
    """
    Get available filter options for the sales dashboard.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Extract unique values for each filter using pandas
    years = sorted(all_work_order_parts["year"].dropna().unique().tolist())
    customers = sorted(all_work_order_parts["customer"].dropna().unique().tolist())
    service_types = sorted(
        all_work_order_parts["service_type"].dropna().unique().tolist()
    )
    currencies = sorted(all_work_order_parts["currency"].dropna().unique().tolist())
    creator_companies = sorted(
        all_work_order_parts["creator_company"].dropna().unique().tolist()
    )

    # Format the data
    filter_options = {
        "years": [int(year) for year in years],
        "customers": customers,
        "service_types": service_types,
        "currencies": currencies,
        "creator_companies": creator_companies,
    }

    return {"result": filter_options}


@router.get("/sales-by-year-and-model")
async def get_sales_by_model(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[List[SalesByModelSchema]]:
    """
    Get sales data aggregated by model and year for charting purposes.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Aggregate the data by model and year using pandas
    grouped_df = (
        all_work_order_parts.groupby(["model", "year"])["total_sales"]
        .sum()
        .reset_index()
    )

    # Sort by year, then model
    sorted_df = grouped_df.sort_values(by=["year", "model"])

    # Format the data for the response
    formatted_data = [
        {
            "model": row["model"],
            "year": int(row["year"]),
            "total_sales": float(row["total_sales"]),
        }
        for _, row in sorted_df.iterrows()
    ]

    return {"result": formatted_data}


@router.get("/sales-by-year-and-unit-type")
async def get_sales_by_unit_type(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[List[SalesByUnitTypeSchema]]:
    """
    Get sales data aggregated by unit type and year for charting purposes.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Aggregate the data by unit_type and year using pandas
    grouped_df = (
        all_work_order_parts.groupby(["unit_type", "year"])["total_sales"]
        .sum()
        .reset_index()
    )

    # Sort by year, then unit_type
    sorted_df = grouped_df.sort_values(by=["year", "unit_type"])

    # Format the data for the response
    formatted_data = [
        {
            "unit_type": row["unit_type"],
            "year": int(row["year"]),
            "total_sales": float(row["total_sales"]),
        }
        for _, row in sorted_df.iterrows()
    ]

    return {"result": formatted_data}


@router.get("/sales-by-year-and-product")
async def get_sales_by_year_and_product(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[List[SalesByYearAndProductSchema]]:
    """
    Get sales data aggregated by year and product category for charting purposes.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Aggregate the data by year and part_num_group using pandas
    grouped_df = (
        all_work_order_parts.groupby(["year", "part_num_group"])["total_sales"]
        .sum()
        .reset_index()
    )

    # Sort by year, then part_num_group
    sorted_df = grouped_df.sort_values(by=["year", "part_num_group"])

    # Format the data for the response
    formatted_data = [
        {
            "year": int(row["year"]),
            "part_num_group": row["part_num_group"],
            "total_sales": float(row["total_sales"]),
        }
        for _, row in sorted_df.iterrows()
    ]

    return {"result": formatted_data}


@router.get("/sales-by-year-and-customer")
async def get_sales_by_year_and_customer(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[List[SalesByYearAndCustomerSchema]]:
    """
    Get sales data aggregated by year and customer for charting purposes.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Aggregate the data by year and customer using pandas
    grouped_df = (
        all_work_order_parts.groupby(["year", "customer"])["total_sales"]
        .sum()
        .reset_index()
    )

    # Sort first by year, then by total_sales (descending)
    # We need to do this in two steps since we can't mix ascending and descending with sort_values easily
    grouped_by_year = grouped_df.groupby("year")

    # Create a list to store the sorted data
    sorted_data = []
    for year, group in grouped_by_year:
        # Sort each year group by total_sales descending
        sorted_group = group.sort_values("total_sales", ascending=False)
        sorted_data.append(sorted_group)

    # Combine all the sorted groups
    if sorted_data:
        sorted_df = pd.concat(sorted_data)
    else:
        sorted_df = grouped_df

    # Format the data for the response
    formatted_data = [
        {
            "year": int(row["year"]),
            "customer": row["customer"],
            "total_sales": float(row["total_sales"]),
        }
        for _, row in sorted_df.iterrows()
    ]

    return {"result": formatted_data}


@router.get("/dashboard-analysis")
async def get_dashboard_analysis(
    ijack_db=Depends(get_ijack_db),
    redis_client: Redis = Depends(get_async_redis),
) -> ApiResponse[SalesDashboardAnalysisSchema]:
    """
    Get comprehensive dashboard analysis data.
    Uses the cached work order parts joined data for better performance.
    """
    # Get all work order parts from the cached function
    all_work_order_parts = await get_work_order_parts_joined_cached(
        ijack_db=ijack_db, redis_client=redis_client
    )

    # Analyze by customer using pandas
    customer_sales_df = (
        all_work_order_parts.groupby("customer")["total_sales"].sum().reset_index()
    )

    # Find top customer
    if len(customer_sales_df) > 0:
        top_customer_row = customer_sales_df.loc[
            customer_sales_df["total_sales"].idxmax()
        ]
        top_customer = top_customer_row["customer"]
        top_customer_sales = float(top_customer_row["total_sales"])
    else:
        top_customer = "None"
        top_customer_sales = 0.0

    # Analyze by year using pandas
    year_sales_df = (
        all_work_order_parts.groupby("year")["total_sales"].sum().reset_index()
    )

    # Find top year
    if len(year_sales_df) > 0:
        top_year_row = year_sales_df.loc[year_sales_df["total_sales"].idxmax()]
        top_year = int(top_year_row["year"])
        top_year_sales = float(top_year_row["total_sales"])
    else:
        top_year = 0
        top_year_sales = 0.0

    # Calculate average order value using pandas
    total_sales = all_work_order_parts["total_sales"].sum()
    unique_order_ids = all_work_order_parts["work_order_id"].dropna().unique()
    order_count = len(unique_order_ids)
    avg_order_value = float(total_sales / order_count) if order_count > 0 else 0.0

    # Format the analysis data
    analysis = {
        "top_customer": top_customer,
        "top_customer_sales": top_customer_sales,
        "top_year": top_year,
        "top_year_sales": top_year_sales,
        "avg_order_value": avg_order_value,
    }

    return {"result": analysis}
