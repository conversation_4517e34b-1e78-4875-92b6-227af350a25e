from fastapi import APIRouter, Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV

from app.auth.session import roles_required

from .service_type import router as service_type_router
from .work_order import router as work_order_router

router = APIRouter(
    prefix="/work-order",
    tags=["work_orders", "sales"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
)

router.include_router(service_type_router)
router.include_router(work_order_router)
