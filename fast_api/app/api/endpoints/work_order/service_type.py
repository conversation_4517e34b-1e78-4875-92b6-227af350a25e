from fastapi import APIRouter, Depends, Security
from shared.config import ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV
from shared.models.models_work_order import ServiceType
from sqlalchemy import select

from app.api.responses.auth import AUTHZ_RESPONSES
from app.auth.session import roles_required
from app.db.database import get_ijack_db
from app.db.redis import cache_memoize
from app.schemas.response import ApiResponse

from .schemas import ServiceTypeSchema

router = APIRouter(
    tags=["service type"],
)


@router.get(
    "/service-type",
    tags=["internal"],
    dependencies=[
        Security(roles_required([ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SOFTWARE_DEV]))
    ],
    responses=AUTHZ_RESPONSES,
)
@cache_memoize()
async def read_service_types(
    ijack_db=Depends(get_ijack_db),
) -> ApiResponse[list[ServiceTypeSchema]]:
    """
    Get a list of service types and their details.
    """
    query = select(ServiceType.id, ServiceType.name).order_by(ServiceType.name)
    result = await ijack_db.execute(query)
    return {"result": result.all()}
