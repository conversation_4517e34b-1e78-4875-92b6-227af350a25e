from typing import List, Optional

from pydantic import BaseModel, ConfigDict


class WorkOrderSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    service_required: Optional[str]


class SalesByYearSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    year: int
    total_sales: float


class SalesByCustomerSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    customer: str
    total_sales: float


class SalesByServiceTypeSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    service_type: str
    total_sales: float


class SalesSummarySchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    total_sales: float
    total_count: int
    average_sale: float
    customer_count: int
    top_customer: str
    top_customer_sales: float


class SalesFilterOptionsSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    years: List[int]
    customers: List[str]
    service_types: List[str]
    currencies: List[str]
    creator_companies: List[str]


class SalesByProductSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    part_num_group: str
    total_sales: float


class SalesByModelSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    model: str
    year: int
    total_sales: float


class SalesByUnitTypeSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    unit_type: str
    year: int
    total_sales: float


class SalesByYearAndProductSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    year: int
    part_num_group: str
    total_sales: float


class SalesByYearAndCustomerSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    year: int
    customer: str
    total_sales: float


class SalesDashboardAnalysisSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    top_customer: str
    top_customer_sales: float
    top_year: int
    top_year_sales: float
    avg_order_value: float


class ServiceTypeSchema(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
