from fastapi import APIRouter

from .endpoints.admin import admin_router
from .endpoints.customer.router import router as customer_router
from .endpoints.error_reporting import router as error_reporting_router
from .endpoints.location.router import router as location_router
from .endpoints.metadata.router import router as metadata_router
from .endpoints.pricing.router import router as pricing_router
from .endpoints.sales_analytics.metrics import router as sales_analytics_router
from .endpoints.service_analytics.service_costs import (
    router as service_analytics_router,
)
from .endpoints.simple_apis.router import router as simple_apis_router
from .endpoints.structure.router import router as structure_router
from .endpoints.work_order.router import router as work_order_router
from .responses.defaults import DEFAULT_RESPONSES

api_router = APIRouter(
    prefix="/v1",
    responses=DEFAULT_RESPONSES,
)

# Include the new comprehensive admin router (replaces Flask admin_api.py)
api_router.include_router(admin_router)

# Include error reporting endpoints
api_router.include_router(
    error_reporting_router, prefix="/errors", tags=["error-reporting"]
)

# Include migrated Flask API endpoints
api_router.include_router(metadata_router)
api_router.include_router(simple_apis_router)

# Include other specialized routers
api_router.include_router(location_router)
api_router.include_router(customer_router)
api_router.include_router(work_order_router)
api_router.include_router(structure_router)
api_router.include_router(sales_analytics_router)
api_router.include_router(service_analytics_router)
api_router.include_router(pricing_router)
