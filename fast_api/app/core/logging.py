import logging
import sys

from app.core.config import settings


def setup_logging():
    """
    Configure logging to work with Hypercorn's logging system.

    This function sets up loggers that will output to Hypercorn's
    configured log handlers, ensuring consistent log formatting
    and output destinations.
    """
    # Get the root logger and Hypercorn's error logger
    root_logger = logging.getLogger()
    hypercorn_logger = logging.getLogger("CORE")

    # Set logging level based on environment
    log_level = logging.DEBUG if settings.DEBUG else logging.INFO

    # Configure root logger level
    root_logger.setLevel(log_level)

    # Create formatter that matches Hypercorn's style
    formatter = logging.Formatter(
        "%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S %z",
    )

    # Only add handlers if we don't already have them (avoid duplicate logs)
    if not hypercorn_logger.handlers:
        # Create console handler for error logs
        console_handler = logging.StreamHandler(sys.stderr)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(log_level)

        # Add handler to hypercorn error logger
        hypercorn_logger.addHandler(console_handler)
        hypercorn_logger.setLevel(log_level)
        hypercorn_logger.propagate = False

    # Configure application-specific loggers
    app_logger = logging.getLogger("app")
    app_logger.setLevel(log_level)

    # Configure auth session logger specifically for debugging
    auth_logger = logging.getLogger("app.auth.session")
    auth_logger.setLevel(logging.DEBUG if settings.DEBUG else logging.INFO)

    return hypercorn_logger


# Set up logging before creating the FastAPI app
logger = setup_logging()
