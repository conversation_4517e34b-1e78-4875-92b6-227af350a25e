from typing import Literal

from pydantic import Field, computed_field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    ENVIRONMENT: Literal[
        "development", "testing", "staging", "staging2", "production"
    ] = Field(default="development", description="Current deployment environment")
    DB_IJ: str
    HOST_IJ: str = Field(alias="BOUNCER_RDS_HOST", default="pgbouncer-rds")
    PORT_IJ: str = "5432"
    USER_IJ: str
    PASS_IJ: str
    DB_TS: str
    HOST_TS: str = Field(alias="BOUNCER_TS_HOST", default="pgbouncer-ts")
    PORT_TS: str = "5432"
    USER_TS: str
    PASS_TS: str
    ECHO: bool = False

    REDIS_URL: str = "redis://redis:6379/0"

    SECRET_KEY: str
    SESSION_SALT: str = "cookie-session"

    @computed_field
    @property
    def SESSION_COOKIE_NAME(self) -> str:
        """
        Generate the session cookie name based on the environment.
        This allows for different session cookies in different environments.
        """
        if self.ENVIRONMENT in ["development", "staging", "staging2"]:
            return f"{self.ENVIRONMENT}_session"
        return "session"

    CORS_ORIGINS: list[str] = ["*"]
    TRUSTED_ORIGINS: list[str] = ["*"]

    @computed_field
    @property
    def DEBUG(self) -> bool:
        """
        Determine if the application is in debug mode based on the environment.
        """
        return self.ENVIRONMENT in ["development", "testing"]

    model_config = SettingsConfigDict()


settings = Settings()
