import asyncio
import logging

from app.core.config import settings

logger = logging.getLogger("uvicorn")


async def update_type_definitions():
    """
    This task runs after the application has started handling requests.
    """
    if settings.DEBUG:
        await asyncio.sleep(1)

        cmd = [
            "pnpm",
            "-C",
            "/project/flask_app",
            "--filter",
            "inertia",
            "run",
            "generate-types",
        ]

        # Run the command asynchronously
        proc = await asyncio.create_subprocess_exec(
            *cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await proc.communicate()

        if proc.returncode != 0:
            print("Error:", stderr)

        logger.info("Typescript definitions reloaded.")


async def generate_openapi_types():
    asyncio.create_task(
        update_type_definitions()
    )  # Schedule the task to run in the background
