from datetime import datetime
from typing import Callable, Dict, Optional, Type, Union

from shared.models.base import Base
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql.expression import ColumnElement, Select

from app.schemas import ag_grid as server_models


def ssrm_filter(
    server_request: server_models.ServerSideRowModel | server_models.InifiniteRowModel,
    table: Type[Base],
    filter_criteria: list[ColumnElement] = [],
    overrides: Optional[
        Dict[
            str,
            Callable[
                [Union[server_models.FilterModel, server_models.SetFilterModel]],
                ColumnElement,
            ],
        ]
    ] = {},
):
    if len(server_request.filterModel) != 0:
        for filter_key, filter_model in server_request.filterModel.items():
            if filter_key in overrides.keys():
                filter_criteria.append(overrides[filter_key](filter_model))
            elif filter_model.filterType == "text":
                match filter_model.type:
                    case "equals":
                        filter_criteria.append(
                            func.upper(getattr(table, filter_key))
                            == "filter_model.filter".upper()
                        )
                    case "contains":
                        filter_criteria.append(
                            getattr(func.upper(getattr(table, filter_key)), "like")(
                                f"%{filter_model.filter}%".upper()
                            )
                        )
                    case "endsWith":
                        filter_criteria.append(
                            getattr(func.upper(getattr(table, filter_key)), "like")(
                                f"%{filter_model.filter}".upper()
                            )
                        )
                    case "startsWith":
                        filter_criteria.append(
                            getattr(func.upper(getattr(table, filter_key)), "like")(
                                f"{filter_model.filter}%".upper()
                            )
                        )
            elif filter_model.filterType == "number":
                match filter_model.type:
                    case "equals":
                        filter_criteria.append(
                            getattr(table, filter_key) == filter_model.filter
                        )
                    case "contains":
                        filter_criteria.append(
                            getattr(getattr(table, filter_key), "like")(
                                f"%{filter_model.filter}%"
                            )
                        )
            elif filter_model.filterType == "date":
                match filter_model.type:
                    case "equals" | "contains":
                        day_value = datetime.strptime(
                            filter_model.dateFrom.split(" ")[0], "%Y-%m-%d"
                        )
                        day_start = datetime.combine(day_value, datetime.min.time())
                        day_end = datetime.combine(day_value, datetime.max.time())
                        filter_criteria.append(
                            getattr(getattr(table, filter_key), "between")(
                                day_start, day_end
                            )
                        )
                    case "greaterThan":
                        day_value = datetime.strptime(
                            filter_model.dateFrom.split(" ")[0], "%Y-%m-%d"
                        )
                        day_start = datetime.combine(day_value, datetime.min.time())
                        filter_criteria.append(getattr(table, filter_key) > day_start)
                    case "lessThan":
                        day_value = datetime.strptime(
                            filter_model.dateFrom.split(" ")[0], "%Y-%m-%d"
                        )
                        day_end = datetime.combine(day_value, datetime.max.time())
                        filter_criteria.append(getattr(table, filter_key) < day_end)
                    case "inRange":
                        day_from_value = datetime.strptime(
                            filter_model.dateFrom.split(" ")[0], "%Y-%m-%d"
                        )
                        day_to_value = datetime.strptime(
                            filter_model.dateTo.split(" ")[0], "%Y-%m-%d"
                        )
                        day_from_start = datetime.combine(
                            day_from_value, datetime.min.time()
                        )
                        day_to_end = datetime.combine(day_to_value, datetime.max.time())
                        filter_criteria.append(
                            getattr(getattr(table, filter_key), "between")(
                                day_from_start, day_to_end
                            )
                        )
            elif filter_model.filterType == "set":
                filter_criteria.append(
                    getattr(getattr(table, filter_key), "in_")(
                        filter_model.values or []
                    )
                )
    return filter_criteria


def ssrm_sort(
    server_request: server_models.ServerSideRowModel | server_models.InifiniteRowModel,
    table: Type[Base],
    sort_criteria: list[ColumnElement] = [],
    overrides: Optional[
        Dict[str, Callable[[server_models.SortModelItem], ColumnElement]]
    ] = {},
):
    if len(server_request.sortModel) != 0:
        for sortModel in server_request.sortModel:
            if sortModel.colId in overrides.keys():
                sort_criteria.append(overrides[sortModel.colId](sortModel))
            else:
                sort_criteria.append(
                    getattr(getattr(table, sortModel.colId), sortModel.sort)()
                )
    return sort_criteria


def ssrm_paginate(
    server_request: server_models.ServerSideRowModel | server_models.InifiniteRowModel,
    query: Select,
) -> Select:
    limit_rows = server_request.endRow - server_request.startRow
    query = query.limit(limit_rows).offset(server_request.startRow)
    return query


async def ssrm_count(
    db: AsyncSession,
    query: Select,
) -> int:
    count = await db.scalars(select(func.count()).select_from(query.subquery()))
    return count.one()
