from shared.models import models, models_bom, models_work_order

for model in getattr(models_bom, "__all__", []):
    pass

for model in getattr(models_work_order, "__all__", []):
    pass


class User(models.User):
    __abstract__ = False
    __tablename__ = "users"


class Structure(models.Structure):
    __abstract__ = False
    __tablename__ = "structures"


class OAuth(models.OAuth):
    __abstract__ = False
    __tablename__ = "oauth"
