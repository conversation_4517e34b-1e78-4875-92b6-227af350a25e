"""Test configuration for FastAPI tests."""

import sys
from pathlib import Path

import pytest
from httpx import AsyncClient

# Add parent directory to Python path to access shared modules
parent_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(parent_dir))


from app.main import app  # noqa: E402


@pytest.fixture
async def client():
    """Create an async test client for FastAPI app."""
    from httpx import ASGITransport

    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as ac:
        yield ac
