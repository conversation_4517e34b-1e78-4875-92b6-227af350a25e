"""
Test module for database utilities.
"""

from unittest.mock import MagicMock, patch

import pandas as pd
import pytest

from app.db.database import get_dataframe


@pytest.mark.asyncio
@patch("app.db.database.SessionLocalIjack")
async def test_get_dataframe(mock_session_local):
    """
    Test that get_dataframe properly executes SQL query and returns a pandas DataFrame.
    """
    # Set up mock
    mock_session = MagicMock()
    mock_session_local.return_value = mock_session

    # Mock DataFrame result
    expected_df = pd.DataFrame({"id": [1, 2, 3], "name": ["test1", "test2", "test3"]})

    # Configure mock to return expected DataFrame when run_sync is called
    mock_session.run_sync.return_value = expected_df

    # Execute the function
    query = "SELECT * FROM test_table"
    result_df = await get_dataframe(query)

    # Verify the mock was called with a function that would pass the query
    mock_session.run_sync.assert_called_once()

    # Verify the result is what we expect
    pd.testing.assert_frame_equal(result_df, expected_df)

    # Verify session was closed
    mock_session.close.assert_called_once()
