"""
Test module for the merged get_dataframe function.
"""

from unittest.mock import MagicMock, patch

import pandas as pd
import pytest
from sqlalchemy import select

from app.db.database import get_dataframe


@pytest.mark.asyncio
@patch("app.db.database.SessionLocalIjack")
async def test_get_dataframe_ijack_default(mock_session_local):
    """
    Test that get_dataframe with default db_name="ijack" works properly.
    """
    # Set up mock
    mock_session = MagicMock()
    mock_session_local.return_value = mock_session

    # Mock DataFrame result
    expected_df = pd.DataFrame({"id": [1, 2, 3], "name": ["test1", "test2", "test3"]})

    # Configure mock to return expected DataFrame when run_sync is called
    mock_session.run_sync.return_value = expected_df

    # Execute the function with a string query
    query = "SELECT * FROM test_table"
    result_df = await get_dataframe(query)

    # Verify the mock was called
    mock_session.run_sync.assert_called_once()

    # Verify the result is what we expect
    pd.testing.assert_frame_equal(result_df, expected_df)

    # Verify session was closed
    mock_session.close.assert_called_once()


@pytest.mark.asyncio
@patch("app.db.database.SessionLocalTimescale")
async def test_get_dataframe_timescale(mock_session_local):
    """
    Test that get_dataframe with db_name="timescale" works properly.
    """
    # Set up mock
    mock_session = MagicMock()
    mock_session_local.return_value = mock_session

    # Mock DataFrame result
    expected_df = pd.DataFrame(
        {
            "timestamp": pd.date_range(start="2023-01-01", periods=3),
            "value": [1.1, 2.2, 3.3],
        }
    )

    # Configure mock to return expected DataFrame when run_sync is called
    mock_session.run_sync.return_value = expected_df

    # Execute the function with db_name="timescale"
    query = "SELECT * FROM timeseries_data"
    result_df = await get_dataframe(query, db_name="timescale")

    # Verify the mock was called
    mock_session.run_sync.assert_called_once()

    # Verify the result is what we expect
    pd.testing.assert_frame_equal(result_df, expected_df)

    # Verify session was closed
    mock_session.close.assert_called_once()


@pytest.mark.asyncio
async def test_get_dataframe_invalid_db_name():
    """
    Test that get_dataframe raises ValueError with invalid db_name.
    """
    # Execute the function with an invalid db_name
    query = "SELECT * FROM test_table"
    with pytest.raises(ValueError, match='db_name must be "ijack" or "timescale"'):
        await get_dataframe(query, db_name="invalid")


@pytest.mark.asyncio
@patch("app.db.database.SessionLocalIjack")
async def test_get_dataframe_sqlalchemy_query(mock_session_local):
    """
    Test that get_dataframe works with SQLAlchemy queries.
    """
    # Set up mock
    mock_session = MagicMock()
    mock_session_local.return_value = mock_session

    # Mock DataFrame result
    expected_df = pd.DataFrame({"id": [1, 2, 3], "name": ["test1", "test2", "test3"]})

    # Configure mock to return expected DataFrame when run_sync is called
    mock_session.run_sync.return_value = expected_df

    # Create a SQLAlchemy query
    from sqlalchemy import Column, Integer, MetaData, String, Table

    metadata = MetaData()
    test_table = Table(
        "test_table",
        metadata,
        Column("id", Integer, primary_key=True),
        Column("name", String),
    )
    query = select(test_table)

    # Execute the function with a SQLAlchemy query
    result_df = await get_dataframe(query)

    # Verify the mock was called
    mock_session.run_sync.assert_called_once()

    # Verify the result is what we expect
    pd.testing.assert_frame_equal(result_df, expected_df)

    # Verify session was closed
    mock_session.close.assert_called_once()
