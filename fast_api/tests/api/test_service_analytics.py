import pytest
from httpx import AsyncClient


@pytest.mark.asyncio
async def test_service_cost_overview(client: AsyncClient):
    """Test the service cost overview endpoint"""
    response = await client.post(
        "/v1/service-analytics/cost-overview",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]  # 401 for auth required

    # If auth is bypassed in tests, check response structure
    if response.status_code == 200:
        data = response.json()
        assert "result" in data


@pytest.mark.asyncio
async def test_service_cost_overview_with_selected_years(client: AsyncClient):
    """Test the service cost overview endpoint with selected_years filtering"""
    response = await client.post(
        "/v1/service-analytics/cost-overview",
        json={
            "selected_years": [2024],
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_cost_by_model(client: AsyncClient):
    """Test the cost by model endpoint"""
    response = await client.post(
        "/v1/service-analytics/cost-by-model",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_cost_trends(client: AsyncClient):
    """Test the cost trends endpoint"""
    response = await client.post(
        "/v1/service-analytics/cost-trends",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_high_cost_units(client: AsyncClient):
    """Test the high cost units endpoint"""
    response = await client.post(
        "/v1/service-analytics/high-cost-units",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_high_cost_units_with_selected_years(client: AsyncClient):
    """Test the high cost units endpoint with selected_years filtering"""
    response = await client.post(
        "/v1/service-analytics/high-cost-units",
        json={
            "selected_years": [2024],
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_top_cost_drivers(client: AsyncClient):
    """Test the top cost drivers endpoint"""
    response = await client.post(
        "/v1/service-analytics/top-cost-drivers",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


# Phase 3 Predictive Analytics Tests


@pytest.mark.asyncio
async def test_predictive_cost_modeling(client: AsyncClient):
    """Test the predictive cost modeling endpoint"""
    response = await client.post(
        "/v1/service-analytics/predictive-cost-modeling",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_failure_prediction(client: AsyncClient):
    """Test the failure prediction endpoint"""
    response = await client.post(
        "/v1/service-analytics/failure-prediction",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_advanced_root_cause_analysis(client: AsyncClient):
    """Test the advanced root cause analysis endpoint"""
    response = await client.post(
        "/v1/service-analytics/advanced-root-cause-analysis",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_intelligent_recommendations(client: AsyncClient):
    """Test the intelligent recommendations endpoint"""
    response = await client.post(
        "/v1/service-analytics/intelligent-recommendations",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


# Phase 4 Temporal and Operational Insights Tests


@pytest.mark.asyncio
async def test_seasonal_analysis(client: AsyncClient):
    """Test the seasonal analysis endpoint"""
    response = await client.post(
        "/v1/service-analytics/seasonal-analysis",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]

    if response.status_code == 200:
        data = response.json()
        assert "result" in data
        # Check for seasonal analysis structure
        if data["result"]:
            result = data["result"]
            assert "seasonal_cost_variation" in result
            assert "highest_cost_season" in result
            assert "lowest_cost_season" in result
            assert "monthly_patterns" in result


@pytest.mark.asyncio
async def test_warranty_analysis(client: AsyncClient):
    """Test the warranty analysis endpoint"""
    response = await client.post(
        "/v1/service-analytics/warranty-analysis",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]

    if response.status_code == 200:
        data = response.json()
        assert "result" in data
        # Check for warranty analysis structure
        if data["result"]:
            result = data["result"]
            assert "warranty_costs" in result
            assert "non_warranty_costs" in result
            assert "warranty_percentage" in result


@pytest.mark.asyncio
async def test_geographic_analysis(client: AsyncClient):
    """Test the geographic analysis endpoint"""
    response = await client.post(
        "/v1/service-analytics/geographic-analysis",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]

    if response.status_code == 200:
        data = response.json()
        assert "result" in data
        # Result should be a list of geographic metrics
        assert isinstance(data["result"], list)


@pytest.mark.asyncio
async def test_service_urgency_analysis(client: AsyncClient):
    """Test the service urgency analysis endpoint"""
    response = await client.post(
        "/v1/service-analytics/service-urgency-analysis",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]

    if response.status_code == 200:
        data = response.json()
        assert "result" in data
        if data["result"]:
            result = data["result"]
            assert "emergency_services" in result
            assert "planned_services" in result
            assert "emergency_percentage" in result


@pytest.mark.asyncio
async def test_composite_risk_scores(client: AsyncClient):
    """Test the composite risk scores endpoint"""
    response = await client.post(
        "/v1/service-analytics/composite-risk-scores",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]

    if response.status_code == 200:
        data = response.json()
        assert "result" in data
        # Result should be a list of risk scores
        assert isinstance(data["result"], list)


@pytest.mark.asyncio
async def test_temporal_insights_summary(client: AsyncClient):
    """Test the temporal insights summary endpoint"""
    response = await client.post(
        "/v1/service-analytics/temporal-insights-summary",
        json={
            "service_dates": {"from": "2023-01-01", "to": "2023-12-31"},
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]

    if response.status_code == 200:
        data = response.json()
        assert "result" in data
        if data["result"]:
            result = data["result"]
            assert "seasonal_cost_impact" in result
            assert "warranty_cost_opportunity" in result
            assert "emergency_reduction_potential" in result
            assert "temporal_efficiency_score" in result


# Test with selected_years parameter for Phase 4 endpoints


@pytest.mark.asyncio
async def test_seasonal_analysis_with_selected_years(client: AsyncClient):
    """Test seasonal analysis with selected_years filtering"""
    response = await client.post(
        "/v1/service-analytics/seasonal-analysis",
        json={
            "selected_years": [2024],
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]


@pytest.mark.asyncio
async def test_warranty_analysis_with_selected_years(client: AsyncClient):
    """Test warranty analysis with selected_years filtering"""
    response = await client.post(
        "/v1/service-analytics/warranty-analysis",
        json={
            "selected_years": [2024],
            "customers": [],
            "service_types": [],
            "models": [],
            "technicians": [],
        },
    )
    assert response.status_code in [200, 401]
