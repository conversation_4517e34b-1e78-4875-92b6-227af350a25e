"""
Tests for the geographic analysis API endpoints.

Tests the enhanced geographic analysis features including:
- Hybrid geocoding support
- Geographic filtering
- Regional cost analysis
- Geocoding status tracking
"""

import pytest
from datetime import datetime

from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.endpoints.service_analytics.schemas import ServiceCostFilters


@pytest.mark.asyncio
class TestGeographicAnalysisEndpoint:
    """Test suite for the geographic analysis endpoint."""

    async def test_get_geographic_analysis_success(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        auth_headers: dict,
    ):
        """Test successful geographic analysis retrieval."""
        # Prepare test data
        filters = ServiceCostFilters(
            customers=[1, 2],
            service_dates={"from": "2024-01-01", "to": "2024-12-31"},
            include_unmapped=True,
        )

        response = await async_client.post(
            "/v1/geographic/analysis",
            json=filters.dict(),
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()

        # Verify response structure
        assert "result" in data
        result = data["result"]
        assert "regions" in result
        assert "summary" in result
        assert "analysis_method" in result

        # Verify summary
        summary = result["summary"]
        assert "total_regions" in summary
        assert "avg_geocoding_coverage" in summary
        assert "unmapped_structures" in summary
        assert "total_structures" in summary

    async def test_geographic_analysis_with_filters(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        auth_headers: dict,
    ):
        """Test geographic analysis with various filters applied."""
        filters = ServiceCostFilters(
            customers=[1],
            country_codes=["CA", "US"],
            region_names=["Alberta", "Texas"],
            min_services=10,
            max_distance_from_city=100.0,
            include_unmapped=False,
        )

        response = await async_client.post(
            "/v1/geographic/analysis",
            json=filters.dict(),
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()

        # Verify filters were applied
        regions = data["result"]["regions"]
        for region in regions:
            # Check country filter
            assert region["country_name"] in ["Canada", "United States"]

            # Check region filter
            if region["clustering_method"] == "database":
                assert region["region_name"] in ["Alberta", "Texas"]

            # Check min services filter
            assert region["total_services"] >= 10

            # Check distance filter if applicable
            if region.get("distance_from_major_city"):
                assert region["distance_from_major_city"] <= 100.0

    async def test_geographic_analysis_clustering_methods(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        auth_headers: dict,
    ):
        """Test that different clustering methods are properly identified."""
        filters = ServiceCostFilters(
            include_unmapped=True,
        )

        response = await async_client.post(
            "/v1/geographic/analysis",
            json=filters.dict(),
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()

        # Check for different clustering methods
        regions = data["result"]["regions"]
        clustering_methods = {r["clustering_method"] for r in regions}

        # Should have at least one clustering method
        assert len(clustering_methods) > 0

        # Valid clustering methods
        valid_methods = {"database", "major_city", "coordinate"}
        assert clustering_methods.issubset(valid_methods)

    async def test_geographic_analysis_empty_results(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        auth_headers: dict,
    ):
        """Test geographic analysis with filters that return no results."""
        filters = ServiceCostFilters(
            customers=[99999],  # Non-existent customer
        )

        response = await async_client.post(
            "/v1/geographic/analysis",
            json=filters.dict(),
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()

        # Should return empty regions
        assert data["result"]["regions"] == []
        assert data["result"]["summary"]["total_regions"] == 0

    async def test_geographic_analysis_unauthorized(
        self,
        async_client: AsyncClient,
    ):
        """Test geographic analysis without authentication."""
        filters = ServiceCostFilters()

        response = await async_client.post(
            "/v1/geographic/analysis",
            json=filters.dict(),
        )

        assert response.status_code == 401


@pytest.mark.asyncio
class TestGeocodingStatusEndpoint:
    """Test suite for the geocoding status endpoint."""

    async def test_get_geocoding_status_success(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        auth_headers: dict,
    ):
        """Test successful geocoding status retrieval."""
        response = await async_client.post(
            "/v1/geographic/geocoding-status",
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()

        # Verify response structure
        assert "result" in data
        result = data["result"]

        assert "status_counts" in result
        assert "percentages" in result
        assert "total_structures_with_gps" in result
        assert "last_updated" in result

        # Verify status counts structure
        status_counts = result["status_counts"]
        valid_statuses = {"pending", "success", "failed", "unknown"}

        for status in status_counts:
            assert status in valid_statuses
            assert isinstance(status_counts[status], int)
            assert status_counts[status] >= 0

    async def test_geocoding_status_percentages(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        auth_headers: dict,
    ):
        """Test that geocoding status percentages are calculated correctly."""
        response = await async_client.post(
            "/v1/geographic/geocoding-status",
            headers=auth_headers,
        )

        assert response.status_code == 200
        data = response.json()

        result = data["result"]
        percentages = result["percentages"]

        # Sum of percentages should be approximately 100%
        if percentages:
            total_percentage = sum(percentages.values())
            assert 99.0 <= total_percentage <= 101.0  # Allow for rounding

        # Each percentage should be between 0 and 100
        for pct in percentages.values():
            assert 0 <= pct <= 100

    async def test_geocoding_status_requires_admin_or_service_role(
        self,
        async_client: AsyncClient,
        user_auth_headers: dict,  # Regular user headers
    ):
        """Test that geocoding status requires admin or service role."""
        response = await async_client.post(
            "/v1/geographic/geocoding-status",
            headers=user_auth_headers,
        )

        # Should be forbidden for regular users
        assert response.status_code == 403


@pytest.mark.asyncio
class TestGeographicFilterValidation:
    """Test validation of geographic filters."""

    async def test_invalid_country_codes(
        self,
        async_client: AsyncClient,
        auth_headers: dict,
    ):
        """Test that invalid country codes are handled gracefully."""
        filters = ServiceCostFilters(
            country_codes=["XX", "YY"],  # Invalid codes
        )

        response = await async_client.post(
            "/v1/geographic/analysis",
            json=filters.dict(),
            headers=auth_headers,
        )

        # Should still succeed but return no results
        assert response.status_code == 200
        data = response.json()
        assert data["result"]["regions"] == []

    async def test_negative_min_services(
        self,
        async_client: AsyncClient,
        auth_headers: dict,
    ):
        """Test that negative min_services is rejected."""
        filters = {
            "min_services": -10,
        }

        response = await async_client.post(
            "/v1/geographic/analysis",
            json=filters,
            headers=auth_headers,
        )

        assert response.status_code == 422  # Validation error

    async def test_negative_max_distance(
        self,
        async_client: AsyncClient,
        auth_headers: dict,
    ):
        """Test that negative max_distance_from_city is rejected."""
        filters = {
            "max_distance_from_city": -50.0,
        }

        response = await async_client.post(
            "/v1/geographic/analysis",
            json=filters,
            headers=auth_headers,
        )

        assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
class TestGeographicAnalysisPerformance:
    """Test performance aspects of geographic analysis."""

    async def test_large_dataset_performance(
        self,
        async_client: AsyncClient,
        db_session: AsyncSession,
        auth_headers: dict,
        benchmark,
    ):
        """Test that geographic analysis performs well with large datasets."""
        filters = ServiceCostFilters(
            # No filters to get maximum data
        )

        async def run_analysis():
            response = await async_client.post(
                "/v1/geographic/analysis",
                json=filters.dict(),
                headers=auth_headers,
            )
            return response

        # Benchmark the request
        response = await benchmark(run_analysis)

        assert response.status_code == 200

        # Response time should be reasonable (< 5 seconds)
        # This is handled by the benchmark fixture

    async def test_caching_effectiveness(
        self,
        async_client: AsyncClient,
        auth_headers: dict,
    ):
        """Test that repeated requests benefit from caching."""
        filters = ServiceCostFilters(
            customers=[1, 2],
        )

        # First request (cache miss)
        start_time = datetime.utcnow()
        response1 = await async_client.post(
            "/v1/geographic/analysis",
            json=filters.dict(),
            headers=auth_headers,
        )
        first_duration = (datetime.utcnow() - start_time).total_seconds()

        assert response1.status_code == 200

        # Second request (should hit cache)
        start_time = datetime.utcnow()
        response2 = await async_client.post(
            "/v1/geographic/analysis",
            json=filters.dict(),
            headers=auth_headers,
        )
        second_duration = (datetime.utcnow() - start_time).total_seconds()

        assert response2.status_code == 200

        # Second request should be faster (cache hit)
        # Note: This might not always be true in test environment
        # but is useful for performance monitoring
        assert second_duration <= first_duration * 1.5
