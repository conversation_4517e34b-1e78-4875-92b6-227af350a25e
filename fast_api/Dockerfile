ARG ENVIRONMENT=production
FROM nikolaik/python-nodejs:python3.12-nodejs22-slim AS builder-python

ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}
ENV UV_COMPILE_BYTECODE=1 
ENV UV_LINK_MODE=copy

# Disable Python downloads, because we want to use the system interpreter
# across both images. If using a managed Python version, it needs to be
# copied from the build image into the final image; see `standalone.Dockerfile`
# for an example.
ENV UV_PYTHON_DOWNLOADS=0

# Use Docker BuildKit for better caching and faster builds
ARG DOCKER_BUILDKIT=1
ARG BUILDKIT_INLINE_CACHE=1
# Enable BuildKit for Docker-Compose
ARG COMPOSE_DOCKER_CLI_BUILD=1

# Don't write .pyc bytecode
ENV PYTHONDONTWRITEBYTECODE=1
# Don't buffer stdout. Write it immediately to the Docker log
ENV PYTHONUNBUFFERED=1
ENV PYTHONFAULTHANDLER=1
ENV PYTHONHASHSEED=random

# Tell apt-get we're never going to be able to give manual feedback:
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && \
    apt-get install -y --no-install-recommends build-essential gcc redis-server libpq-dev sass curl ca-certificates \
    # procps iproute2 lsb-release gnupg apt-transport-https \
    # For gcld3 (Google Cloud Language API) neural net language detection
    g++ protobuf-compiler libprotobuf-dev && \
    # Clean up
    apt-get autoremove -y && \
    apt-get clean -y && \
    rm -rf /var/lib/apt/lists/*

# Download the latest uv installer
ADD https://astral.sh/uv/install.sh /uv-installer.sh
# Run the installer then remove it
RUN sh /uv-installer.sh && rm /uv-installer.sh
# Ensure the installed binary is on the `PATH`
ENV PATH="/root/.local/bin/:$PATH"

# ----- packages setup begins ------
WORKDIR /project/packages
COPY packages/ .
RUN uv build

# https://github.com/astral-sh/uv-docker-example/blob/main/multistage.Dockerfile
# uv sync: Synchronizes dependencies according to requirements files
# --frozen: Ensures exact versions are used (similar to pip's --frozen-requirements)
# --no-install-project: This is the key flag you asked about - it prevents uv from installing the current directory as a package
# $(test "$ENVIRONMENT" != "production" && echo "--group dev"): Conditionally adds the --group dev flag if not in production
# Copy only dependency files first
WORKDIR /project/fast_api
COPY fast_api/pyproject.toml fast_api/uv.lock ./
RUN uv sync --frozen --no-install-project $(test "$ENVIRONMENT" != "production" && echo "--group dev")
RUN WHEEL_FILE=$(ls /project/packages/dist/*.whl | head -n 1) && \
    uv pip install "$WHEEL_FILE"
# Then copy application code
COPY fast_api/ .
# RUN --mount=type=cache,target=/root/.cache/uv \
#     uv sync --frozen $(test "$ENVIRONMENT" != "production" && echo "--group dev")

######### Final stage of multi-stage build ############################################################
FROM nikolaik/python-nodejs:python3.12-nodejs22-slim AS production

ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

# For setting up the non-root user in the container
ARG USERNAME=user
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Use Docker BuildKit for better caching and faster builds
ARG DOCKER_BUILDKIT=1
ARG BUILDKIT_INLINE_CACHE=1
# Enable BuildKit for Docker-Compose
ARG COMPOSE_DOCKER_CLI_BUILD=1

# Don't write .pyc bytecode
ENV PYTHONDONTWRITEBYTECODE=1
# Don't buffer stdout. Write it immediately to the Docker log
ENV PYTHONUNBUFFERED=1
ENV PYTHONFAULTHANDLER=1
ENV PYTHONHASHSEED=random

# Tell apt-get we're never going to be able to give manual feedback:
ENV DEBIAN_FRONTEND=noninteractive

# # Add a new non-root user and change ownership of the workdir
# RUN addgroup --gid $USER_GID --system $USERNAME && \
#     adduser --no-create-home --shell /bin/false --disabled-password --uid $USER_UID --system --group $USERNAME
#     # chown -R $USER_UID:$USER_GID /project && \

# Get curl and netcat for Docker healthcheck
RUN apt-get update && \
    apt-get -y --no-install-recommends install nano curl wget \
    iputils-ping netcat-traditional procps net-tools lsof iproute2 \
    # For gcld3 (Google Cloud Language API) neural net language detection
    g++ protobuf-compiler libprotobuf-dev && \
    apt-get clean && \
    # Delete index files we don't need anymore:
    rm -rf /var/lib/apt/lists/*

COPY fast_api/hypercorn.toml /project/fast_api/hypercorn.toml

WORKDIR /project/fast_api

# Copy the virtual environment first
COPY --chown=$USER_UID:$USER_GID --from=builder-python /project/fast_api/.venv /project/fast_api/.venv

# Copy required project files next (excluding what's already in .venv)
COPY --chown=$USER_UID:$USER_GID --from=builder-python /project/fast_api/app /project/fast_api/app

EXPOSE 8000

# Make sure we use the virtualenv
# ENV PATH="/project/.venv/bin:$PATH"
# Place executables in the environment at the front of the path
ENV PATH="/project/fast_api/.venv/bin:$PATH"
RUN echo PATH = $PATH
RUN echo "ENVIRONMENT = $ENVIRONMENT"

# CMD ["uv", "/bin/bash", "/project/entrypoint.sh"]
CMD ["hypercorn", "app.main:app",  "--config", "/project/fast_api/hypercorn.toml"]
