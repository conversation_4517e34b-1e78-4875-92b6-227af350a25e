{
  "python.terminal.activateEnvironment": true,
  "terminal.integrated.env.linux": {
    "PYTHONNOUSERSITE": "1"
  },
  "remote.autoForwardPorts": false,
  "files.eol": "\n",
  "python.languageServer": "Default",
  "python.testing.unittestEnabled": false,
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": [
    "-n=auto",
    "--dist=worksteal",
    "--tb=short"
  ],
  "editor.defaultFormatter": "charliermarsh.ruff",
  "editor.formatOnSave": true,
  "editor.wordWrap": "on",
  "ruff.organizeImports": true,
  "ruff.trace.server": "messages",
  "ruff.nativeServer": true,
  // Remove global ruff.configuration and use folder-specific settings
  // "ruff.configuration": "${workspaceFolder}/pyproject.toml",
  // Use folder-specific settings for different file types
  "[python]": {
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.tabSize": 4,
    "editor.formatOnSave": true,
    "editor.formatOnPaste": false,
    "editor.codeActionsOnSave": {
      "source.fixAll": "explicit",
      "source.organizeImports": "explicit"
    }
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2,
    "editor.formatOnSave": true,
  },
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features",
    "editor.tabSize": 2,
    "editor.formatOnSave": true,
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features",
    "editor.tabSize": 2,
    "editor.formatOnSave": true,
  },
  "[css]": {
    "editor.defaultFormatter": "vscode.css-language-features",
    "editor.tabSize": 2,
    "editor.formatOnSave": true,
  },
  "editor.detectIndentation": false,
  "[sql]": {
    "editor.defaultFormatter": "adpyke.vscode-sql-formatter"
  },
  // "docker.environment": {
  //   "DOCKER_HOST": "ssh://<EMAIL>"
  // }
  "yaml.format.enable": true,
  "yaml.validate": true,
  "yaml.schemas": {
    // any YAML file I open in VS Code will be validated against the Kubernetes schema
    "kubernetes": "*.yaml"
  },
  "yaml.customTags": [
    "!Ref",
    "!Sub",
    "!GetAtt",
    "!ImportValue",
    "!FindInMap sequence",
    "!Join sequence",
    "!Select sequence",
    "!Split sequence",
    "!If sequence"
  ],
  "yaml.completion": true,
  "[yaml]": {
    "editor.defaultFormatter": "redhat.vscode-yaml"
  },
  "[dockercompose]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[properties]": {
    "editor.defaultFormatter": null
  },
  "djlint.showInstallError": false,
  "[dotenv]": {
    "editor.defaultFormatter": null
  },
  "[dockerfile]": {
    "editor.defaultFormatter": "ms-azuretools.vscode-containers"
  },
  "[ignore]": {
    "editor.defaultFormatter": null
  },
  "explorer.confirmDelete": false,
  "[shellscript]": {
    "editor.defaultFormatter": null
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  // tanstack router setup for file based routing
  "files.readonlyInclude": {
    "**/routeTree.gen.ts": true
  },
  "files.watcherExclude": {
    "**/routeTree.gen.ts": true
  },
  "search.exclude": {
    "**/routeTree.gen.ts": true
  }
}