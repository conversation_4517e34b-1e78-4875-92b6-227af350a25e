// .vscode/launch.json
{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "compounds": [
    {
      "name": "Flask, Fast & React Debuggers",
      "configurations": [
        "RCOM w/ Debugger",
        "Attach to Chrome (Host Debugger)",
        "API w/ Debugger"
      ],
      "stopAll": true,
      "presentation": {
        "hidden": false,
        "group": "Flask-Debug",
        "order": 0
      }
    }
  ],
  "configurations": [
    {
      "name": "API w/ Debugger",
      "presentation": {
        "hidden": true,
        "group": "Flask-Debug",
        "order": 0
      },
      "type": "debugpy",
      "request": "launch",
      "cwd": "${workspaceFolder}/fast_api",
      "python": "${workspaceFolder}/fast_api/.venv/bin/python",
      "module": "hypercorn",
      "args": [
        "app.main:app",
        "--config",
        "${workspaceFolder}/fast_api/hypercorn.local.toml",
      ],
      "env": {
        "DEBUG": "true",
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem"
      }
    },
    {
      "name": "RCOM w/ Debugger",
      "presentation": {
        // "hidden": true,
        "group": "Flask-Debug",
        "order": 1
      },
      "type": "debugpy",
      "cwd": "${workspaceFolder}/flask_app",
      "request": "launch",
      "postDebugTask": "Kill Vite",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "flask",
      "autoStartBrowser": false,
      "preLaunchTask": "Vite Build",
      "jinja": true,
      "justMyCode": false,
      "args": [
        "run",
        // "--no-reload"
      ],
      "env": {
        "FLASK_APP": "wsgi:flask_app",
        "FLASK_CONFIG": "development",
        "FLASK_DEBUG": "1",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // Enable SSL for development
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem",
      }
    },
    {
      // https://github.com/microsoft/vscode-js-debug/blob/main/OPTIONS.md
      // run this on your local setup
      // start chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome-profile http://localhost:4999
      // google-chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome-profile http://localhost:4999
      "type": "chrome",
      "request": "attach",
      "name": "Attach to Chrome (Host Debugger)",
      "port": 9222,
      "browserAttachLocation": "ui",
      "urlFilter": "https://app.localhost*", // Filter for your app's URL
      "webRoot": "${workspaceFolder}/flask_app/app/inertia/react",
      "skipFiles": [
        "${workspaceFolder}/flask_app/app/inertia/react/node_modules/**/*.js"
      ],
      "preLaunchTask": "Vite HMR",
      "sourceMaps": true,
      "sourceMapPathOverrides": {
        "https://app.localhost/*": "${webRoot}/*",
        "http://localhost:5173/@fs/*": "*",
        "http://localhost:5173/@vite/*": "${webRoot}/node_modules/*",
        "http://localhost:5173/*": "${webRoot}/*"
      },
      "presentation": {
        "group": "Flask-Debug",
        "order": 1
      }
    },
    {
      "name": "RCOM w/ Inertia & HMR (no Flask Admin)",
      "presentation": {
        "hidden": true,
        "group": "Flask-Debug",
        "order": 1
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "flask",
      "autoStartBrowser": false,
      "preLaunchTask": "Vite Console",
      "postDebugTask": "kill-vite",
      "jinja": true,
      "justMyCode": false,
      "args": [
        "run",
        "--no-reload"
      ],
      "env": {
        "FLASK_APP": "wsgi_rcom_only:flask_app",
        "FLASK_CONFIG": "development",
        "FLASK_DEBUG": "1",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // Enable SSL for development
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem",
      }
    },
    {
      "name": "RCOM w/ Inertia & Build",
      "presentation": {
        "hidden": true,
        "group": "Flask-Debug",
        "order": 1
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "flask",
      "autoStartBrowser": false,
      "preLaunchTask": "Vite Console",
      "jinja": true,
      "justMyCode": false,
      "args": [
        "run",
        "--no-reload"
      ],
      "env": {
        "FLASK_APP": "wsgi:flask_app",
        "FLASK_CONFIG": "development",
        "FLASK_DEBUG": "0",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // Enable SSL for development
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem",
      }
    },
    {
      "name": "Dash RCOM Only (No Flask Admin)",
      "presentation": {
        "hidden": false,
        "group": "Flask-Debug",
        "order": 1
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "flask",
      "autoStartBrowser": false,
      "preLaunchTask": "Vite Console",
      "jinja": true,
      "justMyCode": false,
      "args": [
        "run",
        "--no-reload",
        // "--no-debugger",
        // "--with-threads"
        // "--without-threads"
        // cert and key for https locally (Traefik should handle this)
        // "--cert=/project/certs/cert.pem",
        // "--key=/project/certs/key.pem"
        // "--ssl-context=adhoc"
      ],
      "env": {
        "FLASK_APP": "wsgi_rcom_only:flask_app",
        "FLASK_CONFIG": "development",
        "FLASK_DEBUG": "1",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // Enable SSL for development
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem",
        // // Show a warning if an evaluation takes longer than 1 second
        // "PYDEVD_WARN_EVALUATION_TIMEOUT": "3",
        // // set the PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT to true 
        // // so a thread dump is shown along with this message
        // "PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT": "1",
        // // set the PYDEVD_INTERRUPT_THREAD_TIMEOUT to some value so that the debugger 
        // // tries to interrupt the evaluation (if possible) when this happens
        // "PYDEVD_INTERRUPT_THREAD_TIMEOUT": "10",
        // // if after a given timeout an evaluation doesn't finish, other threads 
        // // are unblocked or you can manually resume all threads
        // "PYDEVD_UNBLOCK_THREADS_TIMEOUT": "3",
        // "DASH_DEBUG": "true",
        // "DASH_UI": "true",
        // "DASH_PROPS_CHECK": "true",
        // "DASH_SERVE_DEV_BUNDLES": "true",
        // "DASH_HOT_RELOAD": "true",
        // "DASH_HOT_RELOAD_INTERVAL": "true",
        // "DASH_HOT_RELOAD_WATCH_INTERVAL": "true",
        // "DASH_HOT_RELOAD_MAX_RETRY": "true",
        // "DASH_SILENCE_ROUTES_LOGGING": "true",
        // "DASH_PRUNE_ERRORS": "true",
      }
    },
    {
      "name": "Flask RCOM Admin All",
      "presentation": {
        "hidden": false,
        "group": "Flask-Debug",
        "order": 1
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      // "program": "${file}",
      // "console": "integratedTerminal",
      "module": "flask",
      "jinja": true,
      "justMyCode": false,
      "autoStartBrowser": false,
      "preLaunchTask": "Build JS Assets",
      // "stopOnEntry": true,
      "env": {
        "FLASK_APP": "wsgi:flask_app",
        "FLASK_CONFIG": "development",
        "FLASK_DEBUG": "1",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // Enable SSL for development
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem",
        // // Show a warning if an evaluation takes longer than 1 second
        // "PYDEVD_WARN_EVALUATION_TIMEOUT": "3",
        // // set the PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT to true 
        // // so a thread dump is shown along with this message
        // "PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT": "1",
        // // set the PYDEVD_INTERRUPT_THREAD_TIMEOUT to some value so that the debugger 
        // // tries to interrupt the evaluation (if possible) when this happens
        // "PYDEVD_INTERRUPT_THREAD_TIMEOUT": "10",
        // // if after a given timeout an evaluation doesn't finish, other threads 
        // // are unblocked or you can manually resume all threads
        // "PYDEVD_UNBLOCK_THREADS_TIMEOUT": "3",
      },
      "args": [
        "run",
        "--no-reload",
        // "--no-debugger",
        // "--with-threads"
        // "--without-threads"
        // cert and key for https locally (Traefik should handle this)
        // "--cert=/project/certs/cert.pem",
        // "--key=/project/certs/key.pem"
      ]
    },
    // Pytest all files
    {
      "name": "Pytest All Files",
      "presentation": {
        "hidden": false,
        "group": "Pytest",
        "order": 2
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "pytest",
      "console": "integratedTerminal",
      "args": [
        // "/home/<USER>/project/tests/",
        "/project/tests/",
        // -n auto will run tests in parallel, but this might mess up the database initialization...
        // "-n",
        // "auto",
        // "-xvs",
        "-sv",
        // "--lf",
        "--exitfirst",
        "--durations=0",
        // // Debugger doesn't always stop on breakpoints with coverage enabled
        // "--no-cov",
        // Playwright options
        "--output=tests/dash_rcom/screenshots",
        "--screenshot=only-on-failure",
        "--tracing=retain-on-failure",
        "--video=retain-on-failure",
        // // Code coverage
        // "--cov=app",
        // "--cov-config=pyproject.toml",
        // // "--cov-fail-under=40"",
        // "--cov-report=term-missing",
        // "--cov-report=html",
        // "--cov-report=xml",
        // "--cov-context=test",
      ],
      "jinja": true,
      "justMyCode": false,
      // "stopOnEntry": true,
      "env": {
        "FLASK_APP": "wsgi:flask_app",
        "FLASK_CONFIG": "testing",
        "FLASK_DEBUG": "0",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // "DISPLAY": ":0", // For GUI tests
        // "PYVIRTUALDISPLAY_DISPLAYFD": "0",
        // Enable SSL for testing
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem",
        // Show a warning if an evaluation takes longer than 1 second
        "PYDEVD_WARN_EVALUATION_TIMEOUT": "3",
        // set the PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT to true 
        // so a thread dump is shown along with this message
        "PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT": "1",
        // set the PYDEVD_INTERRUPT_THREAD_TIMEOUT to some value so that the debugger 
        // tries to interrupt the evaluation (if possible) when this happens
        "PYDEVD_INTERRUPT_THREAD_TIMEOUT": "10",
        // if after a given timeout an evaluation doesn't finish, other threads 
        // are unblocked or you can manually resume all threads
        "PYDEVD_UNBLOCK_THREADS_TIMEOUT": "3",
      },
    },
    // Pytest all files last-failed
    {
      "name": "Pytest All Files Last-Failed",
      "presentation": {
        "hidden": false,
        "group": "Pytest",
        "order": 2
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "pytest",
      "console": "integratedTerminal",
      "args": [
        // "/home/<USER>/project/tests/",
        "/project/tests/",
        // -n auto will run tests in parallel, but this might mess up the database initialization...
        // "-n",
        // "auto",
        // "-xvs",
        "-sv",
        "--lf",
        "--exitfirst",
        "--durations=0",
        // // Debugger doesn't always stop on breakpoints with coverage enabled
        // "--no-cov",
        // Playwright options
        "--output=tests/dash_rcom/screenshots",
        "--screenshot=only-on-failure",
        "--tracing=retain-on-failure",
        "--video=retain-on-failure",
        // // Code coverage
        // "--cov=app",
        // "--cov-config=pyproject.toml",
        // // "--cov-fail-under=40"",
        // "--cov-report=term-missing",
        // "--cov-report=html",
        // "--cov-report=xml",
        // "--cov-context=test",
      ],
      "env": {
        "FLASK_APP": "wsgi:flask_app",
        "FLASK_CONFIG": "testing",
        "FLASK_DEBUG": "0",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // "DISPLAY": ":0", // For GUI tests
        // "PYVIRTUALDISPLAY_DISPLAYFD": "0",
        // Enable SSL for testing
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem",
        // Show a warning if an evaluation takes longer than 1 second
        "PYDEVD_WARN_EVALUATION_TIMEOUT": "3",
        // set the PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT to true 
        // so a thread dump is shown along with this message
        "PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT": "1",
        // set the PYDEVD_INTERRUPT_THREAD_TIMEOUT to some value so that the debugger 
        // tries to interrupt the evaluation (if possible) when this happens
        "PYDEVD_INTERRUPT_THREAD_TIMEOUT": "10",
        // if after a given timeout an evaluation doesn't finish, other threads 
        // are unblocked or you can manually resume all threads
        "PYDEVD_UNBLOCK_THREADS_TIMEOUT": "3",
      },
      "jinja": true,
      "justMyCode": false,
      // "stopOnEntry": true
    },
    // Pytest run the current file only
    {
      "name": "Pytest Current File",
      "presentation": {
        "hidden": false,
        "group": "Pytest",
        "order": 2
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "pytest",
      "console": "integratedTerminal",
      "args": [
        "${file}",
        // -n auto will run tests in parallel, but this might mess up the database initialization...
        // "-n",
        // "auto",
        // "-xvs",
        "-sv",
        // "--exitfirst",
        // "--lf",
        "--durations=0",
        // // Debugger doesn't always stop on breakpoints with coverage enabled
        // "--no-cov",
        // Playwright options
        "--output=tests/dash_rcom/screenshots",
        "--screenshot=only-on-failure",
        "--tracing=retain-on-failure",
        "--video=retain-on-failure",
        // // Code coverage
        // "--cov=app",
        // "--cov-config=pyproject.toml",
        // // "--cov-fail-under=40"",
        // "--cov-report=term-missing",
        // "--cov-report=html",
        // "--cov-report=xml",
        // "--cov-context=test",
      ],
      "jinja": true,
      "justMyCode": false,
      // "stopOnEntry": true,
      "env": {
        "_PYTEST_RAISE": "1",
        "FLASK_APP": "wsgi:flask_app",
        "FLASK_CONFIG": "testing",
        "FLASK_DEBUG": "0",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // Show a warning if an evaluation takes longer than 1 second
        "PYDEVD_WARN_EVALUATION_TIMEOUT": "3",
        // set the PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT to true 
        // so a thread dump is shown along with this message
        "PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT": "1",
        // set the PYDEVD_INTERRUPT_THREAD_TIMEOUT to some value so that the debugger 
        // tries to interrupt the evaluation (if possible) when this happens
        "PYDEVD_INTERRUPT_THREAD_TIMEOUT": "10",
        // if after a given timeout an evaluation doesn't finish, other threads 
        // are unblocked or you can manually resume all threads
        "PYDEVD_UNBLOCK_THREADS_TIMEOUT": "3",
        // "DISPLAY": ":0", // For GUI tests
        // "PYVIRTUALDISPLAY_DISPLAYFD": "0",
        // Enable SSL for testing
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem"
      }
    },
    // Pytest run the current file only, with only the last-failed tests
    {
      "name": "Pytest Current File Last-Failed",
      "presentation": {
        "hidden": false,
        "group": "Pytest",
        "order": 2
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "pytest",
      "console": "integratedTerminal",
      "args": [
        "${file}",
        // -n auto will run tests in parallel, but this might mess up the database initialization...
        // "-n",
        // "auto",
        // "-xvs",
        "-sv",
        "--lf",
        "--durations=0",
        // // Debugger doesn't always stop on breakpoints with coverage enabled
        // "--no-cov",
        // Playwright options
        "--output=tests/dash_rcom/screenshots",
        "--screenshot=only-on-failure",
        "--tracing=retain-on-failure",
        "--video=retain-on-failure",
        // // Code coverage
        // "--cov=app",
        // "--cov-config=pyproject.toml",
        // // "--cov-fail-under=40"",
        // "--cov-report=term-missing",
        // "--cov-report=html",
        // "--cov-report=xml",
        // "--cov-context=test",
      ],
      "jinja": true,
      "justMyCode": false,
      // "stopOnEntry": true,
      "env": {
        "_PYTEST_RAISE": "1",
        "FLASK_APP": "wsgi:flask_app",
        "FLASK_CONFIG": "testing",
        "FLASK_DEBUG": "0",
        "FLASK_RUN_HOST": "0.0.0.0",
        "FLASK_RUN_PORT": "4999",
        "GEVENT_SUPPORT": "False",
        // "DISPLAY": ":0", // For GUI tests
        // "PYVIRTUALDISPLAY_DISPLAYFD": "0",
        // Enable SSL for testing
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem"
      }
    },
    {
      // Main development configuration for Flask with gevent
      "name": "Flask: Gevent Debug",
      "presentation": {
        "hidden": false,
        "group": "Gevent-Debug",
        "order": 3
      },
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/wsgi_rcom_only.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "env": {
        "FLASK_APP": "wsgi_rcom_only:flask_app",
        "FLASK_CONFIG": "development",
        "FLASK_DEBUG": "1",
        "GEVENT_SUPPORT": "False",
        "USE_GEVENT": "True",
        "PYTHONPATH": "${workspaceFolder}",
        // Disable Werkzeug's PIN protection in development
        "WERKZEUG_DEBUG_PIN": "off",
        // Enable SSL for development
        "SSL_CERT_PATH": "${workspaceFolder}/certs/cert.pem",
        "SSL_KEY_PATH": "${workspaceFolder}/certs/key.pem",
        // "SSL_CERT_PATH": "${workspaceFolder}/c_users_sean/.ssh/app.localhost.pem",
        // "SSL_KEY_PATH": "${workspaceFolder}/c_users_sean/.ssh/app.localhost-key.pem",
        // Show a warning if an evaluation takes longer than 1 second
        "PYDEVD_WARN_EVALUATION_TIMEOUT": "3",
        // set the PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT to true 
        // so a thread dump is shown along with this message
        "PYDEVD_THREAD_DUMP_ON_WARN_EVALUATION_TIMEOUT": "1",
        // set the PYDEVD_INTERRUPT_THREAD_TIMEOUT to some value so that the debugger 
        // tries to interrupt the evaluation (if possible) when this happens
        "PYDEVD_INTERRUPT_THREAD_TIMEOUT": "10",
        // if after a given timeout an evaluation doesn't finish, other threads 
        // are unblocked or you can manually resume all threads
        "PYDEVD_UNBLOCK_THREADS_TIMEOUT": "3",
      },
      "args": [
        "run",
        "--no-debugger", // Let VS Code handle debugging
        "--no-reload", // Disable auto-reload to prevent conflicts
        "--host=0.0.0.0",
        "--port=4999"
      ],
      "jinja": true, // Enable Jinja template debugging
      "serverReadyAction": {
        "pattern": "Running on https?://[^:]+:([0-9]+)",
        "uriFormat": "https://app.localhost:%s",
        "action": "openExternally"
      }
    },
    // Regular Python file debugger to run the current file
    {
      "name": "Python Run Current File",
      "presentation": {
        "hidden": false,
        "group": "Other",
        "order": 4
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "program": "${file}",
      "console": "integratedTerminal",
      "jinja": true,
      "justMyCode": false,
      "env": {},
      // "stopOnEntry": true
    },
    {
      "name": "Python: Attach Standard",
      "presentation": {
        "hidden": false,
        "group": "Other",
        "order": 4
      },
      "type": "debugpy",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 5678
      }
    },
    // Login and freeze the docs/ html for PostCSS/PurgeCSS
    {
      "name": "Freeze Static HTML Templates for PurgeCSS",
      "presentation": {
        "hidden": false,
        "group": "Other",
        "order": 4
      },
      "type": "debugpy",
      "request": "launch",
      "python": "${workspaceFolder}/flask_app/.venv/bin/python",
      "module": "freeze",
      "console": "integratedTerminal"
    },
    {
      "name": "Node Run Current File",
      "presentation": {
        "hidden": false,
        "group": "Other",
        "order": 4
      },
      "type": "node",
      "request": "launch",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${file}"
    }
  ],
}