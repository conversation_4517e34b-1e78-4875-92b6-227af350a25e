// .vscode/tasks.json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Chrome Debugger MacOS",
      "type": "shell",
      "command": "/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222"
    },
    {
      "label": "Vite Build",
      "type": "shell",
      "command": "pnpm run build",
      "options": {
        "cwd": "${workspaceFolder}/flask_app"
      },
      "isBackground": false,
      "presentation": {
        "echo": true,
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "close": true
      },
      "problemMatcher": [
        {
          "owner": "vite",
          "fileLocation": [
            "relative",
            "${workspaceFolder}"
          ],
          "pattern": {
            "regexp": "^(ERROR|WARN)  (.*) in (.*)$",
            "severity": 1,
            "message": 2,
            "file": 3,
            "location": 4
          },
        }
      ]
    },
    {
      "label": "Vite HMR",
      "type": "shell",
      "options": {
        "cwd": "${workspaceFolder}/flask_app"
      },
      "command": "bash -c 'until curl -s http://localhost:8000/healthcheck/ > /dev/null 2>&1 || curl -k -s http://localhost:8000/openapi.json > /dev/null 2>&1; do echo \"Waiting for FastAPI server on insecure port 8000...\"; sleep 2; done && echo \"FastAPI server is up, starting Vite HMR...\" && pnpm --filter inertia run dev && sleep 5'",
      "isBackground": true,
      "presentation": {
        "echo": true,
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true
      },
      "problemMatcher": [
        {
          "owner": "vite",
          "pattern": {
            "regexp": "^Never$",
            "severity": 1,
            "message": 2,
            "file": 3,
            "location": 4
          },
          "background": {
            "activeOnStart": true,
            "beginsPattern": ".* \\[vite\\] .*",
            "endsPattern": ".* ready in .*",
          }
        }
      ]
    },
    {
      "label": "Kill Vite",
      "type": "shell",
      // This task is used to kill the Vite process before starting a new one.
      // It uses pkill to find the process by name and kill it, and exits with 0 even if it fails.
      // This is useful to avoid having to manually kill the process before starting a new one.
      "command": "pkill -f vite || exit 0",
      // This command uses lsof to find the process ID of the Vite server running on port 5173 and kills it.
      // It uses xargs to pass the process ID to the kill command.
      // The || exit 0 part ensures that the task exits with 0 even if the kill command fails (e.g., if no process is found).
      // This is useful to avoid having to manually kill the process before starting a new one.
      // "command": "lsof -ti:5173 | xargs kill -9 || exit 0",
      "presentation": {
        "close": true,
        "reveal": "silent"
      },
    },
    {
      "label": "Build JS Assets",
      "type": "shell",
      "options": {
        "cwd": "${workspaceFolder}/flask_app"
      },
      // Run the build script in the background, and answer "yes" to any prompts
      "command": "pnpm -r run build",
      // This task will keep running in the background and doesn't need to complete before the debugger starts
      "isBackground": true,
      "presentation": {
        "reveal": "always",
        "panel": "new",
        // This will close the terminal when the task is done
        "close": true
      },
      // This task is run before some debug tasks.
      // Problem is, it's a watch script, and since it never exits, VSCode
      // complains. All this is needed so VSCode just lets it run.
      "problemMatcher": [
        {
          "pattern": [
            {
              "regexp": ".",
              "file": 1,
              "location": 2,
              "message": 3
            }
          ],
          "background": {
            "activeOnStart": true,
            "beginsPattern": ".",
            "endsPattern": ".",
          }
        }
      ]
      // "runOptions": {
      //   "runOn": "folderOpen"
      // }
    },
    {
      "label": "Rebuild Database",
      "type": "shell",
      "command": "python -m app.models.backout -s head -t base && python -m migrations.util.revision && python -m migrations.util.upgrade",
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "problemMatcher": []
    }
  ]
}