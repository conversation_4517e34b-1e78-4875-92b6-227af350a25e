"""
Unit tests for Flask admin API endpoints used by the Refine admin interface.
Tests the /api/v1/admin/* endpoints defined in app/auth/admin_api.py
"""

import pytest
from shared.models.models import Customer, Role, Structure, User

from app import db
from app.config import R<PERSON><PERSON>_ID_CUSTOMER, ROLE_ID_IJACK_ADMIN, ROLE_ID_IJACK_SALES


class TestAdminAPIEndpoints:
    """Test Flask admin API endpoints at /api/v1/admin/*"""

    @pytest.fixture(autouse=True)
    def setup(self, app, db_session):
        """Set up test fixtures"""
        self.app = app
        self.client = app.test_client()
        self.db = db_session

        # Create test roles
        self.admin_role = Role(id=ROLE_ID_IJACK_ADMIN, name="IJACK Admin")
        self.customer_role = Role(id=ROLE_ID_CUSTOMER, name="Customer")
        self.sales_role = Role(id=ROLE_ID_IJACK_SALES, name="IJACK Sales")

        db.session.add_all([self.admin_role, self.customer_role, self.sales_role])

        # Create test customer
        self.test_customer = Customer(id=1, name="Test Customer", status=1)
        db.session.add(self.test_customer)

        # Create test users
        self.admin_user = User(
            email="<EMAIL>",
            full_name="Admin User",
            confirmed=True,
            customer_id=1,
        )
        self.admin_user.set_password("password123")
        self.admin_user.roles_rel.append(self.admin_role)

        self.customer_user = User(
            email="<EMAIL>",
            full_name="Customer User",
            confirmed=True,
            customer_id=1,
        )
        self.customer_user.set_password("password123")
        self.customer_user.roles_rel.append(self.customer_role)

        # Create additional test data
        self.test_structure = Structure(
            id=1, structure_code="TEST001", customer_id=1, lsd_id=1, active=True
        )

        db.session.add_all([self.admin_user, self.customer_user, self.test_structure])
        db.session.commit()

    def test_admin_api_requires_login(self):
        """Test that admin API endpoints require authentication"""
        # Test without login
        response = self.client.get("/api/v1/admin/users")
        assert response.status_code == 302  # Redirect to login
        assert "/login" in response.location

    def test_admin_api_requires_admin_role(self):
        """Test that admin API endpoints require admin role"""
        # Login as non-admin user
        with self.client:
            self.client.post(
                "/login",
                data={"email": self.customer_user.email, "password": "password123"},
                follow_redirects=True,
            )

            response = self.client.get("/api/v1/admin/users")
            assert response.status_code == 403

            data = response.get_json()
            assert data["error"] == "Admin access required"

    def test_admin_api_list_users(self):
        """Test listing users through admin API"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Test basic list
            response = self.client.get("/api/v1/admin/users")
            assert response.status_code == 200

            data = response.get_json()
            assert "result" in data
            assert "data" in data["result"]
            assert "total" in data["result"]
            assert isinstance(data["result"]["data"], list)
            assert data["result"]["total"] >= 2  # At least our 2 test users

    def test_admin_api_pagination(self):
        """Test pagination parameters"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Test with pagination
            response = self.client.get("/api/v1/admin/users?page=1&per_page=1")
            assert response.status_code == 200

            data = response.get_json()
            assert len(data["result"]["data"]) <= 1

    def test_admin_api_sorting(self):
        """Test sorting parameters"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Test sorting by email ascending
            response = self.client.get("/api/v1/admin/users?sort=email&order=asc")
            assert response.status_code == 200

            data = response.get_json()
            users = data["result"]["data"]
            if len(users) > 1:
                # Check that results are sorted
                emails = [u["email"] for u in users]
                assert emails == sorted(emails)

    def test_admin_api_filtering(self):
        """Test filtering parameters"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Test filtering by email contains
            response = self.client.get(
                "/api/v1/admin/users?filter_email__contains=admin"
            )
            assert response.status_code == 200

            data = response.get_json()
            users = data["result"]["data"]
            # Should find at least our admin user
            assert any(u["email"] == "<EMAIL>" for u in users)

    def test_admin_api_get_single_user(self):
        """Test getting a single user by ID"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Get admin user by ID
            response = self.client.get(f"/api/v1/admin/users/{self.admin_user.id}")
            assert response.status_code == 200

            data = response.get_json()
            assert "result" in data
            assert data["result"]["id"] == self.admin_user.id
            assert data["result"]["email"] == self.admin_user.email

    def test_admin_api_invalid_resource(self):
        """Test accessing invalid resource"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Try to access non-existent resource
            response = self.client.get("/api/v1/admin/invalid_resource")
            assert response.status_code == 400

            data = response.get_json()
            assert "error" in data
            assert "Invalid resource" in data["error"]

    def test_admin_api_non_existent_id(self):
        """Test getting non-existent ID"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Try to get non-existent user
            response = self.client.get("/api/v1/admin/users/99999")
            assert response.status_code == 404

            data = response.get_json()
            assert "error" in data

    def test_admin_api_other_resources(self):
        """Test accessing other resources like customers, structures"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Test customers endpoint
            response = self.client.get("/api/v1/admin/customers")
            assert response.status_code == 200
            data = response.get_json()
            assert "result" in data
            assert data["result"]["total"] >= 1

            # Test structures endpoint
            response = self.client.get("/api/v1/admin/structures")
            assert response.status_code == 200
            data = response.get_json()
            assert "result" in data

    def test_admin_api_create_not_implemented(self):
        """Test that CREATE operations return 501 Not Implemented"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Try to create a user
            response = self.client.post(
                "/api/v1/admin/users",
                json={"email": "<EMAIL>", "full_name": "New User"},
                content_type="application/json",
            )
            assert response.status_code == 501

            data = response.get_json()
            assert "error" in data
            assert "Not implemented" in data["error"]

    def test_admin_api_update_not_implemented(self):
        """Test that UPDATE operations return 501 Not Implemented"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Try to update a user
            response = self.client.put(
                f"/api/v1/admin/users/{self.admin_user.id}",
                json={"full_name": "Updated Name"},
                content_type="application/json",
            )
            assert response.status_code == 501

            data = response.get_json()
            assert "error" in data
            assert "Not implemented" in data["error"]

    def test_admin_api_delete_not_implemented(self):
        """Test that DELETE operations return 501 Not Implemented"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Try to delete a user
            response = self.client.delete(
                f"/api/v1/admin/users/{self.customer_user.id}"
            )
            assert response.status_code == 501

            data = response.get_json()
            assert "error" in data
            assert "Not implemented" in data["error"]

    def test_admin_api_csrf_exempt(self):
        """Test that admin API endpoints are CSRF exempt"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Make request without CSRF token (should still work)
            response = self.client.get("/api/v1/admin/users")
            assert response.status_code == 200

            # POST request without CSRF should also work (though returns 501)
            response = self.client.post(
                "/api/v1/admin/users",
                json={"test": "data"},
                content_type="application/json",
            )
            assert response.status_code == 501  # Not implemented, but not CSRF error
