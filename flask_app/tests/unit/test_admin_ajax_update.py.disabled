from flask_login import current_user
from flask_wtf.csrf import generate_csrf
from shared.models.models import Customer

from app import config
from app.models.models import User
from tests.conftest import ijack_context


def test_admin_ajax_update(all_ijack):
    """
    Test that we can update x-editable fields in the list view, using AJAX
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    # Test authentication status
    with ijack_context(flask_app, logged_in_admin=True):
        assert current_user.is_authenticated
        assert current_user.email == "<EMAIL>"

        # First make a GET request to establish a session
        flask_test_client.get("/admin/users/")
        
        # Generate CSRF token
        csrf_token = generate_csrf()

        new_name = "Fake name for Pytest automated testing..."
        data: dict = {
            "list_form_pk": config.USER_ID_TEST_CUSTOMER,
            "first_name": new_name,
            # "customer_id": CUSTOMER_ID_DEMO,
            # "main_customer_rel": db.session.get(Customer, CUSTOMER_ID_DEMO),
            # "customers_rel": [db.session.get(Customer, CUSTOMER_ID_IJACK_INC)],
            # Apparently this is how to properly test a POST request to a relationship field
            "main_customer_rel": config.CUSTOMER_ID_DEMO,
            "customers_rel": [config.CUSTOMER_ID_IJACK_INC],
            "csrf_token": csrf_token,  # Include CSRF token
        }
        
        response = flask_test_client.post(
            # This user has the HR role so it uses a different endpoint
            "/admin/users/ajax/update",
            data=data,
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "X-CSRFToken": csrf_token,
            },
            follow_redirects=True,
        )
        data = response.data.decode("utf-8")
        
        # Debug: Print response details if status is not 200
        if response.status_code != 200:
            print(f"Response status: {response.status_code}")
            print(f"Response data: {data[:500]}...")  # First 500 chars
            print(f"Current user: {current_user}")
            print(f"Current user roles: {getattr(current_user, 'roles_rel', 'No roles')}")
            
            # Check if the user being updated exists
            test_user = db.session.get(User, config.USER_ID_TEST_CUSTOMER)
            print(f"User being updated exists: {test_user is not None}")
            if test_user:
                print(f"Test user: {test_user.email}")
            
            # Check if customers exist
            demo_customer = db.session.get(Customer, config.CUSTOMER_ID_DEMO)
            ijack_customer = db.session.get(Customer, config.CUSTOMER_ID_IJACK_INC)
            print(f"Demo customer exists: {demo_customer is not None}")
            print(f"IJACK customer exists: {ijack_customer is not None}")
        
        assert response.status_code == 200
        assert data == "Record was successfully saved."
        # assert b"You have successfully duplicated that record" in response.data

        # ensure the value has changed
        rv = flask_test_client.get("/admin/users/?page_size=5")
        assert rv.status_code == 200
        data = rv.data.decode("utf-8")
        assert new_name in data

        user_model = db.session.get(User, config.USER_ID_TEST_CUSTOMER)
        assert user_model.first_name == new_name
        assert user_model.main_customer_rel == db.session.get(
            Customer, config.CUSTOMER_ID_DEMO
        )
        assert user_model.customers_rel == [
            db.session.get(Customer, config.CUSTOMER_ID_IJACK_INC),
            db.session.get(Customer, config.CUSTOMER_ID_DEMO),
        ]
