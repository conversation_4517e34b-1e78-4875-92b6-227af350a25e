"""
Simple unit tests for admin3 access control that only tests the basic functionality.
"""


def test_admin3_requires_login(test_client):
    """Test that admin3 route requires authentication"""
    response = test_client.get("/admin3")
    assert response.status_code == 302
    assert "/login" in response.location


def test_admin3_api_requires_authentication(test_client):
    """Test that admin API endpoints require authentication"""
    endpoints = [
        "/api/v1/admin/users",
        "/api/v1/admin/customers",
        "/api/v1/admin/roles",
    ]

    for endpoint in endpoints:
        response = test_client.get(endpoint)
        # Should either redirect to login or return 401
        assert response.status_code in [302, 401], (
            f"Endpoint {endpoint} should require auth"
        )
