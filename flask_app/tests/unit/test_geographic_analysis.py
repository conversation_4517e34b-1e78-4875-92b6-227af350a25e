#!/usr/bin/env python3
"""
Test script for Enhanced Geographic Analysis Plan implementation.

This script validates that all phases have been implemented correctly
and that the DRY principles have been followed.
"""

import sys
import traceback
from pathlib import Path

# Add project paths
sys.path.append("/project")
sys.path.append("/project/packages")

# Import all test dependencies at module level
try:
    from shared.utils.datetime_utils import utcnow_naive
    from shared.utils.geographic_utils import calc_distance
    from shared.utils.string_utils import generate_random_string
    from shared.models.models import Structure, GeocodingCache
    from shared.services.geocoding_service import (
        get_geocoding_service,
        HybridGeocodingService,
    )
    from shared.jobs.geocoding_job import GeocodingJob
except ImportError as e:
    print(f"Warning: Could not import some dependencies: {e}")
    # We'll handle this gracefully in the test functions


def test_phase_1_utilities():
    """Test Phase 1: Shared utilities are accessible and working"""
    print("Testing Phase 1: Shared Utilities...")

    try:
        # Test datetime utilities
        now = utcnow_naive()
        assert now is not None
        print("  ✓ DateTime utilities working")

        # Test geographic utilities
        distance = calc_distance(51.0447, -114.0719, 53.5461, -113.4938)
        assert distance > 0
        print("  ✓ Geographic utilities working")

        # Test string utilities
        random_str = generate_random_string(10)
        assert len(random_str) == 10
        print("  ✓ String utilities working")

        # Test Flask backward compatibility by checking file exists
        flask_utils_path = Path("/project/flask_app/app/utils/simple.py")
        assert flask_utils_path.exists(), "Flask utils file not found"

        # Check that it imports shared utilities
        flask_utils_content = flask_utils_path.read_text()
        assert "from shared.utils.datetime_utils import" in flask_utils_content
        assert "from shared.utils.geographic_utils import" in flask_utils_content
        print("  ✓ Flask backward compatibility imports configured")

        return True

    except Exception as e:
        print(f"  ✗ Phase 1 failed: {e}")
        traceback.print_exc()
        return False


def test_phase_2_models():
    """Test Phase 2: Database models have geographic fields"""
    print("Testing Phase 2: Database Models...")

    try:
        # Test Structure model has geographic fields
        # Check that the model has the expected attributes (no country_id - accessed via join)
        structure_attrs = dir(Structure)
        required_attrs = [
            "province_id",
            "geocoding_status",
            "geocoding_updated_at",
            "auto_geocoded",
        ]

        for attr in required_attrs:
            assert attr in structure_attrs, f"Missing attribute: {attr}"

        # Ensure country_id is NOT in Structure (should be accessed via province join)
        assert "country_id" not in structure_attrs, (
            "country_id should not exist in Structure - use province join"
        )

        print("  ✓ Structure model has correct geographic fields (province_id only)")

        # Test GeocodingCache model exists
        cache_attrs = dir(GeocodingCache)
        required_cache_attrs = [
            "lat_rounded",
            "lon_rounded",
            "country_id",
            "province_id",
        ]

        for attr in required_cache_attrs:
            assert attr in cache_attrs, f"Missing GeocodingCache attribute: {attr}"

        print("  ✓ GeocodingCache model exists with required fields")

        return True

    except Exception as e:
        print(f"  ✗ Phase 2 failed: {e}")
        traceback.print_exc()
        return False


def test_phase_3_geocoding():
    """Test Phase 3: Geocoding service is working"""
    print("Testing Phase 3: Geocoding Service...")

    try:
        # Test geocoding service import
        service = get_geocoding_service()
        assert isinstance(service, HybridGeocodingService)
        print("  ✓ Geocoding service instantiation working")

        # Test geographic region name generation
        region_info = service.get_geographic_region_name(51.0447, -114.0719)
        assert "region_name" in region_info
        assert "country_name" in region_info
        assert region_info["region_name"] == "Near Calgary"
        print("  ✓ Geographic region name generation working")

        # Test service analytics regions
        test_coords = [
            {"id": 1, "lat": 51.0447, "lon": -114.0719},
            {"id": 2, "lat": 53.5461, "lon": -113.4938},
        ]
        regions = service.generate_service_analytics_regions(test_coords)
        assert len(regions) == 2
        assert all("region_name" in r for r in regions)
        print("  ✓ Service analytics regions working")

        return True

    except Exception as e:
        print(f"  ✗ Phase 3 failed: {e}")
        traceback.print_exc()
        return False


def test_phase_4_migrations():
    """Test Phase 4: Migration files exist"""
    print("Testing Phase 4: Migration Files...")

    try:
        # Check migration file exists
        migration_file = Path(
            "/project/flask_app/migrations/versions/add_geographic_fields_2025_06_20.py"
        )
        assert migration_file.exists(), "Migration file not found"
        print("  ✓ Migration file exists")

        # Check migration content
        content = migration_file.read_text()
        assert "geocoding_cache" in content
        assert "province_id" in content

        # Ensure country_id is NOT added to structures table (proper DB design)
        lines = content.split("\n")
        structure_lines = [
            line
            for line in lines
            if "structures" in line
            and "country_id" in line
            and not line.strip().startswith("#")
        ]
        assert len(structure_lines) == 0, (
            "Migration should not add country_id to structures table"
        )

        print(
            "  ✓ Migration contains proper geographic fields (province_id only for structures)"
        )

        return True

    except Exception as e:
        print(f"  ✗ Phase 4 failed: {e}")
        traceback.print_exc()
        return False


def test_phase_5_jobs():
    """Test Phase 5: Background jobs are working"""
    print("Testing Phase 5: Background Jobs...")

    try:
        # Test geocoding job import
        job = GeocodingJob(batch_size=2)
        assert job.batch_size == 2
        print("  ✓ GeocodingJob instantiation working")

        # Test job processing (dry run)
        test_structures = [
            {"id": 1, "lat": 51.0447, "lon": -114.0719, "name": "Test Site"}
        ]
        results = job.process_structures_batch(test_structures)
        assert "processed" in results
        assert results["processed"] >= 0
        print("  ✓ Job processing working")

        return True

    except Exception as e:
        print(f"  ✗ Phase 5 failed: {e}")
        traceback.print_exc()
        return False


def test_dry_principles():
    """Test that DRY principles are followed"""
    print("Testing DRY Principles...")

    try:
        # Test that shared utilities exist and are working
        # Test shared utilities work
        now = utcnow_naive()
        distance = calc_distance(51.0, -114.0, 53.5, -113.5)
        assert now is not None
        assert distance > 0
        print("  ✓ Shared utilities are working")

        # Test that Flask utils imports from shared
        flask_utils_path = Path("/project/flask_app/app/utils/simple.py")
        flask_content = flask_utils_path.read_text()

        # Check for shared imports
        shared_imports = [
            "from shared.utils.datetime_utils import",
            "from shared.utils.geographic_utils import",
            "from shared.utils.string_utils import",
        ]

        for import_line in shared_imports:
            assert import_line in flask_content, f"Missing shared import: {import_line}"

        print("  ✓ Flask utilities correctly import from shared packages")

        # Test that packages structure follows DRY
        packages_path = Path("/project/packages/shared")
        assert packages_path.exists(), "Shared packages directory not found"

        utils_path = packages_path / "utils"
        models_path = packages_path / "models"
        services_path = packages_path / "services"
        jobs_path = packages_path / "jobs"

        assert utils_path.exists(), "Shared utils not found"
        assert models_path.exists(), "Shared models not found"
        assert services_path.exists(), "Shared services not found"
        assert jobs_path.exists(), "Shared jobs not found"

        print("  ✓ DRY package structure is correctly organized")

        return True

    except Exception as e:
        print(f"  ✗ DRY principles test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests for the Enhanced Geographic Analysis Plan"""
    print("Enhanced Geographic Analysis Plan - Validation Test")
    print("=" * 55)

    tests = [
        test_phase_1_utilities,
        test_phase_2_models,
        test_phase_3_geocoding,
        test_phase_4_migrations,
        test_phase_5_jobs,
        test_dry_principles,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")
            print()

    print("=" * 55)
    print(f"Test Results: {passed}/{total} passed")

    if passed == total:
        print(
            "🎉 All tests passed! Enhanced Geographic Analysis Plan is working correctly."
        )
        print("The implementation follows DRY principles and is ready for production.")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
