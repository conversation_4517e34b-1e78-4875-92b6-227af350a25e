"""
Test inventory manager's delta calculation when work orders are modified.

This test ensures that when a work order is saved multiple times with different
quantities, the inventory system calculates the DIFFERENCE and doesn't duplicate
the operations.
"""

import uuid
from decimal import Decimal

from shared.models.models_bom import (
    InventoryMovement,
    InventoryReservation,
    MovementType,
    ReservationStatus,
    WarehousePart,
)
from shared.models.models_work_order import WorkOrder

from app import config
from app.dashapp.callbacks.work_order import work_order_form_submit
from tests.conftest import ijack_context


def test_work_order_transfer_quantity_update_delta(all_ijack):
    """
    Test that updating a work order with a different transfer quantity
    properly calculates the delta instead of adding the full new quantity.

    Scenario:
    1. Start with 2 units in each warehouse
    2. Create approved work order transferring 1 unit from Moosomin to Dallas
    3. Result: Moosomin=1, Dallas=3
    4. Update same work order to transfer 2 units instead of 1
    5. Expected: Moosomin=0, Dallas=4 (delta of 1 more unit)
    6. NOT: Moosomin=-1, Dallas=5 (which would be adding 2 more)
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    # Unique invoice for this test
    unique_id = str(uuid.uuid4())[:8]
    invoice_summary = (
        f"Delta Test {unique_id}\nTransfer Update Test\nWO n/a\nAFE n/a\nPO n/a\n\n\n"
    )
    signature_base64 = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48L3N2Zz4="

    with ijack_context(flask_app, logged_in_cust=True):
        # Setup warehouse parts with initial quantities
        warehouse_part_moosomin = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN,
            )
            .first()
        )
        if not warehouse_part_moosomin:
            warehouse_part_moosomin = WarehousePart(
                part_id=config.PART_ID_PART_0,
                warehouse_id=config.WAREHOUSE_ID_MOOSOMIN,
                quantity=Decimal("2"),
                quantity_reserved=Decimal("0"),
            )
            db.session.add(warehouse_part_moosomin)
        else:
            warehouse_part_moosomin.quantity = Decimal("2")
            warehouse_part_moosomin.quantity_reserved = Decimal("0")

        warehouse_part_dallas = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_DALLAS,
            )
            .first()
        )
        if not warehouse_part_dallas:
            warehouse_part_dallas = WarehousePart(
                part_id=config.PART_ID_PART_0,
                warehouse_id=config.WAREHOUSE_ID_DALLAS,
                quantity=Decimal("2"),
                quantity_reserved=Decimal("0"),
            )
            db.session.add(warehouse_part_dallas)
        else:
            warehouse_part_dallas.quantity = Decimal("2")
            warehouse_part_dallas.quantity_reserved = Decimal("0")

        db.session.commit()

        # Verify initial state
        assert warehouse_part_moosomin.quantity == Decimal("2")
        assert warehouse_part_dallas.quantity == Decimal("2")
        print("✓ Initial state: Moosomin=2, Dallas=2")

        # Step 1: Create approved work order transferring 1 unit
        (
            modal_open,
            title,
            work_order_modal_title_style,
            body,
            work_order_modal_view_all_btn_class_name,
            work_order_modal_create_new_btn_children,
            store_work_order_id_to_clone_data,
            work_order_modal_clone_card_style,
            edit_work_order_id_data,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2023-01-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_CORP,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Test Crew",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Test Location",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Test transfer delta handling",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Testing delta calculation on updates",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=100.00,
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=100.00,
            work_order_sales_tax_store_data=5.00,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=105.00,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": 0,
                    "description": "Transfer 1 unit from Moosomin to Dallas",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "warehouse_to_id": config.WAREHOUSE_ID_DALLAS,  # Transfer to Dallas
                    "quantity": 1,  # Transfer 1 unit
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                {
                    "delete": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=None,
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2023-01-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=True,  # Approved
            work_order_is_quote_value=False,  # Not a quote
            work_order_is_tax_exempt_value=False,
        )

        # Verify work order creation
        assert modal_open is True
        assert title == "Success!"
        work_order_id = int(edit_work_order_id_data)

        # Check warehouse quantities after first save
        db.session.refresh(warehouse_part_moosomin)
        db.session.refresh(warehouse_part_dallas)

        assert warehouse_part_moosomin.quantity == Decimal("1"), (
            f"After transferring 1 unit, Moosomin should have 1, got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part_dallas.quantity == Decimal("3"), (
            f"After receiving 1 unit, Dallas should have 3, got {warehouse_part_dallas.quantity}"
        )
        print("✓ After first save (transfer 1): Moosomin=1, Dallas=3")

        # Verify reservations are fulfilled
        reservations = (
            db.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.reference_type == "work_order",
            )
            .all()
        )
        assert all(r.status == ReservationStatus.FULFILLED for r in reservations), (
            "All reservations should be fulfilled for approved work order"
        )

        # Verify movements exist
        movements = (
            db.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 1, f"Should have 1 movement, got {len(movements)}"
        assert movements[0].movement_type == MovementType.TRANSFER
        assert movements[0].quantity == Decimal("1")
        print("✓ Movements and reservations created correctly")

        # IMPORTANT: Commit the transaction so the movements are visible to the next operation
        db.session.commit()
        print("✓ Transaction committed")

        # Step 2: Update the same work order to transfer 2 units instead of 1
        print("\n--- Updating work order to transfer 2 units instead of 1 ---")

        # Debug: Check movements before update
        movements_before_update = (
            db.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        print(f"Movements before update: {len(movements_before_update)}")
        for m in movements_before_update:
            print(
                f"  Movement {m.id}: {m.movement_number}, qty={m.quantity}, from_wh={m.from_warehouse_id}, to_wh={m.to_warehouse_id}"
            )

        (
            modal_open_2,
            title_2,
            work_order_modal_title_style_2,
            body_2,
            work_order_modal_view_all_btn_class_name_2,
            work_order_modal_create_new_btn_children_2,
            store_work_order_id_to_clone_data_2,
            work_order_modal_clone_card_style_2,
            edit_work_order_id_data_2,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2023-01-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_CORP,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Test Crew",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Test Location",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Test transfer delta handling",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Testing delta calculation on updates",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=200.00,  # Updated price for 2 units
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=200.00,
            work_order_sales_tax_store_data=10.00,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=210.00,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": 0,
                    "description": "Transfer 2 units from Moosomin to Dallas",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "warehouse_to_id": config.WAREHOUSE_ID_DALLAS,  # Transfer to Dallas
                    "quantity": 2,  # Now transfer 2 units instead of 1
                    "price": 200.00,
                    "cost_before_tax": 200.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                {
                    "delete": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=work_order_id,  # Edit existing work order
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2023-01-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=True,  # Still approved
            work_order_is_quote_value=False,  # Still not a quote
            work_order_is_tax_exempt_value=False,
        )

        # Verify update was successful
        assert modal_open_2 is True
        assert title_2 == "Success!"

        # Check final warehouse quantities
        db.session.refresh(warehouse_part_moosomin)
        db.session.refresh(warehouse_part_dallas)

        print("\nFinal quantities:")
        print(f"  Moosomin: {warehouse_part_moosomin.quantity} (expected: 0)")
        print(f"  Dallas: {warehouse_part_dallas.quantity} (expected: 4)")

        # The key assertion: quantities should reflect the DELTA
        assert warehouse_part_moosomin.quantity == Decimal("0"), (
            f"After updating transfer from 1 to 2 units, Moosomin should have 0 (2-2), "
            f"got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part_dallas.quantity == Decimal("4"), (
            f"After updating transfer from 1 to 2 units, Dallas should have 4 (2+2), "
            f"got {warehouse_part_dallas.quantity}"
        )

        # Verify only one movement exists (updated, not duplicated)
        movements = (
            db.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 1, (
            f"Should still have only 1 movement (updated), got {len(movements)}"
        )
        assert movements[0].quantity == Decimal("2"), (
            f"Movement should be updated to 2 units, got {movements[0].quantity}"
        )

        print("✅ Test passed: Delta calculation works correctly!")

        # Cleanup
        work_order = db.session.get(WorkOrder, work_order_id)
        if work_order:
            # Delete related inventory first
            for reservation in (
                db.session.query(InventoryReservation)
                .filter(
                    InventoryReservation.reference_id == work_order_id,
                    InventoryReservation.reference_type == "work_order",
                )
                .all()
            ):
                db.session.delete(reservation)

            for movement in (
                db.session.query(InventoryMovement)
                .filter(
                    InventoryMovement.reference_id == work_order_id,
                    InventoryMovement.reference_type == "work_order",
                )
                .all()
            ):
                db.session.delete(movement)

            db.session.delete(work_order)

        # Reset warehouse quantities
        warehouse_part_moosomin.quantity = Decimal("2")
        warehouse_part_moosomin.quantity_reserved = Decimal("0")
        warehouse_part_dallas.quantity = Decimal("2")
        warehouse_part_dallas.quantity_reserved = Decimal("0")
        db.session.commit()
