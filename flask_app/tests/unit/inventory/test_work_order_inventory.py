import uuid
from decimal import Decimal

import pytest
from shared.models.models_bom import (
    InventoryMovement,
    InventoryReservation,
    MovementType,
    Part,
    ReservationStatus,
    Warehouse,
    WarehousePart,
)
from shared.models.models_work_order import (
    WorkOrder,
    WorkOrderPart,
)
from shared.utils.inventory_manager import (
    InventoryManager,
    WorkOrderState,
)
from sqlalchemy import text

from app import config
from app.dashapp.callbacks.work_order import (
    work_order_form_submit,
)
from tests.conftest import ijack_context


def ensure_parts_exist(db_session, part_ids, **kwargs):
    """
    DRY helper function to ensure parts exist in the database before running tests.

    This prevents foreign key constraint errors when part IDs are used as foreign keys
    in WarehousePart, WorkOrderPart, or other tables that reference parts.

    Args:
        db_session: SQLAlchemy database session
        part_ids: Single part ID (int) or list of part IDs
        **kwargs: Optional field overrides for part creation

    Returns:
        dict: Mapping of part_id -> Part object for all requested parts

    Example:
        # Ensure single part exists
        parts = ensure_parts_exist(db.session, config.PART_ID_PART_0)

        # Ensure multiple parts exist
        parts = ensure_parts_exist(db.session, [config.PART_ID_PART_0, config.PART_ID_PART_0, 99999])

        # Create parts with custom cost
        parts = ensure_parts_exist(db.session, [123, 456], cost_cad=Decimal("50.00"))
    """
    # Normalize part_ids to a list
    if isinstance(part_ids, (int, str)):
        part_ids = [part_ids]

    # Default values for part creation (all required fields)
    defaults = {
        "cost_cad": Decimal("10.00"),
        "cost_usd": Decimal("10.00"),
        "msrp_mult_cad": 1.0,
        "msrp_mult_usd": 1.0,
        "msrp_cad": Decimal("10.00"),
        "msrp_usd": Decimal("10.00"),
        "transfer_mult_cad_dealer": 1.0,
        "transfer_mult_usd_dealer": 1.0,
        "transfer_mult_inc_to_corp": 1.0,
        "warehouse_mult": 1.0,
        "dealer_cost_cad": Decimal("10.00"),
        "dealer_cost_usd": Decimal("10.00"),
        "ijack_corp_cost": Decimal("10.00"),
        "is_usd": False,
        "is_soft_part": False,
        "is_hard_part": False,
        "cad_per_usd": Decimal("1.42"),
    }

    # Override defaults with any provided kwargs
    defaults.update(kwargs)

    parts = {}

    for part_id in part_ids:
        # Check if part already exists
        part = db_session.get(Part, part_id)

        if not part:
            # Create minimal part record with sensible defaults
            part_data = defaults.copy()
            part_data.update(
                {
                    "id": part_id,
                    "part_num": str(part_id),
                    "description": f"Test Part {part_id}",
                }
            )

            part = Part(**part_data)
            db_session.add(part)

        parts[part_id] = part

    # Commit all new parts at once
    db_session.commit()

    return parts


def generate_unique_invoice_summary(test_name):
    """Generate a unique invoice summary for each test to prevent collisions."""
    unique_id = str(uuid.uuid4())[:8]
    return f"{test_name}_{unique_id}\nTest Invoice Summary\nWO n/a\nAFE n/a\nPO n/a\nTest Description\n\n\n"


def setup_warehouse_part_for_test(db_session, warehouse_id, part_id=None):
    """Set up warehouse part with clean state for testing."""
    # Get the current values from app.config to ensure we use patched values
    from app import config

    if part_id is None:
        part_id = config.PART_ID_PART_0

    warehouse_part = (
        db_session.query(WarehousePart)
        .filter(
            WarehousePart.part_id == part_id,
            WarehousePart.warehouse_id == warehouse_id,
        )
        .first()
    )

    if not warehouse_part:
        warehouse_part = WarehousePart(
            part_id=part_id,
            warehouse_id=warehouse_id,
            quantity=Decimal("2"),
            quantity_reserved=Decimal("0"),
            average_cost=Decimal("10.00"),
            last_cost=Decimal("10.00"),
        )
        db_session.add(warehouse_part)
    else:
        warehouse_part.quantity = Decimal("2")
        warehouse_part.quantity_reserved = Decimal("0")

    db_session.commit()
    return warehouse_part


@pytest.fixture
def setup_warehouse_parts(all_ijack):
    """
    Fixture to set up warehouse parts for tests that need them.
    This fixture ensures clean state before and after each test.
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    # Get the current patched values from app.config
    from app import config

    # Ensure the part exists before creating warehouse parts (prevents FK errors)
    ensure_parts_exist(db.session, config.PART_ID_PART_0)

    # Setup: Create/reset warehouse parts to known state
    warehouse_parts = {}
    for warehouse_id in [config.WAREHOUSE_ID_MOOSOMIN, config.WAREHOUSE_ID_DALLAS]:
        warehouse_part = setup_warehouse_part_for_test(
            db.session, warehouse_id, config.PART_ID_PART_0
        )
        warehouse_parts[warehouse_id] = warehouse_part

    yield warehouse_parts

    # Teardown: Reset warehouse parts to clean state
    for warehouse_id, warehouse_part in warehouse_parts.items():
        warehouse_part.quantity = Decimal("2")
        warehouse_part.quantity_reserved = Decimal("0")

    db.session.commit()


def test_work_order_quote_no_inventory_operations(all_ijack, setup_warehouse_parts):
    """
    Test that when a work order is submitted as a quote and not approved yet,
    no inventory reservations or movements are created.
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    # For filtering after the test, to see if the work order was created correctly
    invoice_summary_value = "test\nQuote Test - No Inventory\nWO n/a\nAFE n/a\nPO n/a\nRepair: Gateway. SN 9999. 9999.\n\n\n"

    signature_base64 = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48L3N2Zz4="

    with ijack_context(flask_app, logged_in_cust=True):
        # Check if the part already exists, create it if it doesn't
        test_part = db.session.get(Part, config.PART_ID_PART_0)
        if test_part is None:
            test_part = Part(
                id=config.PART_ID_PART_0,
                part_num="0",
                description="Anything you want (miscellaneous)",
                cost_cad=1.0,
                cost_usd=1.0,
                msrp_mult_cad=2.0,
                msrp_mult_usd=2.0,
                msrp_cad=2.0,
                msrp_usd=2.0,
                transfer_mult_cad_dealer=1.5,
                transfer_mult_usd_dealer=1.5,
                transfer_mult_inc_to_corp=0.8,
                warehouse_mult=1.2,
                dealer_cost_cad=1.5,
                dealer_cost_usd=1.5,
                ijack_corp_cost=0.8,
                is_usd=False,
                is_soft_part=False,
                is_hard_part=False,
            )
            db.session.add(test_part)
            db.session.commit()

        # Submit work order as a quote (is_quote=True, approved=False)
        (
            modal_open,
            title,
            work_order_modal_title_style,
            body,
            work_order_modal_view_all_btn_class_name,
            work_order_modal_create_new_btn_children,
            store_work_order_id_to_clone_data,
            work_order_modal_clone_card_style,
            edit_work_order_id_data,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2021-09-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_INC,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_CORP,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Sean McCarthy",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Calgary",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Service required for quote test",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Quote - work not done yet",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary_value,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=1.99,
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=1.99,
            work_order_sales_tax_store_data=0.0995,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=2.0895,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": "0",  # Add the actual part number for validation
                    "description": "Anything you want (miscellaneous)",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "quantity": 1,
                    "price": 1.99,
                    "cost_before_tax": 1.99,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                {
                    "delete": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=None,
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2021-09-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=False,  # Not approved yet
            work_order_is_quote_value=True,  # This is a quote
            work_order_is_tax_exempt_value=False,
        )

        # Verify work order was created successfully
        assert modal_open is True, "Confirmation modal should be open"
        assert title == "Success!"
        assert work_order_modal_title_style == {}, "Title should be styled correctly"
        # The body is now a list containing the message and inventory report
        assert isinstance(body, list), (
            "Body should be a list containing message and inventory report"
        )
        assert len(body) >= 2, (
            "Body should contain at least message and inventory report"
        )
        # Check that the first element contains the success message
        assert hasattr(body[0], "children"), (
            "First body element should be a Dash component"
        )
        assert body[0].children == "Work order created", (
            "First element should contain success message"
        )
        assert isinstance(float(edit_work_order_id_data), float), (
            "Edit work order ID should be a float"
        )

        # Find the created work order
        work_order = (
            db.session.query(WorkOrder)
            .filter_by(
                invoice_summary=invoice_summary_value,
                customer_id=config.CUSTOMER_ID_IJACK_CORP,
                currency_id=config.CURRENCY_ID_USD,
            )
            .first()
        )
        assert work_order is not None, "Work order should be created"
        assert str(work_order.id) == edit_work_order_id_data
        assert work_order.creator_id == config.USER_ID_TEST_CUSTOMER
        assert work_order.invoice_approval_req is False, (
            "Quote should not require approval yet"
        )
        assert work_order.is_quote is True, "Should be marked as quote"

        # Verify work order parts were created
        work_order_parts = work_order.work_order_parts_rel
        assert len(work_order_parts) == 1, "Should have one work order part"
        work_order_part = work_order_parts[0]
        assert work_order_part.quantity == Decimal("1")
        assert work_order_part.price == Decimal("1.99")
        assert work_order_part.parts_rel.id == config.PART_ID_PART_0

        # **Key Test: Verify NO inventory reservations were created**
        reservations = (
            db.session.query(InventoryReservation)
            .filter_by(
                reference_type="work_order",
                reference_id=work_order.id,
            )
            .all()
        )
        assert len(reservations) == 0, (
            f"No inventory reservations should be created for a quote. Found {len(reservations)} reservations."
        )

        # **Key Test: Verify NO inventory movements were created**
        movements = (
            db.session.query(InventoryMovement)
            .filter_by(
                reference_type="work_order",
                reference_id=work_order.id,
            )
            .all()
        )
        assert len(movements) == 0, (
            f"No inventory movements should be created for a quote. Found {len(movements)} movements."
        )

        print(
            f"✅ Test passed: Work order {work_order.id} created as quote with no inventory operations"
        )

        # Clean up - delete the work order
        db.session.delete(work_order)
        db.session.commit()

    return


def test_new_approved_work_order_with_inventory_operations(
    all_ijack, setup_warehouse_parts
):
    """
    Test that when an approved work order is submitted, reservation and movement
    are created and warehouse quantities are updated correctly.

    This test verifies:
    1. Initial warehouse part quantity is 2
    2. After creating an approved work order with quantity 1
    3. One reservation is created with quantity 1
    4. One movement is created with quantity 1
    5. Final warehouse part quantity is reduced to 1
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    invoice_summary_value = (
        "Approved Work Order Test\nNew WO with Inventory\nWO n/a\nAFE n/a\nPO n/a\n\n\n"
    )
    signature_base64 = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48L3N2Zz4="

    with ijack_context(flask_app, logged_in_cust=True):
        # First, set up the warehouse parts with initial quantity of 2 for both possible part IDs
        warehouse_part_moosomin = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN,
            )
            .first()
        )

        # Verify initial quantity is 2
        initial_quantity = warehouse_part_moosomin.quantity
        assert initial_quantity == Decimal("2"), (
            f"Initial quantity should be 2, got {initial_quantity}"
        )

        # Submit work order as an approved work order (is_quote=False, approved=True)
        (
            modal_open,
            title,
            work_order_modal_title_style,
            body,
            work_order_modal_view_all_btn_class_name,
            work_order_modal_create_new_btn_children,
            store_work_order_id_to_clone_data,
            work_order_modal_clone_card_style,
            edit_work_order_id_data,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2023-01-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_CORP,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Test Crew",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Test Location",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Test service required",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Test work done",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary_value,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=100.00,
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=100.00,
            work_order_sales_tax_store_data=5.00,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=105.00,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,  # Using our test part
                    "part_num": config.PART_ID_PART_0,  # Using the patched part ID
                    "description": "Test Part",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "quantity": 1,  # Quantity of 1
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                {
                    "delete": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=None,
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2023-01-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=True,  # Approved
            work_order_is_quote_value=False,  # Not a quote
            work_order_is_tax_exempt_value=False,
        )

        # Verify work order was created successfully
        assert modal_open is True, "Confirmation modal should be open"
        assert title == "Success!"
        assert work_order_modal_title_style == {}, "Title should be styled correctly"
        assert isinstance(edit_work_order_id_data, str), (
            "Edit work order ID should be set"
        )
        work_order_id = int(edit_work_order_id_data)

        # Find the created work order using the ID returned from the form submit
        work_order_id = int(edit_work_order_id_data)
        work_order = db.session.get(WorkOrder, work_order_id)
        assert work_order is not None, "Work order should be created"
        assert work_order.is_quote is False, "Work order should not be a quote"
        assert work_order.invoice_approval_req is True, "Work order should be approved"

        # Verify work order parts were created
        work_order_parts = work_order.work_order_parts_rel
        assert len(work_order_parts) == 1, "Should have one work order part"
        work_order_part = work_order_parts[0]
        assert work_order_part.quantity == Decimal("1"), (
            f"Expected part quantity 1, got {work_order_part.quantity}"
        )
        # The part ID should match PART_ID_PART_0
        assert work_order_part.parts_rel.id == config.PART_ID_PART_0, (
            f"Part ID mismatch: expected {config.PART_ID_PART_0}, got {work_order_part.parts_rel.id}"
        )

        # Verify reservation was created
        reservations = (
            db.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.reference_type == "work_order",
            )
            .all()
        )
        assert len(reservations) == 1, (
            f"Expected 1 reservation, got {len(reservations)}"
        )
        reservation = reservations[0]
        assert reservation.part_id == config.PART_ID_PART_0, (
            f"Reservation part ID mismatch: got {reservation.part_id}, expected {config.PART_ID_PART_0}"
        )
        assert reservation.quantity_reserved == Decimal("1"), (
            f"Expected quantity 1, got {reservation.quantity_reserved}"
        )
        assert reservation.status == ReservationStatus.FULFILLED.value, (
            "Reservation should be fulfilled"
        )

        # Verify movement was created
        movements = (
            db.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 1, f"Expected 1 movement, got {len(movements)}"
        movement = movements[0]
        assert movement.part_id == config.PART_ID_PART_0, (
            f"Movement part ID mismatch: got {movement.part_id}, expected {config.PART_ID_PART_0}"
        )
        assert movement.quantity == Decimal("1"), (
            f"Expected quantity 1, got {movement.quantity}"
        )
        assert movement.from_warehouse_id == config.WAREHOUSE_ID_MOOSOMIN, (
            "Movement warehouse mismatch"
        )
        assert movement.movement_type == MovementType.ISSUE.value, (
            "Movement type should be ISSUE"
        )

        # Verify warehouse quantity was reduced by 1
        db.session.refresh(warehouse_part_moosomin)
        final_quantity = warehouse_part_moosomin.quantity
        assert final_quantity == Decimal("1"), (
            f"Final quantity should be 1, got {final_quantity}"
        )

        print(
            f"✅ Test passed: Work order {work_order.id} created with proper inventory operations"
        )

        # Clean up - delete the work order
        db.session.delete(work_order)
        db.session.commit()

        # Reset warehouse part quantity for future tests
        warehouse_part_moosomin.quantity = Decimal("2")
        db.session.commit()


def test_new_approved_work_order_with_transfer_operations(
    all_ijack, setup_warehouse_parts
):
    """
    Test that when an approved work order is submitted with parts from multiple warehouses,
    including a transfer between warehouses, the inventory operations are performed correctly.

    This test verifies:
    1. Two parts with the same part ID but different source warehouses
    2. One part is taken directly from WAREHOUSE_ID_MOOSOMIN
    3. One part is transferred from WAREHOUSE_ID_DALLAS to WAREHOUSE_ID_MOOSOMIN
    4. Two reservations are created (one for each source warehouse)
    5. Two movements are created (one issue and one transfer)
    6. Warehouse quantities are updated correctly in both warehouses
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    invoice_summary_value = "Transfer Test\nApproved WO with Multiple Warehouses\nWO n/a\nAFE n/a\nPO n/a\n\n\n"
    signature_base64 = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48L3N2Zz4="

    with ijack_context(flask_app, logged_in_cust=True):
        # For testing, we'll just use the same warehouse twice - WAREHOUSE_ID_MOOSOMIN
        # This avoids issues with missing warehouses in the test database

        # Set up the warehouse part with initial quantity of 2
        warehouse_part_moosomin = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN,
            )
            .first()
        )

        warehouse_part2 = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_DALLAS,
            )
            .first()
        )

        # Verify initial quantities
        assert warehouse_part_moosomin.quantity == Decimal("2"), (
            f"Initial Moosomin quantity should be 2, got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part2.quantity == Decimal("2"), (
            f"Initial Dallas quantity should be 2, got {warehouse_part2.quantity}"
        )

        # Submit work order with two parts: one from Moosomin and one transfer from Dallas to Moosomin
        (
            modal_open,
            title,
            work_order_modal_title_style,
            body,
            work_order_modal_view_all_btn_class_name,
            work_order_modal_create_new_btn_children,
            store_work_order_id_to_clone_data,
            work_order_modal_clone_card_style,
            edit_work_order_id_data,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2023-01-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_CORP,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Test Crew",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Test Location",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Test service with transfer required",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Test work done with parts from multiple warehouses",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary_value,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=200.00,
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=200.00,
            work_order_sales_tax_store_data=10.00,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=210.00,
            work_order_parts_table_ag_rowData=[
                # First part: Direct from Moosomin warehouse
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": config.PART_ID_PART_0,
                    "description": "Test Part from Moosomin",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "warehouse_to_id": None,  # No transfer for this part
                    "quantity": 1,
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                # Second part: Same part from Dallas, but transferred to Moosomin
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": config.PART_ID_PART_0,
                    "description": "Test Part from Dallas transferred to Moosomin",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_DALLAS,
                    "warehouse_to_id": config.WAREHOUSE_ID_MOOSOMIN,  # Transfer destination
                    "quantity": 1,
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                # Empty row
                {
                    "delete": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=None,
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2023-01-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=True,  # Approved
            work_order_is_quote_value=False,  # Not a quote
            work_order_is_tax_exempt_value=False,
        )

        # Verify work order was created successfully
        assert modal_open is True, "Confirmation modal should be open"
        assert title == "Success!"
        assert work_order_modal_title_style == {}, "Title should be styled correctly"
        assert isinstance(edit_work_order_id_data, str), (
            "Edit work order ID should be set"
        )
        work_order_id = int(edit_work_order_id_data)

        # Find the created work order using the ID returned from the form submit
        work_order_id = int(edit_work_order_id_data)
        work_order = db.session.get(WorkOrder, work_order_id)
        assert work_order is not None, "Work order should be created"
        assert work_order.is_quote is False, "Work order should not be a quote"
        assert work_order.invoice_approval_req is True, "Work order should be approved"

        # Verify work order parts were created
        work_order_parts = work_order.work_order_parts_rel
        assert len(work_order_parts) == 2, "Should have two work order parts"

        # Verify reservations were created (should be three with dual reservation system)
        # 1. Direct issue from Moosomin (positive)
        # 2. Transfer source from Dallas (positive)
        # 3. Transfer destination at Moosomin (negative)
        reservations = (
            db.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.reference_type == "work_order",
            )
            .all()
        )
        assert len(reservations) == 3, (
            f"Expected 3 reservations, got {len(reservations)}"
        )

        # Check reservations by warehouse
        moosomin_reservations = [
            r for r in reservations if r.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN
        ]
        dallas_reservations = [
            r for r in reservations if r.warehouse_id == config.WAREHOUSE_ID_DALLAS
        ]
        assert len(moosomin_reservations) == 2, (
            f"Expected 2 Moosomin reservations (1 direct + 1 transfer dest), got {len(moosomin_reservations)}"
        )
        assert len(dallas_reservations) == 1, (
            f"Expected 1 Dallas reservation, got {len(dallas_reservations)}"
        )

        # Check reservation details - separate positive and negative Moosomin reservations
        moosomin_positive = [
            r for r in moosomin_reservations if r.quantity_reserved > 0
        ][0]
        moosomin_negative = [
            r for r in moosomin_reservations if r.quantity_reserved < 0
        ][0]
        dallas_reservation = dallas_reservations[0]

        # Check positive Moosomin reservation (direct issue)
        assert moosomin_positive.part_id == config.PART_ID_PART_0, (
            "Moosomin positive reservation part ID mismatch"
        )
        assert moosomin_positive.quantity_reserved == Decimal("1"), (
            f"Expected quantity 1, got {moosomin_positive.quantity_reserved}"
        )
        assert moosomin_positive.status == ReservationStatus.FULFILLED.value, (
            "Moosomin positive reservation should be fulfilled"
        )

        # Check negative Moosomin reservation (transfer destination)
        assert moosomin_negative.part_id == config.PART_ID_PART_0, (
            "Moosomin negative reservation part ID mismatch"
        )
        assert moosomin_negative.quantity_reserved == Decimal("-1"), (
            f"Expected quantity -1, got {moosomin_negative.quantity_reserved}"
        )
        assert moosomin_negative.status == ReservationStatus.FULFILLED.value, (
            "Moosomin negative reservation should be fulfilled"
        )

        # Check Dallas reservation (transfer source)
        assert dallas_reservation.part_id == config.PART_ID_PART_0, (
            "Dallas reservation part ID mismatch"
        )
        assert dallas_reservation.quantity_reserved == Decimal("1"), (
            f"Expected quantity 1, got {dallas_reservation.quantity_reserved}"
        )
        assert dallas_reservation.status == ReservationStatus.FULFILLED.value, (
            "Dallas reservation should be fulfilled"
        )
        assert dallas_reservation.warehouse_to_id == config.WAREHOUSE_ID_MOOSOMIN, (
            "Dallas reservation should have Moosomin as destination"
        )

        # Verify movements were created (should be two - one direct issue and one transfer)
        movements = (
            db.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 2, f"Expected 2 movements, got {len(movements)}"

        # Check that we have one movement for each operation type
        issue_movements = [
            m for m in movements if m.movement_type == MovementType.ISSUE.value
        ]
        transfer_movements = [
            m for m in movements if m.movement_type == MovementType.TRANSFER.value
        ]

        assert len(issue_movements) == 1, (
            f"Expected 1 issue movement, got {len(issue_movements)}"
        )
        assert len(transfer_movements) == 1, (
            f"Expected 1 transfer movement, got {len(transfer_movements)}"
        )

        # Check movement details
        issue_movement = issue_movements[0]
        transfer_movement = transfer_movements[0]

        assert issue_movement.part_id == config.PART_ID_PART_0, (
            "Issue movement part ID mismatch"
        )
        assert issue_movement.quantity == Decimal("1"), (
            f"Expected quantity 1, got {issue_movement.quantity}"
        )
        assert issue_movement.from_warehouse_id == config.WAREHOUSE_ID_MOOSOMIN, (
            "Issue movement should be from Moosomin"
        )
        assert issue_movement.to_warehouse_id is None, (
            "Issue movement should not have a destination warehouse"
        )

        assert transfer_movement.part_id == config.PART_ID_PART_0, (
            "Transfer movement part ID mismatch"
        )
        assert transfer_movement.quantity == Decimal("1"), (
            f"Expected quantity 1, got {transfer_movement.quantity}"
        )
        assert transfer_movement.from_warehouse_id == config.WAREHOUSE_ID_DALLAS, (
            "Transfer movement should be from Dallas"
        )
        assert transfer_movement.to_warehouse_id == config.WAREHOUSE_ID_MOOSOMIN, (
            "Transfer movement should be to Moosomin"
        )

        # Verify warehouse quantities were updated correctly
        db.session.refresh(warehouse_part_moosomin)
        db.session.refresh(warehouse_part2)

        final_quantity_moosomin = warehouse_part_moosomin.quantity
        final_quantity_dallas = warehouse_part2.quantity

        # Moosomin: 2 - 1 (direct issue) + 1 (transfer in) = 2
        # The direct issue reduces the quantity, but the transfer in from Dallas increases it
        assert final_quantity_moosomin == Decimal("2"), (
            f"Final Moosomin quantity should be 2, got {final_quantity_moosomin}"
        )

        # Dallas: 2 - 1 (transfer out) = 1
        assert final_quantity_dallas == Decimal("1"), (
            f"Final Dallas quantity should be 1, got {final_quantity_dallas}"
        )

        # There should be no reserved quantities left in the warehouse parts
        assert warehouse_part_moosomin.quantity_reserved == Decimal("0"), (
            f"Moosomin reserved quantity should be 0, got {warehouse_part_moosomin.quantity_reserved}"
        )
        assert warehouse_part2.quantity_reserved == Decimal("0"), (
            f"Dallas reserved quantity should be 0, got {warehouse_part2.quantity_reserved}"
        )

        # Log success for debugging
        print("✅ Test completed successfully!")
        print(f"   Work Order ID: {work_order_id}")
        print(f"   Reservations created: {len(reservations)}")
        print(f"   Movements created: {len(movements)}")
        print(f"   Final Moosomin quantity: {final_quantity_moosomin}")
        print(f"   Final Dallas quantity: {final_quantity_dallas}")

        # Clean up - delete the work order
        db.session.delete(work_order)
        db.session.commit()

        # Reset warehouse part quantity for future tests
        warehouse_part_moosomin.quantity = Decimal("2")
        warehouse_part2.quantity = Decimal("2")
        db.session.commit()


def test_new_approved_work_order_with_one_part_consumed_and_one_transfered(
    all_ijack, setup_warehouse_parts
):
    """
    Test that when an approved work order is submitted with one part consumed and one part transferred,
    the inventory operations are performed correctly.

    This test verifies:
    1. One part is consumed from WAREHOUSE_ID_MOOSOMIN
    2. One part is transferred from WAREHOUSE_ID_MOOSOMIN to WAREHOUSE_ID_DALLAS
    3. Two reservations are created (one for each source warehouse)
    4. Two movements are created (one issue and one transfer)
    5. Warehouse quantities and reserved quantities are updated correctly in both warehouses
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    invoice_summary_value = "Consumption and Transfer Test\nApproved WO with Consumption and Transfer\nWO n/a\nAFE n/a\nPO n/a\n\n\n"
    signature_base64 = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48L3N2Zz4="

    with ijack_context(flask_app, logged_in_cust=True):
        # Get warehouse parts from the fixture to verify initial state
        warehouse_part_moosomin = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN,
            )
            .first()
        )
        warehouse_part_dallas = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_DALLAS,
            )
            .first()
        )

        # Verify initial quantities (should be 2 each from the fixture)
        assert warehouse_part_moosomin.quantity == Decimal("2"), (
            f"Initial Moosomin quantity should be 2, got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part_dallas.quantity == Decimal("2"), (
            f"Initial Dallas quantity should be 2, got {warehouse_part_dallas.quantity}"
        )

        # Ensure both warehouses have the initial quantity of 2
        assert warehouse_part_moosomin.quantity_reserved == Decimal("0"), (
            f"Moosomin reserved quantity should be 0, got {warehouse_part_moosomin.quantity_reserved}"
        )
        assert warehouse_part_dallas.quantity_reserved == Decimal("0"), (
            f"Dallas reserved quantity should be 0, got {warehouse_part_dallas.quantity_reserved}"
        )

        # Submit work order with two parts:
        # 1. One part consumed from Moosomin (no transfer destination)
        # 2. One part transferred from Moosomin to Dallas
        (
            modal_open,
            title,
            work_order_modal_title_style,
            body,
            work_order_modal_view_all_btn_class_name,
            work_order_modal_create_new_btn_children,
            store_work_order_id_to_clone_data,
            work_order_modal_clone_card_style,
            edit_work_order_id_data,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2023-01-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_CORP,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Test Crew",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Test Location",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Test service with consumption and transfer",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Test work done with consumption and transfer",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary_value,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=200.00,
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=200.00,
            work_order_sales_tax_store_data=10.00,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=210.00,
            work_order_parts_table_ag_rowData=[
                # First part: Consumed from Moosomin warehouse (no transfer destination)
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": config.PART_ID_PART_0,
                    "description": "Test Part consumed from Moosomin",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "warehouse_to_id": None,  # Consumed, not transferred
                    "quantity": 1,
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                # Second part: Transferred from Moosomin to Dallas
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": config.PART_ID_PART_0,
                    "description": "Test Part transferred from Moosomin to Dallas",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "warehouse_to_id": config.WAREHOUSE_ID_DALLAS,  # Transfer destination
                    "quantity": 1,
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                # Empty row
                {
                    "delete": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=None,
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2023-01-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=True,  # Still approved
            work_order_is_quote_value=False,  # Not a quote
            work_order_is_tax_exempt_value=False,
        )

        # Verify the work order update was successful
        assert modal_open is True, "Update confirmation modal should be open"
        if title != "Success!":
            print(f"Error title: {title}")
            print(f"Error body: {body}")
            # Don't fail yet, let's see what work order was created
            # assert title == "Success!", f"Expected Success, got {title} with body: {body}"

        # Get the work order ID
        work_order_id = int(edit_work_order_id_data)
        work_order = db.session.get(WorkOrder, work_order_id)
        assert work_order is not None, "Work order should be created"
        assert work_order.is_quote is False, "Work order should not be a quote"
        assert work_order.invoice_approval_req is True, "Work order should be approved"

        # Verify work order parts were created
        work_order_parts = work_order.work_order_parts_rel
        assert len(work_order_parts) == 2, (
            f"Should have 2 work order parts, got {len(work_order_parts)}"
        )

        # Debug: Print work order parts details
        for i, part in enumerate(work_order_parts):
            print(
                f"Part {i + 1}: part_id={part.part_id}, warehouse_id={part.warehouse_id}, "
                f"warehouse_to_id={part.warehouse_to_id}, quantity={part.quantity}"
            )

        # Verify reservations were created
        # Should have 3 reservations:
        # 1. Positive reservation for consumed part from Moosomin
        # 2. Positive reservation for transfer source from Moosomin
        # 3. Negative reservation for transfer destination at Dallas
        reservations = (
            db.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.reference_type == "work_order",
            )
            .all()
        )

        # Debug: Print reservation details
        print(f"\nReservations created: {len(reservations)}")
        for i, res in enumerate(reservations):
            print(
                f"Reservation {i + 1}: part_id={res.part_id}, warehouse_id={res.warehouse_id}, "
                f"warehouse_to_id={res.warehouse_to_id}, quantity={res.quantity_reserved}, "
                f"status={res.status}"
            )

        assert len(reservations) == 3, (
            f"Expected 3 reservations, got {len(reservations)}"
        )

        # Check reservations by warehouse
        moosomin_reservations = [
            r for r in reservations if r.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN
        ]
        dallas_reservations = [
            r for r in reservations if r.warehouse_id == config.WAREHOUSE_ID_DALLAS
        ]

        assert len(moosomin_reservations) == 2, (
            f"Expected 2 Moosomin reservations, got {len(moosomin_reservations)}"
        )
        assert len(dallas_reservations) == 1, (
            f"Expected 1 Dallas reservation, got {len(dallas_reservations)}"
        )

        # Check Dallas reservation (should be negative for transfer destination)
        dallas_reservation = dallas_reservations[0]
        assert dallas_reservation.quantity_reserved == Decimal("-1"), (
            f"Dallas reservation should be -1, got {dallas_reservation.quantity_reserved}"
        )
        assert dallas_reservation.status == ReservationStatus.FULFILLED.value, (
            "Dallas reservation should be fulfilled"
        )

        # Verify movements were created (should be 2: one issue and one transfer)
        movements = (
            db.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 2, f"Expected 2 movements, got {len(movements)}"

        # Check movement types
        issue_movements = [
            m for m in movements if m.movement_type == MovementType.ISSUE.value
        ]
        transfer_movements = [
            m for m in movements if m.movement_type == MovementType.TRANSFER.value
        ]

        assert len(issue_movements) == 1, (
            f"Expected 1 issue movement, got {len(issue_movements)}"
        )
        assert len(transfer_movements) == 1, (
            f"Expected 1 transfer movement, got {len(transfer_movements)}"
        )

        # Verify warehouse quantities were updated correctly
        db.session.refresh(warehouse_part_moosomin)
        db.session.refresh(warehouse_part_dallas)

        # Moosomin: Started with 2, consumed 1, transferred out 1 = 0
        assert warehouse_part_moosomin.quantity == Decimal("0"), (
            f"Moosomin should have 0 after consumption and transfer, got {warehouse_part_moosomin.quantity}"
        )

        # Dallas: Started with 2, received 1 from transfer = 3
        assert warehouse_part_dallas.quantity == Decimal("3"), (
            f"Dallas should have 3 after receiving transfer, got {warehouse_part_dallas.quantity}"
        )

        # Reserved quantities should be 0 (all fulfilled)
        assert warehouse_part_moosomin.quantity_reserved == Decimal("0"), (
            f"Moosomin reserved should be 0, got {warehouse_part_moosomin.quantity_reserved}"
        )
        assert warehouse_part_dallas.quantity_reserved == Decimal("0"), (
            f"Dallas reserved should be 0, got {warehouse_part_dallas.quantity_reserved}"
        )

        print(
            "✅ Test passed: Work order created with one consumed and one transferred part"
        )

        # Another test where we now change the work order from approved to a quote,
        # and verify that the reservations and movements are properly deleted,
        # and the warehouse quantities are restored to their original state,
        # and the work order parts are still there but not reserved or moved.

        (
            modal_open_update,
            title_update,
            work_order_modal_title_style_update,
            body_update,
            work_order_modal_view_all_btn_class_name_update,
            work_order_modal_create_new_btn_children_update,
            store_work_order_id_to_clone_data_update,
            work_order_modal_clone_card_style_update,
            edit_work_order_id_data_update,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2023-01-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_CORP,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Test Crew",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Test Location",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Test service with transfer required",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Test work done with parts from multiple warehouses",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary_value,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=100.00,  # Reduced since we only have 1 part now
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=100.00,
            work_order_sales_tax_store_data=5.00,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=105.00,
            # Keep the same part as before
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": config.PART_ID_PART_0,
                    "description": "Test Part from Moosomin",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "warehouse_to_id": None,  # No transfer for this part
                    "quantity": 1,
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                # Second part: Same part from Dallas, but transferred to Moosomin
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": config.PART_ID_PART_0,
                    "description": "Test Part from Dallas transferred to Moosomin",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_DALLAS,
                    "warehouse_to_id": config.WAREHOUSE_ID_MOOSOMIN,  # Transfer destination
                    "quantity": 1,
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
            ],
            edit_work_order_id_data=work_order_id,  # Edit the existing work order
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2023-01-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=False,  # Change to quote
            work_order_is_quote_value=True,  # Now a quote
            work_order_is_tax_exempt_value=False,
        )
        # Verify the work order update was successful
        assert modal_open_update is True, "Update confirmation modal should be open"
        assert title_update == "Success!"
        # Refresh the work order
        updated_work_order = db.session.get(WorkOrder, work_order_id)
        assert updated_work_order is not None, "Work order should still exist"
        assert updated_work_order.is_quote is True, "Work order should now be a quote"
        assert updated_work_order.invoice_approval_req is False, (
            "Work order should not require invoice approval"
        )
        assert updated_work_order.work_order_parts_rel is not None, (
            "Work order parts should still exist"
        )
        assert len(updated_work_order.work_order_parts_rel) == 2, (
            "Work order should still have 2 parts"
        )
        # Verify that the reservations and movements were deleted
        reservations = (
            db.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.reference_type == "work_order",
                InventoryReservation.status != ReservationStatus.CANCELLED,
            )
            .all()
        )
        assert len(reservations) == 0, (
            f"After changing to quote, should have 0 active reservations, got {len(reservations)}"
        )
        movements = (
            db.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 0, (
            f"After changing to quote, should have 0 movements, got {len(movements)}"
        )
        # Verify warehouse quantities were restored to their original state
        db.session.refresh(warehouse_part_moosomin)
        db.session.refresh(warehouse_part_dallas)
        assert warehouse_part_moosomin.quantity == Decimal("2"), (
            f"After changing to quote, Moosomin quantity should be 2, got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part_dallas.quantity == Decimal("2"), (
            f"After changing to quote, Dallas quantity should be 2, got {warehouse_part_dallas.quantity}"
        )
        assert warehouse_part_moosomin.quantity_reserved == Decimal("0"), (
            f"After changing to quote, Moosomin reserved quantity should be 0, got {warehouse_part_moosomin.quantity_reserved}"
        )
        assert warehouse_part_dallas.quantity_reserved == Decimal("0"), (
            f"After changing to quote, Dallas reserved quantity should be 0, got {warehouse_part_dallas.quantity_reserved}"
        )
        print(
            "✅ Work order changed to quote successfully: Reservations and movements deleted, quantities restored"
        )

        # Clean up - delete the work order
        db.session.delete(updated_work_order)
        db.session.commit()

        # Reset warehouse part quantities for future tests
        warehouse_part_moosomin.quantity = Decimal("2")
        warehouse_part_dallas.quantity = Decimal("2")
        db.session.commit()


def create_or_update_work_order_with_transfer(
    work_order_id=None,
    invoice_summary="Test Transfer\\nWO n/a\\nAFE n/a\\nPO n/a\\n\\n\\n",
):
    """
    Helper function to create or update a work order with a transfer.
    DRY helper for testing multiple saves of the same work order.
    """
    signature_base64 = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48L3N2Zz4="

    return work_order_form_submit(
        work_order_submit_store_data=True,
        work_order_date_service_value="2025-12-31",
        work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
        work_order_currency_id_value=config.CURRENCY_ID_CAD,
        work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_INC,
        work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
        work_order_users_value=[config.USER_ID_SEAN],
        work_order_users_sales_value=[config.USER_ID_SEAN],
        work_order_requested_by_dropdown_value=str(config.USER_ID_SEAN),
        work_order_service_crew_value=None,
        work_order_company_rep_dropdown_value=str(config.USER_ID_SEAN),
        work_order_location_value="Test Location",
        work_order_model_types_value=[],
        work_order_service_required_value=None,
        work_order_is_warranty_value=False,
        work_order_work_done_value=None,
        work_order_customer_po_value=None,
        work_order_cust_work_order_value=None,
        work_order_afe_value=None,
        work_order_invoice_summary_value=invoice_summary,
        work_order_structures_value=[config.STRUCTURE_TEINE_200446],
        work_order_structure_slave_value=None,
        work_order_power_units_value=[None],
        work_order_province_id_value=None,
        work_order_zip_code_id_value=None,
        work_order_sales_tax_rate_value=0,
        work_order_gst_rate_value=0,
        work_order_pst_rate_value=0,
        work_order_county_id_value=None,
        work_order_city_id_value=None,
        work_order_subtotal_store_data=0,
        work_order_discount_pct_value=0,
        work_order_subtotal_after_discount_store_data=0,
        work_order_sales_tax_store_data=0,
        work_order_gst_amount_store_data=0,
        work_order_pst_amount_store_data=0,
        work_order_total_store_data=0,
        work_order_parts_table_ag_rowData=[
            # Single part transferred from Moosomin to Dallas
            {
                "delete": "",
                "part_id": str(config.PART_ID_PART_0),
                "part_num": "0",  # Add the actual part number for validation
                "description": "Test Part transferred from Moosomin to Dallas",
                "structure_id": str(config.STRUCTURE_ID_GATEWAYS),
                "warehouse_id": str(config.WAREHOUSE_ID_MOOSOMIN),
                "warehouse_to_id": str(
                    config.WAREHOUSE_ID_DALLAS
                ),  # Transfer destination
                "quantity": 2,
                "price": 0,
                "cost_before_tax": 0,
                "field_tech_id": str(config.USER_ID_SEAN),
            },
        ],
        edit_work_order_id_data=str(work_order_id) if work_order_id else None,
        work_order_file_upload_data_table_data=[],
        signature_store_data=signature_base64,
        work_order_signature_name_value="Test Signature",
        work_order_signature_date_date="2023-01-01",
        work_order_country_id_value=config.COUNTRY_ID_CANADA,
        work_order_approved_value=True,  # Approved
        work_order_is_quote_value=False,  # Not a quote
        work_order_is_tax_exempt_value=True,  # Tax exempt for testing purposes
    )


def test_approved_work_order_with_transfer_saved_multiple_times(
    all_ijack, setup_warehouse_parts
):
    """
    Test that an approved work order with a transfer can be saved multiple times
    without errors, reproducing the production issue where the third save fails.

    This test verifies:
    1. First save creates transfer correctly
    2. Second save is idempotent
    3. Third save is idempotent (reproduces production error)
    4. Fourth save is idempotent (extra verification)
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    # Unique invoice summary for this test
    invoice_summary_value = "Multiple Save Transfer Test\\nApproved WO with Transfer\\nWO n/a\\nAFE n/a\\nPO n/a\\n\\n\\n"

    with ijack_context(flask_app, logged_in_cust=True):
        # Get warehouse parts from the fixture
        warehouse_part_moosomin = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN,
            )
            .first()
        )
        warehouse_part_dallas = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_DALLAS,
            )
            .first()
        )

        # Verify initial quantities
        initial_moosomin_qty = warehouse_part_moosomin.quantity
        initial_dallas_qty = warehouse_part_dallas.quantity
        assert initial_moosomin_qty == Decimal("2"), (
            f"Initial Moosomin quantity should be 2, got {initial_moosomin_qty}"
        )
        assert initial_dallas_qty == Decimal("2"), (
            f"Initial Dallas quantity should be 2, got {initial_dallas_qty}"
        )

        # First save - create the work order
        (
            modal_open,
            title,
            _,
            body,
            _,
            _,
            _,
            _,
            edit_work_order_id_data,
        ) = create_or_update_work_order_with_transfer(
            invoice_summary=invoice_summary_value
        )

        assert modal_open is True, "First save: Confirmation modal should be open"
        assert title == "Success!", f"First save failed: {title}"
        work_order_id = int(edit_work_order_id_data)

        # Verify first save results
        work_order = db.session.get(WorkOrder, work_order_id)
        assert work_order is not None, "Work order should be created"

        # Check inventory state after first save
        db.session.refresh(warehouse_part_moosomin)
        db.session.refresh(warehouse_part_dallas)
        expected_moosomin_qty = initial_moosomin_qty - Decimal("2")  # Transferred out
        expected_dallas_qty = initial_dallas_qty + Decimal("2")  # Transferred in

        assert warehouse_part_moosomin.quantity == expected_moosomin_qty, (
            f"After first save, Moosomin should have {expected_moosomin_qty}, got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part_dallas.quantity == expected_dallas_qty, (
            f"After first save, Dallas should have {expected_dallas_qty}, got {warehouse_part_dallas.quantity}"
        )

        # Store quantities after first save for comparison
        qty_after_first_save_moosomin = warehouse_part_moosomin.quantity
        qty_after_first_save_dallas = warehouse_part_dallas.quantity

        # IMPORTANT: Check reserved quantities - these should be 0 after fulfillment
        assert warehouse_part_moosomin.quantity_reserved == Decimal("0"), (
            f"After first save, Moosomin reserved should be 0, got {warehouse_part_moosomin.quantity_reserved}"
        )
        assert warehouse_part_dallas.quantity_reserved == Decimal("0"), (
            f"After first save, Dallas reserved should be 0, got {warehouse_part_dallas.quantity_reserved}"
        )

        # Debug: Check Dallas warehouse state directly from DB
        dallas_check = db.session.execute(
            text(
                f"SELECT quantity, quantity_reserved FROM warehouses_parts_rel WHERE warehouse_id = {config.WAREHOUSE_ID_DALLAS} AND part_id = {config.PART_ID_PART_0}"
            )
        ).fetchone()
        print(
            f"DEBUG: Dallas warehouse after first save - quantity: {dallas_check[0]}, reserved: {dallas_check[1]}"
        )

        # Test multiple saves
        for save_num in range(2, 5):  # Test saves 2, 3, and 4
            print(f"\n🔄 Testing save #{save_num}...")

            # Save the work order again with no changes
            (
                modal_open,
                title,
                _,
                body,
                _,
                _,
                _,
                _,
                edit_work_order_id_data_resave,
            ) = create_or_update_work_order_with_transfer(
                work_order_id=work_order_id, invoice_summary=invoice_summary_value
            )

            # Check for errors
            if title != "Success!":
                # If there's an error, print details for debugging
                print(f"❌ Save #{save_num} failed!")
                print(f"   Title: {title}")
                print(f"   Body: {body}")
                if save_num == 3:
                    pytest.fail(
                        f"Save #{save_num} failed (reproducing production error): {title}\n"
                        f"Body: {body}\n"
                        f"This is the third save that fails in production with 'unfulfilled reservations' error"
                    )
                else:
                    pytest.fail(f"Save #{save_num} failed: {title}\nBody: {body}")

            assert modal_open is True, (
                f"Save #{save_num}: Confirmation modal should be open"
            )
            assert title == "Success!", f"Save #{save_num} should succeed"
            assert int(edit_work_order_id_data_resave) == work_order_id, (
                f"Save #{save_num}: Work order ID should remain {work_order_id}"
            )

            # Verify quantities haven't changed
            db.session.refresh(warehouse_part_moosomin)
            db.session.refresh(warehouse_part_dallas)

            assert warehouse_part_moosomin.quantity == qty_after_first_save_moosomin, (
                f"Save #{save_num}: Moosomin quantity should remain {qty_after_first_save_moosomin}, "
                f"got {warehouse_part_moosomin.quantity}"
            )
            assert warehouse_part_dallas.quantity == qty_after_first_save_dallas, (
                f"Save #{save_num}: Dallas quantity should remain {qty_after_first_save_dallas}, "
                f"got {warehouse_part_dallas.quantity}"
            )

            # CRITICAL: Check reserved quantities remain 0 (the production bug)
            assert warehouse_part_moosomin.quantity_reserved == Decimal("0"), (
                f"Save #{save_num}: Moosomin reserved should remain 0, got {warehouse_part_moosomin.quantity_reserved}"
            )
            assert warehouse_part_dallas.quantity_reserved == Decimal("0"), (
                f"Save #{save_num}: Dallas reserved should remain 0 (not increasing!), got {warehouse_part_dallas.quantity_reserved}"
            )

            # Verify reservations and movements haven't changed
            reservations = (
                db.session.query(InventoryReservation)
                .filter(
                    InventoryReservation.reference_id == work_order_id,
                    InventoryReservation.reference_type == "work_order",
                    InventoryReservation.status != ReservationStatus.CANCELLED,
                )
                .all()
            )

            movements = (
                db.session.query(InventoryMovement)
                .filter(
                    InventoryMovement.reference_id == work_order_id,
                    InventoryMovement.reference_type == "work_order",
                )
                .all()
            )

            # Should have 2 reservations (source + destination) and 1 movement
            assert len(reservations) == 2, (
                f"Save #{save_num}: Should have 2 active reservations, got {len(reservations)}"
            )
            assert len(movements) == 1, (
                f"Save #{save_num}: Should have 1 movement, got {len(movements)}"
            )

            # All reservations should remain fulfilled
            for res in reservations:
                assert res.status == ReservationStatus.FULFILLED.value, (
                    f"Save #{save_num}: Reservation {res.id} should be fulfilled, but status is {res.status}"
                )

            print(f"✅ Save #{save_num} successful - inventory unchanged")

        print("\n✅ All saves completed successfully - work order is idempotent!")

        # Clean up
        db.session.delete(work_order)
        db.session.commit()


def test_negative_stock_policy(all_ijack, setup_warehouse_parts):
    """Test warehouse negative stock policy enforcement during work order approval"""
    flask_app, test_client, dash_app, db = all_ijack

    with flask_app.app_context():
        # Create inventory manager
        inventory_manager = InventoryManager(db.session)

        moosomin_warehouse = db.session.get(Warehouse, config.WAREHOUSE_ID_MOOSOMIN)
        assert moosomin_warehouse is not None, "Moosomin warehouse should exist"

        # First Pass: Allow negative stock
        print("\n🔬 PASS 1: Testing with allows_negative_stock = True")

        # Set warehouse to allow negative stock
        moosomin_warehouse.allows_negative_stock = True
        db.session.commit()

        # Set warehouse part to zero quantity
        warehouse_part = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN,
            )
            .first()
        )

        if not warehouse_part:
            warehouse_part = WarehousePart(
                part_id=config.PART_ID_PART_0,
                warehouse_id=config.WAREHOUSE_ID_MOOSOMIN,
                quantity=Decimal("0"),
                quantity_reserved=Decimal("0"),
                average_cost=Decimal("10.00"),
                last_cost=Decimal("10.00"),
            )
            db.session.add(warehouse_part)
        else:
            warehouse_part.quantity = Decimal("0")
            warehouse_part.quantity_reserved = Decimal("0")

        db.session.commit()

        # Create an approved work order that tries to consume 1 unit
        work_order = WorkOrder(
            is_quote=False,
            invoice_approval_req=True,  # Approved
            customer_id=config.CUSTOMER_ID_IJACK_INC,
            creator_id=config.USER_ID_RICHIE,
            creator_company_id=config.CUSTOMER_ID_IJACK_INC,
            service_type_id=config.SERVICE_TYPE_ID_REPAIR,
            country_id=config.COUNTRY_ID_CANADA,
            subtotal=Decimal("100"),
            sales_tax_rate=Decimal("0.05"),
            sales_tax=Decimal("5"),
            total=Decimal("105"),
            invoice_summary=generate_unique_invoice_summary(
                "test_negative_stock_policy"
            ),
        )
        db.session.add(work_order)

        # Add a part that consumes 1 unit from Moosomin
        work_order_part = WorkOrderPart(
            work_order_id=work_order.id,
            part_id=config.PART_ID_PART_0,
            warehouse_id=config.WAREHOUSE_ID_MOOSOMIN,
            warehouse_to_id=None,  # No transfer, just consumption
            quantity=Decimal("1"),
            description="Consume 1 unit with zero stock",
            price=Decimal("100"),
            sales_tax_rate=Decimal("0.05"),
        )
        work_order.work_order_parts_rel = [work_order_part]
        db.session.commit()

        # No tracking needed since we'll manually clean up

        # Handle state change - should succeed with negative stock allowed
        result1 = inventory_manager.handle_state_change(
            work_order=work_order,
            old_state=None,
            new_state=WorkOrderState.WORK_ORDER_APPROVED,
            user_id=config.USER_ID_RICHIE,
        )

        # Should succeed with a warning
        assert result1.success is True, (
            f"Should succeed with negative stock allowed: {result1.errors}"
        )
        print(f"Result warnings: {result1.warnings}")
        print(f"Result message: {result1.message}")

        # Check for warnings (may come from reservation or movement creation)
        # The warning might be in the result or just logged
        if result1.warnings:
            assert any("negative stock" in str(w).lower() for w in result1.warnings), (
                f"Warning should mention negative stock, got: {result1.warnings}"
            )

        # Verify negative quantity
        db.session.refresh(warehouse_part)
        assert warehouse_part.quantity == Decimal("-1"), (
            f"Should have negative quantity, got {warehouse_part.quantity}"
        )
        print(
            f"✅ Pass 1 successful: Created negative inventory (quantity={warehouse_part.quantity})"
        )

        # Clean up before Pass 2
        db.session.delete(work_order)
        db.session.commit()

        # Second Pass: Disallow negative stock
        print("\n🔬 PASS 2: Testing with allows_negative_stock = False")

        # Set warehouse to NOT allow negative stock
        moosomin_warehouse.allows_negative_stock = False
        db.session.commit()

        # Reset warehouse part to zero quantity
        warehouse_part.quantity = Decimal("0")
        warehouse_part.quantity_reserved = Decimal("0")
        db.session.commit()

        # Create another approved work order that tries to consume 1 unit
        work_order2 = WorkOrder(
            is_quote=False,
            invoice_approval_req=True,  # Approved
            customer_id=config.CUSTOMER_ID_IJACK_INC,
            creator_id=config.USER_ID_RICHIE,
            creator_company_id=config.CUSTOMER_ID_IJACK_INC,
            service_type_id=config.SERVICE_TYPE_ID_REPAIR,
            country_id=config.COUNTRY_ID_CANADA,
            subtotal=Decimal("100"),
            sales_tax_rate=Decimal("0.05"),
            sales_tax=Decimal("5"),
            total=Decimal("105"),
            invoice_summary=generate_unique_invoice_summary(
                "test_negative_stock_policy"
            ),
        )
        db.session.add(work_order2)

        # Add a part that consumes 1 unit from Moosomin
        work_order_part2 = WorkOrderPart(
            work_order_id=work_order2.id,
            part_id=config.PART_ID_PART_0,
            warehouse_id=config.WAREHOUSE_ID_MOOSOMIN,
            warehouse_to_id=None,  # No transfer, just consumption
            quantity=Decimal("1"),
            description="Try to consume 1 unit with zero stock",
            price=Decimal("100"),
            sales_tax_rate=Decimal("0.05"),
        )
        work_order2.work_order_parts_rel = [work_order_part2]
        db.session.commit()

        # Handle state change - should fail with negative stock NOT allowed
        result2 = inventory_manager.handle_state_change(
            work_order=work_order2,
            old_state=None,
            new_state=WorkOrderState.WORK_ORDER_APPROVED,
            user_id=config.USER_ID_RICHIE,
        )

        # Should fail
        assert result2.success is False, "Should fail when negative stock not allowed"
        assert len(result2.errors) > 0, "Should have error about insufficient inventory"
        assert "does not allow negative stock" in str(result2.errors[0]).lower(), (
            f"Error should mention negative stock policy, got: {result2.errors[0]}"
        )

        # Verify quantity is still zero (not negative)
        db.session.refresh(warehouse_part)
        assert warehouse_part.quantity == Decimal("0"), (
            f"Should still have zero quantity, got {warehouse_part.quantity}"
        )
        print(
            f"✅ Pass 2 successful: Blocked negative inventory (quantity={warehouse_part.quantity})"
        )

        print("\n✅ Negative stock policy test completed successfully!")


def test_approved_work_order_quantity_reduction(all_ijack):
    """Test reducing quantity on approved work order after initial save"""
    flask_app, test_client, dash_app, db_instance = all_ijack

    test_part_id = 99999  # Use a unique part ID

    # Use DRY helper to ensure part exists
    ensure_parts_exist(db_instance.session, test_part_id)

    with flask_app.app_context():
        # Use existing helper to set up warehouse part (handles warehouse FK too)
        warehouse_part = setup_warehouse_part_for_test(
            db_instance.session, config.WAREHOUSE_ID_MOOSOMIN, test_part_id
        )

        # setup_warehouse_part_for_test already sets quantity=2 and quantity_reserved=0

        # Create inventory manager
        inventory_manager = InventoryManager(db_instance.session)

        # Create an approved work order that consumes 2 units
        work_order = WorkOrder(
            id=9999,  # Use unique ID
            is_quote=False,
            invoice_approval_req=True,  # Approved
            customer_id=config.CUSTOMER_ID_IJACK_INC,
            creator_id=config.USER_ID_RICHIE,
            creator_company_id=config.CUSTOMER_ID_IJACK_INC,
            service_type_id=config.SERVICE_TYPE_ID_REPAIR,
            country_id=config.COUNTRY_ID_CANADA,
            subtotal=Decimal("200"),
            sales_tax_rate=Decimal("0.05"),
            sales_tax=Decimal("10"),
            total=Decimal("210"),
        )
        db_instance.session.add(work_order)

        # Add a part that consumes 2 units from Moosomin
        work_order_part = WorkOrderPart(
            work_order_id=9999,
            part_id=test_part_id,
            warehouse_id=config.WAREHOUSE_ID_MOOSOMIN,
            warehouse_to_id=None,  # No transfer, just consumption
            quantity=Decimal("2"),
            description="Consume part",
            price=Decimal("100"),
            sales_tax_rate=Decimal("0.05"),
        )
        work_order.work_order_parts_rel = [work_order_part]
        db_instance.session.commit()

        # First save - should consume 2 units
        result1 = inventory_manager.handle_state_change(
            work_order=work_order,
            old_state=None,
            new_state=WorkOrderState.WORK_ORDER_APPROVED,
            user_id=1,
        )
        assert result1.success, f"First save failed: {result1.errors}"

        # Verify state after first save
        db_instance.session.refresh(warehouse_part)

        assert warehouse_part.quantity == Decimal("0"), (
            f"After consuming 2, quantity should be 0, got {warehouse_part.quantity}"
        )
        assert warehouse_part.quantity_reserved == Decimal("0"), (
            f"Reserved should be 0 after fulfillment, got {warehouse_part.quantity_reserved}"
        )

        # Check movement
        movements = (
            db_instance.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order.id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 1
        assert movements[0].quantity == Decimal("2")
        assert movements[0].movement_type == MovementType.ISSUE

        # Check reservations
        reservations = (
            db_instance.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order.id,
                InventoryReservation.reference_type == "work_order",
                InventoryReservation.status != ReservationStatus.CANCELLED,
            )
            .all()
        )
        assert len(reservations) == 1
        assert reservations[0].quantity_reserved == Decimal("2")
        assert reservations[0].quantity_fulfilled == Decimal("2")
        assert reservations[0].status == ReservationStatus.FULFILLED

        # Now reduce quantity from 2 to 1
        work_order_part.quantity = Decimal("1")
        db_instance.session.commit()

        # Save again with reduced quantity
        result2 = inventory_manager.handle_state_change(
            work_order=work_order,
            old_state=WorkOrderState.WORK_ORDER_APPROVED,
            new_state=WorkOrderState.WORK_ORDER_APPROVED,
            user_id=1,
        )
        assert result2.success, f"Second save failed: {result2.errors}"

        # Verify state after quantity reduction
        db_instance.session.expire_all()
        warehouse_part = (
            db_instance.session.query(WarehousePart)
            .filter(
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN,
                WarehousePart.part_id == test_part_id,
            )
            .first()
        )

        # Should have 1 unit back (2 consumed originally, now only 1 consumed)
        assert warehouse_part.quantity == Decimal("1"), (
            f"After reducing consumption to 1, quantity should be 1, got {warehouse_part.quantity}"
        )
        assert warehouse_part.quantity_reserved == Decimal("0"), (
            f"Reserved should remain 0, got {warehouse_part.quantity_reserved}"
        )

        # Check movement is updated
        movements = (
            db_instance.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order.id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 1
        assert movements[0].quantity == Decimal("1"), (
            f"Movement quantity should be updated to 1, got {movements[0].quantity}"
        )

        # Check reservation is updated
        reservations = (
            db_instance.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order.id,
                InventoryReservation.reference_type == "work_order",
                InventoryReservation.status != ReservationStatus.CANCELLED,
            )
            .all()
        )
        assert len(reservations) == 1
        assert reservations[0].quantity_reserved == Decimal("1")
        assert reservations[0].quantity_fulfilled == Decimal("1")
        assert reservations[0].status == ReservationStatus.FULFILLED


def test_approved_work_order_with_transfer_saved_again_no_changes(
    all_ijack, setup_warehouse_parts
):
    """
    Test that when an approved work order with a transfer is saved again with no changes,
    there are no errors about unfulfilled reservations.

    This test reproduces the production error:
    "Inventory operations failed for work order 22390: ['Approved work order has 1 unfulfilled reservations']"
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    # Unique invoice summary for this test
    invoice_summary_value = "Transfer Re-save Test\\nApproved WO with Transfer\\nWO n/a\\nAFE n/a\\nPO n/a\\n\\n\\n"
    signature_base64 = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48L3N2Zz4="

    with ijack_context(flask_app, logged_in_cust=True):
        # Get warehouse parts from the fixture
        warehouse_part_moosomin = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_MOOSOMIN,
            )
            .first()
        )
        warehouse_part_dallas = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == config.PART_ID_PART_0,
                WarehousePart.warehouse_id == config.WAREHOUSE_ID_DALLAS,
            )
            .first()
        )

        # Verify initial quantities
        assert warehouse_part_moosomin.quantity == Decimal("2"), (
            f"Initial Moosomin quantity should be 2, got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part_dallas.quantity == Decimal("2"), (
            f"Initial Dallas quantity should be 2, got {warehouse_part_dallas.quantity}"
        )

        # Create an approved work order with a single part transferred from Moosomin to Dallas
        (
            modal_open,
            title,
            work_order_modal_title_style,
            body,
            work_order_modal_view_all_btn_class_name,
            work_order_modal_create_new_btn_children,
            store_work_order_id_to_clone_data,
            work_order_modal_clone_card_style,
            edit_work_order_id_data,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2023-01-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_CORP,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Test Crew",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Test Location",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Test service with transfer",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Test work done with transfer",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary_value,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=100.00,
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=100.00,
            work_order_sales_tax_store_data=5.00,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=105.00,
            work_order_parts_table_ag_rowData=[
                # Single part transferred from Moosomin to Dallas
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": config.PART_ID_PART_0,
                    "description": "Test Part transferred from Moosomin to Dallas",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "warehouse_to_id": config.WAREHOUSE_ID_DALLAS,  # Transfer destination
                    "quantity": 1,
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                # Empty row
                {
                    "delete": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=None,
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2023-01-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=True,  # Approved
            work_order_is_quote_value=False,  # Not a quote
            work_order_is_tax_exempt_value=False,
        )

        # Verify work order was created successfully
        assert modal_open is True, "Confirmation modal should be open"
        assert title == "Success!"
        assert isinstance(edit_work_order_id_data, str), (
            "Edit work order ID should be set"
        )
        work_order_id = int(edit_work_order_id_data)

        # Get the created work order
        work_order = db.session.get(WorkOrder, work_order_id)
        assert work_order is not None, "Work order should be created"
        assert work_order.is_quote is False, "Work order should not be a quote"
        assert work_order.invoice_approval_req is True, "Work order should be approved"

        # Verify reservations were created (should be 2: source positive and destination negative)
        reservations = (
            db.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.reference_type == "work_order",
            )
            .all()
        )
        assert len(reservations) == 2, (
            f"Expected 2 reservations, got {len(reservations)}"
        )

        # Verify both reservations are fulfilled
        for reservation in reservations:
            assert reservation.status == ReservationStatus.FULFILLED.value, (
                f"Reservation {reservation.id} should be fulfilled, but status is {reservation.status}"
            )

        # Verify movement was created
        movements = (
            db.session.query(InventoryMovement)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .all()
        )
        assert len(movements) == 1, f"Expected 1 movement, got {len(movements)}"
        movement = movements[0]
        assert movement.movement_type == MovementType.TRANSFER.value, (
            "Movement should be TRANSFER"
        )
        assert movement.from_warehouse_id == config.WAREHOUSE_ID_MOOSOMIN
        assert movement.to_warehouse_id == config.WAREHOUSE_ID_DALLAS
        assert movement.quantity == Decimal("1")

        # Verify warehouse quantities were updated
        db.session.refresh(warehouse_part_moosomin)
        db.session.refresh(warehouse_part_dallas)
        assert warehouse_part_moosomin.quantity == Decimal("1"), (
            f"Moosomin should have 1 after transfer out, got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part_dallas.quantity == Decimal("3"), (
            f"Dallas should have 3 after transfer in, got {warehouse_part_dallas.quantity}"
        )

        print(
            f"✅ Initial work order {work_order_id} created successfully with transfer"
        )
        print(f"   Reservations: {len(reservations)} (all fulfilled)")
        print(f"   Movements: {len(movements)}")
        print(f"   Moosomin quantity: {warehouse_part_moosomin.quantity}")
        print(f"   Dallas quantity: {warehouse_part_dallas.quantity}")

        # Now save the same work order again with NO changes
        # This simulates the production issue where re-saving causes unfulfilled reservation errors
        (
            modal_open_resave,
            title_resave,
            work_order_modal_title_style_resave,
            body_resave,
            work_order_modal_view_all_btn_class_name_resave,
            work_order_modal_create_new_btn_children_resave,
            store_work_order_id_to_clone_data_resave,
            work_order_modal_clone_card_style_resave,
            edit_work_order_id_data_resave,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_date_service_value="2023-01-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_CORP,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_INC,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Test Crew",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Test Location",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Test service with transfer",
            work_order_is_warranty_value=False,
            work_order_work_done_value="Test work done with transfer",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary_value,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=100.00,
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=100.00,
            work_order_sales_tax_store_data=5.00,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=105.00,
            work_order_parts_table_ag_rowData=[
                # Same part as before - no changes
                {
                    "delete": "",
                    "part_id": config.PART_ID_PART_0,
                    "part_num": config.PART_ID_PART_0,
                    "description": "Test Part transferred from Moosomin to Dallas",
                    "structure_id": config.STRUCTURE_ID_GATEWAYS,
                    "warehouse_id": config.WAREHOUSE_ID_MOOSOMIN,
                    "warehouse_to_id": config.WAREHOUSE_ID_DALLAS,  # Transfer destination
                    "quantity": 1,
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": config.USER_ID_SEAN,
                },
                # Empty row
                {
                    "delete": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=work_order_id,  # Edit the existing work order
            work_order_file_upload_data_table_data=[],
            signature_store_data=signature_base64,
            work_order_signature_name_value="Test Signature",
            work_order_signature_date_date="2023-01-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_approved_value=True,  # Still approved
            work_order_is_quote_value=False,  # Still not a quote
            work_order_is_tax_exempt_value=False,
        )

        # The re-save should succeed without errors
        assert modal_open_resave is True, "Confirmation modal should be open"
        assert title_resave == "Success!", f"Expected Success, got: {title_resave}"

        # If there's an error message in the body, it will contain the unfulfilled reservation error
        if isinstance(body_resave, str) and "unfulfilled reservations" in body_resave:
            pytest.fail(f"Re-saving work order failed with: {body_resave}")

        # Verify reservations are still fulfilled after re-save
        reservations_after = (
            db.session.query(InventoryReservation)
            .filter(
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.reference_type == "work_order",
            )
            .all()
        )

        for reservation in reservations_after:
            assert reservation.status == ReservationStatus.FULFILLED.value, (
                f"After re-save, reservation {reservation.id} should still be fulfilled, "
                f"but status is {reservation.status}"
            )

        # Verify warehouse quantities haven't changed
        db.session.refresh(warehouse_part_moosomin)
        db.session.refresh(warehouse_part_dallas)
        assert warehouse_part_moosomin.quantity == Decimal("1"), (
            f"After re-save, Moosomin should still have 1, got {warehouse_part_moosomin.quantity}"
        )
        assert warehouse_part_dallas.quantity == Decimal("3"), (
            f"After re-save, Dallas should still have 3, got {warehouse_part_dallas.quantity}"
        )

        print(
            "✅ Work order re-saved successfully without unfulfilled reservation errors"
        )

        # Clean up - delete the work order
        db.session.delete(work_order)
        db.session.commit()

        # Reset warehouse part quantities for future tests
        warehouse_part_moosomin.quantity = Decimal("2")
        warehouse_part_dallas.quantity = Decimal("2")
        db.session.commit()


def test_reservation_quantity_even_number_bug(all_ijack):
    """
    Test that reproduces the bug where updating part quantity to even numbers (2, 4)
    causes reserved quantity to go to 0, while odd numbers (1, 3) work correctly.

    This test mimics the scenario described:
    1. Reserve part 0 with patched id for quantity 1 - works correctly
    2. Increase quantity to 2 - reserved quantity should be 2 (bug: goes to 0)
    3. Increase quantity to 3 - works correctly
    4. Increase quantity to 4 - reserved quantity should be 4 (bug: goes to 0)
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Use DRY helper to ensure part exists with custom values for this test
        ensure_parts_exist(
            db.session,
            config.PART_ID_PART_0,
            part_num="0",
            cad_per_usd=Decimal("1.42"),
            cost_cad=Decimal("10"),
            cost_usd=Decimal("10"),
            msrp_cad=Decimal("0.0"),
            msrp_usd=Decimal("0.0"),
            transfer_mult_inc_to_corp=0.8,
            transfer_mult_cad_dealer=10,
            transfer_mult_usd_dealer=10,
            warehouse_mult=10,
            msrp_mult_cad=10,
            msrp_mult_usd=10,
            dealer_cost_cad=Decimal("10"),
            dealer_cost_usd=Decimal("10"),
            ijack_corp_cost=Decimal("0.0"),
        )

        # Get or create warehouse part for Part 0 at Moosomin
        warehouse_part = setup_warehouse_part_for_test(
            db.session, config.WAREHOUSE_ID_MOOSOMIN, config.PART_ID_PART_0
        )
        # Set initial quantity high enough for our tests
        warehouse_part.quantity = Decimal("100")
        warehouse_part.quantity_reserved = Decimal("0")
        db.session.commit()

        # Generate unique invoice summary
        invoice_summary = generate_unique_invoice_summary(
            "test_reservation_quantity_bug"
        )

        # Create initial work order with quantity 1
        print("\n=== Step 1: Create work order with quantity 1 ===")

        # Common work order data
        common_data = {
            "work_order_submit_store_data": True,
            "work_order_date_service_value": "2023-01-01",
            "work_order_service_type_value": config.SERVICE_TYPE_ID_REPAIR,
            "work_order_currency_id_value": config.CURRENCY_ID_CAD,
            "work_order_creator_company_id_data": config.CUSTOMER_ID_IJACK_INC,
            "work_order_customer_value": config.CUSTOMER_ID_IJACK_INC,
            "work_order_users_value": [config.USER_ID_SEAN],
            "work_order_users_sales_value": [config.USER_ID_SEAN],
            "work_order_requested_by_dropdown_value": None,
            "work_order_service_crew_value": "Test Crew",
            "work_order_company_rep_dropdown_value": config.USER_ID_SEAN,
            "work_order_location_value": "Test Location",
            "work_order_model_types_value": [],
            "work_order_service_required_value": "Test quantity bug",
            "work_order_is_warranty_value": False,
            "work_order_work_done_value": "Testing even number bug",
            "work_order_customer_po_value": "PO123",
            "work_order_cust_work_order_value": "CWO123",
            "work_order_afe_value": "AFE123",
            "work_order_invoice_summary_value": invoice_summary,
            "work_order_structures_value": [config.STRUCTURE_GING_TEST_CALGARY],
            "work_order_structure_slave_value": None,
            "work_order_power_units_value": [config.POWER_UNIT_ID_GING_TEST_CALGARY],
            "work_order_province_id_value": config.PROVINCE_ID_AB,
            "work_order_zip_code_id_value": None,
            "work_order_sales_tax_rate_value": 0,
            "work_order_gst_rate_value": 0,
            "work_order_pst_rate_value": 0,
            "work_order_county_id_value": None,
            "work_order_city_id_value": None,
            "work_order_subtotal_store_data": 100.00,
            "work_order_discount_pct_value": 0,
            "work_order_subtotal_after_discount_store_data": 100.00,
            "work_order_sales_tax_store_data": 0,
            "work_order_gst_amount_store_data": 0,
            "work_order_pst_amount_store_data": 0,
            "work_order_total_store_data": 100.00,
            "work_order_file_upload_data_table_data": [],
            "signature_store_data": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48L3N2Zz4=",
            "work_order_signature_name_value": "Test Signature",
            "work_order_signature_date_date": "2023-01-01",
            "work_order_country_id_value": config.COUNTRY_ID_CANADA,
            "work_order_approved_value": False,  # Not approved
            "work_order_is_quote_value": False,  # Not a quote
            "work_order_is_tax_exempt_value": False,
        }

        # Create with quantity 1
        result = work_order_form_submit(
            **common_data,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": str(config.PART_ID_PART_0),
                    "part_num": "0",  # Add the actual part number for validation
                    "description": "Test Part 0",
                    "structure_id": str(config.STRUCTURE_ID_GATEWAYS),
                    "warehouse_id": str(config.WAREHOUSE_ID_MOOSOMIN),
                    "warehouse_to_id": None,
                    "quantity": 1,  # Start with quantity 1
                    "price": 100.00,
                    "cost_before_tax": 100.00,
                    "field_tech_id": str(config.USER_ID_SEAN),
                }
            ],
            edit_work_order_id_data=None,
        )

        work_order_id = int(result[8])  # edit_work_order_id_data

        # Check initial reservation
        db.session.refresh(warehouse_part)
        print(
            f"After quantity 1: quantity_reserved = {warehouse_part.quantity_reserved}"
        )
        assert warehouse_part.quantity_reserved == Decimal("1"), (
            f"Expected reserved quantity 1, got {warehouse_part.quantity_reserved}"
        )

        # Step 2: Update to quantity 2
        print("\n=== Step 2: Update work order to quantity 2 ===")

        result = work_order_form_submit(
            **common_data,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": str(config.PART_ID_PART_0),
                    "part_num": "0",  # Add the actual part number for validation
                    "description": "Test Part 0",
                    "structure_id": str(config.STRUCTURE_ID_GATEWAYS),
                    "warehouse_id": str(config.WAREHOUSE_ID_MOOSOMIN),
                    "warehouse_to_id": None,
                    "quantity": 2,  # Update to quantity 2
                    "price": 100.00,
                    "cost_before_tax": 200.00,
                    "field_tech_id": str(config.USER_ID_SEAN),
                }
            ],
            edit_work_order_id_data=work_order_id,
        )

        # Refresh warehouse part to get latest state from database
        db.session.refresh(warehouse_part)
        print(
            f"After quantity 2: quantity_reserved = {warehouse_part.quantity_reserved}"
        )
        assert warehouse_part.quantity_reserved == Decimal("2"), (
            f"BUG: Expected reserved quantity 2, got {warehouse_part.quantity_reserved}"
        )

        # Step 3: Update to quantity 3
        print("\n=== Step 3: Update work order to quantity 3 ===")
        result = work_order_form_submit(
            **common_data,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": str(config.PART_ID_PART_0),
                    "part_num": "0",  # Add the actual part number for validation
                    "description": "Test Part 0",
                    "structure_id": str(config.STRUCTURE_ID_GATEWAYS),
                    "warehouse_id": str(config.WAREHOUSE_ID_MOOSOMIN),
                    "warehouse_to_id": None,
                    "quantity": 3,  # Update to quantity 3
                    "price": 100.00,
                    "cost_before_tax": 300.00,
                    "field_tech_id": str(config.USER_ID_SEAN),
                }
            ],
            edit_work_order_id_data=work_order_id,
        )

        db.session.refresh(warehouse_part)
        print(
            f"After quantity 3: quantity_reserved = {warehouse_part.quantity_reserved}"
        )
        assert warehouse_part.quantity_reserved == Decimal("3"), (
            f"Expected reserved quantity 3, got {warehouse_part.quantity_reserved}"
        )

        # Step 4: Update to quantity 4
        print("\n=== Step 4: Update work order to quantity 4 ===")
        result = work_order_form_submit(
            **common_data,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": str(config.PART_ID_PART_0),
                    "part_num": "0",  # Add the actual part number for validation
                    "description": "Test Part 0",
                    "structure_id": str(config.STRUCTURE_ID_GATEWAYS),
                    "warehouse_id": str(config.WAREHOUSE_ID_MOOSOMIN),
                    "warehouse_to_id": None,
                    "quantity": 4,  # Update to quantity 4
                    "price": 100.00,
                    "cost_before_tax": 400.00,
                    "field_tech_id": str(config.USER_ID_SEAN),
                }
            ],
            edit_work_order_id_data=work_order_id,
        )

        db.session.refresh(warehouse_part)
        print(
            f"After quantity 4: quantity_reserved = {warehouse_part.quantity_reserved}"
        )
        assert warehouse_part.quantity_reserved == Decimal("4"), (
            f"BUG: Expected reserved quantity 4, got {warehouse_part.quantity_reserved}"
        )

        print("\n✅ Test passed: Reserved quantities correctly updated for all values")

        # Cleanup
        work_order = db.session.get(WorkOrder, work_order_id)
        if work_order:
            db.session.delete(work_order)
            db.session.commit()

        # Reset warehouse part
        warehouse_part.quantity = Decimal("2")
        warehouse_part.quantity_reserved = Decimal("0")
        db.session.commit()
