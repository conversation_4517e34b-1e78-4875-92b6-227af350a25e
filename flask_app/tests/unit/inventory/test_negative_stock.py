#!/usr/bin/env python3
"""
Test script for warehouse negative stock policy implementation.

This script tests the new warehouse-specific negative stock validation
to ensure it respects the Warehouse.allows_negative_stock setting.
"""

from decimal import Decimal

import pytest
from shared.models.models_bom import Warehouse, WarehousePart
from shared.utils.inventory_manager import (
    ReservationManager,
    validate_inventory_availability,
    warehouse_allows_negative_stock,
)

from app import config
from tests.conftest import ijack_context


def test_warehouse_allows_negative_stock(all_ijack):
    """Test the warehouse_allows_negative_stock utility function"""
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Create test warehouses with different negative stock policies
        warehouse_1 = Warehouse(
            id=9901, name="Test Warehouse Allows Negative", allows_negative_stock=True
        )
        warehouse_2 = Warehouse(
            id=9902, name="Test Warehouse No Negative", allows_negative_stock=False
        )

        db.session.add(warehouse_1)
        db.session.add(warehouse_2)
        db.session.commit()

        try:
            # Test warehouse that allows negative stock
            result = warehouse_allows_negative_stock(db.session, 9901)
            assert result is True, f"Expected True, got {result}"

            # Test warehouse that doesn't allow negative stock
            result = warehouse_allows_negative_stock(db.session, 9902)
            assert result is False, f"Expected False, got {result}"

            # Test non-existent warehouse
            with pytest.raises(ValueError, match="Warehouse 999999 not found"):
                warehouse_allows_negative_stock(db.session, 999999)

        finally:
            # Clean up
            db.session.query(Warehouse).filter(Warehouse.id.in_([9901, 9902])).delete()
            db.session.commit()


def test_validate_inventory_availability(all_ijack):
    """Test the validate_inventory_availability function"""
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Create test warehouses
        warehouse_1 = Warehouse(
            id=9903, name="Test Warehouse Allows Negative", allows_negative_stock=True
        )
        warehouse_2 = Warehouse(
            id=9904, name="Test Warehouse No Negative", allows_negative_stock=False
        )

        db.session.add(warehouse_1)
        db.session.add(warehouse_2)
        db.session.commit()

        try:
            # Test case 1: Sufficient inventory (should always pass)
            result = validate_inventory_availability(
                session=db.session,
                warehouse_id=9903,
                part_id=100,
                required_quantity=Decimal("5"),
                current_available=Decimal("10"),
            )
            assert result.is_valid is True, "Should be valid when sufficient inventory"
            assert len(result.errors) == 0, "Should have no errors"
            assert len(result.warnings) == 0, "Should have no warnings"

            # Test case 2: Insufficient inventory, warehouse allows negative
            result = validate_inventory_availability(
                session=db.session,
                warehouse_id=9903,  # Allows negative
                part_id=100,
                required_quantity=Decimal("15"),
                current_available=Decimal("10"),
            )
            assert result.is_valid is True, (
                "Should be valid when warehouse allows negative"
            )
            assert len(result.errors) == 0, "Should have no errors"
            assert len(result.warnings) == 1, "Should have warning about negative stock"

            # Test case 3: Insufficient inventory, warehouse doesn't allow negative
            result = validate_inventory_availability(
                session=db.session,
                warehouse_id=9904,  # Doesn't allow negative
                part_id=100,
                required_quantity=Decimal("15"),
                current_available=Decimal("10"),
            )
            assert result.is_valid is False, (
                "Should be invalid when warehouse doesn't allow negative"
            )
            assert len(result.errors) == 1, "Should have error"
            assert "warehouse does not allow negative stock" in result.errors[0]

        finally:
            # Clean up
            db.session.query(Warehouse).filter(Warehouse.id.in_([9903, 9904])).delete()
            db.session.commit()


def test_reservation_manager_integration(all_ijack):
    """Test ReservationManager with new validation"""
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Create test warehouse
        warehouse = Warehouse(
            id=9905, name="Test Warehouse for Reservation", allows_negative_stock=False
        )
        db.session.add(warehouse)
        db.session.commit()

        try:
            # Create a warehouse part with limited inventory
            warehouse_part = WarehousePart(
                warehouse_id=9905,
                part_id=config.PART_ID_PART_0,
                quantity=Decimal("10"),
                quantity_reserved=Decimal("0"),
            )
            db.session.add(warehouse_part)
            db.session.commit()

            # Note: Full integration test with ReservationManager
            # would require more setup - this is just a basic structure test
            from shared.utils.inventory_manager import InventoryLedgerManager

            ledger_manager = InventoryLedgerManager()
            reservation_manager = ReservationManager(db.session, ledger_manager)

            # Verify the reservation manager was created successfully
            assert reservation_manager is not None

        finally:
            # Clean up
            db.session.query(WarehousePart).filter(
                WarehousePart.warehouse_id == 9905
            ).delete()
            db.session.query(Warehouse).filter(Warehouse.id == 9905).delete()
            db.session.commit()
