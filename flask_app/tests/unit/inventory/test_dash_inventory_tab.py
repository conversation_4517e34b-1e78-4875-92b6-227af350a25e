"""
Unit tests for Flask inventory management operations.

These tests verify that the Flask inventory callbacks properly integrate
with the shared inventory management system.
"""

import hashlib
import inspect
import os
import time
from decimal import Decimal
from unittest.mock import patch

import pytest
from shared.models.models_bom import (
    InventoryLedger,
    InventoryMovement,
    Part,
    WarehousePart,
)

from app.config import (
    PART_ID_PART_0,
    USER_ID_SEAN,
    WAREHOUSE_ID_MOOSOMIN,
)
from app.dashapp.callbacks.inventory import (
    save_inventory_modal,
    update_inventory_database,
)
from tests.conftest import ijack_context


def _get_existing_warehouse_id(db_session):
    """Get an existing warehouse ID from the database."""
    from shared.models.models_bom import Warehouse

    existing_warehouse = db_session.query(Warehouse).first()
    if not existing_warehouse:
        pytest.skip("No warehouse available for testing")
    return existing_warehouse.id


def _get_unique_test_id(base_id):
    """Generate unique ID for parallel test execution"""
    # Get the test method name
    frame = inspect.currentframe()
    while frame:
        if frame.f_code.co_name.startswith("test_"):
            test_name = frame.f_code.co_name
            break
        frame = frame.f_back
    else:
        test_name = "unknown_test"

    # Create a unique ID based on test name, timestamp, and process ID
    unique_string = f"{test_name}_{time.time_ns()}_{os.getpid()}"
    hash_obj = hashlib.md5(unique_string.encode())
    hash_int = int(hash_obj.hexdigest()[:6], 16)
    return base_id + hash_int


@pytest.fixture
def setup_clean_warehouse_part(all_ijack):
    """Set up a clean warehouse part for testing with unique part ID."""
    flask_app, flask_test_client, dash_app, db = all_ijack

    # Generate unique part ID for this test
    unique_part_id = _get_unique_test_id(PART_ID_PART_0)

    with ijack_context(flask_app, logged_in_cust=True):
        # Get a warehouse that actually exists in the database
        warehouse_id = _get_existing_warehouse_id(db.session)

        # Create the part if it doesn't exist
        existing_part = db.session.get(Part, unique_part_id)
        if not existing_part:
            part = Part(
                id=unique_part_id,
                part_num=f"TEST-PART-{unique_part_id}",
                cad_per_usd=1.42,
                cost_cad=10,
                cost_usd=10,
                msrp_cad=0.0,
                msrp_usd=0.0,
                transfer_mult_inc_to_corp=0.8,
                transfer_mult_cad_dealer=10,
                transfer_mult_usd_dealer=10,
                warehouse_mult=10,
                msrp_mult_cad=10,
                msrp_mult_usd=10,
                dealer_cost_cad=10,
                dealer_cost_usd=10,
                ijack_corp_cost=0.0,
                is_usd=False,
                is_soft_part=False,
                is_hard_part=False,
            )
            db.session.add(part)
            db.session.commit()

        # Get a warehouse that actually exists in the database
        warehouse_id = _get_existing_warehouse_id(db.session)

        # Clean up any existing inventory data for this part
        db.session.query(InventoryLedger).filter(
            InventoryLedger.part_id == unique_part_id,
            InventoryLedger.warehouse_id == warehouse_id,
        ).delete()

        db.session.query(InventoryMovement).filter(
            InventoryMovement.part_id == unique_part_id,
        ).delete()

        existing_warehouse_part = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == unique_part_id,
                WarehousePart.warehouse_id == warehouse_id,
            )
            .first()
        )
        if existing_warehouse_part:
            db.session.delete(existing_warehouse_part)

        db.session.commit()

        yield db, unique_part_id, warehouse_id

        # Cleanup after test
        db.session.query(InventoryLedger).filter(
            InventoryLedger.part_id == unique_part_id,
            InventoryLedger.warehouse_id == warehouse_id,
        ).delete()

        db.session.query(InventoryMovement).filter(
            InventoryMovement.part_id == unique_part_id,
        ).delete()

        warehouse_part = (
            db.session.query(WarehousePart)
            .filter(
                WarehousePart.part_id == unique_part_id,
                WarehousePart.warehouse_id == warehouse_id,
            )
            .first()
        )
        if warehouse_part:
            db.session.delete(warehouse_part)

        # Clean up the part we created
        part = db.session.get(Part, unique_part_id)
        if part:
            db.session.delete(part)

        db.session.commit()


def test_save_inventory_modal_add_new_part(all_ijack, setup_clean_warehouse_part):
    """Test adding a new part to a warehouse using the inventory modal."""
    flask_app, flask_test_client, dash_app, db = all_ijack
    db, unique_part_id, warehouse_id = setup_clean_warehouse_part

    with ijack_context(flask_app, logged_in_cust=True):
        # Mock the current_user to be an IJACK employee
        with patch("app.dashapp.callbacks.inventory.current_user") as mock_user:
            mock_user.id = USER_ID_SEAN

            with patch(
                "app.dashapp.callbacks.inventory.user_is_ijack_employee",
                return_value=True,
            ):
                # Call the save_inventory_modal function
                result = save_inventory_modal(
                    n_clicks=1,
                    inventory_modal_new_part=unique_part_id,
                    inventory_modal_new_warehouse=warehouse_id,
                    inventory_modal_actual_quantity=10,
                    inventory_modal_desired_quantity=15,
                )

                # Unpack the result
                (
                    inventory_modal_is_open,
                    inventory_modal_message_children,
                    inventory_modal_message_color,
                    store_inventory_modal_updated_data,
                ) = result

                # Verify the response
                assert inventory_modal_is_open is True
                assert (
                    "Inventory added" in inventory_modal_message_children
                )  # Changed to match new message
                assert inventory_modal_message_color == "green"
                assert store_inventory_modal_updated_data is True

                # Verify the warehouse part was created
                warehouse_part = (
                    db.session.query(WarehousePart)
                    .filter(
                        WarehousePart.part_id == unique_part_id,
                        WarehousePart.warehouse_id == warehouse_id,
                    )
                    .first()
                )
                assert warehouse_part is not None
                assert warehouse_part.quantity == Decimal("10")
                assert warehouse_part.quantity_desired == Decimal("15")
                assert warehouse_part.quantity_reserved == Decimal(
                    "0"
                )  # Should be initialized to 0


def test_save_inventory_modal_update_existing_part(all_ijack):
    """Test updating an existing part in a warehouse using the inventory modal."""
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Get existing warehouse and create unique part
        warehouse_id = _get_existing_warehouse_id(db.session)
        unique_part_id = _get_unique_test_id(PART_ID_PART_0)

        # Create the part if it doesn't exist
        existing_part_record = db.session.get(Part, unique_part_id)
        if not existing_part_record:
            part = Part(
                id=unique_part_id,
                part_num=f"TEST-PART-{unique_part_id}",
                cad_per_usd=1.42,
                cost_cad=10,
                cost_usd=10,
                msrp_cad=0.0,
                msrp_usd=0.0,
                transfer_mult_inc_to_corp=0.8,
                transfer_mult_cad_dealer=10,
                transfer_mult_usd_dealer=10,
                warehouse_mult=10,
                msrp_mult_cad=10,
                msrp_mult_usd=10,
                dealer_cost_cad=10,
                dealer_cost_usd=10,
                ijack_corp_cost=0.0,
                is_usd=False,
                is_soft_part=False,
                is_hard_part=False,
            )
            db.session.add(part)
            db.session.commit()

        # First, create an existing warehouse part
        existing_part = WarehousePart(
            part_id=unique_part_id,
            warehouse_id=warehouse_id,
            quantity=Decimal("5"),
            quantity_desired=Decimal("10"),
            quantity_reserved=Decimal("2"),
        )
        db.session.add(existing_part)
        db.session.commit()

        # Mock the current_user to be an IJACK employee
        with patch("app.dashapp.callbacks.inventory.current_user") as mock_user:
            mock_user.id = USER_ID_SEAN

            with patch(
                "app.dashapp.callbacks.inventory.user_is_ijack_employee",
                return_value=True,
            ):
                # Call the save_inventory_modal function to update
                result = save_inventory_modal(
                    n_clicks=1,
                    inventory_modal_new_part=unique_part_id,
                    inventory_modal_new_warehouse=warehouse_id,
                    inventory_modal_actual_quantity=20,
                    inventory_modal_desired_quantity=25,
                )

                # Unpack the result
                (
                    inventory_modal_is_open,
                    inventory_modal_message_children,
                    inventory_modal_message_color,
                    store_inventory_modal_updated_data,
                ) = result

                # Verify the response
                assert inventory_modal_is_open is True
                assert (
                    "Inventory updated" in inventory_modal_message_children
                )  # Changed to match new message
                assert inventory_modal_message_color == "green"
                assert store_inventory_modal_updated_data is True

                # Verify the warehouse part was updated
                db.session.refresh(existing_part)
                assert existing_part.quantity == Decimal("20")
                assert existing_part.quantity_desired == Decimal("25")
                # Reserved quantity should NOT be changed by this operation
                assert existing_part.quantity_reserved == Decimal("2")

        # Cleanup
        db.session.delete(existing_part)
        # Also clean up the part we created
        part = db.session.get(Part, unique_part_id)
        if part:
            db.session.delete(part)
        db.session.commit()


def test_update_inventory_database_cell_change(all_ijack):
    """Test updating inventory when a cell is changed in the AG Grid."""
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Get existing warehouse and create unique part
        warehouse_id = _get_existing_warehouse_id(db.session)
        unique_part_id = _get_unique_test_id(PART_ID_PART_0)

        # Create the part if it doesn't exist
        existing_part_record = db.session.get(Part, unique_part_id)
        if not existing_part_record:
            part = Part(
                id=unique_part_id,
                part_num=f"TEST-PART-{unique_part_id}",
                cad_per_usd=1.42,
                cost_cad=10,
                cost_usd=10,
                msrp_cad=0.0,
                msrp_usd=0.0,
                transfer_mult_inc_to_corp=0.8,
                transfer_mult_cad_dealer=10,
                transfer_mult_usd_dealer=10,
                warehouse_mult=10,
                msrp_mult_cad=10,
                msrp_mult_usd=10,
                dealer_cost_cad=10,
                dealer_cost_usd=10,
                ijack_corp_cost=0.0,
                is_usd=False,
                is_soft_part=False,
                is_hard_part=False,
            )
            db.session.add(part)
            db.session.commit()

        # Create an existing warehouse part
        warehouse_part = WarehousePart(
            part_id=unique_part_id,
            warehouse_id=warehouse_id,
            quantity=Decimal("10"),
            quantity_desired=Decimal("15"),
            quantity_reserved=Decimal("0"),
        )
        db.session.add(warehouse_part)
        db.session.commit()

        # Mock necessary functions
        with patch(
            "app.dashapp.callbacks.inventory.get_highest_part_revision_id",
            return_value=unique_part_id,
        ):
            with patch(
                "app.dashapp.callbacks.inventory.delete_other_duplicates_in_many_to_many"
            ):
                # Simulate updating the actual quantity
                cell_change_event = [
                    {
                        "data": {
                            "warehouse_name": "Moosomin",
                            "part_name": "TEST_PART_0",
                        },
                        "colId": "warehouse_quantity",
                        "oldValue": 10,
                        "value": "25",
                    }
                ]

                result = update_inventory_database(cell_change_event)

                # Unpack the result
                (
                    modal_message_header_children,
                    modal_message_body_children,
                    modal_message_is_open,
                    modal_message_backdrop,
                    inventory_add_part_to_warehouse_btn_n_clicks,
                    inventory_modal_new_part,
                    inventory_modal_actual_quantity,
                    inventory_modal_desired_quantity,
                    inventory_modal_message_children,
                    inventory_modal_message_color,
                ) = result

                # Verify the response
                assert modal_message_is_open is True
                assert "Actual Inventory Updated!" in modal_message_header_children
                assert "25" in modal_message_body_children
                assert "10" in modal_message_body_children  # Old value

                # Verify the warehouse part was updated
                db.session.refresh(warehouse_part)
                assert warehouse_part.quantity == Decimal("25")
                assert warehouse_part.quantity_desired == Decimal("15")  # Unchanged
                assert warehouse_part.quantity_reserved == Decimal("0")  # Unchanged

        # Cleanup
        db.session.delete(warehouse_part)
        # Also clean up the part we created
        part = db.session.get(Part, unique_part_id)
        if part:
            db.session.delete(part)
        db.session.commit()


def test_update_inventory_database_desired_quantity_change(all_ijack):
    """Test updating desired quantity when a cell is changed in the AG Grid."""
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Get existing warehouse and create unique part
        warehouse_id = _get_existing_warehouse_id(db.session)
        unique_part_id = _get_unique_test_id(PART_ID_PART_0)

        # Create the part if it doesn't exist
        existing_part_record = db.session.get(Part, unique_part_id)
        if not existing_part_record:
            part = Part(
                id=unique_part_id,
                part_num=f"TEST-PART-{unique_part_id}",
                cad_per_usd=1.42,
                cost_cad=10,
                cost_usd=10,
                msrp_cad=0.0,
                msrp_usd=0.0,
                transfer_mult_inc_to_corp=0.8,
                transfer_mult_cad_dealer=10,
                transfer_mult_usd_dealer=10,
                warehouse_mult=10,
                msrp_mult_cad=10,
                msrp_mult_usd=10,
                dealer_cost_cad=10,
                dealer_cost_usd=10,
                ijack_corp_cost=0.0,
                is_usd=False,
                is_soft_part=False,
                is_hard_part=False,
            )
            db.session.add(part)
            db.session.commit()

        # Create an existing warehouse part
        warehouse_part = WarehousePart(
            part_id=unique_part_id,
            warehouse_id=warehouse_id,
            quantity=Decimal("10"),
            quantity_desired=Decimal("15"),
            quantity_reserved=Decimal("0"),
        )
        db.session.add(warehouse_part)
        db.session.commit()

        # Mock necessary functions
        with patch(
            "app.dashapp.callbacks.inventory.get_highest_part_revision_id",
            return_value=unique_part_id,
        ):
            with patch(
                "app.dashapp.callbacks.inventory.delete_other_duplicates_in_many_to_many"
            ):
                # Simulate updating the desired quantity
                cell_change_event = [
                    {
                        "data": {
                            "warehouse_name": "Moosomin",
                            "part_name": "TEST_PART_0",
                        },
                        "colId": "warehouse_quantity_desired",
                        "oldValue": 15,
                        "value": "30",
                    }
                ]

                result = update_inventory_database(cell_change_event)

                # Unpack the result
                (
                    modal_message_header_children,
                    modal_message_body_children,
                    modal_message_is_open,
                    modal_message_backdrop,
                    inventory_add_part_to_warehouse_btn_n_clicks,
                    inventory_modal_new_part,
                    inventory_modal_actual_quantity,
                    inventory_modal_desired_quantity,
                    inventory_modal_message_children,
                    inventory_modal_message_color,
                ) = result

                # Verify the response
                assert modal_message_is_open is True
                assert "Desired Inventory Updated!" in modal_message_header_children
                assert "30" in modal_message_body_children
                assert "15" in modal_message_body_children  # Old value

                # Verify the warehouse part was updated
                db.session.refresh(warehouse_part)
                assert warehouse_part.quantity == Decimal("10")  # Unchanged
                assert warehouse_part.quantity_desired == Decimal("30")
                assert warehouse_part.quantity_reserved == Decimal("0")  # Unchanged

        # Cleanup
        db.session.delete(warehouse_part)
        # Also clean up the part we created
        part = db.session.get(Part, unique_part_id)
        if part:
            db.session.delete(part)
        db.session.commit()


def test_inventory_ledger_integration_on_add(all_ijack, setup_clean_warehouse_part):
    """Test that adding inventory creates proper ledger entries."""
    flask_app, flask_test_client, dash_app, db = all_ijack
    db, unique_part_id, warehouse_id = setup_clean_warehouse_part

    with ijack_context(flask_app, logged_in_cust=True):
        # Mock the current_user to be an IJACK employee
        with patch("app.dashapp.callbacks.inventory.current_user") as mock_user:
            mock_user.id = USER_ID_SEAN

            with patch(
                "app.dashapp.callbacks.inventory.user_is_ijack_employee",
                return_value=True,
            ):
                # Count ledger entries before operation
                ledger_count_before = db.session.query(InventoryLedger).count()

                # Add inventory using the modal
                save_inventory_modal(
                    n_clicks=1,
                    inventory_modal_new_part=unique_part_id,
                    inventory_modal_new_warehouse=warehouse_id,
                    inventory_modal_actual_quantity=10,
                    inventory_modal_desired_quantity=15,
                )

                # The Flask implementation now uses the ledger system
                # Verify ledger entries are created
                ledger_count_after = db.session.query(InventoryLedger).count()

                # After migration, this should create ledger entries
                # We expect 1 ledger entry for the inventory adjustment
                assert ledger_count_after == ledger_count_before + 1

                # Also verify movement was created
                movement_count = (
                    db.session.query(InventoryMovement)
                    .filter(InventoryMovement.part_id == unique_part_id)
                    .count()
                )
                assert movement_count == 1


def test_inventory_validation_errors(all_ijack):
    """Test validation errors in the inventory modal."""
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Mock the current_user to be an IJACK employee
        with patch("app.dashapp.callbacks.inventory.current_user") as mock_user:
            mock_user.id = USER_ID_SEAN

            with patch(
                "app.dashapp.callbacks.inventory.user_is_ijack_employee",
                return_value=True,
            ):
                # Test with missing part
                result = save_inventory_modal(
                    n_clicks=1,
                    inventory_modal_new_part=None,
                    inventory_modal_new_warehouse=WAREHOUSE_ID_MOOSOMIN,
                    inventory_modal_actual_quantity=10,
                    inventory_modal_desired_quantity=15,
                )

                assert result[1] == "Please select a part"
                assert result[2] == "red"

                # Test with negative quantity
                result = save_inventory_modal(
                    n_clicks=1,
                    inventory_modal_new_part=PART_ID_PART_0,
                    inventory_modal_new_warehouse=WAREHOUSE_ID_MOOSOMIN,
                    inventory_modal_actual_quantity=-5,
                    inventory_modal_desired_quantity=15,
                )

                assert result[1] == "Actual quantity must be 0 or greater"
                assert result[2] == "red"


def test_non_ijack_employee_cannot_update_inventory(all_ijack):
    """Test that non-IJACK employees cannot update inventory."""
    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_cust=True):
        # Mock the current_user to NOT be an IJACK employee
        with patch("app.dashapp.callbacks.inventory.current_user") as mock_user:
            mock_user.id = USER_ID_SEAN

            with patch(
                "app.dashapp.callbacks.inventory.user_is_ijack_employee",
                return_value=False,
            ):
                # Attempt to save inventory
                with pytest.raises(Exception):  # Should raise PreventUpdate
                    save_inventory_modal(
                        n_clicks=1,
                        inventory_modal_new_part=PART_ID_PART_0,
                        inventory_modal_new_warehouse=WAREHOUSE_ID_MOOSOMIN,
                        inventory_modal_actual_quantity=10,
                        inventory_modal_desired_quantity=15,
                    )
