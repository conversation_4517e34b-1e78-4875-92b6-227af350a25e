# tests/test_databases.py

import os
import unittest
from unittest.mock import MagicMock, patch

from flask import g
from sqlalchemy.exc import OperationalError
from sqlalchemy.sql import text

from app import create_flask_app, db
from app.config import CUSTOMER_ID_DEMO
from app.databases import (
    get_aws_iot_ats_endpoint,
    get_boto3_client,
    get_conn,
    run_sql_query,
)

# def insert_path(path):
#     try:
#         sys.path.index(str(path))
#     except ValueError:
#         sys.path.insert(0, str(path))


# workspace_folder = Path(__file__).absolute().parent.parent
# insert_path(workspace_folder)


@patch("app.home.views.run_sql_query")
@patch("app.home.views.cache")
def test_healthcheck_page(mock_cache, mock_run_sql_query, all_ijack):
    """
    Tests that both the database connections are healthy
    """
    flask_app, flask_test_client, dash_app, db = all_ijack

    # Mock the database queries to return dummy data
    mock_run_sql_query.return_value = ([], [])  # Empty rows and columns
    mock_cache.set.return_value = True

    # with flask_app.app_context():
    response = flask_test_client.get("/healthcheck", follow_redirects=True)

    assert response.status_code == 200
    assert b"Both databases, and Redis, are working fine at " in response.data

    # Verify the expected database queries were made
    assert mock_run_sql_query.call_count == 2
    mock_cache.set.assert_called_once_with("healthcheck", "ok", timeout=5)


def test_run_sql_query(all_ijack):
    """Test the test IJACK Postgres database"""

    flask_app, flask_test_client, dash_app, db = all_ijack
    sql = text(
        """
        select customer
        --group_id
        from public.customers
        where id = :id
        limit 1
    """
    ).bindparams(
        # Demo customer in conftest.py
        id=CUSTOMER_ID_DEMO,
    )
    with flask_app.app_context():
        rows, cols = run_sql_query(sql, db_name="ijack")

    assert cols == [
        "customer",
    ]
    # Demo customer in conftest.py
    assert rows[0]["customer"] == "Demo Customer"


def test_get_boto3_client_new_client(app_context, mock_aws_credentials):
    """Test creating a new boto3 client"""
    # Mock boto3.Session to prevent actual AWS calls
    mock_session = MagicMock()
    mock_client = MagicMock()
    mock_session.client.return_value = mock_client

    with patch("boto3.Session", return_value=mock_session):
        # Call the function
        _ = get_boto3_client("s3")

        # Verify the client was created with correct parameters
        mock_session.client.assert_called_once_with(
            service_name="s3",
            region_name="us-west-2",
            aws_access_key_id="test-key",
            aws_secret_access_key="test-secret",
            # config=config,
            # verify=True,
            # endpoint_url=None,
        )

        # Verify client was stored in g
        assert g.boto_clients["s3"] == mock_client


def test_get_boto3_client_reuse_existing(app_context):
    """Test reusing an existing boto3 client from g"""
    # Create a mock client and store it in g
    mock_client = MagicMock()
    g.boto_clients = {"s3": mock_client}

    # Call the function
    client = get_boto3_client("s3")

    # Verify the existing client was returned
    assert client == mock_client


def test_get_boto3_client_iot_real_endpoint(app_context):
    """Test getting the IoT Data-ATS endpoint"""
    # Call the function
    client = get_boto3_client("iot")

    # Verify the client was created with the correct endpoint
    assert client.meta.endpoint_url == "https://iot.us-west-2.amazonaws.com"


def test_get_boto3_client_iot_data_real_endpoint(app_context):
    """Test getting the IoT Data-ATS endpoint"""
    # Call the function
    client = get_boto3_client("iot-data")

    # Verify the client was created with the correct endpoint
    assert (
        client.meta.endpoint_url
        # == "https://a2zzb3nqu1iqym-ats.iot.us-west-2.amazonaws.com"
        == "https://data-ats.iot.us-west-2.amazonaws.com"
    )


def test_get_boto3_client_s3_real_endpoint(app_context):
    """Test getting the IoT Data-ATS endpoint"""
    # Call the function
    client = get_boto3_client("s3")

    # Verify the client was created with the correct endpoint
    assert client.meta.endpoint_url == "https://s3.us-west-2.amazonaws.com"


def test_get_aws_iot_ats_endpoint_iot_data():
    """Test getting the IoT Data-ATS endpoint"""
    # Test the iot-data service endpoint
    # expected_url = "https://a2zzb3nqu1iqym-ats.iot.us-west-2.amazonaws.com"
    expected_url = "https://data-ats.iot.us-west-2.amazonaws.com"
    result = get_aws_iot_ats_endpoint(service_name="iot-data")
    assert result == expected_url


def test_get_aws_iot_ats_endpoint_iot():
    """Test getting the IoT endpoint"""
    # Test the iot service endpoint
    expected_url = "https://iot.us-west-2.amazonaws.com"
    result = get_aws_iot_ats_endpoint(service_name="iot")
    assert result == expected_url


def test_get_aws_iot_ats_endpoint_s3():
    """Test getting the S3 endpoint (should return None)"""
    # Test S3 service endpoint
    result = get_aws_iot_ats_endpoint(service_name="s3")
    assert result == "https://s3.us-west-2.amazonaws.com"


def test_get_aws_iot_ats_endpoint_unknown_service():
    """Test behavior with unknown service name"""
    # Test with an unknown service name
    result = get_aws_iot_ats_endpoint(service_name="unknown-service")
    assert result is None


class TestDatabaseConnections(unittest.TestCase):
    """Test suite for database connection handling with production config"""

    def setUp(self):
        """Set up test environment before each test"""
        # Force production config for testing
        os.environ["FLASK_CONFIG"] = "production"

        # Mock MQTT client to avoid certificate requirements in tests
        with patch("app.mqtt.MQTTClient.init_app"):
            # Create Flask app with production config
            self.app = create_flask_app()

        self.app_context = self.app.app_context()
        self.app_context.push()

    def tearDown(self):
        """Clean up after each test"""
        self.app_context.pop()
        # Reset environment variables
        os.environ["FLASK_CONFIG"] = "testing"

    def test_valid_database_names(self):
        """Test that only valid database names are accepted"""
        # Test invalid database name
        with self.assertRaises(ValueError) as context:
            with get_conn("invalid_db"):
                pass
        self.assertIn("must be 'ijack' or 'timescale'", str(context.exception))

    @patch("sqlalchemy.engine.base.Engine.connect")
    def test_connection_error_logging(self, mock_connect):
        """Test that connection errors are properly logged"""
        # Setup mock to raise OperationalError
        mock_connect.side_effect = OperationalError("statement", "params", "orig")

        # Attempt connection and verify error handling
        with self.assertRaises(OperationalError):
            with get_conn("ijack"):
                pass

    @patch("sqlalchemy.engine.base.Engine.connect")
    def test_connection_cleanup(self, mock_connect):
        """Test that connections are properly cleaned up"""
        # Create mock connection
        mock_conn = MagicMock()
        mock_connect.return_value = mock_conn

        # Use context manager
        with get_conn("ijack"):
            pass

        # Verify connection was closed
        mock_conn.close.assert_called_once()

    @patch("sqlalchemy.engine.base.Engine.connect")
    def test_transaction_rollback_on_error(self, mock_connect):
        """Test that transactions are rolled back on error"""
        # Create mock connection with transaction flag
        mock_conn = MagicMock()
        mock_conn.in_transaction.return_value = True
        mock_connect.return_value = mock_conn

        # Simulate error during transaction
        with self.assertRaises(Exception):
            with get_conn("ijack"):
                raise Exception("Test error")

        # Verify rollback was called
        mock_conn.rollback.assert_called_once()

    def test_production_config_connection_params(self):
        """Test that production configuration uses correct connection parameters"""
        # Verify PgBouncer host is used in production
        self.assertEqual(
            self.app.config["SQLALCHEMY_DATABASE_URI"].split("@")[1].split("/")[0],
            "pgbouncer-rds:5432",
        )
        # Verify that the correct user is used
        self.assertEqual(
            self.app.config["SQLALCHEMY_BINDS"]["ijack"]["url"]
            .split("@")[0]
            .split("//")[1]
            .split(":")[0],
            os.getenv("USER_IJ"),
        )

    @patch("sqlalchemy.engine.base.Engine.connect")
    def test_connection_pool_class(self, mock_connect):
        """Test that NullPool is used in production config"""
        # Verify NullPool is configured
        self.assertEqual(
            str(self.app.config["SQLALCHEMY_ENGINE_OPTIONS"]["poolclass"]),
            "<class 'sqlalchemy.pool.impl.NullPool'>",
        )

    @patch("sqlalchemy.engine.base.Engine.connect")
    def test_multiple_database_support_in_production(self, mock_connect):
        """Test connections to both supported databases in production"""
        mock_conn = MagicMock()
        mock_connect.return_value = mock_conn

        # Test ijack connection
        with get_conn("ijack"):
            self.assertEqual(
                str(db.engine.url),
                f"postgresql+psycopg2://{os.getenv('USER_IJ')}:***@pgbouncer-rds:5432/ijack",
            )
            self.assertEqual(
                str(db.engines["ijack"].url),
                f"postgresql+psycopg2://{os.getenv('USER_IJ')}:***@pgbouncer-rds:5432/ijack",
            )

        # Test timescale connection
        with get_conn("timescale"):
            self.assertEqual(
                str(db.engines["timescale"].url),
                f"postgresql+psycopg2://{os.getenv('USER_TS')}:***@pgbouncer-ts:5432/ijack",
            )


if __name__ == "__main__":
    unittest.main()
