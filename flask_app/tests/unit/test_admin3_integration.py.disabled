"""
Integration tests for admin3 Refine interface with Flask API.
Tests the complete flow from frontend access to API responses.
"""

import pytest
from shared.models.models import Customer, Role, Structure, User
from shared.models.models_bom import Part

from app import db
from app.config import ROLE_ID_CUSTOMER, ROLE_ID_IJACK_ADMIN


class TestAdmin3Integration:
    """Test integration between Refine admin frontend and Flask API"""

    @pytest.fixture(autouse=True)
    def setup(self, app, db_session):
        """Set up test fixtures with comprehensive test data"""
        self.app = app
        self.client = app.test_client()
        self.db = db_session

        # Create roles
        self.admin_role = Role(id=ROLE_ID_IJACK_ADMIN, name="IJACK Admin")
        self.customer_role = Role(id=ROLE_ID_CUSTOMER, name="Customer")
        db.session.add_all([self.admin_role, self.customer_role])

        # Create customers
        self.ijack_customer = Customer(id=1, name="IJACK Technologies", status=1)
        self.test_customer = Customer(id=2, name="Test Customer", status=1)
        db.session.add_all([self.ijack_customer, self.test_customer])

        # Create admin user
        self.admin_user = User(
            email="<EMAIL>",
            full_name="Admin User",
            confirmed=True,
            customer_id=1,
        )
        self.admin_user.set_password("password123")
        self.admin_user.roles_rel.append(self.admin_role)

        # Create regular user
        self.regular_user = User(
            email="<EMAIL>",
            full_name="Regular User",
            confirmed=True,
            customer_id=2,
        )
        self.regular_user.set_password("password123")
        self.regular_user.roles_rel.append(self.customer_role)

        db.session.add_all([self.admin_user, self.regular_user])

        # Create some test structures
        for i in range(5):
            structure = Structure(
                structure_code=f"STR{i:03d}", customer_id=2, lsd_id=i + 1, active=True
            )
            db.session.add(structure)

        # Create some test parts
        for i in range(3):
            part = Part(
                part_number=f"PART{i:03d}",
                description=f"Test Part {i}",
                cost=100.00 + (i * 10),
                price=150.00 + (i * 15),
                active=True,
            )
            db.session.add(part)

        db.session.commit()

    def test_admin3_page_loads_with_correct_props(self):
        """Test that admin3 page loads with correct Inertia props"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Access admin3 page
            response = self.client.get("/admin3")
            assert response.status_code == 200

            # Check that Inertia props are passed
            # In a real Inertia response, props would be in the page data
            response_text = response.data.decode()
            assert "admin3" in response_text
            assert self.admin_user.email in response_text
            assert self.admin_user.full_name in response_text

    def test_admin3_api_flow_list_resources(self):
        """Test complete flow: login -> access admin3 -> API list resources"""
        with self.client:
            # Step 1: Login as admin
            login_response = self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )
            assert login_response.status_code == 200

            # Step 2: Access admin3 page
            admin_response = self.client.get("/admin3")
            assert admin_response.status_code == 200

            # Step 3: API call to list users (simulating Refine data provider)
            api_response = self.client.get(
                "/api/v1/admin/users?page=1&per_page=10&sort=created_at&order=desc"
            )
            assert api_response.status_code == 200

            data = api_response.get_json()
            assert "result" in data
            assert "data" in data["result"]
            assert "total" in data["result"]
            assert data["result"]["total"] >= 2  # Our test users

    def test_admin3_api_flow_with_filters(self):
        """Test API flow with Refine-style filters"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Test filter_field__operator format
            response = self.client.get(
                "/api/v1/admin/users?filter_email__contains=ijack"
            )
            assert response.status_code == 200

            data = response.get_json()
            users = data["result"]["data"]
            # Should find admin user
            assert any(u["email"] == "<EMAIL>" for u in users)
            # Should not find regular user
            assert not any(u["email"] == "<EMAIL>" for u in users)

    def test_admin3_multiple_resources(self):
        """Test accessing multiple resource types"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            resources_to_test = [
                ("users", 2),  # At least 2 users
                ("customers", 2),  # At least 2 customers
                ("structures", 5),  # 5 structures created
                ("parts", 3),  # 3 parts created
                ("roles", 2),  # At least 2 roles
            ]

            for resource, min_count in resources_to_test:
                response = self.client.get(f"/api/v1/admin/{resource}")
                assert response.status_code == 200, f"Failed to access {resource}"

                data = response.get_json()
                assert data["result"]["total"] >= min_count, (
                    f"Expected at least {min_count} {resource}"
                )

    def test_admin3_pagination_across_pages(self):
        """Test pagination across multiple pages"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Get first page of structures
            response1 = self.client.get("/api/v1/admin/structures?page=1&per_page=2")
            assert response1.status_code == 200
            data1 = response1.get_json()
            assert len(data1["result"]["data"]) <= 2

            # Get second page
            response2 = self.client.get("/api/v1/admin/structures?page=2&per_page=2")
            assert response2.status_code == 200
            data2 = response2.get_json()

            # Ensure different data on different pages
            if data1["result"]["data"] and data2["result"]["data"]:
                page1_ids = [s["id"] for s in data1["result"]["data"]]
                page2_ids = [s["id"] for s in data2["result"]["data"]]
                assert not any(id in page2_ids for id in page1_ids), (
                    "Pages should have different records"
                )

    def test_admin3_access_denied_for_non_admin(self):
        """Test complete denial flow for non-admin users"""
        with self.client:
            # Login as regular user
            self.client.post(
                "/login",
                data={"email": self.regular_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Try to access admin3 - should be forbidden
            admin_response = self.client.get("/admin3")
            assert admin_response.status_code == 403

            # Try to access API directly - should also be forbidden
            api_response = self.client.get("/api/v1/admin/users")
            assert api_response.status_code == 403

            data = api_response.get_json()
            assert data["error"] == "Admin access required"

    def test_admin3_sorting_different_fields(self):
        """Test sorting by different fields"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Test sorting parts by cost ascending
            response = self.client.get("/api/v1/admin/parts?sort=cost&order=asc")
            assert response.status_code == 200

            data = response.get_json()
            parts = data["result"]["data"]
            if len(parts) > 1:
                costs = [p.get("cost", 0) for p in parts]
                assert costs == sorted(costs), (
                    "Parts should be sorted by cost ascending"
                )

    def test_admin3_complex_filters(self):
        """Test complex filtering scenarios"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            # Test multiple filters (if supported)
            response = self.client.get(
                "/api/v1/admin/structures?filter_customer_id=2&filter_active=1"
            )
            assert response.status_code == 200

            data = response.get_json()
            structures = data["result"]["data"]
            # All returned structures should match filters
            for structure in structures:
                assert structure.get("customer_id") == 2
                assert structure.get("active") in [True, 1, "1"]

    def test_admin3_response_format_consistency(self):
        """Test that all API responses follow consistent format"""
        with self.client:
            # Login as admin
            self.client.post(
                "/login",
                data={"email": self.admin_user.email, "password": "password123"},
                follow_redirects=True,
            )

            resources = ["users", "customers", "structures", "parts", "roles"]

            for resource in resources:
                # Test list endpoint
                list_response = self.client.get(f"/api/v1/admin/{resource}")
                assert list_response.status_code == 200

                list_data = list_response.get_json()
                assert "result" in list_data
                assert "data" in list_data["result"]
                assert "total" in list_data["result"]
                assert isinstance(list_data["result"]["data"], list)
                assert isinstance(list_data["result"]["total"], int)

                # Test single item endpoint (if items exist)
                if list_data["result"]["data"]:
                    item_id = list_data["result"]["data"][0]["id"]
                    item_response = self.client.get(
                        f"/api/v1/admin/{resource}/{item_id}"
                    )
                    assert item_response.status_code == 200

                    item_data = item_response.get_json()
                    assert "result" in item_data
                    assert "id" in item_data["result"]
                    assert item_data["result"]["id"] == item_id
