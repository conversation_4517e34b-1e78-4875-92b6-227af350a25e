from decimal import Decimal

from dash import no_update
from shared.models.models_bom import Part
from shared.models.models_work_order import (
    WorkOrder,
    WorkOrderPart,
)

from app import config
from app.dashapp.callbacks.work_order import (
    clone_work_order,
    work_order_form_submit,
)
from tests.conftest import ijack_context


def test_us_work_order(all_ijack):
    """Test that the US work order is created correctly"""

    flask_app, flask_test_client, dash_app, db = all_ijack

    # For filtering after the test, to see if the work order was created correctly
    invoice_summary_value = "test\nSean is the best\nWO n/a\nAFE n/a\nPO n/a\nRepair: Gateway. SN 9999. 9999.\n\n\n"

    signature_base64 = "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MDAiIGhlaWdodD0iMzAwIj48cmVjdCB3aWR0aD0iNjAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0id2hpdGUiLz48cGF0aCBkPSJNNzQuNSwyMDYuNjAwMDA2MTAzNTE1NjIgTDg3LjUsMTg3LjYwMDAwNjEwMzUxNTYyIEwxMzkuNSwxNDIuNjAwMDA2MTAzNTE1NjIgTDIwMS41LDkxLjYwMDAwNjEwMzUxNTYyIEwxODIuNSw5MS42MDAwMDYxMDM1MTU2MiBMMTQzLjUsMTAwLjYwMDAwNjEwMzUxNTYyIEwyNS41LDE0Mi42MDAwMDYxMDM1MTU2MiBMNC41LDE1Ni42MDAwMDYxMDM1MTU2MiBMMzMuNSwxNjYuNjAwMDA2MTAzNTE1NjIgTDE1My41LDE4OC42MDAwMDYxMDM1MTU2MiBMMTUzLjUsMTkyLjYwMDAwNjEwMzUxNTYyIEwxNDUuNSwyMDQuNjAwMDA2MTAzNTE1NjIgTDExMC41LDIzMS42MDAwMDYxMDM1MTU2MiBMMTA5LjUsMjMwLjYwMDAwNjEwMzUxNTYyIEwxMzUuNSwxODYuNjAwMDA2MTAzNTE1NjIgTDE1MC41LDE2OC42MDAwMDYxMDM1MTU2MiBMMTcwLjUsMTU1LjYwMDAwNjEwMzUxNTYyIEwxNzMuNSwxNTUuNjAwMDA2MTAzNTE1NjIgTDE3NS41LDE3My42MDAwMDYxMDM1MTU2MiBMMTcyLjUsMTkyLjYwMDAwNjEwMzUxNTYyIEwxNTkuNSwyMTYuNjAwMDA2MTAzNTE1NjIgTDE1Ny41LDIxNi42MDAwMDYxMDM1MTU2MiBMMTc2LjUsMTk4LjYwMDAwNjEwMzUxNTYyIEwyMTAuNSwxODEuNjAwMDA2MTAzNTE1NjIgTDIxMS41LDE4OS42MDAwMDYxMDM1MTU2MiBMMjQ3LjUsMTg4LjYwMDAwNjEwMzUxNTYyIEwyNTUuNSwxOTkuNjAwMDA2MTAzNTE1NjIgTDI2NS41LDIwNi42MDAwMDYxMDM1MTU2MiBMMjg2LjUsMjEyLjYwMDAwNjEwMzUxNTYyIEwyOTcuNSwyMTIuNjAwMDA2MTAzNTE1NjIgTDMwMy41LDIwNy42MDAwMDYxMDM1MTU2MiAiIHN0cm9rZT0iIzQ0NCIgc3Ryb2tlLXdpZHRoPSI0IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTM2MS41LDExNi42MDAwMDYxMDM1MTU2MiBMMzI1LjUsMTE3LjYwMDAwNjEwMzUxNTYyIEwzMTEuNSwxMjYuNjAwMDA2MTAzNTE1NjIgTDMxNi41LDE0Mi42MDAwMDYxMDM1MTU2MiBMMzI0LjUsMTU0LjYwMDAwNjEwMzUxNTYyIEwzMjguNSwxNTguNjAwMDA2MTAzNTE1NjIgTDMzNS41LDE2MC42MDAwMDYxMDM1MTU2MiAiIHN0cm9rZT0iIzQ0NCIgc3Ryb2tlLXdpZHRoPSI0IiBmaWxsPSJub25lIi8+PC9zdmc+"

    with ijack_context(flask_app, logged_in_cust=True):
        # Check if the part already exists, create it if it doesn't
        test_part = db.session.get(Part, config.PART_ID_PART_0)
        if test_part is None:
            test_part = Part(
                id=config.PART_ID_PART_0,
                part_num="0",
                description="Anything you want (miscellaneous)",
                cost_cad=1.0,
                cost_usd=1.0,
                msrp_mult_cad=2.0,
                msrp_mult_usd=2.0,
                msrp_cad=2.0,
                msrp_usd=2.0,
                transfer_mult_cad_dealer=1.5,
                transfer_mult_usd_dealer=1.5,
                transfer_mult_inc_to_corp=0.8,
                warehouse_mult=1.2,
                dealer_cost_cad=1.5,
                dealer_cost_usd=1.5,
                ijack_corp_cost=0.8,
                is_usd=False,
                is_soft_part=False,
                is_hard_part=False,
            )
            db.session.add(test_part)
            db.session.commit()
        (
            modal_open,
            title,
            work_order_modal_title_style,
            body,
            work_order_modal_view_all_btn_class_name,
            work_order_modal_create_new_btn_children,
            store_work_order_id_to_clone_data,
            work_order_modal_clone_card_style,
            edit_work_order_id_data,
        ) = work_order_form_submit(
            work_order_submit_store_data=True,
            work_order_approved_value=True,
            work_order_date_service_value="2021-09-01",
            work_order_service_type_value=config.SERVICE_TYPE_ID_REPAIR,
            work_order_currency_id_value=config.CURRENCY_ID_USD,
            work_order_creator_company_id_data=config.CUSTOMER_ID_IJACK_INC,
            work_order_customer_value=config.CUSTOMER_ID_IJACK_CORP,
            work_order_users_value=[config.USER_ID_SEAN],
            work_order_users_sales_value=[config.USER_ID_SEAN],
            work_order_requested_by_dropdown_value=None,
            work_order_service_crew_value="Sean McCarthy",
            work_order_company_rep_dropdown_value=config.USER_ID_SEAN,
            work_order_location_value="Calgary",
            work_order_model_types_value=[config.MODEL_TYPE_ID_XFER_1235_DUAL],
            work_order_service_required_value="Service required",
            work_order_is_warranty_value=False,
            # work_order_is_warranty_reason_value="n/a",
            # work_order_picker_truck_value=False,
            # work_order_crew_truck_value=False,
            # work_order_man_lift_value=False,
            # work_order_trailer_value=False,
            work_order_work_done_value="Work done",
            work_order_customer_po_value="PO123",
            work_order_cust_work_order_value="CWO123",
            work_order_afe_value="AFE123",
            work_order_invoice_summary_value=invoice_summary_value,
            work_order_structures_value=[config.STRUCTURE_GING_TEST_CALGARY],
            work_order_structure_slave_value=None,
            work_order_power_units_value=[config.POWER_UNIT_ID_GING_TEST_CALGARY],
            work_order_province_id_value=config.PROVINCE_ID_AB,
            work_order_zip_code_id_value=config.ZIP_CODE_ID_WILLISTON,
            # work_order_sales_tax_id_value,
            work_order_sales_tax_rate_value=5,
            work_order_gst_rate_value=0,
            work_order_pst_rate_value=0,
            work_order_county_id_value=config.COUNTY_ID_HARRISON,
            work_order_city_id_value=config.CITY_ID_WILLISTON,
            work_order_subtotal_store_data=1.99,
            work_order_discount_pct_value=0,
            work_order_subtotal_after_discount_store_data=1.99,
            work_order_sales_tax_store_data=0.0995,
            work_order_gst_amount_store_data=0,
            work_order_pst_amount_store_data=0,
            work_order_total_store_data=2.0895,
            work_order_parts_table_ag_rowData=[
                {
                    "delete": "",
                    "part_id": str(config.PART_ID_PART_0),
                    "part_num": "0",  # Add the actual part number for validation
                    "description": "Anything you want (miscellaneous)",
                    "structure_id": str(config.STRUCTURE_ID_GATEWAYS),
                    "warehouse_id": str(config.WAREHOUSE_ID_MOOSOMIN),
                    "quantity": 1,
                    "price": 1.99,
                    "cost_before_tax": 1.99,
                    "field_tech_id": str(config.USER_ID_SEAN),
                },
                {
                    "delete": "",
                    "part_id": "",
                    "part_num": "",
                    "description": "",
                    "structure_id": "",
                    "warehouse_id": "",
                    "quantity": "",
                    "price": "",
                    "cost_before_tax": "",
                },
            ],
            edit_work_order_id_data=None,
            # Upload files
            work_order_file_upload_data_table_data=[
                {
                    "file_name": "robots.txt",
                    "file_type": "data:text/plain;base64",
                    "file_bytes": "VXNlci1hZ2VudDogKgo=",
                }
            ],
            # Base64 image string
            signature_store_data=signature_base64,
            work_order_signature_name_value="Sean McCarthy",
            work_order_signature_date_date="2021-09-01",
            work_order_country_id_value=config.COUNTRY_ID_USA,
            work_order_is_quote_value=True,
            work_order_is_tax_exempt_value=False,
        )

        assert modal_open is True, "Confirmation modal should be open"
        assert title == "Success!"
        assert work_order_modal_title_style == {}, "Title should be styled correctly"
        # The body is a list containing an HTML P element and an inventory report
        assert isinstance(body, list), "Body should be a list"
        assert len(body) >= 1, "Body should have at least one element"
        assert hasattr(body[0], "children"), "First element should be an HTML element"
        assert body[0].children == "Work order created", (
            "First element should say 'Work order created'"
        )
        assert work_order_modal_view_all_btn_class_name == ""
        assert work_order_modal_create_new_btn_children == "Create New Work Order"
        assert store_work_order_id_to_clone_data is None
        assert work_order_modal_clone_card_style is no_update
        assert isinstance(float(edit_work_order_id_data), float), (
            "Edit work order ID should be a float"
        )

        work_order = (
            db.session.query(WorkOrder)
            .filter_by(
                invoice_summary=invoice_summary_value,
                customer_id=config.CUSTOMER_ID_IJACK_CORP,
                currency_id=config.CURRENCY_ID_USD,
            )
            .first()
        )
        assert work_order is not None, "Work order should be created"
        assert str(work_order.id) == edit_work_order_id_data
        # assert isinstance(work_order, WorkOrder)
        assert work_order.creator_id == config.USER_ID_TEST_CUSTOMER
        assert work_order.invoice_approval_req is True
        assert work_order.is_quote is True
        assert work_order.signature_svg == signature_base64
        assert work_order.signature_name == "Sean McCarthy"

        # Check the work order parts
        work_order_parts = work_order.work_order_parts_rel
        assert len(work_order_parts) == 1
        work_order_part = work_order_parts[0]
        # assert isinstance(work_order_part, WorkOrderPart)
        assert work_order_part.quantity == Decimal("1")
        assert work_order_part.price == Decimal("1.99")
        assert work_order_part.sales_tax_rate == Decimal("5")
        assert work_order_part.cost_before_tax == Decimal("1.99")
        assert work_order_part.parts_rel.id == config.PART_ID_PART_0
        assert work_order_part.parts_rel.part_num == "0"

        # Check the work order file uploaded work_order.work_order_upload_files_rel
        work_order_files = work_order.work_order_upload_files_rel
        assert len(work_order_files) == 1
        work_order_file = work_order_files[0]
        # assert isinstance(work_order_file, WorkOrderUploadFile)
        assert work_order_file.file_name == "robots.txt"
        assert work_order_file.file_type == "data:text/plain;base64"
        # assert work_order_file.file_bytes == "VXNlci1hZ2VudDogKgo="
        assert work_order_file.file_bytes == b"User-agent: *\n"
        assert work_order_file.work_order_id == work_order.id

        # Delete the work order to clean up
        db.session.delete(work_order)
        db.session.commit()

    return


def test_clone_work_order(
    all_ijack,
):
    """Test make_units_structures_radios Dash callback with a Teine unit from an email URL link"""

    flask_app, flask_test_client, dash_app, db = all_ijack

    with ijack_context(flask_app, logged_in_admin=True):
        # First make the US IJACK CORP work order, then clone it to IJACK Inc Canada

        work_order_id = 7863454
        work_order_model = WorkOrder(
            id=work_order_id,
            customer_id=config.CUSTOMER_ID_IJACK_CORP,
            currency_id=config.CURRENCY_ID_USD,
            creator_id=config.USER_ID_SEAN,
            creator_company_id=config.CUSTOMER_ID_IJACK_INC,  # Add missing creator_company_id
            sales_tax_rate=0.05,
            subtotal=100,
            sales_tax=5,
            total=105,
            service_type_id=config.SERVICE_TYPE_ID_REPAIR,
            country_id=config.COUNTRY_ID_USA,
            warehouse_id=config.WAREHOUSE_ID_MOOSOMIN,  # Add missing warehouse_id
            # work_order_parts_rel=[],
        )
        db.session.add(work_order_model)

        # These parts won't be cloned
        dont_clone_part_nums = [
            "050-0500",
            "050-0501",
            "050-0510",
            "050-0520",
            "050-0530",
            "050-0540",
            "050-0550",
            "050-0560",
            "050-0570",
            "050-0573",
        ]
        # These parts will be cloned
        parts_to_add = dont_clone_part_nums.copy()
        parts_to_add.append("XFER 2270")

        for part_num in parts_to_add:
            part_model = (
                db.session.query(Part).filter(Part.part_num == part_num).first()
            )

            # Create the part if it doesn't exist
            if part_model is None:
                part_model = Part(
                    part_num=part_num,
                    description=f"Test part {part_num}",
                    cost_cad=5.0,
                    cost_usd=5.0,
                    msrp_mult_cad=2.0,
                    msrp_mult_usd=2.0,
                    msrp_cad=10.0,
                    msrp_usd=10.0,
                    transfer_mult_cad_dealer=1.5,
                    transfer_mult_usd_dealer=1.5,
                    transfer_mult_inc_to_corp=0.8,
                    warehouse_mult=1.2,
                    dealer_cost_cad=7.5,
                    dealer_cost_usd=7.5,
                    ijack_corp_cost=0.8,
                    is_usd=False,
                    is_soft_part=False,
                    is_hard_part=False,
                )
                db.session.add(part_model)
                db.session.flush()  # Get the ID without committing
            else:
                # These ones previously weren't populated (they are required now!)
                part_model.msrp_cad = 10
                part_model.msrp_usd = 10
                part_model.transfer_mult_inc_to_corp = 0.8

            work_order_part_model = WorkOrderPart(
                work_order_id=work_order_id,
                description="",
                quantity=1,
                price=10,
                sales_tax_rate=0.05,
                warehouse_id=config.WAREHOUSE_ID_MOOSOMIN,  # Add missing warehouse_id
                # part_id=part_model.id,
                parts_rel=part_model,
            )
            db.session.add(work_order_part_model)

            work_order_model.work_order_parts_rel.append(work_order_part_model)

        db.session.commit()

        work_order_id_new: int = clone_work_order(work_order=work_order_model)

        original_work_order = (
            db.session.query(WorkOrder).filter(WorkOrder.id == work_order_id).first()
        )
        assert original_work_order.id == work_order_id
        assert original_work_order.creator_id == config.USER_ID_SEAN
        assert original_work_order.sales_tax_rate == Decimal("0.05")
        assert original_work_order.subtotal == Decimal("100")
        assert original_work_order.sales_tax == Decimal("5")
        assert original_work_order.total == Decimal("105")
        assert original_work_order.currency_id == config.CURRENCY_ID_USD

        cloned_work_order = (
            db.session.query(WorkOrder)
            .filter(WorkOrder.id == work_order_id_new)
            .first()
        )
        assert cloned_work_order.id == work_order_id_new
        assert cloned_work_order.creator_id == config.USER_ID_SEAN
        # Don't charge sales taxes for inter-company transfers
        assert cloned_work_order.sales_tax_rate == Decimal("0")
        # Recalculate the subtotal, taxes, etc based on the remaining parts
        assert cloned_work_order.subtotal == Decimal("8.0")
        assert cloned_work_order.sales_tax == Decimal("0")
        assert cloned_work_order.total == Decimal("8.0")
        # The Canadian INC work order also uses USD, since Olga wants INC to have some USD to pay US suppliers
        assert cloned_work_order.currency_id == config.CURRENCY_ID_USD

        # No more parts left to clone
        assert len(cloned_work_order.work_order_parts_rel) == 1
        for part_model in work_order_model.work_order_parts_rel:
            assert part_model not in cloned_work_order.work_order_parts_rel
