from datetime import datetime

import pytz

from app import config
from app.dashapp.utils import get_datetime_from_parts
from tests.conftest import ijack_context


def test_service_clock_america_regina():
    """Test that the 'America/Regina' timezone is set correctly"""

    dt_str = "2024-03-04 12:00:00"
    clock_in_timestamp_regina = datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
    tz_regina = pytz.timezone("America/Regina")

    # .replace won't work!
    # https://stackoverflow.com/questions/36156675/behavior-of-pytz-timezones-is-inconsistent
    # https://pythonhosted.org/pytz/
    # clock_in_regina_aware = clock_in_timestamp_regina.replace(tzinfo=tz_regina)
    clock_in_regina_aware = tz_regina.localize(clock_in_timestamp_regina)

    # Change Regina time to UTC time
    clock_in_utc_aware = clock_in_regina_aware.astimezone(pytz.UTC)
    # Convert back to Regina time
    clock_in_timestamp_local = clock_in_utc_aware.astimezone(tz_regina)
    # Change Regina time to UTC time
    clock_in_utc_again = clock_in_timestamp_local.astimezone(pytz.UTC)

    # Check that the time is the same
    assert clock_in_timestamp_local == clock_in_regina_aware
    assert clock_in_timestamp_local.hour == 12
    assert clock_in_timestamp_local.minute == 0
    assert clock_in_timestamp_local.second == 0
    assert clock_in_regina_aware.hour == 12
    assert clock_in_regina_aware.minute == 0
    assert clock_in_regina_aware.second == 0

    assert clock_in_utc_again == clock_in_utc_aware
    assert clock_in_utc_again.hour == 18
    assert clock_in_utc_again.minute == 0
    assert clock_in_utc_again.second == 0
    assert clock_in_utc_aware.hour == 18
    assert clock_in_utc_aware.minute == 0


def test_get_datetime_from_parts(all_ijack):
    """Test the get_datetime_from_parts function"""

    flask_app, flask_test_client, dash_app, db = all_ijack

    date_str = "2024-03-04"
    time_str = "12:15:30"

    with ijack_context(flask_app):
        dt: datetime = get_datetime_from_parts(
            date_str=date_str,
            time_str=time_str,
            time_zone_id=config.TIME_ZONE_ID_AMERICA_REGINA,
        )

    assert dt.year == 2024
    assert dt.month == 3
    assert dt.day == 4
    assert dt.hour == 12
    assert dt.minute == 15
    assert dt.second == 30
    assert dt.tzinfo.zone == "America/Regina"
    # Not "LMT"!
    assert dt.tzinfo.tzname(dt) == "CST"
    assert dt.tzinfo.utcoffset(dt).total_seconds() == -21600
    assert dt.tzinfo.dst(dt).total_seconds() == 0
