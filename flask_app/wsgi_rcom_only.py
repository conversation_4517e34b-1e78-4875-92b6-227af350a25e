# print("Starting: importing create_dash_app, create_flask_app, and packages...")
try:
    from app import create_dash_app, create_flask_app, flask_always_needs_last
except Exception as err:
    print(f"ERROR: {err}")
    print("ERROR: Unable to import create_dash_app factory function! Exiting...")
    raise

# Now we can safely import Flask and other dependencies
import logging
import os
import time
import ssl
from pathlib import Path

from werkzeug.debug import DebuggedApplication

# print("Creating app...")
try:
    flask_app = create_flask_app(config_name=None)
    dash_app = create_dash_app(flask_app=flask_app)
    flask_always_needs_last(flask_app=flask_app)
except Exception as err:
    print(f"ERROR: {err}")
    print("ERROR: Unable to create Dash app! Exiting...")
    raise

flask_app.logger.warning("Flask app is ready ✅")
# The dash_app.server IS the flask_app above
dash_app.server.logger.warning("Dash app is ready ✅")

# Configure logging first
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


# Optional: Add WebSocket support if needed
class WebSocketHandler:
    """Custom handler class for WebSocket support with longer timeouts"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timeout = 300  # 5 minutes for debugging sessions


def get_ssl_context():
    """
    Create an SSL context for development with proper certificate handling.
    Returns None if SSL is not configured or if in production.
    """
    if os.getenv("FLASK_CONFIG") not in ("development", "wsl"):
        return None

    try:
        # Get certificate paths from environment or use defaults
        cert_path = os.environ.get("SSL_CERT_PATH", "./certs/cert.pem")
        key_path = os.environ.get("SSL_KEY_PATH", "./certs/key.pem")

        # Convert to Path objects for better path handling
        cert_path = Path(cert_path)
        key_path = Path(key_path)

        # Verify certificate files exist
        if not cert_path.exists() or not key_path.exists():
            logger.warning(f"SSL certificates not found at {cert_path} or {key_path}")
            return None

        # Create SSL context
        context = ssl.create_default_context(purpose=ssl.Purpose.CLIENT_AUTH)
        context.load_cert_chain(certfile=str(cert_path), keyfile=str(key_path))

        # Configure SSL context for development
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE

        logger.info("SSL context created successfully")
        return context

    except Exception as e:
        logger.error(f"Failed to create SSL context: {e}")
        return None


def run_development_server(app):
    """
    Run the development server with proper gevent and debugger support.
    Now includes SSL context handling for HTTPS support.
    """
    if os.environ.get("USE_GEVENT") == "True":
        try:
            from gevent.pywsgi import WSGIServer

            # Wrap the application in the debugger if needed
            if app.debug:
                app.wsgi_app = DebuggedApplication(app.wsgi_app, evalex=True)

            # Get SSL context for HTTPS
            ssl_context = get_ssl_context()

            # Create and configure the server with SSL if available
            http_server = WSGIServer(
                ("0.0.0.0", 5000),
                app,
                log=app.logger,
                error_log=app.logger,
                ssl_context=ssl_context,
                # Optional: Increase timeout for debugging
                handler_class=WebSocketHandler if hasattr(app, "websocket") else None,
            )

            # Monitor request durations
            def log_request(env, start_response):
                start_time = time.time()

                def custom_start_response(status, headers, exc_info=None):
                    duration = time.time() - start_time
                    if duration > 5:
                        app.logger.warning(
                            f"Slow request to {env.get('PATH_INFO')} took {duration:.2f}s"
                        )
                    return start_response(status, headers, exc_info)

                return app(env, custom_start_response)

            # Log server startup details
            protocol = "HTTPS" if ssl_context else "HTTP"
            logger.info(f"Starting gevent WSGIServer with {protocol}...")

            http_server.serve_forever()

        except Exception as e:
            logger.error(f"Failed to start gevent server: {e}")
            raise
    else:
        # Regular Flask development server with SSL context
        ssl_context = get_ssl_context()
        app.run(host="0.0.0.0", port=5000, ssl_context=ssl_context)


if __name__ == "__main__":
    # Initialize the application
    logger.info("Initializing Flask application...")

    # Start the server
    run_development_server(flask_app)
