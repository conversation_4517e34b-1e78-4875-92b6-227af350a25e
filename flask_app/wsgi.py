# print("Starting: importing create_dash_app, create_flask_app, and packages...")
try:
    from app import (
        create_dash_app,
        create_flask_app,
        flask_always_needs_last,
        register_flask_admin,
    )
except Exception as err:
    print(f"ERROR: {err}")
    print("ERROR: Unable to import create_dash_app factory function! Exiting...")
    raise


def init_test_db(flask_app) -> None:
    """Check if the test database should be initialized"""
    import os

    if (
        os.getenv("FLASK_CONFIG", None) == "testing"
        and os.getenv("INIT_TEST_DB", "no") == "yes"
    ):
        from tests.conftest import init_database_func

        print("Initializing test database 🧪...")
        init_database_func(flask_app=flask_app)

    return None


# print("Creating app...")
try:
    flask_app = create_flask_app(config_name=None)
    dash_app = create_dash_app(flask_app=flask_app)
    register_flask_admin(flask_app=flask_app)
    flask_always_needs_last(flask_app=flask_app)
    init_test_db(flask_app=flask_app)
except Exception as err:
    print(f"ERROR: {err}")
    print("ERROR: Unable to create Dash app! Exiting...")
    raise

flask_app.logger.warning("Flask app is ready ✅")
# The dash_app.server IS the flask_app above
dash_app.server.logger.warning("Dash app is ready ✅")
