import warnings

from flask_admin import Admin
from flask_admin.theme import Bootstrap4Theme
from shared.models.models import (
    Alert,
    CustSubGroup,
    Gw,
    PowerUnit,
    ReportEmailHourly,
    ReportEmailOpHours,
)

from app.flask_admin.base import SecuredAdminCustIndexView
from app.flask_admin.contrib.sqla.sqla_views_cust import (
    AlertViewAdminCust,
    CustSubGroupViewAdminCust,
    GwViewAdminCust,
    PowerUnitViewAdminCust,
    ReportEmailHourlyViewAdminCust,
    ReportEmailOpHoursViewAdminCust,
    StructureViewAdminCust,
    UserViewAdminCust,
)
from app.flask_admin.menu import SecuredMenuLinkAdminCust
from app.models.models import Structure, User

flask_admin_cust = Admin(
    name="Customer Admin",
    # template_mode="bootstrap4",
    # The URL will be myijack.com/admin_cust/
    # endpoint refers to the blueprint [e.g. url_for('admin_cust.index')] where index() is the function/view
    index_view=SecuredAdminCustIndexView(
        name="Admin", endpoint="admin_cust", url="/admin_cust"
    ),
    # What the URL will be for the static files (CSS, JS, etc.)
    # static_url_path="/static/admin",
    theme=Bootstrap4Theme(),
)


def admin_cust_views(app, db):
    """Add the database 'models' (tables) to the admin page, for customer admins only"""

    # For customer admins only #####################################################################################
    # Create the secured flask_admin instance with index_view=SecuredAdminCustIndexView()
    flask_admin_cust.init_app(app)

    # flask_admin_cust.init_app(app)
    # Add model (database table) views to the myijack.com/admin page
    with warnings.catch_warnings():
        # The "edit" page doesn't show the "form_password" and "form_password_verify" fields,
        # which are only shown when creating a new user. No need for a warning.
        warnings.filterwarnings(
            action="ignore", message="Fields missing from ruleset", category=UserWarning
        )
        flask_admin_cust.add_view(
            UserViewAdminCust(
                User,
                db.session,
                # category="Users",
                name="Users",
                # endpoint="admin_cust.users",
                endpoint="users_",
                url="/admin_cust/users_",
            )
        )
    flask_admin_cust.add_view(
        CustSubGroupViewAdminCust(
            CustSubGroup,
            db.session,
            # category="Groups",
            name="Groups",
            endpoint="groups_",
            url="/admin_cust/groups",
        )
    )
    flask_admin_cust.add_view(
        StructureViewAdminCust(
            Structure,
            db.session,
            category="Units",
            name="Structure Info and Remote Control Permissions",
            endpoint="structure_info_and_remote_control",
            url="/admin_cust/structure_info_and_remote_control",
        )
    )
    flask_admin_cust.add_view(
        PowerUnitViewAdminCust(
            PowerUnit,
            db.session,
            category="Units",
            name="Power Unit and Alert Settings",
            endpoint="power_unit_and_alert_settings",
            url="/admin_cust/power_unit_and_alert_settings",
        )
    )
    flask_admin_cust.add_view(
        GwViewAdminCust(
            Gw,
            db.session,
            category="Units",
            name="Cellular Gateway Information",
            endpoint="cellular_gateway_info",
            url="/admin_cust/cellular_gateway_info",
        )
    )

    # Now the "Alerts menu"
    flask_admin_cust.add_view(
        AlertViewAdminCust(
            Alert,
            db.session,
            # category="Alerts",
            name="Alerts",
            endpoint="alerts_",
            url="/admin_cust/alerts",
        )
    )

    # Reports menu
    flask_admin_cust.add_view(
        ReportEmailHourlyViewAdminCust(
            ReportEmailHourly,
            db.session,
            category="Reports",
            name="Hourly Update Email",
            endpoint="hourly_update_",
            url="/admin_cust/hourly_update",
        )
    )

    flask_admin_cust.add_view(
        ReportEmailOpHoursViewAdminCust(
            ReportEmailOpHours,
            db.session,
            category="Reports",
            name="Operating Hours Email",
            endpoint="operating_hours_email_",
            url="/admin_cust/operating_hours_email",
        )
    )

    # Add other links to the admin page
    with app.app_context():
        # flask_admin_cust.add_link(
        #     SecuredMenuLinkAdminCust(
        #         category="Users",
        #         name="Register User",
        #         endpoint=".create_view",
        #     )
        # )

        # flask_admin_cust.add_link(
        #     SecuredMenuLinkAdminCust(
        #         category="Groups",
        #         name="Create Group",
        #         # This one points to a view we manually created in admin_cust.py
        #         endpoint="create_sub_group",
        #         url="/admin_cust/create_sub_group",
        #     )
        # )

        flask_admin_cust.add_link(
            SecuredMenuLinkAdminCust(name="Logout", endpoint="auth.logout")
        )
