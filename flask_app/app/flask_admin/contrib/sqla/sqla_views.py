import os
from copy import deepcopy
from datetime import date, datetime
from gettext import gettext
from math import ceil
from typing import List

import pytz
from flask import abort, current_app, flash, redirect, request, url_for
from flask_admin import expose
from flask_admin._compat import iteritems, string_types
from flask_admin.contrib.sqla import ModelView, tools
from flask_admin.contrib.sqla.ajax import QueryAjaxModelLoader
from flask_admin.contrib.sqla.filters import Date<PERSON><PERSON><PERSON><PERSON>Filter, FilterEqual, FilterLike
from flask_admin.form.fields import DateTimeField as DateTimeFieldFA
from flask_admin.helpers import get_redirect_target
from flask_admin.model import template
from flask_admin.model.helpers import get_mdict_item_or_list
from flask_login import current_user
from markupsafe import Markup
from shared.models.models import (
    Alert,
    AlertsSentMaintEmailType,
    Barrel,
    CheckValve,
    City,
    CompressionPattern,
    Country,
    County,
    Currency,
    Customer,
    CustSubGroup,
    DayOfWeek,
    ErrorLog,
    GatewayType,
    Gw,
    GwNotConnectedDontWorry,
    GwTestedCellular,
    Hour,
    HydPistonType,
    MapAbbrevItem,
    PackingGland,
    PowerUnit,
    PowerUnitFixedIPNetwork,
    PowerUnitModbusNetwork,
    Province,
    ReportEmailOpHoursType,
    Rod,
    Role,
    ShuttleValve,
    SurfacePattern,
    TimeZone,
    UnitType,
    WebsiteView,
    ZipCode,
)
from shared.models.models_bom import (
    BOMDGAS,
    BOMBasePowerUnit,
    BOMBasePowerUnitPowerUnitType,
    BOMDGASModelType,
    BOMPowerUnit,
    BOMPowerUnitPowerUnitType,
    BOMPumpTop,
    BOMPumpTopModelType,
    BOMStructure,
    BOMStructureModelType,
    CycleCount,
    ModelType,
    ModelTypePart,
    ModelTypePartPMSealKit,
    Part,
    PartCategory,
    PartFilter,
    PowerUnitType,
    PowerUnitTypePart,
    PowerUnitTypePartFilter,
    Warehouse,
    WarehouseLocation,
)
from shared.models.models_work_order import (
    MaintenanceType,
    Service,
    ServiceType,
    WorkOrder,
    WorkOrderStatus,
)
from sqlalchemy import func
from sqlalchemy.inspection import inspect
from wtforms import TextAreaField
from wtforms.validators import DataRequired, Email

from app import db, get_user_role_ids, is_admin
from app.auth.forms import validate_phone
from app.config import (
    CUSTOMER_ID_DEMO,
    CUSTOMER_ID_IJACK_CORP,
    CUSTOMER_ID_IJACK_INC,
    IJACK_CUST_IDS_LIST,
    MODEL_TYPE_ID_EGAS_823,
    MODEL_TYPE_ID_EGAS_828,
    MODEL_TYPE_ID_VRU_823,
    MODEL_TYPE_ID_VRU_828,
    ROLE_ID_BOM_MASTER_PARTS_TABLES,
    ROLE_ID_CUSTOMER,
    ROLE_ID_IJACK_ADMIN,
    ROLE_ID_IJACK_HUMAN_RESOURCES,
    ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
    ROLE_ID_VIEW_WORK_ORDER_TOTALS,
    TIME_ZONE_ID_AMERICA_REGINA,
    UNIT_TYPE_ID_DGAS,
    USER_ID_RICHIE,
)
from app.dashapp.callbacks.service_request import submit_service_request
from app.dashapp.utils import get_or_update_gps
from app.flask_admin.contrib.sqla.ajax import (
    QueryAjaxModelLoaderAdmin,
    QueryAjaxModelLoaderAdminCust,
)
from app.flask_admin.contrib.sqla.form import WorkOrderInlineModelConverter
from app.flask_admin.fields import ColorField
from app.flask_admin.forms import SecureFormCustom
from app.flask_admin.model.form import WorkOrderPartInlineForm
from app.flask_admin.utils import (
    ImageUploadFieldDB,
    alerts_formatter,
    clone_model,
    datetime_formatter_sk_time,
    email_formatter,
    format_gateway_power_unit,
    format_like_integer,
    get_columns_for_field,
    get_formatter,
    image_thumbnail,
    many_to_many_formatter,
    money_formatter,
    preserve_line_breaks,
    structures_formatter,
    suction_range_formatter,
    user_formatter,
    user_phone_num_formatter,
)
from app.flask_admin.widgets import XEditableWidgetAJAX
from app.models.models import Structure, User
from app.utils.complex import (
    decimal_formatter,
    getattrd,
    remove_unnecessary_decimal,
    send_new_user_sms,
    send_new_user_whatsapp,
)
from app.utils.simple import (
    create_jwt_encoded_token,
    generate_random_string,
    utcnow_naive,
)


def timezone_filtering_function():
    """Ensures a user can't see the 'test' timezones"""
    return TimeZone.query.filter(TimeZone.can_show_to_customers.is_(True))


class MyModelView(ModelView):
    """Customized model view for Flask-Admin page (for database tables)
    https://flask-admin.readthedocs.io/en/latest/introduction/#
    """

    def __init__(self, *args, **kwargs):
        """
        Initialize the view with the given model.
        role_ids_accepted: list of role_ids that are allowed to view this page
        role_ids_rejected: list of role_ids that are not allowed to view this page
        """
        self.role_ids_accepted = kwargs.pop("role_ids_accepted", list())
        self.role_ids_rejected = kwargs.pop("role_ids_rejected", list())

        # Enable CSRF protection for Flask-Admin forms in production and when testing with CSRF enabled
        try:
            if os.getenv("FLASK_CONFIG", "production") in ("production", "staging") or (
                current_app and current_app.config.get("TEST_CSRF", False)
            ):
                self.form_base_class = SecureFormCustom
        except RuntimeError:
            # No application context available, skip CSRF configuration
            pass

        # Get current page size options as list so we can modify it
        self._page_size_options = list(
            getattr(self, "page_size_options", (20, 50, 100))
        )

        # Get configured page size
        self._page_size = getattr(self, "page_size", 20)

        # If can_set_page_size is True and page_size isn't in options, add it so we don't see a warning
        if (
            getattr(self, "can_set_page_size", False)
            and self._page_size not in self._page_size_options
        ):
            # Add the page size
            self._page_size_options.append(self._page_size)
            # Sort the options for a clean dropdown
            self._page_size_options.sort()
            # Convert back to tuple
            self.page_size_options = tuple(self._page_size_options)

        super().__init__(
            # Change this so we can separate the Flask-Admin assets from the rest of the site
            # static_folder=DIST_FOLDER.joinpath("flask_admin/templates/bootstrap4/admin"),
            # static_folder=DIST_FOLDER,
            *args,
            **kwargs,
        )

    def is_accessible(self):
        """The user must be both authenticated, and an admin user to view these pages"""
        has_the_role: bool = True
        role_ids_accepted: list = getattr(self, "role_ids_accepted", None)
        role_ids_rejected: list = getattr(self, "role_ids_rejected", None)
        if not hasattr(current_user, "roles_rel"):
            has_the_role = False
        else:
            if role_ids_accepted:
                has_the_role = any(
                    [current_user.has_role_id(role_id) for role_id in role_ids_accepted]
                )
            if role_ids_rejected:
                has_the_role = not any(
                    [current_user.has_role_id(role_id) for role_id in role_ids_rejected]
                )
        return current_user.is_authenticated and is_admin() and has_the_role

    def inaccessible_callback(self, name, **kwargs):
        """
        Handle the response to inaccessible views.

        By default, it throws an HTTP 403 error. Override this method to customize the behaviour.
        """
        role_ids_accepted: list = getattr(self, "role_ids_accepted", None)
        role_ids_rejected: list = getattr(self, "role_ids_rejected", None)

        description_str = ""
        role_names_required = []
        role_names_rejected = []
        role_names_required_str = ""
        role_names_rejected_str = ""

        if role_ids_accepted:
            for role_id in role_ids_accepted:
                role = Role.query.get(role_id)  # Get the role object
                if role:
                    role_names_required.append(role.name)  # Add name if role exists
                else:
                    # Include the role ID in the list if the role doesn't exist
                    role_names_required.append(f"Unknown Role (ID: {role_id})")

        if role_ids_rejected:
            for role_id in role_ids_rejected:
                if current_user.has_role_id(role_id):
                    role = Role.query.get(role_id)  # Get the role object
                    if role:
                        role_names_rejected.append(role.name)  # Add name if role exists
                    else:
                        # Include the role ID in the list if the role doesn't exist
                        role_names_rejected.append(f"Unknown Role (ID: {role_id})")

        if not is_admin():
            description_str = "You must have the 'Admin' role to view the admin tables."

        if role_names_required:
            role_names_required_str = (
                f"\n\nRoles needed to view this page: {', '.join(role_names_required)}."
            )
        if role_names_rejected:
            role_names_rejected_str = f"\n\nRejected for having the following roles: {', '.join(role_names_rejected)}."

        return abort(
            403,
            description=f"{description_str} {role_names_required_str} {role_names_rejected_str}",
        )

    def _handle_view(self, name, **kwargs):
        """
        This method will be executed before calling any view method.

        It will execute the ``inaccessible_callback`` if the view is not
        accessible.

        :param name:
            View function name
        :param kwargs:
            View function arguments
        """
        if not current_user.is_authenticated:
            return redirect(url_for("dash.login", next="/admin/"))
        return super()._handle_view(name, **kwargs)

    def is_visible(self):
        """
        Override this method if you want to dynamically hide or show administrative views
        from Flask-Admin menu structure

        By default, item is visible in menu.

        Please note that item MUST be both visible and accessible to be displayed in menu.
        """
        # return self.is_accessible()
        return True

    # Set this (default is False) to True so we can apply English-human-readable filters,
    # like in the /duplicate/id route, so we can show the new record once it's been inserted.
    named_filter_urls = True

    # To add CSRF protection to the forms that are generated by ModelView instances,
    # use the SecureForm class in your ModelView subclass by specifying the form_base_class parameter

    # To disable some of the CRUD operations, set any of these boolean parameters
    # so we can save the meta_data, which needs to see the "id" field
    column_display_pk = True
    can_create = True
    can_edit = True
    can_delete = True
    can_set_page_size = True
    page_size = 50
    page_size_options = (5, 10, 20, 50, 100, 200, 500, 1000)
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        page_size = 5
    # For my custom index view function, to start on the page paginated page
    start_on_last_page = False

    column_extra_row_actions = [
        template.LinkRowAction(
            icon_class="fa fa-clone",
            # Calls the .../duplicate?id={row_id} view
            # with the row_id from the Jinja template
            url="duplicate?id={row_id}",
            title="Duplicate Record",
        ),
    ]

    # To enable csv export of the model view
    can_export = True

    # column_type_formatters = MY_DEFAULT_FORMATTERS

    # If your model has too much data to display in the list view, you can add a read-only details view by setting
    can_view_details = True

    # Or, have the add & edit forms display inside a modal window on the list page, instead of the dedicated create & edit pages
    # These are popup windows that focus your attention when
    # editing or creating table records. I think they're cool
    create_modal = True
    edit_modal = True

    # Custom templates
    list_template = "admin/model/list_extended.html"
    # # Even if we're using a popup modal, we still need to update the regular create form since it's available to the user
    # create_template = "admin/model/create_custom.html"
    # create_modal_template = "admin/model/modals/create_custom.html"
    # # Even if we're using a popup modal, we still need to update the regular edit form since it's available to the user
    # edit_template = "admin/model/edit_custom.html"
    # edit_modal_template = "admin/model/modals/edit_custom.html"

    def init_search(self):
        """
        Initialize search. Returns `True` if search is supported for this
        view.

        For SQLAlchemy, this will initialize internal fields: list of
        column objects used for filtering, etc.
        """
        if self.column_searchable_list:
            self._search_fields = []

            for name in self.column_searchable_list:
                attr, joins = tools.get_field_with_path(self.model, name)

                if not attr:
                    raise Exception("Failed to find field for search field: %s" % name)

                if tools.is_hybrid_property(self.model, name):
                    column = attr
                    if isinstance(name, string_types):
                        column.key = name.split(".")[-1]
                    self._search_fields.append((column, joins))
                else:
                    # I changed the below to make it more accommodating
                    # for column in tools.get_columns_for_field(attr):
                    for column in get_columns_for_field(attr):
                        self._search_fields.append((column, joins))

        return bool(self.column_searchable_list)

    def get_list_form(self):
        """Override this function and supply my own CustomWidget with AJAX"""
        if self.form_args:
            # get only validators, other form_args can break FieldList wrapper
            validators = dict(
                (key, {"validators": value["validators"]})
                for key, value in iteritems(self.form_args)
                if value.get("validators")
            )
        else:
            validators = None
        return self.scaffold_list_form(
            widget=XEditableWidgetAJAX(), validators=validators
        )

    def get_list_row_actions(self):
        """
        Return list of row action objects, each is instance of
        :class:`~flask_admin.model.template.BaseListRowAction`
        """
        actions = []

        if self.can_view_details:
            if self.details_modal:
                actions.append(template.ViewPopupRowAction())
            else:
                actions.append(template.ViewRowAction())

        if self.can_edit:
            if self.edit_modal:
                actions.append(template.EditPopupRowAction())
            else:
                actions.append(template.EditRowAction())

        if self.can_delete:
            actions.append(template.DeleteRowAction())

        return actions + (self.column_extra_row_actions or [])

    # Views
    @expose("/")
    def index_view(self):
        """
        List view overridden to DEFAULT to the last page,
        if no other request args have been submitted
        """
        if self.can_delete:
            delete_form = self.delete_form()
        else:
            delete_form = None

        # Grab parameters from URL
        view_args = self._get_list_extra_args()

        # Map column index to column name
        sort_column = self._get_column_by_idx(view_args.sort)
        if sort_column is not None:
            sort_column = sort_column[0]

        # Get page size
        page_size = view_args.page_size or self.page_size

        #####################################################################
        # Custom functionality to start on the last page instead of the first
        if self.start_on_last_page and len(request.args) == 0:
            # Standard request for the first page (no additional args)
            count_query = self.get_count_query() if not self.simple_list_pager else None
            # Calculate number of rows if necessary
            count = count_query.scalar() if count_query else None
            # Calculate number of pages
            if count is not None and page_size:
                num_pages = int(ceil(count / float(page_size)))
                setattr(view_args, "page", num_pages - 1)

        # End of custom code. The rest below is from the Flask-Admin package
        ############################################################################

        # Get count and data
        count, data = self.get_list(
            view_args.page,
            sort_column,
            view_args.sort_desc,
            view_args.search,
            view_args.filters,
            page_size=page_size,
        )

        list_forms = {}
        if self.column_editable_list:
            for row in data:
                list_forms[self.get_pk_value(row)] = self.list_form(obj=row)

        # Calculate number of pages
        if count is not None and page_size:
            num_pages = int(ceil(count / float(page_size)))
        elif not page_size:
            num_pages = 0  # hide pager for unlimited page_size
        else:
            num_pages = None  # use simple pager

        # Various URL generation helpers
        def pager_url(p):
            """Create URLs for the pagination links at the bottom of the view"""
            url = self._get_list_url(view_args.clone(page=p))
            if url.find("?") == -1:  # first page
                # Add back the page number if it's the first page.
                # This way we can start on the last page and still access the first page
                url += "?page=0"
            return url

        def sort_url(column, invert=False, desc=None):
            if not desc and invert and not view_args.sort_desc:
                desc = 1

            return self._get_list_url(view_args.clone(sort=column, sort_desc=desc))

        def page_size_url(s):
            if not s:
                s = self.page_size

            return self._get_list_url(view_args.clone(page_size=s))

        # Actions
        actions, actions_confirmation = self.get_actions_list()
        if actions:
            action_form = self.action_form()
        else:
            action_form = None

        clear_search_url = self._get_list_url(
            view_args.clone(
                page=0,
                sort=view_args.sort,
                sort_desc=view_args.sort_desc,
                search=None,
                filters=None,
            )
        )

        try:
            return self.render(
                self.list_template,
                data=data,
                list_forms=list_forms,
                delete_form=delete_form,
                action_form=action_form,
                # List
                list_columns=self._list_columns,
                sortable_columns=self._sortable_columns,
                editable_columns=self.column_editable_list,
                list_row_actions=self.get_list_row_actions(),
                # Pagination
                count=count,
                pager_url=pager_url,
                num_pages=num_pages,
                can_set_page_size=self.can_set_page_size,
                page_size_url=page_size_url,
                page=view_args.page,
                page_size=page_size,
                default_page_size=self.page_size,
                # Sorting
                sort_column=view_args.sort,
                sort_desc=view_args.sort_desc,
                sort_url=sort_url,
                # Search
                search_supported=self._search_supported,
                clear_search_url=clear_search_url,
                search=view_args.search,
                search_placeholder=self.search_placeholder(),
                # Filters
                filters=self._filters,
                filter_groups=self._get_filter_groups(),
                active_filters=view_args.filters,
                filter_args=self._get_filters(view_args.filters),
                # Actions
                actions=actions,
                actions_confirmation=actions_confirmation,
                # Misc
                enumerate=enumerate,
                get_pk_value=self.get_pk_value,
                get_value=self.get_list_value,
                return_url=self._get_list_url(view_args),
                # Extras
                extra_args=view_args.extra_args,
            )
        except Exception:
            current_app.logger.exception("Error in Flask Admin index_view (list view)")
            raise

    @expose("/duplicate/")
    def duplicate_record(self):
        """Make a duplicate of the current record"""

        # Grab parameters from URL
        self._get_list_extra_args()

        # Duplicate current record
        return_url = get_redirect_target() or self.get_url(".index_view")

        if not self.can_create:
            flash(
                gettext("You're not allowed to duplicate records on this page"), "error"
            )
            return redirect(return_url)

        id_ = get_mdict_item_or_list(request.args, "id")
        if id_ is None:
            flash(
                gettext("Can't find the 'id' for the record to be duplicated."), "error"
            )
            return redirect(return_url)

        old_model = self.get_one(id_)
        if old_model is None:
            flash(gettext("Record does not exist."), "error")
            return redirect(return_url)

        # Make a clone of the old model, without the primary key
        new_model, clone_msg = clone_model(old_model)

        # Add duplicate record to the database
        db.session.add(new_model)
        db.session.commit()
        flash(
            gettext(f"You have successfully duplicated that record. {clone_msg}"),
            "success",
        )

        # Now that the record has been inserted, it will have an id value.
        # Get the primary key tuple, so we can filter on it and show it.
        identity = inspect(new_model).identity

        # Filter on the id field for the identity of the new_model above
        return redirect(url_for(".index_view", flt_id_equals=identity))


class MetaDataView(MyModelView):
    """Flask-Admin view for MetaDataTbl model"""

    # # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    # column_exclude_list = ['password_hash', ]
    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    # column_hide_backrefs = False

    column_list = ("id", "id_cell", "element", "color")
    column_default_sort = "id"
    column_sortable_list = column_list
    can_set_page_size = True
    page_size = 50

    # Control the order of the columns in the forms
    form_columns = column_list
    form_choices = {"element": [("0", "text-color"), ("1", "background")]}

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customers' or 'structures' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = column_list
    column_filters = column_list

    # For a faster editing experience, enable inline editing in the list view:
    # Won't work if you add 'gateway' to this list since 'gateway' is the primary key
    column_editable_list = column_list

    column_labels = {
        "id_cell": "ID Cell",
        "element": "Element",
        "color": "Color",
    }
    column_descriptions = {
        "id_cell": "The primary key and identifier",
        "element": "The element to style (e.g. 'text color' or 'fill color'",
        "color": "The hex-color of the element we're styling",
    }


# We'll reuse the below lists/dictionaries in the inline model form for new users
alert_form_columns = (
    "users_rel",
    "power_units_rel",
    # "gateways_rel",
    "wants_sms",
    "wants_email",
    "wants_phone",
    # WhatsApp is not yet implemented
    # "wants_whatsapp",
    "wants_short_sms",
    "wants_short_email",
    "wants_short_phone",
    "heartbeat",
    "online_hb",
    "spm",
    "stboxf",
    "hyd_temp",
    "suction",
    "discharge",
    "change_suction",
    "change_dgp",
    "change_hyd_temp",
    "change_hp_delta",
    "wants_card_ml",
    # Hydraulic oil alerts
    "hyd_oil_lvl",
    "hyd_filt_life",
    # Hydraulic oil life isn't proven yet, so default it to False
    # "hyd_oil_life",
    "chk_mtr_ovld",
    "pwr_fail",
    "soft_start_err",
    "grey_wire_err",
    "ae011",
)

alert_column_labels = {
    "alert_id": "Alert ID",
    "users_rel": "User Being Alerted",
    "email": "Email",
    "users_rel.first_name": "First Name",
    "users_rel.last_name": "Last Name",
    "customers_rel.customer": "Customer",
    "users_rel.phone": "Phone",
    "power_units_rel": "Power Unit",
    "power_unit.power_unit": "Power Unit",
    "name": "Sub-Group",
    "description": "Sub-Group Description",
    "structures_rel": "Structures",
    "surface": "Surface Location",
    "downhole": "Downhole Location",
    "structure.structure": "Structure",
    "structure.surface": "Surface Location",
    "structure.downhole": "Downhole Location",
    "wants_sms": "SMS Alert",
    "users_rel.sms_stop_all": "SMS STOPPED",
    "wants_email": "Email Alert",
    "wants_phone": "Phone Alert",
    "wants_whatsapp": "WhatsApp Alert",
    "users_rel.whatsapp_stop_all": "WhatsApp STOPPED",
    "wants_short_sms": "Want Shorter SMS Message",
    "wants_short_email": "Want Shorter Email Message",
    "wants_short_phone": "Want Shorter Phone Message",
    "heartbeat": "Not Stroking Alert",
    "online_hb": "No Power/Internet Alert",
    "change_suction": "Change Detection Suction Pressure",
    "change_hyd_temp": "Change Detection Hydraulic Temperature",
    "change_dgp": "Change Detection Discharge Pressure",
    "change_hp_delta": "Change Detection Horsepower Upstroke-Downstroke Delta",
    "suction": "High Suction Pressure Alert",
    "discharge": "Low Discharge Pressure Alert",
    "spm": "Low Strokes per Minute Alert",
    "stboxf": "Stuffing Box Float Switch Alert",
    "hyd_temp": "High Hydraulic Temperature Alert",
    "wants_card_ml": "Artificial Intelligence Alert",
    # Hydraulic oil alerts
    "hyd_oil_lvl": "Low Hydraulic Oil Level",
    "hyd_filt_life": "Low Hydraulic Filter Life",
    "hyd_oil_life": "Low Hydraulic Oil Life",
    "chk_mtr_ovld": "Check Motor Overload",
    "pwr_fail": "Power Failure",
    "soft_start_err": "Soft Start Error",
    "grey_wire_err": "Grey Wire Error",
    "ae011": "Check Motor Rotation, No Pressure",
}
alert_column_descriptions = {
    "customers_rel": "One unit can potentially be co-owned by several customers (including the 'demo' customer)",
    "alert_id": "Alert ID (primary key for database table)",
    "gateways_rel": "Unit for which alert is being sent",
    "power_units_rel": "Unit for which alert is being sent",
    "users_rel": "User who is being alerted",
    "wants_sms": "Select if you want an SMS alert",
    "users_rel.sms_stop_all": "The user has replied 'STOP' to an IJACK alert, so no alerts will be sent until the user sends 'START' to ************",
    "wants_email": "Select if you want an email alert",
    "wants_phone": "Select if you want a phone call alert",
    "wants_whatsapp": "Select if you want a WhatsApp alert",
    "wants_short_sms": "Select if you want a shorter version of the SMS alert",
    "wants_short_email": "Select if you want a shorter version of the email alert (usually a longer email is better, actually)",
    "wants_short_phone": "Select if you want a shorter version of the phone call alert (most customers want this)",
    "heartbeat": "Select if you want to be notified when the unit stops stroking (all customers want this)",
    "online_hb": "Select if you want to be notified when the unit loses power or internet connection (most customers want this)",
    "change_suction": "Select if you want to be notified when the suction pressure goes above its normal range (useful)",
    "change_dgp": "Select if you want to be notified when the discharge pressure goes below its normal range (useful)",
    "change_hyd_temp": "Select if you want to be notified when the hydraulic temperature goes above its normal range (not many customers use this)",
    "change_hp_delta": "Select if you want to be notified when the upstroke horsepower is much greater than the downstroke horsepower, or vice versa. This is useful for early detection of check valve issues.",
    "suction": "Select if you want to be notified when the suction pressure goes above a certain threshold (threshold is set in 'Units/Gateways')",
    "discharge": "Select if you want to be notified when the discharge pressure goes down to a certain threshold (typically zero; set in 'Units/Gateways')",
    "spm": "Select if you want to be notified when 'strokes per minute' goes down to a certain threshold (typically zero; set in 'Units/Gateways')",
    "stboxf": "Select if you want to be notified when 'stuffing box float switch' is triggered",
    "hyd_temp": f"Select if you want to be notified when hydraulic temperature goes above to a certain threshold (typically 65{chr(176)} Celsius; set in 'Units/Gateways')",
    "wants_card_ml": "Select if you want to be notified when IJACK's artificial intelligence algorithm detects problems with surface/compression cards",
    # Hydraulic oil alerts
    "hyd_oil_lvl": "Select if you want to be notified when the hydraulic oil level is below a certain threshold (%)",
    "hyd_filt_life": "Select if you want to be notified when the hydraulic filter life is below a certain threshold (%)",
    "hyd_oil_life": "Select if you want to be notified when the hydraulic oil life is below a certain threshold (%)",
    "chk_mtr_ovld": "Select if you want to be notified when the motor overload is triggered",
    "pwr_fail": "Select if you want to be notified when the power fails",
    "soft_start_err": "Select if you want to be notified when the soft start error is triggered",
    "grey_wire_err": "Select if you want to be notified when the grey wire error is triggered",
    "ae011": "Select if you want to be notified when the 'check motor rotation, no pressure' flag is triggered",
}


class AlertView(MyModelView):
    """Flask-Admin view for Alert model (public.alerts table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    page_size = 50

    column_list = (
        "users_rel",
        "power_units_rel",
        "users_rel.first_name",
        "users_rel.last_name",
        # "customers_rel.customer",
        "users_rel.phone",
        # 'structures_rel',
        "wants_sms",
        "users_rel.sms_stop_all",
        "wants_email",
        "wants_phone",
        # WhatsApp is not yet implemented
        # "wants_whatsapp",
        # "users_rel.whatsapp_stop_all",
        "id",
    )
    column_sortable_list = (
        # "users_rel",
        # "power_units_rel",
        "users_rel.first_name",
        "users_rel.last_name",
        # "customers_rel.customer",
        "users_rel.phone",
        # 'structures_rel',
        "wants_sms",
        "users_rel.sms_stop_all",
        "wants_email",
        "wants_phone",
        # WhatsApp is not yet implemented
        # "wants_whatsapp",
        # "users_rel.whatsapp_stop_all",
        "id",
    )

    # For a faster editing experience, enable inline editing in the list view:
    # Won't work if you add 'gateway' to this list since 'gateway' is the primary key
    column_editable_list = (
        "power_units_rel",
        "users_rel",
        "wants_sms",
        "wants_email",
        "wants_phone",
        # WhatsApp is not yet implemented
        # "wants_whatsapp",
    )

    # Control the order of the columns in the forms
    form_columns = alert_form_columns

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "power_units_rel": QueryAjaxModelLoader(
            "power_units_rel",
            db.session,
            PowerUnit,
            # fields=["power_unit_str", Structure.downhole, Structure.surface],
            fields=["power_unit_str"],
            order_by="power_unit",
            placeholder="Please select a power unit",
            **options,
        ),
        "users_rel": QueryAjaxModelLoader(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
    }

    column_default_sort = "customers_rel.customer"

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "users_rel",
        "power_units_rel",
        "users_rel.first_name",
        "users_rel.last_name",
        # "customers_rel.customer",
        "users_rel.phone",
    )
    column_filters = (
        # "customers_rel",
        User.email,
        User.first_name,
        User.last_name,
        "power_units_rel",
        # "power_unit.power_unit",
        "wants_sms",
        "wants_email",
        "wants_phone",
        "wants_whatsapp",
        "users_rel.sms_stop_all",
        "users_rel.whatsapp_stop_all",
        Structure.structure,
        Structure.surface,
        Structure.downhole,
        # 'structures_rel',
        # 'surface',
        # 'downhole',
    )

    column_formatters = {
        # `view` is current administrative view
        # `context` is instance of jinja2.runtime.Context
        # `model` is model instance
        # `name` is property name
        # Format your column string with lambda here (e.g show first 20 characters).
        # Can return any valid HTML (e.g. a link to another view to show the detail or a popup window).
        "users_rel.phone": user_phone_num_formatter,
        "power_units_rel": remove_unnecessary_decimal,
    }

    column_labels = alert_column_labels
    column_descriptions = alert_column_descriptions

    # def on_model_change(self, form, model, is_created):
    #     pass

    def after_model_change(self, form, model, is_created):
        """
        Perform some actions after a model was created or updated and committed to the database.
        After an alert is created/changed, if the "wants_sms" is True,
        send a notification with unsubscribe information
        """
        if getattr(model, "wants_sms", False):
            user = db.session.get(User, model.user_id)

            # Send them a first SMS text message
            # so their carrier doesn't block future messages as spam
            send_new_user_sms(user)

        if getattr(model, "wants_whatsapp", False):
            user = db.session.get(User, model.user_id)

            # Send them a first SMS text message
            # so their carrier doesn't block future messages as spam
            send_new_user_whatsapp(user)


user_column_list = (
    "email",
    "first_name",
    "last_name",
    "main_customer_rel",
    # "customers_rel.customer",
    "job_title",
    "is_active",
    "date_created",
    # Which alerts the user is subscribed to
    "alerts_rel",
    # Remote control of structures
    "structures_rel",
    # Units for which the user is a maintenance contact
    "maintenance_structures_rel",
    # Units for which the user is a sales contact
    "sales_structures_rel",
    # Operators who don't have remote control of structures, or alerts
    "structures_operators_rel",
    # Customer sub-groups for which the user wants to be CC'd when there's a service request
    "cust_sub_groups_notify_service_requests_rel",
    # Does the user want to be notified of ALL service requests, regardless of sub-group?
    "notify_service_requests",
    "phone",
    "countries_rel",
    "user_lat",
    "user_lon",
    "current_login_at",
    "is_confirmed",
    "sms_stop_all",
    "sms_stopped_at_utc",
    "eml_unsubscribe_all",
    "eml_unsubscribe_all_stopped_at_utc",
    "id",
)

user_form_columns = (
    "first_name",
    "last_name",
    "email",
    # Just let them login with Microsoft Azure, or reset their password
    # "form_password",
    # "form_password_verify",
    "main_customer_rel",
    "customers_rel",
    "job_title",
    "is_active",
    "date_created",
    # Structures the user can remote control
    "structures_rel",
    # Structures for which the user is a maintenance contact
    "maintenance_structures_rel",
    # Structures for which the user is a sales contact
    "sales_structures_rel",
    # Operators who don't have remote control of structures, or alerts
    "structures_operators_rel",
    # Customer sub-groups for which the user wants to CC'd when there's a service request
    "cust_sub_groups_notify_service_requests_rel",
    # Does the user want to be notified of ALL service requests, regardless of sub-group?
    "notify_service_requests",
    # Only Human Resources people can change roles
    # "roles_rel",
    "phone",
    "countries_rel",
    "is_us_phone",
    "time_zones_rel",
    "user_lat",
    "user_lon",
    "is_confirmed",
    # Only Twilio can write to the following fields
    # "sms_stop_all",
    # "sms_stopped_at_utc",
    # Email marketing campaigns
    "eml_unsubscribe_all",
    "eml_marketing",
    "eml_new_products",
    "eml_service",
    "eml_rcom",
    # alerts_rel is an inline model, so it's not included here
)

user_form_create_rules_rows = [
    "first_name",
    "last_name",
    # BSRow(
    #     [
    #         BSCol(["first_name"], classes="col-md-6"),
    #         BSCol(["last_name"], classes="col-md-6"),
    #     ]
    # ),
    "email",
    # BSRow(
    #     [
    #         BSCol(["email"], classes="col-md-12"),
    #     ]
    # ),
    "is_confirmed",
    "is_active",
    # BSRow(
    #     [
    #         BSCol(["is_confirmed"], classes="col-md-4"),
    #         BSCol(["is_active"], classes="col-md-4"),
    #     ]
    # ),
    # BSRow(
    #     [
    #         BSCol(["form_password"], classes="col-md-6"),
    #         BSCol(["form_password_verify"], classes="col-md-6"),
    #     ]
    # ),
    "main_customer_rel",
    "customers_rel",
    # BSRow(
    #     [
    #         BSCol(["main_customer_rel"], classes="col-md-6"),
    #         BSCol(["customers_rel"], classes="col-md-6"),
    #     ]
    # ),
    "job_title",
    # Structures the user can remote control
    "structures_rel",
    # Structures for which the user is a maintenance contact
    "maintenance_structures_rel",
    # Structures for which the user is a sales contact
    "sales_structures_rel",
    # Operators who don't have remote control of structures, or alerts
    "structures_operators_rel",
    # Customer sub-groups for which the user wants to CC'd when there's a service request
    "cust_sub_groups_notify_service_requests_rel",
    # Does the user want to be notified of ALL service requests, regardless of sub-group?
    "notify_service_requests",
    # Only Human Resources people can change roles
    # "roles_rel",
    "phone",
    "time_zones_rel",
    # BSRow(
    #     [
    #         BSCol(["phone"], classes="col-md-6"),
    #         BSCol(["time_zones_rel"], classes="col-md-6"),
    #     ]
    # ),
    "is_us_phone",
    "user_lat",
    "user_lon",
    # BSRow(
    #     [
    #         BSCol(["user_lat"], classes="col-md-6"),
    #         BSCol(["user_lon"], classes="col-md-6"),
    #     ]
    # ),
    # Only Twilio can write to the following fields
    # "sms_stop_all",
    # "sms_stopped_at_utc",
    # Email marketing campaigns
    "eml_unsubscribe_all",
    "eml_marketing",
    "eml_new_products",
    "eml_service",
    "eml_rcom",
    "alerts_rel",
]
user_form_create_rules = (
    user_form_create_rules_rows,
    # BSContainer(
    #     rules=user_form_create_rules_rows,
    #     classes="mt-3",
    # ),
)
# Separate form creation rules for Human Resources people, including the "roles" field
user_hr_form_create_rules_rows = user_form_create_rules_rows.copy()
user_hr_form_create_rules_rows.insert(4, "roles_rel")
user_hr_form_create_rules = (
    user_hr_form_create_rules_rows,
    # BSContainer(
    #     rules=user_hr_form_create_rules_rows,
    #     classes="mt-3",
    # ),
)


class UserView(MyModelView):
    """Flask-Admin view for User model (users table)"""

    # Use the /register link for creating users, so the password gets hashed
    # can_create = False
    # Don't delete users, just deactivate them
    can_delete = True
    # can_delete = False
    can_set_page_size = True
    page_size = 50
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        # This view takes a long time to open in development mode...
        page_size = 5

    # These cause problems with the register_admin.js script
    create_modal = False
    edit_modal = False

    # column_hide_backrefs = False
    column_list = user_column_list
    column_sortable_list = (
        "email",
        "first_name",
        "last_name",
        # "main_customer_rel",
        # "customers_rel.customer",
        "job_title",
        "is_active",
        "notify_service_requests",
        "phone",
        # "countries_rel",
        "user_lat",
        "user_lon",
        "last_seen_at",
        "current_login_at",
        "is_confirmed",
        "sms_stop_all",
        "sms_stopped_at_utc",
        "eml_unsubscribe_all",
        "eml_unsubscribe_all_stopped_at_utc",
        # "structures_rel",
        # "alerts_rel",
        "id",
    )
    column_default_sort = "email"

    # # This won't work for editing an existing user since the email validation checks uniqueness
    # form = RegistrationFormAdmin

    # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    column_exclude_list = ("password_hash",)
    form_exclude_list = ("password_hash",)

    column_editable_list = (
        "email",
        "first_name",
        "last_name",
        "main_customer_rel",
        "structures_rel",
        "maintenance_structures_rel",
        "sales_structures_rel",
        # Operators who don't have remote control of structures, or alerts
        "structures_operators_rel",
        "cust_sub_groups_notify_service_requests_rel",
        "notify_service_requests",
        # This is many-to-many now, so it can't be edited here
        # "customers_rel",
        "job_title",
        "is_active",
        "time_zones_rel",
        "countries_rel",
        "user_lat",
        "user_lon",
        # 'structures_rel',
        # phone is too difficult to validate as an editable field...
        # "phone",
    )

    # Control the order of the columns in the forms
    # Not needed for User model IF we're using the custom form_create_rules
    form_columns = user_form_columns

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "first_name",
        "last_name",
        "email",
        "main_customer_rel",
        "customers_rel",
        "job_title",
        "countries_rel.country_name",
        "phone",
    )
    column_filters = (
        "first_name",
        "last_name",
        "email",
        "eml_unsubscribe_all",
        "eml_marketing",
        "eml_new_products",
        "eml_service",
        "eml_rcom",
        "main_customer_rel",
        "customers_rel",
        "cust_sub_groups_notify_service_requests_rel",
        "notify_service_requests",
        "is_confirmed",
        "is_active",
        "countries_rel",
        "user_lat",
        "user_lon",
        "job_title",
        "phone",
        "is_us_phone",
        "time_zones_rel.time_zone",
        "last_seen_at",
        "current_login_at",
        "id",
    )

    # form_create_rules = user_form_create_rules

    # # This rules list does NOT contain a password. Change that separately by resetting it.
    # form_edit_rules = user_form_create_rules

    # # This is the correct way to deal with password verification and hashing
    # form_extra_fields = {
    #     "form_password": PasswordField("Password", validators=PASSWORD_VALIDATORS),
    #     "form_password_verify": PasswordField(
    #         "Password Verify", validators=[EqualTo("form_password"), DataRequired()]
    #     ),
    # }
    form_args = {
        "phone": {"validators": [validate_phone]},
        "email": {"validators": [Email()]},
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "main_customer_rel": QueryAjaxModelLoader(
            "main_customer_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select the main customer for this user",
            **options,
        ),
        "customers_rel": QueryAjaxModelLoader(
            "customers_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select all the customers this user can see/control",
            **options,
        ),
        # Units the user can remote-control (main form, not inline model)
        "structures_rel": QueryAjaxModelLoaderAdmin(
            "structures_rel",
            db.session,
            Structure,
            fields=["structure_str", "surface", "downhole", PowerUnit.power_unit_str],
            # fields=[Structure.structure, Structure.surface, Structure.downhole, PowerUnit.power_unit_str],
            order_by=PowerUnit.power_unit_str,
            placeholder="Please select units for this user to remote-control",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        # Units for which the user is a maintenance contact
        "maintenance_structures_rel": QueryAjaxModelLoaderAdmin(
            "maintenance_structures_rel",
            db.session,
            Structure,
            fields=["structure_str", "surface", "downhole", PowerUnit.power_unit_str],
            order_by=PowerUnit.power_unit_str,
            placeholder="Please select the structures for which this user is a maintenance contact",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        # Units for which the user is a sales contact
        "sales_structures_rel": QueryAjaxModelLoaderAdmin(
            "sales_structures_rel",
            db.session,
            Structure,
            fields=["structure_str", "surface", "downhole", PowerUnit.power_unit_str],
            order_by=PowerUnit.power_unit_str,
            placeholder="Please select the structures for which this user is a sales contact",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        # Operators who don't have remote control of structures, or alerts
        "structures_operators_rel": QueryAjaxModelLoaderAdmin(
            "structures_operators_rel",
            db.session,
            Structure,
            fields=["structure_str", "surface", "downhole", PowerUnit.power_unit_str],
            order_by=PowerUnit.power_unit_str,
            placeholder="Please select the structures for which this user is an operator (no remote control or alerts)",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        "time_zones_rel": QueryAjaxModelLoader(
            "time_zones_rel",
            db.session,
            TimeZone,
            fields=["time_zone"],
            order_by="time_zone",
            placeholder="Please select a time zone for this user (for alerts and charts)",
            get_query_func=timezone_filtering_function,
            # default=TIME_ZONE_ID_AMERICA_REGINA,
            **options,
        ),
        "roles_rel": QueryAjaxModelLoader(
            "roles_rel",
            db.session,
            Role,
            fields=["name"],
            order_by="name",
            placeholder="Please select the role for this user",
            **options,
        ),
        "countries_rel": QueryAjaxModelLoader(
            "countries_rel",
            db.session,
            Country,
            fields=["country_name"],
            order_by="country_name",
            placeholder="Please select the country for this user",
            **options,
        ),
        # Customer sub-groups for which the user wants to be CC'd when there's a service request
        "cust_sub_groups_notify_service_requests_rel": QueryAjaxModelLoader(
            "cust_sub_groups_notify_service_requests_rel",
            db.session,
            CustSubGroup,
            fields=["name"],
            order_by="name",
            placeholder="Select a customer sub-group for which this user wants to be CC'd when there's a service request",
            **options,
        ),
    }

    def create_form(self, obj=None):
        """
        Instantiate model creation form and return it.

        Override to implement custom behavior, like default values for a select dropdown.
        """
        self._add_roles_field()
        form = super().create_form()

        # There's no way to set a default for form_ajax_refs select dropdowns
        # or "select many" dropdowns like for "roles_rel", so do it here
        if hasattr(form, "roles_rel"):
            if not form.roles_rel.data:
                role_customer = db.session.get(Role, ROLE_ID_CUSTOMER)
                form.roles_rel.data = [role_customer]

        if not form.time_zones_rel.data:
            time_zone_regina = db.session.get(TimeZone, TIME_ZONE_ID_AMERICA_REGINA)
            form.time_zones_rel.data = time_zone_regina

        # IJACK Shop GPS latitude == 50.1631 and longitude == -101.6754
        if not form.user_lat.data:
            form.user_lat.data = 50.1631
        if not form.user_lon.data:
            form.user_lon.data = -101.6754

        return form

    # So we can create multiple new alerts for different units, with the new user as the alertee.
    # This uses the defaults for new alerts, such as "wants_sms = True" for SMS alerts, not email
    inline_models = (
        (
            Alert,
            {
                "form_ajax_refs": {
                    "power_units_rel": QueryAjaxModelLoader(
                        "power_units_rel",
                        db.session,
                        PowerUnit,
                        fields=[
                            "power_unit",
                            "power_unit_str",
                            Structure.downhole,
                            Structure.surface,
                        ],
                        order_by="power_unit",
                        placeholder="Please select a power unit for the alert",
                        **options,
                    )
                },
                "column_labels": alert_column_labels,
                "column_descriptions": alert_column_descriptions,
                # Must include "id" primary key, which is used for inline-form construction, for some reason...
                # https://stackoverflow.com/questions/34313253/flask-admin-inline-modelling-passing-form-arguments-throws-attributeerror
                "form_columns": ["id"]
                + [col for col in alert_form_columns if col != "users_rel"],
            },
        ),
    )

    def on_model_change(self, form, model, is_created):
        """When someone changes a record, force the email address to be lowercase"""

        if is_created:
            # This will use the User model's @password.setter
            # function to set the "password_hash".
            # Password change is only available from the "create" form, not "edit"
            # model.password = form.form_password.data
            model.password = generate_random_string(24)

            if hasattr(model, "roles_rel") and not model.roles_rel:
                # Default role is "customer"
                role_customer = db.session.get(Role, ROLE_ID_CUSTOMER)
                model.roles_rel = [role_customer]

        if hasattr(form, "first_name") and hasattr(form.first_name, "data"):
            model.first_name = form.first_name.data.strip()
        if hasattr(form, "last_name") and hasattr(form.last_name, "data"):
            model.last_name = form.last_name.data.strip()
        if hasattr(form, "email") and hasattr(form.email, "data"):
            model.email = form.email.data.strip().lower()

        if getattrd(form, "eml_unsubscribe_all.data", False):
            model.eml_unsubscribe_all_stopped_at_utc = utcnow_naive()
        if not getattrd(form, "eml_marketing.data", True):
            model.eml_marketing_stopped_at_utc = utcnow_naive()
        if not getattrd(form, "eml_new_products.data", True):
            model.eml_new_products_stopped_at_utc = utcnow_naive()
        if not getattrd(form, "eml_service.data", True):
            model.eml_service_stopped_at_utc = utcnow_naive()
        if not getattrd(form, "eml_rcom.data", True):
            model.eml_rcom_stopped_at_utc = utcnow_naive()

        # The main customer definitely needs to be in the list of customers the user can see/control
        main_customer: User = getattr(form.main_customer_rel, "data", None)
        # This one may not be populated yet, so use getattrd
        all_customers: List[User] = getattrd(form, "customers_rel.data", [])
        if main_customer and main_customer not in all_customers:
            model.customers_rel.append(main_customer)

        # When we set a user as inactive in admin site, remove all user remote control and alerts
        if not model.is_active:
            model.structures_rel = []
            model.alerts_rel = []
            model.maintenance_structures_rel = []
            model.sales_structures_rel = []
            model.structures_operators_rel = []
            flash(
                gettext(
                    f"User {model.email} has been deactivated. All remote control and alerts have been removed."
                ),
                "warning",
            )

    def after_model_change(self, form, model, is_created):
        """
        When a new user is created or updated,
        do the following AFTER committing to the database
        """
        if is_created:
            if os.getenv("FLASK_CONFIG", "production") not in (
                "development",
                "testing",
                "wsl",
            ):
                send_new_user_sms(model)

    column_formatters = {
        "phone": user_phone_num_formatter,
        "alerts_rel": alerts_formatter,
        "current_login_at": datetime_formatter_sk_time,
        "last_seen_at": datetime_formatter_sk_time,
        "maintenance_structures_rel": many_to_many_formatter,
        "sales_structures_rel": many_to_many_formatter,
        # Operators who don't have remote control of structures, or alerts
        "structures_operators_rel": many_to_many_formatter,
        "structures_rel": structures_formatter,
        "sms_stopped_at_utc": datetime_formatter_sk_time,
        "eml_unsubscribe_all_stopped_at_utc": datetime_formatter_sk_time,
    }

    column_labels = {
        "email": "Email",
        "first_name": "First Name",
        "last_name": "Last Name",
        "main_customer_rel": "Main Customer",
        "main_customer_rel.customer": "Main Customer",
        "customers_rel": "All Customers",
        "customers_rel.customer": "All Customers",
        "roles_rel": "Roles",
        "job_title": "Job Title",
        # Is a user a maintenance contact for a customer?
        "maintenance_structures_rel": "Maintenance Units",
        # Is a user a sales contact for a customer?
        "sales_structures_rel": "Sales Units",
        # Operators who don't have remote control of structures, or alerts
        "structures_operators_rel": "Operators",
        # "cust_sub_groups_rel": "Customer Sub-Groups",
        # Customer sub-groups for which the user wants to CC'd when there's a service request
        "cust_sub_groups_notify_service_requests_rel": "Customer Sub-Groups for Service Requests",
        # Does the user want to be notified of ALL service requests, regardless of sub-group?
        "notify_service_requests": "Notify of ALL Service Requests",
        "structures_rel": "Units the User Can Remote-Control",
        "alerts_rel": "Unit Alerts",
        "current_login_at": "Last Login",
        "last_seen_at": "Last Seen",
        # "can_set": "Can Set Targets (Remote Control)",
        "phone": "Phone Number",
        "countries_rel": "Country",
        "countries_rel.country_name": "Country",
        "is_us_phone": "Is US Phone Number?",
        "time_zones_rel": "Time Zone",
        "user_lat": "User Latitude",
        "user_lon": "User Longitude",
        "is_confirmed": "Email Confirmed",
        "is_active": "Is Active",
        "sms_stop_all": "SMS STOPPED",
        "sms_stopped_at_utc": "SMS STOPPED (SK Time)",
        # Email marketing campaigns
        "eml_unsubscribe_all": "Email Unsubscribe All",
        "eml_unsubscribe_all_stopped_at_utc": "Email Unsubscribe All (SK Time)",
        "eml_marketing": "Wants Marketing Emails",
        "eml_new_products": "Wants New Products Emails",
        "eml_service": "Wants Service Emails",
        "eml_rcom": "Wants RCOM Update Emails",
    }
    column_descriptions = {
        "main_customer_rel": "Main customer the user works for",
        "main_customer_rel.customer": "Main customer the user works for",
        "customers_rel": "All customers the user can see/control",
        "customers_rel.customer": "All customers the user can see/control",
        "email": "Login email address that identifies the user's account",
        "roles_rel": "Security roles for the user, to determine what they're allowed to do",
        "job_title": "Job Title (e.g. Foreman, Operator, etc)",
        "maintenance_structures_rel": "Units (pumps) for which this user is a maintenance contact",
        "sales_structures_rel": "Units (pumps) for which this user is a sales contact",
        # Operators who don't have remote control of structures, or alerts
        "structures_operators_rel": "Units (pumps) for which this user is an operator (no remote control or alerts)",
        # "cust_sub_groups_rel": "Customer-defined sub-groups of which a user is a member (be careful to select only that customer's sub-group)",
        "cust_sub_groups_notify_service_requests_rel": "Customer-defined sub-groups for which the user wants to be CC'd when there's a service request",
        "notify_service_requests": "Does the user want to be notified when ALL service requests are submitted, regardless of customer sub-group?",
        # "can_set": "User has general permission to set targets or 'remote-control' units. Without this top-level permission, they cannot remote-control any units.",
        "structures_rel": "Units (pumps) the user can specifically 'remote-control' or set targets for",
        "alerts_rel": "Units for which user gets alerts",
        "last_seen_at": "Last time the user was seen online (SK time)",
        "current_login_at": "Last time the user logged in (SK time)",
        "phone": "International standard phone number (e.g. starting with +1)",
        "countries_rel": "Country where the user is located",
        "countries_rel.country_name": "Country where the user is located",
        "is_us_phone": "Is this a US phone number? If so, we'll send them alerts from our US phone number.",
        "time_zones_rel": "Time Zone",
        "user_lat": "Latitude of the user's main location (defaults to 50.1631 for the IJACK shop in Moosomin, SK)",
        "user_lon": "Longitude of the user's main location (defaults to -101.6754 for the IJACK shop in Moosomin, SK)",
        "is_confirmed": "Email address has been confirmed/verified",
        "is_active": "User is active (can log in and do things). Deactivate the user instead of deleting him/her, so work orders and other data are correct.",
        "sms_stop_all": "The user has replied 'STOP' to an IJACK alert, so no alerts will be sent until the user sends 'START' to ************",
        "sms_stopped_at_utc": "Timestamp in Central Standard time when the user replied 'STOP' to an SMS, disabling all future SMS alerts from IJACK",
        # Email marketing campaigns
        "eml_unsubscribe_all": "User has unsubscribed from ALL IJACK emails",
        "eml_unsubscribe_all_stopped_at_utc": "Timestamp in Central Standard time when the user unsubscribed from ALL IJACK emails",
        "eml_marketing": "User is signed up to receive marketing emails",
        "eml_new_products": "User is signed up to receive new Products emails",
        "eml_service": "User is signed up to receive service emails",
        "eml_rcom": "User is signed up to receive RCOM update emails",
    }

    def _add_roles_field(self):
        """Add the 'roles_rel' field to the form for Human Resources people"""
        role_ids: list = get_user_role_ids(user_id=getattr(current_user, "id", None))
        if ROLE_ID_IJACK_HUMAN_RESOURCES in role_ids:
            self.form_args = {
                "phone": {"validators": [validate_phone]},
                "email": {"validators": [Email()]},
                "roles_rel": {"validators": [DataRequired()]},
            }
            self.column_list = user_column_list + ("roles_rel",)
            self.form_columns = user_form_columns + ("roles_rel",)
        else:
            # NOTE: Be careful to put these back to the original values if the user is not HR,
            # since these are cached on the class and will be used for all users
            self.form_args = {
                "phone": {"validators": [validate_phone]},
                "email": {"validators": [Email()]},
            }
            self.column_list = user_column_list
            self.form_columns = user_form_columns

    @expose("/")
    def index_view(self):
        """List view"""
        self._add_roles_field()
        # Refresh the list columns, which are cached on the class
        self._list_columns = self.get_list_columns()
        return super().index_view()

    @expose("/new/", methods=("GET", "POST"))
    def create_view(self):
        """For creating a new model"""
        self._add_roles_field()
        # Refresh the forms cache, which is cached on the class
        self._refresh_forms_cache()
        return super().create_view()

    @expose("/edit/", methods=("GET", "POST"))
    def edit_view(self):
        """For editing an existing model"""
        self._add_roles_field()
        # Refresh the forms cache, which is cached on the class
        self._refresh_forms_cache()
        return super().edit_view()

    def render(self, template, **kwargs):
        """Add some extra JavaScript"""
        current_app.config.get("VERSION_MYIJACK", None)
        self.extra_css = [
            # "https://cdn.jsdelivr.net/npm/intl-tel-input@24.5.0/build/css/intlTelInput.min.css",
            "intlTelInput.min.css"
        ]
        self.extra_js = [
            # "https://cdn.jsdelivr.net/npm/intl-tel-input@24.5.0/build/js/intlTelInput.min.js",
            # "https://cdn.jsdelivr.net/npm/intl-tel-input@24.5.0/build/js/utils.js",
            # Use the name of the vite bundle here
            "register_admin.js",
        ]
        return super().render(template, is_ijack_admin=True, **kwargs)


# class UserViewHR(UserView):
#     """User view for Human Resources people only, which contains the 'roles_rel' field"""

#     column_list = user_column_list + ("roles_rel",)

#     # form_columns not needed for User model, since we're using the custom form_create_rules
#     # form_columns = user_form_columns + ("roles_rel",)

#     # form_create_rules = user_hr_form_create_rules

#     # form_edit_rules = user_hr_form_create_rules

#     form_args = {
#         "phone": {"validators": [validate_phone]},
#         "email": {"validators": [Email()]},
#         "roles_rel": {"validators": [DataRequired()]},
#     }


class UserAPITokenView(MyModelView):
    """Flask-Admin view for UserAPIToken model"""

    # Use the /register link for creating users, so the password gets hashed
    # can_create = False
    can_set_page_size = True
    page_size = 50
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        # This view takes a long time to open in development mode...
        page_size = 5

    can_create = True
    can_edit = False
    can_delete = True

    create_modal = True
    edit_modal = True

    column_list = (
        "id",
        "expires",
        "users_rel",
        "name",
        "description",
        "token",
    )
    column_sortable_list = (
        # "users_rel.first_name",
        "name",
        "description",
        "expires",
        "token",
        "id",
    )
    column_default_sort = "id"

    # # This won't work for editing an existing user since the email validation checks uniqueness
    # form = RegistrationFormAdmin

    # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    # column_exclude_list = ("password_hash",)
    form_exclude_list = ("token",)

    column_editable_list = (
        "name",
        "description",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "users_rel",
        "name",
        "description",
        "expires",
    )

    column_labels = {
        "users_rel": "User",
        "name": "Token Name",
        "description": "Token Description",
    }

    column_descriptions = {
        "users_rel": "User for which the token is valid",
        "name": "Name of the token",
        "description": "Description of the token",
        "token": "Token string",
        "expires": "Date and time when the token expires",
    }

    def on_model_change(self, form, model, is_created):
        """When someone changes a record"""
        if not hasattr(model, "expires") or not isinstance(model.expires, date):
            raise TypeError("Please provide an expiry date for the token")

        if isinstance(model.user_id, int):
            user_id_str = str(model.user_id)
        else:
            if not hasattr(model, "users_rel") or not hasattr(model.users_rel, "id"):
                raise TypeError(
                    "Please provide a user with an 'id' field for the token"
                )
            user_id_str = str(model.users_rel.id)

        if isinstance(model.expires, datetime):
            time_delta = model.expires - datetime.now()
        else:
            time_delta = model.expires - date.today()

        model.token = create_jwt_encoded_token(expires_in=time_delta, sub=user_id_str)
        return None


class RoleView(MyModelView):
    """Flask-Admin view for Role model (roles table)"""

    def is_accessible(self):
        """The user must have certain roles to access this view"""
        role_ids: list = get_user_role_ids(user_id=getattr(current_user, "id", None))
        return (
            ROLE_ID_IJACK_HUMAN_RESOURCES in role_ids
            and ROLE_ID_IJACK_ADMIN in role_ids
        )

    def inaccessible_callback(self, name, **kwargs):
        """Redirect to login page if user doesn't have access"""
        return redirect(url_for("home.rcom", next=request.url))

    column_list = ("name", "description", "id")

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name",)
    column_filters = ("name",)

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = ("name", "description")

    # # This is the only way to ensure "users_rel" is required
    # form_args = {
    #     "users_rel": {"validators": [DataRequired()]},
    # }

    column_labels = {
        "name": "Role Name",
        "description": "Role Description",
        "users_rel": "Users with this role",
    }


def user_id_link(view, context, model, name) -> Markup:
    """Get a link to view the user's details"""
    url = url_for("users.edit_view", id=model.user_id)
    markupstring = f"<a href='{url}'>{model.name}</a>"
    return Markup(markupstring)


class VwUserRoleView(MyModelView):
    """Flask-Admin view for VwUserRole model (vw_users_roles view)"""

    can_create = False
    can_edit = False
    can_delete = False
    # Duplicate button - make None to disable
    column_extra_row_actions = None

    column_list = ("customer", "name", "email", "job_title", "roles", "units")

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = column_list
    column_filters = column_list
    column_sortable_list = column_list

    # True == sort descending
    column_default_sort = [
        ("customer", False),
        ("name", False),
    ]

    column_formatters = {
        "name": user_id_link,
    }

    column_labels = {
        "customer": "Customer",
        "name": "Name",
        "email": "Email",
        "job_title": "Job Title",
        "roles": "Roles",
        "units": "Units",
    }
    column_descriptions = {
        "roles": "Security roles that define what the user is allowed to do",
        "units": "Units for which the user has remote control permissions",
    }


class CustomerView(MyModelView):
    """Flask-Admin view for Customer model (customers table)"""

    column_list = (
        "customer",
        "country_rel.country_name",
        "formal",
        "description",
        # "country",
        # "mqtt_topic",
        "cust_sub_groups_rel.name",
        "is_tax_exempt",
        # Address fields
        "unit",
        "street",
        "city",
        # "state",
        "province_rel.name",
        "postal",
        "accounting_contact_rel",
        "gst_hst_number",
        # Other fields
        # "users_rel",
        "id",
    )
    column_sortable_list = (
        "customer",
        "formal",
        "description",
        # "mqtt_topic",
        "cust_sub_groups_rel.name",
        "is_tax_exempt",
        # Address fields
        "unit",
        "street",
        "city",
        # "state",
        "province_rel.name",
        "postal",
        # "country",
        "country_rel.country_name",
        "gst_hst_number",
        # Other fields
        # "users_rel",
        "id",
    )
    column_default_sort = "customer"

    # Control the order of the columns in the forms
    form_columns = (
        "customer",
        "formal",
        "description",
        # "country",
        "country_rel",
        # "mqtt_topic",
        "cust_sub_groups_rel",
        "is_tax_exempt",
        "structures_rel",
        # Address fields
        "unit",
        "street",
        "city",
        # "state",
        "province_rel",
        "postal",
        "accounting_contact_rel",
        "gst_hst_number",
    )
    column_editable_list = form_columns

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "customer",
        "description",
    )
    column_filters = ("customer", "id")

    column_labels = {
        "customer": "Customer Short Name",
        "formal": "Formal Name",
        "mqtt_topic": "MQTT Topic Name",
        "cust_sub_groups_rel": "Customer Sub-Groups",
        "cust_sub_groups_rel.name": "Customer Sub-Groups",
        "is_tax_exempt": "Tax Exempt",
        "structures_rel": "Units",
        "description": "Description",
        "unit": "Unit Number",
        "street": "Street Address",
        "city": "City",
        "postal": "Postal or ZIP Code",
        "province_rel": "Province/State",
        "province_rel.name": "Province/State",
        "country_rel": "Country",
        "country_rel.country_name": "Country",
        "accounting_contact_rel": "Accounting Contact",
        "gst_hst_number": "GST/HST Number",
    }
    column_descriptions = {
        "customer": "Short name of the customer (e.g. 'Teine' or 'Whitecap')",
        "formal": "Formal name of the customer (e.g. 'Teine Energy Ltd.' or 'Whitecap Resources Inc.')",
        "mqtt_topic": "One short word, usually the first word of the customer name",
        "cust_sub_groups_rel": "For grouping units by general area",
        "cust_sub_groups_rel.name": "For grouping units by general area",
        "is_tax_exempt": "Is the customer tax-exempt?",
        "structures_rel": "Structure IDs of units the customer owns",
        "accounting_contact_rel": "Person to contact for accounting questions",
        "description": "Description of the customer, or special notes about the customer",
        "unit": "Unit number or suite number",
        "street": "Street address",
        "city": "City where the customer is located",
        "province_rel": "Province or state where the customer is located",
        "province_rel.name": "Province or state where the customer is located",
        "country_rel": "Country where the customer is located",
        "country_rel.country_name": "Country where the customer is located",
        "gst_hst_number": "GST or HST number for tax-exempt customers",
    }

    # Use AJAX to load the fields
    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "cust_sub_groups_rel": QueryAjaxModelLoader(
            "cust_sub_groups_rel",
            db.session,
            CustSubGroup,
            fields=["name"],
            order_by="name",
            placeholder="Please select a customer sub-group",
            **options,
        ),
        "structures_rel": QueryAjaxModelLoaderAdmin(
            "structures_rel",
            db.session,
            Structure,
            fields=["structure", PowerUnit.power_unit_str, "downhole", "surface"],
            order_by="structure",
            placeholder="Please select the units owned by this customer",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        "users_rel": QueryAjaxModelLoader(
            "users_rel",
            db.session,
            User,
            fields=["first_name", "last_name", "email"],
            order_by="first_name",
            placeholder="Please select the users who work for this company",
            **options,
        ),
        "accounting_contact_rel": QueryAjaxModelLoader(
            "accounting_contact_rel",
            db.session,
            User,
            fields=["first_name", "last_name", "email"],
            order_by="first_name",
            placeholder="Please select the accounting contact for this customer",
            **options,
        ),
        "province_rel": QueryAjaxModelLoader(
            "province_rel",
            db.session,
            Province,
            fields=["name", "abbrev"],
            order_by="name",
            placeholder="Please select a province or state",
            **options,
        ),
        "country_rel": QueryAjaxModelLoader(
            "country_rel",
            db.session,
            Country,
            fields=["country_name", "country_code"],
            order_by="country_name",
            placeholder="Please select a country",
            **options,
        ),
    }


class CustSubGroupView(MyModelView):
    """Flask-Admin view for Customer sub-group model (cust_sub_groups table)"""

    # There's a separate form for creating sub-groups, for the customer-admin, but not yet for IJACK-admin
    can_create = True
    can_create = True
    can_edit = True
    can_delete = True

    column_list = (
        "name",
        "abbrev",
        "description",
        "customers_rel.customer",
        "structures_rel.structure",
        "id",
    )
    column_sortable_list = (
        "name",
        "abbrev",
        "description",
        "customers_rel.customer",
        "structures_rel.structure",
        "id",
    )
    column_default_sort = "name"

    # Control the order of the columns in the forms
    form_columns = ("name", "abbrev", "description", "customers_rel", "structures_rel")

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "name",
        "abbrev",
        "description",
        "customers_rel.customer",
        "structures_rel.structure",
        "power_units_rel.power_unit_str",
        "structures_rel.surface",
        "structures_rel.downhole",
    )
    column_filters = (
        "name",
        "abbrev",
        "description",
        "customers_rel.customer",
        "structures_rel.structure",
        "power_units_rel.power_unit_str",
    )

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = ("name", "abbrev", "description")

    column_labels = {
        "customers_rel": "Customer",
        "customers_rel.customer": "Customer",
        "structures_rel": "Units in Sub-Group",
        "structures_rel.structure": "Units in Sub-Group",
        "name": "Sub-Group Name",
        "abbrev": "Abbreviated Name",
        "description": "Sub-Group Description",
    }
    column_descriptions = {
        "customers_rel": "Customer that owns the sub-group",
        "customers_rel.customer": "Customer that owns the sub-group",
        "structures_rel": "Units (pumps) included in the customer-defined sub-group",
        "structures_rel.structure": "Units (pumps) included in the customer-defined sub-group",
        "name": "Customer-defined name of the sub-group (e.g. 'Bakken Operators')",
        "abbrev": "Abbreviated sub-group name (e.g. 'Bakken')",
        "description": "Description of the customer sub-group (e.g. 'Operators working in the Bakken region')",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        # We need customer to be able to test this model and generate fake data
        "customers_rel": QueryAjaxModelLoader(
            "customers_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select a customer",
            **options,
        ),
    }

    def __repr__(self) -> str:
        return str(self.__class__.__name__)


class GwView(MyModelView):
    """Flask-Admin view for Gw model (public.gw table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    # Use only the string field name here, not the Customer.customer object, or you'll get an error...
    column_default_sort = ("id", False)  # True == sort descending
    can_set_page_size = True
    # For my custom index view function, to start on the page paginated page
    start_on_last_page = True
    page_size = 100
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        # This view takes a long time to open in development mode...
        page_size = 5

    column_list = (
        "gateway_types_rel.name",
        "timestamp_utc_inserted",
        "customers_rel.customer",
        "cust_sub_groups_rel.name",
        # "model_types_rel",
        "gateway",
        "serial_gw",
        "model_gw",
        "imei",
        "power_units_rel.power_unit_str",
        "structures_rel.structure",
        "structures_rel.downhole",
        "structures_rel.surface",
        "notes",
        "test_can_bus",
        "test_cellular",
        "test_cellular_user_rel",
        "ready_and_working",
        "location_gw",
        # "users_rel",
        # "time_zones_rel",
        # "change_detect_sens",
        # "wait_time_mins_ol",
        "id",
    )
    column_sortable_list = (
        "gateway_types_rel.name",
        "timestamp_utc_inserted",
        "customers_rel.customer",
        "cust_sub_groups_rel.name",
        # "model_types_rel",
        "gateway",
        "serial_gw",
        "model_gw",
        "imei",
        "power_units_rel.power_unit_str",
        "structures_rel.structure",
        "structures_rel.downhole",
        "structures_rel.surface",
        "notes",
        "test_can_bus",
        "test_cellular",
        # "test_cellular_user_rel",
        "ready_and_working",
        "location_gw",
        # "users_rel",
        # "time_zones_rel",
        # "change_detect_sens",
        # "wait_time_mins_ol",
        "id",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "gateway_types_rel",
        "timestamp_utc_inserted",
        "gateway",
        "aws_thing",
        "mac",
        "serial_gw",
        "model_gw",
        "imei",
        "power_units_rel",
        # 'structure',
        # 'structures_rel',
        "notes",
        "test_can_bus",
        "test_cellular",
        # "test_cellular_user_rel",
        "ready_and_working",
        "location_gw",
        # "users_rel",
        # 'alerts_rel',
        # "time_zones_rel",
    )
    column_editable_list = (
        "gateway_types_rel",
        "timestamp_utc_inserted",
        "gateway",
        "aws_thing",
        "mac",
        "serial_gw",
        "model_gw",
        "imei",
        "power_units_rel",
        # 'structure',
        # 'structures_rel',
        "notes",
        "test_can_bus",
        "test_cellular",
        # "test_cellular_user_rel",
        "ready_and_working",
        "location_gw",
    )

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "power_units_rel": QueryAjaxModelLoader(
            "power_units_rel",
            db.session,
            PowerUnit,
            fields=[PowerUnit.power_unit],
            order_by="power_unit",
            placeholder="Please select a power unit",
            **options,
        ),
        "gateway_types_rel": QueryAjaxModelLoader(
            "gateway_types_rel",
            db.session,
            GatewayType,
            fields=["name"],
            order_by="name",
            placeholder="Please select a gateway type",
            **options,
        ),
        # Only query the users with the current_user's customer_id
        # "test_cellular_user_rel": QueryAjaxModelLoaderAdminCust(
        #     name="test_cellular_user_rel",
        #     session=db.session,
        #     model=User,
        #     fields=[User.first_name, User.last_name, User.email],
        #     order_by=User.first_name,
        #     placeholder="Choose a user who tested the cellular",
        #     **options,
        # ),
    }

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "gateway_types_rel.name",
        "gateway",
        "customers_rel.customer",
        "cust_sub_groups_rel.name",
        "power_units_rel.power_unit_str",
        # "model_types_rel",
        "notes",
        "location_gw",
        "serial_gw",
        "model_gw",
        "structures_rel.downhole",
        "structures_rel.surface",
        "imei",
        # PowerUnit.power_unit,
        # Structure.structure, Structure.surface, Structure.downhole, Structure.well_license,
    )

    column_filters = (
        "gateway_types_rel.name",
        "customers_rel",
        "cust_sub_groups_rel.name",
        # "model_types_rel",
        "model_gw",
        "imei",
        "location_gw",
        "test_can_bus",
        "test_cellular",
        "ready_and_working",
        "location_gw",
    )

    # def user_time_formatter(view, context, model, name) -> str:
    #     sql = text(
    #         """
    #         select timestamp_utc
    #         from public.gw_tested_cellular
    #         where gateway = :gateway
    #     """
    #     ).bindparams(
    #         gateway=model.gateway,
    #     )
    #     models = getattr(model, name)
    #     new_list = []
    #     for user in models:
    #         new_list.append("users_rel")
    #     return new_list

    column_formatters = {
        "power_units_rel": remove_unnecessary_decimal,
        "structures_rel": remove_unnecessary_decimal,
        "notes": preserve_line_breaks,
        # "test_cellular_user_rel": user_time_formatter,
    }

    column_labels = {
        "gateway_types_rel": "Gateway Type",
        "gateway_types_rel.name": "Gateway Type",
        "timestamp_utc_inserted": "Date Record Created",
        "aws_thing": "AWS IoT Unit ID",
        "mac": "MAC ID for Eurotech units",
        "customers_rel": "Customer",
        "customers_rel.customer": "Customer",
        "name": "Customer Sub-Group (Optional)",
        "cust_sub_groups_rel.name": "Customer Sub-Group (Optional)",
        "gateway": "Gateway",
        "structures_rel.structure": "Structures",
        "power_units_rel": "Power Unit Serial",
        "power_units_rel.power_unit_str": "Power Unit Serial",
        "structures_rel.downhole": "Downhole Land Location",
        "structures_rel.surface": "Surface Land Location",
        # "model_types_rel": "Model",
        "imei": "IMEI",
        "ready_and_working": "Ready and Working",
        # "apn": "Access Point Name",
        # "well_license": "Well License",
        # time_zones_rel: "Time Zone for Alerts",
        "gps_lat": "GPS Latitude",
        "gps_lon": "GPS Longitude",
        "test_can_bus": "CAN Bus Tested?",
        "test_cellular": "Cellular Working?",
        "location_gw": "Gateway Location",
        "serial_gw": "Gateway Serial",
        "model_gw": "Gateway Model/Part Num",
        "test_cellular_user_rel": "Cellular Tested?",
        "not_connected_dont_worry_rel": "Not Connected, Don't Worry",
    }

    column_descriptions = {
        "gateway_types_rel": "Either Axiomtek (new) or Eurotech (old)",
        "gateway_types_rel.name": "Either Axiomtek (new) or Eurotech (old)",
        "timestamp_utc_inserted": "Date in UTC time when the gateway record was created in this table",
        "aws_thing": "Amazon Web Services (AWS) IoT Unit ID for the AWS device shadow",
        "mac": "MAC ID for Eurotech units - not used much",
        # users_rel: "Users allowed to 'remote-control' or set targets for this unit",
        "customers_rel": "This field is an intentional DUPLICATE of the primary 'customer' field in the 'structures' table. It's only here so we can search on customer.",
        "cust_sub_groups_rel": "Customer-defined groups in which unit is categorized, for filtering on the main page",
        "gateway": "Name of the internet-connected gateway that transmits data to the cloud",
        "structures_rel": "Structures that depend on this gateway",
        "power_units_rel": "Serial number of the power unit cabinet",
        "structures_rel.downhole": "Downhole land location for UNO/UNOGAS/EGAS (combines with 'surface' location)",
        "structures_rel.surface": "Surface land location",
        "imei": "International Mobile Equipment Identity",
        # time_zones_rel: "Time zone for alerts/logs/charts",
        # "apn": "Access Point Name for SIM card to connect to customer's Internet Service Provider (ISP)",
        # "well_license": "Registered well licence number (government identifier)",
        "ready_and_working": "Has the gateway been fully tested? Is it ready for a customer?",
        "test_can_bus": "Has the CAN bus been tested?",
        "test_cellular": "Is the cellular internet actually working now?",
        "location_gw": "Where is this gateway currently located?",
        "serial_gw": "Gateway serial number on the lid, which Assured Systems tracks (e.g. E2162C1201070030-OE)",
        "model_gw": "Gateway Model/Part Num (e.g. ICO120-83D-N3350-CAN-DIO-DC)",
        "test_cellular_user_rel": "The person who tested the cellular, when, and on which network",
        "not_connected_dont_worry_rel": "Notes about gateways that aren't connected to the internet, and whether we should worry about them (see 'All' tab in RCOM)",
    }

    form_choices = {
        "location_gw": [
            ("Customer", "Customer"),
            ("Moosomin", "Moosomin"),
            ("Returned", "Returned"),
            ("Calgary Sean", "Calgary Sean"),
            ("Calgary Ging", "Calgary Ging"),
        ],
        "model_gw": [
            ("ICO120-83D-N3350-CAN-DIO-DC", "ICO120-83D-N3350-CAN-DIO-DC"),
        ],
    }

    # So we can create multiple new alerts for different users, linked to the gateway.
    # This uses the defaults for new alerts, such as "wants_sms = True" for SMS alerts, not email
    inline_models = (
        (
            GwTestedCellular,
            {
                "form_ajax_refs": {
                    # Only IJACK employees can test the cellular
                    "users_rel": QueryAjaxModelLoaderAdminCust(
                        "users_rel",
                        db.session,
                        User,
                        fields=["first_name", "last_name", "email"],
                        order_by=User.first_name,
                        placeholder="Please choose the user who tested cellular",
                        **options,
                    ),
                },
                # "form_columns": ["id", "timestamp_utc", "users_rel", "network"],
                "column_labels": {
                    "users_rel": "User Who Tested Cellular",
                    "users_rel.first_name": "First Name",
                    "users_rel.last_name": "Last Name",
                    "users_rel.email": "Email",
                    "network_rel": "Cellular Network",
                    "timestamp_utc": "Date and Time Tested (UTC timezone)",
                },
            },
        ),
        (
            GwNotConnectedDontWorry,
            {
                "form_ajax_refs": {
                    # Only IJACK employees can test the cellular, so only show them in the dropdown
                    "users_rel": QueryAjaxModelLoaderAdminCust(
                        "users_rel",
                        db.session,
                        User,
                        fields=["first_name", "last_name", "email"],
                        order_by=User.first_name,
                        placeholder="Please choose the user who says not to worry about this connection",
                        **options,
                    ),
                },
                # "form_columns": ["id", "timestamp_utc", "users_rel", "network"],
                "column_labels": {
                    "users_rel": "User Who Says Don't Worry",
                    "users_rel.first_name": "First Name",
                    "users_rel.last_name": "Last Name",
                    "users_rel.email": "Email",
                    "timestamp_utc": "Date and Time (UTC timezone)",
                },
            },
        ),
    )


class GwInfoView(MyModelView):
    """Flask-Admin view for GwInfo model (public.gw_info table)"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = True
    can_set_page_size = True
    # For my custom index view function, to start on the page paginated page
    start_on_last_page = False
    page_size = 100
    # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
    #     # This view takes a long time to open in development mode...
    #     page_size = 5

    column_list = (
        "power_unit_rel.customers_rel",
        "power_unit_str",
        "gateways_rel.gateway",
        "aws_thing",
        "time_since_reported",
        "connected",
        "hyd",
        "warn1",
        "warn2",
        "hours",
        "gateway_types_rel.name",
        "swv_canpy",
        "swv_plc",
        "suction_range",
        "has_slave",
        "modem_model",
        "modem_firmware_rev",
        "modem_drivers",
        "sim_operator",
        "os_name",
        "os_pretty_name",
        "os_version",
        "os_version_id",
        "os_release",
        "os_machine",
        "os_platform",
        "os_python_version",
        "drive_size_gb",
        "drive_used_gb",
        "memory_size_gb",
        "memory_used_gb",
        "timestamp_utc_updated",
    )

    column_sortable_list = (
        # "power_unit_rel.customers_rel",
        "power_unit_str",
        "gateways_rel.gateway",
        "aws_thing",
        "time_since_reported",
        "connected",
        "hyd",
        "warn1",
        "warn2",
        "hours",
        "gateway_types_rel.name",
        "swv_canpy",
        "swv_plc",
        "suction_range",
        "has_slave",
        "modem_model",
        "modem_firmware_rev",
        "modem_drivers",
        "sim_operator",
        "os_name",
        "os_pretty_name",
        "os_version",
        "os_version_id",
        "os_release",
        "os_machine",
        "os_platform",
        "os_python_version",
        "timestamp_utc_updated",
        "drive_size_gb",
        "drive_used_gb",
        "memory_size_gb",
        "memory_used_gb",
    )
    # True == sort descending
    column_default_sort = [
        ("os_name", False),
        ("swv_canpy", False),
        ("time_since_reported", False),
    ]

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "power_unit_rel.customers_rel",
        "power_unit_str",
        "gateways_rel.gateway",
        "aws_thing",
        "os_name",
        "os_pretty_name",
        "os_version",
        "os_version_id",
        "os_release",
        "os_machine",
        "os_platform",
        "os_python_version",
        "modem_model",
        "modem_firmware_rev",
        "modem_drivers",
        "sim_operator",
        "swv_canpy",
        "swv_plc",
    )

    column_filters = (
        "power_unit_rel.customers_rel",
        "power_unit_str",
        "gateways_rel",
        "timestamp_utc_updated",
        "connected",
        "hyd",
        "warn1",
        "warn2",
        "aws_thing",
        "hours",
        # GatewayType.name,
        "os_name",
        "os_pretty_name",
        "os_version",
        "os_version_id",
        "os_release",
        "os_machine",
        "os_platform",
        "os_python_version",
        "modem_model",
        "modem_firmware_rev",
        "modem_drivers",
        "sim_operator",
        "swv_canpy",
        "swv_plc",
        "suction_range",
        "has_slave",
        "drive_size_gb",
        "drive_used_gb",
        "memory_size_gb",
        "memory_used_gb",
    )

    column_formatters = {
        "power_units_rel": remove_unnecessary_decimal,
        "structures_rel": remove_unnecessary_decimal,
        "notes": preserve_line_breaks,
        "timestamp_utc_updated": datetime_formatter_sk_time,
        "drive_size_gb": decimal_formatter(1),
        "drive_used_gb": decimal_formatter(1),
        "memory_size_gb": decimal_formatter(1),
        "memory_used_gb": decimal_formatter(1),
        "suction_range": suction_range_formatter,
    }

    column_labels = {
        "power_unit_rel.customers_rel": "Customer",
        "power_unit_str": "Power Unit",
        "gateways_rel": "Gateway",
        "gateways_rel.gateway": "Gateway",
        "gateway_types_rel.name": "Gateway Type",
        "aws_thing": "AWS Thing",
        "hyd": "Hydraulics",
        "warn1": "Warning 1 (Motor)",
        "warn2": "Warning 2 (Hydraulics)",
        "hours": "Hours",
        "os_name": "OS Name",
        "os_pretty_name": "OS Name - Pretty",
        "os_version": "OS Version",
        "os_version_id": "OS Version ID",
        "os_release": "OS Release",
        "os_machine": "OS Machine",
        "os_platform": "OS Platform",
        "os_python_version": "OS Python Version",
        "modem_model": "Modem Model",
        "modem_firmware_rev": "Modem Firmware Rev",
        "modem_drivers": "Modem Drivers",
        "sim_operator": "Modem SIM Operator",
        "timestamp_utc_updated": "Timestamp UTC Updated",
        "swv_canpy": "CanPy SW Version",
        "swv_plc": "PLC SW Version",
        "suction_range": "Casing PSI Switch",
        "has_slave": "Has master/slave configuration",
        "time_since_reported": "Time Since Reported",
        "connected": "Connected",
        "drive_size_gb": "Drive Size (GB)",
        "drive_used_gb": "Drive Used (GB)",
        "memory_size_gb": "Memory Size (GB)",
        "memory_used_gb": "Memory Used (GB)",
    }

    column_descriptions = {
        "gateways_rel": "Gateway",
        "gateways_rel.gateway": "Gateway",
        "aws_thing": "AWS Thing",
        "hyd": "Hydraulics on or not",
        "warn1": "Warning 1 (Motor)",
        "warn2": "Warning 2 (Hydraulics)",
        "hours": "Hours the unit has been running (all time)",
        "os_name": "OS Name",
        "os_pretty_name": "OS Name - Pretty",
        "os_version": "OS Version",
        "os_version_id": "OS Version ID",
        "os_release": "OS Release",
        "os_machine": "OS Machine",
        "os_platform": "OS Platform",
        "os_python_version": "OS Python Version",
        "modem_model": "Modem Model",
        "modem_firmware_rev": "Modem Firmware Rev",
        "modem_drivers": "Modem Drivers",
        "sim_operator": "Modem SIM Operator",
        "timestamp_utc_updated": "Timestamp when record was last modified (UTC)",
        "swv_canpy": "Sean's Python software version",
        "swv_plc": "Dan's PLC software version",
        "suction_range": "Casing PSI switch where 2 = VRU Mode and 4 = Vessel Level Mode",
        "has_slave": "Has master/slave configuration",
        "time_since_reported": "How long since we last heard from this gateway?",
        "connected": "Is the gateway connected to AWS IoT?",
    }


class PowerUnitView(MyModelView):
    """Flask-Admin view for PowerUnit model (public.power_units table)"""

    # # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    # column_exclude_list = ['password_hash', ]
    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    # column_hide_backrefs = False
    column_default_sort = ("power_unit", False)  # True == sort descending
    can_set_page_size = True
    # For my custom index view function, to start on the page paginated page
    start_on_last_page = True
    page_size = 100
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        # This view takes a long time to open in development mode...
        page_size = 5

    column_list = (
        "power_unit",
        "power_unit_type_rel.name",
        "run_mfg_date",
        "website_card_msg",
        "structures_rel.notes_1",
        "notes",
        "customers_rel.customer",
        "structures_rel.model_types_rel.model",
        "structures_rel",
        "gateways_rel",
        "modbus_networks_rel",
        "fixed_ip_networks_rel",
        "id",
    )
    column_sortable_list = (
        "power_unit",
        "power_unit_type_rel.name",
        "run_mfg_date",
        "website_card_msg",
        "structures_rel.notes_1",
        "notes",
        "customers_rel.customer",
        "structures_rel.model_types_rel.model",
        # "structures_rel",
        # "gateways_rel",
        "id",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "power_unit",
        "power_unit_type_rel",
        "run_mfg_date",
        "website_card_msg",
        "notes",
        "structures_rel",  # Yes, we can change this from here!
        # "power",
        # "voltage",
        # There's an inline model for this, so we don't need it here
        # "modbus_networks_rel",
        # There's an inline model for this, so we don't need it here
        # "fixed_ip_networks_rel",
        # Alerts settings for unit
        "change_detect_sens",
        "wait_time_mins",
        "wait_time_mins_ol",
        "wait_time_mins_suction",
        "suction",
        "wait_time_mins_discharge",
        "discharge",
        "wait_time_mins_spm",
        "wait_time_mins_stboxf",
        "spm",
        "wait_time_mins_hyd_temp",
        "hyd_temp",
        "alerts_edge",
        "heartbeat_enabled",
        "online_hb_enabled",
        "wait_time_mins_hyd_oil_lvl",
        "wait_time_mins_hyd_filt_life",
        # Hydraulic oil life isn't proven yet, so default it to False
        # "wait_time_mins_hyd_oil_life",
        "hyd_oil_lvl_thresh",
        "hyd_filt_life_thresh",
        # Hydraulic oil life isn't proven yet, so default it to False
        # "hyd_oil_life_thresh",
        "wait_time_mins_ae011",
    )
    column_editable_list = (
        "power_unit",
        "power_unit_type_rel",
        "run_mfg_date",
        "website_card_msg",
        "notes",
        # There's an inline model for this, so we don't need it here
        # "modbus_networks_rel",
        # There's an inline model for this, so we don't need it here
        # "fixed_ip_networks_rel",
        # This doesn't work!
        # "structures_rel",
    )
    # column_editable_list.remove("structures_rel")

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "power_unit_str",
        "power_unit_type_rel.name",
        "notes",
        "structures_rel",
        "structures_rel.notes_1",
        "structures_rel.model_types_rel.model",
        "customers_rel.customer",
        "modbus_networks_rel.ip_address",
        "modbus_networks_rel.gateway",
        "fixed_ip_networks_rel.ip_address",
        "fixed_ip_networks_rel.gateway",
    )

    column_filters = (
        "id",
        "power_unit",
        "power_unit_type_rel.name",
        "run_mfg_date",
        "website_card_msg",
        "structures_rel",
        "modbus_networks_rel.ip_address",
        "modbus_networks_rel.subnet",
        "modbus_networks_rel.gateway",
        "fixed_ip_networks_rel.ip_address",
        "fixed_ip_networks_rel.subnet",
        "fixed_ip_networks_rel.gateway",
    )

    column_labels = {
        "power_unit": "Power Unit Serial",
        "power_unit_str": "Power Unit Serial",
        "power_unit_type_rel": "Power Unit Type",
        "power_unit_type_rel.name": "Power Unit Type",
        "run_mfg_date": "Date Manufactured",
        "website_card_msg": "Website Card AI Messages Allowed",
        "notes": "Notes",
        "structures_rel": "Structure IDs",
        "structures_rel.notes_1": "Structure Notes",
        "structures_rel.model_types_rel.model": "Model",
        "customers_rel": "Customer",
        "customers_rel.customer": "Customers",
        "power": "Power",
        "voltage": "Voltage",
        "gateways_rel": "Gateway",
        "alerts_rel": "Alerts",
        "modbus_networks_rel": "Modbus Networks",
        "modbus_networks_rel.ip_address": "Modbus IP Address",
        "modbus_networks_rel.subnet": "Modbus Subnet",
        "modbus_networks_rel.gateway": "Modbus Gateway",
        "fixed_ip_networks_rel": "Fixed IP Networks",
        "fixed_ip_networks_rel.ip_address": "Fixed IP Address",
        "fixed_ip_networks_rel.subnet": "Fixed Subnet",
        "fixed_ip_networks_rel.gateway": "Fixed Gateway",
        "alerts_edge": "Alerts from Gateway",
        "heartbeat_enabled": "Heartbeat Enabled",
        "online_hb_enabled": "Heartbeat (Online) Enabled",
        "change_detect_sens": "Change Detection Sensitivity",
        "wait_time_mins": "Alerting Wait Time Mins if Offline",
        "wait_time_mins_ol": "Alerting Wait Time Mins if Online",
        "wait_time_mins_suction": "Alerting Wait Time Mins for Suction Pressure",
        "wait_time_mins_discharge": "Alerting Wait Time Mins for Discharge Pressure",
        "wait_time_mins_spm": "Alerting Wait Time Mins for SPM",
        "wait_time_mins_stboxf": "Alerting Wait Time Mins for Stuffing Box Float Switch",
        "wait_time_mins_hyd_temp": "Alerting Wait Time Mins for Hydraulic Temperature",
        # Hydraulic oil/filter alerts
        "wait_time_mins_hyd_oil_lvl": "Alerting Wait Time Mins for Hydraulic Oil Level",
        "wait_time_mins_hyd_filt_life": "Alerting Wait Time Mins for Hydraulic Filter Life",
        "wait_time_mins_hyd_oil_life": "Alerting Wait Time Mins for Hydraulic Oil Life",
        "spm": "Strokes per Minute Threshold",
        "hyd_temp": "Hydraulic Temperature Threshold",
        "suction": "Suction Pressure Threshold",
        "discharge": "Discharge Pressure Threshold",
        # Hydraulic oil/filter alerts
        "hyd_oil_lvl_thresh": "Hydraulic Oil Level Threshold",
        "hyd_filt_life_thresh": "Hydraulic Filter Life Threshold",
        "hyd_oil_life_thresh": "Hydraulic Oil Life Threshold",
        "wait_time_mins_ae011": "Alerting Wait Time Mins for 'check motor rotation, no pressure'",
    }
    column_descriptions = {
        # power_unit": "Amazon Web Services (AWS) IoT Unit ID for the AWS device shadow",
        "power_unit_type_rel": "The type of power unit (select from list)",
        "power_unit_type_rel.name": "The type of power unit (select from list)",
        "run_mfg_date": "Date manufactured in the shop",
        "website_card_msg": "Under the card chart in RCOM, whether to show AI messages",
        "power": "Horsepower",
        "structures_rel": "Structures for which this power unit provides power",
        "customers_rel.customer": "One unit can potentially be co-owned by several customers (including the 'demo' customer)",
        "structures_rel.notes_1": "Notes from the structures table",
        "gateways_rel": "Gateway that's linked to this power unit",
        "alerts_rel": "Alerts for this power unit",
        "modbus_networks_rel": "Modbus networks needed for this power unit",
        "modbus_networks_rel.ip_address": "IP address for Modbus TCP/IP connections on ethernet interface (e.g. ************)",
        "modbus_networks_rel.subnet": "Subnet mask for Modbus TCP/IP connections on ethernet interface (e.g. 24, 16, 8, etc)",
        "modbus_networks_rel.gateway": "Default gateway for Modbus TCP/IP connections on ethernet interface (e.g. ************)",
        "fixed_ip_networks_rel": "Fixed IP addresses needed on the non-Modbus ethernet interface",
        "fixed_ip_networks_rel.ip_address": "IP address for non-Modbus TCP/IP connections on ethernet interface (e.g. ***********)",
        "fixed_ip_networks_rel.subnet": "Subnet mask for non-Modbus TCP/IP connections on ethernet interface (e.g. 24, 16, 8, etc)",
        "fixed_ip_networks_rel.gateway": "Default gateway for non-Modbus TCP/IP connections on ethernet interface (e.g. blank or something else like ***********)",
        "alerts_edge": "Alerts are sent directly from the gateway in the field (better data access), rather than from the cloud server",
        "heartbeat_enabled": "Flag for identifying old Eurotech/power units that don't transmit a 'heartbeat'",
        "online_hb_enabled": "Flag for identifying units that don't transmit an 'online heartbeat' regardless of whether the unit is stroking",
        "change_detect_sens": "For 'change detection' alerts (e.g. when suction pressure rises above a threshold). Smaller number is more sensitive, generating more alerts",
        "wait_time_mins": "For alerts: wait X minutes before alerting if a unit loses power or internet connection",
        "wait_time_mins_ol": "For alerts: wait X minutes before alerting if a unit stops stroking and has an active internet connection",
        "wait_time_mins_spm": "For alerts: wait X minutes before alerting after strokes per minute (SPM) goes to zero",
        "wait_time_mins_stboxf": "For alerts: wait X minutes before alerting after stuffing box float switch is triggered",
        "wait_time_mins_suction": "For alerts: wait X minutes before alerting after suction pressure goes above threshold",
        "wait_time_mins_discharge": "For alerts: wait X minutes before alerting after discharge pressure goes down to threshold",
        "wait_time_mins_hyd_temp": "For alerts: wait X minutes before alerting after hydraulic temperature goes above threshold",
        # Hydraulic oil/filter alerts
        "wait_time_mins_hyd_oil_lvl": "For alerts: wait X minutes before alerting after hydraulic oil level goes below threshold (%)",
        "wait_time_mins_hyd_filt_life": "For alerts: wait X minutes before alerting after hydraulic filter life level goes below threshold (%)",
        "wait_time_mins_hyd_oil_life": "For alerts: wait X minutes before alerting after hydraulic oil life level goes below threshold (%)",
        "spm": "Alert if 'strokes per minute' falls to this level (typically zero)",
        "hyd_temp": f"Alert if hydraulic temperature goes above this level (typically 65{chr(176)} Celsius)",
        "suction": "Alert if suction pressure increases to this level (typically 700 PSI)",
        "discharge": "Alert if discharge pressure falls to this level (typically zero PSI)",
        # Hydraulic oil/filter alerts
        "hyd_oil_lvl_thresh": "Alert if hydraulic oil level falls below this threshold (%)",
        "hyd_filt_life_thresh": "Alert if hydraulic filter life falls below this threshold (%)",
        "hyd_oil_life_thresh": "Alert if hydraulic oil life falls below this threshold (%)",
        "wait_time_mins_ae011": "For alerts: wait X minutes before alerting after 'check motor rotation, no pressure' is detected",
    }

    column_formatters = {
        "power_unit": remove_unnecessary_decimal,
        "structures_rel": remove_unnecessary_decimal,
        # Don't show the power unit - land location, just the gateway name
        "gateways_rel": lambda v, c, model, name: repr(model.gateways_rel),
        "run_mfg_date": lambda v, c, model, name: (
            model.run_mfg_date.strftime("%Y-%m-%d")
            if model.run_mfg_date
            else model.run_mfg_date
        ),
        "notes": preserve_line_breaks,
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "power_unit_type_rel": QueryAjaxModelLoader(
            "power_unit_type_rel",
            db.session,
            PowerUnitType,
            fields=["name"],
            order_by="name",
            placeholder="Please select a power unit type",
            **options,
        ),
        "structures_rel": QueryAjaxModelLoaderAdmin(
            "structures_rel",
            db.session,
            Structure,
            fields=["structure", "surface", "downhole", PowerUnit.power_unit_str],
            order_by="structure",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        "customers_rel": QueryAjaxModelLoader(
            "customers_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select a customer",
            **options,
        ),
        "gateways_rel": QueryAjaxModelLoader(
            "gateways_rel",
            db.session,
            Gw,
            fields=["gateway"],
            order_by="gateway",
            placeholder="Please select a gateway",
            **options,
        ),
        # "modbus_networks_rel": QueryAjaxModelLoader(
        #     "modbus_networks_rel",
        #     db.session,
        #     PowerUnitModbusNetwork,
        #     fields=["ip_address", "subnet", "gateway"],
        #     order_by="ip_address",
        #     placeholder="Please select a Modbus network",
        #     **options,
        # ),
    }

    inline_models = (
        (
            Alert,
            {
                "form_ajax_refs": {
                    "users_rel": QueryAjaxModelLoader(
                        "users_rel",
                        db.session,
                        User,
                        fields=["first_name", "last_name", "email"],
                        order_by=User.first_name,
                        placeholder="Please select a user for the alert",
                        **options,
                    ),
                    "power_units_rel": QueryAjaxModelLoader(
                        "power_units_rel",
                        db.session,
                        PowerUnit,
                        fields=["power_unit_str"],
                        order_by=PowerUnit.power_unit_str,
                        placeholder="Please select a power unit",
                        **options,
                    ),
                },
                "column_labels": alert_column_labels,
                "column_descriptions": alert_column_descriptions,
                # Must include "id" primary key, which is used for inline-form construction, for some reason...
                # https://stackoverflow.com/questions/34313253/flask-admin-inline-modelling-passing-form-arguments-throws-attributeerror
                "form_columns": ["id"]
                + [
                    col
                    for col in alert_form_columns
                    # if col not in ("gateways_rel", "gateways_rel.aws_thing", "mac")
                    if col not in ("power_unit", "power_unit_str")
                ],
            },
        ),
        (
            PowerUnitModbusNetwork,
            {
                "form_ajax_refs": {
                    "power_unit_rel": QueryAjaxModelLoader(
                        "power_unit_rel",
                        db.session,
                        PowerUnit,
                        fields=["power_unit_str"],
                        order_by=PowerUnit.power_unit_str,
                        placeholder="Please select a power unit",
                        **options,
                    ),
                },
                "column_labels": {
                    "power_unit_rel": "Power Unit",
                    "ip_address": "IP Address",
                    "subnet": "Subnet",
                    "gateway": "Gateway",
                },
                "column_descriptions": {
                    "power_unit_rel": "The power unit to which this Modbus network is connected",
                    "ip_address": "IP address for Modbus TCP/IP connections on ethernet interface (e.g. ********)",
                    "subnet": "Subnet mask for Modbus TCP/IP connections on ethernet interface (e.g. 24, 16, 8, etc)",
                    "gateway": "Default gateway for Modbus TCP/IP connections on ethernet interface (e.g. *********)",
                },
                "form_columns": ["id", "ip_address", "subnet", "gateway"],
            },
        ),
        (
            PowerUnitFixedIPNetwork,
            {
                "form_ajax_refs": {
                    "power_unit_rel": QueryAjaxModelLoader(
                        "power_unit_rel",
                        db.session,
                        PowerUnit,
                        fields=["power_unit_str"],
                        order_by=PowerUnit.power_unit_str,
                        placeholder="Please select a power unit",
                        **options,
                    ),
                },
                "column_labels": {
                    "power_unit_rel": "Power Unit",
                    "ip_address": "IP Address",
                    "subnet": "Subnet",
                    "gateway": "Gateway",
                },
                "column_descriptions": {
                    "power_unit_rel": "The power unit to which this fixed-IP network is connected",
                    "ip_address": "IP address for fixed-IP TCP/IP connections on ethernet interface (e.g. ********)",
                    "subnet": "Subnet mask for fixed-IP TCP/IP connections on ethernet interface (e.g. 24, 16, 8, etc)",
                    "gateway": "Default gateway for fixed-IP TCP/IP connections on ethernet interface (e.g. *********)",
                },
                "form_columns": ["id", "ip_address", "subnet", "gateway"],
            },
        ),
    )


class PowerUnitModbusNetworkView(MyModelView):
    """Flask-Admin view for PowerUnitModbusNetwork model (public.power_units_modbus_networks table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    # For my custom index view function, to start on the page paginated page
    start_on_last_page = False

    page_size = 100
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        # This view takes a long time to open in development mode...
        page_size = 20

    column_list = (
        "power_unit_rel.power_unit_str",
        "power_unit_rel.customers_rel",
        "ip_address",
        "subnet",
        "gateway",
        "never_default",
    )

    column_sortable_list = (
        "power_unit_rel.power_unit_str",
        # "power_unit_rel.customers_rel",
        "ip_address",
        "subnet",
        "gateway",
        "never_default",
    )

    column_searchable_list = (
        "power_unit_rel.power_unit_str",
        # "power_unit_rel.customers_rel",
        "ip_address",
        "subnet",
        "gateway",
    )

    column_filters = (
        "power_unit_rel.power_unit_str",
        # "power_unit_rel.customers_rel",
        "ip_address",
        "subnet",
        "gateway",
        "never_default",
    )

    form_columns = (
        "power_unit_rel",
        "ip_address",
        "subnet",
        "gateway",
        "never_default",
    )

    column_editable_list = (
        "ip_address",
        "subnet",
        "gateway",
        "never_default",
    )

    column_labels = {
        "power_unit_rel": "Power Unit",
        "power_unit_rel.power_unit_str": "Power Unit",
        "power_unit_rel.customers_rel": "Customer",
        "ip_address": "IP Address",
        "subnet": "Subnet",
        "gateway": "Gateway",
        "never_default": "Never Default",
    }

    column_descriptions = {
        "power_unit_rel": "The power unit to which this Modbus network is connected",
        "power_unit_rel.power_unit_str": "The power unit to which this Modbus network is connected",
        "power_unit_rel.customers_rel": "The customer who owns this power unit",
        "ip_address": "IP address for Modbus TCP/IP connections on ethernet interface (e.g. ********)",
        "subnet": "Subnet mask for Modbus TCP/IP connections on ethernet interface (e.g. 24, 16, 8, etc)",
        "gateway": "Default gateway for Modbus TCP/IP connections on ethernet interface (e.g. *********)",
        "never_default": "If true, this network will never be the default Internet network for the power unit (e.g. usually true for Modbus connections on eth1 and false from non-Modbus connections on eth2)",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "power_unit_rel": QueryAjaxModelLoader(
            "power_unit_rel",
            db.session,
            PowerUnit,
            fields=["power_unit_str"],
            order_by=PowerUnit.power_unit_str,
            placeholder="Please select a power unit",
            **options,
        ),
    }


class GwNotConnectedDontWorryView(MyModelView):
    """Flask-Admin view for GwNotConnectedDontWorry model (public.gw_not_connected_dont_worry table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True

    column_list = [
        "gateways_rel.gateway",
        "gateways_rel.gateway_types_rel",
        # "customers_rel",
        "surface",
        "timestamp_utc",
        "users_rel.first_name",
        "users_rel.last_name",
        "am_i_worried",
        # User.first_name,
        # User.last_name,
        "notes",
        # "test_cellular_user_rel",
        "gateway_info_days_since_reported",
        "operators_contacted",
    ]
    column_sortable_list = [
        "gateways_rel.gateway",
        "timestamp_utc",
        "notes",
    ]
    # True == sort descending
    column_default_sort = [
        # (Gw.customers, False),
        # ("customers_rel", False),
        # (Gw.gateway_info_days_since_reported, False),
        ("timestamp_utc", False),
        ("gateways_rel.gateway", False),
    ]
    can_set_page_size = True
    # For my custom index view function, to start on the page paginated page
    start_on_last_page = False
    page_size = 20
    # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
    #     # This view takes a long time to open in development mode...
    #     page_size = 5

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "gateways_rel.gateway",
        "notes",
        # "customers_rel",
        "gateways_rel.gateway_types_rel",
        # Association proxy
        "gateways_rel.power_unit_str",
    )
    column_editable_list = (
        # "gateways_rel",
        "timestamp_utc",
        "am_i_worried",
        "notes",
        "operators_contacted",
    )

    column_filters = (
        "timestamp_utc",
        User.first_name,
        User.last_name,
        User.email,
        "am_i_worried",
        # Association proxy works fine
        "gateways_rel.power_unit_str",
        "operators_contacted",
    )

    column_formatters = {
        "notes": preserve_line_breaks,
        "timestamp_utc": datetime_formatter_sk_time,
        "gateways_rel": format_gateway_power_unit,
        "gateway_info_days_since_reported": lambda v,
        c,
        model,
        name: f"{getattr(model, name)[0]} days",
        # "operators_contacted": datetime_formatter_sk_time,
    }

    def on_model_change(self, form, model, is_created):
        """Convert the line breaks back to newline chars"""
        model.notes = str(model.notes).replace("<br>", "\n")

    column_labels = {
        "gateways_rel": "Gateway",
        "gateways_rel.gateway": "Gateway",
        "gateways_rel.gateway_types_rel": "Gateway Type",
        "gateways_rel.power_unit_str": "Power Unit",
        # "customers_rel": "Customers",
        "timestamp_utc": "Timestamp",
        "notes": "Notes",
        "users_rel": "User Creating this Note",
        "users_rel.first_name": "First Name",
        "users_rel.last_name": "Last Name",
        "gateway_info_days_since_reported": "Last Reported",
        "am_i_worried": "Am I Worried?",
        "operators_contacted": "Operators Contacted",
    }

    column_descriptions = {
        "gateways_rel": "As of this date, don't worry about this gateway not being connected",
        "gateways_rel.gateway": "As of this date, don't worry about this gateway not being connected",
        "timestamp_utc": "This record was created at this time (SK time)",
        "notes": "Notes about why we shouldn't worry about this gateway not being connected",
        "gateway_info_days_since_reported": "How many days since this unit last connected to AWS IoT?",
        "am_i_worried": "Am I worried about this unit not being connected?",
        "users_rel": "To record who created this note",
        "operators_contacted": "When were the operators contacted?",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "gateways_rel": QueryAjaxModelLoaderAdmin(
            "gateways_rel",
            db.session,
            Gw,
            # Fields against which to run query
            fields=[
                "gateway",
                "aws_thing",
                # Must use the "get_query_func" to get the association proxy fields to work
                PowerUnit.power_unit_str,
                Structure.surface,
            ],
            # order_by=Gw.aws_thing,
            order_by="aws_thing",
            placeholder="Please select the gateway we're not to worry about",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Gw.query.join(PowerUnit).join(Structure),
            **options,
        ),
        # Only get IJACK employees in this dropdown
        "users_rel": QueryAjaxModelLoaderAdminCust(
            "users_rel",
            db.session,
            # Fields against which to run query
            User,
            fields=["first_name", "last_name", "email"],
            order_by=User.first_name,
            placeholder="Please select the user who says not to worry",
            **options,
        ),
    }


class StructureView(MyModelView):
    """Flask-Admin view for Structure model (public.structures table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    # For my custom index view function, to start on the page paginated page
    start_on_last_page = True
    page_size = 100

    column_list = (
        "structure",
        "power_units_rel",
        # "power_units_rel.power_unit_str",
        "qb_sale",
        # 'qb_install',
        "model_types_rel",
        # "model_types_rel.model",
        "unit_types_rel",
        # "unit_types_rel.unit_type",
        "power_units_rel.power_unit_type_rel.name",
        "op_months_interval",
        "run_mfg_date",
        "structure_install_date",
        "customers_rel",
        "cust_sub_groups_rel",
        "warehouse_rel",
        "surface",
        "downhole",
        "time_zones_rel",
        "gps_lat",
        "gps_lon",
        "has_rcom",
        "structure_slave_rel",
        "slave_install_date",
        "model_type_slaves_rel",
        "notes_1",
        # 'notes_2',
        "power_units_rel.notes",
        "on_genset",
        "auto_greaser",
        "rod_rel",
        "barrel_rel",
        "shuttle_valve_rel",
        "check_valve_rel",
        "packing_gland_rel",
        "hyd_piston_type_rel",
        "well_license",
        "dgas_pumpjack",
        "gateways_rel.aws_thing",
        "operators_users_rel",
        "maintenance_users_rel",
        "sales_users_rel",
        # "users_rel",
        # 'afe',
        # 'dgas_pumpjack',
        # 'area',
        # 'status',
        # 'casing_sensor',
        # 'tundra_name',
        "id",
    )
    column_sortable_list = (
        "structure",
        # "power_units_rel.power_unit_str",
        "qb_sale",
        # 'qb_install',
        # "model_types_rel.model",
        # "unit_types_rel.unit_type",
        "power_units_rel.power_unit_type_rel.name",
        "on_genset",
        "auto_greaser",
        "op_months_interval",
        "run_mfg_date",
        "structure_install_date",
        "slave_install_date",
        "customers_rel.customer",
        # "cust_sub_groups_rel.name",
        "downhole",
        "surface",
        "time_zones_rel.time_zone",
        "gps_lat",
        "gps_lon",
        "has_rcom",
        # "warehouse_rel.name",
        "notes_1",
        "power_units_rel.notes",
        # "rod_rel.name",
        # "barrel_rel.name",
        # "shuttle_valve_rel.name",
        # "check_valve_rel.name",
        # "packing_gland_rel.name",
        # "hyd_piston_type_rel.name",
        "well_license",
        "dgas_pumpjack",
        "gateways_rel.aws_thing",
        "id",
    )
    # True == sort descending
    column_default_sort = ("structure", False)

    # column_type_formatters = SEAN_FORMATTERS
    # column_type_formatters = {}

    # Control the order of the columns in the forms
    form_columns = (
        "structure",
        "structure_slave_rel",
        "power_units_rel",
        # Users with remote control permissions
        "users_rel",
        # Users who operate the unit but don't have remote control permissions
        "operators_users_rel",
        # Users who maintain the unit
        "maintenance_users_rel",
        # Users who sold the unit
        "sales_users_rel",
        "on_genset",
        "auto_greaser",
        "auto_greaser_needs_alerts",
        "op_months_interval",
        "qb_sale",
        # 'qb_install',
        "model_types_rel",
        "model_type_slaves_rel",
        "unit_types_rel",
        # # The power unit stuff can't be changed from the structures table
        # PowerUnit.power_unit_type_rel,
        "run_mfg_date",
        "structure_install_date",
        "slave_install_date",
        "customers_rel",
        "cust_sub_groups_rel",
        "downhole",
        "surface",
        "time_zones_rel",
        "gps_lat",
        "gps_lon",
        "has_rcom",
        "warehouse_rel",
        "notes_1",
        # 'notes_2',
        "well_license",
        "afe",
        "rod_rel",
        "barrel_rel",
        "shuttle_valve_rel",
        "packing_gland_rel",
        "hyd_piston_type_rel",
        "check_valve_rel",
        "dgas_pumpjack",
        # 'casing_sensor',
        # 'area',
        # 'status',
        # 'tundra_name',
    )
    column_editable_list = [
        col
        for col in form_columns
        # Many-to-many fields can't be edited from the structures table
        if col
        not in (
            "users_rel",
            "customers_rel",
            "cust_sub_groups_rel",
            "operators_users_rel",
            "maintenance_users_rel",
            "sales_users_rel",
        )
    ]

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "structure",
        "power_units_rel.power_unit_str",
        "gateways_rel.aws_thing",
        "qb_sale",
        # 'qb_install',
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "model_type_slaves_rel",
        "well_license",
        "surface",
        "downhole",
        "warehouse_rel.name",
        "time_zones_rel.time_zone",
        "customers_rel.customer",
        "cust_sub_groups_rel.name",
        "notes_1",
        "rod_rel.name",
        "barrel_rel.name",
        "shuttle_valve_rel.name",
        "packing_gland_rel.name",
        "hyd_piston_type_rel.name",
        "check_valve_rel.name",
        "dgas_pumpjack",
        "power_units_rel.power_unit_type_rel.name",
        "id",
    )
    column_filters = (
        "id",
        "structure",
        "structure_slave_rel.structure",
        "power_units_rel",
        "power_units_rel.power_unit_str",
        "gateways_rel.aws_thing",
        "qb_sale",
        "customers_rel",
        "cust_sub_groups_rel.name",
        "unit_types_rel",
        "model_types_rel",
        "structure_install_date",
        "on_genset",
        "auto_greaser",
        "op_months_interval",
        "power_units_rel.power_unit_type_rel.name",
        "warehouse_rel.name",
        "time_zones_rel.time_zone",
        "gps_lat",
        "gps_lon",
        "has_rcom",
        "notes_1",
        "rod_rel.name",
        "barrel_rel.name",
        "shuttle_valve_rel.name",
        "packing_gland_rel.name",
        "hyd_piston_type_rel.name",
        "check_valve_rel.name",
    )

    column_formatters = {
        # `view` is current administrative view
        # `context` is instance of jinja2.runtime.Context
        # `model` is model instance
        # `name` is property name
        # Format your column string with lambda here (e.g show first 20 characters).
        # Can return any valid HTML (e.g. a link to another view to show the detail or a popup window).
        # 'notes_1': lambda v, c, m, n: m.notes_1[:30] if isinstance(m.notes_1, str) else m.notes_1,
        # 'notes_2': lambda v, c, m, n: m.notes_2[:30] if isinstance(m.notes_2, str) else m.notes_2,
        # 'power_units_rel.notes': lambda v, c, m, n: m.power_units_rel.notes[:30] if isinstance(m.power_units_rel.notes, str) else m.power_units_rel.notes,
        "gps_lat": lambda v, c, m, n: str(m.gps_lat)[:7],
        "gps_lon": lambda v, c, m, n: str(m.gps_lon)[:7],
        "structure": remove_unnecessary_decimal,
        "structure_slave_rel": remove_unnecessary_decimal,
        "power_units_rel": remove_unnecessary_decimal,
        "notes_1": preserve_line_breaks,
        "users_rel": many_to_many_formatter,
        "operators_users_rel": many_to_many_formatter,
        "maintenance_users_rel": many_to_many_formatter,
        "sales_users_rel": many_to_many_formatter,
        "cust_sub_groups_rel": many_to_many_formatter,
        # "op_months_interval": format_like_integer,
    }

    column_labels = {
        "structure": "Main Structure SN",
        "structure_slave_rel": "Slave Structure SN",
        "power_units_rel": "Power Unit SN",
        "power_units_rel.power_unit_str": "Power Unit",
        "power_units_rel.power_unit_type_rel.name": "Power Unit Type",
        "power_units_rel.notes": "Power Unit Notes",
        "on_genset": "On Genset",
        "auto_greaser": "Auto Greaser",
        "auto_greaser_needs_alerts": "Need Auto Greaser Alerts?",
        "gateways_rel.aws_thing": "Gateway",
        "qb_sale": "QB Invoices",
        "model_types_rel": "Main Type",
        "model_types_rel.model": "Main Type",
        "unit_types_rel": "Unit Type",
        "unit_types_rel.unit_type": "Unit Type",
        "model_type_slaves_rel": "Slave Type",
        "customers_rel": "Customers",
        "cust_sub_groups_rel": "Customer-Defined Sub-Groups",
        "cust_sub_groups_rel.name": "Customer-Defined Sub-Groups",
        "afe": "AFE",
        "dgas_pumpjack": "DGAS Pumpjack",
        "op_months_interval": "Operating Months b/w Maintenance",
        "run_mfg_date": "Date Main Structure Manufactured",
        "structure_install_date": "Startup Date",
        "slave_install_date": "Secondary Unit Startup Date",
        "well_license": "Well License",
        "surface": "Surface Location",
        "downhole": "Downhole Location",
        "time_zones_rel": "Time Zone for Alerts",
        "gps_lat": "GPS Lat",
        "gps_lon": "GPS Lon",
        "has_rcom": "Has RCOM?",
        "warehouse_rel": "Warehouse",
        "warehouse_rel.name": "Warehouse",
        "notes_1": "Structure Notes",
        "users_rel": "Users with Remote-Control Permissions",
        "rod_rel.name": "Rod",
        "rod_rel": "Rod",
        "barrel_rel.name": "Barrel",
        "barrel_rel": "Barrel",
        "shuttle_valve_rel.name": "Shuttle Valve",
        "shuttle_valve_rel": "Shuttle Valve",
        "packing_gland_rel.name": "Packing Gland",
        "packing_gland_rel": "Packing Gland",
        "hyd_piston_type_rel.name": "Hydraulic Piston Type",
        "hyd_piston_type_rel": "Hydraulic Piston Type",
        "check_valve_rel.name": "Check Valve",
        "check_valve_rel": "Check Valve",
        "operators_users_rel": "Operators",
        "maintenance_users_rel": "Maintenance People",
        "sales_users_rel": "Sales People",
    }
    column_descriptions = {
        "unit_types_rel": "High-level category of either UNO, EGAS, XFER, DGAS, or UNOGAS. We need this for UNOGAS types especially, for proper grouping on RCOM.",
        "model_types_rel": "Specific model type (more specific than unit type) of the Main structure. If UNOGAS unit type, this column tells you if it's an UNO or EGAS",
        "model_type_slaves_rel": "Specific model type for the slave unit (e.g. the EGAS on an UNOGAS)",
        "customers_rel": "One unit can potentially be co-owned by several customers (including the 'demo' customer)",
        "cust_sub_groups_rel": "Customer-defined sub-group(s) for RCOM grouping in the dynamic list",
        "cust_sub_groups_rel.name": "Customer-defined sub-group(s) for RCOM grouping in the dynamic list",
        "structure": "Main unit structure ID",
        "structure_slave_rel": "Slave unit structure ID (e.g. the EGAS on an UNOGAS)",
        "power_units_rel": "Power Unit serial ID",
        "power_units_rel.power_unit_type_rel.name": "Type of power unit",
        "power_units_rel.notes": "Notes on this power unit",
        "on_genset": "Is this unit powered by a generator/genset? Helps with alerts.",
        "auto_greaser": "Does the unit have an auto-greaser? Important for 6-month alerts",
        "auto_greaser_needs_alerts": "If the unit has an auto-greaser, does it need alerts? Some units have auto-greasers but don't need alerts (e.g. CP 200473 and 200474)",
        "op_months_interval": "Operating months between required preventative maintenance",
        "gateways_rel.aws_thing": "Gateway ID in AWS IoT",
        "dgas_pumpjack": "If DGAS unit type, the type of pumpjack on which the DGAS is installed",
        "slave_install_date": "If UNOGAS unit type, the date on which the EGAS was added to the UNO to make an UNOGAS unit",
        "run_mfg_date": "Date on which the unit was manufactured",
        "structure_install_date": "Date on which the unit was started up for the customer, not the date on which it was installed",
        "downhole": "Downhole land location for UNO/UNOGAS/EGAS (combines with 'surface' location)",
        "surface": "Surface land location",
        "time_zones_rel": "Time zone for alerts/logs/charts",
        "qb_sale": "QuickBooks invoices relating to this unit",
        "gps_lat": "GPS latitude for Google Maps",
        "gps_lon": "GPS longitude for Google Maps",
        "has_rcom": "Does this unit have RCOM? DGAS and XTO units, for example, do not have RCOM",
        "warehouse_rel": "Warehouse to which the unit is assigned, based on GPS distance",
        "warehouse_rel.name": "Warehouse to which the unit is assigned, based on GPS distance",
        "users_rel": "Users allowed to 'remote-control' or set targets for this unit",
        "operators_users_rel": "Users who operate this unit but do not have remote-control permissions",
        "maintenance_users_rel": "Users responsible for maintaining this unit (i.e. preventative maintenance)",
        "sales_users_rel": "Users who sold this unit",
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "power_units_rel": QueryAjaxModelLoader(
            "power_units_rel",
            db.session,
            PowerUnit,
            fields=[PowerUnit.power_unit_str],
            order_by="power_unit",
            placeholder="Please select a power unit",
            **options,
        ),
        "structure_slave_rel": QueryAjaxModelLoader(
            "structure_slave_rel",
            db.session,
            Structure,
            fields=[Structure.structure],
            order_by="structure",
            placeholder="Please select a structure",
            **options,
        ),
        "model_types_rel": QueryAjaxModelLoader(
            "model_types_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select a model",
            **options,
        ),
        "model_type_slaves_rel": QueryAjaxModelLoader(
            "model_type_slaves_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            **options,
        ),
        "unit_types_rel": QueryAjaxModelLoader(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select a unit type",
            **options,
        ),
        "customers_rel": QueryAjaxModelLoaderAdmin(
            "customers_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select a customer",
            **{
                "minimum_input_length": 0,
                # "many_to_many_relationship_table": True,
            },
        ),
        "cust_sub_groups_rel": QueryAjaxModelLoader(
            "cust_sub_groups_rel",
            db.session,
            CustSubGroup,
            fields=["name"],
            order_by="name",
            placeholder="Please select a customer sub-group",
            **options,
        ),
        "rod_rel": QueryAjaxModelLoader(
            "rod_rel",
            db.session,
            Rod,
            fields=["name"],
            order_by="name",
            placeholder="Please select a rod type",
            **options,
        ),
        "barrel_rel": QueryAjaxModelLoader(
            "barrel_rel",
            db.session,
            Barrel,
            fields=["name"],
            order_by="name",
            placeholder="Please select a barrel type",
            **options,
        ),
        "shuttle_valve_rel": QueryAjaxModelLoader(
            "shuttle_valve_rel",
            db.session,
            ShuttleValve,
            fields=["name"],
            order_by="name",
            placeholder="Please select a shuttle valve type",
            **options,
        ),
        "packing_gland_rel": QueryAjaxModelLoader(
            "packing_gland_rel",
            db.session,
            PackingGland,
            fields=["name"],
            order_by="name",
            placeholder="Please select a packing gland type",
            **options,
        ),
        "hyd_piston_type_rel": QueryAjaxModelLoader(
            "hyd_piston_type_rel",
            db.session,
            HydPistonType,
            fields=["name"],
            order_by="name",
            placeholder="Please select a hydraulic piston type",
            **options,
        ),
        "check_valve_rel": QueryAjaxModelLoader(
            "check_valve_rel",
            db.session,
            CheckValve,
            fields=["name"],
            order_by="name",
            placeholder="Please select a check valve type",
            **options,
        ),
        "time_zones_rel": QueryAjaxModelLoader(
            "time_zones_rel",
            db.session,
            TimeZone,
            fields=["time_zone"],
            order_by="time_zone",
            placeholder="Please select a time zone",
            get_query_func=timezone_filtering_function,
            **options,
        ),
        "warehouse_rel": QueryAjaxModelLoader(
            "warehouse_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select a warehouse",
            **options,
        ),
        "users_rel": QueryAjaxModelLoader(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select the people who can remote-control this unit",
            **options,
        ),
        "operators_users_rel": QueryAjaxModelLoaderAdmin(
            "operators_users_rel",
            db.session,
            User,
            fields=[User.first_name, User.last_name, User.email],
            order_by=User.first_name,
            placeholder="Please select the operators for this unit",
            # get_query_func=lambda: User.query.filter(User.role == "operator"),
            **options,
        ),
        "maintenance_users_rel": QueryAjaxModelLoaderAdmin(
            "maintenance_users_rel",
            db.session,
            User,
            fields=[User.first_name, User.last_name, User.email],
            order_by=User.first_name,
            placeholder="Please select the maintenance people for this unit",
            # get_query_func=lambda: User.query.filter(User.role == "maintenance"),
            **options,
        ),
        "sales_users_rel": QueryAjaxModelLoaderAdmin(
            "sales_users_rel",
            db.session,
            User,
            fields=[User.first_name, User.last_name, User.email],
            order_by=User.first_name,
            placeholder="Please select the sales people for this unit",
            # get_query_func=lambda: User.query.filter(User.role == "sales"),
            **options,
        ),
    }

    # # Change the width of the form input in the edit modal
    # form_widget_args = {
    #     'power_unit': {
    #         'rows': 11,
    #         'class': 'input-large'
    #     }
    # }

    def update_model(self, form, model):
        """Override this method to change the model before it's updated"""

        # Make a copy of the old surface location before it's updated, so we can compare it later
        # Strings in Python are immutable, so you don’t need to create a copy of them
        self.old_surface = str(model.surface)

        return super().update_model(form, model)

    def on_model_change(self, form, model, is_created):
        """When someone changes a record, auto-update the unit_type from the model_type"""

        if hasattr(model, "model_types_rel") and model.model_types_rel:
            if hasattr(model, "unit_types_rel") and not model.unit_types_rel:
                if (
                    hasattr(model.model_types_rel, "unit_types_rel")
                    and model.model_types_rel.unit_types_rel
                ):
                    model.unit_types_rel = model.model_types_rel.unit_types_rel
                    # The following doesn't show up when updating with inline editing/AJAX, only when using the form
                    flash(
                        gettext(
                            f"Unit type saved as {model.unit_types_rel} based on model {model.model_types_rel}"
                        ),
                        "warning",
                    )

        if is_created:
            # There's a different op months interval for each unit/model type
            if model.unit_type_id == UNIT_TYPE_ID_DGAS:
                model.op_months_interval = 12
            elif model.model_type_id in (
                MODEL_TYPE_ID_EGAS_823,
                MODEL_TYPE_ID_VRU_823,
                MODEL_TYPE_ID_EGAS_828,
                MODEL_TYPE_ID_VRU_828,
            ):
                model.op_months_interval = 24
            else:
                model.op_months_interval = 9

    def after_model_change(self, form, model, is_created):
        """
        Check to see if the "surface" field has changed, and if so, update the GPS coordinates.
        Do this after the model has changed, so we can start a new database transaction.
        """
        if "surface" in form.data.keys() and form.data["surface"] != self.old_surface:
            get_or_update_gps(
                structure_obj=model,
                reason=f"""IJACK user {current_user} changed surface location in admin site.
Power unit {model.power_units_rel}. \n\nOld: '{self.old_surface}'. \nNew: '{form.data["surface"]}'. \n\nUpdating the GPS coordinates now...""",
            )


class StructurePerformanceView(MyModelView):
    """Calculated or manually-input performance data for structures"""

    can_create = False
    can_edit = True
    can_delete = False
    column_display_pk = True
    # column_hide_backrefs = False
    column_default_sort = "structure"
    column_sortable_list = ("structure",)
    can_set_page_size = True
    page_size = 20
    # Duplicate button - make None to disable
    column_extra_row_actions = None

    column_list = (
        "structure",
        "power_units_rel.power_unit_str",
        "customers_rel.customer",
        "cust_sub_groups_rel.name",
        "surface",
        "downhole",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "max_spm",
        "max_suction",
        "max_discharge",
        "max_delta_p",
        "max_discharge_temp",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "max_spm",
        "max_suction",
        "max_discharge",
        "max_delta_p",
        "max_discharge_temp",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "structure",
        "power_units_rel.power_unit_str",
        "customers_rel.customer",
        "cust_sub_groups_rel.name",
        "surface",
        "downhole",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
    )
    column_filters = (
        "structure",
        "power_units_rel.power_unit_str",
        "customers_rel.customer",
        "cust_sub_groups_rel.name",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "max_spm",
        "max_suction",
        "max_discharge",
        "max_delta_p",
        "max_discharge_temp",
    )
    column_sortable_list = (
        "structure",
        "power_units_rel.power_unit_str",
        "customers_rel.customer",
        "cust_sub_groups_rel.name",
        "surface",
        "downhole",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "max_spm",
        "max_suction",
        "max_discharge",
        "max_delta_p",
        "max_discharge_temp",
    )

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "max_spm",
        "max_suction",
        "max_discharge",
        "max_delta_p",
        "max_discharge_temp",
    )
    column_formatters = {
        "structure": remove_unnecessary_decimal,
        "power_units_rel": remove_unnecessary_decimal,
    }

    column_labels = {
        "structure": "Structure",
        "power_units_rel.power_unit_str": "Power Unit",
        "customers_rel.customer": "Customer",
        "cust_sub_groups_rel.name": "Customer Sub-Group",
        "surface": "Surface",
        "downhole": "Downhole",
        "unit_types_rel.unit_type": "Unit Type",
        "model_types_rel.model": "Model Type",
        "max_spm": "Max SPM",
        "max_suction": "Max Suction",
        "max_discharge": "Max Discharge",
        "max_delta_p": "Max Delta P",
        "max_discharge_temp": "Max Discharge Temp",
    }
    column_descriptions = {
        "unit_types_rel": "High-level category of either UNO, EGAS, XFER, DGAS, or UNOGAS. We need this for UNOGAS types especially, for proper grouping on RCOM.",
        "model_types_rel": "Specific model type (more specific than unit type) of the Main structure. If UNOGAS unit type, this column tells you if it's an UNO or EGAS",
        "model_type_slaves_rel": "Specific model type for the slave unit (e.g. the EGAS on an UNOGAS)",
        "customers_rel": "One unit can potentially be co-owned by several customers (including the 'demo' customer)",
        "cust_sub_groups_rel": "Customer-defined sub-group for RCOM grouping in the dynamic list",
        "structure": "Main unit structure ID",
        "structure_slave_rel": "Slave unit structure ID (e.g. the EGAS on an UNOGAS)",
        "power_units_rel": "Power Unit serial ID",
        "power_units_rel.power_unit_type_rel.name": "Type of power unit",
        "max_spm": "Maximum strokes per minute",
        "max_suction": "Maximum suction pressure",
        "max_discharge": "Maximum discharge pressure",
        "max_delta_p": "Maximum delta of pressure",
        "max_discharge_temp": "Maximum discharge temperature",
    }


class PowerUnitTypeView(MyModelView):
    """Flask-Admin view for PowerUnitType model (public.power_unit_types table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    # column_hide_backrefs = False
    can_set_page_size = True
    # This page is a little slow to load with all the many-to-many relationships displayed
    page_size = 10

    column_list = (
        "part_rel",
        "name",
        "description",
        "bom_base_powerunit_rel.name",
        "bom_powerunit_rel.name",
        "parts_rel.part_num",
        "filters_rel.part_num",
        "power_units_rel.power_unit_str",
        "id",
    )

    column_default_sort = "name"
    column_sortable_list = (
        # "part_rel",
        "name",
        "description",
        # "bom_base_powerunit_rel.name",
        # "power_units_rel.power_unit_str",
        "id",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "part_rel",
        "name",
        "description",
        "bom_base_powerunit_rel",
        "bom_powerunit_rel",
        "parts_rel",
        "filters_rel",
        "power_units_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "part_rel.part_name",
        "name",
        "description",
        # NOTE: These don't work because they're many-to-many relationships
        # "bom_base_powerunit_rel.name",
        # "power_units_rel.power_unit_str",
    )
    column_filters = (
        "part_rel",
        "name",
        "description",
        #   "bom_base_powerunit_rel.name", "power_units_rel.power_unit_str"
    )

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "part_rel",
        "name",
        "description",
    )

    column_labels = {
        "part_rel": "Part Number",
        "part_rel.part_num": "Part Number",
        "name": "Power Unit Type",
        "description": "Description",
        "bom_base_powerunit_rel": "BOM Base Powerunits Needed",
        "bom_base_powerunit_rel.name": "BOM Base Powerunits Needed",
        "bom_powerunit_rel": "BOM Powerunits Needed",
        "bom_powerunit_rel.name": "BOM Powerunits Needed",
        "parts_rel": "Hydraulic Oil",
        "parts_rel.part_num": "Hydraulic Oil",
        "filters_rel.part_num": "Oil Filter Parts",
        "filters_rel": "Oil Filter Parts",
        "power_units_rel": "Power Units in Field",
        "power_units_rel.power_unit_str": "Power Units in Field",
    }
    column_descriptions = {
        "part_rel": "Part number of the power unit type",
        "part_rel.part_num": "Part number of the power unit type",
        "name": "Name of the power unit type",
        "description": "Description of the power unit type",
        "bom_base_powerunit_rel.name": "BOM Base Powerunit finished goods that use this power unit type",
        "bom_base_powerunit_rel": "BOM Base Powerunit finished goods that use this power unit type",
        "bom_powerunit_rel.name": "BOM Powerunit finished goods that use this power unit type",
        "bom_powerunit_rel": "BOM Powerunit finished goods that use this power unit type",
        "power_units_rel": "Power units in the field that use this power unit type",
        "power_units_rel.power_unit_str": "Power units in the field that use this power unit type",
        "parts_rel": "Hydraulic oil needed in this power unit type",
        "parts_rel.part_num": "Hydraulic oil needed in this power unit type",
        "filters_rel.part_num": "Select the oil filters the unit needs for its six-month maintenance",
        "filters_rel": "Select the oil filters the unit needs for its six-month maintenance",
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "part_rel": QueryAjaxModelLoader(
            "part_rel",
            db.session,
            Part,
            fields=["part_num", "part_name"],
            order_by="part_num",
            placeholder="Please select the part number for this power unit type",
            **options,
        ),
        "power_units_rel": QueryAjaxModelLoader(
            "power_units_rel",
            db.session,
            PowerUnit,
            fields=["power_unit_str"],
            order_by="power_unit_str",
            placeholder="Please select the power units in the field that use this power unit type",
            **options,
        ),
    }

    column_formatters = {
        "bom_base_powerunit_rel.name": many_to_many_formatter,
        "bom_powerunit_rel.name": many_to_many_formatter,
        "parts_rel.part_num": many_to_many_formatter,
        "power_units_rel.power_unit_str": many_to_many_formatter,
        "filters_rel.part_num": many_to_many_formatter,
    }

    inline_models = (
        # BOM Base Powerunit parts
        (
            BOMBasePowerUnitPowerUnitType,
            {
                "form_ajax_refs": {
                    "bom_base_powerunit_rel": QueryAjaxModelLoader(
                        "bom_base_powerunit_rel",
                        db.session,
                        BOMBasePowerUnit,
                        fields=["name"],
                        order_by="name",
                        placeholder="Please select the base powerunit used in this model",
                        **options,
                    ),
                },
                "column_labels": {
                    "finished_good_rel": "Base Powerunit Type",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "quantity": "Quantity needed for this base powerunit type (sometimes more than one required)",
                },
            },
        ),
        # BOM Powerunit parts
        (
            BOMPowerUnitPowerUnitType,
            {
                "form_ajax_refs": {
                    "bom_powerunit_rel": QueryAjaxModelLoader(
                        "bom_powerunit_rel",
                        db.session,
                        BOMPowerUnit,
                        fields=["name"],
                        order_by="name",
                        placeholder="Please select the powerunit type used in this model",
                        **options,
                    ),
                },
                "column_labels": {
                    "finished_good_rel": "Powerunit Type",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "quantity": "Quantity needed for this powerunit type (sometimes more than one required)",
                },
            },
        ),
        # Hydraulic oil parts
        (
            PowerUnitTypePart,
            {
                "form_ajax_refs": {
                    "parts_rel": QueryAjaxModelLoader(
                        "parts_rel",
                        db.session,
                        Part,
                        fields=["part_num"],
                        order_by="part_num",
                        placeholder="Please select the hydraulic oil used in this power unit type",
                        **options,
                    ),
                },
                "column_labels": {
                    "part_rel": "Hydraulic Oil",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "part_rel": "Hydraulic oil used in this power unit type",
                    "quantity": "Quantity needed for this power unit type (sometimes more than one required)",
                },
            },
        ),
        # Oil filter parts
        (
            PowerUnitTypePartFilter,
            {
                "form_ajax_refs": {
                    "filters_rel": QueryAjaxModelLoader(
                        "filters_rel",
                        db.session,
                        PartFilter,
                        fields=["part_num"],
                        order_by="part_num",
                        placeholder="Please select the oil filter used in this power unit type",
                        **options,
                    ),
                },
                "column_labels": {
                    "part_filter_rel": "Oil Filter Part Number",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "part_filter_rel": "Oil filter part number used in this power unit type",
                    "quantity": "Quantity needed for this power unit type (sometimes more than one required)",
                },
            },
        ),
    )


class SIMCardView(MyModelView):
    """Flask-Admin view for SIMCard model (public.sim_cards table)"""

    # # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    # column_exclude_list = ['password_hash', ]
    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    # column_hide_backrefs = False

    column_list = (
        "sim_card",
        "sim_card_num",
        "customers_rel.customer",
        "gateways_rel.gateway",
        "sim_card_activated",
        "sim_card_phone",
        "cell_provider",
        "location",
        "notes",
        "id",
    )
    # column_list = ('power_units_rel', 'run_mfg_date', 'power', 'voltage', 'notes', )

    # Control the order of the columns in the forms
    form_columns = (
        "sim_card",
        "sim_card_num",
        "customers_rel",
        "gateways_rel",
        "sim_card_activated",
        "sim_card_phone",
        "cell_provider",
        "location",
        "notes",
    )
    column_editable_list = form_columns

    column_default_sort = "sim_card_num"

    column_sortable_list = (
        "sim_card",
        "sim_card_num",
        "customers_rel.customer",
        "gateways_rel.gateway",
        "sim_card_activated",
        "sim_card_phone",
        "cell_provider",
        "location",
        "notes",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'structures' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "sim_card",
        "sim_card_num",
        "sim_card_phone",
        "cell_provider",
        "customers_rel.customer",
        "gateways_rel.gateway",
        "location",
        "notes",
    )
    column_filters = (
        "customers_rel",
        "cell_provider",
        "gateways_rel",
        "location",
        "notes",
    )

    can_set_page_size = True
    page_size = 50

    column_labels = {
        "gateways_rel": "Gateway",
        "gateways_rel.gateway": "Gateway",
        "gateways_rel.aws_thing": "AWS IoT Unit ID",
        "customers_rel": "Customer",
        "customers_rel.customer": "Customer",
        "sim_card": "SIM Card Number",
        "sim_card_num": "SIM Card Alias",
        "sim_card_phone": "SIM Card Phone Number",
        "cell_provider": "Cellular Service Provider",
        "location": "Location",
    }
    column_descriptions = {
        "gateways_rel": "Gateway to which this SIM card is assigned",
        "gateways_rel.gateway": "Gateway to which this SIM card is assigned",
        "gateways_rel.aws_thing": "Amazon Web Services (AWS) IoT unit ID for the AWS device shadow",
        "customers_rel": "Customer to which this SIM card is assigned",
        "customers_rel.customer": "Customer to which this SIM card is assigned",
        "sim_card": "SIM card serial number",
        "sim_card_num": "Optional alias for the SIM card",
        "sim_card_activated": "Is the SIM card activated yet?",
        "sim_card_phone": "Phone number associated with the SIM card",
        "cell_provider": "Cellular service provider for the SIM card",
        "location": "Location of the SIM card",
    }

    column_formatters = {
        # Don't show the power unit - land location, just the gateway name
        # "gateways_rel": lambda v, c, model, name: repr(model.gateways_rel.gateway),
        "notes": preserve_line_breaks,
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "customers_rel": QueryAjaxModelLoader(
            "customers_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select a customer",
            **options,
        ),
        "gateways_rel": QueryAjaxModelLoaderAdmin(
            "gateways_rel",
            db.session,
            Gw,
            fields=[
                Gw.gateway,
                Gw.aws_thing,
                PowerUnit.power_unit_str,
                Structure.surface,
            ],
            order_by="gateway",
            placeholder="Please select a gateway",
            get_query_func=lambda: Gw.query.join(PowerUnit).join(Structure),
            **options,
        ),
    }


class PartView(MyModelView):
    """Flask-Admin view for Part model (parts table)"""

    def is_accessible(self):
        """Only show this view to users with the 'admin' role"""
        role_ids: list = get_user_role_ids(user_id=getattr(current_user, "id", None))
        return (
            ROLE_ID_BOM_MASTER_PARTS_TABLES in role_ids
            and ROLE_ID_IJACK_ADMIN in role_ids
        )

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True
    page_size = 100
    can_export = True
    export_max_rows = 0

    column_list = (
        "worksheet",
        # "ws_row",
        "part_num",
        "description",
        "part_image",
        # "part_image_rel",
        # "msrp_mult_cad",
        # "transfer_mult_cad_dealer",
        # "msrp_mult_usd",
        # "transfer_mult_inc_to_corp",
        # "transfer_mult_usd_dealer",
        # "warehouse_mult",
        # "cost_cad",
        "msrp_cad",
        "dealer_cost_cad",
        # "cost_usd",
        "msrp_usd",
        "dealer_cost_usd",
        "is_usd",
        "cad_per_usd",
        "is_soft_part",
        "is_hard_part",
        "weight",
        "harmonization_code",
        "country_of_origin",
        "no_delete",
        "flagged_for_deletion",
        "id",
    )

    form_columns = (
        "part_num",
        "description",
        "part_image",
        # "part_image_rel",
        "no_delete",
        "flagged_for_deletion",
        "worksheet",
        "ws_row",
        "cost_cad",
        "cost_usd",
        "msrp_mult_cad",
        "msrp_mult_usd",
        "msrp_cad",
        "msrp_usd",
        "transfer_mult_cad_dealer",
        "transfer_mult_usd_dealer",
        "transfer_mult_inc_to_corp",
        "warehouse_mult",
        "dealer_cost_cad",
        "dealer_cost_usd",
        "is_usd",
        "cad_per_usd",
        "is_soft_part",
        "is_hard_part",
        "weight",
        "harmonization_code",
        "country_of_origin",
    )

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "description",
        "no_delete",
        "flagged_for_deletion",
        "is_soft_part",
        "is_hard_part",
        "weight",
        "harmonization_code",
        "country_of_origin",
    )

    column_export_list = (
        "worksheet",
        "ws_row",
        "part_num",
        "description",
        # "msrp_mult_cad",
        # "transfer_mult_cad_dealer",
        # "msrp_mult_usd",
        # "transfer_mult_inc_to_corp",
        # "transfer_mult_usd_dealer",
        # "warehouse_mult",
        # "cost_cad",
        "msrp_cad",
        "dealer_cost_cad",
        # "cost_usd",
        "msrp_usd",
        "dealer_cost_usd",
        "is_usd",
        "cad_per_usd",
        "is_soft_part",
        "is_hard_part",
        "weight",
        "harmonization_code",
        "country_of_origin",
        "no_delete",
        "flagged_for_deletion",
    )
    column_labels = {
        "worksheet": "BOM Master Worksheet",
        "ws_row": "Worksheet Row",
        "part_num": "Part Number",
        "part_image": "Part Image",
        # "part_image_rel": "Part Image",
        "description": "Description",
        "cost_cad": "Cost (CAD)",
        "cost_usd": "Cost (USD)",
        "msrp_mult_cad": "MSRP Multiplier (CAD)",
        "msrp_mult_usd": "MSRP Multiplier (USD)",
        "transfer_mult_cad_dealer": "Transfer Multiplier (CAD Dealer)",
        "transfer_mult_usd_dealer": "Transfer Multiplier (USD Dealer)",
        "transfer_mult_inc_to_corp": "Transfer Multiplier (Inc to Corp)",
        "warehouse_mult": "Warehouse Multiplier",
        "msrp_cad": "MSRP (CAD)",
        "msrp_usd": "MSRP (USD)",
        "dealer_cost_cad": "Dealer Cost (CAD)",
        "dealer_cost_usd": "Dealer Cost (USD)",
        "is_usd": "Is USD?",
        "cad_per_usd": "CAD per USD",
        "is_soft_part": "Is Soft Part?",
        "is_hard_part": "Is Hard Part?",
        "weight": "Weight (lbs)",
        "harmonization_code": "Harmonization Code",
        "country_of_origin": "Country of Origin",
        "no_delete": "Don't Delete",
        "flagged_for_deletion": "Flagged for Deletion",
        "id": "ID",
    }
    column_descriptions = {
        "worksheet": "Worksheet from which part came in BOM Master",
        "ws_row": "Worksheet row in BOM Master spreadsheet",
        "part_num": "Part number from BOM Master spreadsheet",
        "part_image": "Image of part from BOM Master spreadsheet",
        # "part_image_rel": "Image of part from BOM Master spreadsheet",
        "description": "Description of part from BOM Master spreadsheet",
        "cost_cad": "Part cost (CAD)",
        "cost_usd": "Part cost (USD)",
        "msrp_mult_cad": "Multiplier to set customer price (CAD)",
        "msrp_mult_usd": "Multiplier to set customer price (USD)",
        "msrp_cad": "Manufacturer Suggested Retail Price (CAD)",
        "msrp_usd": "Manufacturer Suggested Retail Price (USD)",
        "transfer_mult_cad_dealer": "Multiplier to set transfer price from IJACK Inc to CAD Dealer",
        "transfer_mult_usd_dealer": "Multiplier to set transfer price from IJACK Inc to USD Dealer",
        "transfer_mult_inc_to_corp": "Multiplier to set transfer price from IJACK Inc to IJACK Corp",
        "warehouse_mult": "Multiplier to set warehouse price",
        "dealer_cost_cad": "Dealer cost (CAD)",
        "dealer_cost_usd": "Dealer cost (USD)",
        "is_usd": "Is the part cost in USD?",
        "cad_per_usd": "CAD per USD FX rate used",
        "is_soft_part": "Is the part a soft part for preventative maintenance?",
        "is_hard_part": "Is the part a hard part for preventative maintenance?",
        "weight": "Weight of the part (lbs)",
        "harmonization_code": "Harmonization code for customs",
        "country_of_origin": "Country of origin for the part",
        "no_delete": "This indicates whether the part should remain in the database even if it's no longer used in the BOM Master spreadsheet. Things like 'miscellaneous' for the work order part line items, should be 'true' for 'no_delete'",
        "flagged_for_deletion": "This indicates whether the part is flagged for deletion in the database because it's no longer in the BoM Master spreadsheet, and it's not used in any work order part line items. This is a soft delete, and the part can be un-flagged for deletion if needed.",
    }
    column_formatters = {
        "cost_cad": money_formatter,
        "msrp_cad": money_formatter,
        "dealer_cost_cad": money_formatter,
        "cost_usd": money_formatter,
        "msrp_usd": money_formatter,
        "dealer_cost_usd": money_formatter,
        "worksheet": format_like_integer,
        # "part_image": image_thumbnail,
        "part_image": ImageUploadFieldDB.display_thumbnail,
        # "part_image_rel": image_thumbnail,
        # "part_image_rel": ImageUploadFieldDB.display_thumbnail,
    }

    form_extra_fields = {
        # The part image is not required, so we don't need to validate it
        "part_image": ImageUploadFieldDB("Image", validators=[]),
    }

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "part_num",
        "description",
        "id",
        "worksheet",
        "harmonization_code",
        "country_of_origin",
    )
    column_filters = (
        "part_num",
        "no_delete",
        "is_soft_part",
        "is_hard_part",
        "is_usd",
        "worksheet",
        "msrp_cad",
        "msrp_usd",
        "warehouse_mult",
        "weight",
        "harmonization_code",
        "country_of_origin",
        # So we can filter for whether the image is null
        # ERROR: Unsupported filter type part_image
        # "part_image",
        "flagged_for_deletion",
        "id",
    )


def structures_display(view, context, model, name):
    """Display the structures that use this warehouse"""
    # try_1 = ", ".join([f"{structure.structure_str} ({structure.surface})" for structure in model.structures_rel])
    # try_2 = f"{len(model.structures_rel):,} structures"
    models_dict = {}
    # Find distinct models and counts in model.structures_rel
    for structure in model.structures_rel:
        model_name = getattr(structure.model_types_rel, "model", None)
        if not model_name:
            continue
        if model_name not in models_dict.keys():
            models_dict[model_name] = 1
        else:
            models_dict[model_name] += 1
    # Create a list of models and counts
    models_list = sorted([f"{model} ({count})" for model, count in models_dict.items()])
    # Join the list of models and counts
    return Markup("<br>".join(models_list))


class WarehouseView(MyModelView):
    """Warehouse model"""

    can_set_page_size = True
    page_size = 20
    can_export = True
    export_max_rows = 0

    column_list = (
        "name",
        "description",
        "is_active",
        "allows_negative_stock",
        "is_main_warehouse",
        "can_show_to_customers",
        "address",
        "city",
        "zip_code",
        # "province_rel.name",
        # "country_rel.country_name",
        # "time_zone_rel.time_zone",
        "province_rel",
        "country_rel",
        "time_zone_rel",
        "gps_lat",
        "gps_lon",
        "structures_rel.structure",
        "id",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "name",
        "description",
        "is_active",
        "allows_negative_stock",
        "is_main_warehouse",
        "can_show_to_customers",
        "address",
        "city",
        "zip_code",
        "province_rel",
        "country_rel",
        "time_zone_rel",
        "gps_lat",
        "gps_lon",
        # This one takes forever to load
        # "structures_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "name",
        "description",
        "address",
        "city",
        "zip_code",
        "province_rel.name",
        "country_rel.country_name",
        "structures_rel.structure",
        "time_zone_rel.time_zone",
    )

    column_filters = (
        "name",
        "description",
        "is_active",
        "allows_negative_stock",
        "is_main_warehouse",
        "can_show_to_customers",
        "address",
        "city",
        "zip_code",
        "province_rel.name",
        "country_rel.country_name",
        "structures_rel.structure",
        "time_zone_rel.time_zone",
    )

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "name",
        "description",
        "is_active",
        "allows_negative_stock",
        "is_main_warehouse",
        "can_show_to_customers",
        "address",
        "city",
        "zip_code",
        "province_rel",
        "country_rel",
        "time_zone_rel",
        "gps_lat",
        "gps_lon",
        "structures_rel",
    )

    column_sortable_list = (
        "name",
        "description",
        "is_active",
        "allows_negative_stock",
        "is_main_warehouse",
        "can_show_to_customers",
        "address",
        "city",
        "zip_code",
        # "province_rel.name",
        # "country_rel.country_name",
        # "time_zone_rel.time_zone",
        "gps_lat",
        "gps_lon",
        "structures_rel.structure",
    )

    column_labels = {
        "name": "Name",
        "description": "Description",
        "is_active": "Is Active?",
        "allows_negative_stock": "Allows Negative Stock?",
        "is_main_warehouse": "Is Main Warehouse?",
        "can_show_to_customers": "Can Show to Customers?",
        "address": "Address",
        "city": "City",
        "zip_code": "Zip Code",
        "province_rel.name": "Province",
        "province_rel": "Province",
        "country_rel.country_name": "Country",
        "country_rel": "Country",
        "time_zone_rel.time_zone": "Time Zone",
        "time_zone_rel": "Time Zone",
        "gps_lat": "GPS Latitude",
        "gps_lon": "GPS Longitude",
        "structures_rel.structure": "Structures",
        "structures_rel": "Structures",
    }

    column_descriptions = {
        "name": "Name of warehouse",
        "description": "Description of warehouse",
        "is_active": "Is warehouse up and running?",
        "allows_negative_stock": "Can this warehouse have negative stock? If not, work orders will not be able to reserve parts from this warehouse if the quantity is zero.",
        "is_main_warehouse": "Is this the main warehouse?",
        "can_show_to_customers": "Can this warehouse be shown to customers?",
        "address": "Address of warehouse",
        "city": "City of warehouse",
        "zip_code": "Zip code of warehouse",
        "province_rel.name": "Province of warehouse",
        "province_rel": "Province of warehouse",
        "country_rel.country_name": "Country of warehouse",
        "country_rel": "Country of warehouse",
        "time_zone_rel.time_zone": "Time zone of warehouse",
        "time_zone_rel": "Time zone of warehouse",
        "gps_lat": "GPS latitude of warehouse",
        "gps_lon": "GPS longitude of warehouse",
        "structures_rel.structure": "Structures that use this warehouse",
        "structures_rel": "Structures that use this warehouse",
    }

    column_formatters = {
        "gps_lat": lambda v, c, m, n: str(m.gps_lat)[:7],
        "gps_lon": lambda v, c, m, n: str(m.gps_lon)[:7],
        # "structures_rel.structure": lambda v, c, model, n: ", ".join(
        #     [f"{structure.structure_str} ({structure.surface})" for structure in model.structures_rel]
        # ),
        "structures_rel.structure": structures_display,
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "province_rel": QueryAjaxModelLoader(
            "province_rel",
            db.session,
            Province,
            fields=["name"],
            order_by="name",
            placeholder="Please select a province/state",
            **options,
        ),
        "country_rel": QueryAjaxModelLoader(
            "country_rel",
            db.session,
            Country,
            fields=["country_name"],
            order_by="country_name",
            placeholder="Please select a country",
            **options,
        ),
        "time_zone_rel": QueryAjaxModelLoader(
            "time_zone_rel",
            db.session,
            TimeZone,
            fields=["time_zone"],
            order_by="time_zone",
            placeholder="Please select a time zone",
            get_query_func=timezone_filtering_function,
            **options,
        ),
        "structures_rel": QueryAjaxModelLoaderAdmin(
            "structures_rel",
            db.session,
            Structure,
            fields=["structure", "surface", PowerUnit.power_unit_str],
            order_by="structure",
            placeholder="Please select the structures that use this warehouse",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
    }


class WarehousePartView(MyModelView):
    """WarehousePart model"""

    can_set_page_size = True
    page_size = 50
    can_export = True
    export_max_rows = 0

    column_list = (
        "warehouse_rel.name",
        "part_rel.part_num",
        "part_rel.description",
        "quantity",
        "quantity_reserved",
        "quantity_available",
        "quantity_desired",
        "id",
    )
    column_editable_list = (
        # "quantity",
        # "quantity_reserved",
        "quantity_desired",
    )
    form_columns = (
        # "warehouse_rel",
        # "part_rel",
        # "quantity",
        # "quantity_reserved",
        "quantity_desired",
    )
    column_filters = (
        "warehouse_rel.name",
        "part_rel.part_num",
        "quantity",
        "quantity_reserved",
        "quantity_available",
        "quantity_desired",
    )
    column_sortable_list = (
        "warehouse_rel.name",
        "part_rel.part_num",
        "part_rel.description",
        "quantity",
        "quantity_reserved",
        "quantity_available",
        "quantity_desired",
        "id",
    )
    column_default_sort = [("warehouse_rel.name", False), ("part_rel.part_num", False)]
    column_searchable_list = (
        "warehouse_rel.name",
        "part_rel.part_num",
        "part_rel.description",
    )
    column_labels = {
        "warehouse_rel.name": "Warehouse",
        "warehouse_rel": "Warehouse",
        "part_rel.part_num": "Part Number",
        "part_rel.description": "Description",
        "part_rel": "Part",
        "quantity": "Quantity",
        "quantity_reserved": "Reserved Quantity",
        "quantity_available": "Available Quantity",
        "quantity_desired": "Desired Quantity",
        "id": "ID",
    }
    column_descriptions = {
        "warehouse_rel.name": "Warehouse where part is stored",
        "warehouse_rel": "Warehouse where part is stored",
        "quantity": "Quantity of part in warehouse",
        "quantity_reserved": "Quantity of part reserved for work orders",
        "quantity_available": "Quantity of part available in warehouse, calculated as quantity - reserved quantity",
        "quantity_desired": "Desired quantity of part in warehouse",
        "part_rel.part_num": "Part number from BOM Master spreadsheet",
        "part_rel.description": "Description of part from BOM Master spreadsheet",
    }
    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "warehouse_rel": QueryAjaxModelLoader(
            "warehouse_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select a warehouse",
            **options,
        ),
        "part_rel": QueryAjaxModelLoader(
            "part_rel",
            db.session,
            Part,
            fields=["part_num"],
            order_by="part_num",
            placeholder="Please select a part",
            **options,
        ),
    }


# Add these classes to the end of sqla_views.py, after the existing view classes


class WarehouseLocationView(MyModelView):
    """WarehouseLocation model - Storage locations within warehouses"""

    can_set_page_size = True
    page_size = 50
    can_export = True
    export_max_rows = 0

    column_list = (
        "warehouse_rel.name",
        "location_code",
        "location_name",
        "location_type",
        "parent_location_rel.location_code",
        "is_active",
        "is_pickable",
        "is_receivable",
        "max_weight_kg",
        "max_volume_m3",
        "id",
    )

    column_editable_list = (
        "warehouse_rel",
        "location_code",
        "location_name",
        "location_type",
        "parent_location_rel",
        "is_active",
        "is_pickable",
        "is_receivable",
        "max_weight_kg",
        "max_volume_m3",
    )

    form_columns = (
        "warehouse_rel",
        "location_code",
        "location_name",
        "location_type",
        "parent_location_rel",
        "is_active",
        "is_pickable",
        "is_receivable",
        "max_weight_kg",
        "max_volume_m3",
    )

    column_filters = (
        "warehouse_rel.name",
        "location_code",
        "location_name",
        "location_type",
        "is_active",
        "is_pickable",
        "is_receivable",
    )

    column_sortable_list = (
        "warehouse_rel.name",
        "location_code",
        "location_name",
        "location_type",
        "is_active",
        "is_pickable",
        "is_receivable",
        "max_weight_kg",
        "max_volume_m3",
        "id",
    )

    column_default_sort = [("warehouse_rel.name", False), ("location_code", False)]

    column_searchable_list = (
        "warehouse_rel.name",
        "location_code",
        "location_name",
        "location_type",
    )

    column_labels = {
        "warehouse_rel.name": "Warehouse",
        "warehouse_rel": "Warehouse",
        "location_code": "Location Code",
        "location_name": "Location Name",
        "location_type": "Location Type",
        "parent_location_rel": "Parent Location",
        "parent_location_rel.location_code": "Parent Location",
        "is_active": "Is Active",
        "is_pickable": "Is Pickable",
        "is_receivable": "Is Receivable",
        "max_weight_kg": "Max Weight (kg)",
        "max_volume_m3": "Max Volume (m³)",
    }

    column_descriptions = {
        "warehouse_rel": "Warehouse containing this location",
        "location_code": "Unique code for this location within the warehouse",
        "location_name": "Descriptive name for the location",
        "location_type": "Type of location (zone, aisle, rack, shelf, bin)",
        "parent_location_rel": "Parent location in hierarchy",
        "is_active": "Is this location currently active",
        "is_pickable": "Can items be picked from this location",
        "is_receivable": "Can items be received into this location",
        "max_weight_kg": "Maximum weight capacity in kilograms",
        "max_volume_m3": "Maximum volume capacity in cubic meters",
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "warehouse_rel": QueryAjaxModelLoader(
            "warehouse_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select a warehouse",
            **options,
        ),
        "parent_location_rel": QueryAjaxModelLoader(
            "parent_location_rel",
            db.session,
            WarehouseLocation,
            fields=["location_code", "location_name"],
            order_by="location_code",
            placeholder="Please select a parent location",
            **options,
        ),
    }


class InventoryMovementView(MyModelView):
    """InventoryMovement model - Tracks all inventory movements"""

    can_create = False  # Movements should be created through the API or other processes
    can_edit = False  # Movements should be immutable once created
    can_delete = False  # Movements should not be deleted directly
    can_set_page_size = True
    page_size = 50
    can_export = True
    export_max_rows = 0

    column_list = (
        "movement_number",
        "movement_type",
        "movement_date",
        "part_rel.part_num",
        "part_rel.description",
        "quantity",
        "unit_cost",
        "total_cost",
        "from_warehouse_rel.name",
        "to_warehouse_rel.name",
        "reference_type",
        "reference_number",
        "created_by_rel",
        "is_reversed",
        "id",
    )

    column_sortable_list = (
        "movement_number",
        "movement_type",
        "movement_date",
        "part_rel.part_num",
        "quantity",
        "unit_cost",
        "total_cost",
        "from_warehouse_rel.name",
        "to_warehouse_rel.name",
        "reference_type",
        "reference_number",
        "is_reversed",
        "id",
    )

    column_default_sort = [("movement_date", True), ("movement_number", True)]

    column_searchable_list = (
        "movement_number",
        "movement_type",
        "part_rel.part_num",
        "part_rel.description",
        "from_warehouse_rel.name",
        "to_warehouse_rel.name",
        "reference_number",
        "notes",
        "reason_code",
    )

    column_filters = (
        "movement_type",
        "movement_date",
        "part_rel.part_num",
        "from_warehouse_rel.name",
        "to_warehouse_rel.name",
        "reference_type",
        "is_reversed",
        "created_by_rel",
    )

    form_columns = (
        "movement_number",
        "movement_type",
        "movement_date",
        "part_rel",
        "quantity",
        "unit_cost",
        "total_cost",
        "from_warehouse_rel",
        "to_warehouse_rel",
        "reference_type",
        "reference_id",
        "reference_number",
        "lot_number",
        "notes",
        "reason_code",
    )

    column_labels = {
        "movement_number": "Movement Number",
        "movement_type": "Movement Type",
        "movement_date": "Movement Date",
        "part_rel.part_num": "Part Number",
        "part_rel.description": "Part Description",
        "part_rel": "Part",
        "quantity": "Quantity",
        "unit_cost": "Unit Cost",
        "total_cost": "Total Cost",
        "from_warehouse_rel.name": "From Warehouse",
        "from_warehouse_rel": "From Warehouse",
        "to_warehouse_rel.name": "To Warehouse",
        "to_warehouse_rel": "To Warehouse",
        "reference_type": "Reference Type",
        "reference_id": "Reference ID",
        "reference_number": "Reference Number",
        "lot_number": "Lot Number",
        "notes": "Notes",
        "reason_code": "Reason Code",
        "created_by_rel": "Created By",
        "is_reversed": "Is Reversed",
    }

    column_descriptions = {
        "movement_number": "Unique identifier for this movement",
        "movement_type": "Type of movement (receipt, issue, transfer, adjustment, etc.)",
        "movement_date": "Date and time of the movement",
        "part_rel": "Part being moved",
        "quantity": "Quantity being moved",
        "unit_cost": "Cost per unit",
        "total_cost": "Total cost of the movement",
        "from_warehouse_rel": "Source warehouse (if applicable)",
        "to_warehouse_rel": "Destination warehouse (if applicable)",
        "reference_type": "Type of source document",
        "reference_id": "ID of source document",
        "reference_number": "Number of source document",
        "lot_number": "Lot number for tracking",
        "notes": "Additional notes",
        "reason_code": "Reason for the movement",
        "created_by_rel": "User who created this movement",
        "is_reversed": "Has this movement been reversed",
    }

    column_formatters = {
        "movement_date": datetime_formatter_sk_time,
        "unit_cost": money_formatter,
        "total_cost": money_formatter,
        "notes": preserve_line_breaks,
        "created_by_rel": user_formatter,
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "part_rel": QueryAjaxModelLoader(
            "part_rel",
            db.session,
            Part,
            fields=["part_num", "description"],
            order_by="part_num",
            placeholder="Please select a part",
            **options,
        ),
        "from_warehouse_rel": QueryAjaxModelLoader(
            "from_warehouse_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select source warehouse",
            **options,
        ),
        "to_warehouse_rel": QueryAjaxModelLoader(
            "to_warehouse_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select destination warehouse",
            **options,
        ),
    }


class InventoryReservationView(MyModelView):
    """InventoryReservation model - Tracks inventory reservations"""

    can_set_page_size = True
    page_size = 50
    can_export = True
    can_delete = False  # Reservations should not be deleted directly
    can_create = False  # Reservations should be created through work orders
    can_edit = False  # Reservations should not be edited directly
    export_max_rows = 0

    column_list = (
        "reservation_number",
        "status",
        "part_rel.part_num",
        "part_rel.description",
        "warehouse_rel.name",
        "quantity_reserved",
        "quantity_fulfilled",
        "quantity_remaining",
        "reservation_date",
        "expiry_date",
        "reference_type",
        "reference_number",
        "priority",
        "id",
    )

    column_editable_list = (
        "status",
        "quantity_fulfilled",
        "expiry_date",
        "priority",
        "notes",
    )

    form_columns = (
        "reservation_number",
        "status",
        "part_rel",
        "warehouse_rel",
        "quantity_reserved",
        "quantity_fulfilled",
        "reservation_date",
        "expiry_date",
        "reference_type",
        "reference_id",
        "reference_number",
        "priority",
        "notes",
    )

    column_filters = (
        "status",
        "part_rel.part_num",
        "warehouse_rel.name",
        "reservation_date",
        "expiry_date",
        "reference_type",
        "priority",
    )

    column_sortable_list = (
        "reservation_number",
        "status",
        "part_rel.part_num",
        "warehouse_rel.name",
        "quantity_reserved",
        "quantity_fulfilled",
        "quantity_remaining",
        "reservation_date",
        "expiry_date",
        "priority",
        "id",
    )

    column_default_sort = [("reservation_date", True), ("priority", True)]

    column_searchable_list = (
        "reservation_number",
        "part_rel.part_num",
        "part_rel.description",
        "warehouse_rel.name",
        "reference_number",
        "notes",
    )

    column_labels = {
        "reservation_number": "Reservation Number",
        "status": "Status",
        "part_rel.part_num": "Part Number",
        "part_rel.description": "Part Description",
        "part_rel": "Part",
        "warehouse_rel.name": "Warehouse",
        "warehouse_rel": "Warehouse",
        "quantity_reserved": "Quantity Reserved",
        "quantity_fulfilled": "Quantity Fulfilled",
        "quantity_remaining": "Quantity Remaining",
        "reservation_date": "Reservation Date",
        "expiry_date": "Expiry Date",
        "reference_type": "Reference Type",
        "reference_id": "Reference ID",
        "reference_number": "Reference Number",
        "priority": "Priority",
        "notes": "Notes",
    }

    column_descriptions = {
        "reservation_number": "Unique identifier for this reservation",
        "status": "Current status of the reservation",
        "part_rel": "Part being reserved",
        "warehouse_rel": "Warehouse where part is reserved",
        "quantity_reserved": "Total quantity reserved",
        "quantity_fulfilled": "Quantity already fulfilled",
        "quantity_remaining": "Quantity still to be fulfilled",
        "reservation_date": "Date reservation was created",
        "expiry_date": "Date reservation expires",
        "reference_type": "Type of document that created this reservation",
        "reference_id": "ID of source document",
        "reference_number": "Number of source document",
        "priority": "Priority for allocation (higher = more important)",
        "notes": "Additional notes",
    }

    column_formatters = {
        "reservation_date": datetime_formatter_sk_time,
        "expiry_date": datetime_formatter_sk_time,
        "notes": preserve_line_breaks,
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "part_rel": QueryAjaxModelLoader(
            "part_rel",
            db.session,
            Part,
            fields=["part_num", "description"],
            order_by="part_num",
            placeholder="Please select a part",
            **options,
        ),
        "warehouse_rel": QueryAjaxModelLoader(
            "warehouse_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select a warehouse",
            **options,
        ),
    }


class CycleCountView(MyModelView):
    """CycleCount model - Cycle count headers"""

    can_set_page_size = True
    page_size = 50
    can_export = True
    export_max_rows = 0

    column_list = (
        "count_number",
        "count_name",
        "status",
        "warehouse_rel.name",
        "count_type",
        "scheduled_date",
        "started_date",
        "completed_date",
        "total_items",
        "counted_items",
        "variance_items",
        "total_variance_value",
        "created_by_rel",
        "assigned_to_rel",
        "id",
    )

    column_editable_list = (
        "count_name",
        "status",
        "scheduled_date",
        "assigned_to_rel",
    )

    form_columns = (
        "count_number",
        "count_name",
        "status",
        "warehouse_rel",
        "count_type",
        "scheduled_date",
        "started_date",
        "completed_date",
        "abc_codes",
        "assigned_to_rel",
    )

    column_filters = (
        "status",
        "warehouse_rel.name",
        "count_type",
        "scheduled_date",
        "created_by_rel",
        "assigned_to_rel",
    )

    column_sortable_list = (
        "count_number",
        "count_name",
        "status",
        "warehouse_rel.name",
        "count_type",
        "scheduled_date",
        "started_date",
        "completed_date",
        "total_items",
        "counted_items",
        "variance_items",
        "total_variance_value",
        "id",
    )

    column_default_sort = [("scheduled_date", True), ("count_number", False)]

    column_searchable_list = (
        "count_number",
        "count_name",
        "warehouse_rel.name",
    )

    column_labels = {
        "count_number": "Count Number",
        "count_name": "Count Name",
        "status": "Status",
        "warehouse_rel.name": "Warehouse",
        "warehouse_rel": "Warehouse",
        "count_type": "Count Type",
        "scheduled_date": "Scheduled Date",
        "started_date": "Started Date",
        "completed_date": "Completed Date",
        "abc_codes": "ABC Codes",
        "total_items": "Total Items",
        "counted_items": "Counted Items",
        "variance_items": "Variance Items",
        "total_variance_value": "Total Variance Value",
        "created_by_rel": "Created By",
        "assigned_to_rel": "Assigned To",
    }

    column_descriptions = {
        "count_number": "Unique identifier for this cycle count",
        "count_name": "Descriptive name for the count",
        "status": "Current status of the count",
        "warehouse_rel": "Warehouse being counted",
        "count_type": "Type of count (ABC, RANDOM, CATEGORY, LOCATION)",
        "scheduled_date": "Date scheduled for counting",
        "started_date": "Date counting was started",
        "completed_date": "Date counting was completed",
        "abc_codes": "ABC codes to include in count",
        "total_items": "Total number of items to count",
        "counted_items": "Number of items already counted",
        "variance_items": "Number of items with variances",
        "total_variance_value": "Total financial impact of variances",
        "created_by_rel": "User who created this count",
        "assigned_to_rel": "User assigned to perform the count",
    }

    column_formatters = {
        "scheduled_date": datetime_formatter_sk_time,
        "started_date": datetime_formatter_sk_time,
        "completed_date": datetime_formatter_sk_time,
        "total_variance_value": money_formatter,
        "created_by_rel": user_formatter,
        "assigned_to_rel": user_formatter,
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "warehouse_rel": QueryAjaxModelLoader(
            "warehouse_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select a warehouse",
            **options,
        ),
        "assigned_to_rel": QueryAjaxModelLoader(
            "assigned_to_rel",
            db.session,
            User,
            fields=["first_name", "last_name", "email"],
            order_by="first_name",
            placeholder="Please select a user to assign to",
            **options,
        ),
    }


class CycleCountItemView(MyModelView):
    """CycleCountItem model - Individual items within cycle counts"""

    can_create = False  # Items are typically generated automatically
    can_set_page_size = True
    page_size = 50
    can_export = True
    export_max_rows = 0

    column_list = (
        "cycle_count_rel.count_number",
        "part_rel.part_num",
        "part_rel.description",
        "system_quantity",
        "counted_quantity",
        "variance_quantity",
        "unit_cost",
        "variance_value",
        "is_counted",
        "count_date",
        "counted_by_rel",
        "variance_reason",
        "adjustment_approved",
        "id",
    )

    column_editable_list = (
        "counted_quantity",
        "variance_reason",
        "adjustment_approved",
        "notes",
    )

    form_columns = (
        "cycle_count_rel",
        "part_rel",
        "system_quantity",
        "counted_quantity",
        "unit_cost",
        "variance_reason",
        "adjustment_approved",
        "notes",
    )

    column_filters = (
        "cycle_count_rel.count_number",
        "part_rel.part_num",
        "is_counted",
        "adjustment_approved",
        "variance_reason",
    )

    column_sortable_list = (
        "cycle_count_rel.count_number",
        "part_rel.part_num",
        "system_quantity",
        "counted_quantity",
        "variance_quantity",
        "unit_cost",
        "variance_value",
        "is_counted",
        "count_date",
        "id",
    )

    column_default_sort = [
        ("cycle_count_rel.count_number", False),
        ("part_rel.part_num", False),
    ]

    column_searchable_list = (
        "cycle_count_rel.count_number",
        "part_rel.part_num",
        "part_rel.description",
        "variance_reason",
        "notes",
    )

    column_labels = {
        "cycle_count_rel.count_number": "Count Number",
        "cycle_count_rel": "Cycle Count",
        "part_rel.part_num": "Part Number",
        "part_rel.description": "Part Description",
        "part_rel": "Part",
        "system_quantity": "System Quantity",
        "counted_quantity": "Counted Quantity",
        "variance_quantity": "Variance Quantity",
        "unit_cost": "Unit Cost",
        "variance_value": "Variance Value",
        "is_counted": "Is Counted",
        "count_date": "Count Date",
        "counted_by_rel": "Counted By",
        "variance_reason": "Variance Reason",
        "adjustment_approved": "Adjustment Approved",
        "notes": "Notes",
    }

    column_descriptions = {
        "cycle_count_rel": "Parent cycle count",
        "part_rel": "Part being counted",
        "system_quantity": "Quantity according to system",
        "counted_quantity": "Quantity actually counted",
        "variance_quantity": "Difference between system and counted",
        "unit_cost": "Cost per unit for variance calculation",
        "variance_value": "Financial impact of variance",
        "is_counted": "Has this item been counted",
        "count_date": "Date item was counted",
        "counted_by_rel": "User who counted this item",
        "variance_reason": "Reason for any variance",
        "adjustment_approved": "Has the adjustment been approved",
        "notes": "Additional notes",
    }

    column_formatters = {
        "count_date": datetime_formatter_sk_time,
        "unit_cost": money_formatter,
        "variance_value": money_formatter,
        "counted_by_rel": user_formatter,
        "notes": preserve_line_breaks,
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "cycle_count_rel": QueryAjaxModelLoader(
            "cycle_count_rel",
            db.session,
            CycleCount,
            fields=["count_number", "count_name"],
            order_by="count_number",
            placeholder="Please select a cycle count",
            **options,
        ),
        "part_rel": QueryAjaxModelLoader(
            "part_rel",
            db.session,
            Part,
            fields=["part_num", "description"],
            order_by="part_num",
            placeholder="Please select a part",
            **options,
        ),
    }


class PartCategoryView(MyModelView):
    """PartCategory model - Categories for organizing parts"""

    can_set_page_size = True
    page_size = 50
    can_export = True
    export_max_rows = 0

    column_list = (
        "code",
        "name",
        "parent_category_rel.code",
        "abc_code",
        "count_frequency_days",
        "is_active",
        "id",
    )

    column_editable_list = (
        "code",
        "name",
        "parent_category_rel",
        "abc_code",
        "count_frequency_days",
        "is_active",
    )

    form_columns = (
        "code",
        "name",
        "parent_category_rel",
        "abc_code",
        "count_frequency_days",
        "is_active",
    )

    column_filters = (
        "code",
        "name",
        "abc_code",
        "is_active",
        "parent_category_rel.code",
    )

    column_sortable_list = (
        "code",
        "name",
        "abc_code",
        "count_frequency_days",
        "is_active",
        "id",
    )

    column_default_sort = [("code", False)]

    column_searchable_list = (
        "code",
        "name",
    )

    column_labels = {
        "code": "Category Code",
        "name": "Category Name",
        "parent_category_rel": "Parent Category",
        "parent_category_rel.code": "Parent Category",
        "abc_code": "ABC Code",
        "count_frequency_days": "Count Frequency (Days)",
        "is_active": "Is Active",
    }

    column_descriptions = {
        "code": "Unique code for this category",
        "name": "Descriptive name for the category",
        "parent_category_rel": "Parent category in hierarchy",
        "abc_code": "ABC classification (A, B, or C) for cycle counting",
        "count_frequency_days": "Number of days between cycle counts",
        "is_active": "Is this category currently active",
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "parent_category_rel": QueryAjaxModelLoader(
            "parent_category_rel",
            db.session,
            PartCategory,
            fields=["code", "name"],
            order_by="code",
            placeholder="Please select a parent category",
            **options,
        ),
    }


class ModelTypeView(MyModelView):
    """Flask-Admin view for Model model (models like XFER 2270)"""

    can_set_page_size = True
    # This page is a little slow to load with all the many-to-many relationships displayed
    page_size = 10

    column_list = (
        "part_rel",
        "model",
        "description",
        "max_delta_p",
        "color",
        "bom_structure_rel.name",
        "bom_pump_top_rel.name",
        "bom_dgas_rel.name",
        "parts_rel.part_num",
        "pm_seal_kits_rel.part_num",
        "unit_types_rel.unit_type",
        # "structures_rel.structure",
        "can_show_to_customers",
        "id",
    )

    column_formatters = {
        "bom_structure_rel.name": many_to_many_formatter,
        "bom_pump_top_rel.name": many_to_many_formatter,
        "bom_dgas_rel.name": many_to_many_formatter,
        "parts_rel.part_num": many_to_many_formatter,
        "pm_seal_kits_rel.part_num": many_to_many_formatter,
        "structures_rel.structure": many_to_many_formatter,
    }

    form_columns = (
        "part_rel",
        "model",
        "description",
        "max_delta_p",
        "color",
        "bom_structure_rel",
        "bom_pump_top_rel",
        "bom_dgas_rel",
        "parts_rel",
        "pm_seal_kits_rel",
        "unit_types_rel",
        "structures_rel",
        "can_show_to_customers",
    )

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "part_rel",
        "model",
        "description",
        "max_delta_p",
        "color",
        "can_show_to_customers",
    )

    # True = sort descending
    column_default_sort = [
        ("unit_types_rel.unit_type", True),
        ("model", False),
    ]

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("part_rel.part_name", "model", "description")

    column_filters = (
        "part_rel",
        "model",
        "description",
        "max_delta_p",
        "can_show_to_customers",
        # "parts_rel",
        # "pm_seal_kits_rel",
        # "bom_structure_rel",
        # "bom_pump_top_rel",
        # "bom_dgas_rel",
        "id",
    )

    column_labels = {
        "part_rel": "Part Number",
        "part_rel.part_name": "Part Number",
        "model": "Model",
        "description": "Description",
        "max_delta_p": "Max Delta P",
        "parts_rel.part_num": "Parts Needed",
        "parts_rel": "Parts Needed",
        "pm_seal_kits_rel.part_num": "PM Seal Kits Needed",
        "pm_seal_kits_rel": "PM Seal Kits Needed",
        "bom_structure_rel.name": "BoM Structures Needed",
        "bom_structure_rel": "BoM Structures Needed",
        "bom_pump_top_rel.name": "BoM Pump Tops Needed",
        "bom_pump_top_rel": "BoM Pump Tops Needed",
        "bom_dgas_rel.name": "BoM DGAS Needed",
        "bom_dgas_rel": "BoM DGAS Needed",
        "unit_types_rel.unit_type": "Unit Type",
        "unit_types_rel": "Unit Type",
        "structures_rel.structure": "Structures in Field",
        "structures_rel": "Structures in Field",
        "can_show_to_customers": "Can Show to Customers",
    }

    column_descriptions = {
        "part_rel": "Part number for this model type",
        "part_rel.part_name": "Part number for this model type",
        "max_delta_p": "Maximum delta of pressure",
        "parts_rel.part_num": "Parts needed for this structure type. This is for estimating the parts needed in each warehouse for this structure type",
        "parts_rel": "Parts needed for this structure type. This is for estimating the parts needed in each warehouse for this structure type",
        "pm_seal_kits_rel.part_num": "Preventative maintenance seal kits needed for this structure type",
        "pm_seal_kits_rel": "Preventative maintenance seal kits needed for this structure type",
        "bom_structure_rel.name": "BoM Structure finished goods used in this structure type",
        "bom_structure_rel": "BoM Structure finished goods used in this structure type",
        "bom_pump_top_rel.name": "BoM Pump Top finished goods used in this structure type",
        "bom_pump_top_rel": "BoM Pump Top finished goods used in this structure type",
        "bom_dgas_rel.name": "BoM DGAS finished goods used in this structure type",
        "bom_dgas_rel": "BoM DGAS finished goods used in this structure type",
        "unit_types_rel.unit_type": "This structure type belongs to this unit type",
        "unit_types_rel": "This structure type belongs to this unit type",
        "structures_rel.structure": "Structures in the field that use this structure type",
        "structures_rel": "Structures in the field that use this structure type",
        "can_show_to_customers": "Can this structure type be shown to customers?",
    }

    form_overrides = {
        "color": ColorField,
    }

    form_widget_args = {
        "color": {
            # The <input type="color" list="presetColors">
            # where list is a <datalist> in document
            "list": "presetColors",
            "autocomplete": "",
        },
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "part_rel": QueryAjaxModelLoader(
            "part_rel",
            db.session,
            Part,
            fields=["part_num", "part_name"],
            order_by="part_num",
            placeholder="Please select the part number for this model",
            **options,
        ),
        # Need this for testing since it's a foreign key
        "unit_types_rel": QueryAjaxModelLoader(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select the unit type for this model",
            **options,
        ),
        "structures_rel": QueryAjaxModelLoader(
            "structures_rel",
            db.session,
            Structure,
            fields=["structure", "surface", "downhole"],
            order_by="structure",
            placeholder="Please select the structures that use this model",
            **options,
        ),
    }

    inline_models = (
        # BoM Structures Needed
        (
            BOMStructureModelType,
            {
                "form_ajax_refs": {
                    "bom_structure_rel": QueryAjaxModelLoader(
                        "bom_structure_rel",
                        db.session,
                        BOMStructure,
                        fields=["name"],
                        order_by="name",
                        placeholder="Please select the finished structure used in this structure type",
                        **options,
                    ),
                },
                "column_labels": {
                    "finished_good_rel": "Structure",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "quantity": "Quantity needed for this structure type (sometimes more than one required)",
                },
            },
        ),
        # Pump Tops Needed
        (
            BOMPumpTopModelType,
            {
                "form_ajax_refs": {
                    "bom_pump_top_rel": QueryAjaxModelLoader(
                        "bom_pump_top_rel",
                        db.session,
                        BOMPumpTop,
                        fields=["name"],
                        order_by="name",
                        placeholder="Please select the finished pump top used in this structure type",
                        **options,
                    ),
                },
                "column_labels": {
                    "finished_good_rel": "Pump Top",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "quantity": "Quantity needed for this structure type (sometimes more than one required)",
                },
            },
        ),
        # DGAS Needed
        (
            BOMDGASModelType,
            {
                "form_ajax_refs": {
                    "bom_dgas_rel": QueryAjaxModelLoader(
                        "bom_dgas_rel",
                        db.session,
                        BOMDGAS,
                        fields=["name"],
                        order_by="name",
                        placeholder="Please select the finished DGAS used in this structure type",
                        **options,
                    ),
                },
                "column_labels": {
                    "finished_good_rel": "DGAS Model",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "quantity": "Quantity needed for this structure type (sometimes more than one required)",
                },
            },
        ),
        # Extra parts needed (seal kits, etc.)
        (
            ModelTypePart,
            {
                "form_ajax_refs": {
                    "parts_rel": QueryAjaxModelLoader(
                        "parts_rel",
                        db.session,
                        Part,
                        fields=["part_num"],
                        order_by="part_num",
                        placeholder="Please select the part used in this structure type",
                        **options,
                    ),
                },
                "column_labels": {
                    "parts_rel": "Part Number",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "parts_rel": "Part number used in this structure type",
                    "quantity": "Quantity needed for this structure type (sometimes more than one required)",
                },
            },
        ),
        # PM Seal Kits Needed
        (
            ModelTypePartPMSealKit,
            {
                "form_ajax_refs": {
                    "parts_rel": QueryAjaxModelLoader(
                        "parts_rel",
                        db.session,
                        Part,
                        fields=["part_num"],
                        order_by="part_num",
                        placeholder="Please select the PM seal kit used in this structure type for preventative maintenance",
                        **options,
                    ),
                },
                "column_labels": {
                    "parts_rel": "Part Number (PM Seal Kit)",
                    "quantity": "Quantity Needed",
                },
                "column_descriptions": {
                    "parts_rel": "Part number (PM seal kit) used in this structure type for preventative maintenance",
                    "quantity": "Quantity needed for this structure type (sometimes more than one required)",
                },
            },
        ),
    )

    def render(self, template, **kwargs):
        """Add some extra JavaScript"""
        current_app.config.get("VERSION_MYIJACK", None)
        # Use the name of the vite bundle here
        self.extra_js = ["admin/model_types_view.js"]
        return super().render(template, **kwargs)


class UnitTypeView(MyModelView):
    """Flask-Admin view for UnitType model (unit types like XFER, UNO, EGAS, UNOGAS)"""

    column_list = (
        "unit_type",
        "description",
        "models_rel.model",
        # "structures_rel.structure",
        "can_show_to_customers",
        "id",
    )

    form_columns = ("unit_type", "description", "can_show_to_customers")
    column_editable_list = ("unit_type", "description", "can_show_to_customers")
    column_searchable_list = ("unit_type", "description")
    column_filters = ("unit_type", "description", "can_show_to_customers", "id")

    column_default_sort = ("unit_type", False)  # True == sort descending

    can_create = True
    can_edit = True
    can_delete = True
    can_set_page_size = True
    page_size = 200
    column_display_pk = True

    column_labels = {
        "unit_type": "Unit Type",
        "description": "Description",
        "models_rel.model": "Models",
        "structures_rel.structure": "Structures",
        "can_show_to_customers": "Can Show to Customers",
    }

    column_descriptions = {
        "unit_type": "Unit Type",
        "description": "Description",
        "models_rel.model": "Models that use this unit type",
        "structures_rel.structure": "Structures that use this unit type",
        "can_show_to_customers": "Can this unit type be shown to customers?",
    }

    column_formatters = {
        "models_rel.model": many_to_many_formatter,
        "structures_rel.structure": many_to_many_formatter,
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "models_rel": QueryAjaxModelLoader(
            "models_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select a model",
            **options,
        ),
        "structures_rel": QueryAjaxModelLoader(
            "structures_rel",
            db.session,
            Structure,
            fields=["structure"],
            order_by="structure",
            placeholder="Please select a structure",
            **options,
        ),
    }


class BOMPricingView(MyModelView):
    """Flask-Admin view for BOMPricing model"""

    can_create = False
    can_edit = True
    can_delete = False

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True

    page_size = (
        5 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 20
    )

    column_list = (
        "name",
        "description",
        "bom_pricing_rel",
        "bom_pricing_parts_rel",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        "id",
    )

    form_columns = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        # "model_types_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "description")

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_sortable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_default_sort = ("name", False)  # True == sort descending

    column_formatters = {
        # "part_rel.part_num": lambda v, c, model, n: ", ".join(
        #     [f"{part.part_num}" for part in model.part_rel]
        # ),
    }

    column_labels = {
        "name": "Name",
        "description": "Description",
        "bom_pricing_rel": "Pricing",
        "bom_pricing_parts_rel": "Pricing Parts",
        "timestamp_utc_inserted": "Date Inserted",
        "timestamp_utc_updated": "Date Updated",
    }

    column_descriptions = {
        "name": "Name from BOM Master spreadsheet",
        "bom_pricing_rel": "Pricing from BOM Master spreadsheet",
        "bom_pricing_parts_rel": "Pricing parts from BOM Master spreadsheet",
        "timestamp_utc_inserted": "Date inserted into database",
        "timestamp_utc_updated": "Date updated in database",
    }


class BOMPricingPartView(MyModelView):
    """Flask-Admin view for BOMPricingPart model"""

    can_create = False
    can_edit = True
    can_delete = False

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True

    page_size = (
        5 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 20
    )

    column_list = (
        "name",
        "description",
        "quantity",
        "bom_pricing_rel",
        "bom_pricing_parts_rel",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        "id",
    )

    form_columns = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        # "model_types_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "description")

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_sortable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_default_sort = ("name", False)  # True == sort descending

    column_formatters = {
        # "part_rel.part_num": lambda v, c, model, n: ", ".join(
        #     [f"{part.part_num}" for part in model.part_rel]
        # ),
    }

    column_labels = {
        "name": "Name",
        "description": "Description",
        "bom_pricing_rel": "Pricing",
        "bom_pricing_parts_rel": "Pricing Parts",
        "timestamp_utc_inserted": "Date Inserted",
        "timestamp_utc_updated": "Date Updated",
    }

    column_descriptions = {
        "name": "Name from BOM Master spreadsheet",
        "bom_pricing_rel": "Pricing from BOM Master spreadsheet",
        "bom_pricing_parts_rel": "Pricing parts from BOM Master spreadsheet",
        "timestamp_utc_inserted": "Date inserted into database",
        "timestamp_utc_updated": "Date updated in database",
    }


class BOMBasePowerUnitView(MyModelView):
    """Flask-Admin view for BOMBasePowerUnit model"""

    can_create = False
    can_edit = True
    can_delete = False

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True

    page_size = (
        5 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 20
    )

    column_list = (
        "name",
        "description",
        # "quantity",
        "bom_base_powerunit_part_rel",
        "power_unit_type_rel",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        "id",
    )

    form_columns = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        # "model_types_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "description")

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_sortable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_default_sort = ("name", False)  # True == sort descending

    column_formatters = {
        # "part_rel.part_num": lambda v, c, model, n: ", ".join(
        #     [f"{part.part_num}" for part in model.part_rel]
        # ),
        "timestamp_utc_inserted": datetime_formatter_sk_time,
        "timestamp_utc_updated": datetime_formatter_sk_time,
    }

    column_labels = {
        "name": "Name",
        "description": "Description",
        "bom_base_powerunit_part_rel": "BoM Base Power Unit Part",
        "power_unit_type_rel": "Power Unit Type",
        "timestamp_utc_inserted": "Date Inserted (SK time)",
        "timestamp_utc_updated": "Date Updated (SK time)",
    }

    column_descriptions = {
        "name": "Name from BOM Master spreadsheet",
        "bom_base_powerunit_part_rel": "BoM Base Power Unit Part from BOM Master spreadsheet",
        "power_unit_type_rel": "Power Unit Type from BOM Master spreadsheet",
        "timestamp_utc_inserted": "Date inserted into database (Saskatchewan time)",
        "timestamp_utc_updated": "Date updated in database (Saskatchewan time)",
    }


class BOMPowerUnitView(MyModelView):
    """Flask-Admin view for BOMPowerUnit model"""

    can_create = False
    can_edit = True
    can_delete = False

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True

    page_size = (
        5 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 20
    )

    column_list = (
        "name",
        "description",
        # "quantity",
        "bom_powerunit_part_rel",
        "power_unit_type_rel",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        "id",
    )

    form_columns = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        # "model_types_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "description")

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_sortable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_default_sort = ("name", False)  # True == sort descending

    column_formatters = {
        # "part_rel.part_num": lambda v, c, model, n: ", ".join(
        #     [f"{part.part_num}" for part in model.part_rel]
        # ),
        "timestamp_utc_inserted": datetime_formatter_sk_time,
        "timestamp_utc_updated": datetime_formatter_sk_time,
    }

    column_labels = {
        "name": "Name",
        "description": "Description",
        "bom_powerunit_part_rel": "BoM Power Unit Part",
        "power_unit_type_rel": "Power Unit Type",
        "timestamp_utc_inserted": "Date Inserted (SK time)",
        "timestamp_utc_updated": "Date Updated (SK time)",
    }

    column_descriptions = {
        "name": "Name from BOM Master spreadsheet",
        "bom_powerunit_part_rel": "BoM Power Unit Part from BOM Master spreadsheet",
        "power_unit_type_rel": "Power Unit Type from BOM Master spreadsheet",
        "timestamp_utc_inserted": "Date inserted into database (Saskatchewan time)",
        "timestamp_utc_updated": "Date updated in database (Saskatchewan time)",
    }


class BOMStructureView(MyModelView):
    """Flask-Admin view for BOMStructure model"""

    can_create = False
    can_edit = True
    can_delete = False

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True

    page_size = (
        5 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 20
    )

    column_list = (
        "name",
        "description",
        # "quantity",
        "bom_structure_rel",
        "model_type_rel",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        "id",
    )

    form_columns = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        # "model_types_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "description")

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_sortable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_default_sort = ("name", False)  # True == sort descending

    column_formatters = {
        # "part_rel.part_num": lambda v, c, model, n: ", ".join(
        #     [f"{part.part_num}" for part in model.part_rel]
        # ),
        "timestamp_utc_inserted": datetime_formatter_sk_time,
        "timestamp_utc_updated": datetime_formatter_sk_time,
    }

    column_labels = {
        "name": "Name",
        "description": "Description",
        "bom_structure_rel": "BoM Structure",
        "model_type_rel": "Model Type",
        "timestamp_utc_inserted": "Date Inserted (SK time)",
        "timestamp_utc_updated": "Date Updated (SK time)",
    }

    column_descriptions = {
        "name": "Name from BOM Master spreadsheet",
        "bom_structure_rel": "BoM Structure from BOM Master spreadsheet",
        "model_type_rel": "Model Type from BOM Master spreadsheet",
        "timestamp_utc_inserted": "Date inserted into database (Saskatchewan time)",
        "timestamp_utc_updated": "Date updated in database (Saskatchewan time)",
    }


class BOMPumpTopView(MyModelView):
    """Flask-Admin view for BOMPumpTop model"""

    can_create = False
    can_edit = True
    can_delete = False

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True

    page_size = (
        5 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 20
    )

    column_list = (
        "name",
        "description",
        # "quantity",
        "bom_pump_top_part_rel",
        "model_type_rel",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        "id",
    )

    form_columns = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        # "model_types_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "description")

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_sortable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_default_sort = ("name", False)  # True == sort descending

    column_formatters = {
        # "part_rel.part_num": lambda v, c, model, n: ", ".join(
        #     [f"{part.part_num}" for part in model.part_rel]
        # ),
        "timestamp_utc_inserted": datetime_formatter_sk_time,
        "timestamp_utc_updated": datetime_formatter_sk_time,
    }

    column_labels = {
        "name": "Name",
        "description": "Description",
        "bom_pump_top_part_rel": "BoM Pump Top Part",
        "model_type_rel": "Model Type",
        "timestamp_utc_inserted": "Date Inserted (SK time)",
        "timestamp_utc_updated": "Date Updated (SK time)",
    }

    column_descriptions = {
        "name": "Name from BOM Master spreadsheet",
        "bom_pump_top_part_rel": "BoM Pump Top Part from BOM Master spreadsheet",
        "model_type_rel": "Model Type from BOM Master spreadsheet",
        "timestamp_utc_inserted": "Date inserted into database (Saskatchewan time)",
        "timestamp_utc_updated": "Date updated in database (Saskatchewan time)",
    }


class BOMDGASView(MyModelView):
    """Flask-Admin view for the BOMDGAS model"""

    can_create = False
    can_edit = True
    can_delete = False

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True

    page_size = (
        5 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 20
    )

    column_list = (
        "name",
        "description",
        # "quantity",
        "bom_dgas_part_rel",
        "model_type_rel",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        "id",
    )

    form_columns = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
        # "model_types_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "description")

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_sortable_list = (
        "name",
        "description",
        "timestamp_utc_inserted",
        "timestamp_utc_updated",
    )

    column_default_sort = ("name", False)  # True == sort descending

    column_formatters = {
        # "part_rel.part_num": lambda v, c, model, n: ", ".join(
        #     [f"{part.part_num}" for part in model.part_rel]
        # ),
        "timestamp_utc_inserted": datetime_formatter_sk_time,
        "timestamp_utc_updated": datetime_formatter_sk_time,
    }

    column_labels = {
        "name": "Name",
        "description": "Description",
        "bom_dgas_part_rel": "BoM DGAS Part",
        "model_type_rel": "Model Type",
        "timestamp_utc_inserted": "Date Inserted (SK time)",
        "timestamp_utc_updated": "Date Updated (SK time)",
    }

    column_descriptions = {
        "name": "Name from BOM Master spreadsheet",
        "bom_dgas_part_rel": "BoM DGAS Part from BOM Master spreadsheet",
        "model_type_rel": "Model Type from BOM Master spreadsheet",
        "timestamp_utc_inserted": "Date inserted into database (Saskatchewan time)",
        "timestamp_utc_updated": "Date updated in database (Saskatchewan time)",
    }


class BOMFinishedGoodPartView(MyModelView):
    """
    Flask-Admin view for BOM[FinishedGood]Part many-to-many relationship mapping tables.
    This way we can edit the quantities instead of just defaulting to 1.
    """

    can_create = False
    can_edit = True
    can_delete = False

    # Whether to display the primary key in the list view
    column_display_pk = True
    can_set_page_size = True

    page_size = (
        5 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 20
    )

    column_list = (
        "finished_good_rel.name",
        "part_rel.part_num",
        "quantity",
        "id",
    )

    form_columns = (
        # "finished_good_rel",
        # "part_rel",
        "quantity",
    )

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = (
        # "finished_good_rel",
        # "part_rel",
        "quantity",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "finished_good_rel.name",
        "part_rel.part_num",
        "id",
    )

    column_filters = (
        "finished_good_rel.name",
        "part_rel.part_num",
        "quantity",
        "id",
    )

    column_sortable_list = (
        "finished_good_rel.name",
        "part_rel.part_num",
        "quantity",
        "id",
    )

    # True == sort descending
    column_default_sort = [
        ("finished_good_rel.name", False),
        ("part_rel.part_num", False),
    ]

    column_labels = {
        "finished_good_rel.name": "Finished Good",
        "finished_good_rel": "Finished Good",
        "part_rel.part_num": "Part",
        "part_rel": "Part",
        "quantity": "Quantity",
    }

    column_descriptions = {
        "finished_good_rel.name": "Finished good from BOM Master spreadsheet",
        "finished_good_rel": "Finished good from BOM Master spreadsheet",
        "part_rel.part_num": "Part number from BOM Master spreadsheet",
        "part_rel": "Part number from BOM Master spreadsheet",
        "quantity": "Quantity of this part for this finished good",
    }

    # # AJAX search
    # options = {"minimum_input_length": 0}
    # form_ajax_refs = {
    #     "part_rel": QueryAjaxModelLoader(
    #         "part_rel",
    #         db.session,
    #         Part,
    #         fields=["part_num"],
    #         order_by="part_num",
    #         placeholder="Please select a part",
    #         **options,
    #     ),
    # }


class GatewayTypeView(MyModelView):
    """Flask-Admin view for GatewayType model"""

    # Whether to display the primary key in the list view
    column_display_pk = True

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "description")

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = ("name", "description")

    can_set_page_size = True

    page_size = 200

    # AJAX search for gateways to link to this gateway type
    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        # Need this for testing since it's a foreign key
        "gateways_rel": QueryAjaxModelLoader(
            "gateways_rel",
            db.session,
            Gw,
            fields=["gateway"],
            order_by="gateway",
            placeholder="Please select a gateway",
            **options,
        ),
    }


class StructureByModelView(MyModelView):
    """Flask-Admin view for StructureByModel view model"""

    column_display_pk = False
    can_create = False
    can_edit = False
    can_delete = False

    can_set_page_size = True
    page_size = 200

    column_list = (
        "model",
        "most_recent_install_date",
        "total_units",
    )

    column_sortable_list = (
        "model",
        "most_recent_install_date",
        "total_units",
    )

    # True == sort descending
    column_default_sort = [("model", True), ("total_units", True)]

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("model",)

    column_filters = ("model", "most_recent_install_date", "total_units")

    column_labels = {
        "model": "Model",
        "total_units": "Total Structures",
    }

    column_descriptions = {
        "model": "Model of the structure",
        "most_recent_install_date": "Most recent install date for this model",
        "total_units": "Total structures in structures table",
    }


class PartFilterView(MyModelView):
    """Flask-Admin view for Filters model (hydraulic oil filters)"""

    column_list = (
        "part_num",
        "description",
        "id",
    )

    # Whether to display the primary key in the list view
    column_display_pk = True
    page_size = 100
    can_set_page_size = True

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("part_num", "description")
    column_filters = ("part_num",)

    # For a faster editing experience, enable inline editing in the list view:
    column_editable_list = ("part_num", "description")

    column_labels = {
        "part_num": "Part Number",
        "description": "Description",
    }


class ReportEmailHourlyView(MyModelView):
    """Flask-Admin view for ReportEmailHourly model (public.report_email_hourly table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    page_size = 50

    column_list = (
        "users_rel",
        "customers_rel.customer",
        "hours_rel",
        "days_of_week_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
        "id",
    )

    column_sortable_list = (
        # "users_rel",
        "customers_rel.customer",
        # "hours_rel",
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
        "id",
    )
    column_default_sort = [("customers_rel.customer", False)]

    # For a faster editing experience, enable inline editing in the list view:
    # Won't work if you add 'gateway' to this list since 'gateway' is the primary key
    column_editable_list = (
        "users_rel",
        "hours_rel",
        "days_of_week_rel",
        # The following are many-to-many relationship tables, so they don't work with inline editing
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "users_rel",
        # No need for this, since the alerting software will only show units from the user's "customer"
        # "customers_rel",
        "hours_rel",
        "days_of_week_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
    )

    column_searchable_list = (
        "users_rel.first_name",
        "users_rel.last_name",
        "customers_rel.customer",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "warehouses_rel.name",
        "id",
    )
    column_filters = column_searchable_list

    column_labels = {
        "users_rel": "User Being Emailed",
        "users_rel.first_name": "First Name",
        "users_rel.last_name": "Last Name",
        "customers_rel.customer": "Customer",
        "hours_rel": "Hours Email Sent",
        "days_of_week_rel": "Days of Week Email Sent",
        "unit_types_rel": "Unit Types Wanted",
        "unit_types_rel.unit_type": "Unit Types Wanted",
        "model_types_rel": "Models Wanted",
        "model_types_rel.model": "Models Wanted",
        "warehouses_rel": "Warehouses Wanted",
        "warehouses_rel.name": "Warehouses Wanted",
        "max_distance_km": "Max Distance (km)",
    }
    column_descriptions = {
        "users_rel": "User to whom the email report is being sent",
        "hours_rel": "Hours in which email is being sent",
        "days_of_week_rel": "Days of the week in which email is being sent",
        "unit_types_rel": "High-level unit types wanted (e.g. XFER, EGAS, UNO)",
        "model_types_rel": "Specific models wanted (e.g. XFER 2270)",
        "warehouses_rel": "Show units from these warehouses in report",
        "max_distance_km": "Maximum distance in km from user's main GPS location (which is set in admin/customers/users table and defaults to the IJACK shop in Moosomin, SK)",
    }

    column_formatters = {
        "max_distance_km": lambda v, c, model, n: f"{model.max_distance_km:,} km",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "users_rel": QueryAjaxModelLoader(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
        "unit_types_rel": QueryAjaxModelLoader(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select unit types",
            **options,
        ),
        "model_types_rel": QueryAjaxModelLoader(
            "model_types_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select models",
            **options,
        ),
        "warehouses_rel": QueryAjaxModelLoader(
            "warehouses_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select warehouses",
            **options,
        ),
    }


class ReportEmailDeratesView(ReportEmailHourlyView):
    column_list = (
        "users_rel",
        "derate_threshold",
        "customers_rel.customer",
        "hours_rel",
        "days_of_week_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
        "id",
    )

    column_sortable_list = (
        # "users_rel",
        "derate_threshold",
        "customers_rel.customer",
        # "hours_rel",
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
        "id",
    )
    column_default_sort = [("customers_rel.customer", False)]

    # For a faster editing experience, enable inline editing in the list view:
    # Won't work if you add 'gateway' to this list since 'gateway' is the primary key
    column_editable_list = (
        "users_rel",
        "derate_threshold",
        "hours_rel",
        "days_of_week_rel",
        # The following are many-to-many relationship tables, so they don't work with inline editing
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "users_rel",
        # No need for this, since the alerting software will only show units from the user's "customer"
        # "customers_rel",
        "derate_threshold",
        "hours_rel",
        "days_of_week_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
    )

    column_searchable_list = (
        "users_rel.first_name",
        "users_rel.last_name",
        "derate_threshold",
        "customers_rel.customer",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "warehouses_rel.name",
        "id",
    )
    column_filters = column_searchable_list

    column_labels = {
        "users_rel": "User Being Emailed",
        "users_rel.first_name": "First Name",
        "users_rel.last_name": "Last Name",
        "derate_threshold": "Derate Threshold",
        "customers_rel.customer": "Customer",
        "hours_rel": "Hours Email Sent",
        "days_of_week_rel": "Days of Week Email Sent",
        "unit_types_rel": "Unit Types Wanted",
        "unit_types_rel.unit_type": "Unit Types Wanted",
        "model_types_rel": "Models Wanted",
        "model_types_rel.model": "Models Wanted",
        "warehouses_rel": "Warehouses Wanted",
        "warehouses_rel.name": "Warehouses Wanted",
        "max_distance_km": "Max Distance (km)",
    }
    column_descriptions = {
        "users_rel": "User to whom the email report is being sent",
        "derate_threshold": "Minimum derate threshold to be included in the report",
        "hours_rel": "Hours in which email is being sent",
        "days_of_week_rel": "Days of the week in which email is being sent",
        "unit_types_rel": "High-level unit types wanted (e.g. XFER, EGAS, UNO)",
        "model_types_rel": "Specific models wanted (e.g. XFER 2270)",
        "warehouses_rel": "Show units from these warehouses in report",
        "max_distance_km": "Maximum distance in km from user's main GPS location (which is set in admin/customers/users table and defaults to the IJACK shop in Moosomin, SK)",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "users_rel": QueryAjaxModelLoader(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
        "unit_types_rel": QueryAjaxModelLoader(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select unit types",
            **options,
        ),
        "model_types_rel": QueryAjaxModelLoader(
            "model_types_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select models",
            **options,
        ),
        "warehouses_rel": QueryAjaxModelLoader(
            "warehouses_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select warehouses",
            **options,
        ),
    }


class ReportEmailOpHoursView(MyModelView):
    """Flask-Admin view for ReportEmailOpHours model (public.report_email_op_hours table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    page_size = 50

    column_list = (
        "user_rel",
        "customers_rel.customer",
        "types_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
        "id",
    )

    column_sortable_list = (
        # "user_rel",
        "customers_rel.customer",
        # "types_rel",
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
        "id",
    )
    column_default_sort = [("customers_rel.customer", False)]

    column_editable_list = (
        "user_rel",
        # The following are many-to-many relationship tables, so they don't work with inline editing
        # "types_rel",
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
    )

    form_columns = (
        "user_rel",
        # No need for this, since the alerting software will only show units from the user's "customer"
        # "customers_rel",
        "types_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
    )

    column_searchable_list = (
        "user_rel.first_name",
        "user_rel.last_name",
        "customers_rel.customer",
        "types_rel.name",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "warehouses_rel.name",
        "id",
    )
    column_filters = column_searchable_list

    column_labels = {
        "user_rel": "User Being Emailed",
        "user_rel.first_name": "First Name",
        "user_rel.last_name": "Last Name",
        "customers_rel.customer": "Customer",
        "types_rel": "Email Types Wanted",
        "types_rel.name": "Email Types Wanted",
        "unit_types_rel": "Unit Types Wanted",
        "unit_types_rel.unit_type": "Unit Types Wanted",
        "model_types_rel": "Models Wanted",
        "model_types_rel.model": "Models Wanted",
        "warehouses_rel": "Warehouses Wanted",
        "warehouses_rel.name": "Warehouses Wanted",
        "max_distance_km": "Max Distance (km)",
    }
    column_descriptions = {
        "user_rel": "User to whom the email report is being sent",
        "types_rel": "Types of email wanted (e.g. 100 hours, 1000 hours, etc.)",
        "unit_types_rel": "High-level unit types wanted (e.g. XFER, EGAS, UNO)",
        "model_types_rel": "Specific models wanted (e.g. XFER 2270)",
        "warehouses_rel": "Show units from these warehouses in report",
        "max_distance_km": "Maximum distance in km from user's main GPS location (which is set in admin/customers/users table and defaults to the IJACK shop in Moosomin, SK)",
    }

    column_formatters = {
        "max_distance_km": lambda v, c, model, n: f"{model.max_distance_km:,} km",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "user_rel": QueryAjaxModelLoader(
            "user_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
        "types_rel": QueryAjaxModelLoader(
            "types_rel",
            db.session,
            ReportEmailOpHoursType,
            fields=["name"],
            order_by="name",
            placeholder="Please select an email type",
            **options,
        ),
        "unit_types_rel": QueryAjaxModelLoader(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select unit types",
            **options,
        ),
        "model_types_rel": QueryAjaxModelLoader(
            "model_types_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select models",
            **options,
        ),
        "warehouses_rel": QueryAjaxModelLoader(
            "warehouses_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select warehouses",
            **options,
        ),
    }


class ReportEmailInventoryView(MyModelView):
    """Flask-Admin view for ReportEmailInventory model (public.report_email_inventory table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    page_size = 50

    column_list = (
        "user_rel",
        "warehouses_rel",
        "days_of_week_rel",
        "hours_rel",
        "max_distance_km",
        "id",
    )

    column_sortable_list = (
        # "user_rel",
        # "warehouses_rel",
        "id",
        "max_distance_km",
    )
    column_default_sort = [("id", False)]

    column_editable_list = (
        "user_rel",
        # The following are many-to-many relationship tables, so they don't work with inline editing
        # "warehouses_rel",
        # "days_of_week_rel",
        # "hours_rel",
        "max_distance_km",
    )

    form_columns = (
        "user_rel",
        "warehouses_rel",
        "days_of_week_rel",
        "hours_rel",
        "max_distance_km",
    )

    column_searchable_list = (
        "user_rel.first_name",
        "user_rel.last_name",
        "warehouses_rel.name",
        "id",
    )
    column_filters = column_searchable_list

    column_labels = {
        "user_rel": "User Being Emailed",
        "user_rel.first_name": "First Name",
        "user_rel.last_name": "Last Name",
        "warehouses_rel": "Warehouses Wanted",
        "warehouses_rel.name": "Warehouses Wanted",
        "days_of_week_rel": "Days of Week Email Sent",
        "hours_rel": "Hours Email Sent",
        "max_distance_km": "Max Distance (km)",
    }
    column_descriptions = {
        "user_rel": "User to whom the email report is being sent",
        "warehouses_rel": "Show units from these warehouses in report",
        "days_of_week_rel": "Days of the week in which email will being sent",
        "hours_rel": "Hours in which email will be sent",
        "max_distance_km": "Maximum distance in km from user's main GPS location (which is set in admin/customers/users table and defaults to the IJACK shop in Moosomin, SK)",
    }

    # column_formatters = {
    #     "max_distance_km": lambda v, c, model, n: f"{model.max_distance_km:,} km",
    # }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "user_rel": QueryAjaxModelLoader(
            "user_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
        "warehouses_rel": QueryAjaxModelLoader(
            "warehouses_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select warehouses",
            **options,
        ),
        "days_of_week_rel": QueryAjaxModelLoader(
            "days_of_week_rel",
            db.session,
            DayOfWeek,
            fields=["name"],
            order_by="name",
            placeholder="Please select days of the week",
            **options,
        ),
        "hours_rel": QueryAjaxModelLoader(
            "hours_rel",
            db.session,
            Hour,
            fields=["hour"],
            order_by="hour",
            placeholder="Please select hours",
            **options,
        ),
    }


class AlertsSentView(MyModelView):
    """Flask-Admin view for AlertsSent model (public.alerts_sent table)"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = True
    # Duplicate button - make None to disable
    column_extra_row_actions = None
    page_size = 10
    can_set_page_size = True

    column_list = (
        "timestamp_utc",
        "power_unit",
        "warning_msg",
        "explanation",
        "program",
        "function",
        "alerts_sent_users_rel",
        "id",
    )
    # True == sort descending
    column_default_sort = [("timestamp_utc", True), ("id", True)]

    def get_query(self):
        """Only view the alerts sent in 'production' as opposed to 'testing'"""
        return self.session.query(self.model).filter(
            self.model.dev_test_prd == "production"
        )

    column_filters = (
        "timestamp_utc",
        "power_unit",
        "warning_msg",
        "program",
        "function",
        "alerts_sent_users_rel",
    )

    column_searchable_list = (
        "power_unit",
        "warning_msg",
        "program",
        "function",
        "alerts_sent_users_rel",
    )

    column_sortable_list = (
        "timestamp_utc",
        "power_unit",
        "program",
        "function",
    )

    column_formatters = {
        "timestamp_utc": datetime_formatter_sk_time,
        # "alerts_sent_users_rel": many_to_many_formatter,
        "warning_msg": preserve_line_breaks,
        "explanation": preserve_line_breaks,
    }
    column_labels = {
        "timestamp_utc": "Date Sent (SK Time)",
        "power_unit": "Power Unit",
        "warning_msg": "Alert Message",
        "explanation": "Why Sent?",
        "program": "Software Program",
        "function": "Software Function",
        "alerts_sent_users_rel": "Users Alerted",
    }
    column_descriptions = {
        "timestamp_utc": "Datetime sent (SK time)",
        "warning_msg": "This is the exact alert that was sent",
        "explanation": "Why did this alert get sent? For diagnostic purposes",
        "program": "Software program (gateway or cloud) that sent the alert",
        "function": "Software function that sent the alert",
        "alerts_sent_users_rel": "These are all the users who were sent alerts",
    }


class AlertsSentUsersView(MyModelView):
    """Flask-Admin view for AlertsSentUser model (public.alerts_sent_users table)"""

    can_create = False
    can_edit = False
    can_delete = False
    # column_display_pk = True
    # Duplicate button - make None to disable
    column_extra_row_actions = None
    page_size = 20
    can_set_page_size = True

    column_list = (
        # "",
        # "",
        # "",
        # "",
        # "",
        # "",
        "alerts_sent_id",
        "users_rel",
        "msg_type",
        "twilio_sid",
        "mailgun_id",
        "id",
    )

    column_labels = {
        "alerts_sent_id": "Alert Sent ID",
        "msg_type": "Message Type",
        "users_rel": "User",
        "twilio_sid": "Twilio SID",
        "mailgun_id": "Mailgun ID",
    }
    column_descriptions = {
        "alerts_sent_id": "ID of the alert that was sent to multiple people",
        "msg_type": "Type of message sent (e.g. SMS or email)",
        "users_rel": "User who was sent the alert",
        "twilio_sid": "Twilio SID for SMS messages",
        "mailgun_id": "Mailgun ID for email messages",
    }

    # # True == sort descending
    column_default_sort = [
        ("alerts_sent_id", True),
    ]

    column_searchable_list = (
        "alerts_sent_id",
        "msg_type",
        "users_rel.first_name",
        "users_rel.last_name",
        "users_rel.email",
        "twilio_sid",
        "mailgun_id",
    )

    column_filters = (
        "alerts_sent_id",
        "msg_type",
        "users_rel",
        "twilio_sid",
        "mailgun_id",
    )

    column_sortable_list = (
        "alerts_sent_id",
        "msg_type",
        "twilio_sid",
        "mailgun_id",
    )

    def get_query(self):
        """Only view the alerts sent in 'production' as opposed to 'testing'"""
        return self.session.query(self.model).filter(
            self.model.dev_test_prd == "production"
        )


class AlertsSentMaintView(MyModelView):
    """Flask-Admin view for ReportEmailHourly model (public.report_email_hourly table)"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = True
    # Duplicate button - make None to disable
    column_extra_row_actions = None

    column_list = (
        "id",
        "timestamp_utc",
        "alerts_sent_maint_email_types_rel",
        "customers_rel",
    )
    # True == sort descending
    column_default_sort = [("timestamp_utc", True), ("id", True)]

    def get_query(self):
        """Only view the alerts sent in 'production' as opposed to 'testing'"""
        return self.session.query(self.model).filter(
            self.model.dev_test_prd == "production"
        )

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "customers_rel": QueryAjaxModelLoader(
            "customers_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select a customer",
            **options,
        ),
        "alerts_sent_maint_email_types_rel": QueryAjaxModelLoader(
            "alerts_sent_maint_email_types_rel",
            db.session,
            AlertsSentMaintEmailType,
            fields=["name", "description"],
            order_by="name",
            placeholder="Please select an email type",
            **options,
        ),
    }

    column_formatters = {
        "timestamp_utc": datetime_formatter_sk_time,
    }
    column_labels = {
        "timestamp_utc": "Date Sent (SK Time)",
        "customers_rel": "Customer",
        "alerts_sent_maint_email_types_rel": "Email Type",
    }
    column_descriptions = {
        "timestamp_utc": "Datetime sent (SK time)",
        "customers_rel": "Customer to whom the maintenance email is being sent",
        "alerts_sent_maint_email_types_rel": "Type of maintenance email being sent (e.g. filter change or auto-greaser reminder)",
    }


class AlertsSentMaintUsersView(MyModelView):
    """Flask-Admin view for AlertsSentMaintUser model (public.vw_alerts_sent_maint_users_joined VIEW)"""

    can_create = False
    can_edit = False
    can_delete = False
    # column_display_pk = True
    # Duplicate button - make None to disable
    column_extra_row_actions = None

    def get_query(self):
        """
        Only view the remote control actions in 'production'
        as opposed to 'testing' or 'development'
        """
        return self.session.query(self.model).filter(
            self.model.dev_test_prd == "production"
        )

    column_list = (
        "alerts_sent_maint_rel",
        "alerts_sent_maint_rel_timestamp_utc_proxy",
        "alerts_sent_maint_rel.email_types_rel_name_proxy",
        "alerts_sent_maint_rel.customers_rel_customer_proxy",
        "users_rel",
    )
    column_searchable_list = (
        "alerts_sent_maint_rel.email_types_rel_name_proxy",
        "alerts_sent_maint_rel.customers_rel_customer_proxy",
        "users_rel.email",
        "users_rel.first_name",
        "users_rel.last_name",
    )
    column_filters = (
        "alerts_sent_maint_rel.email_types_rel_name_proxy",
        "alerts_sent_maint_rel.customers_rel_customer_proxy",
        "users_rel.email",
        "users_rel.first_name",
        "users_rel.last_name",
    )
    column_formatters = {
        "alerts_sent_maint_rel_timestamp_utc_proxy": datetime_formatter_sk_time,
    }
    column_labels = {
        "alerts_sent_maint_rel": "Alert Email Sent - ID",
        "alerts_sent_maint_rel_timestamp_utc_proxy": "Date Sent (SK Time)",
        "alerts_sent_maint_rel.email_types_rel_name_proxy": "Email Type",
        "alerts_sent_maint_rel.customers_rel_customer_proxy": "Customer",
        "users_rel": "User Alerted",
        "users_rel.email": "User Email",
        "users_rel.first_name": "User First Name",
        "users_rel.last_name": "User Last Name",
    }
    column_descriptions = {
        "alerts_sent_maint_rel": "ID of the maintenance email being sent",
        "alerts_sent_maint_rel_timestamp_utc_proxy": "Datetime sent (SK time)",
        "alerts_sent_maint_rel.email_types_rel_name_proxy": "Type of maintenance email being sent (e.g. filter change or auto-greaser reminder)",
        "alerts_sent_maint_rel.customers_rel_customer_proxy": "Customer to whom the maintenance email is being sent",
        "users_rel": "User who was sent the alert",
    }

    # True == sort descending
    column_default_sort = [
        ("alerts_sent_maint_rel_timestamp_utc_proxy", True),
        ("alerts_sent_maint_rel.email_types_rel_name_proxy", True),
        ("alerts_sent_maint_rel.customers_rel_customer_proxy", True),
    ]
    column_sortable_list = (
        "alerts_sent_maint_rel_timestamp_utc_proxy",
        "alerts_sent_maint_rel.email_types_rel_name_proxy",
        "alerts_sent_maint_rel.customers_rel_customer_proxy",
        "users_rel",
    )


class RemoteControlView(MyModelView):
    """Flask-Admin view for RemoteControl model"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = True
    # Duplicate button - make None to disable
    column_extra_row_actions = None
    can_set_page_size = True
    page_size = 20

    def get_query(self):
        """
        Only view the remote control actions in 'production'
        as opposed to 'testing' or 'development'
        """
        return self.session.query(self.model).filter(
            self.model.dev_test_prd == "production"
        )

    column_list = (
        "timestamp_utc",
        "power_unit_rel.power_unit_str",
        "users_rel.email",
        "action",
        "metric",
        "value_wanted",
        "id",
    )

    column_filters = (
        "timestamp_utc",
        "power_unit_rel.power_unit_str",
        "users_rel.email",
        "action",
        "metric",
        "value_wanted",
        "id",
    )

    column_searchable_list = (
        "power_unit_rel.power_unit_str",
        "users_rel.email",
        "users_rel.first_name",
        "users_rel.last_name",
        "action",
        "metric",
        "value_wanted",
        "id",
    )

    column_formatters = {
        "timestamp_utc": datetime_formatter_sk_time,
    }

    # True == sort descending
    column_default_sort = [("timestamp_utc", True), ("id", True)]

    column_sortable_list = (
        "timestamp_utc",
        "power_unit_rel.power_unit_str",
        "users_rel.email",
        "action",
        "metric",
        "value_wanted",
        "id",
    )

    column_labels = {
        "timestamp_utc": "Date Recorded (SK Time)",
        "power_unit_rel.power_unit_str": "Power Unit",
        "users_rel.email": "User Email",
        "action": "Action",
        "metric": "Metric",
        "value_wanted": "Value Wanted",
    }

    column_descriptions = {
        "timestamp_utc": "Datetime recorded (SK time)",
        "power_unit_rel.power_unit_str": "Power unit that was remotely controlled",
        "users_rel.email": "User who remotely controlled the power unit",
        "action": "Action taken (e.g. start, stop, etc.)",
        "metric": "Metric that was remotely controlled (e.g. pressure, temperature, etc.)",
        "value_wanted": "New value that was set for the metric",
    }


class RodBarrelShuttleView(MyModelView):
    """Flask-Admin view for Rod, Barrel, and ShuttleValve models"""

    # # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    # column_exclude_list = ['password_hash', ]
    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    page_size = 8

    column_list = (
        "name",
        "description",
        "id",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "name",
        "description",
    )
    column_editable_list = form_columns

    column_default_sort = ("name", False)  # True == sort descending

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "name",
        "description",
    )
    column_filters = (
        "name",
        "description",
    )


class CompressionImageView(MyModelView):
    """Flask-Admin view for both CompressionImage and SurfaceImage models"""

    can_create = False
    can_edit = True
    can_delete = False
    column_display_pk = True
    can_set_page_size = True
    page_size = 200

    column_list = (
        "image",
        "description_rel.description",
        # "description_rel",
        "pattern_id",
        "num_in_cluster",
        "description_rel.send_alert",
        "description_rel.solution",
        "description_rel.explanation",
        "ml_version",
        "cluster_id",
    )
    column_sortable_list = (
        "image",
        "description_rel.description",
        "pattern_id",
        "num_in_cluster",
        "description_rel.send_alert",
        "description_rel.solution",
        "description_rel.explanation",
        "ml_version",
        "cluster_id",
    )

    # Control the order of the columns in the forms
    form_columns = (
        # "image",
        # "ml_version",
        # "cluster_id",
        # 'description_rel' is related to the foreign key 'pattern_id'
        "description_rel",
        # Association proxy form columns don't work
        # "solution",
        # "explanation",
    )
    # For some reason, this doesn't work
    # column_editable_list = (
    #     "description_rel",
    #     # "solution",
    #     # "explanation",
    # )

    # True = sort descending
    column_default_sort = [
        ("ml_version", True),
        ("description_rel.send_alert", True),
        # ("description_rel.description", False),
        ("cluster_id", False),
    ]

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "ml_version",
        "cluster_id",
        "description_rel.description",
        "description_rel.solution",
        "description_rel.explanation",
    )
    column_filters = (
        "ml_version",
        "cluster_id",
        "description_rel.description",
        "description_rel.solution",
        "description_rel.explanation",
        "description_rel.send_alert",
    )

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        # Need this for testing since it's a foreign key
        "description_rel": QueryAjaxModelLoader(
            "description_rel",
            db.session,
            CompressionPattern,
            fields=["description"],
            order_by="description",
            placeholder="Please select a compression pattern",
            **options,
        ),
    }

    column_formatters = {"image": image_thumbnail}

    column_labels = {
        "image": "Image",
        "description_rel": "Pattern",
        "description_rel.description": "Description",
        "description_rel.send_alert": "Send Alert",
        "description_rel.solution": "Solution",
        "description_rel.explanation": "Explanation",
        "ml_version": "ML Version",
        "cluster_id": "Cluster ID",
        "pattern_id": "Pattern ID",
        "num_in_cluster": "Num in Cluster",
    }

    column_descriptions = {
        "image": "Image of the pattern",
        "description_rel": "Pattern of the image",
        "description_rel.description": "Description of the pattern",
        "description_rel.send_alert": "Send alert for this pattern?",
        "description_rel.solution": "Solution for this pattern",
        "description_rel.explanation": "Explanation for this pattern",
        "ml_version": "Machine learning version",
        "cluster_id": "Cluster ID",
        "pattern_id": "Pattern ID",
        "num_in_cluster": "Number of samples in cluster",
    }

    # basedir = os.path.abspath(os.path.dirname(__file__))
    # file_path = os.path.join(basedir, 'files')

    # form_extra_fields = {
    #     'path': form.ImageUploadS3Field(
    #         'Image', base_path=file_path, thumbnail_size=(100, 100, True)
    #     )
    # }


class SurfaceImageView(CompressionImageView):
    """Flask-Admin view for SurfaceImage models"""

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        # Need this for testing since it's a foreign key
        "description_rel": QueryAjaxModelLoader(
            "description_rel",
            db.session,
            SurfacePattern,
            fields=["description"],
            order_by="description",
            placeholder="Please select a surface card pattern",
            **options,
        ),
    }


class CompressionPatternView(MyModelView):
    """Flask-Admin view for both CompressionPattern and SurfaceImage models"""

    can_create = True
    can_edit = True
    can_delete = False
    column_display_pk = True
    can_set_page_size = True
    page_size = 200

    column_list = (
        "description",
        "send_alert",
        "solution",
        "explanation",
        "id",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "description",
        "send_alert",
        "solution",
        "explanation",
    )
    column_editable_list = form_columns

    # True = sort descending
    column_default_sort = [("description", False), ("id", False)]

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "description",
        "solution",
        "explanation",
    )
    column_filters = form_columns


class MapAbbrevItemView(MyModelView):
    """
    Flask-Admin view for MapAbbrevItem model
    (map_abbrev_item table at admin.can_bus_cob_map)
    """

    # Allow Ging and Dan to edit as well
    can_create = True
    can_edit = True
    can_delete = False
    column_display_pk = True
    can_set_page_size = True
    page_size = 100

    column_list = (
        "cob",
        "byte_start_index",
        "num_bytes",
        "signed",
        "data_type",
        "decimals",
        "resolution",
        "offset_",
        "bit_start_index",
        "abbrev",
        "plus1_variable",
        "plus1_program",
        "rcom_name",
        "rcom_tab",
        "controller_version",
        "machine",
        "item",
        "units",
        "description",
        "control_num",
        # "enum",
        "number_",
        # "interesting",
        "modbus_holding_registers_rel",
        # "modbus_holding_registers_rel.address",
        # "modbus_holding_registers_rel.n_registers",
        "id",
    )
    column_sortable_list = (
        "cob",
        "byte_start_index",
        "num_bytes",
        "signed",
        "data_type",
        "decimals",
        "resolution",
        "offset_",
        "bit_start_index",
        "abbrev",
        "plus1_variable",
        "plus1_program",
        "rcom_name",
        "rcom_tab",
        "controller_version",
        "machine",
        "item",
        "units",
        "description",
        "control_num",
        # "enum",
        "number_",
        # "interesting",
        # The following doesn't work since it's a relationship
        # "modbus_holding_registers_rel",
        "id",
    )
    column_default_sort = [
        ("cob", False),
        ("byte_start_index", False),
        ("bit_start_index", False),
        ("abbrev", False),
    ]

    form_columns = (
        "cob",
        "byte_start_index",
        "num_bytes",
        "signed",
        "data_type",
        "decimals",
        "resolution",
        "offset_",
        "bit_start_index",
        "abbrev",
        "plus1_variable",
        "plus1_program",
        "rcom_name",
        "rcom_tab",
        "controller_version",
        "machine",
        "item",
        "units",
        "description",
        "control_num",
        # "enum",
        "number_",
        # "interesting",
    )
    column_editable_list = form_columns

    form_choices = {
        "plus1_program": [
            ("DP200", "DP200"),
            ("MC024", "MC024"),
        ],
        "rcom_tab": [
            ("Not on RCOM", "Not on RCOM"),
            ("Indicators", "Indicators"),
            ("Charts (Normal)", "Charts (Normal)"),
            ("Charts (RT)", "Charts (RT)"),
            ("Cards", "Cards"),
            ("Control", "Control"),
            ("Alarm Log", "Alarm Log"),
        ],
    }

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = column_list
    column_filters = column_list

    column_labels = {
        "cob": "Cob",
        "byte_start_index": "Byte Start Index",
        "bit_start_index": "Bit Start Index",
        "abbrev": "Abbrev",
        "plus1_variable": "PLUS+1 Name",
        "plus1_program": "PLUS+1 Program",
        "rcom_name": "RCOM Name",
        "rcom_tab": "RCOM Tab",
        "machine": "Machine",
        "controller_version": "Controller Version",
        "item": "Item",
        "units": "Units",
        "description": "Description",
        "num_bytes": "Num Bytes",
        "data_type": "Data Type",
        "control_num": "Control Number",
        # enum: "Control Enum",
        "resolution": "Resolution",
        "offset_": "Offset",
        "number_": "Number",
        "interesting": "Interesting",
        "signed": "Signed or Unsigned",
        "decimals": "Decimals Wanted",
        "modbus_holding_registers_rel": "Modbus Holding Registers",
        # "modbus_holding_registers_rel.address": "Modbus Address",
        # "modbus_holding_registers_rel.n_registers": "Modbus Number of Registers",
    }
    column_descriptions = {
        "cob": "Cob ID on CAN bus",
        "byte_start_index": "Byte start index in 8-byte data array (0-7)",
        "bit_start_index": "Bit start index for boolean values in a 1-byte U8 (0-7)",
        "abbrev": "The Python software and database use this",
        "plus1_variable": "PLUS+1 Guide variable/signal name",
        "plus1_program": "Which program transmits, the display (DP200) or the controller (MC024)?",
        "rcom_name": "RCOM indicator or chart variable name",
        "rcom_tab": "RCOM tab on which this metric is found",
        "machine": "To what type of IJACK does this apply?",
        "controller_version": "Which controller software update introduced it? Typically this is equivalent to '>=SWV' unless it's deprecated",
        "item": "Description that customer sees",
        "units": "Unit of measurement",
        "description": "Longer description",
        "num_bytes": "Number of bytes used (e.g. 1=U8; 2=U16)",
        "data_type": "PLUS+1 software data length (e.g. U8, U16, U32, etc)",
        "control_num": "ID number for remote controlling this item via single CAN cob ID (Advanced Remote Control)",
        # enum: "Enumeration for advanced remote control (e.g. 1-10, typically for data type/divide by)",
        "resolution": "Resolution",
        "offset_": "Offset",
        "number_": "Number (not sure what this is used for)",
        "interesting": "Whether this is interesting enough to share with a customer (1-3 ranking, or empty)",
        "signed": "Send/receive as a signed (positive or negative) integer on the CAN bus or Modbus",
        "decimals": "Decimals wanted (e.g. 2 decimals would transform 4,321 on the bus into decimal number 43.21)",
        "modbus_holding_registers_rel": "Modbus Holding Registers (some items may be on more than one holding register, like 'HOURS' for operating hours)",
        # "modbus_holding_registers_rel.address": "Modbus address (e.g. 0, 1, 2, etc)",
        # "modbus_holding_registers_rel.n_registers": "Number of registers required (i.e. 1 or 2 16-bit registers)",
    }

    # def signed_unsigned_formatter(v, c, m, n) -> str:
    #     """Formatter for signed or unsigned"""
    #     is_signed = getattr(m, n)
    #     if is_signed:
    #         return "Signed"
    #     return "Unsigned"

    # column_formatters = {
    #     "modbus_holding_registers_rel.address": lambda v,
    #     c,
    #     m,
    #     n: f"{m.modbus_holding_registers_rel.address:,}",
    #     "modbus_holding_registers_rel.n_registers": lambda v,
    #     c,
    #     m,
    #     n: f"{m.modbus_holding_registers_rel.n_registers:,}",
    # }


class ModbusHoldingRegisterView(MyModelView):
    """Flask-Admin view for ModbusHoldingRegister model"""

    can_create = True
    can_edit = True
    can_delete = False
    column_display_pk = True
    can_set_page_size = True
    page_size = (
        10 if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") else 100
    )

    column_list = (
        "abbrev_rel.abbrev",
        "abbrev_rel.description",
        "address",
        "n_registers",
        "holding_reg_40k",
        "in_web_api",
        "writable_modbus",
        "writable_web_api",
        "min_val",
        "max_val",
        "id",
    )

    column_sortable_list = column_list
    column_searchable_list = column_list
    column_default_sort = [
        ("abbrev_rel.abbrev", False),
        ("address", False),
    ]

    column_filters = column_list

    form_columns = (
        "abbrev_rel",
        "address",
        "n_registers",
        "in_web_api",
        "writable_modbus",
        "writable_web_api",
        "min_val",
        "max_val",
    )
    column_editable_list = (
        "in_web_api",
        "writable_modbus",
        "writable_web_api",
        "min_val",
        "max_val",
    )

    column_labels = {
        "abbrev_rel": "Abbreviation / Metric Name",
        "abbrev_rel.abbrev": "Abbreviation",
        "abbrev_rel.description": "Description",
        "address": "Address",
        "n_registers": "Number of Registers",
        "holding_reg_40k": "Holding Register 40k",
        "in_web_api": "In Web API",
        "writable_modbus": "Writable Modbus",
        "writable_web_api": "Writable Web API",
        "min_val": "Min Value",
        "max_val": "Max Value",
    }
    column_descriptions = {
        "abbrev_rel": "Abbreviation of the metric name",
        "abbrev_rel.abbrev": "Abbreviation of the metric name",
        "abbrev_rel.description": "Description of the metric",
        "address": "Modbus address (e.g. 0, 1, 2, etc)",
        "n_registers": "Number of registers required (i.e. 1 or 2 16-bit registers)",
        "holding_reg_40k": "Holding register number (e.g. 40,001, 40,002, etc), equal to Modbus address + 40,001",
        "in_web_api": "Is this metric in the Web API as well?",
        "writable_modbus": "Is this metric writable (i.e. can remote-control it) via Modbus?",
        "writable_web_api": "Is this metric writable (i.e. can remote-control it) via the Web API?",
        "min_val": "Minimum value for this metric",
        "max_val": "Maximum value for this metric",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "abbrev_rel": QueryAjaxModelLoader(
            "abbrev_rel",
            db.session,
            MapAbbrevItem,
            fields=["abbrev", "description"],
            order_by="abbrev",
            placeholder="Please select an abbreviation",
            **options,
        ),
    }


class CalculatorView(MyModelView):
    """Flask-Admin view for Calculator model"""

    page_size = 2000

    column_list = [
        "name",
        "description",
        "model_types_rel",
        "model_types_rel.unit_types_rel",
        "power_unit_type_rel",
        "diameter",
        "area",
        "stroke",
        "max_spm",
        "max_delta_p",
        "mawp",
        "rod_size",
        "motor_hp",
        "hyds",
        # Two generated columns
        "max_liquid_m3pd",
        "min_liquid_m3pd_10pct",
        "hyd_size_inch",
        "single_loss_cu_inch",
        "port_area_sq_inch",
        # Generated column
        "single_port_gas_level",
        "charge_pump_disp_cc",
        "charge_pump_press_psi",
        "friction_min_delta_p",
        "friction_max_delta_p",
    ]

    form_columns = [
        "name",
        "description",
        "model_types_rel",
        "power_unit_type_rel",
        "diameter",
        "area",
        "stroke",
        "max_spm",
        "max_delta_p",
        "mawp",
        "rod_size",
        "motor_hp",
        "hyds",
        # # Two generated columns
        # "max_liquid_m3pd",
        # "min_liquid_m3pd_10pct",
        "hyd_size_inch",
        "single_loss_cu_inch",
        "port_area_sq_inch",
        # # Generated column
        # "single_port_gas_level",
        "charge_pump_disp_cc",
        "charge_pump_press_psi",
        "friction_min_delta_p",
        "friction_max_delta_p",
    ]
    column_editable_list = form_columns
    column_searchable_list = column_list
    column_filters = column_list

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        # Need this for testing since it's a foreign key
        "model_types_rel.unit_types_rel": QueryAjaxModelLoader(
            "model_types_rel.unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select a unit type",
            **options,
        ),
        "model_types_rel": QueryAjaxModelLoader(
            "model_types_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select a model",
            **options,
        ),
        "power_unit_type_rel": QueryAjaxModelLoader(
            "power_unit_type_rel",
            db.session,
            PowerUnitType,
            fields=["name"],
            order_by="name",
            placeholder="Please select a power unit type",
            **options,
        ),
    }

    def __repr__(self):
        return f"{self.name}"


class WebsiteViewView(MyModelView):
    """Flask-Admin view for WebsiteView model"""

    column_list = [
        "timestamp_utc",
        "page",
        "users_rel",
        "company",
        "city",
        "region",
        "timezone",
        "country_code",
        "country_name",
        "ip",
        "postal",
        "gps_lat",
        "gps_lon",
    ]
    column_searchable_list = [
        "timestamp_utc",
        "page",
        "users_rel.first_name",
        "company",
        "city",
        "region",
        "timezone",
        "country_code",
        "country_name",
        "ip",
        "postal",
        "gps_lat",
        "gps_lon",
    ]
    column_filters = column_list
    form_columns = column_list
    # column_editable_list = form_columns

    can_create = False
    can_edit = False
    # Disable record deletion?
    can_delete = True
    can_set_page_size = True
    page_size = 50

    column_labels = {
        "page": "Page",
        "users_rel": "User",
        "users_rel.first_name": "First Name",
        "company": "Company",
        "city": "City",
        "region": "Region",
        "timestamp_utc": "Timestamp UTC",
        "ip": "IP Address",
        "gps_lat": "Latitude",
        "gps_lon": "Longitude",
    }
    column_descriptions = {
        "page": "myijack.com webpage visited",
        "timestamp_utc": "Timestamp user visited the page, in UTC time",
    }

    # True = sort descending
    column_default_sort = [("timestamp_utc", True)]

    column_extra_row_actions = None

    column_formatters = {
        # Get first name and last name
        "users_rel": user_formatter,
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        # Need this for testing since it's a foreign key
        "users_rel": QueryAjaxModelLoaderAdminCust(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
    }

    def __repr__(self):
        return f"{self.name}"


class WebsiteMostActiveView(MyModelView):
    """Flask-Admin 'aggregated'/counted by page view for WebsiteView model"""

    def get_query(self, cols: list = None):
        """Return a query for the model type.

        This method can be used to set a "persistent filter" on an index_view.

        Example::

            class MyView(ModelView):
                def get_query(self):
                    return (
                        super(MyView, self)
                        .get_query()
                        .filter(User.username == current_user.username)
                    )

        If you override this method, don't forget to also override `get_count_query`, for displaying the correct
        item count in the list view, and `get_one`, which is used when retrieving records for the edit view.
        """

        # model = self.model
        # web = WebsiteView.__table__.alias("web1")
        # user = User.__table__.alias("user1")
        # cust = Customer.__table__.alias("cust1")

        # web = aliased(WebsiteView)
        # user = aliased(User)
        # cust = aliased(Customer)

        web = WebsiteView
        user = User
        cust = Customer

        # assert WebsiteView is self.model
        cols = cols or [
            func.row_number().over().label("id"),
            web.page,
            cust.customer,
            cust.id.label("customer_id"),
            user.first_name,
            user.last_name,
            user.email,
            user.phone,
            func.count("*").label("count_records"),
            func.min(web.timestamp_utc).label("timestamp_utc"),
        ]
        # subquery = db.session.query(Web).join(user).join(cust).subquery()
        query = (
            # select(*cols)
            db.session.query(*cols)
            # db.session.query(Web)
            .select_from(web)
            .join(user, web.user_id == user.id)
            .join(cust, user.customer_id == cust.id)
            .filter(
                web.user_id != None,  # noqa: E711
                "media" not in str(web.page),
                "protected" not in str(web.page),
                cust.id not in (CUSTOMER_ID_IJACK_INC, CUSTOMER_ID_DEMO),
            )
            .group_by(
                web.page,
                cust.customer,
                cust.id,
                user.first_name,
                user.last_name,
                user.email,
                user.phone,
            )
            # .load_only(cols)
            .order_by(web.page, func.count("*").desc(), cust.customer)
            # .subquery()
        )
        # query = db.session.query(query)
        # query = db.session.query().from_statement(select_query)
        # statement1 = query
        # statement2 = str(query)
        # statement3 = query.statement
        # statement4 = str(query.statement)
        # statement5 = query.statement.compile()
        # statement6 = str(query.statement.compile())
        current_app.logger.debug(f"\n\n{query}\n\n")
        # current_app.logger.debug(str(query))
        return query

    def get_count_query(self):
        """
        Return the count query for the model type

        A ``query(self.model).count()`` approach produces an excessive
        subquery, so ``query(func.count('*'))`` should be used instead.
        """
        # Web = WebsiteView
        # # assert WebsiteView is self.model
        # query = (
        #     select(func.count("*"))
        #     .join(User)
        #     .join(Customer)
        #     .where(
        #         Web.user_id != None,
        #         "media" not in str(Web.page),
        #         "protected" not in str(Web.page),
        #         Customer.id not in (CUSTOMER_ID_IJACK_INC, CUSTOMER_ID_DEMO),
        #     )
        #     .group_by(Web.page)
        #     .order_by(Web.page, "count_records")
        # )
        # return self.get_query(cols=[func.count("*")])
        # return query
        subquery = self.get_query().subquery()
        query = db.session.query(func.count("*")).select_from(subquery)
        current_app.logger.debug(f"\n\n{query}\n\n")
        return query
        # return self.get_query().query(func.count("*"))
        # return self.session.query(func.count("*"))

    # Web.user_id != None,
    # "media" not in str(Web.page),
    # "protected" not in str(Web.page),
    # Customer.id not in (CUSTOMER_ID_IJACK_INC, CUSTOMER_ID_DEMO),
    # column_filters = ["timestamp_utc", "page", "customers_rel", "users_rel"]
    column_filters = [
        DateBetweenFilter(column=WebsiteView.timestamp_utc, name="Dates"),
        "page",
        # The following user and customer fields create errors when joined...
        # "customers_rel",
        # "first_name",
        # "last_name",
        # "email",
        # Customer.customer,
    ]

    # Sorting must be done in the get_query() above
    # otherwise columns will not be in the group_by (weird error)
    column_sortable_list = []

    # Sorting must be done in the get_query() above
    # since "count_records" is not in the group_by (weird error)
    # column_default_sort = [
    #     ("page", False),
    #     # True = sort descending
    #     ("count_records", True),
    #     ("customers_rel", False),
    # ]

    column_list = [
        "timestamp_utc",
        "count_records",
        "page",
        "users_rel.customers_rel",
        # "users_rel",
        "users_rel.first_name",
        "users_rel.last_name",
        "users_rel.email",
        "users_rel.phone",
        # "company",
        # "city",
        # "region",
        # "timezone",
        # "country_code",
        # "country_name",
        # "ip",
        # "postal",
        # "gps_lat",
        # "gps_lon",
    ]
    # form_columns = column_list
    # column_editable_list = form_columns

    # # Searching this view results in an error!
    # column_searchable_list = [
    #     "page",
    #     "users_rel.customers_rel",
    #     "users_rel.first_name",
    #     "users_rel.last_name",
    #     "users_rel.email",
    #     "users_rel.phone",
    # ]

    can_create = False
    can_edit = False
    # Disable record deletion?
    can_delete = True
    can_set_page_size = True
    page_size = 50

    column_labels = {
        "count_records": "Views",
        "page": "Page",
        "users_rel": "User",
        "users_rel.customers_rel": "Customer",
        "users_rel.first_name": "First Name",
        "users_rel.last_name": "Last Name",
        "users_rel.email": "Email",
        "users_rel.phone": "Phone",
        "company": "Company",
        "city": "City",
        "region": "Region",
        "timestamp_utc": "Earliest Date in Sample",
        "ip": "IP Address",
        "gps_lat": "Latitude",
        "gps_lon": "Longitude",
    }
    column_descriptions = {
        "count_records": "Number of times user has viewed this page",
        "page": "myijack.com webpage visited",
        "timestamp_utc": "Earliest Date in Sample",
    }

    column_extra_row_actions = None

    column_formatters = {
        # Get first name and last name
        "users_rel": user_formatter,
        "users_rel.phone": user_phone_num_formatter,
        "users_rel.email": email_formatter,
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        # Need this for testing since it's a foreign key
        "users_rel": QueryAjaxModelLoaderAdmin(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
    }

    def __repr__(self):
        return f"{self.name}"


class WebsiteViewMostActiveView(MyModelView):
    """WebsiteView model aggregated by who uses RCOM the most"""

    can_create = False
    can_edit = False
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = [
        "page",
        "count_",
        "customer",
        "first_name",
        "last_name",
        "email",
        "phone",
        "earliest_date_in_sample",
    ]
    form_columns = None
    column_editable_list = None
    column_searchable_list = column_list
    column_filters = ["customer", "page"]
    # True = sort descending
    # column_default_sort = [("timestamp_utc", True)]
    column_extra_row_actions = None

    column_labels = {
        "page": "Page",
        "count_": "Visits",
        "customer": "Customer",
        "first_name": "First Name",
        "last_name": "Last Name",
        "email": "Email",
        "phone": "Phone",
        "earliest_date_in_sample": "Earliest Date in Sample",
    }
    column_descriptions = {
        "page": "Page visited on website",
        "count_": "Times user has visited this page",
        "earliest_date_in_sample": "First time the user visited this page",
    }

    column_formatters = {
        # Get first name and last name
        "users_rel": user_formatter,
        "email": email_formatter,
        "phone": user_phone_num_formatter,
        # # Change "/" to "/rcom/" since that's actually what it is, I think???
        # "page": lambda view, context, model, name: "/rcom/"
        # if model.page == "/"
        # else model.page,
    }

    def __repr__(self):
        return f"Page '{self.page}'. Count '{self.count_}'. Customer '{self.customer}'. Name '{self.first_name} {self.last_name}'"


class VwProfilerView(MyModelView):
    """Flask-Admin view for VwProfiler model"""

    can_create = False
    can_edit = False
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = (
        "full_name",
        "customer",
        "status_code",
        "timestamp_utc_started",
        "timestamp_utc_ended",
        "elapsed",
        "cpu_start",
        "cpu_end",
        "endpoint_name",
        "referrer",
        "method",
        "args",
        "kwargs",
        "query_string",
        "form",
        "ip",
        "files",
        "path",
        "request_args",
        "scheme",
        "user_agent",
        "body",
        "headers",
        "id",
    )

    column_sortable_list = column_list

    column_filters = column_list

    column_labels = {
        "full_name": "Full Name",
        "customer": "Customer",
        "status_code": "Status Code",
        "timestamp_utc_started": "Request Started (SK Time)",
        "timestamp_utc_ended": "Request Ended (SK Time)",
        "elapsed": "Seconds Elapsed",
        "cpu_start": "CPU Start",
        "cpu_end": "CPU End",
        "endpoint_name": "Endpoint Name",
        "referrer": "Previous URL",
        "method": "Method",
        "args": "Args",
        "kwargs": "Kwargs",
        "query_string": "Query String",
        "form": "Form",
        "ip": "IP",
        "files": "Files",
        "path": "Path",
        "request_args": "Request Args",
        "scheme": "Scheme",
        "user_agent": "User Agent",
        "body": "Body",
        "headers": "Headers",
    }

    column_descriptions = {
        "full_name": "Full name of the user",
        # "customer": "Customer",
        "status_code": "HTTP status code",
        "timestamp_utc_started": "Timestamp the request started, in UTC time",
        "timestamp_utc_ended": "Timestamp the request ended, in UTC time",
        "elapsed": "Time elapsed in seconds",
        "cpu_start": "CPU usage (%) at the start of the request",
        "cpu_end": "CPU usage (%) at the end of the request",
        "endpoint_name": "New URL endpoint requested",
        "referrer": "Previous URL from where the request was made",
        "method": "HTTP method",
        "args": "Arguments passed to the endpoint",
        "kwargs": "Keyword arguments passed to the endpoint",
        "query_string": "Query string in the URL",
        "form": "Form data in the request",
        "ip": "IP address of the user",
        "files": "Files uploaded in the request",
        "path": "Path of the request URL",
        "request_args": "Arguments in the request",
        "scheme": "HTTP or HTTPS",
        "user_agent": "User agent string of the browser",
        "body": "Body of the request (e.g. JSON)",
        "headers": "Headers of the request (e.g. content type)",
    }

    column_formatters = {
        "timestamp_utc_started": datetime_formatter_sk_time,
        "timestamp_utc_ended": datetime_formatter_sk_time,
    }


# For the WorkOrderIncView
not_editable = [
    "id",
    "timestamp_utc_inserted",
    "work_order_parts_rel",
    "subtotal",
    # "sales_tax_rate",
    "sales_tax",
    "total",
    "users_rel",
    # "creator_rel",
    "unit_types_rel",
    "structures_rel",
    "power_units_rel",
    "model_types_rel",
]

# The following are also not "editable"
form_excluded_columns = [
    "id",  # auto
    "timestamp_utc_inserted",  # auto
    "subtotal",  # auto
    "sales_tax",  # auto
    "total",  # auto
    "total_hours",  # auto
    "creator_rel",  # auto
    # "approved_by_rel",  # always Richie
    "date_due",  # same as service date, for QuickBooks
    "terms",  # always same, for QuickBooks
    "is_paid",  # do this later, manually
    "notes",  # add this later, if necessary
    "sales_tax_rate",  # comes from province
    "unit_types_rel",  # TODO
    # "status",
    # "invoice_approval_req",  # TODO
    "safe_work_permit_num",  # TODO
]


def download_uploaded_wo_file_link(view, context, model, name):
    """Custom formatter for work_order_upload_files_rel.file_name"""
    # This link needs to download from the IJACK PostgreSQL database
    return Markup(
        "<br>".join(
            f'{index + 1} - <a href="/download-work-order-upload-file/{model.id}">{model.file_name}</a>'
            for index, model in enumerate(model.work_order_upload_files_rel)
        )
    )


def download_uploaded_career_file_link(view, context, model, name):
    """Custom formatter for career_files_rel"""
    # This link needs to download from the IJACK PostgreSQL database
    return Markup(
        "<br>".join(
            f'{index + 1} - <a href="/download-career-file/{model2.career_file_id}">{model2}</a>'
            for index, model2 in enumerate(model.career_files_rel)
        )
    )


# def format_invoice_approval_req(v, c, m, n):
#     """Show the name (approved_by_rel) of the person who approved the invoice"""
#     if m.invoice_approval_req:
#         return f"{m.approved_by_rel.first_name} {m.approved_by_rel.last_name}"
#     return "Not approved"


column_formatters_wo = {
    # Get first name and last name
    "id": format_like_integer,
    "quickbooks_num": format_like_integer,
    "users_rel": user_formatter,
    "users_sales_rel": user_formatter,
    "creator_rel": user_formatter,
    # Show the name (approved_by_rel) of the person who approved the invoice
    # NOTE: this formatter gets overridden by the x-editable-boolean JS formatter
    # "invoice_approval_req": format_invoice_approval_req,
    "approved_by_rel": user_formatter,
    "structures_rel": remove_unnecessary_decimal,
    # "structure_slave": remove_unnecessary_decimal,
    "power_units_rel": remove_unnecessary_decimal,
    "timestamp_utc_inserted": datetime_formatter_sk_time,
    "subtotal": money_formatter,
    "sales_tax": money_formatter,
    "total": money_formatter,
    "work_done": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
    "service_required": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
    # "work_done": col_width_400_formatter,
    "invoice_summary": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
    "notes": preserve_line_breaks,
    "work_order_upload_files_rel.file_name": download_uploaded_wo_file_link,
}

column_formatters_wo_export = deepcopy(column_formatters_wo)
column_formatters_wo_export.pop("work_done", None)
column_formatters_wo_export.pop("notes", None)
column_formatters_wo_export.pop("invoice_summary", None)
column_formatters_wo_export.pop("work_order_upload_files_rel.file_name", None)


class WorkOrderIncView(MyModelView):
    """Flask-Admin view for WorkOrder model, for IJACK Inc"""

    @expose("/")
    def index_view(self):
        """
        Override the main list view for the work orders to exclude certain columns
        if the user does not have the ROLE_ID_VIEW_WORK_ORDER_TOTALS role
        """

        if not current_user.has_role_id(ROLE_ID_VIEW_WORK_ORDER_TOTALS):
            cols_to_exclude: list = [
                "subtotal",
                "sales_tax",
                "total",
            ]
            self._list_columns = [
                (col, name)
                for (col, name) in self._list_columns
                if col not in cols_to_exclude
            ]
            self._sortable_columns = [
                col for col in self._sortable_columns if col not in cols_to_exclude
            ]
            self.column_editable_list = [
                col for col in self.column_editable_list if col not in cols_to_exclude
            ]
            self.column_filters = [
                col for col in self.column_filters if col not in cols_to_exclude
            ]

        return super().index_view()

    @expose("/new/", methods=("GET", "POST"))
    def create_view(self):
        """Custom Dash-based create model view"""
        return_url = get_redirect_target() or self.get_url(".index_view")

        if not self.can_create:
            return redirect(return_url)

        return redirect(url_for("dash.work_order"))

    @expose("/edit/", methods=("GET", "POST"))
    def edit_view(self):
        """Custom Dash-based edit model view"""

        return_url = get_redirect_target() or self.get_url(".index_view")

        if not self.can_edit:
            return redirect(return_url)

        id = get_mdict_item_or_list(request.args, "id")
        if id is None:
            return redirect(return_url)

        return redirect(url_for("dash.work_order", work_order_id=id))

    def get_query(self):
        """Only show Canadian 'IJACK Inc' work orders"""
        return self.session.query(self.model).filter(
            # Which company's revenues are affected by this work order?
            # Note it has nothing to do with the currency.
            self.model.creator_company_id == CUSTOMER_ID_IJACK_INC,
            # It's not a quote
            self.model.is_quote.is_(False),
        )

    def get_count_query(self):
        """Return a the count query for the model type"""
        return (
            self.session.query(func.count("*"))
            .select_from(self.model)
            .filter(
                # Which company's revenues are affected by this work order?
                # Note it has nothing to do with the currency.
                self.model.creator_company_id == CUSTOMER_ID_IJACK_INC,
                # It's not a quote
                self.model.is_quote.is_(False),
            )
        )

    # @expose("/details/")
    # def details_view(self):
    #     """Custom Dash-based details model view"""
    #     return_url = get_redirect_target() or self.get_url(".index_view")

    #     if not self.can_view_details:
    #         return redirect(return_url)

    #     id = get_mdict_item_or_list(request.args, "id")
    #     if id is None:
    #         return redirect(return_url)

    #     return redirect(url_for("dash.work_order_details", work_order_id=id))

    can_create = True
    can_edit = True
    # Delete the work order from the "edit" form, not the list view,
    # since the list view struggles to return parts to inventory
    can_delete = False
    can_set_page_size = True
    can_view_details = True  # use edit instead?
    page_size = 25
    # Whether to display the primary key in the list view
    column_display_pk = True

    can_export = True
    export_max_rows = 0
    # # CSV is default, depends on Tablib package
    # export_types = ["xls"]
    # 0 is unlimited

    # For my custom index view function, to start on the page paginated page.
    # We use quickbooks_num/date_service sorting instead
    start_on_last_page = False

    # Duplicate work order button!
    # column_extra_row_actions = None

    # Custom templates
    create_modal = False
    edit_modal = False

    # create_template = "admin/custom/rule_create.html"
    # edit_template = "admin/custom/rule_edit.html"
    # details_template = "admin/model/details2.html"
    # details_modal_template = "admin/model/details2.html"

    column_list = [
        "id",
        "creator_rel",
        # IJACK field techs
        "users_rel",
        # IJACK sales credit goes to
        "users_sales_rel",
        "creator_company_rel",
        "timestamp_utc_inserted",
        "date_service",
        "invoice_approval_req",
        # "date_sent_for_approval",
        "approved_by_rel",
        "is_quote",
        "quickbooks_num",  # QuickBooks invoice number (Michelle)
        "status_rel",
        "is_warranty",
        "service_type_rel",
        "customers_rel.customer",
        "currency_rel.name",
        "location",
        "county_rel.name",
        "model_types_rel",
        "structures_rel",
        "power_units_rel",
        "total",
        "service_required",
        "work_done",
        "approval_person_rel",
        # "customer_po",
        # "service_crew",
        # "cust_work_order",
        # "afe",
        # "service_hours",
        # "travel_time_hours",
        # "invoice_summary",
        # "unit_types_rel",
        # "structure_slave",
        # "work_order_parts_rel",
        # "subtotal",
        # "province_rel",
        # "sales_tax_rate",
        # "sales_tax",
        # "currency_rel",
        # Uploaded files
        "work_order_upload_files_rel.file_name",
    ]

    column_sortable_list = [
        "timestamp_utc_inserted",
        "date_service",
        "quickbooks_num",  # QuickBooks invoice number (Michelle)
        "invoice_approval_req",
        # "approved_by_rel",
        "is_warranty",
        "is_quote",
        "location",
        "county_rel.name",
        # This is how we sort on relationship field
        "creator_rel.first_name",
        # "status_rel.name",
        # "creator_company_rel.customer",
        "customers_rel.customer",
        "currency_rel.name",
        "service_type_rel.name",
        # Can't sort on structures_rel since it's many-to-many like the power units
        # "structures_rel.structure",
        # "model_types_rel.model",
        # "power_units_rel.power_unit",
        "total",
        "service_required",
        "work_done",
        "work_order_upload_files_rel.file_name",
        "id",
    ]

    # True = sort descending
    column_default_sort = [
        ("date_service", True),
        # Invoice uploaded to (status)...
        # This is how we sort on relationship field
        (WorkOrderStatus.name, True),
        # Invoice number
        ("quickbooks_num", True),
        ("timestamp_utc_inserted", True),
    ]

    column_formatters = column_formatters_wo
    column_formatters_export = column_formatters_wo_export
    column_formatters_detail = column_formatters_wo_export

    column_filters = [
        "id",
        "is_paid",
        "is_quote",
        # Olga needs to filter on status and "is_paid"
        # "status.name",
        WorkOrderStatus.name,
        "date_service",
        "timestamp_utc_inserted",
        "quickbooks_num",
        ServiceType.name,
        # "inventory_source",
        # Creator is one of two User relationships on the WorkOrder table, so must use "creator_rel.first_name" format
        # ("creator_rel", User.first_name),
        "creator_rel.first_name",
        "creator_company_rel.customer",
        "customers_rel.customer",
        "currency_rel.name",
        # users make too long of a list
        "users_rel.first_name",
        "users_rel.last_name",
        "users_rel.email",
        "users_sales_rel.first_name",
        "users_sales_rel.last_name",
        "users_sales_rel.email",
        "is_tax_exempt",
        Province.name,
        "county_rel.name",
        "model_types_rel.model",
        "power_units_rel.power_unit_str",
        "work_order_parts_rel.part_num",
        "work_order_parts_rel",
        "work_order_upload_files_rel.file_name",
    ]

    column_export_exclude_list = None
    # column_export_list = [
    #     "id",  # unique work order number for importing from CSV
    #     "quickbooks_num", # Unique QuickBooks-generated number
    #     "customers_rel.customer",
    #     "creator_company_rel.customer",
    #     "date_service",  # InvoiceDate
    #     "date_due",  # DueDate
    #     "terms",  # Terms
    #     "location",
    #     "invoice_summary",  # Memo
    #     "structures_rel.structure",  # Item(Product/Service)
    #     "currency_rel",
    #     "sales_tax_rate",
    # ]

    column_searchable_list = column_list

    form_columns = [
        "date_service",
        "customers_rel",
        "currency_rel",
        "creator_company_rel",
        "location",
        # "service_type_rel"
        "service_type_rel",
        # "date_sent_for_approval",
        # Do this from Dash form, not here
        # "invoice_approval_req",
        # Must update this in work order create/edit form in Dash, since when this changes, inventories need to be updated
        # "is_quote",
        "status_rel",
        "quickbooks_num",
        "users_rel",
        "users_sales_rel",
        "service_crew",
        "requested_by_rel",
        "approval_person_rel",
        "cust_work_order",
        "afe",
        "customer_po",
        "model_types_rel",
        "power_units_rel",
        "structures_rel",
        "structure_slave",
        # "service_hours",
        # "travel_time_hours",
        "is_warranty",
        "is_warranty_reason",
        "service_required",
        "work_done",
        "invoice_summary",
        "work_order_parts_rel",
        "province_rel",
        "currency_rel",
        "county_rel",
        "work_order_upload_files_rel",
    ]
    form_excluded_columns = form_excluded_columns

    column_details_list = form_columns

    column_editable_list = (
        "date_service",
        # Do this from Dash form, not here
        # "invoice_approval_req",
        "quickbooks_num",
        "status_rel",
        "is_warranty",
        # Must update this in work order create/edit form in Dash, since when this changes, inventories need to be updated
        # "is_quote",
        "service_type_rel",
        "creator_company_rel",
        "customers_rel",
        "location",
        "service_required",
        "work_done",
    )

    column_labels = {
        "id": "Work Order ID",
        "quickbooks_num": "QuickBooks Invoice Number",
        "notes": "Olga's Notes",
        "timestamp_utc_inserted": "Date Inserted (SK Time)",
        "service_type_rel": "Service Type",
        # "work_order_num": "Work Order Num",
        # "charged_to": "Charged To",
        "service_crew": "Service Crew",
        "approval_person_rel": "Company Approval Person",
        # "date_sent_for_approval": "Date Sent for Approval",
        "invoice_approval_req": "Approved for Invoice Creation?",
        "approved_by_rel": "Approval Person",
        "is_paid": "Invoice Paid",
        "is_quote": "Is Quote",
        "status_rel": "Status of Invoice",
        "location": "Location",
        # "has_rcom": "RCOM",
        # "motor": "Motor",
        "model_types_rel": "Equipment Serviced (Models)",
        "model_types_rel.model": "Equipment Serviced (Models)",
        "service_required": "Service Required",
        "date_service": "Service Date",
        # "service_hours": "Service Hours",
        # "travel_time_hours": "Travel Time (Hours)",
        # "inventory_source": "Inventory Source",
        "is_warranty": "Warranty",
        "is_warranty_reason": "Warranty Reason",
        "is_tax_exempt": "Tax Exempt",
        "work_done": "Description of Work Performed",
        "customer_po": "Customer PO",
        "cust_work_order": "Customer Work Order",
        "afe": "AFE",
        "invoice_summary": "Invoice Summary",
        "currency_rel.name": "Currency",
        "currency_rel": "Currency",
        "customers_rel": "Customer",
        "customers_rel.customer": "Customer",
        "creator_company_rel": "Creator Company",
        "creator_company_rel.customer": "Creator Company",
        "creator_rel": "Work Order Creator",
        "creator_rel.first_name": "Work Order Creator First Name",
        "users_rel": "IJACK Field Technicians",
        "users_rel.first_name": "IJACK Field Technicians - First Name",
        "users_rel.last_name": "IJACK Field Technicians - Last Name",
        "users_rel.email": "IJACK Field Technicians - Email",
        "users_sales_rel": "IJACK Sales Credit",
        "users_sales_rel.first_name": "IJACK Sales Credit - First Name",
        "users_sales_rel.last_name": "IJACK Sales Credit - Last Name",
        "users_sales_rel.email": "IJACK Sales Credit - Email",
        "structures_rel": "Structures",
        "structures_rel.structure": "Structures",
        "structure_slave": "Structure Slaves",
        "power_units_rel": "Power Units",
        "power_units_rel.power_unit_str": "Power Units",
        "province_rel": "Province",
        "sales_tax_rate": "Sales Tax Rate",
        "work_order_parts_rel": "Parts in Work Order",
        "subtotal": "Subtotal",
        "sales_tax": "Sales Tax",
        "total": "Total",
        "county_rel": "County",
        "county_rel.name": "County",
        "work_order_parts_rel.part_num": "Part Number",
        "work_order_upload_files_rel.file_name": "Uploaded Files",
        "work_order_upload_files_rel": "Uploaded Files",
    }
    column_descriptions = {
        "id": "Work order ID in the database (primary key, auto-generated, not changeable)",
        "quickbooks_num": "QuickBooks invoice number (Michelle to add after creating invoice in QuickBooks)",
        "notes": "Olga's Notes",
        "is_paid": "Is the invoice paid?",
        "is_quote": "Is this just a quote, or a real invoice?",
        "status_rel": "To which site has this invoice been uploaded?",
        "timestamp_utc_inserted": "Date-time (SK time) at which this work order was created",
        "date_service": "Date of service (required)",
        "service_type_rel": "Type of service (required)",
        # "inventory_source": "Inventory source (required)",
        "currency_rel.name": "Currency of invoice (required)",
        "currency_rel": "Currency of invoice (required)",
        "customers_rel": "Customer (required)",
        "customers_rel.customer": "Customer (required)",
        "creator_company_rel": "IJACK company that created the invoice: Canada (Inc) or USA (Corp)",
        "creator_company_rel.customer": "IJACK company that created the invoice: Canada (Inc) or USA (Corp)",
        "users_rel": "IJACK field technicians (required)",
        "users_rel.first_name": "IJACK field technician first name",
        "users_rel.last_name": "IJACK field technician last name",
        "users_rel.email": "IJACK field technician email",
        "users_sales_rel": "IJACK sales credit goes to these people",
        "users_sales_rel.first_name": "IJACK sales credit first name",
        "users_sales_rel.last_name": "IJACK sales credit last name",
        "users_sales_rel.email": "IJACK sales credit email",
        # "work_order_num": "Number",
        # "charged_to": "To whom was it charged?",
        "service_crew": "Service crew",
        "approval_person_rel": "Company approval person who is responsible for approving this quote",
        # "date_sent_for_approval": "Date the invoice was sent to customer for approval",
        "invoice_approval_req": "Did the customer approve the invoice?",
        "approved_by_rel": "If not yet approved, who is responsible for approving this invoice? If approved already, who approved it?",
        "location": "Location of service",
        # "has_rcom": "Does the unit have RCOM?",
        # "motor": "Does the unit have a motor?",
        "model_types_rel": "IJACK product models",
        "model_types_rel.model": "IJACK product models",
        "service_required": "What kind of service is required?",
        "work_done": "Description of work performed (IJACK internal notes)",
        # "service_hours": "Hours taken to perform service",
        # "travel_time_hours": "Hours of travel required for service",
        "is_warranty": "Was this job on warranty?",
        "is_warranty_reason": "Warranty reason",
        "is_tax_exempt": "Is this invoice tax exempt?",
        "customer_po": "Customer PO",
        "cust_work_order": "Customer work order",
        "afe": "Authorization for expenditure (AFE)",
        "invoice_summary": "Invoice summary",
        "creator_rel": "Who filled in and submitted/created the work order form?",
        "structures_rel": "Structures involved",
        "structures_rel.structure": "Structures involved",
        "structure_slave": "Structure slaves involved",
        "power_units_rel": "Power units involved",
        "work_order_parts_rel": "Parts (line items) included in work order",
        "subtotal": "Subtotal of line items (parts)",
        # "sales_tax_rel": "Province for determining sales tax rate",
        "province_rel": "Province or state",
        "sales_tax_rate": r"Sales tax rate [currently this is province PLUS federal (e.g. Alberta 5% +5% = 10%)]",
        "sales_tax": "Sales tax amount",
        "total": "Total amount (subtotal + sales tax + other - deposit)",
        "work_order_upload_files_rel.file_name": "Uploaded Files",
        "work_order_upload_files_rel": "Uploaded Files",
    }

    # Just for the edit form
    # form_widget_args = {
    #     # description of work performed
    #     "work_done": {"style": "width: 400px"},
    # }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "customers_rel": QueryAjaxModelLoader(
            "customers_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select a customer",
            **options,
        ),
        "creator_company_rel": QueryAjaxModelLoaderAdminCust(
            "creator_company_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select a company",
            # Only two IJACK options from which to choose
            get_query_func=lambda: Customer.query.filter(
                Customer.id.in_((CUSTOMER_ID_IJACK_INC, CUSTOMER_ID_IJACK_CORP))
            ),
            **options,
        ),
        # We only want to see IJACK employees in this list
        # IJACK field technicians
        "users_rel": QueryAjaxModelLoaderAdminCust(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select an IJACK field technician",
            **options,
        ),
        # IJACK sales credit goes to
        "users_sales_rel": QueryAjaxModelLoaderAdminCust(
            "users_sales_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select an IJACK sales credit person",
            **options,
        ),
        "creator_rel": QueryAjaxModelLoaderAdminCust(
            "creator_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Who created this work order?",
            **options,
        ),
        "approved_by_rel": QueryAjaxModelLoaderAdminCust(
            "approved_by_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Who needs to approve this invoice?",
            **options,
        ),
        "structures_rel": QueryAjaxModelLoaderAdmin(
            "structures_rel",
            db.session,
            Structure,
            fields=["structure_str", "surface", "downhole", PowerUnit.power_unit_str],
            order_by="structure",
            placeholder="Please select structures",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        # "structure_slave": QueryAjaxModelLoader(
        #     "structure_slave",
        #     db.session,
        #     Structure,
        #     fields=[Structure.structure],
        #     order_by="structure",
        #     placeholder="Please select a structure slave",
        #     **options,
        # ),
        "power_units_rel": QueryAjaxModelLoader(
            "power_units_rel",
            db.session,
            PowerUnit,
            fields=[PowerUnit.power_unit],
            order_by="power_unit",
            placeholder="Please select a power unit",
            **options,
        ),
        # "work_order_part_rel.parts_rel": QueryAjaxModelLoader(
        #     name="work_order_part_rel.parts_rel",
        #     session=db.session,
        #     model=Part,
        #     fields=[Part.part_num, "description"],
        #     order_by="part_num",
        #     placeholder="Please select a part",
        #     **options,
        # ),
        "unit_types_rel": QueryAjaxModelLoader(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select a unit type",
            **options,
        ),
        "currency_rel": QueryAjaxModelLoader(
            "currency_rel",
            db.session,
            Currency,
            fields=["name"],
            order_by="name",
            placeholder="Please select a currency",
            **options,
        ),
        "service_type_rel": QueryAjaxModelLoader(
            "service_type_rel",
            db.session,
            ServiceType,
            fields=["name"],
            order_by="name",
            placeholder="Please select a service type",
            **options,
        ),
        "status_rel": QueryAjaxModelLoader(
            "status_rel",
            db.session,
            WorkOrderStatus,
            fields=["name"],
            order_by="name",
            placeholder="Please select a work order status",
            **options,
        ),
        "province_rel": QueryAjaxModelLoader(
            "province_rel",
            db.session,
            Province,
            fields=["name"],
            order_by="name",
            placeholder="Please select a province or state",
            **options,
        ),
        "county_rel": QueryAjaxModelLoader(
            "county_rel",
            db.session,
            County,
            fields=["name"],
            order_by="name",
            placeholder="Please select a county",
            **options,
        ),
    }

    # Maybe we'll add inline part line items in the future, in Flask Admin. Right now Dash/RCOM handles the form...
    inline_models = (WorkOrderPartInlineForm(),)
    inline_model_form_converter = WorkOrderInlineModelConverter

    def on_model_change(self, form, model, is_created):
        """When someone changes a record, recalculate the totals"""
        if is_created:
            model.creator_id = getattr(current_user, "id", None)
            # model.creator_rel = current_user
            if not model.approved_by_id:
                model.approved_by_id = USER_ID_RICHIE

        # Record who changed the approval status, if it was changed
        if form.data.get("invoice_approval_req", None) is not None:
            model.approved_by_id = getattr(current_user, "id", None)

        # # There has to be at least one IJACK field tech in the list
        # if not model.users_rel:
        #     flash("Please select at least one IJACK field technician", "danger")
        #     raise ValidationError("Please select at least one IJACK field technician")

        # # There has to be at least one IJACK sales credit person in the list
        # if not model.users_sales_rel:
        #     flash("Please select at least one IJACK sales credit person", "danger")

        model.update_subtotal()

    def render(self, template, **kwargs):
        """Add some extra JavaScript"""
        current_app.config.get("VERSION_MYIJACK", None)
        # Use the name of the vite bundle here
        self.extra_js = ["admin/flask_admin_work_orders_list_view.js"]
        return super().render(template, **kwargs)


class WorkOrderCorpView(WorkOrderIncView):
    """Work order view for US IJACK Corp employees"""

    @expose("/new/", methods=("GET", "POST"))
    def create_view(self):
        """Custom Dash-based create model view"""
        return_url = get_redirect_target() or self.get_url(".index_view")

        if not self.can_create:
            return redirect(return_url)

        return redirect(url_for("dash.work_order_corp"))

    @expose("/edit/", methods=("GET", "POST"))
    def edit_view(self):
        """Custom Dash-based edit model view"""

        return_url = get_redirect_target() or self.get_url(".index_view")

        if not self.can_edit:
            return redirect(return_url)

        id = get_mdict_item_or_list(request.args, "id")
        if id is None:
            return redirect(return_url)

        return redirect(url_for("dash.work_order_corp", work_order_id=id))

    def get_query(self):
        """Only show US 'IJACK Corp' work orders"""
        return self.session.query(self.model).filter(
            # Which company's revenues are affected by this work order?
            # Note it has nothing to do with the currency.
            self.model.creator_company_id == CUSTOMER_ID_IJACK_CORP,
            # Is not a quote
            self.model.is_quote.is_(False),
        )

    def get_count_query(self):
        """Return a the count query for the model type"""
        return (
            self.session.query(func.count("*"))
            .select_from(self.model)
            .filter(
                # Which company's revenues are affected by this work order?
                # Note it has nothing to do with the currency.
                self.model.creator_company_id == CUSTOMER_ID_IJACK_CORP,
                # Is not a quote
                self.model.is_quote.is_(False),
            )
        )


class WorkOrderIncQuoteView(WorkOrderIncView):
    """Work order view for Canadian quotes"""

    def get_query(self):
        """Only show Canadian 'IJACK Inc' work orders that are quotes"""
        return self.session.query(self.model).filter(
            # Which company's revenues are affected by this work order?
            # Note it has nothing to do with the currency.
            self.model.creator_company_id == CUSTOMER_ID_IJACK_INC,
            # It's a quote, not a real work order
            self.model.is_quote.is_(True),
        )

    def get_count_query(self):
        """Return a the count query for the model type"""
        return (
            self.session.query(func.count("*"))
            .select_from(self.model)
            .filter(
                # Which company's revenues are affected by this work order?
                # Note it has nothing to do with the currency.
                self.model.creator_company_id == CUSTOMER_ID_IJACK_INC,
                # It's a quote, not a real work order
                self.model.is_quote.is_(True),
            )
        )


class WorkOrderCorpQuoteView(WorkOrderCorpView):
    """Work order view for US quotes"""

    def get_query(self):
        """Only show US 'IJACK Corp' work orders that are quotes"""
        return self.session.query(self.model).filter(
            # Which company's revenues are affected by this work order?
            # Note it has nothing to do with the currency.
            self.model.creator_company_id == CUSTOMER_ID_IJACK_CORP,
            # It's a quote, not a real work order
            self.model.is_quote.is_(True),
        )

    def get_count_query(self):
        """Return a the count query for the model type"""
        return (
            self.session.query(func.count("*"))
            .select_from(self.model)
            .filter(
                # Which company's revenues are affected by this work order?
                # Note it has nothing to do with the currency.
                self.model.creator_company_id == CUSTOMER_ID_IJACK_CORP,
                # It's a quote, not a real work order
                self.model.is_quote.is_(True),
            )
        )


def work_order_id_link(view, context, model, name) -> Markup:
    """Get a link to view the work order"""
    work_order_id = getattr(model, "work_order_id", None)
    url = url_for("work_orders.edit_view", id=work_order_id)
    markupstring = f"<a href='{url}'>{work_order_id}</a>"
    return Markup(markupstring)


column_formatters_wo_by_unit = {
    # "work_order_id": lambda v, c, m, n: Markup(f"<a href='{}'>{}</a>"),
    "work_order_id": work_order_id_link,
    "structure": remove_unnecessary_decimal,
    # "structure_slave": remove_unnecessary_decimal,
    "power_units_rel": remove_unnecessary_decimal,
    "service_required": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
    "work_done": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
}
column_formatters_wo_by_unit_export = deepcopy(column_formatters_wo_by_unit)
column_formatters_wo_by_unit_export.pop("work_order_id", None)
column_formatters_wo_by_unit_export.pop("service_required", None)
column_formatters_wo_by_unit_export.pop("work_done", None)


class WorkOrdersByUnitView(MyModelView):
    """View for seeing the work orders grouped by unit (i.e. power unit or structure)"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = True
    # Duplicate button - make None to disable
    column_extra_row_actions = None

    column_list = (
        "work_order_id",
        "date_service",
        "customer",
        "model",
        "unit_type",
        "power_unit",
        "power_unit_type",
        "structure",
        "downhole",
        "surface",
        "service_required",
        "work_done",
        "is_warranty",
        "is_warranty_reason",
    )

    column_formatters = column_formatters_wo_by_unit
    column_formatters_export = column_formatters_wo_by_unit_export
    column_formatters_detail = column_formatters_wo_by_unit_export

    column_searchable_list = (
        "work_order_id",
        "customer",
        "model",
        "unit_type",
        "power_unit_str",
        "power_unit_type",
        "structure",
        "downhole",
        "surface",
        "service_required",
        "work_done",
        "is_warranty_reason",
    )
    column_filters = (
        "work_order_id",
        "date_service",
        "customer",
        "model",
        "unit_type",
        "power_unit_str",
        "power_unit_type",
        "structure",
        "is_warranty",
    )

    column_labels = {
        "work_order_id": "Work Order ID",
        "date_service": "Service Date",
        "customer": "Customer",
        "model": "Model",
        "unit_type": "Unit Type",
        "power_unit": "Power Unit",
        "power_unit_str": "Power Unit",
        "power_unit_type": "Power Unit Type",
        "structure": "Structure",
        "service_required": "Service Required",
        "work_done": "Work Done",
        "is_warranty": "Warranty?",
        "is_warranty_reason": "Warranty Reason",
        "downhole": "Downhole",
        "surface": "Surface",
    }
    column_descriptions = {
        "work_order_id": "Work order ID in the database (primary key, auto-generated, not changeable)",
        "service_required": "What kind of service is required?",
        "work_done": "Description of work performed (IJACK internal notes)",
    }


class VwHoursBilledByFieldTechByWorkOrderView(MyModelView):
    """View for seeing the hours billed by field tech by work order"""

    can_create = False
    can_edit = False
    can_delete = False

    column_display_pk = True
    column_extra_row_actions = None

    column_list = (
        "work_order_id",
        "service_year",
        "service_month",
        "is_warranty",
        "name_",
        "date_service",
        "customer",
        "monthly_hours_worked_clock",
        "quantity_hours_billed",
    )

    column_searchable_list = (
        "name_",
        "customer",
    )

    column_filters = (
        "name_",
        "customer",
        "service_year",
        "service_month",
        "is_warranty",
        "date_service",
        "monthly_hours_worked_clock",
        "quantity_hours_billed",
        "work_order_id",
        "work_order_id",
    )

    column_labels = {
        "work_order_id": "Work Order ID",
        "service_year": "Service Year",
        "service_month": "Service Month",
        "is_warranty": "Warranty?",
        "name_": "Field Technician",
        "date_service": "Service Date",
        "customer": "Customer",
        "monthly_hours_worked_clock": "Monthly Hours Clocked",
        "quantity_hours_billed": "Quantity Hours Billed",
    }

    column_descriptions = {
        "work_order_id": "Work order ID in the database",
        "service_year": "Year of service",
        "service_month": "Month of service",
        "is_warranty": "Was this job on warranty?",
        "name_": "Field technician's name",
        "date_service": "Date of service",
        "customer": "Customer",
        "monthly_hours_worked_clock": "Monthly hours clocked by this field technician",
        "quantity_hours_billed": "Monthly hours billed in work orders for this field technician",
    }

    column_formatters = {
        "service_year": remove_unnecessary_decimal,
        "service_month": remove_unnecessary_decimal,
        "monthly_hours_worked_clock": decimal_formatter(1),
        "quantity_hours_billed": decimal_formatter(1),
        "work_order_id": work_order_id_link,
    }


class HoursBilledByFieldTechMonthlyEfficiencyView(MyModelView):
    """View for seeing the hours billed by field tech monthly efficiency"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = False
    column_extra_row_actions = None

    column_list = (
        "service_year",
        "service_month",
        "full_name",
        "billed_per_hour_worked",
        "quantity_hours_billed",
        "monthly_hours_worked_clock",
    )

    column_searchable_list = ("full_name",)

    column_filters = (
        "full_name",
        "service_year",
        "service_month",
        "billed_per_hour_worked",
        "quantity_hours_billed",
        "monthly_hours_worked_clock",
    )

    column_labels = {
        "service_year": "Service Year",
        "service_month": "Service Month",
        "full_name": "Field Technician",
        "billed_per_hour_worked": "Utilization Rate",
        "quantity_hours_billed": "Quantity Hours Billed",
        "monthly_hours_worked_clock": "Monthly Hours Clocked",
    }

    column_descriptions = {
        "service_year": "Year of service",
        "service_month": "Month of service",
        "full_name": "Field technician's name",
        "billed_per_hour_worked": "Billed hours per hour clocked in the service clock",
        "quantity_hours_billed": "Monthly hours billed in work orders for this field technician",
        "monthly_hours_worked_clock": "Monthly hours clocked by this field technician",
    }

    column_formatters = {
        "service_year": remove_unnecessary_decimal,
        "service_month": remove_unnecessary_decimal,
        "billed_per_hour_worked": decimal_formatter(2),
        "quantity_hours_billed": decimal_formatter(1),
        "monthly_hours_worked_clock": decimal_formatter(1),
    }


class HoursBilledMonthlyEfficiencyView(MyModelView):
    """View for seeing the hours billed monthly efficiency, for the whole team"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = False
    column_extra_row_actions = None

    column_list = (
        "service_year",
        "service_month",
        "billed_per_hour_worked",
        "quantity_hours_billed",
        "monthly_hours_worked_clock",
    )

    column_searchable_list = ("service_year",)

    column_filters = (
        "service_year",
        "service_month",
        "billed_per_hour_worked",
        "quantity_hours_billed",
        "monthly_hours_worked_clock",
    )

    column_labels = {
        "service_year": "Service Year",
        "service_month": "Service Month",
        "billed_per_hour_worked": "Utilization Rate",
        "quantity_hours_billed": "Quantity Hours Billed",
        "monthly_hours_worked_clock": "Monthly Hours Clocked",
    }

    column_descriptions = {
        "service_year": "Year of service",
        "service_month": "Month of service",
        "billed_per_hour_worked": "Billed hours per hour clocked in the service clock",
        "quantity_hours_billed": "Monthly hours billed in work orders for this field technician",
        "monthly_hours_worked_clock": "Monthly hours clocked by this field technician",
    }

    column_formatters = {
        "service_year": remove_unnecessary_decimal,
        "service_month": remove_unnecessary_decimal,
        "billed_per_hour_worked": decimal_formatter(2),
        "quantity_hours_billed": decimal_formatter(1),
        "monthly_hours_worked_clock": decimal_formatter(1),
    }


class WorkOrderPartView(MyModelView):
    """Flask-Admin view for WorkOrderPart model (public.work_orders_parts table)"""

    can_create = False
    can_edit = False
    can_delete = False
    # for Michelle, for importing into QuickBooks!
    can_export = True
    column_display_pk = True
    can_set_page_size = True
    page_size = 20
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        page_size = 5

    # Duplicate work order button!
    column_extra_row_actions = None

    column_list = (
        "id",
        "work_orders_rel",
        "quickbooks_num",
        # "work_orders_rel.status_rel",
        "work_orders_rel.status_rel.name",
        "invoice_approval_req",
        "approved_by_rel",
        "creator_rel",
        "field_tech_rel",
        # "sales_person_rel",
        "work_orders_rel.customers_rel.customer",
        "date_service",
        "timestamp_utc_inserted",
        "location",
        "invoice_summary",
        "work_orders_rel.work_done",
        "parts_rel.part_num",
        "part_id",
        "description",
        "structures_rel.structure_str",
        # "power_units_rel.power_unit_str",
        "quantity",
        "price",
        "cost_before_tax",
        "sales_tax_rate",
        "province_rel.name",
        "county_rel.name",
        "work_orders_rel.currency_rel.name",
        "sales_tax_part_amount",
    )
    column_sortable_list = (
        "quickbooks_num",
        "work_orders_rel.status_rel.name",
        "invoice_approval_req",
        # approved_by_rel causes an error
        # "approved_by_rel",
        # "creator_rel.first_name",
        # "work_orders_rel.id",
        "work_orders_rel.customers_rel.customer",
        "date_service",
        "timestamp_utc_inserted",
        "location",
        "invoice_summary",
        "work_orders_rel.work_done",
        "parts_rel.part_num",
        "part_id",
        "description",
        "structures_rel.structure_str",
        # "power_units_rel.power_unit_str",
        "quantity",
        "price",
        "cost_before_tax",
        "sales_tax_rate",
        "province_rel.name",
        "county_rel.name",
        "work_orders_rel.currency_rel.name",
        "sales_tax_part_amount",
        "id",
    )

    # True = sort descending
    column_default_sort = [
        ("date_service", True),
        # Invoice uploaded to (status)...
        # This is how we sort on relationship field
        (WorkOrderStatus.name, True),
        # Invoice number
        ("quickbooks_num", True),
        ("timestamp_utc_inserted", True),
    ]
    # form_columns = column_list

    # So Michelle can edit these easily (but they're association proxies...)
    # column_editable_list = (
    #     # "quickbooks_num",
    #     # "work_orders_rel.status_rel",
    # )
    # form_columns = (
    #     # "quickbooks_num",
    #     # "work_orders_rel.status_rel",
    # )

    column_export_list = (
        "id",  # unique number for importing CSV invoices into QuickBooks
        "work_orders_rel.customers_rel.customer",
        "timestamp_utc_inserted",
        "date_service",
        "date_due",
        "terms",
        "location",
        "invoice_summary",
        "work_orders_rel.work_done",
        "parts_rel.part_num",
        "part_id",
        "description",
        "structures_rel.structure_str",
        # "power_units_rel.power_unit_str",
        "quantity",
        "cost_before_tax",
        "sales_tax_rate",
        "sales_tax_part_amount",
        "work_orders_rel.currency_rel.name",
        "county_rel.name",
        "field_tech_rel",
        # "sales_person_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "id",
        "part_id",
        "quickbooks_num",
        "location",
        "invoice_summary",
        "parts_rel.part_num",
        "description",
        "structures_rel.structure_str",
        # "power_units_rel.power_unit_str",
        "work_orders_rel.creator_rel",
        "county_rel.name",
        "work_orders_rel.customers_rel.customer",
        "work_orders_rel",
        "work_orders_rel.status_rel",
        "work_orders_rel.work_done",
        "field_tech_rel.first_name",
        "field_tech_rel.last_name",
        # "sales_person_rel.first_name",
        # "sales_person_rel.last_name",
    )
    column_filters = (
        "id",
        "part_id",
        "parts_rel.part_num",
        "county_rel.name",
        "timestamp_utc_inserted",
        "structures_rel.structure_str",
        # "power_units_rel.power_unit_str",
        "quickbooks_num",
        "work_orders_rel.customers_rel",
        "work_orders_rel",
        "work_orders_rel.status_rel",
        "work_orders_rel.creator_rel",
        "work_orders_rel.work_done",
        "field_tech_rel.first_name",
        "field_tech_rel.last_name",
        # "sales_person_rel.first_name",
        # "sales_person_rel.last_name",
    )

    column_formatters = {
        "price": money_formatter,
        "cost_before_tax": money_formatter,
        "sales_tax_part_amount": money_formatter,
        "quickbooks_num": format_like_integer,
        "invoice_summary": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
        "description": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
        "approved_by_rel": user_formatter,
        "work_orders_rel.creator_rel": user_formatter,
        "timestamp_utc_inserted": datetime_formatter_sk_time,
        "work_orders_rel": work_order_id_link,
        "field_tech_rel": user_formatter,
        # "sales_person_rel": user_formatter,
    }
    # Don't truncate the invoice summary when exporting
    column_formatters_export = column_formatters

    column_labels = {
        "id": "Work Order Part ID",
        "parts_rel.part_num": "Part Number",
        "part_id": "Part ID",
        "creator_rel": "Creator",
        "quickbooks_num": "QuickBooks Invoice Number",
        # "work_orders_rel.customers_rel": "Customer",
        "work_orders_rel.customers_rel.customer": "Customer",
        "date_service": "Service Date",
        "timestamp_utc_inserted": "Date Inserted (SK Time)",
        "location": "Location",
        "invoice_summary": "Invoice Summary",
        "work_orders_rel.work_done": "Work Done",
        "description": "Description",
        # "power_units_rel.power_unit_str": "Power Unit",
        # "power_units_rel": "Power Unit",
        "structures_rel.structure_str": "Structure",
        "structures_rel": "Structure",
        "quantity": "Quantity",
        "price": "Price",
        "sales_tax": "Sales Tax",
        "work_orders_rel.currency_rel.name": "Currency",
        "work_orders_rel.status_rel.name": "Work Order Status",
        "work_orders_rel": "Work Order",
        "cost_before_tax": "Cost Before Tax",
        "sales_tax_rate": "Sales Tax Rate (%)",
        "sales_tax_part_amount": "Sales Tax Amount",
        "invoice_approval_req": "Invoice Approved?",
        "approved_by_rel": "Approver",
        # "sales_person_rel": "Sales Person",
        "field_tech_rel": "Credited To",
        "work_orders_rel.creator_rel": "Work Order Creator",
        "province_rel.name": "Province",
        "county_rel.name": "County",
    }
    column_descriptions = {
        "id": "Work order part ID in the database (primary key, auto-generated, not changeable)",
        "parts_rel.part_num": "Part from 'bill of materials master' spreadsheet",
        "part_id": "Part ID in the database (primary key, auto-generated, not changeable)",
        "quickbooks_num": "QuickBooks invoice number, created automatically by QuickBooks, for unique invoice identification in QuickBooks",
        "work_orders_rel.customers_rel.customer": "IJACK customer being invoiced",
        "date_service": "Service date (and invoice date)",
        "location": "Land location where service was performed",
        "invoice_summary": "Summary of various invoice fields, to be copied into QuickBooks",
        "work_orders_rel.work_done": "Description of work performed",
        "description": "Description of part or service line item in work order",
        # "power_units_rel.power_unit_str": "Power Unit to which this part belongs",
        # "power_units_rel": "Power Unit to which this part belongs",
        "structures_rel.structure_str": "Structure to which this part belongs",
        "structures_rel": "Structure to which this part belongs",
        "quantity": "Quantity of the part",
        "price": "Price of the part or service",
        "sales_tax": "Sales tax amount",
        "work_orders_rel.currency_rel.name": "Currency in which invoice is to be paid (e.g. CAD or USD)",
        "work_orders_rel.status_rel": "Has the work order been invoiced yet? Is it a warranty job?",
        "work_orders_rel": "Work order on which this part is listed",
        "cost_before_tax": "Quantity * price",
        "sales_tax_rate": "Sales Tax Rate (%)",
        "sales_tax_part_amount": "Sales Tax Amount",
        "invoice_approval_req": "Is the invoice approved?",
        "approved_by_rel": "Who is responsible for approving this invoice?",
        "work_orders_rel.creator_rel": "Who filled in and submitted/created the work order form?",
        "field_tech_rel": "Field technician who performed the work",
        # "sales_person_rel": "Sales person who gets credit for this sale",
    }

    # Only include the form_ajax_refs for testing
    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "work_orders_rel": QueryAjaxModelLoader(
            "work_orders_rel",
            db.session,
            WorkOrder,
            fields=["id", WorkOrder.status_rel],
            order_by="id",
            placeholder="Please select a work order",
            **options,
        ),
        "parts_rel": QueryAjaxModelLoader(
            "parts_rel",
            db.session,
            Part,
            fields=["part_num", "description"],
            order_by="part_num",
            placeholder="Please select a part",
            **options,
        ),
        # "power_units_rel": QueryAjaxModelLoader(
        #     "power_units_rel",
        #     db.session,
        #     PowerUnit,
        #     fields=["power_unit_str"],
        #     order_by="power_unit_str",
        #     placeholder="Please select a power unit",
        #     **options,
        # ),
        "structures_rel": QueryAjaxModelLoader(
            "structures_rel",
            db.session,
            Structure,
            fields=["structure_str"],
            order_by="structure_str",
            placeholder="Please select a structure",
            **options,
        ),
        "field_tech_rel": QueryAjaxModelLoader(
            "field_tech_rel",
            db.session,
            User,
            fields=["first_name", "last_name"],
            order_by="first_name",
            placeholder="Please select a field technician",
            **options,
        ),
        # "sales_person_rel": QueryAjaxModelLoader(
        #     "sales_person_rel",
        #     db.session,
        #     User,
        #     fields=["first_name", "last_name"],
        #     order_by="first_name",
        #     placeholder="Please select a sales person",
        #     **options,
        # ),
    }

    def render(self, template, **kwargs):
        """Add some extra JavaScript"""
        current_app.config.get("VERSION_MYIJACK", None)
        # Use the name of the vite bundle here
        self.extra_js = ["admin/flask_admin_work_order_parts_list_view.js"]
        return super().render(template, **kwargs)


class CurrencyView(MyModelView):
    """Flask-Admin view for Currency model"""

    can_create = True
    can_edit = True
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = (
        "name",
        "description",
        "fx_rate_cad_per",
        "country_rel.country_name",
        "id",
    )
    form_columns = ("name", "description", "fx_rate_cad_per", "country_rel")
    column_editable_list = ("fx_rate_cad_per",)

    column_labels = {
        "name": "Name",
        "description": "Description",
        "fx_rate_cad_per": "FX Rate (CAD per)",
        "country_rel.country_name": "Country Name",
        "country_rel": "Country Name",
    }

    column_descriptions = {
        "fx_rate_cad_per": "Foreign exchange rate (CAD per unit of this currency)",
    }


class WorkOrderStatusView(MyModelView):
    """Flask-Admin view for WorkOrderStatus model"""

    can_create = True
    can_edit = True
    can_delete = True
    can_set_page_size = True
    page_size = 50

    column_list = ("name", "description", "id")
    form_columns = ("name", "description")


class CountryView(MyModelView):
    """Flask-Admin view for Country model"""

    can_create = True
    can_edit = True
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = ("country_code", "country_name", "sales_tax_rate", "id")
    form_columns = ("country_code", "country_name", "sales_tax_rate")
    column_editable_list = ("sales_tax_rate",)

    column_searchable_list = ("country_code", "country_name")
    column_filters = ("country_code", "country_name", "sales_tax_rate")
    column_sortable_list = ("country_code", "country_name", "sales_tax_rate")

    column_labels = {
        "country_code": "Country Code",
        "country_name": "Country Name",
        "sales_tax_rate": "Sales Tax Rate (%)",
    }

    column_descriptions = {
        "country_code": "Two-letter country code (e.g. CA or US)",
        "country_name": "Country name (e.g. Canada or United States)",
        "sales_tax_rate": "Sales tax rate (%, e.g. Canadian GST)",
    }

    # options = {"minimum_input_length": 0}
    # form_ajax_refs = {
    #     "country_rel": QueryAjaxModelLoader(
    #         "country_rel",
    #         db.session,
    #         Country,
    #         fields=["country_name"],
    #         order_by="country_name",
    #         placeholder="Please select a country",
    #         **options,
    #     ),
    # }


class ProvinceView(MyModelView):
    """Flask-Admin view for Province model"""

    can_create = True
    can_edit = True
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = ("name", "abbrev", "country_rel.country_name", "id")
    form_columns = ("name", "abbrev", "country_rel")

    column_searchable_list = ("name", "abbrev", "country_rel.country_name")
    column_filters = ("name", "abbrev", "country_rel.country_name")
    column_sortable_list = ("name", "abbrev", "country_rel.country_name")

    column_labels = {
        "name": "Name",
        "abbrev": "Abbrev",
        # "country_code": "Country Code",
        "country_rel.country_name": "Country Name",
        "country_rel": "Country Name",
    }

    column_descriptions = {
        "abbrev": "Two-letter abbreviation (e.g. SK or ND)",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "country_rel": QueryAjaxModelLoader(
            "country_rel",
            db.session,
            Country,
            fields=["country_name"],
            order_by="country_name",
            placeholder="Please select a country",
            **options,
        ),
    }


class CountyView(MyModelView):
    """Flask-Admin view for County model"""

    can_create = True
    can_edit = True
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = ("name", "description", "province_rel.name", "county_code", "id")
    form_columns = ("name", "description", "province_rel", "county_code")

    column_searchable_list = ("name", "description", "county_code", "province_rel.name")
    column_filters = (
        "name",
        "province_rel.name",
        "description",
        "county_code",
    )
    column_sortable_list = (
        "name",
        "description",
        "province_rel.name",
        "county_code",
    )

    column_labels = {
        "name": "Name",
        "description": "Description",
        "county_code": "County Code",
        "province_rel": "Province or State",
        "province_rel.name": "Province or State",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "province_rel": QueryAjaxModelLoader(
            "province_rel",
            db.session,
            Province,
            fields=["name"],
            order_by="name",
            placeholder="Please select a province or state",
            **options,
        ),
    }


class CityView(MyModelView):
    """Model view for cities"""

    can_create = True
    can_edit = True
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = ("name", "province_rel.name", "county_rel.name", "id")
    column_editable_list = ("name",)
    form_columns = ("name", "province_rel", "county_rel")

    column_searchable_list = ("name", "province_rel.name", "county_rel.name")
    column_filters = ("name", "province_rel.name", "county_rel.name")
    column_sortable_list = ("name", "province_rel.name", "county_rel.name")

    column_labels = {
        "name": "Name",
        "province_rel": "Province or State",
        "province_rel.name": "Province or State",
        "county_rel": "County",
        "county_rel.name": "County",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "province_rel": QueryAjaxModelLoader(
            "province_rel",
            db.session,
            Province,
            fields=["name"],
            order_by="name",
            placeholder="Please select a province or state",
            **options,
        ),
        "county_rel": QueryAjaxModelLoader(
            "county_rel",
            db.session,
            County,
            fields=["name"],
            order_by="name",
            placeholder="Please select a county",
            **options,
        ),
    }


class ZipCodeSalesTaxView(MyModelView):
    """Flask-Admin view for ZipCodeSalesTax model"""

    can_create = True
    can_edit = True
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = (
        "zip_code_rel.zip_code",
        "province_rel.name",
        "county_rel.name",
        "city_rel.name",
        "state_plus_county",
        "combined_rate_est",
        "state_rate",
        "county_rate",
        "city_rate",
        "special_rate",
        "risk_level",
        "id",
    )
    column_editable_list = (
        # Calculated fields are not editable
        # "state_plus_county",
        # "combined_rate_est",
        "state_rate",
        "county_rate",
        "city_rate",
        "special_rate",
        "risk_level",
    )
    form_columns = (
        # "zip_code_rel",
        # "province_rel",
        # "county_rel",
        # "city_rel",
        # Calculated fields are not editable
        # "state_plus_county",
        # "combined_rate_est",
        "state_rate",
        "county_rate",
        "city_rate",
        "special_rate",
        "risk_level",
        "province_rel",
        "county_rel",
        "city_rel",
        "zip_code_rel",
    )

    column_searchable_list = (
        "zip_code_rel.zip_code",
        "province_rel.name",
        "county_rel.name",
        "city_rel.name",
        "combined_rate_est",
    )
    column_filters = (
        "zip_code_rel.zip_code",
        "province_rel.name",
        "county_rel.name",
        "city_rel.name",
        "combined_rate_est",
    )
    column_sortable_list = (
        "zip_code_rel.zip_code",
        "province_rel.name",
        "county_rel.name",
        "city_rel.name",
        "state_plus_county",
        "combined_rate_est",
        "state_rate",
        "county_rate",
        "city_rate",
        "special_rate",
        "risk_level",
    )

    column_labels = {
        "zip_code_rel": "Zip Code",
        "zip_code_rel.zip_code": "Zip Code",
        "province_rel": "State",
        "province_rel.name": "State",
        "county_rel": "County",
        "county_rel.name": "County",
        "city_rel": "City",
        "city_rel.name": "City",
        "state_plus_county": "State + County",
        "combined_rate_est": "All Rates Combined",
        "state_rate": "State Rate",
        "county_rate": "County Rate",
        "city_rate": "City Rate",
        "special_rate": "Special Rate",
        "risk_level": "Risk Level",
    }

    column_descriptions = {
        "combined_rate_est": "State + County + City + Special sales tax rate",
        "state_plus_county": "State + County sales tax rate",
        "state_rate": "State sales tax rate",
        "county_rate": "County sales tax rate",
        "city_rate": "City sales tax rate",
        "special_rate": "Special sales tax rate",
        "risk_level": "Risk level",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "province_rel": QueryAjaxModelLoader(
            "province_rel",
            db.session,
            Province,
            fields=["name"],
            order_by="name",
            placeholder="Please select a state",
            **options,
        ),
        "county_rel": QueryAjaxModelLoader(
            "county_rel",
            db.session,
            County,
            fields=["name"],
            order_by="name",
            placeholder="Please select a county",
            **options,
        ),
        "city_rel": QueryAjaxModelLoader(
            "city_rel",
            db.session,
            City,
            fields=["name"],
            order_by="name",
            placeholder="Please select a city",
            **options,
        ),
        "zip_code_rel": QueryAjaxModelLoader(
            "zip_code_rel",
            db.session,
            ZipCode,
            fields=["zip_code"],
            order_by="zip_code",
            placeholder="Please select a zip code",
            **options,
        ),
    }


class SalesTaxView(MyModelView):
    """Flask-Admin view for SalesTax model"""

    can_create = True
    can_edit = True
    can_delete = True
    can_set_page_size = True
    page_size = 50

    column_list = ("province_rel.name", "rate", "id")
    form_columns = ("province_rel", "rate")
    column_editable_list = ("rate",)

    column_sortable_list = ("province_rel.name", "rate")
    column_searchable_list = ("province_rel.name", "rate")
    column_filters = ("province_rel.name", "rate")

    column_labels = {
        "province_rel": "Province or State",
        "province_rel.name": "Province or State",
        "rate": "Provincial Sales Tax Rate",
    }
    column_descriptions = {
        "rate": "Provincial sales tax rate (e.g. 0.06 for 6% for Saskatchewan, or 0 for Alberta no PST)",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "province_rel": QueryAjaxModelLoader(
            "province_rel",
            db.session,
            Province,
            fields=["name"],
            order_by="name",
            placeholder="Please select a province or state",
            **options,
        ),
    }


class ContactFormView(MyModelView):
    """Flask-Admin view for ContactForm model"""

    can_create = False
    can_edit = True
    can_delete = True
    can_set_page_size = True
    page_size = 50

    column_list = (
        "timestamp_utc_inserted",
        "user_rel",
        "message",
        "first_name",
        "last_name",
        "phone",
        "email",
        "id",
    )
    form_columns = (
        "timestamp_utc_inserted",
        "user_rel",
        "message",
        "first_name",
        "last_name",
        "phone",
        "email",
    )
    column_editable_list = (
        "message",
        "first_name",
        "last_name",
        "phone",
        "email",
        "user_rel",
    )
    column_sortable_list = (
        # "user_rel.email",
        "timestamp_utc_inserted",
        "first_name",
        "last_name",
        "phone",
        "email",
    )
    column_searchable_list = (
        "user_rel.email",
        "message",
        "first_name",
        "last_name",
        "phone",
        "email",
    )
    column_filters = (
        "user_rel.email",
        "message",
        "first_name",
        "last_name",
        "phone",
        "email",
    )
    # Default sort order - True = descending
    column_default_sort = [("timestamp_utc_inserted", True)]

    column_labels = {
        "timestamp_utc_inserted": "Date Inserted (SK Time)",
        "user_rel": "User",
        "user_rel.email": "User Email",
        "message": "Message",
        "first_name": "First Name",
        "last_name": "Last Name",
        "phone": "Phone",
        "email": "Email",
    }
    column_descriptions = {
        "timestamp_utc_inserted": "Date and time the contact form was submitted (SK time)",
        "user_rel": "User who submitted the contact form",
        "user_rel.email": "User who submitted the contact form",
        "message": "Message submitted in the contact form",
        "first_name": "First name of the person who submitted the contact form",
        "last_name": "Last name of the person who submitted the contact form",
        "phone": "Phone number of the person who submitted the contact form",
        "email": "Email address of the person who submitted the contact form",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "user_rel": QueryAjaxModelLoader(
            "user_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
    }


class CareerApplicationView(MyModelView):
    """Flask-Admin view for CareerApplication model"""

    can_create = False
    can_edit = False
    can_delete = False
    can_set_page_size = True
    page_size = 50
    # Duplicate button - make None to disable
    column_extra_row_actions = None

    column_list = (
        "timestamp_inserted_utc",
        "users_rel",
        "job_type",
        "message",
        "career_files_rel",
        "id",
    )
    form_columns = (
        "timestamp_inserted_utc",
        "users_rel",
        "job_type",
        "message",
        "career_files_rel",
    )
    column_editable_list = (
        "job_type",
        "message",
    )
    column_sortable_list = (
        "timestamp_inserted_utc",
        "users_rel.email",
        "job_type",
        "message",
        "id",
    )
    column_searchable_list = (
        "users_rel.email",
        "users_rel.first_name",
        "users_rel.last_name",
        "job_type",
        "message",
    )
    column_filters = (
        "users_rel.email",
        "users_rel.first_name",
        "users_rel.last_name",
        "job_type",
        "message",
        "id",
    )
    # Default sort order - True = descending
    column_default_sort = [("timestamp_inserted_utc", True)]

    column_formatters = {
        "timestamp_inserted_utc": datetime_formatter_sk_time,
        "users_rel": user_formatter,
        "career_files_rel": download_uploaded_career_file_link,
    }

    column_labels = {
        "timestamp_inserted_utc": "Date Inserted (SK Time)",
        "users_rel": "Applicant Person",
        "users_rel.email": "Email",
        "users_rel.first_name": "First Name",
        "users_rel.last_name": "Last Name",
        "job_type": "Job Type",
        "message": "Message",
        "career_files_rel": "Files Submitted",
    }

    column_descriptions = {
        "timestamp_inserted_utc": "Date and time the career application was submitted (SK time)",
        "users_rel": "User who submitted the career application",
        "users_rel.email": "User who submitted the career application",
        "job_type": "Type of job applied for",
        "message": "Message submitted with the career application",
        "career_files_rel": "Files submitted with the career application",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "users_rel": QueryAjaxModelLoader(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
    }


class ServiceView(MyModelView):
    """Class for Service requests data model"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True

    page_size = 50

    column_list = (
        "timestamp_utc",
        "priority",
        "users_rel.email",
        "description",
        "is_resolved",
        "resolution",
        "operator_name",
        "operator_phone",
        "operator_email",
        "service_type_rel.name",
        "structures_rel.power_unit_str",
        "work_orders_rel.id",
    )
    form_columns = (
        "timestamp_utc",
        "priority",
        "users_rel",
        "description",
        "is_resolved",
        "resolution",
        "operator_name",
        "operator_phone",
        "operator_email",
        "service_type_rel",
        "structures_rel",
        # "work_orders_rel",
    )
    column_editable_list = (
        "timestamp_utc",
        "priority",
        # "users_rel",
        "description",
        "is_resolved",
        "resolution",
        "operator_name",
        # If this is editable, then the link formatting won't work well...
        # "operator_phone",
        # "operator_email",
        # "service_type_rel",
        # "structures_rel",
        # "work_orders_rel",
    )
    column_labels = {
        "timestamp_utc": "Date Created (SK Time)",
        "priority": "Priority",
        "users_rel": "Creator",
        "users_rel.email": "Creator Email",
        "description": "Description",
        "is_resolved": "Resolved?",
        "resolution": "Resolution",
        "operator_name": "Operator Name",
        "operator_phone": "Operator Phone",
        "operator_email": "Operator Email",
        "service_type_rel": "Service Type",
        "service_type_rel.name": "Service Type",
        "structures_rel": "Unit",
        "structures_rel.power_unit_str": "Power Unit",
        "work_orders_rel": "Work Order",
        "work_orders_rel.id": "Work Order",
    }
    column_descriptions = {
        "timestamp_utc": "Date and time the service request was created (SK time)",
        "priority": "Priority of the service request (1 is most urgent)",
        "users_rel": "User who created the service request",
        "users_rel.email": "User who created the service request",
        "description": "Description of the service request",
        "is_resolved": "Has the service request been resolved?",
        "resolution": "Resolution of the service request. What did we do to resolve it?",
        "operator_name": "Name of the operator who requested the service",
        "operator_phone": "Phone number of the operator who requested the service",
        "operator_email": "Email of the operator who requested the service",
        "service_type_rel": "Type of service requested",
        "service_type_rel.name": "Type of service requested",
        "structures_rel": "Unit for which service was requested",
        "structures_rel.power_unit_str": "Unit for which service was requested",
        "work_orders_rel": "Work order associated with this service request",
        "work_orders_rel.id": "Work order associated with this service request",
    }
    column_sortable_list = (
        "timestamp_utc",
        "priority",
        "users_rel.email",
        "description",
        "is_resolved",
        "resolution",
        "operator_name",
        "operator_phone",
        "operator_email",
        "service_type_rel.name",
        "structures_rel.power_unit_str",
        "work_orders_rel.id",
    )
    # True = sort descending
    column_default_sort = [
        ("is_resolved", False),
        ("priority", False),
        ("timestamp_utc", True),
    ]
    column_filters = (
        "timestamp_utc",
        "priority",
        "users_rel.email",
        "description",
        "is_resolved",
        "resolution",
        "operator_name",
        "operator_phone",
        "operator_email",
        "service_type_rel.name",
        "structures_rel.power_unit_str",
        "work_orders_rel.id",
    )
    column_searchable_list = (
        "users_rel.email",
        "description",
        "resolution",
        "operator_name",
        "operator_phone",
        "operator_email",
        "service_type_rel.name",
        "structures_rel.power_unit_str",
    )

    column_formatters = {
        "timestamp_utc": datetime_formatter_sk_time,
        # Doesn't work for anonymous users
        # "users_rel.email": user_formatter,
        "operator_phone": user_phone_num_formatter,
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "structures_rel": QueryAjaxModelLoaderAdmin(
            "structures_rel",
            db.session,
            Structure,
            fields=[Structure.structure, PowerUnit.power_unit_str, Structure.surface],
            order_by="structure",
            placeholder="Please select a structure",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        "service_type_rel": QueryAjaxModelLoader(
            "service_type_rel",
            db.session,
            ServiceType,
            fields=["name"],
            order_by="name",
            placeholder="Please select a service type",
            **options,
        ),
        "users_rel": QueryAjaxModelLoader(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
    }

    def reorder_rows(self, changed_model, new_priority):
        """Reorder the rows after a priority change"""
        # Get all rows ordered by priority (including the new priority row)
        rows = Service.query.order_by("priority").all()

        # Find the index of the changed model
        index_changed_model = next(
            (i for i, row in enumerate(rows) if row.id == changed_model.id), None
        )
        # Pop the changed model from the list
        changed_model = rows.pop(index_changed_model)

        if isinstance(new_priority, int):
            # Insert the changed model at the new priority
            rows.insert(new_priority - 1, changed_model)

        # Update the priority of each row based on the new priority
        for index, row in enumerate(rows):
            row.priority = index + 1

    def on_model_change(self, form, model, is_created):
        """When someone creates a new service request, email the IJACK admin"""
        if is_created:
            structure_obj = model.structures_rel
            # Submit a service request and create a work order
            submit_service_request(
                operator_full_name=model.operator_name,
                operator_phone=model.operator_phone,
                operator_email=model.operator_email,
                description=model.description,
                service_type_id=model.service_type_id,
                structure_id=getattr(structure_obj, "id", None),
                user_id=getattr(current_user, "id", None),
                create_work_order=True,
            )

        # Adjust the priority if it's changed
        if "priority" in form.data.keys() and form.priority.data is not None:
            # Update other rows based on the new priority
            self.reorder_rows(model, form.priority.data)


class ServiceEmaileeView(MyModelView):
    """ServiceEmailee model"""

    can_create = True
    can_edit = True
    can_delete = True
    can_set_page_size = True
    page_size = 50
    # Duplicate button - make None to disable
    column_extra_row_actions = None

    column_list = ("user_rel", "id")
    form_columns = ("user_rel",)
    column_editable_list = ("user_rel",)
    column_default_sort = [("id", False)]
    column_searchable_list = (
        "user_rel.email",
        "user_rel.first_name",
        "user_rel.last_name",
    )
    column_filters = ("user_rel.email", "user_rel.first_name", "user_rel.last_name")

    column_labels = {
        "user_rel": "User To Be Emailed",
        "user_rel.email": "User Email",
        "user_rel.first_name": "User First Name",
        "user_rel.last_name": "User Last Name",
    }
    column_descriptions = {
        "user_rel": "User who will receive the service request emails",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "user_rel": QueryAjaxModelLoader(
            "user_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
    }


class ServiceTypeView(MyModelView):
    """Flask-Admin view for ServiceType model"""

    can_create = True
    can_edit = True
    # Work orders use these service types, so don't delete them!
    can_delete = False
    can_set_page_size = True
    page_size = 50

    column_list = ("name", "description", "id")
    form_columns = ("name", "description")


class AlertCustomView(MyModelView):
    """Flask-Admin view for AlertCustom model (public.alerts_custom table)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True

    column_list = (
        "customers_rel",
        "users_rel",
        "months_rel",
        "days_rel",
        "hour_end_rel",
        "time_zones_rel",
        "want_email",
        "want_sms",
        "structures_rel",
        "subject",
        "body",
        "images_rel",
        "id",
    )
    column_sortable_list = (
        "customers_rel.customer",
        # "users_rel.first_name",
        "months_rel.month",
        "days_rel.day",
        "hour_end_rel.hour",
        "time_zones_rel.time_zone",
        "want_email",
        "want_sms",
        "structures_rel.structure",
        "subject",
        "body",
        # "images_rel.image",
        "id",
    )
    # True = sort descending
    column_default_sort = [
        ("customers_rel.customer", False),
        ("subject", False),
    ]
    can_set_page_size = True
    page_size = 20

    # Control the order of the columns in the forms
    form_columns = (
        "subject",
        "body",
        "customers_rel",
        "months_rel",
        "days_rel",
        "hour_end_rel",
        "time_zones_rel",
        "want_email",
        "want_sms",
        "users_rel",
        "structures_rel",
        "images_rel",
    )
    # # The many-to-many fields don't show up at first
    # column_editable_list = (
    #     "subject",
    #     "body",
    #     # "customers_rel",
    #     # "months_rel",
    #     # "days_rel",
    #     # "hour_end_rel",
    #     # "users_rel",
    #     # "structures_rel",
    # )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "subject",
        "body",
        "customers_rel.customer",
        "users_rel.first_name",
        "users_rel.last_name",
        "structures_rel.structure",
    )
    column_filters = (
        "customers_rel",
        # "months_rel",
        # "days_rel",
        # "hour_end_rel",
        "users_rel",
        "structures_rel",
        "want_email",
        "want_sms",
    )

    column_formatters = {
        # `view` is current administrative view
        # `context` is instance of jinja2.runtime.Context
        # `model` is model instance
        # `name` is property name
        # Format your column string with lambda here (e.g show first 20 characters).
        # Can return any valid HTML (e.g. a link to another view to show the detail or a popup window).
        "structures_rel": remove_unnecessary_decimal,
        # "subject": lambda v, c, m, n: m.subject[:15]
        # if isinstance(m.subject, str)
        # else m.subject,
        "body": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
        # if isinstance(getattr(m, n), str)
        # else getattr(m, n),
    }

    column_labels = {
        "subject": "Subject",
        "body": "Body",
        "images_rel": "Images",
        "customers_rel": "Customer",
        "months_rel": "Months of Year",
        "days_rel": "Days of Month",
        "hour_end_rel": "Hour of Day",
        "time_zones_rel": "Time Zone",
        "want_email": "Email Alert",
        "want_sms": "SMS Alert",
        "users_rel": "Users",
        "structures_rel": "Units",
    }
    column_descriptions = {
        "subject": "Subject of email alert",
        "body": "Message body (can be HTML or plain text)",
        "images_rel": "Images to attach to the custom alert email",
        "customers_rel": "Customer for which alert applies",
        "months_rel": "Months in which alert is sent",
        "days_rel": "Days of month in which alert is sent",
        "hour_end_rel": "Hour of day in which alert is sent",
        "time_zones_rel": "Time zone for 'Hour of Day'",
        "want_email": "Do you want to receive an email alert?",
        "want_sms": "Do you want to receive an SMS alert?",
        "users_rel": "Users receiving this alert",
        "structures_rel": "Units for which alert applies",
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "customers_rel": QueryAjaxModelLoader(
            "customers_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select a customer",
            **options,
        ),
        "users_rel": QueryAjaxModelLoaderAdminCust(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
        "structure": QueryAjaxModelLoader(
            "structure",
            db.session,
            Structure,
            fields=[Structure.structure, PowerUnit.power_unit_str, Structure.surface],
            order_by="structure",
            placeholder="Please select a structure",
            **options,
        ),
        "time_zones_rel": QueryAjaxModelLoader(
            "time_zones_rel",
            db.session,
            TimeZone,
            fields=["time_zone"],
            order_by="time_zone",
            placeholder="Please select a time zone",
            get_query_func=timezone_filtering_function,
            **options,
        ),
        "hour_end_rel": QueryAjaxModelLoader(
            "hour_end_rel",
            db.session,
            Hour,
            fields=["hour_ending"],
            order_by="hour_ending",
            placeholder="Please select an hour for the alert",
            **options,
        ),
    }

    # These don't work in a many-to-many relationship. They work in the "images" table directly
    # column_formatters = {"images_rel": ImageUploadFieldDB.display_thumbnail}
    # form_extra_fields = {
    #     "images_rel": ImageUploadFieldDB("Image", thumbnail_size=(100, 100, True))
    # }


class ImageFieldView(MyModelView):
    """Flask-Admin view for database-stored images"""

    can_create = True
    can_edit = True
    # Don't allow image deletion since someone might delete an image needed for an alert
    can_delete = False
    column_display_pk = True
    can_set_page_size = True
    page_size = 10

    column_list = (
        "name",
        "description",
        "image",
        "format",
        "id",
    )
    column_sortable_list = column_list

    # Control the order of the columns in the forms
    form_columns = (
        "name",
        "description",
        "image",
    )
    column_editable_list = (
        "name",
        "description",
        # "image",
    )

    # True = sort descending
    column_default_sort = [
        ("name", False),
        ("description", False),
    ]

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "name",
        "description",
    )
    column_filters = ("id", "format")

    column_formatters = {"image": ImageUploadFieldDB.display_thumbnail}
    form_extra_fields = {
        "image": ImageUploadFieldDB("Image", validators=[DataRequired()])
    }


# class DateTimeLocalTimezoneInput(DateTimeLocalInput):
#     """
#     Renders an input with type "datetime-local" and a timezone selector.
#     """

#     input_type = "datetime-local"
#     validation_attrs = ["required", "max", "min", "step"]

#     def render_timezone_selector(self, field):
#         """Renders a timezone selector."""
#         if not hasattr(field, "timezone") or not field.timezone:
#             return ""
#         return Markup(
#             '<select class="select2" data-placeholder="%s" name="%s" id="%s">%s</select>'
#             % (
#                 field.timezone,
#                 field.timezone,
#                 field.name + "_timezone",
#                 "".join(
#                     [
#                         '<option value="%s"%s>%s</option>'
#                         % (
#                             tz,
#                             ' selected="selected"' if tz == field.timezone else "",
#                             tz,
#                         )
#                         for tz in pytz.common_timezones
#                     ]
#                 ),
#             )
#         )

#     def __call__(self, field, **kwargs):
#         kwargs.setdefault("type", self.input_type)
#         kwargs.setdefault("step", "1")
#         if field.data:
#             try:
#                 # Get the unit's time zone from the form
#                 timezone_db = pytz.timezone(kwargs["timezone_db"])
#                 timezone_display = pytz.timezone(kwargs["timezone_display"])

#                 # Convert to local time
#                 value_local = (
#                     timezone_db.localize(field.data, is_dst=None)
#                     .astimezone(timezone_display)
#                     .strftime("%Y-%m-%dT%H:%M:%S")
#                 )
#                 kwargs.setdefault("value", value_local)
#             except Exception:
#                 kwargs.setdefault("value", field.data.strftime("%Y-%m-%dT%H:%M:%S"))
#         else:
#             kwargs.setdefault("value", "")

#         return Markup(
#             # "<input {}>{}".format(
#             "<input {}>".format(
#                 self.html_params(name=field.name, **kwargs),
#                 # None
#                 # self.render_timezone_selector(field),
#             )
#         )


class DateTimeField(DateTimeFieldFA):
    """
    Same as :class:`~wtforms.fields.DateTimeField`, but represents an
    ``<input type="datetime-local">``.
    """

    # widget = DateTimeLocalTimezoneInput()

    def __init__(self, *args, **kwargs):
        self.timezone_db = kwargs.pop("timezone_db", "UTC")
        self.timezone_display = kwargs.pop("timezone_display", None)
        super().__init__(*args, **kwargs)

    def process_data(self, value) -> None:
        """
        Process the Python data applied to this field and store the result.

        This will be called during form construction by the form's `kwargs` or
        `obj` argument.

        :param value: The python object containing the value to process.
        """
        if value and self.timezone_display:
            # Convert from database timezone to desired local timezone,
            # for display in the create/edit forms
            timezone_db = pytz.timezone(self.timezone_db)
            timezone_display = pytz.timezone(self.timezone_display)
            self.data = (
                timezone_db.localize(value, is_dst=None)
                .astimezone(timezone_display)
                .replace(tzinfo=None)
            )
        else:
            self.data = value

        return None


class ServiceClockView(MyModelView):
    """Flask-Admin view for the ServiceClock table"""

    def is_accessible(self):
        """The user must have certain roles to access this view"""
        role_ids: list = get_user_role_ids(user_id=getattr(current_user, "id", None))
        # These users will be approving the service clock timesheet hours
        # before payroll is run.
        return (
            ROLE_ID_SERVICE_CLOCK_TIME_APPROVER in role_ids
            and ROLE_ID_IJACK_ADMIN in role_ids
        )

    def inaccessible_callback(self, name, **kwargs):
        """Redirect to login page if user doesn't have access"""
        return redirect(url_for("home.rcom", next=request.url))

    @expose("/", methods=("GET", "POST"))
    def index_view(self):
        """Main list view"""
        if not current_user.has_role_id(ROLE_ID_SERVICE_CLOCK_TIME_APPROVER):
            self.can_edit = False
            self.can_delete = False
            self.can_create = False
            # Remove the duplicate button
            self.column_extra_row_actions = None

        return super().index_view()

    @expose("/edit/", methods=("GET", "POST"))
    def edit_view(self):
        """
        Edit model view - only service clock time approvers can see this view
        """
        if not current_user.has_role_id(ROLE_ID_SERVICE_CLOCK_TIME_APPROVER):
            flash(
                "You do not have permission edit the service clock records. Only users with the service clock time approver role can edit service clock records.",
                "danger",
            )
            return abort(403)

        return super().edit_view()

    @expose("/delete/", methods=("POST",))
    def delete_view(self):
        """
        Delete model view. Only POST method is allowed.
        Only service clock time approvers can delete service clock records.
        """
        if not current_user.has_role_id(ROLE_ID_SERVICE_CLOCK_TIME_APPROVER):
            flash(
                "You do not have permission delete the service clock records. Only users with the service clock time approver role can delete service clock records.",
                "danger",
            )
            return redirect(url_for("service_clock.index_view"))

        return super().delete_view()

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True

    page_size = 60

    column_list = (
        "employee_rel",
        "structure_rel",
        "warehouse_rel",
        # "structure_rel.power_unit_str",
        # "structure_rel.surface",
        "timestamp_utc_in",
        "timestamp_utc_out",
        "time_zone_in_rel",
        "time_zone_out_rel",
        # Calculated field
        "total_hours_worked",
        "gps_lat_in",
        "gps_lon_in",
        "gps_lat_out",
        "gps_lon_out",
        # "notes_in",
        # "notes_out",
        "notes",
        "id",
    )
    column_sortable_list = (
        # "employee_rel",
        # "structure_rel",
        # "structure_rel.power_unit_str",
        # "structure_rel.surface",
        "timestamp_utc_in",
        "timestamp_utc_out",
        # Calculated field
        "total_hours_worked",
        "gps_lat_in",
        "gps_lon_in",
        "gps_lat_out",
        "gps_lon_out",
        # "notes_in",
        # "notes_out",
        "notes",
        "id",
    )

    column_editable_list = (
        "employee_rel",
        "structure_rel",
        "warehouse_rel",
        "timestamp_utc_in",
        "timestamp_utc_out",
        "time_zone_in_rel",
        "time_zone_out_rel",
        # "notes_in",
        # "notes_out",
        "notes",
    )

    # True = sort descending
    column_default_sort = [
        ("timestamp_utc_in", True),
        ("timestamp_utc_out", True),
        (User.first_name, False),
        (User.last_name, False),
    ]

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        User.first_name,
        User.last_name,
        User.email,
        # "notes_in",
        # "notes_out",
        "notes",
        "structure_rel.structure_str",
        "structure_rel.power_unit_str",
        "structure_rel.surface",
        "warehouse_rel.name",
        "time_zone_in_rel.time_zone",
        "time_zone_out_rel.time_zone",
    )
    column_filters = (
        "id",
        User.first_name,
        User.last_name,
        User.email,
        "structure_rel.structure_str",
        "structure_rel.power_unit_str",
        "structure_rel.surface",
        "timestamp_utc_in",
        "timestamp_utc_out",
        "time_zone_in_rel.time_zone",
        "time_zone_out_rel.time_zone",
        "warehouse_rel.name",
    )

    column_labels = {
        "employee_rel": "Employee",
        "first_name": "First Name",
        "last_name": "Last Name",
        "email": "Email",
        "timestamp_utc_in": "Time In (SK Time)",
        "timestamp_utc_out": "Time Out (SK Time)",
        # Calculated field
        "total_hours_worked": "Total Hours Worked",
        "time_zone_in_rel": "Time Zone In",
        "time_zone_in_rel.time_zone": "Time Zone In",
        "time_zone_out_rel": "Time Zone Out",
        "time_zone_out_rel.time_zone": "Time Zone Out",
        "gps_lat_in": "GPS Latitude In",
        "gps_lon_in": "GPS Longitude In",
        "gps_lat_out": "GPS Latitude Out",
        "gps_lon_out": "GPS Longitude Out",
        # "notes_in": "Notes In",
        # "notes_out": "Notes Out",
        "notes": "Notes",
        "structure_rel": "Structure",
        "structure_rel.structure_str": "Structure",
        "structure_rel.power_unit_str": "Power Unit",
        "structure_rel.surface": "Surface",
        "warehouse_rel": "Warehouse",
        "warehouse_rel.name": "Warehouse",
        "id": "ID",
    }
    column_descriptions = {
        "employee_rel": "IJACK employee",
        "timestamp_utc_in": "Time when the user clocked in (SK time)",
        "timestamp_utc_out": "Time when the user clocked out (SK time)",
        # Calculated field
        "total_hours_worked": "Time clocked out minus time clocked in",
        "time_zone_in_rel": "Time zone where the clock-in occurred",
        "time_zone_in_rel.time_zone": "Time zone where the clock-in occurred",
        "time_zone_out_rel": "Time zone where the clock-out occurred",
        "time_zone_out_rel.time_zone": "Time zone where the clock-out occurred",
        "gps_lat_in": "Employee's GPS latitude when clocking in",
        "gps_lon_in": "Employee's GPS longitude when clocking in",
        "gps_lat_out": "Employee's GPS latitude when clocking out",
        "gps_lon_out": "Employee's GPS longitude when clocking out",
        # "notes_in": "Notes about the clock in",
        # "notes_out": "Notes about the clock out",
        "notes": "Notes about the clock in/out",
        "structure_rel": "Structure for which the employee clocked in/out",
        "structure_rel.structure_str": "Structure for which the employee clocked in/out",
        "warehouse_rel": "Warehouse for which the employee clocked in/out",
        "warehouse_rel.name": "Warehouse for which the employee clocked in/out",
    }

    # These are just for the list view. We see them as local time in the list view,
    # and when we edit the record, we convert to UTC time in "on_model_change" below.
    # These column formatters don't work for the "edit" or "create" forms. For those,
    # we use the "form_overrides" and "form_args" below, and the "DateTimeField" class
    # with the "timezone_db" and "timezone_display" arguments, and the revamped
    # "process_data" method in the "DateTimeField" class.
    column_formatters = {
        "timestamp_utc_in": datetime_formatter_sk_time,
        "timestamp_utc_out": datetime_formatter_sk_time,
    }

    # These are ONLY for the edit view. We see them as UTC time in the edit view
    form_overrides = {
        "timestamp_utc_in": DateTimeField,
        "timestamp_utc_out": DateTimeField,
    }

    # These are ONLY for the edit view. We see them as UTC time in the edit view
    form_args = {
        "timestamp_utc_in": {
            "timezone_db": "UTC",
            "timezone_display": "America/Regina",
        },
        "timestamp_utc_out": {
            "timezone_db": "UTC",
            "timezone_display": "America/Regina",
        },
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "employee_rel": QueryAjaxModelLoaderAdmin(
            "employee_rel",
            db.session,
            User,
            fields=["first_name", "last_name", "email"],
            # Only IJACK employees
            get_query_func=lambda: User.query.filter(
                User.customer_id.in_(IJACK_CUST_IDS_LIST)
            ),
            order_by=User.first_name,
            placeholder="Please select an employee",
            **options,
        ),
        "structure_rel": QueryAjaxModelLoaderAdmin(
            "structure_rel",
            db.session,
            Structure,
            fields=[Structure.structure, PowerUnit.power_unit_str, Structure.surface],
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            order_by="structure",
            placeholder="Please select a structure",
            **options,
        ),
        "warehouse_rel": QueryAjaxModelLoaderAdmin(
            "warehouse_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select a warehouse",
            **options,
        ),
        "time_zone_in_rel": QueryAjaxModelLoaderAdmin(
            "time_zone_in_rel",
            db.session,
            TimeZone,
            fields=["time_zone"],
            order_by="time_zone",
            placeholder="Please select a clock-in time zone",
            get_query_func=timezone_filtering_function,
            **options,
        ),
        "time_zone_out_rel": QueryAjaxModelLoaderAdmin(
            "time_zone_out_rel",
            db.session,
            TimeZone,
            fields=["time_zone"],
            order_by="time_zone",
            placeholder="Please select a clock-out time zone",
            get_query_func=timezone_filtering_function,
            **options,
        ),
    }

    def on_model_change(self, form, model, is_created) -> None:
        """
        Convert the timestamp_utc_in and timestamp_utc_out fields from CST timezone to UTC timezone for the database.
        This is because the timestamp_utc_in and timestamp_utc_out fields are stored in the database as UTC timezone.
        However, the datetime_formatter_sk_time() function converts the UTC timezone to CST timezone.
        So, we need to convert the CST timezone back to UTC timezone for the database.
        """
        # Convert to UTC time from Regina time, and save the UTC time in the database
        for field_name, instance in self.form_overrides.items():
            if not issubclass(instance, DateTimeField):
                continue
            # If the field has been changed (i.e. it's in the form)
            dt_form = form.data.get(field_name, None)
            if isinstance(dt_form, datetime):
                timezone_display = pytz.timezone(
                    self.form_args.get(field_name, {}).get("timezone_display", None)
                )
                timezone_db = pytz.timezone(
                    self.form_args.get(field_name, {}).get("timezone_db", None)
                )
                dt_db = timezone_display.localize(dt_form, is_dst=None).astimezone(
                    timezone_db
                )
                setattr(model, field_name, dt_db)

        return None


class VwServiceClockHoursDailyView(MyModelView):
    """Flask-Admin view for the VwServiceClockHoursDaily view"""

    def is_accessible(self):
        """The user must have certain roles to access this view"""
        role_ids: list = get_user_role_ids(user_id=getattr(current_user, "id", None))
        return (
            ROLE_ID_SERVICE_CLOCK_TIME_APPROVER in role_ids
            and ROLE_ID_IJACK_ADMIN in role_ids
        )

    def inaccessible_callback(self, name, **kwargs):  # type: ignore
        """Redirect to login page if user doesn't have access"""
        return redirect(url_for("home.rcom", next=request.url))

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = False
    can_set_page_size = True

    page_size = 60

    column_list = (
        "year_",
        "month_",
        "day_",
        "name_",
        "time_records",
        "days_worked",
        "hours_worked",
        # "total_hours",
        # "service_hours",
        # "travel_hours",
    )
    column_sortable_list = (
        "year_",
        "month_",
        "day_",
        "name_",
        "hours_worked",
        "days_worked",
        # "travel_hours",
        # "service_hours",
        # "total_hours",
    )
    column_default_sort = [
        ("year_", True),
        ("month_", True),
        ("day_", True),
        ("name_", True),
    ]
    column_labels = {
        "year_": "Year",
        "month_": "Month",
        "day_": "Day",
        "name_": "Employee",
        "hours_worked": "Hours Worked",
        "days_worked": "Days Worked",
        "time_records": "Time Records (SK Time)",
        # "travel_hours": "Travel Hours Billed",
        # "service_hours": "Service Hours Billed",
        # "total_hours": "Total Hours Billed",
    }
    column_descriptions = {
        "time_records": "Clock-in, clock-out records for this day, converted to SK time (CST)",
        # "travel_hours": "Travel hours billed in work orders",
        # "service_hours": "Service hours billed in work orders",
        # "total_hours": "Total hours billed in work orders",
    }
    column_formatters = {
        "year_": lambda v, c, m, n: f"{m.year_:.0f}",
        "month_": lambda v, c, m, n: f"{m.month_:.0f}",
        "day_": lambda v, c, m, n: f"{m.day_:.0f}",
        "hours_worked": lambda v, c, m, n: f"{m.hours_worked:,.2f}",
        "days_worked": lambda v, c, m, n: f"{m.days_worked:,.2f}",
        # These hours are NUMERIC type and they error out if we don't convert null values to floats
        # "travel_hours": lambda v, c, m, n: f"{m.travel_hours or 0:,.2f}",
        # "service_hours": lambda v, c, m, n: f"{m.service_hours or 0:,.2f}",
        # "total_hours": lambda v, c, m, n: f"{m.total_hours or 0:,.2f}",
    }
    column_searchable_list = (
        "year_",
        "month_",
        "day_",
        "name_",
    )
    column_filters = (
        "year_",
        "month_",
        "day_",
        "name_",
        "hours_worked",
        "days_worked",
        # "travel_hours",
        # "service_hours",
        # "total_hours",
    )


class VwServiceClockHoursMonthlyView(VwServiceClockHoursDailyView):
    """Flask-Admin view for the VwServiceClockHoursMonthly view"""

    column_list = (
        "year_",
        "month_",
        "name_",
        "days_worked",
        "hours_worked",
        # "total_hours",
        # "service_hours",
        # "travel_hours",
    )
    column_sortable_list = (
        "year_",
        "month_",
        "name_",
        "hours_worked",
        "days_worked",
        # "travel_hours",
        # "service_hours",
        # "total_hours",
    )
    column_default_sort = [
        ("year_", True),
        ("month_", True),
        ("name_", True),
    ]
    column_labels = {
        "year_": "Year",
        "month_": "Month",
        "name_": "Employee",
        "hours_worked": "Hours Worked",
        "days_worked": "Days Worked",
        # "travel_hours": "Travel Hours Billed",
        # "service_hours": "Service Hours Billed",
        # "total_hours": "Total Hours Billed",
    }
    column_descriptions = {
        "hours_worked": "Total hours worked in the month",
        "days_worked": "Total days worked in the month",
        # "travel_hours": "Total travel hours billed in work orders",
        # "service_hours": "Total service hours billed in work orders",
        # "total_hours": "Total hours billed in work orders",
    }
    column_formatters = {
        "year_": lambda v, c, m, n: f"{m.year_:.0f}",
        "month_": lambda v, c, m, n: f"{m.month_:.0f}",
        "hours_worked": lambda v, c, m, n: f"{m.hours_worked:,.2f}",
        "days_worked": lambda v, c, m, n: f"{m.days_worked:,.2f}",
        # These hours are NUMERIC type and they error out if we don't convert null values to floats
        # "travel_hours": lambda v, c, m, n: f"{m.travel_hours or 0:,.2f}",
        # "service_hours": lambda v, c, m, n: f"{m.service_hours or 0:,.2f}",
        # "total_hours": lambda v, c, m, n: f"{m.total_hours or 0:,.2f}",
    }
    column_searchable_list = (
        "year_",
        "month_",
        "name_",
    )
    column_filters = (
        "year_",
        "month_",
        "name_",
        "hours_worked",
        "days_worked",
        # "travel_hours",
        # "service_hours",
        # "total_hours",
    )


class VwServiceClockHoursYearlyView(VwServiceClockHoursDailyView):
    """Flask-Admin view for the VwServiceClockHoursYearly view"""

    column_list = (
        "year_",
        "name_",
        "days_worked",
        "hours_worked",
        # "total_hours",
        # "service_hours",
        # "travel_hours",
    )
    column_sortable_list = (
        "year_",
        "name_",
        "hours_worked",
        "days_worked",
        # "travel_hours",
        # "service_hours",
        # "total_hours",
    )
    column_default_sort = [
        ("year_", True),
        ("name_", True),
    ]
    column_labels = {
        "year_": "Year",
        "name_": "Employee",
        "hours_worked": "Hours Worked",
        "days_worked": "Days Worked",
        # "travel_hours": "Travel Hours Billed",
        # "service_hours": "Service Hours Billed",
        # "total_hours": "Total Hours Billed",
    }
    column_descriptions = {
        "hours_worked": "Total hours worked in the year",
        "days_worked": "Total days worked in the year",
        # "travel_hours": "Total travel hours billed in work orders",
        # "service_hours": "Total service hours billed in work orders",
        # "total_hours": "Total hours billed in work orders",
    }
    column_formatters = {
        "year_": lambda v, c, m, n: f"{m.year_:.0f}",
        "hours_worked": lambda v, c, m, n: f"{m.hours_worked:,.2f}",
        "days_worked": lambda v, c, m, n: f"{m.days_worked:,.2f}",
        # These hours are NUMERIC type and they error out if we don't convert null values to floats
        # "travel_hours": lambda v, c, m, n: f"{m.travel_hours or 0:,.2f}",
        # "service_hours": lambda v, c, m, n: f"{m.service_hours or 0:,.2f}",
        # "total_hours": lambda v, c, m, n: f"{m.total_hours or 0:,.2f}",
    }
    column_searchable_list = (
        "year_",
        "name_",
    )
    column_filters = (
        "year_",
        "name_",
        "hours_worked",
        "days_worked",
        # "travel_hours",
        # "service_hours",
        # "total_hours",
    )


class ReleaseNotesView(MyModelView):
    """Class for ReleaseNote model"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = True
    can_set_page_size = True

    page_size = 50

    column_list = (
        "version",
        "description",
        "is_stable",
        "timestamp_utc",
        "id",
    )
    column_default_sort = [
        # True = sort descending
        ("version", True),
        ("timestamp_utc", True),
    ]
    column_editable_list = (
        "version",
        "description",
        "is_stable",
        "timestamp_utc",
    )
    column_labels = {
        "version": "Version",
        "description": "Release Notes",
        "is_stable": "Is Stable?",
        "timestamp_utc": "Date Released (UTC)",
    }
    column_descriptions = {
        # "version": "Version",
        "description": "So customers and IJACK employees can read the release notes before deciding whether to upgrade the gateway's software",
        "is_stable": "Is this a stable version, or in development or testing?",
        # "timestamp_utc": "Date Released (UTC)",
    }


class AlarmLogMetricView(MyModelView):
    """Class for AlarmLogMetric model"""

    can_create = True
    can_edit = True
    can_delete = False
    column_display_pk = True
    can_set_page_size = True

    page_size = 50

    column_list = (
        "abbrev",
        "index",
        "name",
        "description",
        "id",
    )
    column_editable_list = (
        "abbrev",
        "index",
        "name",
        "description",
    )
    column_labels = {
        "abbrev": "Abbreviation",
        "index": "Index",
        "name": "Name",
        "description": "Description",
    }
    column_descriptions = {
        "abbrev": "Abbreviation of the alarm log metric",
        "index": "Index of the alarm log metric so different unit types can have different versions (e.g. 0, 1, 2, ...)",
        "name": "Name of the alarm log metric",
        "description": "Description of the alarm log metric that the customer sees in the alarm log",
    }
    column_sortable_list = (
        "abbrev",
        "index",
        "name",
        "description",
        "id",
    )
    # True = sort descending
    column_default_sort = [
        ("abbrev", False),
        ("index", False),
        ("id", False),
    ]
    column_filters = (
        "abbrev",
        "index",
        "name",
        "description",
        "id",
    )
    column_searchable_list = (
        "abbrev",
        "name",
        "description",
    )


class DiagnosticMetricView(MyModelView):
    """Class for DiagnosticMetric model"""

    can_create = True
    can_edit = True
    can_delete = False
    column_display_pk = True
    can_set_page_size = True

    page_size = 50

    column_list = (
        "diag_num",
        "decimals",
        "units",
        "name",
        "color",
        "description",
        "timestamp_utc_modified",
        "id",
    )
    column_editable_list = (
        "diag_num",
        "name",
        "color",
        "description",
        "decimals",
        "units",
    )
    column_labels = {
        "timestamp_utc_modified": "Date Modified (UTC)",
        "diag_num": "Diagnostic Number",
        "name": "Name",
        "description": "Description",
        "decimals": "Decimals",
        "units": "Units",
    }
    column_descriptions = {
        "timestamp_utc_modified": "Date when the diagnostic metric was last modified (UTC)",
        "diag_num": "Diagnostic number identifying the metric on the CAN bus",
        "name": "Name of the diagnostic metric",
        "description": "Description of the diagnostic metric",
        "decimals": "Number of decimal places to display",
        "units": "Units of measurement",
        "color": "Color of the line when plotted in chart on RCOM",
    }
    column_sortable_list = (
        "timestamp_utc_modified",
        "diag_num",
        "name",
        "description",
        "decimals",
        "units",
        "id",
    )
    # True = sort descending
    column_default_sort = [
        ("diag_num", False),
    ]
    column_filters = (
        "timestamp_utc_modified",
        "diag_num",
        "name",
        "description",
        "decimals",
        "units",
        "id",
    )
    column_searchable_list = (
        "diag_num",
        "name",
        "description",
        "units",
    )

    form_overrides = {
        "color": ColorField,
    }

    form_widget_args = {
        "color": {
            # The <input type="color" list="presetColors">
            # where list is a <datalist> in document
            "list": "presetColors",
            "autocomplete": "",
        },
    }


class DiagnosticView(MyModelView):
    """Class for Diagnostic data model"""

    can_create = True
    can_edit = True
    can_delete = False
    column_display_pk = True
    can_set_page_size = True

    page_size = 50

    column_list = (
        "power_unit_str",
        "timestamp_utc",
        "timestamp_utc_modified",
        "is_main",
        "msg_type",
        "diag_num",
        "value",
    )
    column_labels = {
        "power_unit_str": "Power Unit",
        "timestamp_utc": "Datetime (UTC)",
        "timestamp_utc_modified": "Datetime Modified (UTC)",
        "is_main": "Main Unit?",
        "msg_type": "Input Velocity",
        "diag_num": "Diagnostic Number",
        "value": "Value",
    }
    column_descriptions = {
        "timestamp_utc": "Date and time the data were recorded (UTC)",
        "timestamp_utc_modified": "Date and time the data were last modified (UTC)",
        "is_main": "Is this the main unit (i.e. main) or secondary (i.e. slave) unit?",
        "msg_type": "Input Velocity (%)",
        "diag_num": "Diagnostic number identifying the metric on the CAN bus",
        "value": "Value of the diagnostic metric",
    }
    column_sortable_list = (
        "timestamp_utc",
        "timestamp_utc_modified",
        "is_main",
        "msg_type",
        "diag_num",
        "value",
    )
    # True = sort descending
    column_default_sort = [
        ("power_unit_str", False),
        ("is_main", False),
        ("diag_num", False),
        ("timestamp_utc", False),
        ("msg_type", False),
    ]
    column_filters = (
        "power_unit_str",
        "timestamp_utc",
        "timestamp_utc_modified",
        "is_main",
        "msg_type",
        "diag_num",
        "value",
    )
    column_searchable_list = ("power_unit_str",)


def op_hours_formatter(view, context, model, name):
    """Format operating hours nicely with commas and no decimal places"""
    return f"{getattr(model, name):,.0f}"


class MaintenanceView(MyModelView):
    """Class for Maintenance data model"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True

    page_size = 50

    column_list = (
        "structures_rel.power_unit_str",
        "timestamp_utc",
        "timestamp_utc_inserted",
        "description",
        "op_hours",
        "op_months",
        "maintenance_type_rel.name",
        "users_rel.email",
        "id",
    )
    form_columns = (
        "structures_rel",
        "timestamp_utc",
        "description",
        "op_hours",
        "maintenance_type_rel",
        "users_rel",
    )
    column_editable_list = (
        "timestamp_utc",
        "description",
        "op_hours",
    )
    column_labels = {
        "structures_rel.power_unit_str": "Unit",
        "structures_rel": "Unit",
        "timestamp_utc": "Datetime (SK Time)",
        "timestamp_utc_inserted": "Date Inserted (SK Time)",
        "description": "Description",
        "op_hours": "Operating Hours",
        "op_months": "Operating Months (Calculated)",
        "maintenance_type_rel.name": "Maintenance Type",
        "maintenance_type_rel": "Maintenance Type",
        "users_rel.email": "User",
        "users_rel": "User",
    }
    column_descriptions = {
        "timestamp_utc": "Date and time the data were recorded (SK time)",
        "timestamp_utc_inserted": "Date and time the data were inserted into the database (SK time)",
        "description": "Description of the maintenance",
        "op_hours": "Operating hours at time of maintenance",
        "op_months": "Operating months at time of maintenance, calculated as op_hours / (24 * 30.4375)",
        "maintenance_type_rel.name": "Maintenance type recorded",
        "maintenance_type_rel": "Maintenance type recorded",
        "users_rel.email": "User who recorded the maintenance",
        "users_rel": "User who recorded the maintenance",
    }
    column_sortable_list = (
        "structures_rel.power_unit_str",
        "timestamp_utc",
        "timestamp_utc_inserted",
        "description",
        "op_hours",
        "op_months",
        "maintenance_type_rel.name",
        "users_rel.email",
        "id",
    )
    # True = sort descending
    column_default_sort = [
        ("timestamp_utc", False),
    ]
    column_filters = (
        "structures_rel.power_unit_str",
        "timestamp_utc",
        "timestamp_utc_inserted",
        "description",
        "op_hours",
        "op_months",
        "maintenance_type_rel.name",
        "users_rel.email",
        "id",
    )
    column_searchable_list = (
        "structures_rel.power_unit_str",
        "description",
        "maintenance_type_rel.name",
        "users_rel.email",
        "users_rel.first_name",
        "users_rel.last_name",
        "id",
    )

    column_formatters = {
        "op_hours": op_hours_formatter,
        "op_months": lambda v, c, m, n: f"{getattr(m, n):,.1f}",
        "timestamp_utc": datetime_formatter_sk_time,
        "timestamp_utc_inserted": datetime_formatter_sk_time,
    }

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "structures_rel": QueryAjaxModelLoaderAdmin(
            "structures_rel",
            db.session,
            Structure,
            fields=[Structure.structure, PowerUnit.power_unit_str, Structure.surface],
            order_by="structure",
            placeholder="Please select a structure",
            # Need to have this special query function to get the association proxy fields to work
            get_query_func=lambda: Structure.query.join(PowerUnit, isouter=True),
            **options,
        ),
        "maintenance_type_rel": QueryAjaxModelLoader(
            "maintenance_type_rel",
            db.session,
            MaintenanceType,
            fields=["name"],
            order_by="name",
            placeholder="Please select a maintenance type",
            **options,
        ),
        "users_rel": QueryAjaxModelLoader(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            **options,
        ),
    }


class MaintenanceTypeView(MyModelView):
    """Class for MaintenanceType data model"""

    can_create = True
    can_edit = True
    can_delete = False
    column_display_pk = True
    can_set_page_size = True

    page_size = 50

    column_list = (
        "name",
        "description",
    )
    column_labels = {
        "name": "Maintenance Type",
        "description": "Description",
    }
    column_descriptions = {
        "name": "Maintenance type",
        "description": "Description of the maintenance type",
    }
    column_sortable_list = (
        "name",
        "description",
    )
    # True = sort descending
    column_default_sort = [
        ("name", False),
    ]
    column_filters = (
        "name",
        "description",
    )
    column_searchable_list = (
        "name",
        "description",
    )


class VwSalesByPersonMonthView(MyModelView):
    """VwSalesByPersonMonth view"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = False
    can_set_page_size = True
    page_size = 50

    column_list = (
        "service_year",
        "service_month",
        "name_",
        "sales_warranty",
        "sales_total",
        "sales_labour",
        "sales_parts",
        "type_sales",
        "type_rentals",
        "type_new_installs",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
    )
    column_labels = {
        "service_year": "Year",
        "service_month": "Month",
        "name_": "Employee",
        "sales_total": "Total Sales",
        "sales_warranty": "Warranty Sales",
        "sales_labour": "Labour Sales",
        "sales_parts": "Parts Sales",
        "type_new_installs": "New Installs Type",
        "type_sales": "Sales Type",
        "type_repairs": "Repairs Type",
        "type_parts": "Parts Type",
        "type_prevent_maint": "Preventative Maintenance Type",
        "type_rentals": "Rentals Type",
    }
    column_descriptions = {
        "service_year": "Year of the sales",
        "service_month": "Month of the sales",
        # "name_": "Employee",
        "sales_total": "Total sales (i.e. labour + parts etc, no warranty)",
        "sales_warranty": "Warranty sales",
        "sales_labour": "Labour sales (i.e. not parts)",
        "sales_parts": "Parts sales (i.e. not labour)",
        "type_new_installs": "New installs type",
        "type_sales": "Sales type",
        "type_repairs": "Repairs type",
        "type_parts": "Parts type",
        "type_prevent_maint": "Preventative maintenance type",
        "type_rentals": "Rentals type",
    }
    column_sortable_list = (
        "service_year",
        "service_month",
        "name_",
        "sales_total",
        "sales_warranty",
        "sales_labour",
        "sales_parts",
        "type_new_installs",
        "type_sales",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
        "type_rentals",
    )
    column_default_sort = [
        ("service_year", True),
        ("service_month", True),
        ("sales_total", True),
        ("name_", True),
    ]
    column_filters = (
        "service_year",
        "service_month",
        "name_",
        "sales_total",
        "sales_warranty",
        "sales_labour",
        "sales_parts",
        "type_new_installs",
        "type_sales",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
        "type_rentals",
    )
    column_searchable_list = (
        "service_year",
        "service_month",
        "name_",
    )
    column_formatters = {
        "service_year": lambda v, c, m, n: f"{m.service_year:.0f}",
        "service_month": lambda v, c, m, n: f"{m.service_month:.0f}",
        "sales_total": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_warranty": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_labour": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_parts": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_new_installs": get_formatter(
            decimals=0, right_align=True, dollar_sign=True
        ),
        "type_sales": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_repairs": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_parts": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_prevent_maint": get_formatter(
            decimals=0, right_align=True, dollar_sign=True
        ),
        "type_rentals": get_formatter(decimals=0, right_align=True, dollar_sign=True),
    }


class VwSalesByPersonQuarterView(MyModelView):
    """VwSalesByPersonQuarter model view"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = False
    can_set_page_size = True
    page_size = 50

    column_list = (
        "service_year",
        "service_quarter",
        "name_",
        "sales_warranty",
        "sales_total",
        "sales_labour",
        "sales_parts",
        "type_sales",
        "type_rentals",
        "type_new_installs",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
    )
    column_labels = {
        "service_year": "Year",
        "service_quarter": "Quarter",
        "name_": "Employee",
        "sales_total": "Total Sales",
        "sales_warranty": "Warranty Sales",
        "sales_labour": "Labour Sales",
        "sales_parts": "Parts Sales",
        "type_new_installs": "New Installs Type",
        "type_sales": "Sales Type",
        "type_repairs": "Repairs Type",
        "type_parts": "Parts Type",
        "type_prevent_maint": "Preventative Maintenance Type",
        "type_rentals": "Rentals Type",
    }
    column_descriptions = {
        "service_year": "Year of the sales",
        "service_quarter": "Quarter when the sales took place",
        # "name_": "Employee",
        "sales_total": "Total sales (i.e. labour + parts etc, no warranty)",
        "sales_warranty": "Warranty sales",
        "sales_labour": "Labour sales (i.e. not parts)",
        "sales_parts": "Parts sales (i.e. not labour)",
        "type_new_installs": "New installs type",
        "type_sales": "Sales type",
        "type_repairs": "Repairs type",
        "type_parts": "Parts type",
        "type_prevent_maint": "Preventative maintenance type",
        "type_rentals": "Rentals type",
    }
    column_sortable_list = (
        "service_year",
        "service_quarter",
        "name_",
        "sales_total",
        "sales_warranty",
        "sales_labour",
        "sales_parts",
        "type_new_installs",
        "type_sales",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
        "type_rentals",
    )
    column_default_sort = [
        ("service_year", True),
        ("service_quarter", True),
        ("sales_total", True),
        ("name_", True),
    ]
    column_filters = (
        "service_year",
        "service_quarter",
        "name_",
        "sales_total",
        "sales_warranty",
        "sales_labour",
        "sales_parts",
        "type_new_installs",
        "type_sales",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
        "type_rentals",
    )
    column_searchable_list = (
        "service_year",
        "name_",
    )
    column_formatters = {
        "service_year": lambda v, c, m, n: f"{m.service_year:.0f}",
        "service_quarter": lambda v, c, m, n: f"{m.service_quarter:.0f}",
        "sales_total": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_warranty": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_labour": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_parts": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_new_installs": get_formatter(
            decimals=0, right_align=True, dollar_sign=True
        ),
        "type_sales": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_repairs": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_parts": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_prevent_maint": get_formatter(
            decimals=0, right_align=True, dollar_sign=True
        ),
        "type_rentals": get_formatter(decimals=0, right_align=True, dollar_sign=True),
    }


class VwSalesByPersonYearView(MyModelView):
    """VwSalesByPersonYear model view"""

    can_create = False
    can_edit = False
    can_delete = False
    column_display_pk = False
    can_set_page_size = True
    page_size = 50

    column_list = (
        "service_year",
        "name_",
        "sales_warranty",
        "sales_total",
        "sales_labour",
        "sales_parts",
        "type_sales",
        "type_rentals",
        "type_new_installs",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
    )
    column_labels = {
        "service_year": "Year",
        "name_": "Employee",
        "sales_total": "Total Sales",
        "sales_warranty": "Warranty Sales",
        "sales_labour": "Labour Sales",
        "sales_parts": "Parts Sales",
        "type_new_installs": "New Installs Type",
        "type_sales": "Sales Type",
        "type_repairs": "Repairs Type",
        "type_parts": "Parts Type",
        "type_prevent_maint": "Preventative Maintenance Type",
        "type_rentals": "Rentals Type",
    }
    column_descriptions = {
        "service_year": "Year of the sales",
        # "name_": "Employee",
        "sales_total": "Total sales (i.e. labour + parts etc, no warranty)",
        "sales_warranty": "Warranty sales",
        "sales_labour": "Labour sales (i.e. not parts)",
        "sales_parts": "Parts sales (i.e. not labour)",
        "type_new_installs": "New installs type",
        "type_sales": "Sales type",
        "type_repairs": "Repairs type",
        "type_parts": "Parts type",
        "type_prevent_maint": "Preventative maintenance type",
        "type_rentals": "Rentals type",
    }
    column_sortable_list = (
        "service_year",
        "name_",
        "sales_total",
        "sales_warranty",
        "sales_labour",
        "sales_parts",
        "type_new_installs",
        "type_sales",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
        "type_rentals",
    )
    column_default_sort = [
        ("service_year", True),
        ("sales_total", True),
        ("name_", True),
    ]
    column_filters = (
        "service_year",
        "name_",
        "sales_total",
        "sales_warranty",
        "sales_labour",
        "sales_parts",
        "type_new_installs",
        "type_sales",
        "type_repairs",
        "type_parts",
        "type_prevent_maint",
        "type_rentals",
    )
    column_searchable_list = (
        "service_year",
        "name_",
    )
    column_formatters = {
        "service_year": lambda v, c, m, n: f"{m.service_year:.0f}",
        "sales_total": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_warranty": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_labour": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "sales_parts": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_new_installs": get_formatter(
            decimals=0, right_align=True, dollar_sign=True
        ),
        "type_sales": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_repairs": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_parts": get_formatter(decimals=0, right_align=True, dollar_sign=True),
        "type_prevent_maint": get_formatter(
            decimals=0, right_align=True, dollar_sign=True
        ),
        "type_rentals": get_formatter(decimals=0, right_align=True, dollar_sign=True),
    }


class ErrorLogView(MyModelView):
    """Admin view for error logs"""

    # Cannot create new error logs manually
    can_create = False

    # List view settings
    column_list = [
        "id",
        "timestamp_utc",
        "user_rel",
        "error_type",
        "error_message",
        "request_url",
        "environment",
        "status_code",
        "resolved",
    ]

    column_sortable_list = [
        "id",
        "timestamp_utc",
        "error_type",
        "environment",
        "status_code",
        "resolved",
    ]

    column_default_sort = ("timestamp_utc", True)  # Sort by timestamp_utc descending

    # Formatting
    column_formatters = {
        "error_message": lambda v, c, m, p: m.error_message[:100] + "..."
        if m.error_message and len(m.error_message) > 100
        else m.error_message,
        "request_url": lambda v, c, m, p: m.request_url[-100:]
        if m.request_url and len(m.request_url) > 100
        else m.request_url,
        "timestamp_utc": datetime_formatter_sk_time,
    }

    # Search
    column_searchable_list = [
        "error_type",
        "error_message",
        "request_url",
        "user_agent",
        "client_ip",
        "app_version",
    ]

    # Filters
    column_filters = [
        "user_rel.email",
        FilterEqual("resolved", "Resolved"),
        FilterEqual("environment", "Environment"),
        FilterEqual("status_code", "Status Code"),
        FilterLike("error_type", "Error Type"),
        "timestamp_utc",
    ]

    # Details view
    can_view_details = True
    column_details_list = [
        "id",
        "timestamp_utc",
        "user_id",
        "error_type",
        "error_message",
        "error_traceback",
        "request_method",
        "request_url",
        "request_params",
        "request_body",
        "request_headers",
        "user_agent",
        "client_ip",
        "app_version",
        "environment",
        "status_code",
        "resolved",
        "resolution_notes",
        "resolved_by_user_id",
        "resolved_at_utc",
    ]

    # Edit settings
    can_edit = True
    column_editable_list = ["resolved", "resolution_notes"]
    form_columns = ["resolved", "resolution_notes"]
    form_overrides = {"resolution_notes": TextAreaField}
    form_widget_args = {"resolution_notes": {"rows": 5}}

    # Export settings
    can_export = True
    export_max_rows = 1000
    export_types = ["csv", "xlsx"]

    column_labels = {
        "id": "ID",
        "timestamp_utc": "Timestamp (SK Time)",
        "user_rel": "User",
        "user_rel.email": "User Email",
        "error_type": "Error Type",
        "error_message": "Error Message",
        "error_traceback": "Error Traceback",
        "request_method": "Request Method",
        "request_url": "Request URL",
        "request_params": "Request Params",
        "request_body": "Request Body",
        "request_headers": "Request Headers",
        "user_agent": "User Agent",
        "client_ip": "Client IP",
        "app_version": "App Version",
        "environment": "Environment",
        "status_code": "Status Code",
        "resolved": "Resolved",
        "resolution_notes": "Resolution Notes",
        "resolved_by_user_id": "Resolved By User ID",
        "resolved_at_utc": "Resolved At (UTC)",
    }

    column_extra_row_actions = [
        template.LinkRowAction(
            icon_class="fa fa-check-circle",
            # Calls the .../resolve?id={row_id} view
            # with the row_id from the Jinja template
            # url="resolve/?id={row_id}",
            # Point to the resolve_one endpoint with the ID in the path
            url="resolve_one/{row_id}",
            title="Mark as Resolved",
        ),
    ]

    # Define an action to resolve multiple errors at once
    @expose("/resolve/", methods=["POST"])
    def resolve_view(self):
        """Mark ALL selected errors as resolved (for multiple selected errors in the list view)"""

        # Grab parameters from URL
        self._get_list_extra_args()

        # id_ = get_mdict_item_or_list(request.args, "id")
        # if id_ is None:
        #     flash(
        #         gettext("Can't find the 'id' for the record to be duplicated."), "error"
        #     )
        #     return redirect(url_for(".index_view"))

        model_ids = request.form.getlist("rowid")
        if not model_ids:
            flash("No error logs were selected", "error")
            return redirect(url_for(".index_view"))

        try:
            # Update all selected records
            now = utcnow_naive()
            query = self.model.query.filter(self.model.id.in_(model_ids))
            count = query.update(
                {
                    "resolved": True,
                    "resolved_by_user": getattr(current_user, "id", None),
                    "resolved_at_utc": now,
                },
                synchronize_session=False,
            )

            db.session.commit()
            flash(f"Successfully marked {count} error logs as resolved", "success")
        except Exception as ex:
            flash(f"Failed to resolve error logs: {str(ex)}", "error")
            db.session.rollback()

        return redirect(url_for(".index_view"))

    @expose("/resolve_one/<int:id>", methods=["GET", "POST"])
    def resolve_one_view(self, id, **kwargs):
        """Mark a single error as resolved"""
        # error_log = self.get_one(id)
        error_log = db.session.get(ErrorLog, id)
        if not error_log:
            flash("Error log not found", "error")
            return redirect(url_for(".index_view"))

        if error_log.resolved:
            flash("Error log is already resolved", "info")
        else:
            try:
                error_log.resolved = True
                error_log.resolved_by_user_id = getattr(current_user, "id", None)
                error_log.resolved_at_utc = utcnow_naive()
                db.session.commit()
                flash("Error log marked as resolved", "success")
            except Exception as ex:
                flash(f"Failed to resolve error log: {str(ex)}", "error")
                db.session.rollback()

        return redirect(url_for(".index_view"))


class BuildModelView(MyModelView):
    """Generic Flask-Admin view for Build category models (ModelTypeOption, PowerUnitTypeOption, etc.)"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    page_size = 20

    # Standard columns for all Build models
    column_list = (
        "part_rel",
        "part_rel.part_name",
        "part_rel.description",
        "timestamp_utc_inserted",
        "part_id",
    )

    # Control the order of the columns in the forms
    form_columns = ("part_rel",)

    # Make part_rel editable in list view
    column_editable_list = ("part_rel",)

    # Default sort by part name
    column_default_sort = ("part_rel.part_name", False)  # False = ascending

    # Searchable fields
    column_searchable_list = (
        "part_rel.part_name",
        "part_rel.description",
        "part_rel.part_num",
    )

    # Filterable fields
    column_filters = (
        "part_rel.part_name",
        "part_rel.description",
        "part_rel.part_num",
        "timestamp_utc_inserted",
    )

    # Column formatters
    column_formatters = {
        "timestamp_utc_inserted": datetime_formatter_sk_time,
    }

    # Column labels
    column_labels = {
        "part_rel": "Part",
        "part_rel.part_name": "Part Name",
        "part_rel.description": "Part Description",
        "part_rel.part_num": "Part Number",
        "timestamp_utc_inserted": "Date Added (SK Time)",
        "part_id": "Part ID",
    }

    # Column descriptions
    column_descriptions = {
        "part_rel": "The part associated with this build option",
        "part_rel.part_name": "Name of the part",
        "part_rel.description": "Description of the part",
        "part_rel.part_num": "Part number identifier",
        "timestamp_utc_inserted": "When this record was created (Saskatchewan time)",
        "part_id": "Internal part identifier",
    }


# Specific view classes for each Build model
class ModelTypeOptionView(BuildModelView):
    """Flask-Admin view for ModelTypeOption model"""

    pass


class PowerUnitTypeOptionView(BuildModelView):
    """Flask-Admin view for PowerUnitTypeOption model"""

    pass


class PowerUnitTypeSpeedView(BuildModelView):
    """Flask-Admin view for PowerUnitTypeSpeed model"""

    pass


class PowerUnitTypePowerView(BuildModelView):
    """Flask-Admin view for PowerUnitTypePower model"""

    pass


class PowerUnitTypeVoltageView(BuildModelView):
    """Flask-Admin view for PowerUnitTypeVoltage model"""

    pass


class HourView(MyModelView):
    """Flask-Admin view for the Hour model"""

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    can_set_page_size = True
    page_size = 30
    column_list = (
        "id",
        "hour",
        "hour_ending",
    )
    column_labels = {
        "id": "ID",
        "hour": "Hour",
        "hour_ending": "Hour Ending",
    }
    column_descriptions = {
        "id": "ID of the hour",
        "hour": "Hour of the day (0-23)",
        "hour_ending": "Hour ending of the day (1-24)",
    }
    column_sortable_list = (
        "id",
        "hour",
        "hour_ending",
    )
    column_default_sort = [
        ("hour", True),
    ]
    column_filters = (
        "id",
        "hour",
        "hour_ending",
    )
    column_searchable_list = (
        "id",
        "hour",
        "hour_ending",
    )
    column_editable_list = (
        "hour",
        "hour_ending",
    )
    form_columns = (
        "hour",
        "hour_ending",
    )
