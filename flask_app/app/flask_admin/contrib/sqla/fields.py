from flask_admin.contrib.sqla.fields import InlineModelFormList

from app.flask_admin.form.widgets import WorkOrderFieldListWidget


class WorkOrderInlineModelFormList(InlineModelFormList):
    """This InlineModelFormList will use our custom widget and hide row controls"""

    widget = WorkOrderFieldListWidget()

    # def display_row_controls(self, field):
    #     """Whether to display the edit/delete/duplicate controls"""
    #     return False
    #     return field.get_pk() is not None
