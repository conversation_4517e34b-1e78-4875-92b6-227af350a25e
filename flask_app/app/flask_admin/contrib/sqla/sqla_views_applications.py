from flask_admin.contrib.sqla.ajax import QueryAjaxModelLoader
from markupsafe import Markup
from shared.models.models import Customer

from app import db
from app.config import (
    APPLICATION_BOOST,
    APPLICATION_DGAS,
    APPLICATION_EGAS_CGP,
    APPLICATION_EGAS_GP,
    APPLICATION_VRU,
    APPLICATION_XFER,
)
from app.flask_admin.contrib.sqla.sqla_views import MyModelView
from app.flask_admin.utils import datetime_formatter_sk_time
from app.models.models import User


def discharging_into_emulsion1_or_gas_line2(view, context, model, name):
    """Custom formatter for discharging_into_emulsion1_or_gas_line2"""
    if model.discharging_into_emulsion1_or_gas_line2 == 1:
        return "Emulsion Line"
    elif model.discharging_into_emulsion1_or_gas_line2 == 2:
        return "Gas Line"
    return ""


def pump_set_on_cement1_piles2(view, context, model, name):
    """Custom formatter for pump_set_on_cement1_piles2"""
    if model.pump_set_on_cement1_piles2 == 1:
        return "Cement"
    elif model.pump_set_on_cement1_piles2 == 2:
        return "Piles"
    return ""


def use_ppm1_percent2(view, context, model, name):
    """Custom formatter for use_ppm1_percent2"""
    if model.use_ppm1_percent2 == 1:
        return "PPM"
    elif model.use_ppm1_percent2 == 2:
        return "%"
    return ""


def use_psi1_kpa2_ozsqinch3(view, context, model, name):
    """Custom formatter for use_psi1_kpa2_ozsqinch3"""
    if model.use_psi1_kpa2_ozsqinch3 == 1:
        return "PSI"
    elif model.use_psi1_kpa2_ozsqinch3 == 2:
        return "KPA"
    elif model.use_psi1_kpa2_ozsqinch3 == 3:
        return "Oz/in^2"
    return ""


def use_e3m3d1_mcfd2(view, context, model, name):
    """Custom formatter for use_e3m3d1_mcfd2"""
    if model.use_e3m3d1_mcfd2 == 1:
        return "e3m3/d"
    elif model.use_e3m3d1_mcfd2 == 2:
        return "Mcf/d"
    return ""


def use_celsius1_fahrenheit2(view, context, model, name):
    """Custom formatter for use_celsius1_fahrenheit2"""
    if model.use_celsius1_fahrenheit2 == 1:
        return "Celsius"
    elif model.use_celsius1_fahrenheit2 == 2:
        return "Fahrenheit"
    return ""


def download_uploaded_application_file_link(view, context, model, name):
    """Custom formatter for application_upload_files_rel.file_name"""
    # This link needs to download from the IJACK PostgreSQL database
    return Markup(
        "<br>".join(
            f'{index + 1} - <a href="/download-application-upload-file/{model.id}">{model.file_name}</a>'
            for index, model in enumerate(model.application_upload_files_rel)
        )
    )


class ApplicationView(MyModelView):
    """Flask-Admin view for Application model (public.applications table)"""

    can_create = False
    can_edit = True
    can_delete = True
    column_display_pk = True
    # Duplicate button - make None to disable
    column_extra_row_actions = None
    page_size = 20
    can_set_page_size = True

    # def get_query(self):
    #     """Only view certain applications"""
    #     return self.session.query(self.model).filter(
    #         self.model.application_type_id.in_((APPLICATION_XFER, APPLICATION_BOOST))
    #     )

    column_list = (
        # All unit types
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        # Uploaded files
        "application_upload_files_rel.file_name",
        "id",
    )

    # column_editable_list = (
    #     "customer_rel",
    #     "user_rel",
    # )

    # True == sort descending
    column_default_sort = [
        ("timestamp_utc_inserted", True),
        ("customer_rel.customer", False),
    ]

    column_filters = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_searchable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_sortable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_formatters = {
        "timestamp_utc_inserted": datetime_formatter_sk_time,
        # "alerts_sent_users_rel": many_to_many_formatter,
        # "explanation": preserve_line_breaks,
        "discharging_into_emulsion1_or_gas_line2": discharging_into_emulsion1_or_gas_line2,
        "pump_set_on_cement1_piles2": pump_set_on_cement1_piles2,
        "use_ppm1_percent2": use_ppm1_percent2,
        "use_psi1_kpa2_ozsqinch3": use_psi1_kpa2_ozsqinch3,
        "use_e3m3d1_mcfd2": use_e3m3d1_mcfd2,
        "use_celsius1_fahrenheit2": use_celsius1_fahrenheit2,
        "application_upload_files_rel.file_name": download_uploaded_application_file_link,
    }

    column_labels = {
        "timestamp_utc_inserted": "Datetime Inserted (SK time)",
        "application_types_rel": "Application Type",
        "application_types_rel.name": "Application Type",
        "customer_rel": "Customer",
        "customer_rel.customer": "Customer",
        "company_name": "Company Name",
        "street": "Street",
        "city": "City",
        "province_rel": "Province",
        "province_rel.name": "Province",
        "countries_rel": "Country",
        "countries_rel.country_name": "Country",
        "user_rel": "User",
        "user_rel.email": "User Email",
        "contact_name": "Contact Name",
        "contact_phone": "Contact Phone",
        "contact_email": "Contact Email",
        "install_location": "Install Location",
        "field_contact_name": "Field Contact Name",
        "field_contact_phone": "Field Contact Phone",
        "field_contact_email": "Field Contact Email",
        "project_objective": "Project Objective",
        "current_process_equipment": "Current Process Equipment",
        "current_process_issues": "Current Process Issues",
        "when_need_equipment": "When Need Equipment?",
        # XFER-specific stuff
        "emulsion_coming_from": "Emulsion Coming From",
        "emulsion_discharging_into": "Emulsion Discharging Into",
        # VRU-specific stuff
        "vapour_coming_off_storage": "Vapour Coming Off Storage",
        "vapour_coming_off_vr_tanks": "Vapour Coming Off VR Tanks",
        "vapour_coming_off_other": "Vapour Coming Off Other",
        "vapour_discharging_into": "Vapour Discharging Into",
        "tank_pressure_rating": "Tank Pressure Rating",
        "tank_pressure_desired": "Tank Pressure Desired",
        # EGAS stuff
        "casing_count": "Casing Count",
        "discharging_into_emulsion1_or_gas_line2": "Discharging Into Emulsion or Gas Line",
        "flowline_pressure_on_test": "Flowline Pressure on Test",
        "artificial_lift_system": "Artificial Lift System",
        "separators_installed": "Separators Installed",
        "compressor_b4_separators": "Compressor Before Separators",
        # DGAS stuff
        "well_pumped_off_status": "Well Pumped Off Status",
        "pump_fillage_pct": "Pump Fillage %",
        "formation_pressure": "Formation Pressure",
        "pump_make": "Pump Make",
        "pump_model": "Pump Model",
        "pump_speed_spm": "Pump Speed (SPM)",
        "pump_stroke_length": "Pump Stroke Length",
        "pump_rod_load": "Pump Rod Load",
        "pump_long_stroke": "Pump Long Stroke",
        "pump_set_on_cement1_piles2": "Pump Set on Cement or Piles",
        "pump_base_height": "Pump Base Height",
        "pump_num_rails": "Pump Number of Rails",
        "pump_num_tiedowns": "Pump Number of Tiedowns",
        # Corrosive elements
        "use_ppm1_percent2": "Use PPM or %",
        "h2s": "H2S",
        "co2": "CO2",
        "salinity": "Salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3": "Use PSI, KPA, or Oz/in^2",
        "inlet_pressure_current": "Inlet Pressure Current",
        "inlet_pressure_desired": "Inlet Pressure Desired",
        "discharge_pressure": "Discharge Pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2": "Use e3m3/d or Mcf/d",
        "expected_gas_volume": "Expected Gas Volume",
        "expected_water_volume": "Expected Water Volume",
        "expected_oil_volume": "Expected Oil Volume",
        # Use C or F
        "use_celsius1_fahrenheit2": "Use Celsius or Fahrenheit",
        "inlet_temp": "Inlet Temperature",
        "max_discharge_flowline_temp": "Max Discharge Flowline Temperature",
        "max_ambient_temp": "Max Ambient Temperature",
        # Booleans for sand or parafin
        "sand": "Sand",
        "frac_sand": "Frac Sand",
        "parafin": "Parafin",
        "other_solids": "Other Solids",
        "pipeline_diameter_inlet_inches": "Pipeline Diameter Inlet (inches)",
        "pipeline_diameter_discharge_inches": "Pipeline Diameter Discharge (inches)",
        # Power on site
        "power_available": "Power Available",
        "power_voltage": "Power Voltage",
        "power_phase": "Power Phase",
        "power_amps": "Power Amps",
        "power_grid": "Power Grid",
        "power_generator": "Power Generator",
        # Fuel sources available
        "fuel_diesel": "Diesel Available",
        "fuel_natural_gas": "Natural Gas Available",
        "fuel_propane": "Propane Available",
        # Cellular signal strength
        "cell_signal_good": "Cellular Signal Good",
        # Special metals/coatings needed
        "special_metals_needed": "Special Metals Needed",
        # Special requirements/additional info
        "special_requirements": "Special Requirements/Additional Info",
        # Uploaded files
        "application_upload_files_rel.file_name": "Uploaded Files",
        "application_upload_files_rel": "Uploaded Files",
        "id": "ID",
    }

    column_descriptions = {
        "application_upload_files_rel.file_name": "Click the link to download the file",
        "application_upload_files_rel": "Click the link to download the file",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "customer_rel": QueryAjaxModelLoader(
            "customer_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select the customer that submitted the application",
            **options,
        ),
        "user_rel": QueryAjaxModelLoader(
            "user_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select the user who submitted the application",
            **options,
        ),
    }


class ApplicationXFERView(ApplicationView):
    """View for XFERs only"""

    def get_query(self):
        """Only view certain applications"""
        return self.session.query(self.model).filter(
            self.model.application_type_id.in_((APPLICATION_XFER, APPLICATION_BOOST))
        )

    column_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        # "max_discharge_flowline_temp",
        # "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_grid",
        "power_generator",
        "power_voltage",
        "power_phase",
        "power_amps",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    # column_editable_list = (
    #     "customer_rel",
    #     "user_rel",
    # )

    # True == sort descending
    column_default_sort = [
        ("timestamp_utc_inserted", True),
        ("customer_rel.customer", False),
    ]

    # def get_query(self):
    #     """Only view the alerts sent in 'production' as opposed to 'testing'"""
    #     return self.session.query(self.model).filter(
    #         self.model.dev_test_prd == "production"
    #     )

    column_filters = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_searchable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_sortable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_labels = {
        "timestamp_utc_inserted": "Datetime Inserted (SK time)",
        "application_types_rel": "Application Type",
        "application_types_rel.name": "Application Type",
        "customer_rel": "Customer",
        "customer_rel.customer": "Customer",
        "company_name": "Company Name",
        "street": "Street",
        "city": "City",
        "province_rel": "Province",
        "province_rel.name": "Province",
        "countries_rel": "Country",
        "countries_rel.country_name": "Country",
        "user_rel": "User",
        "user_rel.email": "User Email",
        "contact_name": "Contact Name",
        "contact_phone": "Contact Phone",
        "contact_email": "Contact Email",
        "install_location": "Install Location",
        "field_contact_name": "Field Contact Name",
        "field_contact_phone": "Field Contact Phone",
        "field_contact_email": "Field Contact Email",
        "project_objective": "Project Objective",
        "current_process_equipment": "Current Process Equipment",
        "current_process_issues": "Current Process Issues",
        "when_need_equipment": "When Need Equipment?",
        # XFER-specific stuff
        "emulsion_coming_from": "Emulsion Coming From",
        "emulsion_discharging_into": "Emulsion Discharging Into",
        # VRU-specific stuff
        "vapour_coming_off_storage": "Vapour Coming Off Storage",
        "vapour_coming_off_vr_tanks": "Vapour Coming Off VR Tanks",
        "vapour_coming_off_other": "Vapour Coming Off Other",
        "vapour_discharging_into": "Vapour Discharging Into",
        "tank_pressure_rating": "Tank Pressure Rating",
        "tank_pressure_desired": "Tank Pressure Desired",
        # EGAS stuff
        "casing_count": "Casing Count",
        "discharging_into_emulsion1_or_gas_line2": "Discharging Into Emulsion or Gas Line",
        "flowline_pressure_on_test": "Flowline Pressure on Test",
        "artificial_lift_system": "Artificial Lift System",
        "separators_installed": "Separators Installed",
        "compressor_b4_separators": "Compressor Before Separators",
        # DGAS stuff
        "well_pumped_off_status": "Well Pumped Off Status",
        "pump_fillage_pct": "Pump Fillage %",
        "formation_pressure": "Formation Pressure",
        "pump_make": "Pump Make",
        "pump_model": "Pump Model",
        "pump_speed_spm": "Pump Speed (SPM)",
        "pump_stroke_length": "Pump Stroke Length",
        "pump_rod_load": "Pump Rod Load",
        "pump_long_stroke": "Pump Long Stroke",
        "pump_set_on_cement1_piles2": "Pump Set on Cement or Piles",
        "pump_base_height": "Pump Base Height",
        "pump_num_rails": "Pump Number of Rails",
        "pump_num_tiedowns": "Pump Number of Tiedowns",
        # Corrosive elements
        "use_ppm1_percent2": "Use PPM or %",
        "h2s": "H2S",
        "co2": "CO2",
        "salinity": "Salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3": "Use PSI, KPA, or Oz/in^2",
        "inlet_pressure_current": "Inlet Pressure Current",
        "inlet_pressure_desired": "Inlet Pressure Desired",
        "discharge_pressure": "Discharge Pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2": "Use e3m3/d or Mcf/d",
        "expected_gas_volume": "Expected Gas Volume",
        "expected_water_volume": "Expected Water Volume",
        "expected_oil_volume": "Expected Oil Volume",
        # Use C or F
        "use_celsius1_fahrenheit2": "Use Celsius or Fahrenheit",
        "inlet_temp": "Inlet Temperature",
        "max_discharge_flowline_temp": "Max Discharge Flowline Temperature",
        "max_ambient_temp": "Max Ambient Temperature",
        # Booleans for sand or parafin
        "sand": "Sand",
        "frac_sand": "Frac Sand",
        "parafin": "Parafin",
        "other_solids": "Other Solids",
        "pipeline_diameter_inlet_inches": "Pipeline Diameter Inlet (inches)",
        "pipeline_diameter_discharge_inches": "Pipeline Diameter Discharge (inches)",
        # Power on site
        "power_available": "Power Available",
        "power_voltage": "Power Voltage",
        "power_phase": "Power Phase",
        "power_amps": "Power Amps",
        "power_grid": "Power Grid",
        "power_generator": "Power Generator",
        # Fuel sources available
        "fuel_diesel": "Diesel Available",
        "fuel_natural_gas": "Natural Gas Available",
        "fuel_propane": "Propane Available",
        # Cellular signal strength
        "cell_signal_good": "Cellular Signal Good",
        # Special metals/coatings needed
        "special_metals_needed": "Special Metals Needed",
        # Special requirements/additional info
        "special_requirements": "Special Requirements/Additional Info",
        "id": "ID",
    }


class ApplicationVRUView(ApplicationView):
    """View for VRUs only"""

    def get_query(self):
        """Only view certain applications"""
        return self.session.query(self.model).filter(
            self.model.application_type_id.in_((APPLICATION_VRU,))
        )

    column_list = (
        # All unit types
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    # column_editable_list = (
    #     "customer_rel",
    #     "user_rel",
    # )

    # True == sort descending
    column_default_sort = [
        ("timestamp_utc_inserted", True),
        ("customer_rel.customer", False),
    ]

    column_filters = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_searchable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_sortable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        "expected_water_volume",
        "expected_oil_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "sand",
        "frac_sand",
        "parafin",
        "other_solids",
        "pipeline_diameter_inlet_inches",
        "pipeline_diameter_discharge_inches",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special metals/coatings needed
        "special_metals_needed",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_labels = {
        "timestamp_utc_inserted": "Datetime Inserted (SK time)",
        "application_types_rel": "Application Type",
        "application_types_rel.name": "Application Type",
        "customer_rel": "Customer",
        "customer_rel.customer": "Customer",
        "company_name": "Company Name",
        "street": "Street",
        "city": "City",
        "province_rel": "Province",
        "province_rel.name": "Province",
        "countries_rel": "Country",
        "countries_rel.country_name": "Country",
        "user_rel": "User",
        "user_rel.email": "User Email",
        "contact_name": "Contact Name",
        "contact_phone": "Contact Phone",
        "contact_email": "Contact Email",
        "install_location": "Install Location",
        "field_contact_name": "Field Contact Name",
        "field_contact_phone": "Field Contact Phone",
        "field_contact_email": "Field Contact Email",
        "project_objective": "Project Objective",
        "current_process_equipment": "Current Process Equipment",
        "current_process_issues": "Current Process Issues",
        "when_need_equipment": "When Need Equipment?",
        # XFER-specific stuff
        "emulsion_coming_from": "Emulsion Coming From",
        "emulsion_discharging_into": "Emulsion Discharging Into",
        # VRU-specific stuff
        "vapour_coming_off_storage": "Vapour Coming Off Storage",
        "vapour_coming_off_vr_tanks": "Vapour Coming Off VR Tanks",
        "vapour_coming_off_other": "Vapour Coming Off Other",
        "vapour_discharging_into": "Vapour Discharging Into",
        "tank_pressure_rating": "Tank Pressure Rating",
        "tank_pressure_desired": "Tank Pressure Desired",
        # Corrosive elements
        "use_ppm1_percent2": "Use PPM or %",
        "h2s": "H2S",
        "co2": "CO2",
        "salinity": "Salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3": "Use PSI, KPA, or Oz/in^2",
        "inlet_pressure_current": "Inlet Pressure Current",
        "inlet_pressure_desired": "Inlet Pressure Desired",
        "discharge_pressure": "Discharge Pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2": "Use e3m3/d or Mcf/d",
        "expected_gas_volume": "Expected Gas Volume",
        "expected_water_volume": "Expected Water Volume",
        "expected_oil_volume": "Expected Oil Volume",
        # Use C or F
        "use_celsius1_fahrenheit2": "Use Celsius or Fahrenheit",
        "inlet_temp": "Inlet Temperature",
        "max_discharge_flowline_temp": "Max Discharge Flowline Temperature",
        "max_ambient_temp": "Max Ambient Temperature",
        # Booleans for sand or parafin
        "sand": "Sand",
        "frac_sand": "Frac Sand",
        "parafin": "Parafin",
        "other_solids": "Other Solids",
        "pipeline_diameter_inlet_inches": "Pipeline Diameter Inlet (inches)",
        "pipeline_diameter_discharge_inches": "Pipeline Diameter Discharge (inches)",
        # Power on site
        "power_available": "Power Available",
        "power_voltage": "Power Voltage",
        "power_phase": "Power Phase",
        "power_amps": "Power Amps",
        "power_grid": "Power Grid",
        "power_generator": "Power Generator",
        # Fuel sources available
        "fuel_diesel": "Diesel Available",
        "fuel_natural_gas": "Natural Gas Available",
        "fuel_propane": "Propane Available",
        # Cellular signal strength
        "cell_signal_good": "Cellular Signal Good",
        # Special metals/coatings needed
        "special_metals_needed": "Special Metals Needed",
        # Special requirements/additional info
        "special_requirements": "Special Requirements/Additional Info",
        "id": "ID",
    }


class ApplicationEGASView(MyModelView):
    """Flask-Admin view for Application model (public.applications table)"""

    def get_query(self):
        """Only view certain applications"""
        return self.session.query(self.model).filter(
            self.model.application_type_id.in_(
                (APPLICATION_EGAS_CGP, APPLICATION_EGAS_GP)
            )
        )

    column_list = (
        # All unit types
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        "other_solids",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    # column_editable_list = (
    #     "customer_rel",
    #     "user_rel",
    # )

    # True == sort descending
    column_default_sort = [
        ("timestamp_utc_inserted", True),
        ("customer_rel.customer", False),
    ]

    column_filters = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "other_solids",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_searchable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "other_solids",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_sortable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # XFER-specific stuff
        "emulsion_coming_from",
        "emulsion_discharging_into",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # Corrosive elements
        "use_ppm1_percent2",
        "h2s",
        "co2",
        "salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        # Booleans for sand or parafin
        "other_solids",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_labels = {
        "timestamp_utc_inserted": "Datetime Inserted (SK time)",
        "application_types_rel": "Application Type",
        "application_types_rel.name": "Application Type",
        "customer_rel": "Customer",
        "customer_rel.customer": "Customer",
        "company_name": "Company Name",
        "street": "Street",
        "city": "City",
        "province_rel": "Province",
        "province_rel.name": "Province",
        "countries_rel": "Country",
        "countries_rel.country_name": "Country",
        "user_rel": "User",
        "user_rel.email": "User Email",
        "contact_name": "Contact Name",
        "contact_phone": "Contact Phone",
        "contact_email": "Contact Email",
        "install_location": "Install Location",
        "field_contact_name": "Field Contact Name",
        "field_contact_phone": "Field Contact Phone",
        "field_contact_email": "Field Contact Email",
        "project_objective": "Project Objective",
        "current_process_equipment": "Current Process Equipment",
        "current_process_issues": "Current Process Issues",
        "when_need_equipment": "When Need Equipment?",
        # XFER-specific stuff
        "emulsion_coming_from": "Emulsion Coming From",
        "emulsion_discharging_into": "Emulsion Discharging Into",
        # VRU-specific stuff
        "vapour_coming_off_storage": "Vapour Coming Off Storage",
        "vapour_coming_off_vr_tanks": "Vapour Coming Off VR Tanks",
        "vapour_coming_off_other": "Vapour Coming Off Other",
        "vapour_discharging_into": "Vapour Discharging Into",
        "tank_pressure_rating": "Tank Pressure Rating",
        "tank_pressure_desired": "Tank Pressure Desired",
        # EGAS stuff
        "casing_count": "Casing Count",
        "discharging_into_emulsion1_or_gas_line2": "Discharging Into Emulsion or Gas Line",
        "flowline_pressure_on_test": "Flowline Pressure on Test",
        "artificial_lift_system": "Artificial Lift System",
        "separators_installed": "Separators Installed",
        "compressor_b4_separators": "Compressor Before Separators",
        # DGAS stuff
        "well_pumped_off_status": "Well Pumped Off Status",
        "pump_fillage_pct": "Pump Fillage %",
        "formation_pressure": "Formation Pressure",
        "pump_make": "Pump Make",
        "pump_model": "Pump Model",
        "pump_speed_spm": "Pump Speed (SPM)",
        "pump_stroke_length": "Pump Stroke Length",
        "pump_rod_load": "Pump Rod Load",
        "pump_long_stroke": "Pump Long Stroke",
        "pump_set_on_cement1_piles2": "Pump Set on Cement or Piles",
        "pump_base_height": "Pump Base Height",
        "pump_num_rails": "Pump Number of Rails",
        "pump_num_tiedowns": "Pump Number of Tiedowns",
        # Corrosive elements
        "use_ppm1_percent2": "Use PPM or %",
        "h2s": "H2S",
        "co2": "CO2",
        "salinity": "Salinity",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3": "Use PSI, KPA, or Oz/in^2",
        "inlet_pressure_current": "Inlet Pressure Current",
        "inlet_pressure_desired": "Inlet Pressure Desired",
        "discharge_pressure": "Discharge Pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2": "Use e3m3/d or Mcf/d",
        "expected_gas_volume": "Expected Gas Volume",
        # Use C or F
        "use_celsius1_fahrenheit2": "Use Celsius or Fahrenheit",
        "inlet_temp": "Inlet Temperature",
        "max_discharge_flowline_temp": "Max Discharge Flowline Temperature",
        "max_ambient_temp": "Max Ambient Temperature",
        # Booleans for sand or parafin
        "other_solids": "Other Solids",
        # Power on site
        "power_available": "Power Available",
        "power_voltage": "Power Voltage",
        "power_phase": "Power Phase",
        "power_amps": "Power Amps",
        "power_grid": "Power Grid",
        "power_generator": "Power Generator",
        # Fuel sources available
        "fuel_diesel": "Diesel Available",
        "fuel_natural_gas": "Natural Gas Available",
        "fuel_propane": "Propane Available",
        # Cellular signal strength
        "cell_signal_good": "Cellular Signal Good",
        # Special requirements/additional info
        "special_requirements": "Special Requirements/Additional Info",
        "id": "ID",
    }


class ApplicationDGASView(MyModelView):
    """Flask-Admin view for Application model (public.applications table)"""

    def get_query(self):
        """Only view certain applications"""
        return self.session.query(self.model).filter(
            self.model.application_type_id.in_((APPLICATION_DGAS,))
        )

    column_list = (
        # All unit types
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        # VRU-specific stuff
        "vapour_coming_off_storage",
        "vapour_coming_off_vr_tanks",
        "vapour_coming_off_other",
        "vapour_discharging_into",
        "tank_pressure_rating",
        "tank_pressure_desired",
        # EGAS stuff
        "casing_count",
        "discharging_into_emulsion1_or_gas_line2",
        "flowline_pressure_on_test",
        "artificial_lift_system",
        "separators_installed",
        "compressor_b4_separators",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        "other_solids",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    # column_editable_list = (
    #     "customer_rel",
    #     "user_rel",
    # )

    # True == sort descending
    column_default_sort = [
        ("timestamp_utc_inserted", True),
        ("customer_rel.customer", False),
    ]

    column_filters = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        "artificial_lift_system",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        "other_solids",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_searchable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        "artificial_lift_system",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        "other_solids",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_sortable_list = (
        "timestamp_utc_inserted",
        "application_types_rel.name",
        "customer_rel.customer",
        "company_name",
        "street",
        "city",
        "province_rel.name",
        "countries_rel.country_name",
        "user_rel.email",
        "contact_name",
        "contact_phone",
        "contact_email",
        "install_location",
        "field_contact_name",
        "field_contact_phone",
        "field_contact_email",
        "project_objective",
        "current_process_equipment",
        "current_process_issues",
        "when_need_equipment",
        "artificial_lift_system",
        # DGAS stuff
        "well_pumped_off_status",
        "pump_fillage_pct",
        "formation_pressure",
        "pump_make",
        "pump_model",
        "pump_speed_spm",
        "pump_stroke_length",
        "pump_rod_load",
        "pump_long_stroke",
        "pump_set_on_cement1_piles2",
        "pump_base_height",
        "pump_num_rails",
        "pump_num_tiedowns",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3",
        "inlet_pressure_current",
        "inlet_pressure_desired",
        "discharge_pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2",
        "expected_gas_volume",
        # Use C or F
        "use_celsius1_fahrenheit2",
        "inlet_temp",
        "max_discharge_flowline_temp",
        "max_ambient_temp",
        "other_solids",
        # Power on site
        "power_available",
        "power_voltage",
        "power_phase",
        "power_amps",
        "power_grid",
        "power_generator",
        # Fuel sources available
        "fuel_diesel",
        "fuel_natural_gas",
        "fuel_propane",
        # Cellular signal strength
        "cell_signal_good",
        # Special requirements/additional info
        "special_requirements",
        "id",
    )

    column_labels = {
        "timestamp_utc_inserted": "Datetime Inserted (SK time)",
        "application_types_rel": "Application Type",
        "application_types_rel.name": "Application Type",
        "customer_rel": "Customer",
        "customer_rel.customer": "Customer",
        "company_name": "Company Name",
        "street": "Street",
        "city": "City",
        "province_rel": "Province",
        "province_rel.name": "Province",
        "countries_rel": "Country",
        "countries_rel.country_name": "Country",
        "user_rel": "User",
        "user_rel.email": "User Email",
        "contact_name": "Contact Name",
        "contact_phone": "Contact Phone",
        "contact_email": "Contact Email",
        "install_location": "Install Location",
        "field_contact_name": "Field Contact Name",
        "field_contact_phone": "Field Contact Phone",
        "field_contact_email": "Field Contact Email",
        "project_objective": "Project Objective",
        "current_process_equipment": "Current Process Equipment",
        "current_process_issues": "Current Process Issues",
        "when_need_equipment": "When Need Equipment?",
        "artificial_lift_system": "Artificial Lift System",
        # DGAS stuff
        "well_pumped_off_status": "Well Pumped Off Status",
        "pump_fillage_pct": "Pump Fillage %",
        "formation_pressure": "Formation Pressure",
        "pump_make": "Pump Make",
        "pump_model": "Pump Model",
        "pump_speed_spm": "Pump Speed (SPM)",
        "pump_stroke_length": "Pump Stroke Length",
        "pump_rod_load": "Pump Rod Load",
        "pump_long_stroke": "Pump Long Stroke",
        "pump_set_on_cement1_piles2": "Pump Set on Cement or Piles",
        "pump_base_height": "Pump Base Height",
        "pump_num_rails": "Pump Number of Rails",
        "pump_num_tiedowns": "Pump Number of Tiedowns",
        # VRU's use Oz/inch^2
        "use_psi1_kpa2_ozsqinch3": "Use PSI, KPA, or Oz/in^2",
        "inlet_pressure_current": "Inlet Pressure Current",
        "inlet_pressure_desired": "Inlet Pressure Desired",
        "discharge_pressure": "Discharge Pressure",
        # Use e3m3/d or Mcf/d
        "use_e3m3d1_mcfd2": "Use e3m3/d or Mcf/d",
        "expected_gas_volume": "Expected Gas Volume",
        # Use C or F
        "use_celsius1_fahrenheit2": "Use Celsius or Fahrenheit",
        "inlet_temp": "Inlet Temperature",
        "max_discharge_flowline_temp": "Max Discharge Flowline Temperature",
        "max_ambient_temp": "Max Ambient Temperature",
        # Booleans for sand or parafin
        "sand": "Sand",
        "frac_sand": "Frac Sand",
        "parafin": "Parafin",
        "other_solids": "Other Solids",
        # Power on site
        "power_available": "Power Available",
        "power_voltage": "Power Voltage",
        "power_phase": "Power Phase",
        "power_amps": "Power Amps",
        "power_grid": "Power Grid",
        "power_generator": "Power Generator",
        # Fuel sources available
        "fuel_diesel": "Diesel Available",
        "fuel_natural_gas": "Natural Gas Available",
        "fuel_propane": "Propane Available",
        # Cellular signal strength
        "cell_signal_good": "Cellular Signal Good",
        # Special requirements/additional info
        "special_requirements": "Special Requirements/Additional Info",
        "id": "ID",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "customer_rel": QueryAjaxModelLoader(
            "customer_rel",
            db.session,
            Customer,
            fields=["customer"],
            order_by="customer",
            placeholder="Please select the customer that submitted the application",
            **options,
        ),
        "user_rel": QueryAjaxModelLoader(
            "user_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select the user who submitted the application",
            **options,
        ),
    }
