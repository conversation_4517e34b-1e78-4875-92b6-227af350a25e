from flask_admin.contrib.sqla.ajax import QueryAjaxModelLoader
from flask_login import current_user
from shared.models.models import Customer

from app.utils.complex import get_user_cust_ids


class QueryAjaxModelLoaderAdmin(QueryAjaxModelLoader):
    """Custom AJAX model loader, which is extended for admin customers below"""

    def __init__(self, name, session, model, **options):
        """
        Constructor.

        :param fields:
            Fields to run query against
        :param filters:
            Additional filters to apply to the loader
        :param get_query_func:
            Custom get_query() function, in case you need special joins or something
        """
        self.get_query_func = options.get("get_query_func", None)

        super().__init__(name, session, model, **options)

    def get_one(self, pk):
        # prevent autoflush from occuring during populate_obj
        with self.session.no_autoflush:
            # The following returns an error because get()
            # can't be used on pre-filtered (by customer_id) data
            # return self.get_query().get(pk)

            model = self.session.get(self.model, pk)
            # kwargs = {model_pk: pk}
            # query = self.get_query()
            # query_filtered = query.filter_by(**kwargs)
            # model = query_filtered.first()
            return model

    def get_query(self):
        """If the user supplied a special get_query() function, use that"""
        if self.get_query_func:
            return self.get_query_func()
        return self.session.query(self.model)


class QueryAjaxModelLoaderAdminCust(QueryAjaxModelLoaderAdmin):
    """For customer admins, to ensure they only see their own units"""

    def get_query(self):
        """
        Each model must have a 'customer_id' field
        so we can apply this filter to dropdown menu AJAX queries
        """
        if self.get_query_func:
            # The following returns an error because the model doesn't have a customer_id in call cases.
            # Just trust that the get_query_func will work properly
            # return self.get_query_func().filter(
            #     self.model.customer_id == current_user.customer_id
            # )
            return self.get_query_func()

        # return self.session.query(self.model)
        user_id: int = getattr(current_user, "id", None)
        user_cust_ids: tuple = get_user_cust_ids(user_id=user_id)
        if hasattr(self.model, "customer_id"):
            return self.session.query(self.model).filter(
                self.model.customer_id.in_(user_cust_ids)
            )
        elif hasattr(self.model, "customers_rel"):
            return self.session.query(self.model).filter(
                self.model.customers_rel.has(Customer.id.in_(user_cust_ids))
            )
        elif hasattr(self.model, "can_show_to_customers"):
            return self.session.query(self.model).filter(
                self.model.can_show_to_customers.is_(True)
            )

        raise AttributeError(
            f"Model {self.model.__name__} doesn't have a customer_id or customers_rel attribute"
        )
