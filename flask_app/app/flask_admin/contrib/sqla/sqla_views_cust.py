import os
from gettext import gettext

from flask import current_app, flash, redirect, request, url_for
from flask_admin._compat import iteritems, string_types
from flask_admin.contrib.sqla import ModelView, tools
from flask_admin.contrib.sqla.ajax import QueryAjaxModelLoader
from flask_login import current_user
from shared.models.models import (
    Alert,
    Customer,
    CustSubGroup,
    Gw,
    PowerUnit,
    ReportEmailHourly,
    ReportEmailOpHours,
    ReportEmailOpHoursType,
    Role,
    TimeZone,
    UnitType,
    structure_customer_rel,
)
from shared.models.models_bom import ModelType, Warehouse
from sqlalchemy import func
from wtforms.validators import Email

from app import db, is_admin, is_admin_cust
from app.auth.forms import validate_phone
from app.config import ROLE_ID_CUSTOMER
from app.dashapp.utils import get_or_update_gps
from app.flask_admin.contrib.sqla.ajax import QueryAjaxModelLoaderAdminCust
from app.flask_admin.contrib.sqla.sqla_views import (
    alert_column_descriptions,
    alert_column_labels,
    alert_form_columns,
)
from app.flask_admin.forms import SecureFormCustom
from app.flask_admin.utils import (
    alerts_formatter,
    datetime_formatter_sk_time,
    get_columns_for_field,
    many_to_many_formatter,
    structures_formatter,
    user_phone_num_formatter,
)
from app.flask_admin.widgets import XEditableWidgetAJAX
from app.models.models import Structure, User
from app.utils.complex import (
    get_user_cust_ids,
    getattrd,
    remove_unnecessary_decimal,
    send_new_user_sms,
)
from app.utils.simple import generate_random_string

ALERT_COLUMN_LABELS = {
    "alert_id": "Alert ID",
    "gateway.gateway": "Unit Being Alerted On",
    "users_rel": "User Being Alerted",
    "users_rel.first_name": "First Name",
    "users_rel.last_name": "Last Name",
    "users_rel.phone": "Phone",
    "users_rel.email": "Email",
    "customers_rel": "Customer",
    "name": "Sub-Group",
    "description": "Sub-Group Description",
    "power_units_rel": "Power Unit",
    "power_units_rel.power_unit": "Power Unit",
    "structures_rel": "Structures",
    "surface": "Surface Location",
    "structures_rel.downhole": "Downhole Location",
    "wants_sms": "SMS Alert",
    "users_rel.sms_stop_all": "SMS STOPPED",
    "wants_email": "Email Alert",
    "wants_phone": "Phone Alert",
    "wants_whatsapp": "WhatsApp Alert",
    "users_rel.whatsapp_stop_all": "WhatsApp STOPPED",
    "wants_short_sms": "Want Shorter SMS Message",
    "wants_short_email": "Want Shorter Email Message",
    "wants_short_phone": "Want Shorter Phone Message",
    "heartbeat": "Not Stroking Alert",
    "online_hb": "No Power/Internet Alert",
    "change_suction": "Change Detection Suction Pressure",
    "change_dgp": "Change Detection Discharge Pressure",
    "change_hyd_temp": "Change Detection Hydraulic Temperature",
    "change_hp_delta": "Change Detection Horsepower Upstroke-Downstroke Delta",
    "suction": "High Suction Pressure Alert",
    "discharge": "Low Discharge Pressure Alert",
    "spm": "Low Strokes per Minute Alert",
    "stboxf": "Stuffing Box Float Switch Alert",
    "hyd_temp": "High Hydraulic Temperature Alert",
    "wants_card_ml": "Artificial Intelligence Alert",
    # Hydraulic oil alerts
    "hyd_oil_lvl": "Low Hydraulic Oil Level",
    "hyd_filt_life": "Low Hydraulic Filter Life",
    "hyd_oil_life": "Low Hydraulic Oil Life",
    "chk_mtr_ovld": "Check Motor Overload",
    "pwr_fail": "Power Failure",
    "soft_start_err": "Soft Start Error",
    "grey_wire_err": "Grey Wire Error",
    "ae011": "Check Motor Rotation, No Pressure",
}

ALERT_COLUMN_DESCRIPTIONS = {
    "structures_rel": "IDs of the structure(s) on which the RCOM gateway sits",
    "users_rel": "User who is being alerted",
    "wants_sms": "Select if you want an SMS alert",
    "users_rel.sms_stop_all": "The user has replied 'STOP' to an IJACK alert, so no alerts will be sent until the user sends 'START' to ************",
    "wants_whatsapp": "Select if you want a WhatsApp alert",
    "users_rel.whatsapp_stop_all": "The user has replied 'STOP' to an IJACK WhatsApp alert, so no alerts will be sent until the user sends 'START' to ************ on WhatsApp",
    "wants_email": "Select if you want an email alert",
    "wants_phone": "Select if you want a phone call alert",
    "wants_short_sms": "Select if you want a shorter version of the SMS alert",
    "wants_short_email": "Select if you want a shorter version of the email alert (usually a longer email is better, actually)",
    "wants_short_phone": "Select if you want a shorter version of the phone call alert (most customers want this)",
    "heartbeat": "Select if you want to be notified when the unit stops stroking (all customers want this)",
    "online_hb": "Select if you want to be notified when the unit loses power or internet connection (most customers want this)",
    "change_suction": "Select if you want to be notified when the suction pressure goes above its normal range (useful)",
    "change_dgp": "Select if you want to be notified when the discharge pressure goes below its normal range (useful)",
    "change_hyd_temp": "Select if you want to be notified when the hydraulic temperature goes above its normal range (not many customers use this)",
    "change_hp_delta": "Select if you want to be notified when the upstroke horsepower is much greater than the downstroke horsepower, or vice versa. This is useful for early detection of check valve issues.",
    "suction": "Select if you want to be notified when the suction pressure goes above a certain threshold (threshold is set in 'Units/Unit Information')",
    "discharge": "Select if you want to be notified when the discharge pressure goes down to a certain threshold (typically zero; set in 'Units/Unit Information')",
    "spm": "Select if you want to be notified when 'strokes per minute' goes down to a certain threshold (typically zero; set in 'Units/Unit Information')",
    "stboxf": "Select if you want to be notified when 'stuffing box float switch' is triggered",
    "hyd_temp": f"Select if you want to be notified when hydraulic temperature goes above to a certain threshold (typically 65{chr(176)} Celsius; set in 'Units/Unit Information')",
    "wants_card_ml": "Select if you want to be notified when IJACK's artificial intelligence algorithm detects problems with surface/compression cards",
    # Hydraulic oil alerts
    "hyd_oil_lvl": "Select if you want to be notified when the hydraulic oil level is below a certain threshold (%)",
    "hyd_filt_life": "Select if you want to be notified when the hydraulic filter life is below a certain threshold (%)",
    "hyd_oil_life": "Select if you want to be notified when the hydraulic oil life is below a certain threshold (%)",
    "chk_mtr_ovld": "Select if you want to be notified when the motor overload is triggered",
    "pwr_fail": "Select if you want to be notified when the power fails",
    "soft_start_err": "Select if you want to be notified when the soft start error is triggered",
    "grey_wire_err": "Select if you want to be notified when the grey wire error is triggered",
    "ae011": "Select if you want to be notified when the 'check motor rotation, no pressure' flag is triggered",
}

ALERT_FORM_COLUMNS = (
    "users_rel",
    "power_units_rel",
    "wants_sms",
    "wants_email",
    "wants_phone",
    "wants_short_sms",
    "wants_short_email",
    "wants_short_phone",
    "wants_whatsapp",
    "heartbeat",
    "online_hb",
    "spm",
    "stboxf",
    "hyd_temp",
    "suction",
    "discharge",
    "change_suction",
    "change_dgp",
    "change_hyd_temp",
    "change_hp_delta",
    "wants_card_ml",
    # Hydraulic oil alerts
    "hyd_oil_lvl",
    "hyd_filt_life",
    # Hydraulic oil life isn't proven yet, so default it to False
    # "hyd_oil_life",
    "chk_mtr_ovld",
    "pwr_fail",
    "soft_start_err",
    "grey_wire_err",
    "ae011",
)


class ModelViewAdminCust(ModelView):
    """
    Customized model view for Flask-Admin page (for database tables)
    https://flask-admin.readthedocs.io/en/latest/introduction/#
    """

    def __init__(self, *args, **kwargs):
        """Initialize the view with the given model"""

        # Enable CSRF protection for Flask-Admin forms in production and when testing with CSRF enabled
        try:
            if os.getenv("FLASK_CONFIG", "production") in ("production", "staging") or (
                current_app and current_app.config.get("TEST_CSRF", False)
            ):
                self.form_base_class = SecureFormCustom
        except RuntimeError:
            # No application context available, skip CSRF configuration
            pass

        # Get current page size options as list so we can modify it
        self._page_size_options = list(
            getattr(self, "page_size_options", (20, 50, 100))
        )

        # Get configured page size
        self._page_size = getattr(self, "page_size", 20)

        # If can_set_page_size is True and page_size isn't in options, add it so we don't see a warning
        if (
            getattr(self, "can_set_page_size", False)
            and self._page_size not in self._page_size_options
        ):
            # Add the page size
            self._page_size_options.append(self._page_size)
            # Sort the options for a clean dropdown
            self._page_size_options.sort()
            # Convert back to tuple
            self.page_size_options = tuple(self._page_size_options)

        super().__init__(
            # Change this so we can separate the Flask-Admin assets from the rest of the site
            # static_folder=DIST_FOLDER.joinpath("flask_admin/templates/bootstrap4/admin"),
            # static_folder=DIST_FOLDER,
            *args,
            **kwargs,
        )

    def is_accessible(self):
        """The user must be both authenticated, and an admin user to view these pages"""
        return current_user.is_authenticated and (is_admin_cust() or is_admin())

    def inaccessible_callback(self, name, **kwargs):
        # redirect to login page if user doesn't have access
        return redirect(url_for("dash.login", next=request.url))

    # Custom templates
    list_template = "admin/model/list_extended.html"
    # # Even if we're using a popup modal, we still need to update the regular create form since it's available to the user
    # create_template = "admin/model/create_custom.html"
    # create_modal_template = "admin/model/modals/create_custom.html"
    # # Even if we're using a popup modal, we still need to update the regular edit form since it's available to the user
    # edit_template = "admin/model/edit_custom.html"
    # edit_modal_template = "admin/model/modals/edit_custom.html"

    # To disable some of the CRUD operations, set any of these boolean parameters
    # The only things a customer can 'create' are customer sub-groups, or new users of the same customer,
    # but I have a custom registration form for that
    can_create = False
    can_edit = True
    can_delete = True
    can_set_page_size = True
    page_size = 50

    # To enable csv export of the model view
    can_export = False

    # If your model has too much data to display in the list view, you can add a read-only details view by setting
    can_view_details = False  # no need for customers to see the password hash, their customer, or the "is_admin" or "role" fields

    # Or, have the add & edit forms display inside a modal window on the list page, instead of the dedicated create & edit pages
    # These are popup windows that focus your attention when
    # editing or creating table records. I think they're cool
    create_modal = True
    edit_modal = True

    def init_search(self):
        """
        Initialize search. Returns `True` if search is supported for this
        view.

        For SQLAlchemy, this will initialize internal fields: list of
        column objects used for filtering, etc.
        """
        if self.column_searchable_list:
            self._search_fields = []

            for name in self.column_searchable_list:
                attr, joins = tools.get_field_with_path(self.model, name)

                if not attr:
                    raise Exception("Failed to find field for search field: %s" % name)

                if tools.is_hybrid_property(self.model, name):
                    column = attr
                    if isinstance(name, string_types):
                        column.key = name.split(".")[-1]
                    self._search_fields.append((column, joins))
                else:
                    # I changed the below to make it more accommodating
                    # for column in tools.get_columns_for_field(attr):
                    for column in get_columns_for_field(attr):
                        self._search_fields.append((column, joins))

        return bool(self.column_searchable_list)

    def get_list_form(self):
        """Override this function and supply my own CustomWidget with AJAX"""
        if self.form_args:
            # get only validators, other form_args can break FieldList wrapper
            validators = dict(
                (key, {"validators": value["validators"]})
                for key, value in iteritems(self.form_args)
                if value.get("validators")
            )
        else:
            validators = None
        return self.scaffold_list_form(
            widget=XEditableWidgetAJAX(), validators=validators
        )


def cust_sub_group_filtering_function():
    """Ensures a user can only see the customer-sub-groups owned by his/her company"""

    user_cust_ids: tuple = get_user_cust_ids(user_id=getattr(current_user, "id", None))
    query = CustSubGroup.query.filter(CustSubGroup.customer_id.in_(user_cust_ids))
    # current_app.logger.debug(query.statement)
    return query


def structures_filtering_function():
    """Ensures a user can only see the units owned by his/her company"""

    user_cust_ids: tuple = get_user_cust_ids(user_id=getattr(current_user, "id", None))
    query = (
        Structure.query.join(structure_customer_rel)
        .join(PowerUnit, isouter=True)
        .filter(structure_customer_rel.c.customer_id.in_(user_cust_ids))
    )
    # current_app.logger.debug(query.statement)
    return query


def power_units_filtering_function():
    """Ensures a user can only see the units owned by his/her company"""

    user_cust_ids: tuple = get_user_cust_ids(user_id=getattr(current_user, "id", None))
    query = (
        PowerUnit.query.join(Structure, isouter=True)
        .join(structure_customer_rel)
        .filter(structure_customer_rel.c.customer_id.in_(user_cust_ids))
    )
    # current_app.logger.debug(query.statement)
    return query


def gateway_filtering_function():
    """Ensures a user can only see the units owned by his/her company"""

    user_cust_ids: tuple = get_user_cust_ids(user_id=getattr(current_user, "id", None))
    query = (
        Gw.query.join(PowerUnit)
        .join(Structure)
        .join(structure_customer_rel)
        .filter(structure_customer_rel.c.customer_id.in_(user_cust_ids))
    )
    # current_app.logger.debug(query.statement)
    return query


def user_filtering_function():
    """Ensures a user can only see the users owned by his/her company"""

    user_cust_ids: tuple = get_user_cust_ids(user_id=getattr(current_user, "id", None))
    query = User.query.filter(User.customer_id.in_(user_cust_ids))
    # current_app.logger.debug(query.statement)
    return query


def alert_filtering_function():
    """Ensures a user can only see the alerts owned by his/her company"""

    user_cust_ids: tuple = get_user_cust_ids(user_id=getattr(current_user, "id", None))
    query = (
        Alert.query.join(User)
        .join(PowerUnit)
        .join(Structure)
        .join(structure_customer_rel)
        .filter(structure_customer_rel.c.customer_id.in_(user_cust_ids))
        .filter(User.customer_id.in_(user_cust_ids))
    )
    # current_app.logger.debug(query.statement)
    return query


def unit_type_filtering_function():
    """Ensures a user can't see the 'test' unit types"""
    return UnitType.query.filter(UnitType.can_show_to_customers.is_(True))


def model_type_filtering_function():
    """Ensures a user can't see the 'test' model types"""
    return ModelType.query.filter(ModelType.can_show_to_customers.is_(True))


def warehouse_filtering_function():
    """Ensures a user can't see the 'test' warehouses"""
    return Warehouse.query.filter(Warehouse.can_show_to_customers.is_(True))


def timezone_filtering_function():
    """Ensures a user can't see the 'test' timezones"""
    return TimeZone.query.filter(TimeZone.can_show_to_customers.is_(True))


class CustSubGroupViewAdminCust(ModelViewAdminCust):
    """Flask-Admin view for Customer sub-group model (cust_sub_groups table)"""

    def get_query(self):
        """Customer-admin must only be able to see stuff for his own customer_id"""
        user_cust_ids: tuple = get_user_cust_ids(
            user_id=getattr(current_user, "id", None)
        )
        query = (
            db.session.query(CustSubGroup)
            .join(Customer)
            .filter(Customer.id.in_(user_cust_ids))
        )
        return query

    def get_count_query(self):
        """
        Customer-admin must only be able to see stuff for his own customer_id.
        If the 'get_query()' function is being modified, the 'get_count_query()'
        function must also be modified.

        We still need this for the customers' structure view because each structure
        can have more than one customer.
        """
        count_query = self.get_query().with_entities(func.count())
        return count_query

    # The customers control these groups themselves
    can_create = True  # There's a separate form for creating sub-groups
    can_edit = True
    can_delete = True

    column_list = (
        "name",
        "abbrev",
        "description",
        "structures_rel",
    )
    column_editable_list = ("name", "abbrev", "description")
    # Control the order of the columns in the forms
    form_columns = (
        "name",
        "abbrev",
        "description",
        "structures_rel",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = ("name", "abbrev", "description")
    column_filters = ("name", "abbrev", "description")

    column_default_sort = "name"

    column_descriptions = {
        "structures_rel": "Units (pumps) included in the customer-defined sub-group",
        "name": "Customer-defined name of the sub-group (e.g. 'Bakken Operators')",
        "abbrev": "Abbreviated sub-group name (e.g. 'Bakken')",
        "description": "Description of the customer sub-group (e.g. 'Operators working in the Bakken region')",
    }
    column_labels = {
        "structures_rel": "Units in Sub-Group",
        "name": "Sub-Group Name",
        "abbrev": "Abbreviated Name",
        "description": "Sub-Group Description",
    }

    column_formatters = {
        "structures_rel": remove_unnecessary_decimal,
    }

    # form_overrides = {
    #     # QuerySelectField has a "query_factory" parameter
    #     "structures_rel": QuerySelectField
    # }

    # # The "query_factory" allows us to pass a custom filtering function,
    # # to ensure a customer only sees its own units/customers/groups
    # # We don't need this if we're using form_ajax_refs!!! In fact, it'll cause an error in the "edit" modal.
    # form_args = {
    #     structures_rel={query_factory=structures_filtering_function),
    # )

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        # Units in the customer cub-group
        "structures_rel": QueryAjaxModelLoaderAdminCust(
            "structures_rel",
            db.session,
            Structure,
            fields=[
                "structure_str",
                Structure.surface,
                Structure.downhole,
                PowerUnit.power_unit_str,
            ],
            order_by=Structure.structure,
            placeholder="Please select units in this group",
            get_query_func=structures_filtering_function,
            **options,
        ),
    }

    def on_model_change(self, form, model, is_created):
        """When the model changes, ensure the customer_id is set"""

        if is_created:
            user_cust_ids: tuple = get_user_cust_ids(
                user_id=getattr(current_user, "id", None)
            )
            if len(user_cust_ids) > 0:
                model.customer_id = user_cust_ids[0]
            else:
                raise AttributeError(
                    "User doesn't have a customer_id associated with him/her, so can't set the customer_id for the CustSubGroup model!"
                )


class GwViewAdminCust(ModelViewAdminCust):
    """
    Flask-Admin view for Gw model (i.e. the 'units' or public.gw table)
    """

    def get_query(self):
        """Customer-admin must only be able to see stuff for his own customer_id"""
        user_cust_ids: tuple = get_user_cust_ids(
            user_id=getattr(current_user, "id", None)
        )
        query = (
            db.session.query(Gw)
            .join(PowerUnit)
            .join(Structure)
            .join(structure_customer_rel)
            .filter(structure_customer_rel.c.customer_id.in_(user_cust_ids))
        )
        return query

    def get_count_query(self):
        """
        Customer-admin must only be able to see stuff for his own customer_id.
        If the 'get_query()' function is being modified, the 'get_count_query()'
        function must also be modified.

        We still need this for the customers' structure view because each structure
        can have more than one customer.
        """
        count_query = self.get_query().with_entities(func.count())
        return count_query

    # The customers edit these units themselves, but we create them currently
    can_create = False
    can_edit = True
    can_delete = False
    column_display_pk = False

    column_list = (
        # "cust_sub_groups_rel",
        "power_units_rel",
        "structures_rel",
        "structures_rel.surface",
        "structures_rel.downhole",
        "structures_rel.model_types_rel",
        "imei",
        "gateway",
        # "apn",
        # "users_rel",
    )
    column_editable_list = (
        # 'structures_rel.surface', 'structures_rel.downhole', # These don't seem to work. Maybe need an "inline model?"
        # 'cust_sub_groups_rel',
        # 'change_detect_sens',
    )

    # Control the order of the columns in the forms
    form_columns = (
        # 'structures_rel.surface', 'structures_rel.downhole', # These don't seem to work. Maybe need an "inline model?"
        # "users_rel",
        # "apn",
        "imei",
    )

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "gateway",
        "structures_rel",
        "power_units_rel",
        "structures_rel.model_types_rel",
        "structures_rel.surface",
        "structures_rel.downhole",
        "imei",
    )
    column_filters = (
        "gateway",
        "structures_rel",
        "power_units_rel",
        "structures_rel.model_types_rel",
        "imei",
    )
    column_default_sort = "structures_rel.surface"

    page_size = 50

    column_labels = {
        "gateway_types_rel": "Gateway Type",
        # users_rel: "Users with Remote-Control Permissions",
        "cust_sub_groups_rel": "Group (Optional)",
        "gateway": "Gateway Name",
        "power_units_rel": "Power Unit Serial",
        "structures_rel": "Structures",
        # "apn": "Access Point Name",
        # "well_license": "Well License",
        "structures_rel.downhole": "Downhole Land Location",
        "structures_rel.surface": "Surface Land Location",
        "structures_rel.model_types_rel": "Model",
        "imei": "IMEI",
    }
    column_descriptions = {
        "gateway_types_rel": "Either Axiomtek, FATBOX, STW, or Eurotech (old)",
        # users_rel: "Users allowed to 'remote-control' or set targets for this unit",
        "structures_rel.downhole": "Downhole land location for UNO/UNOGAS/EGAS (combines with 'surface' location)",
        "structures_rel.surface": "Surface land location",
        "imei": "International Mobile Equipment Identity",
        "cust_sub_groups_rel": "Customer-defined group in which unit is categorized, for filtering on the main page",
        "gateway": "Name of the internet-connected gateway that transmits data to the cloud",
        "power_units_rel": "Serial number of the power unit cabinet",
        "structures_rel": "Structure IDs of attached units (e.g. a UNOGAS has a UNO structure and an EGAS structure)",
    }

    column_formatters = {
        "power_units_rel": remove_unnecessary_decimal,
        "structures_rel": remove_unnecessary_decimal,
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "structures_rel": QueryAjaxModelLoaderAdminCust(
            "structures_rel",
            db.session,
            Structure,
            fields=[
                "structure_str",
                Structure.surface,
                Structure.downhole,
                PowerUnit.power_unit_str,
            ],
            order_by=Structure.structure,
            placeholder="Please select units in this group",
            get_query_func=structures_filtering_function,
            **options,
        ),
        "power_units_rel": QueryAjaxModelLoaderAdminCust(
            "power_units_rel",
            db.session,
            PowerUnit,
            fields=["power_unit_str"],
            order_by=PowerUnit.power_unit,
            placeholder="Please select a power unit",
            get_query_func=power_units_filtering_function,
            **options,
        ),
    }


class PowerUnitViewAdminCust(ModelViewAdminCust):
    """Flask-Admin view for PowerUnit model (public.power_units table)"""

    def get_query(self):
        """Customer-admin must only be able to see stuff for his own customer_id"""
        user_cust_ids: tuple = get_user_cust_ids(
            user_id=getattr(current_user, "id", None)
        )
        query = (
            db.session.query(PowerUnit)
            .join(Structure)
            .join(structure_customer_rel)
            .filter(structure_customer_rel.c.customer_id.in_(user_cust_ids))
        )
        return query

    def get_count_query(self):
        """
        Customer-admin must only be able to see stuff for his own customer_id.
        If the 'get_query()' function is being modified, the 'get_count_query()'
        function must also be modified.

        We still need this for the customers' structure view because each structure
        can have more than one customer.
        """
        count_query = self.get_query().with_entities(func.count())
        return count_query

    # # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    # column_exclude_list = ['password_hash', ]
    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = True
    # column_hide_backrefs = False
    column_default_sort = ("power_unit", False)  # True == sort descending
    can_set_page_size = True
    # For my custom index view function, to start on the page paginated page
    start_on_last_page = True
    page_size = 100
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        # This view takes a long time to open in development mode...
        page_size = 5

    column_list = (
        "power_unit",
        "structures_rel",
        "structures_rel.surface",
        "structures_rel.downhole",
        "power_unit_type_rel.name",
        "run_mfg_date",
        # "structures_rel.notes_1",
        # "notes",
        # "customers_rel.customer",
        "structures_rel.model_types_rel.model",
        "gateways_rel",
        "modbus_networks_rel",
        # "id",
    )
    column_sortable_list = (
        "power_unit",
        "power_unit_type_rel.name",
        "run_mfg_date",
        # "structures_rel.notes_1",
        # "notes",
        # "customers_rel.customer",
        "structures_rel.model_types_rel.model",
        # "structures_rel",
        # "gateways_rel",
        # "modbus_networks_rel",
        # "id",
    )

    # Control the order of the columns in the forms
    form_columns = (
        # "power_unit",
        # "power_unit_type_rel",
        # "run_mfg_date",
        # "structures_rel",  # Yes, we can change this from here!
        # "alerts_edge",
        # "heartbeat_enabled",
        # "online_hb_enabled",
        "suction",
        "discharge",
        "spm",
        "hyd_temp",
        "change_detect_sens",
        "wait_time_mins",
        "wait_time_mins_ol",
        "wait_time_mins_suction",
        "wait_time_mins_discharge",
        "wait_time_mins_spm",
        "wait_time_mins_stboxf",
        # Hydraulic oil life isn't proven yet, so default it to False
        # "wait_time_mins_hyd_oil_life",
        "hyd_oil_lvl_thresh",
        "hyd_filt_life_thresh",
        # Hydraulic oil life isn't proven yet, so default it to False
        # "hyd_oil_life_thresh",
        "wait_time_mins_hyd_temp",
        "wait_time_mins_hyd_oil_lvl",
        "wait_time_mins_hyd_filt_life",
        "wait_time_mins_ae011",
    )
    column_editable_list = []

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "power_unit_str",
        "power_unit_type_rel.name",
        "structures_rel",
        "structures_rel.model_types_rel.model",
        "modbus_networks_rel.ip_address",
        "modbus_networks_rel.gateway",
    )

    column_filters = (
        "id",
        "power_unit",
        "power_unit_type_rel.name",
        "run_mfg_date",
        "structures_rel",
        "modbus_networks_rel.ip_address",
        "modbus_networks_rel.subnet",
        "modbus_networks_rel.gateway",
    )

    column_labels = {
        "power_unit": "Power Unit Serial",
        "power_unit_str": "Power Unit Serial",
        "power_unit_type_rel.name": "Power Unit Type",
        "run_mfg_date": "Date Manufactured",
        "notes": "Notes",
        "structures_rel": "Structure IDs",
        "structures_rel.notes_1": "Structure Notes",
        "structures_rel.model_types_rel.model": "Model",
        "structures_rel.surface": "Surface Land Location",
        "structures_rel.downhole": "Downhole Land Location",
        "customers_rel": "Customer",
        "customers_rel.customer": "Customers",
        "modbus_networks_rel": "Modbus Networks",
        "modbus_networks_rel.ip_address": "Modbus IP Address",
        "modbus_networks_rel.subnet": "Modbus Subnet",
        "modbus_networks_rel.gateway": "Modbus Gateway",
        "power": "Power",
        "voltage": "Voltage",
        "gateways_rel": "Gateway",
        "alerts_rel": "Alerts",
        "apn": "Access Point Name",
        "alerts_edge": "Alerts from Gateway",
        "heartbeat_enabled": "Heartbeat Enabled",
        "online_hb_enabled": "Heartbeat (Online) Enabled",
        "change_detect_sens": "Change Detection Sensitivity",
        "wait_time_mins": "Alerting Wait Time Mins if Offline",
        "wait_time_mins_ol": "Alerting Wait Time Mins if Online",
        "wait_time_mins_suction": "Alerting Wait Time Mins for Suction Pressure",
        "wait_time_mins_discharge": "Alerting Wait Time Mins for Discharge Pressure",
        "wait_time_mins_spm": "Alerting Wait Time Mins for SPM",
        "wait_time_mins_stboxf": "Alerting Wait Time Mins for Stuffing Box Float Switch",
        "wait_time_mins_hyd_temp": "Alerting Wait Time Mins for Hydraulic Temperature",
        # Hydraulic oil/filter alerts
        "wait_time_mins_hyd_oil_lvl": "Alerting Wait Time Mins for Hydraulic Oil Level",
        "wait_time_mins_hyd_filt_life": "Alerting Wait Time Mins for Hydraulic Filter Life",
        "wait_time_mins_hyd_oil_life": "Alerting Wait Time Mins for Hydraulic Oil Life",
        "spm": "Strokes per Minute Threshold",
        "hyd_temp": "Hydraulic Temperature Threshold",
        "suction": "Suction Pressure Threshold",
        "discharge": "Discharge Pressure Threshold",
        # Hydraulic oil/filter alerts
        "hyd_oil_lvl_thresh": "Hydraulic Oil Level Threshold",
        "hyd_filt_life_thresh": "Hydraulic Filter Life Threshold",
        "hyd_oil_life_thresh": "Hydraulic Oil Life Threshold",
        "wait_time_mins_ae011": "Alerting Wait Time Mins for 'check motor rotation, no pressure'",
    }
    column_descriptions = {
        # power_unit": "Amazon Web Services (AWS) IoT Unit ID for the AWS device shadow",
        "power_unit_type_rel": "The type of power unit (select from list)",
        "power_unit_type_rel.name": "The type of power unit (select from list)",
        "run_mfg_date": "Date manufactured in the shop",
        "power": "Horsepower",
        "structures_rel": "Structures for which this power unit provides power",
        "customers_rel.customer": "One unit can potentially be co-owned by several customers (including the 'demo' customer)",
        "structures_rel.notes_1": "Notes from the structures table",
        "gateways_rel": "Gateway that's linked to this power unit",
        "alerts_rel": "Alerts for this power unit",
        "apn": "Access Point Name for SIM card to connect to customer's Internet Service Provider (ISP)",
        "modbus_networks_rel": "Modbus networks needed for this power unit",
        "modbus_networks_rel.ip_address": "IP address for Modbus TCP/IP connections on ethernet interface (e.g. ************)",
        "modbus_networks_rel.subnet": "Subnet mask for Modbus TCP/IP connections on ethernet interface (e.g. 24, 16, 8, etc)",
        "modbus_networks_rel.gateway": "Default gateway for Modbus TCP/IP connections on ethernet interface (e.g. ************)",
        "alerts_edge": "Alerts are sent directly from the gateway in the field (better data access), rather than from the cloud server",
        "heartbeat_enabled": "Flag for identifying old Eurotech/power units that don't transmit a 'heartbeat'",
        "online_hb_enabled": "Flag for identifying units that don't transmit an 'online heartbeat' regardless of whether the unit is stroking",
        "change_detect_sens": "For 'change detection' alerts (e.g. when suction pressure rises above a threshold). Smaller number is more sensitive, generating more alerts",
        "wait_time_mins": "For alerts: wait X minutes before alerting if a unit loses power or internet connection",
        "wait_time_mins_ol": "For alerts: wait X minutes before alerting if a unit stops stroking and has an active internet connection",
        "wait_time_mins_spm": "For alerts: wait X minutes before alerting after strokes per minute (SPM) goes to zero",
        "wait_time_mins_stboxf": "For alerts: wait X minutes before alerting after stuffing box float switch is triggered",
        "wait_time_mins_suction": "For alerts: wait X minutes before alerting after suction pressure goes above threshold",
        "wait_time_mins_discharge": "For alerts: wait X minutes before alerting after discharge pressure goes down to threshold",
        # Hydraulic oil/filter alerts
        "wait_time_mins_hyd_oil_lvl": "For alerts: wait X minutes before alerting after hydraulic oil level goes below threshold (%)",
        "wait_time_mins_hyd_filt_life": "For alerts: wait X minutes before alerting after hydraulic filter life level goes below threshold (%)",
        "wait_time_mins_hyd_oil_life": "For alerts: wait X minutes before alerting after hydraulic oil life level goes below threshold (%)",
        "spm": "Alert if 'strokes per minute' falls to this level (typically zero)",
        "hyd_temp": f"Alert if hydraulic temperature goes above this level (typically 65{chr(176)} Celsius)",
        "suction": "Alert if suction pressure increases to this level (typically 700 PSI)",
        "discharge": "Alert if discharge pressure falls to this level (typically zero PSI)",
        # Hydraulic oil/filter alerts
        "hyd_oil_lvl_thresh": "Alert if hydraulic oil level falls below this threshold (%)",
        "hyd_filt_life_thresh": "Alert if hydraulic filter life falls below this threshold (%)",
        "hyd_oil_life_thresh": "Alert if hydraulic oil life falls below this threshold (%)",
        "wait_time_mins_ae011": "For alerts: wait X minutes before alerting after 'check motor rotation, no pressure' flag is triggered",
    }

    column_formatters = {
        "power_unit": remove_unnecessary_decimal,
        "structures_rel": remove_unnecessary_decimal,
        # Don't show the power unit - land location, just the gateway name
        "gateways_rel": lambda v, c, m, p: repr(m.gateways_rel),
        "run_mfg_date": lambda v, c, m, p: (
            m.run_mfg_date.strftime("%Y-%m-%d") if m.run_mfg_date else m.run_mfg_date
        ),
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "structures_rel": QueryAjaxModelLoaderAdminCust(
            "structures_rel",
            db.session,
            Structure,
            fields=[
                "structure_str",
                Structure.surface,
                Structure.downhole,
                PowerUnit.power_unit_str,
            ],
            order_by=Structure.structure,
            placeholder="Please select a structure",
            get_query_func=structures_filtering_function,
            **options,
        ),
        "gateways_rel": QueryAjaxModelLoaderAdminCust(
            "gateways_rel",
            db.session,
            Gw,
            fields=["gateway"],
            order_by=Gw.gateway,
            placeholder="Please select a gateway",
            get_query_func=gateway_filtering_function,
            **options,
        ),
    }

    inline_models = (
        (
            Alert,
            {
                "form_ajax_refs": {
                    "users_rel": QueryAjaxModelLoaderAdminCust(
                        "users_rel",
                        db.session,
                        User,
                        fields=["first_name", "last_name", "email"],
                        order_by=User.first_name,
                        placeholder="Please select a user for the alert",
                        get_query_func=user_filtering_function,
                        **options,
                    ),
                    "power_units_rel": QueryAjaxModelLoaderAdminCust(
                        "power_units_rel",
                        db.session,
                        PowerUnit,
                        fields=["power_unit_str"],
                        order_by=PowerUnit.power_unit_str,
                        placeholder="Please select a power unit",
                        get_query_func=power_units_filtering_function,
                        **options,
                    ),
                },
                "column_labels": alert_column_labels,
                "column_descriptions": alert_column_descriptions,
                # Must include "id" primary key, which is used for inline-form construction, for some reason...
                # https://stackoverflow.com/questions/34313253/flask-admin-inline-modelling-passing-form-arguments-throws-attributeerror
                "form_columns": ["id"]
                + [
                    col
                    for col in alert_form_columns
                    if col not in ("power_unit", "power_unit_str")
                ],
            },
        ),
    )


class StructureViewAdminCust(ModelViewAdminCust):
    """
    Flask-Admin view for Structure model (public.structures table)
    Known to the customer as "Unit Information"
    """

    def get_query(self):
        """Customer-admin must only be able to see stuff for his own customer_id"""
        user_cust_ids: tuple = get_user_cust_ids(
            user_id=getattr(current_user, "id", None)
        )
        query = (
            db.session.query(Structure)
            .join(structure_customer_rel)
            .filter(structure_customer_rel.c.customer_id.in_(user_cust_ids))
        )
        return query

    def get_count_query(self):
        """
        Customer-admin must only be able to see stuff for his own customer_id.
        If the 'get_query()' function is being modified, the 'get_count_query()'
        function must also be modified.

        We still need this for the customers' structure view because each structure
        can have more than one customer.
        """
        count_query = self.get_query().with_entities(func.count())
        return count_query

    # # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    # column_exclude_list = ['password_hash', ]
    can_create = False
    can_edit = True
    can_delete = False
    column_display_pk = False

    column_list = (
        "unit_types_rel",
        "model_types_rel",
        "power_unit_type",
        "on_genset",
        "downhole",
        "surface",
        "cust_sub_groups_rel",
        "area",
        "dgas_pumpjack",
        "well_license",
        "afe",
        "op_months_interval",
        "run_mfg_date",
        "structure_install_date",
        "slave_install_date",
        "time_zones_rel",
        # 'casing_sensor',
        "structure",
        "power_units_rel",
        "users_rel",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "downhole",
        "surface",
        "on_genset",
        "cust_sub_groups_rel",
        "area",
        "well_license",
        "afe",
        "op_months_interval",
        "run_mfg_date",
        "structure_install_date",
        "slave_install_date",
        "time_zones_rel",
        "users_rel",
    )
    column_editable_list = (
        "downhole",
        "surface",
        "on_genset",
        # Many-to-many can't be edited in the list view
        # "cust_sub_groups_rel",
        "area",
        "well_license",
        "afe",
        "op_months_interval",
        "run_mfg_date",
        "structure_install_date",
        "slave_install_date",
        "time_zones_rel",
    )

    options = {"minimum_input_length": 0}

    column_default_sort = ("structure", True)  # True == sort descending

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'cust' or 'power_unit' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        "structure",
        "power_units_rel",
        "well_license",
        "afe",
        "surface",
        "downhole",
        "cust_sub_groups_rel.name",
        "area",
        "unit_types_rel",
        "model_types_rel",
        "time_zones_rel",
    )

    column_formatters = {
        "power_units_rel": remove_unnecessary_decimal,
        "structure": remove_unnecessary_decimal,
        # "op_months_interval": format_like_integer,
        "cust_sub_groups_rel": many_to_many_formatter,
    }

    column_filters = (
        "model_types_rel",
        "area",
        "on_genset",
        "op_months_interval",
    )

    # # For a faster editing experience, enable inline editing in the list view:
    # # Won't work if you add 'gateway' to this list since 'gateway' is the primary key
    # # If these columns contain null values, it will show up as a red-coloured "Empty" which Dan doesn't like...
    # column_editable_list = column_list

    can_set_page_size = True
    page_size = 8

    # column_formatters = {
    #     # `view` is current administrative view
    #     # `context` is instance of jinja2.runtime.Context
    #     # `model` is model instance
    #     # `name` is property name
    #     # Format your column string with lambda here (e.g show first 20 characters).
    #     # Can return any valid HTML (e.g. a link to another view to show the detail or a popup window).
    #     'notes_1': lambda v, c, m, n: m.notes_1[:20] if isinstance(m.notes_1, str) else m.notes_1,
    #     'notes_2': lambda v, c, m, n: m.notes_2[:20] if isinstance(m.notes_2, str) else m.notes_2,
    # }

    column_labels = {
        "structure": "Structure",
        "power_units_rel": "Power Unit",
        "qb_sale": "QB Sale",
        "qb_install": "QB Install",
        "afe": "AFE",
        "dgas_pumpjack": "DGAS Pumpjack",
        "structure_install_date": "Structure Startup Date",
        "slave_install_date": "UNOGAS Startup Date",
        "cust": "Customer",
        "cust_sub_groups_rel": "Sub-Groups",
        "cust_sub_groups_rel.name": "Sub-Groups",
        "unit_types_rel": "Unit Type",
        "model_types_rel": "Model",
        "well_license": "Well License",
        "surface": "Surface Land Location",
        "on_genset": "On Genset",
        "downhole": "Downhole Land Location",
        "time_zones_rel": "Time Zone for Alerts",
        "users_rel": "Users with Remote-Control Permissions",
        "op_months_interval": "Operating Hours b/w Preventative Maintenance",
    }
    column_descriptions = {
        "structure": "Structure ID",
        "power_units_rel": "Power Unit serial ID",
        "on_genset": "Is the unit powered by a generator/genset? Helps with alerts.",
        "cust_sub_groups_rel": "Customer-defined sub-group(s) for grouping units together",
        "cust_sub_groups_rel.name": "Customer-defined sub-group(s) for grouping units together",
        "dgas_pumpjack": "If DGAS unit type, the type of pumpjack on which the DGAS is installed",
        "slave_install_date": "If UNOGAS unit type, the date on which the EGAS was added to the UNO to make an UNOGAS unit",
        "run_mfg_date": "Date on which the unit was manufactured",
        "structure_install_date": "Date on which the unit was started up",
        "time_zones_rel": "Time zone for alerts/logs/charts",
        "users_rel": "Users allowed to 'remote-control' or set targets for this unit",
        "op_months_interval": "Operating hours between preventative maintenance activities",
    }

    form_ajax_refs = {
        "cust_sub_groups_rel": QueryAjaxModelLoaderAdminCust(
            "cust_sub_groups_rel",
            db.session,
            CustSubGroup,
            fields=["name"],
            order_by="name",
            placeholder="Please select a customer sub-group",
            get_query_func=cust_sub_group_filtering_function,
            **options,
        ),
        # This one doesn't need the admin customer one since it's just time zones
        "time_zones_rel": QueryAjaxModelLoaderAdminCust(
            "time_zones_rel",
            db.session,
            TimeZone,
            fields=["time_zone"],
            order_by="time_zone",
            placeholder="Please select a time zone",
            get_query_func=timezone_filtering_function,
            **options,
        ),
        "users_rel": QueryAjaxModelLoaderAdminCust(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            get_query_func=user_filtering_function,
            **options,
        ),
        "power_units_rel": QueryAjaxModelLoaderAdminCust(
            "power_units_rel",
            db.session,
            PowerUnit,
            fields=["power_unit_str"],
            order_by=PowerUnit.power_unit_str,
            placeholder="Please select a power unit",
            get_query_func=power_units_filtering_function,
            **options,
        ),
        "unit_types_rel": QueryAjaxModelLoaderAdminCust(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select a unit type",
            get_query_func=unit_type_filtering_function,
            **options,
        ),
        "model_types_rel": QueryAjaxModelLoaderAdminCust(
            "model_types_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select a model type",
            get_query_func=model_type_filtering_function,
            **options,
        ),
    }

    def update_model(self, form, model):
        """Override this method to change the model before it's updated"""

        # Make a copy of the old surface location before it's updated, so we can compare it later
        # Strings in Python are immutable, so you don’t need to create a copy of them
        self.old_surface = str(model.surface)

        return super().update_model(form, model)

    def on_model_change(self, form, model, is_created):
        """When someone changes a record, auto-update the unit_type from the model_type"""

        if hasattr(model, "model_types_rel") and model.model_types_rel:
            if hasattr(model, "unit_types_rel") and not model.unit_types_rel:
                if (
                    hasattr(model.model_types_rel, "unit_types_rel")
                    and model.model_types_rel.unit_types_rel
                ):
                    model.unit_types_rel = model.model_types_rel.unit_types_rel
                    # The following doesn't show up when updating with inline editing/AJAX, only when using the form
                    flash(
                        gettext(
                            f"Unit type saved as {model.unit_types_rel} based on model {model.model_types_rel}"
                        ),
                        "warning",
                    )

    def after_model_change(self, form, model, is_created):
        """
        Check to see if the "surface" field has changed, and if so, update the GPS coordinates.
        Do this after the model has changed, so we can start a new database transaction.
        """
        if "surface" in form.data.keys() and form.data["surface"] != self.old_surface:
            get_or_update_gps(
                structure_obj=model,
                reason=f"""IJACK user {current_user} changed surface location in admin site.
Power unit {model.power_units_rel}. \n\nOld: '{self.old_surface}'. \nNew: '{form.data["surface"]}'. \n\nUpdating the GPS coordinates now...""",
            )


class UserViewAdminCust(ModelViewAdminCust):
    """Flask-Admin view for User model (users table)"""

    def get_query(self):
        """Customer-admin must only be able to see stuff for his own customer_id"""
        user_cust_ids: tuple = get_user_cust_ids(
            user_id=getattr(current_user, "id", None)
        )
        # Only show the active users so there's no confusion about who's active and who's not.
        # I don't love this solution, but it works in the short term.
        # It's only a problem if a user is fired and then gets re-hired, but a rare occurrence.
        query = db.session.query(User).filter(
            User.customer_id.in_(user_cust_ids), User.is_active
        )
        return query

    def get_count_query(self):
        """
        Customer-admin must only be able to see stuff for his own customer_id.
        If the 'get_query()' function is being modified, the 'get_count_query()'
        function must also be modified.

        We still need this for the customers' structure view because each structure
        can have more than one customer.
        """
        count_query = self.get_query().with_entities(func.count())
        return count_query

    page_size = 20
    can_create = True
    # Make users "inactive" instead of deleting them. Otherwise it'll cause problems with the foreign key constraints
    # like with "alerts_sent_users" and "alerts" tables.
    can_delete = False

    # These cause problems with the register_admin.js script
    create_modal = False
    edit_modal = False

    column_list = (
        "email",
        "first_name",
        "last_name",
        "is_active",
        "phone",
        "job_title",
        # Which alerts the user is subscribed to
        # "alerts_rel",
        # Remote control of structures
        "structures_rel",
        # Units for which the user is a maintenance contact
        "maintenance_structures_rel",
        # Customer sub-groups for which the user wants to be CC'd when there's a service request
        "cust_sub_groups_notify_service_requests_rel",
        # Does the user want to be notified of ALL service requests, regardless of sub-group?
        "notify_service_requests",
        "phone",
        "user_lat",
        "user_lon",
        "sms_stop_all",
        "sms_stopped_at_utc",
        "eml_unsubscribe_all",
        "eml_unsubscribe_all_stopped_at_utc",
    )
    column_exclude_list = ("password_hash",)
    form_exclude_list = ("password_hash",)

    # # This is the correct way to deal with password verification and hashing
    # form_extra_fields = {
    #     "form_password": PasswordField("Password", validators=PASSWORD_VALIDATORS),
    #     "form_password_verify": PasswordField(
    #         "Password Verify", validators=[EqualTo("form_password"), DataRequired()]
    #     ),
    # }
    form_args = {
        "phone": {"validators": [validate_phone]},
        "email": {"validators": [Email()]},
    }

    # Control the order of the columns in the forms
    form_columns = (
        "first_name",
        "last_name",
        "is_active",
        "email",
        # "form_password",
        # "form_password_verify",
        "job_title",
        # Remote control privileges
        "structures_rel",
        "phone",
        "is_us_phone",
        "time_zones_rel",
        "user_lat",
        "user_lon",
        # Structures for which the user is a maintenance contact
        "maintenance_structures_rel",
        # Customer sub-groups for which the user wants to be CC'd when there's a service request
        "cust_sub_groups_notify_service_requests_rel",
        # Does the user want to be notified of ALL service requests, regardless of sub-group?
        "notify_service_requests",
        # Only Twilio can write to the following fields
        # "sms_stop_all",
        # "sms_stopped_at_utc",
        # Email marketing campaigns
        "eml_unsubscribe_all",
        "eml_marketing",
        "eml_new_products",
        "eml_service",
        "eml_rcom",
        # alerts_rel is an inline model, so it's not included here
        # "alerts_rel",
    )

    column_editable_list = (
        "email",
        "first_name",
        "last_name",
        "is_active",
        "job_title",
        "user_lat",
        "user_lon",
        # Structures for which the user is a maintenance contact
        "maintenance_structures_rel",
        # Customer sub-groups for which the user wants to be CC'd when there's a service request
        "cust_sub_groups_notify_service_requests_rel",
        # Does the user want to be notified of ALL service requests, regardless of sub-group?
        "notify_service_requests",
        # phone is too difficult to validate as an editable field...
        # "phone",
        "eml_unsubscribe_all",
    )

    # Exclude these columns from the 'list' and 'edit' forms so the admin users can't change them
    form_excluded_columns = column_exclude_list

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    column_searchable_list = (
        "first_name",
        "last_name",
        "email",
        "phone",
        "job_title",
    )
    column_filters = (
        "first_name",
        "last_name",
        "is_active",
        "email",
        "phone",
        "job_title",
        # Structures for which the user is a maintenance contact
        "maintenance_structures_rel",
        # Customer sub-groups for which the user wants to be CC'd when there's a service request
        "cust_sub_groups_notify_service_requests_rel",
        # Does the user want to be notified of ALL service requests, regardless of sub-group?
        "notify_service_requests",
        "user_lat",
        "user_lon",
        "sms_stop_all",
        "sms_stopped_at_utc",
        "eml_unsubscribe_all",
        "eml_unsubscribe_all_stopped_at_utc",
    )

    column_sortable_list = (
        "email",
        "first_name",
        "last_name",
        # "customers_rel.customer",
        "job_title",
        "is_active",
        "notify_service_requests",
        "phone",
        "user_lat",
        "user_lon",
        "is_confirmed",
        "sms_stop_all",
        "sms_stopped_at_utc",
        "eml_unsubscribe_all",
        "eml_unsubscribe_all_stopped_at_utc",
        # "structures_rel",
        # "alerts_rel",
    )
    column_default_sort = "email"

    # form_create_rules = (
    #     # BSContainer(
    #     #     rules=[
    #     "first_name",
    #     "last_name",
    #     "is_active",
    #     "job_title",
    #     # BSRow(
    #     #     [
    #     #         BSCol(["first_name"], classes="col-md-6"),
    #     #         BSCol(["last_name"], classes="col-md-6"),
    #     #     ]
    #     # ),
    #     # BSRow(
    #     #     [
    #     #         BSCol(["is_active"], classes="col-md-6"),
    #     #         BSCol(["job_title"], classes="col-md-6"),
    #     #     ]
    #     # ),
    #     "email",
    #     "structures_rel",
    #     # Structures for which the user is a maintenance contact
    #     "maintenance_structures_rel",
    #     "notify_service_requests",
    #     "phone",
    #     "time_zones_rel",
    #     # BSRow(
    #     #     [
    #     #         BSCol(["phone"], classes="col-md-6"),
    #     #         BSCol(["time_zones_rel"], classes="col-md-6"),
    #     #     ]
    #     # ),
    #     "is_us_phone",
    #     "user_lat",
    #     "user_lon",
    #     # BSRow(
    #     #     [
    #     #         BSCol(["user_lat"], classes="col-md-6"),
    #     #         BSCol(["user_lon"], classes="col-md-6"),
    #     #     ]
    #     # ),
    #     # Only Twilio can write to the following fields
    #     # "sms_stop_all",
    #     # "sms_stopped_at_utc",
    #     # Email marketing campaigns
    #     "eml_unsubscribe_all",
    #     "eml_marketing",
    #     "eml_new_products",
    #     "eml_service",
    #     "eml_rcom",
    #     "alerts_rel",
    #     #     ],
    #     #     classes="mt-3",
    #     # ),
    # )

    # # This rules list does NOT contain a password. Change that separately by resetting it.
    # form_edit_rules = (
    #     # BSContainer(
    #     #     rules=[
    #     "first_name",
    #     "last_name",
    #     "is_active",
    #     "job_title",
    #     # BSRow(
    #     #     [
    #     #         BSCol(["first_name"], classes="col-md-6"),
    #     #         BSCol(["last_name"], classes="col-md-6"),
    #     #     ]
    #     # ),
    #     # BSRow(
    #     #     [
    #     #         BSCol(["is_active"], classes="col-md-6"),
    #     #         BSCol(["job_title"], classes="col-md-6"),
    #     #     ]
    #     # ),
    #     "email",
    #     "structures_rel",
    #     # Structures for which the user is a maintenance contact
    #     "maintenance_structures_rel",
    #     "notify_service_requests",
    #     "phone",
    #     "time_zones_rel",
    #     # BSRow(
    #     #     [
    #     #         BSCol(["phone"], classes="col-md-6"),
    #     #         BSCol(["time_zones_rel"], classes="col-md-6"),
    #     #     ]
    #     # ),
    #     "is_us_phone",
    #     "user_lat",
    #     "user_lon",
    #     # BSRow(
    #     #     [
    #     #         BSCol(["user_lat"], classes="col-md-6"),
    #     #         BSCol(["user_lon"], classes="col-md-6"),
    #     #     ]
    #     # ),
    #     # Only Twilio can write to the following fields
    #     # "sms_stop_all",
    #     # "sms_stopped_at_utc",
    #     # Email marketing campaigns
    #     "eml_unsubscribe_all",
    #     "eml_marketing",
    #     "eml_new_products",
    #     "eml_service",
    #     "eml_rcom",
    #     "alerts_rel",
    #     #     ],
    #     #     classes="mt-3",
    #     # ),
    # )

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        # Units the user can remote-control (main form, not inline model)
        "structures_rel": QueryAjaxModelLoaderAdminCust(
            "structures_rel",
            db.session,
            Structure,
            fields=[
                "structure_str",
                "surface",
                "downhole",
                PowerUnit.power_unit_str,
            ],
            order_by="structure",
            placeholder="Please select units for this user to remote-control",
            get_query_func=structures_filtering_function,
            **options,
        ),
        # Units for which the user is a maintenance contact
        "maintenance_structures_rel": QueryAjaxModelLoaderAdminCust(
            "maintenance_structures_rel",
            db.session,
            Structure,
            fields=["structure_str", "surface", "downhole", PowerUnit.power_unit_str],
            order_by=PowerUnit.power_unit_str,
            placeholder="Please select the structures for which this user is a maintenance contact",
            get_query_func=structures_filtering_function,
            **options,
        ),
        "time_zones_rel": QueryAjaxModelLoaderAdminCust(
            "time_zones_rel",
            db.session,
            TimeZone,
            fields=["time_zone"],
            order_by="time_zone",
            placeholder="Please select a time zone for this user (for alerts and charts)",
            # default=TIME_ZONE_ID_AMERICA_REGINA,
            get_query_func=timezone_filtering_function,
            **options,
        ),
        "alerts_rel": QueryAjaxModelLoaderAdminCust(
            "alerts_rel",
            db.session,
            Alert,
            fields=["power_units_rel"],
            order_by="power_units_rel",
            placeholder="Please select the alerts for this user",
            get_query_func=alert_filtering_function,
            **options,
        ),
        # Customer sub-groups for which the user wants to be CC'd when there's a service request
        "cust_sub_groups_notify_service_requests_rel": QueryAjaxModelLoaderAdminCust(
            "cust_sub_groups_notify_service_requests_rel",
            db.session,
            CustSubGroup,
            fields=["name"],
            order_by="name",
            placeholder="Please select a customer sub-group for which this user wants to be CC'd when there's a service request",
            get_query_func=cust_sub_group_filtering_function,
            **options,
        ),
    }
    # So we can create multiple new alerts for different units, with the new user as the alertee.
    # This uses the defaults for new alerts, such as "wants_sms = True" for SMS alerts, not email
    inline_models = (
        (
            Alert,
            {
                "form_ajax_refs": {
                    "power_units_rel": QueryAjaxModelLoaderAdminCust(
                        "power_units_rel",
                        db.session,
                        PowerUnit,
                        fields=[
                            PowerUnit.power_unit_str,
                            Structure.downhole,
                            Structure.surface,
                        ],
                        order_by="power_unit",
                        placeholder="Please select a power unit for the alert",
                        get_query_func=power_units_filtering_function,
                        **options,
                    ),
                    "users_rel": QueryAjaxModelLoaderAdminCust(
                        "users_rel",
                        db.session,
                        User,
                        fields=["first_name", "last_name", "email"],
                        order_by=User.first_name,
                        placeholder="Please select a user for the alert",
                        get_query_func=user_filtering_function,
                        **options,
                    ),
                },
                "column_labels": ALERT_COLUMN_LABELS,
                "column_descriptions": ALERT_COLUMN_DESCRIPTIONS,
                # Must include "id" primary key, which is used for inline-form construction, for some reason...
                # https://stackoverflow.com/questions/34313253/flask-admin-inline-modelling-passing-form-arguments-throws-attributeerror
                "form_columns": ["id"]
                + [col for col in ALERT_FORM_COLUMNS if col != "users_rel"],
                "column_searchable_list": [
                    "power_units_rel",
                    "power_unit_str",
                    Structure.downhole,
                    Structure.surface,
                ],
            },
        ),
    )

    column_labels = {
        "first_name": "First Name",
        "last_name": "Last Name",
        "email": "Email",
        # cust_sub_groups_rel: "Customer Sub-Groups",
        "structures_rel": "Units the User Can Remote-Control",
        "alerts_rel": "Unit Alerts",
        "maintenance_structures_rel": "Maintenance Contacts",
        # Customer sub-groups for which the user wants to be CC'd when there's a service request
        "cust_sub_groups_notify_service_requests_rel": "Sub-Groups for Service Requests",
        # Does the user want to be notified of ALL service requests, regardless of sub-group?
        "notify_service_requests": "Notify of ALL Service Requests",
        # "can_set": "Can Set Targets (Remote Control)",
        "phone": "Phone Number",
        "job_title": "Job Title",
        "is_active": "Active",
        "time_zones_rel": "Time Zone",
        "user_lat": "User Latitude",
        "user_lon": "User Longitude",
        "sms_stop_all": "SMS STOPPED",
        "sms_stopped_at_utc": "SMS STOPPED (SK Time)",
        # Email marketing campaigns
        "eml_unsubscribe_all": "Email Unsubscribe All",
        "eml_unsubscribe_all_stopped_at_utc": "Email Unsubscribe All (SK Time)",
        "eml_marketing": "Wants Marketing Emails",
        "eml_new_products": "Wants New Products Emails",
        "eml_service": "Wants Service Emails",
        "eml_rcom": "Wants RCOM Update Emails",
    }
    column_descriptions = {
        # cust_sub_groups_rel: "Customer-defined sub-groups of which a user is a member",
        "email": "Login email address that identifies the user's account",
        "job_title": "Job Title (e.g. Foreman, Operator, etc)",
        "is_active": "If user is no longer with the company, uncheck this box to disable their account completely, including login, alerts, and remote-control",
        # "can_set": "User has general permission to set targets or 'remote-control' units. Without this top-level permission, they cannot remote-control any units.",
        "structures_rel": "Units (pumps) the user can specifically 'remote-control' or set targets for",
        "maintenance_structures_rel": "Units for which the user is a maintenance contact",
        "cust_sub_groups_notify_service_requests_rel": "Sub-Groups for which the user wants to be CC'd when there's a service request",
        "notify_service_requests": "User wants to be notified of ALL service requests, regardless of sub-group",
        "alerts_rel": "Units for which user gets alerts",
        "phone": "International standard phone number (e.g. starting with +1)",
        "time_zones_rel": "Time zone where the user lives",
        "user_lat": "Latitude of the user's main location",
        "user_lon": "Longitude of the user's main location",
        "sms_stop_all": "The user has replied 'STOP' to an IJACK alert, so no alerts will be sent until the user sends 'START' to ************",
        "sms_stopped_at_utc": "Timestamp in Central Standard time when the user replied 'STOP' to an SMS, disabling all future SMS alerts from IJACK",
        # Email marketing campaigns
        "eml_unsubscribe_all": "User has unsubscribed from ALL IJACK emails",
        "eml_unsubscribe_all_stopped_at_utc": "Timestamp in Central Standard time when the user unsubscribed from ALL IJACK emails",
        "eml_marketing": "User is signed up to receive marketing emails",
        "eml_new_products": "User is signed up to receive new Products emails",
        "eml_service": "User is signed up to receive service emails",
        "eml_rcom": "User is signed up to receive RCOM update emails",
    }

    # # This won't work for editing an existing user since the email validation checks uniqueness
    # form = RegistrationForm

    # # The "query_factory" allows us to pass a custom filtering function,
    # # to ensure a customer only sees its own units/customers/groups
    # form_overrides = {
    #     # QuerySelectField has a "query_factory" parameter
    #     # "structures_rel": QuerySelectField,
    #     "cust_sub_groups_rel": QuerySelectField,
    # }
    # form_args = {
    #     # We don't need this if we're using form_ajax_refs!!! In fact, it'll cause an error in the "edit" modal.
    #     # "structures_rel": {"query_factory": structures_filtering_function},
    #     "cust_sub_groups_rel": {"query_factory": cust_sub_group_filtering_function},
    #     # "time_zones_rel": {"default": db.session.get(TimeZone, TIME_ZONE_ID_AMERICA_REGINA)},
    # }

    # form_overrides = {
    #     "time_zones_rel": QuerySelectField(default=TIME_ZONE_ID_AMERICA_REGINA),
    # }

    def on_model_change(self, form, model, is_created):
        """
        When a new user is created or updated,
        do the following BEFORE committing to the database
        """
        if is_created:
            # This will use the User model's @password.setter
            # function to set the "password_hash".
            # Password change was only ever available from the "create" form, not "edit"
            # NOTE: always set the password first, before other
            # model.password = form.form_password.data
            model.password = generate_random_string(24)

            # For some reason this query for the roles list MUST occur
            # AFTER the model.password_hash is set with model.password above!
            model.roles_rel = [db.session.get(Role, ROLE_ID_CUSTOMER)]

            # These are specified without an option
            # model.can_set = True
            model.is_confirmed = False
            model.is_active = True

            # Set the new user's main customer ID to the current user's main customer ID
            # model.customer_id = current_user.customer_id
            model.main_customer_rel = db.session.get(Customer, current_user.customer_id)
            model.customers_rel = [model.main_customer_rel]

        first_name: str = getattrd(form, "first_name.data", None)
        last_name: str = getattrd(form, "last_name.data", None)
        email: str = getattrd(form, "email.data", None)
        if isinstance(first_name, str):
            model.first_name = first_name.strip()
        if isinstance(last_name, str):
            model.last_name = last_name.strip()
        if isinstance(email, str):
            model.email = email.strip().lower()

    def after_model_change(self, form, model, is_created):
        """
        When a new user is created or updated,
        do the following AFTER committing to the database
        """
        if is_created:
            if os.getenv("FLASK_CONFIG", "production") not in (
                "development",
                "wsl",
                "testing",
            ):
                send_new_user_sms(model)

    def on_form_prefill(self, form, id):
        """
        Perform additional actions to pre-fill the edit form.

        Called from edit_view, if the current action is rendering the form rather than receiving client side input, after default pre-filling has been performed.

        By default does nothing.

        You only need to override this if you have added custom fields that depend on the database contents in a way that Flask-admin can't figure out by itself. Fields that were added by name of a normal column or relationship should work out of the box.
        """
        # print("")

    column_formatters = {
        "phone": user_phone_num_formatter,
        "alerts_rel": alerts_formatter,
        "maintenance_structures_rel": many_to_many_formatter,
        "structures_rel": structures_formatter,
        "sms_stopped_at_utc": datetime_formatter_sk_time,
        "eml_unsubscribe_all_stopped_at_utc": datetime_formatter_sk_time,
    }

    # def validate_form(self, form) -> bool:
    #     """
    #     Validate the form on submit.

    #     :param form:
    #         Form to validate
    #     """
    #     if form.phone and form.phone.data:
    #         try:
    #             validate_phone(form.phone)
    #         except Exception as err:
    #             flash(f"Error: {err}")
    #             return False

    #     return validate_form_on_submit(form)

    def render(self, template, **kwargs):
        """Add some extra JavaScript"""
        current_app.config.get("VERSION_MYIJACK", None)
        self.extra_css = [
            # "https://cdn.jsdelivr.net/npm/intl-tel-input@24.5.0/build/css/intlTelInput.min.css",
            "intlTelInput.min.css"
        ]
        self.extra_js = [
            # "https://cdn.jsdelivr.net/npm/intl-tel-input@24.5.0/build/js/intlTelInput.min.js",
            # "https://cdn.jsdelivr.net/npm/intl-tel-input@24.5.0/build/js/utils.js",
            # Use the name of the vite bundle here
            "register_admin.js",
        ]
        return super().render(template, is_ijack_admin=False, **kwargs)


class AlertViewAdminCust(ModelViewAdminCust):
    """Flask-Admin view for Alert model (public.alerts table)"""

    def get_query(self):
        """Customer-admin must only be able to see stuff for his own customer_id"""
        user_cust_ids: tuple = get_user_cust_ids(
            user_id=getattr(current_user, "id", None)
        )
        query = (
            db.session.query(Alert)
            .join(User)
            .filter(User.customer_id.in_(user_cust_ids))
        )
        return query

    def get_count_query(self):
        """
        Customer-admin must only be able to see stuff for his own customer_id.
        If the 'get_query()' function is being modified, the 'get_count_query()'
        function must also be modified.

        We still need this for the customers' structure view because each structure
        can have more than one customer.
        """
        count_query = self.get_query().with_entities(func.count())
        return count_query

    can_create = True
    can_edit = True
    can_delete = True  # disable record deletion maybe...
    column_display_pk = False
    page_size = 50

    column_list = (
        "power_units_rel",
        "users_rel",
        "users_rel.phone",
        "wants_sms",
        "users_rel.sms_stop_all",
        "wants_email",
        "wants_phone",
    )

    # For a faster editing experience, enable inline editing in the list view:
    # Won't work if you add 'gateway' to this list since 'gateway' is the primary key
    column_editable_list = (
        "power_units_rel",
        "users_rel",
        "wants_sms",
        "wants_email",
        "wants_phone",
    )

    # Control the order of the columns in the forms
    form_columns = ALERT_FORM_COLUMNS

    options = {"minimum_input_length": 0}

    form_ajax_refs = {
        "power_units_rel": QueryAjaxModelLoaderAdminCust(
            "power_units_rel",
            db.session,
            PowerUnit,
            fields=["power_unit_str", Structure.downhole, Structure.surface],
            order_by="power_unit",
            placeholder="Please select a unit",
            get_query_func=power_units_filtering_function,
            **options,
        ),
        "users_rel": QueryAjaxModelLoaderAdminCust(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            get_query_func=user_filtering_function,
            **options,
        ),
    }

    # Can't sort on user since it's not actually in the table (an error will occur)
    # column_default_sort = 'customer'
    # column_default_sort = 'users_rel'

    # To make columns searchable, or to use them for filtering, specify a list of column names:
    # Can't add 'customer' to the following list (not sure why, but maybe something to do with it being a backref/relationship field)
    column_searchable_list = (
        # User.email, User.first_name, User.last_name,
        # 'email', 'first_name', 'last_name',
        "users_rel.email",
        "users_rel.first_name",
        "users_rel.last_name",
        "users_rel.phone",
        "power_units_rel.power_unit_str",
        Structure.surface,
        Structure.downhole,
        Customer.customer,
        CustSubGroup.name,
        CustSubGroup.description,
    )
    column_filters = (
        Customer.customer,
        # User.email, User.first_name, User.last_name,
        # 'email', 'first_name', 'last_name',
        "users_rel.email",
        "users_rel.first_name",
        "users_rel.last_name",
        "power_units_rel.power_unit_str",
        Structure.structure,
        Structure.surface,
        Structure.downhole,
        "wants_sms",
        "wants_email",
        "wants_phone",
    )

    column_formatters = {
        # `view` is current administrative view
        # `context` is instance of jinja2.runtime.Context
        # `model` is model instance
        # `name` is property name
        # Format your column string with lambda here (e.g show first 20 characters).
        # Can return any valid HTML (e.g. a link to another view to show the detail or a popup window).
        "users_rel.phone": user_phone_num_formatter,
        "power_units_rel": remove_unnecessary_decimal,
        # "structures_rel": remove_unnecessary_decimal,
    }

    column_labels = ALERT_COLUMN_LABELS
    column_descriptions = ALERT_COLUMN_DESCRIPTIONS


class ReportEmailHourlyViewAdminCust(ModelViewAdminCust):
    """Flask-Admin view for ReportEmailHourly model (public.report_email_hourly table)"""

    def get_query(self):
        """Customer-admin must only be able to see stuff for his own customer_id"""
        user_cust_ids: tuple = get_user_cust_ids(
            user_id=getattr(current_user, "id", None)
        )
        query = (
            db.session.query(ReportEmailHourly)
            .join(User)
            .filter(User.customer_id.in_(user_cust_ids))
        )
        return query

    def get_count_query(self):
        """
        Customer-admin must only be able to see stuff for his own customer_id.
        If the 'get_query()' function is being modified, the 'get_count_query()'
        function must also be modified.

        We still need this for the customers' structure view because each structure
        can have more than one customer.
        """
        count_query = self.get_query().with_entities(func.count())
        return count_query

    # # Removing columns from the list view is easy, just pass a list of column names for the column_excludes_list parameter
    # column_exclude_list = ['password_hash', ]
    can_create = True
    can_edit = True
    can_delete = True  # disable record deletion maybe...
    column_display_pk = False
    can_set_page_size = True
    page_size = 50

    column_list = (
        "users_rel",
        # "first_name",
        # "last_name",
        # "customers_rel",
        "hours_rel",
        "days_of_week_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
        # "id",
    )

    column_sortable_list = (
        # "users_rel",
        # "hours_rel",
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
        "id",
    )
    # column_default_sort = [("customers_rel.customer", False)]

    # For a faster editing experience, enable inline editing in the list view:
    # Won't work if you add 'gateway' to this list since 'gateway' is the primary key
    column_editable_list = (
        "users_rel",
        "hours_rel",
        "days_of_week_rel",
        # The following are many-to-many relationship tables, so they don't work with inline editing
        # "model_types_rel",
        # "unit_types_rel",
        "max_distance_km",
    )

    # Control the order of the columns in the forms
    form_columns = (
        "users_rel",
        "hours_rel",
        "days_of_week_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
    )

    # form_choices = {
    #     "hours_rel": [(x, x) for x in range(1, 25)],
    #     "days_of_week_rel": [
    #         (1, "Monday"),
    #         (2, "Tuesday"),
    #         (3, "Wednesday"),
    #         (4, "Thursday"),
    #         (5, "Friday"),
    #         (6, "Saturday"),
    #         (7, "Sunday"),
    #     ],
    # }

    column_searchable_list = (
        "users_rel.first_name",
        "users_rel.last_name",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "warehouses_rel.name",
    )
    column_filters = column_searchable_list

    column_labels = {
        "users_rel": "User Being Emailed",
        "users_rel.first_name": "First Name",
        "users_rel.last_name": "Last Name",
        "hours_rel": "Hours Email Sent",
        "days_of_week_rel": "Days of Week Email Sent",
        "unit_types_rel": "Unit Types Wanted",
        "unit_types_rel.unit_type": "Unit Types Wanted",
        "model_types_rel": "Models Wanted",
        "model_types_rel.model": "Models Wanted",
        "warehouses_rel": "Warehouses Wanted",
        "warehouses_rel.name": "Warehouses Wanted",
        "max_distance_km": "Max Distance (km)",
    }
    column_descriptions = {
        "users_rel": "User to whom the email report is being sent",
        "hours_rel": "Hours in which email is being sent",
        "days_of_week_rel": "Days of the week in which email is being sent",
        "unit_types_rel": "High-level unit types wanted (e.g. XFER, EGAS, UNO)",
        "model_types_rel": "Specific models wanted (e.g. XFER 2270)",
        "warehouses_rel": "Show units from these warehouses in report",
        "max_distance_km": "Maximum distance in km from user's main GPS location (which is set in admin/customers/users table and defaults to the IJACK shop in Moosomin, SK)",
    }

    column_formatters = {
        "max_distance_km": lambda v, c, model, n: f"{model.max_distance_km:,} km",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "users_rel": QueryAjaxModelLoaderAdminCust(
            "users_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            get_query_func=user_filtering_function,
            **options,
        ),
        "unit_types_rel": QueryAjaxModelLoaderAdminCust(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select unit types",
            get_query_func=unit_type_filtering_function,
            **options,
        ),
        "model_types_rel": QueryAjaxModelLoaderAdminCust(
            "model_types_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select models",
            get_query_func=model_type_filtering_function,
            **options,
        ),
        "warehouses_rel": QueryAjaxModelLoader(
            "warehouses_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select warehouses",
            **options,
        ),
    }


class ReportEmailOpHoursViewAdminCust(ModelViewAdminCust):
    """Flask-Admin view for ReportEmailOpHours model (public.report_email_op_hours table)"""

    def get_query(self):
        """Customer-admin must only be able to see stuff for his own customer_id"""
        user_cust_ids: tuple = get_user_cust_ids(
            user_id=getattr(current_user, "id", None)
        )
        query = (
            db.session.query(ReportEmailOpHours)
            .join(User)
            .filter(User.customer_id.in_(user_cust_ids))
        )
        return query

    def get_count_query(self):
        """
        Customer-admin must only be able to see stuff for his own customer_id.
        If the 'get_query()' function is being modified, the 'get_count_query()'
        function must also be modified.

        We still need this for the customers' structure view because each structure
        can have more than one customer.
        """
        count_query = self.get_query().with_entities(func.count())
        return count_query

    can_create = True
    can_edit = True
    can_delete = True
    column_display_pk = False
    can_set_page_size = True
    page_size = 50

    column_list = (
        "user_rel",
        "types_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
    )

    column_sortable_list = (
        # "user_rel",
        # "types_rel",
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
    )
    # column_default_sort = [("customers_rel.customer", False)]

    column_editable_list = (
        "user_rel",
        # The following are many-to-many relationship tables, so they don't work with inline editing
        # "types_rel",
        # "unit_types_rel",
        # "model_types_rel",
        # "warehouses_rel",
        "max_distance_km",
    )

    form_columns = (
        "user_rel",
        "types_rel",
        "unit_types_rel",
        "model_types_rel",
        "warehouses_rel",
        "max_distance_km",
    )

    column_searchable_list = (
        "user_rel.first_name",
        "user_rel.last_name",
        "types_rel.name",
        "unit_types_rel.unit_type",
        "model_types_rel.model",
        "warehouses_rel.name",
    )
    column_filters = column_searchable_list

    column_labels = {
        "user_rel": "User Being Emailed",
        "user_rel.first_name": "First Name",
        "user_rel.last_name": "Last Name",
        "types_rel": "Email Types Wanted",
        "types_rel.name": "Email Types Wanted",
        "unit_types_rel": "Unit Types Wanted",
        "unit_types_rel.unit_type": "Unit Types Wanted",
        "model_types_rel": "Models Wanted",
        "model_types_rel.model": "Models Wanted",
        "warehouses_rel": "Warehouses Wanted",
        "warehouses_rel.name": "Warehouses Wanted",
        "max_distance_km": "Max Distance (km)",
    }
    column_descriptions = {
        "user_rel": "User to whom the email report is being sent",
        "types_rel": "Types of email wanted (e.g. 100 hours, 1000 hours, etc.)",
        "unit_types_rel": "High-level unit types wanted (e.g. XFER, EGAS, UNO)",
        "model_types_rel": "Specific models wanted (e.g. XFER 2270)",
        "warehouses_rel": "Show units from these warehouses in report",
        "max_distance_km": "Maximum distance in km from user's main GPS location (which is set in admin/customers/users table and defaults to the IJACK shop in Moosomin, SK)",
    }

    column_formatters = {
        "max_distance_km": lambda v, c, model, n: f"{model.max_distance_km:,} km",
    }

    options = {"minimum_input_length": 0}
    form_ajax_refs = {
        "user_rel": QueryAjaxModelLoaderAdminCust(
            "user_rel",
            db.session,
            User,
            fields=["email", "first_name", "last_name"],
            order_by=User.email,
            placeholder="Please select a user",
            get_query_func=user_filtering_function,
            **options,
        ),
        "types_rel": QueryAjaxModelLoader(
            "types_rel",
            db.session,
            ReportEmailOpHoursType,
            fields=["name"],
            order_by="name",
            placeholder="Please select an email type",
            **options,
        ),
        "unit_types_rel": QueryAjaxModelLoaderAdminCust(
            "unit_types_rel",
            db.session,
            UnitType,
            fields=["unit_type"],
            order_by="unit_type",
            placeholder="Please select unit types",
            get_query_func=unit_type_filtering_function,
            **options,
        ),
        "model_types_rel": QueryAjaxModelLoaderAdminCust(
            "model_types_rel",
            db.session,
            ModelType,
            fields=["model"],
            order_by="model",
            placeholder="Please select models",
            get_query_func=model_type_filtering_function,
            **options,
        ),
        "warehouses_rel": QueryAjaxModelLoader(
            "warehouses_rel",
            db.session,
            Warehouse,
            fields=["name"],
            order_by="name",
            placeholder="Please select warehouses",
            **options,
        ),
    }
