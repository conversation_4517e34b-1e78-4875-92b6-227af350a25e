from flask import redirect, request, url_for
from flask_admin.contrib.fileadmin import FileAdmin
from flask_login import current_user

from app import is_admin


class SecuredFileAdmin(FileAdmin):
    """Protected FileAdmin for static files"""

    def is_accessible(self):
        """The user must be both authenticated, and an admin user to view these pages"""
        return current_user.is_authenticated and is_admin()

    def inaccessible_callback(self, name, **kwargs):
        """Redirect to login page if user doesn't have access"""
        return redirect(url_for("dash.login", next=request.url))
