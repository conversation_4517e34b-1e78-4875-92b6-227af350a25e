import base64
import io
import os
import random
from datetime import date, datetime
from datetime import time as time_dt
from decimal import Decimal
from numbers import Number
from typing import Tuple

import boto3
import botocore
import phonenumbers
import pytz
from flask import current_app, flash, redirect
from flask_admin.form import FileUploadField, ImageUploadInput
from flask_admin.form.rules import NestedRule
from flask_admin.model import typefmt
from markupsafe import Markup
from PIL import Image, ImageOps, UnidentifiedImageError
from sqlalchemy.sql import text
from sqlalchemy.types import Float, Integer, Numeric, SmallInteger, Text
from wtforms.validators import ValidationError

from app import db
from app.dashapp.utils import update_shadow
from app.databases import run_sql_query
from app.utils.complex import getattrd


def date_format(view, value, name):
    """New default date format for Flask-Admin"""
    return value.strftime("%Y-%m-%d")


MY_DEFAULT_FORMATTERS = typefmt.BASE_FORMATTERS
MY_DEFAULT_FORMATTERS.update({date: date_format})


def get_columns_for_field(field):
    """
    I'm changing this Flask-Admin function to make it work for columns in secondary tables.
    e.g. need to search customers from the gateways view, and customers are related via the structures table
    """
    if (
        not field
        or not hasattr(field, "property")
        or not hasattr(field.property, "columns")
        or not field.property.columns
    ):
        # I added the below to use "_all_columns", instead of simply raising the exception right away
        try:
            # print(f"get_columns_for_field({field.key}) not working natively; trying something else...")
            # cols_in_related_table = field.property.mapper.columns._data._list
            # print(f"cols_in_related_table: {cols_in_related_table}\n")

            # Previously I was using field.property.table.columns._all_columns
            # but after upgrading to p oetry and upgrading all dependencies, it didn't work anymore
            # return field.property.table.columns._all_columns
            return field.property.mapper.columns
        except Exception:
            # print(f"ERROR with get_columns_for_field({field.key})")
            raise Exception("Invalid field %s: does not contains any columns." % field)

    return field.property.columns


def user_formatter(view, context, model, name) -> str:
    """Nice formatting of the user model"""
    from app.models.models import User  # avoid circular reference

    def get_fname_lname(attr):
        """Get first name and last name"""
        if hasattr(attr, "first_name") and hasattr(attr, "last_name"):
            return f"{attr.first_name} {attr.last_name}"
        return str(attr)

    attr = getattrd(model, name)
    if isinstance(attr, list):
        list_ = []
        for user in attr:
            list_.append(get_fname_lname(user))
        return list_

    repr = get_fname_lname(attr)
    if repr:
        return repr

    user = db.session.get(User, model.user_id)
    if user:
        return f"{user.first_name} {user.last_name}"

    return ""


def user_phone_num_formatter(view, context, model, name) -> Markup:
    """Format the User model phone number"""
    phone = None
    if hasattr(model, "user_phone"):
        phone = model.user_phone
    elif hasattr(model, "phone"):
        phone = model.phone
    elif str(getattrd(model, name)).startswith("+1"):
        phone = getattrd(model, name)

    if phone:
        phone = str(phone).strip()
        try:
            parsed = phonenumbers.parse(phone)
            formatted = phonenumbers.format_number(
                parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
            )
        except Exception:
            current_app.logger.exception("ERROR parsing or formatting phone number!")
            return ""

        return Markup(f'<a href="tel:{phone}">{formatted}</a>')

    return ""


def email_formatter(view, context, model, name) -> Markup:
    """Format an email address as a mailto: link"""
    return Markup(f'<a href="mailto:{model.email}">{model.email}</a>')


def datetime_formatter(view, context, model, name) -> str:
    """Format a datetime"""

    if hasattr(model, name):
        attr = getattr(model, name)
        if isinstance(attr, datetime):
            return attr.strftime("%Y-%m-%d at %I:%M %p UTC")

    return ""


def get_formatter(
    decimals: int = 0,
    right_align: bool = True,
    dollar_sign: bool = False,
    prefix: str = "",
    suffix: str = "",
) -> callable:
    """
    Get a formatter function for Flask Admin that formats a value model.name
    with commas and a certain number of decimals
    """

    def formatter(view, context, model, name: str) -> str | Markup:
        """This is the function that gets returned by the get_formatter() function"""

        new_str: str = ""
        attr = getattr(model, name, None)
        if isinstance(attr, (Number, Decimal, float, int)):
            new_str = f"{attr:,.{decimals}f}"
        elif attr is not None:
            new_str = attr

        if dollar_sign:
            new_str = f"{prefix}${new_str}{suffix}"
        else:
            new_str = f"{prefix}{new_str}{suffix}"

        if right_align:
            return Markup(f'<div style="text-align: right;">{new_str}</div>')

        return new_str

    return formatter


def datetime_formatter_sk_time(view, context, model, name) -> str:
    """Format a datetime and convert from UTC to Saskatchewan time"""

    if hasattr(model, name):
        date_time_obj_utc = getattr(model, name)
        if isinstance(date_time_obj_utc, datetime):
            # Make the datetime aware of its UTC time zone
            # dt_utc_aware = date_time_obj_utc.replace(tzinfo=pytz.UTC)
            dt_utc_aware = pytz.UTC.localize(date_time_obj_utc)

            # Convert to SK time
            tz_wanted = pytz.timezone("America/Regina")
            dt_sk = dt_utc_aware.astimezone(tz_wanted)

            # return dt_sk.strftime("%Y-%m-%d at %-I:%M %p CST")
            return dt_sk.strftime("%Y-%m-%d at %H:%M CST")

    return ""


def time_formatter(view, context, model, name) -> str:
    """Format a datetime.time"""

    if hasattr(model, name):
        attr = getattr(model, name)
        if isinstance(attr, time_dt):
            return attr.strftime("%I:%M %p")

    return ""


def format_like_integer(view, context, model, name) -> str:
    """Format a number with commas and no decimals"""

    if hasattr(model, name):
        attr = getattr(model, name)
        if isinstance(attr, Number):
            return f"{attr:,.0f}"
        elif str(attr).isnumeric():
            num = Decimal(attr)
            return f"{num:,.0f}"
        elif attr is None:
            return ""
        return attr

    return ""


def money_formatter(view, context, model, name) -> str:
    """Format a number with dollar sign, comma, and two decimals"""

    if hasattr(model, name):
        attr = getattr(model, name)
        if isinstance(attr, Number):
            return f"${attr:,.2f}"
        elif attr is None:
            return "$0.00"

    return ""


def col_width_400_formatter(view, context, model, name) -> str:
    """Format field with a certain width"""

    if hasattr(model, name):
        attr = getattr(model, name)
        return Markup(f'<div style="width: 400px;">{attr}</div>')

    return ""


def alerts_formatter(view, context, model, name):
    """Format the alerts for a user, ignoring the alertee's name since we already know that"""
    return ", ".join(
        [str(getattr(alert, "power_units_rel")) for alert in getattr(model, name, [])]
    )


def structures_formatter(view, context, model, name):
    """Format the structures for a user, focusing on the power unit and the surface location"""
    return ", ".join(
        [
            f"{getattr(structure, 'power_unit_str', '')} - {getattr(structure, 'surface', '')}"
            for structure in getattr(model, name, [])
        ]
    )


def many_to_many_formatter(view, context, model, name: str) -> str:
    """This function formats the many-to-many and many-to-one fields for display in the list view"""
    try:
        if "." in name:
            rel_field, col_name = name.split(".")
        else:
            rel_field = name
            col_name = name
        tags = getattrd(model, rel_field)
        if hasattr(tags, "__iter__"):
            # It's a list of tags
            list_of_results: list = [
                str(getattrd(tag, col_name, str(tag))) for tag in tags
            ]
        else:
            # It's a single tag
            list_of_results: list = [str(getattrd(tags, col_name, str(tags)))]
        new_str: str = ", ".join(list_of_results)
    except Exception:
        current_app.logger.exception(
            "Error trying to format many-to-many relationship field in list view!"
        )
        new_str = ""

    return new_str


def suction_range_formatter(view, context, model, name) -> str:
    """
    Format the suction range for display
    With SWV >= 412, this indicates the mode of operation
    where 2 = 'VRU Mode' and 4 = 'Vessel Level Mode'
    """
    label: str = "Unknown"
    if isinstance(model.suction_range, int):
        if model.suction_range == 2:
            label = "VRU Mode"
        elif model.suction_range == 4:
            label = "Vessel Level Mode"
        else:
            label = str(model.suction_range)

    return label


def preserve_line_breaks(view, context, model, name) -> str:
    """
    For column_formatters in Flask-Admin table views.
    Preserve line breaks like \n and \r by replacing with <br>
    """
    old_text = getattr(model, name)
    # It's going to be Markdown HTML, so double-quotes will mess up some formatting!
    old_text2 = str(old_text).replace('"', "'")
    # old_text = "something \nnewline \rreturn <br>\ncombinedn <br>\rcombinedr"
    # new_text1 = f'<span style="white-space: pre-wrap">{old_text}</span>'
    # new_text2 = str(old_text).replace("\n", "<br>\n").replace("\r", "<br>\r")
    # new_text3 = re.sub(r"([\n]+)([A-Z])+", r"____\2", old_text)
    # new_text4 = re.sub(r"^(?!BUY)\n|\r", "|", old_text)

    # First replace the combination <br><\n\r> with just <\n\r>
    # so we don't keep adding new line breaks each time it changes...
    # new_text1 = str(old_text2).replace("<br>\n", "\n").replace("<br>\r", "\r")
    # new_text2 = str(new_text1).replace("\n", "<br>\n").replace("\r", "<br>\r")
    new_text2 = str(old_text2).replace("\n", "<br>").replace("\r", "<br>")
    # return new_text2
    return Markup(new_text2)


def format_gateway_power_unit(view, context, model, name) -> str:
    """
    Format the gateway with the AWS thing name, and the power unit
    """
    if hasattr(model, "aws_thing") and hasattr(model, "power_unit_str"):
        return f"{model.aws_thing} (PU {model.power_unit_str})"
    return getattr(model, name)


def clone_model(model, **kwargs) -> Tuple[object, str]:
    """Clone an arbitrary SQLAlchemy model object without its primary key values"""

    table = model.__table__

    # Make a new model, to which we'll add data
    new_model = model.__class__()
    # new_model = copy_object(model)

    # model_cols = [a for a in dir(model) if not a.startswith("_")]
    # model_cols = [a for a in dir(new_model) if not a.startswith("_")]
    # model_kwargs = model.__dict__
    cols_with_constraints = []
    for c in table.columns:
        # for key, value in model.__dict__.items():
        # for key in model_cols:
        key = c.name
        value = getattr(model, key)
        # if key == "service_type_id":
        #     print("not nullable")
        if str(key).startswith("_"):
            continue
        c = table.columns.get(key, None)
        if c is None or value is None:
            setattr(new_model, key, value)
            continue
        if any((c.primary_key, c.unique)):
            # For the message to the user
            cols_with_constraints.append(c)
        if key == "power_unit_str":
            # This is a generated column in PostgreSQL
            print(f"is c.computed? {c.computed}")
            continue
        if c.primary_key:
            continue
        if c.unique and c.nullable:
            # e.g. structure_slave_id
            continue
        if c.computed:
            # It's a computed column
            continue
        if not c.unique:
            # Copy the existing data
            setattr(new_model, key, value)
        elif not c.nullable:
            if len(c.foreign_keys) != 0:
                raise Exception(
                    f"The column {key} is not nullable and must be a value from another table, "
                    + f"so we can't duplicate this row. Its foreign key name is {c.foreign_keys[0].name}"
                )
            else:
                col_type = c.type._type_affinity
                # SMALLINT max range -32768 to +32767
                if col_type in (Float, Integer, Numeric, SmallInteger):
                    # Find the largest existing value and increment it by one
                    sql = f"select max({key}) as {key} from {table.schema}.{table.name}"
                    rows, _ = run_sql_query(sql, db_name="ijack")
                    max_val = rows[0][key]
                    setattr(new_model, key, max_val + 1)
                elif col_type is Text:
                    setattr(new_model, key, random.randint(-32768, 32767))
                else:
                    raise Exception(
                        f"The column {key} must be unique, and its col_type is {col_type}. "
                        + "I'm not sure what to do with it"
                    )

    if len(cols_with_constraints) == 0:
        msg = ""
    else:
        cols_string = ", ".join([col.name for col in cols_with_constraints]).strip(", ")
        msg = f"The following columns must be unique in the database: {cols_string}"

    # for constraint in table.constraints:
    #     children = constraint.get_children()
    #     argument_for = constraint.argument_for()
    #     contains_column = constraint.contains_column()
    #     copy = constraint.copy()

    return new_model, msg


def get_gateway_id(db_name: str, gateway: str):
    """Given a gateway, return its ID"""

    if db_name not in ("ijack", "timescale"):
        raise ValueError(
            f"db_name must be 'ijack' or 'timescale' for SQLAlchemy, not '{db_name}'"
        )

    sql = text(
        """
        select id as gateway_id
        from public.gw
        where gateway = :gw
    """
    ).bindparams(gw=gateway)
    rows, _ = run_sql_query(sql, db_name=db_name)

    if len(rows) == 0:
        return None

    return rows[0]["gateway_id"]


def get_unit_type_by_gateway(gateway, db_name):
    """Find the lowercase unit type by gateway"""

    sql = text(
        """
        select
            gateway,
            t2.structure,
            lower(t3.unit_type) as unit_type
        from public.gw t1
        left join public.structures t2
            on t2.power_unit_id = t1.power_unit_id
        left join public.unit_types t3
            on t3.id = t2.unit_type_id
        where t1.power_unit_id is not null
            and t1.gateway = :gw
    """
    ).bindparams(gw=gateway)
    rows, _ = run_sql_query(sql, db_name=db_name)

    if len(rows) == 0:
        return None

    return rows[0]["unit_type"]


# noqa: C901
def change_gateway_in_db_tables(
    old_gateway,
    new_gateway,
):
    """When a gateway changes, we need to update the data in the tables"""

    # if not new_gateway and not delete_old:
    if not new_gateway:
        msg = "Must specify both new and old gateway names, if you're not deleting the data"
        flash(msg)
        raise ValueError(msg)

    # Update the device shadow so we don't get alerts telling us to update the gateway's power unit
    shadow_new = {
        "state": {
            "reported": {
                "SERIAL_NUMBER": None,
                "C__POWER_UNIT": None,
                "C__CUSTOMER": None,
                "C__MQTT_TOPIC": None,
                "POWER_UNIT": None,
            }
        }
    }
    current_app.logger.info("Finished updates/deletions. Updating shadow now! ...")
    try:
        update_shadow(shadow_new, old_gateway)
    except Exception:
        current_app.logger.exception("Problem updating the AWS IoT shadow!")
        raise

    flash("AWS IoT shadow update complete!")
    return redirect("/admin/")


def download_from_bucket(bucket_name, file_in_bucket, filepath_on_target):
    """For downloading files from AWS S3 bucket"""

    # # Load the secret environment variables using python-dotenv
    # from dotenv import load_dotenv
    # path_to_load = str(Path(__file__).parent.parent.parent.parent.parent.joinpath('.env'))
    # load_dotenv(path_to_load)
    # ACCESS_ID = os.environ['AWS_ACCESS_KEY_ID_SEAN']
    # ACCESS_KEY = os.environ['AWS_SECRET_ACCESS_KEY_SEAN']

    s3 = boto3.resource(
        "s3",
        region_name="us-west-2",
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID", None),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY", None),
    )
    bucket = s3.Bucket(bucket_name)
    try:
        # Downloads file_in_bucket from bucket_name, and saves as file_name as well
        current_app.logger.info(
            f"Downloading file '{file_in_bucket}' from bucket '{bucket_name}' to folder '{filepath_on_target}'..."
        )
        rc = bucket.download_file(
            Key=file_in_bucket,
            Filename=filepath_on_target,
            ExtraArgs=None,
            Callback=None,
            Config=None,
        )
        current_app.logger.info(f"rc from bucket.download_file: {rc}")
    except botocore.exceptions.ClientError as e:
        if e.response["Error"]["Code"] == "404":
            current_app.logger.exception("The object does not exist.")
            raise
        raise
    except Exception:
        current_app.logger.exception(
            f"Problem downloading file {file_in_bucket} from AWS bucket"
        )
        raise
    return None


def image_thumbnail(view, context, model, name):
    """Open and display a thumbnail image for Flask-Admin list view"""
    try:
        image = getattr(model, name, None)
        data_uri = base64.b64encode(image).decode("utf-8")
    except Exception:
        return "No preview available"
    img_tag = Markup(f'<img src="data:image/png;base64,{data_uri}">')
    return img_tag


class ImageUploadFieldDB(FileUploadField):
    """
    Upload an image to a SQL database as a "large binary" object,
    rather than saving to a file as in the "ImageUploadField" base class.

    Does image validation, thumbnail generation, updating and deleting images.

    Requires PIL (or Pillow) to be installed.
    """

    widget = ImageUploadInput()

    keep_image_formats = ("PNG",)
    """
        If field detects that uploaded image is not in this list, it will save image
        as PNG.
    """

    def __init__(
        self,
        label=None,
        validators=None,
        base_path=None,
        relative_path=None,
        namegen=None,
        allowed_extensions=None,
        max_size=None,
        # thumbgen=None,
        # thumbnail_size=None,
        permission=0o666,
        url_relative_path=None,
        endpoint="static",
        **kwargs,
    ):
        """
        Constructor.

        :param label:
            Display label
        :param validators:
            Validators
        :param base_path:
            Absolute path to the directory which will store files
        :param relative_path:
            Relative path from the directory. Will be prepended to the file name for uploaded files.
            Flask-Admin uses `urlparse.urljoin` to generate resulting filename, so make sure you have
            trailing slash.
        :param namegen:
            Function that will generate filename from the model and uploaded file object.
            Please note, that model is "dirty" model object, before it was committed to database.

            For example::

                import os.path as op


                def prefix_name(obj, file_data):
                    parts = op.splitext(file_data.filename)
                    return secure_filename("file-%s%s" % parts)


                class MyForm(BaseForm):
                    upload = FileUploadField("File", namegen=prefix_name)

        :param allowed_extensions:
            List of allowed extensions. If not provided, then gif, jpg, jpeg, png and tiff will be allowed.
        :param max_size:
            Tuple of (width, height, force) or None. If provided, Flask-Admin will
            resize image to the desired size.

            Width and height is in pixels. If `force` is set to `True`, will try to fit image into dimensions and
            keep aspect ratio, otherwise will just resize to target size.
        :param thumbgen:
            Thumbnail filename generation function. All thumbnails will be saved as JPEG files,
            so there's no need to keep original file extension.

            For example::

                import os.path as op


                def thumb_name(filename):
                    name, _ = op.splitext(filename)
                    return secure_filename("%s-thumb.jpg" % name)


                class MyForm(BaseForm):
                    upload = ImageUploadField("File", thumbgen=thumb_name)

        :param thumbnail_size:
            Tuple or (width, height, force) values. If not provided, thumbnail won't be created.

            Width and height is in pixels. If `force` is set to `True`, will try to fit image into dimensions and
            keep aspect ratio, otherwise will just resize to target size.
        :param url_relative_path:
            Relative path from the root of the static directory URL. Only gets used when generating
            preview image URLs.

            For example, your model might store just file names (`relative_path` set to `None`), but
            `base_path` is pointing to subdirectory.
        :param endpoint:
            Static endpoint for images. Used by widget to display previews. Defaults to 'static'.
        """
        # Check if PIL is installed
        if Image is None:
            raise ImportError("PIL library was not found")

        self.max_size = max_size
        # self.thumbnail_fn = thumbgen or thumbgen_filename
        # self.thumbnail_size = thumbnail_size
        self.endpoint = endpoint
        self.image = None
        self.url_relative_path = url_relative_path

        if not allowed_extensions:
            # WEBP format doesn't display in Outlook
            allowed_extensions = ("gif", "jpg", "jpeg", "png", "tiff")

        super().__init__(
            label,
            validators,
            base_path=base_path,
            relative_path=relative_path,
            namegen=namegen,
            allowed_extensions=allowed_extensions,
            permission=permission,
            **kwargs,
        )

    def get_image(self):
        """Open the image as a PIL image"""
        # if self._is_uploaded_file(self.data):
        try:
            self.image = Image.open(self.data)
        except Exception as e:
            raise ValidationError("Invalid image: %s" % e)

    def pre_validate(self, form):
        super().pre_validate(form)

        if self._is_uploaded_file(self.data):
            try:
                self.image = Image.open(self.data)
            except Exception as e:
                raise ValidationError("Invalid image: %s" % e)

    @staticmethod
    def image_to_bytes(image: Image, format: str) -> bytes:
        """Convert Pillow image to bytes (e.g. for database)"""
        # Get stream of binary data
        stream = io.BytesIO()
        # Save the image to the stream
        image.save(stream, format=format)
        # Get the binary bytes from the stream
        image_bytes: bytes = stream.getvalue()
        return image_bytes

    @staticmethod
    def binary_to_image(binary) -> Image:
        """Convert binary data (e.g. from database) to Pillow image"""
        if not binary:
            return None
        stream = io.BytesIO(binary)
        try:
            image = Image.open(stream)
        except UnidentifiedImageError:
            return None
        return image

    def populate_obj(self, obj, name):
        """
        Save the binary data from the uploaded image, to the database.

        The binary data has already been opened to self.image in pre_validate()
        """
        if self.image is None:
            self.get_image()

        # Resize first?
        if self.max_size:
            (width, height, force) = self.max_size
            image = self.resize(self.image, width, height, force)
        else:
            image = self.image

        image_bytes = self.image_to_bytes(image, image.format)  # type: ignore

        # Set the object's data to the image_bytes
        setattr(obj, name, image_bytes)
        # Also set the format
        if hasattr(obj, "format"):
            setattr(obj, "format", image.format)  # type: ignore

    # def _resize(self, image, size):
    #     """Resize image file"""
    #     (width, height, force) = size

    #     if image.size[0] > width or image.size[1] > height:
    #         if force:
    #             return ImageOps.fit(self.image, (width, height), Image.Resampling.LANCZOS)
    #         else:
    #             thumb = self.image.copy()
    #             thumb.thumbnail((width, height), Image.Resampling.LANCZOS)
    #             return thumb

    #     return image

    @staticmethod
    def resize(image: Image, width: int, height: int, force: bool) -> Image:
        """Resize image file"""
        if not image:
            return None

        if image.size[0] > width or image.size[1] > height:
            if force:
                return ImageOps.fit(image, (width, height), Image.Resampling.LANCZOS)
            else:
                thumb = image.copy()
                thumb.thumbnail((width, height), Image.Resampling.LANCZOS)
                return thumb

        return image

    @classmethod
    def display_thumbnail(cls, view, context, model, name):
        """
        Open and display a thumbnail image in Flask Admin list view
        """
        try:
            binary = getattr(model, name)
            image = cls.binary_to_image(binary)
        except Exception:
            return "No preview available"

        if not image:
            return "No preview available"

        thumb = cls.resize(image, width=200, height=200, force=True)
        image_bytes = cls.image_to_bytes(thumb, format=image.format)

        if hasattr(model, "format"):
            format = str(model.format).lower()
        else:
            # Default to JPEG
            format = "jpeg"
        data_uri = base64.b64encode(image_bytes).decode("utf-8")
        img_tag = Markup(f'<img src="data:image/{format};base64,{data_uri}">')
        return img_tag


class Row(NestedRule):
    """
    Bootstrap grid "row" div with automatic Bootstrap columns
    """

    def __init__(self, *columns, **kw):
        """
        Constructor

        :param columns:
            Arguments (args, unlimited number) which each will become Bootstrap columns.
        :param kw:
            Keyword arguments, which may contain:
            "row_classes"
                Specify the classes for the Bootstrap row (e.g. "form-row justify-content-center").
                Default "form-row"
            "col_classes":
                Space-separated classes to use for the Bootstrap columns (e.g. "col-md-6").
                Default "col"
        """
        super().__init__(rules=columns, separator="")
        self.row_classes = kw.get("row_classes", "form-row")
        self.col_classes = kw.get("col_classes", "col")

    def __call__(self, form, form_opts=None, field_args=None):
        """
        Render all children when called in the Jinja template.

        :param form:
            Form object
        :param form_opts:
            Form options
        :param field_args:
            Optional arguments that should be passed to template or the field
        """
        if field_args is None:
            field_args = {}
        cols = []
        for col in self.rules:
            if col.visible_fields:
                w_args = form_opts.widget_args.setdefault(col.visible_fields[0], {})
                w_args.setdefault("column_class", self.col_classes)
            cols.append(col(form, form_opts, field_args))

        return Markup(
            '<div class="{}">{}</div>'.format(self.row_classes, "".join(cols))
        )


class NestedRuleClasses(NestedRule):
    """
    Nested rule inside a <div> with customizable classes.

    For example, to make a Bootstrap container div:
        NestedRuleClasses(rules=["field1", "field2"], classes="container")
    """

    def __init__(self, rules=None, separator="", classes=""):
        """
        Constructor

        :param rules:
            Child rule list. Typically these are rules.Col() Bootstrap columns.
        :param separator:
            Default separator between rules when rendering them.
        :param classes:
            Space-separated classes to use (e.g. "container", "row", or "col")
        """
        if rules is None:
            rules = []
        super().__init__(rules=rules, separator=separator)
        self.classes = classes

    def __call__(self, form, form_opts=None, field_args=None):
        """
        Render all children.

        :param form:
            Form object
        :param form_opts:
            Form options
        :param field_args:
            Optional arguments that should be passed to template or the field
        """
        if field_args is None:
            field_args = {}
        result = []
        for rule in self.rules:
            # if rule.visible_fields:
            #     w_args = form_opts.widget_args.setdefault(rule.visible_fields[0], {})
            #     w_args.setdefault('column_class', 'col')
            result.append(rule(form, form_opts, field_args))

        children = self.separator.join(result)
        return Markup(f'<div class="{self.classes}">{children}</div>')


class BSContainer(NestedRuleClasses):
    """
    Bootstrap container, which should have Bootstrap rows and columns nested inside.
    """

    def __init__(self, rules=None, separator="", classes=""):
        """
        Constructor

        :param rules:
            Child rule list. Typically these are rules.Row() Bootstrap rows.
        :param separator:
            Default separator between rules when rendering them.
        :param classes:
            Space-separated classes to add to the default "container" class.
            Try something like classes="container-fluid" to make a full-width container.
        """
        if rules is None:
            rules = []
        classes = f"container {classes}"
        super().__init__(rules=rules, separator=separator, classes=classes)


class BSRow(NestedRuleClasses):
    """
    Bootstrap row, which should have Bootstrap columns nested inside.
    """

    def __init__(self, rules=None, separator="", classes=""):
        """
        Constructor

        :param rules:
            Child rule list. Typically these are rules.Col() Bootstrap columns.
        :param separator:
            Default separator between rules when rendering them.
        :param classes:
            Space-separated classes to add to the default "form-row" class.
            Try something like classes="justify-content-center" to center your columns in the row.
        """
        if rules is None:
            rules = []
        classes = f"form-row {classes}"

        # Ensure the children in a Bootstrap row are BSCol classes
        columns = []
        for rule in rules:
            if isinstance(rule, str):
                rule = BSCol(rules=[rule])
            columns.append(rule)

        super().__init__(rules=columns, separator=separator, classes=classes)


class BSCol(NestedRuleClasses):
    """
    Bootstrap column, which can have another rule nested inside.
    """

    def __init__(self, rules=None, separator="", classes=""):
        """
        Constructor

        :param rules:
            Child rule list. Typically these are rules.Col() Bootstrap columns.
        :param separator:
            Default separator between rules when rendering them.
        :param classes:
            Space-separated classes to add to the default "col" class.
            Try something like classes="col-md-3" to have the column fill 1/4
            of the screen if it's at least a medium-sized device.
        """
        if rules is None:
            rules = []
        classes = f"col {classes}"
        super().__init__(rules=rules, separator=separator, classes=classes)
