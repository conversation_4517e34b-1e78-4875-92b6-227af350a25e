select
    a1.warehouse_name,
    a1.finished_good_type,
    a1.finished_good_name,
--     a1.part_id,
    a1.part_num,
    a1.description,
    a1.cost_cad,
    a1.warehouse_quantity1,
    a1.warehouse_quantity2,
    a1.warehouse_quantity1 * a1.cost_cad as total_cad1,
    a1.warehouse_quantity2 * a1.cost_cad as total_cad2
from (
    SELECT
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end as warehouse_name,
        'Base Powerunit' as finished_good_type,
        t3.name as finished_good_name,
--         t4.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', '') as part_num,
        t5.description,
    --     t4.quantity,
    --     t5.warehouse_mult
        avg(t5.cost_cad) as cost_cad,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric) as warehouse_quantity1,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric / 2) * 2 as warehouse_quantity2
    FROM public.vw_structures_joined t1
    inner join public.bom_base_powerunit_power_unit_type_rel t2
        on t2.power_unit_type_id = t1.power_unit_type_id
    inner join public.bom_base_powerunit t3
        on t3.id = t2.finished_good_id
    inner join public.bom_base_powerunit_part_rel t4
        on t4.finished_good_id = t3.id
    inner join public.parts t5
        on t5.id = t4.part_id
    where
        t2.quantity > 0
        and t4.quantity > 0
        and t5.warehouse_mult > 0
        and t1.surface is not null
        and t1.gps_lat is not null
        and t1.gps_lon is not null
        and t1.customer_id is not null
        and t1.customer_id is distinct from 21
        and t1.time_zone_id is not null
    group by
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end,
        t3.name,
--         t1.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', ''),
        t5.description

    union all

    SELECT
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end as warehouse_name,
        'Powerunit' as finished_good_type,
        t3.name as finished_good_name,
--         t4.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', '') as part_num,
        t5.description,
    --     t4.quantity,
    --     t5.warehouse_mult
        avg(t5.cost_cad) as cost_cad,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric) as warehouse_quantity1,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric / 2) * 2 as warehouse_quantity2
    FROM public.vw_structures_joined t1
    inner join public.bom_powerunit_power_unit_type_rel t2
        on t2.power_unit_type_id = t1.power_unit_type_id
    inner join public.bom_powerunit t3
        on t3.id = t2.finished_good_id
    inner join public.bom_powerunit_part_rel t4
        on t4.finished_good_id = t3.id
    inner join public.parts t5
        on t5.id = t4.part_id
    where
        t2.quantity > 0
        and t4.quantity > 0
        and t5.warehouse_mult > 0
        and t1.surface is not null
        and t1.gps_lat is not null
        and t1.gps_lon is not null
        and t1.customer_id is not null
        and t1.customer_id is distinct from 21
        and t1.time_zone_id is not null
    group by
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end,
        t3.name,
--         t1.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', ''),
        t5.description

    union all

    SELECT
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end as warehouse_name,
        'Structure' as finished_good_type,
        t3.name as finished_good_name,
--         t4.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', '') as part_num,
        t5.description,
    --     t4.quantity,
    --     t5.warehouse_mult
        avg(t5.cost_cad) as cost_cad,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric) as warehouse_quantity1,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric / 2) * 2 as warehouse_quantity2
    FROM public.vw_structures_joined t1
    inner join public.bom_structure_model_type_rel t2
        on t2.model_type_id = t1.model_type_id
    inner join public.bom_structure t3
        on t3.id = t2.finished_good_id
    inner join public.bom_structure_part_rel t4
        on t4.finished_good_id = t3.id
    inner join public.parts t5
        on t5.id = t4.part_id
    where
        t2.quantity > 0
        and t4.quantity > 0
        and t5.warehouse_mult > 0
        and t1.surface is not null
        and t1.gps_lat is not null
        and t1.gps_lon is not null
        and t1.customer_id is not null
        and t1.customer_id is distinct from 21
        and t1.time_zone_id is not null
    group by
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end,
        t3.name,
--         t1.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', ''),
        t5.description

    union all

    SELECT
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end as warehouse_name,
        'Pump Top' as finished_good_type,
        t3.name as finished_good_name,
--         t4.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', '') as part_num,
        t5.description,
    --     t4.quantity,
    --     t5.warehouse_mult
        avg(t5.cost_cad) as cost_cad,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric) as warehouse_quantity1,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric / 2) * 2 as warehouse_quantity2
    FROM public.vw_structures_joined t1
    inner join public.bom_pump_top_model_type_rel t2
        on t2.model_type_id = t1.model_type_id
    inner join public.bom_pump_top t3
        on t3.id = t2.finished_good_id
    inner join public.bom_pump_top_part_rel t4
        on t4.finished_good_id = t3.id
    inner join public.parts t5
        on t5.id = t4.part_id
    where
        t2.quantity > 0
        and t4.quantity > 0
        and t5.warehouse_mult > 0
        and t1.surface is not null
        and t1.gps_lat is not null
        and t1.gps_lon is not null
        and t1.customer_id is not null
        and t1.customer_id is distinct from 21
        and t1.time_zone_id is not null
    group by
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end,
        t3.name,
--         t1.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', ''),
        t5.description

    union all

    SELECT
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end as warehouse_name,
        'DGAS' as finished_good_type,
        t3.name as finished_good_name,
--         t4.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', '') as part_num,
        t5.description,
    --     t4.quantity,
    --     t5.warehouse_mult
        avg(t5.cost_cad) as cost_cad,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric) as warehouse_quantity1,
        CEIL(sum(t2.quantity * t4.quantity * t5.warehouse_mult)::numeric / 2) * 2 as warehouse_quantity2
    FROM public.vw_structures_joined t1
    inner join public.bom_dgas_model_type_rel t2
        on t2.model_type_id = t1.model_type_id
    inner join public.bom_dgas t3
        on t3.id = t2.finished_good_id
    inner join public.bom_dgas_part_rel t4
        on t4.finished_good_id = t3.id
    inner join public.parts t5
        on t5.id = t4.part_id
    where
        t2.quantity > 0
        and t4.quantity > 0
        and t5.warehouse_mult > 0
        and t1.surface is not null
        and t1.gps_lat is not null
        and t1.gps_lon is not null
        and t1.customer_id is not null
        and t1.customer_id is distinct from 21
        and t1.time_zone_id is not null
    group by
    --     customer, unit_type, model, power_unit_type, cust_sub_group,
        case when t1.warehouse_name is null then 'Moosomin' else t1.warehouse_name end,
        t3.name,
--         t1.part_id,
        REGEXP_REPLACE(t5.part_num, 'r\d*$', ''),
        t5.description

    union all

    select
        'Sold Moosomin' as warehouse_name,
        'Sold Moosomin' as finished_good_type,
        'Sold Moosomin' as finished_good_name,
        REGEXP_REPLACE(t2.part_num, 'r\d*$', '') as part_num,
        t2.description,
        avg(t2.cost_cad) as cost_cad,
        sum(t1.quantity) as warehouse_quantity1,
        sum(t1.quantity) as warehouse_quantity2
    from public.work_orders_parts t1
    inner join public.parts t2
        on t2.id = t1.part_id
    inner join public.vw_structures_joined t3
        on (t3.id = t1.structure_id or t3.power_unit_id = t1.power_unit_id)
    where
        t1.quantity > 0
        and t3.surface is not null
        and t3.gps_lat is not null
        and t3.gps_lon is not null
        and t3.customer_id is not null
        and t3.customer_id is distinct from 21
        and t3.time_zone_id is not null
        and t3.warehouse_name = 'Moosomin'
    group by
--         t1.part_id,
        REGEXP_REPLACE(t2.part_num, 'r\d*$', ''),
        t2.description

    union all

    select
        'Sold Williston' as warehouse_name,
        'Sold Williston' as finished_good_type,
        'Sold Williston' as finished_good_name,
        REGEXP_REPLACE(t2.part_num, 'r\d*$', '') as part_num,
        t2.description,
        avg(t2.cost_cad) as cost_cad,
        sum(t1.quantity) as warehouse_quantity1,
        sum(t1.quantity) as warehouse_quantity2
    from public.work_orders_parts t1
    inner join public.parts t2
        on t2.id = t1.part_id
    inner join public.vw_structures_joined t3
        on (t3.id = t1.structure_id or t3.power_unit_id = t1.power_unit_id)
    where
        t1.quantity > 0
        and t3.surface is not null
        and t3.gps_lat is not null
        and t3.gps_lon is not null
        and t3.customer_id is not null
        and t3.customer_id is distinct from 21
        and t3.time_zone_id is not null
        and t3.warehouse_name = 'Williston'
    group by
--         t1.part_id,
        REGEXP_REPLACE(t2.part_num, 'r\d*$', ''),
        t2.description

    union all

    select
        'Sold Stettler' as warehouse_name,
        'Sold Stettler' as finished_good_type,
        'Sold Stettler' as finished_good_name,
        REGEXP_REPLACE(t2.part_num, 'r\d*$', '') as part_num,
        t2.description,
        avg(t2.cost_cad) as cost_cad,
        sum(t1.quantity) as warehouse_quantity1,
        sum(t1.quantity) as warehouse_quantity2
    from public.work_orders_parts t1
    inner join public.parts t2
        on t2.id = t1.part_id
    inner join public.vw_structures_joined t3
        on (t3.id = t1.structure_id or t3.power_unit_id = t1.power_unit_id)
    where
        t1.quantity > 0
        and t3.surface is not null
        and t3.gps_lat is not null
        and t3.gps_lon is not null
        and t3.customer_id is not null
        and t3.customer_id is distinct from 21
        and t3.time_zone_id is not null
        and t3.warehouse_name = 'Stettler'
    group by
--         t1.part_id,
        REGEXP_REPLACE(t2.part_num, 'r\d*$', ''),
        t2.description

    union all

    select
        'Sold Dallas' as warehouse_name,
        'Sold Dallas' as finished_good_type,
        'Sold Dallas' as finished_good_name,
        REGEXP_REPLACE(t2.part_num, 'r\d*$', '') as part_num,
        t2.description,
        avg(t2.cost_cad) as cost_cad,
        sum(t1.quantity) as warehouse_quantity1,
        sum(t1.quantity) as warehouse_quantity2
    from public.work_orders_parts t1
    inner join public.parts t2
        on t2.id = t1.part_id
    inner join public.vw_structures_joined t3
        on (t3.id = t1.structure_id or t3.power_unit_id = t1.power_unit_id)
    where
        t1.quantity > 0
        and t3.surface is not null
        and t3.gps_lat is not null
        and t3.gps_lon is not null
        and t3.customer_id is not null
        and t3.customer_id is distinct from 21
        and t3.time_zone_id is not null
        and t3.warehouse_name = 'Dallas'
    group by
--         t1.part_id,
        REGEXP_REPLACE(t2.part_num, 'r\d*$', ''),
        t2.description
) a1
order by
    warehouse_name,
    finished_good_type,
    finished_good_name,
    part_num
