from flask_admin.model.form import InlineFormAdmin
from shared.models.models_work_order import WorkOrderPart

from app.flask_admin.utils import money_formatter


class WorkOrderPartInlineForm(InlineFormAdmin):
    """Customized inline form handler for WorkOrderParts in WorkOrder view"""

    # "form_ajax_refs": {
    #     "part_num": QueryAjaxModelLoader(
    #         name="part_num",
    #         session=db.session,
    #         model=Part,
    #         fields=[Part.part_num, "description"],
    #         order_by="part_num",
    #         placeholder="Please select a part",
    #         **options,
    #     ),
    # }

    form_columns = [
        # Must include "id" (otherwise AttributeError on save!)
        "id",
        # "part_num",
        "parts_rel",
        "description",
        "quantity",
        "price",
        # # The following are auto-calculated on_model_change()
        # "subtotal",
        # "sales_tax",
        # "total",
    ]

    # form_create_rules = (
    #     "part",
    #     "description",
    #     "quantity",
    #     "price",
    #     # BSContainer(
    #     #     rules=[
    #     #         BSRow(
    #     #             [
    #     #                 "part",
    #     #                 "description",
    #     #             ]
    #     #         ),
    #     #         BSRow(["quantity", "price"]),
    #     #     ],
    #     #     classes="mt-3",
    #     # ),
    # )
    # # Use same rule set for edit page
    # form_edit_rules = form_create_rules

    column_formatters = {
        "price": money_formatter,
        "description": lambda v, c, m, n: f"{str(getattr(m, n))[:40]}...",
    }

    def __init__(self):
        """Run this when the class is instantiated"""
        super().__init__(WorkOrderPart)
