from flask import redirect, request, url_for
from flask_admin.menu import <PERSON>u<PERSON>ink
from flask_login import current_user

from app import is_admin, is_admin_cust


class SecuredMenuLink(MenuLink):
    """Protected MenuLink for generic links like the Dash charting dashboard"""

    def __init__(self, *args, **kwargs):
        """
        Initialize the view with the given model.
        role_ids_accepted: list of role_ids that are allowed to view this page
        role_ids_rejected: list of role_ids that are not allowed to view this page
        """
        self.role_ids_accepted = kwargs.pop("role_ids_accepted", list())
        self.role_ids_rejected = kwargs.pop("role_ids_rejected", list())
        super().__init__(*args, **kwargs)

    def is_accessible(self):
        """The user must be both authenticated, and an admin user to view these pages"""
        has_the_role: bool = True
        role_ids_accepted: list = getattr(self, "role_ids_accepted", None)
        role_ids_rejected: list = getattr(self, "role_ids_rejected", None)
        if not hasattr(current_user, "roles_rel"):
            has_the_role = False
        else:
            if role_ids_accepted:
                has_the_role = any(
                    [current_user.has_role_id(role_id) for role_id in role_ids_accepted]
                )
            if role_ids_rejected:
                has_the_role = not any(
                    [current_user.has_role_id(role_id) for role_id in role_ids_rejected]
                )
        return current_user.is_authenticated and is_admin() and has_the_role

    def inaccessible_callback(self, name, **kwargs):
        """Redirect to login page if user doesn't have access"""
        return redirect(url_for("dash.login", next=request.url))


class SecuredMenuLinkAdminCust(SecuredMenuLink):
    """
    Protected MenuLink for generic links like the Dash charting dashboard.
    This version is for customer admins
    """

    def is_accessible(self):
        """The user must be both authenticated, and an admin user to view these pages"""
        return current_user.is_authenticated and (is_admin_cust() or is_admin())
