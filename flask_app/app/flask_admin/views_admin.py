# app/flask_admin/views_admin.py
import warnings

from flask_admin import Admin
from flask_admin.contrib import rediscli
from flask_admin.theme import Bootstrap4Theme
from redis import Redis
from shared.models.models import (
    AlarmLogMetric,
    Alert,
    AlertCustom,
    AlertsSent,
    AlertsSentMaint,
    AlertsSentMaintUser,
    AlertsSentUser,
    Application,
    Barrel,
    Calculator,
    CareerApplication,
    CheckValve,
    City,
    CompressionImage,
    CompressionPattern,
    ContactForm,
    Country,
    County,
    Currency,
    Customer,
    CustSubGroup,
    Diagnostic,
    DiagnosticMetric,
    ErrorLog,
    GatewayType,
    Gw,
    GwInfo,
    GwNotConnectedDontWorry,
    Hour,
    HoursBilledByFieldTechMonthlyEfficiency,
    HoursBilledMonthlyEfficiency,
    HydPistonType,
    ImageField,
    MapAbbrevItem,
    MetaDataTbl,
    ModbusHoldingRegister,
    PackingGland,
    PowerUnit,
    PowerUnitFixedIPNetwork,
    PowerUnitModbusNetwork,
    Province,
    ReleaseNote,
    RemoteControl,
    ReportEmailDerates,
    ReportEmailHourly,
    ReportEmailInventory,
    ReportEmailOpHours,
    Rod,
    Role,
    SalesTax,
    ServiceClock,
    ShuttleValve,
    SIMCard,
    StructureByModel,
    SurfaceImage,
    SurfacePattern,
    UnitType,
    UserAPIToken,
    VwHoursBilledByFieldTechByWorkOrder,
    VwProfiler,
    VwServiceClockHoursDaily,
    VwServiceClockHoursMonthly,
    VwServiceClockHoursYearly,
    VwUserRole,
    WebsiteView,
    WebsiteViewMostActive,
    ZipCodeSalesTax,
)
from shared.models.models_bom import (
    BOMDGAS,
    BOMBasePowerUnit,
    BOMPowerUnit,
    BOMPricing,
    BOMPricingPart,
    BOMPumpTop,
    BOMStructure,
    CycleCount,
    CycleCountItem,
    InventoryMovement,
    InventoryReservation,
    ModelType,
    ModelTypeOption,
    Part,
    PartCategory,
    PartFilter,
    PowerUnitType,
    PowerUnitTypeOption,
    PowerUnitTypePower,
    PowerUnitTypeSpeed,
    PowerUnitTypeVoltage,
    Warehouse,
    WarehouseLocation,
    WarehousePart,
)
from shared.models.models_work_order import (
    Maintenance,
    MaintenanceType,
    Service,
    ServiceEmailee,
    ServiceType,
    VwSalesByPersonMonth,
    VwSalesByPersonQuarter,
    VwSalesByPersonYear,
    WorkOrder,
    WorkOrderPart,
    WorkOrdersByUnit,
    WorkOrderStatus,
)

from app.config import (
    REDIS_URL,
    ROLE_ID_BOM_MASTER_PARTS_TABLES,
    ROLE_ID_IJACK_HUMAN_RESOURCES,
    ROLE_ID_IJACK_MACHINE_LEARNING,
    ROLE_ID_IJACK_SALES,
    ROLE_ID_IJACK_SERVICE,
    ROLE_ID_IJACK_SOFTWARE_DEV,
    ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
)
from app.flask_admin.base import SecuredAdminIndexView
from app.flask_admin.contrib.sqla.sqla_views import (
    AlarmLogMetricView,
    AlertCustomView,
    AlertsSentMaintUsersView,
    AlertsSentMaintView,
    AlertsSentUsersView,
    AlertsSentView,
    AlertView,
    BOMBasePowerUnitView,
    BOMDGASView,
    BOMFinishedGoodPartView,
    BOMPowerUnitView,
    BOMPricingView,
    BOMPumpTopView,
    BOMStructureView,
    CalculatorView,
    CareerApplicationView,
    CityView,
    CompressionImageView,
    CompressionPatternView,
    ContactFormView,
    CountryView,
    CountyView,
    CurrencyView,
    CustomerView,
    CustSubGroupView,
    CycleCountItemView,
    CycleCountView,
    DiagnosticMetricView,
    DiagnosticView,
    ErrorLogView,
    GatewayTypeView,
    GwInfoView,
    GwNotConnectedDontWorryView,
    GwView,
    HoursBilledByFieldTechMonthlyEfficiencyView,
    HoursBilledMonthlyEfficiencyView,
    HourView,
    ImageFieldView,
    InventoryMovementView,
    InventoryReservationView,
    MaintenanceTypeView,
    MaintenanceView,
    MapAbbrevItemView,
    MetaDataView,
    ModbusHoldingRegisterView,
    # Build category view classes
    ModelTypeOptionView,
    ModelTypeView,
    PartCategoryView,
    PartFilterView,
    PartView,
    PowerUnitModbusNetworkView,
    PowerUnitTypeOptionView,
    PowerUnitTypePowerView,
    PowerUnitTypeSpeedView,
    PowerUnitTypeView,
    PowerUnitTypeVoltageView,
    PowerUnitView,
    ProvinceView,
    ReleaseNotesView,
    RemoteControlView,
    ReportEmailDeratesView,
    ReportEmailHourlyView,
    ReportEmailInventoryView,
    ReportEmailOpHoursView,
    RodBarrelShuttleView,
    RoleView,
    SalesTaxView,
    ServiceClockView,
    ServiceEmaileeView,
    ServiceTypeView,
    ServiceView,
    SIMCardView,
    StructureByModelView,
    StructureView,
    SurfaceImageView,
    UnitTypeView,
    UserAPITokenView,
    UserView,
    VwHoursBilledByFieldTechByWorkOrderView,
    VwProfilerView,
    VwSalesByPersonMonthView,
    VwSalesByPersonQuarterView,
    VwSalesByPersonYearView,
    VwServiceClockHoursDailyView,
    VwServiceClockHoursMonthlyView,
    VwServiceClockHoursYearlyView,
    VwUserRoleView,
    WarehouseLocationView,
    WarehousePartView,
    WarehouseView,
    WebsiteMostActiveView,
    WebsiteViewMostActiveView,
    WebsiteViewView,
    WorkOrderCorpQuoteView,
    WorkOrderCorpView,
    WorkOrderIncQuoteView,
    WorkOrderIncView,
    WorkOrderPartView,
    WorkOrdersByUnitView,
    WorkOrderStatusView,
    ZipCodeSalesTaxView,
)
from app.flask_admin.contrib.sqla.sqla_views_applications import (
    ApplicationDGASView,
    ApplicationEGASView,
    ApplicationView,
    ApplicationVRUView,
    ApplicationXFERView,
)
from app.flask_admin.menu import SecuredMenuLink
from app.models.models import Structure, User

flask_adm = Admin(
    name="Admin",
    # template_mode="bootstrap4",
    # The URL will be myijack.com/admin/
    # endpoint refers to the blueprint [e.g. url_for('admin.index')] where index() is the function/view
    index_view=SecuredAdminIndexView(
        name="Admin",
        endpoint="admin",
        url="/admin/",
    ),
    # What the URL will be for the static files (CSS, JS, etc.)
    # static_url_path="/static/admin",
    theme=Bootstrap4Theme(),
)


def admin_views(app, db):
    """Add the database 'models' (tables) to the admin page"""

    # set optional bootswatch theme for Flask-Admin
    # app.config['FLASK_ADMIN_SWATCH'] = 'cerulean'

    # For IJACK employees only #####################################################################################
    # Create the secured flask_admin instance with index_view=SecuredAdminIndexView(url='/admin')
    flask_adm.init_app(app)

    # Add model (database table) views to the myijack.com/admin page
    # First the "Customers" menu
    with warnings.catch_warnings():
        # The "edit" page doesn't show the "form_password" and "form_password_verify" fields,
        # which are only shown when creating a new user. No need for a warning.
        warnings.filterwarnings(
            action="ignore", message="Fields missing from ruleset", category=UserWarning
        )
        flask_adm.add_view(
            UserView(
                User,
                db.session,
                category="Customers",
                name="Users",
                endpoint="users",
                url="/admin/users",
                # Don't let HR see this view. They'll see the more advanced view
                # role_ids_rejected=[ROLE_ID_IJACK_HUMAN_RESOURCES],
            )
        )
        # # The following view is for HR only, so it has a different endpoint and URL
        # flask_adm.add_view(
        #     UserViewHR(
        #         User,
        #         db.session,
        #         category="Customers",
        #         name="Users",
        #         endpoint="users_hr",
        #         url="/admin/users_hr",
        #         # Only HR can see this view
        #         role_ids_accepted=[ROLE_ID_IJACK_HUMAN_RESOURCES],
        #     )
        # )
    with app.app_context():
        flask_adm.add_link(
            SecuredMenuLink(
                category="Customers",
                name="Register User",
                endpoint="users.create_view",
                # Don't let HR see this view. They'll see the more advanced view
                # role_ids_rejected=[ROLE_ID_IJACK_HUMAN_RESOURCES],
            )
        )
        # flask_adm.add_link(
        #     SecuredMenuLink(
        #         category="Customers",
        #         name="Register User",
        #         endpoint="users_hr.create_view",
        #         # Only HR can see this view
        #         role_ids_accepted=[ROLE_ID_IJACK_HUMAN_RESOURCES],
        #     )
        # )
    flask_adm.add_view(
        ApplicationView(
            Application,
            db.session,
            category="Customers",
            name="Applications - All Combined",
            endpoint="applications",
            url="/admin/applications",
            role_ids_accepted=[ROLE_ID_IJACK_SALES],
        )
    )
    flask_adm.add_view(
        ApplicationXFERView(
            Application,
            db.session,
            category="Customers",
            name="Applications - XFER",
            endpoint="applications_xfer",
            url="/admin/applications_xfer",
            role_ids_accepted=[ROLE_ID_IJACK_SALES],
        )
    )
    flask_adm.add_view(
        ApplicationVRUView(
            Application,
            db.session,
            category="Customers",
            name="Applications - VRU",
            endpoint="applications_vru",
            url="/admin/applications_vru",
            role_ids_accepted=[ROLE_ID_IJACK_SALES],
        )
    )
    flask_adm.add_view(
        ApplicationEGASView(
            Application,
            db.session,
            category="Customers",
            name="Applications - EGAS",
            endpoint="applications_egas",
            url="/admin/applications_egas",
            role_ids_accepted=[ROLE_ID_IJACK_SALES],
        )
    )
    flask_adm.add_view(
        ApplicationDGASView(
            Application,
            db.session,
            category="Customers",
            name="Applications - DGAS",
            endpoint="applications_dgas",
            url="/admin/applications_dgas",
            role_ids_accepted=[ROLE_ID_IJACK_SALES],
        )
    )
    flask_adm.add_view(
        CareerApplicationView(
            CareerApplication,
            db.session,
            category="Customers",
            name="Career Applications",
            endpoint="career_applications",
            url="/admin/career_applications",
            role_ids_accepted=[ROLE_ID_IJACK_HUMAN_RESOURCES],
        )
    )
    flask_adm.add_view(
        UserAPITokenView(
            UserAPIToken,
            db.session,
            category="Customers",
            name="API Tokens",
            endpoint="api_tokens",
            url="/admin/api_tokens",
        )
    )
    flask_adm.add_view(
        RoleView(
            Role,
            db.session,
            category="Customers",
            name="Roles",
            endpoint="roles",
            url="/admin/roles",
            role_ids_accepted=[ROLE_ID_IJACK_HUMAN_RESOURCES],
        )
    )
    flask_adm.add_view(
        VwUserRoleView(
            VwUserRole,
            db.session,
            category="Customers",
            name="Users' Roles",
            endpoint="users_roles",
            url="/admin/users_roles",
        )
    )
    flask_adm.add_view(
        CustomerView(
            Customer,
            db.session,
            category="Customers",
            name="Customers",
            endpoint="customers",
            url="/admin/customers",
        )
    )
    flask_adm.add_view(
        CustSubGroupView(
            CustSubGroup,
            db.session,
            category="Customers",
            name="Customer Sub-Groups",
            endpoint="cust_sub_groups",
            url="/admin/cust_sub_groups",
        )
    )

    flask_adm.add_view(
        CountryView(
            Country,
            db.session,
            category="Customers",
            name="Countries",
            endpoint="countries",
            url="/admin/countries",
        )
    )

    flask_adm.add_view(
        ProvinceView(
            Province,
            db.session,
            category="Customers",
            name="Provinces/States",
            endpoint="provinces_states",
            url="/admin/provinces_states",
        )
    )

    flask_adm.add_view(
        CountyView(
            County,
            db.session,
            category="Customers",
            name="Counties",
            endpoint="counties",
            url="/admin/counties",
        )
    )

    flask_adm.add_view(
        CityView(
            City,
            db.session,
            category="Customers",
            name="Cities",
            endpoint="cities",
            url="/admin/cities",
        )
    )

    flask_adm.add_view(
        ZipCodeSalesTaxView(
            ZipCodeSalesTax,
            db.session,
            category="Customers",
            name="Sales Tax Rates by Zip Code",
            endpoint="zip_code_sales_tax",
            url="/admin/zip_code_sales_tax",
        )
    )

    flask_adm.add_view(
        SalesTaxView(
            SalesTax,
            db.session,
            category="Customers",
            name="Sales Tax Rates by Province",
            endpoint="work_order_sales_tax",
            url="/admin/work_order_sales_tax",
        )
    )

    # Now the "Units" menu
    flask_adm.add_view(
        StructureView(
            Structure,
            db.session,
            category="Units",
            name="Structures",
            endpoint="structures",
            url="/admin/structures",
        )
    )
    # flask_adm.add_view(
    #     StructurePerformanceView(
    #         Structure,
    #         db.session,
    #         category="Units",
    #         name="Structure Performance",
    #         endpoint="structure_performance",
    #         url="/admin/structure_performance",
    #     )
    # )
    flask_adm.add_view(
        StructureByModelView(
            StructureByModel,
            db.session,
            category="Units",
            name="Structures Built, by Model",
            endpoint="structures_by_model",
            url="/admin/structures_by_model",
        )
    )
    flask_adm.add_view(
        PowerUnitView(
            PowerUnit,
            db.session,
            category="Units",
            name="Power Units",
            endpoint="power_units",
            url="/admin/power_units",
        )
    )
    flask_adm.add_view(
        PowerUnitModbusNetworkView(
            PowerUnitModbusNetwork,
            db.session,
            category="Units",
            name="Power Unit Modbus Network",
            endpoint="power_unit_modbus_network",
            url="/admin/power_unit_modbus_network",
        )
    )
    flask_adm.add_view(
        PowerUnitModbusNetworkView(
            PowerUnitFixedIPNetwork,
            db.session,
            category="Units",
            name="Power Unit Fixed IP Interface",
            endpoint="power_unit_fixed_ip_network",
            url="/admin/power_unit_fixed_ip_network",
        )
    )
    flask_adm.add_view(
        GwView(
            Gw,
            db.session,
            category="Units",
            name="Gateways",
            endpoint="gateways",
            url="/admin/gateways",
        )
    )
    flask_adm.add_view(
        GwInfoView(
            GwInfo,
            db.session,
            category="Units",
            name="Gateway Info",
            endpoint="gateway_info",
            url="/admin/gateway_info",
        )
    )
    flask_adm.add_view(
        GwNotConnectedDontWorryView(
            GwNotConnectedDontWorry,
            db.session,
            category="Units",
            name="Gateways Not Connected Notes",
            endpoint="gateways_not_connected",
            url="/admin/gateways_not_connected",
        )
    )
    flask_adm.add_view(
        ReleaseNotesView(
            ReleaseNote,
            db.session,
            category="Units",
            name="Gateway Release Notes (Software)",
            endpoint="release_notes",
            url="/admin/release_notes",
        )
    )
    flask_adm.add_view(
        MapAbbrevItemView(
            MapAbbrevItem,
            db.session,
            category="Units",
            name="CAN Bus Cob Map",
            endpoint="can_bus_cob_map",
            url="/admin/can_bus_cob_map",
        )
    )
    flask_adm.add_view(
        ModbusHoldingRegisterView(
            ModbusHoldingRegister,
            db.session,
            category="Units",
            name="Modbus and Web API Metrics",
            endpoint="modbus_holding_registers",
            url="/admin/modbus_holding_registers",
        )
    )
    flask_adm.add_view(
        AlarmLogMetricView(
            AlarmLogMetric,
            db.session,
            category="Units",
            name="Alarm Log Metrics",
            endpoint="alarm_log_metrics",
            url="/admin/alarm_log_metrics",
        )
    )
    flask_adm.add_view(
        DiagnosticMetricView(
            DiagnosticMetric,
            db.session,
            category="Units",
            name="Diagnostic Metrics",
            endpoint="diagnostic_metrics",
            url="/admin/diagnostic_metrics",
        )
    )
    flask_adm.add_view(
        DiagnosticView(
            Diagnostic,
            db.session,
            category="Units",
            name="Diagnostic Data",
            endpoint="diagnostic_data",
            url="/admin/diagnostic_data",
        )
    )
    flask_adm.add_view(
        SIMCardView(
            SIMCard,
            db.session,
            category="Units",
            name="SIM Cards",
            endpoint="sim_cards",
            url="/admin/sim_cards",
        )
    )
    flask_adm.add_view(
        CalculatorView(
            Calculator,
            db.session,
            category="Units",
            name="Calculator",
            endpoint="calculator",
            url="/admin/calculator",
        )
    )
    flask_adm.add_view(
        GatewayTypeView(
            GatewayType,
            db.session,
            category="Units",
            name="Gateway Types",
            endpoint="gateway_types",
            url="/admin/gateway_types",
        )
    )
    flask_adm.add_view(
        UnitTypeView(
            UnitType,
            db.session,
            category="BoM",
            name="Unit Types - Very High Level for RCOM/Alerts",
            endpoint="unit_types",
            url="/admin/unit_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        ModelTypeView(
            ModelType,
            db.session,
            category="BoM",
            name="Structure Model Types - Finished Goods Mapping",
            endpoint="structure_types",
            url="/admin/structure_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        PowerUnitTypeView(
            PowerUnitType,
            db.session,
            category="BoM",
            name="Power Unit Model Types - Finished Goods Mapping",
            endpoint="power_unit_types",
            url="/admin/power_unit_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        BOMPricingView(
            BOMPricing,
            db.session,
            category="BoM",
            name="BoM Pricing - Parts Mapping",
            endpoint="bom_pricing",
            url="/admin/bom_pricing",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    # flask_adm.add_view(
    #     BOMFinishedGoodPartView(
    #         BOMPricingModelType,
    #         db.session,
    #         category="BoM",
    #         name="BoM Pricing - Model Type",
    #         endpoint="bom_pricing_parts",
    #         url="/admin/bom_pricing_parts",
    #     )
    # )
    flask_adm.add_view(
        BOMFinishedGoodPartView(
            BOMPricingPart,
            db.session,
            category="BoM",
            name="BoM Pricing - Parts Quantity",
            endpoint="bom_pricing_parts",
            url="/admin/bom_pricing_parts",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        BOMBasePowerUnitView(
            BOMBasePowerUnit,
            db.session,
            category="BoM",
            name="BoM Base Powerunit - Parts Mapping",
            endpoint="bom_base_powerunit",
            url="/admin/bom_base_powerunit",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        BOMPowerUnitView(
            BOMPowerUnit,
            db.session,
            category="BoM",
            name="BoM Powerunit - Parts Mapping",
            endpoint="bom_powerunit",
            url="/admin/bom_powerunit",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        BOMStructureView(
            BOMStructure,
            db.session,
            category="BoM",
            name="BoM Structure - Parts Mapping",
            endpoint="bom_structure",
            url="/admin/bom_structure",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        BOMPumpTopView(
            BOMPumpTop,
            db.session,
            category="BoM",
            name="BoM Pump Top - Parts Mapping",
            endpoint="bom_pump_top",
            url="/admin/bom_pump_top",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        BOMDGASView(
            BOMDGAS,
            db.session,
            category="BoM",
            name="BoM DGAS - Parts Mapping",
            endpoint="bom_dgas",
            url="/admin/bom_dgas",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        PartView(
            Part,
            db.session,
            category="BoM",
            name="BoM Parts - Every Part that Finished Goods Need",
            endpoint="parts",
            url="/admin/parts",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        PartFilterView(
            PartFilter,
            db.session,
            category="BoM",
            name="Filters",
            endpoint="filters",
            url="/admin/filters",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        RodBarrelShuttleView(
            Rod,
            db.session,
            category="BoM",
            name="Rod Types",
            endpoint="rod_types",
            url="/admin/rod_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        RodBarrelShuttleView(
            Barrel,
            db.session,
            category="BoM",
            name="Barrel Types",
            endpoint="barrel_types",
            url="/admin/barrel_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        RodBarrelShuttleView(
            ShuttleValve,
            db.session,
            category="BoM",
            name="Shuttle Valve Types",
            endpoint="shuttle_valve_types",
            url="/admin/shuttle_valve_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        RodBarrelShuttleView(
            CheckValve,
            db.session,
            category="BoM",
            name="Check Valve Types",
            endpoint="check_valve_types",
            url="/admin/check_valve_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        RodBarrelShuttleView(
            PackingGland,
            db.session,
            category="BoM",
            name="Packing Gland Types",
            endpoint="packing_gland_types",
            url="/admin/packing_gland_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        RodBarrelShuttleView(
            HydPistonType,
            db.session,
            category="BoM",
            name="Hydraulic Piston Types",
            endpoint="hydraulic_piston_types",
            url="/admin/hydraulic_piston_types",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )

    ######## "Build" menu #########################################################
    flask_adm.add_view(
        ModelTypeOptionView(
            ModelTypeOption,
            db.session,
            category="Build",
            name="Pump Top Options",
            endpoint="pump_top_options",
            url="/admin/pump_top_options",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        PowerUnitTypeOptionView(
            PowerUnitTypeOption,
            db.session,
            category="Build",
            name="Power Unit Options",
            endpoint="power_unit_options",
            url="/admin/power_unit_options",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        PowerUnitTypeSpeedView(
            PowerUnitTypeSpeed,
            db.session,
            category="Build",
            name="Power Unit Speeds",
            endpoint="power_unit_speeds",
            url="/admin/power_unit_speeds",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        PowerUnitTypePowerView(
            PowerUnitTypePower,
            db.session,
            category="Build",
            name="Power Unit Power",
            endpoint="power_unit_power",
            url="/admin/power_unit_power",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )
    flask_adm.add_view(
        PowerUnitTypeVoltageView(
            PowerUnitTypeVoltage,
            db.session,
            category="Build",
            name="Power Unit Voltage",
            endpoint="power_unit_voltage",
            url="/admin/power_unit_voltage",
            role_ids_accepted=[ROLE_ID_BOM_MASTER_PARTS_TABLES],
        )
    )

    # ######## "Other" menu ##############################################################################
    # Add the Redis CLI to the admin page
    flask_adm.add_view(
        rediscli.RedisCli(
            redis=Redis().from_url(REDIS_URL),
            category="Other",
            name="Redis DB Command Line Interface",
            endpoint="redis_cli",
            url="/admin/redis_cli",
        )
    )

    with app.app_context():
        flask_adm.add_link(
            SecuredMenuLink(
                category="Other",
                name="Email Marketing",
                endpoint="email_marketing",
                url="/admin/email_marketing",
                role_ids_accepted=[ROLE_ID_IJACK_SOFTWARE_DEV],
            )
        )
    flask_adm.add_view(
        MetaDataView(
            MetaDataTbl,
            db.session,
            category="Other",
            name="Meta Data",
            endpoint="meta_data",
            url="/admin/meta_data",
            role_ids_accepted=[ROLE_ID_IJACK_SOFTWARE_DEV],
        )
    )
    flask_adm.add_view(
        HourView(
            Hour,
            db.session,
            category="Other",
            name="Hours in the day",
            endpoint="hours",
            url="/admin/hours",
            role_ids_accepted=[ROLE_ID_IJACK_SOFTWARE_DEV],
        )
    )
    flask_adm.add_view(
        ErrorLogView(
            ErrorLog,
            db.session,
            category="Other",
            name="Error Logs",
            endpoint="error_logs",
            url="/admin/error_logs",
            role_ids_accepted=[ROLE_ID_IJACK_SOFTWARE_DEV],
        )
    )
    flask_adm.add_view(
        VwProfilerView(
            VwProfiler,
            db.session,
            category="Other",
            name="Website Slow Requests",
            endpoint="slow_requests",
            url="/admin/slow_requests",
            # role_ids_accepted=[ROLE_ID_IJACK_SOFTWARE_DEV],
        )
    )
    with app.app_context():
        flask_adm.add_link(
            SecuredMenuLink(
                category="Other",
                name="Website Profiler Page",
                endpoint="profiler",
                url="/profiler",
                role_ids_accepted=[ROLE_ID_IJACK_SOFTWARE_DEV],
            )
        )
        flask_adm.add_link(
            SecuredMenuLink(
                category="Other",
                name="Simulate Long Max-CPU Request",
                endpoint="auth.simulate_hard",
                url="/simulate_hard/",
                role_ids_accepted=[ROLE_ID_IJACK_SOFTWARE_DEV],
            )
        )

    flask_adm.add_view(
        WebsiteViewView(
            WebsiteView,
            db.session,
            category="Other",
            name="Website Views",
            endpoint="website_views",
            url="/admin/website_views",
        )
    )
    flask_adm.add_view(
        WebsiteViewMostActiveView(
            WebsiteViewMostActive,
            db.session,
            category="Other",
            name="Website Most Active Users",
            endpoint="website_most_active_users",
            url="/admin/website_most_active_users",
        )
    )
    flask_adm.add_view(
        WebsiteMostActiveView(
            WebsiteView,
            db.session,
            category="Other",
            name="Website Most Active Users - filterable",
            endpoint="website_most_active_users_filterable",
            url="/admin/website_most_active_users_filterable",
        )
    )
    flask_adm.add_view(
        ImageFieldView(
            ImageField,
            db.session,
            category="Other",
            name="Images",
            endpoint="images",
            url="/admin/images",
        )
    )

    # Machine learning views
    flask_adm.add_view(
        CompressionImageView(
            CompressionImage,
            db.session,
            category="Machine Learning",
            name="Compression Card Image Classification",
            endpoint="compression_images",
            url="/admin/compression_images",
            role_ids_accepted=[ROLE_ID_IJACK_MACHINE_LEARNING],
        )
    )
    flask_adm.add_view(
        SurfaceImageView(
            SurfaceImage,
            db.session,
            category="Machine Learning",
            name="Surface Card Image Classification",
            endpoint="surface_images",
            url="/admin/surface_images",
            role_ids_accepted=[ROLE_ID_IJACK_MACHINE_LEARNING],
        )
    )
    flask_adm.add_view(
        CompressionPatternView(
            CompressionPattern,
            db.session,
            category="Machine Learning",
            name="Compression Card Patterns",
            endpoint="compression_patterns",
            url="/admin/compression_patterns",
            role_ids_accepted=[ROLE_ID_IJACK_MACHINE_LEARNING],
        )
    )
    flask_adm.add_view(
        # Re-use the same view for both Compression and Surface patterns
        CompressionPatternView(
            SurfacePattern,
            db.session,
            category="Machine Learning",
            name="Surface Card Patterns",
            endpoint="surface_patterns",
            url="/admin/surface_patterns",
            role_ids_accepted=[ROLE_ID_IJACK_MACHINE_LEARNING],
        )
    )

    # "Alerts" menu
    flask_adm.add_view(
        AlertView(
            Alert,
            db.session,
            category="Alerts",
            name="Alerts",
            endpoint="alerts",
            url="/admin/alerts",
        )
    )
    flask_adm.add_view(
        AlertsSentView(
            AlertsSent,
            db.session,
            category="Alerts",
            name="Alerts Sent",
            endpoint="alerts_sent",
            url="/admin/alerts_sent",
        )
    )
    flask_adm.add_view(
        AlertCustomView(
            AlertCustom,
            db.session,
            category="Alerts",
            name="Alerts - Custom",
            endpoint="alerts_custom",
            url="/admin/alerts_custom",
        )
    )
    flask_adm.add_view(
        ReportEmailHourlyView(
            ReportEmailHourly,
            db.session,
            category="Alerts",
            name="Hourly Update Email",
            endpoint="hourly_update_report",
            url="/admin/hourly_update_report",
        )
    )
    flask_adm.add_view(
        ReportEmailDeratesView(
            ReportEmailDerates,
            db.session,
            category="Alerts",
            name="Derates Email",
            endpoint="derates_report",
            url="/admin/derates_report",
        )
    )
    flask_adm.add_view(
        ReportEmailOpHoursView(
            ReportEmailOpHours,
            db.session,
            category="Alerts",
            name="Operating Hours Email (100, 1000, etc)",
            endpoint="op_hours_report",
            url="/admin/op_hours_report",
        )
    )
    flask_adm.add_view(
        ReportEmailInventoryView(
            ReportEmailInventory,
            db.session,
            category="Alerts",
            name="Inventory Email",
            endpoint="inventory_report",
            url="/admin/inventory_report",
        )
    )
    flask_adm.add_view(
        AlertsSentUsersView(
            AlertsSentUser,
            db.session,
            category="Alerts",
            name="Alerts Sent - Users Alerted",
            endpoint="alerts_sent_users",
            url="/admin/alerts_sent_users",
        )
    )
    flask_adm.add_view(
        RemoteControlView(
            RemoteControl,
            db.session,
            category="Alerts",
            name="Remote Control Actions",
            endpoint="remote_control",
            url="/admin/remote_control",
        )
    )
    flask_adm.add_view(
        AlertsSentMaintView(
            AlertsSentMaint,
            db.session,
            category="Alerts",
            name="Alerts Sent - Maintenance",
            endpoint="alerts_sent_maintenance",
            url="/admin/alerts_sent_maintenance",
        )
    )
    flask_adm.add_view(
        AlertsSentMaintUsersView(
            AlertsSentMaintUser,
            db.session,
            category="Alerts",
            name="Alerts Sent - Maintenance - Users Alerted",
            endpoint="alerts_sent_maintenance_users",
            url="/admin/alerts_sent_maintenance_users",
        )
    )

    # Reports menu
    flask_adm.add_view(
        VwSalesByPersonYearView(
            VwSalesByPersonYear,
            db.session,
            category="Reports",
            name="Sales by Person by Year",
            endpoint="sales_by_person_year",
            url="/admin/sales_by_person_year",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )
    flask_adm.add_view(
        VwSalesByPersonQuarterView(
            VwSalesByPersonQuarter,
            db.session,
            category="Reports",
            name="Sales by Person by Quarter",
            endpoint="sales_by_person_quarter",
            url="/admin/sales_by_person_quarter",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )
    flask_adm.add_view(
        VwSalesByPersonMonthView(
            VwSalesByPersonMonth,
            db.session,
            category="Reports",
            name="Sales by Person by Month",
            endpoint="sales_by_person_month",
            url="/admin/sales_by_person_month",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    # "Service" menu
    flask_adm.add_view(
        WorkOrderIncView(
            WorkOrder,
            db.session,
            category="Service",
            name="Work Orders (IJACK Inc 🍁)",
            endpoint="work_orders",
            url="/admin/work_orders",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )
    flask_adm.add_view(
        WorkOrderCorpView(
            WorkOrder,
            db.session,
            category="Service",
            name="Work Orders (IJACK Corp - USA)",
            endpoint="work_orders_corp",
            url="/admin/work_orders_corp",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )
    flask_adm.add_view(
        WorkOrderIncQuoteView(
            WorkOrder,
            db.session,
            category="Service",
            name="Work Order Quotes (IJACK Inc 🍁)",
            endpoint="work_order_quotes",
            url="/admin/work_order_quotes",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )
    flask_adm.add_view(
        WorkOrderCorpQuoteView(
            WorkOrder,
            db.session,
            category="Service",
            name="Work Order Quotes (IJACK Corp - USA)",
            endpoint="work_order_quotes_corp",
            url="/admin/work_order_quotes_corp",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    # # To manage static files in addition to database records, Flask-Admin comes with the FileAdmin plug-in
    # path = op.join(op.dirname(__file__), 'static')
    # flask_adm.add_view(SecuredFileAdmin(path, '/static/', name='Static Files'))

    # Add other links to the admin page, beside the RCOM button on the right
    with app.app_context():
        # "Service" menu
        flask_adm.add_link(
            SecuredMenuLink(
                category="Service",
                name="Create Work Order (IJACK Inc 🍁)",
                # Dash blueprint
                endpoint="dash.work_order",
                role_ids_accepted=[
                    ROLE_ID_IJACK_SALES,
                    ROLE_ID_IJACK_SERVICE,
                    ROLE_ID_IJACK_HUMAN_RESOURCES,
                ],
            )
        )
        flask_adm.add_link(
            SecuredMenuLink(
                category="Service",
                name="Create Work Order (IJACK Corp - USA)",
                # Dash blueprint
                endpoint="dash.work_order_corp",
                role_ids_accepted=[
                    ROLE_ID_IJACK_SALES,
                    ROLE_ID_IJACK_SERVICE,
                    ROLE_ID_IJACK_HUMAN_RESOURCES,
                ],
            )
        )
        flask_adm.add_link(SecuredMenuLink(name="Logout", endpoint="auth.logout"))

    flask_adm.add_view(
        WorkOrdersByUnitView(
            WorkOrdersByUnit,
            db.session,
            category="Service",
            name="Work Orders by Unit",
            endpoint="work_orders_by_unit",
            url="/admin/work_orders_by_unit",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    # Parts referenced in work orders
    flask_adm.add_view(
        WorkOrderPartView(
            WorkOrderPart,
            db.session,
            category="Service",
            name="Work Order Parts (Line Items)",
            endpoint="work_order_parts",
            url="/admin/work_order_parts",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    flask_adm.add_view(
        ContactFormView(
            ContactForm,
            db.session,
            category="Service",
            name="Contact Form Submissions",
            endpoint="contact_form",
            url="/admin/contact_form",
        )
    )

    flask_adm.add_view(
        ServiceView(
            Service,
            db.session,
            category="Service",
            name="Service Requests",
            endpoint="service_requests",
            url="/admin/service_requests",
            role_ids_accepted=[ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SERVICE],
        )
    )

    flask_adm.add_view(
        ServiceEmaileeView(
            ServiceEmailee,
            db.session,
            category="Service",
            name="Service Request Email Recipients",
            endpoint="service_request_email_recipients",
            url="/admin/service_request_email_recipients",
            role_ids_accepted=[ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SERVICE],
        )
    )

    flask_adm.add_view(
        MaintenanceView(
            Maintenance,
            db.session,
            category="Service",
            name="Maintenance Records",
            endpoint="maintenance",
            url="/admin/maintenance",
            role_ids_accepted=[ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SERVICE],
        )
    )

    flask_adm.add_view(
        MaintenanceTypeView(
            MaintenanceType,
            db.session,
            category="Service",
            name="Maintenance Types",
            endpoint="maintenance_types",
            url="/admin/maintenance_types",
            role_ids_accepted=[ROLE_ID_IJACK_SALES, ROLE_ID_IJACK_SERVICE],
        )
    )

    flask_adm.add_view(
        ServiceClockView(
            ServiceClock,
            db.session,
            category="Service",
            name="Service Clock",
            endpoint="service_clock",
            url="/admin/service_clock",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )
    # Add this to views_admin.py after the Service category views

    # "Inventory" menu
    flask_adm.add_view(
        WarehouseView(
            Warehouse,
            db.session,
            category="Inventory",
            name="Warehouses",
            endpoint="inventory_warehouses",
            url="/admin/inventory_warehouses",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        WarehouseLocationView(
            WarehouseLocation,
            db.session,
            category="Inventory",
            name="Warehouse Locations",
            endpoint="warehouse_locations",
            url="/admin/warehouse_locations",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        WarehousePartView(
            WarehousePart,
            db.session,
            category="Inventory",
            name="Warehouse Parts (Current Stock)",
            endpoint="inventory_warehouse_parts",
            url="/admin/inventory_warehouse_parts",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        InventoryMovementView(
            InventoryMovement,
            db.session,
            category="Inventory",
            name="Inventory Movements (Audit Trail)",
            endpoint="inventory_movements",
            url="/admin/inventory_movements",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        InventoryReservationView(
            InventoryReservation,
            db.session,
            category="Inventory",
            name="Inventory Reservations",
            endpoint="inventory_reservations",
            url="/admin/inventory_reservations",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        CycleCountView(
            CycleCount,
            db.session,
            category="Inventory",
            name="Cycle Counts",
            endpoint="cycle_counts",
            url="/admin/cycle_counts",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        CycleCountItemView(
            CycleCountItem,
            db.session,
            category="Inventory",
            name="Cycle Count Items",
            endpoint="cycle_count_items",
            url="/admin/cycle_count_items",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        PartCategoryView(
            PartCategory,
            db.session,
            category="Inventory",
            name="Part Categories",
            endpoint="part_categories",
            url="/admin/part_categories",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        HoursBilledMonthlyEfficiencyView(
            HoursBilledMonthlyEfficiency,
            db.session,
            category="Reports",
            name="Hours Billed (Whole Team) - Monthly Efficiency",
            endpoint="hours_billed_monthly_efficiency",
            url="/admin/hours_billed_monthly_efficiency",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    flask_adm.add_view(
        HoursBilledByFieldTechMonthlyEfficiencyView(
            HoursBilledByFieldTechMonthlyEfficiency,
            db.session,
            category="Reports",
            name="Hours Billed by Field Tech - Monthly Efficiency",
            endpoint="hours_billed_by_field_tech_monthly_efficiency",
            url="/admin/hours_billed_by_field_tech_monthly_efficiency",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    flask_adm.add_view(
        VwHoursBilledByFieldTechByWorkOrderView(
            VwHoursBilledByFieldTechByWorkOrder,
            db.session,
            category="Reports",
            name="Hours Billed by Field Tech - by Work Order",
            endpoint="hours_billed_by_field_tech_by_work_order",
            url="/admin/hours_billed_by_field_tech_by_work_order",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    flask_adm.add_view(
        VwServiceClockHoursDailyView(
            VwServiceClockHoursDaily,
            db.session,
            category="Reports",
            name="Service Clock - Daily Hours",
            endpoint="service_clock_daily",
            url="/admin/service_clock_daily",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    flask_adm.add_view(
        VwServiceClockHoursMonthlyView(
            VwServiceClockHoursMonthly,
            db.session,
            category="Reports",
            name="Service Clock - Monthly Hours",
            endpoint="service_clock_monthly",
            url="/admin/service_clock_monthly",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    flask_adm.add_view(
        VwServiceClockHoursYearlyView(
            VwServiceClockHoursYearly,
            db.session,
            category="Reports",
            name="Service Clock - Yearly Hours",
            endpoint="service_clock_yearly",
            url="/admin/service_clock_yearly",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
                ROLE_ID_IJACK_HUMAN_RESOURCES,
            ],
        )
    )

    flask_adm.add_view(
        ServiceTypeView(
            ServiceType,
            db.session,
            category="Service",
            name="Work Order Service Type",
            endpoint="work_order_service_type",
            url="/admin/work_order_service_type",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    flask_adm.add_view(
        WorkOrderStatusView(
            WorkOrderStatus,
            db.session,
            category="Service",
            name="Work Order Status",
            endpoint="work_order_status",
            url="/admin/work_order_status",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_BOM_MASTER_PARTS_TABLES,
            ],
        )
    )

    # flask_adm.add_view(
    #     InventorySourceView(
    #         InventorySource,
    #         db.session,
    #         category="Service",
    #         name="Work Order Inventory Source",
    #         endpoint="work_order_inventory_source",
    #         url="/admin/work_order_inventory_source",
    #     )
    # )

    flask_adm.add_view(
        CurrencyView(
            Currency,
            db.session,
            category="Service",
            name="Currencies",
            endpoint="currencies",
            url="/admin/currencies",
        )
    )

    # Dashboards menu - Custom dropdown for Inertia dashboard pages
    flask_adm.add_link(
        SecuredMenuLink(
            category="Dashboards",
            name="Sales",
            url="/dashboards/sales",
            role_ids_accepted=[
                ROLE_ID_IJACK_SALES,
                ROLE_ID_IJACK_SOFTWARE_DEV,
            ],
        )
    )
    flask_adm.add_link(
        SecuredMenuLink(
            category="Dashboards",
            name="Service",
            url="/dashboards/services/overview",
            role_ids_accepted=[
                ROLE_ID_IJACK_SERVICE,
                ROLE_ID_IJACK_SOFTWARE_DEV,
            ],
        )
    )
