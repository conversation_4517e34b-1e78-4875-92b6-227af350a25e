# app/flask_admin/forms.py
from datetime import timed<PERSON><PERSON>
import os

from flask import session
from flask_admin.form import BaseForm
from wtforms.csrf.session import SessionCSRF
from wtforms.meta import DefaultMeta


class SecureFormCustom(BaseForm):
    """
    BaseForm with CSRF token generation and validation support.
    Requires WTForms 2+
    """

    class Meta(DefaultMeta):  # type: ignore
        """
        Default Meta class for setting up CSRF.
        The only thing this class changes, is the addition of the time limit
        """

        csrf = True
        csrf_class = SessionCSRF
        csrf_secret = str(os.getenv("SECRET_KEY")).encode("utf-8")
        csrf_time_limit = timedelta(days=5)

        # @property
        # def csrf_secret(self):
        #     """Get the app's secret key"""
        #     secret = os.getenv("SECRET_KEY", None) or self._csrf_secret
        #     if isinstance(secret, str):
        #         secret = secret.encode("utf-8")
        #     return secret

        @property
        def csrf_context(self):
            """Get the app's session"""
            return session
