from flask import abort, flash, redirect, request, url_for
from flask_admin import AdminIndexView, expose
from flask_login import current_user

from app import is_admin, is_admin_cust, user_is_demo_customer
from app.email_stuff import send_email_newsletter


class SecuredAdminCustIndexView(AdminIndexView):
    """
    Customized non-model (i.e. non-database-table) views,
    for general stuff like login and user registration
    """

    def is_accessible(self):
        """The user must be both authenticated, and an admin user to view these pages"""
        return current_user.is_authenticated and (is_admin_cust() or is_admin())

    def inaccessible_callback(self, name, **kwargs):
        # redirect to login page if user doesn't have access
        return redirect(url_for("dash.login", next=request.url))

    def is_visible(self):
        # A link/button to this view won't appear in the navbar as the first button right of the brand logo
        return False

    # This is the page that's seen at https://myijack.com/admin (the default page for admins)
    @expose("/")
    def index(self):
        if not current_user.is_authenticated:
            return redirect(url_for("dash.login", next=request.url))

        if is_admin_cust() or is_admin():
            # Jinja HTML template
            return self.render("/admin/custom/index_cust_admin.html")


class SecuredAdminIndexView(AdminIndexView):
    """
    Customized non-model (i.e. non-database-table) views,
    for general stuff like login and user registration
    """

    def is_accessible(self):
        """The user must be both authenticated, and an admin user to view these pages"""
        return current_user.is_authenticated and is_admin()

    def inaccessible_callback(self, name, **kwargs):
        """Redirect to login page if user doesn't have access"""
        return redirect(url_for("dash.login", next=request.url))

    def is_visible(self):
        """A link/button to this view won't appear in the navbar as the first button right of the brand logo"""
        return False

    def check_authenticated(self):
        """Check if the user has permissions to view the page. Otherwise abort"""
        if not current_user.is_authenticated:
            return redirect(url_for("dash.login", next=request.url))
        elif not is_admin():
            return abort(403)
        elif user_is_demo_customer(user_id=getattr(current_user, "id", None)):
            return abort(403)

    @expose("/")
    def index(self):
        """This is the page that's seen at https://myijack.com/admin (the default page for admins)"""
        self.check_authenticated()
        return self.render("/admin/custom/index_ijack_admin.html")

    @expose("/email_marketing/", methods=["GET", "POST"])
    def email_marketing(self):
        """For sending email marketing newsletters"""

        self.check_authenticated()

        return self.render(
            "/admin/custom/email_marketing.html",
            user=current_user,
            heading1="Email Marketing",
            title="Email Marketing",
        )

    @expose("/send_test_marketing_email/", methods=["GET"])
    def send_test_marketing_email(self):
        """Send a test RCOM update email to yourself"""

        self.check_authenticated()
        send_email_newsletter(
            email_type="rcom",
            send_to_yourself=True,
            testing=True,
            ijack_staff_only=True,
        )
        flash("The email has been sent!")

        return redirect("/admin/email_marketing")

    @expose("/send_marketing_email_all_customers/", methods=["GET"])
    def send_marketing_email_all_customers(self):
        """Send the RCOM update email to all customers"""

        self.check_authenticated()
        send_email_newsletter(
            email_type="rcom",
            send_to_yourself=False,
            testing=False,
            ijack_staff_only=False,
        )
        flash("The email has been sent!")

        return redirect("/admin/")

    @expose("/send_test_operators_email/", methods=["GET"])
    def send_test_operators_email(self):
        """Send a test operators' service email to yourself"""

        self.check_authenticated()
        send_email_newsletter(
            email_type="service",
            send_to_yourself=True,
            testing=True,
            ijack_staff_only=True,
        )
        flash("The email has been sent!")

        return redirect("/admin/email_marketing")

    @expose("/send_operators_email_all_customers/", methods=["GET"])
    def send_operators_email_all_customers(self):
        """Send the operators' service email to all customers"""

        self.check_authenticated()
        send_email_newsletter(
            email_type="service",
            send_to_yourself=False,
            testing=False,
            ijack_staff_only=False,
        )
        flash("The email has been sent!")

        return redirect("/admin/")
