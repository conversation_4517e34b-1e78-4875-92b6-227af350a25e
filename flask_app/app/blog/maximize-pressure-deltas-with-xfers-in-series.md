title: Maximize Pressure Deltas with XFERs in Series
date: 2021-10-19
author: <PERSON>
description: Run multiple IJACK XFER multiphase pumps in series to maximize pressure deltas.

In this article we are going to explain the concept of multiple IJACK [XFERs](/xfer/) in series to achieve significantly increased pressure differentials. We will also share some simplified examples.

Oil and gas gathering systems commonly depend on pressure to drive and deliver product through pipelines and satellites all the way to collection and processing plants. While higher pressures provide more energy to drive the product the required distances, they can also hold back and reduce production volumes at the wellhead. This leads to the challenge of reducing pressure at the well to maximise production, while at the same time achieving sufficient drive pressures for gathering and delivery. IJACK hydraulic pumps and compressors provide a solution to this as stand-alone units that move multiphase product while lowering inlet pressures and/or increasing discharge pressure.

The limits of the operational pressure changes are related to each unit's maximum discharge pressure and delta P. The maximum discharge pressure (also known as the Maximum Allowable Working Pressure – MAWP) is the highest operational pressure that the model can deal with given the safety factors and classification of the unit. Typically, IJACK multiphase pumps are manufactured to ANSI 300 class, though 600 and 900 class are also available. This means the units are tested to required pressures based on ANSI specifications for safety and reliability. ANSI 300 lb class requires hydrostatic test pressure of 1,110 PSI at -20 to 100 F° while ANSI 900 lb class requires a hydrostatic test pressure of 2,250 PSI at -20 to 100 F°. The maximum delta P (ΔP) of a pump or compressor is the maximum difference between the inlet and discharge pressure of the unit. It is calculated by subtracting the inlet pressure (Pi) from the discharge pressure (Pd) as follows: Pd – Pi = ΔP. NOTE: "Inlet" pressure is also commonly referred to as "suction" pressure.

<img alt="IJACK XFER inlet and discharge pressure diagram"
src="/static/img/blog/ijack-xfer-inlet-and-discharge-pressure-diagram.jpg"
class="img-fluid"
loading="lazy">

These maximum pressure values are a function of the internal seals within the unit as well as the mechanical energy available to drive the piston and move the multiphase product. For example, our typical 300 lb ANSI 1670 model has a maximum discharge pressure of 740 PSI (5102 kPa) and a maximum delta P of 480 PSI (3309 kPa).

This single unit is able to provide a wide range of operating set points that would safisfy the two constraints. Let's look at a few simple options:
To reduce or minimise the line or wellhead pressure, one could choose a low inlet pressure of 5 - 10 PSI and a discharge of 460 - 490 PSI (which is within the max discharge of 740 PSI and the 480 PSI delta P).

Alternatively, if we wanted to produce into a line with a high pipeline pressure, we could aim for a max discharge of 740 PSI. Using the full available delta P, this would allow for a corresponding minimum inlet pressure of around 260 – 280 PSI.

The unit should be able to comfortably operate between these two low- and high-pressure values provided the delta P across the unit stays at or below the 480 PSI max delta P. Note that volume throughput, and fluid properties, can affect the available mechanical energy and lower the potential delta P below the maximum stated value.

# Two XFERs in Series

Now, suppose we want to maximise the production of a well by achieving a low inlet pressure (5 PSI) at the well site. However, the well is located at the bottom of a distant valley and there is significant vertical fluid head pressure and line friction losses that must be exceeded to deliver to the local battery (700 PSI). As the required discharge pressure is 700 PSI, a single unit's delta P would limit our inlet to a minimum of 220 PSI (700 discharge – 480 ΔP = 220 suction pressure). To achieve both our inlet and discharge pressure requirements we need a higher overall delta P than one unit is capable of. This is where multiple units can vastly expand the operational capability of your IJACK equipment.

When you install two or more units in series (one unit feeding directly into the next), you can stack the delta P differentials of each unit on top of one another for a higher total result. In this case the first unit (Unit 1) pulls in the lowest inlet pressure (Pi1).  This unit then discharges at the first discharge pressure (Pd1) which flows directly into the second unit (Pi2). In this way, the discharge of one unit is the inlet to the next pump (Pd1 = Pi2). You can even add a third unit. Unit 2 discharges (Pd2) into Unit 3 (Pi3) and so forth. The ultimate result is that for 3 units in series, a higher overall delta P of (Pd3 – Pi1 = TOTAL ΔP) is achieved. For X units in series the calculation is (PdX – Pi1 = TOTAL ΔP).

<img alt="IJACK XFER inlet and discharge pressure diagram"
src="/static/img/blog/ijack-xfers-in-series-to-maximize-delta-p.jpg"
class="img-fluid"
loading="lazy">

Placing units in series allows you to operate from a minimum all the way up to the max discharge pressure of the final unit. With the 1670 300 ANSI XFER mentioned above, you could run two units in series and have an inlet pressure of 5 (or lower) PSI and a final discharge pressure of 740 PSI. If you need a larger final discharge pressure than is available from a 300-class unit, a higher ANSI 600 or 900 model can be used. For example, a 1070 high pressure "XFER" unit could be used in series to bring the discharge pressure as high as 2,160 PSI (14,899 kPa).

In summary, IJACK multiphase pumps installed in series can bring a minimal inlet pressure up to a discharge over 2,000 PSI. The final pressure is only constrained by the maximum allowable working pressure of the equipment.

If you have any questions on how pressure affects production and gathering systems, or would like to know how IJACK solutions can address those very challenges please don't hesitate to [contact us](/contact/)!
