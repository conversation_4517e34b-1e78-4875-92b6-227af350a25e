# /app/flask_profiler/__init__.py

import functools
import logging
import re
from pprint import pprint

import psutil
from dash.exceptions import PreventUpdate
from flask import Blueprint, Flask, abort, jsonify, request
from flask_login import current_user, login_required

from app.flask_profiler.storage import get_collection
from app.flask_profiler.storage.base import BaseStorage
from app.utils.simple import utcnow_naive

logger = logging.getLogger("flask-profiler")


class Measurement(object):
    """Represents an endpoint measurement"""

    DECIMAL_PLACES = 6
    cpu_start = None
    cpu_end = None

    def __init__(
        self,
        args: dict,
        kwargs: dict,
        endpoint_name: str,
        endpoint: str,
        url: str,
        request_args: dict,
        form: dict,
        body: str,
        headers: dict,
        ip: str,
        user_agent: str,
        path: str,
        referrer: str,
        query_string: str,
        scheme: str,
        files: list,
        method: str = None,
        status_code: int = None,
        user_id: int = None,
    ):
        super(Measurement, self).__init__()
        self.args = args
        self.kwargs = kwargs
        self.endpoint_name = endpoint_name
        self.endpoint = endpoint
        self.url = url
        self.request_args = request_args
        self.form = form
        self.body = body
        self.headers = headers
        self.ip = ip
        self.method = method
        self.status_code = status_code
        self.user_agent = user_agent
        self.path = path
        self.referrer = referrer
        self.query_string = query_string
        self.scheme = scheme
        self.files = files
        self.user_id = user_id

        self.started_at = 0.0
        self.ended_at = 0.0
        self.timestamp_utc_started = None
        self.timestamp_utc_ended = None
        self.elapsed = None

    def __json__(self):
        return {
            "endpoint_name": self.endpoint_name,
            "endpoint": self.endpoint,
            "url": self.url,
            "request_args": self.request_args,
            "args": self.args,
            "kwargs": self.kwargs,
            "form": self.form,
            "body": self.body,
            "headers": self.headers,
            "ip": self.ip,
            "method": self.method,
            "status_code": self.status_code,
            "user_agent": self.user_agent,
            "path": self.path,
            "referrer": self.referrer,
            "query_string": self.query_string,
            "scheme": self.scheme,
            "files": self.files,
            "started_at": self.started_at,
            "ended_at": self.ended_at,
            "timestamp_utc_started": self.timestamp_utc_started,
            "timestamp_utc_ended": self.timestamp_utc_ended,
            "elapsed": self.elapsed,
            "user_id": self.user_id,
            "cpu_start": self.cpu_start,
            "cpu_end": self.cpu_end,
        }

    def __str__(self):
        return str(self.__json__())

    def start(self):
        """Start the measurement timer"""
        # we use default_timer to get the best clock available.
        # see: http://stackoverflow.com/a/25823885/672798
        # self.started_at = time.time()
        self.timestamp_utc_started = utcnow_naive()
        self.started_at = self.timestamp_utc_started.timestamp()
        # Do this one with a really short interval since it's just a snapshot
        # and start() always runs, whereas stop() only runs if the request is longer than the threshold
        self.cpu_start = psutil.cpu_percent(interval=None)

    def stop(self):
        """Stop the measurement timer and calculate the elapsed time"""
        # self.ended_at = time.time()
        self.timestamp_utc_ended = utcnow_naive()
        self.ended_at = self.timestamp_utc_ended.timestamp()
        self.elapsed = round(self.ended_at - self.started_at, self.DECIMAL_PLACES)
        # self.elapsed = round(
        #     (self.timestamp_utc_ended - self.timestamp_utc_started).total_seconds(),
        #     self.DECIMAL_PLACES,
        # )
        # Do this if the request is longer than the threshold
        # self.cpu_end = psutil.cpu_percent(interval=None)


class Profiler(object):
    """Wrapper for extension."""

    is_first_request: bool = True
    collection: BaseStorage = None
    auth = None

    def __init__(self, app: Flask = None):
        if app is not None:
            self.init_app(app)

    def init_app(self, app: Flask) -> None:
        """
        Initializes the flask-profiler extension.
        :param app: Flask application instance
        :return: None
        """
        self.config: dict = app.config.get(
            "flask_profiler", app.config.get("FLASK_PROFILER", None)
        )
        if not self.config:
            raise Exception(
                "To init flask-profiler, provide required config through Flask app's config. Please refer to the following: https://github.com/muatik/flask-profiler"
            )

        if not self.config.get("enabled", False):
            return None

        basic_auth: dict = self.config.get("basic_auth", None)
        if basic_auth and basic_auth.get("enabled", False):
            from requests.auth import HTTPBasicAuth

            self.auth = HTTPBasicAuth()

            @self.auth.verify_password
            def verify_password(username: str, password: str) -> bool:
                """Verify the username and password for basic auth"""
                if (
                    username == basic_auth["username"]
                    and password == basic_auth["password"]
                ):
                    return True
                logger.warning("flask-profiler authentication failed")
                return False
        else:
            # logger.warning(" * CAUTION: flask-profiler is working without basic auth!")
            pass

        self.register_internal_routers(app)

        # init = functools.partial(self._init_app, app)
        # app.before_first_request(init)
        @app.before_request
        def collect_end_points_on_first_request() -> None:
            """Collects all endpoints on the first request"""
            if self.is_first_request:
                # Set this to False so this function doesn't run again
                self.is_first_request = False
                self.collection = get_collection(self.config.get("storage", {}))
                self.wrap_app_endpoints(app)
            return None

        return None

    def is_ignored(self, endpoint_name: str) -> bool:
        """
        If the endpoint is in the 'ignore' list, return True.
        These patterns take time and effort to check.
        Perhaps better not to measure requests that are fast and don't need optimization.
        """
        ignore_patterns = self.config.get("ignore", [])
        for pattern in ignore_patterns:
            if re.search(pattern, endpoint_name):
                return True
        return False

    def measure(
        self,
        func: callable,
        endpoint_name: str,
        endpoint: str,
        url: str,
        request_args: dict,
        form: dict,
        body: str,
        headers: dict,
        ip: str,
        user_agent: str,
        path: str,
        referrer: str,
        query_string: str,
        scheme: str,
        files: list,
        method: str = None,
        status_code: int = None,
    ) -> callable:
        """
        Measures the time of the given function and stores it in the collection.
        :param func: function to measure
        :param endpoint_name: name of the endpoint
        :param method: HTTP method
        :param context: additional context to store
        :return: wrapped function
        """
        # Logging takes time, so only log if the endpoint is not ignored
        # logger.debug("%s is being processed.", endpoint_name)

        if self.is_ignored(endpoint_name):
            # logger.debug("%s is ignored.", endpoint_name)
            return func

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            """This is the function that will be called instead of the original function"""

            # Get the sampling function from the config and call it
            sampling_function: callable = self.config.get("sampling_function", None)
            if sampling_function:
                if not isinstance(sampling_function, callable):
                    raise Exception(
                        "if sampling_function is provided to flask-profiler via config, "
                        "it must be callable, refer to: "
                        "https://github.com/muatik/flask-profiler#sampling"
                    )
                if not sampling_function():
                    # If the sampling function returns False, just
                    # run the function normally, and don't measure this request
                    return func(*args, **kwargs)

            measurement = Measurement(
                args=args,
                kwargs=kwargs,
                endpoint_name=endpoint_name,
                endpoint=endpoint,
                url=url,
                request_args=request_args,
                form=form,
                body=body,
                headers=headers,
                ip=ip,
                method=method,
                status_code=status_code,
                user_agent=user_agent,
                path=path,
                referrer=referrer,
                query_string=query_string,
                scheme=scheme,
                files=files,
            )
            measurement.start()

            return_value: dict = {}
            try:
                return_value = func(*args, **kwargs)
            except PreventUpdate:
                raise
            except Exception as err:
                logger.debug("Error in %s: %s", endpoint_name, err)
                raise
            finally:
                measurement.stop()
                # Only store measurements that are longer than the threshold,
                # since it takes time and space to store them
                if measurement.elapsed > self.config.get("elapsed_threshold", 1):
                    measurement.user_id = getattr(current_user, "id", None)
                    measurement.status_code = getattr(return_value, "status_code", None)
                    measurement.method = getattr(request, "method", None)
                    # Take a bit more time to get the accurate CPU usage after the request
                    measurement.cpu_end = psutil.cpu_percent(interval=0.1)
                    if self.config.get("verbose", False):
                        # Pretty print the measurement
                        pprint(measurement.__json__())
                    json_measurement = measurement.__json__()
                    self.collection.insert(json_measurement)

            return return_value

        return wrapper

    def wrap_http_endpoint(self, func):
        """Wrap a single HTTP endpoint"""

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            wrapped = self.measure(
                func=func,
                endpoint_name=str(request.url_rule),
                endpoint=request.endpoint,
                url=request.base_url,
                request_args=dict(request.args.items()),
                form=dict(request.form.items()),
                body=request.data.decode("utf-8", "strict"),
                headers=dict(request.headers.items()),
                ip=request.remote_addr,
                # method=request.method,
                # status_code=request.status_code,
                user_agent=request.user_agent.string,
                path=request.path,
                referrer=request.referrer,
                query_string=request.query_string.decode("utf-8", "strict"),
                scheme=request.scheme,
                files=[file.filename for file in request.files.values()],
            )
            return wrapped(*args, **kwargs)

        return wrapper

    def wrap_app_endpoints(self, app) -> None:
        """
        Wraps all endpoints defined in the given Flask app to measure how long time
        each endpoints takes while being executed. This wrapping process is
        not supposed to change endpoint behaviour.
        :param app: Flask application instance
        :return:
        """
        for endpoint, func in app.view_functions.items():
            app.view_functions[endpoint] = self.wrap_http_endpoint(func)

        return None

    def profile(self, *args, **kwargs):
        """
        HTTP endpoint decorator
        """
        if self.config:

            def wrapper(func):
                return self.wrap_http_endpoint(func)

            return wrapper
        raise Exception("before measuring anything, you need to call init_app()")

    def register_internal_routers(self, app: Flask) -> None:
        """
        These are the endpoints which are used to display measurements in the
        flask-profiler dashboard.

        Note: these should be defined after wrapping user defined endpoints
        via wrap_app_endpoints()
        :param app: Flask application instance
        :return:
        """
        url_path = self.config.get("url_prefix", "profiler")

        blueprint = Blueprint(
            "flask_profiler",
            __name__,
            url_prefix=f"/{url_path}",
            static_folder="static/dist/",
            static_url_path="/static/dist",
        )

        from app import is_admin

        @blueprint.before_request
        def check_if_ijack_admin():
            """Check if the user is an IJACK admin before allowing access to the endpoint"""
            if not is_admin():
                return abort(403, "Only IJACK admins can access this endpoint")

        @blueprint.after_request
        def x_robots_tag_header(response):
            """After each request, add the X-Robots-Tag header to prevent indexing"""
            response.headers["X-Robots-Tag"] = "noindex, nofollow"
            return response

        @blueprint.route("/".format())
        @login_required
        def index():
            return blueprint.send_static_file("index.html")

        @blueprint.route("/api/measurements/".format())
        @login_required
        def filterMeasurements():
            args = dict(request.args.items())
            measurements = self.collection.filter(args)
            return jsonify({"measurements": list(measurements)})

        @blueprint.route("/api/measurements/grouped".format())
        @login_required
        def getMeasurementsSummary():
            args = dict(request.args.items())
            measurements = self.collection.get_summary(args)
            return jsonify({"measurements": list(measurements)})

        @blueprint.route("/api/measurements/<measurementId>".format())
        @login_required
        def getContext(measurementId):
            return jsonify(self.collection.get(measurementId))

        @blueprint.route("/api/measurements/timeseries/".format())
        @login_required
        def getRequestsTimeseries():
            """Get the time series of requests"""
            args = dict(request.args.items())
            return jsonify({"series": self.collection.get_time_series(args)})

        @blueprint.route("/api/measurements/method_distribution/".format())
        @login_required
        def get_method_distribution():
            """Get the distribution of methods"""
            args = dict(request.args.items())
            return jsonify(
                {"distribution": self.collection.get_method_distribution(args)}
            )

        # @blueprint.route("/db/dump_database")
        # @login_required
        # def dump_database():
        #     """Dump all measurements from the database"""
        #     # response = jsonify({"summary": self.collection.get_summary()})
        #     # response.headers["Content-Disposition"] = "attachment; filename=dump.json"
        #     response = jsonify({"status": "Not actually dumping the entire database"})
        #     return response

        # @blueprint.route("/db/delete_database")
        # @login_required
        # def delete_database():
        #     """Delete all measurements from the database"""
        #     # Don't actually do this in production
        #     # response = jsonify({"status": self.collection.truncate()})
        #     response = jsonify({"status": "Not actually truncating the database"})
        #     return response

        app.register_blueprint(blueprint)

        return None
