import json
import time
from datetime import datetime
from decimal import ROUND_UP, Decimal
from typing import NoReturn

from flask import current_app, has_app_context
from shared.models.models import Measurement
from sqlalchemy import create_engine, func
from sqlalchemy.orm import declarative_base

from app import db
from app.utils.simple import utcfromtimestamp_naive

from .base import BaseStorage

base = declarative_base()


class Sqlalchemy(BaseStorage):
    """Sqlalchemy storage engine for Flask Profiler."""

    def __init__(self, config=None):
        """Initialize the Sqlalchemy storage engine."""
        super(Sqlalchemy, self).__init__()
        self.config = config

        # db_url: str = self.config.get("db_url", "sqlite:///flask_profiler.sql")
        app_context = None
        if not has_app_context():
            app_context = current_app.app_context()
            app_context.push()
        db_url: str = current_app.config.get(
            "SQLALCHEMY_DATABASE_URI", "sqlite:///flask_profiler.sql"
        )
        if app_context:
            app_context.pop()

        self.db = create_engine(db_url)
        # self.create_database()

    def __enter__(self):
        return self

    def format_date(self, timestamp: int, date_format: str) -> str:
        """Format the timestamp to the date format."""
        return utcfromtimestamp_naive(timestamp).strftime(date_format)

    def insert(self, measurement: dict) -> NoReturn:
        """Insert the measurement data into the database."""

        ended_at = int(measurement.get("ended_at", None))
        started_at = int(measurement.get("started_at", None))
        timestamp_utc_started = measurement.get("timestamp_utc_started", None)
        timestamp_utc_ended = measurement.get("timestamp_utc_ended", None)
        elapsed = Decimal(measurement.get("elapsed", None))
        if elapsed:
            # round up to 4 decimal places
            elapsed = elapsed.quantize(Decimal(".0001"), rounding=ROUND_UP)

        args = json.dumps(list(measurement.get("args", ())))  # tuple -> list -> json
        kwargs = json.dumps(measurement.get("kwargs", ()))
        # context = json.dumps(measurement.get("context", {}))
        method = measurement.get("method", None)
        endpoint_name = measurement.get("endpoint_name", None)
        endpoint = measurement.get("endpoint", None)
        user_id = measurement.get("user_id", None)
        url = measurement.get("url", None)
        request_args = json.dumps(list(measurement.get("request_args", ())))
        form = json.dumps(measurement.get("form", None))
        body = json.dumps(measurement.get("body", None))
        headers = json.dumps(measurement.get("headers", None))
        ip = measurement.get("ip", None)
        status_code = measurement.get("status_code", None)
        user_agent = measurement.get("user_agent", None)
        path = measurement.get("path", None)
        referrer = measurement.get("referrer", None)
        query_string = measurement.get("query_string", None)
        scheme = measurement.get("scheme", None)
        files = measurement.get("files", None)
        cpu_start = measurement.get("cpu_start", None)
        cpu_end = measurement.get("cpu_end", None)

        try:
            # # session = sessionmaker(self.db)()
            model = Measurement(
                ended_at=ended_at,
                started_at=started_at,
                timestamp_utc_started=timestamp_utc_started,
                timestamp_utc_ended=timestamp_utc_ended,
                elapsed=elapsed,
                args=args,
                kwargs=kwargs,
                method=method,
                user_id=user_id,
                endpoint_name=endpoint_name,
                endpoint=endpoint,
                url=url,
                request_args=request_args,
                form=form,
                body=body,
                headers=headers,
                ip=ip,
                status_code=status_code,
                user_agent=user_agent,
                path=path,
                referrer=referrer,
                query_string=query_string,
                scheme=scheme,
                files=files,
                cpu_start=cpu_start,
                cpu_end=cpu_end,
            )
            db.session.add(model)
            db.session.commit()
        except Exception:
            db.session.rollback()
            current_app.logger.exception(
                "Error inserting profiler measurement into database: %s", measurement
            )

    @staticmethod
    def get_filters(kwargs: dict) -> dict:
        """Get the filters from the kwargs and return them as a dictionary."""
        filters = {}
        filters["sort"] = kwargs.get("sort", "ended_at,desc").split(",")

        # because inserting and filtering may take place at the same moment,
        # a very little increment(0.5) is needed to find inserted
        # record by sql.
        filters["ended_at"] = float(kwargs.get("ended_at", time.time() + 0.5))
        filters["started_at"] = float(
            kwargs.get("started_at", time.time() - 3600 * 24 * 7)
        )

        filters["elapsed"] = kwargs.get("elapsed", None)
        filters["method"] = kwargs.get("method", None)
        filters["endpoint_name"] = kwargs.get("endpoint_name", None)
        filters["user_id"] = kwargs.get("user_id", None)
        # args tuple -> list -> json
        filters["args"] = json.dumps(list(kwargs.get("args", ())))
        filters["kwargs"] = json.dumps(kwargs.get("kwargs", ()))
        filters["sort"] = kwargs.get("sort", "ended_at,desc").split(",")
        filters["skip"] = int(kwargs.get("skip", 0))
        filters["limit"] = int(kwargs.get("limit", 100))

        return filters

    def filter(self, criteria={}) -> iter:
        """Filter the records from the database and return the generator."""
        # Find Operation
        filters: dict = Sqlalchemy.get_filters(criteria)
        # db.# session = sessionmaker(self.db)()
        query = db.session.query(Measurement)

        if filters["ended_at"]:
            query = query.filter(Measurement.ended_at <= filters["ended_at"])
        if filters["started_at"]:
            query = query.filter(Measurement.started_at >= filters["started_at"])
        if filters["elapsed"]:
            query = query.filter(Measurement.elapsed >= filters["elapsed"])
        if filters["method"]:
            query = query.filter(Measurement.method == filters["method"])
        if filters["endpoint_name"]:
            query = query.filter(Measurement.endpoint_name == filters["endpoint_name"])
        if filters["user_id"]:
            query = query.filter(Measurement.user_id == filters["user_id"])
        if filters["sort"][1] == "desc":
            query = query.order_by(getattr(Measurement, filters["sort"][0]).desc())
        else:
            query = query.order_by(getattr(Measurement, filters["sort"][0]).asc())
        rows = query.limit(filters["limit"]).offset(filters["skip"])

        # return the generator
        return (Sqlalchemy.jsonify_row(row) for row in rows)

    @staticmethod
    def jsonify_row(row: Measurement) -> dict:
        """Convert the row to a dictionary."""
        data = {
            "id": row.id,
            "timestamp_utc_started": row.timestamp_utc_started,
            "timestamp_utc_ended": row.timestamp_utc_ended,
            "started_at": row.started_at,
            "ended_at": row.ended_at,
            "elapsed": row.elapsed,
            "method": row.method,
            # JSON -> list -> tuple
            "request_args": tuple(json.loads(row.request_args or "[]")),
            "args": tuple(json.loads(row.args or "[]")),
            "kwargs": json.loads(row.kwargs or "{}"),
            "endpoint_name": row.endpoint_name,
            "endpoint": row.endpoint,
            # "context": json.loads(row.context),
            "user_id": row.user_id,
            "url": row.url,
            "form": row.form,
            "body": json.loads(row.body or "{}"),
            "headers": json.loads(row.headers or "{}"),
            "ip": row.ip,
            "status_code": row.status_code,
            "user_agent": row.user_agent,
            "path": row.path,
            "referrer": row.referrer,
            "query_string": row.query_string,
            "scheme": row.scheme,
            "files": row.files,
        }
        return data

    def truncate(self) -> NoReturn:
        """Delete all the records from the database."""
        # session = sessionmaker(self.db)()
        try:
            db.session.query(Measurement).delete()
            db.session.commit()
        except Exception:
            db.session.rollback()
            current_app.logger.exception("Error truncating profiler measurements")

    def delete(self, measurementId: int) -> NoReturn:
        """Delete a record from the database, given its id."""
        # session = sessionmaker(self.db)()
        try:
            db.session.query(Measurement).filter_by(id=measurementId).delete()
            db.session.commit()
            return True
        except Exception:
            db.session.rollback()
            current_app.logger.exception(
                f"Error deleting profiler measurement with id: {measurementId}"
            )
            return False

    def get_summary(self, criteria={}) -> list:  # type: ignore
        """Get the summary of the measurements."""
        filters = Sqlalchemy.get_filters(criteria)
        # session = sessionmaker(self.db)()
        count = func.count(Measurement.id).label("count")
        min_elapsed = func.min(Measurement.elapsed).label("minElapsed")
        max_elapsed = func.max(Measurement.elapsed).label("maxElapsed")
        avg_elapsed = func.avg(Measurement.elapsed).label("avgElapsed")
        query = db.session.query(
            Measurement.method,
            Measurement.endpoint_name,
            count,
            min_elapsed,
            max_elapsed,
            avg_elapsed,
        )

        if filters["started_at"]:
            query = query.filter(Measurement.started_at >= filters["started_at"])
        if filters["ended_at"]:
            query = query.filter(Measurement.ended_at <= filters["ended_at"])
        if filters["elapsed"]:
            query = query.filter(Measurement.elapsed >= filters["elapsed"])
        if filters["user_id"]:
            query = query.filter(Measurement.user_id == filters["user_id"])

        query = query.group_by(Measurement.method, Measurement.endpoint_name)
        if filters["sort"][1] == "desc":
            if filters["sort"][0] == "count":
                query = query.order_by(count.desc())
            elif filters["sort"][0] == "minElapsed":
                query = query.order_by(min_elapsed.desc())
            elif filters["sort"][0] == "maxElapsed":
                query = query.order_by(max_elapsed.desc())
            elif filters["sort"][0] == "avgElapsed":
                query = query.order_by(avg_elapsed.desc())
            else:
                query = query.order_by(getattr(Measurement, filters["sort"][0]).desc())
        else:
            if filters["sort"][0] == "count":
                query = query.order_by(count.asc())
            elif filters["sort"][0] == "minElapsed":
                query = query.order_by(min_elapsed.asc())
            elif filters["sort"][0] == "maxElapsed":
                query = query.order_by(max_elapsed.asc())
            elif filters["sort"][0] == "avgElapsed":
                query = query.order_by(avg_elapsed.asc())
            else:
                query = query.order_by(getattr(Measurement, filters["sort"][0]).asc())
        rows = query.all()

        result = []
        for r in rows:
            result.append(
                {
                    "method": r[0],
                    "endpoint_name": r[1],
                    "count": r[2],
                    "minElapsed": r[3],
                    "maxElapsed": r[4],
                    "avgElapsed": r[5],
                }
            )
        return result

    def get_time_series(self, kwds={}) -> dict:
        """Get the time series data."""
        filters = Sqlalchemy.get_filters(kwds)
        # session = sessionmaker(self.db)()
        if kwds.get("interval", None) == "daily":
            interval = 3600 * 24  # daily
            date_format = "%Y-%m-%d"
            # format = "day"
        else:
            interval = 3600  # hourly
            date_format = "%Y-%m-%d %H"
            # format = "hour"
        ended_at, started_at = filters["ended_at"], filters["started_at"]

        rows = (
            db.session.query(
                Measurement.started_at,
            )
            .filter(
                Measurement.ended_at <= ended_at, Measurement.started_at >= started_at
            )
            .order_by(Measurement.started_at.asc())
            .all()
        )

        # rows = [utcfromtimestamp_naive(row[0]).strftime(date_format) for row in rows]
        rows_new = []
        for row in rows:
            timestamp = row[0]
            if isinstance(timestamp, Decimal):
                timestamp = float(timestamp)
            ts_str = utcfromtimestamp_naive(timestamp).strftime(date_format)
            rows_new.append(ts_str)

        temp = set(rows_new)
        rows = [(time, rows.count(time)) for time in temp]
        series = {}

        for i in range(int(started_at), int(ended_at) + 1, interval):
            series[self.format_date(i, date_format)] = 0

        for row in rows:
            series[
                self.format_date(
                    datetime.strptime(row[0], date_format).timestamp(), date_format
                )
            ] = row[1]
        return series

    def get_method_distribution(self, kwds=None) -> dict:
        """Get the distribution of the methods."""
        if not kwds:
            kwds = {}
        filters: dict = Sqlalchemy.get_filters(kwds)
        # session = sessionmaker(self.db)()
        ended_at, started_at = filters["ended_at"], filters["started_at"]

        rows = (
            db.session.query(Measurement.method, func.count(Measurement.id))
            .filter(
                Measurement.ended_at <= ended_at, Measurement.started_at >= started_at
            )
            .group_by(Measurement.method)
            .all()
        )

        results = {}
        for row in rows:
            results[row[0]] = row[1]
        return results

    def __exit__(self, exc_type, exc_value, traceback):
        return self.db
