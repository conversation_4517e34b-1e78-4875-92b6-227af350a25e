class BaseStorage(object):
    """Storage collection base class."""

    def __init__(self):
        super(BaseStorage, self).__init__()

    def filter(self, criteria):
        raise Exception("Not implemented Error")

    def get_summary(self, criteria):
        raise Exception("Not implemented Error")

    def insert(self, measurement: dict):
        raise Exception("Not implemented Error")

    def delete(self, measurementId):
        raise Exception("Not implemented Error")

    def truncate(self):
        raise Exception("Not implemented Error")
