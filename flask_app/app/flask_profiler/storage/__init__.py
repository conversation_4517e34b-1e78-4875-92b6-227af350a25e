import importlib
import os
import sys
from contextlib import contextmanager

from .base import BaseStorage


@contextmanager
def cwd_in_path():
    """
    Add the current working directory to the sys.path,
    and then remove it when done.
    """
    cwd = os.getcwd()
    if cwd in sys.path:
        yield
    else:
        sys.path.insert(0, cwd)
        try:
            yield cwd
        finally:
            sys.path.remove(cwd)


def get_collection(conf: dict) -> BaseStorage:
    """Get the storage engine from the configuration."""

    engine = conf.get("engine", "")
    if engine.lower() == "sqlalchemy":
        from .sql_alchemy import Sqlalchemy

        return Sqlalchemy(conf)

    try:
        parts = engine.split(".")
        if len(parts) < 1:  # engine must have at least module name and class
            raise ImportError

        module_name = ".".join(parts[:-1])
        klass_name = parts[-1]

        # Make sure we can find the module in our project directory
        with cwd_in_path():
            module = importlib.import_module(module_name)

        storage = getattr(module, klass_name)
        if not issubclass(storage, BaseStorage):
            raise ImportError

    except ImportError:
        raise ValueError(
            (
                "flask-profiler requires a valid storage engine but it is"
                " missing or wrong. provided engine: {}".format(engine)
            )
        )

    return storage(conf)
