import os
import sys
from pathlib import Path

import requests
from flask import current_app, render_template, render_template_string
from flask.helpers import get_root_path
from flask_login import current_user
from sqlalchemy import text

from app.config import ADMIN_EMAILS

# Add the shared packages to Python path if not already there
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "packages"))

# Import shared email utility
try:
    from shared.utils.email_utils import EmailService

    _email_service = EmailService()
    _shared_email_available = True
except ImportError:
    _shared_email_available = False
    current_app.logger.warning(
        "Shared email service not available, using legacy implementation"
    )


def send_email(
    subject: str,
    sender: str = "RCOM Website <<EMAIL>>",
    to_emails: list = ADMIN_EMAILS,
    cc_emails: list | None = None,
    text_body: str = "",
    html_body: str | None = None,
    files_list: list | None = None,
    testing: bool = False,
    send_all_together: bool = False,
) -> requests.models.Response:
    """Function for sending emails using mailgun"""

    # Use shared email service if available
    if _shared_email_available:
        try:
            return _email_service.send_email(
                subject=subject,
                text_body=text_body,
                to_emails=to_emails,
                sender=sender,
                cc_emails=cc_emails,
                html_body=html_body,
                files_list=files_list,
                testing=testing,
                send_all_together=send_all_together,
            )
        except Exception as e:
            current_app.logger.error(
                f"Shared email service failed, falling back to legacy: {e}"
            )

    # Legacy implementation (fallback)
    if testing or os.getenv("FLASK_CONFIG", "development") in (
        "development",
        "testing",
        "wsl",
    ):
        to_emails = ["<EMAIL>"]
        cc_emails = []

    # Initialize cc_emails if None
    if cc_emails is None:
        cc_emails = []

    # https://documentation.mailgun.com/en/latest/api-sending.html#sending
    response = None

    data = {
        "h:sender": sender,
        "from": sender,
        "subject": subject,
        "text": text_body,
    }

    # Add HTML body if provided
    if html_body:
        data["html"] = html_body

    if send_all_together:
        # Send a single email to all to_emails
        data["to"] = to_emails

        # Add CC recipients if provided
        if cc_emails:
            data["cc"] = cc_emails

        response = requests.post(
            "https://api.mailgun.net/v3/myijack.com/messages",
            auth=("api", os.environ["MAILGUN_API_KEY"]),
            files=files_list,
            data=data,
        )
    else:
        all_emails: list = list(to_emails) + list(cc_emails)
        # Send individual emails to each recipient
        for recipient in all_emails:
            response = requests.post(
                "https://api.mailgun.net/v3/myijack.com/messages",
                auth=("api", os.environ["MAILGUN_API_KEY"]),
                files=files_list,
                data={
                    "h:sender": sender,
                    "from": sender,
                    "to": recipient,
                    "subject": subject,
                    "text": text_body,
                    "html": html_body,
                },
            )

    return response


def send_confirm_code_email(code: int, email: str, first_name: str) -> None:
    """
    Send an email with a code to confirm the user's email address
    """
    text_body = render_template(
        "email/confirm_code_email.txt",
        first_name=first_name,
        confirm_code=code,
    )
    html_body = render_template(
        "email/confirm_code_email.html",
        first_name=first_name,
        confirm_code=code,
    )
    to_emails = [email]

    # So Sean can reset passwords in development mode, via Sean's email address
    if os.getenv("FLASK_CONFIG", "development") in ("development", "testing", "wsl"):
        to_emails = ADMIN_EMAILS

    # Send the email with Mailgun
    send_email(
        "Your confirmation code for IJACK",
        sender="<EMAIL>",
        to_emails=to_emails,
        text_body=text_body,
        html_body=html_body,
    )

    return None


def add_file_to_list(filename, list_, folder=None, subfolder=None, file_binary=None):
    """Add a filename to a list of email attachment files, in the correct email format"""

    if file_binary is None:
        folder = folder or Path(__file__).parent.joinpath("instance").joinpath("doc")

        if subfolder:
            filepath = str(folder.joinpath(subfolder).joinpath(filename))
        else:
            filepath = str(folder.joinpath(filename))

        with open(filepath, "rb") as file:
            file_binary = file.read()

    list_.append(("attachment", (filename, file_binary)))
    return list_


def send_email_newsletter(
    email_type="rcom", send_to_yourself=True, testing=True, ijack_staff_only=True
):
    """Email newsletter in HTML"""
    from app.utils.complex import get_all_tables

    # Most of these variables will be overwritten, so these are mostly placeholders
    text_body = ""
    html_body = (
        "<html><body><h1>Test HTML H1</h1><br><p>Test HTML paragraph</p></body></html>"
    )
    sender = "<EMAIL>"
    to_emails: list = ADMIN_EMAILS
    subject = "IJACK RCOM Enhancements"
    html_file = "/templates/email_newsletters/rcom_update_2020_10_02.html"
    text_file = "/templates/email_newsletters/rcom_update_2020_10_02.txt"

    if send_to_yourself:
        to_emails = (current_user.email,)
    elif not testing:
        if ijack_staff_only:
            to_emails = (
                "<EMAIL>",
                "<EMAIL>",
                # "<EMAIL>",
                # "<EMAIL>",
                "<EMAIL>",
            )
        else:
            # Select the users setup to receive emails under each marketing campaign
            if email_type == "rcom":
                sql = text(
                    """
                    select email
                    from public.users
                    where (eml_rcom is true and eml_unsubscribe_all is false and is_active is true)
                        or customer_id = 1 --IJACK users
                """
                )
            elif email_type == "service":
                sql = text(
                    """
                    select email
                    from public.users
                    where (eml_service is true and eml_unsubscribe_all is false and is_active is true)
                        or customer_id = 1 --IJACK users
                """
                )
            else:
                current_app.logger.error(
                    f"Wrong email_type: {email_type}. Expecting 'rcom' or 'service'"
                )
                return None

            # Get a database connection
            from app.databases import run_sql_query

            rows, _ = run_sql_query(sql, db_name="ijack")
            [email_list["email"] for email_list in rows]

            custom_emails_spreadsheet = True
            if custom_emails_spreadsheet:
                # Must still respect the database to_emails due
                # to the "eml_unsubscribe_all" column in the public.users table
                db_emails_list = to_emails.copy()
                custom_filename = "Users to send service email.xlsx"
                doc_folder = Path(__file__).parent.joinpath("instance").joinpath("doc")
                service_folder = doc_folder.joinpath("service_email")
                custom_excel_file = service_folder.joinpath(custom_filename)
                tables_dict_custom = get_all_tables(filename=custom_excel_file)
                users_table_df = tables_dict_custom["users"]["dataframe"]

                # Pylint won't like this, but this needs to be an ==, not "is True"
                condition1 = users_table_df["eml_service"] == True  # noqa: E712
                condition2 = users_table_df["eml_service"].isin(db_emails_list)
                conditions = (condition1) & (condition2)

                to_emails = users_table_df.loc[conditions, "email"].to_list()

            # current_app.logger.warning("Exiting now without sending!")
            # return None

    files_list: list | None = None
    if email_type == "rcom":
        sender = "<EMAIL>"
        subject = "IJACK RCOM Enhancements"
        html_file = "/templates/email_newsletters/rcom_update_2020_10_02.html"
        text_file = "/templates/email_newsletters/rcom_update_2020_10_02.txt"
        files_list = None
    elif email_type == "service":
        sender = "<EMAIL>"
        subject = "New IJACK XFER Greasing Procedure"
        # html_file = "/templates/email_newsletters/service_bulletin_2021_06_03.html"
        # text_file = "/templates/email_newsletters/service_bulletin_2021_06_03.txt"
        # filename = "800-0055r1-IJACK-XFER-Operation-and-Installation-Manual.pdf"
        html_file = "/templates/email_newsletters/service_bulletin_2021_06_03.html"
        text_file = "/templates/email_newsletters/service_bulletin_2021_06_03.txt"
        filenames_to_attach = [
            "ATS_brochure-CMI.pdf",
            "Lubecore_GM_014-2kg-Pump-General-Manual-(003).pdf",
            "Mobil-GL-XX-Mobilux-EP-Series.pdf",
            "TRC-Paragon-3000.pdf",
            "Mobil-IO-CA-MOBIL-EPIC-GREASE.pdf",
        ]
        files_list = []
        for file in filenames_to_attach:
            files_list = add_file_to_list(file, files_list, subfolder="service_email")

    with current_app.app_context():
        path_html = f"{get_root_path(__name__)}{html_file}"
        with open(path_html, "r") as f:
            html_body = render_template_string(f.read())

        path_text = f"{get_root_path(__name__)}{text_file}"
        with open(path_text, "r") as f:
            text_body = f.read()

    current_app.logger.info(f"Email body rendered HTML string: {html_body}")
    current_app.logger.info(f"Email body text string: {text_body}")
    current_app.logger.warning(f"Sending email now to recipients: {to_emails}")

    # files_list=None
    # sync=False
    # send_email_w_smtp(subject, sender, recipients, text_body, html_body,
    #            files_list, sync)

    send_email(subject, sender, to_emails, text_body, html_body, files_list, testing)

    current_app.logger.warning("Email sent!")


def send_error_report_email(
    error_details: dict, user_info: dict = None, additional_context: dict = None
) -> requests.models.Response:
    """
    Send an error report email to IJACK support

    Args:
        error_details: Dictionary containing error information (name, message, stack)
        user_info: User information if available
        additional_context: Additional context like URL, user agent, etc.

    Returns:
        requests.Response from Mailgun API
    """

    # Use shared email service if available
    if _shared_email_available:
        try:
            return _email_service.send_error_report_email(
                error_details=error_details,
                user_info=user_info,
                additional_context=additional_context,
            )
        except Exception as e:
            current_app.logger.error(
                f"Shared email service failed for error report: {e}"
            )

    # Fallback to basic email sending
    from datetime import datetime

    timestamp = datetime.now().isoformat()
    error_type = error_details.get("name", "Unknown Error")
    subject = f"🚨 Flask Error Report: {error_type} - {timestamp}"

    text_body = f"""
IJACK Flask Application Error Report
===================================

Timestamp: {timestamp}
Environment: {os.getenv("FLASK_CONFIG", "unknown")}

ERROR DETAILS:
--------------
Type: {error_details.get("name", "Unknown")}
Message: {error_details.get("message", "No message")}

Stack Trace:
{error_details.get("stack", "No stack trace available")}

USER INFORMATION:
-----------------
"""

    if user_info:
        text_body += f"""
User ID: {user_info.get("id", "Unknown")}
User Email: {user_info.get("email", "Unknown")}
User Name: {user_info.get("name", "Unknown")}
"""
    else:
        text_body += "No user information available"

    text_body += """

ADDITIONAL CONTEXT:
------------------
"""

    if additional_context:
        for key, value in additional_context.items():
            text_body += f"{key}: {value}\n"
    else:
        text_body += "No additional context provided"

    text_body += """

This error report was automatically generated by the Flask application.
"""

    return send_email(
        subject=subject,
        text_body=text_body,
        to_emails=ADMIN_EMAILS,
        sender="IJACK Flask Error Reporter <<EMAIL>>",
        send_all_together=True,
    )
