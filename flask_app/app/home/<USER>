import hashlib
import hmac
import os
import subprocess
import threading
from datetime import datetime, timedelta, timezone
from io import Bytes<PERSON>
from pathlib import Path
from pprint import PrettyPrin<PERSON>
from typing import Tuple

import psutil
import requests
from flask import (
    Blueprint,
    Response,
    abort,
    current_app,
    jsonify,
    make_response,
    redirect,
    render_template,
    request,
    send_file,
    send_from_directory,
    url_for,
)
from flask_dance.contrib.azure import azure
from flask_dance.contrib.github import github
from flask_dance.contrib.google import google
from flask_login import current_user
from shared.models.models import Calorie
from sqlalchemy import text
from twilio.twiml.voice_response import VoiceResponse
from werkzeug.utils import secure_filename

from app import cache, db, pages
from app.auth.sms_api import teine_api_patch_command
from app.auth.views import record_visit
from app.config import ADMIN_EMAILS, DIST_FOLDER, PHONE_NUM_TEINE_API
from app.databases import get_boto3_client, run_sql_query
from app.email_stuff import send_email
from app.gateway_setup import (
    certificate_stuff,
    create_thing_and_add_to_group,
    create_zip_file_from_folder_path,
    detach_revoke_delete_certificate,
    does_aws_thing_already_have_certs,
    upload_to_public_gw_table,
)
from app.models.models import User
from app.utils.complex import generate_presigned_url_from_aws_s3
from app.utils.simple import utcnow_naive

# from app.tasks import create_task
# from celery.result import AsyncResult

# Blueprint
home = Blueprint("home", __name__)

# Title for homepage
TITLE_HOMEPAGE = "IJACK Technologies Inc - CHANGING THE LANDSCAPE"
description_homepage = (
    "Changing the landscape - IJACK makes the world's most automated,"
)
description_homepage += (
    " efficient, and reliable compressors and pumps for increasing your"
)
description_homepage += (
    " oil and gas production, no matter your fluid mixture: 100% oil,"
)
description_homepage += " 100% gas, or anything in between. Set it and forget it,"
description_homepage += " or control it remotely from your phone or computer."
META_IMAGE_HOMEPAGE = None


@home.route("/", methods=["GET"])
def homepage():
    """
    Render the homepage template on the / route
    """
    record_visit(user_id=getattr(current_user, "id", None))
    # print("print: found main home route!")
    # current_app.logger.info("logger: found main home route!")
    return render_template(
        "home/home.html",
        version=current_app.config.get("VERSION_MYIJACK"),
        home=True,
        title=TITLE_HOMEPAGE,
        description=description_homepage,
        meta_image=META_IMAGE_HOMEPAGE,
    )


@home.route("/github")
def login_github():
    """
    OAuth login

    Authorization callback URL:
    https://myijack.com/login/github/authorized/
    """
    if not github.authorized:
        return redirect(url_for("github.login"))
    response = github.get("/user")
    assert response.ok

    return f"You are @{response.json()['login']} on github"


@home.route("/google")
def login_google():
    """
    OAuth login

    Authorization callback URL:
    https://myijack.com/login/google/authorized/
    """
    if not google.authorized:
        return redirect(url_for("google.login"))
    response = google.get("/user")
    assert response.ok

    return f"You are @{response.json()['login']} on google"


@home.route("/azure")
def login_azure():
    """
    OAuth login

    Authorization callback URL:
    https://myijack.com/login/azure/authorized/
    """
    if not azure.authorized:
        return redirect(url_for("azure.login"))
    response = azure.get("/user")
    assert response.ok

    return f"You are @{response.json()['login']} on azure"


@home.route("/login-steps/", methods=["GET"])
def login_steps():
    """
    Render the login steps page
    """
    return render_template(
        "home/login_steps.html",
        title="Login and PassKey Tutorial",
        description="This page shows the steps to log in to the RCOM software and create your WebAuthn PassKey",
    )


@home.route("/version/", methods=["GET"])
def version():
    """Get the RCOM software version"""
    return jsonify({"version_myijack": f"{current_app.config.get('VERSION_MYIJACK')}"})


@home.route("/<path:filename>")
def serve_static(filename: str):
    """Serve static files from the dist folder"""
    # Ensure that the filename does not contain any directory traversal characters (e.g., ../)
    # to prevent accessing files outside the intended directory
    filename = secure_filename(filename)
    # Ensure that only specific types of files (e.g., JavaScript, CSS, images) are served.
    # This can prevent serving sensitive files accidentally
    allowed_extensions = {
        "js",
        "scss",
        "css",
        "png",
        "jpg",
        "jpeg",
        "gif",
        "svg",
        "woff",
        "woff2",
        "ttf",
        "eot",
    }
    filepath: Path = DIST_FOLDER.joinpath(filename)
    if (
        filepath.exists()
        and filepath.is_file()
        and filepath.suffix.lstrip(".") in allowed_extensions
    ):
        response = make_response(send_from_directory(str(DIST_FOLDER), filename))
        # Set appropriate headers to prevent MIME type sniffing and other security vulnerabilities
        # response.headers["X-Content-Type-Options"] = "nosniff"
        return response

    # Pretend like the file doesn't exist (maybe it doesn't!)
    current_app.logger.warning("File not found: %s", filename)
    return abort(404)


@home.route("/offline/", methods=["GET"])
def offline():
    """
    Render the offline page for the progressive web app
    """
    return render_template(
        "offline.html",
        title="No Internet Connection",
        description="This page is shown when the user has no internet connection",
    )


def links_to_health_checks() -> str:
    """
    Create a string of HTML links to the health check endpoints
    """
    health_checks = {
        "https://health.myijack.com/status/rcom/": "Site health history (charts)",
        "/healthcheck/": "Main health check for the RCOM app",
        "/healthcheck/debug/": "Debug endpoint for health checks",
        "/healthcheck/rds/": "AWS RDS database",
        "/healthcheck/timescale/": "Time series database",
        "/healthcheck/redis/": "Redis cache",
        "/healthcheck/aws/s3/": "AWS S3 services",
        "/healthcheck/aws/iot/": "AWS IoT services",
        "/healthcheck/alerts/": "Alerts service",
        "/healthcheck/postgresql_scheduler_jobs/": "Scheduled jobs service",
        "/healthcheck/postgresql_scheduler_monitor/": "Scheduled monitor service",
        "/healthcheck/gitlab_runner/": "GitLab deployment runner service",
    }

    links_str = "<br><br>"
    for url, label in health_checks.items():
        links_str += f'<a href="{url}">{label}</a><br>'

    return links_str


@home.route("/healthcheck/")
def healthcheck():
    """
    Check that the app is up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check database connection for `ijack`
        run_sql_query(text("select id from public.users limit 1"), db_name="ijack")

        # Check database connection for `timescale`
        run_sql_query(
            text("select timestamp_utc from public.time_series limit 1"),
            db_name="timescale",
        )

        # Check Redis connection by setting a key
        cache.set("healthcheck", "ok", timeout=5)

    except Exception as err:
        return f"ERROR with databases or Redis! {err} {links_to_health_checks()}", 500

    return (
        f"Both databases, and Redis, are working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}",
        200,
    )


@home.route("/healthcheck/debug/")
def debug_health():
    """Debug endpoint for health checks."""

    # Get system metrics
    metrics = {
        "cpu_percent": psutil.cpu_percent(),
        "memory_percent": psutil.virtual_memory().percent,
        "active_threads": threading.active_count(),
        "open_files": len(psutil.Process().open_files()),
        "connections": len(psutil.Process().connections()),
    }

    return jsonify(metrics), 200


@home.route("/healthcheck/rds/")
def healthcheck_rds():
    """
    Check that the RDS database is up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check database connection for `ijack`
        run_sql_query(text("select id from public.users limit 1"), db_name="ijack")

    except Exception as err:
        return (
            f"ERROR checking AWS RDS main database: {err} {links_to_health_checks()}",
            500,
        )

    return (
        f"The RDS database is working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}",
        200,
    )


@home.route("/healthcheck/timescale/")
def healthcheck_timescale():
    """
    Check that the Timescale database is up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check database connection for `timescale`
        run_sql_query(
            text("select timestamp_utc from public.time_series limit 1"),
            db_name="timescale",
        )

    except Exception as err:
        return (
            f"ERROR checking TimescaleDB database: {err} {links_to_health_checks()}",
            500,
        )

    return (
        f"The Timescale database is working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}",
        200,
    )


@home.route("/healthcheck/redis/")
def healthcheck_redis():
    """
    Check that the Redis cache is up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check Redis connection by setting a key
        cache.set("healthcheck", "ok", timeout=5)

    except Exception as err:
        return f"ERROR checking Redis container: {err} {links_to_health_checks()}", 500

    return (
        f"Redis cache is working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}",
        200,
    )


@home.route("/healthcheck/aws/s3/")
def healthcheck_aws():
    """
    Check that the AWS services are up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check AWS services
        # Get an S3 client
        s3_client = get_boto3_client("s3")
        # List all buckets
        s3_client.list_buckets()

    except Exception as err:
        return f"ERROR checking AWS S3 services: {err} {links_to_health_checks()}", 500

    return (
        f"AWS S3 services are working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}",
        200,
    )


@home.route("/healthcheck/aws/iot/")
def healthcheck_aws_iot():
    """
    Check that the AWS IoT services are up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check AWS services
        # Get an IoT client
        get_boto3_client("iot")
    except Exception as err:
        current_app.logger.exception("AWS IoT health check failed")
        return jsonify(
            {"status": "error", "message": "Health check failed", "error": str(err)}
        ), 500

    return (
        f"AWS IoT services are working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}",
        200,
    )


@home.route("/healthcheck/alerts/")
def healthcheck_alerts():
    """
    Check that the alerts are up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check that the Docker Swarm alerts service container is up and running
        cp = subprocess.run(
            ["ping", "-c", "1", "alerts_prod"],
            capture_output=True,
        )
        if cp.returncode != 0:
            return (
                f"ERROR checking alerts service: {cp.stderr.decode()} {links_to_health_checks()}",
                500,
            )

    except Exception as err:
        return f"ERROR: {err}", 500

    return f"Alerts are working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}"


@home.route("/healthcheck/postgresql_scheduler_jobs/")
def healthcheck_postgresql_scheduler_jobs():
    """
    Check that the PostgreSQL scheduler 'jobs' service is up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check that the Docker Swarm PostgreSQL scheduler service container is up and running
        cp = subprocess.run(
            ["ping", "-c", "1", "jobs"],
            capture_output=True,
        )
        if cp.returncode != 0:
            return (
                f"ERROR checking PostgreSQL scheduler 'jobs' service: {cp.stderr.decode()} {links_to_health_checks()}",
                500,
            )

    except Exception as err:
        return f"ERROR: {err}", 500

    return f"PostgreSQL scheduler 'jobs' service is working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}"


@home.route("/healthcheck/postgresql_scheduler_monitor/")
def healthcheck_postgresql_scheduler_monitor():
    """
    Check that the PostgreSQL scheduler 'monitor' service is up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check that the Docker Swarm PostgreSQL scheduler service container is up and running
        cp = subprocess.run(
            ["ping", "-c", "1", "monitor"],
            capture_output=True,
        )
        if cp.returncode != 0:
            return (
                f"ERROR checking PostgreSQL scheduler 'monitor' service: {cp.stderr.decode()} {links_to_health_checks()}",
                500,
            )

    except Exception as err:
        return f"ERROR: {err}", 500

    return f"PostgreSQL scheduler 'monitor' service is working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}"


@home.route("/healthcheck/gitlab_runner/")
def healthcheck_gitlab_runner():
    """
    Check that the GitLab runner is up and running,
    for Docker Swarm zero-downtime deployment.
    This endpoint is monitored by UptimeRobot, a free monitoring service
    """
    try:
        # Check that the Docker Swarm GitLab runner service container is up and running
        cp = subprocess.run(
            ["ping", "-c", "1", "gitlab_runner_runner"],
            capture_output=True,
        )
        if cp.returncode != 0:
            return (
                f"ERROR checking GitLab runner (runner) service: {cp.stderr.decode()} {links_to_health_checks()}",
                500,
            )

        cp = subprocess.run(
            ["ping", "-c", "1", "gitlab_runner_dind"],
            capture_output=True,
        )
        if cp.returncode != 0:
            return (
                f"ERROR checking GitLab runner (Docker-in-Docker) service: {cp.stderr.decode()} {links_to_health_checks()}",
                500,
            )

    except Exception as err:
        return f"ERROR: {err}", 500

    return f"GitLab runner and Docker-in-Docker are both working fine at {utcnow_naive().isoformat()} UTC time. {links_to_health_checks()}"


@home.route("/uptime-kuma/alert/", methods=["POST"])
def uptime_kuma_alert_webhook():
    """
    Uptime Kuma is a self-hosted monitoring tool like UptimeRobot.
    This endpoint is for receiving alerts from Uptime Kuma.
    """
    alert_json_dict: dict = request.json
    pretty_printer = PrettyPrinter()
    alert_str: str = pretty_printer.pformat(alert_json_dict)
    msg = alert_json_dict.get("msg", None)
    healthcheck_link = alert_json_dict.get("monitor", {}).get("url", None)
    current_app.logger.info("Uptime Kuma alert: %s", msg)

    # Send an email with the alert
    to_emails: list = ADMIN_EMAILS
    # subject = "Uptime Kuma alert"
    subject: str = f"Alert: {msg}"
    sender: str = "IJACK <<EMAIL>>"
    email_body: str = f"Message: {msg}\n\nHealthcheck link: {healthcheck_link}\n\nOther data:\n{alert_str}"
    send_email(subject, sender, to_emails, email_body, html_body=None)

    return "Uptime Kuma alert received successfully", 200


# @home.route("/sms-twilio-message-status/", methods=["POST"])
# @home.route("/sms_twilio_status_callback/", methods=["POST"])
@home.route("/sms-twilio-status-callback/", methods=["POST"])
def sms_twilio_status_callback():
    """
    URL for Twilio to call via webhook to let me know the delivery status of our SMS message.
    For receiving and perhaps recording the message status of Twilio SMS alerts
    https://console.twilio.com/us1/develop/sms/services?frameUrl=%2Fconsole%2Fsms%2Fservices%3Fx-target-region%3Dus1
    """

    account_sid = request.values.get("AccountSid", None)
    sms_status = request.values.get("SmsStatus", None)
    message_status = request.values.get("MessageStatus", None)
    message_sid = request.values.get("MessageSid", None)
    sent_to = request.values.get("To", None)
    sent_from = request.values.get("From", None)
    body = request.values.get("Body", None)

    all_data = {}
    try:
        # Check the content type to see if it's JSON
        if getattr(request, "content_type", None) == "application/json":
            all_data: dict = request.get_json()
    except Exception as err:
        current_app.logger.error("Trouble with request.get_json(). Error: %s", err)

    pretty_printer = PrettyPrinter()
    all_data_str: str = pretty_printer.pformat(all_data)

    # I don't think the following exist
    sid = request.values.get("Sid", None)
    parent_account_sid = request.values.get("ParentAccountSid", None)
    timestamp = request.values.get("Timestamp", None)
    level = request.values.get("Level", None)
    payload_type = request.values.get("PayloadType", None)

    payload = request.values.get("Payload", {})
    error_code = None
    resource_sid = None
    service_sid = None
    if isinstance(payload, dict):
        error_code = payload.get("error_code", None)
        resource_sid = payload.get("resource_sid", None)
        service_sid = payload.get("service_sid", None)

    msg_combined = f"SmsStatus: {sms_status}"
    msg_combined += f"\nMessageStatus: {message_status}"
    msg_combined += f"\nTo: {sent_to}"
    msg_combined += f"\nFrom: {sent_from}"
    msg_combined += f"\nAccountSid: {account_sid}"
    msg_combined += f"\nMessageSid: {message_sid}"
    msg_combined += f"\nBody: {body}"
    msg_combined += f"\n\nrequest.url (all data): {request.url}"

    # I don't think the following exist
    msg_combined += f"\n\nSID: {sid}"
    msg_combined += f"\nParentAccountSid: {parent_account_sid}"
    msg_combined += f"\nTimestamp: {timestamp}"
    msg_combined += f"\nLevel: {level}"
    msg_combined += f"\nPayloadType: {payload_type}"
    msg_combined += f"\nPayload: {payload}"
    if isinstance(payload, dict):
        msg_combined += f"\nerror_code: {error_code}"
        msg_combined += f"\nresource_sid: {resource_sid}"
        msg_combined += f"\nservice_sid: {service_sid}"
    msg_combined += f"\n\nrequest.get_json() (all data): {all_data_str}"

    # current_app.logger.info(msg_combined)

    if message_status in ("sent", "delivered", None):
        return make_response(
            f"Twilio SMS message status = '{message_status}' for SID '{message_sid}' sent to phone '{sent_to}'",
            204,
        )

    sent_to_str: str = str(sent_to)
    if "***********" not in sent_to_str and "***********" not in sent_to_str:
        # 709-9222 is Johnny Wolff and 480-1671 is Kevin Skogen (both IJACK US people)
        # Send an email with the SMS text message body if the message wasn't delivered successfully
        to_emails = ADMIN_EMAILS
        subject = f"myijack.com Twilio message delivery '{message_status},' not 'sent' or 'delivered'!"
        sender = "IJACK <<EMAIL>>"
        try:
            send_email(subject, sender, to_emails, msg_combined, html_body=None)
        except Exception:
            current_app.logger.exception(f"ERROR sending email! {msg_combined}")

    return make_response(
        f"ERROR: Twilio SMS message status = '{message_status}' for SID '{message_sid}' sent to phone '{sent_to}'",
        204,
    )


@home.route("/sms_twilio_error_webhook/", methods=["POST"])
@home.route("/sms-twilio-error-webhook/", methods=["POST"])
def sms_twilio_error_webhook():
    """
    URL for Twilio to call via webhook when there's been an error with an SMS message.
    https://console.twilio.com/us1/monitor/logs/debugger/webhooks-triggers?frameUrl=%2Fconsole%2Fdebugger%2Falert-triggers%3Fx-target-region%3Dus1

    Expect the following payload to be sent to this webhook endpoint:
        PROPERTY	        DESCRIPTION
        Sid	                Unique identifier of this Debugger event.
        AccountSid	        Unique identifier of the account that generated the Debugger event.
        ParentAccountSid	Unique identifier of the parent account. This parameter only exists if the above account is a subaccount.
        Timestamp	        Time of occurrence of the Debugger event.
        Level	            Severity of the Debugger event. Possible values are Error and Warning.
        PayloadType	        application/json
        Payload	            JSON data specific to the Debugger Event.
    """
    response = make_response("Twilio error callback processed successfully", 204)

    sid = request.values.get("Sid", None)
    account_sid = request.values.get("AccountSid", None)
    parent_account_sid = request.values.get("ParentAccountSid", None)
    timestamp = request.values.get("Timestamp", None)
    level = request.values.get("Level", None)
    payload_type = request.values.get("PayloadType", None)
    payload = request.values.get("Payload", {})
    body = request.values.get("Body", None)

    all_data = {}
    try:
        # Check the content type to see if it's JSON
        if getattr(request, "content_type", None) == "application/json":
            all_data: dict = request.get_json()
    except Exception as err:
        current_app.logger.error("Trouble with request.get_json(). Error: %s", err)

    pretty_printer = PrettyPrinter()
    all_data_str: str = pretty_printer.pformat(all_data)

    error_code = None
    resource_sid = None
    service_sid = None
    if isinstance(payload, dict):
        error_code = payload.get("error_code", None)
        resource_sid = payload.get("resource_sid", None)
        service_sid = payload.get("service_sid", None)

    msg_combined = f"SID: {sid}"
    msg_combined += f"\nAccountSid: {account_sid}"
    msg_combined += f"\nParentAccountSid: {parent_account_sid}"
    msg_combined += f"\nTimestamp: {timestamp}"
    msg_combined += f"\nLevel: {level}"
    msg_combined += f"\nPayloadType: {payload_type}"
    msg_combined += f"\n\nPayload: {payload}"
    if isinstance(payload, dict):
        msg_combined += f"\nerror_code: {error_code}"
        msg_combined += f"\nresource_sid: {resource_sid}"
        msg_combined += f"\nservice_sid: {service_sid}"
    msg_combined += f"\n\nBody: {body}"
    msg_combined += f"\n\nrequest.values (all data): {request.values}"
    msg_combined += f"\n\nrequest.get_json() (all data): \n{all_data_str}"

    current_app.logger.info(msg_combined)

    # Send an email with the SMS text message body
    to_emails = ADMIN_EMAILS
    subject = "Twilio error webhook triggered at https://myijack.com/sms-twilio-error-webhook/"
    sender = "IJACK <<EMAIL>>"
    try:
        send_email(subject, sender, to_emails, msg_combined, html_body=None)
    except Exception:
        current_app.logger.exception(f"ERROR sending email! {msg_combined}")

    return response


@home.route("/sms_twilio_reply/", methods=["POST"])
@home.route("/sms-twilio-reply/", methods=["POST"])
def sms_twilio_reply() -> Tuple[str, int]:
    """
    HTTP webhook for all incoming Twilio SMS messages.
    This is for receiving text messages at our Twilio phone number.
    https://www.twilio.com/docs/sms/tutorials/how-to-confirm-delivery-python
    """
    # no content/no reply SMS
    response = make_response("Twilio SMS reply callback processed successfully", 204)

    # Get the message the user sent our Twilio number
    body = request.values.get("Body", None)
    from_country = request.values.get("FromCountry", None)
    from_state = request.values.get("FromState", None)
    from_city = request.values.get("FromCity", None)
    from_phone = request.values.get("From", None)
    opt_out_type = request.values.get("OptOutType", None)
    opt_out_type_upper = str(opt_out_type).upper()

    all_data = {}
    try:
        # Check the content type to see if it's JSON
        if getattr(request, "content_type", None) == "application/json":
            all_data: dict = request.get_json()
    except Exception as err:
        current_app.logger.error("Trouble with request.get_json(). Error: %s", err)

    pretty_printer = PrettyPrinter()
    all_data_str: str = pretty_printer.pformat(all_data)

    # If not any of the above, then it's a blank request
    if not any(
        [
            body,
            from_country,
            from_state,
            from_city,
            from_phone,
            opt_out_type,
        ]
    ):
        # Sometimes the request.values dict is completely blank. Not sure why.
        # No sense sending a blank email below.
        current_app.logger.info(f"Blank Twilio request.values dict. Request: {request}")
        # return response

    log_msg = f"Twilio incoming SMS message received from phone number {from_phone} in city '{from_city}', state '{from_state}', country '{from_country}'."
    log_msg += f"\n\nMessage body: {body}"
    if opt_out_type:
        log_msg += f"\n\nOpt-out type: {opt_out_type}"
    # request_text = str(request.__dict__)
    log_msg += f"\n\nrequest.url: {request.url}"
    log_msg += f"\nrequest.values: {request.values}"
    log_msg += f"\nrequest.form: {request.form}"
    log_msg += f"\nrequest.args: {request.args}"
    log_msg += f"\nrequest.data: {request.data}"
    log_msg += f"\nrequest.headers: {request.headers}"
    log_msg += f"\n\nrequest.get_json() (all data): {all_data_str}"
    current_app.logger.info(log_msg)

    # Send an email with the SMS text message body
    to_emails = ADMIN_EMAILS
    subject = "myijack.com Twilio SMS received"
    sender = "IJACK <<EMAIL>>"
    # text_body = f"Message from: {first_name} {last_name} \nEmail address: {email} \nPhone number: {phone} \nMessage: \n{message}"
    try:
        send_email(subject, sender, to_emails, log_msg, html_body=None)
    except Exception:
        current_app.logger.exception(f"ERROR sending email! {log_msg}")

    if from_phone == PHONE_NUM_TEINE_API:
        try:
            teine_api_patch_command(body)
        except Exception:
            current_app.logger.exception(
                "Trouble with teine_api_patch_command function"
            )
        return response

    if body is None or from_phone is None:
        return response

    # from_phone_str = str(from_phone)
    # phone_number = from_phone_str[5:]

    if "stop" in body.lower() or opt_out_type_upper in (
        "STOP",
        "UNSUBSCRIBE",
        "STOPALL",
    ):
        # User is trying to unsubscribe from all SMS messages
        users = User.query.filter_by(phone=from_phone).all()
        for user in users:
            user.sms_stop_all = True
            user.sms_stopped_at_utc = utcnow_naive()
            db.session.commit()

            # # Turn SMS alerts off for all alerts
            # alerts = Alert.query.filter_by(user_id=user.id).all()
            # for alert in alerts:
            #     alert.wants_sms = False
            #     db.session.commit()

        # # Send them an SMS message confirming their choice
        # send_unsubscribe_user_sms(user)

    elif "start" in body.lower() or opt_out_type_upper in (
        "START",
        "SUBSCRIBE",
        "RESTART",
    ):
        # User is trying to unsubscribe from all SMS messages
        users = User.query.filter_by(phone=from_phone).all()
        for user in users:
            user.sms_stop_all = False
            user.sms_confirmed_at_utc = utcnow_naive()
            db.session.commit()

            # # Turn SMS alerts back on for all alerts
            # alerts = Alert.query.filter_by(user_id=user.id).all()
            # for alert in alerts:
            #     alert.wants_sms = True
            #     db.session.commit()

        # # Send them an SMS message confirming their choice
        # send_new_user_sms(user)

    return response


@home.route("/whatsapp-twilio-reply/", methods=["POST"])
def whatsapp_twilio_reply() -> Tuple[str, int]:
    """
    HTTP webhook for all incoming Twilio WhatsApp messages.
    This is for receiving WhatsApp messages at our Twilio phone number.
    """
    # no content/no reply WhatsApp
    response = make_response(
        "Twilio WhatsApp reply callback processed successfully", 204
    )

    # Get the message the user sent our Twilio number
    body = request.values.get("Body", None)
    from_country = request.values.get("FromCountry", None)
    from_state = request.values.get("FromState", None)
    from_city = request.values.get("FromCity", None)
    from_phone = request.values.get("From", None)
    opt_out_type = request.values.get("OptOutType", None)
    opt_out_type_upper = str(opt_out_type).upper()

    all_data = {}
    try:
        # Check the content type to see if it's JSON
        if getattr(request, "content_type", None) == "application/json":
            all_data: dict = request.get_json()
    except Exception as err:
        current_app.logger.error("Trouble with request.get_json(). Error: %s", err)

    pretty_printer = PrettyPrinter()
    all_data_str: str = pretty_printer.pformat(all_data)

    # If not any of the above, then it's a blank request
    if not any(
        [
            body,
            from_country,
            from_state,
            from_city,
            from_phone,
            opt_out_type,
        ]
    ):
        # Sometimes the request.values dict is completely blank. Not sure why.
        # No sense sending a blank email below.
        current_app.logger.info(f"Blank Twilio request.values dict. Request: {request}")
        # return response

    log_msg = f"Twilio incoming WhatsApp message received from phone number {from_phone} in city '{from_city}', state '{from_state}', country '{from_country}'."
    log_msg += f"\n\nMessage body: {body}"
    if opt_out_type:
        log_msg += f"\n\nOpt-out type: {opt_out_type}"
    # request_text = str(request.__dict__)
    log_msg += f"\n\nrequest.url: {request.url}"
    log_msg += f"\nrequest.values: {request.values}"
    log_msg += f"\nrequest.form: {request.form}"
    log_msg += f"\nrequest.args: {request.args}"
    log_msg += f"\nrequest.data: {request.data}"
    log_msg += f"\nrequest.headers: {request.headers}"
    log_msg += f"\n\nrequest.get_json() (all data): {all_data_str}"
    current_app.logger.info(log_msg)

    # Send an email with the WhatsApp text message body
    to_emails = ADMIN_EMAILS
    subject = "myijack.com Twilio WhatsApp message received"
    sender = "IJACK <<EMAIL>>"
    # text_body = f"Message from: {first_name} {last_name} \nEmail address: {email} \nPhone number: {phone} \nMessage: \n{message}"
    try:
        send_email(subject, sender, to_emails, log_msg, html_body=None)
    except Exception:
        current_app.logger.exception(f"ERROR sending email! {log_msg}")

    if from_phone == PHONE_NUM_TEINE_API:
        try:
            teine_api_patch_command(body)
        except Exception:
            current_app.logger.exception(
                "Trouble with teine_api_patch_command function"
            )
        return response

    if body is None or from_phone is None:
        return response

    # from_phone_str = str(from_phone)
    # phone_number = from_phone_str[5:]

    if "stop" in body.lower() or opt_out_type_upper in (
        "STOP",
        "UNSUBSCRIBE",
        "STOPALL",
    ):
        # User is trying to unsubscribe from all SMS messages
        users = User.query.filter_by(phone=from_phone).all()
        for user in users:
            user.whatsapp_stop_all = True
            user.whatsapp_stopped_at_utc = datetime.now(timezone.utc).replace(
                tzinfo=None
            )
            db.session.commit()

            # # Turn whatsapp alerts off for all alerts
            # alerts = Alert.query.filter_by(user_id=user.id).all()
            # for alert in alerts:
            #     alert.wants_whatsapp = False
            #     db.session.commit()

        # # Send them an whatsapp message confirming their choice
        # send_unsubscribe_user_whatsapp(user)

    elif "start" in body.lower() or opt_out_type_upper in (
        "START",
        "SUBSCRIBE",
        "RESTART",
    ):
        # User is trying to unsubscribe from all whatsapp messages
        users = User.query.filter_by(phone=from_phone).all()
        for user in users:
            user.whatsapp_stop_all = False
            user.whatsapp_confirmed_at_utc = datetime.now(timezone.utc).replace(
                tzinfo=None
            )
            db.session.commit()

            # # Turn whatsapp alerts back on for all alerts
            # alerts = Alert.query.filter_by(user_id=user.id).all()
            # for alert in alerts:
            #     alert.wants_whatsapp = True
            #     db.session.commit()

        # # Send them an whatsapp message confirming their choice
        # send_new_user_whatsapp(user)

    return response


@home.route("/voice-twilio-reply/", methods=["POST"])
def twilio_voice_response():
    """
    Respond to incoming phone calls with a 'Hello world' message
    Function to respond to Twilio voice calls
    https://www.twilio.com/docs/voice/tutorials/how-to-respond-to-incoming-phone-calls-python
    https://www.twilio.com/docs/voice/twiml
    """
    # Start our TwiML response
    response = VoiceResponse()

    # Use <Say> to give the caller some instructions
    response.say(
        "Hello from IJACK Technologies Inc. Please leave a message after the beep."
    )

    # Play an audio file for the caller "Never Gonna Give You Up" by Rick Astley
    # response.play('https://demo.twilio.com/docs/classic.mp3')

    # Use <Record> to record the caller's message
    response.record()

    # End the call with <Hangup>
    response.hangup()

    # Send an email with the SMS text message body
    to_emails = ADMIN_EMAILS
    subject = "myijack.com Twilio voice call received"
    sender = "IJACK <<EMAIL>>"

    all_data = {}
    try:
        # Check the content type to see if it's JSON
        if getattr(request, "content_type", None) == "application/json":
            all_data: dict = request.get_json()
    except Exception as err:
        current_app.logger.error("Trouble with request.get_json(). Error: %s", err)

    pretty_printer = PrettyPrinter()
    all_data_str: str = pretty_printer.pformat(all_data)

    email_body = "Voice message received and recorded. Please go to the Twilio console to hear the recording."
    email_body += f"\n\nrequest.url: {request.url}"
    email_body += f"\n\nrequest.get_json() (all data): {all_data_str}"

    send_email(subject, sender, to_emails, email_body, html_body=None)

    return str(response)


def mailgun_verify_post_request(signing_key, token, timestamp, signature):
    """
    Verify the webhook is originating from Mailgun
    https://documentation.mailgun.com/en/latest/user_manual.html#webhooks-1
    """
    hmac_digest = hmac.new(
        key=signing_key.encode(),
        msg=f"{timestamp}{token}".encode(),
        digestmod=hashlib.sha256,
    ).hexdigest()
    return hmac.compare_digest(str(signature), str(hmac_digest))


def mailgun_process_request(
    # request_: Request,
    subject: str,
    need_send_email: bool = True,
    deactivate_user: bool = False,
) -> Response:
    """Send an email with a message from Mailgun"""

    # Get Mailgun parameters for verifying webhook authenticity
    data = {}
    try:
        # Check the content type to see if it's JSON
        if getattr(request, "content_type", None) == "application/json":
            data: dict = request.get_json()
    except Exception as err:
        current_app.logger.error("Trouble with request.get_json(). Error: %s", err)

    event_data: dict = data.get("event-data", {})

    signature_data: dict = data.get("signature", {})
    token = signature_data.get("token", None)
    timestamp = signature_data.get("timestamp", None)
    signature = signature_data.get("signature", None)

    # Verify webhook authenticity
    if not mailgun_verify_post_request(
        os.getenv("MAILGUN_WEBHOOK_SIGNING_KEY"), token, timestamp, signature
    ):
        # 406 Not Acceptable
        abort(406)

    text_body: str = ""
    if deactivate_user:
        # Deactivate the user
        email = event_data.get("recipient", None)
        severity = event_data.get("severity", None)
        if email and severity == "permanent" and email != "<EMAIL>":
            user = User.query.filter_by(email=email).first()
            if user:
                user.is_active = False
                db.session.commit()
                text_body += f"Deactivated user with email address '{email}' since severity = 'permanent'!\n\n"
                need_send_email = True

    if need_send_email:
        # Create a pretty printer object
        pretty_printer = PrettyPrinter()
        # Use the pretty printer to format the data to a string
        formatted_data: str = pretty_printer.pformat(event_data)

        text_body += f"{subject} \n\nEvent data:\n{formatted_data}"
        send_email(
            subject=subject,
            sender="myijack.com <<EMAIL>>",
            to_emails=ADMIN_EMAILS,
            text_body=text_body,
        )

    # Mailgun needs a 200 response; otherwise it'll keep sending the POST webhook
    # https://documentation.mailgun.com/en/latest/user_manual.html#webhooks-1
    return make_response("Mailgun callback processed successfully", 200)


@home.route("/mailgun/spam", methods=["POST"])
def mailgun_spam():
    """
    Mailgun webhook for when a user reports one of your emails as spam.
    Note that not all ESPs provide this feedback.
    """
    subject = "Mailgun message marked as spam!"
    return mailgun_process_request(subject)


@home.route("/mailgun/unsubscribe", methods=["POST"])
def mailgun_unsubscribe():
    """
    Mailgun webhook for when a user unsubscribes from your emails.
    Note that not all ESPs provide this feedback.
    """
    subject = "Mailgun user unsubscribed from our emails!"
    return mailgun_process_request(subject)


@home.route("/mailgun/permanent-failure", methods=["POST"])
def mailgun_permanent_failure():
    """
    Mailgun webhook for when a user's email address permanently fails.
    Note that not all ESPs provide this feedback.
    There are several reasons why Mailgun stops attempting to deliver messages
    and drops them including: hard bounces, messages that reached their retry limit,
    previously unsubscribed/bounced/complained addresses, or addresses rejected by an ESP.
    """
    subject = "Mailgun user's email address permanently failed!"
    return mailgun_process_request(subject, need_send_email=True, deactivate_user=False)


@home.route("/mailgun/temporary-failure", methods=["POST"])
def mailgun_temporary_failure():
    """
    Mailgun webhook for when a user's email address temporarily fails.
    Note that not all ESPs provide this feedback.
    There are several reasons why a message temporarily fails to deliver including:
    connection issues, full mailbox, or ISP block.
    Mailgun will automatically retry delivery on temporary failures.
    """
    subject = "Mailgun user's email address temporarily failed!"
    # We don't need to send an email for these temporary failures
    return mailgun_process_request(subject, need_send_email=False)


@home.route("/rcom-info/")
def rcom_info():
    """The products page that gives info on RCOM and what it offers customers"""
    record_visit(user_id=getattr(current_user, "id", None))
    return render_template(
        "home/rcom_info.html",
        title="Control your IJACKs - RCOM - IJACK Technologies Inc.",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="Control your IJACKs. Learn why RCOM&trade; is the ultimate solution for remote oil and gas monitoring, remote control, and smart alerting.",
    )


@home.route("/rcomm/", methods=["GET"])
@home.route("/charts/", methods=["GET"])
def rcom():
    """
    Redirect 301 (permanent) to the new address: /rcom/
    The old address used to be /charts/ but /rcom/ is more appropriate now.
    We also want this in the sitemap, and the admin site has a link to "home.rcom".
    """
    return redirect(url_for("dash.home"), 301)


@home.route("/home/<USER>", methods=["GET"])
def home_about():
    """If someone clicks the "about" link to go straight to the "about" info"""
    # form = ContactForm()
    return render_template(
        "home/home.html",
        version=current_app.config.get("VERSION_MYIJACK"),
        scroll_about="about",
        # form=form,
        home=True,
        title=TITLE_HOMEPAGE,
        description=description_homepage,
        meta_image=META_IMAGE_HOMEPAGE,
    )


@home.route("/media/")
def media():
    """Renders a list of all blog posts using FlatPages library"""
    record_visit(user_id=getattr(current_user, "id", None))
    posts = [p for p in pages if "date" in p.meta]
    posts.sort(key=lambda page: page.meta["date"], reverse=True)
    return render_template(
        "home/media.html",
        pages=posts,
        title="IJACK Media - News and Product Case Studies",
        description="The latest news, ideas, and case studies from IJACK Technologies Inc",
        meta_image=META_IMAGE_HOMEPAGE,
    )


@home.route("/advantages-of-casing-gas-compression")
@home.route("/advantages-of-casing-gas-compression.html")
def advantages_of_casing_gas_compression():
    """Redirect to the new blog post URL"""
    return redirect(
        url_for("home.blog_post", name="advantages-of-casing-gas-compression"), 301
    )


@home.route("/media/<name>")
def blog_post(name):
    """Renders an individual Markdown-formatted blog post collected with FlatPages"""
    record_visit(user_id=getattr(current_user, "id", None))
    page = pages.get_or_404(name)
    return render_template(
        "home/blog_post.html",
        page=page,
        # title=page.title,
        # description=page.description,
        meta_image=META_IMAGE_HOMEPAGE,
    )


@home.route("/s3/<path:filename>")
def s3_no_login(filename):
    """
    For sending static files from AWS S3 bucket,
    like PDFs without increasing Docker image size
    """
    # record_visit(user_id=getattr(current_user, "id", None))
    try:
        presigned_url: str = generate_presigned_url_from_aws_s3(filename)
    except Exception:
        current_app.logger.exception(
            f"Error generating presigned URL for file '{filename}'"
        )
        return Response(f"Error generating presigned URL for '{filename}'", status=500)

    # Download the file from the pre-signed URL
    response = requests.get(presigned_url)
    if response.status_code != 200:
        current_app.logger.error(
            f"Error fetching file '{filename}' from S3: {response.status_code}"
        )
        return Response(f"Error fetching file '{filename}' from S3", status=500)

    # Serve the file to the user
    file_stream = BytesIO(response.content)
    return send_file(file_stream, download_name=filename)


@home.route("/favicon.ico")
def favicon():
    """
    For redirecting to the static folder for the favicon.ico file.
    TODO: Do we need this? ChatGPT says if we don't include this route,
    browsers will typically make a request to /favicon.ico by default,
    and if the file isn't found, it might result in a 404 error
    """
    # return send_from_directory(current_app.static_folder, path="img/favicon.ico")
    return redirect(url_for("static", filename="img/favicon.ico"), code=301)


@home.route("/fake-route")
def fake_route():
    """
    For testing the 500 error page
    """
    return None


@home.route("/robots/")
@home.route("/robots.txt")
def robots():
    """For sending robots.txt from the static folder"""
    return send_from_directory(current_app.static_folder, "robots.txt")


@home.route("/sitemap/")
@home.route("/sitemap.xml")
def sitemap():
    """Automatically generate a sitemap.xml file for Google to crawl"""
    html_pages = set()

    # get static routes
    # use arbitary 10 days ago as last modified date
    lastmod = datetime.now() - timedelta(days=10)
    lastmod = lastmod.strftime("%Y-%m-%d")
    for rule in current_app.url_map.iter_rules():
        if (
            rule.methods
            and "GET" in rule.methods
            and len(rule.arguments) == 0
            # Exclude the following from the sitemap.xml
            and not rule.rule.startswith("/rcom/")
            and not rule.rule.startswith("/docs/")
            and not rule.rule.startswith("/api")
            and not rule.rule.startswith("/admin")
            and not rule.rule.startswith("/test")
            and not rule.rule.startswith("/healthcheck")
            and not rule.rule.startswith("/sms_twilio_reply")
            and not rule.rule.startswith("/profiler")
            and "charts" not in rule.rule
            and "index" not in rule.rule
            and "home" not in rule.rule
            and "feed" not in rule.rule
            and "img" not in rule.rule
            and "#" not in rule.rule
            and "logout" not in rule.rule
            and "redirect" not in rule.endpoint
            and "email" not in rule.endpoint
            and "password" not in rule.endpoint
            and "sitemap" not in rule.endpoint
            and "robots" not in rule.endpoint
        ):
            html_pages.add(("https://myijack.com" + rule.rule, lastmod))

    # Dynamically-generated URLs for the blog posts
    posts = [p for p in pages if "date" in p.meta]
    for post in posts:
        url = f"https://myijack.com/blog/{post.path}.html"
        last_updated = post.meta["date"].strftime("%Y-%m-%d")
        html_pages.add((url, last_updated))

    sitemap_template = render_template(
        "sitemap_template.xml",
        html_pages=html_pages,
    )
    response = make_response(sitemap_template)
    response.headers["Content-Type"] = "application/xml"
    return response


@home.route("/xfer/")
def xfer():
    """
    Render the xfer template
    """
    record_visit(user_id=getattr(current_user, "id", None))

    images = [
        dict(
            file="img/xfer/ijack-xfer-multi-phase-fluid-transfer-pump-connected-to-power-unit.jpg",
            width="1400",
            length="840",
            alt="IJACK XFER tri-phase multiphase fluid transfer and booster pump cost-effective pumping and compression system",
            title="IJACK XFER multiphase fluid transfer and booster pump",
        ),
        dict(
            file="img/xfer/ijack-dual-xfer-triphase-transfer-and-booster-pump.jpg",
            width="1400",
            length="840",
            alt="IJACK XFER transfers oil, water, gas and solids from the wellhead to the battery for increased production lower casing pressure and flowline pressure",
            title="IJACK XFER multiphase fluid transfer and booster pump",
        ),
        dict(
            file="img/xfer/ijack-xfer-tan-color-us-tri-phase-fluid-transfer-pump-with-rcom.jpg",
            width="1400",
            length="840",
            alt="IJACK XFER multiphase fluid transfer and booster pump replaces satellite facilities and can be used as a powerful booster pump for injection wells",
            title="IJACK XFER triphase fluid transfer and booster pump",
        ),
        dict(
            file="img/xfer/ijack-xfer-black-multiphase-fluid-transfer-and-booster-pump-with-remote-communication.jpg",
            width="1400",
            length="840",
            alt="IJACK XFER tri-phase multiphase fluid transfer and booster pump intakes can be tied directly into flowlines, headers or risers without the need for separators",
            title="IJACK XFER multiphase fluid transfer and booster pump",
        ),
    ]

    short_name = "XFER"
    posts = [p for p in pages if "date" in p.meta and short_name in p.body]

    return render_template(
        "home/xfer.html",
        xfer=True,
        title="XFER - Multiphase Fluid Transfer and Booster Pump",
        version=current_app.config.get("VERSION_MYIJACK"),
        meta_image="img/IJACk-info-graphic-V5-Resized-540x1024.png",
        description="IJACK™ XFER increases multiphase gas and fluid transfer efficiency at wellsites, headers or satellites where production doesn't justify costly infrastructure. It can also be used to draw pressure down in a multiwell scenario or as a powerful pressure booster pump for water injection to remote wells.",
        short_name=short_name,
        pages=posts,
        images=images,
    )


@home.route("/boost/")
def boost():
    """
    Render the BOOST template
    """
    record_visit(user_id=getattr(current_user, "id", None))

    # images = [
    #     dict(
    #         file=,
    #         width=,
    #         length=,
    #         alt=,
    #         title=,
    #     ),
    #     dict(
    #         file=,
    #         width=,
    #         length=,
    #         alt=,
    #         title=,
    #     ),
    #     dict(
    #         file=,
    #         width=,
    #         length=,
    #         alt=,
    #         title=,
    #     ),
    #     dict(
    #         file=,
    #         width=,
    #         length=,
    #         alt=,
    #         title=,
    #     ),
    # ]

    short_name = "BOOST"
    posts = [p for p in pages if "date" in p.meta and short_name in p.body]

    return render_template(
        "home/boost.html",
        xfer=True,
        title="BOOST - Water Injection Booster Pump",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="The IJACK BOOST pump can handle high intake pressures and boost the discharges pressures to the required level with pressure differentials up to 2200 PSI.",
        meta_image="img/boost/ijack-boost-high-inlet-pressure-intensifier-pump-illustration-1600px.jpg",
        short_name=short_name,
        pages=posts,
        # images=images,
    )


@home.route("/egas-vru/")
@home.route("/vru/")
def vru():
    """
    Render the VRU template on the /vru route
    """
    record_visit(user_id=getattr(current_user, "id", None))

    # images = [
    #     dict(
    #         file="img/egas/egas-823-casing-gas-compressor-by-pumpjack-on-gravel.jpg",
    #         width=3892,
    #         length=2595,
    #         alt="IJACK EGAS 823 helps you extract oil while controlling casing gas pressure",
    #         title="IJACK EGAS 823 - Automated Casing Gas Compressor",
    #     ),
    #     dict(
    #         file="img/egas/egas-823-casing-gas-compressor-in-young-wheatfield.jpg",
    #         width=4128,
    #         length=2752,
    #         alt="IJACK EGAS 823 helps you extract oil while controlling casing gas pressure",
    #         title="IJACK EGAS 823 - Automated Casing Gas Compressor",
    #     ),
    #     dict(
    #         file="img/egas/egas-823-casing-gas-compressor-ground-level-photo.jpg",
    #         width=4128,
    #         length=2752,
    #         alt="IJACK EGAS 823 helps you extract oil while controlling casing gas pressure",
    #         title="IJACK EGAS 823 - Automated Casing Gas Compressor",
    #     ),
    #     dict(
    #         file="img/egas/egas-823-casing-gas-compressor-by-pumpjack.jpg",
    #         width=5164,
    #         length=2905,
    #         alt="IJACK EGAS 823 helps you extract oil while controlling casing gas pressure",
    #         title="IJACK EGAS 823 - Automated Casing Gas Compressor",
    #     ),
    # ]

    short_name = "VRU"
    posts = [p for p in pages if "date" in p.meta and short_name in p.body]

    return render_template(
        "home/vru.html",
        xfer=True,
        title="VRU - Vapor Recovery Unit for Zero Emissions",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="Vapor Recovery Units can help you achieve your zero emission goals by compressing vapors and associated liquids into flowlines, compressors, or other vessels, thus eliminating venting.",
        meta_image="img/vru/ijack-egas-vru-sizes-1600px.webp",
        short_name=short_name,
        pages=posts,
        # images=images,
    )


@home.route("/egas/")
def egas():
    """
    Render the egas template on the /egas route
    """
    record_visit(user_id=getattr(current_user, "id", None))

    # Normal EGAS pictures
    images = [
        dict(
            file="img/egas/ijack-egas-standalone-hydraulic-gas-compressor.jpg",
            width=1500,
            length=844,
            alt="IJACK EGAS standalone hydraulic gas compressor",
            title="IJACK EGAS - Automated Casing Gas Compressor",
        ),
        dict(
            file="img/egas/ijack-egas-gas-compressor-working-with-hydraulic-uno-pumpjack.jpg",
            width=4810,
            length=2706,
            alt="IJACK EGAS gas compressor working with hydraulic UNO pumpjack",
            title="IJACK EGAS - Automated Casing Gas Compressor",
        ),
        dict(
            file="img/egas/egas-823-winter-1400px.webp",
            width=1500,
            length=844,
            alt="IJACK EGAS is designed to work with hydraulic and conventional pump jacks in oil and gas",
            title="IJACK EGAS - Automated Casing Gas Compressor",
        ),
        dict(
            file="img/egas/ijack-egas-independent-gas-compressor-with-conventional-pumpjack.jpg",
            width=1500,
            length=844,
            alt="IJACK EGAS helps you extract oil while controlling casing gas pressure",
            title="IJACK EGAS - Automated Casing Gas Compressor",
        ),
        # ]
        # # EGAS 823 pictures
        # images_823 = [
        dict(
            file="img/egas/egas-823-casing-gas-compressor-by-pumpjack-on-gravel.jpg",
            width=3892,
            length=2595,
            alt="IJACK EGAS 823 helps you extract oil while controlling casing gas pressure",
            title="IJACK EGAS 823 - Automated Casing Gas Compressor",
        ),
        dict(
            file="img/egas/egas-823-casing-gas-compressor-in-young-wheatfield.jpg",
            width=4128,
            length=2752,
            alt="IJACK EGAS 823 helps you extract oil while controlling casing gas pressure",
            title="IJACK EGAS 823 - Automated Casing Gas Compressor",
        ),
        dict(
            file="img/egas/egas-823-casing-gas-compressor-ground-level-photo.jpg",
            width=4128,
            length=2752,
            alt="IJACK EGAS 823 helps you extract oil while controlling casing gas pressure",
            title="IJACK EGAS 823 - Automated Casing Gas Compressor",
        ),
        dict(
            file="img/egas/egas-823-casing-gas-compressor-by-pumpjack.jpg",
            width=5164,
            length=2905,
            alt="IJACK EGAS 823 helps you extract oil while controlling casing gas pressure",
            title="IJACK EGAS 823 - Automated Casing Gas Compressor",
        ),
    ]

    short_name = "EGAS"
    posts = [p for p in pages if "date" in p.meta and short_name in p.body]

    return render_template(
        "home/egas.html",
        egas=True,
        title="EGAS - Automated Casing Gas Compressor",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="EGAS is a fully automated and independent Casing Gas Compression System used to relieve casing gas pressure and improving well performance.",
        meta_image="img/IJACK-EGAS-Infographic-03-CMYK-April-2020.jpg",
        short_name=short_name,
        pages=posts,
        images=images,
        # images_823=images_823,
    )


@home.route("/dgas/")
def dgas():
    """
    Render the dgas template on the /dgas route
    """
    record_visit(user_id=getattr(current_user, "id", None))

    images = [
        dict(
            file="img/dgas/ijack-dgas-beam-mounted-casing-gas-compressor-on-traditional-pumpjack.jpg",
            width="",
            length="",
            alt="IJACK DGAS beam-mounted gas compressor reached target casing gas pressure on conventional oil well",
            title="IJACK DGAS - Beam-Mounted Casing Gas Compressor",
        ),
        dict(
            file="img/dgas/ijack-dgas-beam-mounted-casing-gas-compressor-on-conventional-pumpjack-1500px.jpg",
            width="",
            length="",
            alt="IJACK DGAS hydraulic pumping unit and gas compressor working on the Lodgepole oil wells",
            title="IJACK DGAS - Beam-Mounted Casing Gas Compressor",
        ),
        dict(
            file="img/dgas/ijack-dgas-beam-mounted-casing-gas-compressor-on-oilboss-pumpjack.jpg",
            width="",
            length="",
            alt="IJACK DGAS beam-mounted gas compressor on Oilboss pumpjack oil well",
            title="IJACK DGAS - Beam-Mounted Casing Gas Compressor",
        ),
        dict(
            file="img/dgas/ijack-dgas-beam-mounted-casing-gas-compressor-on-conventional-oilboss-pumpjack.jpg",
            width="",
            length="",
            alt="IJACK DGAS beam-mounted casing gas compressor on conventional, traditional pumpjack oil well",
            title="IJACK DGAS - Beam-Mounted Casing Gas Compressor",
        ),
    ]

    short_name = "DGAS"
    posts = [p for p in pages if "date" in p.meta and short_name in p.body]

    return render_template(
        "home/dgas.html",
        dgas=True,
        title="DGAS - Beam Mounted Casing Gas Compressor",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="DGAS is an advanced bolt-on gas compression system for conventional pump jacks. DGAS efficiently relieves downhole back pressure and maximizes production.",
        meta_image="img/Dgas-illus-Sept2018-web.png",
        short_name=short_name,
        pages=posts,
        images=images,
    )


@home.route("/uno/")
def uno():
    """
    Render the uno template on the /uno route
    """
    record_visit(user_id=getattr(current_user, "id", None))

    images = [
        dict(
            file="img/uno/ijack-uno-automated-remote-controlled-rcom-hydraulic-pumpjack.jpg",
            width="",
            length="",
            alt="IJACK UNO independent, hydraulic pumpjack structure installed on the wellhead",
            title="IJACK UNO Artificial Lift Pumpjack",
        ),
        dict(
            file="img/uno/ijack-uno-fully-automated-adaptive-hydraulic-pumpjack.jpg",
            width="",
            length="",
            alt="IJACK UNO artificial lift to increase efficiency, enhance production, and offer more control",
            title="IJACK UNO Artificial Lift Pumpjack",
        ),
        dict(
            file="img/uno/ijack-uno-automatic-hydraulic-artificial-lift.jpg",
            width="",
            length="",
            alt="IJACK UNO fully-automated artificial lift technology with remote control",
            title="IJACK UNO Artificial Lift Pumpjack",
        ),
        dict(
            file="img/uno/ijack-unogas-uno-hydraulic-pumpjack-and-egas-standalone-casing-gas-compressor.jpg",
            width="",
            length="",
            alt="IJACK UNOGAS, UNO hydraulic pumpjack and EGAS standalone casing gas compressor",
            title="IJACK UNOGAS Artificial Lift UNO Pumpjack and EGAS Compressor",
        ),
    ]

    short_name = "UNO"
    posts = [p for p in pages if "date" in p.meta and short_name in p.body]

    return render_template(
        "home/uno.html",
        uno=True,
        title="UNO - Automated Hydraulic Lift Technology",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="IJACK™ UNO automated hydraulic lift technology is designed to increase efficiency, enhance production, and offer more control and precision on every well.",
        meta_image="img/Uno-Illus-JSept2018-web.png",
        short_name=short_name,
        pages=posts,
        images=images,
    )


@home.route("/unogas/")
def unogas():
    """
    Render the unogas template on the /unogas route
    """
    record_visit(user_id=getattr(current_user, "id", None))

    images = [
        dict(
            file="img/unogas/ijack-unogas-uno-and-egas-artificial-lift-and-casing-gas-compressor.jpg",
            width="",
            length="",
            alt="IJACK UNOGAS combined UNO artificial lift pumpjack with EGAS casing gas compressor",
            title="IJACK UNOGAS Artificial Lift Plus EGAS Casing Gas Compressor",
        ),
        dict(
            file="img/unogas/ijack-unogas-dual-uno-and-egas-artificial-lift-and-casing-gas-compressor.jpg",
            width="",
            length="",
            alt="IJACK UNOGAS dual UNO artificial lift pumpjack with EGAS casing gas compressor",
            title="IJACK UNOGAS Artificial Lift Plus EGAS Casing Gas Compressor",
        ),
        dict(
            file="img/unogas/ijack-unogas-uno-and-egas-artificial-lift-and-casing-gas-compressor-with-independent-power-unit.jpg",
            width="",
            length="",
            alt="IJACK UNOGAS removes more gas than a comparable beam mounted compressor",
            title="IJACK UNOGAS Artificial Lift Plus EGAS Casing Gas Compressor",
        ),
        dict(
            file="img/unogas/ijack-unogas-uno-and-egas-artificial-lift-and-casing-gas-compressor-winter-cold-snow.jpg",
            width="",
            length="",
            alt="IJACK UNOGAS cheaper than a conventional pumping unit plus beam-mounted compressor",
            title="IJACK UNOGAS Artificial Lift Plus EGAS Casing Gas Compressor",
        ),
    ]

    description = "UNOGAS packages IJACK's innovative UNO™ automated hydraulic lift technology with our"
    description += " advanced EGAS™ casing gas compression system: for the most economical integrated"
    description += " solution on the market Driven by one power unit, UNOGAS is available in multiple sizes"
    description += " to meet your production volumes."

    short_name = "UNOGAS"
    posts = [p for p in pages if "date" in p.meta and short_name in p.body]

    return render_template(
        "home/unogas.html",
        uno=True,
        title="UNOGAS - UNO + EGAS Automated Hydraulic Lift Technology",
        version=current_app.config.get("VERSION_MYIJACK"),
        description=description,
        meta_image="img/Uno-Illus-JSept2018-web.png",
        short_name=short_name,
        pages=posts,
        images=images,
    )


@home.route("/patents/")
def patents():
    """Render the /patents page"""
    record_visit(user_id=getattr(current_user, "id", None))

    return render_template(
        "home/patents.html",
        title="IJACK Patents, Patent Applications, and Trademarks",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="This page lists IJACK's patents, patent applications, and trademarks.",
    )


@home.route("/gateway-setup/", methods=["POST"])
def gateway_setup():
    """
    For sending confidential AWS IoT security certificates to the new gateways
    so they can auto-configure themselves
    """
    record_visit(user_id=getattr(current_user, "id", None))

    aws_thing = request.form.get("aws_thing", None)
    if not aws_thing or not isinstance(aws_thing, str) or not len(aws_thing) > 4:
        return "Missing aws_thing", 400

    thing_type_name = request.form.get("thing_type_name", None)
    if (
        not thing_type_name
        or not isinstance(thing_type_name, str)
        or not len(thing_type_name) > 4
    ):
        return "Missing thing_type_name", 400

    aws_access_key_id = request.form.get("aws_access_key_id", None)
    if not aws_access_key_id or aws_access_key_id != os.getenv(
        "AWS_ACCESS_KEY_ID_GATEWAYS", "not set"
    ):
        return "Missing or incorrect aws_access_key_id", 400

    aws_secret_access_key = request.form.get("aws_secret_access_key", None)
    if not aws_secret_access_key or aws_secret_access_key != os.getenv(
        "AWS_SECRET_ACCESS_KEY_GATEWAYS", "not set"
    ):
        return "Missing or incorrect aws_secret_access_key", 400

    try:
        client_iot = get_boto3_client(service_name="iot")
    except Exception as e:
        return f"Error getting boto3 client: {e}", 500

    # Create and upload certificates
    # Check if thing already has certificates
    already_created = does_aws_thing_already_have_certs(client_iot, aws_thing)
    if already_created:
        msg = f"WARNING: aws_thing '{aws_thing}' already has certificates! Deleting them now..."
        current_app.logger.warning(msg)
        detach_revoke_delete_certificate(client_iot=client_iot, aws_thing=aws_thing)
    else:
        create_thing_and_add_to_group(client_iot, aws_thing, thing_type_name)

    # Flask temporary file location
    bundle_dir = Path("/tmp")
    certificate_stuff(bundle_dir, client_iot, aws_thing)

    certs_dir = bundle_dir.joinpath("certs")
    zip_file_path: str = create_zip_file_from_folder_path(folder_path=certs_dir)

    # Upload to public.gw table to record that a new gateway has been setup
    upload_to_public_gw_table(aws_thing, thing_type_name)

    # Send email to Sean
    sender = "myijack.com <<EMAIL>>"
    subject = f"New gateway setup for aws_thing '{aws_thing}'"
    text_body = f"""
    myijack.com has created new security certificates for gateway type '{thing_type_name}' with aws_thing '{aws_thing}'.

    View the gateway in RCOM at https://myijack.com/rcom/?gateway={aws_thing}
    View the gateway in RCOM Admin at https://myijack.com/admin/gateways?search={aws_thing}
    """
    send_email(
        subject=subject,
        sender=sender,
        to_emails=[
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ],
        text_body=text_body,
        html_body=None,
    )

    return send_file(zip_file_path, as_attachment=True, download_name=zip_file_path)


# @home.route("/", methods=["GET"], subdomain="test")
# def test(subdomain: str):
#     """Render the test template"""
#     print("print: found test subdomain route!")
#     current_app.logger.info("logger: found test subdomain route!")
#     record_visit(user_id=getattr(current_user, "id", None))
#     return {"subdomain": subdomain}


@home.route("/egas-dgas-lubricating-procedure/")
@home.route("/egas-dgas-lubrication-procedure/")
def egas_dgas_lubrication_procedure():
    """Lubricating procedure for EGAS/DGAS units"""
    record_visit(user_id=getattr(current_user, "id", None))

    return render_template(
        "auth/docs/egas-dgas-lubrication-procedure.html",
        title="Lubricating Procedure for EGAS or DGAS Pumps",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="This page details the IJACK EGAS or DGAS pump lubricating procedure.",
    )


@home.route("/egas-823-weg-15hp-motor-greasing-procedure/")
def egas_823_weg_15hp_motor_greasing_procedure():
    """EGAS 823 motor greasing procedure"""
    record_visit(user_id=getattr(current_user, "id", None))

    return render_template(
        "auth/docs/egas-823-weg-15hp-motor-greasing-procedure.html",
        title="EGAS 823 Motor Greasing Procedure",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="This page details the IJACK EGAS 823 motor greasing procedure.",
    )


@home.route("/egas-823-radiator-cleaning-procedure/")
def egas_823_radiator_cleaning_procedure():
    """EGAS 823 cooler radiator cleaning procedure"""
    record_visit(user_id=getattr(current_user, "id", None))

    return render_template(
        "auth/docs/egas-823-radiator-cleaning-procedure.html",
        title="EGAS 823 cooler radiator cleaning Procedure",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="This page details the IJACK EGAS 823 cooler radiator cleaning procedure.",
    )


@home.route("/xfer-lubrication-procedure/")
def xfer_lubrication_procedure():
    """Lubrication/greasing procedure for XFERs"""
    record_visit(user_id=getattr(current_user, "id", None))

    return render_template(
        "auth/docs/xfer-lubrication-procedure.html",
        title="Greasing Procedure for XFER Multiphase Pumps",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="This page details the IJACK XFER pump greasing/lubricating procedure.",
    )


@home.route("/marketing_email/")
def marketing_email():
    return render_template(
        "email_newsletters/rcom_update_2020_10_02.html",
    )


# @home.route("/service_bulletin/")
# def service_bulletin():
#     return render_template("email_newsletters/service_bulletin_2021_06_03.html")


@home.route("/operators_email/")
def operators_email():
    # return render_template("email_newsletters/xfer_manual_2020_10_28.html")
    return render_template(
        "email_newsletters/service_bulletin_2021_06_03.html",
    )


@home.route("/website-terms-and-conditions/")
def website_terms_and_conditions():
    record_visit(user_id=getattr(current_user, "id", None))

    return render_template(
        "home/website_terms_and_conditions.html",
        title="Website Terms and Conditions",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="This page details the terms and conditions of using the myijack.com website.",
    )


@home.route("/website-privacy-policy/")
def website_privacy_policy():
    record_visit(user_id=getattr(current_user, "id", None))

    return render_template(
        "home/website_privacy_policy.html",
        title="Website Privacy Policy",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="This page details the privacy policy of the myijack.com website.",
    )


####################################################################################################################
# Old brochure links on Google that I'm 301-permanently redirecting to the new ones
@home.route("/big-picture-world-energy-statistics-coupled-canadian-oil-natural-gas/")
@home.route("/blog/")
def redirect_world_energy():
    return redirect(url_for("home.media"), code=301)


@home.route("/media/<name>.html")
@home.route("/blog/<name>.html")
@home.route("/blog/<name>")
def redirect_to_blog_post(name):
    return redirect(url_for("home.blog_post", name=name), code=301)


@home.route("/wp-content/uploads/2018/10/IJack-Brochure-Uno-F-web.pdf")
@home.route("/wp-content/uploads/2016/06/IJACK-Uno-Insert.pdf")
@home.route(
    "/wp-content/uploads/2017/09/800-0011r2-IJack-UNO-Service-Rig-Wellhead-Setup.pdf"
)
@home.route("/static/doc/IJack-Brochure-Uno-F-web.pdf")
@home.route("/uno/uno-infographic/")
@home.route("/uno/video-button/")
@home.route("/uno/uno-gallery-2/")
@home.route("/uno/video-button/feed/")
@home.route("/static/img/Uno-Illus-JSept2018-web.png")
@home.route("/ijack-brochure-uno-f-web/")
@home.route("/uno/ijack-brochure-uno-f-web/")
@home.route("/uno/uno-gallery-1/")
def redirect_uno():
    return redirect(url_for("home.uno"), code=301)


@home.route("/about/ijack-unogas-cromer-mb-15/")
@home.route("/about/ijack-unogas-cromer-mb-25-2/feed/")
@home.route("/about/ijack-unogas-cromer-mb-17/feed/")
@home.route("/about/ijack-unogas-cromer-mb-25-2/")
@home.route("/egas/ijack-unogas/")
@home.route("/uno/ijack-brochure-egas-f-web/")
def redirect_unogas():
    return redirect(url_for("home.unogas"), code=301)


@home.route("/static/doc/IJack-Brochure-Egas-F-web.pdf")
@home.route("/wp-content/uploads/2018/10/IJack-Brochure-Egas-F-web.pdf")
@home.route("/egas/egas-website/")
@home.route("/static/img/IJACK-EGAS-Infographic-03-CMYK-April-2020.jpg")
@home.route("/dgas/ijack_egas_infographic_03-web/")
@home.route("/egas-2/")
@home.route("/static/img/iJack_EGAS_Infographic_03-web.png")
@home.route("/static/img/egas/iJack_EGAS_Infographic_03-web.png")
def redirect_egas():
    return redirect(url_for("home.egas"), code=301)


@home.route("/static/doc/IJACK_Brochure_DGAS_Imperial.pdf")
@home.route("/static/doc/IJACK_Brochure_DGAS_Metric.pdf")
@home.route("/static/doc/ijack_brochure_dgas.pdf")
def redirect_dgas_brochures():
    return redirect(url_for("static", filename="doc/IJACK_Brochure_DGAS.pdf"), code=301)


@home.route("/static/doc/IJACK_Brochure_EGAS_Imperial.pdf")
@home.route("/static/doc/IJACK_Brochure_EGAS_Metric.pdf")
@home.route("/static/doc/ijack_brochure_egas.pdf")
def redirect_egas_brochures():
    return redirect(url_for("static", filename="doc/IJACK_Brochure_EGAS.pdf"), code=301)


@home.route("/static/doc/IJACK_Brochure_BOOST_Imperial.pdf")
@home.route("/static/doc/IJACK_Brochure_BOOST_Metric.pdf")
@home.route("/static/doc/ijack_brochure_boost.pdf")
def redirect_boost_brochures():
    return redirect(
        url_for("static", filename="doc/IJACK_Brochure_BOOST.pdf"), code=301
    )


@home.route("/static/doc/IJACK_Brochure_XFER_Imperial.pdf")
@home.route("/static/doc/IJACK_Brochure_XFER_Metric.pdf")
@home.route("/static/doc/ijack_brochure_xfer.pdf")
def redirect_xfer_brochures():
    return redirect(url_for("static", filename="doc/IJACK_Brochure_XFER.pdf"), code=301)


@home.route("/static/doc/IJACK_Brochure_EGAS_VRU_Imperial.pdf")
@home.route("/static/doc/IJACK_Brochure_EGAS_VRU_Metric.pdf")
@home.route("/static/doc/ijack_brochure_egas_vru.pdf")
def redirect_vru_brochures():
    return redirect(
        url_for("static", filename="doc/IJACK_Brochure_EGAS_VRU.pdf"), code=301
    )


# @home.route("/static/doc/IJACK_Brochure_DGAS.pdf")
@home.route("/wp-content/uploads/2018/10/IJack-Brochure-Dgas-F-web.pdf")
@home.route("/wp-content/uploads/2016/06/IJACK-Dgas-Insert.pdf")
@home.route("/static/doc/IJack-Brochure-DGAS-June2020.pdf")
@home.route("/static/doc/IJack-Brochure-Dgas-F-web.pdf")
@home.route("/dgas/uno-illus/")
@home.route("/dgas/dgas-illus/")
@home.route("/internal-resource-center/ijack-dgas-insert/")
@home.route("/dgas/dgas-gallery-1/feed/")
@home.route("/dgas/dgas-gallery-2/")
@home.route("/dgas/ijack_egas_infographic_03-web/feed/")
@home.route("/dgas/uno-illus-jsept2018-web/")
@home.route("/dgas/dgas-infographic/feed/")
@home.route("/ijack-brochure-dgas-f-web/")
@home.route("/static/img/Dgas-illus-Sept2018-web.png")
@home.route("/dgas/dgas-gallery-1/")
@home.route("/dgas/dgas-illus-sept2018-web/")
@home.route("/dgas/dgas-infographic/")
@home.route("/dgas/dgas-well-data-sheet/")
def redirect_dgas():
    return redirect(url_for("home.dgas"), code=301)


@home.route("/boost/")
@home.route("/xfer/ijack-info-graphic-v5-resized/")
@home.route("/wp-content/uploads/2019/06/IJack-Brochure-XFER-Screen-V1.pdf")
@home.route("/static/doc/IJack-Brochure-XFER-Screen-V1.pdf")
@home.route("/static/doc/IJack-Brochure-XFER-I-June2020.pdf")
def redirect_xfer():
    return redirect(url_for("home.xfer"), code=301)


@home.route("/wp-login.php")
@home.route("/assets/editor/file-upload/server/php/index.php")
@home.route("/wp-content/plugins/wp-file-manager/lib/php/connector.minimal.php")
@home.route(
    "/cost-effective-tech-for-transporting-multiphase-fluids-in-single-pipeline.html/"
)
@home.route(
    "/ijack-rcom-delivers-secure-and-cost-efficient-monitoring-of-remote-pumpjack-systems/"
)
@home.route("/about/craig-davidson/")
@home.route("/files/iJack-Brochure.pdf")
@home.route("/apple-touch-icon-120x120-precomposed.png")
@home.route("/about/ijack-unogas-cromer-mb-17/")
@home.route("/author/braveadmin/")
@home.route("/index/")
@home.route("/home/")
def redirect_home():
    return redirect(url_for("home.homepage"), code=301)


@home.route("/static/img/IJACk-info-graphic-V5-Resized-540x1024.png")
@home.route("/about/dan-m/")
@home.route("/about/dan-m/feed/")
@home.route("/about/about-placeholder/")
@home.route("/about/steve-henry/")
@home.route("/about-us/")
@home.route("/home/<USER>/")
def redirect_home_about():
    return redirect(url_for("home.home_about"), code=301)


@home.route("/rcom_info/")
@home.route(
    "/ijack-rcom-delivers-secure-and-cost-efficient-monitoring-of-remote-pumpjack-systems/new-boundary-partnership-announcement-va/"
)
@home.route(
    "/ijack-rcom-delivers-secure-and-cost-efficient-monitoring-of-remote-pumpjack-systems/new-boundary-partnership-announcement-va-2/"
)
def redirect_rcom_info():
    return redirect(url_for("home.rcom_info"), code=301)


@home.route("/world_energy/")
@home.route("/blog/world_energy/")
@home.route("/blog/world_energy.html")
@home.route(
    "/big-picture-world-energy-statistics-coupled-canadian-oil-natural-gas/img_1441/feed/"
)
@home.route(
    "/big-picture-world-energy-statistics-coupled-canadian-oil-natural-gas/image001/feed/"
)
@home.route(
    "/big-picture-world-energy-statistics-coupled-canadian-oil-natural-gas/img_1441/"
)
@home.route(
    "/big-picture-world-energy-statistics-coupled-canadian-oil-natural-gas/feed/"
)
def redirect_world_energy_blog_post():
    return redirect("/blog/world-energy.html", 301)


@home.route("/about/")
def redirect_about():
    return redirect(url_for("home.home_about"), 301)


@home.route("/img/ijack-logo.png")
@home.route("/static/img/ijack-logo.png")
def ijack_logo():
    """Some alert emails use this old IJACK logo"""
    return send_from_directory(
        current_app.static_folder, "img/icons/ijack-logo-189x86.png"
    )


@home.route("/doc/xfer/800-0055r1-IJACK-XFER-Operation-and-Installation-Manual.pdf")
def redirect_xfer_manual():
    return redirect(
        url_for(
            "static",
            filename="doc/xfer/IJACK-XFER-Operation-and-Installation-Manual.pdf",
        ),
        301,
    )


@home.route("/static/img/vru/ijack-egas-vru-sizes-1600px.jpg")
def redirect_vru_pictures():
    """This old JPG picture is being used somewhere"""
    return redirect(
        url_for("static", filename="img/vru/ijack-egas-vru-sizes-1600px.webp"), 301
    )


@home.route("/calories", methods=["POST"])
def calories():
    """
    Main webhook endpoint that Google Assistant calls.
    Handles all voice commands and returns appropriate responses.
    """
    # Parse the incoming JSON request from DialogFlow
    data = request.get_json()

    # Extract the intent (type of command) from the request
    intent = data["queryResult"]["intent"]["displayName"]

    # Handle different types of voice commands
    if intent == "RecordCalories":
        return handle_record_calories(data)
    elif intent == "GetDailySummary":
        return handle_get_summary()

    # Return a generic response if the intent is not recognized
    return {"fulfillmentText": "Sorry, I didn't understand that command."}


def handle_record_calories(data):
    """
    Handle the "Record calories" command.
    Examples:
    - "Record 200 calories"
    - "Record 700 calories burned"
    """
    # Extract parameters from the DialogFlow request
    params = data["queryResult"]["parameters"]

    # Get the number of calories from the voice command
    calories = int(params["number"])

    # Determine if this is calories consumed or burned
    # DialogFlow will set entry_type to 'burn' if user says "burned" or similar
    entry_type = params.get("entry_type", "intake")

    calorie_model = Calorie(
        calories=calories,
        user_id=getattr(current_user, "id", None),
        calorie_type_id=entry_type,
    )

    # Save the entry to the database
    db.session.add(calorie_model)
    db.session.commit()

    # Prepare response based on entry type
    if entry_type == "burn":
        response_text = f"Recorded {calories} calories burned!"
    else:
        response_text = f"Recorded {calories} calories consumed!"

    # Return response that Google Assistant will speak
    return {"fulfillmentText": response_text}


def handle_get_summary():
    """
    Handle the "Get my calorie summary" command.
    Calculates total calories consumed, burned, and net calories for the current day.
    """

    # Get today's date in YYYY-MM-DD format
    today = utcnow_naive().strftime("%Y-%m-%d")

    # Calculate totals for today using SQL
    CALORIE_TYPE_ID_CONSUMED = 2
    CALORIE_TYPE_ID_BURNED = 1
    burn = (
        db.session.query(Calorie)
        .filter(Calorie.user_id == getattr(current_user, "id", None))
        .filter(
            Calorie.timestamp_utc >= today,
            Calorie.calorie_type_id == CALORIE_TYPE_ID_BURNED,
        )
    )
    intake = (
        db.session.query(Calorie)
        .filter(Calorie.user_id == getattr(current_user, "id", None))
        .filter(
            Calorie.timestamp_utc >= today,
            Calorie.calorie_type_id == CALORIE_TYPE_ID_CONSUMED,
        )
    )
    # Calculate net calories
    net = intake - burn  # type: ignore

    # Prepare detailed summary for Google Assistant to speak
    response_text = (
        f"Today's summary:\n"
        f"Calories consumed: {intake}\n"
        f"Calories burned: {burn}\n"
        f"Net calories: {net}"
    )

    return {"fulfillmentText": response_text}


# @home.route("/tasks/", methods=["POST"])
# def run_task():
#     content = request.json
#     task_type = content["type"]
#     task = create_task.delay(int(task_type))
#     return {"task_id": task.id}, 202


# @home.route("/tasks/<task_id>", methods=["GET"])
# def get_status(task_id):
#     task_result = AsyncResult(task_id)
#     result = {
#         "task_id": task_id,
#         "task_status": task_result.status,
#         "task_result": task_result.result,
#     }
#     return result, 200
