import re

import phonenumbers
from flask_login import current_user
from flask_wtf import FlaskForm
from sqlalchemy import func
from wtforms.fields import (
    BooleanField,
    PasswordField,
    SelectField,
    SelectMultipleField,
    StringField,
    SubmitField,
    TelField,
)
from wtforms.validators import (
    DataRequired,
    Email,
    EqualTo,
    Length,
    Regexp,
    ValidationError,
)

from app import user_is_ijack_employee
from app.config import COUNTRY_ID_CANADA
from app.models.models import User

# PASSWORD_REGEXP = "^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{4,8}$"
# PASSWORD_REGEXP = "(?=^.{6,255}$)((?=.*\d)(?=.*[A-Z])(?=.*[a-z])|(?=.*\d)(?=.*[^A-Za-z0-9])(?=.*[a-z])|(?=.*[^A-Za-z0-9])(?=.*[A-Z])(?=.*[a-z])|(?=.*\d)(?=.*[A-Z])(?=.*[^A-Za-z0-9]))^.*"
PASSWORD_REGEXP = r"^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[A-Za-z0-9\[\]\(\)\-\—\– {};:=<>_+^#$@!%*?&.]{8,}$"
# example = "Ab12345&"
PASSWORD_VALIDATORS = [
    DataRequired(),
    # Length(min=8, message="Password must be at least 8 characters"),
    Regexp(
        PASSWORD_REGEXP,
        message="Password must be at least eight characters, including at least one UPPERCASE letter, one lowercase letter, one number, and one special character from the following list: []()-— –{};:=<>_+^#$@!%*?&.",
    ),
]


# class FilesRequired(DataRequired):
#     """Validates that all entries are Werkzeug
#     :class:`~werkzeug.datastructures.FileStorage` object.

#     :param message: error message
#     """

#     def __call__(self, form, field):
#         if not (field.data and all(isinstance(x, FileStorage) for x in field.data)):
#             raise StopValidation(
#                 self.message or field.gettext('This field is required.'),
#             )


# class FilesAllowed(object):
#     """Validates that all the uploaded files are allowed by a given list of
#     extensions or a Flask-Uploads :class:`~flaskext.uploads.UploadSet`.
#     :param upload_set: A list of extensions or an
#         :class:`~flaskext.uploads.UploadSet`
#     :param message: error message
#     You can also use the synonym ``files_allowed``.
#     """

#     def __init__(self, upload_set, message=None):
#         self.upload_set = upload_set
#         self.message = message

#     def __call__(self, form, field):
#         if not (field.data and all(isinstance(x, FileStorage) for x in field.data)):
#             return

#         for data in field.data:
#             filename = data.filename.lower()

#             if isinstance(self.upload_set, Iterable):
#                 if any(filename.endswith('.' + x) for x in self.upload_set):
#                     return

#                 raise StopValidation(self.message or field.gettext(
#                     'File does not have an approved extension: {extensions}'
#                 ).format(extensions=', '.join(self.upload_set)))

#             if not self.upload_set.file_allowed(data, filename):
#                 raise StopValidation(self.message or field.gettext(
#                     'File does not have an approved extension.'
#                 ))


# class Select2MultipleField(SelectMultipleField):
#     """Select2 dropdown field (better than regular SelectMultipleField)"""
#     def pre_validate(self, form):
#         # Prevent "not a valid choice" error
#         pass

#     def process_formdata(self, valuelist):
#         if valuelist:
#             self.data = ",".join(valuelist)
#         else:
#             self.data = ""


def validate_email(
    form, field, must_be_unique: bool = True, raise_error: bool = True
) -> str:
    """Validate whether the email address is already in the database"""

    if isinstance(field, str):
        field_data_email = field.lower().strip()
    elif getattr(field, "data", None):
        field_data_email = str(field.data).lower().strip()
    else:
        if raise_error:
            raise ValueError(f"Unable to validate email address {field}")
        return None

    # Make a regular expression
    # for validating an Email
    regex = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,7}\b"
    if not re.fullmatch(regex, field_data_email):
        if raise_error:
            raise ValidationError("Invalid email address format")
        return None

    if must_be_unique:
        if User.query.filter(func.lower(User.email) == field_data_email).first():
            if raise_error:
                raise ValidationError("Email is already in use.")
            return None

    return field_data_email


def validate_phone(
    form, field, must_be_unique: bool = False, raise_error: bool = True
) -> phonenumbers.PhoneNumber:
    """Validate the submitted phone number"""

    if isinstance(field, str):
        field_data_phone = field.strip()
    elif getattr(field, "data", None):
        field_data_phone = str(field.data).strip()
    else:
        field_data_phone = None

    # IJACK users can intentionally enter a null value for the phone number
    if not field_data_phone:
        if user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
            return True
        if raise_error:
            raise ValidationError("Phone number is required.")
        return None

    try:
        phone_parsed = phonenumbers.parse(field_data_phone, region="US")
        if not phonenumbers.is_valid_number(phone_parsed):
            raise ValueError()
        phone_e164 = phonenumbers.format_number(
            phone_parsed, phonenumbers.PhoneNumberFormat.E164
        )
    except (phonenumbers.phonenumberutil.NumberParseException, ValueError) as err:
        if raise_error:
            raise ValidationError(f"Invalid phone number: {err}")
        return None

    if must_be_unique:
        if User.query.filter(func.btrim(User.phone) == phone_e164).first():
            if raise_error:
                raise ValidationError("Phone number is already in use.")
            return None

    return phone_parsed


class RegistrationForm(FlaskForm):
    """Base user registration form for customer-admin users to create new account"""

    email = StringField(
        "Email", validators=[DataRequired(), Email(), validate_email], id="email"
    )

    password = PasswordField("Password", validators=PASSWORD_VALIDATORS, id="password")
    confirm_password = PasswordField(
        "Confirm Password",
        validators=[EqualTo("password"), DataRequired()],
        id="confirm_password",
    )

    first_name = StringField("First Name", validators=[DataRequired()], id="first_name")
    last_name = StringField("Last Name", validators=[DataRequired()], id="last_name")

    # country_code = SelectField("Country", default="CA", validators=[DataRequired()])
    country_id = SelectField(
        "Country",
        coerce=int,
        default=COUNTRY_ID_CANADA,
        validators=[DataRequired()],
        id="country_id",
    )
    phone = TelField("Phone", validators=[DataRequired(), validate_phone], id="phone")

    # 3 is Edmonton, 2 is Regina
    time_zone_id = SelectField(
        "Time Zone",
        coerce=int,
        default=3,
        validators=[DataRequired()],
        id="time_zone_id",
    )

    # Email marketing campaigns
    eml_unsubscribe_all = BooleanField(
        "Don't receive ANY IJACK emails", default=False, id="eml_unsubscribe_all"
    )
    eml_service = BooleanField("Service update emails", default=True, id="eml_service")
    eml_rcom = BooleanField("RCOM update emails", default=True, id="eml_rcom")
    eml_new_products = BooleanField(
        "New product emails", default=True, id="eml_new_products"
    )
    eml_marketing = BooleanField("Marketing emails", default=True, id="eml_marketing")

    # default_alerts =

    submit = SubmitField("Register", id="submit")


class RegistrationFormVoluntary(RegistrationForm):
    """For marketing purposes where new users self-register themselves."""

    # New voluntary data for self-registration
    city = StringField(
        "City",
        validators=[
            DataRequired(),
            Length(min=3, message="City must be at least 3 characters"),
        ],
        id="city",
    )
    company = StringField(
        "Company",
        validators=[
            DataRequired(),
            Length(min=3, message="Company must be at least 3 characters"),
        ],
        id="company",
    )


class RegistrationFormAdmin(RegistrationForm):
    """
    For IJACK admin users only, not customer admins.
    Extends the base registration form class above.
    """

    customer_id = SelectField(
        "Customer", coerce=int, validators=[DataRequired()], id="customer_id"
    )

    # The default role_id with id = 2 is "Customer"
    roles_label = "Roles: 'Customer (Default)' can only see one company's units"
    roles = SelectMultipleField(
        roles_label, coerce=int, default=2, validators=[DataRequired()], id="roles"
    )

    # IJACK Employees can leave the phone field blank
    phone = TelField("Phone", id="phone")
