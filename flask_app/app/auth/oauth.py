import os
from datetime import <PERSON><PERSON><PERSON>

from flask import flash
from flask_dance.consumer import oauth_authorized, oauth_error
from flask_dance.consumer.storage.sqla import SQLAlchemyStorage
from flask_dance.contrib.azure import azure, make_azure_blueprint
from flask_dance.contrib.github import github, make_github_blueprint
from flask_dance.contrib.google import google, make_google_blueprint
from flask_login import current_user, login_user
from requests import Response
from sqlalchemy.exc import NoResultFound
from werkzeug.security import generate_password_hash

from app import db
from app.models.models import OAuth, User
from shared.utils.string_utils import generate_random_string


def get_client_id_and_secret_dict(provider: str) -> dict:
    """
    Get the client ID and secret names for the given OAuth provider.
    Currently supports Azure, Google, and GitHub.
    """
    provider_upper: str = str(provider).upper()
    if provider_upper not in ("AZURE", "GOOGLE", "GITHUB"):
        raise ValueError(
            f"Invalid OAuth provider: {provider}. Only Azure, Google, and GitHub are supported."
        )

    env: str = os.getenv("FLASK_CONFIG", "production")

    client_id_env_var_name: str = f"{provider_upper}_OAUTH_CLIENT_ID_PROD"
    client_secret_env_var_name: str = f"{provider_upper}_OAUTH_CLIENT_SECRET_PROD"
    if env == "staging":
        client_id_env_var_name = f"{provider_upper}_OAUTH_CLIENT_ID_STAGING"
        client_secret_env_var_name = f"{provider_upper}_OAUTH_CLIENT_SECRET_STAGING"
    elif env in ("testing", "development", "wsl"):
        client_id_env_var_name = f"{provider_upper}_OAUTH_CLIENT_ID_TESTING"
        client_secret_env_var_name = f"{provider_upper}_OAUTH_CLIENT_SECRET_TESTING"

    dict_ = {
        "client_id": os.getenv(client_id_env_var_name),
        "client_secret": os.getenv(client_secret_env_var_name),
    }
    if provider_upper == "AZURE":
        dict_["tenant"] = os.getenv(f"{provider_upper}_OAUTH_TENANT_ID_PROD")

    return dict_


azure_id_and_secret_dict: dict = get_client_id_and_secret_dict("azure")
google_id_and_secret_dict: dict = get_client_id_and_secret_dict("google")
github_id_and_secret_dict: dict = get_client_id_and_secret_dict("github")

# Azure blueprint for Azure OAuth
azure_bp = make_azure_blueprint(
    **azure_id_and_secret_dict,
    # redirect_to="azure.login",
    # redirect_url="/login/azure/authorized",
    # redirect_url="https://myijack.com/login/azure/authorized/",
    # login_url="/login/azure/",
    # authorized_url="/login/azure/authorized",
    storage=SQLAlchemyStorage(
        OAuth,
        db.session,
        user=current_user,
        user_required=False,
        # user_required=True,
    ),
)

# Google blueprint for Google OAuth
google_bp = make_google_blueprint(
    **google_id_and_secret_dict,
    # scope=["profile", "email"],
    scope=[
        "openid",
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile",
    ],
    # redirect_to="google.login",
    # redirect_url="/login/google/authorized",
    # redirect_url="https://myijack.com/login/google/authorized/",
    # login_url="/login/google/",
    # authorized_url="/login/google/authorized",
    storage=SQLAlchemyStorage(
        OAuth,
        db.session,
        user=current_user,
        user_required=False,
        # user_required=True,
    ),
)

# GitHub blueprint for GitHub OAuth
github_bp = make_github_blueprint(
    **github_id_and_secret_dict,
    # redirect_to="github.login",
    # redirect_url="/login/github/authorized",
    # redirect_url="https://myijack.com/login/github/authorized/",
    # login_url="/login/github/",
    # authorized_url="/login/github/authorized",
    storage=SQLAlchemyStorage(
        OAuth,
        db.session,
        user=current_user,
        user_required=False,
        # user_required=True,
    ),
)


def after_oauth_login(blueprint, token: str) -> bool | Response:
    """
    Allow users to log in with OAuth, and create local user accounts automatically when they do so.
    https://flask-dance.readthedocs.io/en/latest/multi-user.html
    https://flask-dance.readthedocs.io/en/latest/signals.html#flask_dance.consumer.oauth_authorized

    Returning False or a Response object from this signal handler indicates to Flask-Dance
    that it should not try to store the OAuth token for you.

    WARNING: If you return False from a oauth_authorized signal handler, and you do not store the
    OAuth token in your database, the OAuth token will be lost, and you will not be able
    to use it to make API calls in the future!

    When the user connects via the github_blueprint, the github_logged_in function gets executed.
    It takes in two parameters: the blueprint and the token (from GitHub).
    We then grabbed the username from the provider and performed one of two actions:
        1. If the username is already present in the tables, we log the user in
        2. If not, we create a new user and then log the user in
    """
    blueprint_name_title = str(blueprint.name).title()
    if not token:
        flash(f"Failed to log in with {blueprint_name_title}", category="error")
        return False

    # # If remember=False (default), it will keep coming back to login
    # # because current_user.is_authenticated = False instantly after the redirect
    # not_my_computer: bool = form.not_my_computer.data
    # # if it's not my computer, don't "remember me"
    remember_me: bool = True
    duration: timedelta = timedelta(days=365)
    # not_my_computer = request.args.get("not_my_computer")
    # not_my_computer = session.get("not_my_computer")

    if blueprint.name == "azure":
        resp = azure.get("/v1.0/me")
    elif blueprint.name == "google":
        resp = google.get("/oauth2/v1/userinfo")
    elif blueprint.name == "github":
        resp = github.get("/user")
    else:
        raise ValueError(
            f"Invalid OAuth provider: {blueprint_name_title}. Only Azure, Google, and GitHub are supported."
        )

    if not resp.ok:
        msg = f"Failed to fetch user info from {blueprint_name_title}"
        flash(msg, category="error")
        return False

    account_info = resp.json()
    if blueprint.name == "azure":
        provider_user_id = str(account_info["id"])
        email = account_info["mail"]
        first_name = account_info["givenName"]
        last_name = account_info["surname"]
    elif blueprint.name == "google":
        provider_user_id = str(account_info["id"])
        email = account_info["email"]
        first_name = account_info["given_name"]
        last_name = account_info["family_name"]
    elif blueprint.name == "github":
        provider_user_id = str(account_info["id"])
        email = account_info["email"]
        first_name = account_info["name"].split()[0]
        last_name = account_info["name"].split()[1]
    else:
        raise ValueError(
            f"Invalid OAuth provider: {blueprint_name_title}. Only Azure, Google, and GitHub are supported."
        )

    # Find this OAuth token in the database, or create it
    query = OAuth.query.filter_by(
        provider=blueprint.name,
        provider_user_id=provider_user_id,
    )
    try:
        oauth = query.one()
    except NoResultFound:
        oauth = OAuth(
            provider=blueprint.name,
            provider_user_id=provider_user_id,
            token=token,
        )

    if oauth.user_rel:
        if not oauth.user_rel.is_active:
            flash(f"User {oauth.user_rel} not active.", category="error")
            return False

        # If this OAuth token already has an associated local account,
        # log in that local user account.
        # Note that if we just created this OAuth token, then it can't
        # have an associated local account yet.
        login_user(oauth.user_rel, remember=remember_me, duration=duration)
        flash(
            f"Successfully signed in with {blueprint_name_title} (email address {email})."
        )

    else:
        # If this OAuth token doesn't have an associated local account,
        # create a new local user account for this user. We can log
        # in that account as well, while we're at it.
        try:
            user = User.query.filter_by(email=email).one()
        except NoResultFound:
            random_password: str = generate_random_string()
            user = User(
                email=email,
                first_name=first_name,
                last_name=last_name,
                password_hash=generate_password_hash(random_password),
            )

        if not user.is_active:
            flash(f"User {user} not active.", category="error")
            return False

        # Associate the new local user account with the OAuth token
        oauth.user_rel = user
        # Save and commit our database models
        db.session.add_all([user, oauth])
        db.session.commit()
        # Log in the new local user account
        login_user(user, remember=remember_me, duration=duration)
        flash(
            f"Successfully signed in with {blueprint_name_title} (email address {email})."
        )

    # Since we're manually creating the OAuth model in the database,
    # we should return False so that Flask-Dance knows that
    # it doesn't have to do it. If we don't return False, the OAuth token
    # could be saved twice, or Flask-Dance could throw an error when
    # trying to incorrectly save it for us.
    return False


@oauth_authorized.connect_via(github_bp)
def github_logged_in(blueprint, token: str) -> bool | Response:
    return after_oauth_login(blueprint, token)


@oauth_authorized.connect_via(google_bp)
def google_logged_in(blueprint, token: str) -> bool | Response:
    return after_oauth_login(blueprint, token)


@oauth_authorized.connect_via(azure_bp)
def azure_logged_in(blueprint, token: str) -> bool | Response:
    return after_oauth_login(blueprint, token)


def on_oauth_error(blueprint, message, response):
    """Notify on OAuth provider error"""
    blueprint_name_title = str(blueprint.name).title()
    msg = f"OAuth error from {blueprint_name_title}! Message={message} response={response}"
    flash(msg, category="error")
    return None


@oauth_error.connect_via(github_bp)
def github_error(blueprint, message, response) -> None:
    """Notify on OAuth provider error"""
    on_oauth_error(blueprint, message, response)
    return None


@oauth_error.connect_via(google_bp)
def google_error(blueprint, message, response) -> None:
    """Notify on OAuth provider error"""
    on_oauth_error(blueprint, message, response)
    return None


@oauth_error.connect_via(azure_bp)
def azure_error(blueprint, message, response) -> None:
    """Notify on OAuth provider error"""
    on_oauth_error(blueprint, message, response)
    return None
