import json
import os
import secrets

from redis import Redis

from shared.utils.datetime_utils import utcnow_naive


class TokenManager:
    """Manages OAuth tokens using Redis for storage"""

    # Define token expiration constants
    AUTH_CODE_EXPIRE = 600  # 10 minutes for authorization codes
    ACCESS_TOKEN_EXPIRE = 3600  # 1 hour for access tokens
    REFRESH_TOKEN_EXPIRE = 2592000  # 30 days for refresh tokens

    # Define Redis key prefixes
    AUTH_CODE_PREFIX = "auth_code:"
    ACCESS_TOKEN_PREFIX = "access_token:"
    REFRESH_TOKEN_PREFIX = "refresh_token:"

    def __init__(self):
        """Initialize Redis connection"""
        self.redis = Redis.from_url(os.getenv("REDIS_URL", "redis://redis:6379/0"))

    def generate_auth_code(self, user_id):
        """Generate and store a secure authorization code"""
        # Create secure random code
        code = secrets.token_urlsafe(32)

        # Create storage key
        key = f"{self.AUTH_CODE_PREFIX}{code}"

        # Store in Redis with expiration
        self.redis.setex(
            key,
            self.AUTH_CODE_EXPIRE,
            json.dumps({"user_id": user_id, "created_at": utcnow_naive().isoformat()}),
        )

        return code

    def validate_auth_code(self, code):
        """Validate and consume an authorization code"""
        # Create key for lookup
        key = f"{self.AUTH_CODE_PREFIX}{code}"

        # Get stored data
        data = self.redis.get(key)

        if not data:
            return None

        # Delete code as it's single-use
        self.redis.delete(key)

        # Return associated user_id
        return json.loads(data)["user_id"]

    def generate_access_token(self, user_id):
        """Generate and store an access token"""
        # Create secure random token
        token = secrets.token_urlsafe(32)

        # Create storage key
        key = f"{self.ACCESS_TOKEN_PREFIX}{token}"

        # Store in Redis with expiration
        self.redis.setex(
            key,
            self.ACCESS_TOKEN_EXPIRE,
            json.dumps({"user_id": user_id, "created_at": utcnow_naive().isoformat()}),
        )

        return token

    def validate_access_token(self, token):
        """Validate an access token"""
        # Create key for lookup
        key = f"{self.ACCESS_TOKEN_PREFIX}{token}"

        # Get stored data
        data = self.redis.get(key)

        if not data:
            return None

        # Return associated user_id
        return json.loads(data)["user_id"]

    def generate_refresh_token(self, user_id):
        """Generate and store a refresh token"""
        # Create secure random token (longer for refresh tokens)
        token = secrets.token_urlsafe(48)

        # Create storage key
        key = f"{self.REFRESH_TOKEN_PREFIX}{token}"

        # Store in Redis with expiration
        self.redis.setex(
            key,
            self.REFRESH_TOKEN_EXPIRE,
            json.dumps({"user_id": user_id, "created_at": utcnow_naive().isoformat()}),
        )

        return token

    def validate_refresh_token(self, token):
        """Validate a refresh token"""
        # Create key for lookup
        key = f"{self.REFRESH_TOKEN_PREFIX}{token}"

        # Get stored data
        data = self.redis.get(key)

        if not data:
            return None

        # Return associated user_id
        return json.loads(data)["user_id"]

    def revoke_all_tokens(self, user_id):
        """Revoke all tokens for a specific user"""
        # Iterate through token types
        for prefix in [self.ACCESS_TOKEN_PREFIX, self.REFRESH_TOKEN_PREFIX]:
            cursor = 0
            while True:
                # Scan Redis for matching keys
                cursor, keys = self.redis.scan(cursor, f"{prefix}*")

                # Check each key
                for key in keys:
                    data = self.redis.get(key)
                    if data and json.loads(data)["user_id"] == user_id:
                        # Delete matching tokens
                        self.redis.delete(key)

                # Exit loop when scan complete
                if cursor == 0:
                    break
