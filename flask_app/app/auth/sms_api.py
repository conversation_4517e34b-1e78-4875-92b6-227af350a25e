"""This is for receiving SMS commands from <PERSON><PERSON>, and forwarding to the AWS Lambda API"""

import json
import os
import re

from flask import current_app
from requests import Response, patch
from requests.auth import HTTP<PERSON>asi<PERSON>Auth

# from twilio.twiml.messaging_response import MessagingResponse
from app.config import TEINE_API_URL
from app.dashapp.utils import convert_to_float


def teine_api_patch_command(sms_body: str) -> Response:
    """
    Parse the contents of the SMS message body,
    and send an HTTP PATCH request command to their AWS Lambda API
    """
    # try:
    #     data = json.loads(sms_body)
    # except Exception:
    #     msg = f"Trouble parsing SMS string to JSON: {sms_body}"
    #     current_app.logger.exception(msg)
    #     return Response(msg, status_code=500)

    response = Response()
    # if we want to reply to the SMS
    # response = MessagingResponse()

    data = {}
    # Split the message body by line
    for line in sms_body.splitlines():
        # Split the line by spaces
        for group in line.split(" "):
            # test = re.match("^[A][EU][_][0-9]{1,3}\\b", group)
            match = re.match("([A-Za-z_]+)=(.+)", group)
            # result = re.sub("\w+(?=\()", "SUBSTRING", group)
            if isinstance(match, re.Match):
                var, val = match.string.split("=")
                val2 = convert_to_float(val, default=val)
                data[var] = val2

    # Check authentication
    username = data.pop("username", None)
    password = data.pop("password", None)
    if (
        username is None
        or password is None
        or username != os.getenv("TEINE_USERNAME", "")
        or password != os.getenv("TEINE_PASSWORD", "")
    ):
        msg = "Teine authentication not correct. Aborting"
        current_app.logger.warning(msg)
        response.status_code = 401
        response.reason = "Unauthorized"
        return response

    unit = data.pop("unit", None)
    if not unit:
        msg = f"Teine unit empty: {unit}"
        current_app.logger.warning(msg)
        response.status_code = 404
        response.reason = "Unit not found"
        return response

    if not data:
        msg = f"Teine data empty: {data}"
        current_app.logger.warning(msg)
        response.status_code = 204
        response.reason = "204 NO CONTENT"
        return response

    current_app.logger.warning(f"Sending commands to Teine API! Commands: {data}")
    params = {"unit": unit}
    auth = HTTPBasicAuth(
        os.getenv("TEINE_USERNAME", None), os.getenv("TEINE_PASSWORD", None)
    )
    json_data = json.dumps(data)
    response = patch(
        url=TEINE_API_URL,
        json=json_data,
        params=params,
        auth=auth,
        allow_redirects=False,
    )

    return response
