from flask import url_for


def get_carousel_items() -> list:
    """Define the carousel items as a list of dictionaries. Needs Flask app context to generate URLs."""
    return [
        # First carousel item with cement pad image
        {
            # Title/caption text that appears over the image
            "caption": "XFER 1235 cement pad",
            # Alt text for accessibility
            "alt": "XFER 1235 cement pad",
            # Dictionary of srcset images with their widths
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-cement-pad-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-cement-pad-1500px.webp",
                ),
            },
            # Default source image if srcset not supported
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-cement-pad-1500px.webp",
            ),
            # Original image dimensions
            "width": 1500,
            "height": 729,
        },
        # Second carousel item with dual image 1
        {
            "caption": "XFER 1235 dual",
            "alt": "XFER 1235 dual",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-1-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-1-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-1-1500px.webp",
            ),
            "width": 1500,
            "height": 729,
        },
        # Third carousel item with dual image 2
        {
            "caption": "XFER 1235 dual",
            "alt": "XFER 1235 dual",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-2-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-2-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-2-1500px.webp",
            ),
            "width": 1500,
            "height": 711,
        },
        # Fourth carousel item with heated building image
        {
            "caption": "XFER 1235 dual in heated building",
            "alt": "XFER 1235 dual in heated building",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-heated-building-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-heated-building-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-dual-heated-building-1500px.webp",
            ),
            "width": 1500,
            "height": 1125,
        },
        # Fifth carousel item with flexible hoses
        {
            "caption": "XFER 1235 with flexible hoses",
            "alt": "XFER 1235 with flexible hoses",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-flexible-hoses-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-flexible-hoses-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-flexible-hoses-1500px.webp",
            ),
            "width": 1500,
            "height": 711,
        },
        # Sixth carousel item with header image
        {
            "caption": "XFER 1235 header",
            "alt": "XFER 1235 header",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-header-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-header-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-header-1500px.webp",
            ),
            "width": 1500,
            "height": 729,
        },
        # Seventh carousel item with rigid piping header
        {
            "caption": "XFER 1235 header with rigid piping",
            "alt": "XFER 1235 header with rigid piping",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-header-rigid-piping-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-header-rigid-piping-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-header-rigid-piping-1500px.webp",
            ),
            "width": 1500,
            "height": 1125,
        },
        # Eighth carousel item with flexhose pumps
        {
            "caption": "XFER 1235 rigid piping with flexhose pumps",
            "alt": "XFER 1235 rigid piping with flexhose pumps",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-rigid-piping-with-flexhose-pumps-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-rigid-piping-with-flexhose-pumps-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-rigid-piping-with-flexhose-pumps-1500px.webp",
            ),
            "width": 1500,
            "height": 711,
        },
        # Ninth carousel item with steel flexible hoses
        {
            "caption": "XFER 1235 with steel flexible hoses",
            "alt": "XFER 1235 with steel flexible hoses",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-steel-flexible-hoses-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-steel-flexible-hoses-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-steel-flexible-hoses-1500px.webp",
            ),
            "width": 1500,
            "height": 729,
        },
        # Tenth carousel item with insulation blanket
        {
            "caption": "XFER 1235 with insulation blanket",
            "alt": "XFER 1235 with insulation blanket",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-with-insulation-blanket-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1235-with-insulation-blanket-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1235-with-insulation-blanket-1500px.webp",
            ),
            "width": 1500,
            "height": 1125,
        },
        # Eleventh carousel item with 1245 cement pad
        {
            "caption": "XFER 1245 on cement pad",
            "alt": "XFER 1245 on cement pad",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1245-cement-pad-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-1245-cement-pad-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-1245-cement-pad-1500px.webp",
            ),
            "width": 1500,
            "height": 710,
        },
        # Twelfth carousel item with 2270 and 1245 in series (1)
        {
            "caption": "XFER 2270 and 1245 in series",
            "alt": "XFER 2270 and 1245 in series",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-and-1245-in-series-units-1-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-and-1245-in-series-units-1-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-2270-and-1245-in-series-units-1-1500px.webp",
            ),
            "width": 1500,
            "height": 710,
        },
        # Thirteenth carousel item with 2270 and 1245 in series (2)
        {
            "caption": "XFER 2270 and 1245 in series",
            "alt": "XFER 2270 and 1245 in series",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-and-1245-in-series-units-2-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-and-1245-in-series-units-2-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-2270-and-1245-in-series-units-2-1500px.webp",
            ),
            "width": 1500,
            "height": 710,
        },
        # Fourteenth carousel item with 2270 header (1)
        {
            "caption": "XFER 2270 header",
            "alt": "XFER 2270 header",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-header-1-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-header-1-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-2270-header-1-1500px.webp",
            ),
            "width": 1500,
            "height": 2000,
        },
        # Fifteenth carousel item with 2270 header (2)
        {
            "caption": "XFER 2270 header",
            "alt": "XFER 2270 header",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-header-2-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-header-2-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-2270-header-2-1500px.webp",
            ),
            "width": 1500,
            "height": 1125,
        },
        # Sixteenth carousel item with 2270 parallel (1)
        {
            "caption": "XFER 2270 parallel",
            "alt": "XFER 2270 parallel",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-parallel-1-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-parallel-1-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-2270-parallel-1-1500px.webp",
            ),
            "width": 1500,
            "height": 711,
        },
        # Seventeenth carousel item with 2270 parallel (2)
        {
            "caption": "XFER 2270 parallel",
            "alt": "XFER 2270 parallel",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-parallel-2-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-parallel-2-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-2270-parallel-2-1500px.webp",
            ),
            "width": 1500,
            "height": 711,
        },
        # Eighteenth carousel item with 2270 separator
        {
            "caption": "XFER 2270 separator",
            "alt": "XFER 2270 separator",
            "srcset": {
                "800w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-separator-800px.webp",
                ),
                "1500w": url_for(
                    "auth.s3_login_needed",
                    filename="doc/xfer/xfer_tie_in_examples/xfer-2270-separator-1500px.webp",
                ),
            },
            "src": url_for(
                "auth.s3_login_needed",
                filename="doc/xfer/xfer_tie_in_examples/xfer-2270-separator-1500px.webp",
            ),
            "width": 1500,
            "height": 711,
        },
    ]
