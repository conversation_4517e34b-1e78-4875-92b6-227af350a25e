"""
Admin API endpoints for React-Admin interface
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required
from shared.models import models as shared_models
from shared.models import models_bom as bom_models
from shared.models import models_work_order as work_order_models
from sqlalchemy import inspect

from app import is_admin
from app.models import models

admin_api_bp = Blueprint("admin_api", __name__, url_prefix="/api/v1/admin")


def admin_required(f):
    """Decorator to ensure user is admin"""

    def decorated_function(*args, **kwargs):
        if not is_admin():
            return jsonify({"error": "Unauthorized"}), 403
        return f(*args, **kwargs)

    decorated_function.__name__ = f.__name__
    return decorated_function


@admin_api_bp.route("/<string:resource>", methods=["GET"])
@login_required
@admin_required
def get_list(resource):
    """Get a list of resources with pagination and filtering"""
    # Get pagination params
    page = int(request.args.get("page", 1))
    per_page = int(request.args.get("per_page", 10))

    # Get sort params
    sort_field = request.args.get("sort", "id")
    sort_order = request.args.get("order", "asc")

    # Map resource names to model classes
    model_map = {
        "users": models.User,
        "customers": shared_models.Customer,
        "structures": shared_models.Structure,
        "gateways": shared_models.Gw,
        "parts": bom_models.Part,
        "work_orders": work_order_models.WorkOrder,
        "work_order_parts": work_order_models.WorkOrderPart,
        "warehouse_parts": bom_models.WarehousePart,
        "inventory_movements": bom_models.InventoryMovement,
        "inventory_ledgers": bom_models.InventoryLedger,
        "error_logs": shared_models.ErrorLog,
        "website_views": shared_models.WebsiteView,
        "applications": shared_models.Application,
        "career_applications": shared_models.CareerApplication,
        "roles": shared_models.Role,
        "countries": shared_models.Country,
        "provinces": shared_models.Province,
        "counties": shared_models.County,
        "cities": shared_models.City,
        "power_units": shared_models.PowerUnit,
        "gateway_info": shared_models.GwInfo,
        "release_notes": shared_models.ReleaseNote,
        "alarm_log_metrics": shared_models.AlarmLogMetric,
        "diagnostic_metrics": shared_models.DiagnosticMetric,
        "diagnostic_data": shared_models.Diagnostic,
        "sim_cards": shared_models.SIMCard,
        "calculator": shared_models.Calculator,
        "gateway_types": shared_models.GatewayType,
        "alerts": shared_models.Alert,
        "alerts_sent": shared_models.AlertsSent,
        "contact_form": shared_models.ContactForm,
        "service_requests": work_order_models.Service,
        "maintenance": work_order_models.Maintenance,
        "maintenance_types": work_order_models.MaintenanceType,
        "service_clock": shared_models.ServiceClock,
        "warehouses": bom_models.Warehouse,
        "warehouse_locations": bom_models.WarehouseLocation,
        "inventory_reservations": bom_models.InventoryReservation,
        "cycle_counts": bom_models.CycleCount,
        "cycle_count_items": bom_models.CycleCountItem,
        "part_categories": bom_models.PartCategory,
        "compression_images": shared_models.CompressionImage,
        "surface_images": shared_models.SurfaceImage,
        "compression_patterns": shared_models.CompressionPattern,
        "surface_patterns": shared_models.SurfacePattern,
        "meta_data": shared_models.MetaDataTbl,
        "hours": shared_models.Hour,
        "images": shared_models.ImageField,
        "report_email_hourly": shared_models.ReportEmailHourly,
        "report_email_derates": shared_models.ReportEmailDerates,
        "report_email_op_hours": shared_models.ReportEmailOpHours,
        "report_email_inventory": shared_models.ReportEmailInventory,
        "alerts_custom": shared_models.AlertCustom,
        "alerts_sent_users": shared_models.AlertsSentUser,
        "remote_control": shared_models.RemoteControl,
        "alerts_sent_maintenance": shared_models.AlertsSentMaint,
        "alerts_sent_maintenance_users": shared_models.AlertsSentMaintUser,
    }

    model_class = model_map.get(resource)
    if not model_class:
        return jsonify({"error": f"Unknown resource: {resource}"}), 404

    # Build query
    query = model_class.query

    # Apply filters
    for key, value in request.args.items():
        if key.startswith("filter[") and key.endswith("]"):
            field_name = key[7:-1]  # Extract field name from filter[field_name]
            if hasattr(model_class, field_name):
                # Simple equality filter for now
                query = query.filter(getattr(model_class, field_name) == value)

    # Apply sorting
    if hasattr(model_class, sort_field):
        order_by = getattr(model_class, sort_field)
        if sort_order.lower() == "desc":
            order_by = order_by.desc()
        query = query.order_by(order_by)

    # Get total count before pagination
    total = query.count()

    # Apply pagination
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)

    # Convert to dict
    data = []
    for item in pagination.items:
        item_dict = {}
        for column in inspect(model_class).columns:
            value = getattr(item, column.name)
            # Handle special types
            if hasattr(value, "isoformat"):
                value = value.isoformat()
            item_dict[column.name] = value
        data.append(item_dict)

    return jsonify({"result": {"data": data, "total": total}})


@admin_api_bp.route("/<string:resource>/<int:id>", methods=["GET"])
@login_required
@admin_required
def get_one(resource, id):
    """Get a single resource by ID"""
    # Use the same model map as get_list
    model_map = {
        "users": models.User,
        "customers": shared_models.Customer,
        "structures": shared_models.Structure,
        "gateways": shared_models.Gw,
        "parts": bom_models.Part,
        "work_orders": work_order_models.WorkOrder,
        "work_order_parts": work_order_models.WorkOrderPart,
        "warehouse_parts": bom_models.WarehousePart,
        "inventory_movements": bom_models.InventoryMovement,
        "inventory_ledgers": bom_models.InventoryLedger,
        "error_logs": shared_models.ErrorLog,
        "website_views": shared_models.WebsiteView,
        "applications": shared_models.Application,
        "career_applications": shared_models.CareerApplication,
        "roles": shared_models.Role,
        "countries": shared_models.Country,
        "provinces": shared_models.Province,
        "counties": shared_models.County,
        "cities": shared_models.City,
        "power_units": shared_models.PowerUnit,
        "gateway_info": shared_models.GwInfo,
        "release_notes": shared_models.ReleaseNote,
        "alarm_log_metrics": shared_models.AlarmLogMetric,
        "diagnostic_metrics": shared_models.DiagnosticMetric,
        "diagnostic_data": shared_models.Diagnostic,
        "sim_cards": shared_models.SIMCard,
        "calculator": shared_models.Calculator,
        "gateway_types": shared_models.GatewayType,
        "alerts": shared_models.Alert,
        "alerts_sent": shared_models.AlertsSent,
        "contact_form": shared_models.ContactForm,
        "service_requests": work_order_models.Service,
        "maintenance": work_order_models.Maintenance,
        "maintenance_types": work_order_models.MaintenanceType,
        "service_clock": shared_models.ServiceClock,
        "warehouses": bom_models.Warehouse,
        "warehouse_locations": bom_models.WarehouseLocation,
        "inventory_reservations": bom_models.InventoryReservation,
        "cycle_counts": bom_models.CycleCount,
        "cycle_count_items": bom_models.CycleCountItem,
        "part_categories": bom_models.PartCategory,
        "compression_images": shared_models.CompressionImage,
        "surface_images": shared_models.SurfaceImage,
        "compression_patterns": shared_models.CompressionPattern,
        "surface_patterns": shared_models.SurfacePattern,
        "meta_data": shared_models.MetaDataTbl,
        "hours": shared_models.Hour,
        "images": shared_models.ImageField,
        "report_email_hourly": shared_models.ReportEmailHourly,
        "report_email_derates": shared_models.ReportEmailDerates,
        "report_email_op_hours": shared_models.ReportEmailOpHours,
        "report_email_inventory": shared_models.ReportEmailInventory,
        "alerts_custom": shared_models.AlertCustom,
        "alerts_sent_users": shared_models.AlertsSentUser,
        "remote_control": shared_models.RemoteControl,
        "alerts_sent_maintenance": shared_models.AlertsSentMaint,
        "alerts_sent_maintenance_users": shared_models.AlertsSentMaintUser,
    }

    model_class = model_map.get(resource)
    if not model_class:
        return jsonify({"error": f"Unknown resource: {resource}"}), 404

    item = model_class.query.get(id)
    if not item:
        return jsonify({"error": "Not found"}), 404

    # Convert to dict
    item_dict = {}
    for column in inspect(model_class).columns:
        value = getattr(item, column.name)
        if hasattr(value, "isoformat"):
            value = value.isoformat()
        item_dict[column.name] = value

    return jsonify({"result": item_dict})


@admin_api_bp.route("/<string:resource>", methods=["POST"])
@login_required
@admin_required
def create(resource):
    """Create a new resource"""
    return jsonify({"error": "Not implemented yet"}), 501


@admin_api_bp.route("/<string:resource>/<int:id>", methods=["PUT"])
@login_required
@admin_required
def update(resource, id):
    """Update a resource"""
    return jsonify({"error": "Not implemented yet"}), 501


@admin_api_bp.route("/<string:resource>/<int:id>", methods=["DELETE"])
@login_required
@admin_required
def delete(resource, id):
    """Delete a resource"""
    return jsonify({"error": "Not implemented yet"}), 501


@admin_api_bp.route("/admin-metadata", methods=["GET"])
@login_required
@admin_required
def get_admin_metadata():
    """Extract all Flask admin view configurations including columns, labels, menu structure, etc."""
    from flask import current_app

    metadata = {"views": {}, "menu_structure": {}, "categories": []}

    # Get the admin instance from the current app
    admin_instance = None
    for extension_name, extension in current_app.extensions.items():
        if extension_name == "admin":
            admin_instance = extension
            break

    if not admin_instance:
        return jsonify({"error": "Flask-Admin not found"}), 404

    categories_set = set()

    # Extract metadata from each view
    for view in admin_instance._views:
        # Skip non-model views (like index, links, etc.)
        if not hasattr(view, "model"):
            continue

        view_name = view.endpoint
        view_data = {
            "name": view.name,
            "endpoint": view.endpoint,
            "url": getattr(view, "url", None),
            "category": getattr(view, "category", None),
            "model": view.model.__name__ if view.model else None,
            "columns": {},
            "column_list": [],
            "column_labels": {},
            "column_searchable_list": [],
            "column_sortable_list": [],
            "column_filters": [],
            "column_editable_list": [],
            "can_create": getattr(view, "can_create", True),
            "can_edit": getattr(view, "can_edit", True),
            "can_delete": getattr(view, "can_delete", True),
            "can_view_details": getattr(view, "can_view_details", False),
            "page_size": getattr(view, "page_size", 20),
            "list_template": getattr(view, "list_template", None),
            "create_template": getattr(view, "create_template", None),
            "edit_template": getattr(view, "edit_template", None),
            "details_template": getattr(view, "details_template", None),
        }

        # Add category to set
        if view_data["category"]:
            categories_set.add(view_data["category"])

        # Extract column information
        if hasattr(view, "model") and view.model:
            model_columns = inspect(view.model).columns

            # Get column list (what columns to display in list view)
            if hasattr(view, "column_list") and view.column_list:
                view_data["column_list"] = list(view.column_list)
            else:
                # Default to all columns if not specified
                view_data["column_list"] = [col.name for col in model_columns]

            # Get column labels (human-readable names)
            if hasattr(view, "column_labels") and view.column_labels:
                view_data["column_labels"] = dict(view.column_labels)

            # Get searchable columns
            if hasattr(view, "column_searchable_list") and view.column_searchable_list:
                view_data["column_searchable_list"] = list(view.column_searchable_list)

            # Get sortable columns
            if hasattr(view, "column_sortable_list"):
                if view.column_sortable_list is True:
                    # All columns are sortable
                    view_data["column_sortable_list"] = view_data["column_list"]
                elif view.column_sortable_list:
                    view_data["column_sortable_list"] = list(view.column_sortable_list)

            # Get filters
            if hasattr(view, "column_filters") and view.column_filters:
                filters = []
                for filter_item in view.column_filters:
                    if isinstance(filter_item, str):
                        filters.append({"column": filter_item, "type": "default"})
                    else:
                        # For more complex filter objects
                        filters.append(
                            {
                                "column": getattr(
                                    filter_item, "column", str(filter_item)
                                ),
                                "type": filter_item.__class__.__name__
                                if hasattr(filter_item, "__class__")
                                else "unknown",
                            }
                        )
                view_data["column_filters"] = filters

            # Get editable columns
            if hasattr(view, "column_editable_list") and view.column_editable_list:
                view_data["column_editable_list"] = list(view.column_editable_list)

            # Extract detailed column information
            for column in model_columns:
                column_info = {
                    "name": column.name,
                    "type": str(column.type),
                    "nullable": column.nullable,
                    "primary_key": column.primary_key,
                    "foreign_key": len(column.foreign_keys) > 0,
                    "default": str(column.default) if column.default else None,
                    "autoincrement": getattr(column, "autoincrement", False),
                }

                # Add foreign key information
                if column.foreign_keys:
                    fk_info = []
                    for fk in column.foreign_keys:
                        fk_info.append(
                            {"table": fk.column.table.name, "column": fk.column.name}
                        )
                    column_info["foreign_keys"] = fk_info

                view_data["columns"][column.name] = column_info

        metadata["views"][view_name] = view_data

    # Build menu structure
    metadata["categories"] = sorted(list(categories_set))

    # Group views by category
    for category in metadata["categories"]:
        metadata["menu_structure"][category] = []
        for view_name, view_data in metadata["views"].items():
            if view_data["category"] == category:
                metadata["menu_structure"][category].append(
                    {
                        "name": view_data["name"],
                        "endpoint": view_data["endpoint"],
                        "url": view_data["url"],
                    }
                )

    # Add views without category to "Other" or root level
    uncategorized_views = []
    for view_name, view_data in metadata["views"].items():
        if not view_data["category"]:
            uncategorized_views.append(
                {
                    "name": view_data["name"],
                    "endpoint": view_data["endpoint"],
                    "url": view_data["url"],
                }
            )

    if uncategorized_views:
        metadata["menu_structure"]["Uncategorized"] = uncategorized_views
        if "Uncategorized" not in metadata["categories"]:
            metadata["categories"].append("Uncategorized")

    return jsonify(metadata)
