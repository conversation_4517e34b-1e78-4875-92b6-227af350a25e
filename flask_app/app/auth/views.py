import concurrent.futures
import json
import os
import time
import urllib.parse
from datetime import datetime
from functools import wraps
from io import BytesIO
from typing import Callable, Type

import ipinfo
import pandas as pd
import pytz
import requests
from flask import (
    Blueprint,
    Response,
    abort,
    current_app,
    flash,
    make_response,
    redirect,
    render_template,
    request,
    send_file,
    url_for,
)
from flask_login import current_user, login_required, logout_user
from ipinfo.handler_utils import cache_key
from shared.models.models import (
    ApplicationUploadFile,
    CareerFile,
    UserAuthenticationChallenge,
    UserRegistrationChallenge,
    WebauthnCredential,
    WebsiteView,
)
from shared.models.models_work_order import WorkOrder, WorkOrderUploadFile
from sqlalchemy import func
from sqlalchemy.sql import text
from webauthn import (
    base64url_to_bytes,
    verify_authentication_response,
    verify_registration_response,
)
from webauthn.helpers.structs import (
    AuthenticationCredential,
    AuthenticatorAssertionResponse,
)

from app import (
    cache_memoize_if_prod,
    db,
    get_user_cust_ids,
    is_admin,
    is_admin_cust,
    user_is_demo_customer,
    user_is_ijack_employee,
)
from app.auth.docs_carousel_images import get_carousel_items
from app.config import (
    UNIT_TYPE_ID_BOOST,
    UNIT_TYPE_ID_EGAS,
    UNIT_TYPE_ID_VRU,
    UNIT_TYPE_ID_XFER,
)
from app.dashapp.callbacks.charts_cards import get_card_data_for_charts
from app.dashapp.callbacks.charts_utils import (
    get_time_series_data,
    time_series_real_time,
)
from app.dashapp.metrics import get_labels, names_uno_unogas, names_xfer_egas
from app.dashapp.utils import update_shadow
from app.databases import run_sql_query
from app.email_stuff import (
    send_email,
)
from app.models.models import User
from app.utils.complex import (
    generate_presigned_url_from_aws_s3,
)
from app.utils.pdf_generator import generate_work_order_pdf
from app.utils.simple import (
    check_confirmed,
    create_jwt_encoded_token,
    decode_jwt_token,
    utcnow_naive,
)

auth = Blueprint("auth", __name__)


def _redirect():
    """Finds where to redirect the user, after logging in, typically"""
    next_url = request.args.get("next", url_for("home.homepage"))
    return redirect(next_url)


@cache_memoize_if_prod(timeout=60)
def record_visit(*, user_id: int, page: str = None) -> bool:
    """
    Record the website visit (who, where, when).
    Note the * which forces users of this function to use keyword arguments only.
    This is helpful in the check_current_user() wrapper.
    """
    if (
        os.getenv("FLASK_CONFIG") in ("development", "wsl")
    ) and not current_app.testing:
        # Don't record visit in development environment, except if we're testing
        return False

    page = page or request.path
    record = None
    if hasattr(current_user, "customers_rel"):
        company: str = None
        customers_list: list = current_user.customers_rel
        if isinstance(customers_list, list) and len(customers_list) > 0:
            first_customer = customers_list[0]
            company = getattr(first_customer, "customer", None)

        record = WebsiteView(
            page=page,
            timestamp_utc=utcnow_naive(),
            user_id=getattr(current_user, "id", None),
            company=company,
        )
    else:
        ip_address = request.remote_addr
        ip_cache_key = cache_key(ip_address)
        handler = ipinfo.getHandler(os.getenv("IP_INFO_TOKEN"))
        try:
            if ip_cache_key in handler.cache:
                details = handler.cache[ip_cache_key]
            else:
                details = handler.getDetails()

            record = WebsiteView(
                page=page,
                timestamp_utc=utcnow_naive(),
                user_id=None,
                city=details.all.get("city", None),
                region=details.all.get("region", None),
                timezone=details.all.get("timezone", None),
                company=details.all.get("company", None),
                country_code=details.all.get("country", None),
                country_name=details.all.get("country_name", None),
                ip=details.all.get("ip", None),
                postal=details.all.get("postal", None),
                gps_lat=details.all.get("gps_lat", None),
                gps_lon=details.all.get("gps_lon", None),
            )
        except ipinfo.exceptions.RequestQuotaExceededError:
            current_app.logger.debug("ipinfo.exceptions.RequestQuotaExceededError")
        except Exception:
            current_app.logger.exception("Trouble getting IP address info")
            # error_send_email_w_details(error, db)

    if isinstance(record, WebsiteView):
        db.session.add(record)
        db.session.commit()

    return True


def flash_errors(form):
    """Flashes form errors"""
    for field, errors in form.errors.items():
        for error in errors:
            flash(
                "Error in the %s field: %s" % (getattr(form, field).label.text, error),
                "error",
            )


def get_rp_id_and_origin() -> tuple:
    """Get the expected RP ID and origin"""
    if os.getenv("FLASK_CONFIG") in ("development", "wsl"):
        expected_rp_id = "app.localhost"
        # If we're using HTTPS/SSL/TLS in development with Traefik, we need to use the https origin
        expected_origin = f"http://{request.host}"
    else:
        expected_rp_id = "myijack.com"
        expected_origin = f"https://{request.host}"
    return expected_rp_id, expected_origin


@auth.route("/debug-domain")
@login_required
def debug_domain():
    """Get the domain name and other request details"""
    return {"host": request.host, "url": request.url, "headers": dict(request.headers)}


# @auth.route("/verify-registration/", methods=["POST"])
@auth.route("/verify-registration/<email>", methods=["POST"])
def verify_registration(email: str):
    """Verify the registration response from the authenticator"""

    if not email:
        return {"verified": False, "msg": "No email found", "status": 400}

    credential_data: dict = request.get_json()
    id_ = credential_data.get("id", None)
    if not id_:
        return {"verified": False, "msg": "No credential id found", "status": 400}

    if isinstance(credential_data, str):
        credential_data: dict = json.loads(credential_data)

    user_reg_challenge_model = (
        UserRegistrationChallenge.query.join(User).filter_by(email=email).first()
    )
    # current_registration_challenge = cache.get(f"registration_challenge_{email}")
    current_registration_challenge = getattr(
        user_reg_challenge_model, "challenge", None
    )
    if not current_registration_challenge:
        return {
            "verified": False,
            "msg": "No registration challenge found. Please click 'Create PassKey' again.",
            "status": 400,
        }

    challenge_expiry_utc: datetime = getattr(
        user_reg_challenge_model, "challenge_expiry_utc", None
    )
    if (
        not isinstance(challenge_expiry_utc, datetime)
        or challenge_expiry_utc < utcnow_naive()
    ):
        challenge_sent_at_utc: datetime = getattr(
            user_reg_challenge_model, "challenge_sent_at_utc", None
        )
        current_app.logger.error(
            "PassKey authentication challenge expired. challenge_expiry_utc = %s; type = %s. challenge_sent_at_utc = %s; type = %s",
            challenge_expiry_utc,
            type(challenge_expiry_utc),
            challenge_sent_at_utc,
            type(challenge_sent_at_utc),
        )
        return {
            "verified": False,
            "msg": "PassKey registration challenge expired. Please authenticate again.",
            "status": 400,
        }

    if isinstance(current_registration_challenge, bytes):
        expected_challenge = current_registration_challenge
    else:
        expected_challenge = base64url_to_bytes(current_registration_challenge)

    expected_rp_id, expected_origin = get_rp_id_and_origin()
    try:
        verification = verify_registration_response(
            credential=credential_data,  # from client-side
            expected_challenge=expected_challenge,  # from server-side
            expected_origin=expected_origin,  # dont forget the ports
            expected_rp_id=expected_rp_id,
            require_user_verification=True,
        )
    except Exception:
        verification = None
        # return {"verified": False, "msg": str(err), "status": 400}
        current_app.logger.exception("Error verifying registration response")
        pass

    if verification is None:
        return {"verified": False, "msg": "Verification failed", "status": 400}

    user = User.query.with_entities(User.id).filter_by(email=email).first()
    if not user or not len(user) > 0:
        return {
            "verified": False,
            "msg": f"No user found with email {email}",
            "status": 400,
        }
    user_id = user[0]

    # There's a unique constraint on user_id and rp_id in the database
    model = WebauthnCredential.query.filter_by(
        user_id=user_id, rp_id=expected_rp_id
    ).first()
    if not model:
        model = WebauthnCredential(user_id=user_id, rp_id=expected_rp_id)
        db.session.add(model)

    model.credential_id = verification.credential_id
    model.credential_public_key = verification.credential_public_key
    model.sign_count = verification.sign_count
    # TODO: Add transports to the model
    # model.transports = verification.get("transports", [])
    db.session.commit()

    return {"verified": True}


@auth.route("/verify-authentication", methods=["POST"])
def verify_authentication():
    """
    Verify the WebAuthn PassKey authentication response from the authenticator,
    after the user has chosen the PassKey to use for signing in.
    """

    # Get the credential data sent from the client
    credential_data = request.get_json()

    # Log the incoming credential data
    current_app.logger.info(f"Received credential: {credential_data}")

    id_ = credential_data.get("id", None)
    if not id_:
        return {"verified": False, "msg": "No credential id found", "status": 400}

    # Get the PassKey from the database
    credential_id: bytes = base64url_to_bytes(id_)
    cred_model = WebauthnCredential.query.filter_by(credential_id=credential_id).first()
    if not cred_model:
        return {
            "verified": False,
            "msg": "No PassKey found in database with this credential ID",
            "status": 400,
        }

    email = cred_model.user_rel.email
    if not email:
        # This is very unlikely to happen
        return {
            "verified": False,
            "msg": f"No email found for user {cred_model.user_rel}",
            "status": 400,
        }

    # current_authentication_challenge = cache.get(f"authentication_challenge_{email}")
    user_auth_challenge_model = (
        UserAuthenticationChallenge.query.join(User).filter_by(email=email).first()
    )
    current_authentication_challenge = getattr(
        user_auth_challenge_model, "challenge", None
    )

    # Log the fetched challenge
    current_app.logger.info(
        f"Stored challenge type: {type(current_authentication_challenge)}"
    )
    current_app.logger.info(f"Stored challenge: {current_authentication_challenge}")

    if not current_authentication_challenge:
        return {
            "verified": False,
            "msg": f"No authentication challenge found for user with email {email}. Please try again.",
            "status": 400,
        }

    challenge_expiry_utc: datetime = getattr(
        user_auth_challenge_model, "challenge_expiry_utc", None
    )
    if (
        not isinstance(challenge_expiry_utc, datetime)
        or challenge_expiry_utc < utcnow_naive()
    ):
        challenge_sent_at_utc: datetime = getattr(
            user_auth_challenge_model, "challenge_sent_at_utc", None
        )
        current_app.logger.error(
            "PassKey authentication challenge expired. challenge_expiry_utc = %s; type = %s. challenge_sent_at_utc = %s; type = %s",
            challenge_expiry_utc,
            type(challenge_expiry_utc),
            challenge_sent_at_utc,
            type(challenge_sent_at_utc),
        )
        return {
            "verified": False,
            "msg": "PassKey authentication challenge expired. Please try again.",
            "status": 400,
        }

    expected_rp_id, expected_origin = get_rp_id_and_origin()
    try:
        # Convert the credential data to the expected format
        credential = AuthenticationCredential(
            # `id`: The Base64URL-encoded representation of raw_id
            # id=base64url_to_bytes(credential_data["id"]),
            # id=bytes_to_base64url(credential_data["id"]),
            id=credential_data["id"],
            # `raw_id`: A byte sequence representing the credential's unique identifier
            raw_id=base64url_to_bytes(credential_data["rawId"]),
            response=AuthenticatorAssertionResponse(
                client_data_json=base64url_to_bytes(
                    credential_data["response"]["clientDataJSON"]
                ),
                authenticator_data=base64url_to_bytes(
                    credential_data["response"]["authenticatorData"]
                ),
                signature=base64url_to_bytes(credential_data["response"]["signature"]),
                user_handle=base64url_to_bytes(
                    credential_data["response"]["userHandle"]
                )
                if credential_data["response"].get("userHandle")
                else None,
            ),
            type=credential_data["type"],
        )
        current_app.logger.info(f"credential: {credential}")

        # Add explicit type conversion for challenge if needed
        if isinstance(current_authentication_challenge, str):
            current_authentication_challenge = str(
                current_authentication_challenge
            ).encode("utf-8")

        # Verify the assertion
        verification = verify_authentication_response(
            credential=credential,
            expected_challenge=current_authentication_challenge,
            expected_rp_id=expected_rp_id,
            expected_origin=expected_origin,
            credential_public_key=cred_model.credential_public_key,
            credential_current_sign_count=cred_model.sign_count,
            require_user_verification=True,
        )
    except Exception as err:
        current_app.logger.exception("WebAuthn PassKey authentication error!")
        # current_app.logger.error(
        #     f"Expected challenge: {current_authentication_challenge}"
        # )
        return {
            "verified": False,
            "msg": f"Error verifying PassKey: {err}",
            "status": 400,
        }

    # Update our credential's sign count to what the authenticator says it is now
    cred_model.sign_count = verification.new_sign_count
    db.session.commit()

    return {"verified": True}


@auth.route("/logout/")
@login_required
def logout():
    """This is what happens when the user logs out"""
    logout_user()
    flash("You have successfully logged out.")
    # return redirect(url_for("home.homepage"))
    return _redirect()


def get_detailed_request_info(request) -> dict:
    """
    Extract detailed information from the request object
    """
    # Get the full URL including query parameters
    full_url = request.url

    # Get the original referrer URL if available
    referrer = request.referrer

    # Get the user's IP address
    ip_address = request.remote_addr

    # Get headers that might help identify the source
    user_agent = request.headers.get("User-Agent")
    origin = request.headers.get("Origin")

    # Get all query parameters
    query_params = request.args.to_dict()

    # Parse the URL to extract SafeLinks parameters if present
    parsed_url = urllib.parse.urlparse(full_url)
    safe_links_params = urllib.parse.parse_qs(parsed_url.query)

    # Extract the original URL from SafeLinks if present
    original_url = None
    if "url" in safe_links_params:
        encoded_url = safe_links_params["url"][0]
        original_url = urllib.parse.unquote(encoded_url)

    return {
        "full_url": full_url,
        "referrer": referrer,
        "ip_address": ip_address,
        "user_agent": user_agent,
        "origin": origin,
        "query_params": query_params,
        "safe_links_params": safe_links_params,
        "original_url": original_url,
    }


def log_unsubscribe_attempt(request_info, token, contents, user=None) -> dict:
    """
    Create detailed log entry for unsubscribe attempts
    """
    # Create timestamp in UTC
    timestamp = utcnow_naive()

    # Build the log entry dictionary
    log_entry = {
        "timestamp": timestamp,
        "token": token,
        "token_contents": contents,
        "request_info": request_info,
        "user": str(user) if user else None,
    }

    # Log the detailed information
    current_app.logger.info(
        f"Unsubscribe attempt logged: {json.dumps(log_entry, default=str)}"
    )

    return log_entry


def create_error_report(
    specific_problem: str, token: str, contents: dict, request_info: dict, user=None
) -> str:
    """
    Create a detailed error report for failed unsubscribe attempts
    """
    error_report = f"""
UNSUBSCRIBE ERROR REPORT
------------------------
Timestamp UTC: {utcnow_naive()}
Specific Problem: {specific_problem}

Request Details:
- Full URL: {request_info["full_url"]}
- Original URL: {request_info["original_url"]}
- Referrer: {request_info["referrer"]}
- IP Address: {request_info["ip_address"]}
- User Agent: {request_info["user_agent"]}
- Origin: {request_info["origin"]}

Token Information:
- Token: {token}
- Decoded Contents: {contents}

User Information:
- Current User: {user}
"""
    return error_report


@auth.route("/update_email_preferences/<token>", methods=["GET", "POST"])
def update_email_preferences(token):  # noqa: C901
    """
    If a user requests to unsubscribe from emails,
    the email link directs them here with a jwt-encoded token
    directing the website what to do. The jwt-encoded token must
    decode to a dictionary with the following keys:
    1. email
    2. message
    3. "on_or_off" = "on" or "off"
    4. "unsubscribe_type" = "all_units", "unit_alerts", "service", "report_email_hourly",
        "op_hours_100", "op_hours_1000", "report_email_derates", "report_inventory"
    """

    def render_template_error(
        specific_problem: str, detailed_info_for_ijack: str = ""
    ) -> Response:
        """Render an error template with a specific message"""

        # Get detailed request information
        request_info: dict = get_detailed_request_info(request)

        # Log the unsubscribe attempt
        log_unsubscribe_attempt(request_info, token, contents, current_user)

        if specific_problem == "ERROR decoding JWT JSON web token: Not enough segments":
            # This happens a lot, so we don't need to send an email about it
            return render_template(
                "auth/email_preferences.html",
                title="Problem Updating Your Email Preferences",
                message="The link you clicked on is invalid. Please contact us if you need help.",
            )

        # Create detailed error report
        error_report: str = create_error_report(
            specific_problem, token, contents, request_info, current_user
        )

        # Send an email with details of the error
        token_and_contents: str = f"Token: {token}. \n\nContents: {contents}."
        # request_info_str: str = f"Request info: {request}."
        send_email(
            "Error updating email preferences!!",
            text_body=f"Specific problem: {specific_problem} \n\n{token_and_contents} \n\nDetailed info for IJACK: {detailed_info_for_ijack} \n\nCurrent user: {current_user}. \n\nError report: {error_report}",
        )

        contact_url: str = url_for("dash.contact", _external=True)
        return render_template(
            "auth/email_preferences.html",
            title="Problem Updating Your Email Preferences",
            message=f"""There was a problem updating your email preferences. IJACK IT staff have been notified of the problem, and should be in contact with you shortly. We take this very seriously. Thank you for your patience.

            Please contact us at {contact_url} if you would like to send us a message.

            Specific problem: {specific_problem}""",
        )

    def render_template_success(message: str, resub_token: str) -> Response:
        """Render a success template with a message and a resubscribe token"""
        return render_template(
            "auth/email_preferences.html",
            message=message,
            resub_token=resub_token,
            title="Update Your Email Preferences",
            description="Update your IJACK email preferences, or unsubscribe to IJACK emails.",
        )

    contents: dict = decode_jwt_token(token)

    error: str = contents.get("error", None)
    if error and len(contents.keys()) == 1:
        return render_template_error(
            specific_problem=error,
        )

    email = contents.get("email", None)
    if email is None:
        return render_template_error(
            specific_problem="No email address in the JWT token.",
        )

    user = User.query.filter(func.lower(User.email) == email).first()
    if not isinstance(user, User):
        return render_template_error(
            specific_problem=f"No user found with email address '{email}'.",
        )

    message = contents.get("message", None)
    on_or_off = contents.get("on_or_off", "off")
    subscribed = "unsubscribed from" if on_or_off == "off" else "re-subscribed to"

    # Get the unit from which to be unsubscribed
    unsubscribe_type = contents.get("unsubscribe_type", None)

    # No longer using the gateway as the identifier
    # gateway = contents.get("gateway", None)
    power_unit_str = contents.get("power_unit_str", None)
    if not power_unit_str:
        # Gateways used to encode "power_unit" (but not anymore)
        # and alerting software encodes "power_unit_str"
        power_unit_str = contents.get("power_unit", None)

    msg_str: str = ""
    if unsubscribe_type == "unit_alerts":
        if power_unit_str is None:
            # We can't unsubscribe from a unit if we don't know which one
            return render_template_error(
                specific_problem="No power unit specified in the JWT token.",
            )

        wants_email = "true" if on_or_off == "on" else "false"
        sql = text(
            """
            update public.alerts
            set wants_email = :wants_email
            where user_id in (
                select id
                from public.users
                where email = :email
                limit 1
            )
                and power_unit_id in (
                    select id
                    from public.power_units
                    where power_unit_str = :power_unit_str
                    limit 1
                )
        """
        ).bindparams(
            wants_email=wants_email,
            email=email,
            power_unit_str=power_unit_str,
        )
        run_sql_query(sql, db_name="ijack", commit=True)

        if message is None:
            sql = text(
                """
                select surface
                from public.vw_structures_joined
                where power_unit_str = :power_unit_str
                    and surface is not null
                    --not the demo customer
                    and customer_id is distinct from 21
                limit 1
            """
            ).bindparams(
                power_unit_str=power_unit_str,
            )
            rows, _ = run_sql_query(sql, db_name="ijack")
            try:
                surface = rows[0]["surface"]
            except Exception:
                surface = None

            if surface:
                message = f"You have successfully been {subscribed} IJACK alert emails for location '{surface}'"
            else:
                message = f"You have successfully been {subscribed} IJACK alert emails for that unit"

        # Create resubscribe token
        if on_or_off == "on":
            # No need for a re-subscribe token if that's what we've just done
            resub_token = None
        else:
            resub_token = create_jwt_encoded_token(
                expires_in=None,
                email=email,
                unsubscribe_type=unsubscribe_type,
                on_or_off="on",
                message=None,
            )
        return render_template_success(message=message, resub_token=resub_token)

    elif unsubscribe_type == "all_units":
        eml_unsubscribe_all = "true" if on_or_off == "off" else "false"
        # sql = text("""
        #     update public.alerts
        #     set wants_email = false
        #     where user_id in (
        #         select id
        #         from public.users
        #         where email = :email
        #         limit 1
        #     )
        # """).bindparams(
        #     email=email
        # )
        sql = text(
            """
            update public.users
            set eml_unsubscribe_all = :eml_unsubscribe_all,
                eml_unsubscribe_all_stopped_at_utc = :eml_unsubscribe_all_stopped_at_utc
            where email = :email
        """
        ).bindparams(
            eml_unsubscribe_all=eml_unsubscribe_all,
            eml_unsubscribe_all_stopped_at_utc=utcnow_naive()
            if on_or_off == "off"
            else None,
            email=email,
        )
        run_sql_query(sql, db_name="ijack", commit=True)
        if message is None:
            message = f"You have successfully been {subscribed} all IJACK alert emails"

        # Create resubscribe token
        if on_or_off == "on":
            # No need for a re-subscribe token if that's what we've just done
            resub_token = None
        else:
            resub_token = create_jwt_encoded_token(
                expires_in=None,
                email=email,
                unsubscribe_type=unsubscribe_type,
                on_or_off="on",
                message=None,
            )
        return render_template_success(message=message, resub_token=resub_token)

    elif unsubscribe_type == "service":
        eml_service = "false" if on_or_off == "off" else "true"
        sql = text(
            """
            update public.users
            set eml_service = :eml_service,
                eml_service_stopped_at_utc = :eml_service_stopped_at_utc
            where email = :email
        """
        ).bindparams(
            eml_service=eml_service,
            eml_service_stopped_at_utc=utcnow_naive() if on_or_off == "off" else None,
            email=email,
        )
        run_sql_query(sql, db_name="ijack", commit=True)
        if message is None:
            message = f"You have successfully been {subscribed} IJACK service emails"

        # Create resubscribe token
        if on_or_off == "on":
            # No need for a re-subscribe token if that's what we've just done
            resub_token = None
        else:
            resub_token = create_jwt_encoded_token(
                expires_in=None,
                email=email,
                unsubscribe_type=unsubscribe_type,
                on_or_off="on",
                message=None,
            )
        return render_template_success(message=message, resub_token=resub_token)

    elif unsubscribe_type in ("xfer_update", "report_email_hourly"):
        if on_or_off == "off":
            sql = text(
                """
                delete from public.report_email_hourly
                where user_id in (
                    select id
                    from public.users
                    where email = :email
                    limit 1
                )
            """
            ).bindparams(
                email=email,
            )
            run_sql_query(sql, db_name="ijack", commit=True)
            if message is None:
                message = (
                    f"You have successfully been {subscribed} IJACK hourly unit reports"
                )
        else:
            message = "Please contact IJACK or your administrator to subscribe to the hourly unit reports"

        resub_token = None
        return render_template_success(message=message, resub_token=resub_token)

    elif unsubscribe_type == "report_email_derates":
        if on_or_off == "off":
            sql = text(
                """
                delete from public.report_email_derates
                where user_id in (
                    select id
                    from public.users
                    where email = :email
                    limit 1
                )
            """
            ).bindparams(
                email=email,
            )
            run_sql_query(sql, db_name="ijack", commit=True)
            if message is None:
                message = (
                    f"You have successfully been {subscribed} IJACK unit derate reports"
                )
        else:
            message = "Please contact IJACK or your administrator to subscribe to the hourly unit reports"

        resub_token = None
        return render_template_success(message=message, resub_token=resub_token)

    elif unsubscribe_type in ("op_hours_100", "op_hours_1000"):
        if on_or_off == "off":
            if unsubscribe_type == "op_hours_100":
                # 100 hours type = 2
                report_email_op_hours_type_id = 2
                msg_str = "100"
            else:
                # 1000 hours type = 1
                report_email_op_hours_type_id = 1
                msg_str = "1000"

            sql = text(
                """
                delete from public.report_email_op_hours_types_rel
                where
                    report_email_op_hours_type_id = :report_email_op_hours_type_id
                    and report_id in (
                        select id
                        from public.report_email_op_hours
                        where user_id in (
                            select id
                            from public.users
                            where email = :email
                            limit 1
                        )
                    )
            """
            ).bindparams(
                report_email_op_hours_type_id=report_email_op_hours_type_id,
                email=email,
            )
            run_sql_query(sql, db_name="ijack", commit=True)
            if message is None:
                message = f"You have successfully been {subscribed} IJACK {msg_str}-hours unit reports"

        else:
            message = f"Please contact IJACK or your administrator to subscribe to the {msg_str}-hours unit reports"

        resub_token = None
        return render_template_success(message=message, resub_token=resub_token)

    elif unsubscribe_type == "report_inventory":
        if on_or_off == "off":
            sql = text(
                """
                delete from public.report_email_inventory
                where user_id in (
                    select id
                    from public.users
                    where email = :email
                    limit 1
                )
            """
            ).bindparams(
                email=email,
            )
            run_sql_query(sql, db_name="ijack", commit=True)
            if message is None:
                message = (
                    f"You have successfully been {subscribed} IJACK inventory reports"
                )
        else:
            message = "Please contact IJACK or your administrator to subscribe to the inventory reports"

        resub_token = None
        return render_template_success(message=message, resub_token=resub_token)

    return render_template_error(
        specific_problem="Unrecognized unsubscribe type",
    )


@auth.route("/docs/", methods=["GET"])
@login_required
def docs():
    """Access to customer documentation for setup and manuals (must be a registered user)"""
    record_visit(user_id=getattr(current_user, "id", None))
    return render_template(
        "auth/docs.html",
        title="Documentation and Setup Instructions",
        description="IJACK documentation and setup instructions for each type of IJACK pump.",
        noindex=True,
        carousel_items=get_carousel_items(),
    )


@auth.route("/s3-secure/<path:filename>")
@login_required
def s3_login_needed(filename) -> Response:
    """
    For sending static files from AWS S3 bucket,
    like images and PDFs, without increasing the Docker image size
    """
    # record_visit(user_id=getattr(current_user, "id", None))
    try:
        presigned_url: str = generate_presigned_url_from_aws_s3(filename)
    except Exception:
        current_app.logger.exception(f"Error fetching file '{filename}' from S3")
        return Response(f"Error fetching file '{filename}' from S3", status=500)

    # Download the file from the pre-signed URL
    response = requests.get(presigned_url)
    if response.status_code != 200:
        current_app.logger.error(
            f"Error fetching file '{filename}' from S3: {response.status_code} {response.text}"
        )
        return Response(f"Error fetching file '{filename}' from S3", status=500)

    # Serve the file to the user
    file_stream = BytesIO(response.content)
    return send_file(file_stream, download_name=filename)


@auth.route("/rcom/download_csv_chart_main")
@login_required
def dash_download_csv_chart_main():
    """Download a CSV file of the main chart data in the Dash application"""
    user_id: int = getattr(current_user, "id", None)
    record_visit(user_id=user_id)

    # Ensure the user can only access his/her company's own units
    # This function is availble to any logged-in users, and they
    # can modify the query string if they know the unit they want
    current_app.logger.info("Querying public.time_series_mv for all records...")
    power_unit_str = request.args.get("pu")

    customer_ids: tuple = get_user_cust_ids(user_id=user_id)
    sql = text(
        """
        select power_unit_str
        from public.vw_structures_joined
        where customer_id in :customer_ids
            and power_unit_str = :power_unit_str
    """
    ).bindparams(customer_ids=customer_ids, power_unit_str=power_unit_str)

    rows, _ = run_sql_query(sql, db_name="ijack")

    if len(rows) < 1 and not user_is_ijack_employee(
        user_id=user_id, user_cust_ids=customer_ids
    ):
        # If the current user's customer_id doesn't match the power_unit,
        # and it's not an IJACK employee...
        abort(403)

    start_str = request.args.get("start")
    end_str = request.args.get("end")
    use_kpa: bool = request.args.get("use_kpa") == "True"
    use_oz_per_inch2_for_suction: bool = (
        request.args.get("use_oz_per_inch2_for_suction") == "True"
    )
    use_cf: bool = request.args.get("use_cf") == "True"
    use_barrels: bool = request.args.get("use_barrels") == "True"
    detail = request.args.get("detail")
    is_vessel_level_mode: bool = request.args.get("is_vessel_level_mode") == "True"
    df = get_time_series_data(
        power_unit_str=power_unit_str,
        start_str=start_str,
        end_str=end_str,
        use_kpa=use_kpa,
        use_cf=use_cf,
        use_barrels=use_barrels,
        detail=detail,
    )

    item_labels_colors = get_labels(
        use_kpa, use_oz_per_inch2_for_suction, use_cf, use_barrels, is_vessel_level_mode
    )

    unit_type_id = int(request.args.get("ut"))
    if unit_type_id in (
        UNIT_TYPE_ID_EGAS,
        UNIT_TYPE_ID_XFER,
        UNIT_TYPE_ID_VRU,
        UNIT_TYPE_ID_BOOST,
    ):
        renames = {
            value: item_labels_colors.get(key, {}).get("label", key)
            for key, value in names_xfer_egas.items()
        }
    else:
        renames = {
            value: item_labels_colors.get(key, {}).get("label", key)
            for key, value in names_uno_unogas.items()
        }

    # Convert from UTC to the customer's local time
    tz_wanted = request.args.get("tzw", None)
    if tz_wanted:
        tz_wanted = pytz.timezone(tz_wanted)
        df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])
        df["timestamp_utc"] = (
            df["timestamp_utc"].dt.tz_localize(pytz.utc).dt.tz_convert(tz_wanted)
        )
        renames["timestamp_utc"] = f"Timestamp {tz_wanted.zone}"
    else:
        renames["timestamp_utc"] = "Timestamp UTC"

    # Keep only the columns we want, and rename them
    columns = [col for col in df.columns if col in renames.keys()]
    # Convert timestamp to string so it doesn't show -7:00 on the end, for example
    df["timestamp_utc"] = df["timestamp_utc"].dt.strftime("%Y-%m-%d %H:%M:%S")
    df = df[columns]
    df = df.rename(columns=renames)

    if user_is_demo_customer(user_id=user_id):
        filename = "IJACK RCOM Data.csv"
    else:
        location = request.args.get("location")
        df = df.assign(Location=location)
        filename = f"IJACK RCOM Data for {location}.csv"

    csv = df.to_csv(index=False)

    return Response(
        csv,
        mimetype="text/csv",
        headers={"Content-disposition": f"attachment; filename={filename}"},
    )


@auth.route("/rcom/download_csv_chart_rt")
@login_required
def dash_download_csv_chart_rt():
    """Download a CSV file of the real time chart data in the Dash application"""
    user_id: int = getattr(current_user, "id", None)
    record_visit(user_id=user_id)

    # Ensure the user can only access his/her company's own units
    # This function is availble to any logged-in users, and they
    # can modify the query string if they know the unit they want
    current_app.logger.info("Querying public.time_series_rt for all records...")
    power_unit_str = request.args.get("pu")

    customer_ids: tuple = get_user_cust_ids(user_id=user_id)
    sql = text(
        """
        select power_unit_str
        from public.vw_structures_joined
        where customer_id in :customer_ids
            and power_unit_str = :power_unit_str
    """
    ).bindparams(customer_ids=customer_ids, power_unit_str=power_unit_str)

    rows, _ = run_sql_query(sql, db_name="ijack")

    if len(rows) < 1 and not user_is_ijack_employee(
        user_id=user_id, user_cust_ids=customer_ids
    ):
        # If the current user's customer_id doesn't match the power_unit,
        # and it's not an IJACK employee...
        abort(403)

    start_str = request.args.get("start")
    # end_str = request.args.get("end")
    use_kpa: bool = request.args.get("use_kpa") == "True"
    use_oz_per_inch2_for_suction: bool = (
        request.args.get("use_oz_per_inch2_for_suction") == "True"
    )
    use_cf: bool = request.args.get("use_cf") == "True"
    use_barrels: bool = request.args.get("use_barrels") == "True"
    # detail = request.args.get("detail")
    is_vessel_level_mode: bool = request.args.get("is_vessel_level_mode") == "True"

    df = time_series_real_time(
        power_unit_str,
        # gateway,
        use_kpa=use_kpa,
        use_oz_per_inch2_for_suction=use_oz_per_inch2_for_suction,
        use_cf=use_cf,
        use_barrels=use_barrels,
        rt_start_str=start_str,
    )

    item_labels_colors = get_labels(
        use_kpa, use_oz_per_inch2_for_suction, use_cf, use_barrels, is_vessel_level_mode
    )

    unit_type_id = int(request.args.get("ut"))
    if unit_type_id in (
        UNIT_TYPE_ID_EGAS,
        UNIT_TYPE_ID_XFER,
        UNIT_TYPE_ID_VRU,
        UNIT_TYPE_ID_BOOST,
    ):
        renames = {
            value: item_labels_colors.get(key, {}).get("label", key)
            for key, value in names_xfer_egas.items()
        }
    else:
        renames = {
            value: item_labels_colors.get(key, {}).get("label", key)
            for key, value in names_uno_unogas.items()
        }

    # Convert from UTC to the customer's local time
    tz_wanted = request.args.get("tzw", None)
    if tz_wanted:
        tz_wanted = pytz.timezone(tz_wanted)
        df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])
        df["timestamp_utc"] = (
            df["timestamp_utc"].dt.tz_localize(pytz.utc).dt.tz_convert(tz_wanted)
        )
        renames["timestamp_utc"] = f"Timestamp {tz_wanted.zone}"
    else:
        renames["timestamp_utc"] = "Timestamp UTC"

    # Keep only the columns we want, and rename them
    columns = [col for col in df.columns if col in renames.keys()]
    # Convert timestamp to string so it doesn't show -7:00 on the end, for example
    df["timestamp_utc"] = df["timestamp_utc"].dt.strftime("%Y-%m-%d %H:%M:%S:%f")
    df = df[columns]
    df = df.rename(columns=renames)

    if user_is_demo_customer(user_id=user_id):
        filename = "IJACK RCOM Real Time Data.csv"
    else:
        location = request.args.get("location")
        df = df.assign(Location=location)
        filename = f"IJACK RCOM Real Time Data for {location}.csv"

    csv = df.to_csv(index=False)

    return Response(
        csv,
        mimetype="text/csv",
        headers={"Content-disposition": f"attachment; filename={filename}"},
    )


@auth.route("/rcom/download_csv_chart_cards")
@login_required
def dash_download_csv_chart_cards():
    """Download a CSV file of the cards chart data in the Dash application"""
    user_id: int = getattr(current_user, "id", None)
    record_visit(user_id=user_id)

    # Ensure the user can only access his/her company's own units
    # This function is availble to any logged-in users, and they
    # can modify the query string if they know the unit they want
    current_app.logger.info("Querying for card data to download...")
    power_unit_str = request.args.get("pus")
    customer_ids: tuple = get_user_cust_ids(user_id=user_id)
    sql = text(
        """
        select power_unit_str
        from public.vw_structures_joined
        where customer_id in :customer_ids
            and power_unit_str = :power_unit_str
    """
    ).bindparams(customer_ids=customer_ids, power_unit_str=power_unit_str)

    rows, _ = run_sql_query(sql, db_name="ijack")

    if len(rows) < 1 and not user_is_ijack_employee(
        user_id=user_id, user_cust_ids=customer_ids
    ):
        # If the current user's customer_id doesn't match the power_unit,
        # and it's not an IJACK employee...
        abort(403)

    start_date_utc_str = request.args.get("start")
    end_date_utc_str = request.args.get("end")
    stroke_length_default = float(request.args.get("sld"))
    tz_wanted = request.args.get("tzw")
    tz_wanted = pytz.timezone(tz_wanted)
    surface_or_compression = request.args.get("soc")

    df, _, _ = get_card_data_for_charts(
        stroke_length_default,
        tz_wanted,
        start_date_utc_str,
        end_date_utc_str,
        surface_or_compression,
        power_unit_str,
    )

    if df is None or len(df) == 0:
        abort(400, "Data not found")

    renames = {
        "timestamp_local": "Local Time",
        "position": "Position",
        "load": "Load",
        "pos_pct": "Position (%)",
        "inches": "Position (inches)",
        "up_down": "Up or Down",
    }
    df = df[renames.keys()]
    # Convert timestamp to string so it doesn't show -7:00 on the end, for example
    df["timestamp_local"] = pd.to_datetime(df["timestamp_local"])
    df["timestamp_local"] = df["timestamp_local"].dt.strftime("%Y-%m-%d %H:%M:%S")
    df = df.sort_values(["timestamp_local", "up_down", "position"])
    df = df.rename(columns=renames)

    if user_is_demo_customer(user_id=user_id, user_cust_ids=customer_ids):
        filename = "IJACK RCOM Card Data.csv"
    else:
        location = request.args.get("location")
        df = df.assign(Location=location)
        filename = f"IJACK RCOM Card Data for {location}.csv"

    csv = df.to_csv(index=False)

    return Response(
        csv,
        mimetype="text/csv",
        headers={"Content-disposition": f"attachment; filename={filename}"},
    )


# @auth.route("/rcom/download_csv_chart_diag_cards")
# @login_required
# def dash_download_csv_chart_diag_cards():
#     """Download a CSV file of the diagnostic cards chart data in the Dash application"""
#     user_id: int = getattr(current_user, "id", None)
#     record_visit(user_id=user_id)

#     # Ensure the user can only access his/her company's own units
#     # This function is availble to any logged-in users, and they
#     # can modify the query string if they know the unit they want
#     current_app.logger.info("Querying for diagnostic card data to download...")
#     power_unit_str = request.args.get("pus")
#     customer_ids: tuple = get_user_cust_ids(user_id=user_id)
#     sql = text(
#         """
#         select power_unit_str
#         from public.vw_structures_joined
#         where customer_id in :customer_ids
#             and power_unit_str = :power_unit_str
#     """
#     ).bindparams(customer_ids=customer_ids, power_unit_str=power_unit_str)

#     rows, _ = run_sql_query(sql, db_name="ijack")

#     if len(rows) < 1 and not user_is_ijack_employee(user_id=getattr(current_user, "id", None), user_cust_ids=customer_ids):
#         # If the current user's customer_id doesn't match the power_unit,
#         # and it's not an IJACK employee...
#         abort(403)

#     start_date_utc_str = request.args.get("start")
#     end_date_utc_str = request.args.get("end")
#     tz_wanted = request.args.get("tzw")
#     tz_wanted = pytz.timezone(tz_wanted)

#     df, _, _ = get_diag_card_data_for_charts(
#         tz_wanted,
#         start_date_utc_str,
#         end_date_utc_str,
#         power_unit_str,
#     )

#     if df is None or len(df) == 0:
#         abort(400, "Data not found")

#     renames = {
#         "timestamp_local": "Local Time",
#         "metric": "Metric",
#         "input_velocity": "Input Velocity",
#         "value": "Value",
#     }
#     df = df[renames.keys()]
#     # Convert timestamp to string so it doesn't show -7:00 on the end, for example
#     df["timestamp_local"] = pd.to_datetime(df["timestamp_local"])
#     df["timestamp_local"] = df["timestamp_local"].dt.strftime("%Y-%m-%d %H:%M:%S")
#     df = df.sort_values(["timestamp_local", "metric", "input_velocity"])
#     df = df.rename(columns=renames)

#     if user_is_demo_customer(user_id=user_id):
#         filename = "IJACK RCOM Diagnostic Card Data.csv"
#     else:
#         location = request.args.get("location")
#         df = df.assign(Location=location)
#         filename = f"IJACK RCOM Diagnostic Card Data for {location}.csv"

#     csv = df.to_csv(index=False)

#     return Response(
#         csv,
#         mimetype="text/csv",
#         headers={"Content-disposition": f"attachment; filename={filename}"},
#     )


@auth.route("/sim-card-installation-guide/")
@login_required
def sim_card_installation_guide():
    """Render the SIM Card Installation Guide page"""
    record_visit(user_id=getattr(current_user, "id", None))

    return render_template(
        "auth/docs/sim-card-installation-guide.html",
        title="SIM Card Installation Guide",
        version=current_app.config.get("VERSION_MYIJACK"),
        description="This page explains how to install a SIM card in an IJACK gateway computer.",
    )


@auth.route("/gateway-shadow-remove-power-unit/<aws_thing>", methods=["GET"])
@login_required
def gateway_shadow_remove_power_unit(aws_thing: str):
    """
    Remove the power unit info from the AWS IoT gateway 'thing' shadow.
    This way we stop getting alerts saying two gateways want to use the same power unit.
    """
    if not is_admin():
        abort(403)

    # Get the gateway ID from the POST request
    # aws_thing: str = request.form.get("aws_thing")
    if not aws_thing:
        abort(400)

    # Remove the power unit info from the shadow
    shadow_new: dict = {
        "state": {
            "reported": {
                "SERIAL_NUMBER": None,
                "C__POWER_UNIT": None,
                "C__CUSTOMER": None,
                "C__MQTT_TOPIC": None,
                "POWER_UNIT": None,
            }
        }
    }

    # Update the shadow in AWS IoT
    try:
        update_shadow(new_state_dict=shadow_new, aws_thing=aws_thing)
    except Exception:
        msg: str = f"Error removing power unit data for AWS thing '{aws_thing}'"
        current_app.logger.exception(msg)
        return msg, 500

    return f"Power unit data removed for AWS thing '{aws_thing}'", 200


@auth.route("/admin-shortcut/", methods=["GET"])
@login_required
def admin_shortcut():
    """
    For the PWA app to add a shortcut to the admin pages.
    """
    if is_admin():
        # For IJACK global admins
        return redirect("/admin/")
    elif is_admin_cust():
        return redirect("/admin_cust/")

    return redirect("/")


@auth.route("/docker-production-handbook/", methods=["GET"])
@login_required
def docker_production_handbook():
    """
    Something Sean paid for,
    and it's easier to view it here from time to time, for reference
    """
    user_id: int = getattr(current_user, "id", None)
    record_visit(user_id=user_id)

    def _redirect():
        """Finds where to redirect, depending on 'is_admin' status"""
        next_url = request.args.get("next")
        if next_url:
            return redirect(next_url)

        return redirect(url_for("dash.login"))

    if not current_user.is_authenticated or not user_is_ijack_employee(user_id=user_id):
        return _redirect()

    return render_template(
        "auth/docs/docker-production-handbook.html",
        title="Docker Production Handbook from Itamar Turing",
        description="To learn best practices for deploying with Docker containers.",
        meta_image=None,
    )


def download_file_route(
    model_class: Type,
    endpoint_name: str,
    name_field: str = "file_name",
    bytes_field: str = "file_bytes",
) -> Callable:
    """
    A decorator factory that creates download file routes with consistent behavior.

    Args:
        model_class: The SQLAlchemy model class to query (e.g., ApplicationUploadFile)
        endpoint_name: A human-readable name for error messages (e.g. "ApplicationUploadFile")
        name_field: The name of the VARCHAR column in the model (e.g. "file_name" or "name")
        bytes_field: The name of the BYTEA column in the model (e.g. "file_bytes" or "pdf")

    Usage:
        @download_file_route(ApplicationUploadFile, "application-upload-file", "name", "pdf")
        def download_application_file(id, model):
            pass  # The decorator handles everything else
    """
    endpoint_name = endpoint_name.replace(" ", "-").lower()

    def decorator(f):
        @wraps(f)
        @auth.route(f"/download-{endpoint_name}/<int:id>", methods=["GET"])
        @login_required
        def wrapped_route(id):
            # Verify IJACK employee status
            if not user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
                abort(403)

            # Get the model instance
            model = db.session.get(model_class, id)
            if not model:
                current_app.logger.warning(f"{model_class} with ID {id} not found")
                abort(404)

            # Call the wrapped function (if any custom processing is needed)
            f(id=id, model=model)

            file_bytes = getattr(model, bytes_field, None)
            if not file_bytes:
                current_app.logger.error(
                    f"{endpoint_name} with ID {id} has no '{file_bytes}' attribute"
                )
                abort(404)

            # Send the file
            return send_file(
                path_or_file=BytesIO(file_bytes),
                mimetype="application/octet-stream",
                as_attachment=True,
                download_name=getattr(model, name_field, f"{endpoint_name}-{id}"),
            )

        return wrapped_route

    return decorator


@download_file_route(
    model_class=WorkOrderUploadFile,
    endpoint_name="work-order-upload-file",
    name_field="file_name",
    bytes_field="file_bytes",
)
def download_work_order_file(id, model):
    """Download the BYTEA file from the database"""
    pass  # All the common functionality is handled by the decorator


@download_file_route(
    model_class=ApplicationUploadFile,
    endpoint_name="application-upload-file",
    name_field="file_name",
    bytes_field="file_bytes",
)
def download_application_file(id, model):
    """Download the BYTEA file from the database"""
    pass  # All the common functionality is handled by the decorator


@download_file_route(
    model_class=CareerFile,
    endpoint_name="career-file",
    name_field="name",
    bytes_field="pdf",
)
def download_career_file(id, model):
    """Download the BYTEA file from the database"""
    pass  # All the common functionality is handled by the decorator


def cpu_stress(duration: int) -> None:
    """Simulate working the CPU hard for a few seconds."""
    end_time = time.time() + duration
    while time.time() < end_time:
        pass  # Busy-wait loop
    return None


@auth.route("/simulate_hard/", methods=["GET"])
@login_required
def simulate_hard():
    """
    Simulate working the CPU hard for a few seconds.
    """
    # Get the duration from query parameters, default to 1 if not provided
    duration = request.args.get("duration", default=1, type=int)

    # cpu_count = multiprocessing.cpu_count()
    cpu_count = 2
    if not isinstance(duration, int):
        duration = 2
    duration = max(1, min(duration, 10))  # Max 10 seconds, min 1 second
    time_start = time.time()

    # Use the 'concurrent.futures' module instead of 'multiprocessing'
    with concurrent.futures.ProcessPoolExecutor() as executor:
        for counter in range(cpu_count):
            current_app.logger.warning(
                "Starting CPU stress process %s of %s for %s seconds",
                counter + 1,
                cpu_count,
                duration,
            )
            executor.submit(cpu_stress, duration)

    elapsed_time = round(time.time() - time_start, 2)
    current_app.logger.warning(
        "CPU stress test of %s seconds completed on each of %s CPU cores in %s seconds",
        duration,
        cpu_count,
        elapsed_time,
    )

    return make_response(
        f"CPU stress test of {duration} seconds completed on each of {cpu_count} CPU cores in {elapsed_time} seconds",
        200,
    )


@auth.route("/work-order/<int:work_order_id>/pdf")
@login_required
@check_confirmed
def make_work_order_pdf(work_order_id):
    """Generate a PDF for a work order"""
    # Get the work order
    work_order = db.session.get(WorkOrder, work_order_id)

    # Check if work order exists
    if not work_order:
        flash("Work order not found", "error")
        return redirect(url_for("dash.work_order"))

    # Check if user has permission to view this work order
    user_has_permission = False
    user_id: int | None = getattr(current_user, "id", None)

    # IJACK admins can view all work orders
    if is_admin(user_id=user_id):
        user_has_permission = True
    # Creator of the work order can view it
    elif work_order.creator_id == user_id:
        user_has_permission = True
    # IJACK field technicians who worked on it can view it
    elif current_user in work_order.users_rel:
        user_has_permission = True
    # Users in the customer's organization can view it
    elif work_order.customer_id == getattr(current_user, "customer_id", None):
        user_has_permission = True

    if not user_has_permission:
        flash("You don't have permission to access this work order", "error")
        return redirect(url_for("dash.work_order"))

    try:
        # Generate the PDF
        pdf_buffer: BytesIO = generate_work_order_pdf(work_order_id)

        # Determine filename
        doc_type = "quote" if work_order.is_quote else "invoice"
        customer_name = ""
        if work_order.customers_rel:
            customer_name = work_order.customers_rel.customer.replace(" ", "_")

        date_str = ""
        if work_order.date_service:
            date_str = work_order.date_service.strftime("%Y-%m-%d")

        filename = f"IJACK_{doc_type}_{customer_name}_{date_str}_{work_order_id}.pdf"

        # Send the PDF as an attachment
        return send_file(
            pdf_buffer,
            mimetype="application/pdf",
            as_attachment=True,
            download_name=filename,
        )

    except Exception as e:
        current_app.logger.error(f"Error generating PDF: {e}")
        return redirect(url_for("dash.work_order"))
