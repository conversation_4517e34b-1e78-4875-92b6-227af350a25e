import os
from functools import wraps

from flask import Blueprint, jsonify, redirect, request, session, url_for
from oauthlib.oauth2 import WebApplicationClient
from redis import Redis

from app.auth.token_manager import TokenManager

# Create Blueprint for OAuth routes
oauth_bp = Blueprint("oauth", __name__)

# Initialize OAuth 2.0 client
client = WebApplicationClient(os.getenv("GOOGLE_ACTIONS_CLIENT_ID"))

# Initialize Redis connection
redis_client = Redis.from_url(os.getenv("REDIS_URL", "redis://redis:6379/0"))

# Import TokenManager class

# Create token manager instance
token_manager = TokenManager()


def require_oauth_token(f):
    """Decorator to require and validate OAuth token in request"""

    @wraps(f)
    def decorated(*args, **kwargs):
        # Extract token from Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return jsonify({"error": "No valid token provided"}), 401

        # Get the token and validate it
        token = auth_header.split(" ")[1]
        user_id = token_manager.validate_access_token(token)

        if not user_id:
            return jsonify({"error": "Invalid or expired token"}), 401

        # Attach user_id to request
        request.oauth_user_id = user_id
        return f(*args, **kwargs)

    return decorated


@oauth_bp.route("/.well-known/oauth-configuration", methods=["GET"])
def oauth_configuration():
    """Endpoint providing OAuth configuration details for Google Actions"""
    return jsonify(
        {
            "authorization_endpoint": url_for("oauth.authorize", _external=True),
            "token_endpoint": url_for("oauth.token", _external=True),
            "scopes_supported": ["smart_home"],
            "token_endpoint_auth_methods_supported": ["client_secret_post"],
            "response_types_supported": ["code"],
            "grant_types_supported": ["authorization_code", "refresh_token"],
        }
    )


@oauth_bp.route("/authorize", methods=["GET"])
def authorize():
    """Authorization endpoint handling initial OAuth flow"""
    # Validate required parameters
    client_id = request.args.get("client_id")
    redirect_uri = request.args.get("redirect_uri")
    state = request.args.get("state")
    request.args.get("response_type")

    # Validate client ID
    if client_id != os.getenv("GOOGLE_ACTIONS_CLIENT_ID"):
        return jsonify({"error": "Invalid client ID"}), 400

    # Store OAuth state in session
    session["oauth_state"] = state
    session["oauth_redirect_uri"] = redirect_uri

    # Check if user is logged in
    if "user_id" not in session:
        return redirect(url_for("auth.login", next=request.url))

    # Generate authorization code using token manager
    auth_code = token_manager.generate_auth_code(session["user_id"])

    # Redirect back to Google with auth code
    redirect_url = f"{redirect_uri}?code={auth_code}&state={state}"
    return redirect(redirect_url)


@oauth_bp.route("/token", methods=["POST"])
def token():
    """Token endpoint for exchanging codes and refresh tokens"""
    # Get and validate request parameters
    grant_type = request.form.get("grant_type")
    client_id = request.form.get("client_id")
    client_secret = request.form.get("client_secret")

    # Validate client credentials
    if not validate_client_credentials(client_id, client_secret):
        return jsonify({"error": "Invalid client credentials"}), 401

    if grant_type == "authorization_code":
        # Handle authorization code exchange
        code = request.form.get("code")
        user_id = token_manager.validate_auth_code(code)

        if not user_id:
            return jsonify({"error": "Invalid authorization code"}), 400

        # Generate new tokens
        access_token = token_manager.generate_access_token(user_id)
        refresh_token = token_manager.generate_refresh_token(user_id)

        return jsonify(
            {
                "access_token": access_token,
                "token_type": "Bearer",
                "expires_in": TokenManager.ACCESS_TOKEN_EXPIRE,
                "refresh_token": refresh_token,
            }
        )

    elif grant_type == "refresh_token":
        # Handle refresh token exchange
        refresh_token = request.form.get("refresh_token")
        user_id = token_manager.validate_refresh_token(refresh_token)

        if not user_id:
            return jsonify({"error": "Invalid refresh token"}), 400

        # Generate new access token
        access_token = token_manager.generate_access_token(user_id)

        return jsonify(
            {
                "access_token": access_token,
                "token_type": "Bearer",
                "expires_in": TokenManager.ACCESS_TOKEN_EXPIRE,
            }
        )

    return jsonify({"error": "Invalid grant type"}), 400


def validate_client_credentials(client_id, client_secret):
    """Validate Google Actions client credentials"""
    return client_id == os.getenv(
        "GOOGLE_ACTIONS_CLIENT_ID"
    ) and client_secret == os.getenv("GOOGLE_ACTIONS_CLIENT_SECRET")


# Example protected endpoint
@oauth_bp.route("/userinfo", methods=["GET"])
@require_oauth_token
def userinfo():
    """Protected endpoint example requiring valid OAuth token"""
    # Access the validated user_id from the request
    user_id = request.oauth_user_id

    # Return user information (implement based on your user model)
    return jsonify(
        {
            "user_id": user_id,
            "scope": ["smart_home"],
            "expires_in": TokenManager.ACCESS_TOKEN_EXPIRE,
        }
    )


def init_app(app):
    """Initialize OAuth blueprint with Flask app"""
    app.register_blueprint(oauth_bp, url_prefix="/oauth")
