from collections import OrderedDict
from copy import deepcopy

IJACK_GREEN = "#C1D72E"
IJACK_GREY = "#717174"
IJACK_BLACK = "#0C1316"
ijack_colours = [I<PERSON><PERSON><PERSON>_<PERSON>RE<PERSON>, IJAC<PERSON>_BLAC<PERSON>, <PERSON><PERSON><PERSON><PERSON>_<PERSON>RE<PERSON>]

# Light red colours
LIGHT_RED = "#FFB6B6"
LIGHT_PINK = "#FFB6C1"
LIGHT_SALMON = "#FFA07A"
LIGHTER_RED = "#FF9999"
VERY_LIGHT_RED = "#FFCCCB"
LIGHT_CORAL = "#F08080"
PASTEL_RED = "#FF8080"

# Light green colours
PALE_GREEN = "#98FB98"
DARK_SEA_GREEN = "#8FBC8F"
LIGHT_GREEN = "#90EE90"
MINT_GREEN = "#98FF98"
SPRING_GREEN = "#00FF7F"
CHARTREUSE_GREEN = "#7FFF00"
LIME_GREEN = "#32CD32"

BOOTSTRAP_BLUE_500 = "#0275d8"
BOOTSTRAP_BLUE_800 = "#052C65"
BOOTSTRAP_GREEN_500 = "#1A8755"
BOOTSTRAP_GREEN_800 = "#0A3622"
BOOTSTRAP_GRAY_500 = "#ADB5BD"
BOOTSTRAP_GRAY_600 = "#6c757d"
BOOTSTRAP_GRAY_800 = "#343a40"
BOOTSTRAP_TEAL_500 = "#20C997"
BOOTSTRAP_TEAL_800 = "#0D503C"
BOOTSTRAP_CYAN_500 = "#0DCAF0"
# BOOTSTRAP_CYAN_600 = "#0AA2C0"
BOOTSTRAP_CYAN_700 = "#087990"
BOOTSTRAP_CYAN_800 = "#055160"
BOOTSTRAP_ORANGE_500 = "#FD7E14"
BOOTSTRAP_ORANGE_800 = "#653207"
BOOTSTRAP_YELLOW_500 = "#FFC109"
BOOTSTRAP_YELLOW_600 = "#CC9A06"
BOOTSTRAP_YELLOW_700 = "#664D04"
BOOTSTRAP_YELLOW_800 = "#664D04"  # this yellow is quite dark
BOOTSTRAP_RED_500 = "#DC3545"
BOOTSTRAP_RED_700 = "#842029"
BOOTSTRAP_RED_800 = "#58161C"
BOOTSTRAP_PINK_500 = "#D63484"
BOOTSTRAP_PINK_800 = "#561535"
BOOTSTRAP_DARK_500 = "#3e4041"
BOOTSTRAP_DARK_800 = "#292b2c"
DEEP_TAUPE_500 = "#795C5F"
BOOTSTRAP_PURPLE_500 = "#6F42C1"
BOOTSTRAP_PURPLE_800 = "#2C1A4D"
BOOTSTRAP_INDIGO_500 = "#6610F2"
BOOTSTRAP_INDIGO_800 = "#290661"
# black_coffee = '#453643'
# bootstrap_cream = '#f7f7f7'

bootstrap_colours = [
    BOOTSTRAP_BLUE_500,
    BOOTSTRAP_RED_500,
    BOOTSTRAP_GREEN_500,
    BOOTSTRAP_ORANGE_500,
    BOOTSTRAP_DARK_500,
    BOOTSTRAP_CYAN_500,
    DEEP_TAUPE_500,
    BOOTSTRAP_BLUE_800,
    BOOTSTRAP_RED_800,
    BOOTSTRAP_GREEN_800,
    BOOTSTRAP_ORANGE_800,
    BOOTSTRAP_CYAN_800,
]

# Labels for the categories_dict_xfer_egas keys
categories_labels_dict = dict(
    important_xfer_egas_only="Important",
    diagnostic="Diagnostic",
    pressure="Pressure",
    performance="Performance",
    derates="Derates",
    production="Production",
    production_egas="Production",
    production_xfer="Production",
    load="Rod Load",
    volume="Card Area",
    fillage="Pump Fillage",
    speed="Speed",
    effort="Effort",
    indicators="Indicators",
)

# Metrics to be graphed by graph high-level category.
# The order of this dictionary matters
categories_dict_xfer_egas = dict(
    important_xfer_egas_only=(
        "spm",
        "cgp",
        "dgp",
        "hp_lowering_avg",
        "hp_raising_avg",
        # "hp_limit",
        "agf_dis_temp",
        "gvf",
        "agft",
        "suction_tmp",
    ),
    # IJACK-only diagnostic metrics
    diagnostic=(
        # Dan doesn't want to see this one since it's problematic, but Ging does!
        "end_stop_avg_pveh",
        "end_stop_counts",
        "end_tap_avg_time",
        "end_tap_counts",
        "end_stop_time",
        "der_ok_counts",
        "lag_top_ms",
        "lag_btm_ms",
        "avg_hyd_press",
        "peak_top_hyd_press",
        "peak_btm_hyd_press",
        "signal",
        "swv_python",
        "cal_status",
        "cal_high_speed_mem",
        "pump_p_hys",
        "pump_s_hys",
        "pump_p_cal_high",
        "pump_p_cal_mid",
        "pump_s_cal_high",
        "pump_s_cal_mid",
        "p2p_up_err_high",
        "p2p_up_err_mid",
        "p2p_down_err_high",
        "p2p_down_err_mid",
        "re_flag_cnt",
        "re_stop_cnt",
        "re_tap_cnt",
        "le_flag_cnt",
        "le_stop_cnt",
        "le_tap_cnt",
        "prox_timeout",
        "buffer_err",
        "pveh_status",
    ),
    pressure=(
        "cgp",
        "dgp",
        "dtp",
        "dtp_max",
        "agft",
        "mgp",
        "ngp",
        "msp",
        "suction_vru",
        # "hyd_press_s",
        # "hyd_press_p",
        "grease_max",
        "grease_min",
        "discharge_opt_psi_d",
    ),
    performance=(
        "agf_dis_temp",
        "agf_dis_temp_max",
        "fl_tmp",
        "suction_tmp",
        "max_fl_tmp_b4_dr",
        "gvf",
        "hyd_oil_lvl",
        "hyd_filt_life",
        "hyd_oil_life",
    ),
    derates=(
        "der_dtp_vpd",
        "der_hp_vpd",
        "der_suc_vpd",
        "der_dis_vpd",
        "der_dis_temp_vpd",
        "lag_time_derate_pct",
        "fl_tmp_derate_pct",
    ),
    production_egas=("e3m3_d",),
    production_xfer=(
        "fluid_rate_vpd",
        "e3m3_d",
    ),
    speed=(
        "spm",
        "agfm",
        "agfn",
        "stroke_speed_avg",
        "stroke_up_time",
        "stroke_down_time",
    ),
    effort=(
        # "hp",
        "hp_limit",
        "ht",
        "hp_lowering_avg",
        "hp_raising_avg",
        "kwh_lasthour",
        "kwh_last30days",
    ),
    indicators=(
        "hyd",
        "warn1",
        "warn2",
        "mtr",
        "clr",
        "htr",
        "aux_egas",
        "aux",
        "prs",
        "sbf",
    ),
)


# Metrics to be graphed by graph high-level category, for SLAVE units.
# IMPORTANT: This dict has the same entry names as the main categories_dict_xfer_egas,
# so they can be combined for a dual unit (whether series or parallel).
categories_dict_xfer_egas_sl = dict(
    important_xfer_egas_only=(
        "spm_sl",
        "cgp_sl",
        "dgp_sl",
        "hp_lowering_avg_sl",
        "hp_raising_avg_sl",
        # "hp_limit_sl",
    ),
    pressure=(
        "cgp_sl",
        "dgp_sl",
        "mgp_sl",
        "agft_sl",
    ),
    speed=(
        "spm_sl",
        "agfm_sl",
        "agfn_sl",
        "stroke_speed_avg_sl",
        "stroke_up_time_sl",
        "stroke_down_time_sl",
    ),
    effort=(
        # "hp_sl",
        "hp_lowering_avg_sl",
        "hp_raising_avg_sl",
        "hp_limit_sl",
    ),
)


# Metrics to be graphed by graph high-level category, for UNO types.
# The order of this dictionary matters
categories_dict_uno = dict(
    # IJACK-only diagnostic metrics
    diagnostic=(
        # Dan doesn't want to see this one since it's problematic, but Ging does!
        "end_stop_avg_pveh",
        "end_stop_counts",
        "end_tap_avg_time",
        "end_tap_counts",
        "end_stop_time",
        "der_ok_counts",
        "lag_top_ms",
        "lag_btm_ms",
        "signal",
        "swv_python",
    ),
    production=("m3pd",),
    load=(
        "mprl_max",
        "mprl_avg",
        "mprl_min",
        "pprl_max",
        "pprl_avg",
        "pprl_min",
    ),
    volume=(
        "area_max",
        "area_avg",
        "area_min",
    ),
    fillage=(
        "pf_max",
        "pf_avg",
        "pf_min",
    ),
    speed=(
        "spm",
        "stroke_speed_avg",
        "stroke_up_time",
        "stroke_down_time",
    ),
    effort=("hp", "hp_limit", "ht", "cgp_uno"),
    indicators=(
        "hyd",
        "warn1",
        "warn2",
        "mtr",
        "clr",
        "htr",
        "aux",
        "prs",
        "sbf",
    ),
)


# Metrics to be graphed by graph high-level category, for SLAVE UNO-types.
# IMPORTANT: This dict has the same entry names as the main categories_dict_xfer_egas,
# so they can be combined for a dual unit (whether series or parallel).
categories_dict_uno_sl = dict(
    speed=(
        "spm_sl",
        "stroke_speed_avg_sl",
        "stroke_up_time_sl",
        "stroke_down_time_sl",
    ),
)

categories_dict_xfer_egas_rt = dict(
    important_xfer_egas_only=(
        "cgp",
        "dgp",
        # Dan doesn't want to see this one since it's problematic (perhaps temporarily 2024-11-28)
        # "dgp_rt_live",
        "spm",
        "hp",
        # New real time metrics March 20, 2024
        "fl_tmp",
        "suction_tmp",
        "lag_top_ms",
        "lag_btm_ms",
        "hp_raising_avg",
        "hp_lowering_avg",
        "agf_dis_temp",
        # "hyd_press_s",
        # "hyd_press_p",
        "gas_prod_rt",
    ),
    pressure=(
        "cgp",
        "dgp",
        "dgp_551",
        # Dan doesn't want to see this one since it's problematic (perhaps temporarily 2024-11-28)
        # "dgp_rt_live",
        # "hyd_press_s",
        # "hyd_press_p",
        "grease",
        "discharge_opt_psi_d",
    ),
    speed=("spm",),
    effort=(
        "hp",
        "spm",
        "hp_limit",
        "hp_raising_avg",
        "hp_lowering_avg",
    ),
)


# Metrics to be graphed by graph high-level category, for real-time SLAVE units.
# IMPORTANT: same key names as the main categories_dict_xfer_egas_rt above,
# so they can be combined for dual units
categories_dict_xfer_egas_rt_sl = dict(
    important_xfer_egas_only=(
        "spm_sl",
        "cgp_sl",
        "dgp_sl",
        "hp_sl",
    ),
    pressure=(
        "cgp_sl",
        "dgp_sl",
    ),
    speed=("spm_sl",),
    effort=(
        "spm_sl",
        "hp_sl",
    ),
)

# Metrics to be graphed by graph high-level category, for UNO-type slave units.
categories_dict_uno_rt = dict(
    speed=("spm",),
    effort=(
        "hp",
        "hp_limit",
    ),
)

# IMPORTANT: same key names as the main categories_dict_xfer_egas_rt above,
# so they can be combined for dual units
categories_dict_uno_rt_sl = dict(
    speed=("spm_sl",),
    effort=("hp_sl",),
)


# Default items selected, by high-level category, for XFER/EGAS types
category_default_items_dict_xfer_egas = dict(
    important_xfer_egas_only=(
        "spm",
        "cgp",
        "dgp",
        "hp_lowering_avg",
        "hp_raising_avg",
        "agf_dis_temp",
        "gvf",
        "agft",
        "suction_tmp",
        "gas_prod_rt",
    ),
    # IJACK-only diagnostic metrics
    diagnostic=(
        # # Dan doesn't want to see this one since it's problematic, but Ging does!
        # # "end_stop_avg_pveh",
        # "end_stop_counts",
        # "end_tap_avg_time",
        # "end_tap_counts",
        # "end_stop_time",
        # "der_ok_counts",
        # "lag_top_ms",
        # "lag_btm_ms",
        "avg_hyd_press",
        "peak_top_hyd_press",
        "peak_btm_hyd_press",
        # "swv_python",
        "signal",
        "cal_status",
        # "cal_high_speed_mem",
        # "pump_p_hys",
        # "pump_s_hys",
        # "pump_p_cal_high",
        # "pump_p_cal_mid",
        # "pump_s_cal_high",
        # "pump_s_cal_mid",
        # "p2p_up_err_high",
        # "p2p_up_err_mid",
        # "p2p_down_err_high",
        # "p2p_down_err_mid",
        "re_flag_cnt",
        "re_stop_cnt",
        "re_tap_cnt",
        "le_flag_cnt",
        "le_stop_cnt",
        "le_tap_cnt",
    ),
    pressure=(
        "cgp",
        "dgp",
        "agft",
        "mgp",
        "ngp",
        "msp",
        # Dan doesn't want "suction_vru" to be selected by default
        # "suction_vru"
    ),
    performance=(
        "gvf",
        "agf_dis_temp",
        "agf_dis_temp_max",
        "fl_tmp",
        "suction_tmp",
        "max_fl_tmp_b4_dr",
        "hyd_oil_lvl",
        "hyd_filt_life",
        "hyd_oil_life",
    ),
    derates=(
        "der_dtp_vpd",
        "der_hp_vpd",
        "der_suc_vpd",
        "der_dis_vpd",
        "der_dis_temp_vpd",
        "lag_time_derate_pct",
        "fl_tmp_derate_pct",
    ),
    speed=(
        "spm",
        "agfm",
        "agfn",
        "stroke_speed_avg",
        "stroke_up_time",
        "stroke_down_time",
    ),
    effort=(
        "spm",
        # "hp",
        "hp_limit",
        "ht",
        "hp_lowering_avg",
        "hp_raising_avg",
    ),
    production_egas=("e3m3_d",),
    production_xfer=(
        "fluid_rate_vpd",
        "e3m3_d",
    ),
    indicators=(
        "hyd",
        "warn1",
    ),  # This one is confusing and hard to see if it's too cluttered
)

# Default items selected, by high-level category, for UNO types
category_default_items_dict_uno = dict(
    # IJACK-only diagnostic metrics
    diagnostic=(
        # Dan doesn't want to see this one since it's problematic, but Ging does!
        # "end_stop_avg_pveh",
        "end_stop_counts",
        "end_tap_avg_time",
        "end_tap_counts",
        "end_stop_time",
        "der_ok_counts",
        "lag_top_ms",
        "lag_btm_ms",
        "signal",
        "swv_python",
    ),
    load=(
        "mprl_max",
        "mprl_avg",
        "mprl_min",
        "pprl_max",
        "pprl_avg",
        "pprl_min",
    ),
    volume=(
        "area_max",
        "area_avg",
        "area_min",
    ),
    fillage=("pf_max", "pf_avg", "pf_min"),
    speed=(
        "spm",
        "stroke_speed_avg",
        "stroke_up_time",
        "stroke_down_time",
    ),
    effort=("hp", "hp_limit", "ht", "cgp_uno"),
    production=("m3pd",),
    indicators=(
        "hyd",
        "warn1",
    ),  # This one is confusing and hard to see if it's too cluttered
)

category_default_items_dict_xfer_egas_rt = dict(
    important_xfer_egas_only=(
        "cgp",
        "dgp",
        # Dan doesn't want to see this one since it's problematic (perhaps temporarily 2024-11-28)
        # "dgp_rt_live",
        "spm",
        "hp",
        # New real time metrics March 20, 2024
        "fl_tmp",
        "suction_tmp",
        "lag_top_ms",
        "lag_btm_ms",
        "hp_raising_avg",
        "hp_lowering_avg",
        "agf_dis_temp",
        # "hyd_press_s",
        # "hyd_press_p",
    ),
    pressure=(
        "cgp",
        "dgp",
        # Dan doesn't want to see this one since it's problematic (perhaps temporarily 2024-11-28)
        # "dgp_rt_live",
        # "hyd_press_s",
        # "hyd_press_p",
        "grease",
    ),
    speed=("spm",),
    effort=(
        "hp",
        "spm",
        "hp_limit",
        "hp_raising_avg",
        "hp_lowering_avg",
    ),
)

category_default_items_dict_uno_rt = dict(
    speed=("spm",),
    effort=(
        "hp",
        "hp_limit",
    ),
)

# categories_options = [{'label': key.title(), 'value': key} for key in categories_dict_xfer_egas.keys()]
categories_list_uno_unogas = (
    "production",
    "load",
    "fillage",
    "volume",
    "speed",
    "effort",
    "indicators",
)
categories_list_uno_unogas_rt = (
    "speed",
)  # not available yet, but it's not the right time to throw an error

categories_list_xfer_only = (
    "important_xfer_egas_only",
    "pressure",
    "performance",
    "derates",
    "speed",
    "effort",
    "production_xfer",
    "indicators",
)

# Don't include "slave" in the following since
# we'll add it only for a select few model type IDs
categories_list_xfer_only_rt = (
    "important_xfer_egas_only",
    "pressure",
    "speed",
    "effort",
)

categories_list_egas_only = (
    "important_xfer_egas_only",
    "pressure",
    "performance",
    "derates",
    "speed",
    "effort",
    "production_egas",
    "indicators",
)
categories_list_egas_only_rt = (
    "important_xfer_egas_only",
    "pressure",
    "speed",
    "effort",
)

# Need separate categories for EGAS/UNO because UNO doesn't have 'pressure', for instance
uno_unogas_categories_options = [
    {"label": categories_labels_dict[key], "value": key}
    for key in categories_dict_uno.keys()
    if key in categories_list_uno_unogas
]

uno_unogas_categories_options_rt = [
    {"label": categories_labels_dict[key], "value": key}
    for key in categories_dict_uno_rt.keys()
    if key in categories_list_uno_unogas
]

categories_options_egas_only = [
    {"label": categories_labels_dict[key], "value": key}
    for key in categories_dict_xfer_egas.keys()
    if key in categories_list_egas_only
]

categories_options_egas_only_rt = [
    {"label": categories_labels_dict[key], "value": key}
    for key in categories_dict_xfer_egas_rt.keys()
    if key in categories_list_egas_only_rt
]

categories_options_xfer_only = [
    {"label": categories_labels_dict[key], "value": key}
    for key in categories_dict_xfer_egas.keys()
    if key in categories_list_xfer_only
]

categories_options_xfer_only_rt = [
    {"label": categories_labels_dict[key], "value": key}
    for key in categories_dict_xfer_egas_rt.keys()
    if key in categories_list_xfer_only_rt
]

# For aggregating the real time chart table data.
# Dict legend: (metric in DB: abbreviation/alias for charts)
# e.g. avg(case when metric = 'CGP_RT' then value else null end) as cgp,
# e.g. avg(case when metric = 'SPM_EGAS_RT_SL' then value else null end) as spm_egas_sl,
rt_metrics_for_chart = {
    "CGP_RT": "cgp",
    "DGP_RT": "dgp",  # cob 356
    "DGP": "dgp_551",  # cob 551
    # Dan wants me to remove this one since it's problematic (perhaps temporarily 2024-11-28)
    # "DGP_RT_LIVE": "dgp_rt_live",
    "DISCHARGE_OPT_PSI_D": "discharge_opt_psi_d",
    "HPE_RT": "hpe",
    "HP_LIMIT": "hp_limit",
    "SPM_EGAS_RT": "spm_egas",
    "CGP_RT_SL": "cgp_sl",
    "DGP_RT_SL": "dgp_sl",
    "HPE_RT_SL": "hpe_sl",
    "SPM_EGAS_RT_SL": "spm_egas_sl",
    "FL_TMP": "fl_tmp",
    "SUCTION_TMP": "suction_tmp",
    "LAG_TOP_MS": "lag_top_ms",
    "LAG_BTM_MS": "lag_btm_ms",
    "HP_RAISING_AVG": "hp_raising_avg",
    "HP_LOWERING_AVG": "hp_lowering_avg",
    "AGF_DIS_TEMP": "agf_dis_temp",
    "HYD_PRESS_S": "hyd_press_s",
    "HYD_PRESS_P": "hyd_press_p",
    "GREASE": "grease",
    "GAS_PROD_RT": "gas_prod_rt",
}

# Metric to imperial conversions
KPA_PER_PSI = 6.89476
OZ_PER_LB = 16
PSI_COLS = [
    "cgp",
    "dgp",
    "dgp_551",
    "dgp_rt_live",
    # "hyd_press_s",
    # "hyd_press_p",
    "dtp",
    "dtp_max",
    "cgp_sl",
    "dgp_sl",
    "cgp_uno",
    "agft",
    "agft_sl",
    "mgp",
    "mgp_sl",
    "ngp",
    "msp",
    "suction_vru",
    "grease_max",
    "grease_min",
    "grease",
    "discharge_opt_psi_d",
]
OZ_PER_LB_COLS = [
    "cgp",
    "cgp_sl",
    "agft",
    "agft_sl",
    "ngp",
    "suction_vru",
]


# CF_PER_M3 = 35.3147
CF_PER_M3 = 35.32272
CF_COLS = [
    "e3m3_d",
    "m3pd",
    "gas_prod_rt",
]

BARRELS_PER_M3 = 6.289811
BARRELS_COLS = ["fluid_rate_vpd"]


def celsius_to_fahrenheit(celsius: float) -> float:
    """Convert"""
    return (celsius * 9 / 5) + 32


CELSIUS_COLS = [
    "ht",
    "agf_dis_temp",
    "agf_dis_temp_max",
    "fl_tmp",
    "suction_tmp",
    "max_fl_tmp_b4_dr",
]

# Colours for the map, which is grouped by unit type
unit_type_colors = {
    "XFER": BOOTSTRAP_RED_500,
    "EGAS": BOOTSTRAP_BLUE_500,
    "DGAS": BOOTSTRAP_PURPLE_500,
    "Gateway": BOOTSTRAP_GRAY_500,
    "SHOP": BOOTSTRAP_ORANGE_500,
    "TEST": BOOTSTRAP_CYAN_500,
    "UNO": BOOTSTRAP_GREEN_500,
    "UNOGAS": BOOTSTRAP_DARK_500,
}

# The order of this dictionary doesn't matter
item_labels_colors = dict(
    cgp=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Suction pressure (PSI)",
    ),
    cgp_sl=dict(
        color=BOOTSTRAP_BLUE_800,
        label="Suction pressure (PSI) - secondary unit",
    ),
    dgp=dict(color=BOOTSTRAP_RED_500, label="Discharge pressure (PSI)"),
    dgp_551=dict(color=BOOTSTRAP_ORANGE_500, label="Discharge pressure slow (PSI)"),
    dgp_rt_live=dict(
        color=BOOTSTRAP_RED_800,
        label="Discharge pressure live (PSI)",
    ),
    dgp_sl=dict(
        color=BOOTSTRAP_RED_700,
        label="Discharge pressure (PSI) - secondary unit",
    ),
    discharge_opt_psi_d=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Discharge pressure transducer (PSI)",
    ),
    grease_max=dict(color=BOOTSTRAP_PURPLE_500, label="Max grease pressure (PSI)"),
    grease_min=dict(color=BOOTSTRAP_PINK_500, label="Min grease pressure (PSI)"),
    grease=dict(color=BOOTSTRAP_CYAN_500, label="Grease pressure (PSI)"),
    spm=dict(
        color=BOOTSTRAP_CYAN_500,
        label="Strokes per minute",
    ),
    spm_sl=dict(
        color=BOOTSTRAP_CYAN_700,
        label="Strokes per Minute - secondary unit",
    ),
    # These are just used for the real-time charts, not the normal charts,
    # which have raising and lowering HP instead
    hp=dict(
        color=BOOTSTRAP_YELLOW_500,
        label="Horsepower",
    ),
    hp_sl=dict(color=BOOTSTRAP_YELLOW_700, label="Horsepower - secondary unit"),
    # Other metrics
    cgp_uno=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Casing gas pressure (PSI)",
    ),
    dtp=dict(
        color=DEEP_TAUPE_500,
        label="Delta P (PSI)",
    ),
    dtp_max=dict(
        color=IJACK_BLACK,
        label="Max delta P before derates (PSI)",
    ),
    dtp_sl=dict(
        color=DEEP_TAUPE_500,
        label="Delta P - secondary unit (PSI)",
    ),
    agft=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Suction target (PSI)",
    ),
    agft_sl=dict(
        color=BOOTSTRAP_GREEN_800,
        label="Suction target - secondary unit (PSI)",
    ),
    mgp=dict(
        color=BOOTSTRAP_ORANGE_500,
        label="Derate discharge setpoint (PSI)",
    ),
    mgp_sl=dict(
        color=BOOTSTRAP_ORANGE_800,
        label="Derate discharge setpoint - secondary unit (PSI)",
    ),
    ngp=dict(
        color=BOOTSTRAP_DARK_500,
        label="Hard stop suction setpoint (PSI)",
    ),
    msp=dict(
        color=BOOTSTRAP_CYAN_500,
        label="Max shutdown pressure (PSI)",
    ),
    agfm=dict(
        color=BOOTSTRAP_RED_500,
        label="Max speed setpoint (%)",
    ),
    agfm_sl=dict(
        color=BOOTSTRAP_RED_800,
        label="Max speed setpoint - secondary unit (%)",
    ),
    agfn=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Min speed setpoint (%)",
    ),
    agfn_sl=dict(
        color=BOOTSTRAP_GREEN_800,
        label="Min speed setpoint - secondary unit (%)",
    ),
    hpt=dict(color=BOOTSTRAP_BLUE_500, label="Horsepower total UNO + EGAS"),
    hp_limit=dict(
        color=BOOTSTRAP_RED_500,
        label="Horsepower limit",
    ),
    hp_limit_sl=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Horsepower limit - secondary unit",
    ),
    ht=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Hydraulic temp (C)",
    ),
    e3m3_d=dict(
        color=BOOTSTRAP_RED_500,
        label="Gas last 24 hours (E3M3/day)",
    ),
    m3pd=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Flow rate (M3/day)",
    ),
    fluid_rate_vpd=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Fluid last 24 hours (M3/day)",
    ),
    ion=dict(color=BOOTSTRAP_BLUE_500, label="IJACK operating normally"),
    hyd=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Hydraulics on",
    ),
    warn1=dict(color=BOOTSTRAP_RED_500, label="Warning 1, motor shutdown"),
    warn2=dict(
        color=BOOTSTRAP_ORANGE_500,
        label="Warning 2, hydraulics shutdown",
    ),
    mtr=dict(color=BOOTSTRAP_CYAN_500, label="Motor on"),
    clr=dict(color=BOOTSTRAP_DARK_500, label="Cooler on"),
    htr=dict(color=DEEP_TAUPE_500, label="Heater on"),
    aux_egas=dict(color=IJACK_BLACK, label="Heat trace on"),
    aux=dict(color=IJACK_GREEN, label="Chemical on"),
    prs=dict(
        color=IJACK_GREY,
        label="Pressure switch",
    ),
    sbf=dict(color=BOOTSTRAP_RED_500, label="Containment float switch"),
    stboxf=dict(color=BOOTSTRAP_RED_500, label="Stuffing box float switch"),
    wait_okay_heartbeat=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Stroking heartbeat",
    ),
    wait_okay_heartbeat_ol=dict(
        color=BOOTSTRAP_RED_500,
        label="Online heartbeat",
    ),
    mprl_max=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Minimum polished rod load max",
    ),
    mprl_avg=dict(
        color=BOOTSTRAP_RED_500,
        label="Minimum polished rod load avg",
    ),
    mprl_min=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Minimum polished rod load min",
    ),
    pprl_max=dict(color=BOOTSTRAP_ORANGE_500, label="Peak polished rod load max"),
    pprl_avg=dict(color=BOOTSTRAP_DARK_500, label="Peak polished rod load avg"),
    pprl_min=dict(color=BOOTSTRAP_CYAN_500, label="Peak polished rod load min"),
    area_max=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Surface card area max",
    ),
    area_avg=dict(
        color=BOOTSTRAP_RED_500,
        label="Surface card area avg",
    ),
    area_min=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Surface card area min",
    ),
    pf_max=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Pump fillage max",
    ),
    pf_avg=dict(
        color=BOOTSTRAP_RED_500,
        label="Pump fillage avg",
    ),
    pf_min=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Pump fillage min",
    ),
    # New metrics Jan 25, 2025
    prox_timeout=dict(color=BOOTSTRAP_RED_500, label="Proximity timeout"),
    buffer_err=dict(color=BOOTSTRAP_ORANGE_500, label="Buffer error"),
    pveh_status=dict(color=BOOTSTRAP_GREEN_500, label="PVEH status"),
    kwh_lasthour=dict(color=BOOTSTRAP_BLUE_500, label="KWH last hour"),
    kwh_last30days=dict(color=BOOTSTRAP_RED_500, label="KWH last 30 days"),
    # New metrics v311
    hp_raising_avg=dict(
        color=BOOTSTRAP_DARK_500,
        label="Horsepower on upstroke",
    ),
    hp_raising_avg_sl=dict(
        color=BOOTSTRAP_DARK_800,
        label="Horsepower on upstroke - secondary unit",
    ),
    hp_lowering_avg=dict(color=BOOTSTRAP_ORANGE_500, label="Horsepower on downstroke"),
    hp_lowering_avg_sl=dict(
        color=BOOTSTRAP_ORANGE_800,
        label="Horsepower on downstroke - secondary unit",
    ),
    der_dtp_vpd=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Differential pressure derate (%)",
    ),
    der_hp_vpd=dict(
        color=BOOTSTRAP_RED_500,
        label="Horsepower derate (%)",
    ),
    der_suc_vpd=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Low suction pressure derate (%)",
    ),
    der_dis_vpd=dict(
        color=BOOTSTRAP_ORANGE_500,
        label="High discharge pressure derate (%)",
    ),
    der_dis_temp_vpd=dict(
        color=BOOTSTRAP_PINK_500,
        label="High discharge temperature derate (%)",
    ),
    gvf=dict(color=BOOTSTRAP_CYAN_500, label="Gas volume fraction (%)"),
    stroke_speed_avg=dict(color=BOOTSTRAP_ORANGE_500, label="Stroke speed average (%)"),
    stroke_speed_avg_sl=dict(
        color=BOOTSTRAP_ORANGE_800,
        label="Stroke speed average - secondary unit (%)",
    ),
    # New metrics v312
    agf_dis_temp=dict(
        color=DEEP_TAUPE_500,
        label="Discharge temperature (C)",
    ),
    agf_dis_temp_max=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Max discharge temperature before derates (C)",
    ),
    gas_prod_rt=dict(
        color=BOOTSTRAP_PURPLE_500,
        label="Gas production real-time (E3M3/day)",
    ),
    # Diagnostic metrics v312
    end_stop_avg_pveh=dict(
        color=BOOTSTRAP_BLUE_500,
        label="End stop avg PVEH",
    ),
    end_stop_counts=dict(
        color=BOOTSTRAP_RED_500,
        label="End stop counts",
    ),
    end_tap_avg_time=dict(color=BOOTSTRAP_GREEN_500, label="End tap average time (ms)"),
    end_tap_counts=dict(
        color=BOOTSTRAP_ORANGE_500,
        label="End tap counts",
    ),
    end_stop_time=dict(
        color=BOOTSTRAP_DARK_500,
        label="End stop time (ms)",
    ),
    der_ok_counts=dict(
        color=DEEP_TAUPE_500,
        label="Derate OK counts",
    ),
    lag_top_ms=dict(
        color=BOOTSTRAP_GRAY_500,
        label="Top lag time (ms)",
    ),
    lag_btm_ms=dict(
        color=BOOTSTRAP_CYAN_500,
        label="Bottom lag time (ms)",
    ),
    avg_hyd_press=dict(
        color=BOOTSTRAP_RED_500,
        label="Average hydraulic pressure (PSI)",
    ),
    peak_top_hyd_press=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Peak top hydraulic pressure (PSI)",
    ),
    peak_btm_hyd_press=dict(
        color=BOOTSTRAP_ORANGE_500,
        label="Peak bottom hydraulic pressure (PSI)",
    ),
    signal=dict(
        color=BOOTSTRAP_PURPLE_500,
        label="Cell Signal (%)",
    ),
    stroke_up_time=dict(
        color=BOOTSTRAP_DARK_500,
        label="Stroke up time (s)",
    ),
    stroke_up_time_sl=dict(
        color=BOOTSTRAP_DARK_800,
        label="Stroke up time - secondary unit (s)",
    ),
    stroke_down_time=dict(
        color=BOOTSTRAP_BLUE_500,
        label="Stroke down time (s)",
    ),
    stroke_down_time_sl=dict(
        color=BOOTSTRAP_BLUE_800,
        label="Stroke down time - secondary unit (s)",
    ),
    hyd_oil_lvl=dict(
        color=BOOTSTRAP_DARK_800,
        label="Hydraulic oil level (%)",
    ),
    hyd_filt_life=dict(
        color=BOOTSTRAP_YELLOW_500,
        label="Hydraulic filter life (%)",
    ),
    hyd_oil_life=dict(
        color=BOOTSTRAP_GRAY_500,
        label="Hydraulic oil life (%)",
    ),
    suction_vru=dict(
        color=BOOTSTRAP_INDIGO_500,
        label="Suction pressure at IJACK (PSI)",
    ),
    max_fl_tmp_b4_dr=dict(
        color=BOOTSTRAP_INDIGO_500,
        label="Max flowline temperature before derates (C)",
    ),
    fl_tmp=dict(
        color=BOOTSTRAP_RED_500,
        label="Flowline temperature (C)",
    ),
    # It's either suction temperature shows up, or flowline temperature
    suction_tmp=dict(
        color=BOOTSTRAP_GREEN_500,
        label="Suction temperature (C)",
    ),
    fl_tmp_derate_pct=dict(
        color=BOOTSTRAP_INDIGO_500,
        label="Flowline temperature derate (%)",
    ),
    lag_time_derate_pct=dict(
        color=BOOTSTRAP_GRAY_600,
        label="Lag time derate (%)",
    ),
    # New real time metrics March 20, 2024
    # hyd_press_s=dict(color=BOOTSTRAP_PURPLE_500, label="Hydraulic pressure S (PSI)"),
    # hyd_press_p=dict(color=BOOTSTRAP_YELLOW_500, label="Hydraulic pressure P (PSI)"),
    swv_python=dict(color=BOOTSTRAP_RED_500, label="Gateway software version"),
    cal_status=dict(color=BOOTSTRAP_GREEN_500, label="CAL Status"),
    cal_high_speed_mem=dict(color=BOOTSTRAP_BLUE_500, label="CAL high speed mem"),
    pump_p_hys=dict(color=BOOTSTRAP_PURPLE_500, label="Pump P hysteresis"),
    pump_s_hys=dict(color=BOOTSTRAP_ORANGE_500, label="Pump S hysteresis"),
    pump_p_cal_high=dict(color=BOOTSTRAP_RED_500, label="Pump P cal high"),
    pump_p_cal_mid=dict(color=BOOTSTRAP_YELLOW_500, label="Pump P cal mid"),
    pump_s_cal_high=dict(color=BOOTSTRAP_INDIGO_500, label="Pump S cal high"),
    pump_s_cal_mid=dict(color=BOOTSTRAP_GRAY_500, label="Pump S cal mid"),
    p2p_up_err_high=dict(color=BOOTSTRAP_RED_500, label="P2P up error high"),
    p2p_up_err_mid=dict(color=BOOTSTRAP_CYAN_500, label="P2P up error mid"),
    p2p_down_err_high=dict(color=BOOTSTRAP_DARK_500, label="P2P down error high"),
    p2p_down_err_mid=dict(color=BOOTSTRAP_GRAY_500, label="P2P down error mid"),
    re_flag_cnt=dict(
        color=BOOTSTRAP_GREEN_800, label="Rolling right-end flag event counter"
    ),
    re_stop_cnt=dict(
        color=BOOTSTRAP_ORANGE_800, label="Rolling right-end stop event counter"
    ),
    re_tap_cnt=dict(
        color=BOOTSTRAP_PINK_800, label="Rolling right-end tap event counter"
    ),
    le_flag_cnt=dict(
        color=BOOTSTRAP_TEAL_800, label="Rolling left-end flag event counter"
    ),
    le_stop_cnt=dict(
        color=BOOTSTRAP_YELLOW_800, label="Rolling left-end stop event counter"
    ),
    le_tap_cnt=dict(
        color=BOOTSTRAP_PURPLE_800, label="Rolling left-end tap event counter"
    ),
)


def get_labels(
    use_kpa: bool = False,
    use_oz_per_inch2_for_suction: bool = False,
    use_cf: bool = False,
    use_barrels: bool = False,
    use_fahrenheit: bool = False,
    is_vessel_level_mode: bool = False,
) -> dict:
    """
    Change the labels if the user prefers kPA instead of PSI, or oz/in^2 instead of PSI,
    or MCF (thousand cubic feet) instead of m^3 (cubic metres)
    """
    global item_labels_colors
    if (
        use_kpa is False
        and use_oz_per_inch2_for_suction is False
        and use_cf is False
        and use_barrels is False
        and use_fahrenheit is False
        and is_vessel_level_mode is False
    ):
        return item_labels_colors

    # Make a deep copy so we don't modify the original dict
    new_labels = deepcopy(item_labels_colors)

    vl_suction_tgt_lbl: str = "Level target %"
    vl_suction_pressure_lbl: str = "Level level %"

    if is_vessel_level_mode:
        new_labels.setdefault("cgp", {})["label"] = vl_suction_pressure_lbl
        new_labels.setdefault("cgp_sl", {})["label"] = (
            f"{vl_suction_pressure_lbl} - secondary unit"
        )
        new_labels.setdefault("agft", {})["label"] = vl_suction_tgt_lbl
        new_labels.setdefault("agft_sl", {})["label"] = (
            f"{vl_suction_tgt_lbl} - secondary unit"
        )

    if use_kpa:
        if is_vessel_level_mode:
            new_labels.setdefault("cgp", {})["label"] = vl_suction_pressure_lbl
            new_labels.setdefault("cgp_sl", {})["label"] = (
                f"{vl_suction_pressure_lbl} - secondary unit"
            )
            new_labels.setdefault("agft", {})["label"] = vl_suction_tgt_lbl
            new_labels.setdefault("agft_sl", {})["label"] = (
                f"{vl_suction_tgt_lbl} - secondary unit"
            )
        else:
            new_labels.setdefault("cgp", {})["label"] = "Suction pressure (kPa)"
            new_labels.setdefault("cgp_sl", {})["label"] = (
                "Suction pressure (kPa) - secondary unit"
            )
            new_labels.setdefault("agft", {})["label"] = "Suction target (kPa)"
            new_labels.setdefault("agft_sl", {})["label"] = (
                "Suction target - secondary unit (kPa)"
            )

        new_labels.setdefault("dgp", {})["label"] = "Discharge pressure (kPa)"
        new_labels.setdefault("dgp_551", {})["label"] = "Discharge pressure slow (kPa)"
        new_labels.setdefault("dgp_rt_live", {})["label"] = (
            "Discharge pressure live (kPa)"
        )
        new_labels.setdefault("discharge_opt_psi_d", {})["label"] = (
            "Discharge pressure transducer (kPa)"
        )
        new_labels.setdefault("grease_max", {})["label"] = (
            "Max grease pressure (kPa)",
        )
        new_labels.setdefault("grease_min", {})["label"] = (
            "Min grease pressure (kPa)",
        )
        new_labels.setdefault("grease", {})["label"] = ("Grease pressure (kPa)",)
        # new_labels.setdefault("hyd_press_s", {})["label"] = "Hydraulic pressure S (kPa)"
        # new_labels.setdefault("hyd_press_p", {})["label"] = "Hydraulic pressure P (kPa)"
        new_labels.setdefault("dgp_sl", {})["label"] = (
            "Discharge pressure (kPa) - secondary unit"
        )
        new_labels.setdefault("dtp", {})["label"] = "Delta P (kPa)"
        new_labels.setdefault("dtp_max", {})["label"] = (
            "Max delta P before derates (kPa)"
        )
        new_labels.setdefault("dtp_sl", {})["label"] = "Delta P - secondary unit (kPa)"
        new_labels.setdefault("mgp", {})["label"] = "Derate discharge setpoint (kPa)"
        new_labels.setdefault("mgp_sl", {})["label"] = (
            "Derate discharge setpoint - secondary unit (kPa)"
        )
        new_labels.setdefault("ngp", {})["label"] = "Hard stop suction setpoint (kPa)"
        new_labels.setdefault("msp", {})["label"] = "Max shutdown pressure (kPa)"
        new_labels.setdefault("cgp_uno", {})["label"] = "Casing gas pressure (kPa)"
        new_labels.setdefault("suction_vru", {})["label"] = (
            "Suction pressure at IJACK (kPa)"
        )

    if use_oz_per_inch2_for_suction:
        if is_vessel_level_mode:
            new_labels.setdefault("cgp", {})["label"] = vl_suction_pressure_lbl
            new_labels.setdefault("cgp_sl", {})["label"] = (
                f"{vl_suction_pressure_lbl} - secondary unit"
            )
            new_labels.setdefault("agft", {})["label"] = vl_suction_tgt_lbl
            new_labels.setdefault("agft_sl", {})["label"] = (
                f"{vl_suction_tgt_lbl} - secondary unit"
            )
        else:
            new_labels.setdefault("cgp", {})["label"] = "Suction pressure (oz/in²)"
            new_labels.setdefault("cgp_sl", {})["label"] = (
                "Suction pressure (oz/in²) - secondary unit"
            )
            new_labels.setdefault("agft", {})["label"] = "Suction target (oz/in²)"
            new_labels.setdefault("agft_sl", {})["label"] = (
                "Suction target - secondary unit (oz/in²)"
            )

        new_labels.setdefault("ngp", {})["label"] = (
            "Hard stop suction setpoint (oz/in²)"
        )
        new_labels.setdefault("suction_vru", {})["label"] = (
            "Suction pressure at IJACK (oz/in²)"
        )

    if use_cf:
        new_labels.setdefault("e3m3_d", {})["label"] = "Gas last 24 hours (MCF/day)"
        new_labels.setdefault("m3pd", {})["label"] = "Flow rate (CF/day)"
        new_labels.setdefault("gas_prod_rt", {})["label"] = (
            "Gas production real-time (MCF/day)"
        )

    if use_barrels:
        new_labels.setdefault("fluid_rate_vpd", {})["label"] = (
            "Fluid last 24 hours (BBL/day)"
        )

    # Update temperature labels to show Fahrenheit instead of Celsius
    if use_fahrenheit:
        new_labels.setdefault("ht", {})["label"] = "Hydraulic temp (F)"
        new_labels.setdefault("agf_dis_temp", {})["label"] = "Discharge temperature (F)"
        new_labels.setdefault("agf_dis_temp_max", {})["label"] = (
            "Max discharge temperature before derates (F)"
        )
        new_labels.setdefault("fl_tmp", {})["label"] = "Flowline temperature (F)"
        new_labels.setdefault("suction_tmp", {})["label"] = "Suction temperature (F)"
        new_labels.setdefault("max_fl_tmp_b4_dr", {})["label"] = (
            "Max flowline temperature before derates (F)"
        )

    return new_labels


# The order of this dict MATTERS
names_uno_unogas = OrderedDict(
    spm="spm",
    hp="hpu",
    hpt="hpt",
    hp_limit="hp_limit",
    ht="ht",
    cgp_uno="cgp_uno",
    m3pd="m3pd",
    ion="ion",
    hyd="hyd",
    warn1="warn1",
    warn2="warn2",
    mtr="mtr",
    clr="clr",
    htr="htr",
    aux="aux",
    prs="prs",
    sbf="sbf",
    stboxf="stboxf",
    mprl_max="mprl_max",
    mprl_avg="mprl_avg",
    mprl_min="mprl_min",
    pprl_max="pprl_max",
    pprl_avg="pprl_avg",
    pprl_min="pprl_min",
    area_max="area_max",
    area_avg="area_avg",
    area_min="area_min",
    pf_max="pf_max",
    pf_avg="pf_avg",
    pf_min="pf_min",
    stroke_speed_avg="stroke_speed_avg",
    stroke_up_time="stroke_up_time",
    stroke_down_time="stroke_down_time",
    end_stop_avg_pveh="end_stop_avg_pveh",
    end_stop_counts="end_stop_counts",
    end_tap_avg_time="end_tap_avg_time",
    end_tap_counts="end_tap_counts",
    end_stop_time="end_stop_time",
    der_ok_counts="der_ok_counts",
    lag_top_ms="lag_top_ms",
    lag_btm_ms="lag_btm_ms",
    signal="signal",
)

# uno_unogas_options = [
#     {"label": item_labels_colors[key]["label"], "value": key} for key, value in names_uno_unogas.items()
# ]
defaults_uno_unogas = ("spm",)

# The order of this dict MATTERS
names_xfer_egas = OrderedDict(
    cgp="cgp",
    cgp_sl="cgp_sl",
    dgp="dgp",
    dgp_rt_live="dgp_rt_live",
    dgp_551="dgp_551",
    dgp_sl="dgp_sl",
    discharge_opt_psi_d="discharge_opt_psi_d",
    grease_max="grease_max",
    grease_min="grease_min",
    grease="grease",
    # These two horsepower metrics are only used for the real-time charts,
    # since it's an average of the two strokes, and not precise enough
    hp="hpe",
    hp_sl="hpe_sl",
    spm="spm_egas",
    spm_sl="spm_egas_sl",
    # Other metrics
    dtp="dtp",
    dtp_max="dtp_max",
    agft="agft",
    agft_sl="agft_sl",
    mgp="mgp",
    mgp_sl="mgp_sl",
    ngp="ngp",
    msp="msp",
    agfm="agfm",
    agfm_sl="agfm_sl",
    agfn="agfn",
    agfn_sl="agfn_sl",
    e3m3_d="e3m3_d",
    hp_limit="hp_limit",
    hp_limit_sl="hp_limit_sl",
    ht="ht_egas",
    ion="ion_egas",
    hyd="hyd_egas",
    warn1="warn1_egas",
    warn2="warn2_egas",
    mtr="mtr_egas",
    clr="clr_egas",
    htr="htr_egas",
    aux_egas="aux_egas",
    aux="aux_egas",
    prs="prs_egas",
    sbf="sbf_egas",
    stboxf="stboxf_egas",
    # New metrics v311
    hp_raising_avg="hp_raising_avg",
    hp_raising_avg_sl="hp_raising_avg_sl",
    hp_lowering_avg="hp_lowering_avg",
    hp_lowering_avg_sl="hp_lowering_avg_sl",
    prox_timeout="prox_timeout",
    buffer_err="buffer_err",
    pveh_status="pveh_status",
    kwh_lasthour="kwh_lasthour",
    kwh_last30days="kwh_last30days",
    der_dtp_vpd="der_dtp_vpd",
    der_hp_vpd="der_hp_vpd",
    der_suc_vpd="der_suc_vpd",
    der_dis_vpd="der_dis_vpd",
    der_dis_temp_vpd="der_dis_temp_vpd",
    gvf="gvf",
    stroke_speed_avg="stroke_speed_avg",
    stroke_speed_avg_sl="stroke_speed_avg_sl",
    fluid_rate_vpd="fluid_rate_vpd",
    agf_dis_temp="agf_dis_temp",
    agf_dis_temp_max="agf_dis_temp_max",
    end_stop_avg_pveh="end_stop_avg_pveh",
    end_stop_counts="end_stop_counts",
    end_tap_avg_time="end_tap_avg_time",
    end_tap_counts="end_tap_counts",
    end_stop_time="end_stop_time",
    der_ok_counts="der_ok_counts",
    lag_top_ms="lag_top_ms",
    lag_btm_ms="lag_btm_ms",
    signal="signal",
    stroke_up_time="stroke_up_time",
    stroke_up_time_sl="stroke_up_time_sl",
    stroke_down_time="stroke_down_time",
    stroke_down_time_sl="stroke_down_time_sl",
    hyd_oil_lvl="hyd_oil_lvl",
    hyd_filt_life="hyd_filt_life",
    hyd_oil_life="hyd_oil_life",
    suction_vru="suction_vru",
    max_fl_tmp_b4_dr="max_fl_tmp_b4_dr",
    fl_tmp="fl_tmp",
    suction_tmp="suction_tmp",
    fl_tmp_derate_pct="fl_tmp_derate_pct",
    lag_time_derate_pct="lag_time_derate_pct",
    # New real time metrics March 20, 2024
    # hyd_press_s="hyd_press_s",
    # hyd_press_p="hyd_press_p",
    swv_python="swv_python",
    avg_hyd_press="avg_hyd_press",
    peak_top_hyd_press="peak_top_hyd_press",
    peak_btm_hyd_press="peak_btm_hyd_press",
    cal_status="cal_status",
    cal_high_speed_mem="cal_high_speed_mem",
    pump_p_hys="pump_p_hys",
    pump_s_hys="pump_s_hys",
    pump_p_cal_high="pump_p_cal_high",
    pump_p_cal_mid="pump_p_cal_mid",
    pump_s_cal_high="pump_s_cal_high",
    pump_s_cal_mid="pump_s_cal_mid",
    p2p_up_err_high="p2p_up_err_high",
    p2p_up_err_mid="p2p_up_err_mid",
    p2p_down_err_high="p2p_down_err_high",
    p2p_down_err_mid="p2p_down_err_mid",
    re_flag_cnt="re_flag_cnt",
    re_stop_cnt="re_stop_cnt",
    re_tap_cnt="re_tap_cnt",
    le_flag_cnt="le_flag_cnt",
    le_stop_cnt="le_stop_cnt",
    le_tap_cnt="le_tap_cnt",
    gas_prod_rt="gas_prod_rt",
)
# egas_xfer_options = [
#     {"label": item_labels_colors[key]["label"], "value": key} for key, value in names_xfer_egas.items()
# ]
defaults_xfer_egas = (
    "cgp",
    "dgp",
    "agft",
    "mgp",
    "msp",
)

# Remote control metric names
settable_metric_names = {
    # Unit control (not Moosomin HQ shop control)
    "AGFT": "Suction Target",
    "AGFM": "Max speed (%)",
    "AGFN": "Min speed (%)",
    "MGP": "Max discharge (PSI)",
    "PID": "Adjustment speed (1-10)",
    "HP_LIMIT": "Max horsepower target",
    "HYD": "Start/stop",
    "HYD_EGAS": "Start/stop",
    "LOCKOUT": "Lock/unlock",
    "LOCKOUT_EGAS": "Lock/unlock",
    # UNO-specific
    "SPM_SET": "SPM setpoint",
    "SPM_MAX": "SPM maximum",
    "SPM_MIN": "SPM minimum",
    "APFT": "Auto pump fillage target",
    "TS": "Top setpoint",
    "BS": "Bottom setpoint",
    "PID_UNO": "UNO adjustment speed (1-10)",
    # Moosomin HQ shop control
    # Doors to unlock/lock
    "unlocked_all": "All doors unlocked",
    "office_unlocked": "Office doors unlocked",
    "unlocked_shop": "Shop doors unlocked",
    "gym_unlocked2": "Gym door unlocked",
    # Lights on/off
    "north_on": "North lights on",
    "south_on": "South lights on",
    "outdoor_on": "Outdoor lights on",
    # Heat recovery ventilation (HRV) system for the office
    "office_hrv_fast_on": "Office HRV fan (fast) on",
    "fan_office_main_on": "Office main fan on",
    # Overhead doors (open,close,stop)
    "ohd_west_open": "Overhead doors - west open",
    "ohd_west_close": "Overhead doors - west close",
    "ohd_west_stop": "Overhead doors - west stop",
    "ohd_east_open": "Overhead doors - east open",
    "ohd_east_close": "Overhead doors - east close",
    "ohd_east_stop": "Overhead doors - east stop",
}

CONTROL_COMMAND_MEANINGS = {
    1: "Read - reply with current value",
    2: "Write - reply with accepted/current val",
}

CONTROL_RESPONSE_MEANINGS = {
    1: "Accepted",
    2: "Declined - max exceeded",
    3: "Declined - min exceeded",
    4: "Declined - data type mismatch",
    5: "Declined - control number invalid",
}

CONTROL_ENUM_MEANINGS = {
    1: "0 dp (x 1)",
    2: "1 dp (x 10)",
    3: "2 dp (x 100)",
    4: "3 dp (x 1000)",
    5: "4 dp (x 10000)",
    6: "5 dp (x 100000)",
}

CONTROL_ENUM_MULTIPLIERS = {
    1: 1,
    2: 10,
    3: 100,
    4: 1000,
    5: 10_000,
    6: 100_000,
}
