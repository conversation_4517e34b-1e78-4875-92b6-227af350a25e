# app/dashapp/dash_setup.py
import os
from pathlib import Path

import dash_mantine_components as dmc
from dash import Dash, _dash_renderer
from dash._utils import interpolate_str
from flask import (
    Blueprint,
    Flask,
    redirect,
    render_template,
    send_from_directory,
    url_for,
)
from flask.templating import render_template_string
from flask_login import current_user, login_required
from flask_wtf.csrf import CSRFProtect

from app.config import (
    DASH_GATEWAY_TROUBLESHOOTING,
    DASH_URL_ACCOUNT,
    DASH_URL_APPLICATION,
    DASH_URL_CAREERS,
    DASH_URL_CONTACT,
    DASH_URL_LOGIN,
    DASH_URL_MODBUS,
    DASH_URL_RCOM,
    DASH_URL_REGISTER,
    DASH_URL_RELEASE_NOTES,
    DASH_URL_SERVICE_CLOCK,
    DASH_URL_SMS_ALERTS_TROUBLESHOOTING,
    DASH_URL_WEB_API,
    DASH_URL_WORK_ORDER,
    DASH_URL_WORK_ORDER_CORP,
    TEMPLATES_FOLDER_DASH,
)
from app.dashapp.layout import get_url_bar_and_content_div
from app.error_setup import log_error
from app.utils.simple import check_confirmed

# if REDIS_URL:
# Use Redis & Celery if REDIS_URL set as an env variable
# background_callback_manager = CeleryManager(celery)
# else:
#     # Diskcache for non-production apps when developing locally
#     import diskcache

#     cache = diskcache.Cache("./cache")
#     background_callback_manager = DiskcacheManager(cache)

# Do this before creating Dash app object.
# Dash Mantine Components is based on REACT 18.
# You must set the env variable REACT_VERSION=18.2.0 before starting the app.
# https://www.dash-mantine-components.com/migration
# _available_react_versions = {"16.14.0", "18.2.0"}
_dash_renderer._set_react_version("18.2.0")

# Stylesheets required for Dash Mantine Components (dmc). You can pick as per your need.
# https://www.dash-mantine-components.com/migration
external_stylesheets = [
    # For datepickers
    dmc.styles.DATES,
    dmc.styles.NOTIFICATIONS,
    # dbc.themes.BOOTSTRAP,
    # dbc.icons.FONT_AWESOME,
    # dbc.icons.BOOTSTRAP,
    # "https://unpkg.com/@mantine/code-highlight@7/styles.css",
    # "https://unpkg.com/@mantine/charts@7/styles.css",
    # "https://unpkg.com/@mantine/carousel@7/styles.css",
    # "https://unpkg.com/@mantine/notifications@7/styles.css",
    # "https://unpkg.com/@mantine/nprogress@7/styles.css",
]


class MyDash(Dash):
    """Override the normal Dash class"""

    # Dict for passing kwargs to the various methods when self.index() is called
    special_kwargs: dict = {}

    def get_html_body(self, **context):
        """
        Render the Dash HTML body from a Jinja2 layout,
        with the request context for is_admin(), etc

        :param context: The variables to make available in the Jinja2 template.
        """
        html_template = self.special_kwargs.get("html_template", None)
        if html_template is None:
            raise KeyError(
                "special_kwargs['html_template'] is None so we can't render the page!"
            )

        context["noindex"] = self.special_kwargs.get("noindex", True)
        context["description"] = self.special_kwargs.get(
            "description",
            """Control your IJACKs with IJACK RCOM.
Real time data, charts, remote control, logs. Wherever you are, you're in control.""",
        )

        with open(html_template, "r", encoding="utf-8") as f:
            html_body = render_template_string(
                f.read(),
                **context,
            )

        comments_to_replace = (
            "title",
            "metas",
            # "favicon",
            "css",
            "app_entry",
            "config",
            "scripts",
            "renderer",
        )
        for comment in comments_to_replace:
            html_body = html_body.replace(f"<!-- {comment} -->", "{%" + comment + "%}")

        return html_body

    def interpolate_index(
        self,
        metas="",
        title="",
        css="",
        config="",
        scripts="",
        app_entry="",
        favicon="",
        renderer="",
    ):
        """
        Called to create the initial HTML string that is loaded on page.
        Override this method to provide you own custom HTML.

        :Example:

            class MyDash(Dash):
                def interpolate_index(self, **kwargs):
                    return '''<!DOCTYPE html>
                    <html>
                        <head>
                            <title>My App</title>
                        </head>
                        <body>
                            <div id="custom-header">My custom header</div>
                            {app_entry}
                            {config}
                            {scripts}
                            {renderer}
                            <div id="custom-footer">My custom footer</div>
                        </body>
                    </html>'''.format(app_entry=kwargs.get('app_entry'),
                                      config=kwargs.get('config'),
                                      scripts=kwargs.get('scripts'),
                                      renderer=kwargs.get('renderer'))

        :param metas: Collected & formatted meta tags.
        :param title: The title of the app.
        :param css: Collected & formatted css dependencies as <link> tags.
        :param config: Configs needed by dash-renderer.
        :param scripts: Collected & formatted scripts tags.
        :param renderer: A script tag that instantiates the DashRenderer.
        :param app_entry: Where the app will render.
        :param favicon: A favicon <link> tag if found in assets folder.
        :return: The interpolated HTML string for the index.
        """

        loading_msg = self.special_kwargs.get("loading_msg", None)
        if loading_msg:
            # Override app_entry with special loading message
            app_entry = f"""
            <div id="react-entry-point">
                <div class="_dash-loading">
                    {loading_msg}
                </div>
            </div>
            """

        title = self.special_kwargs.get("title", "IJACK RCOM")

        # Get a different Jinja2 template with each self.index() call
        template = self.get_html_body(title=title)

        return interpolate_str(
            template=template,
            metas=metas,
            title=title,
            css=css,
            config=config,
            scripts=scripts,
            favicon=favicon,
            renderer=renderer,
            app_entry=app_entry,
        )

    # This method just loads the HTML template string
    # and it takes a while to open and render the Jinja2 template,
    # so we cache it for a day. That way if we change the underlying template, it'll refresh at least once a day
    # @cache_memoize_if_prod(timeout=60 * 60 * 24)
    def index(
        self,
        *args,
        # path is just so we can cache the template string for each URL for speed
        path: str,
        # Need a different template if user isn't authenticated (login/logout buttons)
        is_authenticated: bool = False,
        # Need a different template if user isn't an IJACK employee (no admin button)
        user_id: int = None,
        # Default Dash layout template
        html_template: Path = Path(__file__).parent.parent.joinpath(
            "templates/dash_rcom/layout_dash.html"
        ),
        title: str = "IJACK RCOM",
        description: str = """Control your IJACKs with IJACK RCOM.
Real time data, charts, remote control, logs. Wherever you are, you're in control.""",
        noindex: bool = True,
        loading_msg: str = "Loading...",
        **kwargs,
    ) -> str:
        """Override the normal index() method to pass kwargs to the various methods"""

        self.special_kwargs["path"] = path
        self.special_kwargs["is_authenticated"] = is_authenticated
        self.special_kwargs["user_id"] = user_id
        self.special_kwargs["html_template"] = html_template
        self.special_kwargs["title"] = title
        self.special_kwargs["description"] = description
        self.special_kwargs["noindex"] = noindex
        self.special_kwargs["loading_msg"] = loading_msg

        index: str = super().index(*args, **kwargs)
        return index


def dash_global_error_handler(err) -> None:
    """Setup a global error handler for the Dash app"""

    # Record the error in the database along with user information
    log_error(err)

    return None

    # Return True to allow the error to propagate to the frontend
    return True  # This is key - returning True re-raises the error in the frontend


# Create this outside the factory function so the
# Dash blueprint can access it to call its index() method
dash_app = MyDash(
    name=__name__,
    # We'll add the server later with init_app()
    server=False,
    # This is for looking up React components in the Dash renderer
    url_base_pathname="/dash/",
    # "title" is the text that appears in a browser tab when no callbacks are running
    title="IJACK",
    # update_title appears in the browser tab while callbacks are running
    update_title="Updating...",
    # Don't use Flask-Compress. Use Traefik, which is more efficient!
    # TODO: Traefik compression not working!
    # https://www.whatsmyip.org/http-compression-test/
    compress=True,
    # transforms=[MultiplexerTransform()],
    # assets_folder: str is a path, relative to the current working directory,
    # for extra files to be used in the browser
    assets_folder="assets",  # NOTE: must be a relative string, not a Path object!
    # background_callback_manager=background_callback_manager,
    # Add stylesheets for Dash Mantine Components
    external_stylesheets=external_stylesheets,
    # load the Dash bundles much faster from the https://unpkg.com/ CDN,
    # which is a community-maintained project that serves JavaScript bundles from NPM.
    # For performance-critical apps served beyond an intranet, online CDNs can often deliver
    # these files much faster than loading the resources from the file system,
    # and will reduce the load on the Dash server.
    # https://dash.plotly.com/external-resources
    # If serve_locally=False, the Dash app will try to load the JavaScript and CSS bundles from the CDN.
    # This means CSS files in the assets folder will NOT be loaded.
    serve_locally=True,
    # on_error=dash_global_error_handler,
)


# Create a Flask Blueprint for the Dash app
# dash_bp = Blueprint('dash', __name__, url_prefix='/')
dash_bp = Blueprint("dash", __name__)


def set_dash_routes(flask_app: Flask) -> None:
    """
    Set the routes for the Dash app.
    This is called in the factory function.
    """
    pass


@dash_bp.route(f"/{DASH_URL_RCOM}/")
@login_required
@check_confirmed
# @csrf.exempt
def home():
    """Main RCOM route, for use with url_for()"""
    return dash_app.index(
        path=DASH_URL_RCOM,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("rcom.html"),
        noindex=True,
        title="IJACK RCOM",
        description="""Control your IJACKs with IJACK RCOM.
Real time data, charts, remote control, logs. Wherever you are, you're in control.""",
    )


@dash_bp.route(f"/{DASH_URL_WORK_ORDER}/")
@login_required
@check_confirmed
# @csrf.exempt
def work_order():
    """Form to create a work order for IJACK Inc"""
    # In nav.py, Dash checks the URL and
    # sees "work-order", then loads that layout
    return dash_app.index(
        path=DASH_URL_WORK_ORDER,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("rcom.html"),
        noindex=True,
        title="IJACK RCOM",
        description="""Control your IJACKs with IJACK RCOM.
Real time data, charts, remote control, logs. Wherever you are, you're in control.""",
    )


@dash_bp.route(f"/{DASH_URL_WORK_ORDER_CORP}/")
@login_required
@check_confirmed
# @csrf.exempt
def work_order_corp():
    """Form to create a work order for IJACK Corp"""
    # In nav.py, Dash checks the URL and
    # sees "work-order", then loads that layout
    return dash_app.index(
        path=DASH_URL_WORK_ORDER_CORP,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("rcom.html"),
        noindex=True,
        title="IJACK RCOM",
        description="""Control your IJACKs with IJACK RCOM.
Real time data, charts, remote control, logs. Wherever you are, you're in control.""",
    )


@dash_bp.route(f"/{DASH_URL_SERVICE_CLOCK}/")
@login_required
@check_confirmed
# @csrf.exempt
def service_clock():
    """Service employee clock-in/out form"""
    # In nav.py, Dash checks the URL and
    # sees "service-clock", then loads that layout
    return dash_app.index(
        path=DASH_URL_SERVICE_CLOCK,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("rcom.html"),
        noindex=True,
        title="IJACK RCOM",
        description="""Control your IJACKs with IJACK RCOM.
Real time data, charts, remote control, logs. Wherever you are, you're in control.""",
    )


@dash_bp.route(f"/{DASH_URL_RELEASE_NOTES}/")
@login_required
@check_confirmed
# @csrf.exempt
def release_notes():
    """Form for viewing the release notes and updating the gateway Python software"""
    # In nav.py, Dash checks the URL and
    # sees "release-notes", then loads that layout
    return dash_app.index(
        path=DASH_URL_RELEASE_NOTES,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("rcom.html"),
        noindex=True,
        title="IJACK RCOM",
        description="""Control your IJACKs with IJACK RCOM.
Real time data, charts, remote control, logs. Wherever you are, you're in control.""",
    )


@dash_bp.route("/calculator/")
@dash_bp.route("/application/")
def apply_redirect():
    """Redirect to the application form"""
    return redirect(url_for("dash.apply"), code=301)


@dash_bp.route(f"/{DASH_URL_APPLICATION}/")
# @csrf.exempt
def apply():
    """Form for IJACK application. No need to login first."""
    # In nav.py, Dash checks the URL and
    # sees "apply", then loads that layout
    return dash_app.index(
        path=DASH_URL_APPLICATION,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="IJACK Application",
        description="""Apply for your own customized IJACK pump today!""",
    )


@dash_bp.route("/registration/")
def register_redirect():
    """Redirect to the registration form"""
    return redirect(url_for("dash.register"), code=301)


@dash_bp.route(f"/{DASH_URL_REGISTER}/")
# @csrf.exempt
def register():
    """Form for registering IJACK users. No need to login first."""
    # In nav.py, Dash checks the URL and
    # sees "register", then loads that layout
    return dash_app.index(
        path=DASH_URL_REGISTER,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="Register for Access to IJACK RCOM",
        description="""Register for your own IJACK RCOM dashboard.""",
    )


@dash_bp.route("/my-account/")
@dash_bp.route("/reset-password-request/")
@dash_bp.route("/sign-in/")
@dash_bp.route("/sign_in/")
@dash_bp.route("/log-in/")
@dash_bp.route("/log_in/")
def login_redirect():
    """Redirect to the login form"""
    return redirect(url_for("dash.login"), code=301)


@dash_bp.route(f"/{DASH_URL_LOGIN}/")
# @csrf.exempt
def login():
    """Form for authenticating IJACK users. No need to login first."""
    # In nav.py, Dash checks the URL and
    # sees "login", then loads that layout
    return dash_app.index(
        path=DASH_URL_LOGIN,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="Authenticate Your Account at IJACK",
        description="""Authenticate your account at IJACK to access your IJACK RCOM dashboard.""",
    )


@dash_bp.route(f"/{DASH_URL_CONTACT}/")
# @csrf.exempt
def contact():
    """
    Form for contacting IJACK and submitting a service request
    from a QR code scanned on the structure.
    """
    # In nav.py, Dash checks the URL and
    # sees "contact", then loads that layout
    return dash_app.index(
        path=DASH_URL_CONTACT,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="Contact IJACK",
        description="""Contact IJACK for service, support, or to apply for your own customized IJACK pump today!""",
    )


@dash_bp.route(f"/{DASH_URL_MODBUS}/")
@login_required
# @csrf.exempt
def modbus_registers():
    """Form for authenticating IJACK users. No need to login first."""
    # In nav.py, Dash checks the URL and
    # sees "login", then loads that layout
    return dash_app.index(
        path=DASH_URL_MODBUS,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="Modbus Holding Registers at IJACK",
        description="""Get your IJACK data into your SCADA system with Modbus TCP-IP.""",
    )


@dash_bp.route(f"/{DASH_URL_WEB_API}/")
@login_required
# @csrf.exempt
def web_api_info():
    """Get the web API info for the RCOM page"""
    return dash_app.index(
        path=DASH_URL_WEB_API,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="IJACK RCOM Web API",
        description="""Get your IJACK data into your SCADA system with the IJACK RCOM Web API.""",
    )


@dash_bp.route(f"/{DASH_GATEWAY_TROUBLESHOOTING}/")
@login_required
# @csrf.exempt
def gateway_troubleshooting():
    """Get the troubleshooting instructions for the IJACK Gateway"""
    return dash_app.index(
        path=DASH_GATEWAY_TROUBLESHOOTING,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="IJACK Gateway Troubleshooting Instructions",
        description="""Troubleshoot your IJACK Gateway with these instructions.""",
    )


@dash_bp.route(f"/{DASH_URL_SMS_ALERTS_TROUBLESHOOTING}/")
@login_required
# @csrf.exempt
def sms_alerts_troubleshooting():
    """Get the troubleshooting instructions for the IJACK SMS alerts"""
    return dash_app.index(
        path=DASH_URL_SMS_ALERTS_TROUBLESHOOTING,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="IJACK SMS Alerts Troubleshooting Instructions",
        description="""Troubleshoot your IJACK SMS alerts with these instructions.""",
    )


@dash_bp.route(f"/{DASH_URL_CAREERS}/")
# @csrf.exempt
def careers():
    """Get the careers page"""
    return dash_app.index(
        path=DASH_URL_CAREERS,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("layout_dash.html"),
        noindex=False,
        title="IJACK Careers",
        description="""Join the IJACK team!""",
    )


@dash_bp.route(f"/{DASH_URL_ACCOUNT}/")
@login_required
# @csrf.exempt
def account():
    """Get the account page"""
    return dash_app.index(
        path=DASH_URL_ACCOUNT,
        is_authenticated=current_user.is_authenticated,
        user_id=getattr(current_user, "id", None),
        html_template=TEMPLATES_FOLDER_DASH.joinpath("rcom.html"),
        noindex=False,
        title="IJACK Account",
        description="""View and edit your IJACK account.""",
    )


@dash_bp.route("/dash/<page>.js")
# @csrf.exempt
def serve_js(page):
    """Serve JavaScript files for Dash layouts (NOTE: UNDER DEVELOPMENT!)"""
    # Serve static JavaScript files for Dash layouts
    test = send_from_directory(TEMPLATES_FOLDER_DASH, f"{page}.js")
    print(test)
    test2 = render_template(f"{page}.js")
    print(test2)
    #     test3 = render_template_string("""
    # <script>
    # console.log("Hello from Dash render_template_string")
    # </script>
    # """)
    #     print(test3)
    return test


def protect_dashviews(dash_app) -> None:
    """
    If you want your Dash app to require a login,
    call this function with the Dash app you want to protect
    """
    base_pathname: str = dash_app.config.url_base_pathname
    # Search the dictionary of Flask view functions for those that start with the base_pathname
    for view_func in dash_app.server.view_functions.keys():
        if view_func.startswith(base_pathname):
            dash_app.server.view_functions[view_func] = login_required(
                dash_app.server.view_functions[view_func]
            )

    return None


def check_for_duplicate_callbacks(dash_app) -> None:
    """Don't let app start if there are multiple callbacks with the same output"""
    list_ = []
    for callback in dash_app.callback_map.keys():
        multiple_outputs = callback.split("...")
        for output in multiple_outputs:
            if output in list_:
                # Callback output already seen once.
                # This will create an error in the JavaScript console client-side!
                raise Exception(f"Duplicate callback output found: {output}")
            list_.append(output)

    return None


def register_dash_app(flask_app: Flask) -> MyDash:
    """Register Dash apps with the Flask app"""

    # These imports should be inside this function so that other Flask
    # stuff gets loaded first, since some of the below imports reference the other
    # Flask stuff, creating circular references
    from app.dashapp.callbacks.nav import get_validation_layout

    # favicon.ico in static/img
    # assets_folder = f"{get_root_path(__name__)}/static_dash"

    dash_app.init_app(flask_app)

    # Set the routes for the Dash app
    set_dash_routes(flask_app)

    csrf: CSRFProtect = flask_app.extensions["csrf"]
    csrf.exempt(dash_bp)

    want_debug = os.getenv("FLASK_DEBUG", "0") == "1"
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl") and want_debug:
        # Ensure that the Flask app is in debug mode
        flask_app.debug = True
        # Enable the Dash Dev Tools
        dash_app.enable_dev_tools(
            # bool, activate all the dev tools listed below.
            debug=True,
            # bool, set to False to explicitly disable dev tools UI in debugger mode (default=True). This UI is the blue button in the bottom right corner that contains the error messages, server status, and the callback graph.
            dev_tools_ui=True,
            # bool, set to False to explicitly disable Component Validation (default=True)
            dev_tools_props_check=True,
            # bool, serve the dev JavaScript bundles from the local filesystem
            dev_tools_serve_dev_bundles=True,
            # bool, set to True to enable hot reloading (default=False)
            dev_tools_hot_reload=True,
            # float, interval in seconds at which the renderer will request the reload hash and update the browser page if it changed. (default=3)
            dev_tools_hot_reload_interval=3.0,
            #  float, delay in seconds between each walk of the assets folder to detect file changes. (default=0.5 seconds)
            dev_tools_hot_reload_watch_interval=0.5,
            # int, number of times the reloader is allowed to fail before stopping and sending an alert. (default=8)
            dev_tools_hot_reload_max_retry=8,
            # bool, remove the routes access logging from the console. (default=True)
            dev_tools_silence_routes_logging=True,
            # bool, simplify tracebacks to just user code, omitting stack frames from Dash and Flask internals. (default=True)
            dev_tools_prune_errors=True,
        )

    # # By default, Dash applies validation to your callbacks, which performs checks
    # # such as validating the types of callback arguments and checking to see whether
    # # the specified Input and Output components actually have the specified properties
    # dash_app.config.suppress_callback_exceptions = True

    # "complete" layout, just for callback validation
    dash_app.validation_layout = get_validation_layout()

    # Assign the get_main_layout function without calling it yet;
    # otherwise we need flask_app.app_context() for certain things
    dash_app.layout = get_url_bar_and_content_div

    # # Require login for all Dash views (we don't want this for, say, the application forms)
    # The /dashapp/views.py file has the login_required decorator, which is sufficient I think.
    # Also, if that fails, the layouts aren't loaded in nav.py if the current_user is not authenticated.
    # protect_dashviews(dash_app)

    # Don't let app start if there are multiple callbacks with the same output
    check_for_duplicate_callbacks(dash_app)

    return dash_app
