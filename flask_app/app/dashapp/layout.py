import os
import uuid

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import Input, Output, callback, dcc, html
from dash.exceptions import PreventUpdate
from flask_login import current_user

from app import user_is_ijack_employee
from app.auth.views import record_visit
from app.config import (
    TAB_LIST_FILTERS,
    TAB_MAP,
    TAB_STATUS,
    TAB_UNOGAS_EGAS,
    TAB_UNOGAS_UNO,
)
from app.dashapp.callbacks.alarm_log import get_alarm_log_div
from app.dashapp.callbacks.charts_cards import get_cards_div
from app.dashapp.callbacks.charts_main import get_main_charts_div
from app.dashapp.callbacks.control_action import get_control_div
from app.dashapp.callbacks.health import get_health_div
from app.dashapp.callbacks.indicators import (
    get_ind_charts_cards_tabs,
    get_indicators_div,
)
from app.dashapp.callbacks.inventory import get_inventory_div
from app.dashapp.callbacks.list import get_list_tab_contents
from app.dashapp.callbacks.maintenance import get_maintenance_row
from app.dashapp.callbacks.map import get_map_tab_contents
from app.dashapp.callbacks.operators import get_operators_row
from app.dashapp.callbacks.performance import get_performance_div
from app.dashapp.callbacks.service_request import get_service_request_row
from app.dashapp.callbacks.status import get_status_row
from app.dashapp.callbacks.unit_notes import get_notes_row
from app.dashapp.utils import get_id_triggered


def get_url_bar_and_content_div() -> dmc.MantineProvider:
    """Get the initial bare layout for a multipage Dash app"""
    # return html.Div(
    # It's required that you wrap your app with a dmc.MantineProvider, so Dash doesn't complain.
    # https://www.dash-mantine-components.com/getting-started
    return dmc.MantineProvider(
        # theme={
        #     "colorScheme": "light",
        #     "fontFamily": "'Inter', sans-serif",
        #     "primaryColor": "indigo",
        # },
        # styles={"Button": {"root": {"fontWeight": 400}}},
        # If these are set to True, the Bootstrap 5 navbar at the top
        # uses less-bold font-weight for some reason
        # withGlobalClasses=False,
        # withNormalizeCSS=False,
        # The actual layout starts here
        children=[
            # URL pathname (e.g. /rcom/)
            # represents the browser address bar and doesn't render anything.
            # Note this must be loaded before the dynamic multi-page layout is
            # rendered, so its state can be passed into the dynamically-generated
            # multi-page layout!
            dcc.Location(id="url", refresh=False, search=None),
            # Blank div for the rest of the content
            html.Div(id="page_content"),
            # For appending dynamic JavaScript to the page with a clientside callback
            html.Div(id="dynamic_js", style={"display": "none"}),
            # To enable the Notifications System, include the dmc.NotificationProvider() component anywhere in your app layout
            dmc.NotificationProvider(),
        ]
    )


def get_main_layout() -> html.Div:
    """Get the Dash layout, after the app.models have been loaded"""

    return html.Div(
        [
            # We could probably replace the following flashed messages div with the main one in layout.html
            dbc.Container(id="flashed_messages_div"),
            # hidden signals in divs (interval counter, and places to stash item state between callbacks)
            get_signals(),
            get_body(),
            # popup modal for confirmation of remote-control commands
            get_modal_confirm(),
            # Alerts users to site updates requiring browser refresh
            # get_modal_software_update(),
            # Modal for displaying a simple message, with only a cancel button
            get_modal_message(),
            get_modal_confirm_duplicate_alertees(),
        ]
    )


def get_modal_confirm():
    """
    The popup confirmation modal when a user clicks a button.
    This modal is just a placeholder, and will be replaced in a callback,
    but we need the id='btn_modal_confirm'
    """
    # Main confirmation modal (asking for confirmation before executing the command)
    return dbc.Modal(
        [
            dbc.ModalHeader(dbc.ModalTitle(id="modal_main_header")),
            dbc.ModalBody(
                "",
                id="modal_main_body",
                # style={"color": "#717174"}
            ),
            dbc.ModalFooter(
                [
                    dbc.Button(
                        # [dbc.Spinner(size="sm"), " Loading..."]
                        "Cancel",
                        id="btn_modal_main_cancel",
                        # className="btn btn-secondary",
                        size="sm",
                        color="secondary",
                    ),
                    dbc.Button(
                        # [dbc.Spinner(size="sm"), " Loading..."]
                        "Confirm",
                        id="btn_modal_main_confirm",
                        # className="btn btn-danger",
                        size="sm",
                        color="danger",
                    ),
                ],
            ),
        ],
        id="modal_main",
        centered=True,
        # Render a backdrop that doesn't dismiss the modal when clicked
        # (i.e. user MUST click a button in the footer)
        backdrop="static",
        is_open=False,
    )


def get_modal_confirm_duplicate_alertees():
    """
    When the user requests to duplicate the alertees from one unit to another,
    this model pops up with a list of units and users
    """
    return dbc.Modal(
        [
            dbc.ModalHeader(
                dbc.ModalTitle(
                    "Confirm duplicate alerts to new unit",
                    id="modal_duplicate_alertees_title",
                )
            ),
            dbc.ModalBody(
                [
                    dcc.Store(id="modal_duplicate_alertees_is_open_1", data=False),
                    dcc.Store(id="modal_duplicate_alertees_is_open_2", data=False),
                    dbc.Row(
                        dbc.Col(
                            [
                                html.P(
                                    "The purpose of this form is to quickly duplicate the current unit's alerts to a new unit that has the same operators/alertees."
                                ),
                                html.P(
                                    "First select the new unit, for which you want to create alerts. Then (optionally) modify the alertees for that new unit."
                                ),
                            ]
                        )
                    ),
                    dbc.Row(
                        dbc.Col(
                            [
                                dbc.Label("Select the new unit for the new alerts"),
                                dcc.Dropdown(
                                    id="duplicate_alertees_unit_select",
                                    multi=False,  # just one unit
                                ),
                            ],
                            class_name="mt-2",
                        )
                    ),
                    dbc.Row(
                        dbc.Col(
                            [
                                dbc.Label("Users to be alerted at the new unit"),
                                dcc.Dropdown(
                                    id="duplicate_alertees_users_select",
                                    multi=True,  # multiple users to be alerted
                                ),
                            ],
                            class_name="mt-2",
                        )
                    ),
                    dbc.Row(
                        dbc.Col(
                            [
                                dbc.Label(
                                    "Users to have remote control permissions at the new unit"
                                ),
                                dcc.Dropdown(
                                    id="duplicate_rc_users_select",
                                    multi=True,  # multiple users to be alerted
                                ),
                            ],
                            class_name="mt-2",
                        )
                    ),
                ],
                id="modal_duplicate_alertees_body",
                # style={"color": "#717174"},
            ),
            dbc.ModalFooter(
                [
                    dbc.Button(
                        "Cancel",
                        id="btn_duplicate_alertees_cancel",
                        size="sm",
                        color="secondary",
                    ),
                    dbc.Button(
                        "Confirm",
                        id="btn_duplicate_alertees_confirm",
                        size="sm",
                        color="danger",
                    ),
                ],
            ),
        ],
        id="modal_duplicate_alertees",
        centered=True,
        # Render a backdrop that doesn't dismiss the modal when clicked
        # (i.e. user MUST click a button in the footer)
        backdrop="static",
        is_open=False,
    )


def get_modal_message():
    """Simple popup 'modal' message"""
    return dbc.Modal(
        [
            dbc.ModalHeader(dbc.ModalTitle(id="modal_message_header")),
            dbc.ModalBody(
                id="modal_message_body",
            ),
            dbc.ModalFooter(
                dbc.Button(
                    "Close",
                    id="btn_modal_message_cancel",
                    size="sm",
                    color="secondary",
                ),
            ),
        ],
        id="modal_message",
        centered=True,
        backdrop="static",
        is_open=False,  # Should be false, and only appear if necessary
    )


def get_signals():
    """Hidden signals and interval timers for preserving state"""

    # Create a session ID, which will be saved in the layout as a unique, invisible string
    session_id = str(uuid.uuid4())

    # Interval timer (milliseconds, so 5000 is 5 seconds)
    if os.getenv("FLASK_CONFIG", "production") == "production":
        control_tab_interval = 3000
        rt_charts_interval = 3000
    else:
        control_tab_interval = 1000 * 5
        rt_charts_interval = 1000 * 10
    swv_interval = 2000

    return html.Div(
        [
            # Store the session ID so we can store user session data
            html.Div(session_id, id="session_id", style={"display": "none"}),
            dcc.Location(id="location_main_app", refresh=True),
            # Store the values even if the browser is closed
            # For storing the structure_id (by customer), which may come from the list or the map
            dcc.Store(id="store_structure_dict", storage_type="local", data={}),
            dcc.Store(id="store_url_search", storage_type="local", data={}),
            # Reset on page refresh if storage_type="memory"
            dcc.Store(id="use_url_search", storage_type="memory", data=True),
            dcc.Store(
                id="store_search_bar_values_dict", storage_type="memory", data={}
            ),
            dcc.Store(id="use_search_bar", storage_type="memory", data=True),
            # Store the trigger that calls the customer list callback,
            # and use the same trigger for sub-group, unit type, structures, etc
            # until the structure is stored, and then reset it to None
            dcc.Store(id="store_id_triggered"),
            dcc.Store(id="store_structure_id", storage_type="local"),
            dcc.Store(id="store_customer_id", storage_type="local"),
            dcc.Store(id="store_cust_sub_group_id", storage_type="local"),
            dcc.Store(id="store_unit_type_id", storage_type="local"),
            dcc.Store(id="store_unogas_active_tab", storage_type="local"),
            dcc.Store(id="store_chart_category", storage_type="local"),
            # dcc.Store(id="store_get_diagnostic", storage_type="memory", data=False),
            dcc.Store(id="store_software_version", storage_type="local"),
            # Store the active control tab instead of relying on persistence, which is flaky
            dcc.Store(
                id="store_tabs_for_ind_charts_control_log",
                storage_type="local",
                # # Default tab
                # data=TAB_INDICATORS,
            ),
            dcc.Store(id="store_time_series_rangeselector", storage_type="session"),
            dcc.Store(id="store_rt_rangeselector", storage_type="session"),
            dcc.Store(id="store_rt_countdown", storage_type="memory"),
            # Hidden "setter_new_shadow" which contains the new AWS IoT device shadow wanted
            # It's stored here in JSON form while a modal confirmation window pops up asking for confirmation
            dcc.Store(id="setter_new_shadow", storage_type="memory"),
            dcc.Store(id="store_control_num_read_req", storage_type="session"),
            # Storage pattern so we can have synthetic "multiple outputs"
            dcc.Store(id="ts_rng_btn_3h_store_1", storage_type="memory"),
            dcc.Store(id="ts_rng_btn_3h_store_2", storage_type="memory"),
            # Multiple outputs for recording when a user visits a certain page
            dcc.Store(id="record_visit_rcom_0", storage_type="memory"),
            dcc.Store(id="record_visit_rcom_charts_main", storage_type="memory"),
            dcc.Store(id="record_visit_rcom_charts_real_time", storage_type="memory"),
            dcc.Store(id="record_visit_rcom_indicators", storage_type="memory"),
            dcc.Store(id="record_visit_rcom_control", storage_type="memory"),
            dcc.Store(id="record_visit_rcom_charts_cards", storage_type="memory"),
            dcc.Store(id="record_visit_rcom_charts_diag_cards", storage_type="memory"),
            dcc.Store(id="record_visit_rcom_alarm_log", storage_type="memory"),
            # Hidden signal value, which updates the map
            html.Div(id="hidden_signal", style={"display": "none"}),
            # Refresh the control tab every <interval> milliseconds
            dcc.Interval(
                id="control_tab_refresh_interval",
                # in milliseconds, so 5000 is 5 seconds
                interval=control_tab_interval,
                # automatically incremented by 1 every time "interval" milliseconds passes
                n_intervals=0,
                # There's logic in the program not to let it go
                # more than 100 times in the control setup modules.
                # However, some modules need this to go on forever, so we set it to -1
                # max_intervals=100,
                max_intervals=-1,
                disabled=False,
            ),
            dcc.Interval(
                id="swv_refresh_interval",
                # in milliseconds, so 5000 is 5 seconds
                interval=swv_interval,
                # automatically incremented by 1 every time "interval" milliseconds passes
                n_intervals=0,
                # If -1, then the interval has no limit (the default)
                max_intervals=-1,
                disabled=False,
            ),
            dcc.Interval(
                id="rt_charts_refresh_interval",
                # in milliseconds, so 5000 is 5 seconds
                interval=rt_charts_interval,
                # automatically incremented by 1 every time "interval" milliseconds passes
                n_intervals=0,
                # If -1, then the interval has no limit (the default)
                max_intervals=-1,
                disabled=False,
            ),
        ]
    )


def get_list_map_tab_row(is_ijack_user: bool) -> dbc.Row:
    """Tabs for list or map at the very top"""

    tabs_list = [
        dbc.Tab(
            label="List",
            tab_id=TAB_LIST_FILTERS,
        ),
        dbc.Tab(
            label="Map",
            tab_id=TAB_MAP,
        ),
    ]

    if is_ijack_user:
        tabs_list.append(
            dbc.Tab(
                label="Status",
                tab_id=TAB_STATUS,
            )
        )
        # if True:
        #     tabs_list.append(
        #         dbc.Tab(
        #             label="Sean",
        #             tab_id=4564654,
        #         )
        #     )
        #     pass

    return dbc.Row(
        class_name="justify-content-sm-center my-3",  # put the tabs in the middle
        children=dbc.Col(
            [
                # Tabs for either map or unit list
                dbc.Tabs(
                    tabs_list,
                    id="tabs_for_nav",
                    active_tab=TAB_LIST_FILTERS,  # default tab
                    className="justify-content-center",
                    # style=dict(color="#717174"),
                    persistence=True,
                    persistence_type="local",
                ),
            ],
        ),
    )


def get_map_or_units_list(is_ijack_user: bool):
    """Get the map or the list of units, depending on the tab selected"""
    return dbc.Row(
        class_name="justify-content-center my-3",
        children=[
            # Map or unit list (TBD by tab above)
            # We need everything to be in the initial layout or
            # we'll get warnings that IDs are missing
            dbc.Col(
                lg=12,
                # xxl=10,
                children=[
                    # Map tab
                    get_map_tab_contents(),
                    # List tab
                    get_list_tab_contents(is_ijack_user),
                    # Status tab content, beside the map tab (are the units connected?),
                    get_status_row(),
                ],
            ),
        ],
    )


def get_unogas_uno_egas_tabs_div():
    """
    Get the tabs for UNOGAS unit types only.
    Tabs for UNO or EGAS/XFER - only visible for UNOGAS units
    """
    return dbc.Row(
        style={"display": "none"},  # don't display by default
        id="row_for_unogas_uno_egas_tabs",
        children=dbc.Col(
            dbc.Tabs(
                [
                    dbc.Tab(
                        label="UNO",
                        tab_id=TAB_UNOGAS_UNO,
                    ),
                    dbc.Tab(
                        label="EGAS",
                        tab_id=TAB_UNOGAS_EGAS,
                    ),
                ],
                id="tabs_for_unogas_uno_egas",
                # default tab
                active_tab=TAB_UNOGAS_UNO,
                # className="justify-content-left",
                className="justify-content-center mb-2",
                persistence=True,
                persistence_type="local",
            ),
        ),
    )


def get_body():
    """Get the charts"""

    is_ijack_user: bool = user_is_ijack_employee(
        user_id=getattr(current_user, "id", None)
    )

    body = dbc.Container(
        fluid=True,
        children=[
            get_list_map_tab_row(is_ijack_user),
            # Map or unit list,
            get_map_or_units_list(is_ijack_user),
            get_ind_charts_cards_tabs(is_ijack_user),
            get_unogas_uno_egas_tabs_div(),
            # Diagnostic data request history
            # NOTE: This needs to be above the main charts div (but I don't think it's used anymore!)
            # get_diag_data_request_hist_div(),
            # Diagnostic cards (I don't think anybody uses this currently)
            # get_diag_card_div(),
            # Charts (not surface/compression) main div
            get_main_charts_div(),
            # Surface or compression card
            get_cards_div(),
            # Indicators
            get_indicators_div(is_ijack_user),
            # Remote control tab
            get_control_div(),
            # Alarm log data_table table
            get_alarm_log_div(),
            # Performance tab content
            get_performance_div(),
            # Health tab content
            get_health_div(),
            # Inventory tab content
            get_inventory_div(),
            # Operators table
            get_operators_row(),
            # Maintenance records table
            get_maintenance_row(),
            # Notes on the unit
            get_notes_row(is_ijack_user),
            # Service request form
            get_service_request_row(is_ijack_user),
        ],
    )

    return body


@callback(
    Output("modal_duplicate_alertees", "is_open"),
    Input("modal_duplicate_alertees_is_open_1", "data"),
    Input("modal_duplicate_alertees_is_open_2", "data"),
    prevent_initial_call=True,
)
def modal_duplicate_alertees_is_open_multiple_outputs(
    modal_duplicate_alertees_is_open_1_data: bool,
    modal_duplicate_alertees_is_open_2_data: bool,
) -> bool:
    """Multiple outputs pattern"""
    id_triggered: str = get_id_triggered()
    if id_triggered == "modal_duplicate_alertees_is_open_1.data":
        return modal_duplicate_alertees_is_open_1_data
    elif id_triggered == "modal_duplicate_alertees_is_open_2.data":
        return modal_duplicate_alertees_is_open_2_data

    raise PreventUpdate()


@callback(
    Output("record_visit_rcom_0", "data"),
    Input("record_visit_rcom_charts_main", "data"),
    Input("record_visit_rcom_charts_real_time", "data"),
    Input("record_visit_rcom_indicators", "data"),
    Input("record_visit_rcom_control", "data"),
    Input("record_visit_rcom_charts_cards", "data"),
    Input("record_visit_rcom_charts_diag_cards", "data"),
    Input("record_visit_rcom_alarm_log", "data"),
    # Input("record_visit_rcom_8", "data"),
    # Input("record_visit_rcom_9", "data"),
    # Input("record_visit_rcom_10", "data"),
    prevent_initial_call=True,
)
def record_visit_rcom_multiple_outputs(
    record_visit_rcom_1_data: str,
    record_visit_rcom_2_data: str,
    record_visit_rcom_3_data: str,
    record_visit_rcom_4_data: str,
    record_visit_rcom_5_data: str,
    record_visit_rcom_6_data: str,
    record_visit_rcom_7_data: str,
    # record_visit_rcom_8_data: str,
    # record_visit_rcom_9_data: str,
    # record_visit_rcom_10_data: str,
) -> str:
    """Multiple outputs pattern for recording user visits to certain RCOM tabs"""

    id_triggered: str = get_id_triggered()
    rcom_page = id_triggered.split("record_visit_")[1]
    rcom_page = rcom_page.split(".data")[0]
    if not rcom_page.startswith("rcom_"):
        raise ValueError(
            f"ERROR getting rcom_page ({rcom_page}) from id_triggered ({id_triggered}). Must start with 'rcom_'"
        )
    record_visit(user_id=getattr(current_user, "id", None), page=rcom_page)
    raise PreventUpdate()
