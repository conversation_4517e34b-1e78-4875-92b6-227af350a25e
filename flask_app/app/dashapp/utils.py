import inspect
import json
import logging
import os
import re
import time
from datetime import datetime
from functools import reduce
from itertools import groupby
from numbers import Number
from operator import add
from pathlib import Path
from types import SimpleNamespace
from typing import Any, Callable, List, Tuple, Union
from urllib import parse

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import joblib
import numpy as np
import pandas as pd
import pytz
import requests
from botocore.exceptions import ClientError
from colour import Color
from dash import callback_context, html
from flask import current_app, url_for
from flask_login import current_user
from google.cloud import recaptchaenterprise_v1
from google.cloud.recaptchaenterprise_v1 import Assessment
from markupsafe import Markup
from pandas import DataFrame
from shared.models.models import (
    Customer,
    Gw,
    PowerUnit,
    StructureVw,
    StructureVwAllFiltered,
    TimeZone,
    UnitType,
    structure_customer_rel,
)
from sklearn.preprocessing import MinMaxScaler
from sklearn.utils.validation import check_is_fitted
from sqlalchemy import func, text

from app import (
    cache_memoize_if_prod,
    db,
    get_user_cust_ids,
    user_is_ijack_employee,
)
from app.config import (
    ADMIN_EMAILS,
    CUST_SUB_GROUP_ID_ALL_GROUPS,
    CUST_SUB_GROUP_ID_ALL_OTHERS,
    CUSTOMER_ID_ALL_CUSTOMERS,
    CUSTOMER_ID_DEMO,
    CUSTOMER_ID_IJACK_CORP,
    CUSTOMER_ID_IJACK_INC,
    MODEL_TYPE_ID_ALL_TYPES,
    STRUCTURE_ID_GATEWAYS,
    TAB_UNOGAS_UNO,
    UNIT_TYPE_ID_ALL_TYPES,
    UNIT_TYPE_ID_BOOST,
    UNIT_TYPE_ID_DGAS,
    UNIT_TYPE_ID_EGAS,
    UNIT_TYPE_ID_GATEWAYS,
    UNIT_TYPE_ID_UNO,
    UNIT_TYPE_ID_UNOGAS,
    UNIT_TYPE_ID_VRU,
    UNIT_TYPE_ID_XFER,
    WAREHOUSE_ID_ALL_WAREHOUSES,
    unit_type_upper_dict,
)
from app.dashapp.metrics import BOOTSTRAP_BLUE_500
from app.databases import get_boto3_client, get_sqla_query_string, run_sql_query
from app.email_stuff import send_email
from app.models.models import Structure, User
from app.utils.recaptcha import RecaptchaResponse, recaptcha
from shared.utils.datetime_utils import utcnow_naive


@cache_memoize_if_prod(60)
def get_customers_rows(user_id: int) -> list:
    """
    Get a list of customer 'models'
    This function is cached based on the user provided (unique cache for each user_id)
    """
    has_roles = hasattr(current_user, "roles_rel")
    if not has_roles:
        msg = f"User '{current_user}' has no roles assigned! No units will be shown."
        current_app.logger.critical(msg)
        raise AttributeError(msg)

    sql = None
    user_customer_ids: tuple = get_user_cust_ids(user_id=user_id)
    if user_is_ijack_employee(
        user_id=getattr(current_user, "id", None), user_cust_ids=user_customer_ids
    ):
        # User is IJACK employee, so can see all customers
        sql = text(
            """
            select distinct
                t2.customer_id as id,
                t3.customer,
                lower(t4.country_code) as country_code,
                case when t4.country_code = 'CA' then 0 else 1 end as sort_order
            from public.structures t1
            inner join public.structure_customer_rel t2
                on t2.structure_id = t1.id
            inner join public.customers t3
                on t3.id = t2.customer_id
            inner join public.countries t4
                on t4.id = t3.country_id
            where customer_id is distinct from 21 -- demo customer
            order by
                -- Show Canada first, then the rest
                case when t4.country_code = 'CA' then 0 else 1 end,
                country_code,
                t3.customer
        """
        )

    else:
        # User can see "customer" power_units only
        sql = text(
            """
            select distinct
                t2.customer_id as id,
                t3.customer,
                lower(t4.country_code) as country_code
            from public.structures t1
            inner join public.structure_customer_rel t2
                on t2.structure_id = t1.id
            inner join public.customers t3
                on t3.id = t2.customer_id
            inner join public.countries t4
                on t4.id = t3.country_id
            where t2.customer_id in :customer_ids
            order by country_code, t3.customer
        """
        ).bindparams(customer_ids=user_customer_ids)

    if sql is None:
        return []
    else:
        rows_list, _ = run_sql_query(sql, db_name="ijack")

    return rows_list


def get_warehouse_options(customer_id: int) -> list:
    """Get a list of warehouse options"""

    sql_str = """
        select
            distinct on (warehouse_name)
            warehouse_name as label,
            warehouse_id as value
        from public.vw_structures_joined
        where warehouse_id is not null
            {}
        order by warehouse_name
    """
    if customer_id == CUSTOMER_ID_ALL_CUSTOMERS:
        # IJACK users can see all warehouses, but no sense showing the demo customer stuff
        sql_text = text(sql_str.format(" and customer_id is distinct from 21"))
    else:
        sql_str = sql_str.format(" and customer_id = :customer_id")
        params = {"customer_id": customer_id}
        sql_text = text(sql_str).bindparams(**params)

    rows_list, _ = run_sql_query(sql_text, db_name="ijack")
    rows_list.insert(
        0, {"label": "All Warehouses", "value": WAREHOUSE_ID_ALL_WAREHOUSES}
    )

    return rows_list


# This function is cached based on the user provided (unique cache for each user)
@cache_memoize_if_prod(60)
def get_customer_sub_groups(customer_id: int, warehouse_id: int) -> pd.DataFrame:
    """Get a Pandas DataFrame of customer sub-groups for the given customer ID and warehouse ID"""

    current_app.logger.debug(
        "Querying public.cust_sub_groups for customer sub-groups..."
    )

    # with conn.cursor() as cursor:
    sql_str: str = """
        select distinct
            cust_sub_group_id,
            case when abbrev is null then cust_sub_group_name else abbrev end as cust_sub_group_abbrev
        from (
            select
                case when t31.cust_sub_group_id is null then :all_others_value else t31.cust_sub_group_id end as cust_sub_group_id,
                case when t31.cust_sub_group_id is null then 'All Others' else t3.abbrev end as abbrev,
                case when t31.cust_sub_group_id is null then 'All Others' else t3.name end as cust_sub_group_name
            from public.structures t1
            --many-to-many between structures and customers
            inner join public.structure_customer_rel t2
                on t2.structure_id = t1.id
            --Need a left join to gather structures with no sub-groups, for the "All Others" category
            LEFT JOIN public.structure_cust_sub_group_rel t31
                on t31.structure_id = t1.id
            LEFT JOIN public.cust_sub_groups t3
                ON t3.id = t31.cust_sub_group_id
            where t1.unit_type_id is not null
                and t1.surface is not null
                and t1.structure_install_date is not null
                {}
        ) a1
        order by cust_sub_group_abbrev
    """

    params: dict = {"all_others_value": CUST_SUB_GROUP_ID_ALL_OTHERS}
    filters: str = ""
    if customer_id != CUSTOMER_ID_ALL_CUSTOMERS:
        # Filter on the customer ID
        filters += " and t2.customer_id = :customer_id"
        params["customer_id"] = customer_id

    if warehouse_id != WAREHOUSE_ID_ALL_WAREHOUSES:
        # Filter on warehouse ID
        filters += " and t1.warehouse_id = :warehouse_id"
        params["warehouse_id"] = warehouse_id

    sql_str = sql_str.format(filters)
    sql = text(sql_str).bindparams(**params)
    rows, columns = run_sql_query(sql, db_name="ijack")

    # Put the "All Others" category at the end
    rows_all_others_last = []
    all_others_row = {}
    for row in rows:
        if row["cust_sub_group_id"] == CUST_SUB_GROUP_ID_ALL_OTHERS:
            all_others_row = row
        else:
            rows_all_others_last.append(row)
    if all_others_row:
        rows_all_others_last.append(all_others_row)

    df = pd.DataFrame(rows_all_others_last, columns=columns)
    # current_app.logger.debug("DataFrame queried in 'get_customer_sub_groups()' function:")
    # current_app.logger.debug(f"df.head(): \n{df.head()}")

    return df


# This function is cached based on the user provided (unique cache for each user)
@cache_memoize_if_prod(timeout=60)
def get_structures_rows_cols(
    user_id: int,
    customer_id: str = None,
    warehouse_id: str = None,
    cust_sub_group_id: str = None,
    unit_type_ids: list = None,
    model_type_ids: list = None,
    am_i_worried: bool = None,
    min_days_since_reported: int = None,
    max_days_since_reported: int = None,
) -> Tuple[list, list]:
    """Get the rows and columns of structures the user is allowed to see"""

    sql_str: str = ""
    bind_params: dict = {}
    user_customer_ids: tuple = get_user_cust_ids(user_id=user_id)
    if not user_customer_ids:
        return [], []

    if user_is_ijack_employee(user_id=user_id, user_cust_ids=user_customer_ids):
        # User is IJACK, so can see all power_units
        sql_str = """
            select
                id, structure,
                gateway_id, gateway,
                power_unit_id, power_unit, power_unit_str,
                case when customer is null then 'IJACK' else customer end as customer,
                case when customer_id is null then 1 else customer_id end as customer_id,
                case when cust_sub_group_id is null then 0 else cust_sub_group_id end as cust_sub_group_id,
                case when cust_sub_group is null then '' else cust_sub_group end as cust_sub_group,
                unit_type_id, unit_type,
                model_type_id, model, max_delta_p, model_unit_type_id,
                model_type_id_slave, model_unit_type_id_slave,
                location, gps_lat, gps_lon,
                am_i_worried, days_since_reported,
                warehouse_id, warehouse_name,
                website_card_msg
            --Don't use vw_structures_all_filtered since that filters out IJACK units
            from public.vw_structures_all
            where
                customer_id is distinct from 21 -- demo customer
                and unit_type_id is not null
                and surface is not null
                and structure_install_date is not null
        """
        if customer_id and str(customer_id) != str(CUSTOMER_ID_ALL_CUSTOMERS):
            sql_str += " and customer_id = :customer_id"
            bind_params["customer_id"] = customer_id

    else:
        # User can see their "customer" power_units only
        sql_str = """
            select
                id, structure,
                gateway_id, gateway,
                power_unit_id, power_unit, power_unit_str,
                customer_id, customer,
                case when cust_sub_group_id is null then 0 else cust_sub_group_id end as cust_sub_group_id,
                case when cust_sub_group is null then '' else cust_sub_group end as cust_sub_group,
                unit_type_id, unit_type,
                model_type_id, model, max_delta_p, model_unit_type_id,
                model_type_id_slave, model_unit_type_id_slave,
                location, gps_lat, gps_lon,
                am_i_worried, days_since_reported,
                warehouse_id, warehouse_name,
                website_card_msg
            --Don't use vw_structures_all_filtered since that filters out IJACK units
            from public.vw_structures_all
            where
                --This might be the demo customer
                customer_id in :customer_ids
                and unit_type_id is not null
                and surface is not null
                and structure_install_date is not null
        """
        bind_params["customer_ids"] = user_customer_ids

    if am_i_worried in (True, False):
        sql_str += " and am_i_worried = :am_i_worried"
        bind_params["am_i_worried"] = am_i_worried

    if warehouse_id and str(warehouse_id) != str(WAREHOUSE_ID_ALL_WAREHOUSES):
        sql_str += " and warehouse_id = :warehouse_id"
        bind_params["warehouse_id"] = warehouse_id

    if cust_sub_group_id and str(cust_sub_group_id) != str(
        CUST_SUB_GROUP_ID_ALL_OTHERS
    ):
        sql_str += " and cust_sub_group_id = :cust_sub_group_id"
        bind_params["cust_sub_group_id"] = cust_sub_group_id

    if unit_type_ids and str(UNIT_TYPE_ID_ALL_TYPES) not in unit_type_ids:
        sql_str += " and unit_type_id in :unit_type_ids"
        if isinstance(unit_type_ids, (int, str)):
            unit_type_ids = [unit_type_ids]
        bind_params["unit_type_ids"] = tuple(unit_type_ids)

    if model_type_ids and str(MODEL_TYPE_ID_ALL_TYPES) not in model_type_ids:
        sql_str += " and model_type_id in :model_type_ids"
        if isinstance(model_type_ids, (int, str)):
            model_type_ids = [model_type_ids]
        bind_params["model_type_ids"] = tuple(model_type_ids)

    if min_days_since_reported:
        sql_str += " and days_since_reported >= :min_days_since_reported"
        bind_params["min_days_since_reported"] = min_days_since_reported

    if max_days_since_reported:
        sql_str += " and days_since_reported <= :max_days_since_reported"
        bind_params["max_days_since_reported"] = max_days_since_reported

    sql = text(sql_str).bindparams(**bind_params)
    # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
    #     # sql_text = sql.compile(compile_kwargs={"literal_binds": True}).string
    #     try:
    #         sql_text = sql.compile(compile_kwargs={"literal_binds": True}).string
    #         current_app.logger.debug(f"SQL: {sql_text}")
    #     except Exception as e:
    #         current_app.logger.debug(f"SQL: {e}")

    rows_list, columns = run_sql_query(sql, db_name="ijack")
    return rows_list, columns


@cache_memoize_if_prod(60)
def get_gateways_unused(user_id: int) -> list:
    """
    Get list of unused gateways.
    This function is cached based on the user provided (unique cache for each user_id)
    """
    sql = text(
        """
            select id, gateway, timestamp_utc_inserted, location_gw, model_gw
            from public.gw
            where power_unit_id is null
            order by timestamp_utc_inserted, model_gw, location_gw
        """
    )
    rows_list, _ = run_sql_query(sql, db_name="ijack")

    return rows_list


# If we memoize this, the associated instances in the
# structure_obj won't be available from the cached version...
# @cache_memoize_if_prod(60)
def get_unit_type(tab_uno_egas: int, unit_type_id: int = None) -> Tuple[str, int]:
    """
    Given a structure object and the active tab for UNO/EGAS,
    return the lowercase unit type. This check is needed for UNOGAS types in particular
    """
    if not unit_type_id:
        return "", None

    unit_type_lower = str(unit_type_upper_dict.get(unit_type_id, "")).lower()

    if unit_type_id == UNIT_TYPE_ID_UNOGAS:
        if tab_uno_egas == TAB_UNOGAS_UNO:
            unit_type_lower = "uno"
            unit_type_id = UNIT_TYPE_ID_UNO
        else:
            unit_type_lower = "egas"
            unit_type_id = UNIT_TYPE_ID_EGAS

    return unit_type_lower, unit_type_id


def get_struct_obj_not_demo_customer(
    structure: float = None,
    structure_id: int = None,
    power_unit: float = None,
    power_unit_id: float = None,
    gateway: str = None,
) -> StructureVw | None:
    """We need to hide the "demo" customer whose ID is 21"""
    if all(
        [
            not structure,
            not structure_id,
            not power_unit,
            not power_unit_id,
            not gateway,
        ]
    ):
        current_app.logger.error(
            "ERROR: All of these are not viable (i.e. None-like): structure, structure_id, power_unit, power_unit_id, and gateway. Returning None"
        )
        return None

    struct_obj = None
    filters = [StructureVw.customer_id != CUSTOMER_ID_DEMO]
    if structure:
        filters.append(StructureVw.structure == structure)
    elif structure_id:
        filters.append(StructureVw.id == structure_id)
    elif power_unit:
        filters.append(StructureVw.power_unit == float(power_unit))
    elif power_unit_id:
        filters.append(StructureVw.power_unit_id == float(power_unit_id))
    elif gateway:
        filters.append(StructureVw.gateway == gateway)

    struct_obj = db.session.query(StructureVw).filter(*filters).first()

    return struct_obj


def get_stored_structure_unit(
    store_structure_dict: dict | None, store_customer_id: str | int
) -> Tuple[float, float]:
    """Get the structure_id and unit_type_id from the JSON-stored dictionary by customer"""
    if not isinstance(store_structure_dict, dict):
        current_app.logger.warning(
            f"store_structure_dict is not a dictionary. It's a '{type(store_structure_dict)}' and looks like '{store_structure_dict}'"
        )
        return None, None

    cust_dict = store_structure_dict.get(str(store_customer_id), {})
    if isinstance(cust_dict, int):
        return cust_dict, None

    structure_id = cust_dict.get("structure_id", None)
    unit_type_id = cust_dict.get("unit_type_id", None)

    if structure_id is not None:
        structure_id = float(structure_id)
    if unit_type_id is not None:
        unit_type_id = float(unit_type_id)

    return structure_id, unit_type_id


def copy_object(object) -> SimpleNamespace:
    """
    Make a SimpleNamespace copy of an object (SQLAlchemy model, typically),
    but without any methods
    """
    sn = SimpleNamespace()
    for key, val in object.__dict__.items():
        if str(key).startswith("_"):
            continue
        setattr(sn, key, val)
    return sn


# This doesn't change very often, unless we're updating the GPS on-the-fly!
@cache_memoize_if_prod(timeout=10)
def get_structure_obj(
    structure_id: float, user_id: int, unit_type_id: float = None
) -> StructureVw | None:
    """
    Given a structure_id name, query for the entire row of data.
    Must provide a user_id so cache_memoize_if_prod will store on a per-user basis.
    """
    if not isinstance(structure_id, Number):
        current_app.logger.debug(
            "struct_obj '%s' of type '%s' not an integer. Returning None",
            structure_id,
            type(structure_id),
        )
        # raise PreventUpdate()
        return None

    if unit_type_id == UNIT_TYPE_ID_GATEWAYS:
        # It's actually a gateway, not a structure, so fill in
        # some missing data in the structure_obj.
        # Several gateways may be returned, so grab the first.
        struct_obj = (
            db.session.query(StructureVw).filter_by(id=STRUCTURE_ID_GATEWAYS).first()
        )
        # In this case, the structure_id is actually the gateway_id

        # Make a copy of the structure so we can update its properties
        struct_obj_copy = copy_object(struct_obj)

        # Delete this so we don't actually set values on it, which will result in an error
        # when SQLAlchemy tries to update the database with the new values, and it's a view!
        del struct_obj

        struct_obj_copy.gateway_id = structure_id
        # Several gateways may be returned, so grab the first.
        # Note the structure MUST have a power unit ID because of the view join
        gateway_obj = Gw.query.filter_by(id=structure_id).first()
        if gateway_obj:
            setattr(struct_obj_copy, "gateway", gateway_obj.gateway)
            setattr(struct_obj_copy, "aws_thing", gateway_obj.aws_thing)
            setattr(struct_obj_copy, "power_unit_id", gateway_obj.power_unit_id)
            # setattr(struct_obj_copy, "power_unit", gateway_obj.power_unit)
            setattr(struct_obj_copy, "surface", gateway_obj.location_gw)
            # setattr(struct_obj_copy, "apn", gateway_obj.apn)
        return struct_obj_copy

    user_cust_ids: tuple = get_user_cust_ids(user_id=user_id)
    filters: list = [StructureVw.id == structure_id]
    if user_is_ijack_employee(
        user_id=getattr(current_user, "id", None), user_cust_ids=user_cust_ids
    ):
        # User is an IJACK employee, so can see all structures.
        # We need to hide the "demo" customer.
        filters.append(StructureVw.customer_id != CUSTOMER_ID_DEMO)
    else:
        # User can see "customer" structures only.
        # This might include the "demo" customer.
        filters.append(StructureVw.customer_id.in_(user_cust_ids))

    struct_obj = db.session.query(StructureVw).filter(*filters).first()
    if struct_obj is None:
        current_app.logger.warning(
            f"ERROR: struct_obj is None. Can't find structure_id {structure_id}. User ID: {user_id}. User Customer IDs: {user_cust_ids}. Unit Type ID: {unit_type_id}"
        )

    return struct_obj


def get_all_structures(
    user: User,
    search: str = None,
    unit_type_ids: list = (
        UNIT_TYPE_ID_BOOST,
        UNIT_TYPE_ID_DGAS,
        UNIT_TYPE_ID_EGAS,
        UNIT_TYPE_ID_UNO,
        UNIT_TYPE_ID_UNOGAS,
        UNIT_TYPE_ID_VRU,
        UNIT_TYPE_ID_XFER,
    ),
    ijack_units_allowed: bool = False,
) -> list:
    """Get all the structures the user can see"""

    user_id: int = getattr(user, "id", None)
    user_customer_ids: tuple = get_user_cust_ids(user_id=user_id)
    is_ijack_employee: bool = user_is_ijack_employee(
        user_id=user_id, user_cust_ids=user_customer_ids
    )
    # is_demo_customer: bool = CUSTOMER_ID_DEMO in user_customer_ids

    filters: list = [
        Structure.unit_type_id.isnot(None),
        Structure.unit_type_id.in_(unit_type_ids),
    ]
    if is_ijack_employee:
        # No demo units
        filters.append(structure_customer_rel.c.customer_id != CUSTOMER_ID_DEMO)
    # elif is_demo_customer:
    #     filters.append(structure_customer_rel.c.customer_id == CUSTOMER_ID_DEMO)
    elif user_customer_ids:
        # Only customer's own units
        filters.append(structure_customer_rel.c.customer_id.in_(user_customer_ids))

    if not ijack_units_allowed:
        # Don't show IJACK-owned units
        filters.append(
            ~structure_customer_rel.c.customer_id.in_(
                [CUSTOMER_ID_IJACK_CORP, CUSTOMER_ID_IJACK_INC]
            )
        )

    if search:
        filters.append(Structure.structure_str.ilike(f"%{search}%"))
        filters.append(PowerUnit.power_unit_str.ilike(f"%{search}%"))

    results = (
        db.session.query(
            Structure.id,
            PowerUnit.power_unit_str,
            Structure.structure_str,
            Structure.unit_type_id,
            Structure.gps_lat,
            Structure.gps_lon,
            Structure.surface,
            Structure.time_zone_id,
            UnitType.unit_type,
            Customer.id.label("customer_id"),
            Customer.customer,
        )
        .join(PowerUnit, Structure.power_unit_id == PowerUnit.id, isouter=True)
        .join(UnitType, Structure.unit_type_id == UnitType.id, isouter=True)
        .join(
            structure_customer_rel,
            Structure.id == structure_customer_rel.c.structure_id,
            isouter=True,
        )
        .join(
            Customer, structure_customer_rel.c.customer_id == Customer.id, isouter=True
        )
        .filter(*filters)
        # .distinct(Structure.id)
        .order_by(UnitType.unit_type)
        .order_by(PowerUnit.power_unit)
        .all()
    )

    structures: list = []
    for row in results:
        structures.append(
            {
                "id": row.id,
                "power_unit_str": row.power_unit_str,
                "structure_str": row.structure_str,
                "unit_type_id": row.unit_type_id,
                "unit_type": row.unit_type,
                "customer_id": row.customer_id,
                "customer": row.customer,
                "gps_lat": row.gps_lat,
                "gps_lon": row.gps_lon,
                "surface": row.surface,
                "time_zone_id": row.time_zone_id,
            }
        )

    return structures


def list_named_shadows(aws_thing: str, client_iot=None) -> list:
    """List the named shadows for a given thing"""
    if client_iot is None:
        try:
            client_iot = get_boto3_client(service_name="iot-data")
        except Exception:
            current_app.logger.exception("ERROR getting client_iot")
            return []
    response = client_iot.list_named_shadows_for_thing(thingName=aws_thing)
    results: list = response.get("results", [])
    return results


# Don't cache this for a long time since we want the latest device shadow
@cache_memoize_if_prod(2)
def get_iot_device_shadow(aws_thing: str, shadow_name: str = None) -> dict:
    """This function gets the current thing state"""

    try:
        client_iot = get_boto3_client(service_name="iot-data")
    except Exception:
        current_app.logger.exception("No client_iot so returning empty dict")
        return {}
    response_payload = {}

    if not aws_thing:  # No RCOM on DGAS units
        current_app.logger.warning("No aws_thing so returning empty dict")
        return {}

    try:
        if shadow_name:
            # Named shadow wanted
            named_shadows: list = list_named_shadows(aws_thing, client_iot=client_iot)
            if not named_shadows:
                # current_app.logger.warning("No named shadows for aws_thing '%s'", aws_thing)
                return {}
            response = client_iot.get_thing_shadow(
                thingName=aws_thing, shadowName=shadow_name
            )
        else:
            response = client_iot.get_thing_shadow(thingName=aws_thing)

        streamingBody = response["payload"]
        response_payload = json.loads(streamingBody.read())
    except ClientError as err:
        # This happens too often
        # if err.response["Error"]["Code"] == "ResourceNotFoundException":
        #     current_app.logger.error(
        #         "ResourceNotFoundException: There is no shadow named '%s'",
        #         shadow_name,
        #     )
        # else:
        current_app.logger.error("Unexpected error: %s", err)
    except Exception:
        current_app.logger.exception("ERROR! Probably no shadow exists...")

    return response_payload


# @cache_memoize_if_prod(3)
def update_shadow(new_state_dict: dict, aws_thing: str) -> dict:
    """
    This function updates the current thing state. Requires a dictionary and a thing name.

    The idea behind caching this update_shadow function is so we only
    update the shadow (e.g. request a refresh from the gateway) every five seconds.
    However, if the user issues a command within five seconds of a gateway refresh,
    we must be careful to let that command get through. Therefore, it will only be cached if
    the "new_state_dict" command dictionary, and "aws_thing" are exactly the same,
    so it's safe to cache it.
    """
    # Topic: $aws/things/{aws_thing}/shadow/update
    # payload = {'state': {'desired': {'property': state}}}
    if aws_thing is None:
        return new_state_dict

    current_app.logger.debug(
        "Updating thing shadow; aws_thing: %s; new_state_dict: %s",
        aws_thing,
        new_state_dict,
    )
    JSON_payload = json.dumps(new_state_dict)
    response_payload = new_state_dict  # Initialize it in case the below doesn't get run

    # if os.getenv("FLASK_CONFIG", "development") in ("development", "wsl", "testing"):
    #     aws_thing = "lambda_access"  # test AWS thing

    # if current_user.can_set or permissions_override:
    # Initialize AWS client for IoT
    try:
        client_iot = get_boto3_client(service_name="iot-data")
    except Exception:
        current_app.logger.exception("No client_iot!")
        raise

    try_counter: int = 0
    response: dict = {}
    while try_counter <= 10:
        try_counter += 1
        try:
            response = client_iot.update_thing_shadow(
                thingName=aws_thing, payload=JSON_payload
            )
        except client_iot.exceptions.ConflictException:
            # just a version conflict, likely, so try again more often than for other exceptions
            current_app.logger.exception(
                f"ERROR trying to update the shadow for aws_thing '{aws_thing}'"
            )
            response = client_iot.update_thing_shadow(
                thingName=aws_thing, payload=JSON_payload
            )
        except Exception:
            # Log the error and let it try again
            current_app.logger.exception(
                f"ERROR trying to update the shadow for aws_thing '{aws_thing}'"
            )
        else:
            break

    streaming_body = response["payload"]
    response_payload = json.loads(streaming_body.read())

    return response_payload


def get_shadow_delta(shadow):
    """
    Manually check the device shadow for deltas between desired and reported.
    """
    desired = shadow.get("state", {}).get("desired", {})
    reported = shadow.get("state", {}).get("reported", {})

    shadow_delta = {"state": {}}
    for key, desired_value in desired.items():
        reported_value = reported.get(key, "")
        if reported_value != desired_value:
            shadow_delta["state"][key] = desired_value

    current_app.logger.info(f"shadow_delta: {shadow_delta}")

    if shadow_delta["state"] != {}:
        # set_targets(bus)
        pass

    return shadow_delta


def seconds_since_last_any_msg(shadow) -> tuple:
    """
    How many seconds has it been since we received ANY message from the gateway at AWS,
    and which metric was it?
    """
    time_received_latest = 0
    key_latest = None
    meta_reported = shadow.get("metadata", {}).get("reported", {})
    for key, _ in meta_reported.items():
        # metadata contains the timestamps for each attribute in the desired and reported sections so that you can determine when the state was updated
        if (
            "wait_okay" in key  # alerts sent flags
            # or key == 'connected' # AWS Lambda updates this for the last will and testament
            or key.startswith(
                "AWS_"
            )  # commands from AWS are not okay since it includes the desired state...
            or key.startswith("C__")  # config data, which is refreshed periodically
            # Latitude and longitude can be updated by the website itself, if the unit is selected!
            or key == "LATITUDE"
            or key == "LONGITUDE"
            or key == "connected"
        ):
            continue

        meta_reported_sub_dict = meta_reported.get(key, {})
        if not isinstance(meta_reported_sub_dict, dict):
            continue

        time_received = meta_reported_sub_dict.get("timestamp", 0)
        if time_received > time_received_latest:
            time_received_latest = time_received
            key_latest = key

            # ####################
            # # For debugging only
            # # Convert to a datetime
            # since_when_time_received = datetime.fromtimestamp(time_received_latest, timezone.utc)
            # # Get the timedelta since we started waiting
            # time_delta_time_received = (datetime.now(timezone.utc) - since_when_time_received)
            # # How many seconds has it been since we started waiting?
            # seconds_elapsed_total = time_delta_time_received.days*24*60*60 + time_delta_time_received.seconds
            # current_app.logger.debug(f"Most recent metric in AWS IoT device shadow: {key_latest} as of {round(seconds_elapsed_total/60, 1)} minutes ago")
            # current_app.logger.debug("")

    # How many seconds has it been since we started waiting?
    seconds_elapsed_total = round(time.time() - time_received_latest, 0)

    current_app.logger.debug(
        "Most recent metric in AWS IoT device shadow: %s as of %s minutes ago",
        key_latest,
        round(seconds_elapsed_total / 60, 1),
    )

    return seconds_elapsed_total, key_latest


def time_since_friendly_string(seconds):
    """
    Takes a value in seconds, and returns a string like the following examples:
    8 seconds, 2 minutes, 6 hours, 4 days
    """
    if seconds < 60:
        return f"{seconds} seconds"
    elif seconds < 60 * 60:
        return f"{round(seconds / 60, 1)} minutes"
    elif seconds < 60 * 60 * 24:
        return f"{round(seconds / (60 * 60), 1)} hours"

    return f"{round(seconds / (60 * 60 * 24), 1)} days"


def get_id_triggered() -> str:
    """Get the ID that triggered the callback"""
    if not callback_context.triggered:
        return ""

    # This changed a bit when I updated the Dash version... Be careful
    return callback_context.triggered[0]["prop_id"]


def log_function_caller(index: int = 1) -> None:
    """Log which function we're in, and which function called it"""
    # Set this manually
    want_log_function_caller: bool = False
    if want_log_function_caller and current_app.config["LOG_LEVEL"] == logging.DEBUG:
        # if current_app.logger.isEnabledFor(logging.DEBUG):
        inspect_stack = inspect.stack()
        id_triggered: str = get_id_triggered()
        current_app.logger.debug(
            # "%s -> %s -> %s: %s",
            "%s -> %s: %s",
            # inspect_stack[index + 1].function,
            id_triggered,
            inspect_stack[index].filename.split("/")[-1],
            inspect_stack[index].function,
        )

    # ctx = callback_context
    # current_app.logger.debug("ctx.triggered: %s", ctx.triggered)
    # current_app.logger.debug("id_triggered: %s", id_triggered)
    return None


def parse_url(url_search: str) -> Tuple[str, float]:
    """
    Get the type of unit (e.g. power_unit, structure_id)
    and unit ID (structure_id or power_unit) as a float from the URL search query
    """
    unit_type = None
    unit_id = None

    query_str = parse.urlsplit(url_search).query
    query_dict = parse.parse_qs(query_str)
    for key, list_ in query_dict.items():
        try:
            unit_id = list_[0]
            # unit_id = float(unit_id)
        except Exception as err:
            current_app.logger.debug(
                "ERROR getting unit ID from url_search '%s'.\n%s", url_search, err
            )
        is_power_unit = str(unit_id).startswith("200")
        if key == "power_unit" or is_power_unit:
            unit_type = "power_unit"
        elif key == "power_unit_id":
            unit_type = "power_unit_id"
        elif key in ("unit", "structure_id"):
            unit_type = "structure_id"
        elif key == "structure":
            unit_type = "structure"
        elif key == "gateway":
            unit_type = "gateway"

    return unit_type, unit_id


def get_value_from_query_dict(query_dict: dict, key: str) -> str:
    """Get the value from the query dict"""
    value_list: list = query_dict.get(key, None)
    if isinstance(value_list, (list, tuple)):
        value = value_list[0]
    else:
        value = value_list
    return str(value)


def query_cluster_id_description_solution(surface_or_compression):
    """
    Query for the cluster_id/pattern_id map to get from 200 random clusters to ~24 patterns
    """
    if surface_or_compression == "compression":
        ml_version = current_app.config["ML_VERSION_EGAS"]
    else:
        ml_version = current_app.config["ML_VERSION_UNO"]

    images_table = f"public.{surface_or_compression}_images"
    patterns_table = f"public.{surface_or_compression}_patterns"
    sql = text(
        f"""
SELECT
    t1.cluster_id,
    t2.description,
    t2.solution,
    t2.explanation,
    t2.send_alert
FROM {images_table} t1
LEFT JOIN {patterns_table} t2
    on t2.id = t1.pattern_id
where t1.ml_version = :ml_version
ORDER BY cluster_id ASC
    """
    ).bindparams(ml_version=ml_version)
    rows, columns = run_sql_query(sql, db_name="ijack")

    # Build a DataFrame with the results
    return pd.DataFrame(rows, columns=columns)


@cache_memoize_if_prod(timeout=60)
def get_card_data(
    start_date: str,
    end_date: str,
    surface_or_compression="surface",
    power_unit_str: str = None,
) -> pd.DataFrame:
    """Query the TimescaleDB database and load data into Pandas DataFrame for machine learning or charting"""

    current_app.logger.info(
        f"Querying public.{surface_or_compression} for all records in the last 14 days..."
    )
    sql = text(
        f"""
        select
            --Ensure we have 24 distinct hours of data for each day,
            --for the Plotly-Dash animations
            time_bucket_gapfill('1 hour', t1.timestamp_utc) as timestamp_utc,
            t1.power_unit,
            t1.position,
            t1.is_up,
            --Special TimescaleDB "last one carried forward"
            --(i.e. forward-fill through time, on the group by columns)
            locf(avg(t1.load)) as load
        from public.{surface_or_compression} t1
        where
            --Dates are in UTC, and we need at least 24 hours per day for each animation frame
            --timestamp_utc >= ((TIMESTAMP 'today') - INTERVAL '15 days')
            --and timestamp_utc <= ((TIMESTAMP 'today') + INTERVAL '2 days')
            timestamp_utc >= :start_date
            and timestamp_utc <= :end_date
            and power_unit = :power_unit_str
        group by
            time_bucket_gapfill('1 hour', t1.timestamp_utc),
            t1.power_unit, t1.position, t1.is_up
        order by
            t1.power_unit,
            time_bucket_gapfill('1 hour', t1.timestamp_utc)
    """
    ).bindparams(
        power_unit_str=power_unit_str,
        start_date=start_date,
        end_date=end_date,
    )

    time_start = time.time()
    try:
        # cursor.execute(sql)
        rows, columns = run_sql_query(sql, db_name="timescale")
    except Exception:
        current_app.logger.exception("ERROR querying for card data!")
        return None

        # rows = cursor.fetchall()
    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} seconds to run surface card query"
    )
    # columns = [str.lower(x[0]) for x in cursor.description]

    if len(rows) == 0:
        current_app.logger.warning(
            f"No {surface_or_compression} card data in database..."
        )
        return None

    # Build a DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)

    df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])
    # current_app.logger.debug(f"df['timestamp_utc'].unique(): {df['timestamp_utc'].unique()}")

    # Convert from int64 to float for the pivot_table function (otherwise it throws an error)
    df["position"] = df["position"].astype(float)
    df["load"] = df["load"].astype(float)

    df = df.sort_values(["power_unit", "timestamp_utc", "is_up", "position"])
    # current_app.logger.debug(f"{surface_or_compression} df and sorted. df.tail(20): \n{df.tail(20)}")

    # Do something about the zeros (missing loads at various positions of each stroke).
    # Replace the zeros with nulls (np.nan)
    df["load"] = df["load"].replace(to_replace=0, value=np.nan)

    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} total seconds to gather and transform surface card data"
    )

    return df


@cache_memoize_if_prod(timeout=60)
def get_diag_card_data(
    start_date: str, end_date: str, power_unit_str: str = None
) -> pd.DataFrame:
    """Query the TimescaleDB database and load data into Pandas DataFrame for machine learning or charting"""

    current_app.logger.info(
        "Querying public.diagnostic for all records in the last 14 days..."
    )
    sql = text(
        """
        select
            --Ensure we have 24 distinct hours of data for each day,
            --for the Plotly-Dash animations
            time_bucket_gapfill('1 hour', t1.timestamp_utc) as timestamp_utc,
            t1.power_unit_str as power_unit,
            t1.is_main,
            t1.msg_type as input_velocity,
            t2.name as metric,
            --Special TimescaleDB "last one carried forward"
            --(i.e. forward-fill through time, on the group by columns)
            locf(avg(t1.value)) as value
        from public.diagnostic t1
        left join public.diagnostic_metrics t2
            on t1.diag_num = t2.diag_num
        where
            --Dates are in UTC, and we need at least 24 hours per day for each animation frame
            timestamp_utc >= :start_date
            and timestamp_utc <= :end_date
            and power_unit_str = :power_unit_str
            --101 is the checksum and 102 is for time series data
            and t1.msg_type < 101
        group by
            time_bucket_gapfill('1 hour', t1.timestamp_utc),
            t1.power_unit_str,
            t1.is_main,
            t1.msg_type,
            t2.name
        order by
            t1.power_unit_str,
            t1.is_main,
            t2.name,
            t1.msg_type,
            time_bucket_gapfill('1 hour', t1.timestamp_utc)
    """
    ).bindparams(
        power_unit_str=power_unit_str,
        start_date=start_date,
        end_date=end_date,
    )

    time_start = time.time()
    try:
        rows, columns = run_sql_query(sql, db_name="timescale")
    except Exception:
        current_app.logger.exception("ERROR querying for card data!")
        return None

    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} seconds to run surface card query"
    )

    if len(rows) == 0:
        current_app.logger.warning("No diagnostic card data in database...")
        return None

    # Build a DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)

    df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])
    # current_app.logger.debug(f"df['timestamp_utc'].unique(): {df['timestamp_utc'].unique()}")

    # Convert from int64 to float for the pivot_table function (otherwise it throws an error)
    df["value"] = df["value"].astype(float)

    df = df.sort_values(
        ["power_unit", "timestamp_utc", "is_main", "input_velocity", "metric"]
    )
    # current_app.logger.debug(f"diagnostic df and sorted. df.tail(20): \n{df.tail(20)}")

    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} total seconds to gather and transform diagnostic card data"
    )

    return df


@cache_memoize_if_prod(timeout=60)
def get_card_meta_data(
    start_date: str,
    end_date: str,
    surface_or_compression: str = "surface",
    power_unit: str = None,
):
    """Query the TimescaleDB database and load data into Pandas DataFrame for charting"""

    current_app.logger.info(
        f"Querying public.{surface_or_compression}_meta for all records in the last 14 days..."
    )
    if surface_or_compression == "surface":
        btm_position = "BS_LAG_REAL"
        stroke_length = "STROKE_LENGTH"
        stroke_count = "StrokeCount"
        fillage = "CurrentFillage"
        spm_x10 = "SPM_X10"
    else:
        btm_position = "BTM_POS_E"
        stroke_length = "STROKE_LENGTH_E"
        stroke_count = "StrokeCountE"
        # fillage doesn't actually exist for EGAS/XFER types
        # DERATE_R and DERATE_L are where CurrentFillageE used to be
        fillage = "CurrentFillageE"
        spm_x10 = "SPM_X10E"

    sql_meta = text(
        f"""
            select
                time_bucket_gapfill('1 hour', timestamp_utc) as timestamp_utc,
                power_unit,
                --Special TimescaleDB "last one carried forward"
                --(i.e. forward-fill through time, on the group by columns)
                locf(avg(case when metric = :bp then value else null end)) as btm_position,
                locf(avg(case when metric = :sl then value else null end)) as stroke_length,
                locf(avg(case when metric = :sc then value else null end)) as stroke_count,
                locf(avg(case when metric = :fl then value else null end)) as fillage,
                locf(avg(case when metric = :spm_x10 then value else null end)) as spm_x10
            from public.{surface_or_compression}_meta
            where
                --Dates are in UTC, and we need at least 24 hours per day for each animation frame
                --timestamp_utc >= ((TIMESTAMP 'today') - INTERVAL '15 days')
                --and timestamp_utc <= ((TIMESTAMP 'today') + INTERVAL '2 days')
                timestamp_utc >= :start_date
                and timestamp_utc <= :end_date
                and power_unit = :pu
            group by
                time_bucket_gapfill('1 hour', timestamp_utc),
                power_unit
                --metric
            order by
                time_bucket_gapfill('1 hour', timestamp_utc),
                power_unit
        """
    ).bindparams(
        bp=btm_position,
        sl=stroke_length,
        sc=stroke_count,
        fl=fillage,
        pu=power_unit,
        spm_x10=spm_x10,
        start_date=start_date,
        end_date=end_date,
    )
    # cursor.execute(sql_meta)
    # rows = cursor.fetchall()
    # columns = [str.lower(x[0]) for x in cursor.description]
    rows, columns = run_sql_query(sql_meta, db_name="timescale")

    if len(rows) == 0:
        current_app.logger.warning(
            f"No {surface_or_compression} card metadata in database..."
        )
        return None

    # Build a DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)

    df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])

    # Convert from int64 to float for the pivot_table function (otherwise it throws an error)
    df["btm_position"] = df["btm_position"].astype(float)
    df["stroke_length"] = df["stroke_length"].astype(float)
    df["stroke_count"] = df["stroke_count"].astype(float)
    df["fillage"] = df["fillage"].astype(float)
    df["spm_x10"] = df["spm_x10"].astype(float)

    # If values are missing, it's because they were the same as the previous values so they weren't sent
    df = df.sort_values("timestamp_utc").infer_objects().ffill().bfill()
    # df = df.sort_values(["timestamp_utc"]).ffill()

    # If there are still null values (e.g. old PLC software), remove those rows
    # But if we do this, the animation might not have all hours for each day, causing problems/inconsistencies...
    # df = df.dropna()

    return df


@cache_memoize_if_prod(timeout=60, args_to_ignore=["df"])
def card_data_pivot_and_fill(
    df: pd.DataFrame,
    power_unit_str: str = None,
    ensure_24_hours_first_day: bool = False,
) -> pd.DataFrame:
    """Pivot the long data to wide format for filling null values with previous values"""

    time_start = time.time()

    # Transpose the data so each stroke is a row, so we can forward-fill and backfill the data
    df = df.pivot_table(
        index=["power_unit", "timestamp_utc"],
        columns=["is_up", "position"],
        values="load",
    )

    if ensure_24_hours_first_day:
        # Make the first day start at hour zero, so the cards chart has minimum 24 hours
        first_timestamp_hour_zero = df.reset_index()["timestamp_utc"].min().floor("D")
        last_timestamp = df.reset_index()["timestamp_utc"].max()

        if pd.isnull(first_timestamp_hour_zero):
            current_app.logger.error(
                f"first_timestamp_hour_zero is null: {first_timestamp_hour_zero}"
            )
            return None
        elif pd.isnull(last_timestamp):
            current_app.logger.error(f"last_timestamp is null: {last_timestamp}")
            return None

        try:
            data_range = pd.date_range(
                first_timestamp_hour_zero, last_timestamp, freq="h"
            )
        except ValueError:
            current_app.logger.exception(
                f"ERROR creating pd.date_range with start={first_timestamp_hour_zero} and end={first_timestamp_hour_zero}"
            )
            return None

        dates = pd.DataFrame(
            data=data_range,
            columns=["timestamp_utc"],
        )
        dates["power_unit"] = power_unit_str
        dates = dates.set_index(["power_unit", "timestamp_utc"])
        # The following removes the warning: "merging between different levels is deprecated
        # and will be removed in a future version. Use a unique column instead"
        dates.columns = pd.MultiIndex.from_product([dates.columns, [""]])
        df = df.merge(dates, how="right", left_index=True, right_index=True)

    # If values are missing, it's because they were the same as the previous values so they weren't sent
    # df = df.groupby(level=0).ffill()
    df = (
        df.sort_values(["power_unit", "timestamp_utc"])
        .groupby("power_unit")
        .ffill()
        .bfill()
    )

    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} total seconds to pivot to wide format, and fill in missing values"
    )

    return df


@cache_memoize_if_prod(timeout=60, args_to_ignore=["df"])
def diag_card_data_pivot_and_fill(
    df: pd.DataFrame,
    power_unit_str: str = None,
    ensure_24_hours_first_day: bool = False,
) -> pd.DataFrame:
    """Pivot the long data to wide format for filling null values with previous values"""

    time_start = time.time()

    # Transpose the data so each stroke is a row, so we can forward-fill and backfill the data
    df = df.pivot_table(
        index=["power_unit", "timestamp_utc"],
        columns=["input_velocity", "metric"],
        values="value",
    )

    if ensure_24_hours_first_day:
        # Make the first day start at hour zero, so the cards chart has minimum 24 hours
        first_timestamp_hour_zero = df.reset_index()["timestamp_utc"].min().floor("D")
        last_timestamp = df.reset_index()["timestamp_utc"].max()

        if pd.isnull(first_timestamp_hour_zero):
            current_app.logger.error(
                f"first_timestamp_hour_zero is null: {first_timestamp_hour_zero}"
            )
            return None
        elif pd.isnull(last_timestamp):
            current_app.logger.error(f"last_timestamp is null: {last_timestamp}")
            return None

        try:
            data_range = pd.date_range(
                first_timestamp_hour_zero, last_timestamp, freq="h"
            )
        except ValueError:
            current_app.logger.exception(
                f"ERROR creating pd.date_range with start={first_timestamp_hour_zero} and end={first_timestamp_hour_zero}"
            )
            return None

        dates = pd.DataFrame(
            data=data_range,
            columns=["timestamp_utc"],
        )
        dates["power_unit"] = power_unit_str
        # dates['dummy_col'] = 1
        dates = dates.set_index(["power_unit", "timestamp_utc"])
        # The following removes the warning: "merging between different levels is deprecated
        # and will be removed in a future version. Use a unique column instead"
        dates.columns = pd.MultiIndex.from_product([dates.columns, [""]])
        df = df.merge(dates, how="right", left_index=True, right_index=True)
        # df = df.drop(columns='dummy_col', axis=1)

    # If values are missing, it's because they were the same as the previous values so they weren't sent
    # df = df.groupby(level=0).ffill()
    df = (
        df.sort_values(["power_unit", "timestamp_utc"])
        .groupby("power_unit")
        .ffill()
        .bfill()
    )

    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} total seconds to pivot to wide format, and fill in missing values"
    )

    return df


# @cache_memoize_if_prod(60, args_to_ignore=["df"])
def card_data_wide_to_long(
    df: pd.DataFrame,
    # First level of the column index
    variable_0: str = "is_up",
    # Second level of the column index
    variable_1: str = "position",
    y_axis_var: str = "load",
) -> pd.DataFrame:
    """Take the wide data suitable for machine learning and convert it to long data for charting"""

    time_start = time.time()

    # Re-normalize the data to vertical (long, not wide)
    # df = df.melt(id_vars=["power_unit", "timestamp_utc"])
    df = df.melt(
        # Column(s) to use as identifier variables
        # id_vars=[("power_unit", ""), ("timestamp_utc", "")]
        value_name=y_axis_var,
        # If False, the original index is retained
        ignore_index=False,
    )
    # Move timestamp_utc and power_unit to the columns (from the index)
    df = df.reset_index()
    df = df.rename(
        columns={
            # First level of the column index
            "variable_0": variable_0,
            # Second level of the column index
            "variable_1": variable_1,
        }
    )

    # # The below filters are redundant because they remove zeros, but there are no zeros anymore, right?
    # # Either load_up or load_down must be different from zero
    # current_app.logger.debug(f"df.tail(20): \n{df.tail(20)}")
    # # filters = (np.abs(df[f'{y_axis_var}_down']) > 0) | (np.abs(df[f'{y_axis_var}_up']) > 0)
    # filters = np.abs(df[y_axis_var]) > 0
    # df = df.loc[filters, :]
    # current_app.logger.debug(f"df.tail(20): \n{df.tail(20)}")

    # # Count points available by hour by position (sometimes there's only one point).
    # # This is just for logging purposes, to see how many points we have per hour.
    # df["points_per_hour"] = df.groupby(["power_unit", "timestamp_utc"])[
    #     variable_1
    # ].transform(lambda x: x.count())
    # current_app.logger.debug(f"df.tail(20): \n{df.tail(20)}")

    # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
    #     # Print how many points per row, by hour and power_unit
    #     df_to_print = df.groupby(["power_unit", "timestamp_utc"]).mean()
    #     df_to_print = df_to_print[df_to_print["points_per_hour"] <= 10]
    #     current_app.logger.debug(
    #         f"Rows removed where 'points_per_hour' <= 10: \n{df_to_print.tail(20)}"
    #     )

    # If we remove hours with only a few data points, we won't have 24 hours in a day
    # for the animation, which will cause problems/inconsistencies.
    # # Only keep hours with at least this many data points.
    # # We've already forward-filled the missing values.
    # df = df.loc[df["points_per_hour"] > 10, :]
    # if len(df) == 0:
    #     current_app.logger.warning(
    #         "WARNING: there are fewer than 10 points per hour, so the dataframe is empty"
    #     )

    # current_app.logger.debug(
    #     f"{surface_or_compression} DataFrame queried and cached. df.tail(20): \n{df.tail(20)}"
    # )

    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} total seconds to convert wide data to long data"
    )

    return df


def scale_and_predict(
    df: pd.DataFrame,
    map_df: pd.DataFrame,
    structure_obj,
    surface_or_compression: str,
    power_unit_str: str,
) -> pd.DataFrame:
    if True:
        # # the groupby object we'll be working on
        # g1 = df.groupby('power_unit')

        # Doesn't seem to work...
        # df['position'] = g1[['position']].transform(lambda x: scale_pos.fit_transform(x))
        # df['load'] = g1[['load']].transform(lambda x: scaler_load.fit_transform(x))

        # Import the pre-trained load scalers
        ml_path = Path(__file__).absolute().parent.joinpath("ml")
        if surface_or_compression == "compression":
            #  in ("egas", "xfer"):
            scaler_file = ml_path.joinpath(
                f"scaler_load_egas_v{current_app.config['ML_VERSION_EGAS']}.gz"
            )
        else:
            scaler_file = ml_path.joinpath(
                f"scaler_load_uno_v{current_app.config['ML_VERSION_UNO']}.gz"
            )

        # New units might not have custom scalers, and can use default ones instead
        # power_unit_name_no_colons = str(power_unit).replace(':', '.')
        is_scaler_already_fit = True
        try:
            scaler_load = joblib.load(scaler_file)
        except Exception:
            is_scaler_already_fit = False
            scaler_load = MinMaxScaler(feature_range=(0, 1), copy=True)
            # if unit_type_lower in ("egas", "xfer"):
            #     scaler_load = MinMaxScaler(feature_range=(0, 1), copy=True)
            # else:
            #     scaler_load = StandardScaler(copy=True, with_mean=True, with_std=True)

        current_app.logger.debug("Scaling load for machine learning classification...")
        # this needs a 2D array (i.e. DataFrame, not Series)
        if is_scaler_already_fit:
            assert check_is_fitted(scaler_load) is None, "scaler_load is not fitted!"
            assert scaler_load.data_max_[0] != 1.0, (
                "WARNING: No scaling will be done since the fitted parameter max is 1.!"
            )
            # New property clip was added to MinMaxScaler in later version (since 0.24).
            # Becase clip is defined in __init__ method it is part of MinMaxScaler.__dict__.
            # When you try to create object from pickle __setattr__ method is used to set all attributues,
            # but clip was not used in older version therefore is missing in your new MinMaxScale instance.
            # https://stackoverflow.com/questions/65635575/attributeerror-minmaxscaler-object-has-no-attribute-clip
            scaler_load.clip = False
            df["load"] = scaler_load.transform(df[["load"]])
            current_app.logger.debug(
                "it worked even with the old version of scikit-learn!"
            )
        else:
            # Must fit a new scaler, and then transform the features
            df["load"] = scaler_load.fit_transform(df[["load"]])

        # # For ad hoc charting of the card, for troubleshooting
        # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        #     chart_card_ad_hoc(df, datetime(2021, 3, 25, 9), structure_obj, surface_or_compression)

        # for n, g in df.groupby('power_unit'):
        # count the strokes as well?
        # df.loc[g.index, 'stroke_count'] = ((g['up_down'].shift(1) == 'u') & (g['up_down'] == 'd')).cumsum() + 1
        # df.loc[g.index, 'load'] = scaler_load.transform(g[['load']])

        # if unit_type_lower not in ('egas', 'xfer'):
        #     # Only UNO x-axis data were scaled
        #     # (totally unnecessary mistake in clustering step)
        #     # What matters is the number of x-axis features for the matrix, not their values
        #     try:
        #         scale_pos = joblib.load(scaler_path.joinpath(f'scaler_position_{power_unit_name_no_colons}.gz'))
        #     except Exception:
        #         scale_pos = MinMaxScaler(feature_range=(0, 1), copy=True)
        #     df.loc[g.index, 'position'] = scale_pos.transform(g[['position']])
        #     df['position'] = scale_pos.transform(df['position'])

        # Pivot from long to wide and fill in missing values.
        df = card_data_pivot_and_fill(df, power_unit_str)
        if df is None:
            return None

        _, cols = df.shape
        if cols != 214:
            # Check if we can fix the UNO units, which are missing 4.0 and 216.0
            # on both the upstroke and the downstroke
            if cols == 210:
                for true_false in (True, False):
                    if (true_false, 6.0) in df.columns and (
                        true_false,
                        4.0,
                    ) not in df.columns:
                        # Copy position 6 to position 4 (easy, practical fix)
                        df[true_false, 4.0] = df[true_false, 6.0]
                    if (true_false, 214.0) in df.columns and (
                        true_false,
                        216.0,
                    ) not in df.columns:
                        # Copy position 214 to position 216 (easy, practical fix)
                        df[true_false, 216.0] = df[true_false, 214.0]

            # Check again after the above tried to fix things
            if df.shape[1] != 214:
                current_app.logger.error(
                    f"df.shape needs to be (x, 214) and it's {df.shape}. Exiting function"
                )
                # return return_none
                return None

        # Put power_unit and timestamp_utc back to columns
        df = df.reset_index()

        # Remove future hours (which are there so the surface card chart has 24 hours for each frame)
        current_dt: datetime = utcnow_naive()
        df = df[df["timestamp_utc"] <= current_dt]

        # Convert to local time
        df["timestamp_local"] = (
            df["timestamp_utc"]
            .dt.tz_localize(pytz.utc)
            .dt.tz_convert(structure_obj.time_zone)
        )

        df = df.drop(columns="timestamp_utc")

        df = df.set_index(["power_unit", "timestamp_local"])

        # # Set the index like the machine learning model expects it
        # df = df.index.set_names(['power_unit', 'date_hour_utc'])

        # Load the PCA (principal components analysis) pre-trained model,
        # and the SVC (support vector machines classifier) pre-trained model
        # transformer = None  # PCA, etc
        if surface_or_compression == "compression":
            # if unit_type_lower in ("egas", "xfer"):
            # pca = joblib.load(ml_path.joinpath('pca_egas.gz'))
            # estimator = joblib.load(ml_path.joinpath('svc_trained_model_egas.gz'))
            estimator_path = ml_path.joinpath(
                f"2_k_means_egas_v{current_app.config['ML_VERSION_EGAS']}.gz"
            )
            estimator = joblib.load(estimator_path)
        else:
            # transformer = joblib.load(
            #     ml_path.joinpath(f"pca_v{current_app.config['ML_VERSION_UNO']}.gz")
            # )
            estimator_path = ml_path.joinpath(
                f"2_k_means_uno_v{current_app.config['ML_VERSION_UNO']}.gz"
            )
            estimator = joblib.load(estimator_path)

        # Quick chart for sanity check

        # Aggregate to daily average to get only one classification label per day?
        # (we'll just classify and report each day's average card for now)
        # df = df.groupby([df.index.get_level_values(0), pd.Grouper(freq='D', level=1)]).mean()
        # current_app.logger.debug(f"\ndf daily average: \n{df}")
        X = df.values
        # print(X)

        # # Transform with PCA and then predict the labels using the SVC model
        # if transformer is None:
        #     X_transformed = X
        # else:
        #     try:
        #         X_transformed = transformer.transform(X)
        #     except ValueError:
        #         current_app.logger.exception(
        #             "Problem with PCA transformation! Displaying nothing."
        #         )
        #         return return_none

        pred = estimator.predict(X)
        # current_app.logger.debug(f"\nPredicted labels/cluster IDs: \n{pred}")

        # if unit_type_lower in ("egas", "xfer"):
        # DataFrame of the predicted labels (no X-data)
        df2 = pd.DataFrame(pred, index=df.index, columns=["cluster_id"]).reset_index()

        # Merge the tables
        df2 = df2.merge(map_df, on="cluster_id").sort_values(
            "timestamp_local", ascending=False
        )

        return df2


def verify_return_tuple(return_tuple, n_needed):
    """Assert the return tuple has the correct number of items"""
    n_len = len(return_tuple)
    assert n_len == n_needed, (
        f"Return tuple has wrong number of items. Expecting {n_needed}. Got {n_len}."
    )
    return return_tuple


def get_or_update_gps(
    structure_obj: StructureVw | None = None,
    structure_id: int | None = None,
    reason: str | None = None,
) -> Tuple[float, float]:
    """
    Get the GPS latitude and longitude from the StructureVw.
    If null or defaulted to the IJACK shop GPS, attempt to update
    the database from http://www.legallandconverter.com/ using their API.
    """

    def return_variables(lat_return, lon_return):
        """Default return variables"""
        return lat_return, lon_return

    if structure_obj is None and structure_id is None:
        current_app.logger.error("Must provide either structure_obj or structure_id!")
        return return_variables(lat_return=None, lon_return=None)
    elif structure_obj is None:
        structure_obj = db.session.get(StructureVw, structure_id)
        if structure_obj is None:
            current_app.logger.error(f"Can't find structure with ID {structure_id}!")
            return return_variables(lat_return=None, lon_return=None)

    unit_info: str = ""
    try:
        lat = getattr(structure_obj, "gps_lat", None)
        lon = getattr(structure_obj, "gps_lon", None)
        surface = getattr(structure_obj, "surface", None)
        aws_thing = getattr(structure_obj, "aws_thing", None)

        if lat is None and lon is None and surface is None:
            return return_variables(lat_return=None, lon_return=None)

        customer = getattr(structure_obj, "customers_rel", None)
        power_unit = getattr(structure_obj, "power_unit_str", None)
        structure = getattr(structure_obj, "structure_str", None)
        structure_id = getattr(structure_obj, "id", None)

        lat_rounded, lon_rounded = None, None
        try:
            lat_rounded = round(lat, 4)
            lon_rounded = round(lon, 4)
        except Exception:
            current_app.logger.debug("Trouble rounding lat_rounded or lon_rounded")

        # Check if it's a candidate for updating from the API using the surface location,
        # or whether it already has a good lat/lon for GPS.
        is_ijack_shop: bool = lat_rounded == 50.1631 and lon_rounded == -101.6754
        already_has_good_lat_lon: bool = (
            isinstance(lat_rounded, float)
            and isinstance(lon_rounded, float)
            and lat_rounded
            and lon_rounded
            and not is_ijack_shop
        )
        if already_has_good_lat_lon or not surface:
            # Either already has a good lat/lon, or there's no surface location from which to update
            return return_variables(lat_return=lat, lon_return=lon)

        # Try getting it from the legallandconverter.com API.
        # Extract components from surface land location
        surface_upper = str(surface).upper().strip().replace(" ", "")
        unit_info = f"Customer {customer} structure '{structure}' (power unit '{power_unit}') with surface '{surface}'"

        dont_update_these = (
            "VIRDEN WAREHOUSE",
            "IJACK YARD",
            "Stand at SHOP",
        )
        if (
            surface_upper in dont_update_these
            or "-" not in surface_upper
            or "W" not in surface_upper
            # Structure IDs that don't work:
            # Kraken Matilda May 200579: 1148
            or getattr(structure_obj, "id", None) in (1148,)
        ):
            # Just return the current lat and lon since we can't update it
            return return_variables(lat_return=lat, lon_return=lon)

        current_app.logger.info(f"Getting GPS for {unit_info}")

        # no_spaces = surface_upper.replace(" ", "")
        # re.match("^[A][EU][_][0-9]{1,3}\\b", surface_upper)

        # Remove anything before the first "/" (e.g. "06-30/08-30-012-26w1")
        split_f_slash = surface_upper.split("/")[-1]

        # Remove anything after the "W1" (e.g. "W1 East Unit")
        split_f_slash_part_0 = split_f_slash.split(" ")[0]

        # Split off the meridian (e.g. W1 into "1")
        split_on_W = split_f_slash_part_0.split("W")
        if len(split_on_W) != 2:
            current_app.logger.warning(
                f"{unit_info} can't be split on the W meridian (e.g. W1). Continuing with next..."
            )
            # Just return the current lat and lon since we can't update it
            return return_variables(lat_return=lat, lon_return=lon)
        loc, meridian = split_on_W
        # Remove non-numbers from the meridian (e.g. "1" from "1E")
        meridian = re.sub("[^0-9]", "", meridian)
        w_meridian = f"W{meridian}"

        # Only keep digits and dashes (we'll deal with this later...)
        # digits = re.sub("[^0-9,-]", "", loc)

        split_dash = loc.split("-")

        # Convert to integers to remove leading zeros
        # nums = [int(x) for x in split_dash]
        nums = []
        # regex to find the first digit in each string
        regex = re.compile(r"^[^\d]*(\d+)")
        for x in split_dash:
            try:
                match = re.match(regex, x)
                group = match.group()
                int_ = int(group)
                # Remove letters at the end of the string
                x2 = re.sub(r"[a-zA-Z]$", "", x)
                # Remove letters at the beginning of the string
                x3 = re.sub(r"^[a-zA-Z]", "", x2)
                # # Remove all letters from the string
                # x3 = re.sub(r"[a-zA-Z]", "", x)
                # Remove everything after and including the first letter
                # x4 = x3.split("C")[0]
                x4 = re.sub(r"[a-zA-Z].*", "", x3)
                int_ = int(x4)
            except Exception:
                current_app.logger.warning(
                    f"Can't convert '{x}' from '{split_dash}' to integer!"
                )
                continue
            else:
                nums.append(int_)

        if len(nums) != 4:
            current_app.logger.warning(
                f"{unit_info} can't be split into four parts. Continuing with next..."
            )
            # Just return the current lat and lon since we can't update it
            return return_variables(lat_return=lat, lon_return=lon)

        quarter, section, township, range_ = nums

        if not os.getenv("LEGALLANDCONVERTER_USERNAME", None) or not os.getenv(
            "LEGALLANDCONVERTER_PASSWORD", None
        ):
            msg: str = "LEGALLANDCONVERTER_USERNAME or LEGALLANDCONVERTER_PASSWORD is not set in the environment!"
            current_app.logger.error(msg)
            send_email(
                subject=msg,
                text_body=msg,
                to_emails=ADMIN_EMAILS,
            )
            return return_variables(lat_return=lat, lon_return=lon)

        # API instructions here:
        # https://legallandconverter.com/p51.html#OVERVIEW
        # http://legallandconverter.com/cgi-bin/android5c.cgi?username=DEVELOPX&password=TEST1234&quarter=SW&section=24&township=12&range=20&meridian=W4&cmd=legal
        payload = dict(
            username=os.getenv("LEGALLANDCONVERTER_USERNAME", None),
            password=os.getenv("LEGALLANDCONVERTER_PASSWORD", None),
            # LSD number 1-16 or quarter of the section
            quarter=quarter,
            # 36 sections in township
            section=section,
            # township e.g. 1-127 going north (e.g. farm is 13 north of US border)
            township=township,
            # range e.g. 1-34 going west in the meridian (e.g. farm is 32 of 34 so almost in W2)
            range=range_,
            # meridian e.g. 1 MB, 2 SK, 3 AB (roughly)
            meridian=w_meridian,
            # Canadian DLS Query = "legal"
            cmd="legal",
        )

        api_url: str = "http://legallandconverter.com/cgi-bin/android5c.cgi"
        r = requests.get(api_url, params=payload)
        assert r.status_code == 200, "r.status_code != 200!"
        if "balance is zero" in r.text:
            # Email someone to top up the account! It's 10 cents per request
            subject = "legallandconverter.com balance is zero!"
            sender = "IJACK <<EMAIL>>"
            to_emails = ADMIN_EMAILS
            text_body = "No more funds in account! \n\nGo to http://www.legallandconverter.com/pro/buycredits.html to buy more!"
            if reason:
                text_body += f"\n\nReason for trying to update: {reason}"
            send_email(subject, sender, to_emails, text_body, testing=False)
            # Just return the current lat and lon since we can't update it
            return return_variables(lat_return=lat, lon_return=lon)

        # current_app.logger.debug(f"r.url: {r.url}")
        # current_app.logger.debug(f"r.content: {r.content}")
        # current_app.logger.debug(f"r.encoding: {r.encoding}")
        current_app.logger.debug("request.text: %s", r.text)

        new_latitude = None
        new_longitude = None
        for line in r.text.splitlines():
            line_upper = line.upper()

            if "LATITUDE" in line_upper:
                new_latitude = float(line_upper.split(": ")[1])
            elif "LONGITUDE" in line_upper:
                new_longitude = float(line_upper.split(": ")[1])
            elif "CREDITS: 0" in line_upper:
                current_app.logger.critical("No more credits in account!")
            elif "HAVECONVERSION: 0" in line_upper:
                current_app.logger.critical(f"Problem with conversion of {unit_info}!")
                # raise Exception
            else:
                continue

            if new_latitude and new_longitude:
                break
            # Text Returned:
            # Content-type: text/plain

            # STATUSBAR: Version 1.20 Available
            # LATITUDE: 50.008196
            # LONGITUDE: -112.614401
            # UTM: 12N 384323 5540791
            # MGRS: 12UUA8432240790
            # NTS: D-10-A/82-I-2
            # CREDITS: 29
            # LSD:
            # QUARTER: SW
            # QUARTERLSD: SW
            # USAQUARTER:
            # USASECTION:
            # USATOWNSHIP:
            # USANORTHSOUTH:
            # USARANGE:
            # USAEASTWEST:
            # USAMERIDIAN:
            # USASTATE:
            # COUNTRY: Canada
            # HAVECONVERSION: 1

        if isinstance(new_latitude, float) and isinstance(new_longitude, float):
            msg = f"RCOM website is updating GPS for {unit_info}:\nnew latitude: {new_latitude}; new longitude: {new_longitude}"
            current_app.logger.warning(msg)

            # Email someone since this is interesting and it doesn't happen very often.
            # Plus, it costs money!
            subject = f"RCOM website updating GPS for {unit_info}!"
            sender = "IJACK <<EMAIL>>"
            to_emails = ADMIN_EMAILS
            text_body = msg
            if reason:
                text_body += f"\n\nReason for trying to update: {reason}"
            send_email(subject, sender, to_emails, text_body, testing=False)

            sql_update = text(
                """
                update public.structures
                set
                    gps_lat = :new_latitude,
                    gps_lon = :new_longitude
                where id = :structure_id
            """
            ).bindparams(
                new_latitude=new_latitude,
                new_longitude=new_longitude,
                structure_id=structure_id,
            )
            run_sql_query(sql_update, db_name="ijack", commit=True)

            # Update the shadow as well, so it doesn't get
            # set back to its previous value by "PostgreSQL Scheduler"
            new_state_dict = {
                "state": {
                    "reported": {
                        "LATITUDE": new_latitude,
                        "LONGITUDE": new_longitude,
                    }
                }
            }
            try:
                update_shadow(new_state_dict, aws_thing)
            except Exception:
                current_app.logger.exception(
                    f"Problem updating shadow for {unit_info} with new latitude and longitude"
                )

            # Return the new latitude and longitude 👌
            return return_variables(lat_return=new_latitude, lon_return=new_longitude)

        msg = f"RCOM website spent money but can't update GPS for {unit_info}!\nnew latitude: {new_latitude}; new longitude: {new_longitude}\ntext: {text}"
        current_app.logger.warning(msg)
        # Email someone since this is interesting and it doesn't happen very often.
        # Plus, it costs money!
        subject = f"Can't update GPS for {unit_info}..."
        sender = "IJACK <<EMAIL>>"
        to_emails = ADMIN_EMAILS
        text_body = msg
        if reason:
            text_body += f"\n\nReason for trying to update: {reason}"
        send_email(subject, sender, to_emails, text_body, testing=False)

        # No new latitude and longitude
        return return_variables(lat_return=None, lon_return=None)
    except Exception as err:
        current_app.logger.exception("Problem getting GPS lat/lon")
        # Email someone since this is interesting and it doesn't happen very often.
        # Plus, it costs money!
        subject = f"ERROR updating GPS for {unit_info} via the website!"
        sender = "IJACK <<EMAIL>>"
        to_emails = ADMIN_EMAILS
        text_body = f"Error the myijack.com website experienced when someone looked at the 'Indicators' tab and it tried to update the GPS: \n{err}"
        if reason:
            text_body += f"\n\nReason for trying to update: {reason}"
        send_email(subject, sender, to_emails, text_body, testing=False)

    # No new latitude and longitude
    return return_variables(lat_return=None, lon_return=None)


def convert_to_int(value, default=None) -> int:
    """Convert a value to an integer, and default to something else if it fails"""
    try:
        return int(value)
    except (TypeError, ValueError):
        current_app.logger.warning(
            f"Trouble converting {value} of type {type(value)} to integer. Returning default of {default}"
        )
    except Exception:
        current_app.logger.exception(
            f"Trouble converting {value} of type {type(value)} to integer. Returning default of {default}"
        )
    return default


def convert_to_float(value, default=None) -> float | None:
    """Convert a value to an float, and default to something else if it fails"""
    try:
        return float(value)
    except (TypeError, ValueError):
        current_app.logger.warning(
            f"Trouble converting {value} of type {type(value)} to float. Returning default of {default}"
        )
    except Exception:
        current_app.logger.exception(
            f"Trouble converting {value} of type {type(value)} to float. Returning default of {default}"
        )
    return default


# @cache_memoize_if_prod(120)
def get_db_options(
    columns: list,
    table: str,
    where: str = "",
    schema: str = "public",
    # items_list is for the dropdown menus so it's got a label and a value.
    # If items_list is False, it will still return a list of dicts
    # due to the psycopg2 RealDictCursor cursor_factory.
    items_list: bool = True,
    as_dict: bool = False,
    # add_all_option: bool = False,
    ascending: bool = True,
    values_as_strings: bool = False,
) -> List[dict] | dict:
    """
    Get the database options list for the work order select dropdown menus
    """

    t1_cols: list = [f"t1.{col}" for col in columns]
    columns_str: str = ", ".join(t1_cols)
    sort_order: str = "asc" if ascending else "desc"
    # This query is not subject to SQL injection because customers have no input into the query
    sql = text(
        f"""
        select
            distinct on ({columns_str})
            t1.id, {columns_str}
        from {schema}.{table} t1
        {where}
        order by {columns_str} {sort_order}
    """
    )
    # Set echo to True to see the SQL query
    rows, _ = run_sql_query(sql, db_name="ijack", echo=False)

    if as_dict:
        return {str(dict_["id"]): dict_[columns[0]] for dict_ in rows}

    if items_list is False:
        # This requires the psycopg2 RealDictCursor cursor_factory so that it still returns a list of dicts.
        # Otherwise it's a list of lists with the psycopg2 DictCursor cursor_factory.
        return rows

    items_list = []
    for dict_ in rows:
        label: str = ""
        for col in columns:
            label = f"{label} {dict_[col]}"
        if values_as_strings:
            value = str(dict_["id"])
        else:
            value = dict_["id"]
        items_list.append({"label": label, "value": value})

    return items_list


def get_table_header(
    header: Union[bool, tuple, list, np.ndarray, pd.Index, dict], df: DataFrame
) -> list:
    """Get a header for the table"""
    if not header:
        return []

    if isinstance(header, (tuple, list, np.ndarray, pd.Index)):
        try:
            df.columns = header
        except ValueError:
            raise ValueError(
                "If specifying column names with a sequence, the number "
                "of names must exactly match the number of columns."
            )
    elif isinstance(header, dict):
        df = df.rename(columns=header)

    # Get the actual headers
    n_levels = df.columns.nlevels
    header_values = [
        list(df.columns.get_level_values(level)) for level in range(n_levels)
    ]

    # The sizes of consecutive header groups at each level
    header_spans = [
        [len(list(group)) for _, group in groupby(level_values)]
        for level_values in header_values
    ]

    # The positions of header changes for each level as an integer
    header_breaks = [
        [sum(level_spans[:i]) for i in range(1, len(level_spans) + 1)]
        for level_spans in header_spans
    ]

    # Include breaks from higher levels
    header_breaks = [
        sorted(set(reduce(add, header_breaks[:level])).union({0}))
        for level in range(1, n_levels + 1)
    ]

    # Go from header break positions back to cell spans
    header_spans = [
        reversed(
            [
                level_breaks[i] - level_breaks[i - 1]
                for i in range(len(level_breaks) - 1, 0, -1)
            ]
        )
        for level_breaks in header_breaks
    ]

    table_header = [
        html.Thead(
            [
                html.Tr(
                    children=[
                        html.Th(
                            header_values[level][pos],
                            colSpan=span,
                            className="sticky-top bg-white text-center",
                        )
                        for pos, span in zip(header_breaks[level], header_spans[level])
                    ]
                )
                for level in range(n_levels)
            ]
        )
    ]
    return table_header


class HighlightIf:
    """Hold information on which columns to highlight"""

    def __init__(self, column: str, bg_color: str, func: Callable):
        self.column = column
        self.bg_color = bg_color
        self.func = func

    def __call__(self, value: Any) -> bool:
        """Run the comparison function"""
        return self.func(value)

    def __repr__(self) -> str:
        return f"HighlightIf({self.column}, {self.bg_color})"


def get_rows_from_dataframe(
    df: DataFrame, highlight_ifs: None | List[HighlightIf] = None
) -> list:
    """
    Get the rows from a dataframe, and highlight them
    if certain columns contain certain values
    """
    n_rows = len(df)
    rows_list = []
    for row in range(n_rows):
        cols_list = []
        for col_num, col_name in enumerate(df.columns):
            value = df.iloc[row, col_num]
            style = {}

            if highlight_ifs is not None:
                for highlight_if in highlight_ifs:
                    if highlight_if.column == col_name:
                        if highlight_if(value):
                            style.update({"background-color": highlight_if.bg_color})

            cols_list.append(html.Td(value, style=style))
        rows_list.append(html.Tr(cols_list))

    return rows_list


def generate_table_from_df(
    df,
    float_format=None,
    columns=None,
    header=True,
    index=False,
    index_label=None,
    date_format=None,
    highlight_ifs: None | List[HighlightIf] = None,
    **table_kwargs,
):
    """
    Generate a Table component from a dataframe.

    Parameters
    ----------
    df : pandas.DataFrame
        DataFrame to render as a table.
    float_format : str, optional
        Format to use for floating point numbers.
    columns : sequence, optional
        Columns to render.
    header : boolean or list(str) or dict(str: str), optional
        Write out the column names. If a list of strings is given it is assumed
        to be aliases for the columns names (and hence must be the same length
        as df.columns). A dict can be passed to rename some columns, the format
        is {'<current_name>': '<new_name>'}. The dictionary need not have an
        entry for every column.
    index : boolean, optional
        Render the row names (index).
    index_label : str, optional
        Column label for index column if desired. If None is passed, but both
        header and index are True, then the index name is used.
    date_format : str, optional
        Format string for datetime objects.
    **table_kwargs : Keyword arguments
        Additional arguments to pass to the table component. See
        dash_bootstrap_components.Table for details.
    """

    if columns is not None:
        df = df.loc[:, columns]

    if float_format is not None:
        for c in df.select_dtypes(["float"]).columns:
            df[c] = df[c].map(lambda x: "{1:{0}}".format(float_format, x))

    if date_format is not None:
        for c in df.select_dtypes(["datetime"]).columns:
            df[c] = df[c].map(lambda x: x.strftime(date_format))

    if index:
        df = df.reset_index()
        if index_label is not None:
            df = df.rename(columns={"index": index_label})

    table = get_table_header(header, df)
    rows = get_rows_from_dataframe(df, highlight_ifs)
    table.append(html.Tbody(rows))

    return dbc.Table(table, **table_kwargs)


def discrete_background_color_bins(
    df: DataFrame,
    n_bins: int = 9,
    columns="all",
    reverse: bool = False,
    color_start: str = BOOTSTRAP_BLUE_500,
    is_ag_grid: bool = False,
) -> list:
    """For Dash DataTable, create discrete background color bins for numeric columns"""

    if df.empty:
        return []

    bounds = [i * (1.0 / n_bins) for i in range(n_bins + 1)]
    if columns == "all":
        if "id" in df:
            df_numeric_columns = df.select_dtypes("number").drop(["id"], axis=1)
        else:
            df_numeric_columns = df.select_dtypes("number")
    else:
        df_numeric_columns = df[columns]

    # Convert to float in case it's an object like Numeric
    df_numeric_columns = df_numeric_columns.astype("float")

    styles = []
    # legend = []
    for column_name, column_data in df_numeric_columns.items():
        df_max = column_data.max()
        df_min = column_data.min()
        ranges = [((df_max - df_min) * i) + df_min for i in bounds]

        color = Color(color_start)
        color_lum = Color(color_start)
        color_lum.luminance = 0.9
        color_list = list(color_lum.range_to(color, n_bins))
        if reverse:
            # Change the order of the colours
            color_list = color_list[::-1]

        for i in range(1, len(bounds)):
            min_bound = ranges[i - 1]
            max_bound = ranges[i]
            bg_color = str(color_list[i - 1])
            if reverse:
                color = "white" if i < len(bounds) / 2.0 else "inherit"
            else:
                color = "white" if i > len(bounds) / 2.0 else "inherit"

            if is_ag_grid:
                # Using Dash AG Grid
                condition = f"params.value >= {min_bound}"
                if i < len(bounds) - 1:
                    condition += f" && params.value < {max_bound}"

                styles.append(
                    {
                        "condition": condition,
                        "style": {
                            "backgroundColor": bg_color,
                            "color": color,
                            "text-align": "right",
                        },
                    }
                )
            else:
                # Using Dash DataTable
                filter_query = f"{{{column_name}}} >= {min_bound}"
                if i < len(bounds) - 1:
                    filter_query += f" && {{{column_name}}} < {max_bound}"

                styles.append(
                    {
                        "if": {
                            "filter_query": filter_query,
                            "column_id": column_name,
                        },
                        "backgroundColor": bg_color,
                        "color": color,
                    }
                )

            # legend.append(
            #     html.Div(
            #         style={"display": "inline-block", "width": "60px"},
            #         children=[
            #             html.Div(
            #                 style={
            #                     "backgroundColor": bg_color,
            #                     "borderLeft": "1px rgb(50, 50, 50) solid",
            #                     "height": "10px",
            #                 }
            #             ),
            #             html.Small(round(min_bound, 2), style={"paddingLeft": "2px"}),
            #         ],
            #     )
            # )

    return styles
    # return (styles, html.Div(legend, style={"padding": "5px 0 5px 0"}))


@cache_memoize_if_prod(timeout=60)
def get_structures_df_by_filter(
    customer_id: int,
    warehouse_id: int,
    cust_sub_group_id: int,
    unit_type_id: int,
    # If there's a specific structure ID, get that one
    structure_id: int | None = None,
) -> DataFrame:
    """Get a list of structures for the customer, based on the filters provided"""

    if structure_id:
        filters = [StructureVwAllFiltered.id == structure_id]
    else:
        # DGAS types don't have power units so this filter doesn't work for DGAS types
        # filters = [StructureVwAllFiltered.power_unit_id.isnot(None)]
        filters = []
        if str(customer_id) != str(CUSTOMER_ID_ALL_CUSTOMERS):
            filters.append(StructureVwAllFiltered.customer_id == customer_id)

        if str(warehouse_id) != str(WAREHOUSE_ID_ALL_WAREHOUSES):
            filters.append(StructureVwAllFiltered.warehouse_id == warehouse_id)

        if str(cust_sub_group_id) != str(CUST_SUB_GROUP_ID_ALL_GROUPS):
            filters.append(
                StructureVwAllFiltered.cust_sub_group_id == cust_sub_group_id
            )

        # if str(unit_type_id) == str(UNIT_TYPE_ID_UNOGAS):
        #     filters.append(
        #         StructureVwAllFiltered.unit_type_id.in_([UNIT_TYPE_ID_UNOGAS, UNIT_TYPE_ID_ALL_TYPES])
        #     )

        if str(unit_type_id) != str(UNIT_TYPE_ID_ALL_TYPES):
            filters.append(StructureVwAllFiltered.unit_type_id == unit_type_id)

    # get SQL query from SQLAlchemy
    structures_query = db.session.query(StructureVwAllFiltered).filter(*filters)
    sql: str = get_sqla_query_string(structures_query, log_level=None)
    result, columns = run_sql_query(text(sql), db_name="ijack", as_dict=True)
    df = pd.DataFrame(result, columns=columns)
    # Now remove the duplicates from the DataFrame based on structure_id
    df = df.drop_duplicates(subset=["structure_id"])
    return df


def create_assessment(
    project_id: str,
    recaptcha_site_key: str,
    token: str,
    recaptcha_action: str,
    user_ip_address: str,
    user_agent: str,
    ja3: str,
) -> Assessment:
    """Create an assessment to analyze the risk of a UI action.
    Args:
        project_id: GCloud Project ID
        recaptcha_site_key: Site key obtained by registering a domain/app to use recaptcha services.
        token: The token obtained from the client on passing the recaptchaSiteKey.
        recaptcha_action: Action name corresponding to the token.
        user_ip_address: IP address of the user sending a request.
        user_agent: User agent is included in the HTTP request in the request header.
        ja3: JA3 associated with the request.
    """

    client = recaptchaenterprise_v1.RecaptchaEnterpriseServiceClient(credentials=None)

    # Set the properties of the event to be tracked.
    event = recaptchaenterprise_v1.Event()
    event.site_key = recaptcha_site_key
    event.token = token
    event.user_ip_address = user_ip_address
    event.user_agent = user_agent
    event.ja3 = ja3

    assessment = recaptchaenterprise_v1.Assessment()
    assessment.event = event

    project_name = f"projects/{project_id}"

    # Build the assessment request.
    request = recaptchaenterprise_v1.CreateAssessmentRequest()
    request.assessment = assessment
    request.parent = project_name

    response = client.create_assessment(request)

    # Check if the token is valid.
    if not response.token_properties.valid:
        print(
            "The CreateAssessment call failed because the token was "
            + "invalid for for the following reasons: "
            + str(response.token_properties.invalid_reason)
        )
        return

    # Check if the expected action was executed.
    if response.token_properties.action != recaptcha_action:
        print(
            "The action attribute in your reCAPTCHA tag does"
            + "not match the action you are expecting to score"
        )
        return
    else:
        # Get the risk score and the reason(s)
        # For more information on interpreting the assessment,
        # see: https://cloud.google.com/recaptcha-enterprise/docs/interpret-assessment
        for reason in response.risk_analysis.reasons:
            print(reason)
        print(
            "The reCAPTCHA score for this token is: "
            + str(response.risk_analysis.score)
        )
        # Get the assessment name (id). Use this to annotate the assessment.
        assessment_name = client.parse_assessment_path(response.name).get("assessment")
        print(f"Assessment name: {assessment_name}")
    return response


def recaptcha_is_good(recaptcha_clientside_response: str, expected_action: str) -> bool:
    """For Dash, get the recaptcha response from the client side"""

    if (
        recaptcha_clientside_response is None
        or str(recaptcha_clientside_response).strip() == ""
    ):
        return False

    try:
        result: RecaptchaResponse = recaptcha.verify_token(
            token=recaptcha_clientside_response, expected_action=expected_action
        )
        current_app.logger.info(f"Recaptcha result: {result}")
        return result.success
    except Exception:
        current_app.logger.exception("Problem with recaptcha response")
        return False


def get_work_order_link(row: pd.Series) -> str | Markup:
    """Return a Markdown link to the work order in a Dash DataTable"""
    id_ = getattr(row, "id", None)
    is_quote: bool | None = getattr(row, "is_quote", None)
    text: str = "Work Order Quote" if is_quote else "Work Order"

    if id_:
        text = f"{text} {id_}"
        if row.creator_company_id == CUSTOMER_ID_IJACK_CORP:
            if is_quote:
                endpoint = "work_order_quotes_corp"
            else:
                endpoint = "work_orders_corp"
        else:
            if is_quote:
                endpoint = "work_order_quotes"
            else:
                endpoint = "work_orders"
        url: str = url_for(f"{endpoint}.index_view", flt1_work_order_id_equals=id_)
        if row.creator_company_id == CUSTOMER_ID_IJACK_INC:
            return Markup(f'<a href="{url}" target="_blank">{text}</a>')
        elif row.creator_company_id == CUSTOMER_ID_IJACK_CORP:
            return Markup(f'<a href="{url}" target="_blank">{text}</a>')
    return ""


def get_datetime_from_parts(
    date_str: str, time_str: str, time_zone_id: int
) -> datetime | None:
    """Get a datetime object from the date and time strings"""

    if not time_zone_id:
        current_app.logger.error("No time zone ID provided!")
        return None

    time_zone_model = db.session.get(TimeZone, time_zone_id)
    if not time_zone_model:
        current_app.logger.error(f"Can't find time zone with ID {time_zone_id}!")
        return None

    time_zone_in_pytz = time_zone_model.to_pytz()
    # The following sometimes raises "ValueError: NaTType does not support time"
    try:
        date_obj = pd.to_datetime(date_str).date()
        time_obj = pd.to_datetime(time_str).time()
    except ValueError:
        current_app.logger.error(
            f"Can't convert date '{date_str}' or time '{time_str}' to datetime!"
        )
        return None

    date_time = datetime(
        year=date_obj.year,
        month=date_obj.month,
        day=date_obj.day,
        hour=time_obj.hour,
        minute=time_obj.minute,
        second=time_obj.second,
        # Don't use this, because it doesn't work!
        # tzinfo=time_zone_in_pytz,
    )
    # This is the correct way to do it, using the localize method
    date_time_w_tz = time_zone_in_pytz.localize(date_time)

    return date_time_w_tz


@cache_memoize_if_prod(timeout=60)
def get_users_by_customer_id(
    customer_id: int | None, all_except_this_customer: bool = False
) -> list:
    """Get the users for the customer"""
    if all_except_this_customer:
        filters = [User.customer_id != customer_id]
    else:
        filters = [User.customer_id == customer_id]

    users = (
        # Join by main customer ID
        User.query.join(Customer, User.customer_id == Customer.id)
        .with_entities(
            User.first_name,
            User.last_name,
            User.phone,
            User.email,
            User.id,
            Customer.customer,
        )
        .filter(
            *filters,
            # The user might've approved or requested work orders in the past
            # User.is_active.is_(True),
            func.trim(User.first_name) != "",
            func.trim(User.last_name) != "",
            User.first_name.isnot(None),
            User.last_name.isnot(None),
        )
        .order_by(User.first_name, User.last_name)
        .all()
    )
    return users


def dmc_notification(
    message: str,
    title: str = "Notification",
    color: str = "red",
    loading: bool = False,
    action: str = "show",
    icon: str = "\u2713",
    position: str = "top-center",
    autoClose: int = 5_000,
) -> dmc.Notification:
    """Create a Dash Mantine Components notification"""
    return dmc.Notification(
        title=title,
        message=message,
        color=color,
        loading=loading,
        action=action,
        # Checkmark symbol 2713 or X symbol 2717
        icon=icon,
        # icon=DashIconify(icon="akar-icons:circle-check"),
        position=position,
        autoClose=autoClose,
    )
