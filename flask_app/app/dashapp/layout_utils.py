from datetime import date, timedelta

import dash_bootstrap_components as dbc
from dash import dcc, html
from dash_ag_grid import AgGrid


def horizontal_line_with_word(word: str = "Or"):
    """Horizontal line with word in the middle"""
    return dbc.Row(
        justify="center",  # aligns the content horizontally
        align="center",  # aligns the content vertically
        class_name="my-4",
        children=[
            dbc.Col(
                html.Hr(),
                xs=4,
                lg=5,
            ),
            dbc.Col(word, style={"text-align": "center"}, xs=4, lg=2),
            dbc.Col(
                html.Hr(),
                xs=4,
                lg=5,
            ),
        ],
    )


def get_card_header_with_spinner(
    graph_card_title_id: str, want_spinner: bool = True, refresh_btn_id: str = None
) -> dbc.CardHeader:
    """Get the card header with a spinner for the main charts"""

    cols_list = []

    if want_spinner:
        cols_list.append(
            dbc.Col(dbc.Spinner(id=graph_card_title_id, color="success"), width="auto")
        )
    else:
        cols_list.append(dbc.Col(id=graph_card_title_id, width="auto"))

    if refresh_btn_id:
        # Refresh button floated on right side
        cols_list.append(
            dbc.Col(
                dbc.Button(
                    [html.I(className="fa-solid fa-refresh me-1"), "Refresh"],
                    id=refresh_btn_id,
                    color="secondary",
                    outline=True,
                    size="sm",
                    # line-height 1
                    class_name="lh-1",
                ),
                width="auto",
            )
        )

    return dbc.CardHeader(dbc.Row(cols_list, justify="between"))


def create_dcc_date_picker_range(
    id_, go_btn_id, next_btn_id, prev_btn_id, spinner_id, days=7, min_dt=None
):
    """Create date range picker to select date range for cards and time series charts"""

    start_date = date.today() - timedelta(days=days)
    end_date = date.today()
    # No minimum date
    # min_dt = min_dt or date.today() - timedelta(days=365)
    min_dt = min_dt
    max_dt = date.today() + timedelta(days=1)

    return html.Div(
        [
            dcc.DatePickerRange(
                id=id_,
                min_date_allowed=min_dt,
                max_date_allowed=max_dt,
                initial_visible_month=end_date,
                start_date=start_date,
                end_date=end_date,
                style={"display": "inline-block"},
            ),
            html.Div(
                [
                    dbc.Button(
                        html.I(className="fa-solid fa-arrow-left"),
                        id=prev_btn_id,
                        size="sm",
                        color="secondary",
                        # className="ms-1",
                        style={"margin": "4px 0px 4px 4px"},
                    ),
                    dbc.Button(
                        html.I(className="fa-solid fa-arrow-right"),
                        id=next_btn_id,
                        size="sm",
                        color="secondary",
                        # className="ms-1",
                        style={"margin": "4px 0px 4px 4px"},
                    ),
                    dbc.Button(
                        dbc.Spinner(
                            # The spinner activates when we're
                            # waiting on this id as and Output()
                            html.Div("Go", id=spinner_id),
                            size="sm",
                            spinner_style={
                                "margin": "2.5px 1.5px",
                            },
                        ),
                        color="primary",
                        disabled=False,
                        id=go_btn_id,
                        size="sm",
                        style={"margin": "4px 0px 4px 4px"},
                    ),
                ],
                style={"display": "inline-block"},
            ),
        ],
        style={
            "text-align": "center",
            # Vertical-align the button and spinner in center of row
            "align-self": "center",
            "margin-bottom": "0.5rem",
        },
    )


def create_dcc_date_picker_single(
    id_, go_btn_id, next_btn_id, prev_btn_id, spinner_id, days=7, min_dt=None
):
    """Create date picker to select date for card charts"""

    # start_date = date.today() - timedelta(days=days)
    start_date = date.today()
    # No minimum date
    # min_dt = min_dt or date.today() - timedelta(days=365)
    min_dt = min_dt
    max_dt = date.today() + timedelta(days=1)

    return html.Div(
        [
            dcc.DatePickerSingle(
                id=id_,
                min_date_allowed=min_dt,
                max_date_allowed=max_dt,
                initial_visible_month=start_date,
                date=start_date,
                style={"display": "inline-block"},
                # number_of_months_shown=3,
                persistence=True,
                persistence_type="memory",
            ),
            html.Div(
                [
                    dbc.Button(
                        html.I(className="fa-solid fa-arrow-left"),
                        id=prev_btn_id,
                        size="sm",
                        color="secondary",
                        # className="ms-1",
                        style={"margin": "4px 0px 4px 4px"},
                    ),
                    dbc.Button(
                        html.I(className="fa-solid fa-arrow-right"),
                        id=next_btn_id,
                        size="sm",
                        color="secondary",
                        # className="ms-1",
                        style={"margin": "4px 0px 4px 4px"},
                    ),
                    dbc.Button(
                        dbc.Spinner(
                            # The spinner activates when we're
                            # waiting on this id as and Output()
                            html.Div("Go", id=spinner_id),
                            size="sm",
                            spinner_style={
                                "margin": "2.5px 1.5px",
                            },
                        ),
                        color="primary",
                        disabled=False,
                        id=go_btn_id,
                        size="sm",
                        style={"margin": "4px 0px 4px 4px"},
                    ),
                ],
                style={"display": "inline-block"},
            ),
        ],
        style={
            "text-align": "center",
            # Vertical-align the button and spinner in center of row
            "align-self": "center",
            "margin-bottom": "0.5rem",
        },
    )


def get_list_group_item(
    label_id=None,
    badge_id=None,
    label_width=9,
    tooltip=None,
    label=None,
    badge_children=None,
    badge_color=None,
    spinner=True,
    parent_id=None,
    style=None,
):
    """
    For the indicators tab, get a list group item,
    which is a row with two columns in it:
        one column for the label
        one column for the badge
    """
    style = style or {}
    badge = dbc.Badge(
        badge_children,
        id=badge_id,
        pill=False,
        className="float-end",
        color=badge_color,
        style={"color": "white"},
        target="_blank",  # external, new tab
    )

    if spinner:
        col2_children = dbc.Spinner(
            badge,
            color="success",
            size="sm",
            # spinner_style={"width": "1rem", "height": "1rem"},
            spinner_class_name="m-0 float-end",
            type="grow",
        )
    else:
        col2_children = badge

    children = [
        dbc.Row(
            [
                dbc.Col(
                    html.Div(
                        label,
                        id=label_id,
                    ),
                    width=label_width,
                ),
                dbc.Col(
                    col2_children,
                    width=12 - label_width,
                ),
            ],
            justify="center",
        ),
        # html.Div(dbc.Tooltip(tooltip, target=badge_id) if tooltip else None)
    ]

    lgi = (
        dbc.ListGroupItem(children, id=parent_id, style=style)
        if parent_id
        else dbc.ListGroupItem(children, style=style)
    )

    return lgi


def get_ag_grid(
    id: str = "",
    rowData: list = [],
    getRowId: str = None,
    defaultColDef: dict = {},
    dashGridOptions: dict = {},
    columnDefs: list = [],
    columnSize: str = "autoSize",
    column_filter: bool = True,
    column_sortable: bool = True,
    pagination: bool = True,
    paginationPageSize: int = 10,
    skipHeaderOnAutoSize: bool = False,
    columnHoverHighlight: bool = True,
    # style: dict = None,
    csv_filename: str = f"IJACK Data {date.today().strftime('%Y-%m-%d')}.csv",
    autoHeight: bool = True,
    # If autoHeight is True, height is forced to None so the below has no effect
    height: str = "70vh",
    className: str = "ag-theme-quartz ag-theme-ijack",
    style: dict | None = None,
) -> AgGrid:
    """Get the default AgGrid component"""

    if autoHeight:
        # height must be None for autoHeight to work
        height = None
        domLayout = "autoHeight"
        paginationAutoPageSize = False
        page_sizes: set = {5, 10, 15, 20, 25, 50, 100}
        page_sizes.add(paginationPageSize)
        paginationPageSizeSelector = list(page_sizes)
        paginationPageSizeSelector.sort()
    else:
        height = height
        domLayout = "normal"
        paginationAutoPageSize = True
        paginationPageSizeSelector = False

    # Inside a div with max-height for the viewport
    return AgGrid(
        id=id,
        rowData=rowData,
        columnDefs=columnDefs,
        getRowId=getRowId,
        # Default theme is "ag-theme-quartz"
        className=className,
        # className="ag-theme-alpine dbc-ag-grid",
        style=style or {"height": height, "width": "100%"},
        # or {
        #     # https://dash.plotly.com/dash-ag-grid/grid-size#grid-auto-height
        #     # https://www.ag-grid.com/javascript-data-grid/grid-size/#autoHeight
        #     "height": None,
        #     "width": "100%",
        #     # "whiteSpace": "normal",
        #     # # https://stackoverflow.com/a/71317375/3385948
        #     # "word-break": "break-word",
        #     # 80vh is 80% of the viewport height
        #     # "maxHeight": "80vh",
        #     # "height": "80vh",
        #     # "maxHeight": height,
        #     # "overflow": "auto",
        #     # Set the background color of the header to white
        #     # "--ag-header-background-color": "white",
        # },
        dashGridOptions=dashGridOptions
        or {
            "animateRows": True,
            # allow the grid to auto-size its height to fit rows (this is nice sometimes)
            "domLayout": domLayout,
            "rowHeight": 30,
            "skipHeaderOnAutoSize": skipHeaderOnAutoSize,
            # Default tooltip show delay is 2000 ms (2 seconds)
            "tooltipShowDelay": 100,
            "pagination": pagination,
            "paginationPageSize": paginationPageSize,
            "paginationAutoPageSize": paginationAutoPageSize,
            "paginationPageSizeSelector": paginationPageSizeSelector,
            # Highlight the column on hover, not just the row
            "columnHoverHighlight": columnHoverHighlight,
            # Render all popups (including dropdown menus) in the document body
            # instead of inside the grid container, so they will not be cut off by the grid container.
            "popupParent": {"function": "setBody()"},
            # Make filter buttons more touchable
            "suppressMenuHide": True,  # Always show filter buttons
            # Enable better touch support
            "suppressTouch": False,  # Allow touch interactions
            "rowSelection": "multiple",
            "suppressRowClickSelection": True,
        },
        # https://dash.plotly.com/dash-ag-grid/column-sizing
        # "autoSize" = the grid will fill the width of the container
        # "sizeToFit" = the grid will fill the width of the container, but each column will be auto-sized to fit the data
        # "responsiveSizeToFit" = the grid will fill the width of the container, but each column will be auto-sized to fit the data,
        # and the grid will resize as the container resizes
        columnSize=columnSize,
        defaultColDef=defaultColDef
        or {
            "resizable": True,
            "editable": False,
            "sortable": column_sortable,
            "filter": column_filter,
            "flex": 1,
            "initialWidth": 100,
            "minWidth": 100,
            "maxWidth": 150,
            "wrapHeaderText": True,
            "autoHeaderHeight": True,
            "wrapText": True,
            "autoHeight": True,
            "cellStyle": {
                # Breaks words according to the default line break rules,
                # not 'break-all' which breaks all words on any character.
                "wordBreak": "normal",
                "whiteSpace": "normal",
                "line-height": "1.5",
            },
            "filterParams": {
                "buttons": ["apply", "clear"],  # Add apply/clear buttons
                "closeOnApply": True,  # Close filter popup after applying
            },
            "sortingOrder": ["desc", "asc", None],
        },
        # - csvExportParams (dict; optional):
        #     Object with properties to pass to the exportDataAsCsv() method.
        #     `csvExportParams` is a dict with keys:
        #     - allColumns (boolean; optional):
        #         If True, all columns will be exported in the order they appear
        #         in the columnDefs.
        #     - appendContent (string; optional):
        #         Content to put at the bottom of the file export.
        #     - columnKeys (list of strings; optional):
        #         Provide a list (an array) of column keys or Column objects if
        #         you want to export specific columns.
        #     - columnSeparator (string; optional):
        #         Delimiter to insert between cell values.
        #     - fileName (string; optional):
        #         String to use as the file name.
        #     - onlySelected (boolean; optional):
        #         Export only selected rows.
        #     - onlySelectedAllPages (boolean; optional):
        #         Only export selected rows including other pages (only makes
        #         sense when using pagination).
        #     - prependContent (string; optional):
        #         Content to put at the top of the file export. A 2D array of
        #         CsvCell objects.
        #     - skipColumnGroupHeaders (boolean; optional):
        #         Set to True to skip include header column groups.
        #     - skipColumnHeaders (boolean; optional):
        #         Set to True if you don't want to export column headers.
        #     - skipPinnedBottom (boolean; optional):
        #         Set to True to suppress exporting rows pinned to the bottom of
        #         the grid.
        #     - skipPinnedTop (boolean; optional):
        #         Set to True to suppress exporting rows pinned to the top of
        #         the grid.
        #     - skipRowGroups (boolean; optional):
        #         Set to True to skip row group headers if grouping rows. Only
        #         relevant when grouping rows.
        #     - suppressQuotes (boolean; optional):
        #         Pass True to insert the value into the CSV file without
        #         escaping. In this case it is your responsibility to ensure
        #         that no cells contain the columnSeparator character.
        csvExportParams={
            "allColumns": True,
            "columnSeparator": ",",
            "fileName": csv_filename,
            "skipColumnGroupHeaders": True,
            "skipColumnHeaders": False,
            "skipPinnedBottom": True,
            "skipPinnedTop": True,
            "skipRowGroups": True,
            "suppressQuotes": False,
        },
    )
