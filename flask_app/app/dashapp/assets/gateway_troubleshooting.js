// For the gateway troubleshooting page in the /docs

function openGatewayInstructions(
  axiomtek_clicks,
  compulab_clicks,
  fatbox_clicks,
  eurotech_clicks,
  axiomtek_state,
  compulab_state,
  fatbox_state,
  eurotech_state
) {
  const triggered = dash_clientside.callback_context.triggered.map(
    (t) => t.prop_id
  );

  // Initialize all states to their current values
  let states = {
    axiomtek: axiomtek_state,
    compulab: compulab_state,
    fatbox: fatbox_state,
    eurotech: eurotech_state,
  };

  if (triggered.includes("axiomtek_btn.n_clicks")) {
    states.axiomtek = !axiomtek_state;
  } else if (triggered.includes("compulab_btn.n_clicks")) {
    states.compulab = !compulab_state;
  } else if (triggered.includes("fatbox_btn.n_clicks")) {
    states.fatbox = !fatbox_state;
  } else if (triggered.includes("eurotech_btn.n_clicks")) {
    states.eurotech = !eurotech_state;
  }

  // If the collapse is open, hide the image
  if (states.axiomtek) {
    states.image_ax = { display: "none" };
  }
  if (states.compulab) {
    states.image_cl = { display: "none" };
  }
  if (states.fatbox) {
    states.image_fb = { display: "none" };
  }
  if (states.eurotech) {
    states.image_et = { display: "none" };
  }

  // Return the updated states
  return [
    states.axiomtek,
    states.compulab,
    states.fatbox,
    states.eurotech,
    states.image_ax,
    states.image_cl,
    states.image_fb,
    states.image_et,
  ];
}

window.dash_clientside = Object.assign({}, window.dash_clientside, {
  gateway_troubleshooting_namespace: {
    open_gateway_instructions: openGatewayInstructions,
  },
});
