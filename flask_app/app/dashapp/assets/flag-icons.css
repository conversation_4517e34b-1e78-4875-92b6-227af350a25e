/*
To add a flag, like the Canadian flag, for example, just copy the .fi-ca and .fi-ca.fis styles.
I added Argentina by copying the Canada styles and replacing the country code.
In the 'list.py' file, the country code class gets added to the flag icon element:
flag_classes: str = f"fi fi-{country_code} me-2"
Each IJACK customer has a country associated with it, so the country code is available.
*/

.fib {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
}

.fi {
  background-size: contain;
  background-position: 50%;
  background-repeat: no-repeat;
  position: relative;
  display: inline-block;
  width: 1.33333333em;
  line-height: 1em;
}

.fi:before {
  content: "\00a0";
}

.fi.fis {
  width: 1em;
}

/* Canada */
.fi-ca {
  background-image: url(/static/img/flags/4x3/ca.svg);
}

.fi-ca.fis {
  background-image: url(/static/img/flags/1x1/ca.svg);
}

/* United States */
.fi-us {
  background-image: url(/static/img/flags/4x3/us.svg);
}

.fi-us.fis {
  background-image: url(/static/img/flags/1x1/us.svg);
}

/* Colombia */
.fi-co {
  background-image: url(/static/img/flags/4x3/co.svg);
}

.fi-co.fis {
  background-image: url(/static/img/flags/1x1/co.svg);
}

/* Argentina */
.fi-ar {
  background-image: url(/static/img/flags/4x3/ar.svg);
}

.fi-ar.fis {
  background-image: url(/static/img/flags/1x1/ar.svg);
}

/* Ecuador */
.fi-ec.fis {
  background-image: url(/static/img/flags/1x1/ec.svg);
}

.fi-ec {
  background-image: url(/static/img/flags/4x3/ec.svg);
}

/* Bahrain */
.fi-bh {
  background-image: url(/static/img/flags/4x3/bh.svg);
}

.fi-bh.fis {
  background-image: url(/static/img/flags/1x1/bh.svg);
}

/* Kazakhstan */
.fi-kz {
  background-image: url(/static/img/flags/4x3/kz.svg);
}

.fi-kz.fis {
  background-image: url(/static/img/flags/1x1/kz.svg);
}
