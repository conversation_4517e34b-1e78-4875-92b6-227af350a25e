function ScrollToTop(children, cardBodyId) {
  // Check if cardBodyId is null or undefined
  if (!cardBodyId) {
    // console.error("cardBodyId is null or undefined");
    return null;
  }

  // Get the card-body element by ID
  var cardBody = document.getElementById(cardBodyId);

  // Check if cardBody is null or undefined
  if (!cardBody) {
    // console.error("cardBody element not found");
    return null;
  }

  // Scroll to the top of the card-body
  cardBody.scrollTop = 0;

  // We have to return something for the Dash callback to work
  return null;
}

window.dash_clientside = Object.assign({}, window.dash_clientside, {
  callback_list_namespace: {
    scroll_to_top: ScrollToTop,
  }
})
