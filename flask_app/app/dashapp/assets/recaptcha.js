// Global variable to track reCAPTCHA state
window.recaptchaState = {
  loaded: false,
  siteKey: null,
  action: null,
  initialized: false,
};

// Function to initialize invisible reCAPTCHA
async function initializeRecaptcha(data) {
  // Store siteKey and action globally
  window.recaptchaState.siteKey = data["site_key"];
  window.recaptchaState.action = data["action"];

  // Load reCAPTCHA script if not already loaded
  if (!window.recaptchaState.loaded) {
    await new Promise((resolve, reject) => {
      console.log("Loading reCAPTCHA script...");
      const script = document.createElement("script");
      // If the following doesn't render properly (i.e. the MIME type is set to HTML instead of JS)
      // then make a new site key using reCAPTCHA v3 (invisible and score-based instead of render-based and clickable)
      // Here's the error message if the link below doesn't work properly:
      //    ERROR: The resource from "https://www.google.com/recaptcha/enterprise.js?render=..."
      //    was blocked due to MIME type ("text/html") mismatch (X-Content-Type-Options: nosniff
      script.src = `https://www.google.com/recaptcha/enterprise.js?render=${window.recaptchaState.siteKey}`;
      // script.async = true;
      // script.defer = true;
      // Ensure the correct MIME type is set so the browser doesn't think it's an HTML file
      script.type = "text/javascript";

      script.onload = async () => {
        console.log("reCAPTCHA script loaded successfully");
        window.recaptchaState.loaded = true; // Mark as loaded
        resolve();
      };

      // Handle script load error
      script.onerror = (error) => {
        reject(new Error("Failed to load reCAPTCHA script"));
      };

      // Put the script in the head tag
      document.head.appendChild(script);
    });
  }

  // Initialize after loading
  if (!window.recaptchaState.initialized) {
    try {
      // Wrap grecaptcha.enterprise.ready() in a Promise
      await new Promise((resolve, reject) => {
        window.grecaptcha.enterprise.ready(() => {
          console.log("reCAPTCHA is ready");
          window.recaptchaState.initialized = true;
          resolve();
        });
      });
    } catch (error) {
      console.error("reCAPTCHA initialization error:", error);
      throw error;
    }
  }

  // This is just the children of the div, not the div itself
  return "";
}

// Handle form submission and reCAPTCHA verification
async function handleFormSubmit(n_clicks, data) {
  try {
    // if (!window.recaptchaState.loaded || !window.grecaptcha?.enterprise) {
    if (!window.grecaptcha?.enterprise) {
      throw new Error("reCAPTCHA not loaded yet");
    }

    // Wait for reCAPTCHA to be ready
    await new Promise((resolve, reject) => {
      window.grecaptcha.enterprise.ready(() => {
        console.log("reCAPTCHA is ready");
        resolve();
      });
    });

    // Execute invisible reCAPTCHA
    const token = await window.grecaptcha.enterprise.execute(data["site_key"], {
      action: data["action"],
    });

    console.log("reCAPTCHA token:", token);
    return token; // Return the token to the caller
  } catch (error) {
    console.error("reCAPTCHA error:", error);
    throw error;
  }
}

// Expose functions to Dash clientside callbacks
window.dash_clientside = Object.assign({}, window.dash_clientside, {
  careers_namespace: {
    initialize_recaptcha_in_js: initializeRecaptcha,
    on_submit_application: handleFormSubmit,
  },
  contact_namespace: {
    initialize_recaptcha_in_js: initializeRecaptcha,
    on_submit_application: handleFormSubmit,
  },
});
