function initializeSignaturePad(quoteDetailStyle) {
  if (!quoteDetailStyle || quoteDetailStyle.display === "none") {
    return null;
  }

  // Wait for the DOM to be fully loaded
  setTimeout(() => {
    const canvas = document.createElement("canvas");
    canvas.width =
      document.getElementById("signature-pad-container").offsetWidth - 10;
    canvas.height =
      document.getElementById("signature-pad-container").offsetHeight - 10;
    canvas.id = "signature-canvas";

    // Clear any existing canvas
    const container = document.getElementById("signature-pad-container");
    container.innerHTML = "";
    container.appendChild(canvas);

    // Initialize SignaturePad
    const signaturePad = new SignaturePad(canvas, {
      backgroundColor: "rgb(255, 255, 255)",
      penColor: "rgb(0, 0, 128)",
    });

    // Save signaturePad reference to window object so we can access it later
    window.signaturePad = signaturePad;

    // Add event listener to clear button
    document
      .getElementById("clear-signature-button")
      .addEventListener("click", function () {
        signaturePad.clear();
      });

    // Add event listener to approve button
    document
      .getElementById("approve-quote-button")
      .addEventListener("click", function () {
        if (signaturePad.isEmpty()) {
          alert("Please provide a signature before approving the quote.");
          return;
        }

        // Get signature data as base64 string
        const signatureData = signaturePad.toDataURL();

        // Store signature data in dash store component
        const signatureStore = document.getElementById("signature_data");
        if (signatureStore) {
          signatureStore._dashprivate_setValue(signatureData);

          // Trigger the approval submit callback by clicking a hidden button
          document.getElementById("trigger-approve-quote").click();
        }
      });
  }, 100);

  return null;
}

// Handle the grid click events for the View button
function handleGridClick(gridClickEvent, gridData) {
  if (!gridClickEvent || !gridData || gridData.length === 0) {
    return [null, window.dash_clientside.no_update];
  }

  // Check if the click event target was a button
  const clickEvent = JSON.parse(gridClickEvent);
  if (clickEvent && clickEvent.type === "cellClicked") {
    const target = clickEvent.event.target;

    // Check if the clicked element is a button
    if (target && target.tagName === "BUTTON") {
      // Get the quote ID from the button's data attribute
      const quoteId = target.getAttribute("data-quote-id");
      if (quoteId) {
        // Find the quote data
        const quoteData = gridData.find(
          (quote) => quote.id.toString() === quoteId
        );

        if (quoteData) {
          // Return the quote ID and data to update the respective stores
          return [quoteId, quoteData];
        }
      }
    }
  }

  return [window.dash_clientside.no_update, window.dash_clientside.no_update];
}

// Toggle visibility between quotes table and quote detail
function toggleQuoteDetailView(quoteId, backButtonClicks) {
  // If there's a quote ID or back button was clicked
  if (quoteId || backButtonClicks > 0) {
    const quotesTableStyle = quoteId
      ? { display: "none" }
      : { display: "block" };
    const quoteDetailStyle = quoteId
      ? { display: "block" }
      : { display: "none" };

    return [quotesTableStyle, quoteDetailStyle];
  }

  return [window.dash_clientside.no_update, window.dash_clientside.no_update];
}

// Format currency for display
function formatCurrency(value) {
  if (value === undefined || value === null) return "";
  return (
    "$" +
    parseFloat(value)
      .toFixed(2)
      .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
  );
}

// Populate quote details
function populateQuoteDetails(quoteData) {
  if (!quoteData) {
    return Array(10).fill(window.dash_clientside.no_update);
  }

  // Create line items table
  let lineItemsTable = "";
  if (quoteData.lineItems && quoteData.lineItems.length > 0) {
    lineItemsTable = `
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th>Description</th>
                            <th class="text-end">Quantity</th>
                            <th class="text-end">Unit Price</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                `;

    quoteData.lineItems.forEach((item) => {
      lineItemsTable += `
                        <tr>
                            <td>${item.item || ""}</td>
                            <td>${item.description || ""}</td>
                            <td class="text-end">${item.quantity || ""}</td>
                            <td class="text-end">${this.formatCurrency(
                              item.unitPrice
                            )}</td>
                            <td class="text-end">${this.formatCurrency(
                              item.amount
                            )}</td>
                        </tr>
                    `;
    });

    lineItemsTable += "</tbody></table>";
  } else {
    lineItemsTable = "<p>No line items available</p>";
  }

  // Format date if available
  let formattedDate = "";
  if (quoteData.date) {
    const date = new Date(quoteData.date);
    formattedDate = date.toLocaleDateString();
  }

  // Format expiry date if available
  let formattedExpiry = "";
  if (quoteData.expires) {
    const date = new Date(quoteData.expires);
    formattedExpiry = date.toLocaleDateString();
  }

  // Create status badge
  let statusBadge = "";
  if (quoteData.status) {
    let badgeClass = "badge bg-secondary";
    if (quoteData.status === "Pending") {
      badgeClass = "badge bg-warning text-dark";
    } else if (quoteData.status === "Approved") {
      badgeClass = "badge bg-success";
    } else if (quoteData.status === "Rejected") {
      badgeClass = "badge bg-danger";
    }
    statusBadge = `<span class="${badgeClass}">${quoteData.status}</span>`;
  }

  return [
    quoteData.id.toString(),
    formattedDate,
    statusBadge,
    quoteData.customer || "",
    quoteData.work_order || "",
    quoteData.description || "",
    lineItemsTable,
    this.formatCurrency(quoteData.subtotal),
    this.formatCurrency(quoteData.tax),
    this.formatCurrency(quoteData.total),
    quoteData.terms || "Standard terms and conditions apply.",
  ];
}

// Print preview for the quote
function PrintPreview(readyForPrinting) {
  if (!readyForPrinting) {
    return [window.dash_clientside.no_update, window.dash_clientside.no_update];
  }

  // Generate print view
  const printContent = `
                <div>
                    <p class="text-center">
                        <button id="print-button" class="btn btn-primary">
                            <i class="fa fa-print me-2"></i>Print Quote
                        </button>
                    </p>
                    <div id="printable-content" class="mt-3">
                        <!-- Content will be copied from quote detail view -->
                        ${
                          document.getElementById("quote-detail-container")
                            .innerHTML
                        }
                    </div>
                </div>
            `;

  // Add event listener for print button after modal is shown
  setTimeout(() => {
    document
      .getElementById("print-button")
      .addEventListener("click", function () {
        const printWindow = window.open("", "_blank");
        printWindow.document.write(`
                        <html>
                            <head>
                                <title>Quote ${
                                  document.getElementById("quote-id").innerText
                                }</title>
                                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
                                <style>
                                    @media print {
                                        .no-print { display: none !important; }
                                    }
                                    body { padding: 20px; }
                                </style>
                            </head>
                            <body>
                                <div class="container">
                                    <h2 class="mb-4">Work Order Quote</h2>
                                    ${
                                      document.getElementById(
                                        "printable-content"
                                      ).innerHTML
                                    }
                                </div>
                                <script>
                                    // Remove buttons and other non-printable elements
                                    document.querySelectorAll('.no-print, button, .btn').forEach(el => el.remove());
                                    // Print automatically
                                    window.onload = function() { window.print(); window.close(); };
                                </script>
                            </body>
                        </html>
                    `);
        printWindow.document.close();
      });
  }, 100);

  return [printContent, true];
}

// Initialize SignaturePad when the quote detail page is shown
window.dash_clientside = Object.assign({}, window.dash_clientside, {
  account_namespace: {
    // Initialize signature pad when the quote detail container is shown
    initializeSignaturePad: initializeSignaturePad,
    // Handle grid click events
    handleGridClick: handleGridClick,
    // Toggle quote detail view
    toggleQuoteDetailView: toggleQuoteDetailView,
    // Format currency
    formatCurrency: formatCurrency,
    // Populate quote details
    populateQuoteDetails: populateQuoteDetails,
    // Print preview
    printPreview: PrintPreview,
  },
});
