// Create a global object for Dash Ag Grid Component Functions if it doesn't exist already.
// This uses the logical OR operator to either keep the existing object or create a new empty one.
// These are for UI component renderers that create visual elements within the AG Grid.
// These are the functions that build what users actually see in grid cells.
var dagcomponentfuncs = (window.dashAgGridComponentFunctions =
  window.dashAgGridComponentFunctions || {});

// Similarly, create a global object for Dash Ag Grid utility and helper functions.
// They handle data processing, formatting, and other non-visual tasks.
var dagfuncs = (window.dashAgGridFunctions = window.dashAgGridFunctions || {});

// Define a new component function that will create a Bootstrap button
dagcomponentfuncs.ButtonDBC = function (props) {
  // Destructure the props to get setData and data properties
  const { setData, data } = props;

  // Define the click handler function
  function onClick() {
    setData();
  }

  // Create and return a React element using the dash_bootstrap_components Button
  return React.createElement(
    // First argument: the component to create (Bootstrap Button)
    window.dash_bootstrap_components.Button,
    // Second argument: the props/configuration object
    {
      onClick, // Attach the click handler
      color: props.color || "primary", // Use provided color or default to "primary"
      size: props.size || "sm", // Use provided size or default to "sm"
      // Add Bootstrap classes to center the button vertically
      className: "d-flex align-items-center",
    },
    // Third argument: the button's content/children
    // Use provided label or default to "View"
    props.label || "View"
  );
};

// custom component to display boolean data as a DBC Switch
// https://dashaggridexamples.pythonanywhere.com/components-dbc
dagcomponentfuncs.SwitchDBC = function (props) {
  const {setData, value} = props;

  // updated the dbc component
  setProps = ({value}) => {
     // update the grid
      props.node.setDataValue(props.column.colId, value);
      // update to trigger a dash callback
      setData(value)
  }

  return React.createElement(
      window.dash_bootstrap_components.Switch, {
          value: value,
          checked: value,
          setProps,
          style: {"paddingTop": 6},
      }
  )
};

dagcomponentfuncs.ServiceClockEditLink = function (props) {
  return React.createElement(
    "a",
    { href: "/service-clock/?edit_service_clock_id=" + props.data.edit },
    // props.data.edit + ' (Edit)'
    // Add crayon icon
    [
      React.createElement("i", {
        className: "fa fa-edit fa-2x mt-2",
        key: "icon",
      }),
      // ' ' + props.data.edit + ' (Edit)'
    ]
  );
};

// Generic link renderer that creates anchor tags based on provided parameters
dagcomponentfuncs.LinkRenderer = function (props) {
  // Return empty if no value provided
  if (!props.value && props.value !== false) {
    return "";
  }

  // Get configuration from params or use defaults
  baseUrl = props.baseUrl || ""; // Base URL prefix
  searchParam = props.searchParam || ""; // URL parameter name for search
  searchField = props.searchField || ""; // Field from row data to use as search value
  target = props.target || "_blank"; // Window target (_blank, _self, etc)
  maxLength = props.maxLength || 80; // Max length for displayed text
  style = props.style || {}; // Custom styles for the link

  // Get the search value from row data if searchField is specified
  const searchValue = props.searchField ? props.data?.[searchField] : null;

  // Build the href URL
  let href = baseUrl;
  if (searchParam && searchValue) {
    // Add ? or & depending if baseUrl already has parameters
    const separator = baseUrl.includes("?") ? "&" : "?";
    href += `${separator}${searchParam}=${searchValue}`;
  }

  // Format display text (truncate if needed)
  let displayText = String(props.value);
  if (maxLength && displayText.length > maxLength) {
    displayText = displayText.substring(0, maxLength) + "...";
  }

  // Create React element for the link
  return React.createElement(
    "a",
    {
      href: href,
      target: target,
      style: style,
    },
    displayText
  );
};

// Google Maps link renderer
dagcomponentfuncs.GoogleMapsLink = function (props) {
  const {
    gps_lat = "", // Hidden latitude column we need
    gps_lon = "", // Hidden longitude column we need
  } = props.data || {};

  // Create Google Maps URL
  const href = `https://www.google.com/maps/search/?api=1&query=${gps_lat},${gps_lon}`;

  // Create React element for the link
  return React.createElement(
    "a",
    {
      href: href,
      target: "_blank",
    },
    props.value
  );
};

// AWS IoT Shadow link renderer
dagcomponentfuncs.AwsShadowLink = function (props) {
  // Return empty if no value provided
  if (!props.value) {
    return "";
  }

  // Create AWS IoT Shadow URL
  const href = `https://us-west-2.console.aws.amazon.com/iot/home?region=us-west-2#/thing/${props.value}/namedShadow/Classic%20Shadow`;

  // Create React element for the link
  return React.createElement(
    "a",
    {
      href: href,
      target: "_blank",
    },
    props.value
  );
};

// Render the HTML exactly as it's given to the component
dagcomponentfuncs.htmlRenderer = function (props) {
  return React.createElement("span", {
    style: { "overflow-wrap": "normal", "word-break": "normal" },
    dangerouslySetInnerHTML: { __html: props.value },
  });
};

// // Date formatter function
// dagcomponentfuncs.DateFormatter = function (props) {
//   if (!props.value) return "";
//   try {
//     return moment(props.value).format("MM/DD/YYYY");
//   } catch (e) {
//     console.error("Date formatting error:", e);
//     return props.value;
//   }
// };

// // Timestamp formatter function
// dagcomponentfuncs.TimestampFormatter = function (props) {
//   if (!props.value) return "";
//   try {
//     return moment(props.value).format("MM/DD/YYYY HH:mm");
//   } catch (e) {
//     console.error("Timestamp formatting error:", e);
//     return props.value;
//   }
// };

// Date formatter function
dagcomponentfuncs.DateFormatter = function (props) {
  // Return empty string if no value
  if (!props.value) return "";

  try {
    // Create Date object from input value
    const date = new Date(props.value);

    // Check if date is valid
    if (isNaN(date)) throw new Error("Invalid date");

    // Format date as MM/DD/YYYY
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const year = date.getFullYear();

    return `${month}/${day}/${year}`;
  } catch (e) {
    console.error("Date formatting error:", e);
    return props.value;
  }
};

// Timestamp formatter function
dagcomponentfuncs.TimestampFormatter = function (props) {
  // Return empty string if no value
  if (!props.value) return "";

  try {
    // Create Date object from input value
    const date = new Date(props.value);

    // Check if date is valid
    if (isNaN(date)) throw new Error("Invalid date");

    // Format date as MM/DD/YYYY HH:mm
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");

    return `${month}/${day}/${year} ${hours}:${minutes}`;
  } catch (e) {
    console.error("Timestamp formatting error:", e);
    return props.value;
  }
};

// Currency formatter function
dagcomponentfuncs.CurrencyFormatter0 = function (props) {
  // Format the value as currency. If the value is null, show ""
  if (props.value === null) {
    return "";
  }
  return (
    "$" +
    parseFloat(props.value).toLocaleString("en-US", {
      maximumFractionDigits: 0,
    })
  );
};

dagcomponentfuncs.Tooltip = function (props) {
  return React.createElement(
    "span",
    {
      title: props.value,
      style: {
        padding: "1px",
        "overflow-wrap": "normal",
        "word-break": "normal",
      },
    },
    props.value
  );
};

dagcomponentfuncs.CurrencyFormatter = function (props) {
  let decimals = props?.decimals ?? 2;
  if (props.value === null || props.value === undefined || props.value === "") {
    return React.createElement("span", { title: "" }, "");
  }
  const currString = props.value.toString();
  const lString = (currString.split(".").at(0) ?? "").padStart(1, "0");
  const rString = (
    String(Number(`0.${currString.split(".").at(1)}`).toFixed(decimals))
      .split(".")
      .at(1) ?? ""
  ).padEnd(decimals, "0");
  const value = `${lString}${decimals > 0 ? "." : ""}${rString}`;
  const formattedValue = `$${parseFloat(value).toLocaleString("en-US")}`;
  return React.createElement("span", { title: formattedValue }, formattedValue);
};

// Format numbers with configurable decimal places
dagcomponentfuncs.NumberFormatter = function (props) {
  // Return empty string if null or not a number
  if (props.value === null || isNaN(props.value)) {
    return "";
  }

  // Get decimal places from params or default to 1
  const decimals = props.params?.decimals ?? 1;

  // Get locale from params or default to en-US
  const locale = props.params?.locale ?? "en-US";

  try {
    return parseFloat(props.value).toLocaleString(locale, {
      maximumFractionDigits: decimals,
      minimumFractionDigits: decimals,
    });
  } catch (e) {
    console.error("Number formatting error:", e);
    return "";
  }
};

// Format numbers to 0 decimal places
dagcomponentfuncs.NumberFormatter0 = function (props) {
  return dagcomponentfuncs.NumberFormatter({
    value: props.value,
    params: { decimals: 0 },
  });
};

// Format numbers to 1 decimal place
dagcomponentfuncs.NumberFormatter1 = function (props) {
  return dagcomponentfuncs.NumberFormatter({
    value: props.value,
    params: { decimals: 1 },
  });
};

// Format numbers to 2 decimal places
dagcomponentfuncs.NumberFormatter2 = function (props) {
  return dagcomponentfuncs.NumberFormatter({
    value: props.value,
    params: { decimals: 2 },
  });
};

// Create a simple Dash Ag Grid component that has a label and a value, for a dropdown in the Dash Ag Grid table.
// filterArray function displays the label instead of the value when the dropdown options contain both.
// Use this in a valueFormatter
// https://github.com/AnnMarieW/dash-ag-grid-examples/blob/b32ce2f5851c223b8a5114cb5783cc10b77da3ad/examples/components/dmc_select_labels_and_vals.py
dagfuncs.filterArray = function (array, condition, col = null) {
  newArray = array.filter((t) => condition.includes(t.value));
  if (!col) {
    return newArray;
  } else {
    let values = [];
    newArray.map((t) => values.push(t[col]));
    return values;
  }
};

// dmc.Select with Popup Parent
// Under most scenarios, the AG Grid dropdown menu will fit inside the grid.
// However if the grid is small and / or the menu is very large, then the menu
// will not fit inside the grid and it will be clipped. This leads to a bad user experience.
// https://dashaggridexamples.pythonanywhere.com/components-dmc
dagfuncs.setBody = () => {
  return document.querySelector("body");
};

// Delete button for AG Grid
// https://community.plotly.com/t/deleting-rows-in-dash-ag-grid/78700
dagcomponentfuncs.DeleteButton = function (props) {
  function onClick() {
    props.api.applyTransaction({ remove: [props.node.data] });
  }

  // Red color? "#ff0000"
  colorWanted = props.colorWanted || "";
  
  return React.createElement(
    "span", // Using span instead of button for less default styling
    {
      onClick,
      style: {
        cursor: "pointer", // Show pointer cursor on hover
        color: colorWanted, 
        fontSize: "16px", // Larger font size
        fontWeight: "bold", // Make it bold
        display: "flex", // Center content
        justifyContent: "center", // Center horizontally
        alignItems: "center", // Center vertically
        width: "100%", // Take full width of the cell
        height: "100%", // Take full height of the cell
        transition: "color 0.2s", // Smooth color transition on hover
      },
      onMouseOver: (e) => (e.currentTarget.style.color = "#cc0000"), // Darker red on hover
      onMouseOut: (e) => (e.currentTarget.style.color = colorWanted), // Restore original color
      title: "Delete row", // Tooltip on hover
    },
    "×" // Using the multiplication symbol which looks nicer than "X"
  );
};

// Function to convert values back to labels for display
dagfuncs.getDisplayLabel = function (params) {
  // If no value, return empty string
  if (
    params.value === undefined ||
    params.value === null ||
    params.value === ""
  ) {
    return "";
  }

  // Get options from cell editor params
  const options = params.colDef.cellEditorParams.options;
  if (!options || !Array.isArray(options)) {
    return params.value;
  }

  // Find the option with matching value and return its label
  const option = options.find((opt) => opt.value === params.value);
  return option ? option.label : params.value;
};
dagfuncs.SelectDMC = class {
  // Called when the editor is first created and before it's used
  init(params) {
    // Store the grid parameters for later use
    this.params = params;

    // Flag to prevent multiple activations of the dropdown
    // This prevents race conditions when rapid events occur
    this.isActivating = false;

    // Define how to handle data coming back from the Mantine component
    // This function is called when the user selects an option from the dropdown
    const setProps = (props) => {
      // Only proceed if a value property exists in the props
      if (typeof props.value !== typeof undefined) {
        // Update our internal value with the selected option
        this.value = props.value;

        // Re-enable keyboard events in the grid that were disabled during editing
        delete params.colDef.suppressKeyboardEvent;

        // Tell the grid to exit edit mode for this cell
        params.api.stopEditing();

        // Return focus to the previously active element (usually the grid cell)
        if (this.prevFocus) this.prevFocus.focus();
      }
    };

    // Create a DOM element to host our React component
    this.eInput = document.createElement("div");

    // Create a React root for rendering into our DOM element
    this.root = ReactDOM.createRoot(this.eInput);

    // Get Mantine components from the global window object
    // These are provided by the dash_mantine_components package
    const MantineProvider = window.dash_mantine_components.MantineProvider;
    const Select = window.dash_mantine_components.Select;

    // Get global Mantine theme configuration if it exists
    // This ensures our dropdown matches the rest of the application's styling
    const globalMantineConfig = {
      theme: window.dash_mantine_components.mantineTheme || {},
      styles: window.dash_mantine_components.mantineStyles || {},
      colorScheme: window.dash_mantine_components.mantineColorScheme || "light",
      emotionCache: window.dash_mantine_components.mantineEmotionCache || null,
      withGlobalStyles: true,
      withNormalizeCSS: true,
    };

    // Log initialization value for debugging
    console.log(`Initializing with value: ${params.value}`);

    // Render the Mantine Select component inside a MantineProvider
    this.root.render(
      React.createElement(
        MantineProvider,
        globalMantineConfig,
        React.createElement(Select, {
          // Data options for the dropdown
          data: params.options,
          value: params.value,
          setProps,

          // Basic styling - using the grid column's width
          style: {
            width: params.column.actualWidth - 2, // Slight adjustment to fit cell
            zIndex: 1000, // Ensure dropdown appears above other elements
          },

          // *** CRITICAL DROPDOWN POSITIONING SETTINGS ***
          // Keep the dropdown in the natural DOM flow instead of using a portal
          // This is key to fixing the positioning issue
          withinPortal: false,

          // Position dropdown below the input and aligned with left edge
          dropdownPosition: "bottom-start",

          // Visual and functional settings
          shadow: "md", // Medium shadow for visual distinction
          clearable: params.clearable !== false, // Allow clearing selection
          searchable: true, // Enable searching within options
          placeholder: params.placeholder || "Select...", // Default placeholder
          maxDropdownHeight: params.maxDropdownHeight || 280, // Control dropdown height
          size: params.size, // Use size parameter if provided

          // Advanced positioning settings for the combobox
          comboboxProps: {
            position: "bottom-start", // Consistent positioning
            middleware: {
              shift: true, // Allow horizontal shifting to keep in viewport
              flip: true, // Allow flipping to above input if no room below
            },
            withinPortal: false, // Consistent with parent setting
          },
        })
      )
    );

    // Make our container element focusable
    this.eInput.tabIndex = "0";

    // Initialize internal value to match the cell's value
    this.value = params.value;
  }

  // Return the DOM element that will be inserted into the grid
  getGui() {
    return this.eInput;
  }

  // Handle focusing and activating the dropdown
  focusChild() {
    // Prevent multiple simultaneous activation attempts
    if (this.isActivating) return;
    this.isActivating = true;

    // Delay to ensure the component has fully rendered
    setTimeout(() => {
      // Safety check - ensure our element still exists
      if (!this.eInput) {
        this.isActivating = false;
        return;
      }

      // Find the input element using multiple possible selectors for compatibility
      const input =
        this.eInput.querySelector(".mantine-Select-input") ||
        this.eInput.querySelector("input");

      // If we can't find the input, abort the operation
      if (!input) {
        this.isActivating = false;
        return;
      }

      // Make the input focusable with tab navigation
      input.tabIndex = "1";

      // Disable grid keyboard events while editing to prevent conflicts
      this.params.colDef.suppressKeyboardEvent = (params) => {
        return params.editing;
      };

      // Focus the input element to prepare for dropdown activation
      input.focus();

      // Delay slightly to ensure focus is complete before clicking
      setTimeout(() => {
        // First try to find and click the dropdown indicator button
        // This is more reliable than clicking the input itself
        const dropdownButton = this.eInput.querySelector(
          ".mantine-Select-rightSection"
        );
        if (dropdownButton) {
          // If found, click the dropdown button to open options
          dropdownButton.click();
        } else {
          // Fallback: click the input element itself
          input.click();
        }

        // Reset activation flag after all attempts are complete
        setTimeout(() => {
          this.isActivating = false;
        }, 100);
      }, 150);
    }, 100);
  }

  // Called by AG Grid after the editor is attached to the DOM
  afterGuiAttached() {
    // Store reference to currently focused element for later restoration
    this.prevFocus = document.activeElement;

    // Create a properly bound version of focusChild that preserves 'this' context
    // This is critical to ensure the method has access to class properties
    const boundFocusChild = this.focusChild.bind(this);

    // Attach the event listener to our element
    this.eInput.addEventListener("focus", boundFocusChild);

    // Store reference to bound function for proper cleanup later
    this._boundFocusChild = boundFocusChild;

    // Trigger focus on our container element
    this.eInput.focus();

    // Explicitly call focusChild to ensure dropdown activates
    // This ensures dropdown opens even if focus event doesn't trigger properly
    this.focusChild();
  }

  // Return the current value to AG Grid when editing is complete
  getValue() {
    return this.value;
  }

  // Clean up resources when the editor is removed
  destroy() {
    // Remove event listeners to prevent memory leaks
    // Using the stored bound function reference ensures proper removal
    if (this._boundFocusChild) {
      this.eInput.removeEventListener("focus", this._boundFocusChild);
    }

    // Use setTimeout to ensure clean unmounting after current execution
    setTimeout(() => {
      // Properly unmount the React component
      if (this.root) {
        this.root.unmount();
        this.root = null; // Clear reference to prevent memory leaks
      }

      // Restore focus to previously active element
      if (this.prevFocus) {
        this.prevFocus.focus();
      }
    }, 0); // Zero timeout ensures it happens after current execution cycle
  }
};
