/* Dash AG Grid  */
.ag-theme-quartz.ag-theme-ijack .ag-header-cell-filtered {
  background-color: var(--bs-primary);
  color: var(--bs-white);
}

.ag-theme-quartz.ag-theme-ijack .ag-header-cell-filtered span {
  color: var(--bs-white);
}

/* When using dashGridOptions = {"domLayout": "autoHeight"}, there is a minimum of 150px
set to the grid rows section. This is to avoid an empty grid, which would look weird.
To remove this minimum height, add the following CSS: */
/* https://www.ag-grid.com/javascript-data-grid/grid-size/#autoHeight */
.ag-center-cols-clipper {
  min-height: unset !important;
}

.ag-theme-quartz.ag-theme-ijack {
  /* --ag-quartz-active-color: var(--bs-primary);
  --ag-selected-row-background-color: rgba(var(--bs-primary), 0.3);
  --ag-row-hover-color: rgba(var(--bs-primary), 0.1);
  --ag-column-hover-color: rgba(var(--bs-primary), 0.1);
  --ag-input-focus-border-color: rgba(var(--bs-primary), 0.4);
  --ag-range-selection-background-color: rgba(var(--bs-primary-), 0.2);
  --ag-range-selection-background-color-2: rgba(var(--bs-primary), 0.36);
  --ag-range-selection-background-color-3: rgba(var(--bs-primary), 0.49);
  --ag-range-selection-background-color-4: rgba(var(--bs-primary), 0.59); */
  --ag-grid-size: 5px;
  --ag-list-item-height: 20px;
  --ag-header-background-color: white;
  --ag-border-color: rgba(173, 181, 189, 0.40);
  --ag-cell-horizontal-border: rgba(173, 181, 189, 0.20);
  --ag-secondary-border-color: rgba(173, 181, 189, 0.20);
  --ag-invalid-color: var(--bs-danger);
  --ag-font-family: var(--bs-font-sans-serif);
  --ag-odd-row-background-color: rgba(0, 0, 0, 0.05);
  --ag-cell-horizontal-border: var(--ag-row-border-width) var(--ag-row-border-style) var(--ag-row-border-color),
}

/* .ag-theme-quartz.ag-theme-ijack {
  --ag-quartz-active-color: var(--bs-primary);
  --ag-cell-horizontal-border: var(--ag-row-border-width) var(--ag-row-border-style) var(--ag-row-border-color);
  --ag-selected-row-background-color: rgba(var(--bs-primary), 0.3);
  --ag-row-hover-color: rgba(var(--bs-primary), 0.1);
  --ag-column-hover-color: rgba(var(--bs-primary), 0.1);
  --ag-input-focus-border-color: rgba(var(--bs-primary), 0.4);
  --ag-range-selection-background-color: rgba(var(--bs-primary-), 0.2);
  --ag-range-selection-background-color-2: rgba(var(--bs-primary), 0.36);
  --ag-range-selection-background-color-3: rgba(var(--bs-primary), 0.49);
  --ag-range-selection-background-color-4: rgba(var(--bs-primary), 0.59);
  --ag-border-color: rgba(173, 181, 189, 0.40);
  --ag-cell-horizontal-border: rgba(173, 181, 189, 0.20);
  --ag-secondary-border-color: rgba(173, 181, 189, 0.20);
  --ag-invalid-color: var(--bs-danger);
  --ag-font-family: var(--bs-font-sans-serif);
  --ag-header-background-color: var(--bs-white);
  --ag-odd-row-background-color: rgba(0, 0, 0, 0.05);
} */
