// Dash Ag Grid Component Functions
var dagcomponentfuncs = (window.dashAgGridComponentFunctions =
  window.dashAgGridComponentFunctions || {});

// Dash Ag Grid Functions (not the same as Dash Ag Grid Component Functions)
var dagfuncs = (window.dashAgGridFunctions = window.dashAgGridFunctions || {});

dagcomponentfuncs.StatusWorriedYesNo = function (props) {
  // Check if value is null/undefined (but allow false as valid value)
  if (!props.value && props.value !== false) {
    return "--";
  }

  // Declare variables for display text and cell styling
  let displayText;

  // Handle boolean values with cell background colors
  if (typeof props.value === "boolean") {
    if (props.value) {
      displayText = "Yes";
    } else {
      displayText = "No";
    }
  }

  // Handle date objects
  else if (props.value instanceof Date) {
    if (isNaN(props.value)) {
      displayText = "--";
    } else {
      const year = props.value.getFullYear();
      const month = String(props.value.getMonth() + 1).padStart(2, "0");
      const day = String(props.value.getDate()).padStart(2, "0");
      const hours = String(props.value.getHours()).padStart(2, "0");
      const minutes = String(props.value.getMinutes()).padStart(2, "0");
      displayText = `${year}-${month}-${day} ${hours}:${minutes}`;
    }
  }

  // Handle strings and other types
  else {
    displayText = String(props.value);
    if (displayText.length > 80) {
      displayText = displayText.substring(0, 80) + "...";
    }
  }

  // Get aws_thing and create href if available
  const awsThing = props.data?.aws_thing;
  let href = awsThing ? `/admin/gateways_not_connected?search=${awsThing}` : "";

  // Create a link if aws_thing is available
  return React.createElement(
    "a",
    {
      href: href,
      target: "_blank",
    },
    displayText
  );
};

/* Example usage in columnDefs:
columnDefs = [{
    field: 'status',
    headerName: 'Status',
    cellRenderer: 'statusWorriedYesNo',
    sortable: true,
    filter: true,
    // Ensure cell takes full height
    cellStyle: { height: '100%' }
}]
*/
