// Create a global object for Dash Ag Grid Component Functions if it doesn't exist already
// This uses the logical OR operator to either keep the existing object or create a new empty one
var dagcomponentfuncs = (window.dashAgGridComponentFunctions =
  window.dashAgGridComponentFunctions || {});

dagcomponentfuncs.QuoteStatusFormatter = function (params) {
  const status = params.value;
  let badgeClass = "badge bg-secondary";

  if (status === "Pending") {
    badgeClass = "badge bg-warning text-dark";
  } else if (status === "Approved") {
    badgeClass = "badge bg-success";
  } else if (status === "Rejected") {
    badgeClass = "badge bg-danger";
  } else if (status === "Expired") {
    badgeClass = "badge bg-secondary";
  }

  // return '<span class="' + badgeClass + '">' + status + "</span>";
  return React.createElement("span", { className: badgeClass }, status);
};

// dagcomponentfuncs.ViewButtonRenderer = function (params) {
//   // return (
//   //   '<button type="button" class="btn btn-primary btn-sm" data-quote-id="' +
//   //   params.data.id +
//   //   '">View</button>'
//   // );
//   return React.createElement(
//     "button",
//     {
//       type: "button",
//       className: "btn btn-primary btn-sm",
//       "data-quote-id": params.data.id,
//     },
//     "View"
//   );
// };
