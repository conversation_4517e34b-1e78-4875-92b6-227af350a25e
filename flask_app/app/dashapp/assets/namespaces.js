function loadJS(pathname) {
  let cleanPathname = pathname.replace(/^\/|\/$/g, "");
  console.log(`Loading ${cleanPathname}.js`);
  let script = document.createElement("script");
  script.src = `/dash/${cleanPathname}.js`;
  document.head.appendChild(script);
  // return "";
  return dash_clientside.no_update;
}

function compareVersionRefresh(swv_refresh_interval, store_software_version) {
  let version = currentVersion;
  console.log(`Current version: ${version}`);
  fetch("/version/")
    .then((response) => response.text())
    .then((latest_version) => {
      console.log(`Latest version: ${latest_version}`);
      if (store_software_version !== latest_version) {
        console.log("Refreshing page");
        window.location.reload();
      }
    });
  return dash_clientside.no_update;
}

window.dash_clientside = Object.assign({}, window.dash_clientside, {
  nav_namespace: {
    load_js_needed: loadJS,
    compare_version_refresh: compareVersionRefresh,
  },
});
