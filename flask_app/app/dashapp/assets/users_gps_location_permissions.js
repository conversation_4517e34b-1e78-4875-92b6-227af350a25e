// Find the user's GPS location permissions

// Function to log location permission state
function reportLocationState(state) {
  console.log(`geolocation permission ${state}`);
}

// Function to check location permissions
async function locationPermissions() {
  // Query the permissions API for geolocation permission
  const result = await navigator.permissions.query({ name: "geolocation" });

  // Add event listener for permission changes
  result.addEventListener("change", () => {
    console.log(`geolocation permission changed! State: ${result.state}`);
  });

  // Handle different permission states
  if (result.state === "granted" || result.state === "prompt") {
    reportLocationState(result.state);
    return true;
  } else if (result.state === "denied") {
    reportLocationState(result.state);
    return false;
  } else {
    console.log("Unknown state");
    return false;
  }
}

// Promise-based function to get coordinates
function getCoordinatesPromise() {
  return new Promise((resolve, reject) => {
    // Check if geolocation is supported
    if (!navigator.geolocation) {
      reject("Geolocation is not supported by this browser.");
    } else {
      // Get current position
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const latitude = position.coords.latitude;
          const longitude = position.coords.longitude;
          resolve({ latitude, longitude });
        },
        (error) => {
          reject(`Unable to retrieve your location. ${error.message}`);
        }
      );
    }
  });
}

// Function to get timezone information
function getTimezoneInfo() {
  try {
    // Get timezone using Intl.DateTimeFormat
    const timezoneName = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Get current timestamp
    const timestamp = new Date();

    // Get timezone offset in minutes
    const timezoneOffset = timestamp.getTimezoneOffset();

    // Convert timezone offset to hours
    const offsetHours = -timezoneOffset / 60;

    // Create UTC offset string
    const utcOffset = `UTC${offsetHours >= 0 ? "+" : ""}${offsetHours}`;

    return {
      timezoneName,
      timezoneOffset,
      offsetHours,
      utcOffset,
      timestamp: timestamp.toISOString(),
    };
  } catch (error) {
    console.error("Error getting timezone information:", error);
    return null;
  }
}

// Main function to get both location and timezone
async function askPermissionGetGPSAndTimezone() {
  try {
    // Check location permissions
    const havePermissions = await locationPermissions();
    if (!havePermissions) {
      return JSON.stringify({
        status: "error",
        message: "No location permissions",
        timezone: getTimezoneInfo(),
      });
    }

    // Get GPS coordinates
    const coordinates = await getCoordinatesPromise();

    // Get timezone information
    const timezone = getTimezoneInfo();

    // Combine all information
    const response = {
      status: "success",
      coordinates,
      timezone,
    };

    console.log("Location and timezone info:", response);
    return JSON.stringify(response);
  } catch (error) {
    console.error(error);
    // Even if location fails, return timezone info
    return JSON.stringify({
      status: "error",
      message: error.toString(),
      timezone: getTimezoneInfo(),
    });
  }
}

// Function to attempt permission removal (note: this may not work in all browsers)
async function removePermissions() {
  console.log("Removing geolocation permissions");
  try {
    if (navigator.permissions.revoke) {
      const result = await navigator.permissions.revoke({
        name: "geolocation",
      });
      console.log(`Permission revoke result: ${result.state}`);
    }
    return "Attempted to remove permissions";
  } catch (error) {
    console.error("Error removing permissions:", error);
    return "Failed to remove permissions";
  }
}

window.dash_clientside = Object.assign({}, window.dash_clientside, {
  service_clock_namespace: {
    ask_for_location_permissions: locationPermissions,
    ask_permission_get_gps: askPermissionGetGPSAndTimezone,
    // remove_permissions: removePermissions
  },
});
