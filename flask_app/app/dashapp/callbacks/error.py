from datetime import date

import dash_bootstrap_components as dbc
from dash import html


def error_layout(
    error_code: int = None, pathname: str = None, msg: str = ""
) -> dbc.Container:
    """Layout for the various error pages"""

    if error_code == 404:
        error_title = "Page Not Found"
        error_message = (
            f"Sorry, we couldn't find the page you were looking for: '{pathname}'"
        )
    elif error_code == 403:
        error_title = "Forbidden"
        error_message = (
            f"Sorry, you don't have permission to view the page '{pathname}'"
        )
    elif error_code == 500:
        error_title = "Internal Server Error"
        error_message = (
            f"Sorry, something went wrong while trying to load the page '{pathname}'"
        )
    else:
        error_title = "Error"
        error_message = f"An error occurred while trying to load the page '{pathname}'"

    return dbc.Container(
        dbc.Row(
            class_name="justify-content-center my-5",
            children=dbc.Col(
                xs=12,
                lg=10,
                xl=8,
                children=[
                    html.H1(error_title),
                    html.Hr(),
                    html.P(error_message),
                    html.P(msg, className="mt-3") if msg else None,
                    html.P(f"Error code: {error_code}", className="mt-3"),
                    html.P(f"Date: {date.today()}"),
                ],
            ),
        ),
    )
