import base64
import os
from datetime import date, datetime
from types import SimpleNamespace
from urllib import parse

import dash_bootstrap_components as dbc
from dash import Input, Output, State, callback, dash_table, dcc, html, no_update
from dash.exceptions import PreventUpdate
from flask import current_app, url_for
from shared.models.models import Application, ApplicationUploadFile, Country, Province

from app import assets, db
from app.config import (
    ADMIN_EMAILS,
    APPLICATION_BOOST,
    APPLICATION_DGAS,
    APPLICATION_EGAS_CGP,
    APPLICATION_EGAS_GP,
    APPLICATION_VRU,
    APPLICATION_XFER,
    COUNTRY_ID_CANADA,
    COUNTRY_ID_USA,
    PROVINCE_ID_AB,
)
from app.dashapp.metrics import BOOTSTRAP_RED_500
from app.dashapp.utils import get_id_triggered, get_value_from_query_dict
from app.email_stuff import send_email
from app.utils.complex import validate_email_address, validate_phone_number
from app.utils.simple import utcnow_naive

CLASS_NAME_DISPLAY_NONE = "d-none"
MODAL_TITLE_ERROR_STYLE = {"background-color": BOOTSTRAP_RED_500, "color": "white"}

APPLICATION_BOOST_STR = str(APPLICATION_BOOST)
APPLICATION_DGAS_STR = str(APPLICATION_DGAS)
APPLICATION_EGAS_CGP_STR = str(APPLICATION_EGAS_CGP)
APPLICATION_EGAS_GP_STR = str(APPLICATION_EGAS_GP)
APPLICATION_VRU_STR = str(APPLICATION_VRU)
APPLICATION_XFER_STR = str(APPLICATION_XFER)


def get_signals():
    """Signals for application form"""
    return html.Div(
        [
            # Hidden signal value, for running callback on page load
            html.Div(id="hidden_signal_application", style={"display": "none"}),
            dcc.Location(id="location_application", refresh=True),
            # dcc.Store(id="application_error_msg", storage_type="session", data=""),
            dcc.Store(
                id="application_store_app_type_id_str", storage_type="session", data=""
            ),
            # Olga wants a darker color for the application labels font, so here's a custom CSS stylesheet
            html.Link(
                rel="stylesheet",
                id="application_stylesheet",
            ),
        ]
    )


def get_product_radios():
    """Get the radios for the top-level products"""
    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Row(
                        dbc.Col(
                            [
                                dbc.Label("Choose a Product"),
                                dbc.RadioItems(
                                    id="application_product_radio",
                                    options=[
                                        {
                                            "label": "XFER",
                                            "value": str(APPLICATION_XFER),
                                        },
                                        {
                                            "label": "EGAS Casing Gas Compressor",
                                            "value": str(APPLICATION_EGAS_CGP),
                                        },
                                        {
                                            "label": "EGAS Gas Compressor",
                                            "value": str(APPLICATION_EGAS_GP),
                                        },
                                        {
                                            "label": "DGAS",
                                            "value": str(APPLICATION_DGAS),
                                        },
                                        {
                                            "label": "BOOST",
                                            "value": str(APPLICATION_BOOST),
                                        },
                                        {"label": "VRU", "value": str(APPLICATION_VRU)},
                                    ],
                                    value=APPLICATION_VRU,
                                    persistence=True,
                                ),
                            ],
                        ),
                        class_name="mb-auto",
                    ),
                    dbc.Row(
                        dbc.Col(
                            [
                                dbc.Label(
                                    "Install Location (LSD or Other)",
                                    class_name="mt-3 mb-0",
                                ),
                                dbc.Input(
                                    id="application_install_location",
                                    type="text",
                                    placeholder="12-31-007-05W2",
                                    persistence=True,
                                ),
                            ]
                        ),
                        # class_name="mt-3",
                        class_name="mb-3",
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "When do you need the equipment?",
                                        class_name="mt-3 mb-0 me-2",
                                    ),
                                ],
                            ),
                        ],
                        # class_name="mt-3",
                        # class_name="mb-3",
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dcc.DatePickerSingle(
                                        id="application_when_need_equipment",
                                        min_date_allowed=date.today(),
                                        # max_date_allowed=None,
                                        initial_visible_month=date.today(),
                                        date=date.today(),
                                        # style={"display": "inline-block"},
                                        # number_of_months_shown=3,
                                        persistence=True,
                                        persistence_type="memory",
                                    ),
                                ],
                            ),
                        ],
                        # class_name="mt-3",
                        class_name="mb-3",
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Cellular Signal Available on Location (for RCOM data / control)",
                                        class_name="mt-3 mb-0",
                                    ),
                                    dbc.RadioItems(
                                        options=[
                                            {"label": "Yes", "value": "yes"},
                                            {"label": "No", "value": "no"},
                                        ],
                                        value="yes",
                                        id="application_cell_signal_good_radio",
                                        inline=True,
                                        persistence=True,
                                    ),
                                ],
                            ),
                        ],
                        # class_name="mt-3",
                        class_name="mb-3",
                    ),
                    # dbc.Row(
                    #     dbc.Col(
                    #         [
                    #             dbc.Label(
                    #                 "Choose an Existing Company, or Create a New One",
                    #                 html_for="application_company_select",
                    #                 class_name="mt-3 mb-0",
                    #             ),
                    #             dbc.Select(
                    #                 id="application_company_select",
                    #                 persistence=True,
                    #             ),
                    #         ],
                    #     ),
                    #     class_name="mb-3",
                    # ),
                ],
                md=5,
                lg=4,
                xl=4,
                class_name="d-flex align-items-start flex-column order-last order-md-first mt-2",
            ),
            dbc.Col(
                [
                    html.Img(
                        id="application_product_img",
                        className="img-fluid",
                        # loading="eager",
                        sizes="100vw",
                        width=1400,
                        height=694,
                        # https://blog.hubspot.com/website/css-fade-in
                        style={
                            "animation": "fadeIn 5s",
                            "-webkit-animation": "fadeIn 5s",
                            "-moz-animation": "fadeIn 5s",
                            "-o-animation": "fadeIn 5s",
                            "-ms-animation": "fadeIn 5s",
                        },
                    )
                ],
                md=7,
                lg=8,
                xl=8,
                class_name="order-first order-md-last mb-2",
            ),
        ],
        className="mt-3",
    )


def company_info():
    """Form rows for entering company info"""
    return html.Div(
        [
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Company Name",
                                html_for="application_company_name",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_company_name",
                                type="text",
                                placeholder="Best Oil Co.",
                                persistence=True,
                            ),
                        ],
                    ),
                ],
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Company Street Address",
                                html_for="application_street",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_street",
                                type="text",
                                placeholder="1000 525 8th Ave SW",
                                persistence=True,
                            ),
                        ],
                        md=6,
                    ),
                    dbc.Col(
                        [
                            dbc.Label(
                                "Company City Name",
                                html_for="application_city",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_city",
                                type="text",
                                placeholder="Calgary",
                                persistence=True,
                            ),
                        ],
                        md=6,
                    ),
                ]
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Company Country",
                                html_for="application_country_id",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Select(
                                id="application_country_id",
                                value=COUNTRY_ID_CANADA,
                                persistence=True,
                            ),
                        ],
                        md=6,
                    ),
                    dbc.Col(
                        [
                            dbc.Label(
                                "Company Province/State",
                                html_for="application_province_id",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Select(
                                id="application_province_id",
                                value=PROVINCE_ID_AB,
                                persistence=True,
                            ),
                        ],
                        md=6,
                    ),
                ]
            ),
        ],
        id="application_company_info_div",
        # style={"display": "none"},
        className="mt-3",
    )


def other_form_info():
    """Form rows for entering other info"""
    return html.Div(
        [
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Contact Name",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_contact_name",
                                type="text",
                                placeholder="Murray Edwards",
                                persistence=True,
                            ),
                        ],
                        md=6,
                        lg=4,
                    ),
                    dbc.Col(
                        [
                            dbc.Label(
                                "Contact Email",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_contact_email",
                                type="email",
                                placeholder="<EMAIL>",
                                persistence=True,
                            ),
                            html.Div(id="application_contact_email_validation_div"),
                        ],
                        md=6,
                        lg=4,
                    ),
                    dbc.Col(
                        [
                            dbc.Label(
                                "Contact Phone",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_contact_phone",
                                type="tel",
                                placeholder="************",
                                persistence=True,
                            ),
                            html.Div(id="application_contact_phone_validation_div"),
                        ],
                        md=6,
                        lg=4,
                    ),
                ],
                class_name="mt-3",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Field Contact Name",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_field_contact_name",
                                type="text",
                                placeholder="Ralph Klein",
                                persistence=True,
                            ),
                        ],
                        md=6,
                        lg=4,
                    ),
                    dbc.Col(
                        [
                            dbc.Label(
                                "Field Contact Email",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_field_contact_email",
                                type="email",
                                placeholder="<EMAIL>",
                                persistence=True,
                            ),
                            html.Div(
                                id="application_field_contact_email_validation_div"
                            ),
                        ],
                        md=6,
                        lg=4,
                    ),
                    dbc.Col(
                        [
                            dbc.Label(
                                "Field Contact Phone",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Input(
                                id="application_field_contact_phone",
                                type="tel",
                                placeholder="************",
                                persistence=True,
                            ),
                            html.Div(
                                id="application_field_contact_phone_validation_div"
                            ),
                        ],
                        md=6,
                        lg=4,
                    ),
                ],
                class_name="mt-3",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Project Objective and Goals",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Textarea(
                                id="application_project_objective",
                                placeholder="Enter project objective here",
                                persistence=True,
                            ),
                        ],
                    ),
                ],
                class_name="mt-3",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Current Process / Equipment Being Used (if any)",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Textarea(
                                id="application_current_process_equipment",
                                placeholder="Enter current process / equipment here",
                                persistence=True,
                            ),
                        ],
                    ),
                ]
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Issues with Application / Current Equipment Looking to be Remedied",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Textarea(
                                id="application_current_process_issues",
                                placeholder="Enter current process issues here",
                                persistence=True,
                            ),
                        ],
                    ),
                ]
            ),
            # EGAS only
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "How many casings will be tied into the compressor?",
                                class_name="mt-3 mb-0",
                            ),
                            dbc.Input(
                                id="application_casing_count",
                                type="number",
                                placeholder="1",
                                persistence=True,
                            ),
                        ],
                        md=8,
                        lg=6,
                        xl=4,
                    ),
                ],
                id="application_casing_count_div",
                style={"display": "none"},
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Emulsion Coming From",
                                class_name="mt-2 mb-0",
                                id="application_emulsion_coming_from_label",
                            ),
                            dbc.Textarea(
                                id="application_emulsion_coming_from",
                                placeholder="Where is it coming from?",
                                persistence=True,
                            ),
                        ],
                        id="application_emulsion_coming_from_div",
                        style={"display": "none"},
                    ),
                    dbc.Col(
                        [
                            dbc.Label(
                                "Vapour Coming From",
                                class_name="mt-2 mb-0",
                            ),
                            dbc.Checklist(
                                options=[
                                    {"label": "Storage Tanks", "value": "tanks"},
                                    {
                                        "label": "Vapour Recovery Tanks",
                                        "value": "vr_tanks",
                                    },
                                ],
                                value=[],
                                id="application_vapour_coming_from_checklist",
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Textarea(
                                id="application_vapour_coming_off_other",
                                placeholder="Other",
                                persistence=True,
                            ),
                        ],
                        id="application_vapour_coming_from_div",
                        style={"display": "none"},
                    ),
                ],
                class_name="mt-3",
            ),
            dbc.Row(
                dbc.Col(
                    [
                        dbc.Label(
                            "Are there any fluid separators installed on site before the intended location of the EGAS gas compressor?",
                            class_name="mt-2 mb-0",
                            # id="application_separators_installed_label",
                        ),
                        dbc.RadioItems(
                            id="application_separators_installed_radio",
                            options=[
                                {"label": "Yes", "value": "yes"},
                                {"label": "No", "value": "no"},
                            ],
                            value="no",
                            inline=True,
                            persistence=True,
                        ),
                    ]
                ),
                class_name="mt-3",
                id="application_separators_installed_div",
                style={"display": "none"},
            ),
            dbc.Row(
                dbc.Col(
                    [
                        dbc.Label(
                            "Can the EGAS gas compressor be installed before the separators?",
                            class_name="mt-2 mb-0",
                            # id="application_separators_installed_label",
                        ),
                        dbc.RadioItems(
                            id="application_compressor_b4_separators_radio",
                            options=[
                                {"label": "Yes", "value": "yes"},
                                {"label": "No", "value": "no"},
                            ],
                            value="no",
                            inline=True,
                            persistence=True,
                        ),
                    ]
                ),
                class_name="mt-3",
                id="application_compressor_b4_separators_div",
                style={"display": "none"},
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Emulsion Discharging Into",
                                class_name="mt-2 mb-0",
                                id="application_discharging_into_label",
                            ),
                            dbc.RadioItems(
                                id="application_discharging_into_radio",
                                options=[
                                    {
                                        "label": "Emulsion Line",
                                        "value": "emulsion_line",
                                    },
                                    {"label": "Gas Line", "value": "gas_line"},
                                ],
                                value="emulsion_line",
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Textarea(
                                id="application_discharging_into",
                                placeholder="Enter emulsion discharging into here",
                                persistence=True,
                            ),
                        ],
                    ),
                ],
                class_name="mt-3",
                id="application_discharging_into_div",
                style={"display": "none"},
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Corrosive Elements (PPM or Percent)",
                                class_name="mt-3 mb-0",
                            ),
                            dbc.RadioItems(
                                id="application_use_ppm1_percent2_radio",
                                options=[
                                    {"label": "PPM", "value": 1},
                                    {"label": "Percent", "value": 2},
                                ],
                                value=1,
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label("H2S", className="mt-2 mb-0"),
                                            dbc.Input(
                                                id="application_h2s",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label("CO2", className="mt-2 mb-0"),
                                            dbc.Input(
                                                id="application_co2",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Salinity", className="mt-2 mb-0"
                                            ),
                                            dbc.Input(
                                                id="application_salinity",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                ]
                            ),
                        ],
                    ),
                ],
                class_name="mt-3",
                id="application_corrosive_elements_div",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Inlet / Suction Pressure",
                                class_name="mt-3 mb-0",
                                id="application_pressures_label",
                            ),
                            dbc.RadioItems(
                                id="application_use_psi1_kpa2_ozsqinch3_radio",
                                options=[
                                    {"label": "PSI", "value": 1},
                                    {"label": "KPA", "value": 2},
                                    {"label": "Oz / sq inch", "value": 3},
                                ],
                                value=1,
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Current Suction",
                                                className="mt-2 mb-0",
                                                id="application_current_suction_label",
                                            ),
                                            dbc.Input(
                                                id="application_suction_pressure_current",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                        id="application_current_suction_div",
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Tank Pressure Rating",
                                                className="mt-2 mb-0",
                                                id="application_tank_pressure_rating_label",
                                            ),
                                            dbc.Input(
                                                id="application_tank_pressure_rating",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                        id="application_tank_pressure_rating_div",
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Desired Suction",
                                                className="mt-2 mb-0",
                                                id="application_desired_suction_label",
                                            ),
                                            # This is also the desired tank pressure for VRU applications
                                            dbc.Input(
                                                id="application_suction_pressure_desired",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Flowline Discharge Pressure",
                                                className="mt-2 mb-0",
                                                id="application_discharge_pressure_label",
                                            ),
                                            dbc.Input(
                                                id="application_discharge_pressure",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                    # EGAS extra field for discharge pressure
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Flowline Pressure On Test",
                                                className="mt-2 mb-0",
                                                # id="application_discharge_pressure_label",
                                            ),
                                            dbc.Input(
                                                id="application_flowline_pressure_on_test",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                        id="application_flowline_pressure_on_test_div",
                                    ),
                                    # DGAS formation pressure
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Formation Pressure (if known)",
                                                className="mt-2 mb-0",
                                            ),
                                            dbc.Input(
                                                id="application_formation_pressure",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                        id="application_formation_pressure_div",
                                    ),
                                ]
                            ),
                        ],
                    ),
                ],
                class_name="mt-3",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Expected Volumes",
                                class_name="mt-3 mb-1",
                            ),
                            dbc.RadioItems(
                                id="application_use_e3m3d1_mcfd2",
                                options=[
                                    {"label": "e3m3 / day", "value": 1},
                                    {"label": "Mcf / day", "value": 2},
                                ],
                                value=1,
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Expected Gas Volume",
                                                className="mt-2 mb-0",
                                                id="application_expected_gas_volume_label",
                                            ),
                                            dbc.Input(
                                                id="application_expected_gas_volume",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Expected Water Volume",
                                                className="mt-2 mb-0",
                                            ),
                                            dbc.Input(
                                                id="application_expected_water_volume",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                        id="application_expected_water_volume_div",
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Expected Oil Volume",
                                                className="mt-2 mb-0",
                                            ),
                                            dbc.Input(
                                                id="application_expected_oil_volume",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                        id="application_expected_oil_volume_div",
                                    ),
                                ]
                            ),
                        ],
                    ),
                ],
                class_name="mt-3",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Emulsion Inlet Temperature",
                                class_name="mt-3 mb-0",
                                id="application_inlet_temp_label",
                            ),
                            dbc.RadioItems(
                                id="application_use_celsius1_fahrenheit2",
                                options=[
                                    {"label": "Celsius", "value": 1},
                                    {"label": "Fahrenheit", "value": 2},
                                ],
                                value=1,
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Input(
                                id="application_inlet_temp",
                                type="number",
                                placeholder=0,
                                persistence=True,
                            ),
                            html.Small(
                                "Please provide gas analysis if there is a max flowline temperature restriction",
                                className="text-muted",
                                id="application_inlet_temp_note",
                                style={"display": "none"},
                            ),
                        ],
                        md=6,
                        lg=5,
                        xl=4,
                    ),
                ],
                class_name="mt-3",
                id="application_inlet_temp_div",
                style={"display": "none"},
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Max Discharge Flowline Temperature",
                                class_name="mt-3 mb-0",
                                id="application_max_discharge_temp_label",
                            ),
                            dbc.Input(
                                id="application_max_discharge_temp",
                                type="number",
                                placeholder=0,
                                persistence=True,
                            ),
                            html.Small(
                                "If applicable (e.g. yellow jacket, poly, fibre lines)",
                                className="text-muted",
                                id="application_max_discharge_temp_note",
                                style={"display": "none"},
                            ),
                        ]
                    ),
                    dbc.Col(
                        [
                            dbc.Label(
                                "Max Ambient Temperature",
                                class_name="mt-3 mb-0",
                                id="application_max_ambient_temp_label",
                            ),
                            dbc.Input(
                                id="application_max_ambient_temp",
                                type="number",
                                placeholder=0,
                                persistence=True,
                            ),
                        ]
                    ),
                ],
                id="application_max_temp_div",
                class_name="mt-3",
                style={"display": "none"},
            ),
            html.Div(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Is well close to, or at, pumped off state?",
                                        class_name="mt-3 mb-1",
                                    ),
                                    dbc.RadioItems(
                                        id="application_well_pumped_off_radio",
                                        options=[
                                            {
                                                "label": "Not close to pumped off",
                                                "value": "not_close_to",
                                            },
                                            {
                                                "label": "Close to pumped off",
                                                "value": "close_to",
                                            },
                                            {
                                                "label": "Pumped off",
                                                "value": "pumped_off",
                                            },
                                        ],
                                        value="not_close_to",
                                        inline=True,
                                        persistence=True,
                                    ),
                                ]
                            ),
                        ],
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Current Pump Fillage (%)",
                                        class_name="mt-3 mb-1",
                                    ),
                                    dbc.Input(
                                        id="application_pump_fillage_pct",
                                        type="number",
                                        placeholder=0,
                                        persistence=True,
                                    ),
                                ],
                                sm=12,
                                md=8,
                                lg=6,
                                xl=4,
                            ),
                        ],
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Pump Jack Information",
                                        class_name="mt-3 mb-1",
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Make",
                                                        class_name="mt-1 mb-0",
                                                    ),
                                                    dbc.Input(
                                                        id="application_pump_make",
                                                        type="text",
                                                        placeholder="Lufkin",
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Model",
                                                        class_name="mt-1 mb-0",
                                                    ),
                                                    dbc.Input(
                                                        id="application_pump_model",
                                                        type="text",
                                                        placeholder="__-__-__",
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                        ]
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Pump Speed (SPM)",
                                                        class_name="mt-2 mb-0",
                                                    ),
                                                    dbc.Input(
                                                        id="application_pump_speed_spm",
                                                        type="number",
                                                        placeholder=0,
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Stroke Length (inches)",
                                                        class_name="mt-2 mb-0",
                                                    ),
                                                    dbc.Input(
                                                        id="application_pump_stroke_length",
                                                        type="number",
                                                        placeholder=0,
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Rod Load (lbs)",
                                                        class_name="mt-2 mb-0",
                                                    ),
                                                    dbc.Input(
                                                        id="application_pump_rod_load",
                                                        type="number",
                                                        placeholder=0,
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                        ]
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Can the pumpjack be put into long stroke?",
                                                        class_name="mt-2 mb-0",
                                                    ),
                                                    dbc.RadioItems(
                                                        id="application_pump_long_stroke_radio",
                                                        options=[
                                                            {
                                                                "label": "Yes",
                                                                "value": "yes",
                                                            },
                                                            {
                                                                "label": "No",
                                                                "value": "no",
                                                            },
                                                        ],
                                                        value="no",
                                                        inline=True,
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                        ]
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Set on cement base or piles?",
                                                        class_name="mt-2 mb-0",
                                                    ),
                                                    dbc.RadioItems(
                                                        id="application_pump_set_on_cement1_piles2_radio",
                                                        options=[
                                                            {
                                                                "label": "Cement",
                                                                "value": 1,
                                                            },
                                                            {
                                                                "label": "Piles",
                                                                "value": 2,
                                                            },
                                                        ],
                                                        value=1,
                                                        inline=True,
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Base Height (inches)",
                                                        class_name="mt-2 mb-0",
                                                    ),
                                                    dbc.Input(
                                                        id="application_pump_base_height",
                                                        type="number",
                                                        placeholder=0,
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                        ]
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Cement Base Setups",
                                                        class_name="mt-2 mb-0",
                                                    ),
                                                    dbc.Row(
                                                        [
                                                            dbc.Col(
                                                                [
                                                                    dbc.Label(
                                                                        "Number of Rails",
                                                                        class_name="mt-2 mb-0",
                                                                    ),
                                                                    dbc.Input(
                                                                        id="application_pump_num_rails",
                                                                        type="number",
                                                                        placeholder=0,
                                                                        persistence=True,
                                                                    ),
                                                                ]
                                                            ),
                                                            dbc.Col(
                                                                [
                                                                    dbc.Label(
                                                                        "Number of Tiedowns",
                                                                        class_name="mt-2 mb-0",
                                                                    ),
                                                                    dbc.Input(
                                                                        id="application_pump_num_tiedowns",
                                                                        type="number",
                                                                        placeholder=0,
                                                                        persistence=True,
                                                                    ),
                                                                ]
                                                            ),
                                                        ]
                                                    ),
                                                ]
                                            ),
                                        ]
                                    ),
                                ],
                            ),
                        ],
                    ),
                ],
                id="application_dgas_div",
                className="mt-3",
                style={"display": "none"},
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Artificial Lift System",
                                class_name="mt-3 mb-0",
                                id="application_artificial_lift_label",
                            ),
                            dbc.Textarea(
                                id="application_artificial_lift_system",
                                placeholder="ESP, PCP, rod lift, plunger lift, gas lift, etc",
                                persistence=True,
                            ),
                        ]
                    ),
                ],
                id="application_artificial_lift_div",
                class_name="mt-3",
                style={"display": "none"},
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Solids (Yes or No)",
                                class_name="mt-3 mb-0",
                            ),
                            dbc.Checklist(
                                options=[
                                    {"label": "Sand", "value": "sand"},
                                    {"label": "Frac Sand", "value": "frac_sand"},
                                    {"label": "Parafin", "value": "parafin"},
                                ],
                                value=[],
                                id="application_solids_checklist",
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Input(
                                "application_other_solids",
                                type="text",
                                placeholder="Other solids",
                                persistence=True,
                            ),
                        ],
                    ),
                ],
                id="application_solids_div",
                class_name="mt-3",
                style={"display": "none"},
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Pipeline Diameter (inches)",
                                class_name="mt-3 mb-0",
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Inlet",
                                                className="mt-1 mb-0",
                                            ),
                                            dbc.Input(
                                                id="application_pipeline_diameter_inlet_inches",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Discharge",
                                                className="mt-1 mb-0",
                                            ),
                                            dbc.Input(
                                                id="application_pipeline_diameter_discharge_inches",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                ]
                            ),
                        ],
                    ),
                ],
                id="application_pipeline_diameter_div",
                class_name="mt-3",
                style={"display": "none"},
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Electric Power Available on Location",
                                class_name="mt-3 mb-0",
                            ),
                            dbc.RadioItems(
                                options=[
                                    {"label": "Yes", "value": "yes"},
                                    {"label": "No", "value": "no"},
                                ],
                                value="yes",
                                id="application_power_available_radio",
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Checklist(
                                options=[
                                    {"label": "Grid", "value": "grid"},
                                    {"label": "Generator", "value": "generator"},
                                ],
                                value=[],
                                id="application_power_available_checklist",
                                inline=True,
                                persistence=True,
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Voltage",
                                                className="mt-2 mb-0",
                                            ),
                                            dbc.Input(
                                                id="application_power_voltage",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Phase",
                                                className="mt-2 mb-0",
                                            ),
                                            dbc.Input(
                                                id="application_power_phase",
                                                type="number",
                                                placeholder=3,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Amperage",
                                                className="mt-2 mb-0",
                                            ),
                                            dbc.Input(
                                                id="application_power_amps",
                                                type="number",
                                                placeholder=0,
                                                persistence=True,
                                            ),
                                        ],
                                        sm=4,
                                    ),
                                ]
                            ),
                        ],
                    ),
                ],
                class_name="mt-3",
                id="application_power_available_div",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Fuel Source Available on Location",
                                class_name="mt-3 mb-0",
                            ),
                            dbc.Checklist(
                                options=[
                                    {"label": "Propane", "value": "propane"},
                                    {"label": "Natural Gas", "value": "natural_gas"},
                                    {"label": "Diesel", "value": "diesel"},
                                ],
                                value=[],
                                id="application_fuel_source_checklist",
                                inline=True,
                                persistence=True,
                            ),
                        ],
                    ),
                ],
                class_name="mt-3",
                id="application_fuel_source_div",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Does the downhole and/or surface equipment in the area use special materials (metals) and/or coatings to handle the emulsion chemistry (e.g. nickel, epoxy, or nicarb coatings, N50 polished rods, tungsten or titanium metals, etc)?",
                                class_name="mt-3 mb-0",
                            ),
                            dbc.RadioItems(
                                options=[
                                    {"label": "Yes", "value": "yes"},
                                    {"label": "No", "value": "no"},
                                ],
                                value="no",
                                id="application_special_metals_needed",
                                inline=True,
                                persistence=True,
                            ),
                        ],
                    ),
                ],
                id="application_special_metals_needed_div",
                class_name="mt-3",
            ),
            dbc.Row(
                dbc.Col(
                    [
                        dbc.Label(
                            "Special Requirements / Additional Information",
                            class_name="mt-3 mb-0",
                        ),
                        dbc.Textarea(
                            id="application_special_requirements",
                            placeholder="Enter special requirements here",
                            persistence=True,
                        ),
                    ],
                ),
                class_name="mt-3",
            ),
            dbc.Row(
                dbc.Col(
                    [
                        dbc.Label(
                            "File Upload",
                            class_name="mt-3 mb-0",
                        ),
                        # https://dash.plotly.com/dash-core-components/upload
                        dcc.Upload(
                            id="application_file_upload",
                            children=html.Div(
                                [
                                    "Drag and Drop or ",
                                    html.A("Select Files"),
                                ],
                            ),
                            style={
                                "width": "100%",
                                "height": "80px",
                                "lineHeight": "80px",
                                "borderWidth": "1px",
                                "borderStyle": "dashed",
                                "borderRadius": "5px",
                                "textAlign": "center",
                                # "margin": "10px",
                            },
                            # Allow multiple files to be uploaded
                            multiple=True,
                        ),
                        html.Div(
                            id="application_file_upload_data_div",
                            style={"display": "none"},
                            className="mt-2",
                            children=dash_table.DataTable(
                                id="application_file_upload_data_table",
                                columns=[
                                    {"name": "File Name", "id": "file_name"},
                                    {"name": "Content Type", "id": "file_type"},
                                    {"name": "Contents", "id": "file_bytes"},
                                ],
                                hidden_columns=["file_bytes"],
                                # Don't show the "show-hide" columns button above the table
                                css=[
                                    {"selector": ".show-hide", "rule": "display: none"}
                                ],
                                row_deletable=True,
                                style_table={
                                    # "border": "none",
                                    "fontFamily": "Open Sans, sans-serif",
                                    "fontSize": "1rem",
                                    "width": "100%",
                                    "overflowX": "auto",  # 'auto' or 'scroll' (both seem to work, and not work at times...)
                                },
                                style_data={
                                    # "border": "none",
                                    "whiteSpace": "normal",
                                    "height": "auto",
                                    "fontFamily": "Open Sans, sans-serif",
                                    "fontSize": "1rem",
                                    "lineHeight": "1.5rem",
                                },
                                style_header={
                                    # "border": "none",
                                    "backgroundColor": "white",
                                    "fontFamily": "Open Sans, sans-serif",
                                    "fontSize": "1rem",
                                    # "fontWeight": "bold",
                                    # Wrap text (whiteSpace: "normal")
                                    "whiteSpace": "normal",
                                    "textAlign": "left",
                                    "lineHeight": "1.5rem",
                                },
                                style_cell={
                                    "fontSize": "1rem",
                                    "textAlign": "left",
                                    # Padding-left is important actually, both between columns and on the left
                                    "padding": "0.5rem",
                                    "color": "rgb(33, 37, 41)",
                                },
                                style_data_conditional=[
                                    {
                                        "if": {"row_index": "even"},
                                        "backgroundColor": "rgba(0, 0, 0, 0.05)",
                                    }
                                ],
                            ),
                        ),
                    ],
                    xs=12,
                    # md=10,
                    # lg=8,
                    # xl=6,
                ),
                class_name="mt-3",
            ),
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label(
                                "Note: Please provide a fluid analysis if available",
                                class_name="mt-3 mb-0",
                                id="application_note_label",
                            ),
                            dbc.Row(
                                dbc.Col(
                                    html.Img(
                                        id="application_note_img",
                                        className="img-fluid",
                                        # loading="eager",
                                        sizes="100vw",
                                        width=1454,
                                        height=1254,
                                        # https://blog.hubspot.com/website/css-fade-in
                                        style={
                                            "animation": "fadeIn 5s",
                                            "-webkit-animation": "fadeIn 5s",
                                            "-moz-animation": "fadeIn 5s",
                                            "-o-animation": "fadeIn 5s",
                                            "-ms-animation": "fadeIn 5s",
                                        },
                                    ),
                                ),
                                id="application_note_img_div",
                                style={"display": "none"},
                                class_name="mt-3",
                            ),
                        ],
                        sm=12,
                        md=12,
                        lg=10,
                        xl=8,
                        xxl=6,
                    ),
                ],
                class_name="mt-3",
            ),
        ],
        id="application_other_info_div",
        # style={"display": "none"},
        className="mt-3",
    )


def get_submit_button():
    """Get the submit button"""
    return html.Div(
        [
            dbc.Row(
                dbc.Col(
                    dbc.Button(
                        "Submit",
                        id="application_submit_btn",
                        className="float-end",
                        disabled=False,
                    ),
                ),
                class_name="mt-3",
            ),
            # Little message below the submit button
            dbc.Row(
                dbc.Col(
                    dbc.FormText(
                        id="application_submit_msg",
                        class_name="float-end",
                    )
                ),
                class_name="mt-1",
            ),
        ]
    )


def get_modal():
    """Modal for application"""
    return html.Div(
        [
            dbc.Modal(
                [
                    dbc.ModalHeader("Error", id="application_modal_header"),
                    dbc.ModalBody(id="application_modal_body"),
                    dbc.ModalFooter(
                        [
                            dbc.Button(
                                "Confirm",
                                id="application_modal_confirm_btn",
                                className="ml-auto me-2",
                                disabled=False,
                                color="danger",
                                # # Redirect after submitting form
                                # external_link=True,
                                # href=f"/{DASH_URL_RCOM}/",
                                # href=f"/{DASH_URL_APPLICATION}/",
                            ),
                            # html.A(
                            #     html.Button("Refresh Page"), href="relative_pathname"
                            # ),
                            dbc.Button(
                                "Cancel",
                                id="application_modal_cancel_btn",
                                # className="ml-auto",
                            ),
                        ]
                    ),
                ],
                id="application_modal",
                size="lg",
                centered=True,
                is_open=False,
            ),
        ]
    )


def application_layout():
    """Layout for application form"""

    # Mantine components for the nice date-picker
    # layout = dmc.MantineProvider(
    # If these are set to True, the Bootstrap 5 navbar at the top
    # uses less-bold font-weight for some reason
    # withGlobalClasses=False,
    # withNormalizeCSS=False,
    # The actual layout starts here
    layout = dbc.Container(
        [
            # Hidden signal value, for running callback on page load
            get_signals(),
            get_modal(),
            get_product_radios(),
            company_info(),
            other_form_info(),
            get_submit_button(),
        ]
    )
    # )

    return layout


def get_img_src(product_type: str) -> str:
    """Get the image source for the product type"""
    if product_type in (APPLICATION_XFER_STR, APPLICATION_BOOST_STR):
        return url_for(
            "static",
            filename="img/xfer/xfer-tri-multi-phase-transfer-pump-top-page-1400px.webp",
        )
    elif product_type in (APPLICATION_EGAS_CGP_STR, APPLICATION_EGAS_GP_STR):
        return url_for(
            "static",
            filename="img/egas/egas-823-on-gravel-1400px.webp",
        )
    elif product_type in (APPLICATION_VRU_STR,):
        return url_for(
            "static",
            filename="img/vru/ijack-vru-vapour-recovery-unit-hd-2500px.webp",
        )
    elif product_type in (APPLICATION_DGAS_STR,):
        return url_for(
            "static",
            filename="img/dgas/ijack-dgas-beam-mounted-casing-gas-compressor-on-traditional-pumpjack.jpg",
        )

    return no_update


def get_img_alt(product_type: str) -> str:
    """Get the image alt for the product type"""
    if product_type in (APPLICATION_XFER_STR, APPLICATION_BOOST_STR):
        return "IJACK dual XFER multiphase pump for oil and gas production, tied into satellite office"
    elif product_type in (APPLICATION_EGAS_CGP_STR, APPLICATION_EGAS_GP_STR):
        return "IJACK EGAS standalone casing gas compressor pump on gravel"
    elif product_type in (APPLICATION_VRU_STR,):
        return "IJACK Vapour Recovery Unit (VRU) eliminates emissions, venting, and flaring"
    elif product_type in (APPLICATION_DGAS_STR,):
        return "IJACK DGAS beam-mounted gas compressor reached target casing gas pressure on conventional oil well"

    return no_update


def get_img_srcSet(product_type: str) -> str:
    """Get the image srcSet for the product type"""
    if product_type in (APPLICATION_XFER_STR, APPLICATION_BOOST_STR):
        return {
            "600w": url_for(
                "static",
                filename="img/xfer/xfer-tri-multi-phase-transfer-pump-top-page-600px.webp",
            ),
            "1000w": url_for(
                "static",
                filename="img/xfer/xfer-tri-multi-phase-transfer-pump-top-page-1000px.webp",
            ),
            "1400w": url_for(
                "static",
                filename="img/xfer/xfer-tri-multi-phase-transfer-pump-top-page-1400px.webp",
            ),
        }
    elif product_type in (APPLICATION_EGAS_CGP_STR, APPLICATION_EGAS_GP_STR):
        return {
            "600w": url_for(
                "static",
                filename="img/egas/egas-823-on-gravel-600px.webp",
            ),
            "1000w": url_for(
                "static",
                filename="img/egas/egas-823-on-gravel-1000px.webp",
            ),
            "1400w": url_for(
                "static",
                filename="img/egas/egas-823-on-gravel-1400px.webp",
            ),
        }
    elif product_type in (APPLICATION_VRU_STR,):
        return {
            "500w": url_for(
                "static",
                filename="img/vru/ijack-vru-vapour-recovery-unit-hd-500px.webp",
            ),
            "1000w": url_for(
                "static",
                filename="img/vru/ijack-vru-vapour-recovery-unit-hd-1000px.webp",
            ),
            "1500w": url_for(
                "static",
                filename="img/vru/ijack-vru-vapour-recovery-unit-hd-1500px.webp",
            ),
            "2000w": url_for(
                "static",
                filename="img/vru/ijack-vru-vapour-recovery-unit-hd-2000px.webp",
            ),
            "2500w": url_for(
                "static",
                filename="img/vru/ijack-vru-vapour-recovery-unit-hd-2500px.webp",
            ),
        }
    elif product_type in (APPLICATION_DGAS_STR,):
        return None

    return no_update


# @callback(
#     Output("application_company_select", "options"),
#     Output("application_company_select", "value"),
#     Input("hidden_signal_application", "children"),
#     prevent_initial_call=False,
# )
# def get_existing_companies(_) -> list:
#     """Get a dropdown list of companies"""
#     customers: list = (
#         Customer.query.filter(
#             ~Customer.id.in_([CUSTOMER_ID_DEMO, CUSTOMER_ID_NO_CUSTOMER])
#         )
#         .order_by(Customer.customer)
#         .all()
#     )
#     options_list = [
#         {
#             "label": customer.formal if customer.formal else customer.customer,
#             "value": customer.id,
#         }
#         for customer in customers
#     ]
#     options_list.insert(0, {"label": "New Company", "value": 0})

#     return options_list, 0


@callback(
    Output("application_country_id", "options"),
    Output("application_province_id", "options"),
    Output("application_stylesheet", "href"),
    Input("hidden_signal_application", "children"),
    prevent_initial_call=False,
)
def get_page_load_stuff(_) -> list:
    """Get the stuff we need when the page loads"""
    countries: list = (
        Country.query.filter(~Country.id.in_([COUNTRY_ID_CANADA, COUNTRY_ID_USA]))
        .order_by(Country.country_name)
        .all()
    )
    options_list_countries = [
        {"label": country.country_name, "value": country.id} for country in countries
    ]
    # Insert US and Canada at the top of the list
    options_list_countries.insert(0, {"label": "Canada", "value": COUNTRY_ID_CANADA})
    options_list_countries.insert(
        0, {"label": "United States", "value": COUNTRY_ID_USA}
    )

    provinces: list = Province.query.order_by(Province.country_id, Province.name).all()
    options_list_provinces = [
        {"label": province.name, "value": province.id} for province in provinces
    ]
    # Add the "Other" option (for countries that don't have provinces/states)
    options_list_provinces.insert(0, {"label": "Other", "value": 0})

    custom_stylesheet_href = assets.url_for(
        "assets/src/css/styles_dash_application.css"
    )

    return options_list_countries, options_list_provinces, custom_stylesheet_href


@callback(
    Output("application_country_id", "value"),
    Input("application_province_id", "value"),
    prevent_initial_call=True,
)
def update_country_from_province_state(application_province_value):
    """If a province is selected, update the country, since we know it"""
    if not application_province_value:
        # This also captures the "Other" option, which has a value of "0"
        raise PreventUpdate

    try:
        country_id = db.session.get(Province, application_province_value).country_id
    except Exception:
        current_app.logger.exception(
            f"Error getting country_id from province_id '{application_province_value}'"
        )
        raise PreventUpdate

    return country_id


# @callback(
#     Output("application_company_info_div", "style"),
#     Input("application_company_select", "value"),
#     prevent_initial_call=True,
# )
# def show_new_company_form_info(application_company_select_value):
#     """If it's a new company, show the form to enter company info"""
#     if application_company_select_value in (0, "0"):
#         return {}
#     return {"display": "none"}


@callback(
    Output("application_store_app_type_id_str", "data"),
    Output("application_product_radio", "value"),
    Input("url", "search"),
    prevent_initial_call=True,
)
def store_url_search_params(url_search):
    """Get the unit_type_id from the query string"""
    if not url_search:
        raise PreventUpdate

    query_str: str = parse.urlsplit(url_search).query
    query_dict: dict = parse.parse_qs(query_str)

    app_type_id_str: str = get_value_from_query_dict(query_dict, "app_type_id")

    return app_type_id_str, app_type_id_str


# @callback(
#     Output("application_company_name", "value"),
#     Input("application_company_select", "value"),
#     prevent_initial_call=True,
# )
# def get_company_name(application_company_select_value):
#     """Get the company name from the database"""
#     if application_company_select_value in (None, 0, "0"):
#         raise PreventUpdate

#     try:
#         company_name = db.session.get(Customer, application_company_select_value).customer
#     except Exception:
#         current_app.logger.exception(
#             f"Error getting company name from customer_id '{application_company_select_value}'"
#         )
#         raise PreventUpdate

#     return company_name


@callback(
    Output("application_contact_email_validation_div", "children"),
    Output("application_contact_email", "invalid"),
    Input("application_contact_email", "n_blur"),
    Input("application_submit_btn", "n_clicks"),
    State("application_contact_email", "value"),
    prevent_initial_call=True,
)
def is_main_email_invalid(n_blur, n_clicks, application_contact_email_value):
    """Validate the email address"""

    def return_vars(msg: str, is_valid: bool = True):
        return msg, not is_valid

    if not application_contact_email_value:
        return return_vars("Please enter an email address.", False)

    return_class: SimpleNamespace = validate_email_address(
        application_contact_email_value
    )

    return return_vars(return_class.msg, return_class.is_valid)


@callback(
    Output("application_field_contact_email_validation_div", "children"),
    Output("application_field_contact_email", "invalid"),
    Input("application_field_contact_email", "n_blur"),
    Input("application_submit_btn", "n_clicks"),
    State("application_field_contact_email", "value"),
    prevent_initial_call=True,
)
def is_field_contact_email_invalid(
    n_blur, n_clicks, application_field_contact_email_value
):
    """Validate the email address"""

    def return_vars(msg: str, is_valid: bool = True):
        return msg, not is_valid

    if not application_field_contact_email_value:
        return return_vars("Please enter an email address.", False)

    return_class: SimpleNamespace = validate_email_address(
        application_field_contact_email_value
    )

    return return_vars(return_class.msg, return_class.is_valid)


@callback(
    Output("application_contact_phone_validation_div", "children"),
    Output("application_contact_phone", "invalid"),
    Input("application_contact_phone", "n_blur"),
    Input("application_submit_btn", "n_clicks"),
    State("application_contact_phone", "value"),
    prevent_initial_call=True,
)
def is_main_phone_invalid(n_blur, n_clicks, application_contact_phone_value):
    """Validate the phone number"""

    def return_vars(msg: str, is_valid: bool = True):
        return msg, not is_valid

    if not application_contact_phone_value:
        return return_vars("Please enter a phone number.", False)

    return_class: SimpleNamespace = validate_phone_number(
        application_contact_phone_value
    )

    return return_vars(return_class.msg, return_class.is_valid)


@callback(
    Output("application_field_contact_phone_validation_div", "children"),
    Output("application_field_contact_phone", "invalid"),
    Input("application_field_contact_phone", "n_blur"),
    Input("application_submit_btn", "n_clicks"),
    State("application_field_contact_phone", "value"),
    prevent_initial_call=True,
)
def is_field_contact_phone_invalid(
    n_blur, n_clicks, application_field_contact_phone_value
):
    """Validate the field contact phone number"""

    def return_vars(msg: str, is_valid: bool = True):
        return msg, not is_valid

    if not application_field_contact_phone_value:
        return return_vars("Please enter a phone number.", False)

    return_class: SimpleNamespace = validate_phone_number(
        application_field_contact_phone_value
    )

    return return_vars(return_class.msg, return_class.is_valid)


@callback(
    Output("application_emulsion_coming_from_label", "children"),
    Output("application_emulsion_coming_from_div", "style"),
    Output("application_vapour_coming_from_div", "style"),
    Output("application_discharging_into_div", "style"),
    Output("application_discharging_into_radio", "style"),
    Output("application_discharging_into_label", "children"),
    Output("application_discharging_into", "placeholder"),
    Output("application_corrosive_elements_div", "style"),
    Output("application_current_suction_div", "style"),
    Output("application_tank_pressure_rating_div", "style"),
    Output("application_pressures_label", "children"),
    Output("application_current_suction_label", "children"),
    Output("application_desired_suction_label", "children"),
    Output("application_discharge_pressure_label", "children"),
    Output("application_expected_water_volume_div", "style"),
    Output("application_expected_oil_volume_div", "style"),
    Output("application_expected_gas_volume_label", "children"),
    Output("application_inlet_temp_label", "children"),
    Output("application_max_temp_div", "style"),
    Output("application_solids_div", "style"),
    Output("application_pipeline_diameter_div", "style"),
    Output("application_special_metals_needed_div", "style"),
    Output("application_casing_count_div", "style"),
    Output("application_flowline_pressure_on_test_div", "style"),
    Output("application_note_label", "children"),
    Output("application_artificial_lift_div", "style"),
    Output("application_separators_installed_div", "style"),
    Output("application_compressor_b4_separators_div", "style", allow_duplicate=True),
    Output("application_inlet_temp_note", "style"),
    Output("application_max_discharge_temp_note", "style"),
    Output("application_inlet_temp_div", "style"),
    Output("application_dgas_div", "style"),
    Output("application_formation_pressure_div", "style"),
    Output("application_power_available_div", "style"),
    Output("application_fuel_source_div", "style"),
    Output("application_note_img_div", "style"),
    Output("application_note_img", "src"),
    Output("application_product_img", "src"),
    Output("application_product_img", "alt"),
    Output("application_product_img", "srcSet"),
    # Output("application_product_img", "style"),
    Input("application_product_radio", "value"),
    # We want "False" here but allow_duplicate=True requires prevent_initial_call to be True or "initial_duplicate"
    prevent_initial_call="initial_duplicate",
)
def hide_fields_based_on_product_type(application_product_radio_value: str):
    """Hide fields based on the product type"""
    if not application_product_radio_value:
        raise PreventUpdate

    # Convert to string, just in case
    application_product_radio_value = str(application_product_radio_value)

    def return_vars(
        application_emulsion_coming_from_label_children,
        application_emulsion_coming_from_div_style,
        application_vapour_coming_from_div_style,
        application_discharging_into_div_style,
        application_discharging_into_radio_style,
        application_discharging_into_label_children,
        application_discharging_into_placeholder,
        application_corrosive_elements_div_style,
        application_current_suction_div_style,
        application_tank_pressure_rating_div_style,
        application_pressures_label_children,
        application_current_suction_label_children,
        application_desired_suction_label_children,
        application_discharge_pressure_label_children,
        application_expected_water_volume_div_style,
        application_expected_oil_volume_div_style,
        application_expected_gas_volume_label_children,
        application_inlet_temp_label_children,
        application_max_temp_div_style,
        application_solids_div_style,
        application_pipeline_diameter_div_style,
        application_special_metals_needed_div_style,
        application_casing_count_div_style,
        application_flowline_pressure_on_test_div_style,
        application_note_label_children,
        application_artificial_lift_div_style,
        application_separators_installed_div_style,
        application_compressor_b4_separators_div_style,
        application_inlet_temp_note_style,
        application_max_discharge_temp_note_style,
        application_inlet_temp_div_style,
        application_dgas_div_style,
        application_formation_pressure_div_style,
        application_power_available_div_style,
        application_fuel_source_div_style,
        application_note_img_div_style,
        application_note_img_src,
        application_product_img_src: str = None,
        application_product_img_alt: str = None,
        application_product_img_srcSet: str = None,
        # application_product_img_style,
    ):
        return (
            application_emulsion_coming_from_label_children,
            application_emulsion_coming_from_div_style,
            application_vapour_coming_from_div_style,
            application_discharging_into_div_style,
            application_discharging_into_radio_style,
            application_discharging_into_label_children,
            application_discharging_into_placeholder,
            application_corrosive_elements_div_style,
            application_current_suction_div_style,
            application_tank_pressure_rating_div_style,
            application_pressures_label_children,
            application_current_suction_label_children,
            application_desired_suction_label_children,
            application_discharge_pressure_label_children,
            application_expected_water_volume_div_style,
            application_expected_oil_volume_div_style,
            application_expected_gas_volume_label_children,
            application_inlet_temp_label_children,
            application_max_temp_div_style,
            application_solids_div_style,
            application_pipeline_diameter_div_style,
            application_special_metals_needed_div_style,
            application_casing_count_div_style,
            application_flowline_pressure_on_test_div_style,
            application_note_label_children,
            application_artificial_lift_div_style,
            application_separators_installed_div_style,
            application_compressor_b4_separators_div_style,
            application_inlet_temp_note_style,
            application_max_discharge_temp_note_style,
            application_inlet_temp_div_style,
            application_dgas_div_style,
            application_formation_pressure_div_style,
            application_power_available_div_style,
            application_fuel_source_div_style,
            application_note_img_div_style,
            application_note_img_src,
            application_product_img_src or get_img_src(application_product_radio_value),
            application_product_img_alt or get_img_alt(application_product_radio_value),
            application_product_img_srcSet
            or get_img_srcSet(application_product_radio_value),
            # {
            #     "animation": "fadeIn 5s",
            #     "-webkit-animation": "fadeIn 5s",
            #     "-moz-animation": "fadeIn 5s",
            #     "-o-animation": "fadeIn 5s",
            #     "-ms-animation": "fadeIn 5s",
            # },
        )

    if application_product_radio_value in (
        APPLICATION_XFER_STR,
        APPLICATION_BOOST_STR,
    ):
        return return_vars(
            application_emulsion_coming_from_label_children="Emulsion Coming From",
            application_emulsion_coming_from_div_style={},
            application_vapour_coming_from_div_style={"display": "none"},
            application_discharging_into_div_style={},
            application_discharging_into_radio_style={"display": "none"},
            application_discharging_into_label_children="Emulsion Discharging Into",
            application_discharging_into_placeholder="Emulsion Discharging Into",
            application_corrosive_elements_div_style={},
            application_current_suction_div_style={},
            application_tank_pressure_rating_div_style={"display": "none"},
            application_pressures_label_children="Pressures",
            application_current_suction_label_children="Inlet / Suction Pressure",
            application_desired_suction_label_children="Desired Suction",
            application_discharge_pressure_label_children="Flowline Discharge Pressure",
            application_expected_water_volume_div_style={},
            application_expected_oil_volume_div_style={},
            application_expected_gas_volume_label_children="Expected Gas Volume",
            application_inlet_temp_label_children="Emulsion Inlet Temperature",
            application_max_temp_div_style={"display": "none"},
            application_solids_div_style={},
            application_pipeline_diameter_div_style={},
            application_special_metals_needed_div_style={},
            application_casing_count_div_style={"display": "none"},
            application_flowline_pressure_on_test_div_style={"display": "none"},
            application_note_label_children="Note: Please provide a fluid analysis if available",
            application_artificial_lift_div_style={"display": "none"},
            application_separators_installed_div_style={"display": "none"},
            application_compressor_b4_separators_div_style={"display": "none"},
            application_inlet_temp_note_style={"display": "none"},
            application_max_discharge_temp_note_style={"display": "none"},
            application_inlet_temp_div_style={},
            application_dgas_div_style={"display": "none"},
            application_formation_pressure_div_style={"display": "none"},
            application_power_available_div_style={},
            application_fuel_source_div_style={},
            application_note_img_div_style={"display": "none"},
            application_note_img_src=no_update,
        )

    elif application_product_radio_value in (
        APPLICATION_EGAS_CGP_STR,
        APPLICATION_EGAS_GP_STR,
    ):
        if application_product_radio_value == APPLICATION_EGAS_CGP_STR:
            application_current_suction_label_children = "Current Casing Pressure"
            application_desired_suction_label_children = "Desired Casing Pressure"
            application_emulsion_coming_from_div_style = {"display": "none"}
            application_casing_count_div_style = {}
            application_separators_installed_div_style = {"display": "none"}
            application_compressor_b4_separators_div_style = {"display": "none"}
            application_flowline_pressure_on_test_div_style = {}
            application_artificial_lift_div_style = {}
        else:
            application_current_suction_label_children = "Current Suction Pressure"
            application_desired_suction_label_children = "Desired Suction Pressure"
            application_emulsion_coming_from_div_style = {}
            application_casing_count_div_style = {"display": "none"}
            application_separators_installed_div_style = {}
            application_compressor_b4_separators_div_style = {}
            application_flowline_pressure_on_test_div_style = {"display": "none"}
            application_artificial_lift_div_style = {"display": "none"}

        return return_vars(
            application_emulsion_coming_from_label_children="Gas Coming From",
            application_emulsion_coming_from_div_style=application_emulsion_coming_from_div_style,
            application_vapour_coming_from_div_style={"display": "none"},
            application_discharging_into_div_style={},
            application_discharging_into_radio_style={},
            application_discharging_into_label_children="Gas Discharging Into",
            application_discharging_into_placeholder="Gas Discharging Into",
            application_corrosive_elements_div_style={},
            application_current_suction_div_style={},
            application_tank_pressure_rating_div_style={"display": "none"},
            application_pressures_label_children="Pressures",
            application_current_suction_label_children=application_current_suction_label_children,
            application_desired_suction_label_children=application_desired_suction_label_children,
            application_discharge_pressure_label_children="Discharge Pressure",
            application_expected_water_volume_div_style={"display": "none"},
            application_expected_oil_volume_div_style={"display": "none"},
            application_expected_gas_volume_label_children="Expected Gas Volume",
            application_inlet_temp_label_children="Gas Inlet Temperature",
            application_max_temp_div_style={},
            application_solids_div_style={"display": "none"},
            application_pipeline_diameter_div_style={"display": "none"},
            application_special_metals_needed_div_style={"display": "none"},
            application_casing_count_div_style=application_casing_count_div_style,
            application_flowline_pressure_on_test_div_style=application_flowline_pressure_on_test_div_style,
            application_note_label_children="""Note: The best results for increased production and/or lowering the decline rate will be achieved
in wells at or close to pump off state, with low bottom hole pressure and where maintaining 80% constant pump fillage is difficult.""",
            application_artificial_lift_div_style=application_artificial_lift_div_style,
            application_separators_installed_div_style=application_separators_installed_div_style,
            application_compressor_b4_separators_div_style=application_compressor_b4_separators_div_style,
            application_inlet_temp_note_style={},
            application_max_discharge_temp_note_style={},
            application_inlet_temp_div_style={},
            application_dgas_div_style={"display": "none"},
            application_formation_pressure_div_style={"display": "none"},
            application_power_available_div_style={},
            application_fuel_source_div_style={},
            application_note_img_div_style={"display": "none"},
            application_note_img_src=no_update,
        )

    elif application_product_radio_value in (APPLICATION_VRU_STR,):
        return return_vars(
            application_emulsion_coming_from_label_children="Gas Coming From",
            application_emulsion_coming_from_div_style={"display": "none"},
            application_vapour_coming_from_div_style={},
            application_discharging_into_div_style={},
            application_discharging_into_radio_style={"display": "none"},
            application_discharging_into_label_children="Vapour Discharging Into",
            application_discharging_into_placeholder="Vapour Discharging Into",
            application_corrosive_elements_div_style={"display": "none"},
            application_current_suction_div_style={"display": "none"},
            application_tank_pressure_rating_div_style={},
            application_pressures_label_children="Pressures",
            application_current_suction_label_children=no_update,
            application_desired_suction_label_children="Desired Tank Pressure",
            application_discharge_pressure_label_children="Discharge Line Pressure",
            application_expected_water_volume_div_style={"display": "none"},
            application_expected_oil_volume_div_style={"display": "none"},
            application_expected_gas_volume_label_children="Expected Vapour Volume",
            application_inlet_temp_label_children="Vapour Inlet Temperature",
            application_max_temp_div_style={},
            application_solids_div_style={"display": "none"},
            application_pipeline_diameter_div_style={"display": "none"},
            application_special_metals_needed_div_style={"display": "none"},
            application_casing_count_div_style={"display": "none"},
            application_flowline_pressure_on_test_div_style={"display": "none"},
            application_note_label_children="Note: Please provide a fluid analysis if available",
            application_artificial_lift_div_style={"display": "none"},
            application_separators_installed_div_style={"display": "none"},
            application_compressor_b4_separators_div_style={"display": "none"},
            application_inlet_temp_note_style={"display": "none"},
            application_max_discharge_temp_note_style={"display": "none"},
            application_inlet_temp_div_style={},
            application_dgas_div_style={"display": "none"},
            application_formation_pressure_div_style={"display": "none"},
            application_power_available_div_style={},
            application_fuel_source_div_style={},
            application_note_img_div_style={"display": "none"},
            application_note_img_src=no_update,
        )

    elif application_product_radio_value in (APPLICATION_DGAS_STR,):
        return return_vars(
            application_emulsion_coming_from_label_children=no_update,
            application_emulsion_coming_from_div_style={"display": "none"},
            application_vapour_coming_from_div_style={"display": "none"},
            application_discharging_into_div_style={"display": "none"},
            application_discharging_into_radio_style={},
            application_discharging_into_label_children=no_update,
            application_discharging_into_placeholder=no_update,
            application_corrosive_elements_div_style={"display": "none"},
            application_current_suction_div_style={},
            application_tank_pressure_rating_div_style={"display": "none"},
            application_pressures_label_children="Casing Pressure",
            application_current_suction_label_children="Current Casing Pressure",
            application_desired_suction_label_children="Desired Casing Pressure",
            application_discharge_pressure_label_children="Current Flowline Pressure",
            application_expected_water_volume_div_style={"display": "none"},
            application_expected_oil_volume_div_style={"display": "none"},
            application_expected_gas_volume_label_children="Expected Gas Volume",
            application_inlet_temp_label_children=no_update,
            application_max_temp_div_style={"display": "none"},
            application_solids_div_style={"display": "none"},
            application_pipeline_diameter_div_style={"display": "none"},
            application_special_metals_needed_div_style={"display": "none"},
            application_casing_count_div_style={"display": "none"},
            application_flowline_pressure_on_test_div_style={},
            application_note_label_children="""
Note: The best results for increased production and/or lowering the decline rate will be achieved in wells
at or close to pump off state, with low bottom hole pressure and where maintaining 80% constant pump fillage is difficult.""",
            application_artificial_lift_div_style={"display": "none"},
            application_separators_installed_div_style={"display": "none"},
            application_compressor_b4_separators_div_style={"display": "none"},
            application_inlet_temp_note_style={"display": "none"},
            application_max_discharge_temp_note_style={"display": "none"},
            application_inlet_temp_div_style={"display": "none"},
            application_dgas_div_style={},
            application_formation_pressure_div_style={},
            application_power_available_div_style={"display": "none"},
            application_fuel_source_div_style={"display": "none"},
            application_note_img_div_style={},
            application_note_img_src=url_for(
                "static",
                filename="img/dgas/dgas-application-diagram.png",
            ),
        )

    raise PreventUpdate


@callback(
    Output("application_compressor_b4_separators_div", "style", allow_duplicate=True),
    Input("application_separators_installed_radio", "value"),
    prevent_initial_call=True,
)
def show_compressor_b4_separators_div(application_separators_installed_radio_value):
    """Show the compressor before separators div if separators are installed"""
    if application_separators_installed_radio_value == "yes":
        return {}
    return {"display": "none"}


@callback(
    Output("application_file_upload_data_table", "data"),
    Output("application_file_upload_data_div", "style"),
    Input("application_file_upload", "contents"),
    State("application_file_upload", "filename"),
    # State("application_file_upload", "last_modified"),
    State("application_file_upload_data_table", "data"),
    prevent_initial_call=True,
)
def display_upload_files(
    list_of_contents,
    list_of_names,
    #  list_of_dates,
    data,
):
    """Upload a file"""
    if not list_of_contents:
        raise PreventUpdate

    data = data or []
    for contents, name in zip(list_of_contents, list_of_names):
        content_type, content_string = contents.split(",")
        data.append(
            {
                "file_name": name,
                "file_type": content_type,
                "file_bytes": content_string,
            }
        )

    if data:
        style = {}
    else:
        style = {"display": "none"}

    return data, style


@callback(
    Output("application_modal", "is_open", allow_duplicate=True),
    # Output("application_modal_confirm_btn", "disabled"),
    Output("application_modal_header", "children"),
    Output("application_modal_body", "children"),
    Output("application_submit_msg", "children", allow_duplicate=True),
    Output("application_submit_msg", "color", allow_duplicate=True),
    Input("application_submit_btn", "n_clicks"),
    # For validation
    State("application_field_contact_email", "value"),
    State("application_field_contact_phone", "value"),
    State("application_contact_email", "value"),
    State("application_contact_phone", "value"),
    # State("application_error_msg", "data"),
    prevent_initial_call=True,
)
def application_submit_modal(
    submit_btn_n_clicks,
    application_field_contact_email_value,
    application_field_contact_phone_value,
    application_contact_email_value,
    application_contact_phone_value,
    # application_error_msg_data,
) -> tuple:
    """
    Callback for when the user submits the form.
    Opens the modal and displays a message, and asks for confirmation
    """
    if not submit_btn_n_clicks:
        raise PreventUpdate

    def return_vars(
        application_modal_is_open: bool,
        # application_modal_confirm_btn_disabled: bool,
        application_modal_header: str,
        application_modal_body: str,
        application_submit_msg: str = None,
        application_submit_msg_color: str = None,
    ) -> tuple:
        return (
            application_modal_is_open,
            # application_modal_confirm_btn_disabled,
            application_modal_header,
            application_modal_body,
            application_submit_msg,
            application_submit_msg_color,
        )

    # Last chance to validate the form
    (_, invalid) = is_main_email_invalid(1, 1, application_contact_email_value)
    if invalid:
        return return_vars(
            False,
            no_update,
            no_update,
            "Please enter a valid main email address.",
            "danger",
        )

    (_, invalid) = is_field_contact_email_invalid(
        1, 1, application_field_contact_email_value
    )
    if invalid:
        return return_vars(
            False,
            no_update,
            no_update,
            "Please enter a valid field contact email address.",
            "danger",
        )

    (_, invalid) = is_main_phone_invalid(1, 1, application_contact_phone_value)
    if invalid:
        return return_vars(
            False,
            no_update,
            no_update,
            "Please enter a valid main phone number.",
            "danger",
        )

    (_, invalid) = is_field_contact_phone_invalid(
        1, 1, application_field_contact_phone_value
    )
    if invalid:
        return return_vars(
            False,
            no_update,
            no_update,
            "Please enter a valid field contact phone number.",
            "danger",
        )

    # id_triggered: str = get_id_triggered()
    # if application_error_msg_data and id_triggered == "application_error_msg.data":
    #     return return_vars(
    #         application_modal_is_open=True,
    #         application_modal_header="Error Updating Database",
    #         application_modal_body=application_error_msg_data,
    #     )

    modal_body = "Please confirm you want to submit the application."

    return return_vars(
        application_modal_is_open=True,
        application_modal_header="Please Confirm",
        application_modal_body=modal_body,
        application_submit_msg=no_update,
        application_submit_msg_color=no_update,
    )


@callback(
    Output("application_modal", "is_open", allow_duplicate=True),
    Output("application_submit_msg", "children", allow_duplicate=True),
    Output("application_submit_msg", "color", allow_duplicate=True),
    Input("application_modal_confirm_btn", "n_clicks"),
    Input("application_modal_cancel_btn", "n_clicks"),
    State("application_product_radio", "value"),
    # State("application_company_select", "value"),
    State("application_company_name", "value"),
    State("application_street", "value"),
    State("application_city", "value"),
    State("application_province_id", "value"),
    State("application_country_id", "value"),
    State("application_contact_name", "value"),
    State("application_contact_phone", "value"),
    State("application_contact_email", "value"),
    State("application_field_contact_name", "value"),
    State("application_field_contact_phone", "value"),
    State("application_field_contact_email", "value"),
    State("application_install_location", "value"),
    State("application_project_objective", "value"),
    State("application_current_process_equipment", "value"),
    State("application_current_process_issues", "value"),
    # XFER-specific stuff
    State("application_emulsion_coming_from", "value"),
    State("application_discharging_into", "value"),
    # EGAS
    State("application_discharging_into_radio", "value"),
    # VRU
    State("application_vapour_coming_from_checklist", "value"),
    State("application_vapour_coming_off_other", "value"),
    State("application_tank_pressure_rating", "value"),
    # Corrosive elements
    State("application_use_ppm1_percent2_radio", "value"),
    State("application_h2s", "value"),
    State("application_co2", "value"),
    State("application_salinity", "value"),
    State("application_use_psi1_kpa2_ozsqinch3_radio", "value"),
    State("application_suction_pressure_current", "value"),
    State("application_suction_pressure_desired", "value"),
    State("application_discharge_pressure", "value"),
    # Use e3m3/d or Mcf/d
    State("application_use_e3m3d1_mcfd2", "value"),
    State("application_expected_gas_volume", "value"),
    State("application_expected_water_volume", "value"),
    State("application_expected_oil_volume", "value"),
    # Use C or F
    State("application_use_celsius1_fahrenheit2", "value"),
    # Max temperatures for VRU
    State("application_max_discharge_temp", "value"),
    State("application_max_ambient_temp", "value"),
    State("application_inlet_temp", "value"),
    State("application_pipeline_diameter_inlet_inches", "value"),
    State("application_pipeline_diameter_discharge_inches", "value"),
    # Power on site
    State("application_power_available_radio", "value"),
    State("application_power_available_checklist", "value"),
    State("application_power_voltage", "value"),
    State("application_power_phase", "value"),
    State("application_power_amps", "value"),
    # Fuel sources available
    State("application_fuel_source_checklist", "value"),
    # Cellular signal strength
    State("application_cell_signal_good_radio", "value"),
    # Special metals/coatings needed
    State("application_special_metals_needed", "value"),
    # Special requirements/additional info
    State("application_special_requirements", "value"),
    State("application_when_need_equipment", "date"),
    State("application_solids_checklist", "value"),
    State("application_other_solids", "value"),
    # EGAS
    State("application_casing_count", "value"),
    State("application_flowline_pressure_on_test", "value"),
    State("application_artificial_lift_system", "value"),
    State("application_separators_installed_radio", "value"),
    State("application_compressor_b4_separators_radio", "value"),
    # DGAS
    State("application_well_pumped_off_radio", "value"),
    State("application_pump_fillage_pct", "value"),
    State("application_formation_pressure", "value"),
    State("application_pump_make", "value"),
    State("application_pump_model", "value"),
    State("application_pump_speed_spm", "value"),
    State("application_pump_stroke_length", "value"),
    State("application_pump_rod_load", "value"),
    State("application_pump_long_stroke_radio", "value"),
    State("application_pump_set_on_cement1_piles2_radio", "value"),
    State("application_pump_base_height", "value"),
    State("application_pump_num_rails", "value"),
    State("application_pump_num_tiedowns", "value"),
    State("application_file_upload_data_table", "data"),
    prevent_initial_call=True,
)
def application_modal_submit_and_close(
    application_modal_confirm_btn_n_clicks,
    application_modal_cancel_btn_n_clicks,
    application_product_radio,
    # application_company_select,
    application_company_name,
    application_street,
    application_city,
    application_province_id,
    application_country_id,
    application_contact_name,
    application_contact_phone,
    application_contact_email,
    application_field_contact_name,
    application_field_contact_phone,
    application_field_contact_email,
    application_install_location,
    application_project_objective,
    application_current_process_equipment,
    application_current_process_issues,
    # XFER-specific stuff
    application_emulsion_coming_from,
    application_discharging_into,
    # EGAS
    application_discharging_into_radio_value,
    # VRU
    application_vapour_coming_from_checklist_value,
    application_vapour_coming_off_other,
    application_tank_pressure_rating,
    # Corrosive elements
    application_use_ppm1_percent2_radio_value,
    application_h2s,
    application_co2,
    application_salinity,
    application_use_psi1_kpa2_ozsqinch3_radio_value,
    application_suction_pressure_current,
    application_suction_pressure_desired,
    application_discharge_pressure,
    # Use e3m3/d or Mcf/d
    application_use_e3m3d1_mcfd2_value,
    application_expected_gas_volume,
    application_expected_water_volume,
    application_expected_oil_volume,
    # Use C or F
    application_use_celsius1_fahrenheit2_value,
    # Max temperatures for VRU
    application_max_discharge_temp,
    application_max_ambient_temp,
    application_inlet_temp,
    application_pipeline_diameter_inlet_inches,
    application_pipeline_diameter_discharge_inches,
    # Power on site
    application_power_available_radio_value,
    application_power_available_checklist_value,
    application_power_voltage,
    application_power_phase,
    application_power_amps,
    # Fuel sources available
    application_fuel_source_checklist_value,
    # Cellular signal strength
    application_cell_signal_good_radio_value,
    # Special metals/coatings needed
    application_special_metals_needed_value,
    # Special requirements/additional info
    application_special_requirements,
    application_when_need_equipment_date,
    application_solids_checklist_value,
    application_other_solids,
    # EGAS
    application_casing_count,
    application_flowline_pressure_on_test,
    application_artificial_lift_system,
    application_separators_installed_radio_value,
    application_compressor_b4_separators_radio_value,
    # DGAS
    application_well_pumped_off_radio_value,
    application_pump_fillage_pct,
    application_formation_pressure,
    application_pump_make,
    application_pump_model,
    application_pump_speed_spm,
    application_pump_stroke_length,
    application_pump_rod_load,
    application_pump_long_stroke_radio_value,
    application_pump_set_on_cement1_piles2_radio_value,
    application_pump_base_height,
    application_pump_num_rails,
    application_pump_num_tiedowns,
    # Upload files
    application_file_upload_data_table_data,
) -> bool:
    """Callback to submit the form to the database and close the modal"""

    def return_vars(
        application_modal_is_open: bool = False,
        application_submit_msg_children: str = no_update,
        application_submit_msg_color: str = no_update,
    ) -> tuple:
        return (
            application_modal_is_open,
            application_submit_msg_children,
            application_submit_msg_color,
        )

    id_triggered: str = get_id_triggered()
    if id_triggered == "application_modal_cancel_btn.n_clicks":
        # Close the modal
        return return_vars(
            application_modal_is_open=False,
            application_submit_msg_children="Not submitted.",
            application_submit_msg_color="danger",
        )

    if (
        id_triggered != "application_modal_confirm_btn.n_clicks"
        or not application_modal_confirm_btn_n_clicks
    ):
        # Ensure we close the modal, and don't insert the data into the database
        return return_vars(application_modal_is_open=False)

    # If the user clicks the "Confirm" button in the modal,
    # submit the form to the database and close the modal.
    # company_dict: dict = {
    #     "company_name": application_company_name,
    #     "street": application_street,
    #     "city": application_city,
    #     "province_id": application_province_id,
    #     "country_id": application_country_id,
    # }
    # if application_company_select:
    #     # The user has selected an existing company
    #     customer_model = db.session.get(Customer, application_company_select)
    #     if customer_model:
    #         company_dict = {
    #             "customer_id": customer_model.id,
    #             "company_name": customer_model.customer,
    #             "street": customer_model.street,
    #             "city": customer_model.city,
    #             "province_id": customer_model.province_id,
    #             "country_id": customer_model.country_id,
    #         }

    fuel_diesel = "diesel" in application_fuel_source_checklist_value
    fuel_propane = "propane" in application_fuel_source_checklist_value
    fuel_natural_gas = "natural_gas" in application_fuel_source_checklist_value

    sand = "sand" in application_solids_checklist_value
    frac_sand = "frac_sand" in application_solids_checklist_value
    parafin = "parafin" in application_solids_checklist_value

    power_available = "yes" == application_power_available_radio_value
    power_grid = "grid" in application_power_available_checklist_value
    power_generator = "generator" in application_power_available_checklist_value

    cell_signal_good = "yes" == application_cell_signal_good_radio_value
    special_metals_needed = "yes" == application_special_metals_needed_value
    when_need_equipment = datetime.strptime(
        application_when_need_equipment_date, "%Y-%m-%d"
    ).date()

    # Validate the phone numbers
    contact_phone = getattr(
        validate_phone_number(application_contact_phone), "E164", None
    )
    field_contact_phone = getattr(
        validate_phone_number(application_field_contact_phone), "E164", None
    )

    # EGAS
    discharging_into_emulsion1_or_gas_line2 = (
        1 if "emulsion_line" == application_discharging_into_radio_value else 2
    )
    separators_installed = (
        True if application_separators_installed_radio_value == "yes" else False
    )
    compressor_b4_separators = (
        True if application_compressor_b4_separators_radio_value == "yes" else False
    )

    # VRU stuff
    vapour_coming_off_storage = (
        "tanks" in application_vapour_coming_from_checklist_value
    )
    vapour_coming_off_vr_tanks = (
        "vr_tanks" in application_vapour_coming_from_checklist_value
    )

    # DGAS
    if application_well_pumped_off_radio_value == "not_close_to":
        well_pumped_off_status = 1
    elif application_well_pumped_off_radio_value == "close_to":
        well_pumped_off_status = 2
    elif application_well_pumped_off_radio_value == "pumped_off":
        well_pumped_off_status = 3
    else:
        well_pumped_off_status = None
    pump_long_stroke = (
        True if application_pump_long_stroke_radio_value == "yes" else False
    )
    pump_set_on_cement1_piles2 = application_pump_set_on_cement1_piles2_radio_value
    pump_base_height = application_pump_base_height
    pump_num_rails = application_pump_num_rails
    pump_num_tiedowns = application_pump_num_tiedowns

    # Common database fields
    application_type_id = application_product_radio
    company_name = application_company_name
    street = application_street
    city = application_city
    province_id = application_province_id
    country_id = application_country_id
    contact_name = application_contact_name
    contact_phone = contact_phone
    contact_email = application_contact_email
    field_contact_name = application_field_contact_name
    field_contact_phone = field_contact_phone
    field_contact_email = application_field_contact_email
    install_location = application_install_location
    project_objective = application_project_objective
    current_process_equipment = application_current_process_equipment
    current_process_issues = application_current_process_issues
    when_need_equipment = when_need_equipment
    # Special requirements/additional info
    special_requirements = application_special_requirements
    # Power on site
    power_available = power_available
    power_voltage = application_power_voltage
    power_phase = application_power_phase
    power_amps = application_power_amps
    power_grid = power_grid
    power_generator = power_generator
    # Fuel sources available
    fuel_diesel = fuel_diesel
    fuel_natural_gas = fuel_natural_gas
    fuel_propane = fuel_propane
    # Cellular signal strength
    cell_signal_good = cell_signal_good

    # Initialize the variables so they're not unbound
    emulsion_coming_from = None
    emulsion_discharging_into = None
    vapour_coming_off_other = None
    vapour_discharging_into = None
    tank_pressure_rating = None
    tank_pressure_desired = None
    pump_fillage_pct = None
    formation_pressure = None
    pump_make = None
    pump_model = None
    pump_speed_spm = None
    pump_stroke_length = None
    pump_rod_load = None
    use_ppm1_percent2 = None
    h2s = None
    co2 = None
    salinity = None
    use_psi1_kpa2_ozsqinch3 = None
    inlet_pressure_current = None
    inlet_pressure_desired = None
    discharge_pressure = None
    max_discharge_flowline_temp = None
    max_ambient_temp = None
    use_e3m3d1_mcfd2 = None
    expected_gas_volume = None
    expected_water_volume = None
    expected_oil_volume = None
    use_celsius1_fahrenheit2 = None
    inlet_temp = None
    sand = None
    frac_sand = None
    parafin = None
    other_solids = None
    pipeline_diameter_inlet_inches = None
    pipeline_diameter_discharge_inches = None
    special_metals_needed = None
    casing_count = None
    discharging_into_emulsion1_or_gas_line2 = None
    flowline_pressure_on_test = None
    artificial_lift_system = None
    separators_installed = None
    compressor_b4_separators = None
    well_pumped_off_status = None
    pump_long_stroke = None
    pump_set_on_cement1_piles2 = None
    pump_base_height = None
    max_discharge_flowline_temp = None
    use_ppm1_percent2 = None

    if application_product_radio in (APPLICATION_XFER_STR, APPLICATION_BOOST_STR):
        # XFER-specific stuff
        emulsion_coming_from = application_emulsion_coming_from
        emulsion_discharging_into = application_discharging_into
        # Corrosive elements
        use_ppm1_percent2 = application_use_ppm1_percent2_radio_value
        h2s = application_h2s
        co2 = application_co2
        salinity = application_salinity
        use_psi1_kpa2_ozsqinch3 = application_use_psi1_kpa2_ozsqinch3_radio_value
        inlet_pressure_current = application_suction_pressure_current
        inlet_pressure_desired = application_suction_pressure_desired
        discharge_pressure = application_discharge_pressure
        # Use e3m3/d or Mcf/d
        use_e3m3d1_mcfd2 = application_use_e3m3d1_mcfd2_value
        expected_gas_volume = application_expected_gas_volume
        expected_water_volume = application_expected_water_volume
        expected_oil_volume = application_expected_oil_volume
        # Use C or F
        use_celsius1_fahrenheit2 = application_use_celsius1_fahrenheit2_value
        # Booleans for sand or parafin
        sand = sand
        frac_sand = frac_sand
        parafin = parafin
        other_solids = application_other_solids
        pipeline_diameter_inlet_inches = application_pipeline_diameter_inlet_inches
        pipeline_diameter_discharge_inches = (
            application_pipeline_diameter_discharge_inches
        )
        # Special metals/coatings needed
        special_metals_needed = special_metals_needed
        # VRU stuff
        vapour_coming_off_storage = None
        vapour_coming_off_vr_tanks = None
        vapour_coming_off_other = None
        vapour_discharging_into = None
        tank_pressure_rating = None
        tank_pressure_desired = None
        max_discharge_flowline_temp = None
        max_ambient_temp = None
        inlet_temp = None
        # EGAS stuff
        casing_count = None
        discharging_into_emulsion1_or_gas_line2 = None
        flowline_pressure_on_test = None
        artificial_lift_system = None
        separators_installed = None
        compressor_b4_separators = None
        # DGAS stuff
        well_pumped_off_status = None
        pump_fillage_pct = None
        formation_pressure = None
        pump_make = None
        pump_model = None
        pump_speed_spm = None
        pump_stroke_length = None
        pump_rod_load = None
        pump_long_stroke = None
        pump_set_on_cement1_piles2 = None
        pump_base_height = None
        pump_num_rails = None
        pump_num_tiedowns = None

    elif application_product_radio in (
        APPLICATION_EGAS_CGP_STR,
        APPLICATION_EGAS_GP_STR,
    ):
        # For EGAS units.
        if application_product_radio == APPLICATION_EGAS_CGP_STR:
            emulsion_coming_from = None
            separators_installed = None
            compressor_b4_separators = None
            artificial_lift_system = application_artificial_lift_system
            flowline_pressure_on_test = application_flowline_pressure_on_test

        else:
            emulsion_coming_from = application_emulsion_coming_from
            separators_installed = separators_installed
            compressor_b4_separators = compressor_b4_separators
            artificial_lift_system = None
            flowline_pressure_on_test = None

        emulsion_discharging_into = application_discharging_into
        # Corrosive elements
        use_ppm1_percent2 = application_use_ppm1_percent2_radio_value
        h2s = application_h2s
        co2 = application_co2
        salinity = application_salinity
        use_psi1_kpa2_ozsqinch3 = application_use_psi1_kpa2_ozsqinch3_radio_value
        inlet_pressure_current = application_suction_pressure_current
        inlet_pressure_desired = application_suction_pressure_desired
        discharge_pressure = application_discharge_pressure
        # Use e3m3/d or Mcf/d
        use_e3m3d1_mcfd2 = application_use_e3m3d1_mcfd2_value
        expected_gas_volume = application_expected_gas_volume
        expected_water_volume = None
        expected_oil_volume = None
        # Use C or F
        use_celsius1_fahrenheit2 = application_use_celsius1_fahrenheit2_value
        # Booleans for sand or parafin
        sand = None
        frac_sand = None
        parafin = None
        other_solids = application_other_solids
        pipeline_diameter_inlet_inches = None
        pipeline_diameter_discharge_inches = None
        # Special metals/coatings needed
        special_metals_needed = None
        # VRU stuff
        vapour_coming_off_storage = None
        vapour_coming_off_vr_tanks = None
        vapour_coming_off_other = None
        vapour_discharging_into = None
        tank_pressure_rating = None
        tank_pressure_desired = None
        max_discharge_flowline_temp = None
        max_ambient_temp = None
        inlet_temp = None
        # EGAS stuff
        casing_count = application_casing_count
        discharging_into_emulsion1_or_gas_line2 = (
            discharging_into_emulsion1_or_gas_line2
        )
        # DGAS stuff
        well_pumped_off_status = None
        pump_fillage_pct = None
        formation_pressure = None
        pump_make = None
        pump_model = None
        pump_speed_spm = None
        pump_stroke_length = None
        pump_rod_load = None
        pump_long_stroke = None
        pump_set_on_cement1_piles2 = None
        pump_base_height = None
        pump_num_rails = None
        pump_num_tiedowns = None

    elif application_product_radio in (APPLICATION_VRU_STR,):
        # XFER-specific stuff
        emulsion_coming_from = application_emulsion_coming_from
        emulsion_discharging_into = application_discharging_into
        # Corrosive elements
        use_ppm1_percent2 = application_use_ppm1_percent2_radio_value
        h2s = application_h2s
        co2 = application_co2
        salinity = application_salinity
        use_psi1_kpa2_ozsqinch3 = application_use_psi1_kpa2_ozsqinch3_radio_value
        inlet_pressure_current = application_suction_pressure_current
        inlet_pressure_desired = application_suction_pressure_desired
        discharge_pressure = application_discharge_pressure
        # Use e3m3/d or Mcf/d
        use_e3m3d1_mcfd2 = application_use_e3m3d1_mcfd2_value
        expected_gas_volume = application_expected_gas_volume
        expected_water_volume = application_expected_water_volume
        expected_oil_volume = application_expected_oil_volume
        # Use C or F
        use_celsius1_fahrenheit2 = application_use_celsius1_fahrenheit2_value
        # Booleans for sand or parafin
        sand = sand
        frac_sand = frac_sand
        parafin = parafin
        other_solids = application_other_solids
        pipeline_diameter_inlet_inches = application_pipeline_diameter_inlet_inches
        pipeline_diameter_discharge_inches = (
            application_pipeline_diameter_discharge_inches
        )
        # Special metals/coatings needed
        special_metals_needed = special_metals_needed
        # VRU stuff
        vapour_coming_off_storage = vapour_coming_off_storage
        vapour_coming_off_vr_tanks = vapour_coming_off_vr_tanks
        vapour_coming_off_other = application_vapour_coming_off_other
        vapour_discharging_into = application_discharging_into
        tank_pressure_rating = application_tank_pressure_rating
        tank_pressure_desired = application_suction_pressure_desired
        max_discharge_flowline_temp = application_max_discharge_temp
        max_ambient_temp = application_max_ambient_temp
        inlet_temp = application_inlet_temp
        # EGAS stuff
        casing_count = None
        discharging_into_emulsion1_or_gas_line2 = None
        flowline_pressure_on_test = None
        artificial_lift_system = None
        separators_installed = None
        compressor_b4_separators = None
        # DGAS stuff
        well_pumped_off_status = None
        pump_fillage_pct = None
        formation_pressure = None
        pump_make = None
        pump_model = None
        pump_speed_spm = None
        pump_stroke_length = None
        pump_rod_load = None
        pump_long_stroke = None
        pump_set_on_cement1_piles2 = None
        pump_base_height = None
        pump_num_rails = None
        pump_num_tiedowns = None

    elif application_product_radio in (APPLICATION_DGAS_STR,):
        # XFER-specific stuff
        emulsion_coming_from = None
        emulsion_discharging_into = None
        # Corrosive elements
        use_ppm1_percent2 = None
        h2s = None
        co2 = None
        salinity = None
        use_psi1_kpa2_ozsqinch3 = application_use_psi1_kpa2_ozsqinch3_radio_value
        inlet_pressure_current = application_suction_pressure_current
        inlet_pressure_desired = application_suction_pressure_desired
        discharge_pressure = application_discharge_pressure
        # Use e3m3/d or Mcf/d
        use_e3m3d1_mcfd2 = application_use_e3m3d1_mcfd2_value
        expected_gas_volume = application_expected_gas_volume
        expected_water_volume = None
        expected_oil_volume = None
        # Use C or F
        use_celsius1_fahrenheit2 = application_use_celsius1_fahrenheit2_value
        # Booleans for sand or parafin
        sand = None
        frac_sand = None
        parafin = None
        other_solids = application_other_solids
        pipeline_diameter_inlet_inches = None
        pipeline_diameter_discharge_inches = None
        # Special metals/coatings needed
        special_metals_needed = None
        # VRU stuff
        vapour_coming_off_storage = None
        vapour_coming_off_vr_tanks = None
        vapour_coming_off_other = None
        vapour_discharging_into = None
        tank_pressure_rating = None
        tank_pressure_desired = None
        max_discharge_flowline_temp = None
        max_ambient_temp = None
        inlet_temp = None
        # EGAS stuff
        casing_count = None
        discharging_into_emulsion1_or_gas_line2 = None
        flowline_pressure_on_test = None
        artificial_lift_system = application_artificial_lift_system
        separators_installed = None
        compressor_b4_separators = None
        # DGAS stuff
        well_pumped_off_status = well_pumped_off_status
        pump_fillage_pct = application_pump_fillage_pct
        formation_pressure = application_formation_pressure
        pump_make = application_pump_make
        pump_model = application_pump_model
        pump_speed_spm = application_pump_speed_spm
        pump_stroke_length = application_pump_stroke_length
        pump_rod_load = application_pump_rod_load
        pump_long_stroke = pump_long_stroke
        pump_set_on_cement1_piles2 = pump_set_on_cement1_piles2
        pump_base_height = pump_base_height
        pump_num_rails = pump_num_rails
        pump_num_tiedowns = pump_num_tiedowns

    try:
        # Query the database to see if the user has already clocked in or out
        model = Application(
            application_type_id=application_type_id,
            company_name=company_name,
            street=street,
            city=city,
            province_id=province_id,
            country_id=country_id,
            contact_name=contact_name,
            contact_phone=contact_phone,
            contact_email=contact_email,
            field_contact_name=field_contact_name,
            field_contact_phone=field_contact_phone,
            field_contact_email=field_contact_email,
            install_location=install_location,
            project_objective=project_objective,
            current_process_equipment=current_process_equipment,
            current_process_issues=current_process_issues,
            when_need_equipment=when_need_equipment,
            # XFER-specific stuff
            emulsion_coming_from=emulsion_coming_from,
            emulsion_discharging_into=emulsion_discharging_into,
            # VRU-specific stuff
            vapour_coming_off_storage=vapour_coming_off_storage,
            vapour_coming_off_vr_tanks=vapour_coming_off_vr_tanks,
            vapour_coming_off_other=vapour_coming_off_other,
            vapour_discharging_into=vapour_discharging_into,
            tank_pressure_rating=tank_pressure_rating,
            tank_pressure_desired=tank_pressure_desired,
            # DGAS
            well_pumped_off_status=well_pumped_off_status,
            pump_fillage_pct=pump_fillage_pct,
            formation_pressure=formation_pressure,
            pump_make=pump_make,
            pump_model=pump_model,
            pump_speed_spm=pump_speed_spm,
            pump_stroke_length=pump_stroke_length,
            pump_rod_load=pump_rod_load,
            pump_long_stroke=pump_long_stroke,
            pump_set_on_cement1_piles2=pump_set_on_cement1_piles2,
            pump_base_height=pump_base_height,
            pump_num_rails=pump_num_rails,
            pump_num_tiedowns=pump_num_tiedowns,
            # Corrosive elements
            use_ppm1_percent2=use_ppm1_percent2,
            h2s=h2s,
            co2=co2,
            salinity=salinity,
            use_psi1_kpa2_ozsqinch3=use_psi1_kpa2_ozsqinch3,
            inlet_pressure_current=inlet_pressure_current,
            inlet_pressure_desired=inlet_pressure_desired,
            discharge_pressure=discharge_pressure,
            # Max temperatures for VRU
            max_discharge_flowline_temp=max_discharge_flowline_temp,
            max_ambient_temp=max_ambient_temp,
            # Use e3m3/d or Mcf/d
            use_e3m3d1_mcfd2=use_e3m3d1_mcfd2,
            expected_gas_volume=expected_gas_volume,
            expected_water_volume=expected_water_volume,
            expected_oil_volume=expected_oil_volume,
            # Use C or F
            use_celsius1_fahrenheit2=use_celsius1_fahrenheit2,
            inlet_temp=inlet_temp,
            # Booleans for sand or parafin
            sand=sand,
            frac_sand=frac_sand,
            parafin=parafin,
            other_solids=other_solids,
            pipeline_diameter_inlet_inches=pipeline_diameter_inlet_inches,
            pipeline_diameter_discharge_inches=pipeline_diameter_discharge_inches,
            # Power on site
            power_available=power_available,
            power_voltage=power_voltage,
            power_phase=power_phase,
            power_amps=power_amps,
            power_grid=power_grid,
            power_generator=power_generator,
            # Fuel sources available
            fuel_diesel=fuel_diesel,
            fuel_natural_gas=fuel_natural_gas,
            fuel_propane=fuel_propane,
            # Cellular signal strength
            cell_signal_good=cell_signal_good,
            # Special metals/coatings needed
            special_metals_needed=special_metals_needed,
            # Special requirements/additional info
            special_requirements=special_requirements,
            casing_count=casing_count,
            flowline_pressure_on_test=flowline_pressure_on_test,
            artificial_lift_system=artificial_lift_system,
            separators_installed=separators_installed,
            compressor_b4_separators=compressor_b4_separators,
        )
        db.session.add(model)

        # session.flush() communicates a series of operations to the database (insert, update, delete).
        # The database maintains them as pending operations in a transaction.
        # The changes aren't persisted permanently to disk, or visible to other transactions
        # until the database receives a COMMIT for the current transaction (which is what session.commit() does).
        db.session.flush()

        # At this point, the object f has been pushed to the DB,
        # and has been automatically assigned a unique primary key id.
        # Refresh updates given object in the session with its state in the DB
        # (and can also only refresh certain attributes - search for documentation)
        db.session.refresh(model)

        # files list for email attachments
        files_list = []
        if application_file_upload_data_table_data:
            for file_dict in application_file_upload_data_table_data:
                # Change contents to BYTEA for database
                file_bytes = base64.b64decode(file_dict["file_bytes"])
                file_model = ApplicationUploadFile(
                    timestamp_utc_inserted=utcnow_naive(),
                    application_id=model.id,
                    file_name=file_dict["file_name"],
                    file_type=file_dict["file_type"],
                    file_bytes=file_bytes,
                )
                model.application_upload_files_rel.append(file_model)

                # Add to files list for email attachments
                files_list.append(("attachment", (file_dict["file_name"], file_bytes)))

        db.session.commit()

        if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
            # Just the developers
            to_emails = ADMIN_EMAILS
        else:
            to_emails = ["<EMAIL>"]

        send_email(
            subject="New IJACK Application",
            sender="RCOM Website <<EMAIL>>",
            to_emails=to_emails,
            text_body=f"""
New IJACK application submitted:
https://myijack.com/admin/applications/details/?id={model.id}

Company Name: {company_name}
Contact Name: {contact_name}
Contact Phone: {contact_phone}
Contact Email: {contact_email}
Field Contact Name: {field_contact_name}
Field Contact Phone: {field_contact_phone}
Field Contact Email: {field_contact_email}
Install Location: {install_location}
Project Objective: {project_objective}
Current Process Equipment: {current_process_equipment}
Current Process Issues: {current_process_issues}
When Need Equipment: {when_need_equipment}
Special Requirements: {special_requirements}

""",
            html_body=None,
            files_list=files_list,
        )

    except Exception as err:
        current_app.logger.exception("Error inserting IJACK application data")
        error_message = f"Error inserting IJACK application data into database: \n{err}"
        return return_vars(False, error_message, "danger")

    # Close the modal
    return return_vars(False, "Success - application submitted!", "success")
