from math import ceil
from pathlib import Path
from decimal import Decimal

import dash_bootstrap_components as dbc
import pandas as pd
from dash import Input, Output, State, callback, dcc, html, no_update
from dash.exceptions import PreventUpdate
from dash_ag_grid import AgGrid
from flask import current_app
from flask_login import current_user
from pandas import DataFrame
from shared.models.models import Customer, CustSubGroup, StructureVw, UnitType
from shared.models.models_bom import Warehouse, WarehousePart
from shared.utils.inventory_manager import InventoryManager
from sqlalchemy import text

from app import cache_memoize_if_prod, db, get_user_role_ids, user_is_ijack_employee
from app.config import (
    ADMIN_EMAILS,
    CURRENCY_ID_CAD,
    CURRENCY_ID_USD,
    CUST_SUB_GROUP_ID_ALL_GROUPS,
    CUST_SUB_GROUP_ID_ALL_OTHERS,
    CUSTOMER_ID_ALL_CUSTOMERS,
    ROLE_ID_INVENTORY_COST_COLUMNS,
    TAB_INVENTORY,
    UNIT_TYPE_ID_ALL_TYPES,
    WAREHOUSE_ID_ALL_WAREHOUSES,
)
from app.dashapp.layout_utils import get_ag_grid
from app.dashapp.metrics import BOOTSTRAP_BLUE_500
from app.dashapp.utils import (
    discrete_background_color_bins,
    get_db_options,
    get_structure_obj,
    get_structures_df_by_filter,
    log_function_caller,
)
from app.databases import run_sql_query
from app.email_stuff import send_email


def get_inventory_modal() -> dbc.Modal:
    """
    Get the inventory modal for adding a part to a warehouse with an actual and desired quantity.
    """
    return dbc.Modal(
        id="inventory_modal",
        size="lg",
        centered=True,
        children=[
            dbc.ModalHeader(
                dbc.ModalTitle("Add Part to Warehouse"),
            ),
            dbc.ModalBody(
                [
                    # Part Name Row
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Label("Part Name", class_name="fw-semibold"),
                                width=3,
                                class_name="d-flex align-items-center",
                            ),
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Select the new part to add to the warehouse",
                                        class_name="mb-1",
                                    ),
                                    dcc.Dropdown(
                                        id="inventory_modal_new_part",
                                        multi=False,
                                        disabled=True,
                                        className="shadow-sm",
                                    ),
                                    dbc.FormText(
                                        id="inventory_modal_new_part_text",
                                        children="",
                                    ),
                                ],
                                width=9,
                            ),
                        ],
                        class_name="mb-4",
                    ),
                    # Warehouse Row
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Label("Warehouse", class_name="fw-semibold"),
                                width=3,
                                class_name="d-flex align-items-center",
                            ),
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Select the warehouse in which to add the part",
                                        class_name="mb-1",
                                    ),
                                    dcc.Dropdown(
                                        id="inventory_modal_new_warehouse",
                                        options=[],
                                        disabled=True,
                                        className="shadow-sm",
                                    ),
                                    dbc.FormText(
                                        id="inventory_modal_new_warehouse_text",
                                        children="",
                                    ),
                                ],
                                width=9,
                            ),
                        ],
                        class_name="mb-4",
                    ),
                    # Section divider
                    dbc.Row(
                        dbc.Col(
                            html.Hr(className="border-1 border-secondary-subtle"),
                        ),
                        class_name="my-3",
                    ),
                    # Quantities Header
                    # dbc.Row(
                    #     dbc.Col(
                    #         html.H6(
                    #             "Quantity Information", className="text-secondary mb-3"
                    #         ),
                    #     ),
                    # ),
                    # Actual Quantity Row
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Label(
                                    "Actual Quantity",
                                    class_name="fw-semibold",
                                ),
                                width=3,
                                class_name="d-flex align-items-center",
                            ),
                            dbc.Col(
                                dbc.Input(
                                    id="inventory_modal_actual_quantity",
                                    type="number",
                                    value=0,
                                    min=0,
                                    step=1,
                                    disabled=True,
                                    class_name="shadow-sm",
                                ),
                                width=9,
                            ),
                        ],
                        class_name="mb-4",
                    ),
                    # Desired Quantity Row
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Label(
                                    "Desired Quantity",
                                    class_name="fw-semibold",
                                ),
                                width=3,
                                class_name="d-flex align-items-center",
                            ),
                            dbc.Col(
                                dbc.Input(
                                    id="inventory_modal_desired_quantity",
                                    type="number",
                                    value=0,
                                    min=0,
                                    step=1,
                                    disabled=True,
                                    class_name="shadow-sm",
                                ),
                                width=9,
                            ),
                        ],
                        class_name="mb-2",
                    ),
                    # Message row about form errors or successes, centered and increased size
                    dbc.Row(
                        dbc.Col(
                            dbc.FormText(
                                id="inventory_modal_message",
                                children="",
                            ),
                            class_name="text-center fs-3",
                        ),
                    ),
                ],
                class_name="px-4 py-3",
            ),
            # Modal Footer
            dbc.ModalFooter(
                [
                    dbc.Button(
                        [html.I(className="fa fa-times me-2"), "Close"],
                        id="inventory_modal_close_btn",
                        color="outline-secondary",
                        class_name="me-auto",
                    ),
                    # Store something to indicate when the inventory has been updated
                    dcc.Store(id="store_inventory_modal_updated"),
                    dbc.Button(
                        [html.I(className="fa fa-save me-2"), "Save"],
                        id="inventory_modal_save_btn",
                        color="primary",
                        disabled=True,
                        class_name="px-4",
                    ),
                ],
                # class_name="border-top border-2 border-light-subtle",
            ),
        ],
    )


def get_inventory_div():
    """Get the inventory div"""

    return dbc.Card(
        class_name="mt-3",
        id="inventory_div",
        style={"display": "none"},
        children=[
            get_inventory_modal(),
            dbc.CardHeader(
                dbc.Row(
                    [
                        dbc.Col(
                            dbc.Spinner(
                                "Inventory",
                                id="inventory_card_title",
                                color="success",
                            ),
                            width="auto",
                        ),
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fa-solid fa-refresh me-1"),
                                    "Refresh",
                                ],
                                id="inventory_refresh_btn",
                                color="primary",
                                outline=True,
                                size="sm",
                                # line-height 1
                                class_name="lh-1",
                            ),
                            width="auto",
                        ),
                    ],
                    justify="between",
                )
            ),
            dbc.CardBody(
                [
                    dbc.Row(
                        dbc.Col(
                            # class_name="my-3",
                            children=html.H4(
                                "This is a work-in-progress! Only IJACK admins can see this tab"
                            )
                        )
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                xs=12,
                                # md=6,
                                children=dbc.Switch(
                                    id="inventory_also_filter_on_units_switch",
                                    label="Only include specific unit",
                                    value=False,
                                    persistence=True,
                                    label_class_name="mb-0",
                                ),
                            ),
                        ],
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                xs=12,
                                # md=6,
                                children=dbc.Switch(
                                    id="inventory_include_warranty_parts_switch",
                                    label="Include parts sold on warranty as well",
                                    value=True,
                                    persistence=True,
                                    label_class_name="mb-0",
                                ),
                            ),
                        ],
                    ),
                    dbc.Row(
                        # class_name="mt-3",
                        children=dbc.Col(
                            [
                                # dbc.Label("Currency/Country"),
                                dbc.RadioItems(
                                    id="inventory_soft_hard_parts_radio",
                                    options=[
                                        {"label": "All Parts", "value": "all_parts"},
                                        {
                                            "label": "PM Reset Flag",
                                            "value": "pm_reset_flag",
                                        },
                                        {
                                            "label": "NOT PM Reset Flag",
                                            "value": "not_pm_reset_flag",
                                        },
                                    ],
                                    value="all_parts",
                                    inline=True,
                                    persistence=True,
                                    persistence_type="session",
                                ),
                            ]
                        ),
                    ),
                    dbc.Row(
                        # class_name="mt-3",
                        children=dbc.Col(
                            [
                                # dbc.Label("Currency/Country"),
                                dbc.Checklist(
                                    id="inventory_table_source_radio",
                                    options=[
                                        # {"label": "All Sources", "value": "All Sources"},
                                        {
                                            "label": "Base Powerunit",
                                            "value": "Base Powerunit",
                                        },
                                        {"label": "Powerunit", "value": "Powerunit"},
                                        {"label": "Structure", "value": "Structure"},
                                        {"label": "Pump Top", "value": "Pump Top"},
                                        {"label": "DGAS", "value": "DGAS"},
                                        {
                                            "label": "Extra Parts",
                                            "value": "Extra Parts",
                                        },
                                        {
                                            "label": "PM Seal Kit",
                                            "value": "PM Seal Kit",
                                        },
                                        {
                                            "label": "Power Unit Hydraulic Oil",
                                            "value": "Power Unit Hydraulic Oil",
                                        },
                                        {
                                            "label": "Power Unit Oil Filters",
                                            "value": "Power Unit Oil Filters",
                                        },
                                    ],
                                    value=[
                                        "Base Powerunit",
                                        "Powerunit",
                                        "Structure",
                                        "Pump Top",
                                        "DGAS",
                                        "Extra Parts",
                                        "PM Seal Kit",
                                        "Power Unit Hydraulic Oil",
                                        "Power Unit Oil Filters",
                                    ],
                                    inline=True,
                                    persistence=True,
                                    persistence_type="session",
                                ),
                            ]
                        ),
                    ),
                    dbc.Row(
                        # class_name="mt-3",
                        children=dbc.Col(
                            [
                                # dbc.Label("Currency/Country"),
                                dbc.RadioItems(
                                    id="inventory_currency_country_radio",
                                    options=[
                                        {"label": "CAD", "value": CURRENCY_ID_CAD},
                                        {"label": "USD", "value": CURRENCY_ID_USD},
                                    ],
                                    value=CURRENCY_ID_CAD,
                                    inline=True,
                                    persistence=True,
                                    persistence_type="session",
                                ),
                            ]
                        ),
                    ),
                    dbc.Row(
                        dbc.Col(
                            dbc.Spinner(
                                id="inventory_descriptive_data_col",
                                color="success",
                            ),
                        ),
                    ),
                    dbc.Row(
                        class_name="mt-3",
                        children=dbc.Col(
                            [
                                dbc.Button(
                                    "Download CSV",
                                    id="inventory_download_csv",
                                    color="secondary",
                                    outline=True,
                                    size="sm",
                                ),
                                dbc.Button(
                                    "Add part to warehouse",
                                    id="inventory_add_part_to_warehouse_btn",
                                    color="primary",
                                    outline=True,
                                    size="sm",
                                    class_name="ms-2",
                                ),
                            ]
                        ),
                    ),
                    dbc.Row(
                        class_name="mt-3",
                        children=dbc.Col(
                            dbc.Spinner(
                                id="inventory_col",
                                color="success",
                                children=get_ag_grid(
                                    # Need to set the AgGrid on page load since we're updating the actual values from the grid,
                                    # so there are callbacks that depend on this grid being on the page.
                                    id="inventory_ag_grid",
                                ),
                            ),
                        ),
                    ),
                ],
            ),
        ],
    )


# Memoize the function, so it only runs every so often
@cache_memoize_if_prod(timeout=120)
def get_bom_est_inventory(
    model_type_ids_list: list,
    is_soft_part_tuple: tuple = (True, False),
    inventory_table_source: list | None = [],
) -> DataFrame:
    """
    Get the BoM-estimated inventory by model_type_id
    """

    if not model_type_ids_list:
        return pd.DataFrame()

    if not isinstance(is_soft_part_tuple, tuple) or not is_soft_part_tuple:
        raise ValueError(
            "'is_soft_part_tuple' must be a tuple with at least one element"
        )

    if not isinstance(inventory_table_source, list):
        raise ValueError("'inventory_table_source' must be a list")

    if isinstance(model_type_ids_list, (int, str)):
        model_type_ids_tuple = (model_type_ids_list,)
    else:
        model_type_ids_tuple = tuple(model_type_ids_list)

    file_path = Path(__file__).parent.joinpath("inventory_sql.sql")
    with open(file_path, "r", encoding="utf-8") as file:
        sql_str = file.read()

    sql_text = text(sql_str).bindparams(
        # bindparam("model_type_ids", model_type_ids_tuple, type_=ARRAY(Integer)),
        # bindparam("is_soft_part_tuple", is_soft_part_tuple, type_=ARRAY(Boolean)),
        model_type_ids=model_type_ids_tuple,
        is_soft_part_tuple=is_soft_part_tuple,
    )
    result, columns = run_sql_query(
        sql_text, db_name="ijack", as_dict=True, debug=False
    )
    df = pd.DataFrame(result, columns=columns)

    # Assert that the num_occurrences_bom column only contains 1s, meaning there are no duplicates
    # for each combination of model_unit_type_id and part_name
    if df.num_occurrences_bom.nunique() not in {0, 1}:
        send_email(
            subject="The num_occurrences_bom column must only contain 1s",
            sender="RCOM Website <<EMAIL>>",
            to_emails=ADMIN_EMAILS,
            text_body=f"""The num_occurrences_bom column must only contain 1s
            current_user: {current_user}
            df: {df}
            """,
            html_body=None,
        )

    # Filter for only certain 'part_type' values
    if inventory_table_source:
        df = df[df["part_type"].isin(inventory_table_source)]

    # Drop the num_occurrences_bom column
    df.drop(columns=["num_occurrences_bom"], inplace=True)

    # model_type_id_count_sql should not be null or zero for any row
    if (
        df.model_type_id_count_sql.isnull().any()
        or (df.model_type_id_count_sql == 0).any()
    ):
        send_email(
            subject="The model_type_id_count_sql column should not contain nulls or zeros",
            sender="RCOM Website <<EMAIL>>",
            to_emails=ADMIN_EMAILS,
            text_body=f"""The model_type_id_count_sql column should not contain nulls or zeros.
            current_user: {current_user}
            df: {df}
            """,
            html_body=None,
        )

    # # NOTE: is the following redundant if it's already done in the SQL query?
    # # Replace nulls with zero
    # df["bom_estimate_total"] = df["bom_estimate_total"].fillna(0)

    # # Calculate the average per model by dividing by the model_type_id_count_sql for the bom_estimate_total,
    # # since it's done by model type, and by the model_unit_type_id_count_sql for the sold quantity,
    # # since it's done by the model's unit type. Later we'll multiply these by the
    # # model_type_id_count_pandas and model_unit_type_id_count_pandas to get the total estimated quantities.
    # df["bom_estimate_per_model"] = (
    #     df["bom_estimate_total"] / df["model_type_id_count_sql"]
    # )

    return df


# Memoize the function, so it only runs every so often
@cache_memoize_if_prod(timeout=120)
def get_sold_parts(
    model_unit_type_ids_list: list,
    include_warranty_parts: bool = True,
    is_soft_part_tuple: tuple = (True, False),
) -> DataFrame:
    """
    Get the BoM-estimated inventory by model_type_id
    """

    if not model_unit_type_ids_list:
        return pd.DataFrame()

    # Convert list to tuple for SQL query
    if isinstance(model_unit_type_ids_list, (int, str)):
        model_unit_type_ids_tuple = (model_unit_type_ids_list,)
    else:
        model_unit_type_ids_tuple = tuple(model_unit_type_ids_list)

    file_path = Path(__file__).parent.joinpath("inventory_sold_sql.sql")
    with open(file_path, "r", encoding="utf-8") as file:
        sql_str = file.read()

    if include_warranty_parts:
        # Include parts whether or not they are under warranty
        is_warranty = (True, False)
    else:
        # Only include parts that are not under warranty
        is_warranty = (False,)

    sql_text = text(sql_str).bindparams(
        model_unit_type_ids=model_unit_type_ids_tuple,
        is_warranty=is_warranty,
        is_soft_part_tuple=is_soft_part_tuple,
    )
    result, columns = run_sql_query(sql_text, db_name="ijack", as_dict=True)
    df = pd.DataFrame(result, columns=columns)

    # Assert that the num_occurrences_sold column only contains 1s, meaning there are no duplicates
    # for each combination of model_unit_type_id and part_name
    if df.num_occurrences_sold.nunique() not in {0, 1}:
        send_email(
            subject="The num_occurrences_sold column must only contain 1s",
            sender="RCOM Website <<EMAIL>>",
            to_emails=ADMIN_EMAILS,
            text_body="""The num_occurrences_sold column must only contain 1s
            current_user: {current_user}
            df: {df}
            """,
            html_body=None,
        )

    # Drop the num_occurrences_sold column
    df.drop(columns=["num_occurrences_sold"], inplace=True)

    # model_unit_type_id_count_sql should not be null or zero for any row
    if (
        df.model_unit_type_id_count_sql.isnull().any()
        or (df.model_unit_type_id_count_sql == 0).any()
    ):
        send_email(
            subject="The model_unit_type_id_count_sql column should not contain nulls or zeros",
            sender="RCOM Website <<EMAIL>>",
            to_emails=ADMIN_EMAILS,
            text_body=f"""The model_unit_type_id_count_sql column should not contain nulls or zeros.
            current_user: {current_user}
            df: {df}
            """,
            html_body=None,
        )

    # # NOTE: is the following redundant if it's already done in the SQL query?
    # # Replace nulls with zero
    # df["sold_quantity_total"] = df["sold_quantity_total"].fillna(0)

    # # Calculate the average per model by dividing by the model_unit_type_id_count_sql for the sold quantity,
    # # since it's done by the model's unit type. Later we'll multiply by the
    # # model_unit_type_id_count_pandas to get the total estimated sold quantity.
    # df["sold_quantity_per_model"] = (
    #     df["sold_quantity_total"] / df["model_unit_type_id_count_sql"]
    # )

    return df


def get_highest_part_revision_id(part_name: str) -> int:
    """Given a part name, get the part ID with the highest part revision"""

    sql = text("""
select t1.id, t1.part_name, t1.part_rev
from parts t1
inner join (
    --subquery to find the highest part revision for each part name
    select part_name, max(part_rev) as part_rev
    from parts
    where part_name = :part_name
    group by part_name
) a1
on a1.part_name = t1.part_name
    and a1.part_rev = t1.part_rev
    """).bindparams(part_name=part_name)
    result, _ = run_sql_query(sql, db_name="ijack", as_dict=True)
    # Get the ID
    if not result:
        return None
    id_ = result[0].get("id", None)
    return id_


def delete_other_duplicates_in_many_to_many(part_id: int, warehouse_id: int) -> None:
    """Get the other warehouse/part parts that have the same part_name but different part_id"""

    sql = text("""
delete
--select *
from warehouses_parts_rel t1
where
    --same warehouse
    t1.warehouse_id = :warehouse_id
    and t1.part_id in (
        select id
        from parts
        where
            --not the part ID we just created
            id != :part_id
            --but has the same/duplicate part name
            and part_name = (
                --Get the one part name for this part ID
                select part_name
                from parts
                where id = :part_id
                limit 1
            )
    )
returning *
    """).bindparams(part_id=part_id, warehouse_id=warehouse_id)

    result, columns = run_sql_query(
        sql, db_name="ijack", as_dict=True, commit=True, debug=True
    )
    df = pd.DataFrame(result, columns=columns)
    current_app.logger.debug(f"Deleted records: \n{df.head(100)}")

    return None


def get_actual_inventory_by_warehouse_id(
    warehouse_ids_list: list, is_soft_part_tuple: tuple = (True, False)
) -> DataFrame:
    """Get the actual inventory by warehouse ID"""

    # Get the actual quantity from the warehouses_parts_rel table
    sql = text("""
select
    a1.part_name,
    t3.name as warehouse_name,
    --Aggregate the warehouse names and quantities into one string
    --STRING_AGG(
    --    CONCAT(t3.name, ' (', a1.quantity, ')'),
    --    ', ' ORDER BY t3.name
    --) AS warehouse_quantity
    a1.quantity as warehouse_quantity,
    a1.quantity_available as warehouse_quantity_available,
    a1.quantity_desired as warehouse_quantity_desired
from (
    --Subquery to get the actual quantity of parts in each warehouse,
    --grouped by part_name (not part ID, which includes revisions) and warehouse_id
    select
        --Remove the part revision from the part name
        t2.part_name,
        t1.warehouse_id,
        sum(t1.quantity) as quantity,
        sum(t1.quantity_available) as quantity_available,
        sum(t1.quantity_desired) as quantity_desired
    from warehouses_parts_rel t1
    inner join parts t2
        on t1.part_id = t2.id
    where t1.warehouse_id in :warehouse_ids_list
        and t2.is_soft_part in :is_soft_part_tuple
    group by t2.part_name, t1.warehouse_id
) a1
inner join warehouses t3
    on a1.warehouse_id = t3.id
--group by
--    a1.part_name,
--    t3.name
    """).bindparams(
        warehouse_ids_list=tuple(warehouse_ids_list),
        is_soft_part_tuple=is_soft_part_tuple,
    )
    result, columns = run_sql_query(sql, db_name="ijack", as_dict=True)
    return pd.DataFrame(result, columns=columns)


def add_part_info_to_df(df: pd.DataFrame, is_canadian: bool) -> pd.DataFrame:
    """Add part info to the DataFrame"""

    currency_str = "cad" if is_canadian else "usd"
    sql = text(f"""
select
    distinct on (part_name)
    part_name,
    description,
    cost_{currency_str} as part_cost,
    dealer_cost_{currency_str} as dealer_cost,
    ijack_corp_cost_{currency_str} as ijack_corp_cost,
    case when weight is null then '' else weight end as weight,
    case when harmonization_code is null then '' else harmonization_code end as harmonization_code,
    case when country_of_origin is null then '' else country_of_origin end as country_of_origin
from parts
    """)
    result, columns = run_sql_query(sql, db_name="ijack", as_dict=True)
    parts_df = pd.DataFrame(result, columns=columns)

    # Merge the part info into the main DataFrame
    final_df = pd.merge(df, parts_df, on="part_name", how="left")
    return final_df


def get_inventory_df(
    structures_df: DataFrame,
    include_warranty_parts: bool = True,
    is_canadian: bool = True,
    is_soft_part_tuple: tuple = (True, False),
    inventory_table_source: list = [],
    # can_see_bom_master_cols: bool = False,
) -> DataFrame:
    """Get the inventory DataFrame for the Dash Ag Grid table"""

    if len(structures_df) == 0:
        return pd.DataFrame()

    # structure_ids_list: list = structures_df.structure_id.unique().tolist()
    model_unit_type_ids_list = structures_df.model_unit_type_id.unique().tolist()
    model_type_ids_list = structures_df.model_type_id.unique().tolist()
    warehouse_ids_list: list = structures_df.warehouse_id.unique().tolist()

    # Get the parts, costs, and quantities from the database
    bom_est_df: DataFrame = get_bom_est_inventory(
        model_type_ids_list=model_type_ids_list,
        is_soft_part_tuple=is_soft_part_tuple,
        inventory_table_source=inventory_table_source,
    )
    if len(bom_est_df) == 0:
        return pd.DataFrame()

    # Count the number of each model_type_id in the DataFrame.
    # This is used to calculate the estimated BoM quantity.
    model_type_id_counts_df: DataFrame = (
        structures_df.groupby(["model_type_id"])
        .size()
        .reset_index(name="model_type_id_count_pandas")
    )

    # Merge the BoM estimate DataFrame into the main DataFrame
    bom_df = pd.merge(
        model_type_id_counts_df,
        bom_est_df,
        on="model_type_id",
        how="left",
    )

    # Multiply the unitary amount by the count to get the total quantity of this part
    bom_df["bom_estimate"] = (
        bom_df["bom_estimate_per_model"] * bom_df["model_type_id_count_pandas"]
    )

    # Do the same thing with the sold parts, but do them by model_unit_type_id instead of model_type_id
    sold_parts_df: DataFrame = get_sold_parts(
        model_unit_type_ids_list=model_unit_type_ids_list,
        include_warranty_parts=include_warranty_parts,
        is_soft_part_tuple=is_soft_part_tuple,
    )

    # Count the number of each model_unit_type_id in the DataFrame
    # This is used to calculate the estimated sold quantity.
    model_unit_type_id_counts_df: DataFrame = (
        structures_df.groupby(["model_unit_type_id"])
        .size()
        .reset_index(name="model_unit_type_id_count_pandas")
    )

    # Merge the sold parts DataFrame into the main DataFrame
    sold_df = pd.merge(
        model_unit_type_id_counts_df,
        sold_parts_df,
        on="model_unit_type_id",
        how="left",
    )

    # Multiply the unitary amount by the count to get the total quantity of this part
    sold_df["sold_quantity"] = (
        sold_df["sold_quantity_per_model"] * sold_df["model_unit_type_id_count_pandas"]
    )

    # Aggregate by part_name so we can merge with the BoM estimate and actual DataFrames
    sold_agg_df = (
        sold_df.groupby("part_name")
        .agg(
            {
                "sold_quantity": "sum",
            }
        )
        # .sort_index(ascending=True)
        .reset_index()
    )

    # Before creating the final bom_agg_df, we'll aggregate the part_types
    # First, create a temporary DataFrame that combines part_types for each part_name.
    # It only has two columns: part_name and part_type, so we can re-add the combined part_types to the main DataFrame later.
    part_type_agg_df = (
        bom_df.groupby("part_name")["part_type"]
        # Convert the series of part_types to a set to remove duplicates,
        # sort them to ensure consistent ordering,
        # and join them with commas to create a single string value to show
        # where they came from (e.g. "Extra Parts, Powerunit")
        .agg(
            lambda x: ", ".join(sorted(set(x)))
        )  # Combine unique part_types with comma separator
        .reset_index()
    )

    # Now, prepare a version of bom_df with the other metrics aggregated by part_name only.
    # This way we have the sum of the BoM estimate for each part (regardless of part type), and the first of the other metrics.
    metrics_agg_df = (
        bom_df.groupby("part_name")
        .agg(
            {
                "cost_cad": "first",
                "cost_usd": "first",
                "msrp_cad": "first",
                "msrp_usd": "first",
                "bom_estimate": "sum",
            }
        )
        .reset_index()
    )

    # Merge the aggregated part_types with the metrics
    bom_agg_df = pd.merge(metrics_agg_df, part_type_agg_df, on="part_name", how="left")

    est_and_sold_df = pd.merge(
        bom_agg_df,
        sold_agg_df,
        on="part_name",
        how="left",
    )

    # Take the average of the two estimates
    est_and_sold_df["calc_quantity_b4_rounding"] = est_and_sold_df[
        ["bom_estimate", "sold_quantity"]
    ].mean(axis=1)

    # Convert NaNs to zeros
    est_and_sold_df["calc_quantity_b4_rounding"] = est_and_sold_df[
        "calc_quantity_b4_rounding"
    ].fillna(0)

    # This is the calculated quantity, rounded up to the nearest whole number,
    # but with a minimum of 2. NOTE: Do this after the summing, not before.
    est_and_sold_df["calc_quantity"] = est_and_sold_df[
        "calc_quantity_b4_rounding"
    ].apply(lambda value: max(2, ceil(value)))

    actual_inventory: DataFrame = get_actual_inventory_by_warehouse_id(
        warehouse_ids_list=warehouse_ids_list, is_soft_part_tuple=is_soft_part_tuple
    )

    # Merge the actual quantity into the main DataFrame
    # Consider doing a full "outer" join since there are multiple warehouses for each part?
    # But then we'd show a lot of warehouse parts that we didn't filter for!
    final_df = pd.merge(est_and_sold_df, actual_inventory, on="part_name", how="left")

    # Add more part info to the DataFrame, if the user is allowed to see it
    # if can_see_bom_master_cols:
    final_df = add_part_info_to_df(final_df, is_canadian)

    # Sort the final DataFrame
    final_df = final_df.sort_values(
        by=["part_type", "part_name", "warehouse_name"], ascending=True
    )

    return final_df


def get_inventory_columns(
    df: pd.DataFrame,
    is_cad: bool = True,
    can_see_bom_master_cols: bool = False,
) -> list:
    """Get the columns for the inventory table"""

    if is_cad:
        cost_col = "cost_cad"
        cost_header_name = "Part Cost CAD"
        cost_header_tooltip = "Supplier cost of the part in Canadian dollars"

        price_col = "msrp_cad"
        price_header_name = "Part MSRP CAD"
        price_header_tooltip = "Retail price of the part in Canadian dollars"

        dealer_cost_header_name = "Dealer Cost CAD"
        dealer_cost_header_tooltip = (
            "The cost of the part to the dealer in Canadian dollars"
        )
    else:
        cost_col = "cost_usd"
        cost_header_name = "Part Cost USD"
        cost_header_tooltip = "Supplier cost of the part in US dollars"

        price_col = "msrp_usd"
        price_header_name = "Part MSRP USD"
        price_header_tooltip = "Retail price of the part in US dollars"

        dealer_cost_header_name = "Dealer Cost USD"
        dealer_cost_header_tooltip = "The cost of the part to the dealer in US dollars"

    columns = [
        # {
        #     "field": "warehouse_id",
        #     "headerName": "Warehouse ID",
        #     "cellDataType": "text",
        # },
        # {
        #     "field": "warehouse_name",
        #     "headerName": "Warehouse Name",
        #     "headerTooltip": "The name of the warehouse that has the parts",
        #     "cellDataType": "text",
        # },
        # {
        #     "field": "model",
        #     "headerName": "Model",
        #     "headerTooltip": "The model type of the structure(s) that the parts are used in",
        #     "cellDataType": "text",
        # },
        # {
        #     "field": "model_unit_type_id_count_sql",
        #     "headerName": "Model Count",
        #     "headerTooltip": "The number of structures that use this model type",
        #     "cellDataType": "number",
        #     "cellRenderer": "NumberFormatter0",
        # },
        # {
        #     "field": "finished_good_type",
        #     "headerName": "Finished Good Type",
        #     "cellDataType": "text",
        # },
        # {
        #     "field": "finished_good_name",
        #     "headerName": "Finished Good Name",
        #     "cellDataType": "text",
        # },
        {
            "field": "part_name",
            "headerName": "Part Number",
            "headerTooltip": "The part number of the part",
            "cellDataType": "text",
            "pinned": "left",
        },
        {
            "field": "part_type",
            "headerName": "Part Type",
            "headerTooltip": "Where the part is used, or where it comes from",
            "cellDataType": "text",
        },
        {
            "field": "warehouse_name",
            "headerName": "Warehouse",
            "headerTooltip": "The name of the warehouse that has the part",
            "cellDataType": "text",
        },
        {
            "field": "description",
            "headerName": "Part Description",
            "headerTooltip": "More information about the part",
            "cellDataType": "text",
            # "minWidth": 100,
            # "width": 180,
            # This column can get pretty wide
            "maxWidth": 700,
        },
        {
            "field": price_col,
            "headerName": price_header_name,
            "headerTooltip": price_header_tooltip,
            "cellDataType": "number",
            "cellRenderer": "CurrencyFormatter0",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=[price_col],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "bom_estimate",
            "headerName": "BoM Estimate",
            "headerTooltip": "The bill of materials master-estimated quantity of the part that should be in inventory",
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["bom_estimate"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "sold_quantity",
            "headerName": "Sold Quantity",
            "headerTooltip": "The quantity of the part that has been sold in the past two months, on average, using data from the last 18 months",
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["sold_quantity"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "calc_quantity",
            "headerName": "Combined Estimate",
            "headerTooltip": "The estimated optimal quantity of the part that should be in the warehouse. This is calculated by taking the average of the BoM estimate and the sold quantity, and rounding up.",
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["calc_quantity"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "warehouse_quantity",
            "headerName": "Warehouse Quantity",
            "headerTooltip": "The actual quantity of the part that is in each warehouse",
            "cellDataType": "text",
            "editable": True,
            "maxWidth": 600,
            # "cellRenderer": "NumberFormatter1",
            # "cellStyle": {
            #     "styleConditions": discrete_background_color_bins(
            #         df=df,
            #         n_bins=9,
            #         columns=["warehouse_quantity"],
            #         reverse=False,
            #         color_start=BOOTSTRAP_BLUE_500,
            #         is_ag_grid=True,
            #     )
            # },
        },
        {
            "field": "warehouse_quantity_available",
            "headerName": "Warehouse Quantity Minus Reserved",
            "headerTooltip": "The actual quantity of the part in each warehouse, minus the reserved quantity not yet consumed, but allocated to work orders",
            "cellDataType": "text",
            "editable": False,
            "maxWidth": 600,
        },
        {
            "field": "warehouse_quantity_desired",
            "headerName": "Warehouse Quantity Desired",
            "headerTooltip": "The desired quantity of the part that should be in each warehouse",
            "cellDataType": "text",
            "editable": True,
        },
    ]

    # Certain IJACK people can see more columns
    if can_see_bom_master_cols:
        columns.extend(
            [
                {
                    "field": cost_col,
                    "headerName": cost_header_name,
                    "headerTooltip": cost_header_tooltip,
                    "cellDataType": "number",
                    "cellRenderer": "CurrencyFormatter0",
                    "cellStyle": {
                        "styleConditions": discrete_background_color_bins(
                            df=df,
                            n_bins=9,
                            columns=[cost_col],
                            reverse=False,
                            color_start=BOOTSTRAP_BLUE_500,
                            is_ag_grid=True,
                        )
                    },
                },
                {
                    "field": "dealer_cost",
                    "headerName": dealer_cost_header_name,
                    "headerTooltip": dealer_cost_header_tooltip,
                    "cellDataType": "number",
                    "cellRenderer": "CurrencyFormatter0",
                    "cellStyle": {
                        "styleConditions": discrete_background_color_bins(
                            df=df,
                            n_bins=9,
                            columns=["dealer_cost"],
                            reverse=False,
                            color_start=BOOTSTRAP_BLUE_500,
                            is_ag_grid=True,
                        )
                    },
                },
                {
                    "field": "ijack_corp_cost",
                    "headerName": "IJACK Corp Cost",
                    "headerTooltip": "The cost of the part to IJACK Corp",
                    "cellDataType": "number",
                    "cellRenderer": "CurrencyFormatter0",
                    "cellStyle": {
                        "styleConditions": discrete_background_color_bins(
                            df=df,
                            n_bins=9,
                            columns=["ijack_corp_cost"],
                            reverse=False,
                            color_start=BOOTSTRAP_BLUE_500,
                            is_ag_grid=True,
                        )
                    },
                },
                {
                    "field": "weight",
                    "headerName": "Weight",
                    "headerTooltip": "The weight of the part in kilograms",
                    "cellDataType": "text",
                },
                {
                    "field": "harmonization_code",
                    "headerName": "Harmonization Code",
                    "headerTooltip": "The harmonization code of the part",
                    "cellDataType": "text",
                },
                {
                    "field": "country_of_origin",
                    "headerName": "Country of Origin",
                    "headerTooltip": "The country of origin of the part",
                    "cellDataType": "text",
                },
            ]
        )

    return columns


@callback(
    Output("inventory_ag_grid", "exportDataAsCsv"),
    Input("inventory_download_csv", "n_clicks"),
    prevent_initial_call=True,
)
def export_data_as_csv(n_clicks):
    """Export the data as CSV"""
    if n_clicks:
        return True
    return False


@callback(
    Output("inventory_ag_grid", "rowData"),
    Output("inventory_ag_grid", "columnDefs"),
    Output("inventory_card_title", "children"),
    Output("inventory_descriptive_data_col", "children"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("inventory_refresh_btn", "n_clicks"),
    Input("store_inventory_modal_updated", "data"),
    Input("customer_radio", "value"),
    Input("warehouse_radio", "value"),
    Input("cust_sub_groups_radio", "value"),
    Input("unit_type_ids_radio", "value"),
    Input("store_structure_id", "data"),
    Input("inventory_also_filter_on_units_switch", "value"),
    Input("inventory_include_warranty_parts_switch", "value"),
    Input("inventory_currency_country_radio", "value"),
    Input("inventory_soft_hard_parts_radio", "value"),
    Input("inventory_table_source_radio", "value"),
    State("inventory_ag_grid", "virtualRowData"),
    # Input("store_url_search", "data"),
    # State("use_url_search", "data"),
    # Ensure this callback always fires on page load (from url.search prop updating).
    # The only time we don't want it to fire is if the map tab is active.
    prevent_initial_call=True,
)
def make_inventory_div(
    active_tab,
    inventory_refresh_btn_n_clicks,
    store_inventory_modal_updated_data,
    customer_radio_value,
    warehouse_radio_value,
    cust_sub_groups_radio_value,
    unit_type_ids_radio_value,
    store_structure_id_data,
    inventory_also_filter_on_units_switch_value,
    inventory_include_warranty_parts_switch_value,
    inventory_currency_country_radio_value,
    inventory_soft_hard_parts_radio_value,
    inventory_table_source_radio_value,
    inventory_ag_grid_virtualRowData,
):
    """Make filter for customers (the first filter)"""
    log_function_caller()

    if (
        active_tab != TAB_INVENTORY
        or customer_radio_value is None
        or warehouse_radio_value is None
        or cust_sub_groups_radio_value is None
        or unit_type_ids_radio_value is None
        or store_structure_id_data is None
    ):
        raise PreventUpdate()

    def return_vars(
        inventory_ag_grid_rowData: list,
        inventory_ag_grid_columnDefs: list,
        inventory_card_title_children: str,
        inventory_descriptive_data_col: str,
    ) -> tuple:
        """Default return vars"""
        return (
            inventory_ag_grid_rowData,
            inventory_ag_grid_columnDefs,
            inventory_card_title_children,
            inventory_descriptive_data_col,
        )

    # Make the card title
    if customer_radio_value == CUSTOMER_ID_ALL_CUSTOMERS:
        customer = None
        customer_str = "All Customers"
    else:
        customer = db.session.get(Customer, customer_radio_value)
        if hasattr(customer, "customer"):
            customer_str = customer.customer
        else:
            customer_str = ""

    user_id: int = getattr(current_user, "id", None)
    is_ijack: bool = user_is_ijack_employee(user_id=user_id)
    if is_ijack:
        # Only IJACK employees can filter on warehouse
        if warehouse_radio_value == WAREHOUSE_ID_ALL_WAREHOUSES:
            warehouse_str = "All Warehouses, "
        else:
            warehouse = db.session.get(Warehouse, warehouse_radio_value)
            warehouse_str = getattr(warehouse, "name", None)
            if warehouse_str:
                warehouse_str = f"{warehouse_str} Warehouse, "
            else:
                warehouse_str = "No Warehouse Name, "
    else:
        warehouse_str = ""

    if inventory_also_filter_on_units_switch_value and store_structure_id_data:
        # structure_ids_list = [store_structure_id_data]
        structure_id = store_structure_id_data
        structure_obj: StructureVw | None = get_structure_obj(
            store_structure_id_data, user_id, unit_type_ids_radio_value
        )
        model_type = getattr(structure_obj, "model", None)
        if model_type:
            unit_type_str = f"{model_type} Model"
            inventory_card_title_children = (
                f"Inventory Data for {warehouse_str}{unit_type_str}"
            )
        else:
            inventory_card_title_children = "No inventory data found for this structure"
    else:
        # structure_ids_list = None
        structure_id = None

        if cust_sub_groups_radio_value == CUST_SUB_GROUP_ID_ALL_GROUPS:
            cust_sub_group_str = "All Groups"
        elif cust_sub_groups_radio_value == CUST_SUB_GROUP_ID_ALL_OTHERS:
            cust_sub_group_str = "All Others Group"
        else:
            cust_sub_group = db.session.get(CustSubGroup, cust_sub_groups_radio_value)
            cust_sub_group_str = getattr(cust_sub_group, "name", None)
            if cust_sub_group_str:
                cust_sub_group_str = f"{cust_sub_group_str} Group"
            else:
                cust_sub_group_str = "No Group Name"

        if unit_type_ids_radio_value == UNIT_TYPE_ID_ALL_TYPES:
            unit_type_str = "All Unit Types"
        else:
            unit_type = db.session.get(UnitType, unit_type_ids_radio_value)
            unit_type_str = getattr(unit_type, "unit_type", None)
            if unit_type_str:
                unit_type_str = f"{unit_type_str} Unit Type"
            else:
                unit_type_str = "No Unit Type Found"

        inventory_card_title_children = f"Inventory Data for {customer_str}, {warehouse_str}{cust_sub_group_str}, {unit_type_str}"

    # Get the row data for the inventory table
    structures_df: DataFrame = get_structures_df_by_filter(
        customer_id=customer_radio_value,
        warehouse_id=warehouse_radio_value,
        cust_sub_group_id=cust_sub_groups_radio_value,
        unit_type_id=unit_type_ids_radio_value,
        # If there's a specific structure ID, get that one and ignore the other filters
        structure_id=structure_id,
    )
    # Count the model field and put in DataFrame
    models_df = structures_df.groupby("model").size().reset_index(name="model_count")
    # Count the warehouse_name field and put in DataFrame
    warehouses_df = (
        structures_df.groupby("warehouse_name")
        .size()
        .reset_index(name="warehouse_count")
    )
    # Combine the two DataFrames and make a nice HTML table
    inventory_descriptive_data_col = dbc.Row(
        [
            dbc.Col(
                xs=12,
                sm=6,
                class_name="mt-3",
                children=get_ag_grid(
                    rowData=models_df.to_dict("records"),
                    columnDefs=[
                        {
                            "field": "model",
                            "headerName": "Model",
                            "cellDataType": "text",
                        },
                        {
                            "field": "model_count",
                            "headerName": "Count",
                            "cellDataType": "number",
                            "cellRenderer": "NumberFormatter0",
                        },
                    ],
                    # columnSize="autoSize",
                    columnSize="sizeToFit",
                    pagination=False,
                    column_filter=False,
                ),
            ),
            dbc.Col(
                xs=12,
                sm=6,
                class_name="mt-3",
                children=get_ag_grid(
                    rowData=warehouses_df.to_dict("records"),
                    columnDefs=[
                        {
                            "field": "warehouse_name",
                            "headerName": "Warehouse Name",
                            "cellDataType": "text",
                        },
                        {
                            "field": "warehouse_count",
                            "headerName": "Count",
                            "cellDataType": "number",
                            "cellRenderer": "NumberFormatter0",
                        },
                    ],
                    # columnSize="autoSize",
                    columnSize="sizeToFit",
                    pagination=False,
                    column_filter=False,
                ),
            ),
        ]
    )

    # Add some more part info to the DataFrame, if the user is allowed to see it
    role_ids: list = get_user_role_ids(user_id=user_id)
    can_see_bom_master_cols: bool = ROLE_ID_INVENTORY_COST_COLUMNS in role_ids
    is_canadian: bool = str(inventory_currency_country_radio_value) == str(
        CURRENCY_ID_CAD
    )

    # Default is either soft or hard part when filtering the boolean field 'is_soft_part'
    # (i.e. where is_soft_part in :is_soft_part_tuple)
    is_soft_part_tuple: tuple = (True, False)
    if inventory_soft_hard_parts_radio_value == "pm_reset_flag":
        is_soft_part_tuple = (True,)
    elif inventory_soft_hard_parts_radio_value == "not_pm_reset_flag":
        is_soft_part_tuple = (False,)

    df: pd.DataFrame = get_inventory_df(
        structures_df,
        include_warranty_parts=inventory_include_warranty_parts_switch_value,
        is_canadian=is_canadian,
        is_soft_part_tuple=is_soft_part_tuple,
        inventory_table_source=inventory_table_source_radio_value,
        # can_see_bom_master_cols=can_see_bom_master_cols,
    )
    if len(df) == 0:
        rows = []
    else:
        rows = df.to_dict("records")

    # Show the cost columns if the user is an IJACK employee.
    # Also decide on the currency to show
    columns = get_inventory_columns(
        df=df,
        is_cad=is_canadian,
        # This is important! Don't show the secret columns to the wrong people!
        can_see_bom_master_cols=can_see_bom_master_cols,
    )

    return return_vars(
        inventory_ag_grid_rowData=rows,
        inventory_ag_grid_columnDefs=columns,
        inventory_card_title_children=inventory_card_title_children,
        inventory_descriptive_data_col=inventory_descriptive_data_col,
    )


@callback(
    Output("inventory_ag_grid", "columnSize"),
    Output("inventory_ag_grid", "dashGridOptions"),
    # Output("inventory_ag_grid", "defaultColDef"),
    # Output("inventory_ag_grid", "getRowStyle"),
    Input("inventory_ag_grid", "columnDefs"),
    prevent_initial_call=True,
)
def set_ag_grid_options_after_data_arrives(_):
    """Must set the 'columnSize' and 'domLayout' after the rowData; otherwise they won't work!"""
    # autoSize fits the column width to its content
    # sizeToFit fits the table width to the width of the viewport
    # return "sizeToFit"
    ag_grid: AgGrid = get_ag_grid()
    return (
        ag_grid.columnSize,
        ag_grid.dashGridOptions,
        # ag_grid.defaultColDef,
        # ag_grid.getRowStyle,
    )


@callback(
    Output("inventory_modal", "is_open", allow_duplicate=True),
    Input("inventory_modal_close_btn", "n_clicks"),
    prevent_initial_call=True,
)
def close_inventory_modal(n_clicks):
    """Close the inventory modal"""
    return False


@callback(
    Output("inventory_modal", "is_open", allow_duplicate=True),
    Output("inventory_modal_new_part", "options"),
    Output("inventory_modal_new_part", "disabled"),
    Output("inventory_modal_new_warehouse", "options"),
    Output("inventory_modal_new_warehouse", "disabled"),
    Output("inventory_modal_actual_quantity", "disabled"),
    Output("inventory_modal_desired_quantity", "disabled"),
    Output("inventory_modal_save_btn", "disabled"),
    Input("inventory_add_part_to_warehouse_btn", "n_clicks"),
    prevent_initial_call=True,
)
def open_inventory_modal(n_clicks):
    """Open the inventory modal"""

    user_id: int = getattr(current_user, "id", None)
    if not n_clicks or not user_is_ijack_employee(user_id=user_id):
        raise PreventUpdate

    def return_vars(
        inventory_modal_is_open: bool,
        inventory_modal_new_part_options: list,
        inventory_modal_new_part_disabled: bool,
        inventory_modal_new_warehouse_options: list,
        inventory_modal_new_warehouse_disabled: bool,
        inventory_modal_actual_quantity_disabled: bool,
        inventory_modal_desired_quantity_disabled: bool,
        inventory_modal_save_btn_disabled: bool,
    ):
        """Return variables"""
        return (
            inventory_modal_is_open,
            inventory_modal_new_part_options,
            inventory_modal_new_part_disabled,
            inventory_modal_new_warehouse_options,
            inventory_modal_new_warehouse_disabled,
            inventory_modal_actual_quantity_disabled,
            inventory_modal_desired_quantity_disabled,
            inventory_modal_save_btn_disabled,
        )

    part_options: list = get_db_options(
        columns=["part_num"],
        table="parts",
        # Don't allow parts in the dropdown if they're flagged for deletion (i.e. not in the BoM Master anymore)
        where="where flagged_for_deletion = false",
    )
    warehouse_options: list = get_db_options(columns=["name"], table="warehouses")

    return return_vars(
        inventory_modal_is_open=True,
        inventory_modal_new_part_options=part_options,
        inventory_modal_new_part_disabled=False,
        inventory_modal_new_warehouse_options=warehouse_options,
        inventory_modal_new_warehouse_disabled=False,
        inventory_modal_actual_quantity_disabled=False,
        inventory_modal_desired_quantity_disabled=False,
        inventory_modal_save_btn_disabled=False,
    )


@callback(
    Output("inventory_modal", "is_open", allow_duplicate=True),
    Output("inventory_modal_message", "children", allow_duplicate=True),
    Output("inventory_modal_message", "color", allow_duplicate=True),
    Output("store_inventory_modal_updated", "data"),
    Input("inventory_modal_save_btn", "n_clicks"),
    State("inventory_modal_new_part", "value"),
    State("inventory_modal_new_warehouse", "value"),
    State("inventory_modal_actual_quantity", "value"),
    State("inventory_modal_desired_quantity", "value"),
    prevent_initial_call=True,
)
def save_inventory_modal(
    n_clicks,
    inventory_modal_new_part,
    inventory_modal_new_warehouse,
    inventory_modal_actual_quantity,
    inventory_modal_desired_quantity,
):
    """Save the inventory modal"""

    user_id: int = getattr(current_user, "id", None)
    if not n_clicks or not user_is_ijack_employee(user_id=user_id):
        raise PreventUpdate

    def return_vars(
        inventory_modal_is_open: bool,
        inventory_modal_message_children: str,
        inventory_modal_message_color: str,
        store_inventory_modal_updated_data: bool = no_update,
    ):
        """Return variables"""
        return (
            inventory_modal_is_open,
            inventory_modal_message_children,
            inventory_modal_message_color,
            store_inventory_modal_updated_data,
        )

    def error_return_vars(message: str):
        """Return variables for errors"""
        return return_vars(
            inventory_modal_is_open=True,
            inventory_modal_message_children=message,
            inventory_modal_message_color="red",
            store_inventory_modal_updated_data=no_update,
        )

    if not inventory_modal_new_part:
        return error_return_vars("Please select a part")
    if not inventory_modal_new_warehouse:
        return error_return_vars("Please select a warehouse")
    if not isinstance(inventory_modal_actual_quantity, (int, float)):
        return error_return_vars("Please enter an actual quantity")
    if not isinstance(inventory_modal_desired_quantity, (int, float)):
        return error_return_vars("Please enter a desired quantity")
    if inventory_modal_actual_quantity < 0:
        return error_return_vars("Actual quantity must be 0 or greater")
    if inventory_modal_desired_quantity < 0:
        return error_return_vars("Desired quantity must be 0 or greater")

    # Use the inventory management system to handle the adjustment
    message: str = ""
    inventory_manager = InventoryManager(db.session)

    try:
        # Check if warehouse part exists to determine desired quantity
        wh_part = WarehousePart.query.filter_by(
            warehouse_id=inventory_modal_new_warehouse,
            part_id=inventory_modal_new_part,
        ).first()

        # Create manual adjustment using the inventory manager
        adjustment_result = inventory_manager.create_manual_adjustment(
            part_id=inventory_modal_new_part,
            warehouse_id=inventory_modal_new_warehouse,
            new_quantity=Decimal(str(inventory_modal_actual_quantity)),
            user_id=user_id,
            reason="Manual adjustment via inventory modal",
            adjustment_type="dashboard_modal",
        )

        if not adjustment_result.success:
            db.session.rollback()
            return error_return_vars(
                f"Error creating adjustment: {', '.join(adjustment_result.errors)}"
            )

        # Update desired quantity (this doesn't need movement/ledger tracking)
        if wh_part:
            wh_part.quantity_desired = Decimal(str(inventory_modal_desired_quantity))
            message = f"Inventory updated with tracking! {adjustment_result.message}"
        else:
            # Get the warehouse part that was created by the adjustment
            wh_part = WarehousePart.query.filter_by(
                warehouse_id=inventory_modal_new_warehouse,
                part_id=inventory_modal_new_part,
            ).first()
            if wh_part:
                wh_part.quantity_desired = Decimal(
                    str(inventory_modal_desired_quantity)
                )
            message = f"Inventory added with tracking! {adjustment_result.message}"

        db.session.commit()
    except Exception as err:
        db.session.rollback()
        current_app.logger.exception("Error saving inventory with ledger")
        return error_return_vars(f"Error saving inventory: {err}")

    return return_vars(
        inventory_modal_is_open=True,
        inventory_modal_message_children=message,
        inventory_modal_message_color="green",
        # Signal to refresh the AG Grid data
        store_inventory_modal_updated_data=True,
    )


@callback(
    Output("modal_message_header", "children", allow_duplicate=True),
    Output("modal_message_body", "children", allow_duplicate=True),
    Output("modal_message", "is_open", allow_duplicate=True),
    Output("modal_message", "backdrop", allow_duplicate=True),
    # This will open the "add part to warehouse inventory modal" when clicked
    Output("inventory_add_part_to_warehouse_btn", "n_clicks"),
    Output("inventory_modal_new_part", "value"),
    Output("inventory_modal_actual_quantity", "value"),
    Output("inventory_modal_desired_quantity", "value"),
    Output("inventory_modal_message", "children", allow_duplicate=True),
    Output("inventory_modal_message", "color", allow_duplicate=True),
    Input("inventory_ag_grid", "cellValueChanged"),
    prevent_initial_call=True,
)
def update_inventory_database(inventory_ag_grid_cellValueChanged: list):
    """Update the database when a cell is changed in the inventory table"""

    if (
        not inventory_ag_grid_cellValueChanged
        or not isinstance(inventory_ag_grid_cellValueChanged, list)
        or not inventory_ag_grid_cellValueChanged[0]
    ):
        raise PreventUpdate()

    def return_variables(
        modal_message_header_children=None,
        modal_message_body_children=None,
        modal_message_is_open=False,
        modal_message_backdrop=True,
        # This will open the "add part to warehouse inventory modal" when clicked
        inventory_add_part_to_warehouse_btn_n_clicks=no_update,
        inventory_modal_new_part=no_update,
        inventory_modal_actual_quantity=no_update,
        inventory_modal_desired_quantity=no_update,
        inventory_modal_message_children=no_update,
        inventory_modal_message_color=no_update,
    ) -> tuple:
        """Return the variables"""
        return (
            modal_message_header_children,
            modal_message_body_children,
            modal_message_is_open,
            modal_message_backdrop,
            inventory_add_part_to_warehouse_btn_n_clicks,
            inventory_modal_new_part,
            inventory_modal_actual_quantity,
            inventory_modal_desired_quantity,
            inventory_modal_message_children,
            inventory_modal_message_color,
        )

    warehouse_name: str = ""
    part_name: str = ""
    modal_title: str = ""
    modal_body_metric: str = ""
    new_value: float | None = None
    old_value: float | None = None
    for row in inventory_ag_grid_cellValueChanged:
        row_data = row["data"]
        warehouse_name = row_data["warehouse_name"]
        part_name = row_data["part_name"]
        # The column ID that was changed (e.g. "warehouse_quantity" or "warehouse_quantity_desired")
        col_id = row["colId"]
        old_value = row["oldValue"]
        # Get the new value and convert it to a float
        try:
            new_value = float(row["value"])
        except Exception:
            current_app.logger.exception("Error converting new value to float")
            db.session.rollback()
            return return_variables(
                modal_message_header_children="Error Updating Database",
                modal_message_body_children="The new quantity must be a number",
                modal_message_is_open=True,
                modal_message_backdrop=True,
            )

        # Find the part number scalar
        part_id = get_highest_part_revision_id(part_name)
        if not part_id:
            db.session.rollback()
            return return_variables(
                modal_message_header_children="Error Updating Database",
                modal_message_body_children=f"Could not find the part ID for part '{part_name}'",
                modal_message_is_open=True,
                modal_message_backdrop=True,
            )

        # In case we need to add a part/warehouse to the inventory modal
        warehouse_inventory_update_kwargs = {"inventory_modal_new_part": part_id}

        # Figure out which quantity was changed
        if col_id == "warehouse_quantity_desired":
            modal_title = "Desired Inventory Updated!"
            modal_body_metric = "desired inventory quantity"
            warehouse_inventory_update_kwargs["inventory_modal_desired_quantity"] = (
                new_value
            )
        elif col_id == "warehouse_quantity":
            modal_title = "Actual Inventory Updated!"
            modal_body_metric = "actual inventory quantity"
            warehouse_inventory_update_kwargs["inventory_modal_actual_quantity"] = (
                new_value
            )
        # elif col_id == "warehouse_quantity_available":
        #     modal_title = "Actual Inventory (Minus Reserved) Updated!"
        #     modal_body_metric = "actual inventory quantity minus reserved quantity"
        #     column = "quantity_reserved"
        #     warehouse_inventory_update_kwargs[
        #         "inventory_modal_actual_quantity_minus_reserved"
        #     ] = new_value
        else:
            db.session.rollback()
            return return_variables(
                modal_message_header_children="Error Updating Database",
                modal_message_body_children=f"The column ID '{col_id}' that was changed is not recognized in the database",
                modal_message_is_open=True,
                modal_message_backdrop=True,
            )

        # Find the warehouse ID scalar
        warehouse_id = (
            db.session.query(Warehouse.id)
            .filter(Warehouse.name == warehouse_name)
            .scalar()
        )
        if not warehouse_id:
            db.session.rollback()
            # We need to add the part/warehouse to the inventory modal
            return return_variables(
                modal_message_header_children="Error Updating Database",
                modal_message_body_children=f"Could not find the warehouse ID for warehouse '{warehouse_name}'",
                modal_message_is_open=False,
                modal_message_backdrop=True,
                # Open the "add part to warehouse inventory modal" when clicked
                inventory_add_part_to_warehouse_btn_n_clicks=1,
                **warehouse_inventory_update_kwargs,
                inventory_modal_message_children="Please select a warehouse to which to add this part",
                inventory_modal_message_color="red",
            )

        # Handle actual quantity changes through inventory manager
        if col_id == "warehouse_quantity":
            inventory_manager = InventoryManager(db.session)

            try:
                # Create manual adjustment for actual quantity change
                adjustment_result = inventory_manager.create_manual_adjustment(
                    part_id=part_id,
                    warehouse_id=warehouse_id,
                    new_quantity=Decimal(str(new_value)),
                    user_id=getattr(current_user, "id", None),
                    reason=f"Grid edit: {old_value} → {new_value}",
                    adjustment_type="grid_adjustment",
                )

                if not adjustment_result.success:
                    db.session.rollback()
                    return return_variables(
                        modal_message_header_children="Error Updating Database",
                        modal_message_body_children=f"Error creating adjustment: {', '.join(adjustment_result.errors)}",
                        modal_message_is_open=True,
                        modal_message_backdrop=True,
                    )

                db.session.commit()

            except Exception as err:
                current_app.logger.exception("Error updating inventory with ledger")
                db.session.rollback()
                return return_variables(
                    modal_message_header_children="Error Updating Database",
                    modal_message_body_children=f"There was an error setting the {modal_body_metric} to {new_value}: {err}",
                    modal_message_is_open=True,
                    modal_message_backdrop=True,
                )
        else:
            # Handle desired quantity changes (no movement/ledger needed)
            wh_part_model = (
                db.session.query(WarehousePart)
                .filter_by(
                    warehouse_id=warehouse_id,
                    part_id=part_id,
                )
                .first()
            )
            if not wh_part_model:
                # Create new warehouse part with zero quantity
                wh_part_model = WarehousePart(
                    warehouse_id=warehouse_id,
                    part_id=part_id,
                    quantity=Decimal("0"),
                    quantity_reserved=Decimal("0"),
                )
                db.session.add(wh_part_model)

            try:
                wh_part_model.quantity_desired = Decimal(str(new_value))
                db.session.commit()
            except Exception as err:
                current_app.logger.exception("Error setting desired quantity")
                db.session.rollback()
                return return_variables(
                    modal_message_header_children="Error Updating Database",
                    modal_message_body_children=f"There was an error setting the {modal_body_metric} to {new_value}: {err}",
                    modal_message_is_open=True,
                    modal_message_backdrop=True,
                )

        # Delete the other parts that have the same part_name but different part_id.
        # This way we avoid having two records with same part_name but different part_id
        # in the many-to-many relationship table.
        delete_other_duplicates_in_many_to_many(part_id, warehouse_id)

    return return_variables(
        modal_message_header_children=modal_title,
        modal_message_body_children=f"The {modal_body_metric} has been changed to {new_value} from {old_value} for part '{part_name}' in warehouse '{warehouse_name}'",
        modal_message_is_open=True,
        modal_message_backdrop=True,
    )
