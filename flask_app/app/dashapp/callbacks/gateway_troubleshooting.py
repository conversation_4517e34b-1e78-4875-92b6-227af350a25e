import os

import dash_bootstrap_components as dbc
from dash import ClientsideFunction, Input, Output, State, clientside_callback, html
from flask import url_for
from flask_login import current_user

from app import user_is_ijack_employee


def create_cellular_connection_commands():
    """
    Creates the HTML content for cellular connection troubleshooting commands.
    This function is used to keep the command references DRY between different gateway types.
    """
    return [
        html.H4("Additional Connection Commands", className="mt-3"),
        html.P(
            [
                "View detailed information about all network devices with ",
                html.Code("nmcli device show"),
            ],
            className="mb-2",
        ),
        html.P(
            [
                "Ensure the cellular radio is turned on with ",
                html.Code("sudo nmcli radio wwan on"),
            ],
            className="mb-2",
        ),
        html.H4("Configure Cellular Connections", className="mt-3"),
        html.P(
            "Add new connections with the correct APN settings (replace 'em' with the proper APN):",
            className="mb-2",
        ),
        html.Pre(
            """# For modem using cdc-wdm0 interface
sudo nmcli con add type gsm con-name emnify ipv4.dns ******* autoconnect yes ifname cdc-wdm0 apn em

# For modem using ttyUSB2 interface 
sudo nmcli con add type gsm con-name user_apn ipv4.dns ******* autoconnect yes ifname ttyUSB2 apn em""",
            className="bg-light p-2",
        ),
        html.P("Activate the connections you just created:", className="mb-2"),
        html.Pre(
            """sudo nmcli con up emnify
sudo nmcli con up user_apn""",
            className="bg-light p-2",
        ),
        html.H4("Check Connection Status", className="mt-3"),
        html.P(
            [
                "Check if the connections were established with ",
                html.Code("nmcli con show"),
            ],
            className="mb-2",
        ),
        html.P(
            [
                "Check internet connectivity status with ",
                html.Code("nmcli net con"),
                " (you want to see 'full')",
            ],
            className="mb-2",
        ),
        html.H4("Modem and SIM Information", className="mt-3"),
        html.P(
            [
                "Get cellular signal strength with ",
                html.Code("sudo mmcli -m 0 --signal-get"),
            ],
            className="mb-2",
        ),
        html.P(
            [
                "View complete modem and SIM card information with ",
                html.Code("mmcli -m 0"),
            ],
            className="mb-2",
        ),
    ]


def create_gateway_item(
    name: str, description: str, button_id: str, image_url: str, image_id: str
) -> dbc.Row:
    """Create a list item for a gateway type"""
    return dbc.Row(
        [
            dbc.Col(
                dbc.Button(
                    "Select",
                    id=button_id,
                    color="dark",
                    size="sm",
                    class_name="mr-2",
                ),
                width="auto",
            ),
            dbc.Col(
                [
                    html.Div(html.Span([html.Strong(f"{name} "), description])),
                    dbc.Row(
                        dbc.Col(
                            md=12,
                            lg=10,
                            xl=8,
                            xxl=6,
                            children=html.Img(
                                src=image_url, className="img-fluid", id=image_id
                            ),
                        )
                    ),
                ],
            ),
        ],
        class_name="my-2",
    )


def create_step_section(title, steps):
    """Create a section with a title and steps"""
    content = [html.H4(title, className="mt-4")] if title else []

    for step in steps:
        # Add the step content
        content.append(html.P(step, className="mb-2"))

    return content


def url_forx(x, filename, has_app_context: bool = False):
    """Return the URL for a static file"""
    if has_app_context:
        return url_for(x, filename=filename)
    return f"https://ijack-myijack-public-static-files/{filename}"


def get_windows_11_network_settings_image(has_app_context: bool = False) -> html.Div:
    """Return the div and image for the Windows 11 network settings"""
    return dbc.Row(
        dbc.Col(
            sm=12,
            md=10,
            lg=8,
            xl=6,
            # Create a responsive image component with lazy loading and srcset for different sizes
            children=html.Img(
                # Set the CSS classes for responsive behavior
                className="img-fluid",
                # Specify the alt text for accessibility
                alt="Windows 11 network settings to connect to Axiomtek over ethernet",
                # Set the sizes attribute to control responsive sizing
                sizes="100vw",
                # Define the srcset for responsive images at different widths
                srcSet=f"""
{url_forx("home.s3_no_login", filename="images/gateway-troubleshooting/windows-11-network-settings-for-axiomtek-gateway-600px.webp", has_app_context=has_app_context)} 600w, 
{url_forx("home.s3_no_login", filename="images/gateway-troubleshooting/windows-11-network-settings-for-axiomtek-gateway-1000px.webp", has_app_context=has_app_context)} 1000w, 
{url_forx("home.s3_no_login", filename="images/gateway-troubleshooting/windows-11-network-settings-for-axiomtek-gateway-1249px.webp", has_app_context=has_app_context)} 1249w""",
                # Set the default source image
                src=url_forx(
                    "home.s3_no_login",
                    filename="images/gateway-troubleshooting/windows-11-network-settings-for-axiomtek-gateway-1249px.webp",
                    has_app_context=has_app_context,
                ),
                # Specify the original dimensions for aspect ratio preservation
                width="1249",
                height="1714",
                # Add loading="lazy" through a data attribute since it's not directly supported
                **{"data-loading": "lazy"},
            ),
            className="text-center my-3",
        )
    )


def gateway_troubleshooting_layout(has_app_context: bool = False):
    """Return the layout for the Gateway Troubleshooting Guide page"""

    is_ijack: bool = (
        user_is_ijack_employee(user_id=getattr(current_user, "id", None))
        if has_app_context
        else False
    )
    image_ax = url_forx(
        "home.s3_no_login",
        filename="images/axiomtek.png",
        has_app_context=has_app_context,
    )
    image_cl = url_forx(
        "home.s3_no_login",
        filename="images/compulab.png",
        has_app_context=has_app_context,
    )
    image_fb = url_forx(
        "home.s3_no_login",
        filename="images/fatbox.png",
        has_app_context=has_app_context,
    )
    image_eu = url_forx(
        "home.s3_no_login",
        filename="images/eurotech.png",
        has_app_context=has_app_context,
    )

    if is_ijack:
        GATEWAY_PASSWORD_AX = os.getenv("GATEWAY_PASSWORD_AX", "")
        GATEWAY_PASSWORD_FB = os.getenv("GATEWAY_PASSWORD_FB", "")
        GATEWAY_PASSWORD_CL = os.getenv("GATEWAY_PASSWORD_CL", "")
        GATEWAY_PASSWORD_EU = os.getenv("GATEWAY_PASSWORD_EU", "")
    else:
        GATEWAY_PASSWORD_AX = "Please ask IJACK"
        GATEWAY_PASSWORD_FB = "Please ask IJACK"
        GATEWAY_PASSWORD_CL = "Please ask IJACK"
        GATEWAY_PASSWORD_EU = "Please ask IJACK"

    # Create Axiomtek Troubleshooting Card Content
    axiomtek_content = [
        html.P(
            "Axiomtek gateways are the most common type of gateway. They are light grey in color, and have a powerful dual-core computer inside. Here are some common troubleshooting steps for Axiomtek gateways:"
        ),
        html.H3("Initial Checks", className="mt-4"),
        html.P(
            "Try a different SIM card, to ensure the data plan is activated. You can even try the SIM card from your phone!",
            className="mb-2",
        ),
        html.P("Does the gateway have power?", className="mb-2"),
        html.P(
            "Does the gateway beep a few times, immediately after it's been given power?",
            className="mb-2",
        ),
        html.P(
            "Does the gateway have one solid green light on the left, and one flashing green light?",
            className="mb-2",
        ),
        html.P(
            "Does the gateway have a number written in red marker on the right side, under the bracket?",
            className="mb-2",
        ),
        html.H3("Connecting to the Gateway", className="mt-4"),
        html.P(
            "Plug in an ethernet cable into ethernet interface #2, and connect it to your laptop.",
            className="mb-2",
        ),
        html.H3("Login to set APN or see diagnostic info", className="mt-4"),
        html.P(
            [
                "Go to ",
                html.Strong("http://***********"),
                " in your web browser to see diagnostic data",
            ],
            className="mb-2",
        ),
        html.Ul(
            [
                html.Li("Username is 'ijack'"),
                html.Li("Password is '***********' (same as IP address in the URL)"),
                html.Li("Click on the 'Diagnostics' tab to see the gateway's status"),
            ],
            className="mb-3",
        ),
        html.H4("Setting up your computer's network", className="mt-3"),
        html.P(
            "To connect to the Axiomtek gateway over ethernet on the gateway's interface #2, you'll either need to set your laptop's network settings to 'Automatic (DHCP)' or, if you're still not able to ping *********** over ethernet, you'll need to set your laptop's network settings to use a compatible IP address on the 192.168.2.xxx subnet (e.g. ************* as in the image below). Here's how to do it in Windows 11:",
            className="mb-2",
        ),
        get_windows_11_network_settings_image(has_app_context),
        html.H3("SSH Access for Advanced Troubleshooting", className="mt-4"),
        html.P(
            "Start 'PowerShell' or 'cmd' Command Prompt in Windows 11 to get to the shell terminal to type commands",
            className="mb-2",
        ),
        html.P(
            "*********** is the fixed IP address for SSH, on ethernet interface #2",
            className="mb-2",
        ),
        html.P(
            [
                "Login with ",
                html.Code("ssh user@***********"),
                " (ssh stands for 'Secure Shell'; user is the username, and *********** is the IP address of the Axiomtek eth2 interface)",
            ],
            className="mb-2",
        ),
        html.P(
            [
                "Password is '",
                GATEWAY_PASSWORD_AX,
                "' but you won't see that being typed as you type it. Just trust the keyboard is working.",
            ],
            className="mb-2",
        ),
        html.H3("Cellular Connection Diagnostics", className="mt-4"),
        html.P(["Type ", html.Code("mmcli -m 0"), " to check:"], className="mb-2"),
        html.Ul(
            [
                html.Li("Cell signal strength"),
                html.Li("Modem hardware"),
                html.Li("IMEI"),
                html.Li(
                    "Mobile network code (MNC) for which network you should be on (e.g. Bell, Telus, Rogers, SaskTel, etc)"
                ),
            ],
            className="mb-3",
        ),
        html.P(
            [
                "Type ",
                html.Code("sudo mmcli -m 0 --signal-get"),
                " to refresh cellular signal strength",
            ],
            className="mb-2",
        ),
        html.P(
            [
                "Type ",
                html.Code("nmcli device"),
                " to see which interfaces are being used, and what they're doing",
            ],
            className="mb-2",
        ),
        html.H4("APN Configuration", className="mt-3"),
        html.P("Ensure the cellular APN is correct:", className="mb-2"),
        html.Ul(
            [
                html.Li(
                    [
                        "Type ",
                        html.Code("nmcli con show"),
                        " to see connections to various cellular providers",
                    ]
                ),
                html.Li(
                    [
                        "Type ",
                        html.Code("nmcli con show user_apn"),
                        " for example, to see details of the 'user_apn' connection that's set when the user goes to *********** in the web browser and manually sets the APN",
                    ]
                ),
            ],
            className="mb-3",
        ),
        # Insert the cellular connection commands
        *create_cellular_connection_commands(),
        html.H3("Advanced Diagnostics", className="mt-4"),
        html.P(
            [
                "Type ",
                html.Code("lsusb -t"),
                " to verify that you have the Linux in-kernel qmi_wwan driver installed and attached for the cellular modules QMI interface over USB",
            ],
            className="mb-2",
        ),
        html.Pre(
            "    |__ Port 1: Dev 3, If 2, Class=Vendor Specific Class, Driver=qmi_wwan, 480M",
            className="bg-light p-2",
        ),
        html.P(
            [
                "Type ",
                html.Code("dmesg | grep wwan"),
                " to see if the proper driver 'qmi_wwan' has been loaded on startup",
            ],
            className="mb-2",
        ),
        html.P(
            [
                "cd /etc/modprobe.d and cat /etc/modprobe.d/blacklist-modem.conf to see whether the qmi_wwan driver has been accidentally blacklisted"
            ],
            className="mb-2",
        ),
        html.Pre(
            """$ dmesg|grep wan
[    7.326781] qmi_wwan 1-4:1.5: cdc-wdm0: USB WDM device
[    7.331894] qmi_wwan 1-4:1.5 wwan0: register 'qmi_wwan' at usb-0000:00:15.0-4, WWAN/QMI device, 82:f0:15:3f:03:17
[    7.335328] usbcore: registered new interface driver qmi_wwan""",
            className="bg-light p-2",
        ),
        html.P(
            [
                "Check which version of kernel, and OS, are in use with ",
                html.Code("cat /etc/os-release"),
            ],
            className="mb-2",
        ),
        html.H3("Antenna Configuration", className="mt-4"),
        html.P(
            "Ensure the antenna goes to 'main' on the SIMCom SIM7600 modem, and the GPS antenna goes to GNSS",
            className="mb-2",
        ),
        html.P(
            "The cellular antenna is usually to the right of the power terminal, and GPS to the left",
            className="mb-2",
        ),
        html.H3("Software Fixes and Restart", className="mt-4"),
        html.P(
            "Fix horrible Python software bug in v3.33402 where the network manager system app was stopped but not restarted",
            className="mb-2",
        ),
        html.Pre(
            """sed -i 's/systemctl stop/systemctl restart/g' /home/<USER>/canpy/canpy/check_internet.py
cat /home/<USER>/canpy/canpy/check_internet.py | grep 'systemctl '""",
            className="bg-light p-2",
        ),
        html.P("Uninstall the SIMCom modem driver (unnecessary)", className="mb-2"),
        html.Pre(
            "sudo sh /home/<USER>/canpy/ax/simcom_uninstall.sh", className="bg-light p-2"
        ),
        html.P("Restart gateway", className="mb-2"),
        html.Pre("sudo shutdown -r now", className="bg-light p-2"),
        html.P(
            "Fix horrible Python software bug in v3.3338 where the check_internet.py couldn't find the MNC_APN_DICT in c.MNC_APN_DICT since I forgot the c. in front of it",
            className="mb-2",
        ),
        html.Pre(
            """sed -i 's/apn_dict in MNC_APN_DICT/apn_dict in c.MNC_APN_DICT/g' /home/<USER>/canpy/canpy/check_internet.py
cat /home/<USER>/canpy/canpy/check_internet.py | grep 'apn_dict in '
sed -i 's/apn_dict = MNC_APN_DICT/apn_dict = c.MNC_APN_DICT/g' /home/<USER>/canpy/canpy/check_internet.py
cat /home/<USER>/canpy/canpy/check_internet.py | grep 'apn_dict = '""",
            className="bg-light p-2",
        ),
    ]

    # Create CompuLab Troubleshooting Card Content
    compulab_content = [
        html.P(
            "Compulab gateways are the newest type of gateway. They are black in color, and have a powerful quad-core computer inside. Here are some common troubleshooting steps for Compulab gateways:"
        ),
        html.H3("WiFi Setup", className="mt-4"),
        html.P("Connect to 'IJACK_WIFI' with password 'ijackwifi'", className="mb-2"),
        html.P(
            [
                "Go to ",
                html.Strong("http://***********"),
                " in your web browser to see diagnostic data",
            ],
            className="mb-2",
        ),
        html.Ul(
            [
                html.Li("Username is 'ijack'"),
                html.Li("Password is '***********' (same as IP address in the URL)"),
                html.Li("Click on the 'Diagnostics' tab to see the gateway's status"),
            ],
            className="mb-3",
        ),
        html.H3("Ethernet Setup", className="mt-4"),
        html.P(
            "Plug in an ethernet cable into ethernet interface #2, and connect it to your laptop",
            className="mb-2",
        ),
        html.H3("Login to set APN or see diagnostic info", className="mt-4"),
        html.P(
            [
                "Go to ",
                html.Strong("http://***********"),
                " in your web browser to see diagnostic data",
            ],
            className="mb-2",
        ),
        html.Ul(
            [
                html.Li("Username is 'ijack'"),
                html.Li("Password is '***********' (same as IP address in the URL)"),
                html.Li("Click on the 'Diagnostics' tab to see the gateway's status"),
            ],
            className="mb-3",
        ),
        html.H4("Setting up your computer's network", className="mt-3"),
        html.P(
            "To connect to the Compulab gateway over ethernet on the gateway's interface #2, you'll either need to set your laptop's network settings to 'Automatic (DHCP)' or, if you're still not able to ping *********** over ethernet, you'll need to set your laptop's network settings to use a compatible IP address on the 192.168.2.xxx subnet (e.g. ************* as in the image below). Here's how to do it in Windows 11:",
            className="mb-2",
        ),
        get_windows_11_network_settings_image(has_app_context),
        html.H3("General Troubleshooting", className="mt-4"),
        html.P(
            "Try a different SIM card, to ensure the data plan is activated. You can even try the SIM card from your phone!",
            className="mb-2",
        ),
        html.P(
            "Start 'PowerShell' or 'cmd' Command Prompt in Windows 11 to get to the shell terminal to type commands",
            className="mb-2",
        ),
        html.P(
            "If you connected using WIFI, the IP address is ***********",
            className="mb-2",
        ),
        html.P(
            "If you connected using ethernet, the IP address is ***********",
            className="mb-2",
        ),
        html.P(
            [
                "Login with ",
                html.Code("ssh user@***********"),
                " or ",
                html.Code("ssh user@***********"),
                " depending on whether you connected with ethernet (2.2) or WIFI (3.3). ssh stands for 'Secure Shell'. 'user' is the username, and '***********' or '***********' is the IP address of the network interface)",
            ],
            className="mb-2",
        ),
        html.P(
            [
                "Password is '",
                GATEWAY_PASSWORD_CL,
                "' but you won't see that being typed as you type it. Just trust the keyboard is working.",
            ],
            className="mb-2",
        ),
        html.H3("Cellular Connection Diagnostics", className="mt-4"),
        html.P(["Type ", html.Code("mmcli -m 0"), " to check:"], className="mb-2"),
        html.Ul(
            [
                html.Li("Cell signal strength"),
                html.Li("Modem hardware"),
                html.Li("IMEI"),
                html.Li(
                    "Mobile network code (MNC) for which network you should be on (e.g. Bell, Telus, Rogers, SaskTel, etc)"
                ),
            ],
            className="mb-3",
        ),
        html.P(
            [
                "Type ",
                html.Code("sudo mmcli -m 0 --signal-get"),
                " to refresh cellular signal strength",
            ],
            className="mb-2",
        ),
        html.P(
            [
                "Type ",
                html.Code("nmcli device"),
                " to see which interfaces are being used, and what they're doing",
            ],
            className="mb-2",
        ),
        html.H4("APN Configuration", className="mt-3"),
        html.P("Ensure the cellular APN is correct:", className="mb-2"),
        html.Ul(
            [
                html.Li(
                    [
                        "Type ",
                        html.Code("nmcli con show"),
                        " to see connections to various cellular providers",
                    ]
                ),
                html.Li(
                    [
                        "Type ",
                        html.Code("nmcli con show user_apn"),
                        " for example, to see details of the 'user_apn' connection that's set when the user goes to *********** in the web browser and manually sets the APN",
                    ]
                ),
            ],
            className="mb-3",
        ),
        # Insert the cellular connection commands
        *create_cellular_connection_commands(),
        html.P(
            [
                "Check which version of kernel, and OS, are in use with ",
                html.Code("cat /etc/os-release"),
            ],
            className="mb-2",
        ),
        html.H3("Hardware Checks", className="mt-4"),
        html.P("Does the gateway have power?", className="mb-2"),
        html.P(
            "Does the gateway beep a few times, immediately after it's been given power?",
            className="mb-2",
        ),
        html.P(
            "Does the gateway have one solid green light on the left, and one flashing green light?",
            className="mb-2",
        ),
        html.H3("Antenna Configuration", className="mt-4"),
        html.P(
            "Ensure the antenna goes to 'main' little terminal on the modem, and the GPS antenna goes to GNSS",
            className="mb-2",
        ),
        html.P(
            "The cellular antenna is usually to the right of the power terminal, and GPS to the left",
            className="mb-2",
        ),
        html.H3("Restart Gateway", className="mt-4"),
        html.Pre("sudo shutdown -r now", className="bg-light p-2"),
    ]

    # Create FATBOX Troubleshooting Card Content
    fatbox_content = [
        html.P(
            "FATBOX gateways are an older type of gateway. They are black in color, and have a 3G modem inside. Unfortunately, 3G networks are being phased out, so these gateways will have to be replaced soon. Here are some common troubleshooting steps for FATBOX gateways:"
        ),
        html.H3("Initial Checks", className="mt-4"),
        html.P(
            "Try a different SIM card, to ensure the data plan is activated. You can even try the SIM card from your phone!",
            className="mb-2",
        ),
        html.H3("Connecting to the Gateway", className="mt-4"),
        html.P(
            "Plug in an ethernet cable into ethernet interface zero (eth0), and connect it to your laptop",
            className="mb-2",
        ),
        html.H3("Login to set APN or see diagnostic info", className="mt-4"),
        html.P(
            [
                "Go to ",
                html.Strong("http://***********"),
                " in your web browser to see diagnostic data",
            ],
            className="mb-2",
        ),
        html.Ul(
            [
                html.Li("Username is 'admin'"),
                html.Li(["Password is '", GATEWAY_PASSWORD_FB, "'"]),
            ],
            className="mb-3",
        ),
        html.H3("SSH Access for Advanced Troubleshooting", className="mt-4"),
        html.P(
            "Start 'PowerShell' or 'cmd' Command Prompt in Windows 11 to get to the shell terminal to type commands",
            className="mb-2",
        ),
        html.P(
            "*********** is the fixed IP address for SSH, on ethernet interface zero (eth0)",
            className="mb-2",
        ),
        html.P(
            [
                "Login with ",
                html.Code("ssh root@***********"),
                " (ssh stands for 'Secure Shell'; 'root' is the username, and *********** is the IP address of the FATBOX eth0 interface)",
            ],
            className="mb-2",
        ),
        html.P(
            [
                "Password is '",
                GATEWAY_PASSWORD_FB,
                "' but you won't see that being typed as you type it. Just trust the keyboard is working.",
            ],
            className="mb-2",
        ),
        html.H3("Diagnostics Commands", className="mt-4"),
        html.P(
            "Check the IP routes if it's not connecting to the internet",
            className="mb-2",
        ),
        html.Pre("ip route", className="bg-light p-2"),
        html.P("Check the IP addresses of the interfaces", className="mb-2"),
        html.Pre("ifconfig", className="bg-light p-2"),
        html.P("Check the drive space on the FATBOX", className="mb-2"),
        html.Pre("df -h\nfdisk -l /dev/mmcblk0", className="bg-light p-2"),
        html.P("Reboot if necessary", className="mb-2"),
        html.Pre("reboot -f", className="bg-light p-2"),
        html.H3("FATBOX Reset Procedure", className="mt-4"),
        html.P(
            "Before you try the following, just hard-reboot the FATBOX by cycling the power to the FATBOX. This often starts it working again",
            className="mb-2",
        ),
        html.H4("Soft Reset", className="mt-3"),
        html.P(
            "To reset the G3 to factory default, press and hold reset button through a poke-hole next to the red DIP switches, for at least 5 seconds",
            className="mb-2",
        ),
        html.H4("Hard Reset", className="mt-3"),
        html.P(
            "To reset the G3 to factory default, press and hold reset button for at least 10 seconds",
            className="mb-2",
        ),
        html.P("This resets to factory default network settings", className="mb-2"),
        html.H4("Restore SSD drive to original factory settings", className="mt-3"),
        html.P("This will erase all data on the SSD drive", className="mb-2"),
        html.P(
            "You will need a USB drive with a micro-USB connector (OTG), or a USB-to-micro-USB adaptor",
            className="mb-2",
        ),
        html.P("Plug in the USB drive and power up the FATBOX", className="mb-2"),
        html.P(
            "Take note of the high and low signal LEDs, they will blink in an alternating pattern when the firmware flash process is running",
            className="mb-2",
        ),
        html.P("Wait for the LEDs to go solid 'on' (~5 minutes)", className="mb-2"),
        html.P("Remove the USB drive and power cycle the FATBOX", className="mb-2"),
        html.P(
            [
                "Log into the web configuration with the default credentials (default IP address *********** on ethernet 0. Username: 'admin'. Password: '",
                GATEWAY_PASSWORD_FB,
                "')",
            ],
            className="mb-2",
        ),
        html.P("Enable SSH in the management page; save; reboot.", className="mb-2"),
        html.H4("Restore Missing /data Partition", className="mt-3"),
        html.P(
            "To restore the missing /data partition, run the following command:",
            className="mb-2",
        ),
        html.Pre(
            """( echo n # Add a new partition
	echo p # Primary partition
	echo 3 # Partition number
	echo 1544192 # First sector
	echo 7405567 # Last sector (Accept default? varies)
	echo w # Write changes
) | fdisk /dev/mmcblk0""",
            className="bg-light p-2",
        ),
    ]

    # Create Eurotech Troubleshooting Card Content
    eurotech_content = [
        html.P(
            "Eurotech gateways are an older type of gateway. They are light grey in color, and have a 3G modem inside. Unfortunately, 3G networks are being phased out, so these gateways will have to be replaced soon. Here are some common troubleshooting steps for Eurotech gateways:"
        ),
        html.H3("Initial Checks", className="mt-4"),
        html.P(
            "Try a different SIM card, to ensure the data plan is activated. You can even try the SIM card from your phone!",
            className="mb-2",
        ),
        html.P("Does the gateway have lights on, indicating power?", className="mb-2"),
        html.H3("WiFi Connection", className="mt-4"),
        html.P("Does the gateway broadcast its WIFI?", className="mb-2"),
        html.P(
            "Check for a WIFI network something like 'kura_gateway_0_04:A3:16:F8:B5:D3'",
            className="mb-2",
        ),
        html.P(
            "Static IP address for accessing ESF on gateways over WIFI: **********",
            className="mb-2",
        ),
        html.Ul(
            [html.Li("username: admin"), html.Li("password: testKEYS")],
            className="mb-3",
        ),
        html.H3("SSH Access", className="mt-4"),
        html.P(
            "Start 'PowerShell' or 'cmd' Command Prompt in Windows 11 to get to the shell terminal to type commands",
            className="mb-2",
        ),
        html.P(
            [
                "Login with ",
                html.Code("ssh root@**********"),
                " (ssh stands for 'Secure Shell'; 'root' is the username, and ********** is the IP address of the Eurotech gateway)",
            ],
            className="mb-2",
        ),
        html.Ul(
            [
                html.Li("username: root"),
                html.Li(["password: '", GATEWAY_PASSWORD_EU, "'"]),
            ],
            className="mb-3",
        ),
        html.H3("Web Interface", className="mt-4"),
        html.P(
            "Go to either '**********' in the web browser if you accessed the Eurotech via ethernet, or '**********' if you accessed the Eurotech via WIFI",
            className="mb-2",
        ),
        html.Ul(
            [html.Li("username: admin"), html.Li("password: admin")], className="mb-3"
        ),
        html.H3("Restart Networking", className="mt-4"),
        html.Pre("/etc/init.d/networking restart", className="bg-light p-2"),
    ]

    return dbc.Container(
        [
            dbc.Row(
                justify="center",
                children=dbc.Col(
                    xs=12,
                    lg=10,
                    children=[
                        html.H1(
                            "IJACK Gateway Troubleshooting Guide",
                            # className="text-center",
                        ),
                        html.H2(
                            "Introduction",
                        ),
                        html.P(
                            "Your IJACK pump uses powerful gateway computers to communicate with the cloud over cellular Internet connections, so you can see real time data and control your pumps from anywhere in the world. This guide will help you troubleshoot common problems with your gateway.",
                        ),
                        html.H2(
                            "Gateway Location in Power Unit",
                        ),
                        html.P(
                            "The gateway is physically installed on the inside of the power unit cabinet door, where it's connected to the following components:",
                        ),
                        html.Ul(
                            [
                                html.Li("Power supply"),
                                html.Li("Pump controller"),
                                html.Li("Cellular antenna"),
                                html.Li("Ethernet cable"),
                                html.Li("GPS antenna"),
                            ],
                        ),
                        html.H2(
                            "Choose a Gateway Type",
                        ),
                        html.P(
                            "IJACK uses four different types of gateways, each with its own unique features and capabilities. Please choose your gateway type to see unique troubleshooting steps. The gateway types are listed in order of most common to least common:",
                        ),
                        create_gateway_item(
                            "Axiomtek",
                            "is light grey color. It's a powerful dual-core computer.",
                            "axiomtek_btn",
                            image_url=image_ax,
                            image_id="axiomtek_image",
                        ),
                        dbc.Collapse(
                            id="axiomtek_collapse",
                            is_open=False,
                            class_name="my-4",
                            children=dbc.Card(
                                [
                                    dbc.CardHeader("Axiomtek Troubleshooting"),
                                    dbc.CardBody(axiomtek_content),
                                ],
                            ),
                        ),
                        create_gateway_item(
                            "Compulab",
                            "is black color. It's a powerful quad-core computer, and is IJACK's newest gateway type.",
                            "compulab_btn",
                            image_url=image_cl,
                            image_id="compulab_image",
                        ),
                        dbc.Collapse(
                            id="compulab_collapse",
                            is_open=False,
                            class_name="my-4",
                            children=dbc.Card(
                                [
                                    dbc.CardHeader("Compulab Troubleshooting"),
                                    dbc.CardBody(compulab_content),
                                ],
                            ),
                        ),
                        create_gateway_item(
                            "FATBOX",
                            "is also black color. Unfortunately this gateway has a 3G modem, so it will have to be replaced soon.",
                            "fatbox_btn",
                            image_url=image_fb,
                            image_id="fatbox_image",
                        ),
                        dbc.Collapse(
                            id="fatbox_collapse",
                            is_open=False,
                            class_name="my-4",
                            children=dbc.Card(
                                [
                                    dbc.CardHeader("FATBOX Troubleshooting"),
                                    dbc.CardBody(fatbox_content),
                                ],
                            ),
                        ),
                        create_gateway_item(
                            "Eurotech",
                            "is light grey color. It's the oldest gateway type, and also has a 3G modem, so it will have to be replaced soon.",
                            "eurotech_btn",
                            image_url=image_eu,
                            image_id="eurotech_image",
                        ),
                        dbc.Collapse(
                            id="eurotech_collapse",
                            is_open=False,
                            class_name="my-4",
                            children=dbc.Card(
                                [
                                    dbc.CardHeader("Eurotech Troubleshooting"),
                                    dbc.CardBody(eurotech_content),
                                ],
                            ),
                        ),
                        html.P(
                            [
                                "If you are unable to resolve the issue, please ",
                                html.A(
                                    "contact us",
                                    href="/contact/",
                                    style={"color": "blue"},
                                ),
                                " for further assistance.",
                            ],
                        ),
                    ],
                ),
            ),
        ],
    )


# JavaScript function to handle gateway selection
clientside_callback(
    ClientsideFunction(
        namespace="gateway_troubleshooting_namespace",
        function_name="open_gateway_instructions",
    ),
    Output("axiomtek_collapse", "is_open"),
    Output("compulab_collapse", "is_open"),
    Output("fatbox_collapse", "is_open"),
    Output("eurotech_collapse", "is_open"),
    Output("axiomtek_image", "style"),
    Output("compulab_image", "style"),
    Output("fatbox_image", "style"),
    Output("eurotech_image", "style"),
    Input("axiomtek_btn", "n_clicks"),
    Input("compulab_btn", "n_clicks"),
    Input("fatbox_btn", "n_clicks"),
    Input("eurotech_btn", "n_clicks"),
    State("axiomtek_collapse", "is_open"),
    State("compulab_collapse", "is_open"),
    State("fatbox_collapse", "is_open"),
    State("eurotech_collapse", "is_open"),
    prevent_initial_call=True,
)
