from collections import OrderedDict
from datetime import date

import dash_bootstrap_components as dbc
import numpy as np
import pandas as pd
from dash import Input, Output, callback, dcc, html
from dash.exceptions import PreventUpdate
from dash_ag_grid import AgGrid
from flask import url_for
from flask_login import current_user
from pandas import DataFrame
from sqlalchemy.sql import text

from app import user_is_ijack_employee
from app.config import (
    CUST_SUB_GROUP_ID_ALL_GROUPS,
    CUSTOMER_ID_ALL_CUSTOMERS,
    CUSTOMER_ID_TUNDRA,
    SOFTWARE_VERSIONS_WITH_BUGS,
    TAB_STATUS,
    UNIT_TYPE_ID_ALL_TYPES,
    UNIT_TYPES_IDS_XFER_EGAS,
    WAREHOUSE_ID_ALL_WAREHOUSES,
)
from app.dashapp import metrics
from app.dashapp.callbacks.performance import (
    get_performance_columns,
    get_performance_df,
)
from app.dashapp.layout_utils import get_ag_grid
from app.dashapp.utils import (
    log_function_caller,
)
from app.databases import run_sql_query
from app.utils.complex import get_user_cust_ids


def get_status_row() -> dbc.Row:
    """Row with status table for the units"""
    return dbc.Row(
        id="status_div",
        style={"display": "none"},
        class_name="mb-3",
        children=dbc.Col(
            [
                html.Div(
                    className="mb-3",
                    children=dbc.Button(
                        "Download CSV",
                        id="status_download_csv",
                        color="secondary",
                        outline=True,
                        size="sm",
                    ),
                ),
                dbc.Spinner(
                    color="success",
                    children=[
                        dcc.Store(id="status_structure_id_store"),
                        get_ag_grid(
                            id="status_ag_grid",
                            # Set the structure_id as the row id or "value" in the row data
                            getRowId="params.data.structure_id",
                            paginationPageSize=20,
                            autoHeight=True,
                        ),
                    ],
                ),
            ]
        ),
    )


def get_status_table_df(is_ijack: bool, customer_ids_list: list) -> DataFrame:
    """Get all units for the customer_ids_list"""
    bindparams = {}
    sql = """
        select distinct on (gateway)
            t1.gateway,
            t1.aws_thing,
            gt.name as gateway_type,
            t2.id as customer_id,
            t2.customer,
            t8.abbrev as cust_sub_group_abbrev,
            t5.power_unit_str,
            t4.id as structure_id,
            t4.surface,
            t4.gps_lat,
            t4.gps_lon,
            t6.unit_type_id,
            t7.unit_type,
            t4.model_type_id,
            t6.model,
            t9.days_since_reported,
            t10.am_i_worried,
            t10.why_worried,
            t10.operators_contacted,
            t9.swv_canpy,
            t9.swv_plc,
            t9.hyd,
            t9.warn1,
            t9.warn2,
            case when t9.hours is null then t9.hours else t9.hours / (24 * 30.4375) end as run_months
            --We can get the following from the time_series_locf table instead
            --t9.suction,
            --t9.discharge,
            --t9.spm
        FROM public.structures t4
        LEFT JOIN public.gw t1
            ON t1.power_unit_id = t4.power_unit_id
        LEFT JOIN public.structure_customer_rel t41
            ON t41.structure_id = t4.id
        left join public.customers t2
            on t41.customer_id = t2.id
        LEFT JOIN power_units t5
            ON t1.power_unit_id = t5.id
        LEFT JOIN public.model_types t6
            ON t4.model_type_id = t6.id
        LEFT JOIN public.unit_types t7
            ON t6.unit_type_id = t7.id
        LEFT JOIN public.structure_cust_sub_group_rel t81
            on t81.structure_id = t4.id
        LEFT JOIN public.cust_sub_groups t8
            ON t8.id = t81.cust_sub_group_id
        LEFT JOIN public.gw_info t9
            on t9.gateway_id = t1.id
        LEFT JOIN public.vw_gw_not_connected_dont_worry_latest t10
            on t10.gateway_id = t1.id
            --If they've reported more recently than my notes, then these notes are irrelevant
            and t10.timestamp_utc_worried >= t10.timestamp_utc_last_reported
        LEFT JOIN public.gateway_types gt
            on t1.gateway_type_id = gt.id
        where
            t1.gateway is not null
            and t1.gateway <> 'waiting'
            and t2.customer is not null
            and t4.surface is not null
            and t1.power_unit_id is not null
            and t7.unit_type is not null
            and t4.structure is not null
            and t4.structure_install_date is not null
            and t41.customer_id is distinct from 21 -- demo customer
            and t41.customer_id is distinct from 1 -- IJACK
    """
    if not is_ijack:
        bindparams["cust_ids"] = tuple(customer_ids_list)
        sql = f"{sql} and t41.customer_id in :cust_ids"

    sql = text(sql).bindparams(**bindparams)

    rows, columns = run_sql_query(sql, db_name="ijack")
    return pd.DataFrame(rows, columns=columns)


def get_latest_time_series_df(power_units: list) -> DataFrame:
    """Get the latest time series data for all units"""
    sql = """
        select 
            power_unit as power_unit_str,
            --timestamp_utc,
            max(grease_max) as grease_max,
            min(grease_min) as grease_min,
            avg(agf_dis_temp) as discharge_temp,
            avg(fl_tmp) as flowline_temp,
            avg(gvf) as gas_vol_frac,
            avg(hyd_filt_life) as hyd_filt_life,
            avg(hyd_oil_life) as hyd_oil_life,
            avg(hyd_oil_lvl) as hyd_oil_lvl,
            avg(e3m3_d) as gas_last_24h,
            avg(fluid_rate_vpd) as fluid_last_24h,
            avg(hp_raising_avg) as hp_raising,
            avg(hp_lowering_avg) as hp_lowering,
            avg(ht) as hyd_temp,
            avg(stroke_speed_avg) as stroke_speed,
            avg(spm_egas) as spm,
            avg(cgp) as suction,
            avg(dgp) as discharge,
            avg(dtp) as delta_p
        from public.time_series_locf
        where
            timestamp_utc >= now() - interval '30 minutes'
            and power_unit in :power_units
        group by power_unit
    """
    bindparams = {"power_units": tuple(power_units)}
    sql = text(sql).bindparams(**bindparams)
    rows, columns = run_sql_query(sql, db_name="timescale")
    df = pd.DataFrame(rows, columns=columns)
    # df["is_egas_type"] = df["power_unit"].map(power_units)

    return df


def get_status_column_defs(df: DataFrame, is_ijack: bool) -> list:
    """Get the column definitions for the status Ag Grid"""
    column_defs = [
        # First column with a custom button renderer
        {
            "field": "structure_id",  # Field name for the column
            "headerName": "Select",  # Header for the button column
            "cellRenderer": "ButtonDBC",  # Use custom button renderer
            "cellRendererParams": {"color": "primary", "label": "View"},
            "minWidth": 70,  # Lower minimum width for button column
            "width": 70,  # Fixed width for button column
            # "maxWidth": 80,  # Add this to enforce maximum width
            "suppressSizeToFit": True,  # Prevents auto-sizing from affecting this column
            "flex": 0,  # Very important - prevents column from flexing/growing
            "sortable": False,  # Disable sorting for button column
            "filter": False,  # Disable filtering for button column
            "pinned": "left",  # Pin button column to the left
        },
        {
            "field": "pinned_col",
            "headerName": "Unit",
            "pinned": "left",
            "cellDataType": "text",
            # "cellRenderer": "Tooltip",
            "initialWidth": 200,
            "maxWidth": 200,
        },
        {"field": "customer", "headerName": "Customer"},
        {"field": "cust_sub_group_abbrev", "headerName": "Group"},
        {"field": "model", "headerName": "Model"},
        {
            "field": "surface",
            "headerName": "Surface",
            "headerTooltip": "Surface location of the unit",
            "cellRenderer": "GoogleMapsLink",
        },
        # These two columns are needed for the Google Maps link on the 'surface' field
        {"field": "gps_lat", "hide": True},
        {"field": "gps_lon", "hide": True},
        {
            "field": "power_unit_str",
            "headerName": "Power Unit",
            "headerTooltip": "Power unit serial number",
            "width": 75,
            "initialWidth": 75,
            "cellRenderer": "LinkRenderer",
            "cellRendererParams": {
                "baseUrl": url_for("dash.home"),
                "searchParam": "power_unit",
                "searchField": "power_unit_str",
                "target": "_blank",
            },
        },
        {
            "field": "suction",
            "headerName": "Suction",
            "headerTooltip": "Suction Pressure",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "discharge",
            "headerName": "Discharge",
            "headerTooltip": "Discharge Pressure",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "delta_p",
            "headerName": "Delta P",
            "headerTooltip": "Delta pressure (discharge - suction)",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "grease_max",
            "headerName": "Grease Max",
            "headerTooltip": "Maximum grease pressure",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "grease_min",
            "headerName": "Grease Min",
            "headerTooltip": "Minimum grease pressure",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "discharge_temp",
            "headerName": "Discharge Temp",
            "headerTooltip": "Discharge temperature",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "flowline_temp",
            "headerName": "Flowline Temp",
            "headerTooltip": "Flowline temperature",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "gas_vol_frac",
            "headerName": "Gas Volume Fraction",
            "headerTooltip": "Gas volume as a fraction of total volume",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "hyd_filt_life",
            "headerName": "Hyd Filter Life",
            "headerTooltip": "Hydraulic filter life remaining",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "hyd_oil_life",
            "headerName": "Hyd Oil Life",
            "headerTooltip": "Hydraulic oil life remaining",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "hyd_oil_lvl",
            "headerName": "Hyd Oil Level",
            "headerTooltip": "Hydraulic oil level",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "gas_last_24h",
            "headerName": "Gas Last 24h",
            "headerTooltip": "Gas volume in the last 24 hours",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "fluid_last_24h",
            "headerName": "Fluid Last 24h",
            "headerTooltip": "Fluid volume in the last 24 hours",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "hp_raising",
            "headerName": "HP Upstroke",
            "headerTooltip": "Average hydraulic pressure during upstroke",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "hp_lowering",
            "headerName": "HP Downstroke",
            "headerTooltip": "Average hydraulic pressure during downstroke",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "hyd_temp",
            "headerName": "Hyd Temp",
            "headerTooltip": "Hydraulic temperature",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "stroke_speed",
            "headerName": "Stroke Speed",
            "headerTooltip": "Average stroke speed",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "spm",
            "headerName": "SPM",
            "headerTooltip": "Strokes Per Minute",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
        },
        {
            "field": "hyd",
            "headerName": "Stroking",
            "headerTooltip": "Are the hydraulics stroking",
            "cellDataType": "number",
            "width": 75,
            "initialWidth": 75,
            "cellStyle": {
                "textAlign": "center",
                "styleConditions": [
                    {
                        "condition": "params.value === 0",
                        "style": {"backgroundColor": metrics.BOOTSTRAP_YELLOW_500},
                    },
                ],
            },
        },
        {
            "field": "warn1",
            "headerName": "Warning 1 (Motor)",
            "headerTooltip": "Is there a warning on the motor",
            "cellDataType": "number",
            "width": 75,
            "initialWidth": 75,
            "cellStyle": {
                "textAlign": "center",
                "styleConditions": [
                    {
                        "condition": "params.value === 1",
                        "style": {"backgroundColor": metrics.BOOTSTRAP_RED_500},
                    },
                ],
            },
        },
        {
            "field": "warn2",
            "headerName": "Warning 2 (Hydraulics)",
            "headerTooltip": "Is there a warning on the hydraulics",
            "cellDataType": "number",
            "width": 75,
            "initialWidth": 75,
            "cellStyle": {
                "textAlign": "center",
                "styleConditions": [
                    {
                        "condition": "params.value === 1",
                        "style": {"backgroundColor": metrics.BOOTSTRAP_RED_500},
                    },
                ],
            },
        },
        {
            "field": "run_months",
            "headerName": "Run Months",
            "headerTooltip": "Months since startup",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "textAlign": "right",
            },
            "sortingOrder": ["asc", "desc", None],
        },
        {
            "field": "days_since_reported",
            "headerName": "Days Since Reported",
            "headerTooltip": "Days since we last heard from this unit",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                # "styleConditions": discrete_background_color_bins(
                #     df=df,
                #     n_bins=9,
                #     columns=["days_since_reported"],
                #     reverse=False,
                #     color_start=metrics.BOOTSTRAP_RED_500,
                #     is_ag_grid=True,
                # )
                "styleConditions": [
                    {
                        "condition": "params.value >= 3",
                        "style": {"backgroundColor": metrics.BOOTSTRAP_YELLOW_500},
                    },
                ],
            },
        },
        {
            "field": "swv_plc",
            "headerName": "PLC Software Version",
            "headerTooltip": "PLC Software Version",
            "width": 75,
            "initialWidth": 75,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter0",
            "cellStyle": {
                "textAlign": "center",
                "styleConditions": [
                    {
                        # "condition": "[5,6,7].includes(params.value)",
                        "condition": "params.value <= 315",
                        "style": {"backgroundColor": metrics.BOOTSTRAP_YELLOW_500},
                    },
                ],
            },
        },
        {
            "field": "swv_canpy",
            "headerName": "Gateway Software Version",
            "headerTooltip": "Gateway Software Version",
            "cellDataType": "text",
            "initialWidth": 75,
            "cellStyle": {
                "textAlign": "right",
                "styleConditions": [
                    {
                        "condition": f"{SOFTWARE_VERSIONS_WITH_BUGS}.includes(params.value)",
                        "style": {"backgroundColor": metrics.BOOTSTRAP_RED_500},
                    },
                ],
            },
        },
    ]
    if is_ijack:
        column_defs.append(
            {
                "field": "gateway_type",
                "headerName": "Gateway Type",
                "cellDataType": "text",
                "initialWidth": 75,
            }
        )
        column_defs.append(
            {
                "field": "aws_thing",
                "headerName": "AWS Thing",
                "cellRenderer": "AwsShadowLink",
            }
        )
        column_defs.append(
            {
                "field": "am_i_worried",
                "headerName": "Are We Worried?",
                "headerTooltip": "Are we worried about this unit?",
                "cellRenderer": "StatusWorriedYesNo",
                "initialWidth": 75,
                "cellStyle": {
                    "styleConditions": [
                        {
                            "condition": "params.value === true",
                            "style": {"backgroundColor": metrics.BOOTSTRAP_RED_500},
                        },
                        {
                            "condition": "params.value === false",
                            "style": {"backgroundColor": metrics.IJACK_GREEN},
                        },
                    ]
                },
            }
        )
        column_defs.append(
            {
                "field": "why_worried",
                "headerName": "Why?",
                "headerTooltip": "Why are we worried about this unit?",
                # Render the HTML exactly as it's given to the component
                "cellRenderer": "StatusWorriedYesNo",
                "initialWidth": 275,
                "maxWidth": 275,
            }
        )
        column_defs.append(
            {
                "field": "operators_contacted",
                "headerName": "Operators Contacted",
                "headerTooltip": "Have we contacted the operators?",
                # "cellRenderer": "TimestampFormatter",
                # "cellRenderer": "htmlRenderer",
                "cellRenderer": "StatusWorriedYesNo",
            }
        )

    return column_defs


@callback(
    # Output("status_col", "children"),
    Output("status_ag_grid", "rowData"),
    Output("status_ag_grid", "columnDefs"),
    Input("tabs_for_nav", "active_tab"),
    # Input("store_url_search", "data"),
    # State("use_url_search", "data"),
    # Ensure this callback always fires on page load (from url.search prop updating).
    # The only time we don't want it to fire is if the map tab is active.
    prevent_initial_call=True,
)
def make_status_div(
    tabs_for_nav_active_tab,
    # store_url_search_data,
    # use_url_search_data,
):
    """Make filter for customers (the first filter)"""
    log_function_caller()

    if tabs_for_nav_active_tab != TAB_STATUS:
        raise PreventUpdate()

    def return_vars(status_grid_rowData, status_grid_columnDefs):
        """Default return variables"""
        return status_grid_rowData, status_grid_columnDefs

    user_id: int = getattr(current_user, "id", None)
    cust_ids_tuple: tuple = get_user_cust_ids(user_id=user_id)
    is_ijack: bool = user_is_ijack_employee(
        user_id=user_id, user_cust_ids=cust_ids_tuple
    )

    dfs: DataFrame = get_status_table_df(
        is_ijack=is_ijack, customer_ids_list=cust_ids_tuple
    )
    dfs["is_egas_type"] = dfs["unit_type_id"].isin(UNIT_TYPES_IDS_XFER_EGAS)
    # Get distinct power units
    power_units: list = dfs["power_unit_str"].unique().tolist()

    # Dan wants to include the performance data in the status table as well
    dfp: pd.DataFrame = get_performance_df(
        customer_id=CUSTOMER_ID_ALL_CUSTOMERS,
        warehouse_id=WAREHOUSE_ID_ALL_WAREHOUSES,
        cust_sub_group_id=CUST_SUB_GROUP_ID_ALL_GROUPS,
        unit_type_id=UNIT_TYPE_ID_ALL_TYPES,
        months=(date.today().replace(day=1),),
    )

    # Performance columns we want in the status tab
    # (otherwise there are duplicates like customer, structure, etc.)
    performance_cols_wanted = [
        "structure_id",
        "month_date",
        "sample_size",
        "stroke_speed_avg",
        "hp_usage",
        "discharge_usage",
        "discharge_temp_usage",
        "dtp_usage",
    ]
    dfp = dfp[performance_cols_wanted]

    # Get the column definitions for the performance data (we'll add the status columns later)
    performance_columnDefs: list = [
        d
        for d in get_performance_columns(df=dfp)
        if d["field"] in performance_cols_wanted
    ]

    df1 = dfs.merge(dfp, how="left", on="structure_id")
    del dfs, dfp

    # # Get distinct power units, and whether they are EGAS/XFER type, for chart columns
    # pu_df = df1[["power_unit_str", "is_egas_type"]].drop_duplicates()
    # # Convert to dictionary
    # power_units_dict = pu_df.set_index("power_unit_str")["is_egas_type"].to_dict()

    # current_app.logger.debug("all units df: \n%s", df.head())
    df_ts: DataFrame = get_latest_time_series_df(power_units=power_units)
    df = df1.merge(df_ts, how="left", on="power_unit_str")
    del df1, df_ts

    # Filter out the Tundra units that haven't reported in a long time, and no longer have data
    df = df.loc[
        ~((df["customer_id"] == CUSTOMER_ID_TUNDRA) & (df["days_since_reported"] > 730))
    ]

    # Convert software version columns to floating point numbers
    df["swv_plc"] = df["swv_plc"].astype(float)
    df["swv_canpy"] = df["swv_canpy"].astype(float)

    # Make NaN values 0
    df["swv_plc"] = df["swv_plc"].transform(lambda x: np.where(np.isnan(x), 0, x))
    df["swv_canpy"] = df["swv_canpy"].transform(lambda x: np.where(np.isnan(x), 0, x))

    # Combine power unit and surface columns so we can pin them on the left side of the table
    df["pinned_col"] = (
        df["customer"] + " " + df["unit_type"] + " " + df["power_unit_str"]
    )

    # This dictionary is both for renaming the columns and for sorting them vertically
    col_sort = OrderedDict(
        customer=True,
        cust_sub_group_abbrev=True,
        unit_type=True,
        model=True,
        power_unit_str=True,
        hyd=True,
        warn1=True,
        warn2=True,
        run_months=True,
        suction=True,
        discharge=True,
        spm=True,
        swv_plc=True,
        surface=True,
        aws_thing=True,
        gateway=True,
        why_worried=True,
        gateway_type=True,
        swv_canpy=True,
        operators_contacted=True,
        am_i_worried=False,
        days_since_reported=False,
    )

    # Sort first, before adding the link objects
    df = df.sort_values(by=list(col_sort.keys()), ascending=list(col_sort.values()))

    # [
    #     HighlightIf(
    #         "Days Since Reported", metrics.BOOTSTRAP_YELLOW_500, lambda x: x >= 3
    #     ),
    #     HighlightIf("Are We Worried?", metrics.BOOTSTRAP_RED_500, are_we_worried),
    #     HighlightIf(
    #         "Gateway Software Version",
    #         metrics.BOOTSTRAP_YELLOW_500,
    #         lambda x: x in SOFTWARE_VERSIONS_WITH_BUGS,
    #     ),
    #     HighlightIf(
    #         "PLC Software Version",
    #         metrics.BOOTSTRAP_YELLOW_500,
    #         lambda x: x <= 315,
    #     ),
    #     HighlightIf("Stroking", metrics.IJACK_GREEN, lambda x: x == 1),
    #     HighlightIf(
    #         "Warning 1 (Motor)", metrics.BOOTSTRAP_RED_500, lambda x: x == 1
    #     ),
    #     HighlightIf(
    #         "Warning 2 (Hydraulics)", metrics.BOOTSTRAP_RED_500, lambda x: x == 1
    #     ),
    # ]

    status_grid_columnDefs: list = get_status_column_defs(df=df, is_ijack=is_ijack)
    status_grid_columnDefs.extend(performance_columnDefs)

    return return_vars(
        status_grid_rowData=df.to_dict("records"),
        status_grid_columnDefs=status_grid_columnDefs,
    )


@callback(
    Output("status_ag_grid", "exportDataAsCsv"),
    Input("status_download_csv", "n_clicks"),
    prevent_initial_call=True,
)
def export_data_as_csv(n_clicks):
    """Export the data as CSV"""
    if n_clicks:
        return True
    return False


@callback(
    Output("status_ag_grid", "columnSize"),
    Output("status_ag_grid", "dashGridOptions"),
    # Output("status_ag_grid", "defaultColDef"),
    # Output("status_ag_grid", "getRowStyle"),
    Input("status_ag_grid", "columnDefs"),
    prevent_initial_call=True,
)
def set_ag_grid_options_after_data_arrives(_):
    """Must set the 'columnSize' and 'domLayout' after the rowData; otherwise they won't work!"""
    # autoSize fits the column width to its content
    # sizeToFit fits the table width to the width of the viewport
    # return "sizeToFit"
    ag_grid: AgGrid = get_ag_grid()
    return (
        ag_grid.columnSize,
        ag_grid.dashGridOptions,
        # ag_grid.defaultColDef,
        # ag_grid.getRowStyle,
    )


@callback(
    Output("status_structure_id_store", "data"),
    Input("status_ag_grid", "cellClicked"),
    # State("status_ag_grid", "cellRendererData"),
    # State("status_ag_grid", "rowData"),
    prevent_initial_call=True,
)
def handle_button_click(
    status_ag_grid_cellClicked,
    # data,
    # rows,
):
    """Get the structure_id of the clicked cell in the status Ag Grid"""
    structure_id: int = status_ag_grid_cellClicked.get("value", None)
    if not structure_id:
        raise PreventUpdate

    # df = pd.DataFrame(rows)
    # # Get the row data for the clicked cell
    # df_row = df.loc[df["structure_id"] == structure_id]
    # df_row_dict = df_row.to_dict("records")[0]

    # # Return the row data as JSON string
    # return json.dumps(df_row_dict)

    return structure_id
