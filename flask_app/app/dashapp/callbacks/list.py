import json
import os
from functools import reduce
from types import SimpleNamespace

import dash_bootstrap_components as dbc
import pandas as pd
from dash import (
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
    no_update,
)
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import Gw, StructureVw
from sqlalchemy import func, or_

from app import db, get_user_cust_ids, user_is_demo_customer, user_is_ijack_employee
from app.config import (
    CUST_SUB_GROUP_ID_ALL_GROUPS,
    CUST_SUB_GROUP_ID_ALL_OTHERS,
    CUST_SUB_GROUP_ID_GATEWAYS,
    CUST_SUB_GROUP_ID_SHOP,
    CUSTOMER_ID_ALL_CUSTOMERS,
    CUSTOMER_ID_DEMO,
    CUSTOMER_ID_IJACK_INC,
    MODEL_TYPE_ID_XFER_1235_DUAL,
    MODEL_UNIT_TYPE_ID_EGAS,
    STRUCTURE_ID_SHOP,
    TAB_HEALTH,
    TAB_LIST_FILTERS,
    TAB_MAP,
    TAB_STATUS,
    UNIT_TYPE_ID_ALL_TYPES,
    UNIT_TYPE_ID_DGAS,
    UNIT_TYPE_ID_GATEWAYS,
    UNIT_TYPE_ID_SHOP,
    UNIT_TYPE_ID_UNOGAS,
    WAREHOUSE_ID_ALL_WAREHOUSES,
)
from app.dashapp.utils import (
    convert_to_float,
    get_customer_sub_groups,
    get_customers_rows,
    get_gateways_unused,
    get_id_triggered,
    get_stored_structure_unit,
    get_structure_obj,
    get_structures_rows_cols,
    get_warehouse_options,
    log_function_caller,
)
from app.databases import get_sqla_query_string


def get_search_bar():
    """Get a search bar for searching units, and a radio buttons group to display the results"""
    return dbc.Row(
        # Put stuff in the middle
        class_name="justify-content-sm-center mb-3",
        children=dbc.Col(
            sm=12,
            md=8,
            lg=6,
            xl=4,
            children=[
                dbc.InputGroup(
                    [
                        dbc.Input(
                            id="search_bar",
                            placeholder="Search",
                            type="text",
                            debounce=True,
                            persistence=True,
                            persistence_type="local",
                        ),
                        dbc.Button(
                            [html.I(className="fa-solid fa-search me-1"), "Search"],
                            id="search_bar_btn",
                            color="primary",
                        ),
                    ],
                ),
                # dbc.Modal(
                #     id="search_bar_results_modal",
                #     # centered=True,
                #     # Render a backdrop that doesn't dismiss the modal when clicked
                #     # (i.e. user MUST click a button in the footer)
                #     # backdrop="static",
                #     is_open=False,
                #     children=[
                #         dbc.ModalHeader(
                #             dbc.ModalTitle(
                #                 "Search Results",
                #                 # id="search_bar_results_modal_title",
                #             )
                #         ),
                #         dbc.ModalBody(
                #             dbc.RadioItems(
                #                 label_style=dict(display="block"),
                #                 id="search_bar_results_radio",
                #                 persistence=False,
                #             ),
                #         ),
                #     ],
                # ),
            ],
        ),
    )


def get_search_bar_results():
    """Get a collapse div for the search bar results"""
    return dbc.Collapse(
        id="search_bar_results_collapse",
        is_open=False,
        children=html.Div(
            # Using flex utilities to center and wrap
            className="mb-3 d-flex justify-content-center",
            children=dbc.Card(
                # d-inline-block makes the card only as wide as its contents
                # class_name="d-inline-block",
                children=[
                    dbc.CardHeader(
                        dbc.Row(
                            [
                                dbc.Col(
                                    "Search Results",
                                    width="auto",
                                ),
                                dbc.Col(
                                    dbc.Button(
                                        [
                                            html.I(className="fa-solid fa-times me-1"),
                                            "Close",
                                        ],
                                        id="search_bar_results_close_btn",
                                        color="primary",
                                        outline=True,
                                        size="sm",
                                        class_name="float-end lh-1",
                                    ),
                                    width="auto",
                                ),
                            ],
                            justify="between",
                        )
                    ),
                    dbc.CardBody(
                        # Center horizontally on small screens
                        class_name="justify-content-sm-center",
                        children=dbc.RadioItems(
                            label_style=dict(display="block"),
                            id="search_bar_results_radio",
                            persistence=False,
                        ),
                    ),
                ]
            ),
        ),
    )


def get_list_tab_contents(is_ijack_user: bool):
    """Get the contents for when the user clicks on the 'list' tab"""
    return dbc.Row(
        class_name="justify-content-center",
        children=dbc.Col(
            id="list_div",
            lg=12,
            xxl=10,
            # Show either the map or list by default
            # style={"display": "none"},
            children=[
                get_search_bar(),
                get_search_bar_results(),
                dbc.Row(
                    [
                        dbc.Col(
                            sm=6,
                            md=6,
                            lg=3,
                            xl=3,
                            children=[
                                dbc.Row(
                                    dbc.Col(
                                        # sm=6,
                                        # md=6,
                                        # lg=3,
                                        # xl=3,
                                        children=dbc.Card(
                                            class_name="mb-3",
                                            children=[
                                                dbc.CardHeader(
                                                    "Customer",
                                                ),
                                                dbc.CardBody(
                                                    id="customer_card_body",
                                                    children=dbc.Spinner(
                                                        dbc.RadioItems(
                                                            id="customer_radio",
                                                            label_style=dict(
                                                                display="block"
                                                            ),
                                                            persistence=True,
                                                            persistence_type="local",
                                                        ),
                                                        color="success",
                                                    ),
                                                    style={
                                                        "max-height": "306px",
                                                        "overflow-y": "scroll",
                                                    },
                                                ),
                                            ],
                                        ),
                                    ),
                                ),
                                dbc.Row(
                                    id="warehouse_row",
                                    style={} if is_ijack_user else {"display": "none"},
                                    children=dbc.Col(
                                        # sm=6,
                                        # md=6,
                                        # lg=3,
                                        # xl=3,
                                        children=dbc.Card(
                                            class_name="mb-3",
                                            children=[
                                                dbc.CardHeader(
                                                    "Warehouse",
                                                ),
                                                dbc.CardBody(
                                                    id="warehouse_card_body",
                                                    children=dbc.Spinner(
                                                        dbc.RadioItems(
                                                            id="warehouse_radio",
                                                            label_style=dict(
                                                                display="block"
                                                            ),
                                                            persistence=True,
                                                            persistence_type="local",
                                                        ),
                                                        color="success",
                                                    ),
                                                    style={
                                                        "max-height": "306px",
                                                        "overflow-y": "scroll",
                                                    },
                                                ),
                                            ],
                                        ),
                                    ),
                                ),
                            ],
                        ),
                        dbc.Col(
                            sm=6,
                            md=6,
                            lg=3,
                            xl=3,
                            children=[
                                dbc.Card(
                                    [
                                        dbc.CardHeader(
                                            [
                                                "Group",
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-edit me-1"
                                                        ),
                                                        "Edit Groups",
                                                    ],
                                                    id="cust_sub_groups_edit_btn",
                                                    color="primary",
                                                    outline=True,
                                                    class_name="float-end lh-1",
                                                    size="sm",
                                                    style={"display": "none"},
                                                    external_link=True,
                                                ),
                                            ]
                                        ),
                                        dbc.CardBody(
                                            id="cust_sub_groups_card_body",
                                            children=dbc.Spinner(
                                                dbc.RadioItems(
                                                    id="cust_sub_groups_radio",
                                                    label_style=dict(display="block"),
                                                    persistence=True,
                                                    persistence_type="local",
                                                ),
                                                color="success",
                                            ),
                                            style={
                                                "max-height": "306px",
                                                "overflow-y": "scroll",
                                            },
                                        ),
                                    ],
                                    # We will hide this card if the customer has no sub-groups
                                    id="cust_sub_groups_card",
                                    style={"display": "none"},
                                    class_name="mb-3",
                                ),
                                dbc.Card(
                                    dbc.Spinner(
                                        [
                                            dbc.CardHeader(
                                                "Type",
                                            ),
                                            dbc.CardBody(
                                                id="unit_types_card_body",
                                                # Don't spin it, since there's a
                                                # circular reference on store_structure_dict
                                                # dbc.Spinner(
                                                children=dbc.RadioItems(
                                                    id="unit_type_ids_radio",
                                                    label_style=dict(display="block"),
                                                    persistence=True,
                                                    persistence_type="local",
                                                ),
                                                # color="success",
                                                # )
                                                style={
                                                    "max-height": "306px",
                                                    "overflow-y": "scroll",
                                                },
                                            ),
                                        ],
                                        color="success",
                                    ),
                                    class_name="mb-3",
                                ),
                            ],
                        ),
                        dbc.Col(
                            sm=12,
                            md=12,
                            lg=6,
                            xl=6,
                            children=[
                                dbc.Card(
                                    dbc.Spinner(
                                        [
                                            dbc.CardHeader(
                                                "RCOM Enabled",
                                            ),
                                            dbc.CardBody(
                                                dbc.RadioItems(
                                                    id="rcom_no_rcom_radio",
                                                    label_style=dict(display="block"),
                                                    options=[
                                                        {
                                                            "label": "All",
                                                            "value": "all",
                                                        },
                                                        {
                                                            "label": "Yes",
                                                            "value": "yes",
                                                        },
                                                        {
                                                            "label": "No",
                                                            "value": "no",
                                                        },
                                                    ],
                                                    value="all",
                                                    persistence=True,
                                                    persistence_type="local",
                                                )
                                            ),
                                        ],
                                        color="success",
                                    ),
                                    id="rcom_no_rcom_card",
                                    style={"display": "none"},
                                    class_name="mb-3",
                                ),
                                dbc.Card(
                                    class_name="mb-2",
                                    children=[
                                        dbc.CardHeader(
                                            [
                                                "Units",
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-edit me-1"
                                                        ),
                                                        "Edit Units",
                                                    ],
                                                    id="units_edit_btn",
                                                    color="primary",
                                                    outline=True,
                                                    class_name="float-end lh-1",
                                                    size="sm",
                                                    style={"display": "none"},
                                                    external_link=True,
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-share me-1"
                                                        ),
                                                        "Share",
                                                    ],
                                                    # This button's href is set in the callback
                                                    id="units_share_btn",
                                                    color="primary",
                                                    outline=True,
                                                    class_name="float-end mx-1 lh-1",
                                                    style={"display": "none"},
                                                    size="sm",
                                                    external_link=True,
                                                ),
                                                dcc.Clipboard(
                                                    id="units_share_clipboard",
                                                    # target_id="units_clipboard_target",
                                                    content="",
                                                    n_clicks=0,
                                                    # className="float-end mx-1 lh-1 d-flex justify-content-center align-items-center",
                                                    className="float-end mt-1 mx-1 lh-1",
                                                    style={"display": "none"},
                                                ),
                                            ]
                                        ),
                                        dbc.CardBody(
                                            id="units_card_body",
                                            # Don't spin it, since there's a
                                            # circular reference on store_structure_dict
                                            children=dbc.Spinner(
                                                dbc.RadioItems(
                                                    id="structure_ids_radio",
                                                    label_style=dict(display="block"),
                                                    persistence=True,
                                                    persistence_type="local",
                                                ),
                                                color="success",
                                            ),
                                            style={
                                                "max-height": "306px",
                                                "overflow-y": "scroll",
                                            },
                                        ),
                                    ],
                                ),
                            ],
                        ),
                    ],
                ),
            ],
        ),
    )


def move_default_value_to_top(
    options_list: list, default_value: int, index: int = 1
) -> list:
    """
    Move the default value to the top of the options list.
    This way it's easily visible in the scrollable card.
    """
    ignore_if_less_than: int = 6
    if len(options_list) >= ignore_if_less_than:
        for i, option in enumerate(options_list):
            if option.get("value", None) == default_value and i >= ignore_if_less_than:
                options_list.insert(index, options_list.pop(i))
                break

    return options_list


# Callback to update search results
@callback(
    Output("search_bar_results_radio", "options"),
    Output("search_bar_results_radio", "value"),
    # Output("search_bar_results_modal", "is_open", allow_duplicate=True),
    Output("search_bar_results_collapse", "is_open", allow_duplicate=True),
    Input("search_bar", "value"),
    Input("search_bar", "n_submit"),
    Input("search_bar_btn", "n_clicks"),
    prevent_initial_call=True,
)
def update_search_results(
    search_bar_value,
    search_bar_n_submit: int,
    search_bar_btn_n_clicks: int,
):
    """Update the search results based on the search bar value"""

    def return_vars(
        search_var_radio_options: list,
        search_bar_results_radio_value: int,
        # search_bar_radio_style: dict,
        search_bar_results_is_open: bool = False,
    ):
        return (
            search_var_radio_options,
            search_bar_results_radio_value,
            # search_bar_radio_style,
            search_bar_results_is_open,
        )

    if not search_bar_value:
        raise PreventUpdate()

    # Remove leading and trailing spaces, and make it lowercase
    search_bar_value = str(search_bar_value).strip().lower()

    # IJACK employees can see all customers' power_units
    filters: list = [
        or_(
            StructureVw.power_unit_str.ilike(f"%{search_bar_value}%"),
            StructureVw.structure_str.ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.downhole).ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.surface).ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.aws_thing).ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.customer).ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.cust_sub_group).ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.warehouse_name).ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.unit_type).ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.model).ilike(f"%{search_bar_value}%"),
            func.lower(StructureVw.time_zone).ilike(f"%{search_bar_value}%"),
        ),
        # Must have a customer_id
        StructureVw.customer_id.isnot(None),
    ]
    user_id: int = getattr(current_user, "id", None)
    user_cust_ids: tuple = get_user_cust_ids(user_id=user_id)
    is_ijack_employee: bool = user_is_ijack_employee(
        user_id=user_id, user_cust_ids=user_cust_ids
    )
    if is_ijack_employee:
        # IJACK employees can see all customers' units, but we don't want to see the demo customer
        filters.append(StructureVw.customer_id != CUSTOMER_ID_DEMO)
    else:
        # User can see "customer" power_units only.
        # This might be the demo user or a customer.
        filters.append(StructureVw.customer_id.in_(user_cust_ids))

    query = (
        db.session.query(
            StructureVw.id,
            StructureVw.power_unit_str,
            StructureVw.structure_str,
            StructureVw.surface,
            StructureVw.aws_thing,
            StructureVw.customer,
            StructureVw.cust_sub_group,
            StructureVw.model,
        )
        .filter(*filters)
        .order_by(StructureVw.customer, StructureVw.cust_sub_group)
    )
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        sql_str: str = get_sqla_query_string(query, log_level=None)
        current_app.logger.debug(
            "SQL query to search for the search bar string of %s: \n%s",
            search_bar_value,
            sql_str,
        )

    results_list: list = db.session.execute(query).all()
    structure_or_gateway: str = "structure"
    if not results_list:
        if is_ijack_employee:
            structure_or_gateway = "gateway"
            # Search the gateways as well
            gw_results = Gw.query.filter(
                func.lower(Gw.aws_thing).ilike(f"%{search_bar_value}%")
            ).all()
            # Put into the same format as the structures
            results_list = [
                (gw.id, None, None, None, gw.aws_thing, None, None, None)
                for gw in gw_results
            ]

    num_results: int = len(results_list)
    if num_results == 1:
        # There's only one result, so just load it!
        return return_vars(
            search_var_radio_options=no_update,
            # Get the first structure ID
            search_bar_results_radio_value={structure_or_gateway: results_list[0][0]},
            search_bar_results_is_open=False,
        )
    elif num_results > 1:
        # Multiple results found, so show the modal
        # Use the __repr__ method to show the structure with downhole/surface location
        options_list = []
        for result in results_list:
            unit_id = result[0]
            power_unit_str = result[1]
            # structure_str = result[2]
            surface = result[3]
            aws_thing = result[4]
            customer = result[5]
            cust_sub_group = result[6]
            model = result[7]
            result_str: str = ""
            if is_ijack_employee:
                if customer:
                    result_str = f"{customer} "
                else:
                    result_str = "No customer name. "

            if cust_sub_group:
                result_str += f"{cust_sub_group} "

            if model:
                result_str += f"{model} "
            if surface:
                result_str += f"{surface} "

            if power_unit_str:
                result_str += f"{power_unit_str} "
            elif aws_thing:
                result_str += f"gateway {aws_thing} "
            else:
                result_str += "(no power unit or gateway) "

            # if structure_str:
            #     result_str += f"{structure_str} "

            options_list.append(
                {"label": result_str, "value": {structure_or_gateway: unit_id}}
            )

        return return_vars(
            search_var_radio_options=options_list,
            search_bar_results_radio_value={},
            search_bar_results_is_open=True,
        )

    # If you've made it this far, there are no results
    return return_vars(
        search_var_radio_options=[{"label": "No results found", "value": {}}],
        search_bar_results_radio_value={},
        search_bar_results_is_open=True,
    )


@callback(
    Output("store_search_bar_values_dict", "data"),
    Output("use_search_bar", "data", allow_duplicate=True),
    # Output("search_bar_results_modal", "is_open", allow_duplicate=True),
    Output("search_bar_results_collapse", "is_open", allow_duplicate=True),
    Input("search_bar_results_radio", "value"),
    Input("search_bar_results_close_btn", "n_clicks"),
    prevent_initial_call=True,
)
def store_search_bar_values_dict(
    search_bar_results_radio_value, search_bar_results_close_btn_n_clicks
):
    """Store the search_bar_results_dict so we can reload it on browser refresh"""

    def return_vars(
        store_search_bar_values_dict_data: dict,
        use_search_bar_data: bool,
        search_bar_results_is_open: bool,
    ):
        """Return the variables"""
        return (
            store_search_bar_values_dict_data,
            use_search_bar_data,
            search_bar_results_is_open,
        )

    id_triggered: str = get_id_triggered()
    if (
        id_triggered == "search_bar_results_close_btn.n_clicks"
        and search_bar_results_close_btn_n_clicks
    ):
        return return_vars(
            store_search_bar_values_dict_data=no_update,
            use_search_bar_data=no_update,
            search_bar_results_is_open=False,
        )

    if not search_bar_results_radio_value or not isinstance(
        search_bar_results_radio_value, dict
    ):
        raise PreventUpdate()

    # The key is the string that tells us whether it's a structure or a gateway
    structure_or_gateway: str = list(search_bar_results_radio_value.keys())[0]
    unit_id: int = search_bar_results_radio_value[structure_or_gateway]

    # If no structure IDs were found, but a search bar value was entered
    unit_obj: StructureVw | SimpleNamespace = SimpleNamespace()
    user_id: int = getattr(current_user, "id", None)
    if structure_or_gateway == "structure":
        unit_obj = get_structure_obj(structure_id=unit_id, user_id=user_id)
    elif structure_or_gateway == "gateway":
        gw_model = Gw.query.get(unit_id)
        if not gw_model:
            raise PreventUpdate()
        unit_obj.aws_thing = gw_model.aws_thing
        unit_obj.power_unit_str = None
        unit_obj.customer_id = CUSTOMER_ID_IJACK_INC
        unit_obj.warehouse_id = WAREHOUSE_ID_ALL_WAREHOUSES
        unit_obj.cust_sub_group_id = CUST_SUB_GROUP_ID_GATEWAYS
        unit_obj.unit_type_id = UNIT_TYPE_ID_GATEWAYS

    if not unit_obj:
        raise PreventUpdate()

    unit_ids: list = [unit_id]
    has_rcom: bool = bool(getattr(unit_obj, "aws_thing", False))
    power_unit_str: str = getattr(unit_obj, "power_unit_str", "")

    customer_id = getattr(unit_obj, "customer_id", None)
    customer_ids: list = [customer_id] if customer_id else []

    warehouse_id = getattr(unit_obj, "warehouse_id", None)
    warehouse_ids: list = [warehouse_id] if warehouse_id else []

    cust_sub_group_id = getattr(unit_obj, "cust_sub_group_id", None)
    cust_sub_group_ids: list = (
        [cust_sub_group_id] if cust_sub_group_id else [CUST_SUB_GROUP_ID_ALL_OTHERS]
    )

    unit_type_id = getattr(unit_obj, "unit_type_id", None)
    unit_type_ids: list = [unit_type_id] if unit_type_id else []

    return return_vars(
        store_search_bar_values_dict_data={
            "unit_ids": unit_ids,
            "customer_ids": customer_ids,
            "warehouse_ids": warehouse_ids,
            "cust_sub_group_ids": cust_sub_group_ids,
            "unit_type_ids": unit_type_ids,
            "power_unit_str": power_unit_str,
            "has_rcom": has_rcom,
        },
        # Use search bar results value
        use_search_bar_data=True,
        # Close the search bar results
        search_bar_results_is_open=False,
    )


# Scroll the radio items list back to the top since the default value is at the top
clientside_callback(
    ClientsideFunction(
        namespace="callback_list_namespace",
        function_name="scroll_to_top",
    ),
    Output("customer_card_body", "className"),
    Input("customer_radio", "options"),
    State("customer_card_body", "id"),
    prevent_initial_call=True,
)


@callback(
    Output("customer_radio", "options"),
    Output("customer_radio", "value"),
    Output("store_id_triggered", "data"),
    # # Start the countdown timer, after which we delete the URL search data
    # Output("store_url_search_disable", "n_intervals"),
    # Output("store_url_search_disable", "interval"),
    # Output("store_url_search_disable", "disabled"),
    Input("tabs_for_nav", "active_tab"),
    Input("store_url_search", "data"),
    Input("store_search_bar_values_dict", "data"),
    State("use_url_search", "data"),
    State("use_search_bar", "data"),
    State("store_customer_id", "data"),
    # Ensure this callback always fires on page load (from url.search prop updating).
    # The only time we don't want it to fire is if the map tab is active.
    prevent_initial_call=True,
)
def make_customer_radios(
    tabs_for_nav_active_tab,
    store_url_search_data,
    store_search_bar_values_dict_data: dict,
    use_url_search_data: bool,
    use_search_bar_data: bool,
    store_customer_id,
):
    """Make filter for customers (the first filter)"""
    log_function_caller()

    if tabs_for_nav_active_tab != TAB_LIST_FILTERS:
        raise PreventUpdate()

    rows_list = get_customers_rows(current_user)
    if rows_list == []:
        raise PreventUpdate()

    is_ijack: bool = user_is_ijack_employee(user_id=getattr(current_user, "id", None))

    customer_id_list = set()
    customer_options = []
    for row in rows_list:
        customer_id = row["id"]
        customer_id_list.add(customer_id)

        # For IJACK users, show the country flag
        # https://flagicons.lipis.dev/
        if is_ijack:
            country_code: str = row.get("country_code", "")
            flag_classes: str = f"fi fi-{country_code} me-2"
            label = html.Div([html.Span(className=flag_classes), row["customer"]])
        else:
            label = row["customer"]
        label_val_dict: dict = {
            "label": label,
            "value": customer_id,
        }

        # Show IJACK first
        if customer_id == CUSTOMER_ID_IJACK_INC:
            customer_options.insert(0, label_val_dict)
        else:
            customer_options.append(label_val_dict)

    # Insert the top option for "all" customers
    if is_ijack:
        # Otherwise users can see other customers' units!
        customer_options.insert(
            0, {"label": "All Customers", "value": CUSTOMER_ID_ALL_CUSTOMERS}
        )

    # It's stored as a string, so must be converted
    store_customer_id_float: float | None = convert_to_float(store_customer_id, None)
    if store_customer_id_float in customer_id_list:
        default_value = store_customer_id_float
    else:
        default_value = customer_options[0]["value"]

    store_id_triggered: str | None = get_id_triggered()
    if use_url_search_data and store_id_triggered == "store_url_search.data":
        customer_id = store_url_search_data.get("customer_id", default_value)
        if customer_id in customer_id_list:
            default_value = customer_id

    elif (
        use_search_bar_data
        and store_id_triggered == "store_search_bar_values_dict.data"
    ):
        customer_ids: list = store_search_bar_values_dict_data.get(
            "customer_ids", [default_value]
        )
        for customer_id in customer_ids:
            if customer_id in customer_id_list:
                default_value = customer_id
                break

    # Move the default value to the top of the options list
    customer_options = move_default_value_to_top(
        customer_options, default_value, index=2
    )

    return (
        customer_options,
        default_value,
        store_id_triggered,
    )


# Scroll the radio items list back to the top since the default value is at the top
clientside_callback(
    ClientsideFunction(
        namespace="callback_list_namespace",
        function_name="scroll_to_top",
    ),
    Output("warehouse_card_body", "className"),
    Input("warehouse_radio", "options"),
    State("warehouse_card_body", "id"),
    prevent_initial_call=True,
)


@callback(
    Output("warehouse_radio", "options"),
    Output("warehouse_radio", "value"),
    Input("customer_radio", "value"),
    State("store_id_triggered", "data"),
    State("store_url_search", "data"),
    State("store_search_bar_values_dict", "data"),
    State("use_url_search", "data"),
    State("use_search_bar", "data"),
    State("store_structure_dict", "data"),
    prevent_initial_call=True,
)
def make_warehouse_radios(
    customer_id,
    store_id_triggered_data,
    store_url_search_data: dict,
    store_search_bar_values_dict_data: dict,
    use_url_search_data: bool,
    use_search_bar_data: bool,
    store_structure_dict: dict,
):
    """Make filter for warehouses (e.g. Stettler, Moosomin, Williston)"""
    log_function_caller()

    if customer_id is None:
        raise PreventUpdate()

    if not user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
        return [], WAREHOUSE_ID_ALL_WAREHOUSES

    warehouse_options: list = get_warehouse_options(customer_id=customer_id)
    if warehouse_options == []:
        raise PreventUpdate()

    default_value = WAREHOUSE_ID_ALL_WAREHOUSES

    if use_url_search_data and store_id_triggered_data == "store_url_search.data":
        warehouse_id = store_url_search_data.get("warehouse_id", default_value)
        if warehouse_id in [x["value"] for x in warehouse_options]:
            default_value = warehouse_id

    elif (
        use_search_bar_data
        and store_id_triggered_data == "store_search_bar_values_dict.data"
    ):
        warehouse_ids: list = store_search_bar_values_dict_data.get(
            "warehouse_ids", [default_value]
        )
        for warehouse_id in warehouse_ids:
            if warehouse_id in [x["value"] for x in warehouse_options]:
                default_value = warehouse_id
                break

    # Move the default value to the top of the options list
    warehouse_options = move_default_value_to_top(
        warehouse_options, default_value, index=1
    )

    return warehouse_options, default_value


# Scroll the radio items list back to the top since the default value is at the top
clientside_callback(
    ClientsideFunction(
        namespace="callback_list_namespace",
        function_name="scroll_to_top",
    ),
    Output("cust_sub_groups_card_body", "className"),
    Input("cust_sub_groups_radio", "options"),
    State("cust_sub_groups_card_body", "id"),
    prevent_initial_call=True,
)


@callback(
    Output("cust_sub_groups_radio", "options"),
    Output("cust_sub_groups_radio", "value"),
    Output("cust_sub_groups_card", "style"),
    Input("warehouse_radio", "value"),
    State("customer_radio", "value"),
    State("store_id_triggered", "data"),
    State("store_url_search", "data"),
    State("store_search_bar_values_dict", "data"),
    State("use_url_search", "data"),
    State("use_search_bar", "data"),
    State("store_structure_dict", "data"),
    prevent_initial_call=True,
)
def make_customer_sub_groups_radios(
    warehouse_id,
    customer_id,
    store_id_triggered_data,
    store_url_search_data: dict,
    store_search_bar_values_dict_data: dict,
    use_url_search_data: bool,
    use_search_bar_data: bool,
    store_structure_dict,
):
    """Make filter for customer sub-groups (e.g. Crescent Point SK vs. AB)"""
    log_function_caller()

    if customer_id is None or warehouse_id is None:
        raise PreventUpdate()

    df = get_customer_sub_groups(customer_id, warehouse_id).copy(deep=True)

    # This creates a list of dictionaries--exactly what we need for the customer options
    df = df.rename(
        columns={"cust_sub_group_abbrev": "label", "cust_sub_group_id": "value"}
    )

    cust_sub_group_options = df[df["label"] != "SHOP"].to_dict("records")

    # Check if SHOP is in the list
    if (
        customer_id
        in (
            CUSTOMER_ID_IJACK_INC,
            # CUSTOMER_ID_IJACK_CORP,
            # Only IJACK people can see the "all customers" filter
            CUSTOMER_ID_ALL_CUSTOMERS,
        )
        and warehouse_id == WAREHOUSE_ID_ALL_WAREHOUSES
    ):
        # Show SHOP first (whose "cust_sub_groups" id = 33)
        shop_option = {"label": "SHOP", "value": CUST_SUB_GROUP_ID_SHOP}
        cust_sub_group_options.insert(0, shop_option)

    num_sub_groups = len(cust_sub_group_options)
    sub_group_ids_list = df["value"].to_list()
    if num_sub_groups == 0:
        # Must return something for the value;
        # otherwise the next callback (unit types) won't run.
        return (no_update, None, {"display": "none"})

    # If there's only one group, and it's the "all others" group,
    # return the value but don't show the options
    if num_sub_groups == 1 and sub_group_ids_list[0] == CUST_SUB_GROUP_ID_ALL_OTHERS:
        return (no_update, CUST_SUB_GROUP_ID_ALL_OTHERS, {"display": "none"})

    # Insert the top option for "all" sub-groups
    if num_sub_groups > 1:
        cust_sub_group_options.insert(
            0, {"label": "All Groups", "value": CUST_SUB_GROUP_ID_ALL_GROUPS}
        )

    # Get the customer sub-group, if the structure_id is stored
    default_value = None
    structure_id, unit_type_id = get_stored_structure_unit(
        store_structure_dict, customer_id
    )

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        structure_id, user_id, unit_type_id
    )

    if hasattr(structure_obj, "cust_sub_group_id"):
        if structure_obj.cust_sub_group_id is not None:
            cust_sub_group_id = structure_obj.cust_sub_group_id
        else:
            cust_sub_group_id = CUST_SUB_GROUP_ID_ALL_OTHERS
        if cust_sub_group_id in sub_group_ids_list:
            default_value = cust_sub_group_id

    if default_value is None:
        default_value = cust_sub_group_options[0]["value"]

    if use_url_search_data and store_id_triggered_data == "store_url_search.data":
        cust_sub_group_id = store_url_search_data.get(
            "cust_sub_group_id", default_value
        )
        if cust_sub_group_id in sub_group_ids_list:
            default_value = cust_sub_group_id

    elif (
        use_search_bar_data
        and store_id_triggered_data == "store_search_bar_values_dict.data"
    ):
        cust_sub_group_ids = store_search_bar_values_dict_data.get(
            "cust_sub_group_ids", [default_value]
        )
        for cust_sub_group_id in cust_sub_group_ids:
            if cust_sub_group_id in sub_group_ids_list:
                default_value = cust_sub_group_id
                break

    # If we've made it this far, it means there are, in fact, customer sub-groups,
    # so ensure we show the sub-groups card, which is invisible at first.
    style = {}

    # Move the default value to the top of the options list
    cust_sub_group_options = move_default_value_to_top(
        cust_sub_group_options, default_value, index=1
    )

    return cust_sub_group_options, default_value, style


# Scroll the radio items list back to the top since the default value is at the top
clientside_callback(
    ClientsideFunction(
        namespace="callback_list_namespace",
        function_name="scroll_to_top",
    ),
    Output("unit_types_card_body", "className"),
    Input("unit_type_ids_radio", "options"),
    State("unit_types_card_body", "id"),
    prevent_initial_call=True,
)


@callback(
    Output("unit_type_ids_radio", "options"),
    Output("unit_type_ids_radio", "value"),
    # Input("cust_sub_groups_radio", "options"),
    Input("cust_sub_groups_radio", "value"),
    State("store_url_search", "data"),
    State("store_search_bar_values_dict", "data"),
    State("use_url_search", "data"),
    State("use_search_bar", "data"),
    State("customer_radio", "value"),
    State("warehouse_radio", "value"),
    State("store_structure_dict", "data"),
    State("store_id_triggered", "data"),
    prevent_initial_call=True,
)
def make_unit_types_radios(
    # sub_group_options_list,
    cust_sub_group_id,
    store_url_search_data: dict,
    store_search_bar_values_dict_data: dict,
    use_url_search_data: bool,
    use_search_bar_data: bool,
    customer_id,
    warehouse_id,
    store_structure_dict,
    store_id_triggered_data,
):
    """Based on the customer chosen, make the unit types filter"""
    log_function_caller()

    if customer_id is None:
        raise PreventUpdate()

    # Get all structures for the current user
    # This function is cached so we must use a copy of it
    rows, cols = get_structures_rows_cols(user_id=getattr(current_user, "id", None))
    if not rows:
        return [{"label": "No unit types for customer", "value": None}], None

    df = pd.DataFrame(rows, columns=cols)
    df_row_filters = [
        df["unit_type_id"].notnull(),
        # We'll add these shop and gateways types later, manually
        df["unit_type_id"] != UNIT_TYPE_ID_SHOP,
        df["unit_type_id"] != UNIT_TYPE_ID_GATEWAYS,
    ]
    if customer_id != CUSTOMER_ID_ALL_CUSTOMERS:
        df_row_filters.append(df["customer_id"] == customer_id)
    if cust_sub_group_id != CUST_SUB_GROUP_ID_ALL_GROUPS:
        df_row_filters.append(df["cust_sub_group_id"] == cust_sub_group_id)
    if warehouse_id != WAREHOUSE_ID_ALL_WAREHOUSES:
        df_row_filters.append(df["warehouse_id"] == warehouse_id)

    # Combine filters using logical AND operation with reduce
    combined_filter = reduce(lambda x, y: x & y, df_row_filters)

    unit_types_dups_gone = (
        df.loc[combined_filter, ["unit_type", "unit_type_id"]]
        .drop_duplicates(subset="unit_type_id")
        .sort_values("unit_type", ascending=False)
    )

    # df = df.rename(columns={'unit_type': 'label', 'unit_type_id': 'value'})
    unit_types_list = unit_types_dups_gone["unit_type"].to_list()
    unit_type_ids_list = unit_types_dups_gone["unit_type_id"].to_list()
    unit_type_options = [
        {"label": str(x[0]), "value": x[1]}
        for x in zip(unit_types_list, unit_type_ids_list)
    ]

    default_value: int = None
    if cust_sub_group_id == CUST_SUB_GROUP_ID_SHOP:
        # Make the shop gateway show up first
        unit_type_options.insert(0, {"label": "SHOP", "value": UNIT_TYPE_ID_SHOP})
        default_value = UNIT_TYPE_ID_SHOP

    if cust_sub_group_id == CUST_SUB_GROUP_ID_GATEWAYS:
        # Add the gateways type
        unit_type_options.insert(
            -1, {"label": "GATEWAY", "value": UNIT_TYPE_ID_GATEWAYS}
        )
        default_value = UNIT_TYPE_ID_GATEWAYS

    if unit_type_options == []:
        if cust_sub_group_id is not None:
            return [
                {"label": "No units in group", "value": None}
            ], UNIT_TYPE_ID_ALL_TYPES
        else:
            return [
                {"label": "No units for customer", "value": None}
            ], UNIT_TYPE_ID_ALL_TYPES
    elif len(unit_type_options) > 1:
        # Insert the top option for "all" unit types
        unit_type_options.insert(
            0, {"label": "All Types", "value": UNIT_TYPE_ID_ALL_TYPES}
        )

    if use_url_search_data and store_id_triggered_data == "store_url_search.data":
        unit_type_id = store_url_search_data.get("unit_type_id", None)
        if unit_type_id in unit_type_ids_list:
            default_value = unit_type_id

    elif (
        use_search_bar_data
        and store_id_triggered_data == "store_search_bar_values_dict.data"
    ):
        unit_type_ids: list = store_search_bar_values_dict_data.get("unit_type_ids", [])
        for unit_type_id in unit_type_ids:
            if unit_type_id in unit_type_ids_list:
                default_value = unit_type_id
                break

    if default_value is None:
        # Get the unit type ID, if the structure_id is stored
        structure_id, unit_type_id = get_stored_structure_unit(
            store_structure_dict, customer_id
        )
        # Don't supply the unit_type_id here, since that's what we're trying to find.
        # If we supply a stored unit type of "Gateway"
        user_id: int = getattr(current_user, "id", None)
        structure_obj: StructureVw | None = get_structure_obj(
            structure_id, user_id, unit_type_id
        )
        if hasattr(structure_obj, "unit_type_id"):
            if structure_obj.unit_type_id in unit_type_ids_list:
                default_value = structure_obj.unit_type_id

        if unit_type_id is not None and unit_type_id in unit_type_ids_list:
            # From the store_structure_dict, based on customer ID
            default_value = unit_type_id
        else:
            default_value = unit_type_options[0]["value"]

    # Move the default value to the top of the options list
    unit_type_options = move_default_value_to_top(
        unit_type_options, default_value, index=1
    )

    return unit_type_options, default_value


def filter_structures(
    rows_list: list,
    filter_rcom: bool,
    want_see_rcom: bool,
    customer_id: int,
    warehouse_id: int,
    unit_type_id: int,
    cust_sub_group_id: int,
) -> list:
    """Filter out records from other customers, unit types, or customer sub-groups"""

    rows_list_filtered = []
    structure_ids_list = []  # for tracking duplicates
    # For dual units (e.g. dual XFER), only display one unit since two is confusing,
    # since they both show the exact same thing
    power_unit_ids_list = []

    for record in rows_list:
        # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        #     # Just for debugging
        #     if record['power_unit_str'] in ('10009',):
        #         print('found it')

        if customer_id != CUSTOMER_ID_ALL_CUSTOMERS:
            if record.get("customer_id", None) != customer_id:
                continue

        if warehouse_id != WAREHOUSE_ID_ALL_WAREHOUSES:
            if record.get("warehouse_id", None) != warehouse_id:
                continue

        if cust_sub_group_id != CUST_SUB_GROUP_ID_ALL_GROUPS:
            if record.get("cust_sub_group_id", None) != cust_sub_group_id:
                continue

        unit_type_id_db = record.get("unit_type_id", None)
        if unit_type_id != UNIT_TYPE_ID_ALL_TYPES:
            if unit_type_id_db != unit_type_id:
                continue

        if (
            unit_type_id == UNIT_TYPE_ID_UNOGAS
            and unit_type_id_db == UNIT_TYPE_ID_UNOGAS
        ):
            # model_unit_type_id is the unit type of the model (which is never UNOGAS)
            model_unit_type_id = record.get("model_unit_type_id", None)
            if model_unit_type_id == MODEL_UNIT_TYPE_ID_EGAS:
                # We only need the main unit (i.e. UNO) for this list
                continue

        model_type_id = record.get("model_type_id", None)
        if model_type_id == MODEL_TYPE_ID_XFER_1235_DUAL:
            power_unit_id = record.get("power_unit_id", None)
            if power_unit_id in power_unit_ids_list:
                # We only need one random structure from these dual-unit types
                continue
            else:
                power_unit_ids_list.append(power_unit_id)

        # Check if unit has RCOM (has a gateway)
        gateway_id = record.get("gateway_id", None)
        has_rcom: bool = gateway_id is not None
        if filter_rcom and want_see_rcom is not has_rcom:
            continue

        # Check for duplicate structure IDs, which Dash doesn't seem to allow
        # since it's the value field in the JSON dict
        structure_id_db = record.get("id", None)
        if structure_id_db not in structure_ids_list:
            rows_list_filtered.append(record)
        structure_ids_list.append(structure_id_db)

    return rows_list_filtered


@callback(
    Output("rcom_no_rcom_card", "style"),
    Output("rcom_no_rcom_radio", "value"),
    Input("unit_type_ids_radio", "value"),
    # State("store_url_search", "data"),
    # State("store_search_bar_values_dict", "data"),
    # State("rcom_no_rcom_radio", "value"),
    State("use_url_search", "data"),
    State("use_search_bar", "data"),
    State("cust_sub_groups_radio", "value"),
    State("customer_radio", "value"),
    State("warehouse_radio", "value"),
    State("store_id_triggered", "data"),
    prevent_initial_call=True,
)
def make_rcom_no_rcom_radios(
    unit_type_ids_radio_value,
    # store_url_search_data: dict,
    # store_search_bar_values_dict_data: dict,
    # rcom_no_rcom_radio_value: str,
    use_url_search_data: bool,
    use_search_bar_data: bool,
    cust_sub_group_id,
    customer_id,
    warehouse_id,
    store_id_triggered_data,
):
    """Based on the unit type chosen, make the RCOM-or-no-RCOM radio filter"""
    log_function_caller()

    # if customer_id is None or unit_type_ids_radio_value is None:
    #     raise PreventUpdate()

    if unit_type_ids_radio_value == UNIT_TYPE_ID_GATEWAYS:
        # Gateway types always have RCOM
        return {"display": "none"}, True

    if unit_type_ids_radio_value == UNIT_TYPE_ID_DGAS:
        # DGAS types never have RCOM
        return {"display": "none"}, False

    # This function is cached so we must use a copy of it if we're using the "df"
    rows_list, _ = get_structures_rows_cols(user_id=getattr(current_user, "id", None))

    # Filter out records from other customers, unit types, or customer sub-groups
    rows_list_filtered = filter_structures(
        rows_list=rows_list,
        # filter_rcom=rcom_no_rcom_radio_value != "all",
        # NOTE: Don't filter on RCOM-no-RCOM since we haven't selected a unit type yet
        filter_rcom=False,
        # want_see_rcom=rcom_no_rcom_radio_value in ("all", "yes", True),
        # want_see_rcom only matters if filter_rcom=True
        want_see_rcom=True,
        customer_id=customer_id,
        warehouse_id=warehouse_id,
        unit_type_id=unit_type_ids_radio_value,
        cust_sub_group_id=cust_sub_group_id,
    )

    # to check if some units have gateways, and others not
    gateway_ids_w_rcom_set = set()
    gateway_ids_wo_rcom_set = set()
    for record in rows_list_filtered:
        gateway_id = record.get("gateway_id", None)
        if gateway_id is None:
            gateway_ids_wo_rcom_set.add(gateway_id)
        else:
            gateway_ids_w_rcom_set.add(gateway_id)

    n_gateways_no_rcom = len(gateway_ids_wo_rcom_set)
    n_gateways_w_rcom = len(gateway_ids_w_rcom_set)

    has_rcom_card_style: dict | str = {"display": "none"}
    rcom_no_rcom_radio_value_output: str = "all"
    if n_gateways_w_rcom > 0 and n_gateways_no_rcom == 0:
        # They only have RCOM-enabled units
        has_rcom_card_style = {"display": "none"}
        rcom_no_rcom_radio_value_output = "yes"
    elif n_gateways_w_rcom > 0 and n_gateways_no_rcom > 0:
        # They have units with RCOM, and units without
        has_rcom_card_style = {}
        rcom_no_rcom_radio_value_output = "all"
    elif n_gateways_w_rcom == 0 and n_gateways_no_rcom > 0:
        # They only have units without RCOM
        has_rcom_card_style = {"display": "none"}
        rcom_no_rcom_radio_value_output = "no"
    elif n_gateways_w_rcom == 0 and n_gateways_no_rcom == 0:
        # They have no units so this is just the default value
        has_rcom_card_style = {"display": "none"}
        rcom_no_rcom_radio_value_output = "all"

    if use_url_search_data and store_id_triggered_data == "store_url_search.data":
        rcom_no_rcom_radio_value_output = "all"
    elif (
        use_search_bar_data
        and store_id_triggered_data == "store_search_bar_values_dict.data"
    ):
        rcom_no_rcom_radio_value_output = "all"

    return has_rcom_card_style, rcom_no_rcom_radio_value_output


# Scroll the radio items list back to the top since the default value is at the top
clientside_callback(
    ClientsideFunction(
        namespace="callback_list_namespace",
        function_name="scroll_to_top",
    ),
    Output("units_card_body", "className"),
    Input("structure_ids_radio", "options"),
    State("units_card_body", "id"),
    prevent_initial_call=True,
)


@callback(
    Output("structure_ids_radio", "options"),
    Output("structure_ids_radio", "value"),
    Input("rcom_no_rcom_radio", "value"),
    State("unit_type_ids_radio", "value"),
    State("store_url_search", "data"),
    State("store_search_bar_values_dict", "data"),
    State("use_url_search", "data"),
    State("use_search_bar", "data"),
    State("cust_sub_groups_radio", "value"),
    State("customer_radio", "value"),
    State("warehouse_radio", "value"),
    State("store_structure_dict", "data"),
    State("store_id_triggered", "data"),
    prevent_initial_call=True,
)
def make_units_structures_radios(
    rcom_no_rcom_radio_value: str,
    unit_type_ids_radio_value,
    store_url_search_data: dict,
    store_search_bar_values_dict_data: dict,
    use_url_search_data: bool,
    use_search_bar_data: bool,
    cust_sub_group_id,
    customer_id,
    warehouse_id,
    store_structure_dict,
    store_id_triggered_data,
):
    """Based on the unit type chosen, make the units/structures radio filter"""
    log_function_caller()

    if customer_id is None:
        raise PreventUpdate()

    def return_vars(
        structure_ids_radio_options: list,
        structure_ids_radio_value: int,
    ):
        """Default return variables for the callback"""
        return structure_ids_radio_options, structure_ids_radio_value

    if unit_type_ids_radio_value is None:
        if cust_sub_group_id is None:
            return return_vars(
                structure_ids_radio_options=[
                    {"label": "No type selected", "value": None}
                ],
                structure_ids_radio_value=no_update,
            )
        else:
            return return_vars(
                structure_ids_radio_options=[
                    {"label": "No units in group", "value": None}
                ],
                structure_ids_radio_value=no_update,
            )

    # This is just for selecting the default unit,
    # since the user has already selected a new unit type
    stored_structure_id, _ = get_stored_structure_unit(
        store_structure_dict, customer_id
    )

    default_value = None
    units_options = []
    rows_list_filtered: list = []
    unit_ids_available = set()
    user_id: int = getattr(current_user, "id", None)
    if (
        cust_sub_group_id == CUST_SUB_GROUP_ID_GATEWAYS
        or unit_type_ids_radio_value == UNIT_TYPE_ID_GATEWAYS
    ):
        # Get options and default value for gateways that don't have structures
        gateways = get_gateways_unused(current_user)
        dt_format = "%Y-%m-%d"
        for record in gateways:
            gateway_id = record.get("id", None)
            unit_ids_available.add(gateway_id)
            gateway = record.get("gateway", None)
            timestamp_utc_inserted = record.get("timestamp_utc_inserted", None)
            date_ins = timestamp_utc_inserted.strftime(dt_format)

            location_gw = record.get("location_gw", None)
            if location_gw:
                location = location_gw
            else:
                location = ""

            model_gw = str(record.get("model_gw", None))
            if model_gw == "None":  # we just converted it to a string
                gw_type = ""
            elif "FATBOX" in model_gw:
                gw_type = "FATBOX - "
            elif "ICO120" in model_gw:
                gw_type = "Axiomtek - "
            elif "ICO300" in model_gw:
                gw_type = "Axiomtek - "
            else:
                gw_type = f"{model_gw} - "

            label = f"{date_ins} - {gw_type}{gateway} - {location}"
            units_options.append({"label": label, "value": gateway_id})

            # Set the default value
            if gateway_id == stored_structure_id:
                default_value = gateway_id

    else:
        # This function is cached so we must use a copy of it
        rows_list, _ = get_structures_rows_cols(user_id=user_id)

        rows_list_filtered = filter_structures(
            rows_list=rows_list,
            filter_rcom=rcom_no_rcom_radio_value != "all",
            want_see_rcom=rcom_no_rcom_radio_value in ("all", "yes", True),
            customer_id=customer_id,
            warehouse_id=warehouse_id,
            unit_type_id=unit_type_ids_radio_value,
            cust_sub_group_id=cust_sub_group_id,
        )
        if len(rows_list_filtered) == 0:
            return return_vars(
                structure_ids_radio_options=[
                    {"label": "No units of that type", "value": None}
                ],
                structure_ids_radio_value=no_update,
            )

        for record in rows_list_filtered:
            location_db = record.get("location", None)
            power_unit_db = str(record.get("power_unit", None)).replace(".0", "")
            structure_db = str(record.get("structure", None)).replace(".0", "")
            structure_id_db = record.get("id", None)
            unit_ids_available.add(structure_id_db)
            # power_unit_id_db = record.get("power_unit_id", None)

            # Identify the demo user for which to make fake locations
            is_demo: bool = user_is_demo_customer(user_id=user_id)

            # EGAS units on an UNOGAS sometimes don't have locations
            if location_db is None or is_demo:
                # Don't show a land location
                label = power_unit_db
            elif power_unit_db == "None" and location_db is not None:
                # For DGAS types, show the structure instead of the power unit
                label = f"{structure_db} - {location_db}"
            else:
                # This is the default
                label = f"{power_unit_db} - {location_db}"

            units_options.append({"label": label, "value": structure_id_db})

            # Set the default value
            if structure_id_db == stored_structure_id:
                default_value = structure_id_db

    if not units_options:
        return return_vars(
            structure_ids_radio_options=[
                {"label": "No units of that group or type", "value": None}
            ],
            structure_ids_radio_value=no_update,
        )

    if default_value is None:
        # If it's an IJACK user, always show the IJACK/SHOP/control tab first
        if (
            user_is_ijack_employee(user_id=user_id)  # user's own customer
            and customer_id == CUSTOMER_ID_IJACK_INC  # user-chosen customer
            and cust_sub_group_id == CUST_SUB_GROUP_ID_SHOP
            and unit_type_ids_radio_value == UNIT_TYPE_ID_SHOP
            and STRUCTURE_ID_SHOP in unit_ids_available
        ):
            default_value = STRUCTURE_ID_SHOP
        else:
            default_value = units_options[0].get("value", None)

    if use_url_search_data and store_id_triggered_data == "store_url_search.data":
        structure_id = store_url_search_data.get("structure_id", default_value)
        if structure_id in unit_ids_available:
            default_value = structure_id

    elif (
        use_search_bar_data
        and store_id_triggered_data == "store_search_bar_values_dict.data"
    ):
        unit_ids: list = store_search_bar_values_dict_data.get("unit_ids", [])
        for unit_id in unit_ids:
            if unit_id in unit_ids_available:
                default_value = unit_id
                break

    # Sort the list by label
    units_options = sorted(units_options, key=lambda x: x["label"])

    # Move the default value to the top of the options list
    units_options = move_default_value_to_top(units_options, default_value, index=0)

    return return_vars(
        structure_ids_radio_options=units_options,
        structure_ids_radio_value=default_value,
    )


@callback(
    Output("store_structure_dict", "data"),
    Output("store_structure_id", "data"),
    # store_customer_id isn't used as an input anywhere, just as a state
    Output("store_customer_id", "data"),
    Output("use_url_search", "data"),
    Output("use_search_bar", "data", allow_duplicate=True),
    Input("map", "hoverData"),
    Input("structure_ids_radio", "value"),
    Input("status_structure_id_store", "data"),
    Input("health_view_structure_id_store", "data"),
    State("tabs_for_nav", "active_tab"),
    State("store_tabs_for_ind_charts_control_log", "data"),
    State("store_structure_dict", "data"),
    State("store_unit_type_id", "data"),
    State("store_structure_id", "data"),
    prevent_initial_call=True,
)
def store_structure_id_data(
    map_hover,
    structure_ids_radio,
    status_structure_id_store_data,
    health_view_structure_id_store_data,
    tabs_for_nav_active_tab,
    store_tabs_for_ind_charts_control_log_data,
    store_structure_dict: dict,
    store_unit_type_id,
    store_structure_id_data,
):
    """Update the structure selected (can be from the map, or the list)"""
    log_function_caller()

    # NOTE: It's more reliable to use the active tab than the id_triggered, since the
    # id_triggered can be None if the data=None when initialized (e.g. dcc.Store(data=None))
    id_triggered: str = get_id_triggered()
    # if id_triggered is None:
    #     raise PreventUpdate()

    if (
        id_triggered == "health_view_structure_id_store.data"
        and store_tabs_for_ind_charts_control_log_data == TAB_HEALTH
    ):
        if health_view_structure_id_store_data is None:
            raise PreventUpdate()
        store_structure_id_data = health_view_structure_id_store_data

    elif (
        # id_triggered == "structure_ids_radio.value" or
        tabs_for_nav_active_tab == TAB_LIST_FILTERS
    ):
        if structure_ids_radio is None:
            raise PreventUpdate()
        store_structure_id_data = structure_ids_radio

    elif (
        # id_triggered == "status_structure_id_store.data" or
        tabs_for_nav_active_tab == TAB_STATUS
    ):
        if status_structure_id_store_data is None:
            raise PreventUpdate()
        store_structure_id_data = status_structure_id_store_data

    elif (
        # id_triggered == "map.hoverData" or
        tabs_for_nav_active_tab == TAB_MAP
    ):
        if map_hover is None:
            raise PreventUpdate()
        customdata = map_hover["points"][0]["customdata"]
        try:
            store_structure_id_data = json.loads(customdata)["structure_id"]
        # except json.decoder.JSONDecodeError: # doesn't catch all errors
        except Exception:
            store_structure_id_data = customdata

    else:
        raise PreventUpdate()

    # # I don't think this third option is ever used since the search bar is not an input.
    # # If it were an input, then this would lead to a circular loop.
    # elif use_search_bar_data and id_triggered == "search_bar_results_radio.value":
    #     structure_id_new = store_search_bar_values_dict_data.get(
    #         "structure_id", None
    #     )
    #     if structure_id_new:
    #         store_structure_id_data = structure_id_new

    # So we can get the customer_id for this structure_id
    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        current_app.logger.error(
            f"structure_obj is None for structure_id '{store_structure_id_data}' and store_unit_type_id '{store_unit_type_id}'"
        )
        raise PreventUpdate()

    # Now that we're saving this by customer, it doesn't change nearly as often! TODO: Need new solution!
    # Previously, didn't re-save it if it didn't change, to prevent any circular loops,
    # since the customer list is triggered when the stored structure ID changes!
    # NOTE the customer_id key in {customer_id: structure_id} gets stored as a string anyway!
    customer_id = str(structure_obj.customer_id)
    unit_type_id = str(structure_obj.unit_type_id)

    # If the customer_id is not yet in the dictionary, add it
    if isinstance(store_structure_dict.get(customer_id, 1), int):
        store_structure_dict[customer_id] = {}
    # The following structure_id could actually be a gateway_id if we're looking at gateways
    store_structure_dict[customer_id]["structure_id"] = store_structure_id_data
    store_structure_dict[customer_id]["unit_type_id"] = unit_type_id

    # if only_update_dict:
    #     # We have to re-update the customer/group/type/unit,
    #     # so we'll come back to update the structure_id later.
    #     # Do this so we don't call the downstream callbacks more than once!
    #     return store_structure_dict, no_update, customer_id

    def return_vars(
        store_structure_dict_data: dict,
        store_structure_id_data: int,
        store_customer_id_data: str,
        use_url_search_data: bool,
        use_search_bar_data: bool,
    ) -> tuple:
        """Default return variables"""
        return (
            store_structure_dict_data,
            store_structure_id_data,
            store_customer_id_data,
            use_url_search_data,
            use_search_bar_data,
        )

    if customer_id:
        return return_vars(
            store_structure_dict_data=store_structure_dict,
            store_structure_id_data=store_structure_id_data,
            store_customer_id_data=customer_id,
            use_url_search_data=False,
            use_search_bar_data=False,
        )

    return return_vars(
        store_structure_dict_data=store_structure_dict,
        store_structure_id_data=store_structure_id_data,
        store_customer_id_data=no_update,
        use_url_search_data=False,
        use_search_bar_data=False,
    )
