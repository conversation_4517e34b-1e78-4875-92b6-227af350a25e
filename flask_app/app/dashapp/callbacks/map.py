import random
from typing import Callable

import dash_bootstrap_components as dbc
import numpy as np
import pandas as pd
import plotly.graph_objects as go
from dash import Input, Output, State, callback, dcc, html, no_update
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user

from app import db, user_is_demo_customer
from app.config import (
    COUNTRY_ID_CANADA,
    CUSTOMER_ID_ALL_CUSTOMERS,
    MODEL_TYPE_ID_ALL_TYPES,
    TAB_MAP,
    UNIT_TYPE_ID_ALL_TYPES,
    UNIT_TYPE_ID_GATEWAYS,
    UNIT_TYPE_ID_SHOP,
    UNIT_TYPE_ID_TEST,
    USER_ID_SEAN,
)
from app.dashapp.metrics import unit_type_colors
from app.dashapp.utils import (
    get_db_options,
    get_id_triggered,
    get_stored_structure_unit,
    get_structures_rows_cols,
    log_function_caller,
)
from app.models.models import Structure, User
from shared.utils.geographic_utils import calc_distance


def get_map_filters_all_users():
    """Get the filters for the map"""
    return dbc.Card(
        class_name="mt-3",
        children=[
            dbc.CardHeader("Map Filters"),
            dbc.CardBody(
                class_name="pt-0",
                children=[
                    dbc.Row(
                        [
                            dbc.Col(
                                md=6,
                                class_name="mt-3",
                                children=dbc.ListGroup(
                                    dbc.ListGroupItem(
                                        [
                                            dbc.Label("Unit Type"),
                                            dcc.Dropdown(
                                                id="map_filter_unit_type",
                                                multi=True,
                                                persistence=True,
                                                persistence_type="session",
                                                # All unit types
                                                value=str(UNIT_TYPE_ID_ALL_TYPES),
                                            ),
                                            dbc.FormText(
                                                "Which unit type do you want to see?"
                                            ),
                                        ],
                                    ),
                                ),
                            ),
                            dbc.Col(
                                md=6,
                                class_name="mt-3",
                                children=dbc.ListGroup(
                                    dbc.ListGroupItem(
                                        [
                                            dbc.Label("Model Type"),
                                            dcc.Dropdown(
                                                id="map_filter_model_type",
                                                multi=True,
                                                persistence=True,
                                                persistence_type="session",
                                                # All model_types
                                                value=str(MODEL_TYPE_ID_ALL_TYPES),
                                            ),
                                            dbc.FormText(
                                                "Which model type do you want to see?"
                                            ),
                                        ],
                                    ),
                                ),
                            ),
                        ],
                    ),
                ],
            ),
        ],
    )


def get_map_filters_ijack_only(user: User | None = None):
    """Get the filters for the map, for IJACK users only"""

    user_is_sean: bool = False
    if user and getattr(user, "id", None) == USER_ID_SEAN:
        user_is_sean = True

    return dbc.Card(
        id="map_filters_ijack_only",
        style={"display": "none"},
        class_name="mt-3",
        children=[
            dbc.CardHeader("Map Filters - IJACK Only"),
            dbc.CardBody(
                class_name="pt-0",
                children=[
                    dbc.Row(
                        # The card has its own padding on the top and bottom
                        # class_name="mt-3",
                        children=[
                            dbc.Col(
                                xs=12,
                                md=6,
                                class_name="mt-3",
                                children=dbc.ListGroup(
                                    dbc.ListGroupItem(
                                        [
                                            # dbc.Row(
                                            #     dbc.Col(
                                            dbc.Label(
                                                "Are we worried about this unit?",
                                                # html_for="map_filter_am_i_worried_radio",
                                                # class_name="mt-2",
                                            ),
                                            #     )
                                            # ),
                                            dbc.RadioItems(
                                                id="map_filter_am_i_worried_radio",
                                                options=[
                                                    {"label": "Yes", "value": True},
                                                    {"label": "No", "value": False},
                                                    {"label": "Unsure", "value": None},
                                                ],
                                                value=None,
                                                inline=True,
                                                labelClassName="mr-3",
                                                persistence=True,
                                                persistence_type="local",
                                                # So it's level with the "recalculate" button on the right
                                                # class_name="mb-2",
                                                style={"height": "31px"},
                                            ),
                                            dbc.FormText(
                                                "Have we recorded notes saying that we're worried about this unit not connecting to the internet recently?",
                                            ),
                                        ]
                                    )
                                ),
                            ),
                            dbc.Col(
                                # id="map_recalc_nearest_warehouse_col",
                                # Only Sean can see this, and he's never going to run it anyway
                                # since warehouses are now assigned manually, unless they don't have a warehouse_id.
                                style={} if user_is_sean else {"display": "none"},
                                class_name="mt-3",
                                xs=12,
                                md=6,
                                children=dbc.ListGroup(
                                    dbc.ListGroupItem(
                                        [
                                            dbc.Label(
                                                "Recalculate Nearest Warehouse from GPS Info"
                                            ),
                                            dbc.Row(
                                                # So it's level with the "are we worried" radios on the left
                                                style={"height": "31px"},
                                                justify="start",
                                                children=[
                                                    dbc.Col(
                                                        width="auto",
                                                        # class_name="mt-1",
                                                        children=dbc.Button(
                                                            id="map_recalc_nearest_warehouse_btn",
                                                            disabled=True,
                                                            n_clicks=0,
                                                            color="primary",
                                                            outline=True,
                                                            size="sm",
                                                            children=[
                                                                html.I(
                                                                    className="fa-solid fa-refresh me-1"
                                                                ),
                                                                "Recalculate",
                                                            ],
                                                        ),
                                                    ),
                                                    dbc.Col(
                                                        width="auto",
                                                        # class_name="mt-1",
                                                        children=dbc.Button(
                                                            id="map_recalc_cancel_btn",
                                                            disabled=True,
                                                            n_clicks=0,
                                                            color="primary",
                                                            outline=True,
                                                            size="sm",
                                                            children=[
                                                                html.I(
                                                                    className="fa-solid fa-stop me-1"
                                                                ),
                                                                "Cancel Job",
                                                            ],
                                                        ),
                                                    ),
                                                    dbc.Col(
                                                        width="auto",
                                                        class_name="mt-1",
                                                        children=html.Progress(
                                                            id="map_recalc_progress",
                                                            value="0",
                                                            max="100",
                                                            style={"display": "none"},
                                                        ),
                                                    ),
                                                ],
                                            ),
                                            dbc.Row(
                                                dbc.Col(
                                                    class_name="mt-1",
                                                    children=dbc.Spinner(
                                                        size="sm",
                                                        color="secondary",
                                                        # type="grow",  # or 'border' = default
                                                        children=dbc.FormText(
                                                            id="map_recalc_nearest_warehouse_btn_form_text",
                                                            children="Recalculate the nearest warehouse for all units, based on their GPS coordinates. This takes awhile...",
                                                        ),
                                                    ),
                                                ),
                                            ),
                                        ],
                                    )
                                ),
                            ),
                        ],
                    ),
                    dbc.Row(
                        # class_name="mt-3",
                        children=[
                            dbc.Col(
                                md=6,
                                class_name="mt-3",
                                children=dbc.ListGroup(
                                    dbc.ListGroupItem(
                                        [
                                            dbc.Label("Minimum Days Since Reported"),
                                            dbc.Input(
                                                type="number",
                                                id="map_filter_min_days_since_reported",
                                                placeholder="Days",
                                            ),
                                            dbc.FormText(
                                                "Minimum number of days since the unit last reported to AWS IoT"
                                            ),
                                        ],
                                    ),
                                ),
                            ),
                            dbc.Col(
                                md=6,
                                class_name="mt-3",
                                children=dbc.ListGroup(
                                    dbc.ListGroupItem(
                                        [
                                            dbc.Label("Maximum Days Since Reported"),
                                            dbc.Input(
                                                type="number",
                                                id="map_filter_max_days_since_reported",
                                                placeholder="Days",
                                            ),
                                            dbc.FormText(
                                                "Maximum number of days since the unit last reported to AWS IoT"
                                            ),
                                        ],
                                    ),
                                ),
                            ),
                        ],
                    ),
                    dbc.Row(
                        children=dbc.Col(
                            md=6,
                            class_name="mt-3",
                            children=dbc.ListGroup(
                                dbc.ListGroupItem(
                                    [
                                        dbc.Label("Customer"),
                                        dbc.Select(
                                            id="map_filter_customer",
                                            required=True,
                                            persistence=True,
                                            persistence_type="session",
                                            # All customers
                                            value=str(CUSTOMER_ID_ALL_CUSTOMERS),
                                        ),
                                        dbc.FormText(
                                            "Which customer's units do you want to see?"
                                        ),
                                    ],
                                ),
                            ),
                        ),
                    ),
                ],
            ),
        ],
    )


def get_map_tab_contents(user: User | None = None):
    """Get the contents for when the user clicks on the 'map' tab"""

    filters = get_map_filters_all_users()
    filters_ijack = get_map_filters_ijack_only(user=user)

    graph_row = html.Div(
        [
            dcc.Graph(
                id="map",
                # hoverData={"points": [{"customdata": None}]},
                # Disable the ModeBar with the Plotly logo and other buttons
                config=dict(
                    displayModeBar=False,
                ),
                # Set the height relative to the viewport
                style={"height": "80vh"},
            ),
        ],
    )

    return dbc.Row(
        id="map_tab_contents",
        style={"display": "none"},
        class_name="justify-content-center",
        children=dbc.Col(
            lg=12,
            xxl=10,
            children=[filters, filters_ijack, graph_row],
        ),
    )


def add_warehouse_id(
    map_df: pd.DataFrame,
    # This will rarely, if ever, get done
    # since warehouses are also assigned manually
    force_recalculate: bool = False,
    set_progress_func: Callable = None,
) -> pd.DataFrame:
    """Get the nearest warehouse for each structure in the 'map_df' DataFrame"""

    if force_recalculate:
        # Sean has clicked the button to force a recalculation of ALL units,
        # even if they already have a warehouse_id.
        # Only recalculate the nearest warehouse for units that don't already have one
        needs_warehouse_filters = (
            # map_df["warehouse_id"].isnull()
            map_df["gps_lat"].notnull()
            & map_df["gps_lon"].notnull()
            # and it's a float instance
            & map_df["gps_lat"].transform(lambda x: isinstance(x, float))
            & map_df["gps_lon"].transform(lambda x: isinstance(x, float))
        )
    else:
        # Only recalculate the nearest warehouse for units that don't already have one
        needs_warehouse_filters = (
            map_df["warehouse_id"].isnull()
            & map_df["gps_lat"].notnull()
            & map_df["gps_lon"].notnull()
            # and it's a float instance
            & map_df["gps_lat"].transform(lambda x: isinstance(x, float))
            & map_df["gps_lon"].transform(lambda x: isinstance(x, float))
        )

    df_filtered = map_df[needs_warehouse_filters]
    if df_filtered.empty:
        # No structures need to be updated
        return map_df

    # Get the warehouse DataFrame
    warehouses: list = get_db_options(
        columns=["name", "gps_lat", "gps_lon", "country_id"],
        table="warehouses",
        schema="public",
        where="where is_active = true and can_show_to_customers = true",
        items_list=False,
    )
    warehouse_df = pd.DataFrame(warehouses)

    warehouse_df_ca = warehouse_df.loc[
        warehouse_df["country_id"] == COUNTRY_ID_CANADA
    ].copy(deep=True)

    warehouse_df_us = warehouse_df.loc[
        warehouse_df["country_id"] != COUNTRY_ID_CANADA
    ].copy(deep=True)

    # Get the warehouse closest to each structure by gps_lat and gps_lon
    n_units_to_update = len(df_filtered)
    for idx, row in df_filtered.iterrows():
        if row.gps_lat >= 49:
            # If the structure is in Canada, only consider Canadian warehouses
            warehouse_df2 = warehouse_df_ca
        else:
            # If the structure is in the US, only consider US warehouses
            warehouse_df2 = warehouse_df_us

        # Get the distance between the structure and each warehouse (5-10 of them) in the warehouse_df
        warehouse_df2["distance"] = warehouse_df2.apply(
            lambda series: calc_distance(
                lat1=row.gps_lat,
                lon1=row.gps_lon,
                lat2=series.gps_lat,
                lon2=series.gps_lon,
            ),
            axis=1,
        )

        # Sort the distances from the structure to the warehouses
        warehouse_df2 = warehouse_df2.sort_values("distance")
        # Get the first row (the warehouse with the smallest distance)
        warehouse_nearest_row = warehouse_df2.iloc[0, :]

        structure_id = int(row.id)
        warehouse_id_new = int(warehouse_nearest_row["id"])
        warehouse_name_new = warehouse_nearest_row["name"]

        # Add the warehouse name to the original map_df we're returning
        map_df.loc[map_df["id"] == structure_id, "warehouse_id"] = warehouse_id_new
        map_df.loc[map_df["id"] == structure_id, "warehouse_name"] = warehouse_name_new

        # Add the warehouse_id to the structures table so we don't have to do this next time
        message = f"{idx} of {n_units_to_update}: Updating warehouse to {warehouse_name_new} for structure ID {structure_id}"
        current_app.logger.info(message)
        if set_progress_func and not current_app.config.get(
            "CELERY_TASK_ALWAYS_EAGER", True
        ):
            # Update the Dash background callback progress bar
            # with the current value and max value
            set_progress_func((idx, n_units_to_update))

        model = db.session.get(Structure, structure_id)
        setattr(model, "warehouse_id", warehouse_id_new)
        db.session.commit()

    return map_df


@callback(
    Output("map_filter_customer", "options"),
    Input("map_tab_contents", "style"),
)
def get_customer_options(map_tab_contents_style) -> list:
    """Get the options for the select dropdown"""

    if map_tab_contents_style == {"display": "none"}:
        raise PreventUpdate()

    options: list = get_db_options(
        columns=["customer"],
        table="customers",
        schema="public",
        # add_all_option=True,
    )
    options.insert(0, {"label": "All Customers", "value": "0"})
    return options


@callback(
    Output("map_filter_unit_type", "options"),
    Input("map_tab_contents", "style"),
    prevent_initial_call=True,
)
def get_unit_type_options(map_tab_contents_style) -> list:
    """Get the options for the select dropdown"""

    if map_tab_contents_style == {"display": "none"}:
        raise PreventUpdate()

    tuples_str = ",".join(
        map(str, (UNIT_TYPE_ID_TEST, UNIT_TYPE_ID_GATEWAYS, UNIT_TYPE_ID_SHOP))
    )
    options: list = get_db_options(
        columns=["unit_type"],
        table="unit_types",
        schema="public",
        where=f"where id not in ({tuples_str})",
    )
    options.insert(0, {"label": "All Unit Types", "value": "0"})
    return options


@callback(
    Output("map_filter_model_type", "options"),
    Input("map_tab_contents", "style"),
    Input("map_filter_unit_type", "value"),
    prevent_initial_call=True,
)
def get_model_type_options(
    map_tab_contents_style, map_filter_unit_type_value: list
) -> list:
    """Get the options for the select dropdown"""

    if map_tab_contents_style == {"display": "none"}:
        raise PreventUpdate()

    if not map_filter_unit_type_value or "0" in map_filter_unit_type_value:
        tuples_str = ",".join(
            map(str, (UNIT_TYPE_ID_TEST, UNIT_TYPE_ID_GATEWAYS, UNIT_TYPE_ID_SHOP))
        )
        where = f"where unit_type_id not in ({tuples_str})"
    else:
        map_filter_unit_type_value_str = ",".join(map(str, map_filter_unit_type_value))
        where = f"where unit_type_id in ({map_filter_unit_type_value_str})"

    options: list = get_db_options(
        columns=["model"],
        table="model_types",
        schema="public",
        where=where,
    )
    options.insert(0, {"label": "All Model Types", "value": "0"})
    return options


@callback(
    Output("map_filter_min_days_since_reported", "value"),
    Input("map_filter_am_i_worried_radio", "value"),
    State("map_filter_min_days_since_reported", "value"),
    prevent_initial_call=True,
)
def set_min_days_since_reported_value(
    map_filter_am_i_worried_radio_value, map_filter_min_days_since_reported_value
):
    """Set the value of the min days since reported filter"""
    if map_filter_am_i_worried_radio_value is True:
        return 0.01

    return map_filter_min_days_since_reported_value


@callback(
    Output("map_recalc_nearest_warehouse_btn", "disabled", allow_duplicate=True),
    Input("tabs_for_nav", "active_tab"),
    Input("hidden_signal", "children"),
    # allow_duplicate=True so we must use prevent_initial_call=True
    prevent_initial_call=True,
)
def disable_recalc_button(active_tab, _):
    """Disable the recalc button if it's not Sean"""
    log_function_caller()
    if getattr(current_user, "id", None) == USER_ID_SEAN:
        # Only enable the button (i.e. make it not disabled) for Sean
        return False

    return True


@callback(
    Output("map_recalc_nearest_warehouse_btn_form_text", "children"),
    Input("map_recalc_nearest_warehouse_btn", "n_clicks"),
    prevent_initial_call=True,
    background=True,
    running=[
        (
            Output(
                "map_recalc_nearest_warehouse_btn", "disabled", allow_duplicate=True
            ),
            True,
            False,
        ),
        (Output("map_recalc_cancel_btn", "disabled"), False, True),
        (Output("map_recalc_progress", "style"), {}, {}),
    ],
    cancel=[Input("map_recalc_cancel_btn", "n_clicks")],
    progress=[
        Output("map_recalc_progress", "value"),
        Output("map_recalc_progress", "max"),
    ],
)
def recalculate_nearest_warehouse(
    set_progress_func: Callable,
    map_recalc_nearest_warehouse_btn_n_clicks,
):
    """Recalculate the nearest warehouse for each structure in the database"""

    if (
        not map_recalc_nearest_warehouse_btn_n_clicks
        or get_id_triggered() != "map_recalc_nearest_warehouse_btn.n_clicks"
    ):
        return "Not running since the button hasn't been clicked yet"

    user_id: int = getattr(current_user, "id", None)
    if user_id != USER_ID_SEAN:
        # Only allow Sean to recalculate the nearest warehouse
        return "Only Sean can recalculate the nearest warehouse"

    rows, cols = get_structures_rows_cols(user_id=user_id)
    df = pd.DataFrame(rows, columns=cols)
    if df is None or len(df) == 0:
        return "No structures to update"

    # Get the nearest warehouse for each structure in the 'map_df' DataFrame.
    # This will rarely, if ever, get done since warehouses are now assigned manually.
    df = add_warehouse_id(
        map_df=df, force_recalculate=True, set_progress_func=set_progress_func
    )

    # Get the number of structures that were updated
    n_structures_updated = df["warehouse_id"].notnull().sum()

    return f"Finished updating {n_structures_updated} structures with the nearest warehouse!"


@callback(
    Output("map", "figure"),
    Output("map", "hoverData"),
    Input("tabs_for_nav", "active_tab"),
    Input("map_filter_customer", "value"),
    Input("map_filter_min_days_since_reported", "value"),
    Input("map_filter_max_days_since_reported", "value"),
    Input("map_filter_unit_type", "value"),
    Input("map_filter_model_type", "value"),
    State("map_filter_am_i_worried_radio", "value"),
    State("store_structure_dict", "data"),
    State("store_customer_id", "data"),
    # If we prevent initial call here, and the active_tab doesn't change,
    # but stays on the map tab like before, how will the figure and hoverdata get updated?
    # Try setting prevent_initial_call=True and going to http://app.localhost:5000/rcom/
    prevent_initial_call=False,
)
def make_map(
    active_tab,
    map_filter_customer_value,
    map_filter_min_days_since_reported_value,
    map_filter_max_days_since_reported_value,
    map_filter_unit_type_value,
    map_filter_model_type_value,
    map_filter_am_i_worried_radio_value,
    store_structure_dict,
    store_customer_id,
):
    """
    Based on the tab chosen (or default tab value),
    display a map or a list of units
    """
    log_function_caller()
    if active_tab != TAB_MAP:
        raise PreventUpdate()

    rows, cols = get_structures_rows_cols(
        user_id=getattr(current_user, "id", None),
        customer_id=map_filter_customer_value,
        unit_type_ids=map_filter_unit_type_value,
        model_type_ids=map_filter_model_type_value,
        am_i_worried=map_filter_am_i_worried_radio_value,
        min_days_since_reported=map_filter_min_days_since_reported_value,
        max_days_since_reported=map_filter_max_days_since_reported_value,
    )
    if not rows:
        raise PreventUpdate()

    # Get the nearest warehouse for each structure in the 'map_df' DataFrame
    df = pd.DataFrame(rows, columns=cols)
    if getattr(current_user, "id", None) == USER_ID_SEAN:
        df = add_warehouse_id(map_df=df, force_recalculate=False)

    # Add some interesting extra info to the map in HTML format
    df["unit_info"] = (
        df["customer"].astype(str)
        + " "
        + df["cust_sub_group"].astype(str)
        + "<br>"
        + df["unit_type"].astype(str)
        + " - "
        + df["model"].astype(str)
        + "<br>"
        + df["power_unit_str"].astype(str)
        + " at "
        + df["location"].astype(str)
        + "<br>Warehouse: "
        + df["warehouse_name"].astype(str)
    )
    df["unit_info"] = np.where(
        df["days_since_reported"].isnull(),
        df["unit_info"],
        df["unit_info"]
        + "<br>Last reported: "
        + df["days_since_reported"].astype(str)
        + " days ago",
    )

    # NOTE: Clustering has caused issues with JavaScript errors in the past,
    # especially when removing units from map by filtering
    cluster = None
    # # NOTE you have to hard-refresh the browser to see the changes!
    # cluster = go.scattermap.Cluster(
    #     # Leave the color blank to use the same color as the marker color
    #     # color="black",
    #     # colorsrc=,
    #     # If True, points will be clustered
    #     enabled=True,
    #     # At zoom levels equal to or greater than this (0-24), points will never be clustered
    #     # The greater the zoom, the closer to earth we zoom (2.5 is our default)
    #     maxzoom=20,
    #     opacity=0.7,
    #     # opacitysrc=,
    #     # Size of the cluster balloons (should be bigger than regular marker size)
    #     size=12,
    #     # sizesrc=,
    #     # how many points it takes to create a cluster or advance to the next cluster step
    #     step=2,
    #     # stepsrc=,
    # )

    is_demo_user = user_is_demo_customer(user_id=getattr(current_user, "id", None))
    data = []
    for unit_type, dff in df.groupby("unit_type"):
        if is_demo_user:
            lon = pd.Series(random.randrange(-115, -90))
            lat = pd.Series(random.randrange(30, 53))
            text = "Random Location"
        else:
            lon = dff["gps_lon"]
            lat = dff["gps_lat"]
            text = dff["unit_info"]

        color = unit_type_colors.get(unit_type, None)
        marker = go.scattermap.Marker(
            # Marker size (not including clusters)
            # If clustering is enabled, we can make this much bigger than 5
            size=12 if cluster else 5,
            # 0 is invisible, 1 is no transparency (not including clusters)
            opacity=1,
            color=color,
        )
        trace = go.Scattermap(
            lon=lon,
            lat=lat,
            text=text,
            customdata=dff["id"],  # the structure ID is the value we need to use
            marker=marker,
            name=unit_type,
            showlegend=True,
            # NOTE: Clustering has caused issues with JavaScript errors in the past,
            # especially when removing units from map by filtering
            cluster=cluster,
        )
        data.append(trace)

    layout = go.Layout(
        margin=dict(l=0, r=0, b=20, t=15),
        hovermode="closest",
        # Use the style={"height": "80vh"} on the dcc.Graph instead!
        # height=400,  # 500 is a bit too big on a smartphone. 400 is good.
        legend=dict(
            # font=dict(size=16, color="#717174"),
            font=dict(size=16),
            orientation="v",
            title=dict(text="Unit Type"),
        ),
        map=dict(
            style="light",
            # style="carto-positron",
            center=dict(lat=42.5, lon=-106),  # Default center of map
            zoom=2.5,  # The greater the zoom, the closer to earth we zoom (2-3 is good)
        ),
    )

    if df.empty:
        figure = go.Figure(layout=layout)
    else:
        figure = go.Figure(data=data, layout=layout)

    # Get the HoverData (the selected unit in the chart)
    if df.empty:
        hoverData = no_update
    else:
        # Use the stored structure_id if there is one
        structure_id, _ = get_stored_structure_unit(
            store_structure_dict, store_customer_id
        )
        if structure_id and structure_id in df["id"].values:
            default_structure_id = structure_id
        else:
            first_structure_id = df.iloc[0, :]["id"]
            default_structure_id = first_structure_id
        hoverData = {"points": [{"customdata": default_structure_id}]}

    return figure, hoverData
