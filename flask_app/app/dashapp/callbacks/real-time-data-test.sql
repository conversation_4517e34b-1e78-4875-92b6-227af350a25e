select distinct metric 
from public.time_series_rt
WHERE
    timestamp_utc >= '2025-03-14'
    and power_unit = '200756'

SELECT
    power_unit,
    --timestamp_utc,
    --To round to the nearest half second, multiply the timestamp_utc by 2,
    --round it to the nearest integer, and then divide it by 2 again.
    time_bucket('0.2 seconds', timestamp_utc) AS timestamp_utc,
    avg(
        case
            when metric = 'CGP_RT' then value
            else null
        end
    ) as cgp,
    avg(
        case
            when metric = 'DGP_RT' then value
            else null
        end
    ) as dgp,
    a avg(
        case
            when metric = 'DGP' then value
            else null
        end
    ) as dgp_551,
    avg(
        case
            when metric = 'DISCHARGE_OPT_PSI_D' then value
            else null
        end
    ) as discharge_opt_psi_d,
    avg(
        case
            when metric = 'HPE_RT' then value
            else null
        end
    ) as hpe,
    avg(
        case
            when metric = 'HP_LIMIT' then value
            else null
        end
    ) as hp_limit,
    avg(
        case
            when metric = 'SPM_EGAS_RT' then value
            else null
        end
    ) as spm_egas,
    avg(
        case
            when metric = 'CGP_RT_SL' then value
            else null
        end
    ) as cgp_sl,
    avg(
        case
            when metric = 'DGP_RT_SL' then value
            else null
        end
    ) as dgp_sl,
    avg(
        case
            when metric = 'HPE_RT_SL' then value
            else null
        end
    ) as hpe_sl,
    avg(
        case
            when metric = 'SPM_EGAS_RT_SL' then value
            else null
        end
    ) as spm_egas_sl,
    avg(
        case
            when metric = 'FL_TMP' then value
            else null
        end
    ) as fl_tmp,
    avg(
        case
            when metric = 'SUCTION_TMP' then value
            else null
        end
    ) as suction_tmp,
    avg(
        case
            when metric = 'LAG_TOP_MS' then value
            else null
        end
    ) as lag_top_ms,
    avg(
        case
            when metric = 'LAG_BTM_MS' then value
            else null
        end
    ) as lag_btm_ms,
    avg(
        case
            when metric = 'HP_RAISING_AVG' then value
            else null
        end
    ) as hp_raising_avg,
    avg(
        case
            when metric = 'HP_LOWERING_AVG' then value
            else null
        end
    ) as hp_lowering_avg,
    avg(
        case
            when metric = 'AGF_DIS_TEMP' then value
            else null
        end
    ) as agf_dis_temp,
    avg(
        case
            when metric = 'HYD_PRESS_S' then value
            else null
        end
    ) as hyd_press_s,
    avg(
        case
            when metric = 'HYD_PRESS_P' then value
            else null
        end
    ) as hyd_press_p,
    avg(
        case
            when metric = 'GREASE' then value
            else null
        end
    ) as grease
FROM
    public.time_series_rt
WHERE
    timestamp_utc >= '2025-03-14'
    and power_unit = '200756'
GROUP BY
    power_unit,
    --timestamp_utc
    time_bucket('0.2 seconds', timestamp_utc) --ORDER BY
    --power_unit,
    --timestamp_utc
    --time_bucket('0.2 seconds', timestamp_utc)