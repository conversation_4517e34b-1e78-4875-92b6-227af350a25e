from collections import OrderedDict
from datetime import date, timedelta
from numbers import Number
from typing import List

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import pandas as pd
import phonenumbers
from dash import Input, Output, State, callback, html, no_update
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import (
    <PERSON><PERSON>,
    Customer,
    StructureVw,
    TimeZone,
    user_structure_maintenance_rel,
    user_structure_operators_rel,
    user_structure_remote_control_rel,
    user_structure_sales_rel,
)
from sqlalchemy import case, func, literal, select
from sqlalchemy.sql import text

from app import db, user_is_demo_customer, user_is_ijack_employee
from app.config import CUSTOMER_ID_IJACK_CORP, CUSTOMER_ID_IJACK_INC
from app.dashapp.metrics import BOOTSTRAP_BLUE_500
from app.dashapp.utils import (
    get_id_triggered,
    get_struct_obj_not_demo_customer,
    get_structure_obj,
    get_unit_type,
    log_function_caller,
)
from app.models.models import User
from app.utils.simple import utcnow_naive

STYLE_ALIGN_TEXT_CENTER = {"width": "100%", "text-align": "center"}


def get_alerts_enable_modal() -> dbc.Modal:
    """Get a modal for enabling/reactivating alerts"""
    return dbc.Modal(
        id="alerts_enable_modal",
        children=[
            dbc.ModalHeader(dbc.ModalTitle("Title", id="alerts_enable_modal_title")),
            dbc.ModalBody(
                [
                    dbc.Row(
                        [
                            dbc.Label(
                                id="alerts_enable_modal_body",
                                width="auto",
                            ),
                            dbc.Col(
                                dmc.DatePickerInput(
                                    id="alerts_enable_date_picker",
                                    minDate=date.today(),
                                    value=date.today() + timedelta(days=1),
                                    # initialMonth=date.today(),
                                    className="me-3",
                                    # dropdownType="modal", # default "popover"
                                    # So the datepicker is in front of the Bootstrap modal
                                    popoverProps={"zIndex": 10_000},
                                    # modalProps={"zIndex": 10_000},
                                    # dropdownPosition="bottom-start" fixes bug with Mantine DatePickerInput causing app to lock up!
                                    # https://community.plotly.com/t/dash-mantine-datepicker/75251/4
                                    # dropdownPosition="bottom-start",
                                ),
                                xs=6,
                                id="alerts_enable_date_picker_col",
                            ),
                        ],
                        class_name="g-2",
                        justify="between",
                    ),
                    dbc.Row(
                        [
                            dbc.Label("Time?", width="auto"),
                            dbc.Col(
                                dmc.TimeInput(
                                    id="alerts_enable_time_picker",
                                    # Just a simple HH:MM string for the time input
                                    value="07:00",
                                    withSeconds=False,
                                    className="me-3",
                                ),
                                xs=6,
                            ),
                        ],
                        id="alerts_enable_time_picker_row",
                        class_name="g-2 mt-0",
                        justify="between",
                    ),
                ]
            ),
            dbc.ModalFooter(
                [
                    dbc.Button(
                        "Cancel",
                        id="alerts_enable_modal_cancel_btn",
                        size="sm",
                        color="secondary",
                        n_clicks=0,
                    ),
                    dbc.Button(
                        "Confirm",
                        id="alerts_enable_modal_confirm_btn",
                        size="sm",
                        color="danger",
                        n_clicks=0,
                    ),
                ],
            ),
        ],
    )


def get_operators_row():
    """Row with operators for the unit"""
    return dbc.Row(
        style={"display": "none"},
        id="operators_row",
        justify="center",
        class_name="mt-4",
        children=dbc.Col(
            xs=12,
            xxl=10,
            children=[
                dbc.Card(
                    [
                        dbc.CardHeader(
                            dbc.Spinner(
                                [
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                "Operators for this unit",
                                                id="operators_card_header",
                                                width="auto",
                                            ),
                                            dbc.Col(
                                                dbc.Row(
                                                    [
                                                        # "EDIT" dropdown menu button
                                                        # NOTE: Must use long form (not dbc.) to get correct color/size
                                                        html.Div(
                                                            id="operators_edit_dropdown",
                                                            className="col-auto dropdown lh-1",
                                                            style={"display": "none"},
                                                            children=[
                                                                html.Button(
                                                                    [
                                                                        html.I(
                                                                            className="fa-solid fa-edit me-1"
                                                                        ),
                                                                        "EDIT",
                                                                    ],
                                                                    # All these classes are essential to get the right color and height
                                                                    className="lh-1 mb-1 btn btn-outline-primary btn-sm dropdown-toggle",
                                                                    type="button",
                                                                    # href="#",
                                                                    **{
                                                                        "data-bs-toggle": "dropdown",
                                                                        "aria-expanded": "false",
                                                                    },
                                                                ),
                                                                html.Ul(
                                                                    [
                                                                        html.Li(
                                                                            html.A(
                                                                                "Edit Users",
                                                                                id="edit_users_link",
                                                                                className="dropdown-item lh-1",
                                                                                target="_blank",
                                                                            )
                                                                        ),
                                                                        html.Li(
                                                                            html.A(
                                                                                "Edit Alerts Individually",
                                                                                id="alertees_edit_individual",
                                                                                className="dropdown-item lh-1",
                                                                                target="_blank",
                                                                            )
                                                                        ),
                                                                        html.Li(
                                                                            html.A(
                                                                                "Edit Alerts Bulk",
                                                                                id="alertees_edit_bulk",
                                                                                className="dropdown-item lh-1",
                                                                                target="_blank",
                                                                            )
                                                                        ),
                                                                        html.Li(
                                                                            html.A(
                                                                                "Edit Remote Control Permissions",
                                                                                id="edit_rc_permissions",
                                                                                className="dropdown-item lh-1",
                                                                                target="_blank",
                                                                            )
                                                                        ),
                                                                        html.Li(
                                                                            html.A(
                                                                                "Edit Preventative Maintenance People",
                                                                                id="edit_pm_people",
                                                                                className="dropdown-item lh-1",
                                                                                target="_blank",
                                                                            )
                                                                        ),
                                                                        html.Li(
                                                                            html.A(
                                                                                "Edit Sales Contacts",
                                                                                id="edit_sales_contacts",
                                                                                className="dropdown-item lh-1",
                                                                                target="_blank",
                                                                            )
                                                                        ),
                                                                    ],
                                                                    className="dropdown-menu",
                                                                ),
                                                            ],
                                                        ),
                                                        dbc.Col(
                                                            [
                                                                get_alerts_enable_modal(),
                                                                dbc.Button(
                                                                    [
                                                                        html.I(
                                                                            className="fa-solid fa-pause me-1",
                                                                            id="pause_alerts_btn_icon",
                                                                        ),
                                                                        html.Span(
                                                                            id="pause_alerts_btn_label"
                                                                        ),
                                                                    ],
                                                                    id="pause_alerts_btn",
                                                                    n_clicks=0,
                                                                    color="primary",
                                                                    outline=True,
                                                                    size="sm",
                                                                    class_name="mb-1 lh-1",
                                                                ),
                                                            ],
                                                            id="pause_alerts_btn_column",
                                                            width="auto",
                                                            style={"display": "none"},
                                                        ),
                                                        dbc.Col(
                                                            dbc.Button(
                                                                [
                                                                    html.I(
                                                                        className="fa-solid fa-clone me-1"
                                                                    ),
                                                                    "DUPLICATE ALERTS",
                                                                ],
                                                                id="alertees_duplicate_btn",
                                                                n_clicks=0,
                                                                color="primary",
                                                                outline=True,
                                                                size="sm",
                                                                class_name="mb-1 lh-1",
                                                                style={
                                                                    "display": "none"
                                                                },
                                                                # external_link=True,
                                                            ),
                                                            width="auto",
                                                        ),
                                                        dbc.Col(
                                                            dbc.Button(
                                                                [
                                                                    html.I(
                                                                        className="fa-solid fa-refresh me-1"
                                                                    ),
                                                                    "REFRESH",
                                                                ],
                                                                id="operators_refresh_btn",
                                                                n_clicks=0,
                                                                color="primary",
                                                                outline=True,
                                                                size="sm",
                                                                class_name="mb-1 lh-1",
                                                                # style={"display": "none"},
                                                                # external_link=True,
                                                            ),
                                                            width="auto",
                                                        ),
                                                    ],
                                                    # class_name="g-2 justify-content-end",
                                                    class_name="g-2",
                                                    justify="end",
                                                ),
                                                width="auto",
                                            ),
                                        ],
                                        justify="between",
                                    ),
                                ],
                                color="success",
                            ),
                        ),
                        dbc.CardBody(
                            dbc.Spinner(
                                html.Div(id="operators_card_body"),
                                color="success",
                                # type='grow'
                            ),
                            style={"margin-x": "0.25rem"},
                        ),
                    ],
                ),
            ],
        ),
    )


# Can't memoize this since it'll change if the user pauses his/her alerts
# @cache_memoize_if_prod(timeout=60)
def get_operators(power_unit_id: int, structure_id: int) -> pd.DataFrame:
    """
    Get all operators for a structure by querying alerts and structure relationship tables.

    Uses SQLAlchemy for better maintainability and type safety. Returns all users who have
    any relationship (alerts for power_unit_id OR any structure relationship for structure_id)
    with flags indicating which types of relationships each user has.

    Parameters:
    -----------
    power_unit_id : int
        The power unit ID to check for alert relationships
    structure_id : int
        The structure ID to check for structure relationships

    Returns:
    --------
    pd.DataFrame
        DataFrame containing user information and relationship flags
    """

    # Step 1: Create a CTE to get all relevant user IDs from any relationship type
    alerts_users = select(Alert.user_id.label("user_id")).where(
        Alert.power_unit_id == power_unit_id
    )

    remote_control_users = select(user_structure_remote_control_rel.c.user_id).where(
        user_structure_remote_control_rel.c.structure_id == structure_id
    )

    operators_users = select(user_structure_operators_rel.c.user_id).where(
        user_structure_operators_rel.c.structure_id == structure_id
    )

    maintenance_users = select(user_structure_maintenance_rel.c.user_id).where(
        user_structure_maintenance_rel.c.structure_id == structure_id
    )

    sales_users = select(user_structure_sales_rel.c.user_id).where(
        user_structure_sales_rel.c.structure_id == structure_id
    )

    # Union all user sources and make it distinct
    all_users_cte = alerts_users.union(
        remote_control_users, operators_users, maintenance_users, sales_users
    ).cte("all_users")

    # Step 2: Main query to get user details with relationship flags
    query = (
        select(
            # User identification and contact info
            func.concat_ws(" ", User.first_name, User.last_name).label("alertee"),
            User.id.label("user_id"),
            User.customer_id,
            Customer.customer,
            User.email,
            func.rtrim(User.phone).label("phone"),
            User.job_title,
            TimeZone.time_zone,
            User.sms_stop_all.label("sms_stop_all_bool"),
            case(
                (User.sms_stop_all.is_(True), literal("Stopped")), else_=literal("")
            ).label("sms_stop_all"),
            User.alerts_paused_until_utc,
            # Alert preferences (only available if user has alerts for this power unit)
            Alert.wants_phone,
            Alert.wants_sms,
            Alert.wants_email,
            # Relationship type flags
            case(
                (
                    user_structure_remote_control_rel.c.user_id.isnot(None),
                    literal(True),
                ),
                else_=literal(False),
            ).label("remote_control"),
            case(
                (user_structure_operators_rel.c.user_id.isnot(None), literal(True)),
                else_=literal(False),
            ).label("operators"),
            case(
                (user_structure_maintenance_rel.c.user_id.isnot(None), literal(True)),
                else_=literal(False),
            ).label("maint"),
            case(
                (user_structure_sales_rel.c.user_id.isnot(None), literal(True)),
                else_=literal(False),
            ).label("sales"),
        )
        .select_from(
            all_users_cte.join(User, all_users_cte.c.user_id == User.id)
            .outerjoin(Customer, User.customer_id == Customer.id)
            .outerjoin(TimeZone, User.time_zone_id == TimeZone.id)
            # Left join alerts to get alert preferences (may be null if user has no alerts)
            .outerjoin(
                Alert,
                (Alert.user_id == User.id) & (Alert.power_unit_id == power_unit_id),
            )
            # Left join each relationship table to check for relationships
            .outerjoin(
                user_structure_remote_control_rel,
                (user_structure_remote_control_rel.c.user_id == User.id)
                & (user_structure_remote_control_rel.c.structure_id == structure_id),
            )
            .outerjoin(
                user_structure_operators_rel,
                (user_structure_operators_rel.c.user_id == User.id)
                & (user_structure_operators_rel.c.structure_id == structure_id),
            )
            .outerjoin(
                user_structure_maintenance_rel,
                (user_structure_maintenance_rel.c.user_id == User.id)
                & (user_structure_maintenance_rel.c.structure_id == structure_id),
            )
            .outerjoin(
                user_structure_sales_rel,
                (user_structure_sales_rel.c.user_id == User.id)
                & (user_structure_sales_rel.c.structure_id == structure_id),
            )
        )
        .where(User.is_active.is_(True))
        .order_by(
            Customer.id,
            case(
                (user_structure_maintenance_rel.c.user_id.isnot(None), 1), else_=0
            ).desc(),
            case((user_structure_sales_rel.c.user_id.isnot(None), 1), else_=0).desc(),
            case(
                (user_structure_remote_control_rel.c.user_id.isnot(None), 1), else_=0
            ).desc(),
            case(
                (user_structure_operators_rel.c.user_id.isnot(None), 1), else_=0
            ).desc(),
            func.concat_ws(" ", User.first_name, User.last_name),
        )
    )

    # Execute query using SQLAlchemy session
    result = db.session.execute(query)

    # Convert result rows to list of dictionaries for DataFrame
    rows = [dict(row._mapping) for row in result.fetchall()]

    return pd.DataFrame(rows)


def make_email_link(email):
    """Change the email to a mailto: link"""
    return html.A(email, href=f"mailto:{email}", style={"color": BOOTSTRAP_BLUE_500})


def make_phone_link(row):
    """Change the phone to a tel: clickable link"""
    phone = row.phone
    if not phone:
        formatted = None
        href = None
    else:
        href = f"tel:{phone}"
        try:
            parsed = phonenumbers.parse(phone)
            formatted = phonenumbers.format_number(
                parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
            )
        except Exception:
            current_app.logger.exception("ERROR parsing or formatting phone number!")
            formatted = phone.strip()
    return html.A(formatted, href=href, style={"color": BOOTSTRAP_BLUE_500})


def alert_type_icon(row):
    """Show icons for the different alert types a user might have"""

    classes = []
    if not row.sms_stop_all_bool:
        if row.wants_phone:
            classes.append("fa-solid fa-phone me-2")
        if row.wants_sms:
            classes.append("fa-regular fa-commenting me-2")
    if row.wants_email:
        classes.append("fa-regular fa-envelope me-2")

    icons = [html.I(className=cl) for cl in classes]
    if icons:
        return html.Div(icons, style=STYLE_ALIGN_TEXT_CENTER)

    return ""


def remote_control_icon(remote_control: bool):
    """Add a checkmark icon if user has remote control permissions"""
    if remote_control:
        return html.I(className="fa-solid fa-check", style=STYLE_ALIGN_TEXT_CENTER)

    return ""


def show_maintenance_contacts_icon(maint: bool) -> str:
    """Show a tools icon if the user is a maintenance contact"""
    if maint:
        return html.I(className="fa-solid fa-wrench", style=STYLE_ALIGN_TEXT_CENTER)
    return ""


def show_sales_contacts_icon(sales: bool) -> str:
    """Show a handshake icon if the user is a sales contact"""
    if sales:
        return html.I(className="fa-solid fa-handshake", style=STYLE_ALIGN_TEXT_CENTER)
    return ""


def check_and_add_alertees(
    user_ids_list: List[int],
    power_unit_id_old: int,
    power_unit_id_new: int,
) -> bool:
    """Given a list of user IDs, check if they are already alertees and add them if not"""
    for new_alertee_id in user_ids_list:
        # Check whether there's already an existing alert for this user/gateway
        alert_already_exists = Alert.query.filter_by(
            user_id=new_alertee_id, power_unit_id=power_unit_id_new
        ).first()
        if alert_already_exists:
            continue

        # Create an alert using the default values from "models.py" class Alert.
        # We'll change its default values below
        new_alert = Alert(user_id=new_alertee_id, power_unit_id=power_unit_id_new)

        # Copy the alert settings from the existing alert for the same user at the old unit.
        # See if there's an existing alert for the unit whose alerts we're duplicating (the original unit)
        # This is the main objective, really, since we're duplicating existing operators. However, the user
        # has the option of adding additional users to the new unit, so there may be new users/alertees as well.
        existing_alert = Alert.query.filter_by(
            user_id=new_alertee_id, power_unit_id=power_unit_id_old
        ).first()
        if (
            existing_alert
            and hasattr(existing_alert, "power_unit_id")
            and existing_alert.power_unit_id
        ):
            # alert_copy = copy_object(existing_alert)

            # Make the new alert have the same attributes of the existing alert
            new_alert.wants_sms = existing_alert.wants_sms
            new_alert.wants_email = existing_alert.wants_email
            new_alert.wants_phone = existing_alert.wants_phone
            new_alert.wants_whatsapp = existing_alert.wants_whatsapp
            new_alert.wants_card_ml = existing_alert.wants_card_ml
            new_alert.wants_short_email = existing_alert.wants_short_email
            new_alert.wants_short_phone = existing_alert.wants_short_phone
            new_alert.wants_short_sms = existing_alert.wants_short_sms
            new_alert.hyd_temp = existing_alert.hyd_temp
            new_alert.mtr = existing_alert.mtr
            new_alert.spm = existing_alert.spm
            new_alert.warn1 = existing_alert.warn1
            new_alert.warn2 = existing_alert.warn2
            new_alert.stboxf = existing_alert.stboxf
            new_alert.suction = existing_alert.suction
            new_alert.change_dgp = existing_alert.change_dgp
            new_alert.change_hp_delta = existing_alert.change_hp_delta
            new_alert.change_hyd_temp = existing_alert.change_hyd_temp
            new_alert.change_suction = existing_alert.change_suction
            new_alert.discharge = existing_alert.discharge
            new_alert.heartbeat = existing_alert.heartbeat
            new_alert.online_hb = existing_alert.online_hb
            new_alert.hyd_filt_life = existing_alert.hyd_filt_life
            new_alert.hyd_oil_life = existing_alert.hyd_oil_life
            new_alert.hyd_oil_lvl = existing_alert.hyd_oil_lvl
            new_alert.chk_mtr_ovld = existing_alert.chk_mtr_ovld
            new_alert.pwr_fail = existing_alert.pwr_fail
            new_alert.soft_start_err = existing_alert.soft_start_err
            new_alert.grey_wire_err = existing_alert.grey_wire_err
            new_alert.ae011 = existing_alert.ae011

        db.session.add(new_alert)
        db.session.commit()

    return True


def insert_remote_controllers(
    user_ids_list: List[int],
    structure_id_new: int,
) -> bool:
    """
    Given a list of user IDs, check if they already
    have remote control permissions, and add them if not
    """
    for new_rc_user_id in user_ids_list:
        # # Check whether there's already an existing remote control permission for this user/structure_id_new
        # sql_insert = text(
        #     """
        #     select * from public.user_structure_remote_control_rel
        #     where user_id = :new_rc_user_id
        #         and structure_id = :structure_id_new
        #     """
        # ).bindparams(new_rc_user_id=new_rc_user_id, structure_id_new=structure_id_new)
        # rc_already_exists = db.session.execute(sql_insert).first()
        # if rc_already_exists:
        #     continue

        sql_insert = text(
            """
            insert into public.user_structure_remote_control_rel (user_id, structure_id)
            values (:new_rc_user_id, :structure_id_new)
            --If there's already an existing remote control permission
            --for this user/structure_id_new, do nothing
            on conflict (user_id, structure_id) do nothing
        """
        ).bindparams(new_rc_user_id=new_rc_user_id, structure_id_new=structure_id_new)
        db.session.execute(sql_insert)

    db.session.commit()
    return True


@callback(
    Output("operators_row", "style"),
    Output("operators_card_header", "children"),
    Output("operators_card_body", "children"),
    Output("service_request_operator_ids_store", "data"),
    Input("store_structure_id", "data"),
    Input("pause_alerts_btn_label", "children"),
    Input("operators_refresh_btn", "n_clicks"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def update_operators(
    store_structure_id_data,
    pause_alerts_btn_label_children,
    operators_refresh_btn_n_clicks,
    tab_uno_egas,
    store_unit_type_id,
):
    """Operators' data table"""
    log_function_caller()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    unit_type_lower, _ = get_unit_type(tab_uno_egas, store_unit_type_id)
    if not unit_type_lower:
        # TODO: what to do here? Vermilion guy had a problem here
        pass

    # Set the default values for the return variables

    # Identify the demo user for which to make fake locations
    is_demo: bool = user_is_demo_customer(user_id=user_id)
    if is_demo:
        title = "Operators for Unit"
    else:
        customer_first_word = structure_obj.customer.split()[0]
        customer_unit_info = f"{customer_first_word} {unit_type_lower.upper()} {structure_obj.power_unit_str} at {structure_obj.surface}"
        title = f"Operators for {customer_unit_info}"

    def return_variables(
        operators_row_style: dict = {},
        operators_card_header: str = "",
        operators_card_body=html.H3("No Operators to Display"),
        service_request_operator_ids_store: list = [],
    ):
        return (
            operators_row_style,
            operators_card_header,
            operators_card_body,
            service_request_operator_ids_store,
        )

    df = get_operators(structure_obj.power_unit_id, structure_obj.id)
    service_request_operator_ids_store = [row["user_id"] for _, row in df.iterrows()]

    if len(df.index) > 0:
        if is_demo:
            # 21 = demo customer so only show IJACK employees
            df = df[df["customer_id"] == CUSTOMER_ID_IJACK_INC]
        elif not user_is_ijack_employee(user_id=user_id):
            # If it's not an IJACK employee (1) looking at the page,
            # don't show the IJACK alertees.
            df = df[df["customer_id"] != CUSTOMER_ID_IJACK_INC]

    # After removing the IJACK alertees, check if there are any remaining alertees
    if len(df.index) == 0:
        return return_variables({}, title, html.H3("No Operators to Display"))

    # current_app.logger.debug("alertees df: \n%s", df.head())

    df["email"] = df["email"].transform(make_email_link)
    df["phone"] = df.apply(make_phone_link, axis=1)
    df["remote_control"] = df["remote_control"].transform(remote_control_icon)
    df["maint"] = df["maint"].transform(show_maintenance_contacts_icon)
    df["sales"] = df["sales"].transform(show_sales_contacts_icon)
    df["alerts"] = df.apply(alert_type_icon, axis=1)

    # Order matters since we're using this to determine which columns to show
    rename_map = OrderedDict(
        alertee="Name",
        customer="Customer",
        phone="Phone",
        email="Email",
        job_title="Title",
        remote_control="Remote Control",
        maint="Prev Maint",
        sales="Sales",
        alerts="Alerts",
    )

    # No sense having the last two columns if no-one's alerts are stopped or paused.
    # Need a timezone-naive datetime object for comparison.
    current_datetime = utcnow_naive()
    df_some_alerts_paused = df.loc[df["alerts_paused_until_utc"] > current_datetime]
    if len(df_some_alerts_paused) > 0:
        rename_map.update({"alerts_paused_until_utc": "Alerts Paused Until"})
        fmt = "%a, %b %d"
        df["alerts_paused_until_utc"] = df["alerts_paused_until_utc"].dt.strftime(fmt)

    if len(df.loc[df["sms_stop_all_bool"] == True]) > 0:  # noqa: E712
        rename_map.update({"sms_stop_all": "SMS/Phone Stopped"})

    df = df[rename_map.keys()].rename(columns=rename_map)

    card_body = dbc.Table.from_dataframe(
        df,
        striped=True,
        bordered=True,
        hover=True,
        class_name="mt-1",
        responsive=True,
    )

    return return_variables(
        {},
        title,
        card_body,
        service_request_operator_ids_store,
    )


@callback(
    Output("modal_duplicate_alertees_is_open_1", "data"),
    Output("duplicate_alertees_unit_select", "options"),
    Output("duplicate_alertees_users_select", "options"),
    Output("duplicate_alertees_users_select", "value"),
    Output("duplicate_rc_users_select", "options"),
    Output("duplicate_rc_users_select", "value"),
    Input("alertees_duplicate_btn", "n_clicks"),
    State("store_structure_id", "data"),
    # State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def duplicate_alertees_get_options(
    alertees_duplicate_btn_n_clicks,
    store_structure_id_data,
    # store_unit_type_id,
):
    """Duplicate alertees to a new unit"""
    if not alertees_duplicate_btn_n_clicks:
        raise PreventUpdate()

    modal_duplicate_alertees_is_open_1_data = True  # open the modal

    structure_obj: StructureVw = get_struct_obj_not_demo_customer(
        structure_id=store_structure_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()

    # New units will have to be from the same customer ID as the current unit
    customer_id = structure_obj.customer_id
    power_unit_id = structure_obj.power_unit_id

    # Get the new unit options, owned by the same customer, excluding the current power unit
    structures = (
        db.session.query(StructureVw)
        .filter(
            # Get all structures that are not the current structure and are owned by the same customer
            StructureVw.id != store_structure_id_data,
            StructureVw.customer_id == customer_id,
        )
        .all()
    )
    duplicate_alertees_unit_select_options = [
        {
            "label": f"{struct.power_unit_str} - {struct.model_unit_type} at {struct.surface}",
            "value": struct.id,
        }
        for struct in structures
    ]

    # Get the multi-select user alertees options
    acceptable_customer_ids: tuple = (customer_id,)
    is_ijack: bool = user_is_ijack_employee(user_id=getattr(current_user, "id", None))
    if is_ijack:
        acceptable_customer_ids: tuple = (
            customer_id,
            CUSTOMER_ID_IJACK_INC,
            CUSTOMER_ID_IJACK_CORP,
        )
    users = User.query.filter(
        User.customers_rel.any(Customer.id.in_(acceptable_customer_ids))
    ).all()

    duplicate_alertees_users_select_options = [
        {
            "label": f"{user.first_name} {user.last_name} <{user.email}>",
            "value": user.id,
        }
        for user in users
    ]

    # Get the current alertees so we can duplicate them easily
    if is_ijack:
        current_alertees = (
            User.query.join(Alert).filter(Alert.power_unit_id == power_unit_id).all()
        )
        current_remote_control_users = (
            User.query.join(user_structure_remote_control_rel)
            .filter(
                user_structure_remote_control_rel.c.structure_id == structure_obj.id
            )
            .all()
        )
    else:
        current_alertees = (
            User.query.join(Alert)
            .filter(
                Alert.power_unit_id == power_unit_id,
                User.customers_rel.any(Customer.id == customer_id),
            )
            .all()
        )
        current_remote_control_users = (
            User.query.join(user_structure_remote_control_rel)
            .filter(
                user_structure_remote_control_rel.c.structure_id == structure_obj.id,
                User.customers_rel.any(Customer.id == customer_id),
            )
            .all()
        )
    duplicate_alertees_users_select_value = [user.id for user in current_alertees]
    duplicate_rc_users_select_value = [user.id for user in current_remote_control_users]

    return (
        modal_duplicate_alertees_is_open_1_data,
        duplicate_alertees_unit_select_options,
        duplicate_alertees_users_select_options,
        duplicate_alertees_users_select_value,
        # Same options for the remote control users
        duplicate_alertees_users_select_options,
        duplicate_rc_users_select_value,
    )


@callback(
    Output("modal_duplicate_alertees_is_open_2", "data"),
    Output("modal_message", "is_open", allow_duplicate=True),
    Output("modal_message_header", "children", allow_duplicate=True),
    Output("modal_message_body", "children", allow_duplicate=True),
    Input("btn_duplicate_alertees_cancel", "n_clicks"),
    Input("btn_duplicate_alertees_confirm", "n_clicks"),
    State("duplicate_alertees_unit_select", "value"),
    State("duplicate_alertees_users_select", "value"),
    State("duplicate_rc_users_select", "value"),
    State("store_structure_id", "data"),
    prevent_initial_call=True,
)
def duplicate_alertees_confirm_or_cancel(
    btn_duplicate_alertees_cancel_n_clicks,
    btn_duplicate_alertees_confirm_n_clicks,
    duplicate_alertees_unit_select_value,
    duplicate_alertees_users_select_value,
    duplicate_rc_users_select_value,
    store_structure_id_data,
):
    """Actually duplicate the alerts to the new unit, after the user presses 'confirm' or 'cancel' in the modal"""

    modal_duplicate_alertees_is_open_2_data = no_update
    modal_message_is_open = no_update
    modal_message_header = no_update
    modal_message_body = no_update

    id_triggered: str = get_id_triggered()
    # if id_triggered == "btn_duplicate_alertees_cancel.n_clicks" and btn_duplicate_alertees_cancel_n_clicks:
    if (
        id_triggered != "btn_duplicate_alertees_confirm.n_clicks"
        or not btn_duplicate_alertees_confirm_n_clicks
    ):
        # Just close the modal
        modal_duplicate_alertees_is_open_2_data = False
        return (
            modal_duplicate_alertees_is_open_2_data,
            modal_message_is_open,
            modal_message_header,
            modal_message_body,
        )

    is_alertees_good = (
        isinstance(duplicate_alertees_users_select_value, list)
        and len(duplicate_alertees_users_select_value) > 0
    )
    is_rc_users_good = (
        isinstance(duplicate_rc_users_select_value, list)
        and len(duplicate_rc_users_select_value) > 0
    )
    if (
        not isinstance(duplicate_alertees_unit_select_value, Number)
        # If both of the following are bad... Got to have at least one of the following
        or (not is_alertees_good and not is_rc_users_good)
    ):
        raise PreventUpdate()

    # Get the power_unit_id of the unit whose alerts we're duplicating
    structure_obj_old: StructureVw = get_struct_obj_not_demo_customer(
        structure_id=store_structure_id_data
    )
    if structure_obj_old is None:
        raise PreventUpdate()
    power_unit_id_old = structure_obj_old.power_unit_id

    structure_obj_new: StructureVw = get_struct_obj_not_demo_customer(
        structure_id=duplicate_alertees_unit_select_value
    )
    power_unit_id_new = structure_obj_new.power_unit_id

    # If we've made it this far, we must duplicate the alertees for the new unit
    try:
        check_and_add_alertees(
            duplicate_alertees_users_select_value,
            power_unit_id_old,
            power_unit_id_new,
        )
        insert_remote_controllers(
            duplicate_rc_users_select_value,
            structure_obj_new.id,
        )
    except Exception:
        msg = "Error duplicating that unit's alerts! Please email Sean McCarthy at IJACK (<EMAIL>) with your request"
        current_app.logger.exception(msg)

        modal_duplicate_alertees_is_open_2_data = False
        modal_message_is_open = True
        modal_message_header = msg
        modal_message_body = msg
        return (
            modal_duplicate_alertees_is_open_2_data,
            modal_message_is_open,
            modal_message_header,
            modal_message_body,
        )

    modal_duplicate_alertees_is_open_2_data = False
    modal_message_is_open = True
    modal_message_header = "Success!"

    if (
        hasattr(structure_obj_old, "power_unit")
        and hasattr(structure_obj_old, "model")
        and hasattr(structure_obj_old, "surface")
    ):
        old_unit_str = f"Power unit {structure_obj_old.power_unit} - {structure_obj_old.model} at {structure_obj_old.surface}"
    else:
        old_unit_str = "That unit"

    if (
        hasattr(structure_obj_new, "power_unit")
        and hasattr(structure_obj_new, "model")
        and hasattr(structure_obj_new, "surface")
    ):
        new_unit_str = f"power unit {structure_obj_new.power_unit} - {structure_obj_new.model} at {structure_obj_new.surface}"
    else:
        new_unit_str = "the new unit"

    modal_message_body = (
        f"{old_unit_str}'s alerts have been duplicated to {new_unit_str}"
    )

    return (
        modal_duplicate_alertees_is_open_2_data,
        modal_message_is_open,
        modal_message_header,
        modal_message_body,
    )
