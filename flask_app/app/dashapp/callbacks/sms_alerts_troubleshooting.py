import dash_bootstrap_components as dbc
import phonenumbers
from dash import Input, Output, State, callback, callback_context, dcc, html
from dash.exceptions import PreventUpdate
from twilio.base.exceptions import TwilioRestException

from app.auth.forms import validate_phone
from app.utils.complex import send_twilio_sms


def sms_alerts_layout():
    """
    Main layout for SMS alerts troubleshooting guide
    """
    return dbc.Container(
        class_name="pt-3",
        children=[
            # Store for selected country
            dcc.Store(id="selected_country", data="Canada"),
            # Interval for allowing repeated test SMS button clicks
            dcc.Interval(
                id="test_sms_interval",
                interval=1_000,
                disabled=True,
                n_intervals=0,
                max_intervals=61,
            ),
            # Header section
            dbc.Row(
                class_name="mb-4",
                children=[
                    dbc.Col(
                        xs=12,
                        class_name="text-center",
                        children=[
                            html.H1(
                                "SMS Alerts Troubleshooting Guide", className="mb-3"
                            ),
                            html.P(
                                "Having trouble receiving SMS alerts from IJACK? Follow these steps to ensure our messages reach your inbox.",
                                className="lead",
                            ),
                            # Country selection buttons
                            html.Div(
                                className="my-3",
                                children=[
                                    dbc.ButtonGroup(
                                        [
                                            dbc.<PERSON><PERSON>(
                                                "Canada",
                                                id="btn_canada",
                                                color="primary",
                                                className="me-2",
                                                n_clicks=0,
                                            ),
                                            dbc.Button(
                                                "US",
                                                id="btn_united_states",
                                                color="primary",
                                                outline=True,
                                                n_clicks=0,
                                            ),
                                        ]
                                    )
                                ],
                            ),
                            # Phone number alert
                            dbc.Alert(
                                id="phone_number_container",
                                color="primary",
                            ),
                        ],
                    )
                ],
            ),
            # Device selection buttons
            dbc.Row(
                class_name="mb-4 justify-content-center",
                children=[
                    dbc.Col(
                        xs=12,
                        md=8,
                        class_name="d-flex justify-content-center gap-3",
                        children=[
                            dbc.Button(
                                [
                                    html.I(
                                        # className="bi bi-apple mb-2 d-block",
                                        className="fa-brands fa-apple mb-2 d-block",
                                        style={"font-size": "2rem"},
                                    ),
                                    "iPhone Instructions",
                                ],
                                id="btn_iphone",
                                color="primary",
                                size="lg",
                                class_name="px-4",
                            ),
                            dbc.Button(
                                [
                                    html.I(
                                        # className="bi bi-android2 mb-2 d-block",
                                        className="fa-brands fa-android mb-2 d-block",
                                        style={"font-size": "2rem"},
                                    ),
                                    "Android Instructions",
                                ],
                                id="btn_android",
                                color="success",
                                size="lg",
                                class_name="px-4",
                            ),
                        ],
                    )
                ],
            ),
            # Device instructions sections
            html.Div(
                id="iphone_instructions",
                style={"display": "block"},
                children=[
                    dbc.Card(
                        class_name="mb-4",
                        children=[
                            dbc.CardHeader(
                                [
                                    html.I(
                                        # className="bi bi-apple me-2"
                                        className="fa-brands fa-apple me-2 text-white"
                                    ),
                                    html.H2(
                                        "iPhone Instructions",
                                        className="h4 mb-0 text-white",
                                    ),
                                ],
                                class_name="bg-primary text-white d-flex align-items-center",
                            ),
                            dbc.CardBody(
                                [
                                    html.H3(
                                        "Method 1: Add Number to Contacts",
                                        className="h5",
                                    ),
                                    dbc.ListGroup(
                                        [
                                            dbc.ListGroupItem(
                                                [
                                                    "Open the message from IJACK alerts # ",
                                                    html.Span(
                                                        id="iphone_formatted_phone",
                                                        className="formatted-phone",
                                                    ),
                                                ]
                                            ),
                                            dbc.ListGroupItem(
                                                "Tap the number/contact at the top of the conversation"
                                            ),
                                            dbc.ListGroupItem("Select 'info'"),
                                            dbc.ListGroupItem(
                                                "Choose 'Create New Contact' or 'Add to Existing Contact'"
                                            ),
                                        ],
                                        className="list-group-flush mb-4",
                                    ),
                                    # ... Additional iPhone instructions ...
                                ]
                            ),
                        ],
                    )
                ],
            ),
            html.Div(
                id="android_instructions",
                style={"display": "none"},
                children=[
                    dbc.Card(
                        class_name="mb-4",
                        children=[
                            dbc.CardHeader(
                                [
                                    html.I(
                                        # className="bi bi-android2 me-2"
                                        className="fa-brands fa-android me-2 text-white"
                                    ),
                                    html.H2(
                                        "Android Instructions",
                                        className="h4 mb-0 text-white",
                                    ),
                                ],
                                class_name="bg-success d-flex align-items-center",
                                style={"color": "white !important"},  # Force white text
                            ),
                            dbc.CardBody(
                                [
                                    html.H3("Basic Steps", className="h5"),
                                    dbc.ListGroup(
                                        [
                                            dbc.ListGroupItem(
                                                "Open your default Messages app"
                                            ),
                                            dbc.ListGroupItem(
                                                [
                                                    "Find a message from IJACK alerts # ",
                                                    html.Span(
                                                        id="android_formatted_phone",
                                                        className="formatted-phone",
                                                    ),
                                                ]
                                            ),
                                            dbc.ListGroupItem(
                                                "Tap and hold the conversation thread"
                                            ),
                                            dbc.ListGroupItem(
                                                "Look for the three-dot menu (⋮) or 'More' option"
                                            ),
                                            dbc.ListGroupItem(
                                                "Select 'Not spam' or 'Mark as not spam'"
                                            ),
                                        ],
                                        className="list-group-flush mb-4",
                                    ),
                                    # ... Additional Android instructions ...
                                    dbc.Alert(
                                        [
                                            html.I(
                                                # className="bi bi-info-circle"
                                                className="fa-solid fa-info-circle"
                                            ),
                                            " Note: The exact steps might vary depending on your Android version and phone manufacturer.",
                                        ],
                                        color="info",
                                        class_name="mt-4",
                                    ),
                                ]
                            ),
                        ],
                    )
                ],
            ),
            # Send yourself a test SMS alert message
            dbc.Row(
                class_name="mb-4 justify-content-center",
                children=[
                    dbc.Col(
                        xs=12,
                        md=10,
                        children=[
                            dbc.Card(
                                # class_name="text-center",
                                children=[
                                    dbc.CardHeader(
                                        [
                                            html.I(
                                                className="fa-solid fa-comment-sms me-2 text-white"
                                            ),
                                            html.H2(
                                                "Send Yourself a Test SMS Alert",
                                                className="h4 mb-0 text-white",
                                            ),
                                        ],
                                        class_name="bg-primary d-flex align-items-center",
                                        style={
                                            "color": "white !important"
                                        },  # Force white text
                                    ),
                                    dbc.CardBody(
                                        [
                                            html.P(
                                                "Tap the button below to send yourself a test SMS alert message from IJACK. "
                                                "This will ensure you receive our messages.",
                                                className="mb-3",
                                            ),
                                            html.Div(
                                                [
                                                    dbc.Label(
                                                        "Your Phone Number",
                                                        html_for="phone_input",
                                                        class_name="mb-1",
                                                    ),
                                                    dbc.Input(
                                                        type="tel",
                                                        id="phone_input",
                                                        valid=None,
                                                        required=True,
                                                        persistence=True,
                                                        placeholder="Enter your phone number",
                                                    ),
                                                    dcc.Store(
                                                        id="sms_trouble_register_phone_store",
                                                        data=None,
                                                    ),
                                                    dbc.FormText(
                                                        "We'll never share your phone number",
                                                        id="phone_form_text",
                                                        color="secondary",
                                                        class_name="mt-1",
                                                    ),
                                                ],
                                                className="mb-3",
                                            ),
                                            dbc.Row(
                                                children=[
                                                    dbc.Col(
                                                        width="auto",
                                                        children=[
                                                            dbc.Button(
                                                                children=[
                                                                    html.I(
                                                                        className="fa-solid fa-paper-plane me-2 text-white"
                                                                    ),
                                                                    html.Span(
                                                                        "Send Test SMS Alert",
                                                                        id="send_sms_btn_text",
                                                                    ),
                                                                ],
                                                                id="btn_send_test_sms",
                                                                color="primary",
                                                                size="lg",
                                                                className="px-4",
                                                            ),
                                                        ],
                                                    ),
                                                    dbc.Col(
                                                        width="auto",
                                                        id="test_sms_alert_cdn_us",
                                                        class_name="d-flex align-items-center",
                                                    ),
                                                ],
                                            ),
                                            html.Div(
                                                id="test_sms_alert_message",
                                                className="mt-3",
                                            ),
                                        ],
                                    ),
                                ],
                            )
                        ],
                    )
                ],
            ),
            # Need help section
            dbc.Row(
                class_name="mt-4",
                children=[
                    dbc.Col(
                        xs=12,
                        children=[
                            dbc.Card(
                                [
                                    dbc.CardHeader(
                                        [
                                            html.I(
                                                # className="bi bi-question-circle me-2"
                                                className="fa-solid fa-question-circle me-2 text-white"
                                            ),
                                            html.H2(
                                                "Still Need Help?",
                                                className="h4 mb-0 text-white",
                                            ),
                                        ],
                                        class_name="bg-dark d-flex align-items-center",
                                        style={
                                            "color": "white !important"
                                        },  # Force white text
                                    ),
                                    dbc.CardBody(
                                        [
                                            html.P(
                                                [
                                                    "If you're still having trouble receiving our SMS alerts, please ",
                                                    html.A(
                                                        "contact us", href="/contact/"
                                                    ),
                                                    " for assistance.",
                                                ]
                                            )
                                        ]
                                    ),
                                ]
                            )
                        ],
                    )
                ],
            ),
        ],
    )


# Phone numbers dictionary
PHONE_NUMBERS = {
    "Canada": {"area": "306", "prefix": "988", "suffix": "4140"},
    "US": {"area": "430", "prefix": "245", "suffix": "5554"},
}


@callback(
    Output("btn_canada", "outline"),
    Output("btn_united_states", "outline"),
    Output("selected_country", "data"),
    Input("btn_canada", "n_clicks"),
    Input("btn_united_states", "n_clicks"),
    State("selected_country", "data"),
    prevent_initial_call=True,
)
def update_country_selection(canada_clicks, us_clicks, current_country):
    """Handle country button clicks"""
    ctx = callback_context
    if not ctx.triggered:
        return False, True, "Canada"

    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    if button_id == "btn_canada":
        return False, True, "Canada"
    elif button_id == "btn_united_states":
        return True, False, "US"

    return False, True, "Canada"


@callback(
    Output("phone_number_container", "children"),
    Output("iphone_formatted_phone", "children"),
    Output("android_formatted_phone", "children"),
    Output("test_sms_alert_cdn_us", "children"),
    Input("selected_country", "data"),
    prevent_initial_call=False,
)
def update_phone_numbers(country):
    """Update all phone number displays based on selected country"""
    phone: dict = PHONE_NUMBERS[country]
    formatted: str = f"({phone['area']}) {phone['prefix']}-{phone['suffix']}"
    html_w_flag = html.Span(
        [
            formatted,
            html.I(
                className="fi fi-ca ms-2" if country == "Canada" else "fi fi-us ms-2",
            ),
        ]
    )
    full = f"{phone['area']}{phone['prefix']}{phone['suffix']}"

    phone_link = html.Div(
        [
            html.Span(
                [
                    html.Strong(f"{country} IJACK Alerts Phone Number: "),
                    html.A(formatted, href=f"tel:{full}", className="text-primary"),
                ],
                className="d-block",
            ),
            html.Br(),
            html.Span(
                [
                    "Tap the number above and text ",
                    html.Strong("'START'"),
                    " to IJACK alerts, to ensure IJACK is allowed to send you SMS alerts.",
                ],
                className="text-muted",
            ),
        ]
    )

    return phone_link, html_w_flag, html_w_flag, html.Span(["from ", html_w_flag])


@callback(
    Output("iphone_instructions", "style"),
    Output("android_instructions", "style"),
    Input("btn_iphone", "n_clicks"),
    Input("btn_android", "n_clicks"),
    prevent_initial_call=True,
)
def toggle_device_instructions(iphone_clicks, android_clicks):
    """Show/hide device instructions based on button clicks"""
    ctx = callback_context
    if not ctx.triggered:
        return {"display": "block"}, {"display": "none"}

    button_id = ctx.triggered[0]["prop_id"].split(".")[0]
    if button_id == "btn_iphone":
        return {"display": "block"}, {"display": "none"}
    elif button_id == "btn_android":
        return {"display": "none"}, {"display": "block"}

    return {"display": "block"}, {"display": "none"}


@callback(
    Output("phone_input", "valid"),
    Output("phone_input", "invalid"),
    Input("phone_input", "value"),
    prevent_initial_call=True,
)
def validate_phone_input(phone_value):
    """Validate phone number input"""
    try:
        validate_phone(form=None, field=phone_value, must_be_unique=False)
    except Exception:
        return False, True
    return True, False


@callback(
    Output("btn_send_test_sms", "disabled"),
    # Output("get_code_in_sms_btn", "disabled"),
    Output("send_sms_btn_text", "children"),
    # Output("get_code_in_sms_btn_text", "children"),
    Output("test_sms_interval", "disabled", allow_duplicate=True),
    Input("test_sms_interval", "n_intervals"),
    prevent_initial_call=True,
)
def enable_get_code_btn(test_sms_interval_n_intervals):
    """Enable the 'Get code in email' button after X seconds"""
    if not test_sms_interval_n_intervals:
        raise PreventUpdate()

    msg: str = "Send Test SMS Alert"
    is_disabled: bool = False
    if test_sms_interval_n_intervals < 30:
        is_disabled = True
        seconds_remaining = 30 - test_sms_interval_n_intervals
        msg = f"Try again in {seconds_remaining}s"

    return is_disabled, msg, not is_disabled


@callback(
    Output("test_sms_alert_message", "children"),
    Output("test_sms_interval", "disabled", allow_duplicate=True),
    Output("test_sms_interval", "n_intervals"),
    Input("btn_send_test_sms", "n_clicks"),
    State("selected_country", "data"),
    State("phone_input", "value"),
    prevent_initial_call=True,
)
def send_test_sms_alert(n_clicks, country, phone_value):
    """Send a test SMS alert message"""

    def return_vars(
        test_sms_alert_message_children: str | dbc.Alert | list,
        test_sms_interval_disabled: bool = True,
        test_sms_interval_n_intervals: int = 0,
    ):
        """Default return values"""
        return (
            test_sms_alert_message_children,
            test_sms_interval_disabled,
            test_sms_interval_n_intervals,
        )

    if n_clicks:
        try:
            phone_parsed = validate_phone(
                form=None, field=phone_value, must_be_unique=False
            )
            phone_e164 = phonenumbers.format_number(
                phone_parsed, phonenumbers.PhoneNumberFormat.E164
            )
            phone_international = phonenumbers.format_number(
                phone_parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
            )
        except Exception:
            return return_vars(
                test_sms_alert_message_children=dbc.Alert(
                    "Please enter a valid phone number", color="danger"
                ),
                test_sms_interval_disabled=True,
                test_sms_interval_n_intervals=0,
            )

        try:
            send_twilio_sms(
                sms_phone_list=[phone_e164],
                body="This is a test SMS alert message from IJACK. If you received this message, you're all set to receive our alerts! 🚀",
                is_us_phone=country == "US",
            )
        except TwilioRestException as err:
            return return_vars(
                test_sms_alert_message_children=dbc.Alert(
                    f"Error sending SMS alert: {err.msg}", color="danger"
                ),
                test_sms_interval_disabled=True,
                test_sms_interval_n_intervals=0,
            )

        return return_vars(
            test_sms_alert_message_children=dbc.Alert(
                f"Test SMS alert message sent to phone number {phone_international}! Check your phone for the message. It might be in your 'junk' folder!",
                color="success",
            ),
            test_sms_interval_disabled=False,
            test_sms_interval_n_intervals=0,
        )

    raise PreventUpdate
