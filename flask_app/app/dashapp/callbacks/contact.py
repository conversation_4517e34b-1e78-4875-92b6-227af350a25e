import os
from typing import List

import dash_bootstrap_components as dbc
import phonenumbers
from dash import (
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
)
from dash.exceptions import PreventUpdate
from flask_login import current_user
from gcld3 import NNetLanguageIdentifier
from shared.models.models import ContactForm

from app import db
from app.auth.forms import validate_email, validate_phone
from app.config import (
    UNIT_TYPE_ID_BOOST,
    UNIT_TYPE_ID_DGAS,
    UNIT_TYPE_ID_EGAS,
    UNIT_TYPE_ID_UNO,
    UNIT_TYPE_ID_UNOGAS,
    UNIT_TYPE_ID_VRU,
    UNIT_TYPE_ID_XFER,
)
from app.dashapp.callbacks.service_request import submit_service_request
from app.dashapp.utils import (
    get_all_structures,
    get_db_options,
    get_id_triggered,
    recaptcha_is_good,
    send_email,
)


def contact_layout():
    """Get the Dash layout for the contact page."""
    return dbc.Container(
        dbc.Row(
            justify="center",
            children=dbc.Col(
                lg=10,
                xl=8,
                # xxl=6,
                # fluid=True,
                children=[
                    # Hidden signal value, which starts the page-load callbacks
                    html.Div(id="hidden_signal_contact", style={"display": "none"}),
                    # Store something neeeded for dynamic JavaScript (e.g. Google RECAPTCHA site key)
                    dcc.Store(
                        id="store_site_key_action_contact", storage_type="memory"
                    ),
                    # Need a dummy place for an Output() to nowhere
                    dcc.Store(id="store_nothing_contact", storage_type="memory"),
                    # Store the reCAPTCHA response so we can assess it in the callback
                    dcc.Store(
                        id="store_recaptcha_response_contact", storage_type="memory"
                    ),
                    # dbc.Row(
                    #     dbc.Col(
                    #         [
                    #             html.H1("Contact IJACK"),
                    #             html.P(
                    #                 "IJACK is passionate about CHANGING THE LANDSCAPE of the oil and gas industry"
                    #                 + " by introducing innovative products that offer enhanced efficiencies."
                    #                 + " To learn more about how our products and services can add efficiencies to your business,"
                    #                 + " please call the numbers below or simply drop us a line."
                    #             ),
                    #         ]
                    #     )
                    # ),
                    dbc.Card(
                        class_name="mt-3",
                        children=[
                            dbc.CardHeader(
                                "Contact IJACK",
                            ),
                            dbc.CardBody(
                                [
                                    dbc.Row(
                                        dbc.Col(
                                            html.P(
                                                "IJACK is passionate about CHANGING THE LANDSCAPE of the oil and gas industry"
                                                + " by introducing innovative products that offer enhanced efficiencies."
                                                + " To learn more about how our products and services can add efficiencies to your business,"
                                                + " please call the numbers below or simply fill out the contact form."
                                            ),
                                        ),
                                        class_name="mb-4",
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "First Name",
                                                        html_for="contact_first_name",
                                                        class_name="mb-1",
                                                    ),
                                                    dbc.Input(
                                                        type="text",
                                                        id="contact_first_name",
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Last Name",
                                                        html_for="contact_last_name",
                                                        class_name="mb-1",
                                                    ),
                                                    dbc.Input(
                                                        type="text",
                                                        id="contact_last_name",
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                        ],
                                        class_name="mb-3",
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Email",
                                                        html_for="contact_email",
                                                        class_name="mb-1",
                                                    ),
                                                    dbc.Input(
                                                        type="email",
                                                        id="contact_email",
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Phone",
                                                        html_for="contact_phone",
                                                        class_name="mb-1",
                                                    ),
                                                    # So we can trigger the initial phone number if the user is logged in
                                                    dcc.Store(id="trigger_init_phone"),
                                                    dbc.Input(
                                                        type="tel",
                                                        id="contact_phone",
                                                        persistence=True,
                                                    ),
                                                ]
                                            ),
                                        ],
                                        class_name="mb-3",
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Message",
                                                        html_for="contact_message",
                                                        class_name="mb-1",
                                                    ),
                                                    dbc.Textarea(
                                                        id="contact_message",
                                                        persistence=True,
                                                        # class_name="mb-3",
                                                        placeholder="Enter your message here",
                                                    ),
                                                ]
                                            ),
                                        ],
                                        class_name="mb-4",
                                    ),
                                    dbc.Row(
                                        justify="center",
                                        class_name="mb-3",
                                        children=dbc.Col(
                                            xs=12,
                                            lg=11,
                                            children=dbc.Card(
                                                # class_name="m-4",
                                                children=[
                                                    dbc.CardHeader(
                                                        "Optional Service Request Details (not required for sales inquiries)",
                                                    ),
                                                    dbc.CardBody(
                                                        [
                                                            dbc.Row(
                                                                dbc.Col(
                                                                    [
                                                                        # dropdown with list of all unit types, for filtering the list of units
                                                                        html.Label(
                                                                            "Select a unit type to filter the list of units"
                                                                        ),
                                                                        dcc.Dropdown(
                                                                            id="contact_unit_type_select",
                                                                            options=[
                                                                                {
                                                                                    "label": "No Unit Type Selected",
                                                                                    "value": "0",
                                                                                }
                                                                            ],
                                                                            value="0",
                                                                            persistence=True,
                                                                        ),
                                                                        dbc.FormText(
                                                                            "",
                                                                            id="contact_unit_type_select_ft",
                                                                            color="danger",
                                                                        ),
                                                                    ],
                                                                ),
                                                                class_name="mb-3",
                                                            ),
                                                            dbc.Row(
                                                                dbc.Col(
                                                                    [
                                                                        # dropdown with list of all structures/power units
                                                                        html.Label(
                                                                            "Find your unit to submit a unit-specific service request"
                                                                        ),
                                                                        # For storing and obfuscating the list of units
                                                                        dcc.Store(
                                                                            id="contact_structures_select_store"
                                                                        ),
                                                                        dcc.Dropdown(
                                                                            id="contact_structures_select",
                                                                            options=[
                                                                                {
                                                                                    "label": "No Unit Selected",
                                                                                    "value": "0",
                                                                                }
                                                                            ],
                                                                            value="0",
                                                                            persistence=True,
                                                                            placeholder="e.g. 20xxxx or 19xxxx",
                                                                        ),
                                                                        dbc.FormText(
                                                                            "",
                                                                            id="contact_structures_select_ft",
                                                                            color="danger",
                                                                        ),
                                                                    ],
                                                                ),
                                                                class_name="mb-3",
                                                            ),
                                                            dbc.Row(
                                                                dbc.Col(
                                                                    [
                                                                        # dropdown with list of all structures/power units
                                                                        html.Label(
                                                                            "Service Type Requested"
                                                                        ),
                                                                        dcc.Dropdown(
                                                                            id="contact_service_type_select",
                                                                            options=[
                                                                                {
                                                                                    "label": "No Service Type Selected",
                                                                                    "value": "0",
                                                                                }
                                                                            ],
                                                                            value="0",
                                                                            persistence=True,
                                                                        ),
                                                                        dbc.FormText(
                                                                            "",
                                                                            id="contact_service_type_select_ft",
                                                                            color="danger",
                                                                        ),
                                                                    ],
                                                                ),
                                                                class_name="mb-3",
                                                            ),
                                                        ]
                                                    ),
                                                ],
                                            ),
                                        ),
                                    ),
                                    dbc.Row(
                                        dbc.Col(
                                            dbc.Button(
                                                "Submit",
                                                id="contact_submit_btn",
                                                color="dark",
                                                class_name="mr-1",
                                            ),
                                        ),
                                        class_name="mb-1",
                                    ),
                                    dbc.Row(
                                        dbc.Col(
                                            dbc.FormText(
                                                id="contact_submit_status",
                                            )
                                        ),
                                        class_name="mb-1",
                                    ),
                                ],
                            ),
                        ],
                    ),
                    # Hide phone numbers and emails from bots, to prevent spam
                    dbc.Card(
                        class_name="mt-4",
                        children=[
                            dbc.CardHeader(
                                "Main Contacts",
                            ),
                            dbc.CardBody(
                                dbc.Row(
                                    id="contact_main_contacts",
                                    class_name="mb-2",
                                )
                            ),
                        ],
                    ),
                    # dbc.Card(
                    #     class_name="mt-4",
                    #     children=[
                    #         dbc.CardHeader(
                    #             "US Service",
                    #         ),
                    #         dbc.CardBody(
                    #             dbc.Row(
                    #                 id="contact_us_service",
                    #                 class_name="mb-2",
                    #             )
                    #         ),
                    #     ],
                    # ),
                    dbc.Card(
                        class_name="mt-4",
                        children=[
                            dbc.CardHeader(
                                "Service",
                            ),
                            dbc.CardBody(
                                dbc.Row(
                                    id="contact_canada_service",
                                )
                            ),
                        ],
                    ),
                    dbc.Row(
                        dbc.Col(id="contact_map_locations"),
                        class_name="mt-4",
                    ),
                ],
            ),
        )
    )


@callback(
    Output("contact_main_contacts", "children"),
    Input("hidden_signal_contact", "children"),
)
def populate_main_contacts(_):
    """Populate the main contacts section, hiding the phone number and email address from bots"""

    different_areas = {
        "Operations": {
            "phone": "+1" + "844" + "MYI" + "JA" + "CK",
            "email": "info" + chr(2**6) + "myijack.com",
        },
        "Service": {
            "phone": "+1" + "306" + "434" + "58" + "58",
            "email": "service" + chr(2**6) + "myijack.com",
        },
        "US Sales": {
            "phone": "+1" + "432" + "331" + "42" + "27",
            "email": "sales" + chr(2**6) + "myijack.com",
        },
        "Canada Sales": {
            "phone": "+1" + "403" + "808" + "48" + "70",
            "email": "sales" + chr(2**6) + "myijack.com",
        },
    }
    row_children = []
    for key, value in different_areas.items():
        phone = value["phone"]
        area_code = phone[2:5]
        prefix = phone[5:8]
        add_dash: str = "" if key == "Operations" else "-"
        suffix = phone[8:12]

        phone_link = html.A(
            f"({area_code}) {prefix}{add_dash}{suffix}", href=f"tel:{phone}"
        )

        email = value["email"]
        email_link = html.A(email, href=f"mailto:{email}")

        row_children.append(
            dbc.Col(
                [
                    html.H6(key, className="mb-1 mt-2"),
                    phone_link,
                    html.Br(),
                    email_link,
                ],
            )
        )

    return row_children


@callback(
    Output("contact_canada_service", "children"),
    Input("hidden_signal_contact", "children"),
)
def populate_canada_service(_):
    """Populate the Canada service section, hiding the phone number and email address from bots"""

    service_canada = {
        "Main Contact": {
            "phone": "+1" + "306" + "434" + "58" + "58",
            "email": "service" + chr(2**6) + "myijack.com",
        },
        "Emergency After-Hours": {
            "phone": "+1" + "844" + "709" + "92" + "22",
            "email": "service" + chr(2**6) + "myijack.com",
        },
    }

    row_children = []
    for key, value in service_canada.items():
        phone = value["phone"]
        area_code = phone[2:5]
        prefix = phone[5:8]
        suffix = phone[8:12]

        phone_link = html.A(f"({area_code}) {prefix}-{suffix}", href=f"tel:{phone}")

        email = value["email"]
        email_link = html.A(email, href=f"mailto:{email}")

        row_children.append(
            dbc.Col(
                [
                    html.H6(key, className="mb-1 mt-2"),
                    phone_link,
                    html.Br(),
                    email_link,
                ],
            )
        )

    return row_children


@callback(
    Output("contact_map_locations", "children"),
    Input("hidden_signal_contact", "children"),
)
def create_map_locations(_):
    """Create the map locations section and hide from bots"""

    different_locations = {
        "Head Office and Manufacturing": {
            "address": [
                "0.7 km north of Moosomin on Highway 8",
                html.Br(),
                "PO Box 1759",
                html.Br(),
                "Moosomin, Saskatchewan",
                html.Br(),
                "S0G 3N0",
            ],
            "phone": "+1" + "844" + "MYI" + "JA" + "CK",
            "email": "info" + chr(2**6) + "myijack.com",
            "map": html.Div(
                className="ratio ratio-4x3",
                children=[
                    html.Iframe(
                        width="400",
                        height="300",
                        src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d5111.811745590694!2d-101.675797!3d50.162897!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x2d5afb34733e6ed8!2sIJACK%20Technologies%20Inc!5e0!3m2!1sen!2sca!4v1635886651556!5m2!1sen!2sca",
                    )
                ],
            ),
        },
        "US Service": {
            "address": [
                "117 Energy St",
                html.Br(),
                "Williston, North Dakota",
                html.Br(),
                "58801",
            ],
            "phone": "+1" + "306" + "435" + "75" + "01",
            "email": "service" + chr(2**6) + "myijack.com",
            "map": html.Div(
                className="ratio ratio-4x3",
                children=[
                    html.Iframe(
                        width="400",
                        height="300",
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2657.5640745387245!2d-103.6201012!3d48.2342681!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x5321681cec27986f%3A0x36ee243105e10d75!2s117%20Energy%20St%2C%20Williston%2C%20ND%2058801%2C%20USA!5e0!3m2!1sen!2sca!4v1698173170579!5m2!1sen!2sca",
                    )
                ],
            ),
        },
        "Canada Service": {
            "address": [
                "6107 46 Street",
                html.Br(),
                "Leduc, Alberta",
                html.Br(),
                "T9E 6T8",
            ],
            "phone": "+1" + "306" + "435" + "75" + "01",
            "email": "service" + chr(2**6) + "myijack.com",
            "map": html.Div(
                className="ratio ratio-4x3",
                children=[
                    html.Iframe(
                        width="400",
                        height="300",
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4771.340481098427!2d-113.55019692303588!3d53.277527879870256!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x539ff9af5079982b%3A0xdf4d10b7499ef682!2s6107%2046%20St%2C%20Leduc%2C%20AB%20T9E%206T8!5e0!3m2!1sen!2sca!4v1730407291277!5m2!1sen!2sca",
                    )
                ],
            ),
        },
        "Sales Office": {
            "address": [
                "639 5 Ave SW",
                html.Br(),
                "Calgary, Alberta",
                html.Br(),
                "T2P 0M9",
            ],
            "phone": "+1" + "432" + "331" + "42" + "27",
            "email": "sales" + chr(2**6) + "myijack.com",
            "map": html.Div(
                className="ratio ratio-4x3",
                children=[
                    html.Iframe(
                        width="400",
                        height="300",
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d645.436994842892!2d-114.07608595069269!3d51.04846534476731!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53716fe4eb0fff09%3A0xf7b1149afa3e1b6c!2s639%205%20Ave%20SW%2C%20Calgary%2C%20AB%20T2P%200M9!5e0!3m2!1sen!2sca!4v1635962041578!5m2!1sen!2sca",
                    )
                ],
            ),
        },
    }

    layout = []
    for key, value in different_locations.items():
        address = value["address"]
        phone = value["phone"]
        area_code = phone[2:5]
        prefix = phone[5:8]
        suffix = phone[8:12]
        email = value["email"]
        map_html = value["map"]

        layout.append(
            dbc.Row(
                dbc.Col(
                    dbc.Card(
                        class_name="mt-4",
                        children=[
                            dbc.CardHeader(key),
                            dbc.CardBody(
                                [
                                    html.Div(address),
                                    html.Br(),
                                    html.A(
                                        f"({area_code}) {prefix}-{suffix}",
                                        href=f"tel:{phone}",
                                    ),
                                    html.Br(),
                                    html.A(email, href=f"mailto:{email}"),
                                    html.Br(),
                                    html.Br(),
                                    map_html,
                                ]
                            ),
                        ],
                    )
                )
            )
        )

    return layout


@callback(
    Output("contact_unit_type_select", "options"),
    Input("hidden_signal_contact", "children"),
)
def populate_unit_type_select(_):
    """Populate the unit_type select dropdown"""

    unit_types_allowed: str = ",".join(
        (
            str(UNIT_TYPE_ID_BOOST),
            str(UNIT_TYPE_ID_DGAS),
            str(UNIT_TYPE_ID_EGAS),
            str(UNIT_TYPE_ID_UNO),
            str(UNIT_TYPE_ID_UNOGAS),
            str(UNIT_TYPE_ID_VRU),
            str(UNIT_TYPE_ID_XFER),
        )
    )
    options: list = get_db_options(
        columns=["unit_type"],
        table="unit_types",
        schema="public",
        where=f"where id in ({unit_types_allowed})",
        ascending=False,
    )
    if not options:
        raise PreventUpdate()

    options.insert(0, {"label": "No unit type selected", "value": 0})

    return options


@callback(
    Output("contact_structures_select_store", "data"),
    Input("hidden_signal_contact", "children"),
)
def populate_structures_select_store(_):
    """Query for all structures only once when the page loads"""

    # JSON-ready list of dicts
    structures: List[dict] = get_all_structures(
        user=current_user,
        unit_type_ids=(
            UNIT_TYPE_ID_BOOST,
            UNIT_TYPE_ID_DGAS,
            UNIT_TYPE_ID_EGAS,
            UNIT_TYPE_ID_UNO,
            UNIT_TYPE_ID_UNOGAS,
            UNIT_TYPE_ID_VRU,
            UNIT_TYPE_ID_XFER,
        ),
    )

    if not structures:
        raise PreventUpdate()

    return structures


@callback(
    Output("contact_structures_select", "options"),
    Input("contact_structures_select", "search_value"),
    Input("contact_unit_type_select", "value"),
    Input("contact_structures_select_store", "data"),
    prevent_initial_call=True,
)
def populate_structures_select(
    contact_structures_select_search_value,
    contact_unit_type_select_value,
    contact_structures_select_store_data,
):
    """Populate the structures select dropdown"""

    if not contact_structures_select_store_data:
        return [{"label": "No units found", "value": 0}]

    id_triggered: str = get_id_triggered()
    if (
        id_triggered == "contact_structures_select.search_value"
        and not contact_structures_select_search_value
    ):
        # Don't trigger this callback when the search box is cleared
        # (i.e. after a mouse-up event... feels like a Dash bug, but this addresses/fixes it...)
        raise PreventUpdate()

    # Limit the number of options so people can't see how many units we have
    unit_limit: int = 5
    counter: int = 0
    options: list = [{"label": "No unit selected", "value": 0}]
    for structure in contact_structures_select_store_data:
        if contact_unit_type_select_value:
            # Filter on unit type
            if str(structure["unit_type_id"]) != str(contact_unit_type_select_value):
                continue

        if contact_structures_select_search_value:
            # Filter on search value
            if not (
                str(contact_structures_select_search_value).lower()
                in str(structure["power_unit_str"]).lower()
                or str(contact_structures_select_search_value).lower()
                in str(structure["structure_str"]).lower()
            ):
                continue

        # We're going to add an option, so increment the counter
        counter += 1
        if counter > unit_limit:
            break

        options.append(
            {
                "value": structure["id"],
                "label": (
                    f"{structure['unit_type']} power unit {structure['power_unit_str']}, structure {structure['structure_str']}"
                    if structure["power_unit_str"]
                    else f"{structure['unit_type']} structure {structure['structure_str']}"
                ),
            }
        )

    return options


@callback(
    Output("contact_service_type_select", "options"),
    Input("hidden_signal_contact", "children"),
)
def populate_service_type_select(_):
    """Populate the service_type select dropdown"""

    options: list = get_db_options(
        columns=["name"], table="service_types", schema="public"
    )
    if not options:
        raise PreventUpdate()

    # options: list = [
    #     {"value": s[0], "label": f"Power Unit {s[1]}, Structure {s[2]}"}
    #     for s in sorted(service_type, key=lambda x: (x[1], x[2]))
    # ]
    options.insert(0, {"label": "No Service Type Selected", "value": 0})

    return options


@callback(
    Output("contact_first_name", "value"),
    Output("contact_last_name", "value"),
    Output("contact_email", "value"),
    # Just so we can trigger another callback for
    # initializing contact_phone without duplicate outputs
    Output("trigger_init_phone", "data"),
    Input("hidden_signal_contact", "children"),
)
def populate_customer_info(_):
    """Populate the customer info fields, if we know who's logged in"""

    if not current_user.is_authenticated:
        raise PreventUpdate()

    return (
        current_user.first_name,
        current_user.last_name,
        str(current_user.email).strip(),
        None,
    )


@callback(
    Output("contact_first_name", "invalid"),
    Input("contact_first_name", "n_blur"),
    State("contact_first_name", "value"),
    prevent_initial_call=True,
)
def validate_first_name(_, contact_first_name_value):
    """Validation"""
    if not contact_first_name_value or not str(contact_first_name_value).strip():
        return True
    return False


@callback(
    Output("contact_last_name", "invalid"),
    Input("contact_last_name", "n_blur"),
    State("contact_last_name", "value"),
    prevent_initial_call=True,
)
def validate_last_name(_, contact_last_name_value):
    """Validation"""
    if not contact_last_name_value or not str(contact_last_name_value).strip():
        return True
    return False


@callback(
    Output("contact_email", "invalid"),
    Input("contact_email", "n_blur"),
    State("contact_email", "value"),
    prevent_initial_call=True,
)
def validate_email_field(_, contact_email_value):
    """Validation"""
    try:
        validate_email(form=None, field=contact_email_value, must_be_unique=False)
    except Exception:
        return True

    bad_emails: list = [
        # Tim Beals says to block this guy - it's always spam
        "saudico-procurement.info",
        "saudiaramcoprocurement.com",
    ]
    for bad_email in bad_emails:
        if bad_email in contact_email_value:
            return True

    return False


@callback(
    Output("contact_phone", "invalid"),
    # Nicely-formatted phone number returned
    Output("contact_phone", "value"),
    # Once/if the other initial values are set on page load, we can set the phone number
    Input("trigger_init_phone", "data"),
    Input("contact_phone", "n_blur"),
    State("contact_phone", "value"),
    prevent_initial_call=True,
)
def validate_phone_num_field(_, contact_phone_n_blur, contact_phone_value):
    """Validation and initial value"""

    id_triggered: str = get_id_triggered()
    if id_triggered == "trigger_init_phone.data" and current_user.is_authenticated:
        # Initial value on page load
        contact_phone_value = current_user.phone

    try:
        phone_parsed: phonenumbers.PhoneNumber = validate_phone(
            form=None, field=contact_phone_value, must_be_unique=False
        )
        phone_international = phonenumbers.format_number(
            phone_parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
        )
    except Exception:
        return True, contact_phone_value

    bad_phones: list = [
        # Tim says to block this guy
        "5074015029"
    ]
    for bad_phone in bad_phones:
        if bad_phone in contact_phone_value:
            return True, phone_international

    return False, phone_international


@callback(
    Output("contact_message", "invalid"),
    Input("contact_message", "value"),
    prevent_initial_call=True,
)
def validate_message(contact_message_value):
    """Validation"""

    if not contact_message_value or not str(contact_message_value).strip():
        return True

    if "seo" in str(contact_message_value).lower():
        # Don't want any emails about SEO!
        return True

    # Find the language in which the message is written
    # https://amitness.com/2019/07/identify-text-language-python/
    detector = NNetLanguageIdentifier(min_num_bytes=0, max_num_bytes=1000)
    lang = detector.FindLanguage(text=contact_message_value)

    if lang.language not in (
        "en",
        "es",
        "fr",
        "de",
    ):
        # msg = "Please submit an English-language message."
        return True

    if len(contact_message_value) < 10:
        return True

    return False


@callback(
    Output("contact_structures_select_ft", "children"),
    Output("contact_service_type_select_ft", "children"),
    Input("contact_structures_select", "value"),
    Input("contact_service_type_select", "value"),
    prevent_initial_call=True,
)
def validate_service_request_fields(
    contact_structures_select_value, contact_service_type_select_value
):
    """Validation"""

    null_values = ("", None, 0, "0")
    if (
        contact_structures_select_value not in null_values
        and contact_service_type_select_value in null_values
    ):
        # Must specify a service type if a structure is selected
        return "", "Please select a service type if a unit is selected."
    return "", ""


@callback(
    Output("store_site_key_action_contact", "data"),
    Input("hidden_signal_contact", "children"),
    prevent_initial_call=False,
)
def store_google_recaptcha_sitekey(_):
    """Store the reCAPTCHA sitekey in the hidden div"""
    return {"site_key": os.getenv("RECAPTCHA_SITE_KEY", ""), "action": "contact"}


# The following are in app/dash_assets/recaptcha.js
clientside_callback(
    ClientsideFunction(
        namespace="contact_namespace",
        function_name="initialize_recaptcha_in_js",
    ),
    Output("store_nothing_contact", "data"),
    Input("store_site_key_action_contact", "data"),
    prevent_initial_call=True,
)

clientside_callback(
    ClientsideFunction(
        namespace="contact_namespace",
        function_name="on_submit_application",
    ),
    Output("store_recaptcha_response_contact", "data"),
    Input("contact_submit_btn", "n_clicks"),
    State("store_site_key_action_contact", "data"),
    prevent_initial_call=True,
)


@callback(
    Output("contact_submit_status", "children"),
    Output("contact_submit_status", "color"),
    # Input("contact_submit_btn", "n_clicks"),
    Input("store_recaptcha_response_contact", "data"),
    State("contact_structures_select", "value"),
    State("contact_service_type_select", "value"),
    State("contact_first_name", "value"),
    State("contact_last_name", "value"),
    State("contact_email", "value"),
    State("contact_phone", "value"),
    State("contact_message", "value"),
    # Validation results
    State("contact_first_name", "invalid"),
    State("contact_last_name", "invalid"),
    State("contact_email", "invalid"),
    State("contact_phone", "invalid"),
    State("contact_message", "invalid"),
    # State("contact_structures_select", "invalid"),
    # State("contact_service_type_select", "invalid"),
    State("contact_structures_select_ft", "children"),
    State("contact_service_type_select_ft", "children"),
    prevent_initial_call=True,
)
def process_contact_form(
    # contact_submit_btn_n_clicks,
    contact_recaptcha_response_children,
    contact_structures_select_value,
    contact_service_type_select_value,
    contact_first_name_value,
    contact_last_name_value,
    contact_email_value,
    contact_phone_value,
    contact_message_value,
    # Validation results
    contact_first_name_invalid,
    contact_last_name_invalid,
    contact_email_invalid,
    contact_phone_invalid,
    contact_message_invalid,
    contact_structures_select_ft_children,
    contact_service_type_select_ft_children,
):
    """Process the contact form"""

    if (
        # Validation results
        contact_first_name_invalid
        or contact_last_name_invalid
        or contact_email_invalid
        or contact_phone_invalid
        or contact_message_invalid
        # These need to be empty (i.e. no warning messages)
        or contact_structures_select_ft_children
        or contact_service_type_select_ft_children
    ):
        msg = "Please fill in all required fields"
        color = "danger"
        # flash(msg, color)
        return msg, color

    if not recaptcha_is_good(
        contact_recaptcha_response_children, expected_action="contact"
    ):
        msg = "reCAPTCHA verification failed"
        color = "danger"
        # flash(msg, color)
        return msg, color

    empty_values = ("", None, 0, "0")
    if contact_structures_select_value not in empty_values:
        try:
            msg = submit_service_request(
                operator_full_name=f"{contact_first_name_value} {contact_last_name_value}",
                operator_phone=contact_phone_value,
                operator_email=contact_email_value,
                description=contact_message_value,
                service_type_id=contact_service_type_select_value,
                structure_id=contact_structures_select_value,
                user_id=getattr(current_user, "id", None),
                create_work_order=True,
            )
        except Exception as err:
            msg = f"Error submitting service request: {err}"
            color = "danger"
            return msg, color
        return msg, "success"

    phone_parsed = phonenumbers.parse(contact_phone_value, "US")
    phone_e164 = phonenumbers.format_number(
        phone_parsed, phonenumbers.PhoneNumberFormat.E164
    )
    contact_form_model = ContactForm(
        user_id=getattr(current_user, "id", None),
        first_name=contact_first_name_value,
        last_name=contact_last_name_value,
        email=contact_email_value,
        phone=phone_e164,
        message=contact_message_value,
    )
    db.session.add(contact_form_model)
    db.session.commit()

    to_emails = ["<EMAIL>"]
    subject = "myijack.com 'Contact Us' Message"
    sender = "IJACK <<EMAIL>>"
    text_body = f"Message from: {contact_first_name_value} {contact_last_name_value} \nEmail address: {contact_email_value} \nPhone number: {contact_phone_value} \nMessage: \n{contact_message_value}"
    send_email(subject, sender, to_emails, text_body, html_body=None)

    msg = f"Thanks for your message, {contact_first_name_value}! Someone will contact you as soon as possible."
    return msg, "success"
