import os
import urllib.parse
from datetime import datetime, timezone

import dash_bootstrap_components as dbc
import pytz
from dash import Input, Output, State, callback, html, no_update
from dash.exceptions import PreventUpdate
from flask import (
    current_app,
    flash,
    url_for,
)
from flask.globals import session
from flask_login import current_user, logout_user
from shared.models.models import <PERSON><PERSON>, StructureVw

from app import (
    cache_memoize_if_prod,
    can_see_rcom,
    db,
    get_user_cust_ids,
    get_user_role_ids,
    is_active,
    is_admin,
    is_admin_cust,
    user_is_demo_customer,
    user_is_ijack_employee,
)
from app.config import (
    ACTIVE_TAB_NORMAL_CHARTS,
    ACTIVE_TAB_RT_CHARTS,
    ALERTS_LABEL_ACTIVATE,
    ALERTS_LABEL_PAUSE,
    CUST_SUB_GROUP_ID_ALL_GROUPS,
    CUST_SUB_GROUP_ID_ALL_OTHERS,
    CUSTOMER_ID_IJACK_CORP,
    CUSTOMER_ID_IJACK_INC,
    DASH_GATEWAY_TROUBLESHOOTING,
    DASH_URL_ACCOUNT,
    DASH_URL_APPLICATION,
    DASH_URL_CAREERS,
    DASH_URL_CONTACT,
    DASH_URL_LOGIN,
    DASH_URL_MODBUS,
    DASH_URL_RCOM,
    DASH_URL_REGISTER,
    DASH_URL_RELEASE_NOTES,
    DASH_URL_SERVICE_CLOCK,
    DASH_URL_SMS_ALERTS_TROUBLESHOOTING,
    DASH_URL_WEB_API,
    DASH_URL_WORK_ORDER,
    DASH_URL_WORK_ORDER_CORP,
    ROLE_ID_IJACK_ADMIN,
    ROLE_ID_IJACK_SALES,
    ROLE_ID_IJACK_SERVICE,
    ROLE_ID_SERVICE_CLOCK_TIME_APPROVER,
    STRUCTURE_ID_SHOP,
    TAB_CARDS,
    TAB_CHARTS,
    TAB_CONTROL,
    TAB_HEALTH,
    TAB_INDICATORS,
    TAB_INVENTORY,
    TAB_LIST_FILTERS,
    TAB_LOG,
    TAB_MAP,
    TAB_PERFORMANCE,
    TAB_STATUS,
    TAB_UNOGAS_EGAS,
    TAB_UNOGAS_UNO,
    UNIT_TYPE_ID_ALL_TYPES,
    UNIT_TYPE_ID_GATEWAYS,
    UNIT_TYPE_ID_UNO,
    UNIT_TYPE_ID_UNOGAS,
    UNIT_TYPES_IDS_XFER_EGAS,
)
from app.dashapp.callbacks.account import account_layout
from app.dashapp.callbacks.application import application_layout
from app.dashapp.callbacks.careers import careers_layout
from app.dashapp.callbacks.contact import contact_layout
from app.dashapp.callbacks.error import error_layout
from app.dashapp.callbacks.gateway_troubleshooting import gateway_troubleshooting_layout
from app.dashapp.callbacks.login import login_layout
from app.dashapp.callbacks.modbus import modbus_layout
from app.dashapp.callbacks.register import registration_layout
from app.dashapp.callbacks.release_notes import release_notes_layout
from app.dashapp.callbacks.service_clock import service_clock_layout
from app.dashapp.callbacks.sms_alerts_troubleshooting import sms_alerts_layout
from app.dashapp.callbacks.web_api import web_api_layout
from app.dashapp.callbacks.work_order import work_order_layout
from app.dashapp.layout import get_main_layout, get_url_bar_and_content_div
from app.dashapp.utils import (
    get_id_triggered,
    get_struct_obj_not_demo_customer,
    get_structure_obj,
    get_unit_type,
    log_function_caller,
    parse_url,
)
from app.models.models import User


@cache_memoize_if_prod(timeout=300)
def get_structure_obj_from_url_search(url_search: str) -> StructureVw:
    """The unit_id could be either a power unit, or a structure"""
    if url_search == "":
        current_app.logger.debug("url_search = '' so not returning StructureVw object!")
        # Raise exception so the result isn't cached?
        raise ValueError("url_search is empty")

    unit_type, unit_id = parse_url(url_search)
    if not unit_id or unit_id in ("None", "null"):
        current_app.logger.warning(
            "unit_id = %s so not returning StructureVw object!", unit_id
        )
        raise PreventUpdate()

    if unit_type == "structure":
        return get_struct_obj_not_demo_customer(structure=unit_id)
    elif unit_type == "structure_id":
        return get_struct_obj_not_demo_customer(structure_id=unit_id)
    elif unit_type == "power_unit":
        return get_struct_obj_not_demo_customer(power_unit=unit_id)
    elif unit_type == "power_unit_id":
        return get_struct_obj_not_demo_customer(power_unit_id=unit_id)
    elif unit_type == "gateway":
        return get_struct_obj_not_demo_customer(gateway=unit_id)

    return get_struct_obj_not_demo_customer(unit_id)


def get_validation_layout():
    """
    For validating multipage Dash apps.
    Don't run the queries at this time; just get the layouts
    """
    return html.Div(
        [
            get_url_bar_and_content_div(),
            get_main_layout(),
            work_order_layout(has_app_context=False),
            service_clock_layout(),
            application_layout(),
            release_notes_layout(),
            contact_layout(),
            careers_layout(),
            error_layout(),
            registration_layout(has_app_context=False),
            login_layout(has_app_context=False),
            modbus_layout(),
            web_api_layout(),
            gateway_troubleshooting_layout(has_app_context=False),
            sms_alerts_layout(),
            account_layout(),
        ]
    )


# # This callback is used to load the specific JavaScript files needed for each layout
# clientside_callback(
#     ClientsideFunction(
#         namespace="nav_namespace",
#         function_name="load_js_needed",
#     ),
#     Output("dynamic_js", "children"),
#     Input("url", "pathname"),
# )

# clientside_callback(
#     ClientsideFunction(
#         namespace="nav_namespace",
#         function_name="compare_version_refresh",
#     ),
#     Output("location_main_app", "href"),
#     Input("swv_refresh_interval", "n_intervals"),
#     State("store_software_version", "data"),
#     prevent_initial_call=True,
# )


@callback(
    Output("page_content", "children"),
    Input("url", "pathname"),
    State("url", "search"),
    # This callback loads the dynamic layout,
    # so it should definitely be called on page load
    prevent_initial_call=False,
)
def display_page(pathname, url_search):
    """For multipage app, check the URL and load the appropriate, pre-validated layout"""

    def not_authenticated_return():
        """
        If the user is not authenticated, return the login page.

        NOTE: The @login_required in /dashapp/views.py will redirect to the login page
        BEFORE we even get here, but better safe than sorry.
        """
        msg = "You must be logged in to view this page."
        return error_layout(error_code=403, pathname=pathname, msg=msg)

    def get_url_query_kwarg(kwarg: str) -> str:
        """Get the value of a kwarg from the URL"""
        if url_search:
            query_str = urllib.parse.urlsplit(url_search).query
            query_dict = urllib.parse.parse_qs(query_str)
            result_list = query_dict.get(kwarg, None)
            if isinstance(result_list, (list, tuple)):
                return result_list[0]
        return ""

    def get_work_order_layout(company_id: int):
        """Get the right work order layout after checking a few things"""
        if not current_user.is_authenticated:
            return not_authenticated_return()

        user_id = getattr(current_user, "id", None)
        role_ids: list = get_user_role_ids(user_id=user_id)
        # NOTE: We are preparing to show the work orders layout to customers
        # as well as IJACK people so they can approve them, but it's not quite ready yet
        is_customer: bool = True
        if ROLE_ID_IJACK_SALES in role_ids or ROLE_ID_IJACK_SERVICE in role_ids:
            is_customer = False
            return work_order_layout(
                has_app_context=True, company_id=company_id, is_customer=is_customer
            )

        msg = "This page requires either the 'Service' or 'Sales' roles."
        return error_layout(error_code=403, pathname=pathname, msg=msg)

    # if url_search:
    #     query_str = urllib.parse.urlsplit(url_search).query
    #     query_dict = urllib.parse.parse_qs(query_str)
    #     work_order_id = query_dict.get("work_order_id", None)
    #     if isinstance(work_order_id, (list, tuple)):
    #         work_order_id = work_order_id[0]

    if pathname == f"/{DASH_URL_RCOM}/":
        if not current_user.is_authenticated:
            return not_authenticated_return()
        if not can_see_rcom(user_id=getattr(current_user, "id", None)):
            msg = "Please contact IJACK to request access to the RCOM dashboard. Thank you."
            return error_layout(error_code=403, pathname=pathname, msg=msg)
        return get_main_layout()

    if pathname == f"/{DASH_URL_RELEASE_NOTES}/":
        if not current_user.is_authenticated:
            return not_authenticated_return()
        return release_notes_layout()

    if pathname == f"/{DASH_URL_WORK_ORDER_CORP}/":
        return get_work_order_layout(company_id=CUSTOMER_ID_IJACK_CORP)

    if pathname == f"/{DASH_URL_WORK_ORDER}/":
        return get_work_order_layout(company_id=CUSTOMER_ID_IJACK_INC)

    if pathname == f"/{DASH_URL_SERVICE_CLOCK}/":
        if not current_user.is_authenticated:
            return not_authenticated_return()
        if current_user.has_role_id(
            ROLE_ID_SERVICE_CLOCK_TIME_APPROVER
        ) or current_user.has_role_id(ROLE_ID_IJACK_SERVICE):
            return service_clock_layout()
        msg = "This page requires either the 'Service Clock Time Approver' or 'Service' roles."
        return error_layout(error_code=403, pathname=pathname, msg=msg)

    # User can access the application page whether authenticated or not
    if pathname == f"/{DASH_URL_APPLICATION}/":
        return application_layout()

    if pathname == f"/{DASH_URL_REGISTER}/":
        return registration_layout()

    if pathname == f"/{DASH_URL_LOGIN}/":
        return login_layout(has_app_context=True)

    # User can access the contact page whether authenticated or not
    elif pathname == f"/{DASH_URL_CONTACT}/":
        return contact_layout()

    elif pathname == f"/{DASH_URL_CAREERS}/":
        return careers_layout()

    elif pathname == f"/{DASH_URL_MODBUS}/":
        return modbus_layout()

    elif pathname == f"/{DASH_URL_WEB_API}/":
        return web_api_layout()

    elif pathname == f"/{DASH_GATEWAY_TROUBLESHOOTING}/":
        return gateway_troubleshooting_layout(has_app_context=True)

    elif pathname == f"/{DASH_URL_SMS_ALERTS_TROUBLESHOOTING}/":
        return sms_alerts_layout()

    elif pathname == f"/{DASH_URL_ACCOUNT}/":
        return account_layout()

    # TODO: Add a 404 page or redirect to the login page!
    return error_layout(error_code=404, pathname=pathname)


@callback(
    # This id="url" is common to all layouts in multi-page apps (see "display_page()" above)
    Output("location_main_app", "href"),
    Input("store_structure_id", "data"),
    Input("tabs_for_nav", "active_tab"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("swv_refresh_interval", "n_intervals"),
    prevent_initial_call=False,
)
def check_is_active(
    store_structure_id_data,
    tabs_for_nav_active_tab,
    store_tabs_for_ind_charts_control_log_data,
    swv_refresh_interval_n_intervals,
):
    """Check if the user is active"""
    if not is_active():
        logout_user()
        flash(
            "You have been logged out since your RCOM account is inactive. Please contact IJACK."
        )
        return url_for("dash.login")

    user_id: int = getattr(current_user, "id", None)
    if not can_see_rcom(user_id=user_id):
        flash(
            "Please contact IJACK to request access to the RCOM dashboard. Thank you."
        )
        return url_for("dash.contact")

    raise PreventUpdate()


@callback(
    Output("url", "search"),
    Input("page_content", "children"),
    State("url", "search"),
    prevent_initial_call=True,
)
def re_trigger_url_search_after_dynamic_layout(_, url_search):
    """
    For multipage apps, the url.search callback isn't fired
    when the url.pathname or url.search is first triggered,
    because the page's layout is loaded dynamically in the 'display_page()' callback above,
    so the layout and dcc.Location isn't even available yet
    when the url.search ID would have been triggered!
    """
    return url_search or None


@callback(
    Output("flashed_messages_div", "children"),
    Input("hidden_signal", "children"),
    # Anything that uses "hidden_signal" should run on page load!
    prevent_initial_call=False,
)
def get_flask_flashed_messages(
    _,
    # swv_refresh_interval
):
    """
    Show and remove flashed messages from Flask so they don't show up later
    (e.g. in the admin site, or back on the marketing pages)
    """
    # log_function_caller()

    # flashes = session.get("_flashes", [])
    flashes = session.pop("_flashes", [])
    # if flashes is None:
    #     raise PreventUpdate()

    # if swv_refresh_interval > 2:
    #     id_triggered: str = get_id_triggered()
    #     if id_triggered and "swv_refresh_interval" in id_triggered:
    #         session.pop("_flashes", [])
    #         # If we don't clear the session, the flashes will still be in there
    #         session.clear()

    category_bootstrap_colors = {
        "critical": "danger",
        "error": "danger",
        "info": "info",
        "warning": "warning",
        "success": "success",
        "debug": "info",
        "notset": "info",
        "message": "info",
    }

    div_children = []
    for message in flashes:
        color = category_bootstrap_colors.get(message[0], "info")
        div_children.append(dbc.Alert(message[1], color=color, dismissable=True))

    return div_children


@callback(
    Output("cust_sub_groups_edit_btn", "style"),
    Output("units_share_btn", "style"),
    Output("units_share_clipboard", "style"),
    Output("units_edit_btn", "style"),
    Output("unit_edit_btn", "style"),
    Output("alertees_duplicate_btn", "style"),
    Output("operators_edit_dropdown", "style"),
    Output("edit_users_link", "style"),
    Output("alertees_edit_individual", "style"),
    Output("alertees_edit_bulk", "style"),
    Output("edit_rc_permissions", "style"),
    Output("edit_pm_people", "style"),
    Output("edit_sales_contacts", "style"),
    Output("cust_sub_groups_edit_btn", "href"),
    Output("units_share_btn", "href"),
    Output("units_share_clipboard", "content"),
    Output("units_edit_btn", "href"),
    Output("unit_edit_btn", "href"),
    Output("edit_users_link", "href"),
    Output("alertees_edit_individual", "href"),
    Output("alertees_edit_bulk", "href"),
    Output("edit_rc_permissions", "href"),
    Output("edit_pm_people", "href"),
    Output("edit_sales_contacts", "href"),
    # Output("ijack_only_indicators_row", "style"),
    Output("ind_warn_time_metric_div", "style"),
    # Output("tab_diag_cards_id", "tab_style"),
    Output("tab_inventory_id", "tab_style"),
    Output("service_request_operator_info_div", "style"),
    Input("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    # Allow this to run on page refresh since it's based on the user
    prevent_initial_call=False,
)
def edit_alertees_edit_button(store_structure_id_data, store_unit_type_id_data):
    """If the current_user is_admin(), add a link to edit the alertees in the 'Admin' site."""
    log_function_caller()

    cust_sub_groups_edit_btn_style = {"display": "none"}
    units_share_btn_style = {"display": "none"}
    units_share_clipboard_style = {"display": "none"}
    units_edit_btn_style = {"display": "none"}
    unit_edit_btn_style = {"display": "none"}
    alertees_duplicate_btn_style = {"display": "none"}
    operators_edit_dropdown_style = {"display": "none"}
    edit_users_link_style = {"display": "none"}
    alertees_edit_individual_style = {"display": "none"}
    alertees_edit_bulk_style = {"display": "none"}
    edit_rc_permissions_style = {"display": "none"}
    edit_pm_people_style = {"display": "none"}
    edit_sales_contacts_style = {"display": "none"}
    cust_sub_groups_edit_btn_href = None
    units_share_btn_href = None
    units_share_clipboard_content = None
    units_edit_btn_href = None
    unit_edit_btn_href = None
    edit_users_link_href = None
    alertees_edit_individual_href = None
    alertees_edit_bulk_href = None
    edit_rc_permissions_href = None
    edit_pm_people_href = None
    edit_sales_contacts_href = None
    # {"display": "none"} by default
    # ijack_only_indicators_row_style = no_update
    ind_warn_time_metric_div_style = {"display": "none"}
    # tab_diag_cards_id_tab_style = {"display": "none"}
    tab_inventory_id_tab_style = {"display": "none"}
    service_request_operator_info_div_style = {"display": "none"}

    def return_variables():
        """Get the return variables"""
        return (
            cust_sub_groups_edit_btn_style,
            units_share_btn_style,
            units_share_clipboard_style,
            units_edit_btn_style,
            unit_edit_btn_style,
            alertees_duplicate_btn_style,
            operators_edit_dropdown_style,
            edit_users_link_style,
            alertees_edit_individual_style,
            alertees_edit_bulk_style,
            edit_rc_permissions_style,
            edit_pm_people_style,
            edit_sales_contacts_style,
            cust_sub_groups_edit_btn_href,
            units_share_btn_href,
            units_share_clipboard_content,
            units_edit_btn_href,
            unit_edit_btn_href,
            edit_users_link_href,
            alertees_edit_individual_href,
            alertees_edit_bulk_href,
            edit_rc_permissions_href,
            edit_pm_people_href,
            edit_sales_contacts_href,
            # ijack_only_indicators_row_style,
            ind_warn_time_metric_div_style,
            # tab_diag_cards_id_tab_style,
            tab_inventory_id_tab_style,
            service_request_operator_info_div_style,
        )

    user_id: int = getattr(current_user, "id", None)
    user_cust_ids: tuple = get_user_cust_ids(user_id=user_id)
    if user_is_demo_customer(user_id=user_id, user_cust_ids=user_cust_ids):
        # Demo users don't get to see anything special
        return return_variables()

    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        return return_variables()
    has_rcom: bool = bool(structure_obj.aws_thing)

    sp_gateway: str = ""
    sp_structure: str = ""
    sp_power_unit: str = ""
    if store_structure_id_data:
        if structure_obj.gateway:
            sp_gateway = structure_obj.gateway
        if structure_obj.structure:
            sp_structure = str(structure_obj.structure).replace(".0", "")

        unit_info: str = ""
        if getattr(structure_obj, "customer", None):
            unit_info = f"{structure_obj.customer} "
        if structure_obj.unit_type:
            unit_info += f"{structure_obj.unit_type} "

        link: str = ""
        if structure_obj.power_unit_str:
            sp_power_unit = structure_obj.power_unit_str
            unit_info += f"{structure_obj.power_unit_str} "
            # Make an email link to share the unit
            link = (
                f"https://myijack.com/rcom/?power_unit={structure_obj.power_unit_str}"
            )
        elif structure_obj.structure:
            link = f"https://myijack.com/rcom/?structure={structure_obj.structure_str}"

        if structure_obj.surface:
            unit_info += f"at {structure_obj.surface}"

        units_share_clipboard_content = f"{unit_info}\n{link}"
        units_share_clipboard_style = {}
        subject: str = f"Check out {unit_info}"
        subject = urllib.parse.quote(subject, safe="")
        body: str = f"Here's the link:\n{link}"
        body = urllib.parse.quote(body, safe="")
        units_share_btn_href = f"mailto:?subject={subject}&body={body}"
        units_share_btn_style = {}

    is_admin_ijack: bool = is_admin()
    is_admin_cust_var: bool = is_admin_cust()
    is_ijack_employee: bool = user_is_ijack_employee(
        user_id=user_id, user_cust_ids=user_cust_ids
    )
    if is_admin_ijack or is_admin_cust_var:
        edit_users_link_style = {}
        edit_pm_people_style = {}
        edit_sales_contacts_style = {}
        cust_sub_groups_edit_btn_style = {}
        units_edit_btn_style = {}
        unit_edit_btn_style = {}
        operators_edit_dropdown_style = {}
        if has_rcom:
            # Does the unit have a power unit and gateway (i.e. RCOM)?
            alertees_edit_individual_style = {}
            alertees_edit_bulk_style = {}
            edit_rc_permissions_style = {}
            alertees_duplicate_btn_style = {}

    if is_ijack_employee:
        # This one doesn't get used anymore, I think
        # tab_diag_cards_id_tab_style = {}
        service_request_operator_info_div_style = {}

    sp_indiv = (
        sp_gateway
        if store_unit_type_id_data == UNIT_TYPE_ID_GATEWAYS
        else sp_power_unit
    )
    sp_rc = sp_power_unit if sp_power_unit else sp_structure
    if is_admin_ijack:
        # For IJACK global admins
        tab_inventory_id_tab_style = {}
        # if current_user.has_role_id(ROLE_ID_IJACK_HUMAN_RESOURCES):
        #     edit_users_link_href = "/admin/users_hr/"
        # else:
        edit_users_link_href = "/admin/users/"
        alertees_edit_individual_href = f"/admin/alerts/?search={sp_indiv}"
        alertees_edit_bulk_href = f"/admin/power_units/?search={sp_power_unit}"
        edit_rc_permissions_href = f"/admin/structures/?search={sp_indiv}"
        edit_pm_people_href = edit_rc_permissions_href
        edit_sales_contacts_href = edit_rc_permissions_href

        if store_unit_type_id_data == UNIT_TYPE_ID_GATEWAYS:
            endpoint = "gateways"
            sp_edit_unit = sp_gateway
        else:
            endpoint = "structures"
            sp_edit_unit = sp_structure
        unit_edit_btn_href = f"/admin/{endpoint}/?search={sp_edit_unit}"
        units_edit_btn_href = f"/admin/{endpoint}/"
        cust_sub_groups_edit_btn_href = "/admin/cust_sub_groups/"
        # Only show the following if we're testing something we want to show to IJACK-only users
        # ijack_only_indicators_row_style = {}
        ind_warn_time_metric_div_style = {}

    elif is_admin_cust_var:
        # For customer admins, to admin their own customers/units/sub-groups
        alertees_edit_individual_href = f"/admin_cust/alerts/?search={sp_indiv}"
        # Gateways view
        alertees_edit_bulk_href = (
            f"/admin_cust/power_unit_and_alert_settings/?search={sp_power_unit}"
        )
        # Structures view
        edit_rc_permissions_href = (
            f"/admin_cust/structure_info_and_remote_control/?search={sp_rc}"
        )
        edit_pm_people_href = edit_rc_permissions_href
        edit_sales_contacts_href = edit_rc_permissions_href

        if store_unit_type_id_data == UNIT_TYPE_ID_GATEWAYS:
            endpoint = "power_unit_and_alert_settings"  # gateways
            sp_edit_unit = sp_gateway
        else:
            endpoint = "structure_info_and_remote_control"
            sp_edit_unit = sp_structure
        unit_edit_btn_href = f"/admin_cust/{endpoint}/?search={sp_edit_unit}"
        units_edit_btn_href = f"/admin_cust/{endpoint}/"
        cust_sub_groups_edit_btn_href = "/admin_cust/groups/"

    return return_variables()


@callback(
    Output("tabs_for_ind_charts_control_log", "active_tab"),
    Input("store_structure_id", "data"),
    State("store_tabs_for_ind_charts_control_log", "data"),
    prevent_initial_call=True,
)
def set_active_tab_for_ind_charts_control_log(
    store_structure_id_data,
    store_tabs_for_ind_charts_control_log_data,
):
    """
    If the current_user has role == 1 == 'IJACK' and the structure_id == 1 == "SHOP",
    set the default tab to 'tab_control' so the user can quickly control the shop.
    """
    log_function_caller()
    # If it's an IJACK employee and it's the SHOP gateway, activate the control tab
    user_id: int = getattr(current_user, "id", None)
    if (
        store_structure_id_data == STRUCTURE_ID_SHOP
        and user_is_ijack_employee(user_id=user_id)
        and is_admin()
    ):
        return TAB_CONTROL

    # Returning the active tab, after updating the structure_id,
    # will trigger the info below the tab
    if isinstance(store_tabs_for_ind_charts_control_log_data, (int, float)):
        return store_tabs_for_ind_charts_control_log_data

    return TAB_INDICATORS


@callback(
    Output("store_tabs_for_ind_charts_control_log", "data"),
    Input("tabs_for_ind_charts_control_log", "active_tab"),
    # Other stuff depends on store_tabs_for_ind_charts_control_log
    # being updated when the unit changes so this must be an Input
    Input("store_structure_id", "data"),
    prevent_initial_call=True,
)
def store_tabs_for_ind_charts_control_log(
    tabs_for_ind_charts_control_log_active_tab, store_structure_id_data
):
    """Store the active tab so it defaults on page refresh, which happens a lot"""
    log_function_caller()

    # If it's an IJACK employee and it's the SHOP gateway, activate the control tab
    if (
        store_structure_id_data == STRUCTURE_ID_SHOP
        # And the ID was triggered by the structure_id changing, not the tab changing
        and get_id_triggered() == "store_structure_id.data"
        and user_is_ijack_employee(user_id=getattr(current_user, "id", None))
    ):
        roles_list = [role.id for role in current_user.roles_rel]
        if ROLE_ID_IJACK_ADMIN in roles_list:
            return TAB_CONTROL

    return tabs_for_ind_charts_control_log_active_tab


@callback(
    Output("store_url_search", "data"),
    Output("tabs_for_nav", "active_tab"),
    Input("url", "search"),
    State("tabs_for_nav", "active_tab"),
    State("store_url_search", "data"),
    prevent_initial_call=True,
)
def update_store_url_search(
    url_search,
    tabs_for_nav_active_tab,
    store_url_search_data,
):
    """Update the structure selected (can be from the map, or the list)"""

    def null_return():
        """
        What to return when there's an error.
        Can't use no_update here, or the list won't populate!
        """
        return {}, tabs_for_nav_active_tab

    if not url_search:
        return null_return()
    try:
        structure_obj = get_structure_obj_from_url_search(url_search)
    except Exception:
        return null_return()

    if not structure_obj:
        return null_return()

    try:
        store_url_search_data["structure_id"] = structure_obj.id
        store_url_search_data["customer_id"] = structure_obj.customer_id
        store_url_search_data["warehouse_id"] = structure_obj.warehouse_id
        store_url_search_data["unit_type_id"] = structure_obj.unit_type_id
        store_url_search_data["has_rcom"] = bool(structure_obj.gateway)
        store_url_search_data["cust_sub_group_id"] = (
            structure_obj.cust_sub_group_id or CUST_SUB_GROUP_ID_ALL_OTHERS
        )
    except Exception:
        # Have to return something or everything downstream stops!
        return null_return()
    else:
        # We don't want to open the "all data" tab if we're trying to filter for one unit
        tabs_for_nav_active_tab = TAB_LIST_FILTERS

    return store_url_search_data, tabs_for_nav_active_tab


@callback(
    Output("map_tab_contents", "style"),
    Output("list_div", "style"),
    Output("status_div", "style"),
    Input("tabs_for_nav", "active_tab"),
    # Input("store_url_search", "data"),
    # First thing we do is decide whether to show the map or list,
    # but if we just default to the list, do we need to run this callback right away?
    # YES, because what if we're on the map view, and click an email link like
    # http://app.localhost:5000/rcom/ ? Try it; it doesn't work.
    prevent_initial_call=False,
)
def display_lists_or_map_or_all_units(active_tab):
    """Hide all the divs except the one activated by the active_tab"""
    log_function_caller()

    if active_tab is None:
        raise PreventUpdate()

    # id_triggered: str = get_id_triggered()
    # if url_search and id_triggered == "url.search":
    #     # if url_search and id_triggered == "store_url_search.data":
    #     active_tab = TAB_LIST_FILTERS

    # Remove the {'display': 'none'} style if the tab is active
    style_map: dict = {"display": "none"}
    style_list: dict = {"display": "none"}
    style_all_units: dict = {"display": "none"}
    if active_tab == TAB_MAP:
        style_map = {}
    elif active_tab == TAB_LIST_FILTERS:
        style_list = {}
    elif active_tab == TAB_STATUS:
        style_all_units = {}

    return style_map, style_list, style_all_units


@callback(
    Output("map_filters_ijack_only", "style"),
    Input("map_tab_contents", "style"),
)
def show_ijack_only_filters(map_tab_contents_style):
    """Show the IJACK-only filters if the user is an IJACK employee"""
    log_function_caller()

    if user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
        return {}

    return {"display": "none"}


@callback(
    Output("charts_div_normal", "style"),
    Output("charts_div_rt", "style"),
    Output("rt_chart_options_div", "style"),
    Input("tabs_for_normal_or_rt_charts", "active_tab"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("store_structure_id", "data"),
    # Run this at startup to show the right chart, depending on the persisted/stored tab
    prevent_initial_call=False,
)
def display_normal_or_real_time_charts(
    tabs_for_normal_or_rt_charts_active_tab,
    store_tabs_for_ind_charts_control_log_data,
    store_structure_id_data,
):
    """Hide all the divs except the one activated by the active_tab"""
    log_function_caller()

    if (
        tabs_for_normal_or_rt_charts_active_tab is None
        or store_tabs_for_ind_charts_control_log_data != TAB_CHARTS
    ):
        raise PreventUpdate()

    # Remove the {'display': 'none'} style if the tab is active
    if tabs_for_normal_or_rt_charts_active_tab == ACTIVE_TAB_NORMAL_CHARTS:
        style_normal_charts = {}
        style_rt_charts = {"display": "none"}
        style_rt_chart_options = {"display": "none"}
    else:
        style_normal_charts = {"display": "none"}
        style_rt_charts = {}
        style_rt_chart_options = {}

    return style_normal_charts, style_rt_charts, style_rt_chart_options


@callback(
    Output("indicators_div", "style"),
    Output("main_charts_parent_div", "style"),
    Output("cards_div", "style"),
    Output("control_div", "style"),
    Output("alarm_log_div", "style"),
    # Output("diag_data_request_hist_collapse", "is_open", allow_duplicate=True),
    # Output("diag_card_div", "style"),
    Output("performance_div", "style"),
    Output("health_div", "style"),
    Output("inventory_div", "style"),
    Output("control_tab_refresh_interval", "disabled"),
    Output("control_tab_refresh_interval", "n_intervals", allow_duplicate=True),
    Output("rt_charts_refresh_interval", "disabled", allow_duplicate=True),
    Output("rt_charts_refresh_interval", "n_intervals"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    # Include "store_structure_id" as trigger for which tab is open,
    # since sometimes it doesn't update appropriately
    Input("store_structure_id", "data"),
    State("tabs_for_normal_or_rt_charts", "active_tab"),
    State("store_chart_category", "data"),
    State("rt_data_manual_refresh_switch", "value"),
    # Set one of these divs as the default, and don't hide it initially,
    # and then we don't have to run this callback at startup
    prevent_initial_call=True,
)
def update_charts_tab_div_display_style(
    store_tabs_for_ind_charts_control_log_data,
    store_structure_id_data,
    tabs_for_normal_or_rt_charts_active_tab,
    store_chart_category_data,
    rt_data_manual_refresh_switch_value,
):
    """Hide all the divs except the one activated by the active_tab"""
    log_function_caller()

    if store_tabs_for_ind_charts_control_log_data is None:
        raise PreventUpdate()

    # Defaults, to be overridden
    style_indicators = {"display": "none"}
    style_charts = {"display": "none"}
    style_cards = {"display": "none"}
    style_control = {"display": "none"}
    style_log = {"display": "none"}
    # diag_data_request_hist_collapse_is_open = False
    # style_diag_card = {"display": "none"}
    style_performance = {"display": "none"}
    style_health = {"display": "none"}
    style_inventory = {"display": "none"}
    control_tab_refresh_interval_disabled = True
    control_tab_refresh_interval_n_intervals = no_update
    rt_charts_refresh_interval_disabled = True
    rt_charts_refresh_interval_n_intervals = no_update

    # Remove the {'display': 'none'} style if the tab is active
    if store_tabs_for_ind_charts_control_log_data == TAB_INDICATORS:
        style_indicators = {}
    elif store_tabs_for_ind_charts_control_log_data == TAB_CHARTS:
        style_charts = {}
        if (
            tabs_for_normal_or_rt_charts_active_tab == ACTIVE_TAB_RT_CHARTS
            # Don't enable and reset the interval if manual-refresh mode is on
            and rt_data_manual_refresh_switch_value is False
        ):
            # Enable the refresh interval
            rt_charts_refresh_interval_disabled = False
            # Reset the refresh interval
            rt_charts_refresh_interval_n_intervals = 0
        # if store_chart_category_data == "diagnostic":
        #     diag_data_request_hist_collapse_is_open = True
    elif store_tabs_for_ind_charts_control_log_data == TAB_CARDS:
        style_cards = {}
    elif store_tabs_for_ind_charts_control_log_data == TAB_CONTROL:
        style_control = {}
        # Enable the refresh interval
        control_tab_refresh_interval_disabled = False
        # Reset the refresh interval
        control_tab_refresh_interval_n_intervals = 0
    elif store_tabs_for_ind_charts_control_log_data == TAB_LOG:
        style_log = {}
    # elif store_tabs_for_ind_charts_control_log_data == TAB_DIAG_CARDS:
    #     diag_data_request_hist_collapse_is_open = True
    #     style_diag_card = {}
    elif store_tabs_for_ind_charts_control_log_data == TAB_PERFORMANCE:
        style_performance = {}
    elif store_tabs_for_ind_charts_control_log_data == TAB_HEALTH:
        style_health = {}
    elif store_tabs_for_ind_charts_control_log_data == TAB_INVENTORY:
        style_inventory = {}

    return (
        style_indicators,
        style_charts,
        style_cards,
        style_control,
        style_log,
        # diag_data_request_hist_collapse_is_open,
        # style_diag_card,
        style_performance,
        style_health,
        style_inventory,
        control_tab_refresh_interval_disabled,
        control_tab_refresh_interval_n_intervals,
        rt_charts_refresh_interval_disabled,
        rt_charts_refresh_interval_n_intervals,
    )


@callback(
    Output("store_cust_sub_group_id", "data"),
    Input("cust_sub_groups_radio", "value"),
    prevent_initial_call=True,
)
def store_cust_sub_group_id(value):
    """Store the value so we can reload it on browser refresh"""
    log_function_caller()
    if value in (None, CUST_SUB_GROUP_ID_ALL_GROUPS, CUST_SUB_GROUP_ID_ALL_OTHERS):
        raise PreventUpdate()

    return value


@callback(
    Output("store_unit_type_id", "data"),
    Input("unit_type_ids_radio", "value"),
    Input("store_structure_id", "data"),
    prevent_initial_call=True,
)
def store_unit_type_id(unit_type_ids_radio_value: int, store_structure_id_data: int):
    """Store the unit_type_ids_radio_value so we can reload it on browser refresh"""
    log_function_caller()

    id_triggered: str = get_id_triggered()
    if id_triggered == "store_structure_id.data" or unit_type_ids_radio_value in (
        None,
        UNIT_TYPE_ID_ALL_TYPES,
    ):
        user_id: int = getattr(current_user, "id", None)
        structure_obj: StructureVw | None = get_structure_obj(
            store_structure_id_data, user_id
        )
        if structure_obj is None:
            raise PreventUpdate()
        unit_type_ids_radio_value = structure_obj.unit_type_id

    return unit_type_ids_radio_value


@callback(
    Output("store_chart_category", "data"),
    Input("category_to_chart", "value"),
    prevent_initial_call=True,
)
def store_chart_category_on_change(value):
    """Store the value so we can reload it on browser refresh"""
    log_function_caller()
    if value is None:
        raise PreventUpdate()

    return value


@callback(
    Output("row_for_unogas_uno_egas_tabs", "style"),
    # This tab isn't always visible but many controls depend on it
    Output("tabs_for_unogas_uno_egas", "active_tab"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    # This is just the radio button from the list tab
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def update_unogas_uno_egas_tabs(
    store_structure_id_data,
    store_tabs_for_ind_charts_control_log_data,
    store_unit_type_id_data,
):
    """
    If the structure_id represents an UNOGAS type, show the tabs.
    Otherwise make the tabs invisible and just set the 'active_tab'
    So we know which chart to display
    """
    log_function_caller()

    if store_structure_id_data is None:
        raise PreventUpdate()

    # Initialize return values
    row_for_unogas_uno_egas_tabs_style = {"display": "none"}
    # Default tab is UNO
    tabs_for_unogas_uno_egas_active_tab = TAB_UNOGAS_UNO

    def return_variables():
        """Default return variables"""
        return (
            row_for_unogas_uno_egas_tabs_style,
            tabs_for_unogas_uno_egas_active_tab,
        )

    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        # Just for debugging
        id_triggered: str = get_id_triggered()
        current_app.logger.info(f"id_triggered: {id_triggered}")

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()
    unit_type_id = structure_obj.unit_type_id

    if (
        store_tabs_for_ind_charts_control_log_data in (TAB_CONTROL, TAB_CHARTS)
        and unit_type_id == UNIT_TYPE_ID_UNOGAS
    ):
        # Display the second set of tabs so the user can choose UNO or EGAS
        row_for_unogas_uno_egas_tabs_style = {}

    if unit_type_id == UNIT_TYPE_ID_UNOGAS:
        # It has to be either UNO type or EGAS/XFER type so we know which controls to display below
        _, unit_type_id = get_unit_type(
            tabs_for_unogas_uno_egas_active_tab, store_unit_type_id_data
        )

    if unit_type_id in UNIT_TYPES_IDS_XFER_EGAS:
        # Change the active tab to the EGAS/XFER tab
        tabs_for_unogas_uno_egas_active_tab = TAB_UNOGAS_EGAS
    else:
        tabs_for_unogas_uno_egas_active_tab = TAB_UNOGAS_UNO

    return return_variables()


@callback(
    Output("control_start_uno", "style"),
    Output("control_start_egas", "style"),
    Output("control_pressure_egas", "style"),
    Output("control_speed_egas", "style"),
    Output("control_speed_uno", "style"),
    Output("control_auto_uno", "style"),
    Output("control_other_uno", "style"),
    Output("control_on_site_setpoints_row", "style"),
    Output("control_admin_egas_col", "style"),
    Output("control_admin_adv_rem_ctrl", "style"),
    Input("store_structure_id", "data"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    # This is just the radio button from the list tab
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def update_control_div_styles(
    store_structure_id_data,
    tabs_for_unogas_uno_egas_active_tab,
    store_unit_type_id_data,
):
    """
    If the structure_id represents an UNOGAS type, show the tabs.
    Otherwise make the tabs invisible and just set the 'active_tab'
    So we know which chart to display
    """
    log_function_caller()

    if store_structure_id_data is None:
        raise PreventUpdate()

    # Initialize return values
    control_start_uno_style = {"display": "none"}
    control_start_egas_style = {"display": "none"}
    control_pressure_egas_style = {"display": "none"}
    control_speed_egas_style = {"display": "none"}
    control_speed_uno_style = {"display": "none"}
    control_auto_uno_style = {"display": "none"}
    control_other_uno_style = {"display": "none"}
    control_on_site_setpoints_row_style = {"display": "none"}
    control_admin_egas_col_style = {"display": "none"}
    control_admin_adv_rem_ctrl_style = {"display": "none"}

    def return_variables():
        """Default return variables"""
        return (
            control_start_uno_style,
            control_start_egas_style,
            control_pressure_egas_style,
            control_speed_egas_style,
            control_speed_uno_style,
            control_auto_uno_style,
            control_other_uno_style,
            control_on_site_setpoints_row_style,
            control_admin_egas_col_style,
            control_admin_adv_rem_ctrl_style,
        )

    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        # Just for debugging
        id_triggered: str = get_id_triggered()
        current_app.logger.info(f"id_triggered: {id_triggered}")

    user_id: int = getattr(current_user, "id", None)
    is_ijack_employee: bool = user_is_ijack_employee(user_id=user_id)

    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()
    unit_type_id = structure_obj.unit_type_id

    if unit_type_id == UNIT_TYPE_ID_UNOGAS:
        # It has to be either UNO type or EGAS/XFER type so we know which controls to display below
        _, unit_type_id = get_unit_type(
            tabs_for_unogas_uno_egas_active_tab, store_unit_type_id_data
        )

    if unit_type_id in UNIT_TYPES_IDS_XFER_EGAS:
        control_start_egas_style = {}
        control_pressure_egas_style = {}
        control_speed_egas_style = {}
        control_on_site_setpoints_row_style = {}
        # Remove the IJACK customer_id requirement when this goes to production (now it's just testing)
        if is_ijack_employee:
            control_admin_egas_col_style = {}
    else:
        control_start_uno_style = {}
        control_speed_uno_style = {}
        control_auto_uno_style = {}
        control_other_uno_style = {}

    if is_ijack_employee:
        control_admin_adv_rem_ctrl_style = {}

    return return_variables()


@callback(
    Output("store_unogas_active_tab", "data"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    # Input("store_structure_id", "data"),
    # State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def store_unogas_active_tab(
    store_unogas_active_tab_data,
    # store_structure_id_data,
    # store_unit_type_id_data
):
    """Store the value so we can reload it on browser refresh"""
    log_function_caller()

    # id_triggered: str = get_id_triggered()
    # if id_triggered == "store_structure_id.data":
    #     user_id: int = getattr(current_user, "id", None)
    #     structure_obj: StructureVw | None = get_structure_obj(
    #         store_structure_id_data, user_id, store_unit_type_id_data
    #     )
    #     if structure_obj is None:
    #         raise PreventUpdate()
    #     unit_type_id, _ = get_unit_type(
    #         store_unogas_active_tab_data, store_unit_type_id_data
    #     )
    #     if unit_type_id == UNIT_TYPE_ID_UNO:

    if store_unogas_active_tab_data is None:
        raise PreventUpdate()

    return store_unogas_active_tab_data


@callback(
    Output("store_software_version", "data"),
    # ONLY run on page load/refresh, when it changes.
    # Check it periodically with a never-ending interval timer.
    Input("hidden_signal", "children"),
    # Make this False or it'll pop up every single time!
    # Anything that uses "hidden_signal" should run on page load!
    prevent_initial_call=False,
)
def store_software_version(_):
    """
    ONLY store the software version on initial page load,
    so we can check it later with an interval timer,
    to see if it's changed on the server
    """
    log_function_caller()
    # save the server version in the browser when the page first loads (i.e. 'hidden_signal'),
    # so we can refresh the page if this doesn't match the new server version
    return current_app.config.get("VERSION_MYIJACK")


# @callback(
#     Output("toast_msg_flash_top_nav", "is_open"),
#     # Periodically check the software version in the browser
#     # against the (potentially newer) server version
#     Input("swv_refresh_interval", "n_intervals"),
#     # If the software version is updated on initial page load, close the toast window
#     Input("store_software_version", "data"),
#     # Don't run right away on page load (wait for software version to be updated on page refresh)
#     prevent_initial_call=True,
# )
# def display_modal_refresh_needed(_, version_in_browser):
#     """
#     If the software version saved in the browser doesn't match the
#     newest version on the server, display a 'toast' message asking to refresh
#     """
#     # new_server_version = current_app.config.get("VERSION_MYIJACK", None)
#     # if version_in_browser and version_in_browser != new_server_version:
#     #     # Open toast message
#     #     return True

#     # Otherwise keep toast message closed
#     # raise PreventUpdate()
#     return False


@callback(
    Output("modal_message", "is_open", allow_duplicate=True),
    Input("btn_modal_message_cancel", "n_clicks"),
    prevent_initial_call=True,
)
def close_message_modal(_):
    """If the message modal cancel button is clicked, close the modal"""
    return False


@callback(
    Output("btn_rt_data_x_mins", "disabled"),
    Input("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def disable_real_time_tab_for_uno(store_structure_id_data, store_unit_type_id_data):
    """If it's an UNO unit type, disable the real time data tab"""
    log_function_caller()

    if store_unit_type_id_data in (UNIT_TYPE_ID_UNOGAS, UNIT_TYPE_ID_UNO):
        return True

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        return True

    if structure_obj.gateway is None:
        # No RCOM
        return True

    return False


@callback(
    Output("pause_alerts_btn_label", "children", allow_duplicate=True),
    Output("pause_alerts_btn_icon", "className", allow_duplicate=True),
    Output("pause_alerts_btn_column", "style"),
    Input("hidden_signal", "children"),
    prevent_initial_call="initial_duplicate",
)
def setup_alerts_enabled_button_label_on_page_load(_) -> bool:
    """On page load, setup whether the user's alerts are enabled"""

    has_alerts = Alert.query.filter_by(
        user_id=getattr(current_user, "id", None)
    ).first()
    if not has_alerts:
        return no_update, no_update, {"display": "none"}

    pause_alerts_btn_column_style = {}

    user = db.session.get(User, getattr(current_user, "id", None))
    alerts_paused_until_utc = user.alerts_paused_until_utc
    if isinstance(alerts_paused_until_utc, datetime):
        alerts_paused_until_utc = alerts_paused_until_utc.replace(tzinfo=timezone.utc)

    if alerts_paused_until_utc is None or alerts_paused_until_utc < datetime.now(
        timezone.utc
    ):
        return ALERTS_LABEL_PAUSE, "fa fa-pause me-1", pause_alerts_btn_column_style

    return ALERTS_LABEL_ACTIVATE, "fa fa-play me-1", pause_alerts_btn_column_style


@callback(
    Output("alerts_enable_modal", "is_open"),
    Output("pause_alerts_btn_label", "children", allow_duplicate=True),
    Output("pause_alerts_btn_icon", "className", allow_duplicate=True),
    Output("alerts_enable_modal_title", "children"),
    Output("alerts_enable_modal_body", "children"),
    Output("alerts_enable_date_picker_col", "style"),
    Output("alerts_enable_time_picker_row", "style"),
    Input("pause_alerts_btn", "n_clicks"),
    Input("alerts_enable_modal_cancel_btn", "n_clicks"),
    Input("alerts_enable_modal_confirm_btn", "n_clicks"),
    State("pause_alerts_btn_label", "children"),
    State("alerts_enable_date_picker", "value"),
    State("alerts_enable_time_picker", "value"),
    prevent_initial_call="initial_duplicate",
)
def enable_disable_alerts_switch(
    alerts_enable_switch_value,
    alerts_enable_modal_cancel_btn_n_clicks,
    alerts_enable_modal_confirm_btn_n_clicks,
    pause_alerts_btn_label_children,
    alerts_enable_date_picker_date,
    alerts_enable_time_picker_value,
):
    """Enable or disable alerts for a certain amount of time"""
    null_return = (
        False,
        no_update,
        no_update,
        no_update,
        no_update,
        no_update,
        no_update,
    )

    id_triggered: str = get_id_triggered()
    if id_triggered == "alerts_enable_modal_cancel_btn.n_clicks":
        return null_return

    if alerts_enable_date_picker_date is None:
        # What to do if the user hasn't selected a date yet
        raise PreventUpdate()

    btn_label = no_update
    btn_icon = no_update
    if id_triggered == "pause_alerts_btn.n_clicks":
        # Button was pressed, so confirmation modal will open
        is_open = True
        if pause_alerts_btn_label_children == ALERTS_LABEL_PAUSE:
            modal_title = "Pause Alerts"
            modal_body = "Until which date?"
            datepicker_style = {}
        else:
            modal_title = "Reactivate Alerts"
            modal_body = "You are about to re-enable your alerts"
            datepicker_style = {"display": "none"}

    elif id_triggered == "alerts_enable_modal_confirm_btn.n_clicks":
        # Update the database since the user clicked "Confirm"
        user = db.session.get(User, getattr(current_user, "id", None))
        if pause_alerts_btn_label_children == ALERTS_LABEL_PAUSE:
            # User wants alerts paused until this date
            dt_local = datetime.strptime(alerts_enable_date_picker_date, "%Y-%m-%d")
            # Get the hour and minute from the time input
            time_local = datetime.strptime(alerts_enable_time_picker_value, "%H:%M")
            dt_local = dt_local.replace(hour=time_local.hour, minute=time_local.minute)

            # if hasattr(user, "time_zones_rel"):
            #     tz_wanted = user.time_zones_rel.time_zone
            # else:
            #     tz_wanted = "UTC"
            tz_user = pytz.timezone(user.time_zones_rel.time_zone)

            # Convert to UTC time
            dt_utc = tz_user.localize(dt_local, is_dst=None).astimezone(pytz.utc)
            user.alerts_paused_until_utc = dt_utc
            btn_label = ALERTS_LABEL_ACTIVATE
            btn_icon = "fa fa-play me-1"
        else:
            # User wants alerts reactivated
            user.alerts_paused_until_utc = None
            btn_label = ALERTS_LABEL_PAUSE
            btn_icon = "fa fa-pause me-1"
        db.session.commit()

        # The modal will close
        is_open = False
        modal_title = no_update
        modal_body = no_update
        datepicker_style = no_update
    else:
        return null_return

    return (
        is_open,
        btn_label,
        btn_icon,
        modal_title,
        modal_body,
        datepicker_style,
        datepicker_style,
    )
