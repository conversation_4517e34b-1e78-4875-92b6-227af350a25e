import json
import os
from datetime import timedelta
from urllib import parse

import dash_bootstrap_components as dbc
import phonenumbers
from dash import (
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
    no_update,
)
from dash.exceptions import PreventUpdate
from flask import current_app, flash, request, session, url_for
from flask_login import login_user
from shared.models.models import (
    UserAuthenticationChallenge,
    UserRegistrationChallenge,
    UserVerificationCode,
)
from sqlalchemy import func, or_
from twilio.base.exceptions import TwilioRestException
from webauthn import (
    generate_authentication_options,
    generate_registration_options,
    options_to_json,
)
from webauthn.helpers import (
    bytes_to_base64url,
)
from webauthn.helpers.cose import COSEAlgorithmIdentifier
from webauthn.helpers.structs import (
    AttestationConveyancePreference,
    AuthenticatorAttachment,
    AuthenticatorSelectionCriteria,
    PublicKeyCredentialDescriptor,
    PublicKeyCredentialType,
    ResidentKeyRequirement,
    UserVerificationRequirement,
)

from app import (
    db,
    user_is_demo_customer,
)
from app.auth.forms import validate_email, validate_phone
from app.auth.views import get_rp_id_and_origin
from app.config import USER_ID_COULTON, USER_ID_SEAN
from app.dashapp.layout_utils import horizontal_line_with_word
from app.dashapp.utils import get_id_triggered, get_value_from_query_dict
from app.email_stuff import (
    send_confirm_code_email,
)
from app.models.models import User
from app.utils.complex import send_twilio_sms
from shared.utils.string_utils import generate_confirmation_code
from shared.utils.datetime_utils import utcnow_naive


def get_modal():
    """Get a modal for displaying messages"""
    return dbc.Modal(
        id="login_modal",
        is_open=False,
        size="lg",
        centered=True,  # Vertically centered
        children=[
            dbc.ModalHeader(id="login_modal_header"),
            dbc.ModalBody(id="login_modal_body"),
        ],
    )


def get_cookie_warning():
    """Create a cookie warning alert using Dash Bootstrap Components"""
    return dbc.Alert(
        [
            # Put icon and heading on same line using flexbox utilities
            html.Div(
                [
                    html.I(className="fas fa-exclamation-circle my-auto"),
                    html.H4("Cookies Required", className="alert-heading ms-2 mb-0"),
                ],
                className="d-flex align-items-center mb-2",
            ),
            html.P(
                [
                    "Cookies appear to be disabled in your browser. "
                    "Cookies are required to stay signed in.",
                    html.Br(),
                    "Without cookies, you'll have to log in each time you re-open an IJACK browser tab.",
                ],
                className="mb-2",
            ),
            html.Hr(),
            html.P(
                [
                    "Please enable cookies for this site and refresh the page to continue. ",
                    html.A(
                        "Learn how to enable cookies",
                        href="https://www.whatismybrowser.com/guides/how-to-enable-cookies/",
                        target="_blank",
                        rel="noopener noreferrer",
                        className="alert-link",
                    ),
                ],
                className="mb-0",
            ),
        ],
        color="warning",
        id="cookie_warning",
        style={"display": "none"},  # Hidden by default
        className="mb-3",
    )


def get_signals():
    """Signals for login form"""
    return html.Div(
        [
            # Hidden signal value, for running callback on page load
            html.Div(id="hidden_signal_login", style={"display": "none"}),
            dcc.Location(id="location_login", refresh=True),
            # For triggering clientside callbacks
            dcc.Store(id="ready_for_webauthn_registration", data=""),
            dcc.Store(id="ready_for_webauthn_auth", data=""),
            dcc.Store(id="did_auth_succeed", data=False),
            dcc.Store(id="did_auth_succeed_msg", data=""),
            # Only send a new email code every 30 seconds
            dcc.Interval(
                id="interval_login",
                interval=1_000,
                disabled=True,
                n_intervals=0,
                max_intervals=61,
            ),
            dcc.Store(id="cookies_enabled", data=False),
        ]
    )


def get_login_form(has_app_context: bool = True):
    """Login form"""

    if has_app_context:
        register_url: str = url_for("dash.register")
    else:
        register_url: str = "/register/"

    return html.Div(
        [
            # Add cookie warning at the top
            get_cookie_warning(),
            dbc.Row(
                justify="center",
                children=dbc.Col(
                    [
                        html.H1(
                            "Sign in to IJACK",
                            className="text-center",
                            style={
                                "font-weight": "bold",
                                "font-size": "2em",
                            },
                        ),
                        html.P(
                            className="text-center mt-3 mb-2",
                            children=[
                                "No account? Register ",
                                html.A(
                                    "here",
                                    href=register_url,
                                    style={"color": "blue"},
                                ),
                            ],
                        ),
                        html.P(
                            className="text-center mt-2 mb-3",
                            children=[
                                "Unsure what to do? Check out the ",
                                html.A(
                                    "login tutorial here",
                                    id="login_tutorial_link",
                                    # A callback will update the href
                                    href=None,
                                    style={"color": "blue"},
                                    # Open the link in a new tab
                                    target="_blank",
                                ),
                            ],
                        ),
                    ],
                    width=12,
                ),
            ),
            dbc.Row(
                justify="center",
                children=dbc.Col(
                    xs=12,
                    sm=10,
                    md=8,
                    lg=7,
                    xl=5,
                    children=[
                        dbc.Card(
                            dbc.CardBody(
                                [
                                    dbc.Row(
                                        justify="center",
                                        children=dbc.Col(
                                            dbc.Form(
                                                [
                                                    dbc.Collapse(
                                                        id="email_option_collapse",
                                                        is_open=True,
                                                        children=[
                                                            # html.Hr(className="my-4"),
                                                            dbc.Label(
                                                                "We need either an email or phone number:",
                                                                class_name="d-flex justify-content-center",
                                                            ),
                                                            # Tabs for Email and SMS options
                                                            dbc.Tabs(
                                                                id="email_sms_tabs",
                                                                active_tab="email_tab",
                                                                # Center the tabs
                                                                class_name="d-flex justify-content-center",
                                                                children=[
                                                                    # Email Tab
                                                                    dbc.Tab(
                                                                        label="Email",
                                                                        tab_id="email_tab",
                                                                        children=[
                                                                            # dbc.InputGroup(
                                                                            #     class_name="my-1",
                                                                            #     children=[
                                                                            dbc.Input(
                                                                                type="email",
                                                                                id="email",
                                                                                placeholder="Email",
                                                                                required=False,
                                                                                persistence=True,
                                                                                class_name="my-2",
                                                                            ),
                                                                            #     ],
                                                                            # ),
                                                                        ],
                                                                    ),
                                                                    # SMS Tab
                                                                    dbc.Tab(
                                                                        label="SMS",
                                                                        tab_id="sms_tab",
                                                                        children=[
                                                                            # dbc.InputGroup(
                                                                            #     class_name="my-1",
                                                                            #     children=[
                                                                            dbc.Input(
                                                                                type="phone",
                                                                                id="phone",
                                                                                placeholder="Phone number",
                                                                                required=False,
                                                                                persistence=True,
                                                                                class_name="my-2",
                                                                            ),
                                                                            # horizontal_line_with_word(
                                                                            #     "Then"
                                                                            # ),
                                                                            # html.Div(
                                                                            #     # Center the button
                                                                            #     className="d-flex justify-content-center",
                                                                            #     children=dbc.Button(
                                                                            #         [
                                                                            #             html.I(
                                                                            #                 className="fa-solid fa-comment-sms me-2"
                                                                            #             ),
                                                                            #             html.Span(
                                                                            #                 "Get code",
                                                                            #                 id="get_code_in_sms_btn_text",
                                                                            #             ),
                                                                            #         ],
                                                                            #         id="get_code_in_sms_btn",
                                                                            #         color="primary",
                                                                            #     ),
                                                                            # ),
                                                                            #     ],
                                                                            # ),
                                                                        ],
                                                                    ),
                                                                ],
                                                            ),
                                                            dbc.Switch(
                                                                id="remember_me",
                                                                label="Keep me signed in for one year on this device",
                                                                value=True,
                                                                # Align this switch in the center of the div
                                                                class_name="d-flex justify-content-center my-2",
                                                                label_class_name="ms-2",
                                                            ),
                                                            horizontal_line_with_word(
                                                                "Then"
                                                            ),
                                                            dbc.Label(
                                                                "If you are new to IJACK or you don't like PassKeys...",
                                                                class_name="d-flex justify-content-center",
                                                            ),
                                                            html.Div(
                                                                className="d-grid gap-2",
                                                                children=dbc.Button(
                                                                    [
                                                                        html.I(
                                                                            id="get_code_btn_icon",
                                                                            className="fa-regular fa-envelope",
                                                                        ),
                                                                        html.Span(
                                                                            "Get code",
                                                                            id="get_code_btn_text",
                                                                            className="ms-2",
                                                                        ),
                                                                    ],
                                                                    id="get_code_btn",
                                                                    color="primary",
                                                                ),
                                                            ),
                                                            dbc.FormText(
                                                                id="get_code_form_text",
                                                                class_name="my-1 d-flex justify-content-center",
                                                                color="secondary",
                                                            ),
                                                            dcc.Store("user_id_store"),
                                                        ],
                                                    ),
                                                    dbc.Collapse(
                                                        id="verification_code_collapse",
                                                        is_open=False,
                                                        children=[
                                                            horizontal_line_with_word(
                                                                "Now"
                                                            ),
                                                            dbc.Input(
                                                                type="text",
                                                                id="verification_code",
                                                                placeholder="Enter the code you received",
                                                                class_name="my-1",
                                                                # Highlight the code input so the user knows it's important
                                                                style={
                                                                    "box-shadow": "0 0 20px yellow"
                                                                },
                                                            ),
                                                            html.Div(
                                                                className="d-grid gap-2",
                                                                children=dbc.Button(
                                                                    "Verify code",
                                                                    id="verify_code_btn",
                                                                    color="primary",
                                                                    class_name="my-1",
                                                                ),
                                                            ),
                                                            # Hidden by default, shown in callback
                                                            dbc.FormText(
                                                                id="verify_code_btn_form_text",
                                                                class_name="my-1",
                                                                color="secondary",
                                                            ),
                                                        ],
                                                    ),
                                                    # Create PassKey or just sign in
                                                    dbc.Collapse(
                                                        id="create_passkey_or_sign_in_collapse",
                                                        is_open=False,
                                                        children=[
                                                            horizontal_line_with_word(
                                                                "Now"
                                                            ),
                                                            dbc.Label(
                                                                "Either...",
                                                                class_name="d-flex justify-content-center",
                                                            ),
                                                            dbc.Row(
                                                                [
                                                                    dbc.Col(
                                                                        md=6,
                                                                        class_name="d-grid gap-2",
                                                                        children=dbc.Button(
                                                                            [
                                                                                html.I(
                                                                                    className="fa-solid fa-key me-2"
                                                                                ),
                                                                                html.Span(
                                                                                    "Create PassKey"
                                                                                ),
                                                                            ],
                                                                            id="create_passkey_btn",
                                                                            color="primary",
                                                                            class_name="my-1",
                                                                        ),
                                                                    ),
                                                                    dbc.Col(
                                                                        md=6,
                                                                        class_name="d-grid gap-2",
                                                                        children=[
                                                                            dbc.Button(
                                                                                [
                                                                                    html.I(
                                                                                        className="fa-solid fa-arrow-right me-2"
                                                                                    ),
                                                                                    html.Span(
                                                                                        "Just sign in, no PassKey"
                                                                                    ),
                                                                                ],
                                                                                id="sign_in_no_passkey_btn",
                                                                                color="secondary",
                                                                                class_name="my-1",
                                                                            ),
                                                                        ],
                                                                    ),
                                                                ],
                                                            ),
                                                        ],
                                                    ),
                                                    # Passkey option
                                                    html.Div(
                                                        [
                                                            horizontal_line_with_word(),
                                                            html.Noscript(
                                                                "To login using passkeys, ensure that JavaScript is enabled in your browser"
                                                            ),
                                                            dbc.Label(
                                                                "If you already have a PassKey...",
                                                                class_name="d-flex justify-content-center",
                                                            ),
                                                            html.Div(
                                                                dbc.Button(
                                                                    [
                                                                        html.I(
                                                                            className="fa-solid fa-key me-2"
                                                                        ),
                                                                        html.Span(
                                                                            "Sign in with your PassKey"
                                                                        ),
                                                                    ],
                                                                    id="login_w_passkey_btn",
                                                                    color="primary",
                                                                    class_name="my-1",
                                                                ),
                                                                className="d-grid gap-2",
                                                            ),
                                                            # Hidden by default, shown in callback
                                                            dbc.FormText(
                                                                id="login_w_passkey_btn_form_text",
                                                                class_name="my-1",
                                                                color="secondary",
                                                            ),
                                                        ]
                                                    ),
                                                    # OAuth options
                                                    dbc.Collapse(
                                                        id="oauth_option_collapse",
                                                        is_open=True,
                                                        children=[
                                                            horizontal_line_with_word(),
                                                            dbc.Label(
                                                                "If you prefer...",
                                                                class_name="d-flex justify-content-center",
                                                            ),
                                                            html.Div(
                                                                html.A(
                                                                    # Initially prevent direct navigation to the URL
                                                                    href="#",
                                                                    id="login_w_microsoft",
                                                                    className="btn btn-info my-1",
                                                                    children=[
                                                                        html.I(
                                                                            className="fa-brands fa-windows me-2"
                                                                        ),
                                                                        html.Span(
                                                                            "Sign in with Microsoft"
                                                                        ),
                                                                    ],
                                                                ),
                                                                className="d-grid gap-2",
                                                            ),
                                                        ],
                                                    ),
                                                ],
                                                id="login-form",
                                            ),
                                        ),
                                    ),
                                ]
                            ),
                        ),
                    ],
                ),
            ),
        ],
    )


def login_layout(has_app_context: bool = True):
    """Layout for login form"""

    # The actual layout starts here
    layout = dbc.Container(
        [
            # Hidden signal value, for running callback on page load
            get_signals(),
            get_modal(),
            get_login_form(has_app_context=has_app_context),
        ]
    )

    return layout


# Add clientside callback to check if cookies are enabled
clientside_callback(
    """
    function(n_intervals) {
        // Try to set a test cookie
        try {
            document.cookie = "cookietest=1";
            var cookieEnabled = document.cookie.indexOf("cookietest=") !== -1;
            
            // Clean up the test cookie
            document.cookie = "cookietest=1; expires=Thu, 01-Jan-1970 00:00:01 GMT";
            
            return cookieEnabled;
        } catch (e) {
            return false;
        }
    }
    """,
    Output("cookies_enabled", "data"),
    Input("hidden_signal_login", "children"),
    prevent_initial_call=False,
)


@callback(
    # Update cookie warning visibility
    Output("cookie_warning", "style"),
    Input("cookies_enabled", "data"),
    prevent_initial_call=True,
)
def update_cookie_warning_visibility(cookies_enabled):
    """Show/hide cookie warning and disable form based on cookie status"""
    if cookies_enabled:
        return {"display": "none"}
    # Show the cookie warning
    return {}


@callback(
    Output("login_tutorial_link", "href"),
    Input("hidden_signal_login", "children"),
    prevent_initial_call=False,
)
def populate_login_tutorial_link(hidden_signal_login):
    """Populate the login tutorial link"""
    return url_for("home.login_steps")


@callback(
    Output("get_code_btn_icon", "className"),
    Input("email_sms_tabs", "active_tab"),
    prevent_initial_call=False,
)
def change_get_code_btn_icon(email_sms_tabs_active_tab):
    """Change the icon on the 'Get code' button"""
    if email_sms_tabs_active_tab == "email_tab":
        return "fa-regular fa-envelope"
    elif email_sms_tabs_active_tab == "sms_tab":
        return "fa-solid fa-comment-sms"
    raise PreventUpdate()


@callback(
    Output("login_modal", "is_open", allow_duplicate=True),
    Output("login_modal_body", "children", allow_duplicate=True),
    Output("get_code_form_text", "children", allow_duplicate=True),
    Output("get_code_form_text", "color"),
    Output("verification_code_collapse", "is_open", allow_duplicate=True),
    Output("verification_code", "value"),
    # Output("get_code_btn", "disabled", allow_duplicate=True),
    # Output("get_code_in_sms_btn", "disabled"),
    Output("interval_login", "disabled", allow_duplicate=True),
    Output("interval_login", "n_intervals"),
    Output("user_id_store", "data", allow_duplicate=True),
    Input("get_code_btn", "n_clicks"),
    # Input("get_code_in_sms_btn", "n_clicks"),
    State("email_sms_tabs", "active_tab"),
    State("email", "value"),
    State("phone", "value"),
    prevent_initial_call=True,
)
def send_code_to_email(
    get_code_btn_n_clicks,
    email_sms_tabs_active_tab,
    # get_code_in_sms_btn_n_clicks,
    email_value,
    phone_value,
):
    """Send the code to the email address"""
    if not get_code_btn_n_clicks:
        raise PreventUpdate()

    def return_vars(
        login_modal_is_open: bool = True,
        login_modal_body_children: str = no_update,
        get_code_form_text_children: str = no_update,
        get_code_form_text_color: str = no_update,
        verification_code_collapse_is_open: bool = False,
        verification_code_value: str = no_update,
        # get_code_btn_disabled: bool,
        interval_login_disabled: bool = True,
        interval_login_n_intervals: int = no_update,
        user_id_store_data: str = None,
    ):
        """Return the variables"""
        return (
            login_modal_is_open,
            login_modal_body_children,
            get_code_form_text_children,
            get_code_form_text_color,
            verification_code_collapse_is_open,
            verification_code_value,
            # get_code_btn_disabled,
            interval_login_disabled,
            interval_login_n_intervals,
            user_id_store_data,
        )

    filter_ = None
    email_str: str = ""
    phone_e164: str = ""
    phone_international: str = ""
    if email_sms_tabs_active_tab == "email_tab":
        try:
            email_str = str(email_value).strip().lower()
            validate_email(form=None, field=email_str, must_be_unique=False)
            filter_ = func.lower(User.email) == email_str
        except Exception:
            return return_vars(
                login_modal_body_children="Invalid email address. Please try again.",
                get_code_form_text_children="Invalid email address",
                get_code_form_text_color="danger",
                verification_code_collapse_is_open=False,
                verification_code_value=None,
                # get_code_btn_disabled=False,
            )
    elif email_sms_tabs_active_tab == "sms_tab":
        try:
            phone_parsed = validate_phone(
                form=None, field=phone_value, must_be_unique=False
            )
            phone_e164 = phonenumbers.format_number(
                phone_parsed, phonenumbers.PhoneNumberFormat.E164
            )
            phone_international = phonenumbers.format_number(
                phone_parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
            )
            filter_ = User.phone == phone_e164
        except Exception:
            return return_vars(
                login_modal_body_children="Invalid phone number. Please try again.",
                get_code_form_text_children="Invalid phone number",
                get_code_form_text_color="danger",
                verification_code_collapse_is_open=False,
                verification_code_value=None,
                # get_code_in_sms_btn_disabled=False,
            )
    else:
        raise PreventUpdate()

    user_model: str = (
        User.query.with_entities(User.email)
        .filter(filter_)
        .with_entities(User.id, User.email, User.first_name, User.is_us_phone)
    ).first()
    if not user_model:
        return return_vars(
            login_modal_body_children="User not found. Please try again or register for an account.",
            get_code_form_text_children="User not found",
            get_code_form_text_color="danger",
            verification_code_collapse_is_open=False,
            verification_code_value=None,
            # get_code_btn_disabled=False,
        )

    code: int = generate_confirmation_code()

    # Save the code in the database with an expiration time (more durable than the Redis cache, which restarts sometimes)
    user_code_model = UserVerificationCode.query.filter_by(
        user_id=user_model.id
    ).first()
    if not user_code_model:
        user_code_model = UserVerificationCode(user_id=user_model.id)
        db.session.add(user_code_model)
    user_code_model.code = code
    user_code_model.code_sent_at_utc = utcnow_naive()
    user_code_model.code_expiry_utc = utcnow_naive() + timedelta(minutes=60)
    db.session.commit()

    if (
        # WARNING: If we allow this in production, then anyone who types in Sean's email
        # will be able to log in!
        os.getenv("FLASK_CONFIG", "production") in ("development", "wsl")
        and (user_model.id == USER_ID_SEAN or user_model.id == USER_ID_COULTON)
    ):
        # For convenience in development (note the code is a string after it's saved in Dash state)
        verification_code_value = str(code)
        login_modal_body_children = f"Verification code: {code}"
        if email_sms_tabs_active_tab == "sms_tab":
            get_code_form_text_children = "Code sent to your phone"
        else:
            get_code_form_text_children = "Code sent to your email"
    else:
        verification_code_value = None
        if email_sms_tabs_active_tab == "sms_tab":
            try:
                send_twilio_sms(
                    [phone_e164],
                    body=f"Your IJACK verification code is {code}",
                    is_us_phone=user_model.is_us_phone,
                )
            except TwilioRestException as err:
                if "Attempt to send to unsubscribed recipient" in str(err):
                    msg = f"We cannot send a text message to {phone_international} since the user previously unsubscribed to all SMS messages (i.e. replied 'STOP' at some point). Please contact IJACK to re-enable SMS, or use the email option instead."
                else:
                    msg = "An error occurred when trying to send the SMS. Please try the email option instead."
                return return_vars(
                    login_modal_body_children=msg,
                    get_code_form_text_children="An error occurred",
                    get_code_form_text_color="danger",
                    verification_code_collapse_is_open=False,
                    verification_code_value=None,
                    # get_code_in_sms_btn_disabled=True,
                )
            login_modal_body_children = f"We sent a text message to {phone_international} with a code to verify your phone number."
            get_code_form_text_children = (
                "Code sent to your phone (please check your spam folder)"
            )
        else:
            send_confirm_code_email(code, email_str, user_model.first_name)
            login_modal_body_children = f"We sent an email to {email_str} with a code to verify your email address."
            get_code_form_text_children = (
                "Code sent to your email (please check your spam folder)"
            )

    return return_vars(
        login_modal_body_children=login_modal_body_children,
        get_code_form_text_children=get_code_form_text_children,
        get_code_form_text_color="success",
        verification_code_collapse_is_open=True,
        verification_code_value=verification_code_value,
        interval_login_disabled=False,
        interval_login_n_intervals=0,
        user_id_store_data=user_model.id,
    )


@callback(
    Output("get_code_btn", "disabled"),
    # Output("get_code_in_sms_btn", "disabled"),
    Output("get_code_btn_text", "children"),
    # Output("get_code_in_sms_btn_text", "children"),
    Output("interval_login", "disabled", allow_duplicate=True),
    Input("interval_login", "n_intervals"),
    prevent_initial_call=True,
)
def enable_get_code_btn(interval_login_n_intervals):
    """Enable the 'Get code in email' button after X seconds"""
    if not interval_login_n_intervals:
        raise PreventUpdate()

    msg: str = "Get code"
    is_disabled: bool = False
    if interval_login_n_intervals < 30:
        is_disabled = True
        seconds_remaining = 30 - interval_login_n_intervals
        msg = f"Try again in {seconds_remaining}s"

    return is_disabled, msg, not is_disabled


@callback(
    Output("login_modal", "is_open", allow_duplicate=True),
    Output("login_modal_body", "children", allow_duplicate=True),
    Output("verify_code_btn_form_text", "children"),
    Output("verify_code_btn_form_text", "color"),
    Output("verification_code", "disabled"),
    Output("email_option_collapse", "is_open"),
    Output("verification_code_collapse", "is_open", allow_duplicate=True),
    Output("create_passkey_or_sign_in_collapse", "is_open"),
    Input("verify_code_btn", "n_clicks"),
    State("verification_code", "value"),
    # State("email", "value"),
    State("user_id_store", "data"),
    prevent_initial_call=True,
)
def verify_code(
    verify_code_btn_n_clicks,
    verification_code,
    # email,
    user_id_store_data,
):
    """Verify the code received in the email or SMS"""
    if not verify_code_btn_n_clicks or not verification_code:
        raise PreventUpdate()

    def return_vars(
        login_modal_is_open: bool = True,
        login_modal_body_children: str = no_update,
        # verify_code_btn_form_text_children: str,
        verify_code_btn_form_text_color: str = no_update,
        verification_code_disabled: bool = False,
        email_option_collapse_is_open: bool = True,
        verification_code_collapse_is_open: bool = True,
        create_passkey_or_sign_in_collapse_is_open: bool = False,
    ):
        """Return the variables"""
        return (
            login_modal_is_open,
            login_modal_body_children,
            login_modal_body_children,
            verify_code_btn_form_text_color,
            verification_code_disabled,
            email_option_collapse_is_open,
            verification_code_collapse_is_open,
            create_passkey_or_sign_in_collapse_is_open,
        )

    user_code_model = UserVerificationCode.query.filter_by(
        user_id=user_id_store_data
    ).first()
    # The verification code state value is a string
    if str(verification_code) != str(user_code_model.code):
        return return_vars(
            login_modal_body_children="Invalid code. Please try again.",
            verify_code_btn_form_text_color="danger",
            verification_code_disabled=False,
        )
    else:
        msg_body = [
            html.P("You have confirmed your identity. Thanks!", className="mb-3"),
            html.P(
                "Please create a PassKey for security and convenience, or click the 'Just sign in' button, in which case you'll have to verify your email again next time you sign in."
            ),
        ]
        user = User.query.get(user_id_store_data)
        if not user:
            return return_vars(
                login_modal_body_children="User not found. Please try again or register for an account.",
                verify_code_btn_form_text_color="danger",
                verification_code_disabled=False,
            )

    # The user has been found, so confirm their email address
    if not getattr(user, "is_confirmed", False):
        user.is_confirmed = True
        user.confirmed_at = utcnow_naive()
        db.session.commit()

    return return_vars(
        login_modal_body_children=msg_body,
        verify_code_btn_form_text_color="success",
        verification_code_disabled=True,
        email_option_collapse_is_open=False,
        verification_code_collapse_is_open=False,
        create_passkey_or_sign_in_collapse_is_open=True,
    )


@callback(
    Output("ready_for_webauthn_registration", "data"),
    Input("create_passkey_btn", "n_clicks"),
    State("email", "value"),
    prevent_initial_call=True,
)
def start_webauthn_registration(create_passkey_btn_n_clicks, email):
    """Start the WebAuthn registration process"""
    if not create_passkey_btn_n_clicks:
        raise PreventUpdate()

    def return_vars(
        ready_for_webauthn_registration_data: str,
    ):
        """Default return variables"""
        return (ready_for_webauthn_registration_data,)

    user = User.query.filter_by(email=email).first()
    rp_name = "IJACK Technologies Inc"
    if os.getenv("FLASK_CONFIG") in ("development", "wsl"):
        rp_id = "app.localhost"
    else:
        rp_id = "myijack.com"
    try:
        public_credential_creation_options = generate_registration_options(
            rp_id=rp_id,
            rp_name=rp_name,
            # Convert the user ID to bytes
            # user_id=f"{user.id}".encode("utf-8"),
            user_id=user.uuid.bytes,
            user_name=email,
            user_display_name=f"{user.first_name} {user.last_name}",
            attestation=AttestationConveyancePreference.DIRECT,
            authenticator_selection=AuthenticatorSelectionCriteria(
                user_verification=UserVerificationRequirement.REQUIRED,
                authenticator_attachment=AuthenticatorAttachment.PLATFORM,
                resident_key=ResidentKeyRequirement.REQUIRED,
            ),
            # challenge=bytes([1, 2, 3, 4, 5, 6, 7, 8, 9, 0]),
            exclude_credentials=[
                PublicKeyCredentialDescriptor(
                    id=cred.credential_id,
                    type=PublicKeyCredentialType.PUBLIC_KEY,
                    transports=cred.transports,
                )
                for cred in user.webauthn_credentials_rel
            ],
            supported_pub_key_algs=[
                COSEAlgorithmIdentifier.ECDSA_SHA_512,
                COSEAlgorithmIdentifier.ECDSA_SHA_256,
                COSEAlgorithmIdentifier.RSASSA_PKCS1_v1_5_SHA_256,
            ],
            timeout=12000,
        )
    except Exception as err:
        return return_vars(
            ready_for_webauthn_registration_data={
                "verified": False,
                "msg": str(err),
                "status": 400,
            },
        )

    # Save the challenge in the database (more durable than the Redis cache, which restarts sometimes)
    reg_challenge_model = UserRegistrationChallenge.query.filter_by(
        user_id=user.id
    ).first()
    if not reg_challenge_model:
        reg_challenge_model = UserRegistrationChallenge(user_id=user.id)
        db.session.add(reg_challenge_model)
    reg_challenge_model.challenge = public_credential_creation_options.challenge
    reg_challenge_model.challenge_sent_at_utc = utcnow_naive()
    reg_challenge_model.challenge_expiry_utc = utcnow_naive() + timedelta(minutes=60)
    db.session.commit()

    # return options_to_json(public_credential_creation_options)

    # return redirect(url_for("dash.login"))
    # return return_vars(
    #     options_to_json(public_credential_creation_options),
    #     msg,
    #     "success",
    #     True,
    # )
    json_options: str = options_to_json(public_credential_creation_options)
    return return_vars(
        ready_for_webauthn_registration_data=json_options,
    )


# Run after_code_verified
# This is in app/dashapp/assets/login.js
clientside_callback(
    ClientsideFunction(
        namespace="login_namespace",
        function_name="after_code_verified",
    ),
    Output("login_modal_body", "children", allow_duplicate=True),
    Output("login_modal", "is_open", allow_duplicate=True),
    Input("ready_for_webauthn_registration", "data"),
    prevent_initial_call=True,
)


@callback(
    Output("login_modal", "is_open", allow_duplicate=True),
    Output("login_modal_body", "children", allow_duplicate=True),
    Output("get_code_form_text", "children", allow_duplicate=True),
    Output("ready_for_webauthn_auth", "data"),
    Output("user_id_store", "data", allow_duplicate=True),
    Input("login_w_passkey_btn", "n_clicks"),
    State("email", "value"),
    State("phone", "value"),
    prevent_initial_call=True,
)
def prepare_for_authentication_with_passkey(
    login_w_passkey_btn_n_clicks, email_value, phone_value
):
    """Close the login modal and initiate the WebAuthn authentication process"""
    if not login_w_passkey_btn_n_clicks:
        raise PreventUpdate()

    def return_vars(
        login_modal_is_open: bool = False,
        login_modal_body_children: str = "Please sign in with your passkey.",
        get_code_form_text_children: str = "",
        ready_for_webauthn_auth_data: str = no_update,
        user_id_store_data: str = None,
    ):
        """Return the variables"""
        return (
            login_modal_is_open,
            login_modal_body_children,
            get_code_form_text_children,
            ready_for_webauthn_auth_data,
            user_id_store_data,
        )

    if not email_value and not phone_value:
        return return_vars(
            login_modal_is_open=True,
            login_modal_body_children="Please enter your email address or phone number so we can find your data.",
        )

    email = validate_email(
        form=None, field=email_value, must_be_unique=False, raise_error=False
    )
    phone_parsed = validate_phone(
        form=None, field=phone_value, must_be_unique=False, raise_error=False
    )
    if not email and not phone_parsed:
        return return_vars(
            login_modal_is_open=True,
            login_modal_body_children="Please enter a valid email address or phone number.",
        )

    filters: list = []
    if email:
        filters.append(func.lower(User.email) == email)
    if isinstance(phone_parsed, phonenumbers.PhoneNumber):
        phone_e164 = phonenumbers.format_number(
            phone_parsed, phonenumbers.PhoneNumberFormat.E164
        )
        # phone_international = phonenumbers.format_number(
        #     phone_parsed, phonenumbers.PhoneNumberFormat.INTERATIONAL
        # )
        filters.append(User.phone == phone_e164)

    user_model = User.query.filter(or_(*filters)).first()
    if not user_model:
        # print(phone_international)
        return return_vars(
            login_modal_is_open=True,
            login_modal_body_children=f"User with email {email} and phone {phone_parsed} not found. Please try again or register for an account.",
        )

    expected_rp_id, _ = get_rp_id_and_origin()
    authentication_options = generate_authentication_options(
        rp_id=expected_rp_id,
        challenge=None,
        timeout=12000,
        # allow_credentials=[PublicKeyCredentialDescriptor(id=b"**********")],
        allow_credentials=[
            PublicKeyCredentialDescriptor(
                type=PublicKeyCredentialType.PUBLIC_KEY,
                id=cred.credential_id,
                transports=cred.transports,
            )
            for cred in user_model.webauthn_credentials_rel
        ],
        user_verification=UserVerificationRequirement.REQUIRED,
    )

    # Log the challenge we're storing
    challenge_bytes: bytes = authentication_options.challenge
    challenge_b64: str = bytes_to_base64url(challenge_bytes)
    current_app.logger.info("Generated challenge (b64): %s", challenge_b64)

    # Save the challenge in the database (more durable than the Redis cache, which restarts sometimes)
    auth_challenge_model = UserAuthenticationChallenge.query.filter_by(
        user_id=user_model.id
    ).first()
    if not auth_challenge_model:
        auth_challenge_model = UserAuthenticationChallenge(user_id=user_model.id)
        db.session.add(auth_challenge_model)

    auth_challenge_model.challenge = challenge_bytes
    auth_challenge_model.challenge_sent_at_utc = utcnow_naive()
    auth_challenge_model.challenge_expiry_utc = utcnow_naive() + timedelta(minutes=60)
    db.session.commit()

    # The user is now confirming which PassKey to use
    return return_vars(
        login_modal_is_open=False,
        login_modal_body_children=no_update,
        ready_for_webauthn_auth_data=options_to_json(authentication_options),
        user_id_store_data=user_model.id,
    )


clientside_callback(
    ClientsideFunction(
        namespace="login_namespace",
        function_name="sign_in_w_passkey",
    ),
    Output("did_auth_succeed", "data"),
    Output("did_auth_succeed_msg", "data"),
    Input("ready_for_webauthn_auth", "data"),
    prevent_initial_call=True,
)


@callback(
    Output("location_login", "href", allow_duplicate=True),
    Output("login_modal", "is_open", allow_duplicate=True),
    Output("login_modal_body", "children", allow_duplicate=True),
    Output("login_w_passkey_btn_form_text", "children"),
    Input("did_auth_succeed", "data"),
    Input("sign_in_no_passkey_btn", "n_clicks"),
    State("did_auth_succeed_msg", "data"),
    State("remember_me", "value"),
    # State("email", "value"),
    State("user_id_store", "data"),
    # State("ready_for_webauthn_auth", "data"),
    State("url", "search"),
    prevent_initial_call=True,
)
def redirect_to_rcom(
    did_auth_succeed_data: bool,
    sign_in_no_passkey_btn_n_clicks: int,
    did_auth_succeed_msg_data: str,
    remember_me_value: bool,
    # email_value: str,
    user_id_store_data: str,
    # ready_for_webauthn_auth_data: str,
    url_search: str,
):
    """Redirect to the RCOM page"""

    def return_vars(
        location_login_href: str = no_update,
        login_modal_is_open: bool = False,
        message: str = "Please sign in with your passkey.",
        # login_w_passkey_btn_form_text_children: str = "Sign in with your passkey",
    ):
        """Default return variables"""
        return (
            location_login_href,
            login_modal_is_open,
            message,
            message,
        )

    id_triggered: str = get_id_triggered()
    if (
        id_triggered == "sign_in_no_passkey_btn.n_clicks"
        and sign_in_no_passkey_btn_n_clicks
    ):
        # Proceed with the login process without a passkey
        pass
    elif id_triggered == "did_auth_succeed.data":
        if (
            did_auth_succeed_msg_data
            == "You have successfully signed in with your PassKey"
        ):
            # Proceed with the login process
            pass
        else:
            try:
                message = json.loads(did_auth_succeed_msg_data).get("msg", None)
                if not message:
                    message = "An error occurred. Please try again or contact IJACK."
            except Exception:
                message = "An error occurred. Please try again or contact IJACK."
            return return_vars(
                location_login_href=no_update,
                login_modal_is_open=True,
                message=message,
            )

    else:
        raise PreventUpdate()

    # This also works, but we need the email address to look up the credential in the first place
    # credential_dict: dict = json.loads(ready_for_webauthn_auth_data)
    # id_ = credential_dict.get("allowCredentials", [{}])[0].get("id", None)
    # if not id_:
    #     return return_vars(
    #         location_login_href=no_update,
    #         login_modal_is_open=True,
    #         message="No matching PassKey found in the database. Please try again or contact IJACK.",
    #     )

    # credential_id: bytes = base64url_to_bytes(id_)
    # cred_model = WebauthnCredential.query.filter_by(
    #     credential_id=credential_id
    # ).first()
    # if not cred_model:
    #     return return_vars(
    #         location_login_href=no_update,
    #         login_modal_is_open=True,
    #         message="No matching PassKey found in the database. Please try again or contact IJACK.",
    #     )

    ##########################################################################
    # If we've gotten this far, the user has successfully authenticated
    # user: User = cred_model.user_rel
    user = User.query.get(user_id_store_data)
    if not getattr(user, "is_confirmed", False):
        return return_vars(
            location_login_href=no_update,
            login_modal_is_open=True,
            message="Please confirm your identity first.",
        )

    login_user(user, remember=remember_me_value, duration=timedelta(days=365))

    # Save some details about this login, and previous logins
    user.prev_login_at = (
        None if user.current_login_at is None else user.current_login_at
    )
    user.current_login_at = utcnow_naive()
    user.login_count = 1 if user.login_count is None else user.login_count + 1
    user.last_login_ip = user.current_login_ip
    user.current_login_ip = request.remote_addr
    db.session.commit()

    # Clear any flashed messages
    session.pop("_flashes", None)
    if user_is_demo_customer(user_id=user.id):
        # This particular flash only shows up in FireFox (not Chrome or Edge)
        flash("Please contact IJACK to see units for a specific customer.")

    # Find where the user was trying to go before being redirected to the login page
    query_str = parse.urlsplit(url_search).query
    query_dict = parse.parse_qs(query_str)
    next_url: str = get_value_from_query_dict(query_dict, "next")
    if not next_url or "None" in next_url:
        next_url = url_for("dash.home")
    # elif "None" in next_url:
    #     error_send_email_w_details(
    #         error=f"User {user} is trying to login and the next URL is '{next_url}' with type {type(next_url)}",
    #         db=db,
    #     )
    #     next_url = url_for("dash.home")

    return return_vars(
        location_login_href=next_url, login_modal_is_open=False, message=no_update
    )


@callback(
    Output("location_login", "href", allow_duplicate=True),
    Input("login_w_microsoft", "n_clicks"),
    State("email", "value"),
    State("phone", "value"),
    prevent_initial_call=True,
)
def redirect_to_microsoft(login_w_microsoft_n_clicks, email_value, phone_value):
    """Redirect to the Microsoft login page"""
    if not login_w_microsoft_n_clicks:
        raise PreventUpdate()

    # Update Flask session with additional information
    session["attempted_login_time"] = utcnow_naive().isoformat()
    session["login_source"] = "Microsoft button click"
    session["email"] = email_value
    session["phone"] = phone_value

    return url_for("azure.login")
