select
    b1.model_unit_type_id,
    b1.part_name,
    -- Check if we have any duplicates on model_unit_type_id and part_name
    COUNT(*) OVER (PARTITION BY b1.part_name, b1.model_unit_type_id) AS num_occurrences_sold,
    b1.sold_quantity_total::float,
    b2.model_unit_type_id_count_sql::float,
    b1.sold_quantity_total::float / b2.model_unit_type_id_count_sql::float as sold_quantity_per_model
from (
    select
        t1.model_unit_type_id,
        t2.part_name,
        --Divide by 9 to get parts sold every two months, since we're summing the last 18 months of sales
        sum(parts.quantity) / 9 as sold_quantity_total
    from public.work_orders_parts parts
    inner join public.parts t2
        on t2.id = parts.part_id
    left join public.vw_structures_joined_filtered t1
        on t1.id = parts.structure_id
    inner join public.work_orders t4
        on t4.id = parts.work_order_id
    where
        parts.quantity > 0
        and t4.date_service >= (CURRENT_DATE - INTERVAL '18 months')
        and t4.is_warranty in :is_warranty
        and t1.model_unit_type_id in :model_unit_type_ids
        and t2.is_soft_part in :is_soft_part_tuple
    group by
        t1.model_unit_type_id,
        t2.part_name
) b1
-- Count of unit types, so we can divide the number of parts sold
-- by the number of unit types and get the average number of parts sold by unit type.
left join (
    select
        model_unit_type_id,
        count(*) as model_unit_type_id_count_sql
    from public.vw_structures_joined_filtered t1
    where
        t1.model_unit_type_id in :model_unit_type_ids
    group by
        model_unit_type_id
) b2
    on b1.model_unit_type_id = b2.model_unit_type_id
order by
    num_occurrences_sold desc,
    model_unit_type_id,
    part_name