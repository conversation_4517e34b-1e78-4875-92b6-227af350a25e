import json
import os
import time
from datetime import date, datetime
from decimal import Decimal
from numbers import Number
from random import randint
from typing import Tuple

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import numpy as np
import pandas as pd
import pytz

# from dash_extensions.enrich import DashBlueprint
from dash import Input, Output, State, callback, html, no_update
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import (
    MQTTMessage,
    PowerUnit,
    StructureVw,
)
from shared.models.models_work_order import Maintenance, MaintenanceType
from sqlalchemy import or_, text

from app import (
    cache_memoize_if_prod,
    db,
    mqtt_client,
    user_is_demo_customer,
    user_is_ijack_employee,
)
from app.config import (
    ADMIN_EMAILS,
    CMD_READ,
    CMD_WRITE,
    GATEWAY_SHOP,
    TAB_CONTROL,
    UNIT_TYPE_ID_UNO,
    USER_ID_SEAN,
)
from app.dashapp.callbacks.control_utils import (
    PermissionsAbilities,
    check_is_stroking,
    get_card_body_control_progress,
    get_card_body_permissions_abilities,
    get_is_valid_input,
    get_latest_targets_f_shadow,
    get_on_site_setpoints,
    record_remote_control,
)
from app.dashapp.layout_utils import get_list_group_item
from app.dashapp.metrics import (
    CONTROL_COMMAND_MEANINGS,
    CONTROL_ENUM_MEANINGS,
    CONTROL_ENUM_MULTIPLIERS,
    CONTROL_RESPONSE_MEANINGS,
)
from app.dashapp.utils import (
    convert_to_int,
    get_id_triggered,
    get_iot_device_shadow,
    get_structure_obj,
    get_unit_type,
    log_function_caller,
    update_shadow,
    verify_return_tuple,
)
from app.databases import run_sql_query
from app.models.models import Structure
from app.utils.simple import utcnow_aware

# SKIP_SHADOW_UPDATE: dict = {"state": "skip"}
RESET_PREV_MAINT: int = 1
SET_MAINTENANCE_INTERVAL: int = 2
# Default to an empty dictionary where the key is the aws_thing and the value is a list of messages
stored_messages: dict = {}


def form_group_row(
    label,
    input_id,
    invalid_feedback,
    input_type="number",
    input_value=None,
    input_placeholder=None,
    input_addon=None,
    input_addon_id=None,
    form_invalid_feedback_id=None,
):
    """Get a form group row for the scheduling modal"""

    input_addon_kwargs = {"children": input_addon}
    if input_addon_id:
        input_addon_kwargs["id"] = input_addon_id
    form_invalid_feedback_kwargs = {"children": invalid_feedback, "type": "invalid"}
    if form_invalid_feedback_id:
        form_invalid_feedback_kwargs["id"] = form_invalid_feedback_id

    return dbc.Row(
        [
            dbc.Label(
                label,
                html_for=input_id,
                # width=4
                style={"margin-top": "0.25rem"},
            ),
            dbc.InputGroup(
                [
                    dbc.Input(
                        id=input_id,
                        type=input_type,
                        placeholder=input_placeholder,
                        value=input_value,
                    ),
                    dbc.InputGroupText(**input_addon_kwargs),
                    dbc.FormFeedback("That is a valid input", type="valid"),
                    dbc.FormFeedback(**form_invalid_feedback_kwargs),
                ],
                size="sm",
            ),
        ]
    )


def get_card_pressure():
    """For the control tab, this is the layout for setting pressure stuff"""

    card = dbc.Card(
        [
            get_card_header("Pressure", "pressure_card_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Suction Target",
                                                    html_for="control_suction_target_input",
                                                    id="control_suction_target_label",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_suction_target_input",
                                                            type="number",
                                                            placeholder="Enter 0-700",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "PSI",
                                                            id="control_suction_target_input_addon",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number from 1-700",
                                                            type="invalid",
                                                            id="control_suction_target_form_feedback",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_suction_target_ft",
                                                    className="form-text",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_suction_actual_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Checklist(
                                                    options=[
                                                        {
                                                            "label": "Schedule",
                                                            "value": "schedule",
                                                        }
                                                    ],
                                                    value=[],
                                                    id="control_suction_schedule_switch",
                                                    switch=True,
                                                    style={
                                                        "margin-top": "0.25rem",
                                                        "font-weight": "normal",
                                                    },
                                                ),
                                                dbc.Collapse(
                                                    [
                                                        form_group_row(
                                                            label="Starting Level",
                                                            input_id="suction_sched_value_start",
                                                            input_type="number",
                                                            input_addon="PSI",
                                                            input_addon_id="suction_sched_value_start_addon",
                                                            invalid_feedback="Please enter a value from 1-700",
                                                            form_invalid_feedback_id="suction_sched_value_start_form_feedback",
                                                        ),
                                                        form_group_row(
                                                            label="Start in X minutes",
                                                            input_id="suction_sched_start_after_x_mins",
                                                            input_type="number",
                                                            input_value=0,
                                                            input_addon="Minutes",
                                                            invalid_feedback="Please enter a value >= zero",
                                                        ),
                                                        form_group_row(
                                                            label="Change it gradually over X minutes",
                                                            input_id="suction_sched_over_x_mins",
                                                            input_type="number",
                                                            input_value=30,
                                                            input_addon="Minutes",
                                                            invalid_feedback="Please enter a value >= zero",
                                                        ),
                                                    ],
                                                    id="control_suction_schedule_collapse",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_suction_target",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    ),
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Derate Discharge",
                                                    html_for="control_max_discharge_input",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_max_discharge_input",
                                                            type="number",
                                                            placeholder="Depends on unit",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "PSI",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number from 1-1000",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_max_discharge_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_max_discharge_target",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Max Shutdown Pressure",
                                                    html_for="control_max_shutdown_pressure_input",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_max_shutdown_pressure_input",
                                                            type="number",
                                                            placeholder="Depends on unit (~100-740)",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "PSI",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number from 100-740 if PLC software version less than 412, otherwise 60-740",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_max_shutdown_pressure_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_max_shutdown_pressure_target",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                        # Don't display if PLC software version is less than 310
                        id="control_max_shutdown_pressure_row",
                        style={"display": "none"},
                    ),
                ],
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_header(label: str, refresh_btn_id: str) -> dbc.CardHeader:
    """Get a control card header with refresh button. Requires a unique button ID"""
    return dbc.CardHeader(
        [
            label,
            dbc.Button(
                [html.I(className="fa-solid fa-refresh me-1"), "Refresh"],
                id=refresh_btn_id,
                color="secondary",
                outline=True,
                size="sm",
                class_name="float-end lh-1",
            ),
        ],
    )


def get_start_or_lock_card(unit_type: str) -> dbc.Card:
    """For the control tab, this is the layout for starting, stopping, and locking the units"""

    ut_lower: str = str(unit_type).lower()

    card = dbc.Card(
        [
            get_card_header("Startup", f"control_start_card_header_{ut_lower}_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Label(
                                "Unit is currently",
                                id=f"btn_lock_{ut_lower}_label",
                                width="auto",
                                # xs=7,
                                # md=6,
                                html_for=f"btn_lock_{ut_lower}",
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "Loading...",
                                    color="success",
                                    # block=True,
                                    size="sm",
                                    id=f"btn_lock_{ut_lower}",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                width="auto",
                                # xs=5,
                                # md=6,
                            ),
                        ],
                        # row=True,
                    ),
                    dbc.Row(
                        [
                            dbc.Label(
                                "Unit is currently",
                                id=f"btn_start_{ut_lower}_label",
                                width="auto",
                                # xs=7,
                                # md=6,
                                html_for=f"btn_start_{ut_lower}",
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "Loading...",
                                    color="success",
                                    # block=True,
                                    size="sm",
                                    id=f"btn_start_{ut_lower}",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                width="auto",
                                # xs=5,
                                # md=6,
                            ),
                        ],
                        # row=True,
                    ),
                ]
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_speed():
    """For the control tab, this is the layout for setting speed stuff for EGAS/XFER types"""

    card = dbc.Card(
        [
            get_card_header("Speed", "speed_egas_card_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Max Speed",
                                                    html_for="control_max_speed_input",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_max_speed_input",
                                                            type="number",
                                                            placeholder="Enter 2-100",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "%",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 2-100",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_max_speed_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_max_speed",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Min Speed",
                                                    html_for="control_min_speed_input",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_min_speed_input",
                                                            type="number",
                                                            placeholder="Enter 5-100",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "%",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 5-100",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_min_speed_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_min_speed",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Horsepower Limit",
                                                    html_for="control_horsepower_limit_input",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_horsepower_limit_input",
                                                            type="number",
                                                            placeholder="Depends on unit",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "HP",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 15-200",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_horsepower_limit_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_horsepower",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Adjustment Speed",
                                                    html_for="control_pid_sensitivity_input",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_pid_sensitivity_input",
                                                            type="number",
                                                            placeholder="Enter 1-4",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "PID",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 1-4",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_pid_sensitivity_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_pid_sensitivity",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                    ),
                ],
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_speed_uno():
    """For the control tab, this is the layout for setting speed stuff for UNO units"""

    card = dbc.Card(
        [
            get_card_header("Speed", "speed_uno_card_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "SPM Setpoint",
                                                    html_for="control_spm_set_uno",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_spm_set_uno",
                                                            type="number",
                                                            placeholder="Enter 0.1-15",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "SPM",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 0.1-15",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_spm_set_uno_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_spm_set_uno",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                            # dbc.Col(
                            #     dbc.Form(dbc.ListGroup(dbc.ListGroupItem([
                            #         dbc.Label("Horsepower Limit", html_for='control_horsepower_limit_uno'),
                            #         dbc.InputGroup([
                            #             dbc.Input(
                            #                 id='control_horsepower_limit_uno',
                            #                 type="number",
                            #                 placeholder="Enter 15-200"
                            #             ),
                            #             dbc.InputGroupText(
                            # "HP",
                            # # addon_type="append"
                            # ),
                            #             dbc.FormFeedback("That is a valid input", type="valid"),
                            #             dbc.FormFeedback("Please enter a number between 15-200", type="invalid"),
                            #         ], size='sm'),
                            #         html.Div(None, id="control_horsepower_limit_uno_ft", className="form-text"),
                            #         dbc.Button(
                            # [
                            #     html.I(
                            #         className="fa-solid fa-arrow-circle-right me-1"
                            #     ),
                            #     "Submit",
                            # ], color="primary", id='btn_control_horsepower_uno', size="sm", style={'margin-top': "0.5rem"}, disabled=True),
                            #     ]))),
                            #     xs=12, sm=6
                            # ),
                        ],
                        id="spm_setpoint_row",
                        # form=True,
                        justify="center",
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "SPM Minimum",
                                                    html_for="control_spm_min_uno",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_spm_min_uno",
                                                            type="number",
                                                            placeholder="Enter 0.1-15",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "SPM",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 0.1-15",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_spm_min_uno_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_spm_min_uno",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "SPM Maximum",
                                                    html_for="control_spm_max_uno",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_spm_max_uno",
                                                            type="number",
                                                            placeholder="Enter 0.1-15",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "SPM",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 0.1-15",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_spm_max_uno_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_spm_max_uno",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                    ),
                ],
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_uno_other():
    """This card is for pump fillage, top- and bottom-stroke, and PID for the UNO types"""

    card = dbc.Card(
        [
            get_card_header("Other Settings", "other_settings_card_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Auto Pump Fillage Target",
                                                    html_for="control_apft",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_apft",
                                                            type="number",
                                                            placeholder="Enter 5-90",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "%",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 5-90",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_apft_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_apft",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Adjustment Speed",
                                                    html_for="control_pid_sensitivity_uno",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_pid_sensitivity_uno",
                                                            type="number",
                                                            placeholder="Enter 1-4",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "PID",
                                                            # addon_type="append"
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 1-4",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_pid_sensitivity_uno_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_pid_sensitivity_uno",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                    ),
                    # Second row in "Other Settings"
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Top Setpoint",
                                                    html_for="control_ts",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_ts",
                                                            type="number",
                                                            placeholder="Enter 111-144",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "Inches",
                                                            # addon_type="append",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 111-144",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_ts_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_ts",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Bottom Setpoint",
                                                    html_for="control_bs",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_bs",
                                                            type="number",
                                                            placeholder="Enter 0-33",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "Inches",
                                                            # addon_type="append",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 0-33",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_bs_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_bs",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                    ),
                    # Third row in "Other Settings"
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Auto Tap Target",
                                                    html_for="control_auto_tap_tgt",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_auto_tap_tgt",
                                                            type="number",
                                                            placeholder="Enter 5-20",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "%",
                                                            # addon_type="append",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 5-20",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_auto_tap_tgt_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_auto_tap_tgt",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Pump Efficiency",
                                                    html_for="control_peff",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_peff",
                                                            type="number",
                                                            placeholder="Enter 5-95",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "%",
                                                            # addon_type="append",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 5-95",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_peff_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_peff",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                    ),
                    # Fourth row in "Other Settings"
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Pump Size Code",
                                                    html_for="control_psize",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Input(
                                                            id="control_psize",
                                                            type="number",
                                                            placeholder="Enter 0-11",
                                                        ),
                                                        dbc.InputGroupText(
                                                            "Size Code",
                                                            # addon_type="append",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "That is a valid input",
                                                            type="valid",
                                                        ),
                                                        dbc.FormFeedback(
                                                            "Please enter a number between 0-11",
                                                            type="invalid",
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_psize_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_psize",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                        justify=True,
                    ),
                ],
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_auto_uno():
    """This card is for automatic stuff for UNO types"""

    card = dbc.Card(
        [
            get_card_header(
                "Automatic Settings", "automatic_settings_card_refresh_btn"
            ),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Automatic Settings",
                                                    # html_for="control_auto_stuff",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.Checklist(
                                                            options=[
                                                                {
                                                                    "label": "Auto SPM",
                                                                    "value": "AUTO_SPM",
                                                                },
                                                                {
                                                                    "label": "Auto Tap",
                                                                    "value": "AUTO_TAP",
                                                                },
                                                                {
                                                                    "label": "Auto Tap Top",
                                                                    "value": "AUTO_TAP_TOP",
                                                                },
                                                                {
                                                                    "label": "Auto Tap Bottom",
                                                                    "value": "AUTO_TAP_BTM",
                                                                },
                                                            ],
                                                            # This must be an empty list, not None
                                                            value=[],
                                                            id="control_uno_auto_stuff",
                                                            switch=True,
                                                        ),
                                                    ],
                                                    size="sm",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_uno_auto_stuff",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.Form(
                                    dbc.ListGroup(
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Automatic Type",
                                                    # html_for="control_auto_stuff",
                                                ),
                                                dbc.InputGroup(
                                                    [
                                                        dbc.RadioItems(
                                                            options=[
                                                                {
                                                                    "label": "Maximum",
                                                                    "value": 1,
                                                                },
                                                                {
                                                                    "label": "Average",
                                                                    "value": 0,
                                                                },
                                                            ],
                                                            value=None,
                                                            id="control_uno_auto_type",
                                                        )
                                                    ],
                                                    size="sm",
                                                ),
                                                html.Div(
                                                    None,
                                                    id="control_uno_auto_type_ft",
                                                    className="form-text",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    size="sm",
                                                    id="btn_control_uno_auto_type",
                                                    class_name="mt-2",
                                                    disabled=True,
                                                ),
                                            ],
                                            className="mb-3",
                                        )
                                    )
                                ),
                                xs=12,
                                sm=6,
                            ),
                        ],
                        # form=True,
                    ),
                ],
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_vpn_startup():
    """This card is for VPN startup (for Sean only)"""

    card = dbc.Card(
        children=[
            get_card_header("VPN Access", "vpn_access_card_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Label(
                                "Start or stop VPN",
                                xs=7,
                            ),
                            dbc.Col(
                                dbc.Row(
                                    dbc.Col(
                                        [
                                            dbc.Button(
                                                [
                                                    html.I(
                                                        className="fa-solid fa-play me-1"
                                                    ),
                                                    "Start",
                                                ],
                                                color="success",
                                                size="sm",
                                                id="btn_vpn_start",
                                                disabled=True,
                                                class_name="my-1 me-2",
                                            ),
                                            dbc.Button(
                                                [
                                                    html.I(
                                                        className="fa-solid fa-stop me-1"
                                                    ),
                                                    "Stop",
                                                ],
                                                color="danger",
                                                size="sm",
                                                id="btn_vpn_stop",
                                                disabled=True,
                                                class_name="my-1 me-2",
                                            ),
                                        ],
                                    )
                                ),
                                xs=5,
                            ),
                        ],
                    ),
                    dbc.Row(
                        [
                            dbc.Label(
                                "IP address",
                                xs=7,
                            ),
                            dbc.Col(
                                "Unknown",
                                id="vpn_start_stop_ip_lbl",
                                xs=5,
                                class_name="px-3 py-2",
                            ),
                        ],
                    ),
                ]
            ),
        ],
    )

    return card


def get_card_apn_set() -> dbc.Card:
    """Get a card for setting the cellular APN string, with title, input, and submit button"""

    return dbc.Card(
        children=[
            get_card_header("Set Cellular APN", "apn_card_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Label(
                        "Cellular APN",
                        html_for="apn_input",
                    ),
                    dbc.Input(
                        type="text",
                        id="apn_input",
                        placeholder="Enter APN",
                        size="sm",
                        value="",
                    ),
                    dbc.FormText(
                        "Enter the APN string for the cellular connection",
                        id="apn_input_ft",
                    ),
                    html.Div(
                        dbc.Button(
                            [
                                html.I(className="fa-solid fa-arrow-circle-right me-1"),
                                "Submit",
                            ],
                            color="primary",
                            size="sm",
                            id="btn_apn_submit",
                            disabled=True,
                            class_name="mt-2",
                        ),
                    ),
                ],
            ),
        ],
    )


def get_card_send_command() -> dbc.Card:
    """Send a command to the gateway and display the response"""

    return dbc.Card(
        children=[
            get_card_header("Send Command to Gateway", "send_command_card_refresh_btn"),
            # dcc.Store(id="send_command_output_store", storage_type="memory"),
            dbc.CardBody(
                [
                    html.Div(
                        dbc.Button(
                            [
                                html.I(className="fa-solid fa-play me-1"),
                                "Subscribe to MQTT Messages",
                            ],
                            color="primary",
                            size="sm",
                            id="btn_subscribe_mqtt",
                            disabled=False,
                            class_name="my-2",
                        ),
                    ),
                    dbc.Label(
                        "Command",
                        html_for="send_command_input",
                    ),
                    dbc.Input(
                        type="text",
                        id="send_command_input",
                        placeholder="Enter command",
                        size="sm",
                        value="",
                        persistence=True,
                        persistence_type="local",
                    ),
                    dbc.FormText(
                        "Enter the command to send to the gateway",
                        id="send_command_input_ft",
                    ),
                    html.Div(
                        dbc.Button(
                            [
                                html.I(className="fa-solid fa-arrow-circle-right me-1"),
                                "Submit",
                            ],
                            color="primary",
                            size="sm",
                            id="btn_send_command",
                            disabled=True,
                            class_name="mt-2",
                        ),
                    ),
                    html.Div(id="send_command_output", className="mt-3"),
                    html.H3("Responses from Gateway in Last Hour", className="mt-3"),
                    html.Pre(
                        id="send_command_responses",
                        className="mt-3",
                        style={
                            "backgroundColor": "#f5f5f5",
                            "padding": "10px",
                            "border": "1px solid #ddd",
                            "borderRadius": "5px",
                            "overflowX": "auto",
                            "whiteSpace": "pre-wrap",
                        },
                    ),
                ],
            ),
        ],
    )


def get_card_request_surface_compression_card():
    """This card is for requesting a new compression or surface card (for Sean only)"""

    card = dbc.Card(
        children=[
            dbc.CardHeader("Request New Compression or Surface Card"),
            dbc.CardBody(
                [
                    dbc.Row(
                        dbc.Col(
                            dbc.Label(
                                "Request a new surface or compression card (must cancel request first if you want to request a new card)",
                            ),
                        )
                    ),
                    dbc.Row(
                        dbc.Col(
                            [
                                dbc.Button(
                                    [
                                        "Submit Request",
                                    ],
                                    color="primary",
                                    size="sm",
                                    id="btn_request_new_card",
                                    disabled=False,
                                    class_name="my-1 me-2",
                                ),
                                dbc.Button(
                                    [
                                        "Cancel Request",
                                    ],
                                    color="primary",
                                    size="sm",
                                    id="btn_cancel_new_card",
                                    disabled=False,
                                    class_name="my-1 me-2",
                                ),
                            ],
                        ),
                    ),
                ]
            ),
        ],
    )

    return card


def get_card_email_logs():
    """This card is for requesting gateway logs via email (for Sean only)"""

    return dbc.Card(
        children=[
            get_card_header("Get Gateway Logs", "email_logs_card_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Email",
                                        html_for="email_logs_input_email",
                                    ),
                                    dbc.Input(
                                        type="email",
                                        id="email_logs_input_email",
                                        placeholder="Enter email",
                                        size="sm",
                                        value=ADMIN_EMAILS[0],
                                    ),
                                ],
                                className="me-2",
                            ),
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Log Number",
                                        html_for="email_logs_input_num",
                                    ),
                                    dbc.Input(
                                        type="text",
                                        id="email_logs_input_num",
                                        placeholder="Log nums (e.g. 0,1)",
                                        size="sm",
                                        # min=0,
                                        value="0,1",
                                    ),
                                ],
                                className="me-2",
                            ),
                            dbc.Col(
                                [
                                    dbc.Label(
                                        "Log starts with",
                                        html_for="email_logs_input_starts",
                                    ),
                                    dbc.Input(
                                        type="text",
                                        id="email_logs_input_starts",
                                        placeholder="Starts with (e.g. app_canpy)",
                                        size="sm",
                                        value="app_canpy",
                                        # class_name="px-3 py-2",
                                    ),
                                ],
                                className="me-2",
                            ),
                            dbc.Col(
                                dbc.Button(
                                    [
                                        html.I(
                                            className="fa-solid fa-arrow-circle-right me-1"
                                        ),
                                        "Submit",
                                    ],
                                    color="primary",
                                    id="email_logs_submit_btn",
                                    size="sm",
                                    # Try getting the button to the bottom of the column
                                    class_name="align-self-end mt-auto",
                                    # class_name="mt-auto",
                                    disabled=True,
                                ),
                                width="auto",
                            ),
                        ],
                        # For inline form
                        class_name="g-2",
                        # class_name="g-2 d-flex flex-column",
                    ),
                ],
                class_name="d-flex flex-column",
            ),
        ],
    )


def get_card_admin():
    """For the control tab, this is the layout for doing admin stuff"""

    card = dbc.Card(
        [
            get_card_header("Admin", "control_admin_card_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.ListGroup(
                                    [
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Row(
                                                    dbc.Col(
                                                        [
                                                            dbc.Label(
                                                                "Preventative Maintenance",
                                                                # html_for="control_prev_maint_reset_date_ft",
                                                            ),
                                                            html.Div(
                                                                id="control_prev_maint_reset_date_ft",
                                                                className="form-text",
                                                            ),
                                                            html.Div(
                                                                id="control_prev_maint_current_hours_ft",
                                                                className="form-text",
                                                            ),
                                                            html.Div(
                                                                id="control_prev_maint_reset_hours_ft",
                                                                className="form-text",
                                                            ),
                                                            html.Div(
                                                                [
                                                                    # Switch to indicate if the unit has RCOM
                                                                    dbc.Switch(
                                                                        id="control_prev_maint_has_rcom_switch",
                                                                        label="Has RCOM",
                                                                        value=True,
                                                                        class_name="mt-1",
                                                                    ),
                                                                    dbc.FormText(
                                                                        id="control_prev_maint_has_rcom_switch_ft",
                                                                    ),
                                                                ]
                                                            ),
                                                            dbc.Label(
                                                                "Maintenance Completed",
                                                                html_for="control_prev_maint_type_select",
                                                                class_name="mt-3",
                                                            ),
                                                            dbc.Select(
                                                                placeholder="Select maintenance to record",
                                                                id="control_prev_maint_type_select",
                                                                disabled=False,
                                                                persistence=True,
                                                                persistence_type="local",
                                                                # class_name="mt-1 mb-1",
                                                            ),
                                                            dmc.DatePickerInput(
                                                                id="control_prev_maint_date",
                                                                # label="Maintenance Date",
                                                                # description="Date on which the preventative maintenance was performed.",
                                                                value=date.today(),
                                                                className="my-1",
                                                                # dropdownType="modal", # default "popover"
                                                                # So the datepicker is in front of the Bootstrap modal
                                                                popoverProps={
                                                                    "zIndex": 10_000
                                                                },
                                                                # modalProps={"zIndex": 10_000},
                                                                # dropdownPosition="bottom-start" fixes bug with Mantine DatePickerInput causing app to lock up!
                                                                # https://community.plotly.com/t/dash-mantine-datepicker/75251/4
                                                                # dropdownPosition="bottom-start",
                                                            ),
                                                            dbc.Button(
                                                                [
                                                                    html.I(
                                                                        className="fa-solid fa-arrow-circle-right me-1"
                                                                    ),
                                                                    "Record Maintenance",
                                                                ],
                                                                color="primary",
                                                                size="sm",
                                                                id="btn_control_prev_maint_reset",
                                                                style={
                                                                    "margin-top": "0.5rem"
                                                                },
                                                                disabled=True,
                                                            ),
                                                        ]
                                                    )
                                                ),
                                                dbc.Row(
                                                    dbc.Col(
                                                        [
                                                            dbc.Label(
                                                                "Maintenance Interval",
                                                                html_for="control_maintenance_interval_input",
                                                            ),
                                                            dbc.InputGroup(
                                                                [
                                                                    dbc.Input(
                                                                        id="control_maintenance_interval_input",
                                                                        type="number",
                                                                        placeholder="Typically 6 months",
                                                                    ),
                                                                    dbc.InputGroupText(
                                                                        "Months",
                                                                    ),
                                                                    dbc.FormFeedback(
                                                                        "That is a valid input",
                                                                        type="valid",
                                                                    ),
                                                                    dbc.FormFeedback(
                                                                        "Please enter a number between 1-36",
                                                                        type="invalid",
                                                                    ),
                                                                ],
                                                                size="sm",
                                                            ),
                                                            html.Div(
                                                                None,
                                                                id="control_maintenance_interval_ft",
                                                                className="form-text",
                                                            ),
                                                            dbc.Button(
                                                                [
                                                                    html.I(
                                                                        className="fa-solid fa-arrow-circle-right me-1"
                                                                    ),
                                                                    "Submit",
                                                                ],
                                                                color="primary",
                                                                size="sm",
                                                                id="btn_control_maintenance_interval",
                                                                style={
                                                                    "margin-top": "0.5rem"
                                                                },
                                                                disabled=True,
                                                            ),
                                                        ]
                                                    ),
                                                    class_name="mt-3",
                                                ),
                                            ],
                                            class_name="mb-3",
                                        ),
                                    ]
                                ),
                                xs=12,
                                sm=6,
                            ),
                            dbc.Col(
                                dbc.ListGroup(
                                    [
                                        dbc.ListGroupItem(
                                            [
                                                dbc.Label(
                                                    "Reset velocity control",
                                                    html_for="btn_reset_velocity_control",
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Submit",
                                                    ],
                                                    color="primary",
                                                    # block=True,
                                                    size="sm",
                                                    id="btn_reset_velocity_control",
                                                    disabled=True,
                                                    className="float-end",
                                                ),
                                            ],
                                            class_name="mb-3",
                                        ),
                                    ]
                                ),
                                id="control_admin_egas_col",
                                xs=12,
                                sm=6,
                            ),
                        ],
                    ),
                ]
            ),
        ],
        class_name="mt-3",
    )

    return card


@callback(
    Output("control_prev_maint_has_rcom_switch_ft", "children"),
    Input("control_prev_maint_has_rcom_switch", "value"),
    State("store_structure_id", "data"),
    prevent_initial_call=True,
)
def update_prev_maint_rcom_switch(has_rcom: bool | None, store_structure_id_data: str):
    """If the user toggles the RCOM switch, update the record in the database"""
    if has_rcom is not True and has_rcom is not False:
        raise PreventUpdate

    # Get structure object directly without first checking the view object
    structure_obj: Structure | None = db.session.get(Structure, store_structure_id_data)
    if structure_obj is None or structure_obj.has_rcom == has_rcom:
        raise PreventUpdate

    # Update the value and commit in a single transaction
    structure_obj.has_rcom = has_rcom
    db.session.commit()

    return f"Set RCOM to {has_rcom}"


def get_col_w_input(
    label: str, input_id: str, width: int = 6, class_name: str = "mt-2"
):
    """Get two columns of disabled dbc.Input classes in which to display returned values"""

    return dbc.Col(
        [
            dbc.Label(
                label,
                html_for=input_id,
            ),
            dbc.Input(
                type="number",
                id=input_id,
                placeholder="",
                disabled=True,
            ),
            dbc.FormText(id=f"{input_id}_text"),
        ],
        width=width,
        class_name=class_name,
    )


def get_card_advanced_remote_control():
    """Get card for advanced remote control"""

    return dbc.Card(
        [
            get_card_header("Advanced Remote Control", "control_adv_rc_refresh_btn"),
            dbc.CardBody(
                [
                    dbc.Row(
                        dbc.Col(
                            [
                                dbc.Row(
                                    dbc.Col(
                                        dbc.Select(
                                            placeholder="Select Item to Control",
                                            id="control_select",
                                            disabled=False,
                                            persistence=True,
                                            persistence_type="local",
                                            # value="",
                                            required=True,
                                            class_name="mt-2 mb-2",
                                        )
                                    )
                                ),
                                dbc.Card(
                                    [
                                        dbc.CardHeader(
                                            dbc.Row(
                                                [
                                                    dbc.Col(
                                                        # html.Div(
                                                        "Command",
                                                        # style={
                                                        #     # "text-align": "center",
                                                        #     "text-transform": "uppercase",
                                                        #     "font-weight": 700,
                                                        #     "font-size": "18px",
                                                        #     "border-bottom": "1px",
                                                        # },
                                                        # class_name="card-header"
                                                        # ),
                                                    ),
                                                    dbc.Col(
                                                        # html.Div(
                                                        "Response",
                                                        # style={
                                                        #     # "text-align": "center"
                                                        #     "text-transform": "uppercase",
                                                        #     "font-weight": 700,
                                                        #     "font-size": "18px",
                                                        #     "border-bottom": "1px",
                                                        # },
                                                        # class_name="card-header"
                                                        # ),
                                                    ),
                                                ]
                                            ),
                                        ),
                                        dbc.CardBody(
                                            [
                                                dbc.Row(
                                                    [
                                                        get_col_w_input(
                                                            "Control Num Wanted",
                                                            "control_cmd_cnum",
                                                            class_name=None,
                                                        ),
                                                        get_col_w_input(
                                                            "Control Num Reported",
                                                            "control_resp_cnum",
                                                            class_name=None,
                                                        ),
                                                    ],
                                                ),
                                                dbc.Row(
                                                    [
                                                        get_col_w_input(
                                                            "Command",
                                                            "control_cmd",
                                                        ),
                                                        get_col_w_input(
                                                            "Response",
                                                            "control_resp",
                                                        ),
                                                    ]
                                                ),
                                                dbc.Row(
                                                    [
                                                        get_col_w_input(
                                                            "Enum",
                                                            "control_cmd_enum",
                                                        ),
                                                        get_col_w_input(
                                                            "Enum",
                                                            "control_resp_enum",
                                                        ),
                                                    ]
                                                ),
                                                dbc.Row(
                                                    [
                                                        get_col_w_input(
                                                            "Value Wanted",
                                                            "control_cmd_val",
                                                        ),
                                                        get_col_w_input(
                                                            "Value Reported",
                                                            "control_resp_val",
                                                        ),
                                                    ]
                                                ),
                                                dbc.Row(
                                                    [
                                                        get_col_w_input(
                                                            "Min",
                                                            "control_resp_min",
                                                            width=4,
                                                        ),
                                                        get_col_w_input(
                                                            "Max",
                                                            "control_resp_max",
                                                            width=4,
                                                        ),
                                                        get_col_w_input(
                                                            "Default",
                                                            "control_resp_def",
                                                            width=4,
                                                        ),
                                                    ]
                                                ),
                                            ]
                                        ),
                                    ],
                                    class_name="mt-2",
                                ),
                                dbc.Row(
                                    dbc.Col(
                                        html.Div(
                                            None,
                                            id="control_select_ft",
                                            className="form-text",
                                        ),
                                    )
                                ),
                                dbc.Row(
                                    [
                                        dbc.Label(
                                            "New Target",
                                            # html_for="control_select_input",
                                            width="auto",
                                        ),
                                        dbc.Col(
                                            dbc.InputGroup(
                                                [
                                                    dbc.Input(
                                                        id="control_select_input",
                                                        type="number",
                                                        # placeholder="placeholder",
                                                        # value=1,
                                                        min=None,
                                                        max=None,
                                                    ),
                                                    dbc.InputGroupText(
                                                        # "PSI",
                                                        id="control_select_units"
                                                    ),
                                                    dbc.FormFeedback(
                                                        "That is a valid input",
                                                        type="valid",
                                                    ),
                                                    dbc.FormFeedback(
                                                        "That is not a valid input",
                                                        type="invalid",
                                                    ),
                                                ],
                                                size="sm",
                                                style={"min-width": "100px"},
                                            ),
                                            class_name="me-2",
                                            # width="auto"
                                        ),
                                        dbc.Col(
                                            dbc.Button(
                                                "Read",
                                                color="primary",
                                                size="sm",
                                                id="btn_control_select_read",
                                                # class_name="mt-2",
                                                # class_name="my-1 me-1",
                                                # class_name="me-1",
                                                disabled=True,
                                            ),
                                            width="auto",
                                        ),
                                        dbc.Col(
                                            dbc.Button(
                                                "Read All",
                                                color="primary",
                                                size="sm",
                                                id="btn_control_select_read_all",
                                                # class_name="mt-2",
                                                # class_name="my-1 me-1",
                                                # class_name="me-1",
                                                disabled=True,
                                            ),
                                            width="auto",
                                        ),
                                        dbc.Col(
                                            dbc.Button(
                                                "Write",
                                                color="primary",
                                                size="sm",
                                                id="btn_control_select_write",
                                                # class_name="mt-2",
                                                # class_name="my-1 me-1",
                                                # class_name="me-1",
                                                disabled=True,
                                            ),
                                            width="auto",
                                        ),
                                    ],
                                    # class_name="mt-3 g-3",
                                    # g-2 lets us add a smaller gutter in breakpoints smaller than lg
                                    class_name="mt-3 g-2",
                                ),
                            ],
                            xs=12,
                            lg=10,
                            xl=8,
                        )
                    ),
                    # All the values we currently have in the "named shadow"
                    dbc.Row(
                        dbc.Col(id="adv_ctrl_responses"),
                        class_name="mt-4",
                    ),
                ]
            ),
        ],
        class_name="mt-3 mb-3",
    )


def get_control_div():
    """Get the control div"""

    return dbc.Row(
        id="control_div",
        style={"display": "none"},
        justify="center",
        class_name="mt-3",
        children=dbc.Col(
            lg=12,
            xxl=10,
            children=[
                dbc.Row(
                    children=dbc.Col(
                        dbc.Card(
                            [
                                dbc.CardHeader(
                                    # dbc.Spinner(
                                    id="control_msg_status_card_header",
                                    #     color="success",
                                    #     size="sm",
                                    # )
                                ),
                                dbc.CardBody(
                                    [
                                        html.Div(
                                            id="control_msg_status_card_body_div",
                                        ),
                                        html.Div(
                                            [
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-refresh me-1"
                                                        ),
                                                        "Refresh Gateway Data",
                                                    ],
                                                    id="control_msg_status_refresh_btn",
                                                    color="primary",
                                                    className="me-1 mt-1",
                                                    n_clicks=0,
                                                ),
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-stop me-1"
                                                        ),
                                                        "Cancel All Commands",
                                                    ],
                                                    id="control_msg_status_cancel_btn",
                                                    color="dark",
                                                    className="me-1 mt-1",
                                                ),
                                            ],
                                            id="control_msg_status_card_body_buttons",
                                        ),
                                    ],
                                    style={"margin-x": "0.25rem"},
                                ),
                            ],
                        ),
                        xs=12,
                    ),
                ),
                # This is the layout for controlling the units (not the shop)
                dbc.Row(
                    [
                        dbc.Col(
                            [
                                dbc.Row(
                                    [
                                        dbc.Col(
                                            [
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_start_or_lock_card("UNO"),
                                                        # width="auto",
                                                        xs=12,
                                                    ),
                                                    id="control_start_uno",
                                                ),
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_start_or_lock_card("EGAS"),
                                                        # width="auto",
                                                        xs=12,
                                                    ),
                                                    id="control_start_egas",
                                                ),
                                                # The following is not visible if it's an UNO
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_pressure(),
                                                        width=12,
                                                    ),
                                                    id="control_pressure_egas",
                                                ),
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_uno_other(),
                                                        width=12,
                                                    ),
                                                    id="control_other_uno",
                                                ),
                                            ],
                                            xs=12,
                                            lg=6,
                                        ),
                                        dbc.Col(
                                            [
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_email_logs(),
                                                        # width="auto",
                                                        xs=12,
                                                    ),
                                                    id="control_email_logs_row",
                                                    style={"display": "none"},
                                                    class_name="mt-3",
                                                ),
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_vpn_startup(),
                                                        xs=12,
                                                        # lg=6,
                                                    ),
                                                    id="control_vpn_row",
                                                    style={"display": "none"},
                                                    class_name="mt-3",
                                                ),
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_apn_set(),
                                                        xs=12,
                                                        # lg=6,
                                                    ),
                                                    id="control_apn_row",
                                                    style={"display": "none"},
                                                    class_name="mt-3",
                                                ),
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_send_command(),
                                                        xs=12,
                                                        # lg=6,
                                                    ),
                                                    id="control_send_command_row",
                                                    style={"display": "none"},
                                                    class_name="mt-3",
                                                ),
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_request_surface_compression_card(),
                                                        xs=12,
                                                        # lg=6,
                                                    ),
                                                    id="control_request_card_row",
                                                    style={"display": "none"},
                                                    class_name="mt-3",
                                                ),
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_speed(),
                                                        width=12,
                                                    ),
                                                    id="control_speed_egas",
                                                ),  # EGAS/XFER speed stuff
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_speed_uno(),
                                                        width=12,
                                                    ),
                                                    id="control_speed_uno",
                                                ),  # UNO speed stuff
                                                dbc.Row(
                                                    dbc.Col(
                                                        get_card_auto_uno(),
                                                        width=12,
                                                    ),
                                                    id="control_auto_uno",
                                                ),  # UNO automatic stuff
                                            ],
                                            xs=12,
                                            lg=6,
                                        ),
                                    ],
                                ),
                                # Special indicators for Dan to fine-tune the units on-site
                                dbc.Row(
                                    [
                                        dbc.Col(
                                            dbc.Card(
                                                [
                                                    dbc.CardHeader(
                                                        "On-Site Setpoints",
                                                    ),
                                                    dbc.CardBody(
                                                        [
                                                            dbc.ListGroup(
                                                                [
                                                                    get_list_group_item(
                                                                        "ctrl_on_site_top_decel_lbl",
                                                                        "ctrl_on_site_top_decel_badge",
                                                                        tooltip="",
                                                                        spinner=False,
                                                                    ),
                                                                    get_list_group_item(
                                                                        "ctrl_on_site_top_accel_lbl",
                                                                        "ctrl_on_site_top_accel_badge",
                                                                        tooltip="",
                                                                        spinner=False,
                                                                    ),
                                                                    get_list_group_item(
                                                                        "ctrl_on_site_btm_accel_lbl",
                                                                        "ctrl_on_site_btm_accel_badge",
                                                                        tooltip="",
                                                                        spinner=False,
                                                                    ),
                                                                    get_list_group_item(
                                                                        "ctrl_on_site_btm_decel_lbl",
                                                                        "ctrl_on_site_btm_decel_badge",
                                                                        tooltip="",
                                                                        spinner=False,
                                                                    ),
                                                                    get_list_group_item(
                                                                        "ctrl_on_site_top_wait_time_lbl",
                                                                        "ctrl_on_site_top_wait_time_badge",
                                                                        tooltip="",
                                                                        spinner=False,
                                                                    ),
                                                                    get_list_group_item(
                                                                        "ctrl_on_site_btm_wait_time_lbl",
                                                                        "ctrl_on_site_btm_wait_time_badge",
                                                                        tooltip="",
                                                                        spinner=False,
                                                                    ),
                                                                ]
                                                            ),
                                                        ]
                                                    ),
                                                ],
                                                class_name="mt-3",
                                            ),
                                            xs=12,
                                            lg=6,
                                        ),
                                        dbc.Col(
                                            get_card_admin(),
                                            xs=12,
                                            lg=6,
                                        ),
                                    ],
                                    id="control_on_site_setpoints_row",
                                    style={"display": "none"},
                                ),
                                dbc.Row(
                                    dbc.Col(
                                        get_card_advanced_remote_control(),
                                        id="control_admin_adv_rem_ctrl",
                                        style={"display": "none"},
                                        xs=12,
                                        # lg=10,
                                        # xl=10,
                                    )
                                ),
                            ],
                        ),
                    ],
                    id="control_units_row",
                    style={"display": "none"},
                    class_name="mt-3",
                ),
                # This is the layout for controlling the shop (not the units)
                dbc.Row(
                    dbc.Col(
                        [
                            dbc.Row(
                                [
                                    dbc.Col(
                                        get_card_doors(),
                                        xs=12,
                                        md=6,
                                    ),
                                    dbc.Col(
                                        get_card_lights(),
                                        xs=12,
                                        md=6,
                                    ),
                                ]
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        get_card_hrv(),
                                        xs=12,
                                        md=6,
                                    ),
                                    dbc.Col(
                                        get_card_overhead_doors_west(),
                                        xs=6,
                                        md=3,
                                    ),
                                    dbc.Col(
                                        get_card_overhead_doors_east(),
                                        xs=6,
                                        md=3,
                                    ),
                                ],
                            ),
                        ]
                    ),
                    id="control_shop_row",
                    style={"display": "none"},
                ),
            ],
        ),
    )


@cache_memoize_if_prod(60)
def db_control_select_metadata(control_num: str | int) -> dict:
    """Run a query to get the remote control select menu metadata"""
    sql = text(
        """
        select
            abbrev, item, units
        from public.map_abbrev_item
        where control_num = :control_num
        limit 1
    """
    ).bindparams(control_num=control_num)
    rows, _ = run_sql_query(sql, db_name="ijack")
    return rows[0]


@cache_memoize_if_prod((60 * 5))
def get_all_control_numbers_db() -> list:
    """Run a query to get the remote control select menu metadata"""

    sql = text(
        """
        select
            control_num, item
        from public.map_abbrev_item
        where control_num is not null
    """
    )
    rows, _ = run_sql_query(sql, db_name="ijack", as_dict=True)

    return rows


def get_adv_remote_control_desired_values(
    cnum: int, cval: float, enum: int, read_all: bool = False
) -> tuple:
    """Convert and multiple the values for the AWS IoT desired dictionary"""

    cmd_cnum = convert_to_int(cnum, None)
    cmd_enum = convert_to_int(enum, 1)  # 1-6, with 1 default
    # cmd_val = convert_to_int(cval, None)  # TODO: fix for writes!

    # Deal with enum (e.g. if enum = 2 and value = 2, multiplier = 10 so new value = 2*10 = 20)
    if cmd_enum > 1 and isinstance(cval, Number):
        # Deal with weird floating point issues by converting to Decimal
        multiplier = CONTROL_ENUM_MULTIPLIERS[cmd_enum]
        cval = Decimal(str(cval)) * Decimal(str(multiplier))
        cval = float(cval)

    if read_all:
        # Request a list of control numbers (i.e. all of them)
        rows = get_all_control_numbers_db()
        cmd_cnum = [row.get("control_num", None) for row in rows]

    return cmd_cnum, cmd_enum, cval


def get_adv_remote_control_desired_dict(
    cnum: int, cval: int, enum: int, is_write: bool = False
) -> dict:
    """
    Prepare the AWS device shadow "desired" dictionary
    for the advanced remove control command
    """
    if is_write:
        cmd_label = "CMD_WRITE"
        cmd = CMD_WRITE
    else:
        cmd_label = "CMD_READ"
        cmd = CMD_READ

    desired = {
        cmd_label: {
            # 1 read; 2 write a new value
            "CMD": cmd,
            "CMD_CNUM": cnum,
            # 1 = div by 1; 2 = div by 10
            "CMD_ENUM": enum,
            "CMD_VAL": cval,
        }
    }
    return desired


def dataframe_deal_w_enum(row: pd.Series) -> pd.Series:
    """
    Adjust val, min, max, and def columns if enum > 1
    (e.g. if enum = 2 and value = 2, multiplier = 10 so new value = 2*10 = 20)
    """
    attr = None
    value = None
    multiplier: Decimal | None = None
    new_value: Decimal | None = None
    try:
        # The CONTROL_ENUM_MULTIPLIERS dict only has keys from 1 to 6, and 1 yields a multiplier of 1
        if row.ENUM not in (2, 3, 4, 5, 6):
            return row
        multiplier = Decimal(str(CONTROL_ENUM_MULTIPLIERS[row.ENUM]))
        to_be_divided: tuple = ("VAL", "MIN", "MAX", "DEF")
        for attr in to_be_divided:
            value = getattr(row, attr, None)
            if value in (None, "", np.nan) or str(value).lower() == "nan":
                continue
            new_value = Decimal(str(value)) / multiplier
            if new_value in (None, "", np.nan) or str(new_value).lower() == "nan":
                continue
            # Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas.
            # Value '0.3' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.
            int_value: int = int(new_value)
            setattr(row, attr, int_value)
    except Exception as error:
        msg = f"Problem dealing with ENUM in Pandas DataFrame row. attr = {attr}, new_value = {new_value}, type(new_value) = {type(new_value)}, multiplier = {multiplier}, row = {row}. \n\nError: {error}"
        current_app.logger.error(msg)
        # catch the original exception, modify its message to include additional information, and then re-raise it
        raise type(error)(msg).with_traceback(error.__traceback__)

    return row


def get_named_shadow_children(aws_thing: str) -> dbc.Table:
    """Get the "named shadow" children for a div below the advanced remote control buttons"""

    shadow_all = get_iot_device_shadow(aws_thing, shadow_name="advanced_rc")
    if not shadow_all:
        return dbc.Table()

    reported = shadow_all.get("state", {}).get("reported", {})
    # Remove a record we don't want in the table
    reported.pop("connected", None)

    df = pd.DataFrame.from_dict(reported, orient="index").reset_index()
    df = df.rename(columns={"index": "control_num"})
    df = df.apply(pd.to_numeric)

    # Deal with enum (e.g. if enum = 2 and value = 2, multiplier = 10 so new value = 2*10 = 20)
    # axis=1 goes row by row
    df = df.apply(dataframe_deal_w_enum, axis=1)

    list_of_dicts = get_all_control_numbers_db()
    df_items = pd.DataFrame(list_of_dicts)
    df_merged = df_items.merge(df, how="right", on="control_num")
    # df_merged = df_merged.sort_values(("item"))
    df_merged = df_merged.sort_values(("control_num"))

    metadata = shadow_all.get("metadata", {}).get("reported", {})
    metadata.pop("connected", None)

    default_ts = {"timestamp": datetime(datetime.now().year, 1, 1).timestamp()}
    # timestamps = {key: val.get("VAL", default_ts) for key, val in metadata.items()}
    timestamps = {
        key: val.get(next(iter(val)), default_ts) for key, val in metadata.items()
    }
    ts_df = pd.DataFrame.from_dict(timestamps, orient="index").reset_index()
    ts_df = ts_df.rename(columns={"index": "control_num"})
    ts_df = ts_df.apply(pd.to_numeric)
    ts_df["timestamp"] = pd.to_datetime(ts_df["timestamp"], unit="s")
    ts_df["timestamp"] = ts_df["timestamp"].dt.strftime("%Y-%m-%d")

    df_merged = df_merged.merge(ts_df, how="left", on="control_num")
    df_merged = df_merged.rename(
        columns={"control_num": "CNUM", "item": "Item", "timestamp": "Updated"}
    )

    table = dbc.Table.from_dataframe(
        df_merged,
        striped=True,
        bordered=True,
        hover=True,
        style={"margin-top": "1rem"},
        responsive=True,
        index=False,
    )

    return table


def get_card_doors():
    """For the control tab, this is the layout for controlling the shop doors at HQ in Moosomin"""

    card = dbc.Card(
        [
            dbc.CardHeader(
                "Doors",
                id="control_doors_card_header",
                # style={"color": "#717174"}
            ),
            dbc.CardBody(
                [
                    # dbc.Row(
                    #     [
                    #         dbc.Label(
                    #             "All doors are",
                    #             id="btn_unlock_all_label",
                    #             xs=7,
                    #             # style={"color": "#717174"},
                    #         ),
                    #         dbc.Col(
                    #             # daq.BooleanSwitch(id='btn_unlock_all', on=True, disabled=True),
                    #             dbc.Button(
                    #                 "Unlock",
                    #                 color="success",
                    #                 size="sm",
                    #                 # block=True,
                    #                 id="btn_unlock_all",
                    #                 disabled=True,
                    #                 class_name="my-1",
                    #             ),
                    #             xs=5,
                    #             class_name="d-grid gap-2",
                    #         ),
                    #     ],
                    #     # row=True,
                    # ),
                    dbc.Row(
                        [
                            dbc.Label(
                                "Office doors are",
                                id="btn_unlock_office_label",
                                xs=7,
                                # style={"color": "#717174"},
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "Unlock",
                                    color="success",
                                    size="sm",
                                    # block=True,
                                    id="btn_unlock_office",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                xs=5,
                                class_name="d-grid gap-2",
                            ),
                        ],
                        # row=True,
                    ),
                    dbc.Row(
                        [
                            dbc.Label(
                                "Shop doors are",
                                id="btn_unlocked_shop_label",
                                xs=7,
                                # style={"color": "#717174"},
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "Unlock",
                                    color="success",
                                    size="sm",
                                    # block=True,
                                    id="btn_unlocked_shop",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                xs=5,
                                class_name="d-grid gap-2",
                            ),
                        ],
                        # row=True,
                    ),
                    # dbc.Row(
                    #     [
                    #         dbc.Label(
                    #             "Gym door is",
                    #             id="btn_unlock_gym_label",
                    #             xs=7,
                    #             # style={"color": "#717174"},
                    #         ),
                    #         dbc.Col(
                    #             dbc.Button(
                    #                 "Unlock",
                    #                 color="success",
                    #                 size="sm",
                    #                 # block=True,
                    #                 id="btn_unlock_gym",
                    #                 disabled=True,
                    #                 class_name="my-1",
                    #             ),
                    #             xs=5,
                    #             class_name="d-grid gap-2",
                    #         ),
                    #     ],
                    #     # row=True,
                    # ),
                ]
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_lights():
    """For the control tab, this is the layout for controlling the lights at HQ in Moosomin"""

    card = dbc.Card(
        [
            dbc.CardHeader(
                "Lights",
                id="control_lights_card_header",
                # style={"color": "#717174"}
            ),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Label(
                                "North lights are",
                                id="btn_lights_north_on_label",
                                xs=7,
                                # style={"color": "#717174"},
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "On",
                                    color="warning",
                                    size="sm",
                                    # block=True,
                                    id="btn_lights_north_on",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                xs=5,
                                class_name="d-grid gap-2",
                            ),
                        ],
                        # row=True,
                    ),
                    dbc.Row(
                        [
                            dbc.Label(
                                "South lights are",
                                id="btn_lights_south_on_label",
                                xs=7,
                                # style={"color": "#717174"},
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "On",
                                    color="warning",
                                    size="sm",
                                    # block=True,
                                    id="btn_lights_south_on",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                xs=5,
                                class_name="d-grid gap-2",
                            ),
                        ],
                        # row=True,
                    ),
                    dbc.Row(
                        [
                            dbc.Label(
                                "Outdoor lights are",
                                id="btn_lights_outdoor_on_label",
                                xs=7,
                                # style={"color": "#717174"},
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "On",
                                    color="warning",
                                    size="sm",
                                    # block=True,
                                    id="btn_lights_outdoor_on",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                xs=5,
                                class_name="d-grid gap-2",
                            ),
                        ],
                        # row=True,
                    ),
                ]
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_hrv():
    """
    For the control tab, this is the layout for controlling the
    heat recovery/ventilation (HRV) system at HQ in Moosomin
    """

    card = dbc.Card(
        [
            dbc.CardHeader(
                "Heat Recovery Ventilation (HRV)",
                id="control_hrv_card_header",
                # style={"color": "#717174"},
            ),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Label(
                                "HRV fast is",
                                id="btn_hrv_fast_on_label",
                                xs=7,
                                # style={"color": "#717174"},
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "On",
                                    color="success",
                                    size="sm",
                                    # block=True,
                                    id="btn_hrv_fast_on",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                xs=5,
                                class_name="d-grid gap-2",
                            ),
                        ],
                        # row=True,
                    ),
                    dbc.Row(
                        [
                            dbc.Label(
                                "Office main fan is",
                                id="btn_fan_office_main_on_label",
                                xs=7,
                                # style={"color": "#717174"},
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "On",
                                    color="success",
                                    size="sm",
                                    # block=True,
                                    id="btn_fan_office_main_on",
                                    disabled=True,
                                    class_name="my-1",
                                ),
                                xs=5,
                                class_name="d-grid gap-2",
                            ),
                        ],
                        # row=True,
                    ),
                ]
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_overhead_doors_west():
    """
    For the control tab, this is the layout for controlling the
    west big overhead doors at HQ in Moosomin
    """

    card = dbc.Card(
        [
            dbc.CardHeader(
                "Overhead Doors West",
                id="control_ohd_west_card_header",
                # style={"color": "#717174"},
            ),
            dbc.CardBody(
                [
                    dbc.Form(
                        [
                            dbc.Row(
                                [
                                    # dbc.Label("", xs=7, style={'color': '#717174'}),
                                    dbc.Col(
                                        dbc.Button(
                                            "Open",
                                            color="success",
                                            size="sm",
                                            # block=True,
                                            id="btn_ohd_west_open",
                                            disabled=True,
                                            class_name="my-1",
                                        ),
                                        xs=12,
                                        class_name="d-grid gap-2",
                                    )
                                ],
                                # row=True,
                            ),
                            dbc.Row(
                                [
                                    # dbc.Label("", xs=7, style={'color': '#717174'}),
                                    dbc.Col(
                                        dbc.Button(
                                            "Close",
                                            color="primary",
                                            size="sm",
                                            # block=True,
                                            id="btn_ohd_west_close",
                                            disabled=True,
                                            class_name="my-1",
                                        ),
                                        xs=12,
                                        class_name="d-grid gap-2",
                                    )
                                ],
                                # row=True,
                            ),
                            dbc.Row(
                                [
                                    # dbc.Label("", xs=7, style={'color': '#717174'}),
                                    dbc.Col(
                                        dbc.Button(
                                            "Stop",
                                            color="danger",
                                            size="sm",
                                            # block=True,
                                            id="btn_ohd_west_stop",
                                            disabled=True,
                                            class_name="my-1",
                                        ),
                                        xs=12,
                                        class_name="d-grid gap-2",
                                    )
                                ],
                                # row=True,
                            ),
                        ],
                    ),
                ]
            ),
        ],
        class_name="mt-3",
    )

    return card


def get_card_overhead_doors_east():
    """
    For the control tab, this is the layout for controlling the
    east big overhead doors at HQ in Moosomin
    """

    card = dbc.Card(
        [
            dbc.CardHeader(
                "Overhead Doors East",
                id="control_ohd_east_card_header",
                # style={"color": "#717174"},
            ),
            dbc.CardBody(
                [
                    dbc.Form(
                        [
                            dbc.Row(
                                [
                                    # dbc.Label("", xs=7, style={'color': '#717174'}),
                                    dbc.Col(
                                        dbc.Button(
                                            "Open",
                                            color="success",
                                            size="sm",
                                            # block=True,
                                            id="btn_ohd_east_open",
                                            disabled=True,
                                            class_name="my-1",
                                        ),
                                        xs=12,
                                        class_name="d-grid gap-2",
                                    )
                                ],
                                # row=True,
                            ),
                            dbc.Row(
                                [
                                    # dbc.Label("", xs=7, style={'color': '#717174'}),
                                    dbc.Col(
                                        dbc.Button(
                                            "Close",
                                            color="primary",
                                            size="sm",
                                            # block=True,
                                            id="btn_ohd_east_close",
                                            disabled=True,
                                            class_name="my-1",
                                        ),
                                        xs=12,
                                        class_name="d-grid gap-2",
                                    )
                                ],
                                # row=True,
                            ),
                            dbc.Row(
                                [
                                    # dbc.Label("", xs=7, style={'color': '#717174'}),
                                    dbc.Col(
                                        dbc.Button(
                                            "Stop",
                                            color="danger",
                                            size="sm",
                                            # block=True,
                                            id="btn_ohd_east_stop",
                                            disabled=True,
                                            class_name="my-1",
                                        ),
                                        xs=12,
                                        class_name="d-grid gap-2",
                                    )
                                ],
                                # row=True,
                            ),
                        ],
                    ),
                ]
            ),
        ],
        class_name="mt-3",
    )

    return card


@callback(
    # 4 outputs
    Output("store_control_num_read_req", "data"),
    Output("control_select_units", "children"),  # e.g. PSI
    Output("control_select_ft", "children"),  # form text
    Output("control_select_input", "placeholder"),
    # 15 disabled inputs
    Output("control_cmd_cnum", "value"),
    Output("control_resp_cnum", "value"),
    Output("control_cmd", "value"),
    Output("control_cmd_text", "children"),
    Output("control_resp", "value"),
    Output("control_resp_text", "children"),
    Output("control_cmd_enum", "value"),
    Output("control_cmd_enum_text", "children"),
    Output("control_resp_enum", "value"),
    Output("control_resp_enum_text", "children"),
    Output("control_cmd_val", "value"),
    Output("control_resp_val", "value"),
    Output("control_resp_min", "value"),
    Output("control_resp_max", "value"),
    Output("control_resp_def", "value"),
    # 7 Invalid?
    Output("control_resp_cnum", "invalid"),
    Output("control_resp", "invalid"),
    Output("control_resp_enum", "invalid"),
    Output("control_resp_val", "invalid"),
    Output("control_resp_min", "invalid"),
    Output("control_resp_max", "invalid"),
    Output("control_resp_def", "invalid"),
    # 1 column children
    Output("adv_ctrl_responses", "children"),
    # 9 inputs
    Input("control_tab_refresh_interval", "n_intervals"),
    Input("control_select", "value"),  # dropdown select
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("control_adv_rc_refresh_btn", "n_clicks"),
    Input("btn_control_select_read", "n_clicks"),
    Input("btn_control_select_read_all", "n_clicks"),
    Input("btn_control_select_write", "n_clicks"),
    Input("btn_modal_main_confirm", "n_clicks"),
    # 4 states
    State("store_unit_type_id", "data"),
    State("store_control_num_read_req", "data"),
    State("setter_new_shadow", "data"),
    State("control_select_input", "value"),  # target value
)
def setup_advanced_remote_control(
    # 9 inputs
    control_tab_refresh_interval,
    control_select_input,  # dropdown
    store_structure_id_data,
    active_tab,
    control_adv_rc_refresh_btn_n_clicks,
    # At least the read button must function like the refresh button, intuitively
    btn_control_select_read,
    btn_control_select_read_all,
    btn_control_select_write,
    btn_modal_main_confirm,
    # 4 states
    store_unit_type_id,
    store_control_num_read_req_state,
    setter_new_shadow_data,
    control_select_input_value,  # target value
):
    """Setup the control select dropdown metadata"""

    if (
        control_select_input is None
        or store_structure_id_data is None
        or active_tab != TAB_CONTROL
    ):
        raise PreventUpdate()

    id_triggered: str = get_id_triggered()
    if not id_triggered:
        raise PreventUpdate()
    if (
        "control_tab_refresh_interval" in id_triggered
        and not store_control_num_read_req_state
    ):
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    def return_vars(
        store_control_num_read_req_data,
        control_select_units_children,
        control_select_ft_children,
        control_select_input_placeholder,
        control_cmd_cnum_value,
        control_resp_cnum_value,
        control_cmd_value,
        control_cmd_text_children,
        control_resp_value,
        control_resp_text_children,
        control_cmd_enum_value,
        control_cmd_enum_text_children,
        control_resp_enum_value,
        control_resp_enum_text_children,
        control_cmd_val_value,
        control_resp_val_value,
        control_resp_min_value,
        control_resp_max_value,
        control_resp_def_value,
        control_resp_cnum_invalid,
        control_resp_invalid,
        control_resp_enum_invalid,
        control_resp_val_invalid,
        control_resp_min_invalid,
        control_resp_max_invalid,
        control_resp_def_invalid,
        adv_ctrl_responses_children,
    ):
        """Default values for the outputs"""
        return (
            store_control_num_read_req_data,
            control_select_units_children,
            control_select_ft_children,
            control_select_input_placeholder,
            control_cmd_cnum_value,
            control_resp_cnum_value,
            control_cmd_value,
            control_cmd_text_children,
            control_resp_value,
            control_resp_text_children,
            control_cmd_enum_value,
            control_cmd_enum_text_children,
            control_resp_enum_value,
            control_resp_enum_text_children,
            control_cmd_val_value,
            control_resp_val_value,
            control_resp_min_value,
            control_resp_max_value,
            control_resp_def_value,
            control_resp_cnum_invalid,
            control_resp_invalid,
            control_resp_enum_invalid,
            control_resp_val_invalid,
            control_resp_min_invalid,
            control_resp_max_invalid,
            control_resp_def_invalid,
            adv_ctrl_responses_children,
        )

    shadow_all = get_iot_device_shadow(structure_obj.aws_thing)
    reported = shadow_all.get("state", {}).get("reported", {})

    # Get the metric abbreviation (e.g. "AGFT") and units (e.g. "PSI")
    dict_ = db_control_select_metadata(control_select_input)

    # db_abbrev = dict_["abbrev"]
    db_item = dict_["item"]
    db_units = dict_["units"] or "units"

    control_resp_cnum = reported.get("RESP_CNUM", None)
    # Check if the shadow-reported control number is the
    # same as the selected/wanted control number
    is_same = str(control_resp_cnum) == control_select_input

    resp = reported.get("RESP", None) if is_same else None
    control_resp_str = CONTROL_RESPONSE_MEANINGS.get(resp, resp)

    # This resp_enum is used below in the read command as well
    resp_enum = reported.get("RESP_ENUM", None) if is_same else None
    control_resp_enum_str = CONTROL_ENUM_MEANINGS.get(resp_enum, resp_enum)

    control_resp_val = reported.get("RESP_VAL", None) if is_same else None
    control_resp_min = reported.get("RESP_MIN", None) if is_same else None
    control_resp_max = reported.get("RESP_MAX", None) if is_same else None
    control_resp_def = reported.get("RESP_DEF", None) if is_same else None

    # Divide the values by the "enum multiplier", if necessary
    # This is NOT done on the gateway itself since it's on two separate cobs...
    if is_same:
        multiplier = CONTROL_ENUM_MULTIPLIERS.get(resp_enum, None)
        if isinstance(multiplier, Number) and Decimal(multiplier) > Decimal(0):
            multiplier = Decimal(str(multiplier))
            if isinstance(control_resp_val, Number):
                control_resp_val = Decimal(str(control_resp_val)) / multiplier
                control_resp_val = float(control_resp_val)
            if isinstance(control_resp_min, Number):
                control_resp_min = Decimal(str(control_resp_min)) / multiplier
                control_resp_min = float(control_resp_min)
            if isinstance(control_resp_max, Number):
                control_resp_max = Decimal(str(control_resp_max)) / multiplier
                control_resp_max = float(control_resp_max)
            if isinstance(control_resp_def, Number):
                control_resp_def = Decimal(str(control_resp_def)) / multiplier
                control_resp_def = float(control_resp_def)

    requesting = f"Selected '{db_item}' with control number = {control_select_input}."
    if is_same:
        # No need to poll for new data constantly, since the shadow was updated
        store_control_num_read_req_data = ""
        # Setup form text response
        control_select_ft_children = ""
    else:
        # Request new data, and poll for new data every few seconds with "control_tab_refresh_interval"
        store_control_num_read_req_data = control_select_input
        # Setup form text response
        control_select_ft_children = (
            f"Please request ('Read') the data from the controller! {requesting}"
        )

    if is_same and (
        "btn_control_select_read" in id_triggered
        or "btn_control_select_write" in id_triggered
        or "btn_modal_main_confirm" in id_triggered
    ):
        # Request new data, and poll for new data every few seconds with "control_tab_refresh_interval"
        store_control_num_read_req_data = control_select_input

    if control_resp_val is not None:
        control_select_input_placeholder = control_resp_val
    elif control_resp_def is not None:
        control_select_input_placeholder = control_resp_def
    else:
        control_select_input_placeholder = 0

    # If the dropdown value changes, or the "read" or "refresh" buttons are pressed,
    # send a command to the unit to read the latest value
    if (
        "control_select.value" == id_triggered
        or "btn_control_select_read.n_clicks" == id_triggered
        or "control_adv_rc_refresh_btn" in id_triggered
        # or (
        #     "control_tab_refresh_interval" in id_triggered
        #     and store_control_num_read_req_state
        # )
    ):
        cnum, enum, cval = get_adv_remote_control_desired_values(
            cnum=control_select_input,
            cval=None,
            enum=None,
        )
        desired = get_adv_remote_control_desired_dict(
            cnum=cnum, cval=cval, enum=enum, is_write=False
        )
        new_shadow = {"state": {"desired": desired}}
        # Send the command to the AWS IoT device shadow
        try:
            update_shadow(new_shadow, structure_obj.aws_thing)
        except Exception:
            current_app.logger.exception("Error updating shadow with new command")

    elif isinstance(setter_new_shadow_data, dict):
        # The command that was sent
        desired = setter_new_shadow_data.get("state", {}).get("desired", {})
    else:
        desired = {}

    # Sometimes desired is None after running get_adv_remote_control_desired_dict()
    desired = desired or {}

    control_cmd_cnum = control_select_input
    command_dict = desired.get("CMD_WRITE", {})
    cmd = command_dict.get("CMD", "")
    control_cmd_str = CONTROL_COMMAND_MEANINGS.get(cmd, cmd)

    # control_cmd_val = command_dict.get("CMD_VAL", "")
    if cmd == CMD_READ:
        cmd_enum = no_update
        control_cmd_enum_text = no_update
    else:
        # control_cmd_val_display = command_dict.get("CMD_VAL", "")
        cmd_enum = command_dict.get("CMD_ENUM", "")
        control_cmd_enum_text = CONTROL_ENUM_MEANINGS.get(cmd_enum, cmd_enum)

    if (
        isinstance(control_select_input_value, Number)
        and control_select_input_value != control_resp_val
    ):
        # Keep checking to see if the response value has changed to the desired/target value
        store_control_num_read_req_data = control_select_input

    adv_ctrl_responses_children = get_named_shadow_children(structure_obj.aws_thing)

    return return_vars(
        store_control_num_read_req_data=store_control_num_read_req_data,
        control_select_units_children=db_units,
        control_select_ft_children=control_select_ft_children,
        control_select_input_placeholder=control_select_input_placeholder,
        control_cmd_cnum_value=control_cmd_cnum,
        control_resp_cnum_value=control_resp_cnum,
        control_cmd_value=cmd,
        control_cmd_text_children=control_cmd_str,
        control_resp_value=resp,
        control_resp_text_children=control_resp_str,
        control_cmd_enum_value=cmd_enum,
        control_cmd_enum_text_children=control_cmd_enum_text,
        control_resp_enum_value=resp_enum,
        control_resp_enum_text_children=control_resp_enum_str,
        control_cmd_val_value=control_select_input_value,
        control_resp_val_value=control_resp_val,
        control_resp_min_value=control_resp_min,
        control_resp_max_value=control_resp_max,
        control_resp_def_value=control_resp_def,
        control_resp_cnum_invalid=not is_same,
        control_resp_invalid=not is_same,
        control_resp_enum_invalid=not is_same,
        control_resp_val_invalid=not is_same,
        control_resp_min_invalid=not is_same,
        control_resp_max_invalid=not is_same,
        control_resp_def_invalid=not is_same,
        adv_ctrl_responses_children=adv_ctrl_responses_children,
    )


@cache_memoize_if_prod(60)
def db_control_select_options(user_id: int) -> list:
    """
    Run a query to get the remote control select menu options.
    "user" doesn't get used, except to store/cache this by user
    """
    sql = text(
        """
        select
            abbrev, item, units, control_num
        from public.map_abbrev_item
        where control_num is not null
        order by item
    """
    )
    rows, _ = run_sql_query(sql, db_name="ijack")
    return rows


def get_control_select_options(user) -> list:
    """Get advanced remote control dropdown menu items, and default item"""
    rows = db_control_select_options(user)
    items_list = [
        {"label": dict_["item"], "value": dict_["control_num"]} for dict_ in rows
    ]
    return items_list


@callback(
    # Control the units vs. control the shop
    Output("control_units_row", "style"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    State("store_unit_type_id", "data"),
)
def control_tabs_unit_type(
    store_structure_id_data,
    store_tabs_for_ind_charts_control_log_data,
    store_unit_type_id_data,
):
    """
    Update the tabs for controlling the different types
    of IJACK units (e.g. UNO vs EGAS/XFER)
    """
    log_function_caller()

    if (
        store_structure_id_data is None
        or store_tabs_for_ind_charts_control_log_data != TAB_CONTROL
    ):
        return {"display": "none"}

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()

    # If it's the shop (HQ Moosomin) gateway, the speed/pressure stuff is invisible
    control_units_row_style = (
        {"display": "none"} if structure_obj.gateway == GATEWAY_SHOP else {}
    )

    return control_units_row_style


@callback(
    Output("control_prev_maint_type_select", "options"),
    Output("control_prev_maint_type_select", "value"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
)
def control_prev_maint_type_select(store_tabs_for_ind_charts_control_log_data):
    """Populate the options for the preventative maintenance type dropdown"""
    maint_types = MaintenanceType.query.all()
    if not maint_types:
        raise PreventUpdate()
    options = [{"label": mt.name, "value": mt.id} for mt in maint_types]
    return options, maint_types[0].id


@callback(
    # Output("control_start_uno", "style"),
    # Output("control_start_egas", "style"),
    # Output("control_pressure_egas", "style"),
    # Output("control_speed_egas", "style"),
    # Output("control_speed_uno", "style"),
    # Output("control_auto_uno", "style"),
    # Output("control_other_uno", "style"),
    # Output("control_on_site_setpoints_row", "style"),
    # Output("control_admin_egas_col", "style"),
    # Output("control_admin_adv_rem_ctrl", "style"),
    # Output("row_for_unogas_uno_egas_tabs", "style"),
    # #
    Output("control_max_shutdown_pressure_row", "style"),
    Output("control_vpn_row", "style"),
    Output("control_apn_row", "style"),
    Output("control_send_command_row", "style"),
    Output("control_request_card_row", "style"),
    Output("control_email_logs_row", "style"),
    # EGAS placeholders
    Output("control_suction_target_ft", "children"),  # currently X
    Output("control_suction_actual_ft", "children"),  # currently X
    Output("control_suction_target_form_feedback", "children"),
    Output("control_suction_target_input", "placeholder"),  # suggestion
    Output("control_suction_target_label", "children"),
    Output("control_suction_target_input_addon", "children"),
    Output("suction_sched_value_start_addon", "children"),
    Output("control_max_discharge_ft", "children"),
    Output("control_max_shutdown_pressure_ft", "children"),
    Output("control_max_speed_ft", "children"),
    Output("control_min_speed_ft", "children"),
    Output("control_horsepower_limit_ft", "children"),
    Output("control_pid_sensitivity_ft", "children"),
    Output("control_prev_maint_reset_date_ft", "children"),
    Output("control_prev_maint_reset_hours_ft", "children"),
    Output("control_prev_maint_current_hours_ft", "children"),
    Output("control_maintenance_interval_ft", "children"),
    Output("control_prev_maint_has_rcom_switch", "value"),
    # UNO placeholders
    Output("control_spm_max_uno_ft", "children"),
    Output("control_spm_min_uno_ft", "children"),
    Output("control_spm_set_uno_ft", "children"),
    Output("control_apft_ft", "children"),
    Output("control_pid_sensitivity_uno_ft", "children"),
    Output("control_ts_ft", "children"),
    Output("control_bs_ft", "children"),
    Output("control_auto_tap_tgt_ft", "children"),
    Output("control_peff_ft", "children"),
    Output("control_psize_ft", "children"),
    # Cemote control status messages (desired vs. reported)
    Output("control_msg_status_card_body_buttons", "style"),
    Output("control_msg_status_card_header", "children"),
    Output("control_msg_status_card_body_div", "children"),
    # Control buttons disabled? Must have permission
    Output("btn_start_uno", "disabled"),
    Output("btn_start_egas", "disabled"),
    Output("btn_lock_uno", "disabled"),
    Output("btn_lock_egas", "disabled"),
    Output("btn_control_prev_maint_reset", "disabled"),
    Output("btn_control_maintenance_interval", "disabled"),
    Output("btn_reset_velocity_control", "disabled"),
    Output("btn_control_suction_target", "disabled"),
    Output("btn_control_max_discharge_target", "disabled"),
    Output("btn_control_max_shutdown_pressure_target", "disabled"),
    Output("btn_vpn_start", "disabled"),
    Output("btn_vpn_stop", "disabled"),
    Output("btn_apn_submit", "disabled"),
    Output("btn_send_command", "disabled"),
    Output("btn_subscribe_mqtt", "disabled"),
    Output("email_logs_submit_btn", "disabled"),
    Output("btn_control_max_speed", "disabled"),
    Output("btn_control_min_speed", "disabled"),
    Output("btn_control_horsepower", "disabled"),
    Output("btn_control_pid_sensitivity", "disabled"),
    # UNO control buttons disabled? Must have permission
    Output("btn_control_spm_max_uno", "disabled"),
    Output("btn_control_spm_min_uno", "disabled"),
    Output("btn_control_spm_set_uno", "disabled"),
    Output("btn_control_apft", "disabled"),
    Output("btn_control_pid_sensitivity_uno", "disabled"),
    Output("btn_control_ts", "disabled"),
    Output("btn_control_bs", "disabled"),
    Output("btn_control_auto_tap_tgt", "disabled"),
    Output("btn_control_peff", "disabled"),
    Output("btn_control_psize", "disabled"),
    # Labels for the "start" and "lock" startup buttons
    Output("btn_start_uno", "children"),
    Output("btn_start_egas", "children"),
    Output("btn_lock_uno", "children"),
    Output("btn_lock_egas", "children"),
    Output("vpn_start_stop_ip_lbl", "children"),
    Output("apn_input_ft", "children"),
    # Button colours for the "start" and "lock" startup buttons
    Output("btn_start_uno", "color"),
    Output("btn_start_egas", "color"),
    Output("btn_lock_uno", "color"),
    Output("btn_lock_egas", "color"),
    # On-site setpoint labels
    Output("ctrl_on_site_top_accel_lbl", "children"),
    Output("ctrl_on_site_top_decel_lbl", "children"),
    Output("ctrl_on_site_btm_accel_lbl", "children"),
    Output("ctrl_on_site_btm_decel_lbl", "children"),
    Output("ctrl_on_site_top_wait_time_lbl", "children"),
    Output("ctrl_on_site_btm_wait_time_lbl", "children"),
    # On-site setpoint badge children
    Output("ctrl_on_site_top_accel_badge", "children"),
    Output("ctrl_on_site_top_decel_badge", "children"),
    Output("ctrl_on_site_btm_accel_badge", "children"),
    Output("ctrl_on_site_btm_decel_badge", "children"),
    Output("ctrl_on_site_top_wait_time_badge", "children"),
    Output("ctrl_on_site_btm_wait_time_badge", "children"),
    # On-site setpoint badge colors
    Output("ctrl_on_site_top_accel_badge", "color"),
    Output("ctrl_on_site_top_decel_badge", "color"),
    Output("ctrl_on_site_btm_accel_badge", "color"),
    Output("ctrl_on_site_btm_decel_badge", "color"),
    Output("ctrl_on_site_top_wait_time_badge", "color"),
    Output("ctrl_on_site_btm_wait_time_badge", "color"),
    # Miscellaneous
    Output("suction_sched_value_start", "value"),
    Output("control_select", "options"),
    # Output("control_select", "value"),
    Output("btn_control_select_read", "disabled"),
    Output("btn_control_select_read_all", "disabled"),
    Output("btn_control_select_write", "disabled"),
    Output("record_visit_rcom_control", "data"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("control_msg_status_refresh_btn", "n_clicks"),
    Input("control_tab_refresh_interval", "n_intervals"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    State("suction_sched_value_start", "value"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def setup_unit_control(
    store_structure_id_data,
    store_tabs_for_ind_charts_control_log_data,
    control_msg_status_refresh_btn_n_clicks,
    n_intervals,
    tabs_for_unogas_uno_egas_active_tab,
    # States
    suction_sched_value_start,
    store_unit_type_id_data,
):
    """
    Update the values on the remote control tab. This happens right away when the control tab is selected,
    to set whether the buttons are clickable or not.
    """
    log_function_caller()

    if (
        store_structure_id_data is None
        or store_tabs_for_ind_charts_control_log_data != TAB_CONTROL
    ):
        # return return_variables()
        raise PreventUpdate()

    id_triggered: str = get_id_triggered()
    if id_triggered is None:
        raise PreventUpdate()

    if "control_tab_refresh_interval" in id_triggered and (
        n_intervals > 100
        or os.getenv("FLASK_CONFIG", "production") in ("development", "wsl")
    ):
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()
    has_rcom: bool = bool(structure_obj.aws_thing)
    is_vessel_level_mode: bool = structure_obj.suction_range == 4
    control_prev_maint_has_rcom_switch_value = structure_obj.has_rcom

    # # Initialize return values
    # control_start_uno_style = {"display": "none"}
    # control_start_egas_style = {"display": "none"}
    # control_pressure_egas_style = {"display": "none"}
    # control_speed_egas_style = {"display": "none"}
    # control_speed_uno_style = {"display": "none"}
    # control_auto_uno_style = {"display": "none"}
    # control_other_uno_style = {"display": "none"}
    # control_on_site_setpoints_row_style = {"display": "none"}
    # control_admin_egas_col_style = {"display": "none"}
    # control_admin_adv_rem_ctrl_style = {"display": "none"}
    # row_for_unogas_uno_egas_tabs_style = {"display": "none"}
    # Default values
    control_max_shutdown_pressure_row_style = {"display": "none"}
    control_vpn_row_style = {"display": "none"}
    control_apn_row_style = {"display": "none"}
    control_send_command_row_style = {"display": "none"}
    control_request_card_row_style = {"display": "none"}
    control_email_logs_row_style = {"display": "none"}
    control_suction_target_ft_children = ""
    control_suction_actual_ft_children = ""
    control_suction_target_form_feedback_children = ""
    control_suction_target_input_placeholder = ""
    control_suction_target_label_children = "Suction Target"
    control_suction_target_input_addon_children = "PSI"
    suction_sched_value_start_addon_children = "PSI"
    control_max_discharge_ft_children = ""
    control_max_shutdown_pressure_ft_children = ""
    control_max_speed_ft_children = ""
    control_min_speed_ft_children = ""
    control_horsepower_limit_ft_children = ""
    control_pid_sensitivity_ft_children = ""
    control_prev_maint_reset_date_ft_children = ""
    control_prev_maint_reset_hours_ft_children = ""
    control_prev_maint_current_hours_ft_children = ""
    control_maintenance_interval_ft_children = ""
    control_spm_max_uno_ft_children = ""
    control_spm_min_uno_ft_children = ""
    control_spm_set_uno_ft_children = ""
    control_apft_ft_children = ""
    control_pid_sensitivity_uno_ft_children = ""
    control_ts_ft_children = ""
    control_bs_ft_children = ""
    control_auto_tap_tgt_ft_children = ""
    control_peff_ft_children = ""
    control_psize_ft_children = ""
    control_msg_status_card_body_buttons_style = {"display": "none"}
    control_msg_status_card_header_children = ""
    control_msg_status_card_body_div_children = ""
    btn_start_uno_disabled = True
    btn_start_egas_disabled = True
    btn_lock_uno_disabled = True
    btn_lock_egas_disabled = True
    btn_control_prev_maint_reset_disabled = True
    btn_control_maintenance_interval_disabled = True
    btn_reset_velocity_control_disabled = True
    btn_control_suction_target_disabled = True
    btn_control_max_discharge_target_disabled = True
    btn_control_max_shutdown_pressure_target_disabled = True
    btn_vpn_start_disabled = True
    btn_vpn_stop_disabled = True
    btn_apn_submit_disabled = True
    btn_send_command_disabled = True
    btn_subscribe_mqtt_disabled = True
    email_logs_submit_btn_disabled = True
    btn_control_max_speed_disabled = True
    btn_control_min_speed_disabled = True
    btn_control_horsepower_disabled = True
    btn_control_pid_sensitivity_disabled = True
    btn_control_spm_max_uno_disabled = True
    btn_control_spm_min_uno_disabled = True
    btn_control_spm_set_uno_disabled = True
    btn_control_apft_disabled = True
    btn_control_pid_sensitivity_uno_disabled = True
    btn_control_ts_disabled = True
    btn_control_bs_disabled = True
    btn_control_auto_tap_tgt_disabled = True
    btn_control_peff_disabled = True
    btn_control_psize_disabled = True
    btn_start_uno_children = ""
    btn_start_egas_children = ""
    btn_lock_uno_children = ""
    btn_lock_egas_children = ""
    vpn_start_stop_ip_lbl_children = ""
    apn_input_ft_children = "Enter the APN string for the cellular connection"
    btn_start_uno_color = "danger"
    btn_start_egas_color = "danger"
    btn_lock_uno_color = "danger"
    btn_lock_egas_color = "danger"
    ctrl_on_site_top_accel_lbl_children = ""
    ctrl_on_site_top_decel_lbl_children = ""
    ctrl_on_site_btm_accel_lbl_children = ""
    ctrl_on_site_btm_decel_lbl_children = ""
    ctrl_on_site_top_wait_time_lbl_children = ""
    ctrl_on_site_btm_wait_time_lbl_children = ""
    ctrl_on_site_top_accel_badge_children = ""
    ctrl_on_site_top_decel_badge_children = ""
    ctrl_on_site_btm_accel_badge_children = ""
    ctrl_on_site_btm_decel_badge_children = ""
    ctrl_on_site_top_wait_time_badge_children = ""
    ctrl_on_site_btm_wait_time_badge_children = ""
    ctrl_on_site_top_accel_badge_color = "secondary"
    ctrl_on_site_top_decel_badge_color = "secondary"
    ctrl_on_site_btm_accel_badge_color = "secondary"
    ctrl_on_site_btm_decel_badge_color = "secondary"
    ctrl_on_site_top_wait_time_badge_color = "secondary"
    ctrl_on_site_btm_wait_time_badge_color = "secondary"
    suction_sched_value_start_value = None
    control_select_options = None
    btn_control_select_read_disabled = True
    btn_control_select_read_all_disabled = True
    btn_control_select_write_disabled = True
    record_visit_rcom_control_data = no_update

    def return_variables():
        """Default return variables"""
        return (
            # control_start_uno_style,
            # control_start_egas_style,
            # control_pressure_egas_style,
            # control_speed_egas_style,
            # control_speed_uno_style,
            # control_auto_uno_style,
            # control_other_uno_style,
            # control_on_site_setpoints_row_style,
            # control_admin_egas_col_style,
            # control_admin_adv_rem_ctrl_style,
            # row_for_unogas_uno_egas_tabs_style,
            # the above were moved to nav.py
            #
            control_max_shutdown_pressure_row_style,
            control_vpn_row_style,
            control_apn_row_style,
            control_send_command_row_style,
            control_request_card_row_style,
            control_email_logs_row_style,
            control_suction_target_ft_children,
            control_suction_actual_ft_children,
            control_suction_target_form_feedback_children,
            control_suction_target_input_placeholder,
            control_suction_target_label_children,
            control_suction_target_input_addon_children,
            suction_sched_value_start_addon_children,
            control_max_discharge_ft_children,
            control_max_shutdown_pressure_ft_children,
            control_max_speed_ft_children,
            control_min_speed_ft_children,
            control_horsepower_limit_ft_children,
            control_pid_sensitivity_ft_children,
            control_prev_maint_reset_date_ft_children,
            control_prev_maint_reset_hours_ft_children,
            control_prev_maint_current_hours_ft_children,
            control_maintenance_interval_ft_children,
            control_prev_maint_has_rcom_switch_value,
            control_spm_max_uno_ft_children,
            control_spm_min_uno_ft_children,
            control_spm_set_uno_ft_children,
            control_apft_ft_children,
            control_pid_sensitivity_uno_ft_children,
            control_ts_ft_children,
            control_bs_ft_children,
            control_auto_tap_tgt_ft_children,
            control_peff_ft_children,
            control_psize_ft_children,
            control_msg_status_card_body_buttons_style,
            control_msg_status_card_header_children,
            control_msg_status_card_body_div_children,
            btn_start_uno_disabled,
            btn_start_egas_disabled,
            btn_lock_uno_disabled,
            btn_lock_egas_disabled,
            btn_control_prev_maint_reset_disabled,
            btn_control_maintenance_interval_disabled,
            btn_reset_velocity_control_disabled,
            btn_control_suction_target_disabled,
            btn_control_max_discharge_target_disabled,
            btn_control_max_shutdown_pressure_target_disabled,
            btn_vpn_start_disabled,
            btn_vpn_stop_disabled,
            btn_apn_submit_disabled,
            btn_send_command_disabled,
            btn_subscribe_mqtt_disabled,
            email_logs_submit_btn_disabled,
            btn_control_max_speed_disabled,
            btn_control_min_speed_disabled,
            btn_control_horsepower_disabled,
            btn_control_pid_sensitivity_disabled,
            btn_control_spm_max_uno_disabled,
            btn_control_spm_min_uno_disabled,
            btn_control_spm_set_uno_disabled,
            btn_control_apft_disabled,
            btn_control_pid_sensitivity_uno_disabled,
            btn_control_ts_disabled,
            btn_control_bs_disabled,
            btn_control_auto_tap_tgt_disabled,
            btn_control_peff_disabled,
            btn_control_psize_disabled,
            btn_start_uno_children,
            btn_start_egas_children,
            btn_lock_uno_children,
            btn_lock_egas_children,
            vpn_start_stop_ip_lbl_children,
            apn_input_ft_children,
            btn_start_uno_color,
            btn_start_egas_color,
            btn_lock_uno_color,
            btn_lock_egas_color,
            ctrl_on_site_top_accel_lbl_children,
            ctrl_on_site_top_decel_lbl_children,
            ctrl_on_site_btm_accel_lbl_children,
            ctrl_on_site_btm_decel_lbl_children,
            ctrl_on_site_top_wait_time_lbl_children,
            ctrl_on_site_btm_wait_time_lbl_children,
            ctrl_on_site_top_accel_badge_children,
            ctrl_on_site_top_decel_badge_children,
            ctrl_on_site_btm_accel_badge_children,
            ctrl_on_site_btm_decel_badge_children,
            ctrl_on_site_top_wait_time_badge_children,
            ctrl_on_site_btm_wait_time_badge_children,
            ctrl_on_site_top_accel_badge_color,
            ctrl_on_site_top_decel_badge_color,
            ctrl_on_site_btm_accel_badge_color,
            ctrl_on_site_btm_decel_badge_color,
            ctrl_on_site_top_wait_time_badge_color,
            ctrl_on_site_btm_wait_time_badge_color,
            suction_sched_value_start_value,
            control_select_options,
            btn_control_select_read_disabled,
            btn_control_select_read_all_disabled,
            btn_control_select_write_disabled,
            record_visit_rcom_control_data,
        )

    if not has_rcom:
        control_msg_status_card_header_children = "Remote Control Messages"
        control_msg_status_card_body_div_children = html.H3("Unit doesn't have RCOM")
        return return_variables()

    # #####################################################################################################
    # #####################################################################################################
    # #####################################################################################################
    # #####################################################################################################

    # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
    #     # Just for debugging
    #     id_triggered: str = get_id_triggered()
    #     current_app.logger.info(f"id_triggered: {id_triggered}")

    is_ijack_employee: bool = user_is_ijack_employee(user_id=user_id)

    # structure_obj: StructureVw | None = get_structure_obj(
    #     store_structure_id_data, user_id, store_unit_type_id_data
    # )
    # if structure_obj is None:
    #     raise PreventUpdate()
    # unit_type_id = structure_obj.unit_type_id

    # if unit_type_id == UNIT_TYPE_ID_UNOGAS:
    #     # Display the second set of tabs so the user can choose UNO or EGAS
    #     row_for_unogas_uno_egas_tabs_style = {}
    #     # It has to be either UNO type or EGAS/XFER type so we know which controls to display below
    #     _, unit_type_id = get_unit_type(
    #         tabs_for_unogas_uno_egas_active_tab, store_unit_type_id_data
    #     )

    # if unit_type_id in UNIT_TYPES_IDS_XFER_EGAS:
    #     # Change the active tab to the EGAS/XFER tab
    #     tabs_for_unogas_uno_egas_active_tab = TAB_UNOGAS_EGAS
    #     control_start_egas_style = {}
    #     control_pressure_egas_style = {}
    #     control_speed_egas_style = {}
    #     control_on_site_setpoints_row_style = {}
    #     # Remove the IJACK customer_id requirement when this goes to production (now it's just testing)
    #     if is_ijack_employee:
    #         control_admin_egas_col_style = {}
    # else:
    #     tabs_for_unogas_uno_egas_active_tab = TAB_UNOGAS_UNO
    #     control_start_uno_style = {}
    #     control_speed_uno_style = {}
    #     control_auto_uno_style = {}
    #     control_other_uno_style = {}

    # if is_ijack_employee:
    #     control_admin_adv_rem_ctrl_style = {}

    # #####################################################################################################
    # #####################################################################################################
    # #####################################################################################################
    # #####################################################################################################

    # If it's an UNO unit_type, the "pressure" card/row is not visible
    unit_type_lower, unit_type_id = get_unit_type(
        tabs_for_unogas_uno_egas_active_tab, store_unit_type_id_data
    )

    # Request a data refresh from the gateway
    d = {"state": {"desired": {"AWS_REFRESH": randint(1, 65535)}}}
    try:
        update_shadow(d, structure_obj.aws_thing)
    except Exception:
        current_app.logger.exception("Error updating the AWS shadow")

    # Get the AWS IoT device shadow (latest indicators)
    # The above update_shadow() only gets the metrics it updates
    shadow_all = get_iot_device_shadow(structure_obj.aws_thing)

    # Compare the 'reported' target with the '<target>' and find the latest by timestamp
    reported = shadow_all.get("state", {}).get("reported", {})
    latest_targets = get_latest_targets_f_shadow(shadow_all)

    # EGAS targets for the placeholders
    suction_target = latest_targets.get("suction_target", None)
    suction_actual = latest_targets.get("suction_actual", None)
    if isinstance(suction_target, dict):
        # It's a scheduled command
        suction_target = suction_target.get("value_desired", None)

    max_discharge = latest_targets.get("max_discharge", None)
    max_speed = latest_targets.get("max_speed", None)
    min_speed = latest_targets.get("min_speed", None)
    horsepower_limit = latest_targets.get("horsepower_limit", None)
    max_discharge = latest_targets.get("max_discharge", None)
    max_shutdown_pressure = latest_targets.get("max_shutdown_pressure", None)
    pid_sensitivity = latest_targets.get("pid_sensitivity", None)

    # 8 UNO targets for the placeholders
    control_spm_max_uno = latest_targets.get("spm_max", None)
    control_spm_min_uno = latest_targets.get("spm_min", None)
    control_spm_set_uno = latest_targets.get("spm_set", None)
    control_apft = latest_targets.get("apft", None)
    control_pid_sensitivity_uno = latest_targets.get("pid_uno", None)
    control_ts = latest_targets.get("ts", None)
    control_bs = latest_targets.get("bs", None)
    control_auto_tap_tgt = latest_targets.get("auto_tap_tgt", None)
    control_peff = latest_targets.get("peff", None)
    control_psize = latest_targets.get("psize", None)

    # For VPN access
    vpn_start_stop_ip_lbl_children = latest_targets.get("vpn_ip", "Unknown")
    apn_input_ft_children = (
        f"Current APN: {latest_targets.get('current_apn', 'Unknown')}"
    )

    lockout_metric = "LOCKOUT" if unit_type_id == UNIT_TYPE_ID_UNO else "LOCKOUT_EGAS"
    locked = reported.get(lockout_metric) == 1

    is_stroking = check_is_stroking(unit_type_lower, shadow_all)

    default = "Not currently set"
    if is_vessel_level_mode:
        units = "%"
    else:
        units = " PSI"

    control_suction_target_ft_children = (
        f"Target: currently {suction_target}{units}"
        if suction_target is not None
        else default
    )
    control_suction_actual_ft_children = (
        f"Actual: currently {suction_actual}{units}"
        if suction_actual is not None
        else default
    )
    control_max_discharge_ft_children = (
        f"Currently {max_discharge} PSI" if max_discharge is not None else default
    )
    control_max_shutdown_pressure_ft_children = (
        f"Currently {max_shutdown_pressure} PSI"
        if max_shutdown_pressure is not None
        else default
    )
    control_max_speed_ft_children = (
        f"Currently {max_speed}%" if max_speed is not None else default
    )
    control_min_speed_ft_children = (
        f"Currently {min_speed}%" if min_speed is not None else default
    )
    control_horsepower_limit_ft_children = (
        f"Currently {horsepower_limit} HP" if horsepower_limit is not None else default
    )
    control_pid_sensitivity_ft_children = (
        f"Currently {pid_sensitivity} PID" if pid_sensitivity is not None else default
    )

    # Hours between preventative maintenance resets
    next_interval: str = "Unknown"
    last_maintenance_date = structure_obj.structure_install_date
    filters = [Maintenance.structure_id == structure_obj.id]
    power_unit_id: int = getattr(structure_obj, "power_unit_id", None)
    if isinstance(power_unit_id, int):
        filters.append(Maintenance.power_unit_id == power_unit_id)
    maint_record = (
        Maintenance.query.filter(or_(*filters))
        .order_by(Maintenance.timestamp_utc.desc())
        .first()
    )
    if (
        maint_record
        and isinstance(maint_record.timestamp_utc, datetime)
        and isinstance(last_maintenance_date, datetime)
    ):
        if maint_record.timestamp_utc > last_maintenance_date:
            last_maintenance_date = maint_record.timestamp_utc

    # work_order = (
    #     WorkOrder.query.join(
    #         work_order_structure_rel,
    #         WorkOrder.id == work_order_structure_rel.c.work_order_id,
    #     )
    #     .filter(
    #         work_order_structure_rel.c.structure_id == structure_obj.id,
    #         WorkOrder.service_type_id.in_(
    #             [
    #                 SERVICE_TYPE_ID_NEW_INSTALLATION,
    #                 SERVICE_TYPE_ID_REPAIR,
    #                 SERVICE_TYPE_ID_PARTS,
    #                 SERVICE_TYPE_ID_PREV_MAINT,
    #             ]
    #         ),
    #     )
    #     .order_by(WorkOrder.date_service.desc())
    #     .first()
    # )
    # if (
    #     work_order
    #     and isinstance(work_order.date_service, (datetime, date))
    #     and isinstance(last_maintenance_date, (datetime, date))
    # ):
    #     if work_order.date_service > last_maintenance_date:
    #         last_maintenance_date = work_order.date_service

    control_maintenance_interval_ft_children = (
        f"Currently {structure_obj.op_months_interval} months"
    )

    if maint_record is None:
        control_prev_maint_reset_hours_ft_children = "Last maintenance date: Unknown"
        next_interval = f"{structure_obj.op_months_interval} months"
    else:
        last_maintenance_date = maint_record.timestamp_utc.strftime("%Y-%m-%d")
        if isinstance(maint_record.op_hours, (float, int)) and isinstance(
            structure_obj.op_months_interval, (float, int)
        ):
            next_interval = f"{(maint_record.op_hours / (24 * 30.4375) + structure_obj.op_months_interval):,.0f} months"

    if isinstance(structure_obj.op_hours, (float, int)):
        control_prev_maint_current_hours_ft_children = (
            f"Operating months: {structure_obj.op_hours / (24 * 30.4375):,.1f}"
        )
    else:
        control_prev_maint_current_hours_ft_children = "Operating months: Unknown"

    control_prev_maint_reset_hours_ft_children = f"Next maintenance: {next_interval}"

    control_prev_maint_reset_date_ft_children = (
        f"Last maintenance date: {last_maintenance_date}"
    )

    # 10 UNO placeholders
    control_spm_max_uno_ft_children = (
        f"Currently {control_spm_max_uno} SPM"
        if control_spm_max_uno is not None
        else default
    )
    control_spm_min_uno_ft_children = (
        f"Currently {control_spm_min_uno} SPM"
        if control_spm_min_uno is not None
        else default
    )
    control_spm_set_uno_ft_children = (
        f"Currently {control_spm_set_uno} SPM"
        if control_spm_set_uno is not None
        else default
    )
    # control_horsepower_limit_uno_ph = f'Currently {control_horsepower_limit_uno} HP' if control_horsepower_limit_uno is not None else default
    control_apft_ft_children = (
        f"Currently {control_apft}%" if control_apft is not None else default
    )
    control_pid_sensitivity_uno_ft_children = (
        f"Currently {control_pid_sensitivity_uno} PID"
        if control_pid_sensitivity_uno is not None
        else default
    )
    control_ts_ft_children = (
        f"Currently {control_ts} inches" if control_ts is not None else default
    )
    control_bs_ft_children = (
        f"Currently {control_bs} inches" if control_bs is not None else default
    )
    control_auto_tap_tgt_ft_children = (
        f"Currently {control_auto_tap_tgt}%"
        if control_auto_tap_tgt is not None
        else default
    )
    control_peff_ft_children = (
        f"Currently {control_peff}%" if control_peff is not None else default
    )
    control_psize_ft_children = (
        f"Currently {control_psize}" if control_psize is not None else default
    )

    abilities_dict: PermissionsAbilities = get_card_body_permissions_abilities(
        shadow_all, structure_obj
    )
    has_abilities = abilities_dict.get("has_abilities", False)
    warnings_list = abilities_dict.get("card_body", False)
    can_set_apn = abilities_dict.get("can_set_apn", False)

    # software version PLC (Dan's software)
    try:
        swv_plc = float(reported.get("SWV", 0))
    except Exception:
        swv_plc = 9999

    if is_vessel_level_mode:
        control_suction_target_form_feedback_children = (
            "Please enter a number from 0-100"
        )
        control_suction_target_input_placeholder = "Enter 0-100"
        control_suction_target_label_children = "Vessel Level Target"
        control_suction_target_input_addon_children = "%"
        suction_sched_value_start_addon_children = "%"
    elif 309 <= swv_plc < 316:
        # Allow values > 250 for the suction pressure target
        control_suction_target_form_feedback_children = (
            "Please enter a number from 1-700"
        )
        control_suction_target_input_placeholder = "Enter 1-700"
    elif 316 <= swv_plc < 414:
        # For EGAS-VRU, allow small numbers from 0-1 PSI
        control_suction_target_form_feedback_children = (
            "Please enter a number from 0-700"
        )
        control_suction_target_input_placeholder = "Enter 0-700"
    elif 414 <= swv_plc < 100_000:
        control_suction_target_form_feedback_children = (
            "Please enter a number from -8 to 700"
        )
        control_suction_target_input_placeholder = "Enter -8 to 700"
    else:
        # Only allow values up to 250 for the suction pressure target
        control_suction_target_form_feedback_children = (
            "Please enter a number from 1-250"
        )
        control_suction_target_input_placeholder = "Enter 1-250"

    control_max_shutdown_pressure_row_style = (
        {"display": "none"} if swv_plc < 310 else {}
    )

    # If the current user has permission to "set" targets (i.e. perform remote control),
    # make the button's "disabled" property = False; otherwise disabled=True
    btn_start_uno_disabled = False if has_abilities else True
    btn_start_egas_disabled = False if has_abilities else True
    # stop = False if has_abilities else True
    # unlock = False if has_abilities else True
    btn_lock_uno_disabled = False if has_abilities else True
    btn_lock_egas_disabled = False if has_abilities else True
    # Remove the IJACK customer_id requirement when this goes to production (now it's just testing)
    btn_reset_velocity_control_disabled = (
        False if (has_abilities and is_ijack_employee) else True
    )
    btn_control_prev_maint_reset_disabled = btn_reset_velocity_control_disabled
    btn_control_maintenance_interval_disabled = btn_reset_velocity_control_disabled
    btn_control_select_read_disabled = btn_reset_velocity_control_disabled
    btn_control_select_read_all_disabled = btn_control_select_read_disabled
    btn_control_select_write_disabled = btn_control_select_read_disabled
    btn_control_suction_target_disabled = False if has_abilities else True
    btn_control_max_discharge_target_disabled = False if has_abilities else True
    btn_control_max_shutdown_pressure_target_disabled = False if has_abilities else True

    # only Sean can enable this
    if getattr(current_user, "id", None) == USER_ID_SEAN:
        btn_vpn_start_disabled = False
        btn_vpn_stop_disabled = False
        btn_apn_submit_disabled = False
        btn_send_command_disabled = False
        btn_subscribe_mqtt_disabled = False
        email_logs_submit_btn_disabled = False
        control_vpn_row_style = {}
        # Who can request the email logs?
        control_email_logs_row_style = {}
        # Who can send a command to the unit?
        control_send_command_row_style = {}

    if is_ijack_employee:
        # Who can request new compression and surface cards?
        control_request_card_row_style = {}
        # Who can set the APN manually?
        if can_set_apn:
            control_apn_row_style = {}

    btn_control_max_speed_disabled = not has_abilities
    btn_control_min_speed_disabled = not has_abilities
    btn_control_horsepower_disabled = not has_abilities
    btn_control_pid_sensitivity_disabled = not has_abilities

    # 10 UNO control buttons disabled? Must have permission
    btn_control_spm_max_uno_disabled = not has_abilities
    btn_control_spm_min_uno_disabled = not has_abilities
    btn_control_spm_set_uno_disabled = not has_abilities
    btn_control_apft_disabled = not has_abilities
    btn_control_pid_sensitivity_uno_disabled = not has_abilities
    btn_control_ts_disabled = not has_abilities
    btn_control_bs_disabled = not has_abilities
    btn_control_auto_tap_tgt_disabled = not has_abilities
    btn_control_peff_disabled = not has_abilities
    btn_control_psize_disabled = not has_abilities

    # Labels for the "start" and "lock" startup buttons
    btn_start_uno_children = "Running" if is_stroking else "Stopped"
    btn_start_egas_children = "Running" if is_stroking else "Stopped"
    btn_lock_uno_children = "Locked" if locked else "Unlocked"
    btn_lock_egas_children = "Locked" if locked else "Unlocked"
    # Button colours for the "start" and "lock" startup buttons
    btn_start_uno_color = "success" if is_stroking else "danger"
    btn_start_egas_color = "success" if is_stroking else "danger"
    btn_lock_uno_color = "danger" if locked else "success"
    btn_lock_egas_color = "danger" if locked else "success"

    # Display the current remote control status (desired vs. reported)
    (
        control_msg_collapse_is_open,
        control_msg_status_card_header_children,
        control_msg_status_card_body_div_children,
    ) = get_card_body_control_progress(
        structure_obj.aws_thing,
        shadow_all,
        structure_obj,
        warnings_list,
        tabs_for_unogas_uno_egas_active_tab,
        store_unit_type_id_data,
    )

    # # Record when this tab was last accessed, so we can time out the interval-refresh after 5 minutes or something
    # control_tab_timestamp_start = time.time()

    # Optional schedule starting values
    if suction_sched_value_start is None:
        suction_sched_value_start_value = suction_target
    else:
        suction_sched_value_start_value = suction_sched_value_start

    # Get the options available for the advanced remote control select dropdown
    control_select_options = get_control_select_options(current_user)
    # # Specify a default value
    # control_select_value = (
    #     control_select_value
    #     if control_select_value is not None
    #     else control_select_options[0]["value"]
    # )
    control_msg_status_card_body_buttons_style = {}

    os_setpoints = get_on_site_setpoints(reported)
    ctrl_on_site_top_accel_lbl_children = os_setpoints["top_accel"]["label"]
    ctrl_on_site_top_decel_lbl_children = os_setpoints["top_decel"]["label"]
    ctrl_on_site_btm_accel_lbl_children = os_setpoints["btm_accel"]["label"]
    ctrl_on_site_btm_decel_lbl_children = os_setpoints["btm_decel"]["label"]
    ctrl_on_site_top_wait_time_lbl_children = os_setpoints["top_wait_time"]["label"]
    ctrl_on_site_btm_wait_time_lbl_children = os_setpoints["btm_wait_time"]["label"]
    ctrl_on_site_top_accel_badge_children = os_setpoints["top_accel"]["msg"]
    ctrl_on_site_top_decel_badge_children = os_setpoints["top_decel"]["msg"]
    ctrl_on_site_btm_accel_badge_children = os_setpoints["btm_accel"]["msg"]
    ctrl_on_site_btm_decel_badge_children = os_setpoints["btm_decel"]["msg"]
    ctrl_on_site_top_wait_time_badge_children = os_setpoints["top_wait_time"]["msg"]
    ctrl_on_site_btm_wait_time_badge_children = os_setpoints["btm_wait_time"]["msg"]
    ctrl_on_site_top_accel_badge_color = os_setpoints["top_accel"]["color"]
    ctrl_on_site_top_decel_badge_color = os_setpoints["top_decel"]["color"]
    ctrl_on_site_btm_accel_badge_color = os_setpoints["btm_accel"]["color"]
    ctrl_on_site_btm_decel_badge_color = os_setpoints["btm_decel"]["color"]
    ctrl_on_site_top_wait_time_badge_color = os_setpoints["top_wait_time"]["color"]
    ctrl_on_site_btm_wait_time_badge_color = os_setpoints["btm_wait_time"]["color"]

    record_visit_rcom_control_data = "rcom_control"

    return return_variables()


@callback(
    # 2 UNO automatic controls (switches and radios)
    Output("control_uno_auto_stuff", "value"),
    Output("control_uno_auto_type", "value"),
    # 1 form text
    Output("control_uno_auto_type_ft", "children"),
    # 2 UNO control buttons disabled? Must have permission
    Output("btn_control_uno_auto_stuff", "disabled"),
    Output("btn_control_uno_auto_type", "disabled"),
    # 1 SPM setpoint row style
    Output("spm_setpoint_row", "style"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("control_msg_status_refresh_btn", "n_clicks"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    State("store_unit_type_id", "data"),
)
def setup_uno_automatic_switches_radios(
    store_structure_id_data,
    store_tabs_for_ind_charts_control_log_data,
    refresh_status_clicks,
    tabs_for_unogas_uno_egas_active_tab,
    store_unit_type_id_data,
):
    """Update the disabled parameter on the UNO automatic switches' submit buttons"""
    log_function_caller()

    # How many items to return?
    n_needed = 6

    if (
        store_tabs_for_ind_charts_control_log_data != TAB_CONTROL
        or store_structure_id_data is None
    ):
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()
    aws_thing = structure_obj.aws_thing
    has_rcom: bool = bool(structure_obj.aws_thing)

    if not has_rcom:
        return_tuple = (
            # 2 UNO automatic controls (switches and radios)
            # The switch/checklist must be an empty list, not None, or there will be a JavaScript error!
            [],
            None,
            # 1 form text
            None,
            # 2 UNO control buttons disabled? Must have permission
            True,
            True,
            # 1 SPM setpoint row style
            {"display": "none"},
        )
        return verify_return_tuple(return_tuple, n_needed=n_needed)

    # Get the AWS IoT device shadow (latest indicators)
    # The above update_shadow() only gets the metrics it updates
    shadow_all = get_iot_device_shadow(aws_thing)

    # Compare the 'reported' target with the '<target>' and find the latest by timestamp
    latest_targets = get_latest_targets_f_shadow(shadow_all)

    # 2 automatic switches/radios
    control_uno_auto_stuff_list = latest_targets["control_uno_auto_stuff_list"]
    control_uno_auto_type_value = latest_targets["control_uno_auto_type_value"]

    # Don't show the SPM setpoint if auto-SPM is turned on
    spm_setpoint_row_style = (
        {"display": "none"} if "AUTO_SPM" in control_uno_auto_stuff_list else {}
    )

    control_uno_auto_type_ft = (
        None if control_uno_auto_type_value is not None else "Not currently set"
    )

    abilities_dict: PermissionsAbilities = get_card_body_permissions_abilities(
        shadow_all, structure_obj
    )
    has_abilities: bool = abilities_dict.get("has_abilities", False)

    # 2 UNO control buttons disabled? Must have permission
    btn_control_uno_auto_stuff_disabled = not (has_abilities)
    btn_control_uno_auto_type_disabled = not (has_abilities)

    return_tuple = (
        # 2 UNO automatic controls (switches and radios)
        control_uno_auto_stuff_list,
        control_uno_auto_type_value,
        # 1 form text
        control_uno_auto_type_ft,
        # 2 UNO control buttons disabled? Must have permission
        btn_control_uno_auto_stuff_disabled,
        btn_control_uno_auto_type_disabled,
        # 1 SPM setpoint row style
        spm_setpoint_row_style,
    )
    return verify_return_tuple(return_tuple, n_needed=n_needed)


@callback(
    Output("control_shop_row", "style"),  # Control the shop
    # Control buttons enabled? Must have permission
    # Output("btn_unlock_all", "disabled"),
    Output("btn_unlock_office", "disabled"),
    Output("btn_unlocked_shop", "disabled"),
    # Output("btn_unlock_gym", "disabled"),
    Output("btn_lights_north_on", "disabled"),
    Output("btn_lights_south_on", "disabled"),
    Output("btn_lights_outdoor_on", "disabled"),
    Output("btn_hrv_fast_on", "disabled"),
    Output("btn_fan_office_main_on", "disabled"),
    Output("btn_ohd_west_open", "disabled"),
    Output("btn_ohd_west_close", "disabled"),
    Output("btn_ohd_west_stop", "disabled"),
    Output("btn_ohd_east_open", "disabled"),
    Output("btn_ohd_east_close", "disabled"),
    Output("btn_ohd_east_stop", "disabled"),
    # Button label/children (lock, unlock, on, off)
    # Output("btn_unlock_all", "children"),
    Output("btn_unlock_office", "children"),
    Output("btn_unlocked_shop", "children"),
    # Output("btn_unlock_gym", "children"),
    Output("btn_lights_north_on", "children"),
    Output("btn_lights_south_on", "children"),
    Output("btn_lights_outdoor_on", "children"),
    Output("btn_hrv_fast_on", "children"),
    Output("btn_fan_office_main_on", "children"),
    Output("btn_ohd_west_open", "children"),
    Output("btn_ohd_west_close", "children"),
    Output("btn_ohd_west_stop", "children"),
    Output("btn_ohd_east_open", "children"),
    Output("btn_ohd_east_close", "children"),
    Output("btn_ohd_east_stop", "children"),
    # Button colour (green/'success' or red/'danger')
    # Output("btn_unlock_all", "color"),
    Output("btn_unlock_office", "color"),
    Output("btn_unlocked_shop", "color"),
    # Output("btn_unlock_gym", "color"),
    Output("btn_lights_north_on", "color"),
    Output("btn_lights_south_on", "color"),
    Output("btn_lights_outdoor_on", "color"),
    Output("btn_hrv_fast_on", "color"),
    Output("btn_fan_office_main_on", "color"),
    Output("btn_ohd_west_open", "color"),
    Output("btn_ohd_west_close", "color"),
    Output("btn_ohd_west_stop", "color"),
    Output("btn_ohd_east_open", "color"),
    Output("btn_ohd_east_close", "color"),
    Output("btn_ohd_east_stop", "color"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("control_msg_status_refresh_btn", "n_clicks"),
    Input("control_tab_refresh_interval", "n_intervals"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def setup_shop_control(
    store_structure_id_data,
    store_tabs_for_ind_charts_control_log_data,
    refresh_status_clicks,
    n_intervals,
    store_unit_type_id_data,
):
    """
    Update the values on the remote control tab for the HQ shop control.
    This happens right away when the control tab is selected,
    to set whether the buttons are clickable or not.
    """
    log_function_caller()

    if (
        store_structure_id_data is None
        or store_tabs_for_ind_charts_control_log_data != TAB_CONTROL
    ):
        raise PreventUpdate()
        # return return_vars()

    def return_vars(
        control_shop_row_style: dict = {"display": "none"},
        btn_unlock_office_disabled: bool = True,
        btn_unlocked_shop_disabled: bool = True,
        # btn_unlock_gym_disabled: bool = True,
        btn_lights_north_on_disabled: bool = True,
        btn_lights_south_on_disabled: bool = True,
        btn_lights_outdoor_on_disabled: bool = True,
        btn_hrv_fast_on_disabled: bool = True,
        btn_fan_office_main_on_disabled: bool = True,
        btn_ohd_west_open_disabled: bool = True,
        btn_ohd_west_close_disabled: bool = True,
        btn_ohd_west_stop_disabled: bool = True,
        btn_ohd_east_open_disabled: bool = True,
        btn_ohd_east_close_disabled: bool = True,
        btn_ohd_east_stop_disabled: bool = True,
        # Button label/children (lock, unlock, on, off)
        # btn_unlock_all_children: str = "",
        btn_unlock_office_children: str = "",
        btn_unlocked_shop_children: str = "",
        # btn_unlock_gym_children: str = "",
        btn_lights_north_on_children: str = "",
        btn_lights_south_on_children: str = "",
        btn_lights_outdoor_on_children: str = "",
        btn_hrv_fast_on_children: str = "",
        btn_fan_office_main_on_children: str = "",
        btn_ohd_west_open_children: str = "",
        btn_ohd_west_close_children: str = "",
        btn_ohd_west_stop_children: str = "",
        btn_ohd_east_open_children: str = "",
        btn_ohd_east_close_children: str = "",
        btn_ohd_east_stop_children: str = "",
        # Button colour (green/'success' or red/'danger')
        # btn_unlock_all_color: str = "danger",
        btn_unlock_office_color: str = "danger",
        btn_unlocked_shop_color: str = "danger",
        # btn_unlock_gym_color: str = "danger",
        btn_lights_north_on_color: str = "danger",
        btn_lights_south_on_color: str = "danger",
        btn_lights_outdoor_on_color: str = "danger",
        btn_hrv_fast_on_color: str = "danger",
        btn_fan_office_main_on_color: str = "danger",
        btn_ohd_west_open_color: str = "danger",
        btn_ohd_west_close_color: str = "danger",
        btn_ohd_west_stop_color: str = "danger",
        btn_ohd_east_open_color: str = "danger",
        btn_ohd_east_close_color: str = "danger",
        btn_ohd_east_stop_color: str = "danger",
    ):
        """Default return variables"""
        return (
            control_shop_row_style,
            btn_unlock_office_disabled,
            btn_unlocked_shop_disabled,
            # btn_unlock_gym_disabled,
            btn_lights_north_on_disabled,
            btn_lights_south_on_disabled,
            btn_lights_outdoor_on_disabled,
            btn_hrv_fast_on_disabled,
            btn_fan_office_main_on_disabled,
            btn_ohd_west_open_disabled,
            btn_ohd_west_close_disabled,
            btn_ohd_west_stop_disabled,
            btn_ohd_east_open_disabled,
            btn_ohd_east_close_disabled,
            btn_ohd_east_stop_disabled,
            # Button label/children (lock, unlock, on, off)
            # btn_unlock_all_children,
            btn_unlock_office_children,
            btn_unlocked_shop_children,
            # btn_unlock_gym_children,
            btn_lights_north_on_children,
            btn_lights_south_on_children,
            btn_lights_outdoor_on_children,
            btn_hrv_fast_on_children,
            btn_fan_office_main_on_children,
            btn_ohd_west_open_children,
            btn_ohd_west_close_children,
            btn_ohd_west_stop_children,
            btn_ohd_east_open_children,
            btn_ohd_east_close_children,
            btn_ohd_east_stop_children,
            # Button colour (green/'success' or red/'danger')
            # btn_unlock_all_color,
            btn_unlock_office_color,
            btn_unlocked_shop_color,
            # btn_unlock_gym_color,
            btn_lights_north_on_color,
            btn_lights_south_on_color,
            btn_lights_outdoor_on_color,
            btn_hrv_fast_on_color,
            btn_fan_office_main_on_color,
            btn_ohd_west_open_color,
            btn_ohd_west_close_color,
            btn_ohd_west_stop_color,
            btn_ohd_east_open_color,
            btn_ohd_east_close_color,
            btn_ohd_east_stop_color,
        )

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()
    has_rcom: bool = bool(structure_obj.aws_thing)

    if not has_rcom:
        return return_vars()

    id_triggered: str = get_id_triggered()
    if id_triggered is None:
        raise PreventUpdate()
    elif "control_tab_refresh_interval" in id_triggered and n_intervals > 100:
        raise PreventUpdate()

    # If it's the shop (HQ Moosomin) gateway, the speed/pressure stuff is invisible
    style_for_control_shop_row = (
        {} if structure_obj.gateway == GATEWAY_SHOP else {"display": "none"}
    )

    # Get the AWS IoT device shadow (latest indicators)
    # The above update_shadow() only gets the metrics it updates
    shadow_all = get_iot_device_shadow(structure_obj.aws_thing)
    reported = shadow_all.get("state", {}).get("reported", {})

    # btn_unlock_all_state = reported.get("unlocked_all", None)
    # all_doors_locked_state = reported.get('all_doors_locked', None)
    # btn_unlock_office_state = reported.get('glass_unlocked', None)
    btn_unlock_office_state = reported.get("office_unlocked", None)
    btn_unlocked_shop_state = reported.get("unlocked_shop", None)
    # btn_unlock_gym_state = reported.get("gym_unlocked2", None)
    btn_lights_north_on_state = reported.get("north_on", None)
    btn_lights_south_on_state = reported.get("south_on", None)
    btn_lights_outdoor_on_state = reported.get("outdoor_on", None)
    btn_hrv_fast_on_state = reported.get("office_hrv_fast_on", None)
    btn_fan_office_main_on_state = reported.get("fan_office_main_on", None)
    # btn_ohd_west_open_state = reported.get('ohd_west_open', None)
    # btn_ohd_west_close_state = reported.get('ohd_west_close', None)
    # btn_ohd_west_stop_state = reported.get('ohd_west_stop', None)
    # btn_ohd_east_open_state = reported.get('ohd_east_open', None)
    # btn_ohd_east_close_state = reported.get('ohd_east_close', None)
    # btn_ohd_east_stop_state = reported.get('ohd_east_stop', None)

    # Labels and colors. If the shop is unlocked == 1, then we can lock it

    # if (
    #     btn_unlock_all_state == 1
    #     or btn_unlock_office_state == 1
    #     or btn_unlocked_shop_state == 1
    #     or btn_unlocked_shop_state == 1
    # ):
    #     btn_unlock_all_label, btn_unlock_all_colour = ("Unlocked", "success")
    # else:
    #     btn_unlock_all_label, btn_unlock_all_colour = ("Locked", "danger")

    btn_unlock_office_label, btn_unlock_office_colour = (
        ("Locked", "danger")
        if btn_unlock_office_state == 0
        else ("Unlocked", "success")
    )
    btn_unlocked_shop_label, btn_unlocked_shop_colour = (
        ("Locked", "danger")
        if btn_unlocked_shop_state == 0
        else ("Unlocked", "success")
    )
    # btn_unlock_gym_label, btn_unlock_gym_colour = (
    #     ("Locked", "danger")
    #     if btn_unlock_gym_state == 0
    #     else ("Unlocked", "success")
    # )
    btn_lights_north_on_label, btn_lights_north_on_colour = (
        ("Off", "dark") if btn_lights_north_on_state == 0 else ("On", "warning")
    )
    btn_lights_south_on_label, btn_lights_south_on_colour = (
        ("Off", "dark") if btn_lights_south_on_state == 0 else ("On", "warning")
    )
    btn_lights_outdoor_on_label, btn_lights_outdoor_on_colour = (
        ("Off", "dark") if btn_lights_outdoor_on_state == 0 else ("On", "warning")
    )
    btn_hrv_fast_on_label, btn_hrv_fast_on_colour = (
        ("Off", "dark") if btn_hrv_fast_on_state == 0 else ("On", "success")
    )
    btn_fan_office_main_on_label, btn_fan_office_main_on_colour = (
        ("Off", "dark") if btn_fan_office_main_on_state == 0 else ("On", "success")
    )
    # Overhead doors don't change labels and colours
    btn_ohd_west_open_label, btn_ohd_west_open_colour = ("Open", "success")
    btn_ohd_west_close_label, btn_ohd_west_close_colour = ("Close", "primary")
    btn_ohd_west_stop_label, btn_ohd_west_stop_colour = ("Stop", "danger")
    btn_ohd_east_open_label, btn_ohd_east_open_colour = ("Open", "success")
    btn_ohd_east_close_label, btn_ohd_east_close_colour = ("Close", "primary")
    btn_ohd_east_stop_label, btn_ohd_east_stop_colour = ("Stop", "danger")

    abilities_dict: PermissionsAbilities = get_card_body_permissions_abilities(
        shadow_all, structure_obj
    )
    has_abilities: bool = abilities_dict.get("has_abilities", False)
    doesnt_have_abilities: bool = not (has_abilities)

    return return_vars(
        control_shop_row_style=style_for_control_shop_row,
        btn_unlock_office_disabled=doesnt_have_abilities,
        btn_unlocked_shop_disabled=doesnt_have_abilities,
        # btn_unlock_gym_disabled=doesnt_have_abilities,
        btn_lights_north_on_disabled=doesnt_have_abilities,
        btn_lights_south_on_disabled=doesnt_have_abilities,
        btn_lights_outdoor_on_disabled=doesnt_have_abilities,
        btn_hrv_fast_on_disabled=doesnt_have_abilities,
        btn_fan_office_main_on_disabled=doesnt_have_abilities,
        btn_ohd_west_open_disabled=doesnt_have_abilities,
        btn_ohd_west_close_disabled=doesnt_have_abilities,
        btn_ohd_west_stop_disabled=doesnt_have_abilities,
        btn_ohd_east_open_disabled=doesnt_have_abilities,
        btn_ohd_east_close_disabled=doesnt_have_abilities,
        btn_ohd_east_stop_disabled=doesnt_have_abilities,
        # Button label/children (lock, unlock, on, off)
        # btn_unlock_all_children=btn_unlock_all_label,
        btn_unlock_office_children=btn_unlock_office_label,
        btn_unlocked_shop_children=btn_unlocked_shop_label,
        # btn_unlock_gym_children=btn_unlock_gym_label,
        btn_lights_north_on_children=btn_lights_north_on_label,
        btn_lights_south_on_children=btn_lights_south_on_label,
        btn_lights_outdoor_on_children=btn_lights_outdoor_on_label,
        btn_hrv_fast_on_children=btn_hrv_fast_on_label,
        btn_fan_office_main_on_children=btn_fan_office_main_on_label,
        btn_ohd_west_open_children=btn_ohd_west_open_label,
        btn_ohd_west_close_children=btn_ohd_west_close_label,
        btn_ohd_west_stop_children=btn_ohd_west_stop_label,
        btn_ohd_east_open_children=btn_ohd_east_open_label,
        btn_ohd_east_close_children=btn_ohd_east_close_label,
        btn_ohd_east_stop_children=btn_ohd_east_stop_label,
        # Button colour (green/'success' or red/'danger')
        # btn_unlock_all_color=btn_unlock_all_colour,
        btn_unlock_office_color=btn_unlock_office_colour,
        btn_unlocked_shop_color=btn_unlocked_shop_colour,
        # btn_unlock_gym_color=btn_unlock_gym_colour,
        btn_lights_north_on_color=btn_lights_north_on_colour,
        btn_lights_south_on_color=btn_lights_south_on_colour,
        btn_lights_outdoor_on_color=btn_lights_outdoor_on_colour,
        btn_hrv_fast_on_color=btn_hrv_fast_on_colour,
        btn_fan_office_main_on_color=btn_fan_office_main_on_colour,
        btn_ohd_west_open_color=btn_ohd_west_open_colour,
        btn_ohd_west_close_color=btn_ohd_west_close_colour,
        btn_ohd_west_stop_color=btn_ohd_west_stop_colour,
        btn_ohd_east_open_color=btn_ohd_east_open_colour,
        btn_ohd_east_close_color=btn_ohd_east_close_colour,
        btn_ohd_east_stop_color=btn_ohd_east_stop_colour,
    )


def make_maintenance_record(
    control_prev_maint_date_value: str,
    structure_obj: StructureVw,
    control_prev_maint_type_select_value: int,
    tz_wanted: pytz.timezone,
) -> None:
    """Record the maintenance action in the database, adjusting the operating hours"""

    # Make the local time timezone-aware and assume it happened at 12-noon
    local_dt = datetime.strptime(control_prev_maint_date_value, "%Y-%m-%d").replace(
        hour=12
    )
    # Convert from local to UTC time for the database
    utc_datetime: datetime = tz_wanted.localize(local_dt).astimezone(pytz.utc)
    # Calculate the time difference and convert to hours
    hours_between: float = (utcnow_aware() - utc_datetime).total_seconds() / 3600
    if isinstance(structure_obj.op_hours, (int, float)):
        # Add the hours to the current operating hours
        prev_op_hours = float(structure_obj.op_hours) - hours_between
    else:
        prev_op_hours = None
    utc_datetime_str = utc_datetime.strftime("%Y-%m-%d %H:%M:%S")

    # Record the action in the database
    maint_record = Maintenance(
        user_id=getattr(current_user, "id", None),
        structure_id=structure_obj.id,
        # DGAS doesn't have a power_unit_id
        power_unit_id=getattr(structure_obj, "power_unit_id", None),
        maintenance_type_id=control_prev_maint_type_select_value,
        timestamp_utc=utc_datetime_str,
        op_hours=prev_op_hours,
        description="Reset preventative maintenance interval",
    )
    db.session.add(maint_record)
    db.session.commit()

    return None


@callback(
    Output("control_suction_schedule_collapse", "is_open"),
    Input("control_suction_schedule_switch", "value"),
    State("control_suction_schedule_collapse", "is_open"),
    prevent_initial_call=True,
)
def open_schedule_collapse(
    control_suction_schedule_switch_values,
    # State values
    control_suction_schedule_collapse_is_open,
):
    """Scheduling modal actions"""
    log_function_caller()

    # Find which id in the inputs has been triggered
    id_triggered: str = get_id_triggered()
    if id_triggered is None:
        raise PreventUpdate()

    if control_suction_schedule_switch_values == []:
        return False

    return not control_suction_schedule_collapse_is_open


@callback(
    Output("setter_new_shadow", "data"),
    Output("modal_main_header", "children"),
    Output("modal_main_body", "children"),
    Output("modal_main", "is_open"),
    # 4 message-modal outputs
    Output("modal_message_header", "children", allow_duplicate=True),
    Output("modal_message_body", "children", allow_duplicate=True),
    Output("modal_message", "is_open", allow_duplicate=True),
    Output("modal_message", "backdrop", allow_duplicate=True),
    # Modal confirm/cancel buttons
    Input("btn_modal_main_cancel", "n_clicks"),
    Input("btn_modal_main_confirm", "n_clicks"),
    # 18 main buttons on 'control' tab
    Input("control_msg_status_cancel_btn", "n_clicks"),
    Input("btn_start_uno", "n_clicks"),
    Input("btn_start_egas", "n_clicks"),
    Input("btn_lock_uno", "n_clicks"),
    Input("btn_lock_egas", "n_clicks"),
    Input("btn_control_prev_maint_reset", "n_clicks"),
    Input("btn_control_maintenance_interval", "n_clicks"),
    Input("btn_reset_velocity_control", "n_clicks"),
    Input("btn_vpn_start", "n_clicks"),
    Input("btn_vpn_stop", "n_clicks"),
    Input("btn_apn_submit", "n_clicks"),
    Input("btn_request_new_card", "n_clicks"),
    Input("btn_cancel_new_card", "n_clicks"),
    Input("email_logs_submit_btn", "n_clicks"),
    Input("btn_control_suction_target", "n_clicks"),
    Input("btn_control_max_discharge_target", "n_clicks"),
    Input("btn_control_max_shutdown_pressure_target", "n_clicks"),
    Input("btn_control_max_speed", "n_clicks"),
    Input("btn_control_min_speed", "n_clicks"),
    Input("btn_control_horsepower", "n_clicks"),
    Input("btn_control_pid_sensitivity", "n_clicks"),
    Input("btn_control_select_read_all", "n_clicks"),
    Input("btn_control_select_write", "n_clicks"),
    # Input("btn_control_select_read", "n_clicks"),
    # 12 UNO controls
    Input("btn_control_spm_max_uno", "n_clicks"),
    Input("btn_control_spm_min_uno", "n_clicks"),
    Input("btn_control_spm_set_uno", "n_clicks"),
    # Input('btn_control_horsepower_uno', 'n_clicks'),
    Input("btn_control_apft", "n_clicks"),
    Input("btn_control_pid_sensitivity_uno", "n_clicks"),
    Input("btn_control_ts", "n_clicks"),
    Input("btn_control_bs", "n_clicks"),
    Input("btn_control_uno_auto_stuff", "n_clicks"),
    Input("btn_control_uno_auto_type", "n_clicks"),
    Input("btn_control_auto_tap_tgt", "n_clicks"),
    Input("btn_control_peff", "n_clicks"),
    Input("btn_control_psize", "n_clicks"),
    # Input of HQ (Moosomin shop) control buttons (e.g. 'Unlock/Lock/On/Off')
    # Input("btn_unlock_all", "n_clicks"),
    Input("btn_unlock_office", "n_clicks"),
    Input("btn_unlocked_shop", "n_clicks"),
    # Input("btn_unlock_gym", "n_clicks"),
    Input("btn_lights_north_on", "n_clicks"),
    Input("btn_lights_south_on", "n_clicks"),
    Input("btn_lights_outdoor_on", "n_clicks"),
    Input("btn_hrv_fast_on", "n_clicks"),
    Input("btn_fan_office_main_on", "n_clicks"),
    Input("btn_ohd_west_open", "n_clicks"),
    Input("btn_ohd_west_close", "n_clicks"),
    Input("btn_ohd_west_stop", "n_clicks"),
    Input("btn_ohd_east_open", "n_clicks"),
    Input("btn_ohd_east_close", "n_clicks"),
    Input("btn_ohd_east_stop", "n_clicks"),
    # 15 state values
    State("setter_new_shadow", "data"),
    State("modal_main", "is_open"),
    State("store_structure_id", "data"),
    State("control_suction_target_input", "value"),
    State("control_max_discharge_input", "value"),
    State("control_max_shutdown_pressure_input", "value"),
    State("control_max_speed_input", "value"),
    State("control_min_speed_input", "value"),
    State("control_horsepower_limit_input", "value"),
    State("control_pid_sensitivity_input", "value"),
    State("control_select", "value"),  # dropdown
    State("control_select_input", "value"),  # target value
    State("control_resp_enum", "value"),
    # State("control_select", "value"),
    State("store_unit_type_id", "data"),
    # State("control_select_write", "value"),
    # Do the startup buttons currently say "Running/Stopped" or "Locked/Unlocked"?
    State("btn_start_uno", "children"),
    State("btn_start_egas", "children"),
    State("btn_lock_uno", "children"),
    State("btn_lock_egas", "children"),
    # 12 UNO inputs
    State("control_spm_max_uno", "value"),
    State("control_spm_min_uno", "value"),
    State("control_spm_set_uno", "value"),
    # State('control_horsepower_limit_uno', 'value'),
    State("control_apft", "value"),
    State("control_pid_sensitivity_uno", "value"),
    State("control_ts", "value"),
    State("control_bs", "value"),
    State("control_uno_auto_stuff", "value"),
    State("control_uno_auto_type", "value"),
    State("control_auto_tap_tgt", "value"),
    State("control_peff", "value"),
    State("control_psize", "value"),
    # State of HQ (Moosomin shop) labels (e.g. 'Unlock/Lock/On/Off')
    # State("btn_unlock_all", "children"),
    State("btn_unlock_office", "children"),
    State("btn_unlocked_shop", "children"),
    # State("btn_unlock_gym", "children"),
    State("btn_lights_north_on", "children"),
    State("btn_lights_south_on", "children"),
    State("btn_lights_outdoor_on", "children"),
    State("btn_hrv_fast_on", "children"),
    State("btn_fan_office_main_on", "children"),
    State("btn_ohd_west_open", "children"),
    State("btn_ohd_west_close", "children"),
    State("btn_ohd_west_stop", "children"),
    State("btn_ohd_east_open", "children"),
    State("btn_ohd_east_close", "children"),
    State("btn_ohd_east_stop", "children"),
    # 1 schedule switch state
    State("control_suction_schedule_switch", "value"),
    # 3 schedule form input values
    State("suction_sched_value_start", "value"),
    State("suction_sched_start_after_x_mins", "value"),
    State("suction_sched_over_x_mins", "value"),
    # 3 email logs input values
    State("email_logs_input_email", "value"),
    State("email_logs_input_num", "value"),
    State("email_logs_input_starts", "value"),
    State("apn_input", "value"),
    State("control_prev_maint_type_select", "value"),
    State("control_maintenance_interval_input", "value"),
    State("control_prev_maint_date", "value"),
    prevent_initial_call=True,
)
def control_buttons(
    # Inputs - modal confirm/cancel
    btn_modal_main_cancel_n_clicks,
    btn_modal_main_confirm_n_clicks,
    # 18 main buttons on 'control' tab
    cancel_commands_n_clicks,
    btn_start_uno_n_clicks,
    btn_start_egas_n_clicks,
    btn_lock_uno_n_clicks,
    btn_lock_egas_n_clicks,
    btn_control_prev_maint_reset_n_clicks,
    btn_control_maintenance_interval_n_clicks,
    btn_reset_velocity_control_n_clicks,
    btn_vpn_start_n_clicks,
    btn_vpn_stop_n_clicks,
    btn_apn_submit_n_clicks,
    btn_request_new_card_n_clicks,
    btn_cancel_new_card_n_clicks,
    email_logs_submit_btn_n_clicks,
    suction_clicks,
    max_discharge_clicks,
    max_shutdown_pressure_clicks,
    max_speed_clicks,
    min_speed_clicks,
    horsepower_limit_clicks,
    pid_sensitivity_clicks,
    btn_control_select_read_all_clicks,
    btn_control_select_write_clicks,
    # btn_control_select_read_clicks,
    # 12 UNO controls
    btn_control_spm_max_uno_clicks,
    btn_control_spm_min_uno_clicks,
    btn_control_spm_set_uno_clicks,
    # btn_control_horsepower_uno_clicks,
    btn_control_apft_clicks,
    btn_control_pid_sensitivity_uno_clicks,
    btn_control_ts_clicks,
    btn_control_bs_clicks,
    btn_control_uno_auto_stuff_clicks,
    btn_control_uno_auto_type_clicks,
    btn_control_auto_tap_tgt_clicks,
    btn_control_peff_clicks,
    btn_control_psize_clicks,
    # Input of HQ (Moosomin shop) control buttons (e.g. 'Unlock/Lock/On/Off')
    # btn_unlock_all_clicks,
    btn_unlock_office_clicks,
    btn_unlocked_shop_clicks,
    # btn_unlock_gym_clicks,
    btn_lights_north_on_clicks,
    btn_lights_south_on_clicks,
    btn_lights_outdoor_on_clicks,
    btn_hrv_fast_on_clicks,
    btn_fan_office_main_on_clicks,
    btn_ohd_west_open_clicks,
    btn_ohd_west_close_clicks,
    btn_ohd_west_stop_clicks,
    btn_ohd_east_open_clicks,
    btn_ohd_east_close_clicks,
    btn_ohd_east_stop_clicks,
    # 15 state values
    setter_new_shadow,
    is_open,
    store_structure_id_data,
    suction_target,
    max_discharge,
    max_shutdown_pressure,
    max_speed,
    min_speed,
    horsepower_limit,
    pid_sensitivity,
    control_select_value,  # dropdown metric
    control_select_input_value,  # target value
    control_resp_enum,
    store_unit_type_id,
    # control_select_write_value,
    # Do the startup buttons currently say "Running/Stopped" or "Locked/Unlocked"?
    btn_start_uno_label,
    btn_start_egas_label,
    btn_lock_uno_label,
    btn_lock_egas_label,
    # 12 UNO inputs
    control_spm_max_uno,
    control_spm_min_uno,
    control_spm_set_uno,
    # control_horsepower_limit_uno,
    control_apft,
    control_pid_sensitivity_uno,
    control_ts,
    control_bs,
    control_uno_auto_stuff_value,
    control_uno_auto_type_value,
    control_auto_tap_tgt_value,
    control_peff_value,
    control_psize_value,
    # State of HQ (Moosomin shop) labels (e.g. 'Unlock/Lock/On/Off')
    # btn_unlock_all_label,
    btn_unlock_office_label,
    btn_unlocked_shop_label,
    # btn_unlock_gym_label,
    btn_lights_north_on_label,
    btn_lights_south_on_label,
    btn_lights_outdoor_on_label,
    btn_hrv_fast_on_label,
    btn_fan_office_main_on_label,
    btn_ohd_west_open_label,
    btn_ohd_west_close_label,
    btn_ohd_west_stop_label,
    btn_ohd_east_open_label,
    btn_ohd_east_close_label,
    btn_ohd_east_stop_label,
    # 1 schedule switch state
    control_suction_schedule_switch_value,
    # 3 schedule form input values
    suction_sched_value_start_value,
    suction_sched_start_after_x_mins_value,
    suction_sched_over_x_mins_value,
    # 3 email logs input values
    email_logs_input_email,
    email_logs_input_num,
    email_logs_input_starts,
    apn_input_value,
    control_prev_maint_type_select_value,
    control_maintenance_interval_input_value,
    control_prev_maint_date_value,
):
    """
    Send a signal to the unit by setting the 'desired'
    value in the device shadow.
    """
    log_function_caller()

    def return_variables(
        setter_new_shadow_data=None,
        modal_main_header_children=None,
        modal_main_body_children=None,
        modal_main_is_open=False,
        # 4 message-modal outputs
        modal_message_header_children=no_update,
        modal_message_body_children=no_update,
        modal_message_is_open=False,
        # The following can also be boolean
        modal_message_backdrop="static",
    ):
        """Default return variables"""
        setter_new_shadow_data = setter_new_shadow_data or {}
        return (
            setter_new_shadow_data,
            modal_main_header_children,
            modal_main_body_children,
            modal_main_is_open,
            # 4 message-modal outputs
            modal_message_header_children,
            modal_message_body_children,
            modal_message_is_open,
            modal_message_backdrop,
        )

    def msg_return(header: str, body: str):
        """Return a simple message in the modal"""
        return return_variables(
            setter_new_shadow_data=no_update,
            modal_main_header_children=no_update,
            modal_main_body_children=None,
            modal_main_is_open=False,
            modal_message_header_children=header,
            modal_message_body_children=body,
            modal_message_is_open=True,
        )

    if user_is_demo_customer(user_id=getattr(current_user, "id", None)):
        return msg_return(
            "Demo Mode",
            "This feature is disabled in demo mode. Please contact the administrator.",
        )

    id_triggered: str = get_id_triggered()
    if store_structure_id_data is None or id_triggered is None:
        if id_triggered in ("btn_modal_main_cancel", "btn_modal_main_confirm"):
            return return_variables()
        else:
            raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()
    power_unit_str: str = structure_obj.power_unit_str
    aws_thing: str = structure_obj.aws_thing
    customer: str = structure_obj.customer
    location: str = structure_obj.location
    model: str = structure_obj.model
    is_vessel_level_mode: bool = structure_obj.suction_range == 4

    # Get the AWS IoT device shadow (latest indicators)
    # The above update_shadow() only gets the metrics it updates
    shadow_all: dict = get_iot_device_shadow(aws_thing)
    reported: dict = shadow_all.get("state", {}).get("reported", {})
    # Software version PLC (Dan's software)
    try:
        swv_plc = float(reported.get("SWV", 0))
    except Exception:
        swv_plc = 9999

    try:
        tz_wanted = pytz.timezone(structure_obj.time_zone)
    except pytz.exceptions.UnknownTimeZoneError:
        current_app.logger.exception(f"Unknown time_zone: '{structure_obj.time_zone}'")
        return return_variables()

    # Initialize variables
    desired_val: Number | None = None
    type_name: str | None = None
    desired: dict = {}
    modal_main_header: str | None = None
    modal_main_body: str | None = None

    # Execute the new device shadow if the confirm button in the modal has been clicked
    if "btn_modal_main_cancel" in id_triggered:
        # Close the confirmation modal and set the setter_new_shadow to {}
        return return_variables()

    elif "btn_modal_main_confirm" in id_triggered:
        # If the 'confirmed' button in the modal is clicked, execute the action

        if setter_new_shadow is None:
            raise PreventUpdate()

        aws_thing = setter_new_shadow.pop("aws_thing", None)
        if aws_thing is None:
            raise PreventUpdate()

        # Pop these off before updating the shadow. They're for record-keeping
        action = setter_new_shadow.pop("action", None)
        metric_to_record = setter_new_shadow.pop("metric_to_record", None)
        value_to_record = setter_new_shadow.pop("value_to_record", None)

        if action == RESET_PREV_MAINT:
            make_maintenance_record(
                control_prev_maint_date_value=control_prev_maint_date_value,
                structure_obj=structure_obj,
                control_prev_maint_type_select_value=control_prev_maint_type_select_value,
                tz_wanted=tz_wanted,
            )

        elif action == SET_MAINTENANCE_INTERVAL:
            model = db.session.get(Structure, structure_obj.id)
            model.op_months_interval = control_maintenance_interval_input_value
            db.session.commit()

        # Send the command to the device shadow, and record the action
        if setter_new_shadow not in ({}, "") and is_open:
            try:
                update_shadow(setter_new_shadow, aws_thing)
            except Exception:
                current_app.logger.exception(
                    f"Error updating shadow for aws_thing '{aws_thing}'"
                )
                return msg_return(
                    "Error",
                    f"Error updating shadow for {customer} {model} at {location}. Please try again.",
                )

            # # The following is for recording the action in the database
            # # TODO: can be removed if metric_to_record and value_to_record are recorded in the first step way below!
            # if metric_to_record is None or value_to_record is None:
            #     desired = setter_new_shadow.get("state", {}).get("desired", {})
            #     if desired is None:
            #         # This command cancels all other commands
            #         metric_to_record = "CANCEL ALL"
            #         value_to_record = 1
            #     elif isinstance(desired, dict):
            #         first_metric_in_desired = next(iter(desired.keys()))
            #         if "COMMAND" in desired.keys():
            #             # Advanced remote control
            #             # metric_to_record = "COMMAND"
            #             metric_to_record = desired["COMMAND"]["CMD_CNUM"]
            #             # Needs to be numeric...
            #             # value_to_record = json.dumps(desired)
            #             value_to_record = desired["COMMAND"]["CMD_VAL"]
            #         elif "EMAIL_LOGS" in desired.keys():
            #             email = desired["EMAIL_LOGS"]["email"]
            #             starts = desired["EMAIL_LOGS"]["starts"]
            #             num = desired["EMAIL_LOGS"]["num"]
            #             metric_to_record = (
            #                 f"email_logs - {email} - {starts} - {num}"
            #             )
            #             value_to_record = None
            #         elif isinstance(
            #             desired.get(first_metric_in_desired, None), dict
            #         ):
            #             metric_to_record = first_metric_in_desired
            #             # This is a scheduled command since it's a dict type
            #             value_to_record = desired[metric_to_record]["value_desired"]
            #         else:
            #             # This is a regular command since it's not a dict type
            #             metric_to_record = first_metric_in_desired
            #             value_to_record = desired[metric_to_record]

            # Record the action in the database
            try:
                power_unit_model = PowerUnit.query.filter_by(
                    power_unit_str=power_unit_str
                ).first()
                power_unit_id = getattr(power_unit_model, "id")
            except Exception:
                current_app.logger.exception(
                    f"Error getting power unit ID for power unit '{power_unit_str}'"
                )
                power_unit_id = None

            record_remote_control(
                power_unit_id=power_unit_id,
                power_unit_str=power_unit_str,
                aws_thing=aws_thing,
                metric=metric_to_record,
                value_wanted=value_to_record,
                action=action,
            )

        # Close the confirmation modal and set the setter_new_shadow to ''
        return return_variables(setter_new_shadow_data=setter_new_shadow)

    # Set the aws_thing so we know which aws_thing to update once the user
    # confirms the choice in the modal. The 'action' is for the remote control logging database,
    # to record what the user attempted to do
    new_shadow = {
        "aws_thing": aws_thing,
        "action": None,
        "metric_to_record": None,
        "value_to_record": None,
    }
    is_valid = False  # initialize the validation to False

    unit_info_msg = f"{customer} {model} at {location}"

    if (
        "control_msg_status_cancel_btn" in id_triggered
        and cancel_commands_n_clicks is not None
    ):
        # Nullify the entire "desired" dictionary to cancel all commands
        new_shadow["state"] = {"desired": None}
        new_shadow["action"] = "cancel all commands"
        new_shadow["metric_to_record"] = "CANCEL ALL"
        new_shadow["value_to_record"] = 1
        modal_main_header = "Confirm Cancel All Commands"
        modal_main_body = f"Please confirm you would like to cancel all commands for the {unit_info_msg}"
        is_valid = True

    elif "btn_start_uno" in id_triggered and btn_start_uno_n_clicks is not None:
        if btn_start_uno_label.lower() == "stopped":
            # If it's currently stopped, let's start it
            # 5 for start, 10 for stop
            new_shadow["state"] = {"desired": {"HYD": 1}}
            new_shadow["action"] = "start unit"
            new_shadow["metric_to_record"] = "HYD"
            new_shadow["value_to_record"] = 1
            modal_main_header = "Confirm Start Unit"
            modal_main_body = (
                f"Please confirm you would like to start the {unit_info_msg}"
            )
            is_valid = True
        elif btn_start_uno_label.lower() == "running":
            # elif 'btn_stop_unit' in id_triggered:
            # If it's currently running, let's stop it
            # 5 for start, 10 for stop
            new_shadow["state"] = {"desired": {"HYD": 0}}
            new_shadow["action"] = "stop unit"
            new_shadow["metric_to_record"] = "HYD"
            new_shadow["value_to_record"] = 0
            modal_main_header = "Confirm Stop Unit"
            modal_main_body = (
                f"Please confirm you would like to stop the {unit_info_msg}"
            )
            is_valid = True

    elif "btn_start_egas" in id_triggered and btn_start_egas_n_clicks is not None:
        if btn_start_egas_label.lower() == "stopped":
            # If it's currently stopped, let's start it
            # 5 for start, 10 for stop
            new_shadow["state"] = {"desired": {"HYD_EGAS": 1}}
            new_shadow["action"] = "start unit"
            new_shadow["metric_to_record"] = "HYD_EGAS"
            new_shadow["value_to_record"] = 1
            modal_main_header = "Confirm Start Unit"
            modal_main_body = (
                f"Please confirm you would like to start the {unit_info_msg}"
            )
            is_valid = True
        elif btn_start_egas_label.lower() == "running":
            # elif 'btn_stop_unit' in id_triggered:
            # If it's currently running, let's stop it
            # 5 for start, 10 for stop
            new_shadow["state"] = {"desired": {"HYD_EGAS": 0}}
            new_shadow["action"] = "stop unit"
            new_shadow["metric_to_record"] = "HYD_EGAS"
            new_shadow["value_to_record"] = 0
            modal_main_header = "Confirm Stop Unit"
            modal_main_body = (
                f"Please confirm you would like to stop the {unit_info_msg}"
            )
            is_valid = True

    elif "btn_lock_uno" in id_triggered and btn_lock_uno_n_clicks is not None:
        if btn_lock_uno_label.lower() == "unlocked":
            # If it's currently Unlocked, let's lock it
            # UNLOCK = 15, LOCK = 20
            new_shadow["state"] = {"desired": {"LOCKOUT": 1}}
            new_shadow["action"] = "lock/stop unit"
            new_shadow["metric_to_record"] = "LOCKOUT"
            new_shadow["value_to_record"] = 1
            modal_main_header = "Confirm Lock Unit"
            modal_main_body = (
                f"Please confirm you would like to lock the {unit_info_msg}"
            )
            is_valid = True
        elif btn_lock_uno_label.lower() == "locked":
            # elif 'btn_unlock_unit' in id_triggered:
            # If it's currently Locked, let's unlock it
            # UNLOCK = 15, LOCK = 20
            new_shadow["state"] = {"desired": {"LOCKOUT": 0}}
            new_shadow["action"] = "unlock unit"
            new_shadow["metric_to_record"] = "LOCKOUT"
            new_shadow["value_to_record"] = 0
            modal_main_header = "Confirm Unlock Unit"
            modal_main_body = (
                f"Please confirm you would like to unlock the {unit_info_msg}"
            )
            is_valid = True

    elif "btn_lock_egas" in id_triggered and btn_lock_egas_n_clicks is not None:
        if btn_lock_egas_label.lower() == "unlocked":
            # If it's currently Unlocked, let's lock it
            # UNLOCK = 15, LOCK = 20
            new_shadow["state"] = {"desired": {"LOCKOUT_EGAS": 1}}
            new_shadow["action"] = "lock/stop unit"
            new_shadow["metric_to_record"] = "LOCKOUT_EGAS"
            new_shadow["value_to_record"] = 1
            modal_main_header = "Confirm Lock Unit"
            modal_main_body = (
                f"Please confirm you would like to lock the {unit_info_msg}"
            )
            is_valid = True
        elif btn_lock_egas_label.lower() == "locked":
            # elif 'btn_unlock_unit' in id_triggered:
            # If it's currently Locked, let's unlock it
            # UNLOCK = 15, LOCK = 20
            new_shadow["state"] = {"desired": {"LOCKOUT_EGAS": 0}}
            new_shadow["action"] = "unlock unit"
            new_shadow["metric_to_record"] = "LOCKOUT_EGAS"
            new_shadow["value_to_record"] = 0
            modal_main_header = "Confirm Unlock Unit"
            modal_main_body = (
                f"Please confirm you would like to unlock the {unit_info_msg}"
            )
            is_valid = True

    elif (
        "btn_control_prev_maint_reset" in id_triggered
        and btn_control_prev_maint_reset_n_clicks is not None
    ):
        # Reset the preventative maintenance
        new_shadow["state"] = {"desired": {}}
        new_shadow["action"] = RESET_PREV_MAINT
        new_shadow["metric_to_record"] = "Preventative Maintenance Reset"
        new_shadow["value_to_record"] = None
        modal_main_header = "Confirm Reset Preventative Maintenance Interval"
        modal_main_body = f"Please confirm you would like to reset the preventative maintenance interval for the {unit_info_msg} to {control_prev_maint_date_value}"
        is_valid = True

    elif (
        "btn_control_maintenance_interval" in id_triggered
        and btn_control_maintenance_interval_n_clicks is not None
    ):
        # Reset the preventative maintenance
        new_shadow["state"] = {"desired": {}}
        new_shadow["action"] = SET_MAINTENANCE_INTERVAL
        new_shadow["metric_to_record"] = "Preventative Maintenance Interval Change"
        new_shadow["value_to_record"] = control_maintenance_interval_input_value
        modal_main_header = "Confirm Change Preventative Maintenance Interval"
        modal_main_body = f"Please confirm you would like to change the preventative maintenance interval to {control_maintenance_interval_input_value} months for the {unit_info_msg}"
        is_valid = True

    elif (
        "btn_reset_velocity_control" in id_triggered
        and btn_reset_velocity_control_n_clicks is not None
    ):
        # RESET = 1
        new_shadow["state"] = {"desired": {"AE110": 1}}
        new_shadow["action"] = "reset velocity control"
        new_shadow["metric_to_record"] = "AE110"
        new_shadow["value_to_record"] = 1
        modal_main_header = "Confirm Reset Velocity Control"
        modal_main_body = f"Please confirm you would like to reset the velocity control for the {unit_info_msg}"
        is_valid = True

    # VPN access buttons
    elif "btn_vpn_start" in id_triggered and btn_vpn_start_n_clicks is not None:
        # If it's currently off or not reported, let's start it
        new_shadow["state"] = {"desired": {"VPN": 1}}
        new_shadow["action"] = "start VPN"
        new_shadow["metric_to_record"] = "VPN"
        new_shadow["value_to_record"] = 1
        modal_main_header = "Confirm Start VPN"
        modal_main_body = (
            f"Please confirm you would like to start the VPN for the {unit_info_msg}"
        )
        is_valid = True

    elif "btn_vpn_stop" in id_triggered and btn_vpn_stop_n_clicks is not None:
        new_shadow["state"] = {"desired": {"VPN": 0}}
        new_shadow["action"] = "stop VPN"
        new_shadow["metric_to_record"] = "VPN"
        new_shadow["value_to_record"] = 0
        modal_main_header = "Confirm Stop VPN"
        modal_main_body = (
            f"Please confirm you would like to stop the VPN for the {unit_info_msg}"
        )
        is_valid = True

    elif "btn_apn_submit" in id_triggered and btn_apn_submit_n_clicks is not None:
        # Set the cellular APN
        new_shadow["state"] = {"desired": {"APN_WEB": apn_input_value}}
        new_shadow["action"] = "set APN"
        new_shadow["metric_to_record"] = f"APN: {apn_input_value}"
        new_shadow["value_to_record"] = apn_input_value
        modal_main_header = "Confirm Set APN"
        modal_main_body = f"Please confirm you would like to set the APN to {apn_input_value} for the {unit_info_msg}"
        is_valid = isinstance(apn_input_value, str) and apn_input_value

    elif "btn_request_new_card" in id_triggered and btn_request_new_card_n_clicks:
        # Request a new compression or surface card
        new_shadow["state"] = {"desired": {"WCS": 1}}
        new_shadow["action"] = "request new card"
        new_shadow["metric_to_record"] = "WCS"
        new_shadow["value_to_record"] = 1
        modal_main_header = "Confirm Request New Card"
        modal_main_body = f"Please confirm you would like to request a new card for the {unit_info_msg}"
        is_valid = True

    elif "btn_cancel_new_card" in id_triggered and btn_cancel_new_card_n_clicks:
        # Cancel the request for a new compression or surface card
        new_shadow["state"] = {"desired": {"WCS": 0}}
        new_shadow["action"] = "cancel new card"
        new_shadow["metric_to_record"] = "WCS"
        new_shadow["value_to_record"] = 0
        modal_main_header = "Confirm Cancel New Card"
        modal_main_body = f"Please confirm you would like to cancel the request for a new card for the {unit_info_msg}"
        is_valid = True

    elif (
        "email_logs_submit_btn" in id_triggered
        and email_logs_submit_btn_n_clicks is not None
    ):
        # Send these as lists
        email = str(email_logs_input_email).split(",")
        num = str(email_logs_input_num).split(",")
        starts = str(email_logs_input_starts).split(",")
        new_shadow["state"] = {
            "desired": {
                "EMAIL_LOGS": {
                    "email": email,
                    "num": num,
                    "starts": starts,
                }
            }
        }
        new_shadow["action"] = "email logs"
        new_shadow["metric_to_record"] = f"email_logs - {email} - {starts} - {num}"
        new_shadow["value_to_record"] = None
        modal_main_header = "Confirm Email Logs"
        modal_main_body = f"Please confirm you would like to request the gateway logs numbered {email_logs_input_num}, which start with '{email_logs_input_starts}', to email {email_logs_input_email}, for {unit_info_msg}"
        is_valid = True

    elif "btn_control_suction_target" in id_triggered and suction_clicks is not None:
        # TODO: move this to a better place. It's needed for the SWV in the get_is_valid_input function
        shadow_all = get_iot_device_shadow(aws_thing)
        new_shadow["metric_to_record"] = "AGFT"
        new_shadow["value_to_record"] = suction_target
        if control_suction_schedule_switch_value == []:
            # if no schedule is set
            new_shadow["state"] = {"desired": {"AGFT": suction_target}}
            if is_vessel_level_mode:
                new_shadow["action"] = f"set vessel level target to {suction_target} %"
                modal_main_header = "Confirm Vessel Level Target"
                modal_main_body = f"Please confirm you would like to set the vessel level target to {suction_target}% for the {unit_info_msg}"
            else:
                new_shadow["action"] = f"set suction target to {suction_target} PSI"
                modal_main_header = "Confirm Suction Pressure Target"
                modal_main_body = f"Please confirm you would like to set the suction pressure target to {suction_target} PSI for the {unit_info_msg}"
            is_valid = get_is_valid_input(suction_target, "AGFT", swv_plc=swv_plc)
        else:
            # There is a schedule to follow
            time_start = time.time() + (
                60 * max(0, suction_sched_start_after_x_mins_value)
            )
            time_start_str = datetime.fromtimestamp(time_start, tz=tz_wanted).strftime(
                "%Y-%m-%d at %H:%M"
            )
            time_end = time_start + (60 * max(0, suction_sched_over_x_mins_value))
            time_end_str = datetime.fromtimestamp(time_end, tz=tz_wanted).strftime(
                "%Y-%m-%d at %H:%M"
            )
            scheduled_command_instructions = {
                "method": "linear",
                # Modal form input values
                "time_start": time_start,
                "time_end": time_end,
                "value_desired": suction_target,
                "value_start": suction_sched_value_start_value,
            }
            new_shadow["state"] = {"desired": {"AGFT": scheduled_command_instructions}}

            if is_vessel_level_mode:
                new_shadow["action"] = (
                    f"set vessel level target to {suction_target} % on a schedule"
                )
                modal_main_header = "Confirm Vessel Level Target Schedule"
                modal_main_body = f"Please confirm you would like to set the vessel level target to {suction_target}% for the {unit_info_msg}, "
                modal_main_body += f"starting at {suction_sched_value_start_value}% on {time_start_str} and gradually getting to {suction_target}% by {time_end_str}."
                is_valid_target = (
                    isinstance(suction_target, (int, float))
                    and 0 <= suction_target <= 100
                )
                is_valid_start = (
                    isinstance(suction_sched_value_start_value, (int, float))
                    and 0 <= suction_sched_value_start_value <= 100
                )
            else:
                new_shadow["action"] = (
                    f"set suction target to {suction_target} PSI on a schedule"
                )
                modal_main_header = "Confirm Suction Pressure Target Schedule"
                modal_main_body = f"Please confirm you would like to set the suction pressure target to {suction_target} PSI for the {unit_info_msg}, "
                modal_main_body += f"starting at {suction_sched_value_start_value} PSI on {time_start_str} and gradually getting to {suction_target} PSI by {time_end_str}."
                is_valid_target = get_is_valid_input(
                    suction_target, "AGFT", swv_plc=swv_plc
                )
                is_valid_start = get_is_valid_input(
                    suction_sched_value_start_value, "AGFT", swv_plc=swv_plc
                )

            is_valid = all(
                [
                    is_valid_target,
                    is_valid_start,
                    suction_sched_start_after_x_mins_value >= 0,
                    suction_sched_over_x_mins_value >= 0,
                ]
            )

    elif (
        "btn_control_max_discharge_target" in id_triggered
        and max_discharge_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"MGP": max_discharge}}
        new_shadow["action"] = f"set max discharge target to {max_discharge} PSI"
        new_shadow["metric_to_record"] = "MGP"
        new_shadow["value_to_record"] = max_discharge
        modal_main_header = "Confirm Max Discharge Pressure Target"
        modal_main_body = f"Please confirm you would like to set the maximum discharge pressure target to {max_discharge} PSI for the {unit_info_msg}"
        is_valid = get_is_valid_input(max_discharge, "MGP")

    elif (
        "btn_control_max_shutdown_pressure_target" in id_triggered
        and max_shutdown_pressure_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"MSP": max_shutdown_pressure}}
        new_shadow["action"] = (
            f"set max shutdown pressure target to {max_shutdown_pressure} PSI"
        )
        new_shadow["metric_to_record"] = "MSP"
        new_shadow["value_to_record"] = max_shutdown_pressure
        modal_main_header = "Confirm Max Shutdown Pressure Target"
        modal_main_body = f"Please confirm you would like to set the maximum shutdown pressure target to {max_shutdown_pressure} PSI for the {unit_info_msg}"
        is_valid = get_is_valid_input(max_shutdown_pressure, "MSP")

    elif "btn_control_max_speed" in id_triggered and max_speed_clicks is not None:
        #
        new_shadow["state"] = {"desired": {"AGFM": max_speed}}
        new_shadow["action"] = f"set max speed target to {max_speed}%"
        new_shadow["metric_to_record"] = "AGFM"
        new_shadow["value_to_record"] = max_speed
        modal_main_header = "Confirm Max Speed Target"
        modal_main_body = f"Please confirm you would like to set the maximum speed target to {max_speed}% for the {unit_info_msg}"
        is_valid = get_is_valid_input(max_speed, "AGFM")

    elif "btn_control_min_speed" in id_triggered and min_speed_clicks is not None:
        #
        new_shadow["state"] = {"desired": {"AGFN": min_speed}}
        new_shadow["action"] = f"set min speed target to {min_speed}%"
        new_shadow["metric_to_record"] = "AGFN"
        new_shadow["value_to_record"] = min_speed
        modal_main_header = "Confirm Min Speed Target"
        modal_main_body = f"Please confirm you would like to set the minimum speed target to {min_speed}% for the {unit_info_msg}"
        is_valid = get_is_valid_input(min_speed, "AGFN")

    elif (
        "btn_control_horsepower" in id_triggered and horsepower_limit_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"HP_LIMIT": horsepower_limit}}
        new_shadow["action"] = f"set max horsepower target to {horsepower_limit}"
        new_shadow["metric_to_record"] = "HP_LIMIT"
        new_shadow["value_to_record"] = horsepower_limit
        modal_main_header = "Confirm Max Horsepower Target"
        modal_main_body = f"Please confirm you would like to set the maximum horsepower target to {horsepower_limit} for the {unit_info_msg}"
        is_valid = get_is_valid_input(horsepower_limit, "HP_LIMIT")

    elif (
        "btn_control_pid_sensitivity" in id_triggered
        and pid_sensitivity_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"PID": pid_sensitivity}}
        new_shadow["action"] = f"set adjustment speed target to {pid_sensitivity}"
        new_shadow["metric_to_record"] = "PID"
        new_shadow["value_to_record"] = pid_sensitivity
        modal_main_header = "Confirm PID Sensitivity Target"
        modal_main_body = f"Please confirm you would like to set the adjustment speed (PID sensitivity) target to {pid_sensitivity} for the {unit_info_msg}"
        is_valid = get_is_valid_input(pid_sensitivity, "PID")

    elif (
        "btn_control_select_read_all" in id_triggered
        and btn_control_select_read_all_clicks is not None
    ):
        is_valid = True  # no validation for metrics like "A209"

        cnum, enum, cval = get_adv_remote_control_desired_values(
            cnum=control_select_value,
            cval=control_select_input_value,
            enum=control_resp_enum,
            # Read all setpoints
            read_all=True,
        )
        desired = get_adv_remote_control_desired_dict(
            cnum=cnum,
            cval=cval,
            enum=enum,
            is_write=False,
        )
        new_shadow["state"] = {"desired": desired}
        new_shadow["metric_to_record"] = "CMD_READ_ALL"
        new_shadow["value_to_record"] = 0
        new_shadow["action"] = "Read all setpoint values from controller"
        # CMD_CNUM might be a list
        num_setpoints = desired.get("CMD_READ", {}).get("CMD_CNUM", None)
        num_setpoints = f"{len(num_setpoints)} " if num_setpoints else ""

        modal_main_header = "Confirm Read All Setpoints"
        modal_main_body = f"Please confirm you would like to read ALL {num_setpoints}setpoints for the {unit_info_msg}. This will take the controller some time..."

    elif (
        "control_select.value" == id_triggered
        or "btn_control_select_write" in id_triggered
    ) and (
        "control_select.value" == id_triggered
        or btn_control_select_write_clicks is not None
    ):
        if control_select_value is None:
            return msg_return(
                "No Dropdown Selected", "Please select a value from the dropdown"
            )
        elif control_select_input_value is None:
            return msg_return("No Target Value", "Please specify a target value")
        elif control_resp_enum is None:
            return msg_return(
                "No Enum Reported",
                "Please read/request the enum from the controller",
            )
        elif not isinstance(control_resp_enum, Number):
            return msg_return(
                "Enum Not Numeric",
                f"Enum of {control_resp_enum} is not numeric. Please read/request the enum from the controller",
            )

        meta = db_control_select_metadata(control_select_value)
        item = meta["item"]
        item_proper = str(item).title()
        units = meta["units"]
        units = f" {units}" if units else ""
        is_valid = True  # no validation for metrics like "A209"

        cnum, enum, cval = get_adv_remote_control_desired_values(
            cnum=control_select_value,
            cval=control_select_input_value,
            enum=control_resp_enum,
        )
        desired = get_adv_remote_control_desired_dict(
            cnum=cnum,
            cval=cval,
            enum=enum,
            # Reads are handled in control_setup_advanced.py
            is_write=True,
        )
        new_shadow["state"] = {"desired": desired}
        new_shadow["metric_to_record"] = cnum
        new_shadow["value_to_record"] = cval
        new_shadow["action"] = (
            f"set '{item_proper}' target to {control_select_input_value}{units}"
        )

        modal_main_header = f"Confirm Target for {item_proper}"
        modal_main_body = f"Please confirm you would like to set the '{item_proper}' target to {control_select_input_value}{units} for the {unit_info_msg}."
        if enum > 1:
            enum_meaning = CONTROL_ENUM_MEANINGS[enum]
            modal_main_body += f"\n\nNOTE: value {control_select_input_value} being sent as {cval} due to enum {enum} [{enum_meaning}]"

    ##################################################################################################################
    # 8 UNO buttons follow
    elif (
        "btn_control_spm_max_uno" in id_triggered
        and btn_control_spm_max_uno_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"SPM_MAX": control_spm_max_uno}}
        new_shadow["action"] = f"set max SPM to {control_spm_max_uno}"
        new_shadow["metric_to_record"] = "SPM_MAX"
        new_shadow["value_to_record"] = control_spm_max_uno
        modal_main_header = "Confirm Max SPM Target"
        modal_main_body = f"Please confirm you would like to set the max SPM target to {control_spm_max_uno} SPM for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_spm_max_uno, "SPM_MAX")

    elif (
        "btn_control_spm_min_uno" in id_triggered
        and btn_control_spm_min_uno_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"SPM_MIN": control_spm_min_uno}}
        new_shadow["action"] = f"set min SPM target to {control_spm_min_uno}"
        new_shadow["metric_to_record"] = "SPM_MIN"
        new_shadow["value_to_record"] = control_spm_min_uno
        modal_main_header = "Confirm Min SPM Sensitivity Target"
        modal_main_body = f"Please confirm you would like to set the min SPM target to {control_spm_min_uno} SPM for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_spm_min_uno, "SPM_MIN")

    elif (
        "btn_control_spm_set_uno" in id_triggered
        and btn_control_spm_set_uno_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"SPM_SET": control_spm_set_uno}}
        new_shadow["action"] = f"set SPM target speed to {control_spm_set_uno}"
        new_shadow["metric_to_record"] = "SPM_SET"
        new_shadow["value_to_record"] = control_spm_set_uno
        modal_main_header = "Confirm SPM Target Speed"
        modal_main_body = f"Please confirm you would like to set the target speed to {control_spm_set_uno} SPM for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_spm_set_uno, "SPM_SET")

    elif "btn_control_apft" in id_triggered and btn_control_apft_clicks is not None:
        #
        new_shadow["state"] = {"desired": {"APFT": control_apft}}
        new_shadow["action"] = f"set auto pump fillage target to {control_apft}"
        new_shadow["metric_to_record"] = "APFT"
        new_shadow["value_to_record"] = control_apft
        modal_main_header = "Confirm Auto Pump Fillage Target"
        modal_main_body = f"Please confirm you would like to set the auto pump fillage target to {control_apft}% for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_apft, "APFT")

    elif (
        "btn_control_pid_sensitivity_uno" in id_triggered
        and btn_control_pid_sensitivity_uno_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"PID_UNO": control_pid_sensitivity_uno}}
        new_shadow["action"] = (
            f"set adjustment speed target to {control_pid_sensitivity_uno}"
        )
        new_shadow["metric_to_record"] = "PID_UNO"
        new_shadow["value_to_record"] = control_pid_sensitivity_uno
        modal_main_header = "Confirm PID Sensitivity Target"
        modal_main_body = f"Please confirm you would like to set the adjustment speed (PID sensitivity) target to {control_pid_sensitivity_uno} for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_pid_sensitivity_uno, "PID_UNO")

    elif "btn_control_ts" in id_triggered and btn_control_ts_clicks is not None:
        #
        new_shadow["state"] = {"desired": {"TS": control_ts}}
        new_shadow["action"] = f"set top setpoint target to {control_ts}"
        new_shadow["metric_to_record"] = "TS"
        new_shadow["value_to_record"] = control_ts
        modal_main_header = "Confirm Top Setpoint Target"
        modal_main_body = f"Please confirm you would like to set the top setpoint target to {control_ts} inches for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_ts, "TS")

    elif "btn_control_bs" in id_triggered and btn_control_bs_clicks is not None:
        #
        new_shadow["state"] = {"desired": {"BS": control_bs}}
        new_shadow["action"] = f"set bottom setpoint target to {control_bs}"
        new_shadow["metric_to_record"] = "BS"
        new_shadow["value_to_record"] = control_bs
        modal_main_header = "Confirm Bottom Setpoint Target"
        modal_main_body = f"Please confirm you would like to set the bottom setpoint target to {control_bs} inches for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_bs, "BS")

    elif (
        "btn_control_auto_tap_tgt" in id_triggered
        and btn_control_auto_tap_tgt_clicks is not None
    ):
        #
        new_shadow["state"] = {"desired": {"AUTO_TAP_TGT": control_auto_tap_tgt_value}}
        new_shadow["action"] = f"set auto-tap target to {control_auto_tap_tgt_value}"
        new_shadow["metric_to_record"] = "AUTO_TAP_TGT"
        new_shadow["value_to_record"] = control_auto_tap_tgt_value
        modal_main_header = "Confirm Auto-Tap Target"
        modal_main_body = f"Please confirm you would like to set the auto-tap target to {control_auto_tap_tgt_value}% for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_auto_tap_tgt_value, "AUTO_TAP_TGT")

    elif "btn_control_peff" in id_triggered and btn_control_peff_clicks is not None:
        #
        new_shadow["state"] = {"desired": {"PEFF": control_peff_value}}
        new_shadow["action"] = f"set pump efficiency target to {control_peff_value}"
        new_shadow["metric_to_record"] = "PEFF"
        new_shadow["value_to_record"] = control_peff_value
        modal_main_header = "Confirm Pump Efficiency Target"
        modal_main_body = f"Please confirm you would like to set the pump efficiency target to {control_peff_value}% for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_peff_value, "PEFF")

    elif "btn_control_psize" in id_triggered and btn_control_psize_clicks is not None:
        #
        new_shadow["state"] = {"desired": {"PSIZE": control_psize_value}}
        new_shadow["action"] = f"set pump size code to {control_psize_value}"
        new_shadow["metric_to_record"] = "PSIZE"
        new_shadow["value_to_record"] = control_psize_value
        modal_main_header = "Confirm Pump Size Code"
        modal_main_body = f"Please confirm you would like to set the pump size code to {control_psize_value} for the {unit_info_msg}"
        is_valid = get_is_valid_input(control_psize_value, "PSIZE")

    elif (
        "btn_control_uno_auto_type" in id_triggered
        and btn_control_uno_auto_type_clicks is not None
    ):
        # UNO switch for automatic type (either average or maximum)
        desired = {}
        if control_uno_auto_type_value == 1:
            type_name = "maximum"
            desired_val = 1
        elif control_uno_auto_type_value == 0:
            type_name = "average"
            desired_val = 0

        if desired:
            new_shadow["state"] = {"desired": {"AUTO_TYPE": desired_val}}
            new_shadow["action"] = f"set auto-type mode to {type_name}"
            new_shadow["metric_to_record"] = f"AUTO_TYPE - {type_name}"
            new_shadow["value_to_record"] = desired_val
            modal_main_header = f"Confirm Change Auto-Type Mode to {type_name.title()}"
            modal_main_body = f'Please confirm you would like to set the auto-type mode to "{type_name}" for the {unit_info_msg}'
            is_valid = True

    elif (
        "btn_control_uno_auto_stuff" in id_triggered
        and btn_control_uno_auto_stuff_clicks is not None
    ):
        # UNO switches for automatic stuff
        desired_dict = {}  # for AWS IoT
        list_items = []  # for the HTML confirmation modal's unordered list
        list_of_settings = ["AUTO_SPM", "AUTO_TAP", "AUTO_TAP_TOP", "AUTO_TAP_BTM"]
        labels_dict = {
            "AUTO_SPM": "Auto SPM",
            "AUTO_TAP": "Auto Tap",
            "AUTO_TAP_TOP": "Auto Tap Top",
            "AUTO_TAP_BTM": "Auto Tap Bottom",
        }
        # These items are being turned on since they're in the "control_uno_auto_stuff_value" list
        for item in control_uno_auto_stuff_value:
            if item in list_of_settings:
                desired_dict[item] = 1
                list_of_settings.remove(item)
                list_items.append(
                    html.Li([f"{labels_dict[item]}: ", html.Strong("On")])
                )

        # These remaining items not removed from the "list_of_settings" are being turned off
        for item in list_of_settings:
            desired_dict[item] = 0
            list_items.append(html.Li([f"{labels_dict[item]}: ", html.Strong("Off")]))

        if desired_dict != {}:
            new_shadow["state"] = {"desired": desired_dict}
            desired_list = [f"{k}: {v}" for k, v in desired_dict.items()]
            new_shadow["action"] = f"set automatic UNO settings: {desired_list}"
            metric_to_record = next(iter(desired_dict.keys()))
            new_shadow["metric_to_record"] = metric_to_record
            new_shadow["value_to_record"] = desired[metric_to_record]["value_desired"]
            modal_main_header = "Confirm Change Automatic Settings"
            modal_main_body = html.Div(
                [
                    html.P(
                        f"Please confirm you would like to change the following settings for the {unit_info_msg}:"
                    ),
                    html.Ul(list_items),
                ]
            )
            is_valid = True

    ##################################################################################################################
    # Shop control at HQ in Moosomin, not unit/pump control in the field

    # elif "btn_unlock_all" in id_triggered and btn_unlock_all_clicks is not None:
    #     if btn_unlock_all_label.lower() == "locked":
    #         value_to_send = 1
    #         opposite_verb = "Unlock"
    #     else:
    #         value_to_send = 0
    #         opposite_verb = "Lock"
    #     # value_to_send = 1
    #     new_shadow["state"] = {"desired": {"unlocked_all": value_to_send}}
    #     new_shadow["action"] = f"{opposite_verb} all doors"
    #     new_shadow["metric_to_record"] = "unlocked_all"
    #     new_shadow["value_to_record"] = value_to_send
    #     modal_main_header = f"Confirm {opposite_verb} All Doors"
    #     modal_main_body = f"Please confirm you would like to {opposite_verb.lower()} all the doors"
    #     is_valid = True

    elif "btn_unlock_office" in id_triggered and btn_unlock_office_clicks is not None:
        if btn_unlock_office_label.lower() == "locked":
            value_to_send = 1
            opposite_verb = "Unlock"
        else:
            value_to_send = 0
            opposite_verb = "Lock"
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"office_unlocked": value_to_send}}
        new_shadow["action"] = f"{opposite_verb} office doors"
        new_shadow["metric_to_record"] = "office_unlocked"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm {opposite_verb} Shop"
        modal_main_body = (
            f"Please confirm you would like to {opposite_verb.lower()} the office door"
        )
        is_valid = True

    elif "btn_unlocked_shop.n_clicks" == id_triggered:
        if btn_unlocked_shop_label.lower() == "locked":
            value_to_send = 1
            opposite_verb = "Unlock"
        else:
            value_to_send = 0
            opposite_verb = "Lock"
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"unlocked_shop": value_to_send}}
        new_shadow["action"] = f"{opposite_verb} shop doors"
        new_shadow["metric_to_record"] = "unlocked_shop"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm {opposite_verb} Shop"
        modal_main_body = f"Please confirm you would like to {opposite_verb.lower()} all doors in the shop"
        is_valid = True

    # elif "btn_unlock_gym" in id_triggered and btn_unlock_gym_clicks is not None:
    #     if btn_unlock_gym_label.lower() == "locked":
    #         value_to_send = 1
    #         opposite_verb = "Unlock"
    #     else:
    #         value_to_send = 0
    #         opposite_verb = "Lock"
    #     # value_to_send = 1
    #     new_shadow["state"] = {"desired": {"gym_unlocked2": value_to_send}}
    #     new_shadow["action"] = f"{opposite_verb} gym door"
    #     new_shadow["metric_to_record"] = "gym_unlocked2"
    #     new_shadow["value_to_record"] = value_to_send
    #     modal_main_header = f"Confirm {opposite_verb} Gym Door"
    #     modal_main_body = (
    #         f"Please confirm you would like to {opposite_verb.lower()} the gym door"
    #     )
    #     is_valid = True

    elif (
        "btn_lights_north_on" in id_triggered and btn_lights_north_on_clicks is not None
    ):
        if btn_lights_north_on_label.lower() == "off":
            value_to_send = 1
            opposite_verb = "On"
        else:
            value_to_send = 0
            opposite_verb = "Off"
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"north_on": value_to_send}}
        new_shadow["action"] = f"Turn {opposite_verb.lower()} north lights"
        new_shadow["metric_to_record"] = "north_on"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm Turn North Lights {opposite_verb}"
        modal_main_body = f"Please confirm you would like to turn {opposite_verb.lower()} the north LED lights"
        is_valid = True

    elif (
        "btn_lights_south_on" in id_triggered and btn_lights_south_on_clicks is not None
    ):
        if btn_lights_south_on_label.lower() == "off":
            value_to_send = 1
            opposite_verb = "On"
        else:
            value_to_send = 0
            opposite_verb = "Off"
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"south_on": value_to_send}}
        new_shadow["action"] = f"Turn {opposite_verb.lower()} south lights"
        new_shadow["metric_to_record"] = "south_on"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm Turn South Lights {opposite_verb}"
        modal_main_body = f"Please confirm you would like to turn {opposite_verb.lower()} the south LED lights"
        is_valid = True

    elif (
        "btn_lights_outdoor_on" in id_triggered
        and btn_lights_outdoor_on_clicks is not None
    ):
        if btn_lights_outdoor_on_label.lower() == "off":
            value_to_send = 1
            opposite_verb = "On"
        else:
            value_to_send = 0
            opposite_verb = "Off"
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"outdoor_on": value_to_send}}
        new_shadow["action"] = f"Turn {opposite_verb.lower()} outdoor lights"
        new_shadow["metric_to_record"] = "outdoor_on"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm Turn Outdoor Lights {opposite_verb}"
        modal_main_body = f"Please confirm you would like to turn {opposite_verb.lower()} the outdoor LED lights"
        is_valid = True

    elif "btn_hrv_fast_on" in id_triggered and btn_hrv_fast_on_clicks is not None:
        if btn_hrv_fast_on_label.lower() == "off":
            value_to_send = 1
            opposite_verb = "On"
        else:
            value_to_send = 0
            opposite_verb = "Off"
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"office_hrv_fast_on": value_to_send}}
        new_shadow["action"] = f"Turn {opposite_verb.lower()} office HRV fast"
        new_shadow["metric_to_record"] = "office_hrv_fast_on"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm Turn Office HRV {opposite_verb}"
        modal_main_body = f"Please confirm you would like to turn {opposite_verb.lower()} the office HRV"
        is_valid = True

    elif (
        "btn_fan_office_main_on" in id_triggered
        and btn_fan_office_main_on_clicks is not None
    ):
        if btn_fan_office_main_on_label.lower() == "off":
            value_to_send = 1
            opposite_verb = "On"
        else:
            value_to_send = 0
            opposite_verb = "Off"
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"fan_office_main_on": value_to_send}}
        new_shadow["action"] = f"Turn {opposite_verb.lower()} office fan"
        new_shadow["metric_to_record"] = "fan_office_main_on"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm Turn Office Main Fan {opposite_verb}"
        modal_main_body = f"Please confirm you would like to turn {opposite_verb.lower()} the office main fan"
        is_valid = True

    elif "btn_ohd_west_open" in id_triggered and btn_ohd_west_open_clicks is not None:
        value_to_send = 1
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"ohd_west_open": value_to_send}}
        new_shadow["action"] = "Open west overhead door"
        new_shadow["metric_to_record"] = "ohd_west_open"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm West Overhead Door {btn_ohd_west_open_label}"
        modal_main_body = f"Please confirm you would like to {btn_ohd_west_open_label.lower()} the west overhead door"
        is_valid = True

    elif "btn_ohd_west_close" in id_triggered and btn_ohd_west_close_clicks is not None:
        value_to_send = 1
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"ohd_west_close": value_to_send}}
        new_shadow["action"] = "Close west overhead door"
        new_shadow["metric_to_record"] = "ohd_west_close"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm West Overhead Door {btn_ohd_west_close_label}"
        modal_main_body = f"Please confirm you would like to {btn_ohd_west_close_label.lower()} the west overhead door"
        is_valid = True

    elif "btn_ohd_west_stop" in id_triggered and btn_ohd_west_stop_clicks is not None:
        value_to_send = 1
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"ohd_west_stop": value_to_send}}
        new_shadow["action"] = "Stop west overhead door"
        new_shadow["metric_to_record"] = "ohd_west_stop"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm West Overhead Door {btn_ohd_west_stop_label}"
        modal_main_body = f"Please confirm you would like to {btn_ohd_west_stop_label.lower()} the west overhead door"
        is_valid = True

    elif "btn_ohd_east_open" in id_triggered and btn_ohd_east_open_clicks is not None:
        value_to_send = 1
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"ohd_east_open": value_to_send}}
        new_shadow["action"] = "Open east overhead door"
        new_shadow["metric_to_record"] = "ohd_east_open"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm East Overhead Door {btn_ohd_east_open_label}"
        modal_main_body = f"Please confirm you would like to {btn_ohd_east_open_label.lower()} the east overhead door"
        is_valid = True

    elif "btn_ohd_east_close" in id_triggered and btn_ohd_east_close_clicks is not None:
        value_to_send = 1
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"ohd_east_close": value_to_send}}
        new_shadow["action"] = "Close east overhead door"
        new_shadow["metric_to_record"] = "ohd_east_close"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm East Overhead Door {btn_ohd_east_close_label}"
        modal_main_body = f"Please confirm you would like to {btn_ohd_east_close_label.lower()} the east overhead door"
        is_valid = True

    elif "btn_ohd_east_stop" in id_triggered and btn_ohd_east_stop_clicks is not None:
        value_to_send = 1
        # value_to_send = 1
        new_shadow["state"] = {"desired": {"ohd_east_stop": value_to_send}}
        new_shadow["action"] = "Stop east overhead door"
        new_shadow["metric_to_record"] = "ohd_east_stop"
        new_shadow["value_to_record"] = value_to_send
        modal_main_header = f"Confirm East Overhead Door {btn_ohd_east_stop_label}"
        modal_main_body = f"Please confirm you would like to {btn_ohd_east_stop_label.lower()} the east overhead door"
        is_valid = True

    if not is_valid:
        # Close the confirmation modal and set the setter_new_shadow to {}
        return return_variables()

    # Open the confirmation modal and dump the new AWS IoT shadow to JSON
    return return_variables(
        setter_new_shadow_data=new_shadow,
        modal_main_header_children=html.H4(modal_main_header),
        modal_main_body_children=modal_main_body,
        modal_main_is_open=True,
    )


@callback(
    Output("control_suction_target_input", "valid"),
    Output("control_suction_target_input", "invalid"),
    Input("control_suction_target_input", "value"),
    State("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def check_validity_suction_target_callback(
    value, store_structure_id_data, store_unit_type_id
):
    log_function_caller()
    is_valid, is_invalid, form_feedback = check_validity_suction_target(
        value, store_structure_id_data, store_unit_type_id
    )
    return is_valid, is_invalid


def check_validity_suction_target(
    value, structure_id, unit_type_id: int = None
) -> Tuple[bool, bool, str]:
    log_function_caller()
    if structure_id is None:
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        structure_id, user_id, unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    if not hasattr(structure_obj, "aws_thing"):
        current_app.logger.warning(
            f"structure_id {structure_id} has no 'aws_thing' attribute"
        )
        raise PreventUpdate()

    is_valid: bool = False
    is_invalid: bool = False
    form_feedback: str = ""
    if value:
        is_vessel_level_mode: bool = structure_obj.suction_range == 4
        if is_vessel_level_mode:
            is_valid = value in range(0, 101)
            if not is_valid:
                form_feedback = "Please enter a value between 0 and 100"
        else:
            swv_plc: float = float(structure_obj.swv_plc)
            is_valid = get_is_valid_input(
                value,
                "AGFT",
                swv_plc=swv_plc,
                is_vessel_level_mode=is_vessel_level_mode,
            )
            if not is_valid:
                form_feedback = "Please enter a value between 0 and 1000"
        is_invalid = not is_valid

    return is_valid, is_invalid, form_feedback


@callback(
    Output("control_max_discharge_input", "valid"),
    Output("control_max_discharge_input", "invalid"),
    Input("control_max_discharge_input", "value"),
    prevent_initial_call=True,
)
def check_validity_max_discharge(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "MGP")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_max_shutdown_pressure_input", "valid"),
    Output("control_max_shutdown_pressure_input", "invalid"),
    Input("control_max_shutdown_pressure_input", "value"),
    prevent_initial_call=True,
)
def check_validity_max_shutdown_pressure(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "MSP")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_max_speed_input", "valid"),
    Output("control_max_speed_input", "invalid"),
    Input("control_max_speed_input", "value"),
    prevent_initial_call=True,
)
def check_validity_max_speed(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "AGFM")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_min_speed_input", "valid"),
    Output("control_min_speed_input", "invalid"),
    Input("control_min_speed_input", "value"),
    prevent_initial_call=True,
)
def check_validity_min_speed(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "AGFN")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_horsepower_limit_input", "valid"),
    Output("control_horsepower_limit_input", "invalid"),
    Input("control_horsepower_limit_input", "value"),
    prevent_initial_call=True,
)
def check_validity_max_horsepower(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "HP_LIMIT")
        return is_valid, not is_valid
    return False, False


# @callback(
#     [Output('control_horsepower_limit_uno', 'valid'),
#     Output('control_horsepower_limit_uno', 'invalid')],
#     [Input('control_horsepower_limit_uno', 'value')]),
# prevent_initial_call=True
# def check_validity_max_horsepower_uno(value):
#     if value is not None:
#         is_valid = get_is_valid_input(value, 'HP_LIMIT')
#         return is_valid, not is_valid
#     return False, False


@callback(
    Output("control_pid_sensitivity_input", "valid"),
    Output("control_pid_sensitivity_input", "invalid"),
    Input("control_pid_sensitivity_input", "value"),
    prevent_initial_call=True,
)
def check_validity_pid_sensitivity_egas(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "PID")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_pid_sensitivity_uno", "valid"),
    Output("control_pid_sensitivity_uno", "invalid"),
    Input("control_pid_sensitivity_uno", "value"),
    prevent_initial_call=True,
)
def check_validity_pid_sensitivity_uno(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "PID_UNO")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_apft", "valid"),
    Output("control_apft", "invalid"),
    Input("control_apft", "value"),
    prevent_initial_call=True,
)
def check_validity_apft(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "APFT")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_spm_set_uno", "valid"),
    Output("control_spm_set_uno", "invalid"),
    Input("control_spm_set_uno", "value"),
    prevent_initial_call=True,
)
def check_validity_pid_spm_set(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "SPM_SET")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_spm_max_uno", "valid"),
    Output("control_spm_max_uno", "invalid"),
    Input("control_spm_max_uno", "value"),
    prevent_initial_call=True,
)
def check_validity_spm_max(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "SPM_MAX")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_spm_min_uno", "valid"),
    Output("control_spm_min_uno", "invalid"),
    Input("control_spm_min_uno", "value"),
    prevent_initial_call=True,
)
def check_validity_spm_min(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "SPM_MIN")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_ts", "valid"),
    Output("control_ts", "invalid"),
    Input("control_ts", "value"),
    prevent_initial_call=True,
)
def check_validity_top_setpoint(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "TS")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_bs", "valid"),
    Output("control_bs", "invalid"),
    Input("control_bs", "value"),
    prevent_initial_call=True,
)
def check_validity_bottom_setpoint(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "BS")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_auto_tap_tgt", "valid"),
    Output("control_auto_tap_tgt", "invalid"),
    Input("control_auto_tap_tgt", "value"),
    prevent_initial_call=True,
)
def check_validity_control_auto_tap_tgt(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "AUTO_TAP_TGT")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_peff", "valid"),
    Output("control_peff", "invalid"),
    Input("control_peff", "value"),
    prevent_initial_call=True,
)
def check_validity_control_peff(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "PEFF")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_psize", "valid"),
    Output("control_psize", "invalid"),
    Input("control_psize", "value"),
    prevent_initial_call=True,
)
def check_validity_control_psize(value):
    log_function_caller()
    if value is not None:
        is_valid = get_is_valid_input(value, "PSIZE")
        return is_valid, not is_valid
    return False, False


@callback(
    Output("control_maintenance_interval_input", "valid"),
    Output("control_maintenance_interval_input", "invalid"),
    Input("control_maintenance_interval_input", "value"),
    prevent_initial_call=True,
)
def check_validity_control_maintenance_interval_input(value):
    log_function_caller()
    if isinstance(value, (int, float)) and 1 <= value <= 36:
        return True, False
    return False, True


@callback(
    Output("suction_sched_start_after_x_mins", "valid"),
    Output("suction_sched_start_after_x_mins", "invalid"),
    Input("suction_sched_start_after_x_mins", "value"),
    prevent_initial_call=True,
)
def check_validity_schedule_start_in_x_minutes(value):
    log_function_caller()
    if value is not None:
        is_valid = True if value >= 0 else False
        return is_valid, not is_valid
    return False, False


@callback(
    Output("suction_sched_over_x_mins", "valid"),
    Output("suction_sched_over_x_mins", "invalid"),
    Input("suction_sched_over_x_mins", "value"),
    prevent_initial_call=True,
)
def check_validity_schedule_do_over_x_minutes(value):
    log_function_caller()
    if value is not None:
        is_valid = True if value >= 0 else False
        return is_valid, not is_valid
    return False, False


@callback(
    Output("suction_sched_value_start", "valid"),
    Output("suction_sched_value_start", "invalid"),
    Output("suction_sched_value_start_form_feedback", "children"),
    Input("suction_sched_value_start", "value"),
    State("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def check_validity_suction_sched_value_start(
    value, store_structure_id_data, store_unit_type_id
):
    log_function_caller()
    is_valid: bool = False
    is_invalid: bool = False
    form_feedback: str = ""
    if value is not None:
        is_valid, is_invalid, form_feedback = check_validity_suction_target(
            value, store_structure_id_data, store_unit_type_id
        )
    return is_valid, is_invalid, form_feedback


@callback(
    Output("control_msg_status_refresh_btn", "n_clicks"),
    Input("control_start_card_header_uno_btn", "n_clicks"),
    Input("control_start_card_header_egas_btn", "n_clicks"),
    Input("pressure_card_refresh_btn", "n_clicks"),
    Input("speed_egas_card_refresh_btn", "n_clicks"),
    Input("speed_uno_card_refresh_btn", "n_clicks"),
    Input("other_settings_card_refresh_btn", "n_clicks"),
    Input("automatic_settings_card_refresh_btn", "n_clicks"),
    Input("vpn_access_card_refresh_btn", "n_clicks"),
    Input("apn_card_refresh_btn", "n_clicks"),
    Input("control_msg_status_cancel_btn", "n_clicks"),
    Input("control_admin_card_refresh_btn", "n_clicks"),
    Input("control_adv_rc_refresh_btn", "n_clicks"),
    Input("btn_control_select_read", "n_clicks"),
    State("control_msg_status_refresh_btn", "n_clicks"),
    prevent_initial_call=True,
)
def many_refresh_button_clicks(
    b1,
    b2,
    b3,
    b4,
    b5,
    b6,
    b7,
    b8,
    b9,
    b10,
    b11,
    b12,
    b13,
    main_refresh_button_clicks,
):
    """
    Since all the remote control refresh buttons serve the same purpose,
    make all the other refresh buttons just increment the main refresh button's "n_clicks" property
    to trigger a cascade refresh
    """
    log_function_caller()
    if main_refresh_button_clicks is None:
        return 1

    return main_refresh_button_clicks + 1


@callback(
    Output("control_tab_refresh_interval", "n_intervals", allow_duplicate=True),
    Input("control_msg_status_refresh_btn", "n_clicks"),
    prevent_initial_call=True,
)
def reset_refresh_interval_counter(_):
    """Reset the interval counter to 0 if the user presses any refresh buttons"""
    return 0


@callback(
    # Output("send_command_output_store", "data"),
    Output("send_command_card_refresh_btn", "n_clicks"),
    Output("send_command_output", "children"),
    Input("btn_subscribe_mqtt", "n_clicks"),
    Input("btn_send_command", "n_clicks"),
    State("send_command_input", "value"),
    State("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def send_command_to_unit(
    btn_subscribe_mqtt_n_clicks,
    btn_send_command_n_clicks,
    command,
    store_structure_id_data,
    store_unit_type_id,
):
    log_function_caller()
    id_triggered: str = get_id_triggered()
    if not id_triggered:
        raise PreventUpdate()

    def return_vars(
        # send_command_output_store={},
        send_command_card_refresh_btn=0,
        send_command_output="",
    ):
        """Default return variables"""
        return (
            # send_command_output_store,
            send_command_card_refresh_btn,
            send_command_output,
        )

    if store_structure_id_data is None:
        return return_vars()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        return return_vars()

    if not hasattr(structure_obj, "aws_thing"):
        current_app.logger.warning(
            f"structure_id {store_structure_id_data} has no 'aws_thing' attribute"
        )
        return return_vars()

    aws_thing = structure_obj.aws_thing
    gateway_id = structure_obj.gateway_id

    if id_triggered == "btn_subscribe_mqtt.n_clicks" and btn_subscribe_mqtt_n_clicks:
        mqtt_client.subscribe_to_aws_thing(aws_thing=aws_thing)
        return return_vars(
            # send_command_output_store=no_update,
            send_command_card_refresh_btn=no_update,
            send_command_output="Subscribed to MQTT topic",
        )

    if command is None:
        return return_vars()

    # Send the command to the AWS IoT device
    response: bool = mqtt_client.publish_to_gateway(
        aws_thing=aws_thing, payload={"message": command}, gateway_id=gateway_id
    )
    if not response:
        return return_vars(
            # send_command_output_store={
            #     "message": "Error sending command",
            #     "status": "error",
            # },
            send_command_card_refresh_btn=1,
            send_command_output="Error sending command",
        )

    return return_vars(
        # send_command_output_store=response,
        send_command_card_refresh_btn=1,
        send_command_output=f"Command sent to gateway {aws_thing}: '{command}'",
    )


@callback(
    # MQTT command responses
    Output("send_command_responses", "children"),
    # Input("control_tab_refresh_interval", "n_intervals"),
    Input("send_command_card_refresh_btn", "n_clicks"),
    State("store_structure_id", "data"),
    # State("send_command_output_store", "data"),
    prevent_initial_call=True,
)
def get_mqtt_command_responses(
    # n_intervals,
    n_clicks,
    store_structure_id_data,
    # send_command_output_store_data,
):
    """Get the MQTT command responses from the database, from the last 1 hour"""

    log_function_caller()
    if not n_clicks:
        # or not send_command_output_store_data:
        raise PreventUpdate()

    # Get the structure
    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    # Get all the messages from the database, from the last 1 hour
    messages: list = mqtt_client.get_all_messages_from_db(
        gateway_id=structure_obj.gateway_id, days_ago=1 / 24
    )
    # if not messages:
    #     messages = mqtt_client.get_all_messages()

    children: list = []
    if isinstance(messages, list) and messages:
        for message in messages:
            if isinstance(message, MQTTMessage):
                try:
                    message_dict: dict = json.loads(message.message)
                    if isinstance(message_dict, str):
                        message_dict = json.loads(message_dict)
                except json.JSONDecodeError:
                    message_dict = {"rc": 1, "stdout": "error", "stderr": "error"}
                rc: int = message_dict.get("rc", 0)
                stdout: str = message_dict.get("stdout", "")
                stderr: str = message_dict.get("stderr", "")
                combined: list = [
                    f"{message.timestamp_utc}: rc = {rc}. stdout = {stdout}. stderr = {stderr}",
                    html.Br(),
                    html.Br(),
                ]
                children = children + combined

    if children:
        return html.Div(children)

    return html.Div(html.P("No responses found"))
