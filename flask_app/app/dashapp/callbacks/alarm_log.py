import dash_bootstrap_components as dbc
import numpy as np
import pandas as pd
import pytz
from dash import Input, Output, State, callback, dash_table, html
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import StructureVw
from sqlalchemy.sql import text

from app import cache_memoize_if_prod, user_is_demo_customer, user_is_ijack_employee
from app.config import TAB_LOG, UNIT_TYPE_ID_EGAS, UNIT_TYPE_ID_UNO, UNIT_TYPE_ID_UNOGAS
from app.dashapp.utils import (
    get_structure_obj,
    get_unit_type,
    log_function_caller,
)
from app.databases import run_sql_query
from app.email_stuff import send_email


def get_alarm_log_div():
    """Get the alarm log div"""
    return dbc.Row(
        id="alarm_log_div",
        style={"display": "none"},
        class_name="mt-3",
        children=dbc.Col(
            dbc.Card(
                [
                    dbc.CardHeader(
                        dbc.Row(
                            [
                                dbc.Col(
                                    dbc.Spinner(
                                        "Alarm Log (Last 6 Months)",
                                        id="alarm_log_card_header",
                                        color="success",
                                    ),
                                    width="auto",
                                ),
                                dbc.Col(
                                    dbc.Button(
                                        [
                                            html.I(
                                                className="fa-solid fa-refresh me-1"
                                            ),
                                            "Refresh",
                                        ],
                                        id="alarm_log_refresh_btn",
                                        color="primary",
                                        outline=True,
                                        size="sm",
                                        # line-height 1
                                        class_name="lh-1",
                                    ),
                                    width="auto",
                                ),
                            ],
                            justify="between",
                        )
                    ),
                    dbc.CardBody(
                        dbc.Spinner(
                            html.Div(id="alarm_log_card_body"),
                            color="success",
                        ),
                        class_name="mx-1",
                    ),
                ],
            ),
            xs=12,
        ),
    )


@cache_memoize_if_prod(timeout=60)
def alarm_log_data_for_redis(power_unit_str: str):
    """Query the alarm log data into Pandas DataFrame"""

    current_app.logger.debug("Querying public.alarm_log for all records...")

    # This query used to be simply "select * from public.alarm_log_mv where gateway = :gateway"
    # but that materialized view is not necessary since the table is so well-indexed and relatively small.
    # This allows the log to be more timely, without waiting for the materialized view to refresh.
    sql = text(
        """
        SELECT DISTINCT
            --the following "on ()" statement is necessary because the
            --CUSTOMERS table adds more than one customer per timestamp_local/power_unit_str/abbrev/value
            --That way this table only gets the first combo of timestamp_local/power_unit_str/abbrev/value
            on
            (t1.power_unit, t1.timestamp_local, t1.abbrev, t1.value)
            t1.timestamp_local,
            t1.power_unit,
            t5.power_unit_str,
            t2.name,
            t2.description,
            replace(t1.abbrev, '_', '') AS abbrev,
            t6.customer,
            t7.model,
            t4.surface,
            t1.value
        FROM alarm_log t1
            LEFT JOIN alarm_log_metrics t2
                ON replace(t1.abbrev, '_', '') = t2.abbrev
                AND t1.index = t2.index
            --LEFT JOIN map_abbrev_item t2 ON replace(t1.abbrev, '_', '') = t2.abbrev
            --LEFT JOIN gw t3 ON t1.gateway = t3.gateway
            LEFT JOIN power_units t5 ON t5.power_unit_str = t1.power_unit
            LEFT JOIN structures t4 ON t4.power_unit_id = t5.id
            LEFT JOIN public.structure_customer_rel t41 ON t41.structure_id = t4.id
            LEFT JOIN public.customers t6 ON t6.id = t41.customer_id
            LEFT JOIN public.model_types t7 ON t7.id = t4.model_type_id
        WHERE
            --Only get the last six months, and ignore bad timestamp data way in the future
            t1.timestamp_local between (now() - '180 days'::interval) and '3000-01-01'
            and t1.power_unit = :power_unit_str
        ORDER BY t1.timestamp_local DESC
        LIMIT 1000
    """
    ).bindparams(power_unit_str=power_unit_str)
    # cursor.execute(sql)
    # columns = [str.lower(x[0]) for x in cursor.description]
    # rows = cursor.fetchall()
    rows, columns = run_sql_query(sql, db_name="ijack")

    # Build an alarm log DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)

    # if len(rows) == 0:
    #     return df

    df["timestamp_local"] = pd.to_datetime(df["timestamp_local"])
    df = df.sort_values(["power_unit_str", "timestamp_local"])

    # current_app.logger.info(f"NEW alarm log queried and cached. df.head(): \n{df.head()}")

    return df


@cache_memoize_if_prod(timeout=20)
def get_remote_control_alerts_sent_hist_df(
    aws_thing: str, power_unit_str: str
) -> pd.DataFrame:
    """Query the historical remote control and alerts-sent data into Pandas DataFrame"""

    current_app.logger.debug(
        "Querying public.remote_control and public.alerts_sent for historical data..."
    )
    # df_rc['name'] = 'RC: ' + df_rc['action'] + ' (' + df_rc ['full_name'] + ')'
    sql = text(
        """
        select
            t1.timestamp_utc,
            concat('Remote control: ', t1.action, ' (', t2.first_name, ' ', t2.last_name, ')') as name,
            null as description
        from public.remote_control t1
        left join public.users t2
            on t1.user_id = t2.id
        where
            t1.timestamp_utc >= (now() - '60 days'::interval)
            and t1.dev_test_prd = :dev_test_prd
            and (t1.aws_thing = :aws_thing or t1.power_unit = :power_unit_str)
            -- Exclude records where user_id = 1 (Sean McCarthy) and action contains 'VPN'
            AND NOT EXISTS (
                SELECT 1
                FROM public.remote_control t3
                WHERE t3.user_id = 1
                    AND (t3.metric LIKE '%VPN%' or t3.action LIKE '%VPN%')
                    AND t3.timestamp_utc = t1.timestamp_utc
            )

        union

        select
            t1.timestamp_utc,
            --Truncate the warning_msg column to X characters for the table
            concat('Alert sent: ', left(t1.warning_msg, 100), '...') as name,
            concat('Alert sent: ', t1.warning_msg) as description
        from public.alerts_sent t1
        where
            t1.timestamp_utc >= (now() - '60 days'::interval)
            and t1.dev_test_prd = :dev_test_prd2
            and (t1.gateway = :gateway or t1.power_unit = :power_unit_str)
    """
    ).bindparams(
        dev_test_prd="production",
        dev_test_prd2="production",
        aws_thing=aws_thing,
        gateway=aws_thing,
        power_unit_str=power_unit_str,
    )
    # cursor.execute(sql)
    # columns = [str.lower(x[0]) for x in cursor.description]
    # rows = cursor.fetchall()
    rows, columns = run_sql_query(sql, db_name="ijack")

    if len(rows) == 0:
        return pd.DataFrame()

    # Build a DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)
    df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])

    current_app.logger.debug(
        "Remote control history queried and cached. df.head(): \n%s", df.head()
    )

    return df


@callback(
    Output("alarm_log_card_header", "children"),
    Output("alarm_log_card_body", "children"),
    Output("record_visit_rcom_alarm_log", "data"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    Input("alarm_log_refresh_btn", "n_clicks"),
    # State("store_customer_id", "data"),
    State("store_unit_type_id", "data"),
)
def update_alarm_log(
    store_structure_id_data,
    active_tab,
    tab_uno_egas,
    alarm_log_refresh_btn_n_clicks,
    # store_customer_id,
    store_unit_type_id,
):
    """Alarm log data table"""
    log_function_caller()

    if store_structure_id_data is None or active_tab != TAB_LOG:
        raise PreventUpdate()

    # structure_id, unit_type_id = get_stored_structure_unit(
    #     store_structure_dict, store_customer_id
    # )
    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()
    unit_type_lower, unit_type_id = get_unit_type(tab_uno_egas, store_unit_type_id)
    tz_wanted = structure_obj.time_zone

    aws_thing = structure_obj.aws_thing
    power_unit_str = structure_obj.power_unit_str
    has_rcom: bool = bool(structure_obj.aws_thing)

    # Identify the demo user for which to make fake locations
    if user_is_demo_customer(user_id=user_id):
        title = "Log (Last 6 Months)"
    else:
        customer_first_word = structure_obj.customer.split()[0]
        customer_unit_info = f"{customer_first_word} {unit_type_lower.upper()} {power_unit_str} at {structure_obj.surface}"
        title = f"Log (Last 6 Months) for {customer_unit_info}"

    def return_variables(
        title,
        msg="No data to display",
        record_visit_rcom="rcom_alarm_log",
    ):
        """Default return values"""
        return title, html.H3(msg), record_visit_rcom

    if not has_rcom:
        return return_variables(title, msg="Unit doesn't have RCOM")

    if tz_wanted is None:
        current_app.logger.error(f"structure_obj.time_zone: {tz_wanted}")
        return return_variables(
            title,
            msg="Please set the time zone under 'Account/Admin/Units/Unit Information' to see alarm log data",
        )
    pytz_timezone = pytz.timezone(tz_wanted)

    # Query the materialized view for alarm log data
    df_temp = alarm_log_data_for_redis(power_unit_str)
    df = df_temp.copy(deep=True)

    def localize_timestamp(value, pytz_tz: pytz.timezone):
        """Localize the timestamp column in the DataFrame"""

        # current_app.logger.debug(f"Localizing timestamp: {value}")
        try:
            new_value = pytz_tz.localize(value)
        except Exception as err:
            current_app.logger.exception(f"Error localizing timestamp: {err}")
            return pd.NaT

        return new_value

    try:
        # Localize (make time-zone-aware) the timestamp so we can append the remote-control-history data without errors
        # In the fall in Alberta, the hour at 2:00 a.m. is repeated twice (i.e. "fall back") and since the alarm logs are reported as gateway-local times (not UTC times),
        # I have to infer what local time it actually is... The PLC has no idea about DST, I think.
        # df["timestamp_local"] = df["timestamp_local"].dt.tz_localize(
        #     tz_wanted, ambiguous="NaT"
        # )
        df["timestamp_local"] = df["timestamp_local"].transform(
            localize_timestamp, pytz_tz=pytz_timezone
        )
        # current_app.logger.debug(f"Regular alarm log df: \n{df.head()}")
    except Exception as err:
        current_app.logger.exception(
            f"Error localizing the timestamp_local column in the alarm log data. df.head(): \n{df.head()}"
        )
        send_email(
            subject="Error localizing the timestamp_local column in the alarm log data",
            sender="IJACK <<EMAIL>>",
            to_emails=["<EMAIL>"],
            text_body=f"Error localizing the timestamp_local column in the alarm log data. df.head(): \n{df.head()}\n\nError: {err}",
            testing=False,
        )
        return return_variables(
            title, msg="Error localizing the timestamp_local column"
        )

    data_table_cols = [
        {"name": "Timestamp", "id": "timestamp_local"},
        {"name": "Name (Hover for More)", "id": "name"},
        # {"name": "Description", "id": "description"},
    ]
    if len(df.index) != 0:
        row_filters = None
        if structure_obj.unit_type_id == UNIT_TYPE_ID_UNOGAS:
            if unit_type_id == UNIT_TYPE_ID_EGAS:
                row_filters = df["abbrev"].str.startswith("AE")
            elif unit_type_id == UNIT_TYPE_ID_UNO:
                row_filters = df["abbrev"].str.startswith("AU")

        # If the "name" field is empty, at least show the "abbrev" field
        df["name"] = np.where(
            df["name"].isin([None, ""]),
            df["abbrev"] + " - No description",
            df["name"],
        )

        columns_wanted = ("timestamp_local", "name", "description")

        if user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
            columns_wanted += ("abbrev",)
            data_table_cols.append({"name": "Code", "id": "abbrev"})

        if row_filters is not None:
            df = df.loc[row_filters, columns_wanted]
        else:
            df = df.loc[:, columns_wanted]

    # Add remote control action, and alerts sent, history
    df_rc_temp = get_remote_control_alerts_sent_hist_df(
        aws_thing=aws_thing, power_unit_str=power_unit_str
    )
    df_rc = df_rc_temp.copy(deep=True)
    if len(df_rc.index) != 0:
        # Convert to local gateway time
        df_rc["timestamp_local"] = (
            df_rc["timestamp_utc"].dt.tz_localize(pytz.utc).dt.tz_convert(tz_wanted)
        )

        # Join the alarm log and remote control history dataframes
        if len(df.index) != 0:
            df = pd.concat([df, df_rc], ignore_index=True)
        else:
            df = df_rc

    if len(df.index) == 0:
        return return_variables(title)

    if "timestamp_local" not in df.columns:
        current_app.logger.error(
            f"ERROR: 'timestamp_local' not in df.columns: \n{df.head()}"
        )
        return return_variables(
            title,
            msg="Problem retrieving data. Please check if there's a problem with the time zone under 'Account/Admin/Units/Unit Information'",
        )

    df = df.sort_values(["timestamp_local"], ascending=False)
    df["timestamp_local"] = df["timestamp_local"].dt.strftime("%a, %b %d/%y, %I:%M %p")
    # current_app.logger.debug(f"\ndf: \n{df.head()}")

    df_data: list = df.to_dict("records")
    tooltip_data: list = [
        {"name": {"value": row["description"], "type": "markdown"}} for row in df_data
    ]
    card_body = dash_table.DataTable(
        data=df_data,
        columns=data_table_cols,
        tooltip_data=tooltip_data,
        tooltip_delay=0,  # appear immediately
        tooltip_duration=None,  # None = tooltips remain visible while hovering over them
        page_action="native",  # pagination
        page_size=20,  # num rows to display
        sort_action="native",
        editable=False,
        style_table={
            "fontFamily": "Open Sans, sans-serif",
            "fontSize": "1rem",
            "width": "100%",
            "overflowX": "auto",  # 'auto' or 'scroll' (both seem to work, and not work at times...)
            # 'margin': "0.5rem"
        },
        style_header={
            "backgroundColor": "white",
            "fontFamily": "Open Sans, sans-serif",
            "fontSize": "1rem",
            "fontWeight": "bold",
            # Wrap text (whiteSpace: "normal")
            "whiteSpace": "normal",
            "textAlign": "left",
            "lineHeight": "1.5rem",
        },
        style_data={
            "whiteSpace": "normal",
            "height": "auto",
            "fontFamily": "Open Sans, sans-serif",
            "fontSize": "1rem",
            "lineHeight": "1.5rem",
        },
        # style_data={
        #     # whiteSpace and height serve to wrap the text on small screens
        #     'whiteSpace': 'normal',
        #     # 'height': 'auto'
        # },
        style_cell={
            # Normal font size for the site is 16px
            # "font-size": "14px",
            "fontSize": "1rem",
            "textAlign": "left",
            # Padding-left is important actually, both between columns and on the left
            "padding": "0.5rem",
            # 'margin': "0.5rem",
            # "color": "#717174",
            "color": "rgb(33, 37, 41)",
            # 'height': 'auto',
            # 'whiteSpace': 'normal',
            # 'minWidth': '0px',
            # 'maxWidth': '180px',
            # 'overflow': 'hidden',
            # 'textOverflow': 'ellipsis',
        },
        # No vertical grid lines
        # style_as_list_view=True,
        style_data_conditional=[
            {"if": {"row_index": "even"}, "backgroundColor": "rgba(0, 0, 0, 0.05)"}
        ],
        # content_style='grow',
        # css=[{
        #     'selector': '.dash-cell div.dash-cell-value',
        #     'rule': 'display: inline; white-space: inherit; overflow: inherit; text-overflow: inherit;'
        # }],
        # css=[
        #     {
        #         'selector': 'table',
        #         'rule': 'width: 100%;'
        #     }
        # ],
    )

    record_visit_rcom = "rcom_alarm_log"

    return return_variables(title, card_body, record_visit_rcom)
