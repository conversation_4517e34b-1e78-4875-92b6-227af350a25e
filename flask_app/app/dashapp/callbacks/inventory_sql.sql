select
    b1.model_type_id,
    b1.part_name,
    b1.part_type,
    -- Check if we have any duplicates on model_type_id and part_name
    COUNT(*) OVER (
        PARTITION BY b1.part_name,
        b1.model_type_id,
        b1.part_type
    ) AS num_occurrences_bom,
    -- c1.description,
    -- Four different costs for showing different people
    b1.cost_cad :: float,
    b1.cost_usd :: float,
    b1.msrp_cad :: float,
    b1.msrp_usd :: float,
    b1.bom_estimate_total :: float,
    b5.model_type_id_count_sql :: float,
    b1.bom_estimate_total :: float / b5.model_type_id_count_sql :: float as bom_estimate_per_model
from
    (
        select
            a1.model_type_id,
            a1.part_name,
            a1.part_type,
            -- Check if we have any duplicates on model_type_id and part_name
            -- COUNT(*) OVER (PARTITION BY a1.part_name, a1.model_type_id) AS num_occurrences,
            avg(a1.cost_cad) as cost_cad,
            avg(a1.cost_usd) as cost_usd,
            avg(a1.msrp_cad) as msrp_cad,
            avg(a1.msrp_usd) as msrp_usd,
            sum(a1.bom_estimate_total) as bom_estimate_total
        from
            (
                -- Bill of materials parts for base powerunit types
                SELECT
                    t1.model_type_id,
                    t5.part_name,
                    'Base Powerunit' as part_type,
                    avg(t5.cost_cad) as cost_cad,
                    avg(t5.cost_usd) as cost_usd,
                    avg(t5.msrp_cad) as msrp_cad,
                    avg(t5.msrp_usd) as msrp_usd,
                    sum(t2.quantity * t4.quantity * t5.warehouse_mult) as bom_estimate_total
                FROM
                    public.vw_structures_joined_filtered t1 -- How many base power units are needed for each high-level power unit type?
                    inner join public.bom_base_powerunit_power_unit_type_rel t2 on t2.power_unit_type_id = t1.power_unit_type_id -- inner join public.bom_base_powerunit t3
                    --     on t3.id = t2.finished_good_id
                    -- Relates base power unit to the parts it needs
                    inner join public.bom_base_powerunit_part_rel t4 -- on t4.finished_good_id = t3.id
                    on t4.finished_good_id = t2.finished_good_id
                    inner join public.parts t5 on t5.id = t4.part_id
                where
                    t2.quantity > 0
                    and t4.quantity > 0
                    and t5.warehouse_mult > 0
                    and t1.model_type_id in :model_type_ids
                    and t5.is_soft_part in :is_soft_part_tuple
                    and t5.flagged_for_deletion = false
                group by
                    t1.model_type_id,
                    t5.part_name
                union
                all -- Bill of materials parts for powerunit types
                SELECT
                    t1.model_type_id,
                    t5.part_name,
                    'Powerunit' as part_type,
                    avg(t5.cost_cad) as cost_cad,
                    avg(t5.cost_usd) as cost_usd,
                    avg(t5.msrp_cad) as msrp_cad,
                    avg(t5.msrp_usd) as msrp_usd,
                    sum(t2.quantity * t4.quantity * t5.warehouse_mult) as bom_estimate_total
                FROM
                    public.vw_structures_joined_filtered t1 -- How many power units are needed for each high-level power unit type?
                    inner join public.bom_powerunit_power_unit_type_rel t2 on t2.power_unit_type_id = t1.power_unit_type_id -- inner join public.bom_powerunit t3
                    --     on t3.id = t2.finished_good_id
                    -- How many parts are needed for each power unit?
                    inner join public.bom_powerunit_part_rel t4 -- on t4.finished_good_id = t3.id
                    on t4.finished_good_id = t2.finished_good_id
                    inner join public.parts t5 on t5.id = t4.part_id
                where
                    t2.quantity > 0
                    and t4.quantity > 0
                    and t5.warehouse_mult > 0
                    and t1.model_type_id in :model_type_ids
                    and t5.is_soft_part in :is_soft_part_tuple
                    and t5.flagged_for_deletion = false
                group by
                    t1.model_type_id,
                    t5.part_name
                union
                all -- Bill of materials parts for structure types
                SELECT
                    t1.model_type_id,
                    t5.part_name,
                    'Structure' as part_type,
                    avg(t5.cost_cad) as cost_cad,
                    avg(t5.cost_usd) as cost_usd,
                    avg(t5.msrp_cad) as msrp_cad,
                    avg(t5.msrp_usd) as msrp_usd,
                    sum(t2.quantity * t4.quantity * t5.warehouse_mult) as bom_estimate_total
                FROM
                    public.vw_structures_joined_filtered t1 -- How many structures are needed for each high-level structure type?
                    inner join public.bom_structure_model_type_rel t2 on t2.model_type_id = t1.model_type_id -- inner join public.bom_structure t3
                    --     on t3.id = t2.finished_good_id
                    -- How many parts are needed for each structure?
                    inner join public.bom_structure_part_rel t4 -- on t4.finished_good_id = t3.id
                    on t4.finished_good_id = t2.finished_good_id
                    inner join public.parts t5 on t5.id = t4.part_id
                where
                    t2.quantity > 0
                    and t4.quantity > 0
                    and t5.warehouse_mult > 0
                    and t1.model_type_id in :model_type_ids
                    and t5.is_soft_part in :is_soft_part_tuple
                    and t5.flagged_for_deletion = false
                group by
                    t1.model_type_id,
                    t5.part_name
                union
                all -- Bill of materials parts for pump top types
                SELECT
                    t1.model_type_id,
                    t5.part_name,
                    'Pump Top' as part_type,
                    avg(t5.cost_cad) as cost_cad,
                    avg(t5.cost_usd) as cost_usd,
                    avg(t5.msrp_cad) as msrp_cad,
                    avg(t5.msrp_usd) as msrp_usd,
                    sum(t2.quantity * t4.quantity * t5.warehouse_mult) as bom_estimate_total
                FROM
                    public.vw_structures_joined_filtered t1 -- How many pump tops are needed for each high-level pump top/model type?
                    inner join public.bom_pump_top_model_type_rel t2 on t2.model_type_id = t1.model_type_id -- inner join public.bom_pump_top t3
                    --     on t3.id = t2.finished_good_id
                    -- How many parts are needed for each pump top?
                    inner join public.bom_pump_top_part_rel t4 -- on t4.finished_good_id = t3.id
                    on t4.finished_good_id = t2.finished_good_id
                    inner join public.parts t5 on t5.id = t4.part_id
                where
                    t2.quantity > 0
                    and t4.quantity > 0
                    and t5.warehouse_mult > 0
                    and t1.model_type_id in :model_type_ids
                    and t5.is_soft_part in :is_soft_part_tuple
                    and t5.flagged_for_deletion = false
                group by
                    t1.model_type_id,
                    t5.part_name
                union
                all -- Bill of materials parts for DGAS types
                SELECT
                    t1.model_type_id,
                    t5.part_name,
                    'DGAS' as part_type,
                    avg(t5.cost_cad) as cost_cad,
                    avg(t5.cost_usd) as cost_usd,
                    avg(t5.msrp_cad) as msrp_cad,
                    avg(t5.msrp_usd) as msrp_usd,
                    sum(t2.quantity * t4.quantity * t5.warehouse_mult) as bom_estimate_total
                FROM
                    public.vw_structures_joined_filtered t1 -- How many DGAS are needed for each high-level DGAS/model type?
                    inner join public.bom_dgas_model_type_rel t2 on t2.model_type_id = t1.model_type_id -- inner join public.bom_dgas t3
                    --     on t3.id = t2.finished_good_id
                    -- How many parts are needed for each DGAS?
                    inner join public.bom_dgas_part_rel t4 -- on t4.finished_good_id = t3.id
                    on t4.finished_good_id = t2.finished_good_id
                    inner join public.parts t5 on t5.id = t4.part_id
                where
                    t2.quantity > 0
                    and t4.quantity > 0
                    and t5.warehouse_mult > 0
                    and t1.model_type_id in :model_type_ids
                    and t5.is_soft_part in :is_soft_part_tuple
                    and t5.flagged_for_deletion = false
                group by
                    t1.model_type_id,
                    t5.part_name
                union
                all -- Extra parts needed for each structure model type
                SELECT
                    t1.model_type_id,
                    t5.part_name,
                    'Extra Parts' as part_type,
                    avg(t5.cost_cad) as cost_cad,
                    avg(t5.cost_usd) as cost_usd,
                    avg(t5.msrp_cad) as msrp_cad,
                    avg(t5.msrp_usd) as msrp_usd,
                    sum(t2.quantity * t5.warehouse_mult) as bom_estimate_total
                FROM
                    public.vw_structures_joined_filtered t1 -- How many extra parts are needed for each structure?
                    inner join public.model_types_parts_rel t2 on t2.model_type_id = t1.model_type_id
                    inner join public.parts t5 on t5.id = t2.part_id
                where
                    t2.quantity > 0
                    and t1.model_type_id in :model_type_ids
                    and t5.is_soft_part in :is_soft_part_tuple
                    and t5.flagged_for_deletion = false
                group by
                    t1.model_type_id,
                    t5.part_name -- union all
                    -- -- Graham doesn't need this anymore since these parts are now included in the model_types_parts_rel table above!
                    -- -- Extra preventative maintenance (PM) seal kids needed for each structure model type
                    -- SELECT
                    --     t1.model_type_id,
                    --     t5.part_name,
                    --     'PM Seal Kit' as part_type,
                    --     avg(t5.cost_cad) as cost_cad,
                    --     avg(t5.cost_usd) as cost_usd,
                    --     avg(t5.msrp_cad) as msrp_cad,
                    --     avg(t5.msrp_usd) as msrp_usd,
                    --     sum(t2.quantity * t5.warehouse_mult) as bom_estimate_total
                    -- FROM public.vw_structures_joined_filtered t1
                    -- -- How many PM seal kits are needed for each structure?
                    -- inner join public.model_types_parts_pm_seal_kits_rel t2
                    --     on t2.model_type_id = t1.model_type_id
                    -- inner join public.parts t5
                    --     on t5.id = t2.part_id
                    -- where
                    --     t2.quantity > 0
                    --     and t1.model_type_id in :model_type_ids
                    --     and t5.is_soft_part in :is_soft_part_tuple
                    --     and t5.flagged_for_deletion = false
                    -- group by
                    --     t1.model_type_id,
                    --     t5.part_name
                union
                all -- Extra hydraulic oil parts needed for each power unit type (e.g. litres of hydraulic oil N32)
                SELECT
                    t1.model_type_id,
                    t5.part_name,
                    'Power Unit Hydraulic Oil' as part_type,
                    avg(t5.cost_cad) as cost_cad,
                    avg(t5.cost_usd) as cost_usd,
                    avg(t5.msrp_cad) as msrp_cad,
                    avg(t5.msrp_usd) as msrp_usd,
                    sum(t2.quantity * t5.warehouse_mult) as bom_estimate_total
                FROM
                    public.vw_structures_joined_filtered t1 -- How many hydraulic oil parts are needed for each power unit type?
                    inner join public.power_unit_types_parts_rel t2 on t2.power_unit_type_id = t1.power_unit_type_id
                    inner join public.parts t5 on t5.id = t2.part_id
                where
                    t2.quantity > 0
                    and t1.model_type_id in :model_type_ids
                    and t5.is_soft_part in :is_soft_part_tuple
                    and t5.flagged_for_deletion = false
                group by
                    t1.model_type_id,
                    t5.part_name
                union
                all -- Extra power unit oil filters needed for each power unit type
                SELECT
                    t1.model_type_id,
                    t5.part_name,
                    'Power Unit Oil Filters' as part_type,
                    avg(t5.cost_cad) as cost_cad,
                    avg(t5.cost_usd) as cost_usd,
                    avg(t5.msrp_cad) as msrp_cad,
                    avg(t5.msrp_usd) as msrp_usd,
                    sum(t2.quantity * t5.warehouse_mult) as bom_estimate_total
                FROM
                    public.vw_structures_joined_filtered t1 -- How many oil filters are needed for each power unit type?
                    inner join public.power_unit_types_filters_rel t2 on t2.power_unit_type_id = t1.power_unit_type_id
                    inner join public.parts t5 on t5.id = t2.part_filter_id
                where
                    t2.quantity > 0
                    and t1.model_type_id in :model_type_ids
                    and t5.is_soft_part in :is_soft_part_tuple
                    and t5.flagged_for_deletion = false
                group by
                    t1.model_type_id,
                    t5.part_name
            ) a1
        group by
            a1.model_type_id,
            a1.part_name,
            a1.part_type
    ) b1 -- Count of model types, so we can divide the number of parts estimated
    -- by the number of model types and get the average number of parts estimated by model type.
    left join (
        select
            model_type_id,
            count(*) as model_type_id_count_sql
        from
            public.vw_structures_joined_filtered t1
        where
            t1.model_type_id in :model_type_ids
        group by
            model_type_id
    ) b5 on b1.model_type_id = b5.model_type_id
order by
    num_occurrences_bom desc,
    model_type_id,
    part_name