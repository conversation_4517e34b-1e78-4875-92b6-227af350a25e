from datetime import date, timedelta

import dash_bootstrap_components as dbc
import numpy as np
import pandas as pd
from dash import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
    no_update,
)
from dash.exceptions import PreventUpdate
from flask import current_app, url_for
from flask_login import current_user
from pandas import DataFrame
from shared.models.models import Customer, CustSubGroup, UnitType
from shared.models.models_bom import Part, Warehouse
from shared.models.models_work_order import Maintenance, WorkOrder, WorkOrderPart
from sqlalchemy import and_, func, or_, text

from app import db, user_is_demo_customer, user_is_ijack_employee
from app.config import (
    COUNTRY_ID_CANADA,
    CURRENCY_ID_CAD,
    CURRENCY_ID_USD,
    CUST_SUB_GROUP_ID_ALL_GROUPS,
    CUST_SUB_GROUP_ID_ALL_OTHERS,
    CUSTOMER_ID_ALL_CUSTOMERS,
    CUSTOMER_ID_DEMO,
    CUSTOMER_ID_IJACK_CORP,
    CUSTOMER_ID_IJACK_INC,
    SERVICE_TYPE_ID_PARTS,
    SERVICE_TYPE_ID_PREV_MAINT,
    SERVICE_TYPE_ID_REPAIR,
    TAB_HEALTH,
    UNIT_TYPE_ID_ALL_TYPES,
    WAREHOUSE_ID_ALL_WAREHOUSES,
)
from app.dashapp.layout_utils import get_ag_grid
from app.dashapp.metrics import BOOTSTRAP_BLUE_500, BOOTSTRAP_RED_500
from app.dashapp.utils import (
    discrete_background_color_bins,
    get_db_options,
    get_structures_df_by_filter,
    log_function_caller,
)
from app.databases import run_sql_query
from app.models.models import Structure, User
from app.utils.simple import utcnow_naive


def get_confirmation_modal() -> dbc.Modal:
    """Get the confirmation modal"""
    return dbc.Modal(
        id="health_modal",
        size="lg",
        is_open=False,
        centered=True,
        children=[
            dbc.ModalHeader(id="health_modal_header"),
            dbc.ModalBody(id="health_modal_body"),
            dbc.ModalFooter(
                [
                    dbc.Button("Confirm", id="health_modal_confirm", color="danger"),
                    dbc.Button("Cancel", id="health_modal_cancel", color="secondary"),
                    dbc.Button(
                        "Close",
                        id="health_modal_close",
                        color="primary",
                        style={"display": "none"},
                    ),
                ]
            ),
        ],
    )


def get_health_div():
    """Get the health div"""

    return dbc.Card(
        id="health_div",
        style={"display": "none"},
        class_name="mt-3",
        # # Max width of screen
        # fluid=True,
        children=[
            dbc.CardHeader(
                dbc.Row(
                    [
                        dbc.Col(
                            dbc.Spinner(
                                "Health",
                                id="health_card_title",
                                color="success",
                            ),
                            width="auto",
                        ),
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fa-solid fa-refresh me-1"),
                                    "Refresh",
                                ],
                                id="health_refresh_btn",
                                color="primary",
                                outline=True,
                                size="sm",
                                # line-height 1
                                class_name="lh-1",
                            ),
                            width="auto",
                        ),
                    ],
                    justify="between",
                )
            ),
            dbc.CardBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                xs=12,
                                lg=10,
                                xl=8,
                                xxl=6,
                                # There's already padding of 3rem inside cards, so we don't need to add more
                                class_name="mb-3",
                                children=dbc.ListGroup(
                                    dbc.ListGroupItem(
                                        [
                                            dbc.Label(
                                                "Minimum Operating Months for Analytics Columns",
                                            ),
                                            dbc.Input(
                                                id="health_months_thresh",
                                                type="number",
                                                placeholder=2,
                                                value=0.5,
                                                min=0,
                                                invalid=False,
                                                persistence=True,
                                            ),
                                            dbc.FormText(
                                                "Minimum number of months to consider a unit 'operating', since we're dividing by this number"
                                            ),
                                        ],
                                    )
                                ),
                            ),
                            dbc.Col(
                                xs=12,
                                lg=10,
                                xl=8,
                                xxl=6,
                                # There's already padding of 3rem inside cards, so we don't need to add more
                                class_name="mb-3",
                                children=dbc.ListGroup(
                                    dbc.ListGroupItem(
                                        [
                                            dbc.Label(
                                                "Service Types to Include",
                                            ),
                                            dcc.Dropdown(
                                                id="health_service_types",
                                                value=(
                                                    SERVICE_TYPE_ID_REPAIR,
                                                    SERVICE_TYPE_ID_PARTS,
                                                    SERVICE_TYPE_ID_PREV_MAINT,
                                                ),
                                                multi=True,
                                                persistence=True,
                                            ),
                                            dbc.FormText(
                                                "Which service types to include in the analytics? Typically only repairs, parts, and preventative maintenance."
                                            ),
                                        ],
                                    )
                                ),
                            ),
                        ],
                    ),
                    dbc.Row(
                        dbc.Col(
                            class_name="mb-3",
                            children=[
                                html.Div(
                                    dbc.Button(
                                        "Download CSV",
                                        id="health_download_csv",
                                        color="secondary",
                                        outline=True,
                                        size="sm",
                                    ),
                                    id="health_download_csv_div",
                                    className="mb-3",
                                ),
                                dbc.Spinner(
                                    color="success",
                                    children=[
                                        dcc.Store(id="health_structure_id_store"),
                                        dcc.Store(id="health_view_structure_id_store"),
                                        get_ag_grid(
                                            id="health_ag_grid",
                                            # Set the structure_id as the row id or "value" in the row data
                                            getRowId="params.data.structure_id",
                                            columnSize="autoSize",
                                            # Reduce column widths to fit the width of the container
                                            # columnSize="sizeToFit",
                                            # columnSize="responsiveSizeToFit",
                                            paginationPageSize=10,
                                        ),
                                    ],
                                ),
                            ],
                        ),
                    ),
                ]
            ),
            get_confirmation_modal(),
        ],
    )


def get_structures_view_data(
    structure_ids: list = None, power_unit_ids: list = None, power_units: list = None
) -> DataFrame:
    """
    Get all structures based on either:
        1. the list of structure ids
        2. the list of power unit ids
        3. the list power unit strings
    chosen/filtered
    """
    if not structure_ids and not power_unit_ids and not power_units:
        # return empty DataFrame
        return pd.DataFrame(
            data={
                "structure_id": pd.Series(dtype="float"),
                "power_unit_id": pd.Series(dtype="float"),
                "customer": pd.Series(dtype="object"),
                "unit_type": pd.Series(dtype="object"),
                "model": pd.Series(dtype="object"),
                "cust_sub_group": pd.Series(dtype="object"),
                "surface": pd.Series(dtype="object"),
                "structure_str": pd.Series(dtype="object"),
                "power_unit_str": pd.Series(dtype="object"),
                "power_unit_type": pd.Series(dtype="object"),
                "run_mfg_date": pd.Series(dtype="datetime64[ns]"),
                "slave_install_date": pd.Series(dtype="datetime64[ns]"),
                "structure_install_date": pd.Series(dtype="datetime64[ns]"),
                "op_hours": pd.Series(dtype="float"),
                "op_months_interval": pd.Series(dtype="float"),
                "has_rcom": pd.Series(dtype="bool"),
            }
        )

    sql: str = """
select
    distinct on (structure_id)
    structure_id,
    power_unit_id,
    customer,
    unit_type,
    model,
    cust_sub_group,
    surface,
    structure_str,
    power_unit_str,
    power_unit_type,
    run_mfg_date,
    slave_install_date,
    structure_install_date,
    op_hours,
    op_months_interval,
    --case when unit_type = 'DGAS' then false else has_rcom end as has_rcom
    has_rcom
from public.vw_structures_joined_filtered
where
    customer_id is distinct from :customer_id_demo
    and (
"""
    bind_params: dict = {"customer_id_demo": CUSTOMER_ID_DEMO}
    filters_str: str = ""
    if structure_ids:
        filters_str += " structure_id in :structure_ids"
        bind_params["structure_ids"] = tuple(structure_ids)
    if power_unit_ids:
        if filters_str:
            filters_str += " or"
        filters_str += " power_unit_id in :power_unit_ids"
        bind_params["power_unit_ids"] = tuple(power_unit_ids)
    if power_units:
        if filters_str:
            filters_str += " or"
        filters_str += " power_unit_str in :power_units"
        bind_params["power_units"] = tuple(power_units)

    sql += filters_str + ")"
    # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
    #     current_app.logger.debug(pprint.pprint(sql))
    result, columns = run_sql_query(
        text(sql).bindparams(**bind_params), db_name="ijack", as_dict=True
    )
    df = pd.DataFrame(result, columns=columns)
    # Ensure structure_id is a float, not an object, so the joins will work
    df["structure_id"] = df["structure_id"].astype(float)
    # This second one is for the "quote" button
    df["structure_id2"] = df["structure_id"]
    df["power_unit_id"] = df["power_unit_id"].astype(float)
    df["op_hours"] = df["op_hours"].astype(float)
    df["op_months_interval"] = df["op_months_interval"].astype(float)
    df["run_mfg_date"] = pd.to_datetime(df["run_mfg_date"])
    df["slave_install_date"] = pd.to_datetime(df["slave_install_date"])
    df["structure_install_date"] = pd.to_datetime(df["structure_install_date"])

    return df


def get_pm_seal_kits_msrp(structure_ids: list, want_part_id: bool = False) -> DataFrame:
    """Get the PM Seal Kits MSRP DataFrame by model_type_id"""

    sql = f"""
select
    t1.id as structure_id,
    {"t3.part_id, t3.quantity as pm_seal_kits_quantity," if want_part_id else ""}
    sum(t4.msrp_cad * t3.quantity) as pm_seal_kits_msrp
from public.structures t1
inner join public.model_types t2
    on t1.model_type_id = t2.id
inner join public.model_types_parts_pm_seal_kits_rel t3
    on t2.id = t3.model_type_id
inner join public.parts t4
    on t3.part_id = t4.id
where
    t1.id in :structure_ids
group by
    t1.id
    {", t3.part_id, t3.quantity" if want_part_id else ""}
"""

    bind_params = {
        "structure_ids": tuple(structure_ids),
    }
    result, columns = run_sql_query(
        text(sql).bindparams(**bind_params), db_name="ijack", as_dict=True
    )
    df = pd.DataFrame(result, columns=columns)
    # Ensure structure_id is a float, not an object, so the joins will work
    df["structure_id"] = df["structure_id"].astype(float)

    return df


def get_maintenance_records(structure_ids: list, power_unit_ids: list) -> DataFrame:
    """
    Get the latest 'preventative maintenance reset'
    (last time user clicked the button in 'Control' tab) for the structures
    """

    empty_df = pd.DataFrame(
        data={
            "structure_id": pd.Series(dtype="float"),
            #  "power_unit_id": pd.Series(dtype="float"),
            "last_pm_reset_date": pd.Series(dtype="datetime64[ns]"),
            "op_hours_maint": pd.Series(dtype="float"),
        }
    )

    if not structure_ids and not power_unit_ids:
        return empty_df

    filters: list = []
    if structure_ids:
        filters.append(Maintenance.structure_id.in_(structure_ids))
    if power_unit_ids:
        filters.append(Maintenance.power_unit_id.in_(power_unit_ids))

    subquery = (
        db.session.query(
            Maintenance.structure_id,
            Maintenance.power_unit_id,
            func.max(Maintenance.timestamp_utc).label("max_timestamp"),
        )
        .filter(or_(*filters))
        .group_by(Maintenance.structure_id, Maintenance.power_unit_id)
        .subquery()
    )

    maint_records = (
        db.session.query(Maintenance)
        .join(
            subquery,
            and_(
                or_(*filters),
                Maintenance.timestamp_utc == subquery.c.max_timestamp,
            ),
        )
        .all()
    )

    if not maint_records:
        # No maintenance records, so just return the empty DataFrame
        return empty_df

    maint_records = pd.DataFrame([m.__dict__ for m in maint_records])
    maint_records = maint_records[
        [
            "structure_id",
            #  "power_unit_id",
            "timestamp_utc",
            "op_hours",
        ]
    ].drop_duplicates()
    maint_records = maint_records.rename(
        columns={"timestamp_utc": "last_pm_reset_date", "op_hours": "op_hours_maint"}
    )
    # Ensure structure_id is a float, not an object, so the joins will work
    maint_records["structure_id"] = maint_records["structure_id"].astype(float)

    return maint_records


def get_prev_maint_work_orders(structures_list: list) -> DataFrame:
    """
    Get the preventative maintenance work orders for the structures,
    so we can use these in addition to the maintenance records where someone does a reset in the control tab.
    """

    empty_df = pd.DataFrame(
        data={
            "structure_id": pd.Series(dtype="float"),
            # "power_unit_id": pd.Series(dtype="float"),
            "date_service": pd.Series(dtype="datetime64[ns]"),
            "months_since_hyd_seal_kit": pd.Series(dtype="float"),
        }
    )

    if not structures_list:
        return empty_df

    sql = """
select
    t1.structure_id,
    max(t2.date_service) as date_service
from work_orders_parts t1
inner join work_orders t2
    on t2.id = t1.work_order_id
where 
    t1.structure_id in :structures_tuple
    -- Only completed work orders. Quotes not allowed
    and t2.is_quote = false
    -- 5 = Preventative maintenance
    -- and service_type_id = 5
    and t1.part_id in (
        select id
        from parts
        -- Preventative maintenance auto-reset flag from Bill of Materials master spreadsheet
        where is_soft_part = true
    )
group by
    t1.structure_id
"""

    bindparams = {"structures_tuple": tuple(structures_list)}
    sql = text(sql).bindparams(**bindparams)
    rows, columns = run_sql_query(sql, db_name="ijack")
    if not rows:
        return empty_df

    # Build a DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)

    # Ensure structure_id is a float, not an object, so the joins will work
    df["structure_id"] = df["structure_id"].astype(float)

    # Convert last maintenance date to datetime
    df["date_service"] = pd.to_datetime(df["date_service"])

    # Second query just to get the dates where "hyd seal kit" was used
    sql = """
select
    t1.structure_id,
    max(t2.date_service) as date_hyd_seal_kit
from work_orders_parts t1
inner join work_orders t2
    on t2.id = t1.work_order_id
where
    t1.structure_id in :structures_tuple
    -- Only completed work orders. Quotes not allowed
    and t2.is_quote = false
    -- 5 = Preventative maintenance
    -- and service_type_id = 5
    and t1.part_id in (
        select id
        from parts
        where 
            -- All of these words must be in the description,
            -- but the order doesn't matter, there can be other words in between,
            -- and the words can be in different cases.
            description ilike '%hyd%' 
            and description ilike '%seal%' 
            and description ilike '%kit%'
    )
group by
    t1.structure_id
"""

    bindparams = {"structures_tuple": tuple(structures_list)}
    sql = text(sql).bindparams(**bindparams)
    rows, columns = run_sql_query(sql, db_name="ijack")

    if not rows:
        # df["date_hyd_seal_kit"] = None
        df["months_since_hyd_seal_kit"] = None
    else:
        # Build a DataFrame with the results
        df2 = pd.DataFrame(rows, columns=columns)
        # Ensure structure_id is a float, not an object, so the joins will work
        df2["structure_id"] = df2["structure_id"].astype(float)
        # df2["power_unit_id"] = df2["power_unit_id"].astype(float)
        # Convert last maintenance date to datetime
        df2["date_hyd_seal_kit"] = pd.to_datetime(df2["date_hyd_seal_kit"])
        df2["months_since_hyd_seal_kit"] = np.where(
            df2["date_hyd_seal_kit"].isna(),
            None,
            # Allowed units are 'W', 'D', 'h', 'm', 's', 'ms', 'us', 'ns'
            (utcnow_naive() - df2["date_hyd_seal_kit"]) / np.timedelta64(4, "W"),  # type: ignore
        ).astype(np.float64)
        # Drop the date field
        df2 = df2.drop(columns=["date_hyd_seal_kit"])
        # Merge the two dataframes
        df = pd.merge(
            left=df,
            right=df2,
            # left_on=("structure_id", "power_unit_id"),
            # right_on=("structure_id", "power_unit_id"),
            on="structure_id",
            how="left",
        )

    return df


def get_service_records(structures_list: list, service_types: list) -> DataFrame:
    """Get the latest service records for the structures"""

    empty_df = pd.DataFrame(
        data={
            "structure_id": pd.Series(dtype="float"),
            "work_order_ids": pd.Series(dtype="object"),
            "service_visits": pd.Series(dtype="float"),
            "subtotal": pd.Series(dtype="float"),
            "parts_cost": pd.Series(dtype="float"),
        }
    )

    if not structures_list or not service_types:
        return empty_df

    sql = """
select
    a1.structure_id,
    STRING_AGG(
        DISTINCT
        CASE
            WHEN t2.creator_company_id = :cust_id_ijack_inc  THEN '<a target="_blank" href="/admin/work_orders/?flt1_work_order_id_equals='      || a1.work_order_id || '">' || a1.work_order_id || '</a>'
            WHEN t2.creator_company_id = :cust_id_ijack_corp THEN '<a target="_blank" href="/admin/work_orders_corp/?flt1_work_order_id_equals=' || a1.work_order_id || '">' || a1.work_order_id || '</a>'
            ELSE ''
        END,
        ', '
    ) as work_order_ids,    
    count(distinct a1.work_order_id) as service_visits,
    sum(t2.subtotal) as subtotal,
    sum(a1.parts_cost) as parts_cost
from (
    --This inner query sums up the parts line items
    --by work order, structure, and power unit,
    --so the outer work orders query doesn't have duplicates for each line item.
    select
        t1.work_order_id,
        t1.structure_id,
        --t1.cost_before_tax as parts_cost
        sum(t1.cost_before_tax) as parts_cost
    from public.work_orders_parts t1
    where t1.structure_id in :structures_tuple
    group by
        t1.work_order_id,
        t1.structure_id
) a1
--Get some more information for each work order found in the parts subquery above.
--t1.work_order_id is never null.
inner join work_orders t2
    on a1.work_order_id = t2.id
--We want to filter on service_type_id.
--t2.service_type_id is never null
inner join public.service_types t3
    on t3.id = t2.service_type_id
--t2.customer_id is sometimes null but we'll ignore those
--since we filter on structures and power units in the subquery
where t2.customer_id is distinct from :customer_id_demo
    and t2.service_type_id in :service_type_ids
    -- Only completed work orders. Quotes not allowed
    and t2.is_quote = false
group by
    a1.structure_id
order by
    a1.structure_id
"""

    bindparams = {
        "customer_id_demo": CUSTOMER_ID_DEMO,
        "cust_id_ijack_inc": CUSTOMER_ID_IJACK_INC,
        "cust_id_ijack_corp": CUSTOMER_ID_IJACK_CORP,
        "service_type_ids": tuple(service_types),
        "structures_tuple": tuple(structures_list),
    }

    sql = text(sql).bindparams(**bindparams)
    rows, columns = run_sql_query(sql, db_name="ijack")
    if not rows:
        return empty_df

    # Build a DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)
    # Ensure structure_id is a float, not an object, so the joins will work
    df["structure_id"] = df["structure_id"].astype(float)
    df["service_visits"] = df["service_visits"].astype(float)
    df["subtotal"] = df["subtotal"].astype(float)
    df["parts_cost"] = df["parts_cost"].astype(float)

    return df


def get_health_df(
    customer_id: int,
    warehouse_id: int,
    cust_sub_group_id: int,
    unit_type_id: int,
    months_thresh: float = 0.5,
    service_types: list = None,
) -> DataFrame:
    """Get the health DataFrame for the Dash table or CSV download"""

    if months_thresh in (None, "") or (
        isinstance(months_thresh, (int, float)) and months_thresh < 0
    ):
        months_thresh = 0.0

    if not service_types or not isinstance(service_types, (list, tuple)):
        service_types = (
            SERVICE_TYPE_ID_PREV_MAINT,
            SERVICE_TYPE_ID_REPAIR,
            SERVICE_TYPE_ID_PARTS,
        )

    power_units_df: DataFrame = get_structures_df_by_filter(
        customer_id=customer_id,
        warehouse_id=warehouse_id,
        cust_sub_group_id=cust_sub_group_id,
        unit_type_id=unit_type_id,
    )
    power_units_list: list = power_units_df.power_unit_id.unique().tolist()
    structures_list: list = power_units_df.id.unique().tolist()

    empty_df = pd.DataFrame(
        data={
            "structure_id": pd.Series(dtype="float"),
            "power_unit_id": pd.Series(dtype="float"),
            "customer": pd.Series(dtype="object"),
            "unit_type": pd.Series(dtype="object"),
            "model": pd.Series(dtype="object"),
            "cust_sub_group": pd.Series(dtype="object"),
            "surface": pd.Series(dtype="object"),
            "structure_str": pd.Series(dtype="object"),
            "power_unit_str": pd.Series(dtype="object"),
            "power_unit_type": pd.Series(dtype="object"),
            "run_mfg_date": pd.Series(dtype="datetime64[ns]"),
            "slave_install_date": pd.Series(dtype="datetime64[ns]"),
            "structure_install_date": pd.Series(dtype="datetime64[ns]"),
            "op_hours": pd.Series(dtype="float"),
            "op_months_interval": pd.Series(dtype="float"),
            "has_rcom": pd.Series(dtype="bool"),
            "pinned_col": pd.Series(dtype="object"),
            "startup_date": pd.Series(dtype="object"),
            "op_months": pd.Series(dtype="float"),
            "last_pm_reset_date": pd.Series(dtype="datetime64[ns]"),
            "op_hours_maint": pd.Series(dtype="float"),
            "last_pm_reset_op_months": pd.Series(dtype="float"),
            "date_service": pd.Series(dtype="datetime64[ns]"),
            "months_since_hyd_seal_kit": pd.Series(dtype="float"),
            "last_maint_date": pd.Series(dtype="object"),
            "months_since_last_maint": pd.Series(dtype="float"),
            "op_months_since_last_maint": pd.Series(dtype="float"),
            "last_maint_op_months": pd.Series(dtype="float"),
            "last_hyd_seal_kit_op_months": pd.Series(dtype="float"),
            "op_months_remaining": pd.Series(dtype="float"),
            "work_order_ids": pd.Series(dtype="object"),
            "service_visits": pd.Series(dtype="float"),
            "subtotal": pd.Series(dtype="float"),
            "parts_cost": pd.Series(dtype="float"),
            "service_visits_per_op_month": pd.Series(dtype="object"),
            "subtotal_per_op_month": pd.Series(dtype="object"),
            "parts_cost_per_op_month": pd.Series(dtype="object"),
            "pm_seal_kits_msrp": pd.Series(dtype="float"),
        }
    )
    if not structures_list:
        return empty_df

    structures_df: DataFrame = get_structures_view_data(
        structure_ids=structures_list, power_unit_ids=power_units_list
    ).copy(deep=True)
    if structures_df.empty:
        return empty_df

    user_id: int = getattr(current_user, "id", None)
    if user_is_demo_customer(user_id=user_id):
        # For demo customers, don't show the location
        structures_df["surface"] = "01-01-01-01W1"

    # Combine power unit and surface columns so we can pin them on the left side of the table.
    # If there's no power unit, then use the structure_str and surface columns
    structures_df["pinned_col"] = np.where(
        structures_df["power_unit_str"].isna(),
        structures_df["structure_str"] + " - " + structures_df["surface"],
        structures_df["power_unit_str"] + " - " + structures_df["surface"],
    )

    # If the structure_install_date is null, use the slave_install_date (e.g. UNOGAS)
    structures_df["startup_date"] = np.where(
        structures_df["structure_install_date"].isna(),
        structures_df["slave_install_date"],
        structures_df["structure_install_date"],
    )
    # If the startup_date is still null, use the run_mfg_date
    structures_df["startup_date"] = np.where(
        structures_df["startup_date"].isna(),
        structures_df["run_mfg_date"],
        structures_df["startup_date"],
    )
    structures_df["startup_date"] = pd.to_datetime(structures_df["startup_date"])

    def calc_months_since_startup_date(row):
        """
        Calculate the months since the startup date, for units that have no operating hours.

        NOTE: The following / np.timedelta64(4, "W") calculation doesn't work as well:
        Allowed units are 'W', 'D', 'h', 'm', 's', 'ms', 'us', 'ns'
        (utcnow_naive() - structures_df["startup_date"]) / np.timedelta64(4, "W")
        """
        return (utcnow_naive() - row["startup_date"]).days / 30.4375

    # Get current operating months (i.e. how many months has it been running)
    structures_df["op_months"] = np.where(
        # If the operating hours are null or zero, or the unit doesn't have RCOM, then use the startup date
        structures_df["op_hours"].isna()
        | structures_df["op_hours"].eq(0)
        | structures_df["has_rcom"].eq(False),
        structures_df.apply(calc_months_since_startup_date, axis=1),
        # Otherwise, calculate the operating months based on the operating hours
        structures_df["op_hours"] / (24 * 30.4375),
    )

    # latest 'preventative maintenance reset' button push in 'Control' tab
    maint_records: DataFrame = get_maintenance_records(
        structure_ids=structures_list, power_unit_ids=power_units_list
    ).copy(deep=True)

    # Merge the dataframes
    df = pd.merge(
        left=structures_df,
        right=maint_records,
        on="structure_id",
        how="left",
    )

    # Convert last preventative maintenance reset button-push date to datetime
    df["last_pm_reset_date"] = pd.to_datetime(df["last_pm_reset_date"])

    # The operating months when the user last pressed the
    # preventative maintenance reset button in the control tab
    df["last_pm_reset_op_months"] = np.where(
        df["op_hours_maint"].isna(), 0, df["op_hours_maint"] / (24 * 30.4375)
    )

    # Get preventative maintenance records from work orders and use them too,
    # in addition to the maintenance records where someone does a reset in the control tab.
    work_orders_w_pm_reset_flags: pd.DataFrame = get_prev_maint_work_orders(
        structures_list=structures_list
    )

    df = pd.merge(
        left=df,
        right=work_orders_w_pm_reset_flags,
        on="structure_id",
        how="left",
    )

    # Update the 'last_maint_date' to be the greater of 'last_pm_reset_date' and 'date_service'
    # from the work orders with preventative maintenance.
    df["last_maint_date"] = np.where(
        df["last_pm_reset_date"].isna(),
        df["date_service"],
        np.where(
            df["date_service"].isna(),
            df["last_pm_reset_date"],
            np.where(
                df["last_pm_reset_date"] > df["date_service"],
                df["last_pm_reset_date"],
                df["date_service"],
            ),
        ),
    )
    # Ensure last_maint_date is a datetime
    df["last_maint_date"] = pd.to_datetime(df["last_maint_date"])

    # If the last maintenance date is null, use the startup date
    df["last_maint_date"] = np.where(
        df["last_maint_date"].isna(),
        df["startup_date"],
        df["last_maint_date"],
    )

    # Months since last maintenance
    df["months_since_last_maint"] = np.where(
        df["last_maint_date"].isna(),
        None,
        # Allowed units are 'W', 'D', 'h', 'm', 's', 'ms', 'us', 'ns'
        (utcnow_naive() - df["last_maint_date"]) / np.timedelta64(4, "W"),  # type: ignore
    ).astype(np.float64)

    # Operating months since last maintenance (last_pm_reset_op_months)
    # If there are no operating months, use the months since last maintenance date
    # and just assume it's been running flat out since the last maintenance
    df["op_months_since_last_maint"] = np.where(
        df["op_months"].isna() | df["last_pm_reset_op_months"].isna(),
        df["months_since_last_maint"],
        df["op_months"] - df["last_pm_reset_op_months"],
    ).astype(np.float64)

    # If months since last maintenance (work orders) is less than (i.e. more recent than)
    # op months since last maintenance (maintenance reset records), then use the months
    # since last maintenance. This way we're not overly relying on preventative maintenance
    # reset records, which is where the operating months are stored.
    df["op_months_since_last_maint"] = np.where(
        df["months_since_last_maint"] < df["op_months_since_last_maint"],
        df["months_since_last_maint"],
        df["op_months_since_last_maint"],
    )

    # Adjust the last_pm_reset_op_months to take into account the months since last maintenance
    df["last_maint_op_months"] = np.where(
        df["op_months_since_last_maint"].isna(),
        df["last_pm_reset_op_months"],
        df["op_months"] - df["op_months_since_last_maint"],
    )

    # Calculate the last operating months when the last hyd seal kit was used
    df["last_hyd_seal_kit_op_months"] = (
        np.where(
            # If the months since the last hyd seal kit was used is null,
            # or the months since the last hyd seal kit was used is null,
            # then use 0.0
            df["months_since_hyd_seal_kit"].isna() | df["op_months"].isna(),
            0.0,
            # Maximum of zero or the difference between the current operating months
            # and the months since the last hyd seal kit was used
            np.maximum(0, df["op_months"] - df["months_since_hyd_seal_kit"]),
        )
        .astype(np.float64)
        .round(1)
    )

    # Calculate the months remaining until the next preventative maintenance
    # NOTE op_months_interval is never null
    df["op_months_remaining"] = np.where(
        df["op_months_since_last_maint"].isna(),
        None,
        df["op_months_interval"] - df["op_months_since_last_maint"],
    )

    # Convert to floats so we can round them
    df["op_months"] = df["op_months"].astype(np.float64)
    df["op_months_remaining"] = df["op_months_remaining"].astype(np.float64)
    # Convert dates to strings for easy reading
    df["startup_date"] = pd.to_datetime(df["startup_date"]).dt.strftime("%Y-%m-%d")
    df["last_maint_date"] = pd.to_datetime(df["last_maint_date"]).dt.strftime(
        "%Y-%m-%d"
    )

    # Get the work orders summed/counted by structure_id
    service_records: DataFrame = get_service_records(
        structures_list=structures_list,
        service_types=service_types,
    ).copy(deep=True)

    df = pd.merge(
        left=df,
        right=service_records,
        on="structure_id",
        how="left",
    )

    # Replace nulls in the work_order_ids column with empty strings
    # Must do this after the merge. Otherwise there will be no nulls to replace
    df["work_order_ids"] = df["work_order_ids"].fillna("")

    # Convert to floats so we can do math on them
    df["op_months"] = pd.to_numeric(df["op_months"], errors="coerce")
    df["service_visits"] = pd.to_numeric(df["service_visits"], errors="coerce")
    # df["service_hours"] = pd.to_numeric(df["service_hours"], errors="coerce")
    # df["travel_hours"] = pd.to_numeric(df["travel_hours"], errors="coerce")
    df["subtotal"] = pd.to_numeric(df["subtotal"], errors="coerce")
    df["parts_cost"] = pd.to_numeric(df["parts_cost"], errors="coerce")

    df["service_visits_per_op_month"] = np.where(
        (
            df["op_months"].isna()
            | df["service_visits"].isna()
            | (df["op_months"] < months_thresh)
        ),
        None,
        df["service_visits"] / df["op_months"],
    )

    # df["service_hours_per_op_month"] = np.where(
    #     (
    #         df["op_months"].isna()
    #         | df["service_hours"].isna()
    #         | (df["op_months"] < months_thresh)
    #     ),
    #     None,
    #     df["service_hours"] / df["op_months"],
    # )

    # df["travel_hours_per_op_month"] = np.where(
    #     (
    #         df["op_months"].isna()
    #         | df["travel_hours"].isna()
    #         | (df["op_months"] < months_thresh)
    #     ),
    #     None,
    #     df["travel_hours"] / df["op_months"],
    # )

    df["subtotal_per_op_month"] = np.where(
        (
            df["op_months"].isna()
            | df["subtotal"].isna()
            | (df["op_months"] < months_thresh)
        ),
        None,
        df["subtotal"] / df["op_months"],
    )

    df["parts_cost_per_op_month"] = np.where(
        (
            df["op_months"].isna()
            | df["parts_cost"].isna()
            | (df["op_months"] < months_thresh)
        ),
        None,
        df["parts_cost"] / df["op_months"],
    )

    # Add a column for the PM Seal Kits MSRP total cost
    df_seals: pd.DataFrame = get_pm_seal_kits_msrp(
        structure_ids=structures_list, want_part_id=False
    )
    df = pd.merge(
        left=df,
        right=df_seals,
        on="structure_id",
        how="left",
    )
    # Replace nulls with 0
    df["pm_seal_kits_msrp"] = df["pm_seal_kits_msrp"].astype(np.float64)
    df["pm_seal_kits_msrp"] = df["pm_seal_kits_msrp"].fillna(0)

    # df["work_order_ids"] = df.apply(get_work_order_link, axis=1)

    # df = df.sort_values(by=["subtotal_per_op_month"], ascending=False)

    # Round numeric values to 1 decimal points (REAL in Postgres is 4 bytes np.float64)
    df = df.round(1)

    # # Merge the two dataframes
    # df = pd.merge(
    #     left=df,
    #     right=power_units_df,
    #     how="left",
    #     left_on="power_unit",
    #     right_on="power_unit_str",
    # )
    # Sort by customer, then by unit type
    df = df.sort_values(by=["customer", "unit_type", "model", "op_months_remaining"])

    return df


def get_health_columns(df: DataFrame) -> list:
    """Get the columns for the health table"""

    columns = [
        # First column with a custom button renderer
        {
            "field": "structure_id",  # Field name for the column
            "headerName": "Select",  # Header for the button column
            "cellRenderer": "ButtonDBC",  # Use custom button renderer
            "cellRendererParams": {"color": "primary", "label": "View"},
            "minWidth": 70,  # Lower minimum width for button column
            "width": 70,  # Fixed width for button column
            # "maxWidth": 80,  # Add this to enforce maximum width
            "suppressSizeToFit": True,  # Prevents auto-sizing from affecting this column
            "flex": 0,  # Very important - prevents column from flexing/growing
            "sortable": False,  # Disable sorting for button column
            "filter": False,  # Disable filtering for button column
            "pinned": "left",  # Pin button column to the left
        },
        {
            "field": "pinned_col",
            "headerName": "Location",
            "headerTooltip": "The power unit and surface location of the unit",
            "pinned": "left",
            "cellDataType": "text",
            "cellRenderer": "Tooltip",
            "initialWidth": 200,
            "maxWidth": 200,
        },
        {
            "field": "unit_type",
            "headerName": "Unit Type",
            "cellDataType": "text",
            "cellRenderer": "Tooltip",
            "initialWidth": 140,
        },
        {
            "field": "model",
            "headerName": "Model",
            "cellDataType": "text",
            "cellRenderer": "Tooltip",
            "initialWidth": 150,
        },
        {
            "field": "cust_sub_group",
            "headerName": "Sub Group",
            "cellDataType": "text",
            "cellRenderer": "Tooltip",
            "initialWidth": 150,
        },
        # {
        #     "field": "surface",
        #     "headerName": "Surface",
        #     "cellDataType": "text",
        #     "cellRenderer": "Tooltip",
        #     "initialWidth": 150,
        # },
        {
            "field": "power_unit_type",
            "headerName": "Power Unit Type",
            "cellDataType": "text",
            "initialWidth": 275,
            "maxWidth": 275,
            "cellRenderer": "Tooltip",
        },
        {
            "field": "startup_date",
            "headerName": "Startup Date",
            "cellDataType": "dateString",
            "cellRenderer": "Tooltip",
            "initialWidth": 150,
        },
        {
            "field": "last_maint_date",
            "headerName": "Last Maint Date",
            "headerTooltip": "The most recent date on which either a work order was completed, or the preventative maintenance reset button was pressed in the 'Control' tab",
            "cellDataType": "dateString",
            "cellRenderer": "Tooltip",
        },
        {
            "field": "op_months",
            "headerName": "Op Months",
            "headerTooltip": "The number of months the unit has been running (does NOT include time when the unit was off)",
            "cellDataType": "number",
            "cellRenderer": "Tooltip",
        },
        {
            "field": "last_maint_op_months",
            "headerName": "Last Maint Op Months",
            "headerTooltip": "The operating months when maintenance was last performed (either when a work order was completed, or when the preventative maintenance reset button was pressed in the 'Control' tab)",
            "cellDataType": "number",
            "cellRenderer": "Tooltip",
        },
        {
            "field": "op_months_interval",
            "headerName": "Preventative Maintenance Months Interval",
            "headerTooltip": "The recommended number of months between preventative maintenance intervals",
            "cellDataType": "number",
            "cellRenderer": "Tooltip",
            "minWidth": 120,  # a bit wider to prevent wrapping
        },
        {
            "field": "op_months_remaining",
            "headerName": "Months Remaining",
            "headerTooltip": "The number of months remaining until the next preventative maintenance is due",
            "cellDataType": "number",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["op_months_remaining"],
                    reverse=True,
                    color_start=BOOTSTRAP_RED_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "service_visits_per_op_month",
            "headerName": "Service Visits per Op Month",
            "headerTooltip": "The average number of service visits per operating/running month",
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter1",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["service_visits_per_op_month"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        # {
        #     "field": "service_hours_per_op_month",
        #     "headerName": "Service Hours per Op Month",
        #     "headerTooltip": "The average number of service hours per operating/running month",
        #     "cellDataType": "number",
        #     "cellRenderer": "NumberFormatter1",
        #     "cellStyle": {
        #         "styleConditions": discrete_background_color_bins(
        #             df=df,
        #             n_bins=9,
        #             columns=["service_hours_per_op_month"],
        #             reverse=False,
        #             color_start=BOOTSTRAP_BLUE_500,
        #             is_ag_grid=True,
        #         )
        #     },
        # },
        # {
        #     "field": "travel_hours_per_op_month",
        #     "headerName": "Travel Hours per Op Month",
        #     "headerTooltip": "The average number of travel hours per operating/running month",
        #     "cellDataType": "number",
        #     "cellRenderer": "NumberFormatter1",
        #     "cellStyle": {
        #         "styleConditions": discrete_background_color_bins(
        #             df=df,
        #             n_bins=9,
        #             columns=["travel_hours_per_op_month"],
        #             reverse=False,
        #             color_start=BOOTSTRAP_BLUE_500,
        #             is_ag_grid=True,
        #         )
        #     },
        # },
        {
            "field": "subtotal_per_op_month",
            "headerName": "Service Cost per Op Month",
            "headerTooltip": "The average service cost per operating/running month",
            "cellDataType": "number",
            "cellRenderer": "CurrencyFormatter",
            "cellRendererParams": {"decimals": 0},
            "initialWidth": 200,
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["subtotal_per_op_month"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "parts_cost_per_op_month",
            "headerName": "Parts Cost per Op Month",
            "headerTooltip": "The average parts cost per operating/running month",
            "cellDataType": "number",
            "cellRenderer": "CurrencyFormatter",
            "cellRendererParams": {"decimals": 0},
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["parts_cost_per_op_month"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "pm_seal_kits_msrp",
            "headerName": "PM Seals MSRP",
            "headerTooltip": "The total cost of preventative maintenance seal kits",
            "cellDataType": "number",
            "cellRenderer": "CurrencyFormatter",
            "cellRendererParams": {"decimals": 0},
        },
    ]
    user_id: int = getattr(current_user, "id", None)
    if user_is_ijack_employee(user_id=user_id):
        # Insert at the start of the dictionary
        columns.insert(
            0,
            {
                "headerName": "Customer",
                "headerTooltip": "The customer who owns the unit (IJACK-only column)",
                "field": "customer",
                "cellDataType": "text",
                "cellRenderer": "Tooltip",
                "width": 150,
            },
        )
        # Dan doesn't want non-IJACK users to see the last_hyd_seal_kit_op_months column
        columns.insert(
            10,
            {
                "field": "last_hyd_seal_kit_op_months",
                "headerName": "Last Hyd Seal Kit Op Months",
                "headerTooltip": "The operating months when the last hyd seal kit was used (IJACK-only column)",
                "cellDataType": "number",
                "cellRenderer": "Tooltip",
            },
        )
        # Customers shouldn't be able to see work order links
        columns.append(
            {
                "field": "work_order_ids",
                "headerName": "Work Order IDs",
                "headerTooltip": "The work orders recorded for this unit (IJACK-only column)",
                # Render the HTML exactly as it's given to the component
                "cellRenderer": "htmlRenderer",
            },
        )
        # A custom button to add a work order quote for this unit
        columns.append(
            # First column with a custom button renderer
            {
                "field": "structure_id2",  # Field name for the column
                "headerName": "Make Quote",  # Header for the button column
                "headerTooltip": "Make a work order quote for this unit (IJACK-only column)",
                "cellRenderer": "ButtonDBC",  # Use custom button renderer
                "cellRendererParams": {"color": "primary", "label": "Quote"},
                "width": 100,  # Fixed width for button column
                "sortable": False,  # Disable sorting for button column
                "filter": False,  # Disable filtering for button column
                # "pinned": "left",  # Pin button column to the left
            },
        )
        # A custom switch to toggle the RCOM status
        columns.append(
            {
                "field": "has_rcom",  # Field name for the column
                "headerName": "RCOM",  # Header for the switch column
                "headerTooltip": "Remote Communications (RCOM) status - click to toggle",
                "cellRenderer": "SwitchDBC",  # Use custom switch renderer
                "cellRendererParams": {"color": "success"},
                "width": 80,  # Fixed width for switch column
                "suppressSizeToFit": True,  # Prevents auto-sizing from affecting this column
                "flex": 0,  # Very important - prevents column from flexing/growing
                "sortable": True,  # Enable sorting by RCOM status
                "filter": True,  # Enable filtering by RCOM status
                # "pinned": "left",  # Pin switch column to the left
            }
        )

    return columns


# This is just for validating the "Minimum Operating Months Threshold"
# input field (no negative numbers allowed)
clientside_callback(
    """
    function validateHealthMonthsThresh(months_thresh) {
        if (months_thresh === null || months_thresh === "" || (typeof months_thresh === "number" && months_thresh < 0)) {
            return true;  // Trigger invalid state
        }
        return false;  // Valid state
    }
    """,
    Output("health_months_thresh", "invalid"),
    Input("health_months_thresh", "value"),
    prevent_initial_call=True,
)


@callback(
    Output("health_ag_grid", "exportDataAsCsv"),
    Input("health_download_csv", "n_clicks"),
    prevent_initial_call=True,
)
def export_data_as_csv(n_clicks):
    """Export the data as CSV"""
    if n_clicks:
        return True
    return False


@callback(
    Output("health_service_types", "options"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    prevent_initial_call=True,
)
def update_health_service_types(
    active_tab,
):
    """Update the service types dropdown"""
    log_function_caller()

    if active_tab != TAB_HEALTH:
        raise PreventUpdate()

    options: list = get_db_options(
        columns=["name"],
        table="service_types",
    )

    return options


@callback(
    Output("health_ag_grid", "rowData", allow_duplicate=True),
    Output("health_ag_grid", "columnDefs"),
    Output("health_card_title", "children"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("health_refresh_btn", "n_clicks"),
    Input("customer_radio", "value"),
    Input("warehouse_radio", "value"),
    Input("cust_sub_groups_radio", "value"),
    Input("unit_type_ids_radio", "value"),
    Input("health_service_types", "value"),
    Input("health_months_thresh", "n_blur"),
    State("health_months_thresh", "value"),
    # Input("store_url_search", "data"),
    # State("use_url_search", "data"),
    # Ensure this callback always fires on page load (from url.search prop updating).
    # The only time we don't want it to fire is if the map tab is active.
    prevent_initial_call=True,
)
def make_health_div(
    active_tab,
    health_refresh_btn_n_clicks,
    customer_radio_value,
    warehouse_radio_value,
    cust_sub_groups_radio_value,
    unit_type_ids_radio_value,
    health_service_types_value,
    health_months_thresh_n_blur,
    health_months_thresh_value,
    # store_url_search_data,
    # use_url_search_data,
):
    """Make filter for customers (the first filter)"""
    log_function_caller()

    if (
        active_tab != TAB_HEALTH
        or customer_radio_value is None
        or warehouse_radio_value is None
        or cust_sub_groups_radio_value is None
        or unit_type_ids_radio_value is None
    ):
        raise PreventUpdate()

    def return_vars(
        health_ag_grid_rowData: list,
        health_ag_grid_columnDefs: list,
        health_card_title_children: str,
    ) -> tuple:
        """Default return vars"""
        return (
            health_ag_grid_rowData,
            health_ag_grid_columnDefs,
            health_card_title_children,
        )

    df: pd.DataFrame = get_health_df(
        customer_id=customer_radio_value,
        warehouse_id=warehouse_radio_value,
        cust_sub_group_id=cust_sub_groups_radio_value,
        unit_type_id=unit_type_ids_radio_value,
        months_thresh=health_months_thresh_value,
        service_types=health_service_types_value,
    )

    # Make helpful links, after sorting
    # df["pinned_col"] = df["pinned_col"].transform(
    #     lambda col: html.A(col, href=url_for("dash.home", power_unit=col))
    # )

    # def google_maps_link(row: Series) -> Series:
    #     """Make a link to google maps"""
    #     if (
    #         pd.isna(row.gps_lat)
    #         or pd.isna(row.gps_lon)
    #         or pd.isnull(row.gps_lat)
    #         or pd.isnull(row.gps_lon)
    #     ):
    #         thing = row["surface"]
    #     else:
    #         thing = html.A(
    #             row["surface"],
    #             href=f"https://www.google.com/maps/search/?api=1&query={row.gps_lat},{row.gps_lon}",
    #             target="_blank",
    #         )
    #     return thing

    rowData = df.to_dict("records")
    columnDefs = get_health_columns(df)

    if customer_radio_value == CUSTOMER_ID_ALL_CUSTOMERS:
        customer_str = "All Customers"
    else:
        customer = db.session.get(Customer, customer_radio_value)
        if hasattr(customer, "customer"):
            customer_str = customer.customer
        else:
            customer_str = ""

    user_id: int = getattr(current_user, "id", None)
    if user_is_ijack_employee(user_id=user_id):
        # Only IJACK employees can filter on warehouse
        if warehouse_radio_value == WAREHOUSE_ID_ALL_WAREHOUSES:
            warehouse_str = "All Warehouses, "
        else:
            warehouse = db.session.get(Warehouse, warehouse_radio_value)
            warehouse_str = (
                f"{getattr(warehouse, 'name', 'No Warehouse Name, ')} Warehouse, "
            )
    else:
        warehouse_str = ""

    if cust_sub_groups_radio_value == CUST_SUB_GROUP_ID_ALL_GROUPS:
        cust_sub_group_str = "All Groups"
    elif cust_sub_groups_radio_value == CUST_SUB_GROUP_ID_ALL_OTHERS:
        cust_sub_group_str = "All Others Group"
    else:
        cust_sub_group = db.session.get(CustSubGroup, cust_sub_groups_radio_value)
        cust_sub_group_str = f"{getattr(cust_sub_group, 'name', 'No Group Name')} Group"

    if unit_type_ids_radio_value == UNIT_TYPE_ID_ALL_TYPES:
        unit_type_str = "All Unit Types"
    else:
        unit_type = db.session.get(UnitType, unit_type_ids_radio_value)
        unit_type_str = (
            f"{getattr(unit_type, 'unit_type', 'No unit type found - ')} Types"
        )

    health_card_title_children = f"Health Data for {customer_str}, {warehouse_str}{cust_sub_group_str}, {unit_type_str}"

    return return_vars(
        health_ag_grid_rowData=rowData,
        health_ag_grid_columnDefs=columnDefs,
        health_card_title_children=health_card_title_children,
    )


@callback(
    Output("health_modal", "is_open", allow_duplicate=True),
    Input("health_modal_cancel", "n_clicks"),
    Input("health_modal_close", "n_clicks"),
    prevent_initial_call=True,
)
def close_health_modal(cancel_n_clicks, close_n_clicks):
    """Close the health modal"""
    if cancel_n_clicks or close_n_clicks:
        return False
    raise PreventUpdate


@callback(
    Output("health_modal", "is_open", allow_duplicate=True),
    Output("health_modal_header", "children", allow_duplicate=True),
    Output("health_modal_body", "children", allow_duplicate=True),
    Output("health_modal_confirm", "style", allow_duplicate=True),
    Output("health_modal_cancel", "style", allow_duplicate=True),
    Output("health_modal_close", "style", allow_duplicate=True),
    Input("health_modal_confirm", "n_clicks"),
    State("health_structure_id_store", "data"),
    prevent_initial_call=True,
)
def confirm_health_modal(confirm_n_clicks, structure_id):
    """Confirm the health modal"""
    if not confirm_n_clicks:
        raise PreventUpdate

    def return_vars(
        health_modal_is_open: bool,
        health_modal_header_children: str,
        health_modal_body_children: str,
        health_modal_confirm_style: dict = {"display": "none"},
        health_modal_cancel_style: dict = {"display": "none"},
        health_modal_close_style: dict = {},
    ) -> tuple:
        """Default return vars"""
        return (
            health_modal_is_open,
            health_modal_header_children,
            health_modal_body_children,
            health_modal_confirm_style,
            health_modal_cancel_style,
            health_modal_close_style,
        )

    structure_obj: Structure | None = db.session.get(Structure, structure_id)
    if not structure_obj:
        return return_vars(
            health_modal_is_open=True,
            health_modal_header_children="Error",
            health_modal_body_children=f"No structure found with ID {structure_id}",
        )

    try:
        user_id: int = getattr(current_user, "id", None)
        is_canadian: bool = structure_obj.warehouse_rel.country_id == COUNTRY_ID_CANADA
        first_maintenance_user: User | None = None
        if structure_obj.maintenance_users_rel:
            # Users responsible for maintenance
            first_maintenance_user = structure_obj.maintenance_users_rel[0]
        elif structure_obj.sales_users_rel:
            # Users responsible for sales
            first_maintenance_user = structure_obj.sales_users_rel[0]
        elif structure_obj.users_rel:
            # Users with remote control permissions
            first_maintenance_user = structure_obj.users_rel[0]

        user_who_created_quote: User | None = db.session.get(User, user_id)

        work_order = WorkOrder(
            is_quote=True,
            service_required="Preventative maintenance",
            structures_rel=[structure_obj] if structure_obj else [],
            power_units_rel=[structure_obj.power_units_rel]
            if structure_obj.power_units_rel
            else [],
            model_types_rel=[structure_obj.model_types_rel]
            if structure_obj.model_types_rel
            else [],
            # Don't need unit_types_rel if we have the model type
            # unit_types_rel=[],
            location=structure_obj.surface,
            service_type_id=SERVICE_TYPE_ID_PREV_MAINT,
            creator_id=user_id,
            # Field techs on-site...
            users_rel=[user_who_created_quote],
            # Sales credit goes to...
            users_sales_rel=[user_who_created_quote],
            # IJACK Inc or IJACK Corp
            creator_company_id=CUSTOMER_ID_IJACK_INC
            if is_canadian
            else CUSTOMER_ID_IJACK_CORP,
            # Could be CAD or USD
            currency_id=CURRENCY_ID_CAD if is_canadian else CURRENCY_ID_USD,
            requested_by_id=first_maintenance_user.id
            if first_maintenance_user
            else None,
            approved_by_id=first_maintenance_user.id
            if first_maintenance_user
            else None,
            # Default two weeks from now
            date_service=date.today() + timedelta(days=14),
            # Required fields
            country_id=structure_obj.warehouse_rel.country_id,
            customer_id=structure_obj.get_main_customer_id(),
            sales_tax_rate=0.0,
            discount_pct=0.0,
            sales_tax=0.0,
            subtotal=0.0,
            total=0.0,
        )
        db.session.add(work_order)

        # session.flush() communicates a series of operations to the database (insert, update, delete).
        # The database maintains them as pending operations in a transaction.
        # The changes aren't persisted permanently to disk, or visible to other transactions
        # until the database receives a COMMIT for the current transaction (which is what session.commit() does).
        db.session.flush()

        # At this point, the object f has been pushed to the DB,
        # and has been automatically assigned a unique primary key id.
        # Refresh updates given object in the session with its state in the DB
        # (and can also only refresh certain attributes - search for documentation)
        db.session.refresh(work_order)

        # Get the seal kit part IDs for the preventative maintenance
        seal_kit_part_ids: DataFrame = get_pm_seal_kits_msrp(
            structure_ids=[structure_id], want_part_id=True
        )

        # Convert DataFrame to dictionary with part_id as keys and pm_seal_kits_quantity as values
        # Using set_index() and squeeze() to transform the data into the desired format
        part_ids_and_quantities: dict = seal_kit_part_ids.set_index("part_id")[
            "pm_seal_kits_quantity"
        ].to_dict()

        work_order_parts_rel: list = []
        for part_id, quantity in part_ids_and_quantities.items():
            # Find the appropriate price in CAD or USD for the part
            part_model: Part | None = db.session.get(Part, part_id)
            if not part_model:
                current_app.logger.error(
                    f"Part with ID {part_id} not found in the database when trying to create work order quote for {structure_obj}"
                )
                continue
            price_to_get: str = "msrp_cad" if is_canadian else "msrp_usd"
            price: float = getattr(part_model, price_to_get, 0.0)
            work_order_part = WorkOrderPart(
                work_order_id=work_order.id,
                part_id=part_id,
                description=part_model.description,
                structure_id=structure_id,
                warehouse_id=structure_obj.warehouse_id,
                field_tech_id=user_id,
                quantity=quantity,
                price=price,
                sales_tax_rate=0.0,
            )
            work_order_parts_rel.append(work_order_part)

        work_order.work_order_parts_rel = work_order_parts_rel
        # Now update the subtotal after adding the parts!
        work_order.update_subtotal()

        db.session.commit()
    except Exception as err:
        db.session.rollback()
        return return_vars(
            health_modal_is_open=True,
            health_modal_header_children="Error",
            health_modal_body_children=f"Error creating work order quote for {structure_obj}: {err}",
        )

    href_endpoint: str = "dash.work_order" if is_canadian else "dash.work_order_corp"
    href: str = url_for(href_endpoint, work_order_id=work_order.id)
    return return_vars(
        health_modal_is_open=True,
        health_modal_header_children="Success!",
        health_modal_body_children=html.Div(
            [
                # Main message with some spacing and styling
                html.P(
                    [
                        "Work order quote successfully created for ",
                        html.Strong(str(structure_obj)),  # Make the structure name bold
                    ],
                    # Add margin bottom for spacing
                    className="mb-3",
                ),
                dbc.Button(
                    [
                        "📄 ",  # Document icon
                        "View Work Order",
                    ],
                    href=href,
                    color="primary",  # This replaces btn-primary
                    className="mx-auto",  # Centers the button
                ),
            ],
            className="text-center p-3",
        ),  # Center content and add padding
    )


@callback(
    Output("health_modal", "is_open", allow_duplicate=True),
    Output("health_modal_header", "children", allow_duplicate=True),
    Output("health_modal_body", "children", allow_duplicate=True),
    Output("health_modal_confirm", "style", allow_duplicate=True),
    Output("health_modal_cancel", "style", allow_duplicate=True),
    Output("health_modal_close", "style", allow_duplicate=True),
    Output("health_structure_id_store", "data"),
    Output("health_view_structure_id_store", "data"),
    Input("health_ag_grid", "cellClicked"),
    State("health_ag_grid", "cellRendererData"),
    State("health_ag_grid", "rowData"),
    prevent_initial_call=True,
)
def ag_grid_button_click(
    health_ag_grid_cellClicked,
    health_ag_grid_cellRendererData,
    health_ag_grid_rowData,
):
    """Get the structure_id of the clicked cell in the status Ag Grid"""
    structure_id: int | None = health_ag_grid_cellClicked.get("rowId", None)
    col_id: str | None = health_ag_grid_cellClicked.get("colId", None)
    if not structure_id or col_id not in ("structure_id", "structure_id2"):
        raise PreventUpdate

    def return_vars(
        health_modal_is_open: bool,
        health_modal_header_children: str,
        health_modal_body_children: str,
        health_modal_confirm_style: dict = {},
        health_modal_cancel_style: dict = {},
        health_modal_close_style: dict = {"display": "none"},
        health_structure_id_store_data: int | None = None,
        health_view_structure_id_store_data: int | None = no_update,
    ) -> tuple:
        """Default return vars"""
        return (
            health_modal_is_open,
            health_modal_header_children,
            health_modal_body_children,
            health_modal_confirm_style,
            health_modal_cancel_style,
            health_modal_close_style,
            health_structure_id_store_data,
            health_view_structure_id_store_data,
        )

    if col_id == "structure_id":
        # View structure button clicked, meaning the user wants to view the structure in RCOM
        return return_vars(
            health_modal_is_open=False,
            health_modal_header_children=no_update,
            health_modal_body_children=no_update,
            health_modal_confirm_style=no_update,
            health_modal_cancel_style=no_update,
            health_modal_close_style=no_update,
            health_structure_id_store_data=structure_id,
            health_view_structure_id_store_data=structure_id,
        )

    # Make work order quote button clicked (col_id == "structure_id2")
    structure_obj: Structure | None = Structure.query.get(structure_id)
    if not structure_obj:
        return return_vars(
            health_modal_is_open=True,
            health_modal_header_children="Error",
            health_modal_body_children=f"No structure found with ID {structure_id}",
            health_structure_id_store_data=None,
        )

    return return_vars(
        health_modal_is_open=True,
        health_modal_header_children="Make Work Order Quote",
        health_modal_body_children=f"Are you sure you want to make a work order quote for {structure_obj}?",
        health_structure_id_store_data=structure_id,
    )


@callback(
    Output("health_ag_grid", "style"),
    Input("health_ag_grid", "cellRendererData"),
    prevent_initial_call=True,
)
def toggle_rcom_status(cell_value_changed: dict):
    """Toggle the RCOM status when the BootstrapSwitch is clicked"""
    if not cell_value_changed:
        raise PreventUpdate

    # Check if the clicked cell is the RCOM toggle cell
    if cell_value_changed.get("colId") != "has_rcom":
        raise PreventUpdate

    # Get the structure ID and the current RCOM status
    structure_id: str | None = cell_value_changed.get("rowId", None)
    new_has_rcom: bool | None = cell_value_changed.get("value", None)

    # Validate the data
    if structure_id is None or new_has_rcom is None:
        raise PreventUpdate

    # Find the structure in the database
    structure = db.session.get(Structure, structure_id)
    if structure and getattr(structure, "has_rcom", None) != new_has_rcom:
        # Toggle the has_rcom value
        structure.has_rcom = new_has_rcom
        db.session.commit()

    raise PreventUpdate
