import os
import time
from collections import OrderedDict
from datetime import datetime, timezone
from typing import List, TypedDict

import dash_bootstrap_components as dbc
import pandas as pd

# from datetime import datetime
# from dateutil.relativedelta import relativedelta
import pytz
from dash import html
from flask import current_app
from flask_login import current_user
from shared.models.models import RemoteControl

from app import db, user_is_demo_customer, user_is_ijack_employee
from app.config import ROLE_ID_REMOTE_CONTROL_ALL, STRUCTURE_ID_SHOP
from app.dashapp.metrics import settable_metric_names
from app.dashapp.utils import (
    get_unit_type,
    seconds_since_last_any_msg,
    time_since_friendly_string,
)
from app.utils.simple import utcnow_naive


def record_remote_control(
    power_unit_id: int,
    power_unit_str: str,
    aws_thing: str,
    metric: str = None,
    value_wanted: str | int | float = None,
    action=None,
) -> bool:
    """Record when a user submits a remote control command"""

    remote_control = RemoteControl(
        user_id=getattr(current_user, "id", None),
        power_unit_id=power_unit_id,
        power_unit=power_unit_str,
        metric=metric,
        value_wanted=value_wanted,
        action=action,
        timestamp_utc=str(utcnow_naive()),
        aws_thing=aws_thing,
        dev_test_prd=os.getenv("FLASK_CONFIG", "development"),
    )

    # add remote_control record to the database
    try:
        db.session.add(remote_control)
        db.session.commit()
    except Exception:
        current_app.logger.exception(
            "ERROR: Problem recording remote control action in database..."
        )
        return False

    return True


class PermissionsAbilities(TypedDict):
    # Indicates whether the user has any special abilities
    has_abilities: bool
    # List that contains card body information (could be strings, numbers, or other types)
    card_body: List
    # Indicates whether the user has permission to configure VPN settings
    can_set_apn: bool


def get_card_body_permissions_abilities(
    shadow, structure_obj, swv_plc: int = None
) -> PermissionsAbilities:
    """
    Check that the PLC and gateway have adequate
    software versions to perform remote control.
    Return whether remote control is possible (boolean),
    and the card_body list of messages for the user
    """

    # desired = shadow.get('state', {}).get('desired', {})
    reported = shadow.get("state", {}).get("reported", {})

    is_connected, seconds_since_last_data = check_is_connected(shadow)

    try:
        swv_plc = swv_plc or float(
            reported.get("SWV", 0)
        )  # software version PLC (Dan's software)
        swv_python = float(reported.get("SWV_PYTHON", 0))  # software version Python
    except Exception:
        swv_plc = 9999.0
        swv_python = 9999.0

    structure_id = getattr(structure_obj, "id", None)
    if structure_id is None:
        return PermissionsAbilities(
            has_abilities=False, card_body=[], can_set_apn=False
        )

    swv_plc_msg = ""
    swv_python_msg = ""
    card_body = []

    # To be an IJACK admin (who can remote-control anything), you must have a customer_id of 1 and a role of 1
    role_ids_list = [role.id for role in current_user.roles_rel]
    # Unit-specific remote-control permissions from the user_structure_remote_control_rel table
    structure_ids_list = [structure.id for structure in current_user.structures_rel]

    is_ijack_employee: bool = user_is_ijack_employee(
        user_id=getattr(current_user, "id", None)
    )

    has_abilities = (
        (ROLE_ID_REMOTE_CONTROL_ALL in role_ids_list)
        or (structure_id in structure_ids_list)
        or (is_ijack_employee and structure_id == STRUCTURE_ID_SHOP)
    )

    # If the gateway is not in the list of gateways the user can remote-control,
    # and you are not is_ijack_admin, you cannot remote-control anything
    if not has_abilities:
        specific_unit_permission_msg = f"{current_user.first_name} {current_user.last_name} does not currently have remote-control permissions for this particular unit."
        contact_ijack_msg = "Please contact IJACK and/or your supervisor, if you need to change your permissions."
        card_body.append(
            dbc.Alert(
                [
                    html.P(specific_unit_permission_msg),
                    html.P(contact_ijack_msg),
                ],
                color="danger",
            )
        )

    swv_plc_required_minimum = 305
    if swv_plc < swv_plc_required_minimum:
        has_abilities = False
        # is_open = True
        swv_plc_msg = "This unit's PLC requires a software update before the unit can be controlled remotely."
        swv_plc_msg += f" It says it's running version {swv_plc}, and remote control requires at least version {swv_plc_required_minimum}."
        swv_plc_msg += " Please contact IJACK if you want the PLC software updated. This update must be performed on-site."
        # card_body.append(html.P(swv_plc_msg, style={'color': '#717174'}))
        card_body.append(dbc.Alert(swv_plc_msg, color="danger"))

    swv_python_required_minimum = 1.25
    if swv_python < swv_python_required_minimum:
        has_abilities = False
        # is_open = True
        swv_python_msg = "This unit's gateway requires a software update before the unit can be controlled from this website."
        swv_python_msg += f" It says it's running version {swv_python}, and remote control requires at least version {swv_python_required_minimum}."
        swv_python_msg += " Please contact IJACK to update the gateway's software. This update can be done over-the-air, without a site visit."
        # card_body.append(html.P(swv_python_msg, style={'color': '#717174'}))
        card_body.append(dbc.Alert(swv_python_msg, color="danger"))

    if not is_connected:
        # is_open = True
        friendly_time_since = time_since_friendly_string(seconds_since_last_data)
        conn_msg = f"The unit has not reported in {friendly_time_since}. It may not have power or internet access. If the unit does not have power or internet access, remote-control will not work."
        card_body.append(dbc.Alert(conn_msg, color="warning"))

    # Email us button
    if not has_abilities:
        card_body.append(
            dbc.Button(
                "Email IJACK",
                href="mailto:<EMAIL>",
                target="_blank",
                color="primary",
                # style={'margin-bottom': '10px'},
            )
        )
        card_body.append(html.Hr())  # Horizontal rule (line)

    can_set_apn: bool = swv_python >= 3.334136

    return PermissionsAbilities(
        has_abilities=has_abilities, card_body=card_body, can_set_apn=can_set_apn
    )


def get_message(
    metric, desired_value, reported_value, structure_obj, tab_uno_egas, unit_type_id
):
    """
    Get any messages for the user, after they've remote-controlled a unit.
    These messages will say what the desired and reported states are
    """

    unit_type_lower, unit_type_id = get_unit_type(tab_uno_egas, unit_type_id)
    unit_type_upper = unit_type_lower.upper()
    message = ""

    if metric in ("HYD", "HYD_EGAS"):
        if desired_value == 1 and reported_value == 0:
            message = f"The {unit_type_upper} has been ordered to start, and we are waiting for confirmation that it has started."
        elif desired_value == 0 and reported_value == 1:
            message = f"The {unit_type_upper} has been ordered to stop, and we are waiting for confirmation that it has stopped."
    elif metric in ("LOCKOUT", "LOCKOUT_EGAS"):
        if desired_value == 0 and reported_value == 1:
            message = f"The {unit_type_upper} has been ordered to unlock, and we are waiting for confirmation that it has been unlocked."
        elif desired_value == 1 and reported_value == 0:
            message = f"The {unit_type_upper} has been ordered to lock, and we are waiting for confirmation that it has been locked."
    elif metric == "AGFT":
        message = f"The {unit_type_upper} has been ordered to change the suction pressure target to {desired_value} PSI,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value} PSI."
    elif metric == "AGFM":
        message = f"The {unit_type_upper} has been ordered to change its maximum speed to {desired_value}%,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value}%."
    elif metric == "AGFN":
        message = f"The {unit_type_upper} has been ordered to change its minimum speed to {desired_value}%,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value}%."
    elif metric == "MGP":
        message = f"The {unit_type_upper} has been ordered to change its maximum discharge pressure to {desired_value} PSI,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value} PSI."
    elif metric == "PID":
        message = f"The {unit_type_upper} has been ordered to change its adjustment speed (PID sensitivity) to {desired_value}/10,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value}/10."
    elif metric == "HP_LIMIT":
        message = f"The {unit_type_upper} has been ordered to change its maximum horsepower to {desired_value} HP,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value} HP."
    # UNO messages
    elif metric == "PID_UNO":
        message = f"The {unit_type_upper} has been ordered to change its adjustment speed (PID sensitivity) to {desired_value}/10,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value}/10."
    elif metric == "SPM_SET":
        message = f"The {unit_type_upper} has been ordered to change its strokes per minute to {desired_value} SPM,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value} SPM."
    elif metric == "SPM_MAX":
        message = f"The {unit_type_upper} has been ordered to change its maximum strokes per minute to {desired_value} SPM,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value} SPM."
    elif metric == "SPM_MIN":
        message = f"The {unit_type_upper} has been ordered to change its minimum strokes per minute to {desired_value} SPM,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value} SPM."
    elif metric == "APFT":
        message = f"The {unit_type_upper} has been ordered to change its auto-pump fillage target to {desired_value}%,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value}%."
    elif metric == "TS":
        message = f"The {unit_type_upper} has been ordered to change its top setpoint to {desired_value} inches,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value} inches."
    elif metric == "BS":
        message = f"The {unit_type_upper} has been ordered to change its bottom setpoint to {desired_value} inches,"
        message += f" and we are waiting for confirmation of setpoint change. Currently it is reporting a setpoint of {reported_value} inches."
    elif metric == "AUTO_SPM":
        message = f"The {unit_type_upper} has been ordered to start auto-SPM mode,"
        message += " and we are waiting for confirmation of setpoint change."
    elif metric == "AUTO_TYPE" and desired_value == 1:
        message = (
            f'The {unit_type_upper} has been ordered to start auto-type "maximum" mode,'
        )
        message += " and we are waiting for confirmation of setpoint change."
    elif metric == "AUTO_TYPE" and desired_value == 0:
        message = (
            f'The {unit_type_upper} has been ordered to start auto-type "average" mode,'
        )
        message += " and we are waiting for confirmation of setpoint change."
    # elif metric == 'AUTO_TYPE_MAX':
    #     message = f'The {unit_type_upper} has been ordered to start auto-type "maximum" mode,'
    #     message += f" and we are waiting for confirmation of setpoint change."
    # elif metric == 'AUTO_TYPE_AVG':
    #     message = f'The {unit_type_upper} has been ordered to start auto-type "average" mode,'
    #     message += f" and we are waiting for confirmation of setpoint change."
    elif metric == "AUTO_TAP":
        message = f"The {unit_type_upper} has been ordered to start auto-tap mode,"
        message += " and we are waiting for confirmation of setpoint change."
    elif metric == "AUTO_TAP_TOP":
        message = (
            f'The {unit_type_upper} has been ordered to start auto-tap "top" mode,'
        )
        message += " and we are waiting for confirmation of setpoint change."
    elif metric == "AUTO_TAP_BTM":
        message = (
            f'The {unit_type_upper} has been ordered to start auto-tap "bottom" mode,'
        )
        message += " and we are waiting for confirmation of setpoint change."

    return message


def get_card_body_control_progress(
    aws_thing, shadow, structure_obj, warnings_list, tab_uno_egas, unit_type_id
):
    """Get the card body for the remote control progress/status table (desired vs. reported)"""

    desired = shadow.get("state", {}).get("desired", {})
    reported = shadow.get("state", {}).get("reported", {})

    customer_first_word = structure_obj.customer.split()[0]
    model = structure_obj.model
    surface = structure_obj.surface
    power_unit_str = structure_obj.power_unit_str

    # If the time_zone is None, use Regina as default
    if structure_obj.time_zone:
        tz_wanted = structure_obj.time_zone
    else:
        tz_wanted = "America/Regina"

    metrics_for_table = []
    desired_for_table = []
    reported_for_table = []
    messages = []
    value_translation = {
        "HYD": {1: "Start", 0: "Stop"},
        "HYD_EGAS": {1: "Start", 0: "Stop"},
        "LOCKOUT": {0: "Unlock", 1: "Lock"},
        "LOCKOUT_EGAS": {0: "Unlock", 1: "Lock"},
    }
    for metric, desired_value in desired.items():
        if metric in settable_metric_names.keys():
            reported_value = reported.get(metric, "")
            if reported_value != desired_value:
                metrics_for_table.append(settable_metric_names[metric])

                if isinstance(desired_value, dict):
                    # AGFT could be a "scheduled command" dictionary instead of a single value
                    desired_for_table.append(
                        desired_value.get("value_desired", desired_value)
                    )
                else:
                    desired_for_table.append(
                        value_translation.get(metric, {}).get(
                            desired_value, desired_value
                        )
                    )

                reported_for_table.append(
                    value_translation.get(metric, {}).get(
                        reported_value, reported_value
                    )
                )
                messages.append(
                    get_message(
                        metric,
                        desired_value,
                        reported_value,
                        structure_obj,
                        tab_uno_egas,
                        unit_type_id,
                    )
                )

    # Identify the demo user for which to make fake locations
    is_demo: bool = user_is_demo_customer(user_id=getattr(current_user, "id", None))
    customer_unit_info = f"{customer_first_word} {str(structure_obj.unit_type).upper()} {power_unit_str} at {surface}"

    if is_demo:
        card_header = f"Remote control status for {customer_first_word} {model}"
    else:
        # If we're just testing...
        if aws_thing == "lambda_access":
            card_header = 'Just testing with "lambda_access" AWS thing...'
        else:
            card_header = f"Remote control status for {customer_unit_info}"

    # No sense having the status window open if there are no deltas between desired and reported
    if len(metrics_for_table) == 0:
        # return is_open, card_header, card_body
        # Convert timestamp_utc to local time
        shadow_dt = datetime.fromtimestamp(
            shadow.get("timestamp", time.time()), timezone.utc
        )

        local_time = shadow_dt.astimezone(pytz.timezone(tz_wanted))
        local_time_formatted = local_time.strftime("%A, %b %-d at %H:%M:%S")
        warnings_list.append(
            dbc.Alert(
                html.P(
                    "All remote control commands have been executed successfully.",
                    style={"color": "#717174"},
                ),
                "success",
            )
        )
        warnings_list.append(
            html.P(
                f"Last checked {local_time_formatted}, local pump time.",
                style={"color": "#717174"},
            )
        )
        card_body = html.Div(warnings_list)

        return True, card_header, card_body
        # return False, card_header, html.P('All remote control commands have been executed successfully.', style={'color': '#717174'})
    else:
        is_open = True

    df = pd.DataFrame(
        {
            "Setpoint": metrics_for_table,
            "Desired": desired_for_table,
            "Reported": reported_for_table,
        }
    )
    table = (
        dbc.Table.from_dataframe(
            df,
            striped=True,
            bordered=True,
            hover=True,
            style={"color": "#717174", "margin-top": "1rem"},
        ),
    )

    for x in messages:
        warnings_list.append(html.P(x, style={"color": "#717174"}))

    card_body = [
        dbc.Row(dbc.Col(warnings_list)),
        dbc.Row(dbc.Col(table)),
    ]

    return is_open, card_header, card_body


def get_is_valid_input(
    value, metric, swv_plc: int | float = 9999, is_vessel_level_mode: bool = False
):
    """Validate the user's entered target"""

    if isinstance(value, dict):
        # It's a scheduled command
        value = value.get("value_desired", None)

    if value is None:
        return False

    if metric == "AGFT":
        # Suction target
        lower_suction_limit = 1
        upper_suction_limit = 700
        if is_vessel_level_mode:
            return value in range(0, 101)
        if swv_plc >= 414:
            upper_suction_limit = 700
            lower_suction_limit = -8
        elif swv_plc >= 316:
            upper_suction_limit = 700
            lower_suction_limit = 0
        elif swv_plc >= 309:
            upper_suction_limit = 700
            lower_suction_limit = 1
        else:
            upper_suction_limit = 250
            lower_suction_limit = 1
        return lower_suction_limit <= value <= upper_suction_limit
    elif metric == "AGFM":
        # Max speed
        return value >= 2 and value <= 100
    elif metric == "AGFN":
        # Min speed
        return value >= 5 and value <= 100
    elif metric == "MGP":
        # Max derate discharge pressure
        return value >= 1 and value <= 1350
    elif metric == "MSP":
        # Max shutdown pressure
        if swv_plc >= 412:
            return value >= 60 and value <= 1400
        # Otherwise, can only go down to 100 PSI
        return value >= 100 and value <= 1400
    elif metric == "PID":
        # PID sensitivity
        return value >= 1 and value <= 4
    elif metric == "HP_LIMIT":
        # Horsepower limit
        return value >= 15 and value <= 200
    elif metric == "SPM_SET":
        # SPM setpoint
        return value >= 0.1 and value <= 15
    elif metric == "SPM_MAX":
        # SPM max
        return value >= 0.1 and value <= 15
    elif metric == "SPM_MIN":
        # SPM min
        return value >= 0.1 and value <= 15
    elif metric == "APFT":
        # Auto-pump fillage target
        return value >= 5 and value <= 90
    elif metric == "TS":
        # Top setpoint
        return value >= 111 and value <= 144
    elif metric == "BS":
        # Bottom setpoint
        return value >= 0 and value <= 33
    elif metric == "PID_UNO":
        # PID sensitivity for UNO
        return value >= 1 and value <= 4
    elif metric == "AUTO_TAP_TGT":
        # Auto-tap target for UNO
        return value >= 5 and value <= 20
    elif metric == "PEFF":
        # Efficiency for UNO
        return value >= 5 and value <= 95
    elif metric == "PSIZE":
        # Pump size for UNO
        return value >= 0 and value <= 11
    else:
        return True


def get_latest_targets_f_shadow(shadow_all):
    """
    Find the latest target (i.e. setpoint) values from the AWS IoT device shadow
    """
    reported = shadow_all.get("state", {}).get("reported", {})
    # meta = shadow_all.get('metadata', {}).get('reported', {})

    latest_targets = {}
    latest_targets["suction_target"] = reported.get("AGFT", None)
    latest_targets["suction_actual"] = reported.get("CGP", None)
    latest_targets["max_discharge"] = reported.get("MGP", None)
    latest_targets["max_shutdown_pressure"] = reported.get("MSP", None)
    latest_targets["max_speed"] = reported.get("AGFM", 0)
    latest_targets["min_speed"] = reported.get("AGFN", 0)
    latest_targets["horsepower_limit"] = reported.get("HP_LIMIT", None)
    latest_targets["pid_sensitivity"] = reported.get("PID", None)

    # 8 UNO latest metrics
    latest_targets["spm_set"] = reported.get("SPM_SET", None)
    latest_targets["spm_max"] = reported.get("SPM_MAX", None)
    latest_targets["spm_min"] = reported.get("SPM_MIN", None)
    latest_targets["apft"] = reported.get("APFT", None)
    latest_targets["pid_uno"] = reported.get("PID_UNO", None)
    latest_targets["ts"] = reported.get("TS", None)
    latest_targets["bs"] = reported.get("BS", None)
    latest_targets["auto_tap_tgt"] = reported.get("AUTO_TAP_TGT", None)
    latest_targets["peff"] = reported.get("PEFF", None)
    latest_targets["psize"] = reported.get("PSIZE", None)

    # UNO switch for automatic stuff
    control_uno_auto_stuff_list = []
    for name in ["AUTO_SPM", "AUTO_TAP", "AUTO_TAP_TOP", "AUTO_TAP_BTM"]:
        if reported.get(name, None) == 1:
            control_uno_auto_stuff_list.append(name)

    latest_targets["control_uno_auto_stuff_list"] = control_uno_auto_stuff_list
    latest_targets["control_uno_auto_type_value"] = reported.get("AUTO_TYPE", None)
    latest_targets["vpn"] = reported.get("VPN", None)
    latest_targets["vpn_ip"] = reported.get("VPN_IP", "Unknown")
    latest_targets["current_apn"] = reported.get("APN", "Unknown")

    return latest_targets


def check_is_stroking(unit_type_lower, shadow_all):
    """Check if the unit is stroking (i.e. hydraulics on, and stroking heartbeat is good)"""

    reported = shadow_all.get("state", {}).get("reported", {})

    if unit_type_lower == "UNO":
        hydraulics_on = True if reported.get("HYD", 0) == 1 else False
    else:
        hydraulics_on = True if reported.get("HYD_EGAS", 0) == 1 else False

    return hydraulics_on


def get_on_site_setpoints(reported):
    """Make a dictionary of unit status indicators from the AWS IoT device shadow"""

    # Store all indicators in a dictionary so we can add them all to a list by iterating through the dictionary
    d = OrderedDict()

    # Top deceleration
    metric = "top_decel"
    value = reported.get("TOP_DECEL", None)
    value = f"{value}" if value else "Not Reported"
    d[metric] = {}
    d[metric]["msg"] = value
    d[metric]["color"] = "secondary"
    d[metric]["label"] = "Top deceleration (300 ms default)"

    # Top acceleration
    metric = "top_accel"
    value = reported.get("TOP_ACCEL", None)
    value = f"{value}" if value else "Not Reported"
    d[metric] = {}
    d[metric]["msg"] = value
    d[metric]["color"] = "secondary"
    d[metric]["label"] = "Top acceleration (50 ms default)"

    # Bottom acceleration
    metric = "btm_accel"
    value = reported.get("BTM_ACCEL", None)
    value = f"{value}" if value else "Not Reported"
    d[metric] = {}
    d[metric]["msg"] = value
    d[metric]["color"] = "secondary"
    d[metric]["label"] = "Bottom acceleration (50 ms default)"

    # Bottom deceleration
    metric = "btm_decel"
    value = reported.get("BTM_DECEL", None)
    value = f"{value}" if value else "Not Reported"
    d[metric] = {}
    d[metric]["msg"] = value
    d[metric]["color"] = "secondary"
    d[metric]["label"] = "Bottom deceleration (300 ms default)"

    # Top delay (seconds)
    metric = "top_wait_time"
    value = reported.get("TOP_WAIT_TIME", None)
    value = f"{value:.1f}" if value else "Not Reported"
    d[metric] = {}
    d[metric]["msg"] = value
    d[metric]["color"] = "secondary"
    d[metric]["label"] = "Top stroke delay (0 seconds default)"

    # Bottom delay (seconds)
    metric = "btm_wait_time"
    value = reported.get("BTM_WAIT_TIME", None)
    value = f"{value:.1f}" if value else "Not Reported"
    d[metric] = {}
    d[metric]["msg"] = value
    d[metric]["color"] = "secondary"
    d[metric]["label"] = "Bottom stroke delay (0 seconds default)"

    return d


def check_is_connected(shadow_all):
    """Check if the unit has reported to the device shadow recently"""

    seconds_since_last_reported, _ = seconds_since_last_any_msg(shadow_all)
    UNREPORTED_AFTER_X_SECONDS = current_app.config.get(
        "UNREPORTED_AFTER_X_SECONDS", 60 * 5
    )

    if seconds_since_last_reported < UNREPORTED_AFTER_X_SECONDS:
        return True, seconds_since_last_reported

    return False, seconds_since_last_reported
