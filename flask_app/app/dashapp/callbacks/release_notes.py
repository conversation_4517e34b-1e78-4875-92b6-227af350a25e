import os
from datetime import datetime
from typing import Any, Dict
from urllib import parse
from uuid import uuid1

import boto3
import dash_bootstrap_components as dbc
import pytz
from botocore.exceptions import ClientError
from dash import (
    ALL,
    Input,
    Output,
    State,
    callback,
    callback_context,
    dcc,
    html,
    no_update,
)
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import PowerUnit, ReleaseNote, TimeZone

from app.dashapp.callbacks.control_utils import record_remote_control
from app.dashapp.utils import get_id_triggered, get_iot_device_shadow
from app.databases import get_boto3_client
from app.utils.complex import utc_datetime_to_string

# # Sample release notes for each update
# release_notes = {
#     "Update 1.0.1": "Bug fixes and performance improvements.",
#     "Update 1.0.2": "New feature added: Improved security settings.",
#     "Update 1.1.0": "Major update with UI enhancements and new functionalities.",
# }


# def get_release_notes():
#     """Get the release notes from the database"""
#     release_notes = ReleaseNote.query.all()
#     return release_notes


def create_update_modal():
    """Modal for release notes"""
    return html.Div(
        [
            dbc.Modal(
                [
                    dbc.ModalHeader(
                        "Confirm Update Gateway Software",
                        id="release_notes_modal_header",
                    ),
                    dbc.ModalBody(id="release_notes_modal_body"),
                    dbc.ModalFooter(
                        [
                            dbc.Button(
                                "Confirm",
                                id="release_notes_modal_confirm_btn",
                                className="ml-auto me-2",
                                # disabled=True,
                                color="danger",
                                # Redirect after submitting form
                                external_link=False,
                                # href=f"/{DASH_URL_RELEASE_NOTES}/",
                            ),
                            # html.A(
                            #     html.Button("Refresh Page"), href="relative_pathname"
                            # ),
                            dbc.Button(
                                "Cancel",
                                id="release_notes_modal_cancel_btn",
                                # className="ml-auto",
                            ),
                        ]
                    ),
                ],
                id="release_notes_modal",
                size="lg",
                centered=True,
                is_open=False,
            ),
        ]
    )


def release_notes_layout_children() -> list:
    """release notes layout"""

    if hasattr(current_user, "id"):
        release_notes = ReleaseNote.query.order_by(
            ReleaseNote.timestamp_utc.desc()
        ).all()
    else:
        release_notes = []

    rows = html.Div(
        [
            dbc.Row(
                dbc.Col(
                    dbc.Card(
                        [
                            dbc.CardHeader(
                                f"{release_note.timestamp_utc.strftime('%Y-%m-%d')}: Version {release_note.version}"
                            ),
                            dbc.CardBody(dcc.Markdown(release_note.description)),
                            dbc.CardFooter(
                                dbc.Button(
                                    "Update",
                                    id={
                                        "type": "canpy_update_button",
                                        "canpy_version_wanted": release_note.version,
                                    },
                                )
                            ),
                        ],
                    )
                ),
                class_name="mb-3",
            )
            for release_note in release_notes
        ]
    )

    update_status_div = dbc.Row(
        dbc.Col(
            dbc.Card(
                [
                    dcc.Interval(
                        id="release_notes_refresh_interval",
                        # in milliseconds, so 5000 is 5 seconds
                        interval=10_000,
                        # automatically incremented by 1 every time "interval" milliseconds passes
                        n_intervals=0,
                        # If -1, then the interval has no limit (the default)
                        max_intervals=-1,
                        disabled=True,
                    ),
                    dbc.CardHeader(
                        "Unit Software Status",
                        id="release_notes_update_status_card_header",
                    ),
                    dbc.Spinner(
                        dbc.CardBody(
                            "Nothing to report yet",
                            id="release_notes_update_status_card_body",
                        ),
                    ),
                    dbc.CardFooter(
                        [
                            dbc.Button(
                                "Refresh",
                                id="release_notes_update_status_refresh_btn",
                                color="primary",
                                n_clicks=0,
                                class_name="me-2",
                            ),
                            dbc.Button(
                                "Cancel Update",
                                id="release_notes_update_status_cancel_btn",
                                color="danger",
                                n_clicks=0,
                                disabled=True,
                            ),
                        ]
                    ),
                ]
            )
        ),
        class_name="mb-5",
    )

    return [
        update_status_div,
        html.H1("Release Notes"),
        rows,
        dcc.Store(
            id="release_notes_store_canpy_version_current",
            storage_type="memory",
            data="",
        ),
        dcc.Store(
            id="release_notes_store_canpy_version_wanted",
            storage_type="memory",
            data="",
        ),
        dcc.Store(
            id="release_notes_store_mqtt_job_id",
            storage_type="local",
            data={},
        ),
        dcc.Store(id="release_notes_store_gateway", storage_type="memory", data=""),
        dcc.Store(
            id="release_notes_store_power_unit_str", storage_type="memory", data=""
        ),
        create_update_modal(),
    ]


def release_notes_layout():
    """
    Get the Dash layout for the release notes/Python software update page.
    """
    return dbc.Container(
        dbc.Row(
            dbc.Col(
                release_notes_layout_children(),
                xs=12,
                # sm=10,
                md=10,
                lg=8,
                xl=6,
            ),
            justify="center",
        )
    )


def verify_aws_iot_thing(thing_name: str, client) -> Dict[str, Any]:
    """
    Verify if an AWS IoT thing exists and return its details
    """
    try:
        # Attempt to describe the IoT thing
        response = client.describe_thing(thingName=thing_name)
        current_app.logger.info(f"Successfully verified IoT thing: {thing_name}")
        return response

    except ClientError as e:
        # Handle specific AWS errors
        error_code = e.response.get("Error", {}).get("Code")
        if error_code == "ResourceNotFoundException":
            current_app.logger.error(f"AWS IoT thing '{thing_name}' not found")
            raise ValueError(f"AWS IoT thing '{thing_name}' does not exist")
        else:
            current_app.logger.error(f"AWS error checking IoT thing: {e}")
            raise


def find_thing_in_region(thing_name: str, region: str = "us-west-2"):
    """
    Find an IoT thing using pagination to search through all things
    """
    client = boto3.client("iot", region_name=region)
    paginator = client.get_paginator("list_things")

    try:
        for page in paginator.paginate():
            for thing in page["things"]:
                if thing["thingName"] == thing_name:
                    current_app.logger.info(f"Found thing: {thing}")
                    return thing

        raise ValueError(f"Thing '{thing_name}' not found after searching all pages")

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        error_msg = e.response["Error"]["Message"]
        current_app.logger.error(f"AWS Error ({error_code}): {error_msg}")
        raise


def verify_thing_exists(thing_name: str) -> bool:
    """
    Verify thing exists by listing rather than describing
    """
    try:
        find_thing_in_region(thing_name)
        return True
    except Exception as e:
        current_app.logger.error(f"Error verifying thing existence: {str(e)}")
        return False


def create_mqtt_job_update(canpy_version: str, aws_thing: str) -> str:
    """Create old MQTT job for updating gateway, and return the job ID"""

    rand: str = uuid1(node=None, clock_seq=None)
    job_id: str = f"canpy_v{canpy_version}_{rand}".replace(".", "-")
    targets: list = [f"arn:aws:iot:us-west-2:************:thing/{aws_thing}"]
    JOB_DOC_NAME: str = f"job_document_canpy_v{canpy_version}.json"
    MQTT_JOBS_TIMEOUT_SECONDS: int = 60

    if not verify_thing_exists(aws_thing):
        raise ValueError(f"Thing '{aws_thing}' not found in us-west-2")

    try:
        client = get_boto3_client("iot")

        # First verify the IoT thing exists
        verify_aws_iot_thing(aws_thing, client)

        client.create_job(
            # The unique identifier you assigned to this job
            jobId=job_id,
            # A list of things and thing groups to which the job should be sent
            targets=targets,
            # An S3 link, or S3 object URL, to the job document.
            # The link is an Amazon S3 object URL and is required if you don't specify a value for document
            documentSource=f"https://ijack-canpy-deployment.s3-us-west-2.amazonaws.com/{JOB_DOC_NAME}",
            # document=job_document_file_path,
            description=f"Update the '{aws_thing}' gateways' main 'canpy' software",
            # presignedUrlConfig={
            #     'roleArn': 'string',
            #     'expiresInSec': 123
            # },
            # Specifies whether the job will continue to run (CONTINUOUS), or will be complete after all those things specified as targets have completed the job (SNAPSHOT)
            # If continuous, the job may also be run on a thing when a change is detected in a target.
            # For example, a job will run on a thing when the thing is added to a target group, even after the job was completed by all things originally in the group.
            # We recommend that you use continuous jobs instead of snapshot jobs for dynamic thing group targets.
            # By using continuous jobs, devices that join the group receive the job execution even after the job has been created.
            targetSelection="SNAPSHOT",
            jobExecutionsRolloutConfig={
                "maximumPerMinute": 5,
                "exponentialRate": {
                    "baseRatePerMinute": 2,
                    # Acceleration of update jobs deployment (previously was 1.5)
                    "incrementFactor": 3,
                    "rateIncreaseCriteria": {
                        # 'numberOfNotifiedThings': 10,
                        "numberOfSucceededThings": 10
                    },
                },
            },
            abortConfig={
                "criteriaList": [
                    {
                        "failureType": "FAILED",
                        "action": "CANCEL",
                        "thresholdPercentage": 10,
                        "minNumberOfExecutedThings": 5,
                    },
                ]
            },
            timeoutConfig={"inProgressTimeoutInMinutes": MQTT_JOBS_TIMEOUT_SECONDS},
            # tags=[
            #     {
            #         'Key': 'string',
            #         'Value': 'string'
            #     },
            # ]
            # expires=utcnow_naive() + timedelta(days=0.5),
        )
    except Exception:
        current_app.logger.exception(
            f"Error creating v{canpy_version} software update job for gateway '{aws_thing}'"
        )
        raise

    return job_id


def normal_callbacks():
    """Callbacks that run when the user interacts with the page"""

    @callback(
        Output("release_notes_store_canpy_version_current", "data"),
        Output("release_notes_store_gateway", "data"),
        Output("release_notes_store_power_unit_str", "data"),
        Output(
            "release_notes_update_status_card_body", "children", allow_duplicate=True
        ),
        Output("release_notes_update_status_card_header", "children"),
        Input("url", "search"),
        prevent_initial_call=True,
    )
    def store_url_search_params(url_search):
        """Get the gateway and current software version from the query string"""
        if not url_search:
            raise PreventUpdate

        gateway: str = None
        current_version: str = None

        query_str: str = parse.urlsplit(url_search).query
        query_dict: dict = parse.parse_qs(query_str)

        def get_value_from_query_dict(query_dict: dict, key: str) -> str:
            """Get the value from the query dict"""
            value_list: list = query_dict.get(key, None)
            if isinstance(value_list, (list, tuple)):
                value = value_list[0]
            else:
                value = value_list
            return str(value)

        gateway: str = get_value_from_query_dict(query_dict, "gateway")
        current_version: str = get_value_from_query_dict(query_dict, "current_version")
        surface: str = get_value_from_query_dict(query_dict, "surface")
        power_unit_str: str = get_value_from_query_dict(query_dict, "power_unit")

        return (
            current_version,
            gateway,
            power_unit_str,
            html.Div(["Current software version: ", html.Strong(current_version)]),
            f"{surface} Software Status - Power Unit {power_unit_str}",
        )

    @callback(
        Output("release_notes_store_canpy_version_wanted", "data"),
        Output("release_notes_modal_body", "children"),
        Output("release_notes_modal", "is_open", allow_duplicate=True),
        Input({"type": "canpy_update_button", "canpy_version_wanted": ALL}, "n_clicks"),
        # Input({"type": "canpy_update_button", "canpy_version_wanted": ALL}, "value"),
        # State({"type": "canpy_update_button", "canpy_version_wanted": ALL}, "id"),
        prevent_initial_call=True,
    )
    def open_release_notes_confirmation_modal(
        update_button_n_clicks,
        # update_button_values,
        # update_button_id,
    ):
        """Update the status store with the update status"""
        # id_triggered: str = get_id_triggered()
        if not callback_context.triggered:
            raise PreventUpdate

        canpy_version_wanted: float = callback_context.triggered_id.get(
            "canpy_version_wanted", None
        )
        if (
            not canpy_version_wanted
            or not isinstance(canpy_version_wanted, float)
            or canpy_version_wanted < 3.0
        ):
            raise PreventUpdate

        modal_body_msg: str = f"Are you sure you want to update the gateway software to version {canpy_version_wanted}?"

        return str(canpy_version_wanted), modal_body_msg, True

    @callback(
        Output("release_notes_modal", "is_open", allow_duplicate=True),
        Output(
            "release_notes_update_status_card_body", "children", allow_duplicate=True
        ),
        Output("release_notes_refresh_interval", "disabled", allow_duplicate=True),
        Output("release_notes_store_mqtt_job_id", "data"),
        Output(
            "release_notes_update_status_cancel_btn", "disabled", allow_duplicate=True
        ),
        Input("release_notes_modal_cancel_btn", "n_clicks"),
        Input("release_notes_modal_confirm_btn", "n_clicks"),
        State("release_notes_store_canpy_version_current", "data"),
        State("release_notes_store_canpy_version_wanted", "data"),
        State("release_notes_store_gateway", "data"),
        State("release_notes_store_power_unit_str", "data"),
        State("release_notes_store_mqtt_job_id", "data"),
        prevent_initial_call=True,
    )
    def update_shadow_if_modal_confirmed(
        release_notes_modal_cancel_btn_n_clicks,
        release_notes_modal_confirm_btn_n_clicks,
        release_notes_store_canpy_version_current_data: str,
        release_notes_store_canpy_version_wanted_data: str,
        release_notes_store_gateway_data: str,
        release_notes_store_power_unit_str_data: str,
        release_notes_store_mqtt_job_id_data: dict,
    ):
        """Update the shadow if the modal is confirmed"""
        if not callback_context.triggered:
            raise PreventUpdate

        def return_vars(
            release_notes_modal_is_open: bool,
            release_notes_update_status_card_body: str,
            release_notes_refresh_interval_disabled: bool = True,
            release_notes_store_mqtt_job_id_data: dict = no_update,
            release_notes_update_status_cancel_btn_disabled: bool = True,
        ):
            return (
                release_notes_modal_is_open,
                release_notes_update_status_card_body,
                release_notes_refresh_interval_disabled,
                release_notes_store_mqtt_job_id_data,
                release_notes_update_status_cancel_btn_disabled,
            )

        id_triggered: str = get_id_triggered()
        if (
            id_triggered == "release_notes_modal_cancel_btn.n_clicks"
            or id_triggered != "release_notes_modal_confirm_btn.n_clicks"
            or not release_notes_modal_confirm_btn_n_clicks
        ):
            return return_vars(
                release_notes_modal_is_open=False,
                release_notes_update_status_card_body=no_update,
            )

        gateway: str = release_notes_store_gateway_data
        if not gateway:
            raise PreventUpdate

        canpy_version_current: float = 0.0
        canpy_version_wanted: float = 0.0
        try:
            canpy_version_current = float(
                release_notes_store_canpy_version_current_data
            )
            canpy_version_wanted = float(release_notes_store_canpy_version_wanted_data)
        except Exception:
            current_app.logger.exception(
                f"Error converting either canpy_version_current = {canpy_version_current} or canpy_version_wanted = {canpy_version_wanted} to floats"
            )
            raise

        # Get the gateway's current software version
        if not canpy_version_current:
            shadow = get_iot_device_shadow(gateway)
            reported = shadow.get("state", {}).get("reported", {})
            try:
                canpy_version_current: float = float(reported.get("SWV_PYTHON", 0))
            except Exception:
                current_app.logger.exception(
                    f"Error converting canpy_version_current = {canpy_version_current} to float"
                )
                raise

        if os.getenv("FLASK_CONFIG", "development") in ("testing",):
            gateway = "lambda_access"  # test AWS thing

        # SWV_MIN_FOR_UPDATE_BUTTON: float = 3.334022
        card_body: str = "Update in progress..."
        if canpy_version_current >= canpy_version_wanted:
            card_body += f" Note, this gateway is already running version {canpy_version_current}. Updating anyway..."
            # return return_vars(
            #     release_notes_modal_is_open=False,
            #     release_notes_update_status_card_body=f"Gateway is already up to date with version {canpy_version_current}",
            # )

        # Create an AWS MQTT job to update it (the older, first way of updating gateways)
        try:
            job_id: str = create_mqtt_job_update(
                canpy_version=canpy_version_wanted, aws_thing=gateway
            )
        except Exception as err:
            current_app.logger.exception(
                f"Error creating job to update gateway '{gateway}' to version {canpy_version_wanted}"
            )
            return return_vars(
                release_notes_modal_is_open=False,
                release_notes_update_status_card_body=f"Error creating job to update gateway: {err}",
            )

        job_info_dict: dict = {
            "job_id": job_id,
            "canpy_version_wanted": canpy_version_wanted,
            "canpy_version_current": canpy_version_current,
            "power_unit_str": release_notes_store_power_unit_str_data,
        }
        if isinstance(release_notes_store_mqtt_job_id_data, dict):
            release_notes_store_mqtt_job_id_data[gateway] = job_info_dict
        else:
            release_notes_store_mqtt_job_id_data = {gateway: job_info_dict}

        # new_shadow: dict = {"state": {"desired": {"SWV_PYTHON": canpy_version_wanted}}}
        # try:
        #     update_shadow(new_shadow, aws_thing=gateway)
        # except Exception:
        #     current_app.logger.exception(
        #         f"Error updating shadow for gateway '{gateway}' to version {canpy_version_wanted}"
        #     )

        # Record the action in the database
        try:
            power_unit_model = PowerUnit.query.filter_by(
                power_unit_str=release_notes_store_power_unit_str_data
            ).first()
            power_unit_id = getattr(power_unit_model, "id")
        except Exception:
            current_app.logger.exception(
                f"Error getting power unit ID for power unit '{release_notes_store_power_unit_str_data}'"
            )
            power_unit_id = None

        record_remote_control(
            power_unit_id=power_unit_id,
            power_unit_str=release_notes_store_power_unit_str_data,
            aws_thing=gateway,
            metric="SWV_PYTHON",
            value_wanted=canpy_version_wanted,
            action="Request to update canpy Python software on gateway",
        )

        return return_vars(
            release_notes_modal_is_open=False,
            release_notes_update_status_card_body=card_body,
            # Start the interval timer to check the update status
            release_notes_refresh_interval_disabled=False,
            release_notes_store_mqtt_job_id_data=release_notes_store_mqtt_job_id_data,
            release_notes_update_status_cancel_btn_disabled=False,
        )

    @callback(
        Output(
            "release_notes_update_status_card_body", "children", allow_duplicate=True
        ),
        Output(
            "release_notes_update_status_cancel_btn", "disabled", allow_duplicate=True
        ),
        Output("release_notes_refresh_interval", "disabled", allow_duplicate=True),
        Input("release_notes_update_status_refresh_btn", "n_clicks"),
        Input("release_notes_update_status_cancel_btn", "n_clicks"),
        Input("release_notes_refresh_interval", "n_intervals"),
        State("release_notes_store_mqtt_job_id", "data"),
        # State("release_notes_store_canpy_version_wanted", "data"),
        State("release_notes_store_gateway", "data"),
        prevent_initial_call=True,
    )
    def update_release_notes_update_status_card_body(
        release_notes_update_status_refresh_btn_n_clicks,
        release_notes_update_status_cancel_btn_n_clicks,
        release_notes_refresh_interval_n_intervals,
        release_notes_store_mqtt_job_id_data: str,
        # release_notes_store_canpy_version_wanted_data: str,
        release_notes_store_gateway_data: str,
    ):
        """Update the release notes update status card body"""

        def return_vars(
            release_notes_update_status_card_body: str,
            release_notes_update_status_cancel_btn_disabled: bool = True,
            release_notes_refresh_interval_disabled: bool = False,
        ):
            return (
                release_notes_update_status_card_body,
                release_notes_update_status_cancel_btn_disabled,
                release_notes_refresh_interval_disabled,
            )

        shadow: dict = get_iot_device_shadow(release_notes_store_gateway_data)
        canpy_version_current: str = (
            shadow.get("state", {})
            .get("reported", {})
            .get("SWV_PYTHON", "Not sure yet...")
        )
        current_software_version_list: list = [
            "Current software version: ",
            html.Strong(canpy_version_current),
        ]

        # Set default return vars
        release_notes_update_status_cancel_btn_disabled: bool = True
        release_notes_refresh_interval_disabled: bool = False

        if os.getenv("FLASK_CONFIG", "development") in ("testing",):
            release_notes_store_gateway_data = "lambda_access"  # test AWS thing

        job_id: str = None
        canpy_version_wanted: str = None
        if isinstance(release_notes_store_mqtt_job_id_data, dict):
            job_id: str = release_notes_store_mqtt_job_id_data.get(
                release_notes_store_gateway_data, {}
            ).get("job_id", None)
            canpy_version_wanted: str = release_notes_store_mqtt_job_id_data.get(
                release_notes_store_gateway_data, {}
            ).get("canpy_version_wanted", None)

        try:
            # Use describe_job to get the job status
            client = get_boto3_client("iot")
            response = client.describe_job(jobId=job_id)
        except Exception:
            current_app.logger.warning(
                f"Error finding job status for job ID {job_id} for gateway '{release_notes_store_gateway_data}'. Probably no job with this ID exists."
            )
            release_notes_update_status_cancel_btn_disabled = True
            return return_vars(
                release_notes_update_status_card_body=html.Div(
                    [
                        "No update job found",
                        html.Br(),
                        html.Br(),
                        *current_software_version_list,
                    ]
                ),
                release_notes_update_status_cancel_btn_disabled=release_notes_update_status_cancel_btn_disabled,
                release_notes_refresh_interval_disabled=release_notes_refresh_interval_disabled,
            )

        # Extract and print the job status
        job_status = response.get("job", {}).get(
            "status", "Can't find update job status"
        )
        job_status_friendly: str = str(job_status).replace("_", " ").title()

        # https://docs.aws.amazon.com/iot/latest/developerguide/iot-jobs-lifecycle.html
        #     QUEUED	No	Yes	No	Not applicable
        # IN_PROGRESS	Yes	No	No	Not applicable
        # SUCCEEDED	Yes	No	Yes	Not applicable
        # FAILED	Yes	No	Yes	Yes
        # TIMED_OUT	No	Yes	Yes	Yes
        # REJECTED	Yes	No	Yes	No
        # REMOVED	No	Yes	Yes	No
        # CANCELED
        if job_status in (
            "COMPLETED",
            "FAILED",
            "TIMED_OUT",
            "REJECTED",
            "REMOVED",
            "CANCELED",
        ):
            # Can't cancel it if it's already finished somehow
            # client.cancel_job(jobId=job_id)
            client.delete_job(jobId=job_id)
            release_notes_refresh_interval_disabled = True
        # elif job_status in ("CANCELED",):
        #     client.delete_job(jobId=job_id)
        #     release_notes_refresh_interval_disabled = True
        elif job_status in ("IN_PROGRESS",):
            release_notes_update_status_cancel_btn_disabled = False

        # job_timeout: int = response.get("job", {}).get("timeoutConfig", {}).get(
        #     "inProgressTimeoutInMinutes", 0
        # )
        tz_wanted: TimeZone = current_user.time_zones_rel
        tz_wanted_pytz = pytz.timezone(tz_wanted.time_zone)

        # job_expires_in_x_minutes: str = ""
        # job_expires = response.get("job", {}).get("expires", None)
        # if job_expires:
        #     job_expires_in_x_minutes_float: float = round(
        #         (job_expires - utcnow_naive()).total_seconds() / 60, 1
        #     )
        #     job_expires_local: str = utc_datetime_to_string(
        #         dt_utc=job_expires, to_pytz_timezone=tz_wanted_pytz
        #     )
        #     job_expires_in_x_minutes = f"Job expires in {job_expires_in_x_minutes_float} minutes at {job_expires_local}"

        # Get local time
        last_updated_dt: datetime = response.get("job", {}).get("lastUpdatedAt", None)
        date_time_string: str = utc_datetime_to_string(
            dt_utc=last_updated_dt, to_pytz_timezone=tz_wanted_pytz
        )

        return return_vars(
            html.Div(
                [
                    f"Software update status as of {date_time_string}: ",
                    html.Strong(job_status_friendly),
                    html.Br(),
                    html.Br(),
                    "Software version wanted: ",
                    html.Strong(canpy_version_wanted),
                    html.Br(),
                    *current_software_version_list,
                    # html.Br(),
                    # html.Br(),
                    # job_expires_in_x_minutes,
                ]
            ),
            release_notes_update_status_cancel_btn_disabled,
            release_notes_refresh_interval_disabled,
        )

    @callback(
        Output(
            "release_notes_update_status_cancel_btn", "disabled", allow_duplicate=True
        ),
        Input("release_notes_update_status_cancel_btn", "n_clicks"),
        State("release_notes_store_mqtt_job_id", "data"),
        prevent_initial_call=True,
    )
    def cancel_release_notes_update_status(
        release_notes_update_status_cancel_btn_n_clicks,
        release_notes_store_mqtt_job_id_data: str,
    ):
        """Cancel the MQTT job"""

        if (
            not release_notes_update_status_cancel_btn_n_clicks
            or not release_notes_store_mqtt_job_id_data
        ):
            raise PreventUpdate

        try:
            client = get_boto3_client("iot")
            client.cancel_job(jobId=release_notes_store_mqtt_job_id_data)
        except Exception:
            current_app.logger.warning(
                f"Error canceling job with ID {release_notes_store_mqtt_job_id_data}. Probably no job with this ID exists."
            )

        return True

    return (
        open_release_notes_confirmation_modal,
        store_url_search_params,
        update_shadow_if_modal_confirmed,
        update_release_notes_update_status_card_body,
        cancel_release_notes_update_status,
    )


# Callbacks that run when the user interacts with the page
normal_callbacks()
