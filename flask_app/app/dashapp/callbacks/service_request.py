import os
from datetime import date, datetime, timedelta, timezone
from typing import List

import dash_bootstrap_components as dbc
import phonenumbers
from dash import Input, Output, State, callback, dcc, html
from dash.exceptions import PreventUpdate
from flask import current_app, url_for
from flask_login import current_user
from markupsafe import Markup
from shared.models.models import PowerUnit, StructureVw
from shared.models.models_bom import ModelType
from shared.models.models_work_order import (
    Service,
    ServiceEmailee,
    ServiceType,
    WorkOrder,
)
from sqlalchemy import func, text

from app import db, user_is_ijack_employee
from app.auth.forms import validate_email, validate_phone
from app.config import (
    COUNTRY_ID_CANADA,
    CURRENCY_ID_CAD,
    CURRENCY_ID_USD,
    CUSTOMER_ID_IJACK_CORP,
    CUSTOMER_ID_IJACK_INC,
    SERVICE_TYPE_ID_REPAIR,
    USER_ID_SEAN,
)
from app.dashapp.utils import (
    get_id_triggered,
    get_struct_obj_not_demo_customer,
    get_users_by_customer_id,
    log_function_caller,
)
from app.email_stuff import send_email
from app.models.models import Structure, User
from app.utils.complex import run_sql_query

# def format_phone(phone_str: str) -> str:
#     """Format the phone number to (xxx) xxx-xxxx"""
#     AREA_BOUNDARY = 3  # 800.6288737
#     SUBSCRIBER_SPLIT = 6  # 800628.8737

#     if phone_str.startswith("+"):
#         phone_str = phone_str[1:]
#     if phone_str.startswith("1"):
#         phone_str = phone_str[1:]
#     # phone_str = phone_str.removeprefix("+")
#     # phone_str = phone_str.removeprefix("1")  # remove leading +1, or 1
#     phone_str = re.sub("[ ()-]", "", phone_str)  # remove space, (), -

#     assert len(phone_str) == 10
#     phone_str = (
#         f"{phone_str[:AREA_BOUNDARY]}-"
#         f"{phone_str[AREA_BOUNDARY:SUBSCRIBER_SPLIT]}-{phone_str[SUBSCRIBER_SPLIT:]}"
#     )

#     return phone_str


# def validate_phone(phone_str: str) -> str:
#     """Validate the submitted phone number and convert it to E.164 format"""

#     # IJACK users can intentionally enter a null value for the phone number
#     if user_is_ijack_employee(user_id=getattr(current_user, "id", None)) and not phone_str:
#         return phone_str, phone_str

#     try:
#         phone_parsed = phonenumbers.parse(phone_str)
#         if not phonenumbers.is_valid_number(phone_parsed):
#             raise ValueError()
#     except (phonenumbers.phonenumberutil.NumberParseException, ValueError) as err:
#         raise ValueError("Invalid phone number: %s", err)

#     phone_formatted = phonenumbers.format_number(
#         phone_parsed,
#         phonenumbers.PhoneNumberFormat.E164,
#     )
#     return phone_formatted


def get_service_request_row(is_ijack_user: bool):
    """For the main layout, get the notes row/textarea at the bottom"""

    return dbc.Row(
        # All users can now submit service requests
        # style={} if is_ijack_user else {"display": "none"},
        class_name="mt-4",
        justify="center",
        children=dbc.Col(
            xs=12,
            xxl=10,
            children=dbc.Card(
                [
                    dbc.CardHeader("Service Request"),
                    dbc.CardBody(
                        [
                            dbc.Row(
                                dbc.Col(
                                    [
                                        dbc.Label(
                                            "Service Type",
                                            html_for="service_request_service_type",
                                            class_name="mb-1",
                                        ),
                                        dbc.Select(
                                            id="service_request_service_type",
                                            required=True,
                                            value=SERVICE_TYPE_ID_REPAIR,
                                        ),
                                        # dbc.FormText("Type of service"),
                                    ],
                                ),
                            ),
                            html.Div(
                                id="service_request_operator_info_div",
                                # Only show this row if it's an IJACK employee
                                style={"display": "none"},
                                children=[
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                xs=12,
                                                md=6,
                                                children=[
                                                    dcc.Store(
                                                        id="service_request_operator_ids_store",
                                                        data=[],
                                                    ),
                                                    dbc.Label(
                                                        "Operator Dropdown",
                                                        html_for="service_request_operator_dropdown",
                                                        class_name="mt-3 mb-1",
                                                    ),
                                                    dcc.Dropdown(
                                                        id="service_request_operator_dropdown",
                                                        # type="string",
                                                        placeholder="Please select an operator",
                                                    ),
                                                ],
                                            ),
                                            dbc.Col(
                                                xs=12,
                                                md=6,
                                                children=[
                                                    dbc.Label(
                                                        "Operator Manual Entry",
                                                        html_for="service_request_operator_name",
                                                        class_name="mt-3 mb-1",
                                                    ),
                                                    dbc.Input(
                                                        id="service_request_operator_name",
                                                        type="string",
                                                        placeholder="Operator Name",
                                                    ),
                                                ],
                                            ),
                                        ]
                                    ),
                                    dbc.Row(
                                        [
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Operator Email",
                                                        html_for="service_request_operator_email",
                                                        class_name="mt-3 mb-1",
                                                    ),
                                                    dbc.Input(
                                                        id="service_request_operator_email",
                                                        type="string",
                                                        placeholder="<EMAIL>",
                                                    ),
                                                ],
                                                md=6,
                                            ),
                                            dbc.Col(
                                                [
                                                    dbc.Label(
                                                        "Operator Phone",
                                                        html_for="service_request_operator_phone",
                                                        class_name="mt-3 mb-1",
                                                    ),
                                                    dbc.Input(
                                                        id="service_request_operator_phone",
                                                        type="string",
                                                        placeholder="************",
                                                    ),
                                                ],
                                                md=6,
                                            ),
                                        ],
                                    ),
                                ],
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        [
                                            dbc.Label(
                                                "Details",
                                                html_for="service_request_textarea",
                                                class_name="mt-3 mb-1",
                                            ),
                                            dbc.Textarea(
                                                id="service_request_textarea",
                                                size="lg",
                                                title="Enter details of the service request here",
                                                placeholder="Enter details of the service request here",
                                                wrap=True,
                                            ),
                                            dbc.Button(
                                                [
                                                    html.I(
                                                        className="fa-solid fa-paper-plane me-1"
                                                    ),
                                                    "Submit Service Request",
                                                ],
                                                id="btn_submit_service_request",
                                                class_name="mt-3 me-2",
                                            ),
                                            dbc.Button(
                                                [
                                                    html.I(
                                                        className="fa-solid fa-save me-1"
                                                    ),
                                                    "Create Work Order",
                                                ],
                                                id="btn_create_work_order",
                                                # Only IJACK employees can create work orders
                                                style={}
                                                if is_ijack_user
                                                else {"display": "none"},
                                                class_name="mt-3",
                                            ),
                                        ],
                                    ),
                                ]
                            ),
                            dbc.Row(
                                dbc.Col(
                                    dbc.FormText(
                                        "",
                                        id="btn_submit_service_request_form_text",
                                    ),
                                )
                            ),
                        ]
                    ),
                ]
            ),
        ),
    )


def get_html_body(
    customer: str,
    power_unit_str: str,
    structure: str,
    location: str,
    description: str,
    service_type_name: int,
    operator_name: str,
    operator_phone: str,
    operator_email: str,
    work_order_url: str = None,
    work_order_delete_url: str = None,
) -> str:
    """Get the html body for the email"""

    if current_user.is_authenticated:
        current_user_str = (
            f"{current_user.first_name} {current_user.last_name} ({current_user.email})"
        )
    else:
        current_user_str = "Anonymous user"

    work_order_url_str = ""
    work_order_delete_url_str = ""
    if work_order_url:
        work_order_url_str = Markup(
            f"Work order started here: <a href='https://myijack.com{work_order_url}'>myijack.com{work_order_url}</a>"
        )
    if work_order_delete_url:
        work_order_delete_url_str = Markup(
            f"Delete the work order <a href='https://myijack.com{work_order_delete_url}'>here</a>"
        )

    html_body = f"""
<html>
    <body>
        <p>
<strong>New service request:</strong><br>
Created by: {current_user_str}<br>
Customer: {customer}<br>
Power unit: {power_unit_str}<br>
Structure: {structure}<br>
Location: {location}<br>
Service type: {service_type_name}<br>
Description: {description}<br>
Operator name: {operator_name}<br>
Operator phone: {operator_phone}<br>
Operator email: {operator_email}<br>
{work_order_url_str}<br>
{work_order_delete_url_str}
        </p>
    </body>
</html>
"""
    return html_body


def get_text_body(
    customer: str,
    power_unit_str: str,
    structure: str,
    location: str,
    description: str,
    service_type_name: str,
    operator_name: str,
    operator_phone: str,
    operator_email: str,
    work_order_url: str = None,
    work_order_delete_url: str = None,
) -> str:
    """Get the text body for the email"""

    if current_user.is_authenticated:
        current_user_str = (
            f"{current_user.first_name} {current_user.last_name} ({current_user.email})"
        )
    else:
        current_user_str = "Anonymous user"

    work_order_url_str = ""
    work_order_delete_url_str = ""
    if work_order_url:
        work_order_url_str = Markup(
            f"Work order started here: https://myijack.com{work_order_url}"
        )
    if work_order_delete_url:
        work_order_delete_url_str = Markup(
            f"Delete the work order here: https://myijack.com{work_order_delete_url}"
        )

    text_body = f"""
New service request:\n
Created by: {current_user_str})\n
Customer: {customer}\n
Power unit: {power_unit_str}\n
Structure: {structure}\n
Location: {location}\n
Service type: {service_type_name}\n
Description: {description}\n
Operator name: {operator_name}\n
Operator phone: {operator_phone}\n
Operator email: {operator_email}\n
{work_order_url_str}\n
{work_order_delete_url_str}
"""
    return text_body


def get_notify_service_requests_list(customer_id: int, cust_sub_group_id: int) -> list:
    """Get other email to_emails who want to be CC'd on service requests"""

    # if not customer_id or not cust_sub_group_id:
    #     return []

    if not cust_sub_group_id:
        rows1 = []
    else:
        sql_get_cust_sub_group_subscriptions = text("""
            select t2.email
            from public.user_cust_sub_group_notify_service_requests_rel t1
            join public.users t2 on t1.user_id = t2.id
            where t1.cust_sub_group_id = :cust_sub_group_id
        """).bindparams(cust_sub_group_id=cust_sub_group_id)
        rows1, _ = run_sql_query(
            sql_get_cust_sub_group_subscriptions, db_name="ijack", as_dict=True
        )

    sql_get_other_recipients = text("""
        select email
        from public.users
        where customer_id = :customer_id
            and notify_service_requests = true
    """).bindparams(customer_id=customer_id)

    rows2, _ = run_sql_query(sql_get_other_recipients, db_name="ijack", as_dict=True)

    rows = rows1 + rows2

    return [row["email"] for row in rows]


def get_email_recipients_list(customer_id: int, cust_sub_group_id: int) -> list:
    """Get the list of email to_emails (we can mock this function when testing)"""
    if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl", "testing"):
        filters_list = [ServiceEmailee.user_id == USER_ID_SEAN]
    else:
        filters_list = []

    # IJACK people who need to know about all service requests
    users_to_email = (
        db.session.query(ServiceEmailee)
        .join(User)
        .with_entities(User.email)
        .filter(*filters_list)
        .all()
    )
    recipients_set = set([user[0] for user in users_to_email])

    if customer_id or cust_sub_group_id:
        # Add other users who want to be CC'd when there are service requests for their company's units
        other_emails: list = get_notify_service_requests_list(
            customer_id, cust_sub_group_id
        )
        recipients_set.update(other_emails)

    return list(recipients_set)


def service_request_send_email(
    customer: str,
    customer_id: int,
    cust_sub_group_id: int,
    power_unit_str: str,
    structure: str,
    location: str,
    description: str,
    service_type_name: str,
    operator_name: str,
    operator_phone: str,
    operator_email: str,
    work_order_url: str = None,
    work_order_delete_url: str = None,
) -> list:
    """Send an email to the service team when a new service request is submitted"""

    html_body = get_html_body(
        customer=customer,
        power_unit_str=power_unit_str,
        structure=structure,
        location=location,
        description=description,
        service_type_name=service_type_name,
        operator_name=operator_name,
        operator_phone=operator_phone,
        operator_email=operator_email,
        work_order_url=work_order_url,
        work_order_delete_url=work_order_delete_url,
    )
    text_body = get_text_body(
        customer=customer,
        power_unit_str=power_unit_str,
        structure=structure,
        location=location,
        description=description,
        service_type_name=service_type_name,
        operator_name=operator_name,
        operator_phone=operator_phone,
        operator_email=operator_email,
        work_order_url=work_order_url,
        work_order_delete_url=work_order_delete_url,
    )
    # Get the list of email to_emails, mostly from public.service_emailees table,
    # but also include other users who want to be CC'd on service requests for their company.
    to_emails = get_email_recipients_list(customer_id, cust_sub_group_id)

    send_email(
        # From public.service_emailees table
        to_emails=to_emails,
        subject="New Service Request",
        sender="<EMAIL>",
        text_body=text_body,
        html_body=html_body,
        files_list=None,
        testing=False,
    )
    return to_emails


def get_name_email_phone_structure_id(
    is_ijack_employee: bool,
    operator_full_name: str,
    operator_phone: str,
    operator_email: str,
    structure_id: int,
) -> tuple:
    """Get the name, email, phone, and structure_id for the service request form"""

    if is_ijack_employee or not current_user.is_authenticated:
        # Only IJACK employees fill out these two form fields in the RCOM service request form,
        # but on the "/contact/" page, the user may not be logged in, so the fields are hidden.
        # The fields are hidden for non-IJACK employees.
        operator_name = operator_full_name
        operator_email = operator_email
        phone_parsed = phonenumbers.parse(operator_phone, "US")
    else:
        # Use the logged in user's name and phone number
        operator_name = f"{current_user.first_name} {current_user.last_name}"
        operator_email = current_user.email
        phone_parsed = phonenumbers.parse(current_user.phone, "US")

    phone_international = phonenumbers.format_number(
        phone_parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
    )
    phone_e164 = phonenumbers.format_number(
        phone_parsed, phonenumbers.PhoneNumberFormat.E164
    )

    null_values = ("", None, "0", 0)
    structure_id_final = None if structure_id in null_values else structure_id

    return (
        operator_name,
        operator_email,
        phone_international,
        phone_e164,
        structure_id_final,
    )


def save_work_order(
    structure_id: int,
    service_type_id: int,
    description: str,
    operator_name: str,
    operator_email: str,
    phone_international: str,
    user_id: int | None,
) -> WorkOrder:
    """Create and save a new work order"""

    structure_model: Structure = db.session.get(Structure, structure_id)
    if not structure_model:
        raise ValueError(f"No structure found for structure_id '{structure_id}'")

    power_unit_model: PowerUnit = getattr(structure_model, "power_units_rel", None)
    model_type_model: ModelType = getattr(structure_model, "model_types_rel", None)

    # Figure out if it should be a US or Canadian work order
    customers_rel_model = getattr(structure_model, "customers_rel", None)
    if isinstance(customers_rel_model, list) and len(customers_rel_model) > 0:
        country_id = getattr(customers_rel_model[0], "country_id", COUNTRY_ID_CANADA)
        if country_id == COUNTRY_ID_CANADA:
            creator_company_id = CUSTOMER_ID_IJACK_INC
            currency_id = CURRENCY_ID_CAD
        else:
            creator_company_id = CUSTOMER_ID_IJACK_CORP
            currency_id = CURRENCY_ID_USD
    else:
        creator_company_id = CUSTOMER_ID_IJACK_INC
        currency_id = CURRENCY_ID_CAD
        country_id = COUNTRY_ID_CANADA

    if not isinstance(user_id, int):
        # Try to find the user in the database
        user_model = (
            db.session.query(User)
            .filter(
                func.lower(User.full_name) == str(operator_name).strip().lower(),
                func.trim(User.phone) == str(phone_international).strip(),
                func.lower(User.email) == str(operator_email).strip().lower(),
            )
            .first()
        )
        if user_model:
            user_id = user_model.id

    work_order = WorkOrder(
        service_required=description,
        structures_rel=[structure_model] if structure_model else [],
        power_units_rel=[power_unit_model] if power_unit_model else [],
        model_types_rel=[model_type_model] if model_type_model else [],
        # Don't need unit_types_rel if we have the model type
        # unit_types_rel=[],
        location=structure_model.surface,
        service_type_id=service_type_id,
        creator_id=user_id,
        # IJACK Inc or IJACK Corp
        creator_company_id=creator_company_id,
        country_id=country_id,
        # Could be CAD or USD
        currency_id=currency_id,
        requested_by_id=user_id,
        approved_by_id=user_id,
        # Default two weeks from now
        date_service=date.today() + timedelta(days=14),
        # Required fields
        customer_id=structure_model.get_main_customer_id(),
        sales_tax_rate=0.0,
        discount_pct=0.0,
        sales_tax=0.0,
        subtotal=0.0,
        total=0.0,
    )
    db.session.add(work_order)
    db.session.commit()
    # Flush so the returned work_order object has the id
    db.session.flush()
    db.session.refresh(work_order)
    return work_order


def submit_work_order(
    operator_full_name: str,
    operator_phone: str,
    operator_email: str,
    description: str,
    service_type_id: int,
    structure_id: int,
    user_id: int,
) -> List[html.P]:
    """Submit a work order"""
    is_ijack_employee: bool = user_is_ijack_employee(
        user_id=getattr(current_user, "id", None)
    )
    if not is_ijack_employee:
        return [
            html.P(
                "Only IJACK employees can create work orders.",
                style={"color": "red"},
            )
        ]

    if not operator_phone:
        operator_phone = current_user.phone
        if not operator_phone:
            return [
                html.P(
                    f"User '{current_user.first_name} {current_user.last_name}' does not have a phone number in RCOM, and no phone number was provided, so a work order can't be created! Please add a phone number for the user.",
                    style={"color": "red"},
                )
            ]
    try:
        (
            operator_name,
            operator_email,
            phone_international,
            _,
            structure_id_final,
        ) = get_name_email_phone_structure_id(
            is_ijack_employee=is_ijack_employee,
            operator_full_name=operator_full_name,
            operator_phone=operator_phone,
            operator_email=operator_email,
            structure_id=structure_id,
        )

        work_order = save_work_order(
            structure_id=structure_id_final,
            service_type_id=service_type_id,
            description=description,
            operator_name=operator_name,
            operator_email=operator_email,
            phone_international=phone_international,
            user_id=user_id,
        )
    except Exception as err:
        db.session.rollback()
        msg = "ERROR inserting work order into database:"
        current_app.logger.error(msg)
        return [
            html.P(msg, style={"color": "red"}),
            html.P(err, style={"color": "red"}),
            html.P("Please try again or contact support.", style={"color": "red"}),
        ]

    work_order_id = work_order.id
    if work_order.creator_company_id == CUSTOMER_ID_IJACK_CORP:
        endpoint = "work_orders_corp"
    else:
        endpoint = "work_orders"
    work_order_url = url_for(f"{endpoint}.edit_view", id=work_order_id)
    return [
        html.P(
            [
                "Work order started ",
                html.A(href=work_order_url, children="here"),
                f" with ID {work_order_id}",
            ]
        )
    ]


def submit_service_request(
    operator_full_name: str,
    operator_phone: str,
    operator_email: str,
    description: str,
    service_type_id: int,
    structure_id: int,
    user_id: int,
    create_work_order: bool = False,
) -> str | List[html.P]:
    """Submit service request, and optionally create a work order"""

    is_ijack_employee: bool = user_is_ijack_employee(
        user_id=getattr(current_user, "id", None)
    )
    try:
        (
            operator_name,
            operator_email,
            phone_international,
            phone_e164,
            structure_id_final,
        ) = get_name_email_phone_structure_id(
            is_ijack_employee=is_ijack_employee,
            operator_full_name=operator_full_name,
            operator_phone=operator_phone,
            operator_email=operator_email,
            structure_id=structure_id,
        )

        service = Service(
            description=description,
            structure_id=structure_id_final,
            service_type_id=service_type_id,
            user_id=user_id,
            operator_name=operator_name,
            operator_phone=phone_e164,
            operator_email=operator_email,
        )
        db.session.add(service)
        db.session.commit()
    except Exception as err:
        db.session.rollback()
        msg = f"ERROR inserting new service request into database: {err}."
        current_app.logger.error(msg)
        return [
            html.P(msg, style={"color": "red"}),
            html.P("Please try again or contact support.", style={"color": "red"}),
        ]

    service_type = db.session.get(ServiceType, service_type_id)
    if service_type:
        service_type_name = service_type.name
    else:
        service_type_name = None

    children_to_return = [
        html.P(
            f"Service request submitted successfully at {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')} UTC time"
        )
    ]

    work_order: WorkOrder = None
    if create_work_order and structure_id_final:
        try:
            work_order = save_work_order(
                structure_id=structure_id_final,
                service_type_id=service_type_id,
                description=description,
                operator_name=operator_name,
                operator_email=operator_email,
                phone_international=phone_international,
                user_id=user_id,
            )
        except Exception as err:
            db.session.rollback()
            msg = f"ERROR inserting work order into database: {err}."
            current_app.logger.error(msg)
            return [
                html.P(msg, style={"color": "red"}),
                html.P("Please try again or contact support.", style={"color": "red"}),
            ]

    work_order_url: str = None
    work_order_list_url: str = None
    if work_order:
        db.session.flush()
        db.session.refresh(work_order)
        if work_order.creator_company_id == CUSTOMER_ID_IJACK_CORP:
            endpoint = "work_orders_corp"
        else:
            endpoint = "work_orders"
        work_order_url = url_for(f"{endpoint}.edit_view", id=work_order.id)
        work_order_list_url = url_for(
            f"{endpoint}.index_view", flt1_work_order_id_equals=work_order.id
        )

    if is_ijack_employee and work_order_url:
        children_to_return.append(
            html.P(
                [
                    "Work order started ",
                    html.A(href=work_order_url, children="here"),
                ]
            )
        )
        children_to_return.append(
            html.P(
                [
                    "Delete the work order ",
                    html.A(href=work_order_list_url, children="here"),
                ]
            )
        )

    structure_obj: StructureVw | None = get_struct_obj_not_demo_customer(
        structure_id=structure_id_final
    )
    if not structure_obj:
        raise ValueError(f"No structure found for structure_id '{structure_id}'")

    # Send an email to the service department
    recipients_list: list = service_request_send_email(
        customer=structure_obj.customer,
        customer_id=structure_obj.customer_id,
        cust_sub_group_id=structure_obj.cust_sub_group_id,
        power_unit_str=structure_obj.power_unit_str,
        structure=structure_obj.structure,
        location=structure_obj.location,
        description=description,
        service_type_name=service_type_name,
        operator_name=operator_name,
        operator_phone=phone_international,
        operator_email=operator_email,
        work_order_url=work_order_url,
        work_order_delete_url=work_order_list_url,
    )
    recipients_list_str: str = ", ".join(recipients_list)
    children_to_return.append(
        html.P(f"The following people have been emailed: {recipients_list_str}")
    )
    return children_to_return


@callback(
    Output("service_request_service_type", "options"),
    Input("hidden_signal", "children"),
)
def get_service_type_options(_) -> list:
    """Get the options for the select dropdown"""
    options_list: list = [
        {"label": st.name, "value": st.id} for st in ServiceType.query.all()
    ]
    return options_list


@callback(
    Output("service_request_operator_dropdown", "options"),
    Input("service_request_operator_ids_store", "data"),
    State("store_customer_id", "data"),
    prevent_initial_call=True,
)
def get_operator_dropdown_options(
    service_request_operator_ids_store,
    store_customer_id_data,
) -> List[dict]:
    """Get the options for the operator dropdown"""
    log_function_caller()

    if (
        not isinstance(service_request_operator_ids_store, list)
        or not store_customer_id_data
    ):
        raise PreventUpdate()

    # This function is cached, so it's not a big deal to call it multiple times
    users: list = get_users_by_customer_id(store_customer_id_data)

    operator_options: list = []
    other_user_options: list = []
    for user in users:
        option = {"label": f"{user.first_name} {user.last_name}", "value": user.id}
        if user.id in service_request_operator_ids_store:
            # These operators will go at the top of the dropdown
            option["label"] = f"{option['label']} (Operator)"
            operator_options.append(option)
        else:
            other_user_options.append(option)

    service_request_operator_dropdown_options: list = (
        operator_options + other_user_options
    )
    return service_request_operator_dropdown_options


@callback(
    Output("service_request_operator_name", "value"),
    Output("service_request_operator_phone", "value", allow_duplicate=True),
    Output("service_request_operator_email", "value"),
    Input("service_request_operator_dropdown", "value"),
    prevent_initial_call=True,
)
def get_operator_info(service_request_operator_dropdown_value: int):
    """Get the operator info when the dropdown changes"""
    log_function_caller()

    if not isinstance(service_request_operator_dropdown_value, int):
        # This is a manual entry, so return empty strings
        raise PreventUpdate()

    user = db.session.get(User, service_request_operator_dropdown_value)
    name = f"{user.first_name} {user.last_name}"
    phone = user.phone
    email = user.email

    return name, phone, email


@callback(
    Output("btn_submit_service_request_form_text", "children"),
    Output("btn_submit_service_request_form_text", "color"),
    Output("modal_message_header", "children", allow_duplicate=True),
    Output("modal_message_body", "children", allow_duplicate=True),
    Output("modal_message", "is_open", allow_duplicate=True),
    Output("modal_message", "backdrop", allow_duplicate=True),
    Input("btn_submit_service_request", "n_clicks"),
    Input("btn_create_work_order", "n_clicks"),
    State("store_structure_id", "data"),
    State("service_request_textarea", "value"),
    State("service_request_service_type", "value"),
    State("service_request_operator_name", "value"),
    State("service_request_operator_phone", "value"),
    State("service_request_operator_email", "value"),
    prevent_initial_call=True,
)
def submit_service_request_w_button(
    btn_submit_service_request_n_clicks,
    btn_create_work_order_n_clicks,
    store_structure_id_data,
    service_request_textarea_value,
    service_request_service_type_value,
    service_request_operator_name_value,
    service_request_operator_phone_value,
    service_request_operator_email_value,
):
    """Submit service request"""
    log_function_caller()

    if not (btn_submit_service_request_n_clicks or btn_create_work_order_n_clicks):
        raise PreventUpdate()

    def return_vars(
        btn_submit_service_request_form_text_children: str,
        btn_submit_service_request_form_text_color: str,
        modal_message_header_children: str = None,
        modal_message_body_children: str = None,
        modal_message_is_open: bool = True,
        modal_message_backdrop: bool = True,
    ):
        """Default return variables"""
        modal_message_header_children = (
            modal_message_header_children if modal_message_header_children else "Info"
        )
        modal_message_body_children = (
            modal_message_body_children
            if modal_message_body_children
            else btn_submit_service_request_form_text_children
        )
        return (
            btn_submit_service_request_form_text_children,
            btn_submit_service_request_form_text_color,
            modal_message_header_children,
            modal_message_body_children,
            modal_message_is_open,
            modal_message_backdrop,
        )

    id_triggered: str = get_id_triggered()
    is_ijack_employee: bool = user_is_ijack_employee(
        user_id=getattr(current_user, "id", None)
    )
    if id_triggered == "btn_submit_service_request.n_clicks":
        if is_ijack_employee:
            # IJACK employees have more fields to fill out, which need validating
            if operator_name_invalid(1, 1, service_request_operator_name_value):
                return return_vars("Please fix the name field", "danger", "Error")
            if email_address_invalid(1, 1, service_request_operator_email_value):
                return return_vars("Please fix the email field", "danger", "Error")
            _, phone_invalid = phone_number_invalid(
                1, 1, service_request_operator_phone_value
            )
            if phone_invalid:
                return return_vars("Please fix the phone field", "danger", "Error")

        if service_request_description_invalid(1, 1, service_request_textarea_value):
            return return_vars("Please fix the description field", "danger", "Error")

        try:
            result = submit_service_request(
                operator_full_name=service_request_operator_name_value,
                operator_phone=service_request_operator_phone_value,
                operator_email=service_request_operator_email_value,
                description=service_request_textarea_value,
                service_type_id=service_request_service_type_value,
                structure_id=store_structure_id_data,
                user_id=getattr(current_user, "id", None),
                # IJACK employees have a dedicated button for creating the work order and service request separately
                create_work_order=False if is_ijack_employee else True,
            )
        except Exception as err:
            return return_vars(
                f"ERROR submitting service request: {err}", "danger", "Error"
            )
        else:
            return return_vars(result, "success", "Info")

    elif id_triggered == "btn_create_work_order.n_clicks":
        if service_request_operator_name_value and operator_name_invalid(
            1, 1, service_request_operator_name_value
        ):
            return return_vars("Please fix the name field", "danger", "Error")
        if service_request_operator_email_value and email_address_invalid(
            1, 1, service_request_operator_email_value
        ):
            return return_vars("Please fix the email field", "danger", "Error")
        if service_request_operator_phone_value:
            _, phone_invalid = phone_number_invalid(
                1, 1, service_request_operator_phone_value
            )
            if phone_invalid:
                return return_vars("Please fix the phone field", "danger", "Error")
        if service_request_textarea_value and service_request_description_invalid(
            1, 1, service_request_textarea_value
        ):
            return return_vars("Please fix the description field", "danger", "Error")
        try:
            result = submit_work_order(
                operator_full_name=service_request_operator_name_value,
                operator_phone=service_request_operator_phone_value,
                operator_email=service_request_operator_email_value,
                description=service_request_textarea_value,
                service_type_id=service_request_service_type_value,
                structure_id=store_structure_id_data,
                user_id=getattr(current_user, "id", None),
            )
        except Exception as err:
            return return_vars(f"ERROR creating work order: {err}", "danger", "Error")
        else:
            return return_vars(result, "success", "Info")

    raise PreventUpdate()


@callback(
    Output("service_request_operator_email", "invalid"),
    Input("service_request_operator_email", "n_blur"),
    Input("btn_submit_service_request", "n_clicks"),
    State("service_request_operator_email", "value"),
    prevent_initial_call=True,
)
def email_address_invalid(
    service_request_operator_email_n_blur,
    _,
    service_request_operator_email_value,
):
    """Validate the email address"""
    log_function_caller()

    if not service_request_operator_email_value:
        return True

    if not service_request_operator_email_n_blur:
        raise PreventUpdate()

    try:
        validate_email(
            form=None,
            field=service_request_operator_email_value,
            must_be_unique=False,
        )
    except Exception as err:
        current_app.logger.error(err)
        return True

    return False


@callback(
    Output("service_request_operator_phone", "value", allow_duplicate=True),
    Output("service_request_operator_phone", "invalid"),
    Input("service_request_operator_phone", "n_blur"),
    Input("btn_submit_service_request", "n_clicks"),
    State("service_request_operator_phone", "value"),
    prevent_initial_call=True,
)
def phone_number_invalid(
    service_request_operator_phone_n_blur, _, service_request_operator_phone_value
):
    """Validate the phone number"""
    log_function_caller()

    if service_request_operator_phone_n_blur:
        # if (
        #     len(service_request_operator_phone_value) < 10
        #     or not str(service_request_operator_phone_value).isnumeric()
        # ):
        #     return service_request_operator_phone_value, False, True

        # if len(service_request_operator_phone_value) == 10:
        #     service_request_operator_phone_value = (
        #         f"+1{service_request_operator_phone_value}"
        #     )
        try:
            validate_phone(
                form=None,
                field=service_request_operator_phone_value,
                must_be_unique=False,
            )
            phone_parsed = phonenumbers.parse(
                service_request_operator_phone_value, region="US"
            )
            phone_international = phonenumbers.format_number(
                phone_parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
            )
            # phone_validated = validate_phone(service_request_operator_phone_value)
            # phone_formatted = format_phone(phone_validated)
        except Exception as err:
            current_app.logger.error(err)
            return service_request_operator_phone_value, True
        else:
            return phone_international, False

    return service_request_operator_phone_value, None


# On blur, validate the operator name
@callback(
    Output("service_request_operator_name", "invalid"),
    Input("service_request_operator_name", "n_blur"),
    Input("btn_submit_service_request", "n_clicks"),
    State("service_request_operator_name", "value"),
    prevent_initial_call=True,
)
def operator_name_invalid(
    service_request_operator_name_n_blur, _, service_request_operator_name_value
):
    """Validate the operator name"""
    log_function_caller()

    if service_request_operator_name_n_blur:
        if service_request_operator_name_value:
            return False
        else:
            return True

    return None


# On blur, validate the service request description
@callback(
    Output("service_request_textarea", "invalid"),
    Input("service_request_textarea", "n_blur"),
    Input("btn_submit_service_request", "n_clicks"),
    State("service_request_textarea", "value"),
    prevent_initial_call=True,
)
def service_request_description_invalid(
    service_request_textarea_n_blur, _, service_request_textarea_value
):
    """Validate the service request description"""
    log_function_caller()

    if service_request_textarea_n_blur:
        if (
            isinstance(service_request_textarea_value, str)
            and len(service_request_textarea_value) >= 2
        ):
            return False
        else:
            return True

    return None
