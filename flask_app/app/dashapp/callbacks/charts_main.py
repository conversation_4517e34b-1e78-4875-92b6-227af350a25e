from datetime import date, datetime, timedelta
from typing import OrderedDict

import dash_bootstrap_components as dbc
import pandas as pd
import plotly.graph_objects as go
import pytz
from dash import Input, Output, State, callback, dcc, html, no_update
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import StructureVw, UserChartPreference, UserChartToggle

from app import db, is_admin, user_is_demo_customer, user_is_ijack_employee
from app.config import (
    ACTIVE_TAB_NORMAL_CHARTS,
    ACTIVE_TAB_RT_CHARTS,
    ADMIN_EMAILS,
    STRUCTURE_ID_SHOP,
    TAB_CHARTS,
    TAB_UNOGAS_UNO,
    UNIT_TYPE_ID_DGAS,
    UNIT_TYPE_ID_EGAS,
    UNIT_TYPE_ID_GATEWAYS,
    UNIT_TYPE_ID_UNO,
    UNIT_TYPE_ID_UNOGAS,
    UNIT_TYPE_ID_VRU,
    UNIT_TYPE_ID_XFER,
    UNIT_TYPES_IDS_XFER_EGAS,
)
from app.dashapp.callbacks.charts_real_time import get_main_rt_chart_row
from app.dashapp.callbacks.charts_utils import get_time_series_dataframe
from app.dashapp.layout_utils import (
    create_dcc_date_picker_range,
    get_card_header_with_spinner,
)
from app.dashapp.metrics import (
    bootstrap_colours,
    categories_dict_uno,
    categories_dict_uno_rt,
    categories_dict_uno_rt_sl,
    categories_dict_uno_sl,
    categories_dict_xfer_egas,
    categories_dict_xfer_egas_rt,
    categories_dict_xfer_egas_rt_sl,
    categories_dict_xfer_egas_sl,
    categories_list_egas_only,
    categories_list_egas_only_rt,
    categories_list_uno_unogas,
    categories_list_uno_unogas_rt,
    categories_list_xfer_only,
    categories_list_xfer_only_rt,
    categories_options_egas_only,
    categories_options_egas_only_rt,
    categories_options_xfer_only,
    categories_options_xfer_only_rt,
    category_default_items_dict_uno,
    category_default_items_dict_uno_rt,
    category_default_items_dict_xfer_egas,
    category_default_items_dict_xfer_egas_rt,
    defaults_uno_unogas,
    defaults_xfer_egas,
    get_labels,
    names_uno_unogas,
    names_xfer_egas,
    uno_unogas_categories_options,
    uno_unogas_categories_options_rt,
)
from app.dashapp.utils import (
    get_id_triggered,
    get_structure_obj,
    get_unit_type,
    log_function_caller,
)
from app.email_stuff import send_email
from shared.utils.datetime_utils import utcnow_naive


def get_chart_categories_items_column() -> dbc.Col:
    """Second column in main graph row, for categories/items"""
    return dbc.Col(
        lg=3,
        children=[
            dbc.Card(
                [
                    dbc.CardHeader(
                        "Categories",
                        # style={"color": "#717174"}
                    ),
                    dbc.CardBody(
                        [
                            dbc.RadioItems(
                                id="category_to_chart",
                                # (dict; optional): The style of the <label> that wraps the checkbox input and the option's label
                                label_style=dict(display="block"),
                                # label_class_name='', # (string; default ''): The class of the <label> that wraps the checkbox input and the option's label
                                persistence=True,
                                persistence_type="local",
                            ),
                        ],
                    ),
                ],
            ),
            # Items checklist, below the categories radio buttons
            dbc.Card(
                [
                    dbc.CardHeader(
                        "Category Items",
                    ),
                    dbc.CardBody(
                        [
                            # Whether to show all items instead of just the ones in the category chosen
                            # dbc.Spinner(
                            #     color="success",
                            #     children=dbc.Checklist(
                            dbc.Checklist(
                                id="category_items_options",
                                label_style=dict(display="block"),
                                persistence=True,
                                persistence_type="local",
                                switch=True,
                                value=[],
                                options=[
                                    {
                                        "label": "Use MCF not e3m3",
                                        "value": "cf_not_m3",
                                    },
                                    {
                                        "label": "Use BBL not m3",
                                        "value": "barrels_not_m3",
                                    },
                                    {
                                        "label": "Use kPa not PSI",
                                        "value": "kpa_not_psi",
                                    },
                                    {
                                        "label": "Use Fahrenheit not Celsius",
                                        "value": "fahrenheit_not_celsius",
                                    },
                                    {
                                        "label": "Use oz/in² for suction",
                                        "value": "oz_per_inch2_for_suction",
                                    },
                                    {
                                        "label": "Show all items",
                                        "value": "show_all",
                                    },
                                ],
                                # ),
                            ),
                            html.Hr(className="my-2"),
                            # Items to chart (checklist), based on unit type
                            dbc.Spinner(
                                dbc.Checklist(
                                    id="items_to_chart",
                                    label_style=dict(display="block"),
                                    # persistence=True,
                                    # persistence_type="local",
                                ),
                                color="success",
                            ),
                        ],
                    ),
                ],
                style={"margin-top": "0.5rem"},
            ),
        ],
    )


def create_chart_detail_radios():
    """Create radio buttons for the level of detail in the chart"""
    return dbc.Row(
        dbc.Col(
            [
                "Granularity",
                dbc.RadioItems(
                    id="ts_chart_detail_level_radio",
                    inline=True,
                    label_style=dict(display="block"),
                    options=[
                        {"label": "Auto", "value": "auto"},
                        {"label": "24h", "value": "24h"},
                        {"label": "6h", "value": "6h"},
                        {"label": "3h", "value": "3h"},
                        {"label": "1h", "value": "1h"},
                        {"label": "20m", "value": "20m"},
                        {"label": "2m", "value": "2m"},
                    ],
                    value="auto",
                ),
            ],
            width="auto",
            className="text-center",
        ),
        justify="center",
        id="ts_chart_detail_level_row",
    )


def get_normal_range_selector_buttons():
    """Get the custom range selector buttons for the normal chart"""
    cn = "me-1 mb-1 py-0 px-1"
    st = {"font-size": "0.8em"}
    cl = "secondary"
    return html.Div(
        id="main_graph_rangeselector_buttons",
        style={
            "display": "inline-block",
            "min-width": "400px",
        },
        children=[
            # The first button is offset-1 so it appears above the chart in the right spot
            dbc.Button(
                "3h",
                id="ts_rng_btn_3h",
                color=cl,
                class_name=f"{cn} offset-1",
                style=st,
            ),
            # The first button is offset-1 so it appears above the chart in the right spot
            dbc.Button("1d", id="ts_rng_btn_1d", color=cl, class_name=cn, style=st),
            dbc.Button("7d", id="ts_rng_btn_7d", color=cl, class_name=cn, style=st),
            dbc.Button("1m", id="ts_rng_btn_1m", color=cl, class_name=cn, style=st),
            dbc.Button("3m", id="ts_rng_btn_3m", color=cl, class_name=cn, style=st),
            dbc.Button("6m", id="ts_rng_btn_6m", color=cl, class_name=cn, style=st),
            dbc.Button("1y", id="ts_rng_btn_1y", color=cl, class_name=cn, style=st),
            dbc.Button("all", id="ts_rng_btn_all", color=cl, class_name=cn, style=st),
            # The custom dates "switch" beside the buttons
            dbc.Switch(
                id="ts_charts_custom_dates_switch",
                label="Custom",
                label_style=dict(display="block"),
                value=False,
                persistence=True,
                persistence_type="local",
                style={"display": "inline-block"},
                class_name="offset-1",
            ),
            # The custom dates "switch" beside the buttons
            dbc.Switch(
                id="ts_charts_max_data_switch",
                label="Max Data",
                label_style=dict(display="block"),
                value=False,
                persistence=True,
                persistence_type="local",
                style={"display": "inline-block"},
                class_name="offset-1",
            ),
        ],
    )


def get_main_normal_chart_card() -> dbc.Card:
    """Get the layout for the main chart"""
    # Normal charts are displayed by default, and real time charts are hidden
    return dbc.Card(
        id="charts_div_normal",
        class_name="mb-3",
        children=[
            get_card_header_with_spinner(
                "main_graph_card_title",
                want_spinner=True,
                refresh_btn_id="main_chart_refresh_btn",
            ),
            dbc.CardBody(
                [
                    get_normal_range_selector_buttons(),
                    dbc.Spinner(
                        color="success",
                        delay_show=1000,
                        show_initially=False,
                        children=[
                            dbc.Collapse(
                                dbc.Card(
                                    dbc.CardBody(
                                        [
                                            create_dcc_date_picker_range(
                                                id_="ts_chart_date_range",
                                                go_btn_id="ts_chart_date_range_btn",
                                                spinner_id="ts_chart_date_range_spinner",
                                                days=3,
                                                next_btn_id="ts_chart_date_range_next_btn",
                                                prev_btn_id="ts_chart_date_range_prev_btn",
                                            ),
                                            create_chart_detail_radios(),
                                        ],
                                    ),
                                    class_name="mb-1",
                                ),
                                id="ts_chart_custom_dates_collapse",
                                is_open=False,
                                class_name="mt-2",
                            ),
                            # A place to put a message, such as "no data available for this unit"
                            html.Div(id="main_graph_msg"),
                            dcc.Graph(
                                id="main_graph",
                                # Disable the ModeBar with the Plotly logo and other buttons
                                config=dict(
                                    displayModeBar=False,
                                ),
                                style={"display": "none"},
                            ),
                            html.Div(
                                html.A(
                                    "Download CSV",
                                    id="main_chart_download_csv",
                                    href="#",
                                    className="btn btn-outline-secondary btn-sm mt-2",
                                ),
                                id="main_chart_download_csv_div",
                            ),
                        ],
                    ),
                ],
            ),
        ],
    )


def get_rt_data_hist_request_card() -> dbc.Card:
    """Get the form for requesting historical RT data"""

    return dbc.Card(
        class_name="h-100",
        children=[
            dbc.CardHeader(
                [html.I(className="fa-solid fa-history me-2"), "Historical Data"]
            ),
            dbc.CardBody(
                [
                    dbc.Row(
                        dbc.Col(
                            html.P(
                                "Request high-granularity historical data from a specific time. Processing takes 10-60 seconds.",
                                className="mb-2 text-muted",
                            ),
                        ),
                    ),
                    dbc.Row(
                        dbc.Col(
                            dbc.InputGroup(
                                [
                                    dbc.InputGroupText(
                                        [
                                            html.I(className="fa-solid fa-clock me-1"),
                                            "Since",
                                        ],
                                        # style={"height": "38px"},
                                    ),
                                    dbc.Input(
                                        id="rt_hist_request_datetime",
                                        type="time",
                                        placeholder="HH:MM",
                                        # style={"height": "38px"},
                                    ),
                                    dbc.InputGroupText(
                                        "local time",
                                        # style={"height": "38px"}
                                    ),
                                    dbc.Button(
                                        [
                                            html.I(
                                                className="fa-solid fa-download me-1"
                                            ),
                                            "Request",
                                        ],
                                        color="primary",
                                        id="btn_rt_data_get_hist",
                                        size="sm",
                                        # style={"height": "38px"},
                                    ),
                                ],
                                class_name="mb-2",
                            ),
                        ),
                    ),
                    dcc.Store(id="rt_hist_request_datetime_store"),
                    dbc.Row(
                        dbc.Col(
                            dbc.Spinner(
                                dbc.FormText(
                                    id="rt_data_get_hist_form_text",
                                    color="success",
                                ),
                                color="success",
                            ),
                        )
                    ),
                ],
            ),
        ],
    )


def get_rt_data_streaming_card() -> dbc.Card:
    """Get the form for requesting X minutes of RT data"""

    return dbc.Card(
        class_name="h-100",
        children=[
            dbc.CardHeader(
                [html.I(className="fa-solid fa-play me-2"), "Future Data (Streaming)"]
            ),
            dbc.CardBody(
                [
                    dbc.Row(
                        dbc.Col(
                            html.P(
                                "Request live streaming real-time data for up to 20 minutes starting now.",
                                className="mb-2 text-muted",
                            ),
                        ),
                    ),
                    dbc.Row(
                        dbc.Col(
                            dbc.InputGroup(
                                [
                                    dbc.InputGroupText(
                                        [
                                            html.I(className="fa-solid fa-play me-1"),
                                            "Stream for",
                                        ]
                                    ),
                                    dbc.Input(
                                        id="rt_data_x_mins_input_id",
                                        type="number",
                                        placeholder="1-20",
                                        min=1,
                                        max=20,
                                        style={"height": "38px", "width": "80px"},
                                    ),
                                    dbc.InputGroupText("mins"),
                                    dbc.Button(
                                        [
                                            html.I(className="fa-solid fa-rocket me-1"),
                                            "Start",
                                        ],
                                        color="success",
                                        id="btn_rt_data_x_mins",
                                        size="sm",
                                        style={"height": "38px"},
                                    ),
                                ],
                                class_name="mb-2",
                            ),
                        ),
                    ),
                    dbc.Row(
                        dbc.Col(
                            dbc.Spinner(
                                dbc.FormText(
                                    id="rt_data_x_mins_form_text",
                                    color="success",
                                ),
                                color="success",
                            ),
                        )
                    ),
                ],
            ),
        ],
    )


def get_rt_chart_options_rows() -> html.Div:
    """Special options for above the real-time chart"""
    return html.Div(
        id="rt_chart_options_div",
        children=[
            dbc.Row(
                [
                    # Historical data card on the left
                    dbc.Col(
                        width="auto",
                        class_name="mb-3 d-flex",
                        children=get_rt_data_hist_request_card(),
                    ),
                    # Future data (streaming) card on the right
                    dbc.Col(
                        width="auto",
                        class_name="mb-3 d-flex",
                        children=get_rt_data_streaming_card(),
                    ),
                ],
                class_name="g-3",  # Add some gutter spacing between cards
            ),
        ],
    )


def get_main_and_rt_chart_div() -> dbc.Row:
    """Put all the main and RT chart elements in a row"""
    return html.Div(
        [
            get_rt_chart_options_rows(),
            dbc.Row(
                [
                    dbc.Col(
                        lg=9,
                        children=[
                            get_main_normal_chart_card(),
                            get_main_rt_chart_row(),
                        ],
                    ),
                    get_chart_categories_items_column(),
                ],
            ),
        ]
    )


def get_main_charts_div() -> dbc.Row:
    """Get the layout for the main charts"""
    return dbc.Row(
        id="main_charts_parent_div",
        style={"display": "none"},
        justify="center",
        class_name="mt-3",
        children=dbc.Col(
            lg=12,
            xxl=10,  # Dan wants a 2:1 aspect ratio for charts
            # Tabs for normal chart, or real time chart
            children=[
                dbc.Row(
                    dbc.Col(
                        # Tabs for either map or unit list
                        dbc.Tabs(
                            [
                                dbc.Tab(
                                    label="Normal",
                                    tab_id=ACTIVE_TAB_NORMAL_CHARTS,
                                    id="tab_charts_normal",
                                ),
                                dbc.Tab(
                                    label="Real Time",
                                    tab_id=ACTIVE_TAB_RT_CHARTS,
                                    id="tab_charts_rt",
                                ),
                            ],
                            id="tabs_for_normal_or_rt_charts",
                            active_tab=ACTIVE_TAB_NORMAL_CHARTS,  # default tab
                            class_name="mb-3 justify-content-left",
                            persistence=True,
                            persistence_type="local",
                        ),
                    )
                ),
                # Main chart and real time chart
                get_main_and_rt_chart_div(),
            ],
        ),
    )


@callback(
    Output("category_to_chart", "options"),
    Output("category_to_chart", "value"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("tabs_for_normal_or_rt_charts", "active_tab"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    State("store_chart_category", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def add_category_items_by_unit_type(
    active_tab_1,
    active_tab_2,
    tab_uno_egas,
    store_chart_category,
    store_unit_type_id,
):
    """Depending on the unit_type, display a radio list of categories to chart"""
    log_function_caller()

    if active_tab_1 != TAB_CHARTS:
        raise PreventUpdate()

    # Deal with special UNOGAS case first
    if store_unit_type_id == UNIT_TYPE_ID_UNOGAS:
        if tab_uno_egas == TAB_UNOGAS_UNO:
            unit_type_id = UNIT_TYPE_ID_UNO
        else:
            unit_type_id = UNIT_TYPE_ID_EGAS
    else:
        unit_type_id = store_unit_type_id

    # is_rt_charts = False
    if active_tab_2 == ACTIVE_TAB_NORMAL_CHARTS:
        if unit_type_id in UNIT_TYPES_IDS_XFER_EGAS:
            if unit_type_id == UNIT_TYPE_ID_XFER:
                options_dicts = categories_options_xfer_only.copy()
                default_category = "important_xfer_egas_only"
            else:
                # EGAS, BOOST, VRU units
                options_dicts = categories_options_egas_only.copy()
                default_category = "important_xfer_egas_only"

            # Append IJACK-only diagnostic category
            if user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
                diagnostic_option = {"label": "Diagnostic", "value": "diagnostic"}
                if diagnostic_option not in options_dicts:
                    options_dicts.append(diagnostic_option)
        else:
            options_dicts = uno_unogas_categories_options.copy()
            default_category = "production"
    else:
        # is_rt_charts = True
        if unit_type_id == UNIT_TYPE_ID_XFER:
            options_dicts = categories_options_xfer_only_rt
            default_category = "pressure"
        elif unit_type_id in UNIT_TYPES_IDS_XFER_EGAS:
            options_dicts = categories_options_egas_only_rt
            default_category = "pressure"
        else:
            # # No real time data yet for UNO types
            # raise PreventUpdate()
            options_dicts = uno_unogas_categories_options_rt
            default_category = "speed"

    # Use the stored value, if it's allowed for this unit type
    allowed_values = [d["value"] for d in options_dicts]

    category_selected = (
        store_chart_category
        if store_chart_category in allowed_values
        else default_category
    )

    return options_dicts, category_selected


@callback(
    Output("category_items_options", "value"),
    Input("store_unit_type_id", "data"),
    State("category_items_options", "value"),
    State("store_structure_id", "data"),
    State("tabs_for_normal_or_rt_charts", "active_tab"),
    State("store_chart_category", "data"),
    prevent_initial_call=True,
)
def set_category_items_options_value(
    store_unit_type_id,
    category_items_options_value,
    store_structure_id_data,
    tabs_for_normal_or_rt_charts_active_tab,
    store_chart_category_data,
):
    """Set the value of the category_items_options checklist"""
    log_function_caller()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    is_vessel_level_mode: bool = getattr(structure_obj, "suction_range", None) == 4

    if not isinstance(category_items_options_value, list):
        if category_items_options_value:
            category_items_options_value = [category_items_options_value]
        else:
            category_items_options_value = []

    # Apply default unit-specific behavior (will be overridden by user preferences below)
    option_wanted: str = "oz_per_inch2_for_suction"
    if store_unit_type_id == UNIT_TYPE_ID_VRU:
        if option_wanted not in category_items_options_value:
            category_items_options_value.append(option_wanted)
    else:
        if option_wanted in category_items_options_value:
            category_items_options_value.remove(option_wanted)

    if is_vessel_level_mode:
        # Should we remove the "cgp" metric? I don't think so
        # if "cgp" in category_items_options_value:
        #     category_items_options_value.remove("cgp")
        #     needs_update = True
        if "suction_vru" not in category_items_options_value:
            category_items_options_value.append("suction_vru")

    # Determine which chart type we're using (normal or real-time)

    # Try to get user display preferences from database (global for all chart types and categories)
    user_toggles = None
    if user_id is not None:
        try:
            user_toggles = (
                db.session.query(UserChartToggle).filter_by(user_id=user_id).first()
            )
        except Exception as e:
            current_app.logger.exception(f"Error fetching user chart toggles: {e}")
            # Continue without user preferences if there's an error

    if user_toggles is not None:
        # Update display units based on user preferences if set
        try:
            # Apply unit preference settings - user preferences override defaults
            # Handle kPa preference
            if getattr(user_toggles, "use_kpa", False):
                if "kpa_not_psi" not in category_items_options_value:
                    category_items_options_value.append("kpa_not_psi")
            else:
                # User explicitly wants PSI, remove kPa if present
                if "kpa_not_psi" in category_items_options_value:
                    category_items_options_value.remove("kpa_not_psi")

            # Handle cubic feet preference
            if getattr(user_toggles, "use_cf", False):
                if "cf_not_m3" not in category_items_options_value:
                    category_items_options_value.append("cf_not_m3")
            else:
                # User explicitly wants m3, remove CF if present
                if "cf_not_m3" in category_items_options_value:
                    category_items_options_value.remove("cf_not_m3")

            # Handle barrels preference
            if getattr(user_toggles, "use_barrels", False):
                if "barrels_not_m3" not in category_items_options_value:
                    category_items_options_value.append("barrels_not_m3")
            else:
                # User explicitly wants m3, remove barrels if present
                if "barrels_not_m3" in category_items_options_value:
                    category_items_options_value.remove("barrels_not_m3")

            # Handle Fahrenheit preference
            if getattr(user_toggles, "use_fahrenheit", False):
                if "fahrenheit_not_celsius" not in category_items_options_value:
                    category_items_options_value.append("fahrenheit_not_celsius")
            else:
                # User explicitly wants Celsius, remove Fahrenheit if present
                if "fahrenheit_not_celsius" in category_items_options_value:
                    category_items_options_value.remove("fahrenheit_not_celsius")

            # Handle oz/in² preference - this overrides unit-specific defaults
            if getattr(user_toggles, "use_oz_per_inch2_for_suction", False):
                if "oz_per_inch2_for_suction" not in category_items_options_value:
                    category_items_options_value.append("oz_per_inch2_for_suction")
            else:
                # User explicitly disabled this option, remove it even for VRU units
                if "oz_per_inch2_for_suction" in category_items_options_value:
                    category_items_options_value.remove("oz_per_inch2_for_suction")
        except Exception as e:
            current_app.logger.exception(
                f"Error applying user display preferences: {e}"
            )

    return category_items_options_value


@callback(
    Output("items_to_chart", "options"),
    Output("items_to_chart", "value"),
    # Output("store_get_diagnostic", "data"),
    # Output("diag_data_request_hist_collapse", "is_open", allow_duplicate=True),
    Input("store_chart_category", "data"),
    Input("category_items_options", "value"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    Input("store_unit_type_id", "data"),
    State("store_tabs_for_ind_charts_control_log", "data"),
    State("tabs_for_normal_or_rt_charts", "active_tab"),
    State("store_structure_id", "data"),
    prevent_initial_call=True,
)
def add_checklist_items_by_unit_type(
    store_chart_category,
    category_items_options,
    tab_uno_egas,
    store_unit_type_id,
    active_tab_1,
    active_tab_2,
    store_structure_id_data,
):
    """Depending on the unit_type, display a checklist of items to chart"""
    log_function_caller()

    if store_chart_category is None or active_tab_1 != TAB_CHARTS:
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    is_ijack_employee: bool = user_is_ijack_employee(user_id=user_id)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    has_slave: bool = structure_obj.has_slave
    is_vessel_level_mode: bool = structure_obj.suction_range == 4

    def return_vars(
        items_to_chart_options,
        items_to_chart_value,
        # store_get_diagnostic_data,
        # diag_data_request_hist_collapse_is_open
    ):
        """Default return values"""
        return (
            items_to_chart_options,
            items_to_chart_value,
            # store_get_diagnostic_data,
            # diag_data_request_hist_collapse_is_open
        )

    # Deal with special UNOGAS case first
    if store_unit_type_id == UNIT_TYPE_ID_UNOGAS:
        if tab_uno_egas == TAB_UNOGAS_UNO:
            unit_type_id = UNIT_TYPE_ID_UNO
        else:
            unit_type_id = UNIT_TYPE_ID_EGAS
    else:
        unit_type_id = store_unit_type_id

    if active_tab_2 == ACTIVE_TAB_NORMAL_CHARTS:
        # Non-real-time chart
        if unit_type_id in UNIT_TYPES_IDS_XFER_EGAS:
            categories_dict = categories_dict_xfer_egas
            categories_dict_sl = categories_dict_xfer_egas_sl
            category_default_items_dict = category_default_items_dict_xfer_egas
            if unit_type_id == UNIT_TYPE_ID_XFER:
                allowed_categories = categories_list_xfer_only
            else:
                allowed_categories = categories_list_egas_only

            if is_ijack_employee:
                # Only IJACK employees can see the "diagnostic" category items
                allowed_categories += ("diagnostic",)
        else:
            categories_dict = categories_dict_uno
            categories_dict_sl = categories_dict_uno_sl
            category_default_items_dict = category_default_items_dict_uno
            allowed_categories = categories_list_uno_unogas

        if has_slave:
            # Combine the primary and secondary units' lists
            cats_dict = {}
            for key, list1 in categories_dict.items():
                list2 = categories_dict_sl.get(key, [])
                cats_dict[key] = sorted([*list1, *list2])

            # Add all slave unit items to the defaults
            defaults1 = category_default_items_dict.get(store_chart_category, [])
            defaults2 = categories_dict_sl.get(store_chart_category, [])
            default_items: set = set([*defaults1, *defaults2])
        else:
            cats_dict = categories_dict.copy()
            default_items: set = set(
                category_default_items_dict.get(store_chart_category, set())
            )

        if is_vessel_level_mode:
            # Remove the "cgp" metric when in vessel-level mode? I don't think so
            # default_items.discard("cgp")
            # Add the "suction_vru" metric for vessel-level mode
            default_items.add("suction_vru")

    else:
        # Real-time chart
        if unit_type_id in UNIT_TYPES_IDS_XFER_EGAS:
            categories_dict_rt = categories_dict_xfer_egas_rt
            categories_dict_rt_sl = categories_dict_xfer_egas_rt_sl
            category_default_items_dict_rt = category_default_items_dict_xfer_egas_rt
            if unit_type_id == UNIT_TYPE_ID_XFER:
                allowed_categories = categories_list_xfer_only_rt
            else:
                allowed_categories = categories_list_egas_only_rt
        else:
            categories_dict_rt = categories_dict_uno_rt
            categories_dict_rt_sl = categories_dict_uno_rt_sl
            category_default_items_dict_rt = category_default_items_dict_uno_rt
            allowed_categories = categories_list_uno_unogas_rt

        if has_slave:
            # Combine the primary and secondary units' lists
            cats_dict = {}
            for key, list1 in categories_dict_rt.items():
                list2 = categories_dict_rt_sl.get(key, [])
                cats_dict[key] = sorted([*list1, *list2])

            # Add all slave unit items to the defaults
            defaults1 = category_default_items_dict_rt.get(store_chart_category, [])
            defaults2 = categories_dict_rt_sl.get(store_chart_category, [])
            default_items: set = set([*defaults1, *defaults2])
        else:
            # Use the real-time dictionaries instead
            cats_dict = categories_dict_rt.copy()
            default_items: set = set(
                category_default_items_dict_rt.get(store_chart_category, set())
            )

    # If the category_items_options switch is flipped, show every possible metric
    if "show_all" in category_items_options:
        # There is no ordered set, so we'll use the keys from an OrderedDict.
        # We want to avoid duplicates in the main list since
        # some metrics will be in multiple lists.
        od = OrderedDict()
        for cat_name, list_ in cats_dict.items():
            # If it's an XFER, don't allow UNO categories, etc
            if cat_name in allowed_categories:
                for item in list_:
                    od.update({item: None})
        items: set = set(od.keys())  # discard the values
    else:
        items: set = set(cats_dict.get(store_chart_category, []))

    # Add total UNO + EGAS horsepower metric if it's an UNOGAS
    if "effort" in store_chart_category:
        if store_unit_type_id == UNIT_TYPE_ID_UNOGAS:
            items.add("hpt")
            default_items.add("hpt")

    options = []
    default_values = []

    use_kpa = "kpa_not_psi" in category_items_options
    use_oz_per_inch2_for_suction = "oz_per_inch2_for_suction" in category_items_options
    use_cf = "cf_not_m3" in category_items_options
    use_barrels = "barrels_not_m3" in category_items_options
    use_fahrenheit = "fahrenheit_not_celsius" in category_items_options

    item_labels = get_labels(
        use_kpa,
        use_oz_per_inch2_for_suction,
        use_cf,
        use_barrels,
        use_fahrenheit,
        is_vessel_level_mode,
    )

    # get_diag_data: bool = False
    # diag_data_request_hist_collapse_is_open = False
    # if store_chart_category == "diagnostic" and is_ijack_employee:
    #     # Get the new diagnostic metrics
    #     diag_metrics: list = db.session.query(
    #         DiagnosticMetric.name,
    #         DiagnosticMetric.color,
    #         DiagnosticMetric.description,
    #     ).all()
    #     if diag_metrics:
    #         get_diag_data = True
    #         # if is_ijack_employee:
    #         diag_data_request_hist_collapse_is_open = True
    #         diag_names = set([str(x.name).strip() for x in diag_metrics])
    #         items.update(diag_names)
    #         diag_labels = {
    #             str(x.name).strip(): {"color": x.color, "label": x.description}
    #             for x in diag_metrics
    #         }
    #         item_labels.update(diag_labels)

    # This converts the set to a list and sorts it (Tim likes it this way)
    items_list: list = sorted(items)
    for item in items_list:
        # We don't want this one to be unit-type-specific
        # if unit_type in ('egas', 'xfer'):
        #     value = names_xfer_egas[item]
        # else:
        #     value = names_uno_unogas[item]

        options.append(
            {
                "label": item_labels[item]["label"],
                "value": item,
            }
        )

        if item in default_items:
            # We don't want this one to be unit-type-specific
            default_values.append(item)

    # Determine which chart type we're using (normal or real-time)
    chart_type = "main" if active_tab_2 == ACTIVE_TAB_NORMAL_CHARTS else "real_time"

    # Try to get user preferences from database for this chart type and category
    user_preferences = None
    if user_id is not None:
        try:
            user_preferences = (
                db.session.query(UserChartPreference)
                .filter_by(
                    user_id=user_id,
                    # structure_id=store_structure_id_data,
                    chart_type=chart_type,
                    chart_category=store_chart_category,
                )
                .first()
            )
        except Exception:
            current_app.logger.exception("Error fetching user chart preferences")
            # Continue without user preferences if there's an error

    # If we found user preferences for this chart type and category, use them
    if user_preferences is not None:
        # Replace default values with user's selected metrics.
        # Ensure all metrics in user preferences are valid for current unit type
        valid_user_metrics = [
            metric for metric in user_preferences.selected_metrics if metric in items
        ]

        if valid_user_metrics:
            # Only use user preferences if they contain valid metrics for this unit
            default_values = valid_user_metrics

    return return_vars(
        items_to_chart_options=options,
        items_to_chart_value=default_values,
        # store_get_diagnostic_data=get_diag_data,
        # diag_data_request_hist_collapse_is_open=diag_data_request_hist_collapse_is_open,
    )


@callback(
    Output("main_graph", "figure"),
    Output("main_graph", "style"),
    Output("main_graph_rangeselector_buttons", "style"),
    Output("main_graph_msg", "children"),
    Output("main_graph_card_title", "children"),
    Output("main_chart_download_csv", "href"),
    Output("main_chart_download_csv_div", "style"),
    Output("ts_chart_date_range_spinner", "children"),
    Output("ts_chart_date_range", "start_date"),
    Output("ts_chart_date_range", "end_date"),
    # Output("ts_rng_btn_3h", "disabled"),
    Output("ts_rng_btn_3h_store_2", "data"),
    Output("record_visit_rcom_charts_main", "data"),
    Input("main_chart_refresh_btn", "n_clicks"),
    Input("items_to_chart", "value"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    Input("store_time_series_rangeselector", "data"),
    Input("ts_charts_max_data_switch", "value"),
    Input("ts_chart_detail_level_radio", "value"),
    Input("ts_chart_date_range_prev_btn", "n_clicks"),
    Input("ts_chart_date_range_next_btn", "n_clicks"),
    State("store_unit_type_id", "data"),
    State("store_structure_id", "data"),
    State("store_tabs_for_ind_charts_control_log", "data"),
    State("tabs_for_normal_or_rt_charts", "active_tab"),
    State("category_items_options", "value"),
    # State("ts_chart_custom_dates_collapse", "is_open"),
    State("ts_chart_date_range", "start_date"),
    State("ts_chart_date_range", "end_date"),
    State("ts_charts_custom_dates_switch", "value"),
    # State("store_get_diagnostic", "data"),
    prevent_initial_call=True,
)
def update_main_graph(
    # Inputs
    main_chart_refresh_btn_n_clicks,
    cols_chosen,
    tab_uno_egas,
    data_range,
    ts_charts_max_data_switch_value,
    ts_chart_detail_level_radio,
    prev_day_clicks,
    next_day_clicks,
    store_unit_type_id,
    # States
    store_structure_id_data,
    active_tab_1,
    active_tab_2,
    category_items_options,
    # ts_chart_custom_dates_collapse_is_open,
    start_str,
    end_str,
    ts_charts_custom_dates_switch_value,
    # store_get_diagnostic_data,
):
    """Main time series chart below the map"""
    log_function_caller()

    if (
        store_structure_id_data is None
        or cols_chosen is None
        or active_tab_1 != TAB_CHARTS
        or active_tab_2 != ACTIVE_TAB_NORMAL_CHARTS
    ):
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()
    is_vessel_level_mode: bool = structure_obj.suction_range == 4

    # Get specific unit type, especially for UNOGAS types, where we must determine EGAS or UNO
    unit_type_lower, unit_type_id = get_unit_type(tab_uno_egas, store_unit_type_id)
    has_rcom: bool = bool(structure_obj.aws_thing)

    if not unit_type_lower:
        msg = f"Can't determine unit_type_lower for store_unit_type_id = {store_unit_type_id} and tab_uno_egas = {tab_uno_egas}"
        current_app.logger.error(msg)
        # send_email(
        #     subject="RCOM website error in charts!",
        #     sender="<EMAIL>",
        #     to_emails=ADMIN_EMAILS,
        #     text_body=msg,
        # )
        raise PreventUpdate()

    # Identify the demo user for which to make fake locations
    is_demo: bool = user_is_demo_customer(user_id=user_id)
    customer_first_word = structure_obj.customer.split()[0]
    if is_demo:
        title = f"Data for {customer_first_word} {unit_type_lower.upper()}"
    else:
        # More informative title
        customer_unit_info = f"{customer_first_word} {unit_type_lower.upper()} {structure_obj.power_unit_str} at {structure_obj.surface}"
        title = f"Data for {customer_unit_info}"

    # The Cinco EGAS units didn't have any data, so no times
    def get_no_data_tuple(msg="No data to display", ts_rng_btn_3h_data=no_update):
        """What to return when there's inadequate data"""
        record_visit_rcom = "rcom_charts_main"
        return (
            go.Figure(),  # figure (DO NOT put None here or there will be a clientside error!)
            {"display": "none"},  # style
            {},  # style
            html.H3(msg, className="offset-1"),
            title,  # title
            "#",  # main_chart_download_csv href
            {"display": "none"},  # download CSV button style
            "Go",  # Useless spinner children
            # Only partial update
            no_update,
            no_update,
            ts_rng_btn_3h_data,
            record_visit_rcom,
        )

    if not has_rcom or unit_type_id == UNIT_TYPE_ID_DGAS:
        return get_no_data_tuple(msg="Unit doesn't have RCOM")
    if store_unit_type_id == UNIT_TYPE_ID_GATEWAYS:
        return get_no_data_tuple(
            msg="This gateway hasn't been assigned to a power unit yet"
        )

    try:
        tz_wanted = pytz.timezone(structure_obj.time_zone)
    except pytz.exceptions.UnknownTimeZoneError:
        current_app.logger.exception(f"Unknown time_zone: '{structure_obj.time_zone}'")
        return get_no_data_tuple(
            msg="Please set the time zone under 'Account/Admin/Units/Unit Information' to see chart data"
        )

    # Find the unit_type-specific column name in the appropriate dictionary
    # current_app.logger.debug("cols_chosen: %s", cols_chosen)
    if unit_type_id == UNIT_TYPE_ID_UNO:
        names_dict = names_uno_unogas
        defaults = defaults_uno_unogas
    else:
        names_dict = names_xfer_egas
        defaults = defaults_xfer_egas

    # if store_get_diagnostic_data:
    #     diag_metrics = DiagnosticMetric.query.order_by("name").all()
    #     # Get the diagnostic metric names
    #     for metric_model in diag_metrics:
    #         names_dict[metric_model.name] = metric_model.name

    col, col_default = None, None
    try:
        cols_chosen_db_fields = []
        for col in cols_chosen:
            cols_chosen_db_fields.append(names_dict[col])
        if len(cols_chosen_db_fields) == 0:
            # not unit-type-specific
            cols_chosen = defaults
            # unit-type-specific
            cols_chosen_db_fields = []
            for col_default in defaults:
                cols_chosen_db_fields.append(names_dict[col_default])
    except KeyError:
        # The SHOP unit has this problem from time to time
        if store_structure_id_data != STRUCTURE_ID_SHOP and unit_type_lower != "shop":
            # This happens when a user switches from an XFER/EGAS
            # to an UNO and the cols_chosen hasn't updated yet
            msg = "Can't find all of cols_chosen in names dict.\n\n"
            msg += f"current_user: {current_user}.\n"
            msg += f"title: {title}.\n"
            msg += f"store_structure_id_data: {store_structure_id_data}.\n"
            msg += f"cols_chosen: {cols_chosen}.\n"
            msg += f"unit_type_lower: {unit_type_lower}\n"
            msg += f"Link: https://myijack.com/rcom/?power_unit={structure_obj.power_unit_str}\n\n"
            if col_default:
                msg += f"default column wanted from names_dict: {col_default}\n"
            else:
                msg += f"column wanted from names_dict: {col}\n"
            msg += f"names_dict: {names_dict}"
            current_app.logger.error(msg)
            if cols_chosen != ["m3pd"]:
                send_email(
                    subject="RCOM website error in charts!",
                    sender="<EMAIL>",
                    to_emails=ADMIN_EMAILS,
                    text_body=msg,
                )
        return get_no_data_tuple(msg="There has been an error. Please try again")

    dt_format = "%Y-%m-%d %H:%M"
    dt_now: datetime = utcnow_naive()
    start_dt_utc_default = (dt_now - pd.Timedelta("3 hours")).strftime(dt_format)
    end_dt_utc_default = dt_now.strftime(dt_format)
    id_triggered: str = get_id_triggered()

    ts_rng_btn_3h_data = no_update
    if not isinstance(data_range, dict):
        current_app.logger.debug(
            "ERROR: data_range = '%s'. id_triggered = '%s'",
            data_range,
            id_triggered,
        )

        # Supply a default
        data_range = {
            "start_dt_utc": start_dt_utc_default,
            "end_dt_utc": end_dt_utc_default,
            "chart_btn_range_name": "3-hour",
        }
        # Trigger the 3-hour button to be "active"
        ts_rng_btn_3h_data = True

    chart_btn_range_name = data_range.get("chart_btn_range_name", "custom date range")

    start_dt_utc_str = data_range.get("start_dt_utc", start_dt_utc_default)
    start_dt_utc_obj = datetime.strptime(start_dt_utc_str, dt_format)

    end_dt_utc_str = data_range.get("end_dt_utc", end_dt_utc_default)
    end_dt_utc_obj = datetime.strptime(end_dt_utc_str, dt_format)

    # If the user clicked the previous or next buttons, advance the dates
    timedelta_to_apply = None
    if "ts_chart_date_range_prev_btn.n_clicks" == id_triggered:
        timedelta_to_apply = timedelta(days=-1)
    elif "ts_chart_date_range_next_btn.n_clicks" == id_triggered:
        timedelta_to_apply = timedelta(days=1)

    if timedelta_to_apply is not None:
        start_dt_utc_obj = start_dt_utc_obj + timedelta_to_apply
        end_dt_utc_obj = end_dt_utc_obj + timedelta_to_apply

        start_dt_utc_str = start_dt_utc_obj.strftime("%Y-%m-%d %H:%M")
        end_dt_utc_str = end_dt_utc_obj.strftime("%Y-%m-%d %H:%M")

    end_dt_local_obj = tz_wanted.fromutc(end_dt_utc_obj)
    start_dt_local_obj = tz_wanted.fromutc(start_dt_utc_obj)

    # For the custom datepicker start and end dates
    start_date_str_date_only_local = start_dt_local_obj.strftime("%Y-%m-%d")
    end_date_str_date_only_local = end_dt_local_obj.strftime("%Y-%m-%d")

    use_kpa = "kpa_not_psi" in category_items_options
    use_oz_per_inch2_for_suction = "oz_per_inch2_for_suction" in category_items_options
    use_cf = "cf_not_m3" in category_items_options
    use_barrels = "barrels_not_m3" in category_items_options
    use_fahrenheit = "fahrenheit_not_celsius" in category_items_options

    # If the "max data" switch is activated, always use the 2-minute granularity
    if ts_charts_max_data_switch_value is True:
        ts_chart_detail_level_radio = "2m"
    elif ts_charts_custom_dates_switch_value is False:
        # If the "custom" collapse is closed, use the "auto" setting
        ts_chart_detail_level_radio = "auto"

    dff = get_time_series_dataframe(
        power_unit_str=structure_obj.power_unit_str,
        cols_chosen=cols_chosen_db_fields,
        is_rt=False,
        start_str=start_dt_utc_str,
        end_str=end_dt_utc_str,
        use_kpa=use_kpa,
        use_oz_per_inch2_for_suction=use_oz_per_inch2_for_suction,
        use_cf=use_cf,
        use_barrels=use_barrels,
        use_fahrenheit=use_fahrenheit,
        detail=ts_chart_detail_level_radio,
        # get_diag_data=store_get_diagnostic_data,
    )
    if dff is None:
        msg_old = f"No data in {chart_btn_range_name} chart. Try a longer time period to see older data."
        # print(msg_old)
        return get_no_data_tuple(
            # msg="Problem with time series data on Sep 5, 2024. Sorry! We're working on it!",
            msg=msg_old,
            ts_rng_btn_3h_data=ts_rng_btn_3h_data,
        )

    # Convert from UTC to the customer's local time
    dff["timestamp_utc"] = pd.to_datetime(dff["timestamp_utc"])
    dff["timestamp_local"] = (
        dff["timestamp_utc"].dt.tz_localize(pytz.utc).dt.tz_convert(tz_wanted)
    )

    # Find where the x-axis starts and ends
    x_axis_start = max(start_dt_local_obj, dff["timestamp_local"].min())
    x_axis_end = min(end_dt_local_obj, dff["timestamp_local"].max())

    main_graph_figure = go.Figure()

    # If the user has selected the "Use MCF not m^3 switch", change a few labels
    item_labels_colors: dict = get_labels(
        use_kpa,
        use_oz_per_inch2_for_suction,
        use_cf,
        use_barrels,
        use_fahrenheit,
        is_vessel_level_mode,
    )
    # if store_get_diagnostic_data:
    #     # Get the diagnostic metric names
    #     for metric_model in diag_metrics:
    #         item_labels_colors[metric_model.name] = {
    #             "color": metric_model.color,
    #             "label": metric_model.description,
    #         }

    for col_not_specific, col_specific in zip(cols_chosen, cols_chosen_db_fields):
        if col_specific in dff.columns:
            name = item_labels_colors[col_not_specific]["label"]
            main_graph_figure.add_trace(
                go.Scatter(
                    x=list(dff["timestamp_local"]),
                    y=list(dff[col_specific]),
                    name=name,
                    line=dict(
                        color=item_labels_colors[col_not_specific]["color"],
                        shape="spline",
                        # smoothing = 0-1.3 if shape="spline"
                        smoothing=0.2,
                    ),
                    hoverlabel={"namelength": -1},
                )
            )

    # Use aspect ratio instead of figure-height in layout below?
    # main_graph_style = {"aspect-ratio": "2 / 1"}
    main_graph_style = {"height": "50vh"}

    main_graph_figure.update_layout(
        # height=400,  # 500 is a bit too big on a smartphone
        colorway=bootstrap_colours,
        xaxis=dict(
            range=[str(x_axis_start), str(x_axis_end)],
            type="date",
            gridcolor="rgb(238,238,238)",
        ),
        yaxis=dict(
            gridcolor="rgb(238,238,238)",
        ),
        hovermode="closest",
        # Show the legend even if it's a single line
        showlegend=True,
        legend=dict(
            font=dict(color="#717174"),
            orientation="h",  # Looks much better horizontal than vertical
            # y = Parent: layout.legend
            # Type: number between or equal to -2 and 3
            # Sets the y position (in normalized coordinates) of the legend.
            # Defaults to "1" for vertical legends, defaults to "-0.1" for horizontal legends on graphs
            # w/o range sliders and defaults to "1.1" for horizontal legends on graph with one or multiple range sliders.
            y=-0.2,
        ),
        # Invalid property specified for object of type plotly.graph_objs.Layout: 'titlefont'
        # titlefont={
        #     "color": "#717174",
        #     "size": 22,
        # },
        font={
            "family": "Segoe UI",
            "color": "#717174",
        },
        # Added more margin on the left side to fix the cutoff True/False labels on the booleans
        margin=dict(l=40, r=25, b=0, t=0, pad=0),
        plot_bgcolor="white",
    )

    main_chart_download_csv_href = (
        f"/rcom/download_csv_chart_main?pu={structure_obj.power_unit_str}"
    )
    main_chart_download_csv_href += (
        f"&ut={unit_type_id}&start={start_dt_utc_str}&end={end_dt_utc_str}"
    )
    main_chart_download_csv_href += (
        f"&use_kpa={use_kpa}&detail={ts_chart_detail_level_radio}&tzw={tz_wanted}"
    )
    main_chart_download_csv_href += f"&use_cf={use_cf}&use_barrels={use_barrels}&is_vessel_level_mode={is_vessel_level_mode}"
    if not is_demo:  # demo customer
        # If it's not the demo user, add the location parameter
        main_chart_download_csv_href += f"&location={structure_obj.location}"

    record_visit_rcom = "rcom_charts_main"

    return (
        main_graph_figure,  # figure (DO NOT put None here or there will be a clientside error!)
        main_graph_style,  # style
        {},  # style
        None,  # msg
        title,  # title
        main_chart_download_csv_href,  # main_chart_download_csv href
        {},  # download CSV button style
        "Go",  # Useless spinner children
        start_date_str_date_only_local,
        end_date_str_date_only_local,
        ts_rng_btn_3h_data,
        record_visit_rcom,
    )


@callback(
    # Store the main time series chart date range
    Output("store_time_series_rangeselector", "data"),
    Output("ts_rng_btn_3h_store_1", "data"),
    Output("ts_rng_btn_1d", "active"),
    Output("ts_rng_btn_7d", "active"),
    Output("ts_rng_btn_1m", "active"),
    Output("ts_rng_btn_3m", "active"),
    Output("ts_rng_btn_6m", "active"),
    Output("ts_rng_btn_1y", "active"),
    Output("ts_rng_btn_all", "active"),
    Input("ts_rng_btn_3h", "n_clicks"),
    Input("ts_rng_btn_3h", "disabled"),
    Input("ts_rng_btn_1d", "n_clicks"),
    Input("ts_rng_btn_7d", "n_clicks"),
    Input("ts_rng_btn_1m", "n_clicks"),
    Input("ts_rng_btn_3m", "n_clicks"),
    Input("ts_rng_btn_6m", "n_clicks"),
    Input("ts_rng_btn_1y", "n_clicks"),
    Input("ts_rng_btn_all", "n_clicks"),
    Input("ts_chart_date_range_btn", "n_clicks"),
    State("ts_chart_date_range", "end_date"),
    State("ts_chart_date_range", "start_date"),
    State("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def store_ts_rangeselector_value(
    _3h,
    _3h_disabled,
    _1d,
    _7d,
    _1m,
    _3m,
    _6m,
    _1y,
    _all,
    date_range_btn_clicks,
    end_date,
    start_date,
    store_structure_id_data,
    store_unit_type_id,
):
    """Store which normal time series chart rangeselector button has been clicked"""
    log_function_caller()

    id_triggered: str = get_id_triggered()
    if id_triggered is None:
        raise PreventUpdate()

    if id_triggered == "ts_rng_btn_3h_store_1.data":
        # I'm just using the "data" storage property as a trigger
        # to set the "active property" (multiple outputs trick)
        return (
            # Don't update the date range
            no_update,
            # Only set the 3-hour button "active" state to True
            True,
            *[False] * 7,
        )

    data = {}
    end_dt_utc = utcnow_naive()
    start_dt_utc = end_dt_utc - timedelta(hours=3)
    chart_btn_range_name = "custom date range"
    # As default, don't include "ts_wants_custom_dates" in the checklist values list,
    # to signify the "Custom" checklist should NOT be checked
    # if a time-delta button has been pushed instead.

    # Button "active" colour states
    active_state_btn_3h = False
    active_state_btn_1d = False
    active_state_btn_7d = False
    active_state_btn_1m = False
    active_state_btn_3m = False
    active_state_btn_6m = False
    active_state_btn_1y = False
    active_state_btn_all = False

    if "ts_rng_btn_3h" in id_triggered:
        start_dt_utc = end_dt_utc - timedelta(hours=3)
        chart_btn_range_name = "3-hour"
        active_state_btn_3h = True
    elif "ts_rng_btn_1d" in id_triggered:
        start_dt_utc = end_dt_utc - timedelta(days=1)
        chart_btn_range_name = "1-day"
        active_state_btn_1d = True
    elif "ts_rng_btn_7d" in id_triggered:
        start_dt_utc = end_dt_utc - timedelta(days=7)
        chart_btn_range_name = "7-day"
        active_state_btn_7d = True
    elif "ts_rng_btn_1m" in id_triggered:
        start_dt_utc = end_dt_utc - timedelta(days=30)
        chart_btn_range_name = "1-month"
        active_state_btn_1m = True
    elif "ts_rng_btn_3m" in id_triggered:
        start_dt_utc = end_dt_utc - timedelta(days=90)
        chart_btn_range_name = "3-month"
        active_state_btn_3m = True
    elif "ts_rng_btn_6m" in id_triggered:
        start_dt_utc = end_dt_utc - timedelta(days=180)
        chart_btn_range_name = "6-month"
        active_state_btn_6m = True
    elif "ts_rng_btn_1y" in id_triggered:
        start_dt_utc = end_dt_utc - timedelta(days=365)
        chart_btn_range_name = "1-year"
        active_state_btn_1y = True
    elif "ts_rng_btn_all" in id_triggered:
        start_dt_utc = end_dt_utc - timedelta(days=(365 * 5))
        chart_btn_range_name = "5-year"
        active_state_btn_all = True
    elif (
        (
            "ts_chart_date_range" in id_triggered
            or "ts_chart_date_range_btn" in id_triggered
        )
        and start_date
        and end_date
    ):
        # Include "ts_wants_custom_dates" in the list to signify the "Custom" checklist should be checked
        chart_btn_range_name = "custom dates"
        user_id: int = getattr(current_user, "id", None)
        structure_obj: StructureVw | None = get_structure_obj(
            store_structure_id_data, user_id, store_unit_type_id
        )
        if structure_obj is None:
            raise PreventUpdate()
        try:
            tz_wanted = pytz.timezone(structure_obj.time_zone)
        except pytz.exceptions.UnknownTimeZoneError:
            current_app.logger.exception(
                f"Unknown time_zone: '{structure_obj.time_zone}'"
            )
            raise PreventUpdate()

        dt_format = "%Y-%m-%d"
        start_dt_local = datetime.strptime(start_date, dt_format)
        # Convert to UTC for the query
        # start_dt_utc = start_dt_local.replace(tzinfo=tz_wanted).astimezone(pytz.utc)
        start_dt_utc = tz_wanted.localize(start_dt_local).astimezone(pytz.utc)

        end_dt_local = datetime.strptime(end_date, dt_format)
        # Ensure we go until the end of the day selected
        end_dt_local = end_dt_local.replace(hour=23, minute=59, second=59)
        # Convert to UTC for the query
        # end_dt_utc = end_dt_local.replace(tzinfo=tz_wanted).astimezone(pytz.utc)
        end_dt_utc = tz_wanted.localize(end_dt_local).astimezone(pytz.utc)
    else:
        raise PreventUpdate()

    data = {
        "start_dt_utc": start_dt_utc.strftime("%Y-%m-%d %H:%M"),
        "end_dt_utc": end_dt_utc.strftime("%Y-%m-%d %H:%M"),
        "chart_btn_range_name": chart_btn_range_name,
    }

    return (
        data,
        # custom_dates_switch_value_list,
        # Change the colour of the button if clicked
        active_state_btn_3h,
        active_state_btn_1d,
        active_state_btn_7d,
        active_state_btn_1m,
        active_state_btn_3m,
        active_state_btn_6m,
        active_state_btn_1y,
        active_state_btn_all,
    )


@callback(
    Output("ts_chart_date_range", "initial_visible_month"),
    Input("ts_chart_date_range", "start_date"),
)
def change_ts_initial_visible_month(start_date):
    """
    If the user goes way back in time,
    change the initial visible month to that month
    """
    log_function_caller()
    return date.fromisoformat(start_date)


@callback(
    Output("rt_data_x_mins_input_id", "valid"),
    Output("rt_data_x_mins_input_id", "invalid"),
    Input("rt_data_x_mins_input_id", "value"),
    prevent_initial_call=True,
)
def check_validity_minutes_requested(value):
    """Should be between 1 and 5 minutes of real time data requested for non-IJACK users"""
    log_function_caller()

    try:
        value2 = int(value)
    except Exception:
        raise PreventUpdate()

    if value is not None:
        if is_admin():
            # IJACK admin users can request up to 48 hours of data
            is_valid = value2 > 0 and value2 <= (60 * 48)
            return is_valid, not is_valid

        # Non-IJACK users can only request up to 20 minutes of data
        is_valid = value2 > 0 and value2 <= 20
        return is_valid, not is_valid

    return False, False


@callback(
    Output("ts_chart_custom_dates_collapse", "is_open"),
    Input("ts_charts_custom_dates_switch", "value"),
    prevent_initial_call=False,
)
def open_custom_dates_picker(ts_charts_custom_dates_switch_value):
    """
    If the ts_charts_custom_dates_switch switch is activated,
    show the custom date picker.
    """
    return ts_charts_custom_dates_switch_value


@callback(
    Output("ts_chart_detail_level_row", "style"),
    Input("ts_charts_max_data_switch", "value"),
    prevent_initial_call=False,
)
def main_charts_max_data_switch(ts_charts_max_data_switch_value):
    """If the ts_charts_max_data_switch is activated, hide the custom granularity"""
    if ts_charts_max_data_switch_value is True:
        return {"display": "none"}
    return {}


@callback(
    Output("ts_rng_btn_3h", "active"),
    Input("ts_rng_btn_3h_store_1", "data"),
    Input("ts_rng_btn_3h_store_2", "data"),
    prevent_initial_call=True,
)
def ts_rng_btn_3h_active_multiple_outputs(
    ts_rng_btn_3h_store_1: bool, ts_rng_btn_3h_store_2: bool
) -> bool:
    """
    Pattern for creating synthetic "multiple outputs".
    Both inputs are booleans for "active"
    """
    id_triggered: str = get_id_triggered()
    if "ts_rng_btn_3h_store_1.data" == id_triggered:
        return ts_rng_btn_3h_store_1
    elif "ts_rng_btn_3h_store_2.data" == id_triggered:
        return ts_rng_btn_3h_store_2

    raise PreventUpdate()


@callback(
    Output("items_to_chart", "style"),
    Input("items_to_chart", "value"),
    State("store_chart_category", "data"),
    State("tabs_for_normal_or_rt_charts", "active_tab"),
    # State("store_structure_id", "data"),
    prevent_initial_call=True,
)
def save_user_chart_metric_preferences(
    items_to_chart_value,
    store_chart_category,
    active_tab_2,
    # store_structure_id_data,
):
    """Save user chart preferences when they select different metrics"""
    log_function_caller()

    # Only save preferences if we have a logged-in user
    user_id: int = getattr(current_user, "id", None)
    if user_id is None:
        raise PreventUpdate()

    # Don't save if no items are selected
    if not items_to_chart_value:
        raise PreventUpdate()

    # Determine which chart type we're using (normal or real-time)
    chart_type = "main" if active_tab_2 == ACTIVE_TAB_NORMAL_CHARTS else "real_time"

    try:
        # First handle the chart preferences (selected metrics)
        user_preference = (
            db.session.query(UserChartPreference)
            .filter_by(
                user_id=user_id,
                chart_type=chart_type,
                chart_category=store_chart_category,
            )
            .first()
        )

        if user_preference:
            # Update existing preference
            user_preference.selected_metrics = items_to_chart_value
        else:
            # Create new preference
            user_preference = UserChartPreference(
                user_id=user_id,
                chart_type=chart_type,
                chart_category=store_chart_category,
                selected_metrics=items_to_chart_value,
            )
            db.session.add(user_preference)

        db.session.commit()

    except Exception as e:
        current_app.logger.error(f"Error saving chart preferences: {e}")
        db.session.rollback()

    # This callback never actually returns anything
    raise PreventUpdate()


@callback(
    Output("category_items_options", "style"),
    Input("category_items_options", "value"),
    prevent_initial_call=True,
)
def save_user_chart_display_preferences(category_items_options):
    """Save user chart display preferences when they select different options"""
    log_function_caller()

    # Only save preferences if we have a logged-in user
    user_id: int = getattr(current_user, "id", None)
    if user_id is None:
        raise PreventUpdate()

    # Convert None to empty list for consistent processing
    if category_items_options is None:
        category_items_options = []

    try:
        # Now handle the display toggles (unit preferences)
        user_toggle = (
            db.session.query(UserChartToggle).filter_by(user_id=user_id).first()
        )

        # Extract unit preferences from category_items_options as Boolean values
        use_kpa = "kpa_not_psi" in category_items_options
        use_oz_per_inch2_for_suction = (
            "oz_per_inch2_for_suction" in category_items_options
        )
        use_cf = "cf_not_m3" in category_items_options
        use_barrels = "barrels_not_m3" in category_items_options
        use_fahrenheit = "fahrenheit_not_celsius" in category_items_options

        if user_toggle:
            # Update existing toggle preferences
            user_toggle.use_kpa = use_kpa
            user_toggle.use_oz_per_inch2_for_suction = use_oz_per_inch2_for_suction
            user_toggle.use_cf = use_cf
            user_toggle.use_barrels = use_barrels
            user_toggle.use_fahrenheit = use_fahrenheit
        else:
            # Create new toggle preferences
            user_toggle = UserChartToggle(
                user_id=user_id,
                use_kpa=use_kpa,
                use_oz_per_inch2_for_suction=use_oz_per_inch2_for_suction,
                use_cf=use_cf,
                use_barrels=use_barrels,
                use_fahrenheit=use_fahrenheit,
            )
            db.session.add(user_toggle)

        db.session.commit()

    except Exception as e:
        current_app.logger.error(f"Error saving chart preferences: {e}")
        db.session.rollback()

    # This callback never actually returns anything
    raise PreventUpdate()
