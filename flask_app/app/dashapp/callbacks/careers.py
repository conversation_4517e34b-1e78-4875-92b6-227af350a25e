import base64
import json
import os

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
from dash import (
    ALL,
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
)
from dash.exceptions import PreventUpdate
from flask import (
    current_app,
    url_for,
)
from flask_login import current_user
from shared.models.models import (
    CareerApplication,
    CareerApplicationFile,
    CareerFile,
)
from werkzeug.utils import secure_filename

from app import db
from app.config import (
    ADMIN_EMAILS,
)
from app.dashapp.utils import (
    get_id_triggered,
    recaptcha_is_good,
)
from app.email_stuff import (
    add_file_to_list,
    send_email,
)
from app.models.models import User
from app.utils.simple import utcnow_naive


def careers_layout():
    """Get the Dash layout for the careers page."""
    return dbc.Container(
        class_name="pt-3",
        children=[
            # Hidden signal value, which starts the page-load callbacks
            html.Div(id="hidden_signal_careers", style={"display": "none"}),
            # Store something neeeded for dynamic JavaScript (e.g. Google RECAPTCHA site key)
            dcc.Store(id="store_site_key_action_careers", storage_type="memory"),
            # Need a dummy place for an Output() to nowhere
            dcc.Store(id="store_nothing_careers", storage_type="memory"),
            # Store the reCAPTCHA response so we can assess it in the callback
            dcc.Store(id="store_recaptcha_response_careers", storage_type="memory"),
            # Header Row
            dbc.Row(
                [
                    dbc.Col(
                        [
                            html.H1("Careers at IJACK", className="mb-0"),
                            html.H2("Why IJACK?", className="mt-3"),
                        ],
                        md=6,
                    )
                ]
            ),
            # Company Description Row
            dbc.Row(
                [
                    # Text Column
                    dbc.Col(
                        [
                            html.P("""
                        IJACK is a small but fast-growing company, helping the world's most technologically advanced upstream oil and
                        gas companies green their operations and get more production with much lower environmental impact.
                        We manufacture artificial lift and optimization equipment for oil and gas production and facility areas. Our
                        patented products eliminate the need for methane venting and flaring, and sometimes even entire facilities,
                        making our customers the leanest and greenest in the oilpatch.
                    """)
                        ],
                        xl=4,
                    ),
                    # Image Column
                    dbc.Col(
                        [
                            html.Img(
                                src="/static/img/careers/ijack-shop-2000px-2000px.webp",
                                alt="IJACK shop in Moosomin, Saskatchewan, Canada",
                                className="img-fluid",
                                style={"width": "100%", "height": "auto"},
                            )
                        ],
                        xl=8,
                    ),
                ]
            ),
            # What to Expect Section
            dbc.Row(
                [
                    dbc.Col(
                        [
                            html.H2("What to Expect?"),
                            html.P("""
                        Employees at IJACK experience high levels of autonomy and teamwork.
                        Working at a smaller company, our people often have to "wear many hats" to get the job done for our customers.
                        Staff learn quickly and help each other. Everyone is encouraged to listen to customers - that's how our products
                        are invented and perfected!
                    """),
                        ]
                    )
                ],
                className="mb-3",
            ),
            # Positions Section
            dbc.Row(
                [
                    dbc.Col(
                        [
                            html.H2("Positions We're Often Looking For"),
                            html.P(
                                "IJACK is always looking for talented people, particularly in the following areas:"
                            ),
                            html.Ul(
                                [
                                    html.Li(
                                        [
                                            html.Strong("Technical sales: "),
                                            """New IJACK customers have interesting challenges, and each job is unique.
                            Often, IJACK products and capabilities can sound "too good to be true" since our patented pumps are highly innovative.""",
                                        ]
                                    ),
                                    html.Li(
                                        [
                                            html.Strong("Design engineering: "),
                                            """IJACK staff - and our customers - are always dreaming up new ideas.
                            We strongly believe in continuous improvement, and innovative designs that solve customer problems are the reason for our success.""",
                                        ]
                                    ),
                                    html.Li(
                                        [
                                            html.Strong("Heavy duty mechanics: "),
                                            """As a manufacturing company, IJACK has to build the products,
                            and service them in the field when the need arises.""",
                                        ]
                                    ),
                                ]
                            ),
                        ]
                    )
                ]
            ),
            # Authentication/Form Section - Uses interval to check auth status
            dcc.Interval(id="careers_interval", interval=1000),  # Check every second
            # Custom CSS stylesheet for delete buttons
            html.Link(
                rel="stylesheet",
                id="careers_stylesheet",
            ),
            # This is just for triggering the files list update
            dcc.Store(id="careers_store_trigger"),
            html.Div(
                id="careers_not_logged_in_div",
                children=[
                    html.H2("Register or Login to Submit your Resume!"),
                    dbc.Button(
                        "Register", color="dark", className="me-1", href="/register/"
                    ),
                    dbc.Button("Login", color="dark", className="me-1", href="/login/"),
                ],
            ),
            dbc.Row(
                id="careers_logged_in_div",
                style={"display": "none"},
                justify="center",
                children=[
                    dbc.Col(
                        md=10,
                        lg=8,
                        xl=6,
                        children=[
                            html.H2("Submit your Resume and Cover Letter"),
                            html.Label("Only PDF files under 2 MB in size accepted"),
                            # File Upload Component
                            dcc.Upload(
                                id="careers_file_upload",
                                children=html.Div(
                                    ["Drag and Drop or ", html.A("Select Files")]
                                ),
                                style={
                                    "width": "100%",
                                    "height": "60px",
                                    "lineHeight": "60px",
                                    "borderWidth": "1px",
                                    "borderStyle": "dashed",
                                    "borderRadius": "5px",
                                    "textAlign": "center",
                                    "margin": "10px 0",
                                },
                                multiple=True,
                                accept=".pdf",
                            ),
                            # Upload Status
                            html.Div(id="careers_files_uploaded_status"),
                            # Files List
                            html.Div(id="careers_files_list"),
                            # Files list message
                            html.Div(id="careers_files_list_message"),
                            # Application Form
                            dbc.Form(
                                id="careers_form",
                                class_name="mt-4",
                                children=[
                                    dbc.Label(
                                        "Job Preference", className="mb-1 fw-bold"
                                    ),
                                    dbc.RadioItems(
                                        id="careers_role_selected",
                                        options=[
                                            {
                                                "label": "Technical sales",
                                                "value": "Technical sales",
                                            },
                                            {
                                                "label": "Design engineer",
                                                "value": "Design engineer",
                                            },
                                            {
                                                "label": "Assembler",
                                                "value": "Assembler",
                                            },
                                            {
                                                "label": "Field service technician",
                                                "value": "Field service technician",
                                            },
                                            {
                                                "label": "Other role",
                                                "value": "Other role",
                                            },
                                        ],
                                        className="mb-3",
                                        # Retain the selected value on page refresh
                                        persistence=True,
                                    ),
                                    dbc.Label(
                                        "Message to IJACK Human Resources",
                                        className="mt-4 mb-1 fw-bold",
                                    ),
                                    dbc.Textarea(
                                        id="message",
                                        rows=4,
                                        className="mb-3",
                                        placeholder="Enter your message here",
                                        persistence=True,
                                    ),
                                    # Submit Button
                                    dbc.Button(
                                        "Submit Application",
                                        id="careers_submit_btn",
                                        color="dark",
                                        type="submit",
                                        class_name="g-recaptcha",
                                        # **{
                                        #     "data-sitekey": os.getenv(
                                        #         "RECAPTCHA_SITE_KEY", ""
                                        #     ),
                                        #     "data-action": "careers",
                                        # },
                                    ),
                                    dbc.Alert(
                                        id="careers_submit_alert",
                                        color="success",
                                        class_name="mt-3",
                                        style={"display": "none"},
                                    ),
                                    # For the notifications
                                    html.Div(id="careers_dmc_notifications_container"),
                                ],
                            ),
                        ],
                    )
                ],
            ),
        ],
    )


@callback(
    Output("careers_stylesheet", "href"),
    Input("hidden_signal_careers", "children"),
)
def get_custom_stylesheet(_):
    """Get the custom stylesheet for this page"""
    return url_for("static", filename="src/css/styles_dash_careers.css")


# Callback to check authentication and update content
@callback(
    Output("careers_not_logged_in_div", "style"),
    Output("careers_logged_in_div", "style"),
    Input("careers_interval", "n_intervals"),
    State("careers_not_logged_in_div", "style"),
    State("careers_logged_in_div", "style"),
)
def update_auth_content(_, not_logged_in_style, logged_in_style):
    """
    Updates the page content based on authentication status
    Uses Flask-Login's current_user to check authentication
    """
    if current_user.is_authenticated:
        if logged_in_style == {"display": "none"} or not_logged_in_style != {
            "display": "none"
        }:
            return {"display": "none"}, {}
    else:
        if not_logged_in_style == {"display": "none"} or logged_in_style != {
            "display": "none"
        }:
            return {}, {"display": "none"}
    raise PreventUpdate


@callback(
    Output("careers_files_list", "children"),
    Input("hidden_signal_careers", "children"),
    Input("careers_store_trigger", "data"),
    Input({"type": "delete-file", "index": ALL}, "n_clicks"),
)
def update_files_list(_, __, delete_clicks):
    """
    Update the list of uploaded files and handle file deletions
    """
    if not current_user.is_authenticated:
        return "Please login to view files"

    id_triggered: str = get_id_triggered()
    # Check if this was triggered by a delete button
    if id_triggered and "delete-file" in id_triggered:
        # Extract the file ID from the triggered button
        triggered_id = id_triggered.split(".")[0]
        file_id = json.loads(triggered_id)["index"]
        try:
            # Find and delete the file from the database
            file_to_delete = CareerFile.query.get(file_id)
            if file_to_delete and file_to_delete.user_id == getattr(
                current_user, "id", None
            ):
                db.session.delete(file_to_delete)
                db.session.commit()
        except Exception as e:
            # Log the error but don't raise it to the user
            current_app.logger.error(f"Error deleting file: {str(e)}")

    # Get list of files uploaded by the current user
    files = (
        CareerFile.query.with_entities(CareerFile.id, CareerFile.name)
        .filter_by(user_id=getattr(current_user, "id", None))
        .all()
    )
    if not files:
        return "No files uploaded"

    files = [
        {
            "id": file[0],
            "name": file[1],
        }
        for file in files
    ]

    files_list = [
        dbc.Row(
            dbc.Col(
                class_name="d-flex mt-1 align-items-center",
                id=f"file-{file['id']}",
                children=[
                    # PDF Icon
                    html.Img(
                        src=url_for("static", filename="img/pdf.jpeg"),
                        width="25px",
                        height="25px",
                        alt="PDF Icon",
                        className="flex-shrink-0",
                    ),
                    # File name
                    html.H5(file["name"], className="ms-3 my-0 flex-grow-1"),
                    # Delete button
                    html.Button(
                        "X",  # Using X symbol for delete
                        className="btn btn-link text-danger p-0 ms-2 delete-btn",
                        id={"type": "delete-file", "index": file["id"]},
                        n_clicks=0,
                    ),
                ],
            )
        )
        for file in files
    ]
    return [
        # Make this text bold
        html.Label("Uploaded Files", className="mt-3 mb-2 fw-bold"),
        *files_list,
    ]


@callback(
    Output("careers_store_trigger", "data"),
    Output("careers_dmc_notifications_container", "children", allow_duplicate=True),
    Input("careers_file_upload", "last_modified"),
    State("careers_file_upload", "contents"),
    State("careers_file_upload", "filename"),
    prevent_initial_call=True,
)
def handle_upload(last_modified, contents, filename):
    """
    Handle file upload and validate files
    """
    if not contents:
        raise PreventUpdate

    def return_vars(title, message, color, loading, action, icon):
        return (
            1,
            dmc.Notification(
                title=title,
                message=message,
                color=color,
                loading=loading,
                action=action,
                icon=icon,
                autoClose=10_000,  # 10 seconds
            ),
        )

    # Ensure user is authenticated
    if not current_user.is_authenticated:
        return return_vars(
            title="Error uploading files",
            message="Please login to upload files",
            color="red",
            loading=False,
            action="show",
            icon="\u2717",
        )

    n_files = len(contents)
    filenames_str = ", ".join(filename)
    try:
        for i in range(n_files):
            # Secure the filename
            sec_filename = secure_filename(filename[i])

            # Get file content
            content_type, content_string = contents[i].split(",")
            decoded = base64.b64decode(content_string)

            # Validate file type (PDF only)
            if not sec_filename.lower().endswith(".pdf") or not content_type.startswith(
                "data:application/pdf"
            ):
                # return "Only PDF files are accepted"
                return return_vars(
                    title="Error uploading files",
                    message="Only PDF files are accepted",
                    color="red",
                    loading=False,
                    action="show",
                    icon="\u2717",
                )

            # Validate file size (2MB limit)
            if len(decoded) > 2 * 1024 * 1024:
                # return "File size must be under 2MB"
                return return_vars(
                    title="Error uploading files",
                    message="File size must be under 2MB",
                    color="red",
                    loading=False,
                    action="show",
                    icon="\u2717",
                )

            record = CareerFile.query.filter_by(
                name=sec_filename, user_id=getattr(current_user, "id", None)
            ).first()
            if record is None:
                # Create a new record
                record = CareerFile(
                    name=sec_filename,
                    user_id=getattr(current_user, "id", None),
                    pdf=decoded,
                )
                db.session.add(record)
                current_app.logger.info("Record was created!")
            else:
                # Update existing record, like a put request
                record.pdf = decoded
                record.timestamp_inserted_utc = utcnow_naive()
                current_app.logger.info("Record was updated!")

        db.session.commit()
        # return f"Successfully uploaded '{filenames_str}'"
        # return dmc.Notification(
        #     title="File(s) uploaded",
        #     message=f"Successfully uploaded '{filenames_str}'",
        #     color="green",
        #     loading=False,
        #     action="show",
        #     # Checkmark symbol 2713 or X symbol 2717
        #     # icon="\u2713" if success else "\u2717",
        #     icon="\u2713",
        #     # icon=DashIconify(icon="akar-icons:circle-check"),
        #     # position="bottom-right",
        #     autoClose=10_000,  # 10 seconds
        # )
        return return_vars(
            title="File(s) uploaded",
            message=f"Successfully uploaded '{filenames_str}'",
            color="green",
            loading=False,
            action="show",
            icon="\u2713",
        )

    except Exception as err:
        # return f"Error uploading file(s): {str(err)}"
        # return dmc.Notification(
        #     title="Error uploading files",
        #     message=str(err),
        #     color="red",
        #     loading=False,
        #     action="show",
        #     # Checkmark symbol 2713 or X symbol 2717
        #     # icon="\u2713" if success else "\u2717",
        #     icon="\u2717",
        #     # icon=DashIconify(icon="akar-icons:circle-check"),
        #     # position="bottom-right",
        #     autoClose=10_000,  # 10 seconds
        # )
        return return_vars(
            title="Error uploading files",
            message=str(err),
            color="red",
            loading=False,
            action="show",
            icon="\u2717",
        )


@callback(
    Output("store_site_key_action_careers", "data"),
    Input("hidden_signal_careers", "children"),
    prevent_initial_call=False,
)
def store_google_recaptcha_sitekey(_):
    """Store the reCAPTCHA sitekey in the hidden div"""
    return {"site_key": os.getenv("RECAPTCHA_SITE_KEY", ""), "action": "careers"}


# The following are in app/dash_assets/recaptcha.js
clientside_callback(
    ClientsideFunction(
        namespace="careers_namespace",
        function_name="initialize_recaptcha_in_js",
    ),
    Output("store_nothing_careers", "data"),
    Input("store_site_key_action_careers", "data"),
    prevent_initial_call=True,
)

clientside_callback(
    ClientsideFunction(
        namespace="careers_namespace",
        function_name="on_submit_application",
    ),
    Output("store_recaptcha_response_careers", "data"),
    Input("careers_submit_btn", "n_clicks"),
    State("store_site_key_action_careers", "data"),
    prevent_initial_call=True,
)


# Callback to handle form submission
@callback(
    Output("careers_submit_alert", "children"),
    Output("careers_submit_alert", "color"),
    Output("careers_submit_alert", "style"),
    Input("store_recaptcha_response_careers", "data"),
    State("careers_role_selected", "value"),
    State("message", "value"),
    prevent_initial_call=True,
)
def handle_submission(recaptcha_response, careers_role_selected_value, message):
    """Handle form submission with validation"""
    if not recaptcha_response:
        raise PreventUpdate

    def return_vars(message: str, color: str, style: dict):
        return message, color, style

    # Validate user is authenticated
    if not current_user.is_authenticated:
        return return_vars(
            message="Please login to submit application", color="warning", style={}
        )

    # Validate required fields
    if not careers_role_selected_value:
        return return_vars(message="Please select a role", color="warning", style={})
    if not message:
        return return_vars(message="Please enter a message", color="warning", style={})

    if not recaptcha_is_good(recaptcha_response, expected_action="careers"):
        return return_vars(
            message="reCAPTCHA verification failed", color="danger", style={}
        )

    files_already_uploaded = CareerFile.query.filter_by(
        user_id=getattr(current_user, "id", None)
    ).all()

    if len(files_already_uploaded) == 0:
        return return_vars(message="Please upload a resume", color="warning", style={})

    # Store the application in the database
    record = CareerApplication(
        user_id=getattr(current_user, "id", None),
        job_type=careers_role_selected_value,
        message=message,
        timestamp_inserted_utc=utcnow_naive(),
    )
    db.session.add(record)

    # Flush so the record object has the primary key id
    db.session.flush()
    db.session.refresh(record)

    # Get the application ID
    application_id: int = record.id

    # For each file already uploaded, relate it to the application
    # in the many-to-many relationship table
    for file in files_already_uploaded:
        many_to_many_model = CareerApplicationFile(
            career_application_id=application_id,
            career_file_id=file.id,
        )
        db.session.add(many_to_many_model)

    # Commit the changes
    db.session.commit()

    # Send an email to HR with the PDF files attached
    user = db.session.get(User, getattr(current_user, "id", None))
    first_name = user.first_name
    last_name = user.last_name
    email = user.email
    phone = user.phone

    to_emails = ["<EMAIL>"]
    subject = "myijack.com New Resume Submission"
    sender = "IJACK <<EMAIL>>"
    text_body = f"Message from: {first_name} {last_name} \nEmail address: {email}"
    text_body += f"\nPhone number: {phone} \nPreferred job: {careers_role_selected_value} \nMessage: {message}"

    testing = False
    if os.getenv("FLASK_CONFIG", "development") in ("development", "wsl", "testing"):
        to_emails = ADMIN_EMAILS

    files_list = []
    for file in files_already_uploaded:
        files_list = add_file_to_list(
            filename=file.name,
            list_=files_list,
            file_binary=file.pdf,  # 'bytes' object from database
        )

    send_email(
        subject=subject,
        sender=sender,
        to_emails=to_emails,
        text_body=text_body,
        files_list=files_list,
        testing=testing,
    )

    return return_vars(
        message=f"Thank you very much for your interest in a career at IJACK. Your application was submitted successfully at {utcnow_naive()} UTC time",
        color="success",
        style={},
    )
