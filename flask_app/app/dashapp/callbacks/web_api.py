from datetime import date

import dash_bootstrap_components as dbc
from dash import Input, Output, callback, html

from app.dashapp.layout_utils import get_ag_grid
from app.databases import run_sql_query


def web_api_layout():
    """Return the layout for the web API page"""

    return dbc.Container(
        [
            dbc.Row(
                justify="center",
                children=dbc.Col(
                    xs=12,
                    lg=10,
                    children=[
                        html.Div(id="web_api_hidden_div", style={"display": "none"}),
                        html.H1(
                            "IJACK Web API Information",
                            className="text-center my-4",
                        ),
                        html.P(
                            "Collect your IJACK data using the IJACK Web API. The IJACK Web API is a RESTful API that allows you to access your data in real time.",
                        ),
                        html.P(
                            "You can also use the IJACK Web API to send commands to your IJACKs, such as changing setpoints or stopping and starting the pumps.",
                        ),
                        html.P(
                            [
                                "Please ",
                                html.A(
                                    "contact us",
                                    href="/contact/",
                                    style={"color": "blue"},
                                ),
                                " to request a secure JSON 'bearer' token for access.",
                            ],
                        ),
                    ],
                ),
            ),
            dbc.Row(
                justify="center",
                class_name="mt-4",
                children=dbc.Col(
                    xs=12,
                    # lg=10,
                    # xl=8,
                    children=[
                        dbc.Button(
                            "Download CSV",
                            id="web_api_download_csv_btn",
                            n_clicks=0,
                            className="my-2",
                            color="secondary",
                            outline=True,
                            size="sm",
                        ),
                        get_ag_grid(
                            id="web_api_ag_grid",
                            # rowData=rows,
                            columnSize="sizeToFit",
                            paginationPageSize=10,
                            csv_filename=f"IJACK Web API Metrics {date.today().strftime('%Y-%m-%d')}.csv",
                            columnDefs=[
                                {
                                    "field": "abbrev",
                                    "headerName": "Metric Name",
                                    "cellDataType": "text",
                                },
                                {
                                    "field": "machine",
                                    "headerName": "Machine(s)",
                                    "cellDataType": "text",
                                },
                                {
                                    "field": "item",
                                    "headerName": "Description",
                                    "cellDataType": "text",
                                    "initialWidth": 400,
                                    "maxWidth": 500,
                                },
                                {
                                    "field": "units",
                                    "headerName": "Units",
                                    "cellDataType": "text",
                                },
                                {
                                    "field": "writable",
                                    "headerName": "Writable",
                                    "cellDataType": "boolean",
                                    "cellRenderer": "BooleanFormatter",
                                },
                                {
                                    "field": "min_val",
                                    "headerName": "Min Value",
                                    "cellDataType": "number",
                                    "cellRenderer": "NumberFormatter1",
                                },
                                {
                                    "field": "max_val",
                                    "headerName": "Max Value",
                                    "cellDataType": "number",
                                    "cellRenderer": "NumberFormatter1",
                                },
                            ],
                        ),
                    ],
                ),
            ),
            dbc.Row(
                justify="center",
                children=dbc.Col(
                    xs=12,
                    lg=10,
                    children=[
                        html.H2(
                            "Detailed Instructions",
                            className="text-center mt-5 mb-4",
                        ),
                        html.H4(
                            "Collect Data with GET Requests",
                            className="mt-5 mb-3",
                        ),
                        html.Div(
                            className="mt-3",
                            children='The IJACK web API data can be requested by sending an HTTP "GET" request to:',
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                            ],
                        ),
                        html.Div(
                            className="mt-3",
                            children="200xxx is your IJACK's power unit serial number.",
                        ),
                        html.Div(
                            className="mt-3",
                            children=[
                                'For secure authorization, IJACK requires a "bearer token" to access the data. ',
                                "Please ",
                                html.A(
                                    "contact us",
                                    href="/contact/",
                                    style={"color": "blue"},
                                ),
                                " to request one.",
                            ],
                        ),
                        html.H4(
                            "Remote Control with POST Requests",
                            className="mt-5 mb-3",
                        ),
                        html.Div(
                            className="mt-3",
                            children='For remote-controlling your unit via the web API, send the following HTTP "POST" requests:',
                        ),
                        html.Div(
                            className="mt-3",
                            children="To stop or start the hydraulics:",
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                                html.Strong("/hydraulics"),
                            ],
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                html.Strong("JSON body:", style={"font-weight": 800}),
                                html.Br(),
                                '{"hydraulics": 0} to stop the unit, or ',
                                html.Br(),
                                '{"hydraulics": 1} to start the unit',
                            ],
                        ),
                        html.Div(
                            className="mt-3",
                            children="To lock or unlock the unit:",
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                                html.Strong("/lockout"),
                            ],
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                html.Strong("JSON body:", style={"font-weight": 800}),
                                html.Br(),
                                '{"lockout": 1} to lock the unit, or ',
                                html.Br(),
                                '{"lockout": 0} to unlock the unit',
                            ],
                        ),
                        html.Div(
                            className="mt-3",
                            children="To change the suction pressure setpoint:",
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                                html.Strong("/suction"),
                            ],
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                html.Strong("JSON body:", style={"font-weight": 800}),
                                html.Br(),
                                '{"suction": 20}',
                                html.Br(),
                                "(minimum 0 PSI, maximum 700 PSI)",
                            ],
                        ),
                        html.Div(
                            className="mt-3",
                            children="To change the max discharge pressure setpoint:",
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                                html.Strong("/max_discharge"),
                            ],
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                html.Strong("JSON body:", style={"font-weight": 800}),
                                html.Br(),
                                '{"max_discharge": 800}',
                                html.Br(),
                                "(minimum 1 PSI, maximum 1350 PSI)",
                            ],
                        ),
                        html.Div(
                            className="mt-3",
                            children="To change the max shutdown pressure setpoint:",
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                                html.Strong("/max_shutdown_pressure"),
                            ],
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                html.Strong("JSON body:", style={"font-weight": 800}),
                                html.Br(),
                                '{"max_shutdown_pressure": 900}',
                                html.Br(),
                                "(minimum 100 PSI, maximum 1400 PSI)",
                            ],
                        ),
                        html.Div(
                            className="mt-3",
                            children="To change the max speed in percent:",
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                                html.Strong("/max_speed"),
                            ],
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                html.Strong("JSON body:", style={"font-weight": 800}),
                                html.Br(),
                                '{"max_speed": 100}',
                                html.Br(),
                                "(minimum 2, maximum 100)",
                            ],
                        ),
                        html.Div(
                            className="mt-3",
                            children="To change the min speed in percent:",
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                                html.Strong("/min_speed"),
                            ],
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                html.Strong("JSON body:", style={"font-weight": 800}),
                                html.Br(),
                                '{"min_speed": 5}',
                                html.Br(),
                                "(minimum 5, maximum 100)",
                            ],
                        ),
                        html.Div(
                            className="mt-3",
                            children="To change the horsepower upper limit:",
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                "https://api.myijack.com/v2/data/",
                                html.Em("200xxx"),
                                html.Strong("/hp_limit"),
                            ],
                        ),
                        html.Div(
                            className="mt-3 ms-3",
                            children=[
                                html.Strong("JSON body:", style={"font-weight": 800}),
                                html.Br(),
                                '{"hp_limit": 150}',
                                html.Br(),
                                "(minimum 15, maximum 200)",
                            ],
                        ),
                    ],
                ),
            ),
        ],
    )


@callback(
    Output("web_api_ag_grid", "rowData"),
    Input("web_api_hidden_div", "children"),
    prevent_initial_call=False,
)
def populate_web_api_ag_grid(_):
    """Populate the web API AG Grid with data"""

    sql = """
SELECT
    --t1.holding_reg_40k,
    --t1.address,
    --t1.n_registers,
    t2.abbrev,
    t2.machine,
    t2.item,
    t2.units,
    t1.writable_web_api as writable,
    t1.min_val,
    t1.max_val
-- 	, description,
-- 	num_bytes, data_type, resolution, offset_, number_,
-- 	interesting, rcom_name, signed, decimals
from public.modbus_holding_registers t1
inner join public.map_abbrev_item t2
    on t1.abbrev_id = t2.id
where t1.in_web_api = true
order by t2.abbrev
;
    """
    rows, _ = run_sql_query(sql, db_name="ijack", echo=False, as_dict=True)
    return rows


@callback(
    Output("web_api_ag_grid", "exportDataAsCsv"),
    Input("web_api_download_csv_btn", "n_clicks"),
    prevent_initial_call=True,
)
def export_data_as_csv(n_clicks):
    """Export the data as CSV"""
    if n_clicks:
        return True
    return False
