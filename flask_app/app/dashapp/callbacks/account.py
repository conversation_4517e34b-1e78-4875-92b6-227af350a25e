import datetime
from typing import Dict, List

import dash_bootstrap_components as dbc
from dash import (
    ClientsideFunction,
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
    no_update,
)
from dash.exceptions import PreventUpdate
from flask_login import current_user

from app.config import TAB_ACCOUNT_QUOTES
from app.dashapp.layout_utils import get_ag_grid
from app.dashapp.utils import get_id_triggered, log_function_caller


def get_modal():
    """Get a modal for displaying messages"""
    return dbc.Modal(
        id="account_modal",
        is_open=False,
        size="lg",
        centered=True,  # Vertically centered
        children=[
            dbc.<PERSON>Header(id="account_modal_header"),
            dbc.<PERSON>dalBody(
                id="account_modal_body",
                children=[
                    # Error message div
                    html.Div(
                        id="account_modal_error_msg_div",
                        style={"display": "none"},
                        children=[
                            html.Div(id="account_modal_error_msg"),
                            html.Div(
                                dbc.Button(
                                    "Close",
                                    id="modal-close-button",
                                    color="secondary",
                                    className="mt-3",
                                ),
                                className="text-center",
                            ),
                        ],
                    ),
                    # Approval message div
                    html.Div(
                        id="account_modal_quote_approved_msg",
                        style={"display": "none"},
                        children=[
                            html.P(
                                "Thank you! The quote has been approved successfully."
                            ),
                            html.P(
                                "A confirmation email has been sent to you and the service team."
                            ),
                            html.Div(
                                dbc.Button(
                                    "Return to Quotes",
                                    id="modal-return-button",
                                    color="primary",
                                    className="mt-3",
                                ),
                                className="text-center",
                            ),
                        ],
                    ),
                    html.Div(
                        id="account_modal_quote_rejected_msg",
                        style={"display": "none"},
                        children=[
                            html.P("Are you sure you want to reject this quote?"),
                            html.P("Please provide a reason for the rejection:"),
                            dbc.Textarea(
                                id="rejection-reason",
                                placeholder="Enter reason for rejection...",
                                className="mb-3",
                                style={"height": "120px"},
                            ),
                            html.Div(
                                [
                                    dbc.Button(
                                        "Cancel",
                                        id="modal-cancel-button",
                                        color="secondary",
                                        className="me-2",
                                    ),
                                    dbc.Button(
                                        "Confirm Rejection",
                                        id="modal-confirm-reject-button",
                                        color="danger",
                                    ),
                                ],
                                className="d-flex justify-content-end",
                            ),
                        ],
                    ),
                ],
            ),
        ],
    )


def get_signals():
    """Signals for account page"""
    return html.Div(
        [
            # Hidden signal value, for running callback on page load
            html.Div(id="hidden_signal_account", style={"display": "none"}),
            dcc.Location(id="location_account", refresh=True),
            dcc.Interval(
                id="interval_account",
                interval=1_000,
                disabled=True,
                n_intervals=0,
                max_intervals=61,
            ),
            # Store component for quote data
            dcc.Store(id="selected_quote_data", storage_type="session"),
            # Store component for quote ID
            dcc.Store(id="selected_quote_id", storage_type="session"),
            # Store component for signature data
            dcc.Store(id="signature_data", storage_type="session"),
            # Custom CSS stylesheet to make the radio items look like Bootstrap buttons
            html.Link(
                rel="stylesheet",
                id="signature_stylesheet",
            ),
        ]
    )


def get_quotes_table():
    """Creates AG Grid table for displaying work order quotes"""

    # Column definitions for AG Grid
    columnDefs = [
        {
            "headerName": "Quote #",
            "field": "id",
            "sortable": True,
            "filter": True,
            "width": 110,
        },
        {
            "headerName": "Date",
            "field": "date",
            "sortable": True,
            "filter": True,
            "cellRenderer": "DateFormatter",
            "width": 130,
        },
        {
            "headerName": "Work Order",
            "field": "work_order",
            "sortable": True,
            "filter": True,
            "width": 130,
        },
        {
            "headerName": "Customer",
            "field": "customer",
            "sortable": True,
            "filter": True,
            "width": 200,
        },
        {
            "headerName": "Description",
            "field": "description",
            "sortable": True,
            "filter": True,
            "width": 250,
        },
        {
            "headerName": "Total",
            "field": "total",
            "sortable": True,
            "filter": True,
            "cellRenderer": "CurrencyFormatter",
            "cellRendererParams": {"decimals": 0},
            "width": 130,
        },
        {
            "headerName": "Status",
            "field": "status",
            "sortable": True,
            "filter": True,
            "cellRenderer": "QuoteStatusFormatter",
            "width": 120,
        },
        {
            "headerName": "Expires",
            "field": "expires",
            "sortable": True,
            "filter": True,
            "cellRenderer": "DateFormatter",
            "width": 130,
        },
        {
            "headerName": "View Quote",
            "field": "id",
            # "headerTooltip": "",
            # "cellRenderer": "ViewButtonRenderer",
            "cellRenderer": "ButtonDBC",  # Use custom button renderer
            "cellRendererParams": {"color": "primary", "label": "View"},
            "width": 110,
            "sortable": False,
            "filter": False,
        },
        {
            "headerName": "Approve Quote",
            "field": "id",
            # "headerTooltip": "",
            # "cellRenderer": "ViewButtonRenderer",
            "cellRenderer": "ButtonDBC",  # Use custom button renderer
            "cellRendererParams": {"color": "success", "label": "Approve"},
            "width": 110,
            "sortable": False,
            "filter": False,
        },
    ]

    return html.Div(
        id="quotes-table-container",
        children=[
            html.H4("Work order quotes assigned to me", className="mb-4 text-center"),
            html.Div(
                className="border p-3 rounded bg-light",
                children=[
                    dbc.Row(
                        class_name="justify-content-between",
                        children=[
                            dbc.Col(
                                dbc.Input(
                                    id="quotes-search",
                                    type="text",
                                    placeholder="Search quotes...",
                                    className="mb-3",
                                ),
                                xs=6,
                            ),
                            dbc.Col(
                                dbc.Select(
                                    id="quotes-status-filter",
                                    options=[
                                        {"label": "All Statuses", "value": "all"},
                                        {"label": "Pending", "value": "Pending"},
                                        {"label": "Approved", "value": "Approved"},
                                        {"label": "Rejected", "value": "Rejected"},
                                        {"label": "Expired", "value": "Expired"},
                                    ],
                                    value="all",
                                    className="mb-3",
                                ),
                                xs=6,
                                md=3,
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "Refresh",
                                    id="quotes-refresh-button",
                                    color="primary",
                                    className="mb-3",
                                ),
                                # xs=3,
                                # ms-auto class (margin-start: auto) pushes the column to the right if there's extra space in the row
                                className="text-end ms-auto",
                            ),
                        ],
                    ),
                    dbc.Row(
                        className="mt-1",
                        children=dbc.Col(
                            dbc.Spinner(
                                id="account_col",
                                color="success",
                                children=get_ag_grid(
                                    # Need to set the AgGrid on page load since we're updating the actual values from the grid,
                                    # so there are callbacks that depend on this grid being on the page.
                                    id="account_ag_grid",
                                    # Set the "id" as the row id or "value" in the row data
                                    getRowId="params.data.id",
                                    columnDefs=columnDefs,
                                    rowData=[],  # Will be populated via callback
                                    # dashGridOptions={
                                    #     "rowSelection": "single",
                                    #     "pagination": True,
                                    #     "paginationPageSize": 10,
                                    #     "domLayout": "autoHeight",
                                    # },
                                    # className="ag-theme-alpine",
                                    # style={"height": "100%", "width": "100%"},
                                ),
                            ),
                        ),
                    ),
                ],
            ),
        ],
    )


def get_quote_detail():
    """Creates layout for displaying and approving individual quote"""
    return html.Div(
        [
            dbc.Button(
                "Back to Quotes",
                id="back-to-quotes-button",
                color="secondary",
                className="mb-4",
            ),
            html.Div(
                [
                    html.H4("Quote Details", className="mb-4"),
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    html.Strong("Quote #:", className="me-2"),
                                    html.Span(id="quote-id"),
                                ],
                                width=4,
                                className="mb-3",
                            ),
                            dbc.Col(
                                [
                                    html.Strong("Date:", className="me-2"),
                                    html.Span(id="quote-date"),
                                ],
                                width=4,
                                className="mb-3",
                            ),
                            dbc.Col(
                                [
                                    html.Strong("Status:", className="me-2"),
                                    html.Span(id="quote-status"),
                                ],
                                width=4,
                                className="mb-3",
                            ),
                        ]
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    html.Strong("Customer:", className="me-2"),
                                    html.Span(id="quote-customer"),
                                ],
                                width=6,
                                className="mb-3",
                            ),
                            dbc.Col(
                                [
                                    html.Strong("Work Order:", className="me-2"),
                                    html.Span(id="quote-work-order"),
                                ],
                                width=6,
                                className="mb-3",
                            ),
                        ]
                    ),
                    dbc.Row(
                        [
                            dbc.Col(
                                [
                                    html.Strong("Description:", className="me-2"),
                                    html.Div(id="quote-description", className="mt-1"),
                                ],
                                width=12,
                                className="mb-3",
                            ),
                        ]
                    ),
                    html.Hr(),
                    # Line items table
                    html.H5("Line Items", className="mt-4 mb-3"),
                    html.Div(id="quote-line-items"),
                    dbc.Row(
                        [
                            dbc.Col(width=6),
                            dbc.Col(
                                [
                                    html.Div(
                                        [
                                            html.Div(
                                                [
                                                    html.Strong("Subtotal:"),
                                                    html.Span(
                                                        id="quote-subtotal",
                                                        className="float-end",
                                                    ),
                                                ],
                                                className="mb-2",
                                            ),
                                            html.Div(
                                                [
                                                    html.Strong("Tax:"),
                                                    html.Span(
                                                        id="quote-tax",
                                                        className="float-end",
                                                    ),
                                                ],
                                                className="mb-2",
                                            ),
                                            html.Div(
                                                [
                                                    html.Strong("Total:"),
                                                    html.Span(
                                                        id="quote-total",
                                                        className="float-end",
                                                    ),
                                                ],
                                                className="mb-2 fw-bold",
                                            ),
                                        ],
                                        className="border p-3 rounded",
                                    )
                                ],
                                width=6,
                                className="mt-3",
                            ),
                        ]
                    ),
                    html.Hr(),
                    # Terms and conditions
                    html.H5("Terms & Conditions", className="mt-4 mb-3"),
                    html.Div(
                        id="quote-terms", className="mb-4 border p-3 rounded bg-light"
                    ),
                    # Signature pad section
                    html.H5("Approval", className="mt-4 mb-3"),
                    html.Div(
                        [
                            html.P("Please sign below to approve this quote:"),
                            html.Div(
                                id="signature-pad-container",
                                className="border rounded mb-3 bg-white",
                                style={"height": "200px"},
                            ),
                            dbc.Row(
                                [
                                    dbc.Col(
                                        dbc.Button(
                                            "Clear Signature",
                                            id="clear-signature-button",
                                            color="secondary",
                                            className="w-100",
                                        ),
                                        width=6,
                                    ),
                                    dbc.Col(
                                        dbc.Button(
                                            "Approve Quote",
                                            id="approve-quote-button",
                                            color="success",
                                            className="w-100",
                                        ),
                                        width=6,
                                    ),
                                ]
                            ),
                            dbc.FormText(
                                "By signing and approving, you agree to the terms and conditions of this quote.",
                                className="mt-2",
                            ),
                        ],
                        className="border p-3 rounded",
                    ),
                    # Rejection section
                    html.Div(
                        [
                            dbc.Button(
                                "Reject Quote",
                                id="reject-quote-button",
                                color="danger",
                                className="mt-4",
                            ),
                        ],
                        className="text-center mt-3",
                    ),
                ],
                className="border p-3 rounded bg-light",
            ),
        ],
        id="quote-detail-container",
        style={"display": "none"},
    )


def get_account_page(has_app_context: bool = True) -> html.Div:
    """Account page"""

    tabs_list = [
        dbc.Tab(
            label="Quotes",
            tab_id=TAB_ACCOUNT_QUOTES,
        ),
        # dbc.Tab(
        #     label="Alerts",
        #     tab_id=TAB_ACCOUNT_ALERTS,
        # ),
    ]

    return html.Div(
        [
            dbc.Row(
                class_name="justify-content-sm-center my-3",  # put the tabs in the middle
                children=dbc.Col(
                    [
                        # Tabs for either map or unit list
                        dbc.Tabs(
                            tabs_list,
                            id="tabs_for_nav",
                            active_tab=TAB_ACCOUNT_QUOTES,  # default tab
                            className="justify-content-center",
                            persistence=True,
                            persistence_type="local",
                        ),
                    ],
                ),
            ),
            dbc.Row(
                justify="center",
                children=dbc.Col(
                    width=12,
                    children=[
                        # html.H2("My Work Order Quotes", className="mb-4 mt-3"),
                        # Print page button
                        # dbc.Button(
                        #     "Print Page",
                        #     id="account_print_page_btn",
                        #     color="primary",
                        #     className="mb-3",
                        # ),
                        get_quotes_table(),
                        get_quote_detail(),
                    ],
                ),
            ),
        ],
    )


def account_layout(has_app_context: bool = True):
    """Layout for account form"""

    # The actual layout starts here
    layout = dbc.Container(
        [
            # Hidden signal value, for running callback on page load
            get_signals(),
            get_modal(),
            get_account_page(has_app_context=has_app_context),
        ],
        fluid=True,
        className="py-3",
    )

    return layout


@callback(
    Output("signature_stylesheet", "href"),
    Input("hidden_signal_account", "children"),
)
def get_custom_stylesheet(_):
    """Get the custom stylesheet for this page"""
    # return assets.url_for("styles_dash_signature.css")
    return no_update


def get_sample_quotes() -> List[Dict]:
    """
    Generate sample quotes data for demonstration purposes
    In a real application, this would be replaced with database queries
    """
    today = datetime.datetime.now()

    quotes = [
        {
            "id": 1001,
            "date": (today - datetime.timedelta(days=10)).isoformat(),
            "customer": "Acme Oil & Gas",
            "work_order": "WO-2345",
            "description": "Pump service and repair - Site A",
            "subtotal": 3450.00,
            "tax": 172.50,
            "total": 3622.50,
            "status": "Pending",
            "expires": (today + datetime.timedelta(days=20)).isoformat(),
            "lineItems": [
                {
                    "item": "LABOR-001",
                    "description": "Technician labor - Standard rate",
                    "quantity": 8,
                    "unitPrice": 125.00,
                    "amount": 1000.00,
                },
                {
                    "item": "PARTS-456",
                    "description": "Replacement pump seals",
                    "quantity": 2,
                    "unitPrice": 450.00,
                    "amount": 900.00,
                },
                {
                    "item": "PARTS-789",
                    "description": "Control module",
                    "quantity": 1,
                    "unitPrice": 1550.00,
                    "amount": 1550.00,
                },
            ],
            "terms": "Payment due within 30 days of quote approval. Quote valid for 30 days from issue date.",
        },
        {
            "id": 1002,
            "date": (today - datetime.timedelta(days=5)).isoformat(),
            "customer": "XYZ Energy",
            "work_order": "WO-2346",
            "description": "Gateway installation and configuration",
            "subtotal": 2850.00,
            "tax": 142.50,
            "total": 2992.50,
            "status": "Pending",
            "expires": (today + datetime.timedelta(days=25)).isoformat(),
            "lineItems": [
                {
                    "item": "EQUIP-223",
                    "description": "IJACK Gateway device",
                    "quantity": 1,
                    "unitPrice": 1800.00,
                    "amount": 1800.00,
                },
                {
                    "item": "LABOR-001",
                    "description": "Technician labor - Standard rate",
                    "quantity": 6,
                    "unitPrice": 125.00,
                    "amount": 750.00,
                },
                {
                    "item": "TRAVEL-001",
                    "description": "Travel expenses",
                    "quantity": 1,
                    "unitPrice": 300.00,
                    "amount": 300.00,
                },
            ],
            "terms": "Payment due within 30 days of quote approval. Quote valid for 30 days from issue date.",
        },
        {
            "id": 1003,
            "date": (today - datetime.timedelta(days=15)).isoformat(),
            "customer": "PetroTech Inc",
            "work_order": "WO-2342",
            "description": "Annual maintenance - 3 pumps",
            "subtotal": 5250.00,
            "tax": 262.50,
            "total": 5512.50,
            "status": "Approved",
            "expires": (today + datetime.timedelta(days=15)).isoformat(),
            "lineItems": [
                {
                    "item": "MAINT-001",
                    "description": "Annual maintenance package - per pump",
                    "quantity": 3,
                    "unitPrice": 1500.00,
                    "amount": 4500.00,
                },
                {
                    "item": "PARTS-222",
                    "description": "Consumable parts kit",
                    "quantity": 3,
                    "unitPrice": 250.00,
                    "amount": 750.00,
                },
            ],
            "terms": "Payment due within 30 days of quote approval. Annual maintenance package includes 4 quarterly inspections.",
        },
        {
            "id": 1004,
            "date": (today - datetime.timedelta(days=30)).isoformat(),
            "customer": "Acme Oil & Gas",
            "work_order": "WO-2336",
            "description": "Emergency repair - Site C",
            "subtotal": 2200.00,
            "tax": 110.00,
            "total": 2310.00,
            "status": "Rejected",
            "expires": (today - datetime.timedelta(days=5)).isoformat(),
            "lineItems": [
                {
                    "item": "LABOR-002",
                    "description": "Technician labor - Emergency rate",
                    "quantity": 8,
                    "unitPrice": 175.00,
                    "amount": 1400.00,
                },
                {
                    "item": "PARTS-333",
                    "description": "Replacement parts",
                    "quantity": 1,
                    "unitPrice": 800.00,
                    "amount": 800.00,
                },
            ],
            "terms": "Payment due within 30 days of quote approval. Quote valid for 30 days from issue date.",
        },
        {
            "id": 1005,
            "date": (today - datetime.timedelta(days=45)).isoformat(),
            "customer": "Westfield Energy",
            "work_order": "WO-2325",
            "description": "Control system upgrade",
            "subtotal": 7800.00,
            "tax": 390.00,
            "total": 8190.00,
            "status": "Expired",
            "expires": (today - datetime.timedelta(days=15)).isoformat(),
            "lineItems": [
                {
                    "item": "EQUIP-445",
                    "description": "Control system hardware",
                    "quantity": 1,
                    "unitPrice": 4500.00,
                    "amount": 4500.00,
                },
                {
                    "item": "SOFTWARE-001",
                    "description": "Software license",
                    "quantity": 1,
                    "unitPrice": 2000.00,
                    "amount": 2000.00,
                },
                {
                    "item": "LABOR-001",
                    "description": "Technician labor - Standard rate",
                    "quantity": 10,
                    "unitPrice": 125.00,
                    "amount": 1250.00,
                },
                {
                    "item": "TRAINING-001",
                    "description": "Operator training session",
                    "quantity": 1,
                    "unitPrice": 250.00,
                    "amount": 250.00,
                },
            ],
            "terms": "Payment due within 30 days of quote approval. Includes 1-year software support.",
        },
    ]

    return quotes


@callback(
    Output("account_ag_grid", "rowData"),
    Input("hidden_signal_account", "children"),
    Input("quotes-refresh-button", "n_clicks"),
    Input("quotes-status-filter", "value"),
    Input("quotes-search", "value"),
    prevent_initial_call=False,
)
def load_quotes_data(_, refresh_clicks, status_filter, search_term):
    """Load work order quotes data for the user"""
    log_function_caller()

    if not current_user.is_authenticated:
        raise PreventUpdate()

    # In a real application, you would query the database for quotes assigned to the current user
    # For demonstration purposes, let's generate some sample data
    quotes = get_sample_quotes()

    # Apply status filter if selected
    if status_filter and status_filter != "all":
        quotes = [q for q in quotes if q.get("status") == status_filter]

    # Apply search filter if entered
    if search_term and search_term.strip():
        search_term = search_term.lower().strip()
        filtered_quotes = []
        for quote in quotes:
            # Search in multiple fields
            if (
                search_term in str(quote.get("id", "")).lower()
                or search_term in quote.get("customer", "").lower()
                or search_term in quote.get("work_order", "").lower()
                or search_term in quote.get("description", "").lower()
            ):
                filtered_quotes.append(quote)
        quotes = filtered_quotes

    return quotes


# Use client-side callback to handle grid click events
clientside_callback(
    ClientsideFunction(
        namespace="account_namespace",
        function_name="handleGridClick",
    ),
    Output("selected_quote_id", "data"),
    Output("selected_quote_data", "data"),
    Input("account_ag_grid", "cellClicked"),
    Input("account_ag_grid", "rowData"),
    prevent_initial_call=True,
)

# # Callback for Print button
# clientside_callback(
#     ClientsideFunction(
#         namespace="account_namespace",
#         function_name="printPreview",
#     ),
#     Output("account_modal_body", "children", allow_duplicate=True),
#     Output("account_modal", "is_open", allow_duplicate=True),
#     Input("account_print_page_btn", "n_clicks"),
#     prevent_initial_call=True,
# )

# # Toggle between quotes list and quote detail views
# clientside_callback(
#     ClientsideFunction(
#         namespace="account_namespace",
#         function_name="toggleQuoteDetailView",
#     ),
#     Output("quotes-table-container", "style"),
#     Output("quote-detail-container", "style"),
#     Input("selected_quote_id", "data"),
#     Input("back-to-quotes-button", "n_clicks"),
#     prevent_initial_call=True,
# )

# # Populate quote details
# clientside_callback(
#     ClientsideFunction(
#         namespace="account_namespace",
#         function_name="populateQuoteDetails",
#     ),
#     Output("quote-id", "children"),
#     Output("quote-date", "children"),
#     Output("quote-status", "children"),
#     Output("quote-customer", "children"),
#     Output("quote-work-order", "children"),
#     Output("quote-description", "children"),
#     Output("quote-line-items", "children"),
#     Output("quote-subtotal", "children"),
#     Output("quote-tax", "children"),
#     Output("quote-total", "children"),
#     Output("quote-terms", "children"),
#     Input("selected_quote_data", "data"),
#     prevent_initial_call=True,
# )

# # Initialize signature pad when quote detail is shown
# clientside_callback(
#     ClientsideFunction(
#         namespace="account_namespace",
#         function_name="initializeSignaturePad",
#     ),
#     Output("hidden_signal_account", "children", allow_duplicate=True),
#     Input("quote-detail-container", "style"),
#     prevent_initial_call=True,
# )


# Hidden button to trigger approve quote callback
@callback(
    Output("account_modal_header", "children"),
    Output("account_modal_error_msg", "children"),
    Output("account_modal_error_msg_div", "style"),
    Output("account_modal_quote_approved_msg", "style"),
    Output("account_modal_quote_rejected_msg", "style"),
    Output("account_modal", "is_open"),
    Output("selected_quote_id", "data", allow_duplicate=True),
    Input("approve-quote-button", "n_clicks"),
    Input("reject-quote-button", "n_clicks"),
    State("selected_quote_id", "data"),
    State("selected_quote_data", "data"),
    State("signature_data", "data"),
    prevent_initial_call=True,
)
def handle_quote_action(
    approve_clicks, reject_clicks, quote_id, quote_data, signature_data
):
    """Handle quote approval or rejection"""
    log_function_caller()

    if not current_user.is_authenticated:
        raise PreventUpdate()

    def return_vars(
        account_modal_header_children: str,
        account_modal_error_msg_children: str,
        account_modal_error_msg_div_style: Dict,
        account_modal_quote_approved_msg_style: Dict,
        account_modal_quote_rejected_msg_style: Dict,
        account_modal_is_open: bool,
        selected_quote_id_data: str,
    ):
        """Default return vars"""
        return (
            account_modal_header_children,
            account_modal_error_msg_children,
            account_modal_error_msg_div_style,
            account_modal_quote_approved_msg_style,
            account_modal_quote_rejected_msg_style,
            account_modal_is_open,
            selected_quote_id_data,
        )

    # Determine which button was clicked
    id_triggered: str = get_id_triggered()

    if id_triggered == "approve-quote-button":
        if not signature_data:
            return return_vars(
                account_modal_header_children="Error",
                account_modal_error_msg_children="Please provide a signature before approving the quote.",
                account_modal_error_msg_div_style={"display": "block"},
                account_modal_quote_approved_msg_style={"display": "none"},
                account_modal_quote_rejected_msg_style={"display": "none"},
                account_modal_is_open=True,
                selected_quote_id_data=no_update,
            )

        # In a real application, you would update the quote status in the database
        # For this demo, we'll just show a success message

        # Create a record in the database (pseudocode)
        # db.session.execute(
        #     update(WorkOrderQuote)
        #     .where(WorkOrderQuote.id == quote_id)
        #     .values(
        #         status="Approved",
        #         approved_by=current_user.id,
        #         approved_date=datetime.utcnow_naive(),
        #         signature_data=signature_data
        #     )
        # )
        # db.session.commit()

        return return_vars(
            account_modal_header_children="Quote Approved",
            account_modal_error_msg_children="",
            account_modal_error_msg_div_style={"display": "none"},
            account_modal_quote_approved_msg_style={"display": "block"},
            account_modal_quote_rejected_msg_style={"display": "none"},
            account_modal_is_open=True,
            selected_quote_id_data=None,  # Reset quote ID to return to list view
        )

    elif id_triggered == "reject-quote-button":
        # In a real application, you would update the quote status in the database
        # For this demo, we'll just show a confirmation message

        return return_vars(
            account_modal_header_children="Confirm Rejection",
            account_modal_error_msg_children="",
            account_modal_error_msg_div_style={"display": "none"},
            account_modal_quote_approved_msg_style={"display": "none"},
            account_modal_quote_rejected_msg_style={"display": "block"},
            account_modal_is_open=True,
            selected_quote_id_data=no_update,
        )

    raise PreventUpdate()


@callback(
    Output("account_modal", "is_open", allow_duplicate=True),
    Output("selected_quote_id", "data", allow_duplicate=True),
    Input("modal-return-button", "n_clicks"),
    Input("modal-cancel-button", "n_clicks"),
    Input("modal-confirm-reject-button", "n_clicks"),
    State("selected_quote_id", "data"),
    State("rejection-reason", "value"),
    prevent_initial_call=True,
)
def handle_modal_buttons(
    return_clicks, cancel_clicks, confirm_reject_clicks, quote_id, rejection_reason
):
    """Handle the modal's button actions"""
    log_function_caller()

    id_triggered: str = get_id_triggered()

    if id_triggered == "modal-return-button" or id_triggered == "modal-cancel-button":
        # Just close the modal
        return False, no_update

    elif id_triggered == "modal-confirm-reject-button":
        # In a real application, you would update the quote status and rejection reason in the database
        # db.session.execute(
        #     update(WorkOrderQuote)
        #     .where(WorkOrderQuote.id == quote_id)
        #     .values(
        #         status="Rejected",
        #         rejected_by=current_user.id,
        #         rejected_date=datetime.utcnow_naive(),
        #         rejection_reason=rejection_reason
        #     )
        # )
        # db.session.commit()

        # Close the modal and reset quote ID to return to list view
        return False, None

    raise PreventUpdate()
