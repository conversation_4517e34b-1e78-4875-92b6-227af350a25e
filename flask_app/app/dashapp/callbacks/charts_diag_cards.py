import os
import time
from datetime import date, datetime, timedelta
from typing import OrderedDict

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import numpy as np
import plotly.graph_objects as go
import pytz
from colour import Color
from dash import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
    no_update,
)
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from plotly.subplots import make_subplots
from shared.models.models import (
    Diagnostic,
    DiagnosticMetric,
    StructureVw,
)
from sqlalchemy import func

from app import db, is_admin, user_is_demo_customer
from app.config import (
    TAB_DIAG_CARDS,
    USER_ID_SEAN,
)
from app.dashapp.layout_utils import create_dcc_date_picker_single
from app.dashapp.metrics import bootstrap_colours
from app.dashapp.utils import (
    card_data_wide_to_long,
    diag_card_data_pivot_and_fill,
    get_diag_card_data,
    get_id_triggered,
    get_iot_device_shadow,
    get_structure_obj,
    get_unit_type,
    log_function_caller,
    update_shadow,
    verify_return_tuple,
)
from app.utils.simple import convert_date_string_to_datetime, utcnow_naive


def get_diag_data_request_hist_div() -> dbc.Row:
    """Get the form for requesting historical diagnostic data"""

    return dbc.Row(
        justify="center",
        # style={"display": "none"},
        children=dbc.Col(
            xs=12,
            lg=10,
            xl=8,
            xxl=6,
            children=dbc.Collapse(
                id="diag_data_request_hist_collapse",
                is_open=False,
                children=dbc.Card(
                    class_name="mt-3",
                    children=[
                        dbc.CardHeader(
                            "Request fine-grained diagnostic card data from the gateway"
                        ),
                        dbc.CardBody(
                            [
                                dbc.Row(
                                    dbc.Col(
                                        html.P(
                                            "This will take 10-60 seconds to complete... Press the refresh button occasionally to check if the data have arrived yet.",
                                            className="mb-1",
                                        ),
                                    ),
                                ),
                                dbc.Row(
                                    # no gutters between columns
                                    class_name="mt-1 g-0",
                                    children=[
                                        dbc.Col(
                                            width="auto",
                                            children=[
                                                dbc.Button(
                                                    [
                                                        html.I(
                                                            className="fa-solid fa-arrow-circle-right me-1"
                                                        ),
                                                        "Request high-granularity data since:",
                                                    ],
                                                    color="primary",
                                                    id="btn_diag_data_get_hist",
                                                    size="sm",
                                                    class_name="mb-1 me-1",
                                                    # same height as the date and time pickers
                                                    style={"height": "36px"},
                                                ),
                                            ],
                                        ),
                                        dbc.Col(
                                            width="auto",
                                            children=[
                                                dcc.Store(
                                                    id="diag_hist_request_datetime_store"
                                                ),
                                                # dbc.Label("Time", class_name="mb-1 mt-3"),
                                                dmc.TimeInput(
                                                    id="diag_hist_request_datetime",
                                                    # Just a simple HH:MM local-time string for the time input
                                                    # value=(datetime.now() - timedelta(minutes=15)).strftime("%H:%M"),
                                                    withSeconds=False,
                                                    className="mb-1",
                                                    # style={"height": "31px"},
                                                ),
                                            ],
                                        ),
                                    ],
                                ),
                                dbc.Row(
                                    dbc.Col(
                                        dbc.Spinner(
                                            dbc.FormText(
                                                id="diag_data_get_hist_form_text",
                                                color="success",
                                            ),
                                            color="success",
                                        ),
                                    )
                                ),
                            ],
                        ),
                    ],
                ),
            ),
        ),
    )


def get_diag_card_div():
    """Get the layout for the diagnostic cards"""

    return dbc.Row(
        id="diag_card_div",
        style={"display": "none"},
        justify="center",
        class_name="mt-3",
        children=[
            dbc.Col(
                # Dan wants a 2:1 aspect ratio for card charts
                md=12,
                xxl=8,
                children=[
                    dbc.Row(
                        dbc.Col(
                            dbc.Card(
                                [
                                    dbc.CardHeader(
                                        dbc.Spinner(
                                            id="diag_card_title",
                                            color="success",
                                        )
                                    ),
                                    dbc.CardBody(
                                        [
                                            create_dcc_date_picker_single(
                                                id_="diag_card_date_range",
                                                go_btn_id="diag_card_date_range_btn",
                                                next_btn_id="diag_card_date_range_next_btn",
                                                prev_btn_id="diag_card_date_range_prev_btn",
                                                spinner_id="spinner_div_beside_diag_card_date_range",
                                                # days=2,
                                            ),
                                            html.Div(
                                                # Little daily-average cards above main card chart
                                                id="little_diag_cards_div",
                                                # Only visible on medium screens
                                                className="d-none d-md-block",
                                                style={"margin": "20px 5px 20px 25px"},
                                            ),
                                            html.Div(
                                                dcc.Graph(
                                                    id="diag_card",
                                                    config=dict(
                                                        displayModeBar=False,
                                                    ),
                                                    # Use aspect-ratio instead of figure height?
                                                    # Note the "play/pause" buttons are included in this div,
                                                    # so add extra space for them
                                                    style={"aspect-ratio": "1.8 / 1"},
                                                ),
                                                style={"text-align": "center"},
                                            ),
                                            # CSV file download link
                                            html.Div(
                                                html.A(
                                                    "Download CSV",
                                                    id="diag_cards_chart_download_csv",
                                                    href="#",
                                                    className="btn btn-outline-secondary btn-sm",
                                                    style={"margin-top": "10px"},
                                                ),
                                                id="diag_cards_chart_download_csv_div",
                                            ),
                                        ],
                                    ),
                                ],
                            ),
                        ),
                    ),
                ],
            ),
        ],
    )


def get_diag_card_data_for_charts(
    tz_wanted,
    start_date_utc_str,
    end_date_utc_str,
    power_unit_str,
):
    """This will be used for the card charts and the 'download CSV' button"""

    # Number of items to return
    n_needed = 3

    # This returns wide data, suitable for machine learning.
    # Below we'll make it long for charting.
    df_temp = get_diag_card_data(start_date_utc_str, end_date_utc_str, power_unit_str)
    if df_temp is None or len(df_temp) == 0:
        return_tuple = None, "", ""
        return verify_return_tuple(return_tuple, n_needed=n_needed)
    else:
        df = df_temp.copy()
        del df_temp

    # # Make the downstroke negative if it's a compression card,
    # # since we're charting now, not predicting ML labels
    # if surface_or_compression == "compression":
    #     # Pylint won't like this, but this needs to be an ==, not "is False"
    #     condition = df["is_up"] == False  # noqa: E712
    #     df["value"] = np.where(condition, df["value"] * -1, df["value"])

    # current_app.logger.debug(f"df.head(): \n{df.head()}")

    # Pivot from long to wide and fill in missing values.
    df = diag_card_data_pivot_and_fill(
        df, power_unit_str, ensure_24_hours_first_day=True
    )
    if df is None:
        return_tuple = None, "", ""
        return verify_return_tuple(return_tuple, n_needed=n_needed)

    # Convert the wide data to long data, suitable for charting
    df = card_data_wide_to_long(
        df, variable_0="input_velocity", variable_1="metric", y_axis_var="value"
    )
    # current_app.logger.debug(f"df.head(): \n{df.head()}")

    # # Convert the x-axis to inches, if the data are available
    # df_meta = get_card_meta_data(
    #     start_date_utc_str, end_date_utc_str, surface_or_compression, power_unit_str
    # )
    # if df_meta is None:
    #     has_meta_data = False
    #     mr_btm_position = None
    #     mr_stroke_length = None
    # else:
    #     has_meta_data = True
    #     df_meta = df_meta.copy()
    #     df_meta["btm_position"] = np.where(
    #         np.isnan(df_meta["btm_position"]), 0, df_meta["btm_position"]
    #     )
    #     df_meta["stroke_length"] = np.where(
    #         # df_meta['stroke_length'] == 0,
    #         np.isnan(df_meta["stroke_length"]),
    #         stroke_length_default,
    #         df_meta["stroke_length"],
    #     )

    #     most_recent = df_meta.iloc[-1]
    #     mr_btm_position = most_recent["btm_position"]
    #     mr_stroke_length = most_recent["stroke_length"]
    #     # mr_stroke_count = most_recent['stroke_count']

    # if (
    #     not has_meta_data
    #     or np.isnan(mr_btm_position)
    #     or np.isnan(mr_stroke_length)
    #     or mr_stroke_length == 0
    # ):
    x_axis_title = "Input Velocity (%)"
    # df["stroke_length"] = 100
    # df["btm_position"] = 0
    # df["stroke_count"] = 0
    # df["fillage"] = 0
    # else:
    #     x_axis_title = "Position (Inches)"
    #     # current_app.logger.debug(f"df_meta['timestamp_utc'].unique(): {df_meta['timestamp_utc'].unique()}")
    #     df = df.merge(df_meta, how="left", on=("power_unit", "timestamp_utc"))

    # df["velocity_min"] = df.groupby(["power_unit", "timestamp_utc"])["input_velocity"].transform(
    #     lambda x: x.min()
    # )
    # df["velocity_max"] = df.groupby(["power_unit", "timestamp_utc"])["input_velocity"].transform(
    #     lambda x: x.max()
    # )
    # df["pos_pct"] = (df["position"] - df["velocity_min"]) / (df["velocity_max"] - df["velocity_min"])
    # df["input_velocity"] = (df["pos_pct"] * df["stroke_length"]) + df["btm_position"]
    # df["input_velocity"] = df["input_velocity"].astype(float).round(2)
    # df = df.drop(columns=["velocity_min", "velocity_max", "pos_pct"])

    if df is None or len(df) == 0:
        return_tuple = None, "", ""
        return verify_return_tuple(return_tuple, n_needed=n_needed)

    # # Pylint won't like this, but this needs to be an ==, not "is True"
    # condition = df["is_up"] == True  # noqa: E712
    # df["up_down"] = np.where(condition, "Upstroke", "Downstroke")

    # The following is really helpful in debugging compression cards, with tabulate (install with pip in dev)
    if is_admin() and os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
        df = df.sort_values(["timestamp_utc", "input_velocity"])
        from pandas import Timestamp
        from tabulate import tabulate

        condition = df["timestamp_utc"] <= Timestamp("2023-11-26 00:00:00")
        df2 = df.loc[condition, ["timestamp_utc", "input_velocity"]]
        # current_app.logger.debug(f"list(df2[['timestamp_utc', 'input_velocity', 'up_down']].drop_duplicates().values): {list(df2[['timestamp_utc', 'input_velocity', 'up_down']].drop_duplicates().values)}")
        # current_app.logger.debug(tabulate(df2, headers='keys', tablefmt='psql'))
        current_app.logger.debug(tabulate(df2, headers="keys", tablefmt="psql"))

    # Convert from UTC to the customer's local time
    df["timestamp_local"] = (
        df["timestamp_utc"].dt.tz_localize(pytz.utc).dt.tz_convert(tz_wanted)
    )

    df["hour"] = df["timestamp_local"].dt.hour
    df["timestamp_local_day"] = df["timestamp_local"].dt.floor("D")

    ##############################################
    # Animations with Plotly
    # https://plotly.com/python/animations/

    # df[['timestamp_local', 'timestamp_local_day', 'input_velocity', 'up_down', 'hour', 'load']].to_csv("./dataframe.csv")

    # Keep only local-time days with 24 distinct hours of data for each animation frame
    df["unique_hours_in_day"] = df.groupby("timestamp_local_day")[
        "timestamp_local"
    ].transform(lambda x: x.nunique())

    # I'm just removing this temporarily for testing, for IJACK admins only
    # if not is_admin():
    if not getattr(current_user, "id", None) == USER_ID_SEAN:
        df = df[df["unique_hours_in_day"] == 24]

    # If the time is in the future (because we need 24 unique hours each day, including today),
    # convert to np.nan so it doesn't show up on the chart
    current_local_time = datetime.now(tz_wanted)
    df["value"] = np.where(
        df["timestamp_local"] > current_local_time, np.nan, df["value"]
    )

    # Get the start of tomorrow's datetime so we can filter it out
    dt_tomorrow_local = current_local_time.replace(
        hour=0, minute=0, second=0, microsecond=0
    ) + timedelta(days=1)
    df = df[df["timestamp_local"] < dt_tomorrow_local]

    return_tuple = df, x_axis_title, current_local_time
    return verify_return_tuple(return_tuple, n_needed=n_needed)


clientside_callback(
    """
    function update_diag_hist_request_datetime_local() {
        // Default date is 15 minutes ago
        const now = new Date();
        now.setMinutes(now.getMinutes() - 15);
        // Convert to local time
        const now_local = now.toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: false });
        // console.log("Local datetime string:", now_local);
        return now_local;
    }
    """,
    Output("diag_hist_request_datetime", "value"),
    Input("hidden_signal", "children"),
)


@callback(
    Output("diag_card", "figure"),
    Output("diag_card_title", "children"),
    Output("spinner_div_beside_diag_card_date_range", "children"),
    Output("little_diag_cards_div", "children"),
    Output("diag_cards_chart_download_csv", "href"),
    Output("diag_cards_chart_download_csv_div", "style"),
    Output("record_visit_rcom_charts_diag_cards", "data"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    Input("diag_card", "clickData"),
    Input("diag_card_date_range_btn", "n_clicks"),
    # Input("diag_card_date_range", "end_date"),
    Input("diag_card_date_range", "date"),
    State("diag_card", "figure"),
    State("little_diag_cards_div", "children"),
    State("store_unit_type_id", "data"),
)
def update_diag_card(
    store_structure_id_data,
    active_tab,
    tab_uno_egas,
    graph_click_data,
    date_range_btn_clicks,
    # end_date,
    diag_card_date_range_date,
    graph_figure,
    little_diag_cards_div,
    store_unit_type_id,
):
    """Diagnostic card graph"""
    log_function_caller()

    if store_structure_id_data is None or active_tab != TAB_DIAG_CARDS:
        # or None in hour_selected or None in cols_chosen:
        raise PreventUpdate()

    id_triggered: str = get_id_triggered()
    if id_triggered is None:
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()
    power_unit_str = structure_obj.power_unit_str
    unit_type_lower, _ = get_unit_type(tab_uno_egas, store_unit_type_id)
    has_rcom: bool = bool(structure_obj.aws_thing)

    # If the time_zone is None, use Regina as default
    if structure_obj.time_zone:
        tz_wanted = structure_obj.time_zone
    else:
        tz_wanted = "America/Regina"
    tz_wanted = pytz.timezone(tz_wanted)

    dt_format = "%Y-%m-%d"
    dt_format_db = "%Y-%m-%d %H:%M"
    # Create a date object in the user's local time
    diag_card_date_range_date_obj_local_naive = datetime.strptime(
        diag_card_date_range_date, dt_format
    )
    start_date_obj_local_naive = diag_card_date_range_date_obj_local_naive - timedelta(
        days=3
    )
    # end_date_obj_local_naive = datetime.strptime(end_date, dt_format)
    # Ensure the end date includes all minutes of that day for the DB query
    end_date_obj_local_naive = diag_card_date_range_date_obj_local_naive.replace(
        hour=23, minute=59, second=59, microsecond=59
    )
    # Convert to UTC for the database query
    start_date_obj_utc = tz_wanted.localize(start_date_obj_local_naive).astimezone(
        pytz.utc
    )
    end_date_obj_utc = tz_wanted.localize(end_date_obj_local_naive).astimezone(pytz.utc)
    # Create a string for the database query
    start_date_utc_str = start_date_obj_utc.strftime(dt_format_db)
    end_date_utc_str = end_date_obj_utc.strftime(dt_format_db)

    # # Find the unit_type-specific column name in the appropriate dictionary
    # if store_unit_type_id in (UNIT_TYPE_ID_UNO, UNIT_TYPE_ID_UNOGAS):
    #     surface_or_compression = "surface"  # table name in TimescaleDB
    #     stroke_length_default = 100
    # else:
    #     surface_or_compression = "compression"  # table name in TimescaleDB
    #     stroke_length_default = 49.5

    # Init return values
    diag_cards_chart_download_csv_style = {}
    diag_cards_chart_download_csv_href = (
        f"/rcom/download_csv_chart_diag_cards?pus={power_unit_str}"
    )
    diag_cards_chart_download_csv_href += (
        f"&ut={unit_type_lower}&start={start_date_utc_str}&end={end_date_utc_str}"
    )
    diag_cards_chart_download_csv_href += f"&tzw={tz_wanted}"

    # Identify the demo user for which to make fake locations
    is_demo: bool = user_is_demo_customer(user_id=user_id)
    if is_demo:
        title = "Diagnostic Card"
    else:
        # If it's not the demo user, add the location parameter
        diag_cards_chart_download_csv_href += f"&location={structure_obj.location}"
        # More informative title
        customer_first_word = structure_obj.customer.split()[0]
        customer_unit_info = f"{customer_first_word} {unit_type_lower.upper()} {power_unit_str} at {structure_obj.surface}"
        title = f"Diagnostic Card for {customer_unit_info}"

    def update_scatter_selection(fig, curves_clicked, selection):
        """Change the colour of the selected trace"""

        # Ensure we grab both the upstroke and
        # the downstroke by grabbing hour of day
        hours_of_traces_clicked = [
            d["name"].split(" ")[0]
            for j, d in enumerate(fig["data"])
            if j in curves_clicked
        ]

        for i in range(len(fig["data"])):
            hour_ = fig["data"][i]["name"].split(" ")[0]
            if hour_ in hours_of_traces_clicked:
                # If the trace was hovered/clicked, make it black and large
                fig["data"][i]["selectedpoints"] = selection
                fig["data"][i]["line"]["color"] = "black"
                fig["data"][i]["line"]["width"] = 4
            else:
                fig["data"][i]["selectedpoints"] = None
                fig["data"][i]["line"]["width"] = 2
                old_color = fig["data"][i]["marker"]["color"]
                if old_color == "black":
                    # Change the latest hour from black to grey
                    fig["data"][i]["line"]["color"] = "grey"
                else:
                    fig["data"][i]["line"]["color"] = old_color

        return fig

    # If the input that triggered this function is click- or hoverData,
    # update the existing figure instead of creating a new one
    curves: list = []
    if "diag_card.clickData" in id_triggered:
        selection = [point["pointNumber"] for point in graph_click_data["points"]]
        curves = [point["curveNumber"] for point in graph_click_data["points"]]
    # elif "diag_card.hoverData" in id_triggered:
    #     selection = [point["pointIndex"] for point in graph_hover_data["points"]]
    #     curves = [point["curveNumber"] for point in graph_hover_data["points"]]
    else:
        selection = None

    if selection and graph_figure:
        # Just a quick update of the line colours
        record_visit_rcom = "rcom_charts_cards"
        return (
            update_scatter_selection(graph_figure, curves, selection),
            title,
            "Go",  # spinner inside button
            little_diag_cards_div,
            diag_cards_chart_download_csv_href,
            diag_cards_chart_download_csv_style,
            record_visit_rcom,
        )

    def get_annotation(
        text_,
        x=0.5,
        y=0.5,
        xref="paper",
        yref="paper",
        xanchor="auto",
        yanchor="bottom",
        xshift=0,
        yshift=0,
    ):
        """Get an annotation dictionary for the chart"""
        return dict(
            text=text_,
            showarrow=False,
            x=x,
            y=y,
            xref=xref,
            yref=yref,
            xanchor=xanchor,
            yanchor=yanchor,
            xshift=xshift,
            yshift=yshift,
            font=dict(color="#717174"),
        )

    def get_layout(
        title=None,
        annotations=None,
        x_range=None,
        x_axis_title=None,
        y_range=None,
        y_axis_title=None,
        sliders=None,
        updatemenus=None,
        height=500,
        margin=go.layout.Margin(l=0, r=10, b=0, t=0),  # noqa: E741
    ):
        return go.Layout(
            title=title,
            annotations=annotations,
            # colorway=ijack_colours,
            colorway=bootstrap_colours,
            hovermode="closest",
            # height=height,  # 500 might be a bit too big on a smartphone
            # paper_bgcolor="white",
            plot_bgcolor="white",
            showlegend=True,
            legend=dict(
                font=dict(color="#717174"),
                orientation="h",
                # Sets the y position (in normalized coordinates) of the legend.
                # Defaults to "1" for vertical legends, defaults to "-0.1" for horizontal legends on graphs
                # w/o range sliders and defaults to "1.1" for horizontal legends on graph with one or multiple range sliders.
                # y=1.2,
                y=-0.2,
            ),
            font={"family": "Segoe UI", "color": "#717174"},
            xaxis=dict(
                gridcolor="rgb(238,238,238)",
                range=x_range,
                title=x_axis_title,
            ),
            yaxis=dict(
                gridcolor="rgb(238,238,238)",
                range=y_range,
                title=y_axis_title,
            ),
            # Added more margin on the left side to fix the cutoff True/False labels on the booleans
            margin=margin,
            # padding = go.layout.Padding(l=0, r=0, b=0, t=0),
            sliders=sliders,
            updatemenus=updatemenus,
        )

    def get_null_return(msg="No data to display"):
        """For units that don't have any data"""
        record_visit_rcom = "rcom_charts_cards"
        return (
            go.Figure(
                # Make the initial data, before the animation frames start
                data=None,
                layout=get_layout(
                    annotations=[get_annotation(msg, 0.5, 0.5)],
                ),
            ),
            title,
            "Go",  # Spinner inside button
            "",
            diag_cards_chart_download_csv_href,
            diag_cards_chart_download_csv_style,
            record_visit_rcom,
        )

    if not has_rcom:
        return get_null_return(msg="Unit doesn't have RCOM")

    df, x_axis_title, current_local_time = get_diag_card_data_for_charts(
        tz_wanted,
        start_date_utc_str,
        end_date_utc_str,
        power_unit_str,
    )

    # Last check before creating the frames
    if df is None or len(df) == 0:
        return get_null_return()

    x_min = df["input_velocity"].min()
    x_max = df["input_velocity"].max()
    y_min = df["value"].min()
    y_max = df["value"].max()
    # y_max = 100
    x_range = [x_min - 5, x_max + 5]
    y_range = [y_min - 5, y_max + 5]

    # Find number of unique days, for little charts
    unique_days_in_sample = df["timestamp_local_day"].nunique()
    max_little_charts = 5
    start_at_day_num = max(1, unique_days_in_sample - max_little_charts + 1)
    min_days_in_sample_for_subplots = 2

    # # For debugging only
    # df2 = df.sort_values(['timestamp_utc', 'is_up']).loc[df['timestamp_utc'] == datetime(2021,2,2,22),
    #     ['timestamp_utc', 'position', 'btm_position', 'stroke_length', 'velocity_min', 'velocity_max', 'pos_pct', 'inches']
    # ]

    time_start = time.time()

    ##################################################################################################
    # Graph objects method (much more code, but more flexible)
    frames = []
    slider_steps = []
    slider_distinct_days_set = set()
    y_axis_title = None

    mode = "lines"
    # mode = "markers"
    # marker = dict(
    #     # color='LightSkyBlue',
    #     size=5,
    #     opacity=0.5,
    # )
    line = dict(
        # Don't smooth this line
        shape="linear",
        # Do the following if you want to smooth the line
        # shape="spline",
        # smoothing=1.0,
    )

    # Transition in milliseconds for the animation (default 500)
    duration_frame = 1000
    duration_transition_slider = 1000

    # Docs say redraw not needed for scatterplots, but if it doesn't redraw,
    # the annotations stay the same as for the first frame...
    redraw = True

    # easings.net
    # easing = "cubic-in-out"  # default
    easing = "exp-in-out"
    # easing = "linear"
    # easing = "quadratic-in-out"
    ordering = "layout first"  # default
    # ordering = "traces first"
    mode_animate = "immediate"  # default
    # mode_animate = "next"
    # mode_animate = "afterall"

    # bootstrap_blue_base = Color(BOOTSTRAP_BLUE_500)
    # bootstrap_blue_lum = Color(BOOTSTRAP_BLUE_500)
    # bootstrap_blue_lum.luminance = 0.9

    # bootstrap_red_500_base = Color(BOOTSTRAP_RED_500)
    # bootstrap_red_500_lum = Color(BOOTSTRAP_RED_500)
    # bootstrap_red_500_lum.luminance = 0.9
    colors_dict_simple: dict = dict(
        DiagnosticMetric.query.with_entities(
            DiagnosticMetric.name, DiagnosticMetric.color
        ).all()
    )
    # Make a slightly different color for each hour of the day
    colors_dict_by_hour: dict = {}
    for metric, hex_color in colors_dict_simple.items():
        color = Color(hex_color)
        color_lum = Color(hex_color)
        color_lum.luminance = 0.9
        color_lum_list = list(color_lum.range_to(color, 24))
        colors_dict_by_hour[metric] = {
            hour: color_lum_list[hour].hex for hour in range(24)
        }

    # For the little subplots above the main animation plot
    day_num = 0
    little_charts_counter = 0
    little_charts_annotations_list = []
    subplot_traces = OrderedDict()
    little_charts_cols_num = min(max_little_charts, unique_days_in_sample)
    pct_for_each_col = 1 / little_charts_cols_num

    do_subplots = unique_days_in_sample >= min_days_in_sample_for_subplots

    margin_default = go.layout.Margin(l=0, r=10, b=0, t=40)
    # updatemenus = [
    #     {
    #         "type": "buttons",
    #         "direction": "left",
    #         "pad": {"r": 10, "t": 70},
    #         "showactive": False,
    #         "x": 0.1,
    #         "xanchor": "right",
    #         "y": 0,
    #         "yanchor": "top",
    #         "buttons": [
    #             {
    #                 "label": "Play",
    #                 # "label": "&#9654;", # play symbol
    #                 "method": "animate",
    #                 "args": [
    #                     None,
    #                     {
    #                         "mode": mode_animate,
    #                         "direction": "reverse",  # forward or reverse
    #                         "fromcurrent": True,
    #                         "frame": {"duration": duration_frame, "redraw": redraw},
    #                         "transition": {
    #                             "duration": duration_transition,
    #                             "easing": easing,
    #                         },
    #                         "ordering": ordering,
    #                     },
    #                 ],
    #             },
    #             {
    #                 "label": "Pause",
    #                 # "label": "&#9724;", # pause symbol
    #                 "method": "animate",
    #                 "args": [
    #                     [None],
    #                     {
    #                         "mode": "immediate",
    #                         "frame": {"duration": 0, "redraw": redraw},
    #                         "transition": {
    #                             "duration": 0,
    #                         },
    #                     },
    #                 ],
    #             },
    #         ],
    #     }
    # ]

    # Add scatters to the animation by day
    for gname_day, gdf_day in df.groupby("timestamp_local_day"):
        day_num += 1
        i = 0
        label_date = gname_day.strftime("%b %-d")

        if do_subplots and day_num >= start_at_day_num:
            # Add a little chart to the subplot
            subplot_traces[label_date] = {}
            little_charts_counter += 1

            litl_annotation = get_annotation(
                label_date,
                x=little_charts_counter * pct_for_each_col - (0.5 * pct_for_each_col),
                y=0.5,
                xref="paper",
                yref="paper",
            )
            little_charts_annotations_list.append(litl_annotation)

            cols_needed = ["power_unit", "input_velocity", "metric", "value"]
            groupby_cols = ["power_unit", "input_velocity", "metric"]
            df_daily_avg = gdf_day[cols_needed].groupby(groupby_cols).mean()
            df_daily_avg_reset = df_daily_avg.reset_index()

            for litl_name, litl_metric_df in df_daily_avg_reset.groupby("metric"):
                color = colors_dict_simple[litl_name]
                litl_scatter = go.Scatter(
                    name=label_date,
                    mode="lines",  # lines or markers
                    x=litl_metric_df["input_velocity"],
                    y=litl_metric_df["value"],
                    marker=dict(
                        color=color,
                    ),
                    line=line,
                    showlegend=False,
                    hoverlabel={"namelength": -1},
                )
                subplot_traces[label_date][litl_name] = litl_scatter

        # Find the number of hours in the day, less than the current hour of the current day
        df_hours_less_than_current_hour = gdf_day.loc[
            gdf_day["timestamp_local"] < current_local_time, "timestamp_local"
        ]
        # Need the hours in the day for the range of colors
        hours_in_day = df_hours_less_than_current_hour.nunique()

        # The most recent hour will be black color
        most_recent_hour_ts = df_hours_less_than_current_hour.max()

        # up_colors = list(
        #     bootstrap_red_500_lum.range_to(bootstrap_red_500_base, hours_in_day)
        # )
        # down_colors = list(
        #     bootstrap_blue_lum.range_to(bootstrap_blue_base, hours_in_day)
        # )

        # current_app.logger.debug("")
        # current_app.logger.debug(f"gname_day: {gname_day}")
        # current_app.logger.debug(f"hours_in_day: {hours_in_day}")

        # stroke_count_min = gdf_day["stroke_count"].min()
        # stroke_count_max = gdf_day["stroke_count"].max()
        # strokes_annotation = f"{label_date} stroke count: {stroke_count_min:,.0f} - {stroke_count_max:,.0f}"

        annotations = []
        # annotations.append(get_annotation(strokes_annotation, 0.5, 0.5))
        # if surface_or_compression == "compression":
        #     annotations.append(get_annotation(strokes_annotation, 0.5, 0.5))
        # else:
        #     fillage_min = gdf_day["fillage"].min()
        #     fillage_max = gdf_day["fillage"].max()
        #     fillage_text = f"Fillage: {fillage_min:,.0f}% - {fillage_max:,.0f}%"
        #     annotations.append(get_annotation(strokes_annotation, 0.5, 0.52))
        #     annotations.append(get_annotation(fillage_text, 0.5, 0.47))

        # Create a list of two scatterplots for each hour
        scatterplots = []

        for gname_hour, gdf_hour in gdf_day.groupby("timestamp_local"):
            hour = gname_hour.hour
            label_hour = gname_hour.strftime("%H:%M")

            # current_app.logger.debug("")
            # current_app.logger.debug(f"Unique hour {i}; gname_hour: {gname_hour}")

            for gname_metric, gdf_metric in gdf_hour.groupby("metric"):
                label_hour_stroke = f"{label_hour} {gname_metric}"
                # Make the most recent hour black
                color = (
                    colors_dict_by_hour[gname_metric][hour]
                    if gname_hour < most_recent_hour_ts
                    else "black"
                )
                color = colors_dict_by_hour[gname_metric][hour]
                # current_app.logger.debug(f"{label_date} {gname_metric} {label_hour_stroke} color: {colors_dict_by_hour[gname_metric][hour]}")
                # current_app.logger.debug(f"{gname_metric}, color {colors_dict_by_hour[gname_metric][hour]}")

                # For gdf_metric["value"] values, create a centered moving average to smooth the data
                # new_smoothed_y = (
                #     gdf_metric["value"]
                #     .rolling(window=3, center=True)
                #     .mean()
                #     # .fillna(0)
                # ).tolist()
                # old_smoothed_y = gdf_metric["value"].tolist()

                scatterplots.append(
                    go.Scatter(
                        name=label_hour_stroke,
                        mode=mode,  # lines or markers
                        x=gdf_metric["input_velocity"],
                        y=gdf_metric["value"],
                        # y=old_smoothed_y,
                        # y=new_smoothed_y,
                        # marker=marker,
                        marker=dict(
                            color=color,
                        ),
                        line=line,
                        hoverlabel={"namelength": -1},
                    )
                )

            # Only increment the color selector (i) if it's less than the hours in the day
            if i < (hours_in_day - 1):
                i += 1

        frame = go.Frame(
            name=label_date,
            data=scatterplots,
            layout=go.Layout(
                title={
                    "text": label_date,
                    "xref": "container",
                    "x": 0.5,
                    "xanchor": "center",
                },
                annotations=annotations,
                margin=margin_default,
            ),
        )
        frames.append(frame)
        # current_app.logger.debug(f"frame: \n{frame}")

        if label_date not in slider_distinct_days_set:
            slider_distinct_days_set.add(label_date)
            slider_steps.append(
                {
                    "method": "animate",
                    "label": label_date,  # text label to appear on the slider
                    # "value": gname_hour, # String value of the slider step, used to refer to the step programatically. Defaults to the slider label if not provided
                    "args": [
                        [label_date],
                        {
                            "mode": mode_animate,
                            "frame": {"duration": duration_frame, "redraw": redraw},
                            "transition": {
                                "duration": duration_transition_slider,
                                "easing": easing,
                            },
                            "ordering": ordering,
                        },
                    ],
                }
            )

    # This is just for debugging
    # for gname_day, gdf_day in df.groupby("timestamp_local_day"):
    #     i = 0
    #     label_date = gname_day.strftime("%b %d")
    #     frame = {
    #         "data": [],
    #         "name": label_date,
    #         "layout": {}
    #     }

    #     hours_in_day = gdf_day["timestamp_local"].nunique()
    #     current_app.logger.debug(f"{label_date} hours {hours_in_day}")

    # Add the annotations for the little charts above the main chart
    subplots: go.Figure | None = None
    if do_subplots:
        subplots = make_subplots(
            rows=1,
            cols=little_charts_cols_num,
            shared_yaxes=True,
            print_grid=True,
            # y_title=y_axis_title,
            # subplot_titles=subplot_titles,
            # subplot_titles=list(subplot_traces.keys()),
        )

        for trace_col, (_, dict_) in enumerate(subplot_traces.items()):
            for _, scatter_ in dict_.items():
                subplots.add_trace(scatter_, row=1, col=trace_col + 1)

        subplots.update_layout(
            height=120,
            margin=go.layout.Margin(l=0, r=0, b=0, t=0),
            font={"family": "Segoe UI", "color": "#717174"},
            plot_bgcolor="white",
            showlegend=False,
            annotations=little_charts_annotations_list,
        )

        for p in range(little_charts_cols_num):
            subplots.update_xaxes(
                row=1,
                col=p + 1,
                showgrid=True,
                gridcolor="rgb(238,238,238)",
                range=x_range,
                showticklabels=False,
            )
            subplots.update_yaxes(
                row=1,
                col=p + 1,
                gridcolor="rgb(238,238,238)",
                range=y_range,
            )

    # most_recent_day_available_index = max(0, len(slider_distinct_days_set) - 1)
    # sliders = [
    #     {
    #         # IMPORTANT: this is the "active" step in the slider, which shows up on load
    #         "active": most_recent_day_available_index,
    #         "pad": {"b": 10, "t": 60},
    #         "len": 0.9,
    #         "x": 0.1,
    #         "xanchor": "left",
    #         "y": 0,
    #         "yanchor": "top",
    #         # "currentvalue": {
    #         #     "font": {"size": 20},
    #         #     "prefix": "Hour:",
    #         #     "visible": True,
    #         #     "xanchor": "right"
    #         # },
    #         "steps": slider_steps,
    #         "transition": {"duration": duration_transition_slider},
    #     }
    # ]

    seconds_taken = time.time() - time_start
    current_app.logger.debug("%s seconds to create frames", round(seconds_taken, 1))

    fig = go.Figure(
        # Make the initial data, before the animation frames start
        data=frames[-1]["data"],
        # frames=frames,
        # Make the initial layout, before the animation frames start
        layout=get_layout(
            title=frames[-1]["layout"]["title"],
            annotations=frames[-1]["layout"]["annotations"],
            x_range=x_range,
            x_axis_title=x_axis_title,
            y_range=y_range,
            y_axis_title=y_axis_title,
            # sliders=sliders,
            # updatemenus=updatemenus,
            margin=margin_default,
        ),
    )

    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} total seconds to create frames and figure with initial data"
    )

    if do_subplots:
        little_diag_cards_div_new = dcc.Graph(
            figure=subplots,
            config=dict(
                displayModeBar=False,
            ),
        )
    else:
        little_diag_cards_div_new = ""

    record_visit_rcom = "rcom_charts_diag_cards"
    return (
        fig,
        title,
        "Go",  # spinner inside button
        little_diag_cards_div_new,
        diag_cards_chart_download_csv_href,
        diag_cards_chart_download_csv_style,
        record_visit_rcom,
    )


@callback(
    Output("diag_card_date_range", "initial_visible_month"),
    Input("diag_card_date_range", "date"),
    prevent_initial_call=True,
)
def change_card_initial_visible_month(diag_card_date_range_date):
    """
    If the user goes way back in time,
    change the initial visible month to that month
    """
    log_function_caller()
    if not isinstance(diag_card_date_range_date, str):
        raise PreventUpdate()
    return date.fromisoformat(diag_card_date_range_date)


@callback(
    Output("diag_card_date_range", "date"),
    # Output("diag_card_date_range", "end_date"),
    Input("diag_card_date_range_prev_btn", "n_clicks"),
    Input("diag_card_date_range_next_btn", "n_clicks"),
    State("diag_card_date_range", "date"),
    # State("diag_card_date_range", "end_date"),
    prevent_initial_call=True,
)
def cards_chart_next_prev_day_button_clicks(
    prev_day_clicks, next_day_clicks, start_str
):
    """Respond to next and previous day button clicks on the cards chart"""
    log_function_caller()

    if prev_day_clicks is None and next_day_clicks is None:
        raise PreventUpdate()

    dt_format = "%Y-%m-%d"
    try:
        start_obj = datetime.strptime(start_str, dt_format)
        # end_obj = datetime.strptime(end_str, dt_format)
    except Exception:
        raise PreventUpdate()

    id_triggered: str = get_id_triggered()
    if "diag_card_date_range_prev_btn" in id_triggered:
        start_obj = start_obj - timedelta(days=1)
        # end_obj = end_obj - timedelta(days=1)
    elif "diag_card_date_range_next_btn" in id_triggered:
        if start_obj.date() >= date.today():
            raise PreventUpdate()
        start_obj = start_obj + timedelta(days=1)
        # end_obj = end_obj + timedelta(days=1)

    return start_obj.strftime(dt_format)
    # end_obj.strftime(dt_format),


@callback(
    Output("diag_data_get_hist_form_text", "children"),
    Output("diag_data_get_hist_form_text", "color"),
    Input("btn_diag_data_get_hist", "n_clicks"),
    # Input("btn_rt_data_x_mins", "n_clicks"),
    State("store_structure_id", "data"),
    prevent_initial_call=True,
)
def diag_data_manual_refresh_update_form_text(
    btn_diag_data_get_hist_n_clicks,
    # btn_rt_data_x_mins_n_clicks,
    store_structure_id_data,
):
    """
    After the user clicks the 'Refresh' button, show a message
    that the data is being requested
    """
    log_function_caller()

    def return_vars(
        diag_data_get_hist_form_text: str = "",
        diag_data_get_hist_form_text_color: str = "secondary",
    ):
        """Default return variables"""
        return (
            diag_data_get_hist_form_text,
            diag_data_get_hist_form_text_color,
        )

    # # Get the gateway_type_id since only Axiomteks can request historical data
    # gateway_type_id: int = db.session.execute(
    #     select(Gw.gateway_type_id)
    #     .join(PowerUnit)
    #     .join(Structure)
    #     .where(Structure.id == store_structure_id_data)
    # ).scalar()
    # if gateway_type_id not in (GATEWAY_TYPE_ID_AXIOMTEK, GATEWAY_TYPE_ID_COMPULAB):
    #     return return_vars(
    #         diag_data_get_hist_form_text="Historical data only available from Axiomtek-type gateways",
    #         diag_data_get_hist_form_text_color="danger",
    #     )

    id_triggered: str = get_id_triggered()
    if (
        id_triggered == "btn_diag_data_get_hist.n_clicks"
        and btn_diag_data_get_hist_n_clicks
    ):
        return return_vars(
            diag_data_get_hist_form_text="Historical data requested... Please wait and click the 'Go' button after the date below.",
            diag_data_get_hist_form_text_color="success",
        )

    raise PreventUpdate()


@callback(
    Output("modal_message_header", "children", allow_duplicate=True),
    Output("modal_message_body", "children", allow_duplicate=True),
    Output("modal_message", "is_open", allow_duplicate=True),
    Output("modal_message", "backdrop", allow_duplicate=True),
    Output("diag_hist_request_datetime_store", "data"),
    # Output("diag_hist_request_datetime", "value"),
    Input("btn_diag_data_get_hist", "n_clicks"),
    # The following two States used to be Inputs, but now we use the button instead
    State("store_chart_category", "data"),
    State("store_tabs_for_ind_charts_control_log", "data"),
    State("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    State("diag_hist_request_datetime", "value"),
    prevent_initial_call=True,
)
def request_hist_memory_data_from_gateways(
    btn_diag_data_get_hist_n_clicks,
    store_chart_category_data,
    store_tabs_for_ind_charts_control_log_data,
    store_structure_id_data,
    store_unit_type_id_data,
    diag_hist_request_datetime_value,
):
    """
    This is for automatically requesting saved (but not sent)
    diagnostic data from the gateways
    """
    log_function_caller()

    id_triggered: str = get_id_triggered()

    def return_variables(
        modal_message_header_children=None,
        modal_message_body_children=None,
        modal_message_is_open=False,
        modal_message_backdrop=True,
        diag_hist_request_datetime_store_data: str = no_update,
    ):
        """Default return variables"""
        return (
            modal_message_header_children,
            modal_message_body_children,
            modal_message_is_open,
            modal_message_backdrop,
            diag_hist_request_datetime_store_data,
        )

    # # Get the gateway_type_id since only Axiomteks can request historical data
    # gateway_type_id: int = db.session.execute(
    #     select(Gw.gateway_type_id)
    #     .join(PowerUnit)
    #     .join(Structure)
    #     .where(Structure.id == store_structure_id_data)
    # ).scalar()
    # if gateway_type_id not in (GATEWAY_TYPE_ID_AXIOMTEK, GATEWAY_TYPE_ID_COMPULAB):
    #     return return_variables(
    #         modal_message_header_children="Gateway Type Not Supported",
    #         modal_message_body_children="Historical data only available from Axiomtek and Compulab gateways",
    #         modal_message_is_open=True,
    #         diag_hist_request_datetime_store_data=no_update,
    #     )

    if user_is_demo_customer(user_id=getattr(current_user, "id", None)):
        return return_variables(
            modal_message_header_children="Demo Mode",
            modal_message_body_children="This feature is disabled in demo mode. Please contact the administrator.",
            modal_message_is_open=True,
            diag_hist_request_datetime_store_data=no_update,
        )

    if (
        (
            id_triggered == "btn_diag_data_get_hist.n_clicks"
            and btn_diag_data_get_hist_n_clicks
        )
        # The following two conditions aren't necessary if the button is the only input.
        # They're just here in case we want to change the input back to the store_chart_category
        # or store_tabs_for_ind_charts_control_log_data in the future.
        or (
            id_triggered == "store_tabs_for_ind_charts_control_log.data"
            and store_tabs_for_ind_charts_control_log_data == TAB_DIAG_CARDS
        )
        or (
            id_triggered == "store_chart_category.data"
            and store_chart_category_data == "diagnostic"
        )
    ):
        # Request DIAGNOSTIC data from unit, as opposed to real-time data
        aws_iot_desired_metric = "DIAG_DATA"
        if id_triggered == "store_chart_category.data":
            action = "Diagnostic time series data requested"
        else:
            action = "Diagnostic card data requested"

        user_id: int = getattr(current_user, "id", None)
        structure_obj: StructureVw | None = get_structure_obj(
            store_structure_id_data, user_id, store_unit_type_id_data
        )
        if structure_obj is None:
            raise PreventUpdate()
        power_unit_str: str = structure_obj.power_unit_str
        aws_thing: str = structure_obj.aws_thing

        # Check the PLC version before sending a command
        shadow_all = get_iot_device_shadow(aws_thing)
        swv_plc = shadow_all.get("state", {}).get("reported", {}).get("SWV", 0)
        swv_plc_required_minimum = 330
        if swv_plc < swv_plc_required_minimum:
            return return_variables(
                modal_message_header_children="PLC Firmware Update Required",
                modal_message_body_children=[
                    html.P(
                        f"The PLC firmware version is {swv_plc}. The minimum required version is {swv_plc_required_minimum}.",
                        className="mb-3",
                    ),
                    html.P(
                        "Please update the controller firmware if you want to see diagnostic data."
                    ),
                ],
                modal_message_is_open=True,
                # User can click to backdrop to close the modal
                modal_message_backdrop=True,
            )

        dt_two_hours_ago = utcnow_naive() - timedelta(hours=2)
        default_datetime = (
            db.session.query(func.max(Diagnostic.timestamp_utc))
            .filter(
                Diagnostic.power_unit_str == power_unit_str,
                Diagnostic.timestamp_utc >= dt_two_hours_ago,
            )
            .scalar()
        ) or dt_two_hours_ago

        datetime_wanted_utc, _ = convert_date_string_to_datetime(
            local_datetime_str=diag_hist_request_datetime_value,
            local_timezone_str=structure_obj.time_zone,
            default_datetime=default_datetime,
            tz_wanted=structure_obj.time_zone,
        )

        timestamp_wanted: float = datetime_wanted_utc.timestamp()
        # It'll only send new data if the timestamp is newer, so don't worry about sending this over and over
        random_string = f"{time.time()}_{timestamp_wanted}"
        new_shadow = {
            "state": {"desired": {aws_iot_desired_metric: random_string}},
        }
        try:
            update_shadow(new_shadow, aws_thing)
        except Exception:
            return return_variables(
                modal_message_header_children="Error Sending Command",
                modal_message_body_children=[
                    html.P(
                        f"Error sending command to {power_unit_str}. Please try again."
                    ),
                ],
                modal_message_is_open=True,
                # User can click to backdrop to close the modal
                modal_message_backdrop=True,
                diag_hist_request_datetime_store_data=no_update,
            )
        return return_variables(
            modal_message_header_children="Requested Diagnostic Card Data from Unit",
            modal_message_body_children=f"{action} from gateway. Please wait and refresh chart",
            modal_message_is_open=True,
            modal_message_backdrop=True,
            diag_hist_request_datetime_store_data=diag_hist_request_datetime_value,
        )

    raise PreventUpdate()
