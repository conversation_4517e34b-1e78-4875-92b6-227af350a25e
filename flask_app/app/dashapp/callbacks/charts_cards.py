import time
from datetime import date, datetime, timedelta
from typing import OrderedDict

import dash_bootstrap_components as dbc
import numpy as np
import plotly.graph_objects as go
import pytz
from colour import Color
from dash import Input, Output, State, callback, dcc, html
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from plotly.subplots import make_subplots
from shared.models.models import StructureVw

from app import is_admin, user_is_demo_customer
from app.config import (
    TAB_CARDS,
    UNIT_TYPE_ID_UNO,
    UNIT_TYPE_ID_UNOGAS,
    UNIT_TYPES_IDS_XFER_EGAS,
)
from app.dashapp.callbacks.cards_ml import get_artificial_intelligence_div
from app.dashapp.layout_utils import create_dcc_date_picker_single
from app.dashapp.metrics import BOOTSTRAP_BLUE_500, BOOTSTRAP_RED_500, bootstrap_colours
from app.dashapp.utils import (
    card_data_pivot_and_fill,
    card_data_wide_to_long,
    get_card_data,
    get_card_meta_data,
    get_id_triggered,
    get_structure_obj,
    get_unit_type,
    log_function_caller,
    verify_return_tuple,
)


def get_cards_div():
    """Get the layout for the compression and surface cards"""
    return dbc.Row(
        id="cards_div",
        style={"display": "none"},
        justify="center",
        class_name="mt-3",
        children=[
            dbc.Col(
                # Dan wants a 2:1 aspect ratio for card charts
                md=12,
                lg=12,
                xl=12,
                xxl=8,
                children=[
                    dbc.Row(
                        dbc.Col(
                            dbc.Card(
                                [
                                    dbc.CardHeader(
                                        dbc.Spinner(
                                            id="surface_graph_card_title",
                                            color="success",
                                        )
                                    ),
                                    dbc.CardBody(
                                        [
                                            # create_dcc_date_picker_range(
                                            #     id_="surface_graph_date_range",
                                            #     go_btn_id="surface_graph_date_range_btn",
                                            #     next_btn_id="surface_graph_date_range_next_btn",
                                            #     prev_btn_id="surface_graph_date_range_prev_btn",
                                            #     spinner_id="spinner_div_beside_card_date_range",
                                            #     days=2,
                                            # ),
                                            create_dcc_date_picker_single(
                                                id_="surface_graph_date_range",
                                                go_btn_id="surface_graph_date_range_btn",
                                                next_btn_id="surface_graph_date_range_next_btn",
                                                prev_btn_id="surface_graph_date_range_prev_btn",
                                                spinner_id="spinner_div_beside_card_date_range",
                                                # days=2,
                                            ),
                                            # dcc.Slider(
                                            #     id="card_graph_date_slider",
                                            #     value=int(today_dt.timestamp()),
                                            #     included=False,
                                            #     step=None, # discrete days
                                            #     persistence=True,
                                            # ),
                                            dbc.Row(
                                                dbc.Col(
                                                    # Little daily-average cards above main card chart
                                                    id="little_cards_div",
                                                    xs=12,
                                                    # Only visible on medium screens
                                                    class_name="d-none d-md-block",
                                                    style={
                                                        "margin": "20px 5px 20px 25px"
                                                    },
                                                ),
                                            ),
                                            html.Div(
                                                dcc.Graph(
                                                    id="surface_graph",
                                                    config=dict(
                                                        displayModeBar=False,
                                                    ),
                                                    # Use aspect-ratio instead of figure height?
                                                    # Note the "play/pause" buttons are included in this div,
                                                    # so add extra space for them
                                                    style={"aspect-ratio": "1.8 / 1"},
                                                ),
                                                style={"text-align": "center"},
                                            ),
                                            # CSV file download link
                                            html.Div(
                                                html.A(
                                                    "Download CSV",
                                                    id="cards_chart_download_csv",
                                                    href="#",
                                                    className="btn btn-outline-secondary btn-sm",
                                                    style={"margin-top": "10px"},
                                                ),
                                                id="cards_chart_download_csv_div",
                                            ),
                                        ],
                                    ),
                                ],
                                style={"margin-top": "0.5rem"},
                            ),
                        ),
                    ),
                    get_artificial_intelligence_div(),
                ],
            ),
        ],
    )


def get_card_data_for_charts(
    stroke_length_default,
    tz_wanted,
    start_date_utc_str,
    end_date_utc_str,
    surface_or_compression,
    power_unit_str,
):
    """This will be used for the card charts and the 'download CSV' button"""

    # Number of items to return
    n_needed = 3

    # This returns wide data, suitable for machine learning.
    # Below we'll make it long for charting.
    df_temp = get_card_data(
        start_date_utc_str, end_date_utc_str, surface_or_compression, power_unit_str
    )
    if df_temp is None or len(df_temp) == 0:
        return_tuple = None, "", ""
        return verify_return_tuple(return_tuple, n_needed=n_needed)
    else:
        df = df_temp.copy()

    # Make the downstroke negative if it's a compression card,
    # since we're charting now, not predicting ML labels
    if surface_or_compression == "compression":
        # Pylint won't like this, but this needs to be an ==, not "is False"
        condition = df["is_up"] == False  # noqa: E712
        df["load"] = np.where(condition, df["load"] * -1, df["load"])

    # current_app.logger.debug(f"df.head(): \n{df.head()}")

    # Pivot from long to wide and fill in missing values.
    df = card_data_pivot_and_fill(df, power_unit_str, ensure_24_hours_first_day=True)
    if df is None:
        return_tuple = None, "", ""
        return verify_return_tuple(return_tuple, n_needed=n_needed)

    # current_app.logger.debug(f"df.head(): \n{df.head()}")

    # Convert the wide data to long data, suitable for charting
    df = card_data_wide_to_long(
        df=df,
        # First level of the column index
        variable_0="is_up",
        # Second level of the column index
        variable_1="position",
        y_axis_var="load",
    )
    # current_app.logger.debug(f"df.head(): \n{df.head()}")

    # Convert the x-axis to inches, if the data are available
    df_meta = get_card_meta_data(
        start_date_utc_str, end_date_utc_str, surface_or_compression, power_unit_str
    )
    if df_meta is None:
        has_meta_data = False
        mr_btm_position = None
        mr_stroke_length = None
    else:
        has_meta_data = True
        df_meta = df_meta.copy()
        df_meta["btm_position"] = np.where(
            np.isnan(df_meta["btm_position"]), 0, df_meta["btm_position"]
        )
        df_meta["stroke_length"] = np.where(
            # df_meta['stroke_length'] == 0,
            np.isnan(df_meta["stroke_length"]),
            stroke_length_default,
            df_meta["stroke_length"],
        )

        most_recent = df_meta.iloc[-1]
        mr_btm_position = most_recent["btm_position"]
        mr_stroke_length = most_recent["stroke_length"]
        # mr_stroke_count = most_recent['stroke_count']

    if (
        not has_meta_data
        or np.isnan(mr_btm_position)
        or np.isnan(mr_stroke_length)
        or mr_stroke_length == 0
    ):
        x_axis_title = "Position"
        df["stroke_length"] = 100
        df["btm_position"] = 0
        df["stroke_count"] = 0
        df["fillage"] = 0
    else:
        x_axis_title = "Position (Inches)"
        # current_app.logger.debug(f"df_meta['timestamp_utc'].unique(): {df_meta['timestamp_utc'].unique()}")
        df = df.merge(df_meta, how="left", on=("power_unit", "timestamp_utc"))

    df["pos_min"] = df.groupby(["power_unit", "timestamp_utc"])["position"].transform(
        lambda x: x.min()
    )
    df["pos_max"] = df.groupby(["power_unit", "timestamp_utc"])["position"].transform(
        lambda x: x.max()
    )
    df["pos_pct"] = (df["position"] - df["pos_min"]) / (df["pos_max"] - df["pos_min"])
    df["inches"] = (df["pos_pct"] * df["stroke_length"]) + df["btm_position"]
    df["inches"] = df["inches"].astype(float).round(2)
    # df = df.drop(columns=["pos_min", "pos_max", "pos_pct"])

    if df is None or len(df) == 0:
        return_tuple = None, "", ""
        return verify_return_tuple(return_tuple, n_needed=n_needed)

    # Pylint won't like this, but this needs to be an ==, not "is True"
    condition = df["is_up"] == True  # noqa: E712
    df["up_down"] = np.where(condition, "Upstroke", "Downstroke")

    # # The following is really helpful in debugging compression cards, with tabulate (install with pip in dev)
    # if is_admin() and os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
    #     df = df.sort_values(["timestamp_utc", "inches", "up_down"])
    #     from tabulate import tabulate

    #     condition2 = df["timestamp_utc"] >= utcnow_naive() - timedelta(hours=1)
    #     df2 = df.loc[condition2, ["timestamp_utc", "inches", "up_down", "load"]]
    #     # current_app.logger.debug(f"list(df2[['timestamp_utc', 'inches', 'up_down']].drop_duplicates().values): {list(df2[['timestamp_utc', 'inches', 'up_down']].drop_duplicates().values)}")
    #     # current_app.logger.debug(tabulate(df2, headers='keys', tablefmt='psql'))
    #     current_app.logger.debug(tabulate(df2, headers="keys", tablefmt="psql"))

    # Convert from UTC to the customer's local time
    df["timestamp_local"] = (
        df["timestamp_utc"].dt.tz_localize(pytz.utc).dt.tz_convert(tz_wanted)
    )

    df["hour"] = df["timestamp_local"].dt.hour
    df["timestamp_local_day"] = df["timestamp_local"].dt.floor("D")

    ##############################################
    # Animations with Plotly
    # https://plotly.com/python/animations/

    # df[['timestamp_local', 'timestamp_local_day', 'inches', 'up_down', 'hour', 'load']].to_csv("./dataframe.csv")

    # Keep only local-time days with 24 distinct hours of data for each animation frame
    df["unique_hours_in_day"] = df.groupby("timestamp_local_day")[
        "timestamp_local"
    ].transform(lambda x: x.nunique())

    # I'm just removing this temporarily for testing, for IJACK admins only
    if not is_admin():
        df = df[df["unique_hours_in_day"] == 24]

    # If the time is in the future (because we need 24 unique hours each day, including today),
    # convert to np.nan so it doesn't show up on the chart
    current_local_time = datetime.now(tz_wanted)
    df["load"] = np.where(
        df["timestamp_local"] > current_local_time, np.nan, df["load"]
    )

    # Get the start of tomorrow's datetime so we can filter it out
    dt_tomorrow_local = current_local_time.replace(
        hour=0, minute=0, second=0, microsecond=0
    ) + timedelta(days=1)
    df = df[df["timestamp_local"] < dt_tomorrow_local]

    return_tuple = df, x_axis_title, current_local_time
    return verify_return_tuple(return_tuple, n_needed=n_needed)


@callback(
    Output("tab_cards_id", "label"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    State("store_unit_type_id", "data"),
)
def update_card_tab_label(
    store_tabs_for_ind_charts_control_log_data, tab_uno_egas, store_unit_type_id
):
    """Update the second set of tabs with either 'Surface Card' or 'Compression Card'"""
    log_function_caller()
    if store_tabs_for_ind_charts_control_log_data != TAB_CARDS:
        raise PreventUpdate()

    if store_unit_type_id is None:
        return "Card"

    # Do this especially for UNOGAS types, which must either be EGAS or UNO
    _, unit_type_id = get_unit_type(tab_uno_egas, store_unit_type_id)

    if unit_type_id in UNIT_TYPES_IDS_XFER_EGAS:
        # if unit_type_lower in ("egas", "xfer"):
        return "Compression Card"

    return "Surface Card"


@callback(
    Output("surface_graph", "figure"),
    Output("surface_graph_card_title", "children"),
    Output("spinner_div_beside_card_date_range", "children"),
    Output("little_cards_div", "children"),
    Output("cards_chart_download_csv", "href"),
    Output("cards_chart_download_csv_div", "style"),
    Output("record_visit_rcom_charts_cards", "data"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    Input("surface_graph", "clickData"),
    Input("surface_graph_date_range_btn", "n_clicks"),
    # Input("surface_graph_date_range", "end_date"),
    Input("surface_graph_date_range", "date"),
    State("surface_graph", "figure"),
    State("little_cards_div", "children"),
    State("store_unit_type_id", "data"),
)
def update_cards_graph(
    store_structure_id_data,
    active_tab,
    tab_uno_egas,
    graph_click_data,
    date_range_btn_clicks,
    # end_date,
    surface_graph_date_range_date,
    graph_figure,
    little_cards_div,
    store_unit_type_id,
):
    """Surface/compression card graph"""
    log_function_caller()

    if store_structure_id_data is None or active_tab != TAB_CARDS:
        # or None in hour_selected or None in cols_chosen:
        raise PreventUpdate()

    id_triggered: str = get_id_triggered()
    if id_triggered is None:
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()
    power_unit_str = structure_obj.power_unit_str
    unit_type_lower, _ = get_unit_type(tab_uno_egas, store_unit_type_id)
    has_rcom: bool = bool(structure_obj.aws_thing)

    def return_vars(
        surface_graph_figure,
        surface_graph_card_title_children,
        spinner_div_beside_card_date_range_children,
        little_cards_div_children,
        cards_chart_download_csv_href,
        cards_chart_download_csv_div_style,
        record_visit_rcom_charts_cards_data,
    ):
        """Default return values"""
        return (
            surface_graph_figure,
            surface_graph_card_title_children,
            spinner_div_beside_card_date_range_children,
            little_cards_div_children,
            cards_chart_download_csv_href,
            cards_chart_download_csv_div_style,
            record_visit_rcom_charts_cards_data,
        )

    # If the time_zone is None, use Regina as default
    if structure_obj.time_zone:
        tz_wanted = structure_obj.time_zone
    else:
        tz_wanted = "America/Regina"
    tz_wanted = pytz.timezone(tz_wanted)

    dt_format = "%Y-%m-%d"
    dt_format_db = "%Y-%m-%d %H:%M"
    # Create a date object in the user's local time
    surface_graph_date_range_date_obj_local_naive = datetime.strptime(
        surface_graph_date_range_date, dt_format
    )
    start_date_obj_local_naive = (
        surface_graph_date_range_date_obj_local_naive - timedelta(days=3)
    )
    # end_date_obj_local_naive = datetime.strptime(end_date, dt_format)
    # Ensure the end date includes all minutes of that day for the DB query
    end_date_obj_local_naive = surface_graph_date_range_date_obj_local_naive.replace(
        hour=23, minute=59, second=59, microsecond=59
    )
    # Convert to UTC for the database query
    start_date_obj_utc = tz_wanted.localize(start_date_obj_local_naive).astimezone(
        pytz.utc
    )
    end_date_obj_utc = tz_wanted.localize(end_date_obj_local_naive).astimezone(pytz.utc)
    # Create a string for the database query
    start_date_utc_str = start_date_obj_utc.strftime(dt_format_db)
    end_date_utc_str = end_date_obj_utc.strftime(dt_format_db)

    # Find the unit_type-specific column name in the appropriate dictionary
    if store_unit_type_id in (UNIT_TYPE_ID_UNO, UNIT_TYPE_ID_UNOGAS):
        surface_or_compression = "surface"  # table name in TimescaleDB
        stroke_length_default = 100
    else:
        surface_or_compression = "compression"  # table name in TimescaleDB
        stroke_length_default = 49.5

    # Init return values for CSV download
    cards_chart_download_csv_style = {}
    cards_chart_download_csv_href = (
        f"/rcom/download_csv_chart_cards?pus={power_unit_str}"
    )
    cards_chart_download_csv_href += (
        f"&ut={unit_type_lower}&start={start_date_utc_str}&end={end_date_utc_str}"
    )
    cards_chart_download_csv_href += (
        f"&sld={stroke_length_default}&tzw={tz_wanted}&soc={surface_or_compression}"
    )

    # Identify the demo user for which to make fake locations
    is_demo: bool = user_is_demo_customer(user_id=user_id)
    if is_demo:
        title = f"{surface_or_compression.title()} Card"
    else:
        # If it's not the demo user, add the location parameter
        cards_chart_download_csv_href += f"&location={structure_obj.location}"
        # More informative title
        customer_first_word = structure_obj.customer.split()[0]
        customer_unit_info = f"{customer_first_word} {unit_type_lower.upper()} {power_unit_str} at {structure_obj.surface}"
        title = f"{surface_or_compression.title()} Card for {customer_unit_info}"

    def update_scatter_selection(fig, curves_clicked, selection):
        """Change the colour of the selected trace"""

        # Ensure we grab both the upstroke and
        # the downstroke by grabbing hour of day
        hours_of_traces_clicked = [
            d["name"].split(" ")[0]
            for j, d in enumerate(fig["data"])
            if j in curves_clicked
        ]

        for i in range(len(fig["data"])):
            hour_ = fig["data"][i]["name"].split(" ")[0]
            if hour_ in hours_of_traces_clicked:
                # If the trace was hovered/clicked, make it black and large
                fig["data"][i]["selectedpoints"] = selection
                fig["data"][i]["line"]["color"] = "black"
                fig["data"][i]["line"]["width"] = 8
            else:
                fig["data"][i]["selectedpoints"] = None
                fig["data"][i]["line"]["width"] = 2
                old_color = fig["data"][i]["marker"]["color"]
                if old_color == "black":
                    # Change the latest hour from black to grey
                    fig["data"][i]["line"]["color"] = "grey"
                else:
                    fig["data"][i]["line"]["color"] = old_color

        return fig

    # If the input that triggered this function is click- or hoverData,
    # update the existing figure instead of creating a new one
    curves: list = []
    if "surface_graph.clickData" in id_triggered:
        selection = [point["pointNumber"] for point in graph_click_data["points"]]
        curves = [point["curveNumber"] for point in graph_click_data["points"]]
    # elif "surface_graph.hoverData" in id_triggered:
    #     selection = [point["pointIndex"] for point in graph_hover_data["points"]]
    #     curves = [point["curveNumber"] for point in graph_hover_data["points"]]
    else:
        selection = None

    if selection and graph_figure:
        # Just a quick update of the line colours
        record_visit_rcom = "rcom_charts_cards"
        return return_vars(
            surface_graph_figure=update_scatter_selection(
                graph_figure, curves, selection
            ),
            surface_graph_card_title_children=title,
            spinner_div_beside_card_date_range_children="Go",
            little_cards_div_children=little_cards_div,
            cards_chart_download_csv_href=cards_chart_download_csv_href,
            cards_chart_download_csv_div_style=cards_chart_download_csv_style,
            record_visit_rcom_charts_cards_data=record_visit_rcom,
        )

    def get_annotation(
        text_,
        x=0.5,
        y=0.5,
        xref="paper",
        yref="paper",
        xanchor="auto",
        yanchor="bottom",
        xshift=0,
        yshift=0,
    ):
        """Get an annotation dictionary for the chart"""
        return dict(
            text=text_,
            showarrow=False,
            x=x,
            y=y,
            xref=xref,
            yref=yref,
            xanchor=xanchor,
            yanchor=yanchor,
            xshift=xshift,
            yshift=yshift,
            font=dict(color="#717174"),
        )

    def get_layout(
        title=None,
        annotations=None,
        x_range=None,
        x_axis_title=None,
        y_range=None,
        y_axis_title=None,
        sliders=None,
        updatemenus=None,
        height=500,
        margin=go.layout.Margin(l=0, r=10, b=0, t=0),  # noqa: E741
    ):
        return go.Layout(
            title=title,
            annotations=annotations,
            # colorway=ijack_colours,
            colorway=bootstrap_colours,
            hovermode="closest",
            # height=height,  # 500 might be a bit too big on a smartphone
            # paper_bgcolor="white",
            plot_bgcolor="white",
            showlegend=False,
            # legend=dict(
            #     font=dict(color="#717174"),
            #     orientation="h",
            #     y=1.2,
            # ),
            font={"family": "Segoe UI", "color": "#717174"},
            xaxis=dict(
                gridcolor="rgb(238,238,238)",
                range=x_range,
                title=x_axis_title,
            ),
            yaxis=dict(
                gridcolor="rgb(238,238,238)",
                range=y_range,
                title=y_axis_title,
                # tickformat=".1f",  # Format tick labels with one decimal place
            ),
            # Added more margin on the left side to fix the cutoff True/False labels on the booleans
            margin=margin,
            # padding = go.layout.Padding(l=0, r=0, b=0, t=0),
            sliders=sliders,
            updatemenus=updatemenus,
        )

    def get_null_return(msg="No data to display"):
        """For units that don't have any data"""
        record_visit_rcom = "rcom_charts_cards"
        return return_vars(
            surface_graph_figure=go.Figure(
                layout=get_layout(
                    annotations=[get_annotation(msg, 0.5, 0.5)],
                ),
            ),
            surface_graph_card_title_children=title,
            spinner_div_beside_card_date_range_children="Go",
            little_cards_div_children="",
            cards_chart_download_csv_href=cards_chart_download_csv_href,
            cards_chart_download_csv_div_style=cards_chart_download_csv_style,
            record_visit_rcom_charts_cards_data=record_visit_rcom,
        )

    if not has_rcom:
        return get_null_return(msg="Unit doesn't have RCOM")

    df, x_axis_title, current_local_time = get_card_data_for_charts(
        stroke_length_default,
        tz_wanted,
        start_date_utc_str,
        end_date_utc_str,
        surface_or_compression,
        power_unit_str,
    )

    # Last check before creating the frames
    if df is None or len(df) == 0:
        return get_null_return()

    x_min = df["inches"].min()
    x_max = df["inches"].max()
    y_min = df["load"].min()
    y_max = df["load"].max()
    x_range = [x_min - 5, x_max + 5]
    y_range = [y_min - 5, y_max + 5]

    # Find number of unique days, for little charts
    unique_days_in_sample = df["timestamp_local_day"].nunique()
    max_little_charts = 5
    start_at_day_num = max(1, unique_days_in_sample - max_little_charts + 1)
    min_days_in_sample_for_subplots = 2

    # # For debugging only
    # df2 = df.sort_values(['timestamp_utc', 'is_up']).loc[df['timestamp_utc'] == datetime(2021,2,2,22),
    #     ['timestamp_utc', 'position', 'btm_position', 'stroke_length', 'pos_min', 'pos_max', 'pos_pct', 'inches']
    # ]

    time_start = time.time()

    ##################################################################################################
    # Graph objects method (much more code, but more flexible)
    frames = []
    slider_steps = []
    slider_distinct_days_set = set()
    y_axis_title = (
        "Force (lbs)"
        if surface_or_compression == "surface"
        else "Discharge Pressure (PSI)"
    )

    mode = "lines"
    # mode = "markers"
    # marker = dict(
    #     # color='LightSkyBlue',
    #     size=5,
    #     opacity=0.5,
    # )
    line = dict(
        # # Don't smooth this line
        # shape="linear",
        shape="spline",
        smoothing=1.0,
    )

    # Transition in milliseconds for the animation (default 500)
    duration_frame = 1000
    duration_transition_slider = 1000

    # Docs say redraw not needed for scatterplots, but if it doesn't redraw,
    # the annotations stay the same as for the first frame...
    redraw = True

    # easings.net
    # easing = "cubic-in-out"  # default
    easing = "exp-in-out"
    # easing = "linear"
    # easing = "quadratic-in-out"
    ordering = "layout first"  # default
    # ordering = "traces first"
    mode_animate = "immediate"  # default
    # mode_animate = "next"
    # mode_animate = "afterall"

    bootstrap_blue_base = Color(BOOTSTRAP_BLUE_500)
    bootstrap_blue_lum = Color(BOOTSTRAP_BLUE_500)
    bootstrap_blue_lum.luminance = 0.9

    bootstrap_red_500_base = Color(BOOTSTRAP_RED_500)
    bootstrap_red_500_lum = Color(BOOTSTRAP_RED_500)
    bootstrap_red_500_lum.luminance = 0.9

    # For the little subplots above the main animation plot
    day_num = 0
    little_charts_counter = 0
    little_charts_annotations_list = []
    subplot_traces = OrderedDict()
    little_charts_cols_num = min(max_little_charts, unique_days_in_sample)
    pct_for_each_col = 1 / little_charts_cols_num

    do_subplots = unique_days_in_sample >= min_days_in_sample_for_subplots

    margin_default = go.layout.Margin(l=0, r=10, b=0, t=40)
    # updatemenus = [
    #     {
    #         "type": "buttons",
    #         "direction": "left",
    #         "pad": {"r": 10, "t": 70},
    #         "showactive": False,
    #         "x": 0.1,
    #         "xanchor": "right",
    #         "y": 0,
    #         "yanchor": "top",
    #         "buttons": [
    #             {
    #                 "label": "Play",
    #                 # "label": "&#9654;", # play symbol
    #                 "method": "animate",
    #                 "args": [
    #                     None,
    #                     {
    #                         "mode": mode_animate,
    #                         "direction": "reverse",  # forward or reverse
    #                         "fromcurrent": True,
    #                         "frame": {"duration": duration_frame, "redraw": redraw},
    #                         "transition": {
    #                             "duration": duration_transition,
    #                             "easing": easing,
    #                         },
    #                         "ordering": ordering,
    #                     },
    #                 ],
    #             },
    #             {
    #                 "label": "Pause",
    #                 # "label": "&#9724;", # pause symbol
    #                 "method": "animate",
    #                 "args": [
    #                     [None],
    #                     {
    #                         "mode": "immediate",
    #                         "frame": {"duration": 0, "redraw": redraw},
    #                         "transition": {
    #                             "duration": 0,
    #                         },
    #                     },
    #                 ],
    #             },
    #         ],
    #     }
    # ]

    # Add scatters to the animation by day
    for gname_day, gdf_day in df.groupby("timestamp_local_day"):
        day_num += 1
        i = 0
        label_date = gname_day.strftime("%b %-d")

        if do_subplots and day_num >= start_at_day_num:
            # Add a little chart to the subplot
            subplot_traces[label_date] = {}
            little_charts_counter += 1

            litl_annotation = get_annotation(
                label_date,
                x=little_charts_counter * pct_for_each_col - (0.5 * pct_for_each_col),
                y=0.5,
                xref="paper",
                yref="paper",
            )
            little_charts_annotations_list.append(litl_annotation)

            cols_needed = ["power_unit", "position", "up_down", "inches", "load"]
            groupby_cols = ["power_unit", "position", "up_down"]
            df_daily_avg = gdf_day[cols_needed].groupby(groupby_cols).mean()

            for litl_name, litl_up_down in df_daily_avg.groupby("up_down"):
                color = (
                    bootstrap_blue_base.hex
                    if litl_name == "Downstroke"
                    else bootstrap_red_500_base.hex
                )
                litl_scatter = go.Scatter(
                    name=label_date,
                    mode="lines",  # lines or markers
                    x=litl_up_down["inches"],
                    y=litl_up_down["load"],
                    marker=dict(
                        color=color,
                    ),
                    line=line,
                    showlegend=False,
                    hoverlabel={"namelength": -1},
                    # Format to show one decimal place
                    # hovertemplate="%{y:.1f}<extra>%{fullData.name}</extra>",
                )
                subplot_traces[label_date][litl_name] = litl_scatter

        # Find the number of hours in the day, less than the current hour of the current day
        df_hours_less_than_current_hour = gdf_day.loc[
            gdf_day["timestamp_local"] < current_local_time, "timestamp_local"
        ]
        # Need the hours in the day for the range of colors
        hours_in_day = df_hours_less_than_current_hour.nunique()
        # The most recent hour will be black color
        most_recent_hour_ts = df_hours_less_than_current_hour.max()

        up_colors = list(
            bootstrap_red_500_lum.range_to(bootstrap_red_500_base, hours_in_day)
        )
        down_colors = list(
            bootstrap_blue_lum.range_to(bootstrap_blue_base, hours_in_day)
        )

        # current_app.logger.debug("")
        # current_app.logger.debug(f"gname_day: {gname_day}")
        # current_app.logger.debug(f"hours_in_day: {hours_in_day}")

        stroke_count_min = gdf_day["stroke_count"].min()
        stroke_count_max = gdf_day["stroke_count"].max()
        strokes_annotation = f"{label_date} stroke count: {stroke_count_min:,.0f} - {stroke_count_max:,.0f}"

        annotations = []
        if surface_or_compression == "compression":
            annotations.append(get_annotation(strokes_annotation, 0.5, 0.5))
        else:
            fillage_min = gdf_day["fillage"].min()
            fillage_max = gdf_day["fillage"].max()
            fillage_text = f"Fillage: {fillage_min:,.0f}% - {fillage_max:,.0f}%"
            annotations.append(get_annotation(strokes_annotation, 0.5, 0.52))
            annotations.append(get_annotation(fillage_text, 0.5, 0.47))

        # Create a list of two scatterplots for each hour
        scatterplots = []

        for gname_hour, gdf_hour in gdf_day.groupby("timestamp_local"):
            # hour = gname_hour.hour
            label_hour = gname_hour.strftime("%H:%M")

            # current_app.logger.debug("")
            # current_app.logger.debug(f"Unique hour {i}; gname_hour: {gname_hour}")

            for gname_up_down, gdf_up_down in gdf_hour.groupby("up_down"):
                # # Just for debugging
                # positions_in_half_stroke = gdf_up_down["inches"].nunique()
                # current_app.logger.debug("")
                # current_app.logger.debug(f"positions_in_half_stroke: {positions_in_half_stroke}")

                label_hour_stroke = f"{label_hour} {gname_up_down}"
                colors = down_colors if gname_up_down == "Downstroke" else up_colors
                color = colors[i].hex if gname_hour < most_recent_hour_ts else "black"
                # current_app.logger.debug(f"{label_date} {gname_up_down} {label_hour_stroke} color: {colors[i].hex}")
                # current_app.logger.debug(f"{gname_up_down}, color {colors[i].hex}")

                # For gdf_up_down["load"] values, create a centered moving average to smooth the data
                # new_smoothed_y = (
                #     gdf_up_down["load"]
                #     .rolling(window=3, center=True)
                #     .mean()
                #     # .fillna(0)
                # ).tolist()
                # old_smoothed_y = gdf_up_down["load"].tolist()

                scatterplots.append(
                    go.Scatter(
                        name=label_hour_stroke,
                        mode=mode,  # lines or markers
                        x=gdf_up_down["inches"],
                        y=gdf_up_down["load"],
                        # y=new_smoothed_y,
                        # marker=marker,
                        marker=dict(
                            color=color,
                        ),
                        line=line,
                        hoverlabel={"namelength": -1},
                        # Format to show one decimal place
                        # hovertemplate="%{y:.1f}<extra>%{fullData.name}</extra>",
                    )
                )

            # Only increment the color selector (i) if it's less than the hours in the day
            if i < (hours_in_day - 1):
                i += 1

        frame = go.Frame(
            name=label_date,
            data=scatterplots,
            layout=go.Layout(
                title={
                    "text": label_date,
                    "xref": "container",
                    "x": 0.5,
                    "xanchor": "center",
                },
                annotations=annotations,
                margin=margin_default,
            ),
        )
        frames.append(frame)
        # current_app.logger.debug(f"frame: \n{frame}")

        if label_date not in slider_distinct_days_set:
            slider_distinct_days_set.add(label_date)
            slider_steps.append(
                {
                    "method": "animate",
                    "label": label_date,  # text label to appear on the slider
                    # "value": gname_hour, # String value of the slider step, used to refer to the step programatically. Defaults to the slider label if not provided
                    "args": [
                        [label_date],
                        {
                            "mode": mode_animate,
                            "frame": {"duration": duration_frame, "redraw": redraw},
                            "transition": {
                                "duration": duration_transition_slider,
                                "easing": easing,
                            },
                            "ordering": ordering,
                        },
                    ],
                }
            )

    # This is just for debugging
    # for gname_day, gdf_day in df.groupby("timestamp_local_day"):
    #     i = 0
    #     label_date = gname_day.strftime("%b %d")
    #     frame = {
    #         "data": [],
    #         "name": label_date,
    #         "layout": {}
    #     }

    #     hours_in_day = gdf_day["timestamp_local"].nunique()
    #     current_app.logger.debug(f"{label_date} hours {hours_in_day}")

    # Add the annotations for the little charts above the main chart
    subplots: go.Figure | None = None
    if do_subplots:
        subplots = make_subplots(
            rows=1,
            cols=little_charts_cols_num,
            shared_yaxes=True,
            print_grid=True,
            # y_title=y_axis_title,
            # subplot_titles=subplot_titles,
            # subplot_titles=list(subplot_traces.keys()),
        )

        for trace_col, (_, dict_) in enumerate(subplot_traces.items()):
            for _, scatter_ in dict_.items():
                subplots.add_trace(scatter_, row=1, col=trace_col + 1)

        subplots.update_layout(
            height=120,
            margin=go.layout.Margin(l=0, r=0, b=0, t=0),
            font={"family": "Segoe UI", "color": "#717174"},
            plot_bgcolor="white",
            showlegend=False,
            annotations=little_charts_annotations_list,
        )

        for p in range(little_charts_cols_num):
            subplots.update_xaxes(
                row=1,
                col=p + 1,
                showgrid=True,
                gridcolor="rgb(238,238,238)",
                range=x_range,
                showticklabels=False,
            )
            subplots.update_yaxes(
                row=1,
                col=p + 1,
                gridcolor="rgb(238,238,238)",
                range=y_range,
                # tickformat=".1f",  # Format tick labels with one decimal place
            )

    # most_recent_day_available_index = max(0, len(slider_distinct_days_set) - 1)
    # sliders = [
    #     {
    #         # IMPORTANT: this is the "active" step in the slider, which shows up on load
    #         "active": most_recent_day_available_index,
    #         "pad": {"b": 10, "t": 60},
    #         "len": 0.9,
    #         "x": 0.1,
    #         "xanchor": "left",
    #         "y": 0,
    #         "yanchor": "top",
    #         # "currentvalue": {
    #         #     "font": {"size": 20},
    #         #     "prefix": "Hour:",
    #         #     "visible": True,
    #         #     "xanchor": "right"
    #         # },
    #         "steps": slider_steps,
    #         "transition": {"duration": duration_transition_slider},
    #     }
    # ]

    seconds_taken = time.time() - time_start
    current_app.logger.debug("%s seconds to create frames", round(seconds_taken, 1))

    fig = go.Figure(
        # Make the initial data, before the animation frames start
        data=frames[-1]["data"],
        # frames=frames,
        # Make the initial layout, before the animation frames start
        layout=get_layout(
            title=frames[-1]["layout"]["title"],
            annotations=frames[-1]["layout"]["annotations"],
            x_range=x_range,
            x_axis_title=x_axis_title,
            y_range=y_range,
            y_axis_title=y_axis_title,
            # sliders=sliders,
            # updatemenus=updatemenus,
            margin=margin_default,
        ),
    )

    seconds_taken = time.time() - time_start
    current_app.logger.debug(
        f"{round(seconds_taken, 1)} total seconds to create frames and figure with initial data"
    )

    if do_subplots and subplots and len(subplot_traces) > 0:
        little_cards_div_new = dcc.Graph(
            figure=subplots,
            config=dict(
                displayModeBar=False,
            ),
        )
    else:
        little_cards_div_new = ""

    return return_vars(
        surface_graph_figure=fig,
        surface_graph_card_title_children=title,
        spinner_div_beside_card_date_range_children="Go",
        little_cards_div_children=little_cards_div_new,
        cards_chart_download_csv_href=cards_chart_download_csv_href,
        cards_chart_download_csv_div_style=cards_chart_download_csv_style,
        record_visit_rcom_charts_cards_data="rcom_charts_cards",
    )


@callback(
    Output("surface_graph_date_range", "initial_visible_month"),
    Input("surface_graph_date_range", "date"),
    prevent_initial_call=True,
)
def change_card_initial_visible_month(surface_graph_date_range_date):
    """
    If the user goes way back in time,
    change the initial visible month to that month
    """
    log_function_caller()
    if not isinstance(surface_graph_date_range_date, str):
        raise PreventUpdate()
    return date.fromisoformat(surface_graph_date_range_date)


@callback(
    Output("surface_graph_date_range", "date"),
    # Output("surface_graph_date_range", "end_date"),
    Input("surface_graph_date_range_prev_btn", "n_clicks"),
    Input("surface_graph_date_range_next_btn", "n_clicks"),
    State("surface_graph_date_range", "date"),
    # State("surface_graph_date_range", "end_date"),
    prevent_initial_call=True,
)
def cards_chart_next_prev_day_button_clicks(
    prev_day_clicks, next_day_clicks, start_str
):
    """Respond to next and previous day button clicks on the cards chart"""
    log_function_caller()

    if prev_day_clicks is None and next_day_clicks is None:
        raise PreventUpdate()

    dt_format = "%Y-%m-%d"
    try:
        start_obj = datetime.strptime(start_str, dt_format)
        # end_obj = datetime.strptime(end_str, dt_format)
    except Exception:
        raise PreventUpdate()

    id_triggered: str = get_id_triggered()
    if "surface_graph_date_range_prev_btn" in id_triggered:
        start_obj = start_obj - timedelta(days=1)
        # end_obj = end_obj - timedelta(days=1)
    elif "surface_graph_date_range_next_btn" in id_triggered:
        if start_obj.date() >= date.today():
            raise PreventUpdate()
        start_obj = start_obj + timedelta(days=1)
        # end_obj = end_obj + timedelta(days=1)

    return start_obj.strftime(dt_format)
    # end_obj.strftime(dt_format),
