from copy import deepcopy
from datetime import datetime, timedelta, timezone

import numpy as np
import pandas as pd
from dash.exceptions import PreventUpdate
from flask import current_app
from sqlalchemy.sql import text

from app import cache_memoize_if_prod
from app.dashapp.metrics import (
    BARRELS_COLS,
    BARRELS_PER_M3,
    CELSIUS_COLS,
    CF_COLS,
    CF_PER_M3,
    KPA_PER_PSI,
    OZ_PER_LB,
    OZ_PER_LB_COLS,
    PSI_COLS,
    celsius_to_fahrenheit,
    rt_metrics_for_chart,
)
from app.databases import run_sql_query
from app.utils.simple import utcnow_naive


def time_series_real_time(
    power_unit_str: str,
    use_kpa: bool = False,
    use_oz_per_inch2_for_suction: bool = False,
    use_cf: bool = False,
    use_barrels: bool = False,
    use_fahrenheit: bool = False,
    rt_start_str: str = (datetime.now(timezone.utc) - timedelta(minutes=5)).strftime(
        "%Y-%m-%d %H:%M:%S"
    ),
):
    """Query the TimescaleDB time_series_rt table for frequent chart updates"""

    sql_avg_lines = ",\n".join(
        [
            f"    avg(case when metric = '{metric}' then value else null end) as {abbrev}"
            for metric, abbrev in rt_metrics_for_chart.items()
        ]
    )

    # current_app.logger.info("Querying public.time_series_rt for fast data!")
    sql = text(
        f"""
        SELECT
            power_unit,
            --timestamp_utc,
            --To round to the nearest half second, multiply the timestamp_utc by 2,
            --round it to the nearest integer, and then divide it by 2 again.
            time_bucket('0.2 seconds', timestamp_utc) AS timestamp_utc,
            {sql_avg_lines}
        FROM public.time_series_rt
        WHERE timestamp_utc >= :rt_start
            and power_unit = :power_unit_str
        GROUP BY
            power_unit,
            --timestamp_utc
            time_bucket('0.2 seconds', timestamp_utc)
        --ORDER BY
            --power_unit,
            --timestamp_utc
            --time_bucket('0.2 seconds', timestamp_utc)
    """
    ).bindparams(rt_start=rt_start_str, power_unit_str=power_unit_str)

    # cursor.execute(sql)
    # columns = [str.lower(x[0]) for x in cursor.description]
    # rows = cursor.fetchall()
    rows, columns = run_sql_query(sql, db_name="timescale", commit=False)

    # Build a DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)

    df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])
    df = df.sort_values(["timestamp_utc"], ascending=True).apply(
        lambda x: x.ffill(limit=None)
    )

    # Convert from PSI to kPA if needed
    if use_kpa:
        if use_oz_per_inch2_for_suction:
            # Don't change these ones, since we change them separately, later,
            # so we need to keep them as PSI
            psi_cols2 = [col for col in PSI_COLS if col not in OZ_PER_LB_COLS]
        else:
            psi_cols2 = PSI_COLS
        for col in psi_cols2:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = df[col] * KPA_PER_PSI

    # Convert from PSI to oz/in^2 if needed
    if use_oz_per_inch2_for_suction:
        for col in OZ_PER_LB_COLS:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = df[col] * OZ_PER_LB

    # Convert from M3/day to CF/day
    if use_cf:
        for col in CF_COLS:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = df[col] * CF_PER_M3

    # Convert from M3/day to barrels/day
    if use_barrels:
        for col in BARRELS_COLS:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = df[col] * BARRELS_PER_M3

    # Convert from Celsius to Fahrenheit
    if use_fahrenheit:
        for col in CELSIUS_COLS:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = celsius_to_fahrenheit(df[col])

    # Rounding should always be the last step!
    # Dan and other customers almost never want to see decimals,
    # so remove them for all columns except the following
    ignore = ("timestamp_utc", "power_unit", "gateway")
    decimals_1 = tuple()  # empty now that I moved "cgp" to two decimals
    ignore += decimals_1
    decimals_2 = (
        "spm_egas",
        "spm_egas_sl",
        "cgp",
        "cgp_sl",
        "dgp",
        "dgp_sl",
        "gas_prod_rt",
    )
    ignore += decimals_2

    # Zero decimals
    df.loc[:, ~df.columns.isin(ignore)] = (
        df.loc[:, ~df.columns.isin(ignore)].astype(float).round(decimals=0)
    )
    # One decimal
    df.loc[:, df.columns.isin(decimals_1)] = (
        df.loc[:, df.columns.isin(decimals_1)].astype(float).round(decimals=1)
    )
    # Two decimals
    df.loc[:, df.columns.isin(decimals_2)] = (
        df.loc[:, df.columns.isin(decimals_2)].astype(float).round(decimals=2)
    )

    current_app.logger.debug(
        "DataFrame queried. %s records. df.head(): \n%s", len(df), df.head()
    )

    return df


def calculate_num_data_points(end_minus_start: timedelta, table_name: str) -> float:
    """Calculate the number of data points to query"""

    hours_requested = end_minus_start.total_seconds() / 3600
    minutes_requested = end_minus_start.total_seconds() / 60

    if table_name == "time_series_mvca_24_hour_interval":
        num_data_points = hours_requested / 24.0
    elif table_name == "time_series_mvca_6_hour_interval":
        num_data_points = hours_requested / 6.0
    elif table_name == "time_series_mvca_3_hour_interval":
        num_data_points = hours_requested / 3.0
    elif table_name == "time_series_mvca_1_hour_interval":
        num_data_points = hours_requested
    elif table_name == "time_series_mvca_20_minute_interval":
        num_data_points = minutes_requested / 20.0
    else:
        # Original 2-minute table that's refreshed every 2 mins
        num_data_points = minutes_requested / 2.0

    return num_data_points


# the materialized view gets refreshed every two minutes
@cache_memoize_if_prod(timeout=60)
def get_time_series_data(
    power_unit_str: str,
    start_str: str = None,
    end_str: str = None,
    use_kpa: bool = False,
    use_oz_per_inch2_for_suction: bool = False,
    use_cf: bool = False,
    use_barrels: bool = False,
    use_fahrenheit: bool = False,
    detail: str = "auto",
    get_diag_data: bool = False,
) -> pd.DataFrame:
    """Query the TimescaleDB materialized view and load data into Pandas DataFrame"""

    current_app.logger.debug("Querying TimescaleDB time series data...")

    # if not getattr(current_user, "id", None) == 1:
    #     return pd.DataFrame()

    # In the SQL query below, we're going to filter 'where power_unit in :power_unit_str'
    # so we need to make sure power_unit_str is a tuple, not a string
    if isinstance(power_unit_str, str):
        power_unit_str_tuple: tuple = (power_unit_str,)
    else:
        power_unit_str_tuple = tuple(power_unit_str)

    dt_format = "%Y-%m-%d %H:%M"
    if start_str is None:
        start_obj = utcnow_naive() - timedelta(hours=3)
        start_str = start_obj.strftime(dt_format)
    else:
        start_obj = datetime.strptime(start_str, dt_format)

    if end_str is None:
        end_obj = utcnow_naive()
        end_str = end_obj.strftime(dt_format)
    else:
        end_obj = datetime.strptime(end_str, dt_format)

    # Ensure we query at least min_days_to_query
    # to get at least some data for all metrics
    min_days_to_query = 1.1
    end_minus_start = end_obj - start_obj
    if end_minus_start < timedelta(min_days_to_query):
        start_obj_query = end_obj - timedelta(1)
    else:
        start_obj_query = start_obj

    start_str_query = start_obj_query.strftime(dt_format)

    # less_than_days_time_delta_for_regular_table = 2.1
    if detail == "auto":
        if end_minus_start > timedelta(days=180):
            table_name = "time_series_mvca_24_hour_interval"
        elif end_minus_start > timedelta(days=31):
            table_name = "time_series_mvca_6_hour_interval"
        elif end_minus_start > timedelta(days=14):
            table_name = "time_series_mvca_3_hour_interval"
        elif end_minus_start > timedelta(days=7):
            table_name = "time_series_mvca_1_hour_interval"
        elif end_minus_start > timedelta(days=2.1):
            table_name = "time_series_mvca_20_minute_interval"
        else:
            # Original 2-minute table that's refreshed every 2 mins
            table_name = "time_series"
    else:
        # The user has specified the level of detail/granularity in the "custom" collapse
        if detail == "2m":
            # Original 2-minute table that's refreshed every 2 mins
            table_name = "time_series"
        # Following are TimescaleDB materialized "continuous aggregate" views
        elif detail == "20m":
            table_name = "time_series_mvca_20_minute_interval"
        elif detail == "1h":
            table_name = "time_series_mvca_1_hour_interval"
        elif detail == "3h":
            table_name = "time_series_mvca_3_hour_interval"
        elif detail == "6h":
            table_name = "time_series_mvca_6_hour_interval"
        elif detail == "24h":
            table_name = "time_series_mvca_24_hour_interval"
        else:
            table_name = "time_series"

    # NOTE: hopefully we this is only temporary!
    # ONLY use the "time_series" table for now
    # table_name = "time_series"

    num_data_points: float = calculate_num_data_points(end_minus_start, table_name)
    current_app.logger.debug(
        f"Querying table '{table_name}' for {num_data_points} data points between {start_str_query} and {end_str}..."
    )
    if num_data_points > 10_000:
        current_app.logger.warning(
            f"Querying table '{table_name}' for {num_data_points} data points between {start_str_query} and {end_str}!"
        )
        # Do a partial Dash property update? Better to flash a warning in a modal...
        # Creating a Patch object
        # from dash import Patch
        # patched_figure = Patch()
        # patched_figure["layout"]["title"]["font"]["color"] = "blue"
        # return patched_figure

    # Use materialized view with index on gateway
    sql = text(
        f"""
        select *
        from public.{table_name}
        where power_unit in :power_unit_str_tuple
            and timestamp_utc between :start and :end
        order by timestamp_utc
    """
    ).bindparams(
        # gw=gateway,
        power_unit_str_tuple=power_unit_str_tuple,
        start=start_str_query,
        end=end_str,
    )

    try:
        rows, columns = run_sql_query(sql, db_name="timescale", commit=False)
    except Exception:
        current_app.logger.exception(
            f"Error querying table '{table_name}' between {start_str_query} and {end_str}!"
        )
        return pd.DataFrame()

    # Build a DataFrame with the results
    df = pd.DataFrame(rows, columns=columns)

    if get_diag_data:
        sql = text(
            """
            select
                t1.timestamp_utc,
                t1.power_unit_str as power_unit,
                --diag_num,
                --is_main,
                t2.name,
                t1.value
                --t2.decimals
            from public.time_series_diagnostic t1
            left join public.diagnostic_metrics t2
                on t2.diag_num = t1.diag_num
            where power_unit_str in :power_unit_str_tuple
                and timestamp_utc between :start and :end
            order by timestamp_utc
        """
        ).bindparams(
            power_unit_str_tuple=power_unit_str_tuple,
            start=start_str_query,
            end=end_str,
        )
        rows, columns = run_sql_query(sql, db_name="timescale", commit=False)
        ts_diagnostic = pd.DataFrame(rows, columns=columns)
        current_app.logger.debug(f"ts_diagnostic.head(): \n{ts_diagnostic.head()}")
        # Change the data from long to wide format
        ts_diagnostic_wide = ts_diagnostic.pivot_table(
            index=["timestamp_utc", "power_unit"],
            columns="name",
            values="value",
            aggfunc="mean",
        ).reset_index()
        # Merge the two DataFrames
        df = pd.merge(
            df, ts_diagnostic_wide, how="left", on=["timestamp_utc", "power_unit"]
        )

    if len(df) == 0:
        current_app.logger.warning(
            f"No time series data to chart in table '{table_name}' between {start_str_query} and {end_str}!"
        )
        return df

    # The filling forward of missing values is now handled by the
    # database for longer-term "continuous aggregate" materialized views
    # if end_minus_start < timedelta(days=less_than_days_time_delta_for_regular_table):
    if table_name == "time_series":
        # bool_columns = df.select_dtypes(include='bool').columns
        # Pandas 3.0 future defaults: copy_on_write = True and no_silent_downcasting = True
        pd.options.mode.copy_on_write = True
        pd.set_option("future.no_silent_downcasting", True)
        df = (
            df.sort_values(["timestamp_utc"], ascending=True)
            .infer_objects()
            .ffill()
            .bfill()
        )
        # df = df.sort_values(['timestamp_utc'], ascending=True).apply(lambda x: x.fillna(method='ffill', limit=None))

    # Filter the dataframe to ensure it's great than the start_str_obj.
    # Previously we made it so we at least queried "min_days_to_query" data points.
    # Do this AFTER the forward-filling to ensure there's some data for every metric.
    df = df[df["timestamp_utc"] >= start_obj]

    df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])

    if table_name == "time_series":
        # This is the only table that hasn't had the hp_limit applied to the data already.

        # The hp_lowering_avg and hp_raising_avg are percentages of hp_limit,
        # but we want horsepower values, not percentages, for these charts
        df["hp_limit"] = df["hp_limit"].astype(float)
        df["hp_lowering_avg"] = df["hp_lowering_avg"].astype(float)
        df["hp_raising_avg"] = df["hp_raising_avg"].astype(float)
        # Slave units have their own hp_limit
        df["hp_limit_sl"] = df["hp_limit_sl"].astype(float)
        df["hp_lowering_avg_sl"] = df["hp_lowering_avg_sl"].astype(float)
        df["hp_raising_avg_sl"] = df["hp_raising_avg_sl"].astype(float)

        if power_unit_str == "200467":
            # This unit has data that's already multiplied by the HP_LIMIT on the gateway itself
            dt_changed: datetime = datetime(2023, 5, 9, 20, 42)
        else:
            # All other gateways are set to change over at midnight on May 10th
            dt_changed: datetime = datetime(2023, 5, 10)

        # # For debugging only
        # dt_changed_test: datetime = datetime(2023, 5, 9, 23, 54)
        # df2 = df.loc[
        #     df["timestamp_utc"] >= dt_changed_test,
        #     ["timestamp_utc", "hp_limit", "hp_lowering_avg", "hp_raising_avg"],
        # ].sort_values(["timestamp_utc"], ascending=True)
        # print(df2.head(20))

        df["hp_lowering_avg"] = np.where(
            df["timestamp_utc"] > dt_changed,
            df["hp_lowering_avg"],
            df["hp_lowering_avg"] * df["hp_limit"] / 100,
        )
        df["hp_raising_avg"] = np.where(
            df["timestamp_utc"] > dt_changed,
            df["hp_raising_avg"],
            df["hp_raising_avg"] * df["hp_limit"] / 100,
        )

    # Dan likes to see the following two diagnostic metrics multiplied
    df["end_stop_avg_pveh"] = df["end_stop_avg_pveh"] * 100
    # Currently end_tap_avg_time is in seconds in the database, and Dan wants milliseconds
    df["end_tap_avg_time"] = df["end_tap_avg_time"] * 1000
    df["end_stop_time"] = df["end_stop_time"] * 1000

    # Don't show the hydraulic oil values if they're -1
    metrics_dont_show_if_neg_one = ("hyd_oil_lvl", "hyd_filt_life", "hyd_oil_life")
    for metric in metrics_dont_show_if_neg_one:
        df[metric] = np.where(df[metric] < 0, np.nan, df[metric])

    # Convert from PSI to kPA if needed
    if use_kpa:
        if use_oz_per_inch2_for_suction:
            # Don't change these ones, since we change them separately, later,
            # so we need to keep them as PSI
            psi_cols2 = [col for col in PSI_COLS if col not in OZ_PER_LB_COLS]
        else:
            psi_cols2 = PSI_COLS
        for col in psi_cols2:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = df[col] * KPA_PER_PSI

    # Convert from PSI to oz/in^2 if needed
    if use_oz_per_inch2_for_suction:
        for col in OZ_PER_LB_COLS:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = df[col] * OZ_PER_LB

    # Convert from M3/day to CF/day
    if use_cf:
        for col in CF_COLS:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = df[col] * CF_PER_M3

    # Convert from M3/day to barrels/day
    if use_barrels:
        for col in BARRELS_COLS:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = df[col] * BARRELS_PER_M3

    # Convert from Celsius to Fahrenheit
    if use_fahrenheit:
        for col in CELSIUS_COLS:
            if col in df.columns:
                df[col] = df[col].astype(float)
                df[col] = celsius_to_fahrenheit(df[col])

    # Dan and other customers almost never want to see decimals,
    # so remove them for all columns except the following.
    # This should be the last step, because we don't want to round
    # before multiplying certain small values like "end_tap_avg_time"
    ignore = ("timestamp_utc", "timestamp_utc_inserted", "power_unit", "gateway")
    decimals_1 = ("spm", "spm_egas", "fluid_rate_vpd")
    ignore += decimals_1

    decimals_2 = ("e3m3_d", "end_stop_time", "stroke_up_time", "stroke_down_time")
    pressure_metrics = (
        "cgp",
        "cgp_sl",
        "agft",
        "dgp",
        "dgp_sl",
        "dtp",
        "mgp",
        "mgp_sl",
        "ngp",
        "msp",
        "suction_vru",
    )
    decimals_2 += pressure_metrics

    ignore += decimals_2

    # Make OH and SH floats first
    if "oh" in df.columns:
        df["oh"] = df["oh"].astype(float)
    if "sh" in df.columns:
        df["sh"] = df["sh"].astype(float)

    # Zero decimals
    df.loc[:, ~df.columns.isin(ignore)] = (
        df.loc[:, ~df.columns.isin(ignore)].astype(float).round(decimals=0)
    )
    # One decimal
    df.loc[:, df.columns.isin(decimals_1)] = (
        df.loc[:, df.columns.isin(decimals_1)].astype(float).round(decimals=1)
    )
    # Two decimals
    df.loc[:, df.columns.isin(decimals_2)] = (
        df.loc[:, df.columns.isin(decimals_2)].astype(float).round(decimals=2)
    )

    # current_app.logger.debug(f"DataFrame queried and cached. df.head(): \n{df.head()}")

    return df


def get_time_series_dataframe(
    power_unit_str: str,
    cols_chosen: list,
    is_rt: bool,
    start_str: str = None,
    end_str: str = None,
    use_kpa: bool = False,
    use_oz_per_inch2_for_suction: bool = False,
    use_cf: bool = False,
    use_barrels: bool = False,
    use_fahrenheit: bool = False,
    detail: str = "auto",
    get_diag_data: bool = False,
    rt_start_str: str = (datetime.now(timezone.utc) - timedelta(minutes=5)).strftime(
        "%Y-%m-%d %H:%M:%S"
    ),
) -> pd.DataFrame:
    """Get the dataframe ready"""

    if (
        cols_chosen is None
        or not isinstance(cols_chosen, list)
        or len(cols_chosen) == 0
    ):
        raise PreventUpdate()

    # Don't edit the original cols_chosen (use a copy instead)
    cols_chosen_copy = deepcopy(cols_chosen)

    # Add the timestamp and structure_id columns
    cols_chosen_copy.append("timestamp_utc")
    # cols_chosen_copy.append("gateway")
    cols_chosen_copy.append("power_unit")

    # This function is cached so we run it with no args, then filter it after
    # Don't modify the temp_df since it will be a global variable after it's cached!!!
    if is_rt:
        temp_df = time_series_real_time(
            power_unit_str,
            # gateway,
            use_kpa=use_kpa,
            use_oz_per_inch2_for_suction=use_oz_per_inch2_for_suction,
            use_cf=use_cf,
            use_barrels=use_barrels,
            use_fahrenheit=use_fahrenheit,
            rt_start_str=rt_start_str,
        )
    else:
        temp_df = get_time_series_data(
            power_unit_str,
            start_str=start_str,
            end_str=end_str,
            use_kpa=use_kpa,
            use_oz_per_inch2_for_suction=use_oz_per_inch2_for_suction,
            use_cf=use_cf,
            use_barrels=use_barrels,
            use_fahrenheit=use_fahrenheit,
            detail=detail,
            get_diag_data=get_diag_data,
        )

    if len(temp_df) == 0:
        return None

    try:
        # This is a problem TODO
        cols_to_use = [col for col in cols_chosen_copy if col in temp_df.columns]
        df = temp_df.loc[:, cols_to_use]
    except Exception:
        # KeyError: "Passing list-likes to .loc or [] with any missing labels is no longer supported.
        # The following labels were missing: Index(['agft', 'mgp', 'ngp', 'msp'], dtype='object').
        # See https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#deprecate-loc-reindex-listlike"
        current_app.logger.exception(
            f"cols_chosen_copy {cols_chosen_copy} not present in DataFrame?"
        )
        return None

    # Should be already done by the query
    # df = df.sort_values(['timestamp_utc'], ascending=True)
    # df = df.ffill()

    return df
