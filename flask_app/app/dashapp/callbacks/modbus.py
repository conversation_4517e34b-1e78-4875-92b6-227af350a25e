from datetime import date

import dash_bootstrap_components as dbc
from dash import Input, Output, callback, html

from app.dashapp.layout_utils import get_ag_grid
from app.databases import run_sql_query


def modbus_layout():
    """Return the layout for the Modbus page"""

    return dbc.Container(
        [
            html.Div(id="modbus_hidden_div", style={"display": "none"}),
            html.H1(
                "IJACK Modbus Registers",
                className="text-center my-4",
            ),
            html.P(
                [
                    "The IJACK system uses Modbus TCP/IP over ethernet to communicate with the pump's PLCs. To collect your data, hook up an ethernet cable to the ",
                    html.Strong("'Eth 1'"),
                    " interface on the gateway inside the power unit cabinet door.",
                ],
                className="mb-4",
            ),
            html.P(
                [
                    "Please also ",
                    html.A("let us know", href="/contact/", style={"color": "blue"}),
                    " which IP address(es), port(s), subnets, and default gateways you require for your network.",
                ],
                className="mb-4",
            ),
            dbc.Row(
                dbc.Col(
                    [
                        dbc.<PERSON><PERSON>(
                            "Download CSV",
                            id="modbus_download_csv_btn",
                            n_clicks=0,
                            className="my-2",
                            color="secondary",
                            outline=True,
                            size="sm",
                        ),
                        get_ag_grid(
                            id="modbus_ag_grid",
                            # rowData=rows,
                            columnSize="sizeToFit",
                            paginationPageSize=20,
                            csv_filename=f"IJACK Modbus Holding Registers {date.today().strftime('%Y-%m-%d')}",
                            columnDefs=[
                                {
                                    "field": "holding_reg_40k",
                                    "headerName": "Modbus Holding Register",
                                    "cellDataType": "number",
                                    "cellRenderer": "NumberFormatter0",
                                },
                                {
                                    "field": "address",
                                    "headerName": "Address",
                                    "cellDataType": "number",
                                    "cellRenderer": "NumberFormatter0",
                                },
                                {
                                    "field": "interesting",
                                    "cellRenderer": "Tooltip",
                                    "headerName": "Interesting",
                                    "headerTooltip": "1 is the most interesting, typically, followed by 2 and 3.",
                                    "cellDataType": "number",
                                },
                                {
                                    "field": "n_registers",
                                    "headerName": "Number of Registers",
                                    "cellDataType": "number",
                                    "cellRenderer": "NumberFormatter0",
                                },
                                {
                                    "field": "abbrev",
                                    "headerName": "Metric Name",
                                    "cellDataType": "text",
                                },
                                {
                                    "field": "machine",
                                    "headerName": "Machine(s)",
                                    "cellDataType": "text",
                                },
                                {
                                    "field": "item",
                                    "headerName": "Description",
                                    "cellDataType": "text",
                                    "initialWidth": 400,
                                    "maxWidth": 500,
                                },
                                {
                                    "field": "units",
                                    "headerName": "Units",
                                    "cellDataType": "text",
                                },
                                {
                                    "field": "writable",
                                    "headerName": "Writable",
                                    "cellDataType": "boolean",
                                    "cellRenderer": "BooleanFormatter",
                                },
                                {
                                    "field": "min_val",
                                    "headerName": "Min Value",
                                    "cellDataType": "number",
                                    "cellRenderer": "NumberFormatter1",
                                },
                                {
                                    "field": "max_val",
                                    "headerName": "Max Value",
                                    "cellDataType": "number",
                                    "cellRenderer": "NumberFormatter1",
                                },
                            ],
                        ),
                    ]
                )
            ),
        ],
    )


@callback(
    Output("modbus_ag_grid", "rowData"),
    Input("modbus_hidden_div", "children"),
    prevent_initial_call=False,
)
def populate_modbus_ag_grid(_):
    """Populate the Modbus AG Grid with data"""

    sql = """
SELECT
    t1.holding_reg_40k,
    t1.address,
    t1.n_registers,
    t2.abbrev,
    t2.interesting,
    t2.machine,
    t2.item,
    t2.units,
    t1.writable_modbus as writable,
    t1.min_val,
    t1.max_val
-- 	, description,
-- 	num_bytes, data_type, resolution, offset_, number_,
-- 	interesting, rcom_name, signed, decimals
from public.modbus_holding_registers t1
inner join public.map_abbrev_item t2
    on t1.abbrev_id = t2.id
where t2.abbrev is not null
order by t2.interesting, t1.address
;
    """
    rows, _ = run_sql_query(sql, db_name="ijack", echo=False, as_dict=True)
    return rows


@callback(
    Output("modbus_ag_grid", "exportDataAsCsv"),
    Input("modbus_download_csv_btn", "n_clicks"),
    prevent_initial_call=True,
)
def export_data_as_csv(n_clicks):
    """Export the data as CSV"""
    if n_clicks:
        return True
    return False
