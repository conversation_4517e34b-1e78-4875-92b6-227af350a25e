from datetime import date

import dash_bootstrap_components as dbc
import phonenumbers
from dash import Input, Output, State, callback, dcc, html, no_update
from dash.exceptions import PreventUpdate
from flask import url_for
from flask_login import current_user
from flask_wtf.csrf import generate_csrf
from shared.models.models import Role
from sqlalchemy import func

from app import (
    db,
    user_is_ijack_employee,
)
from app.auth.forms import RegistrationFormVoluntary, validate_email, validate_phone
from app.config import (
    ADMIN_EMAILS,
    COUNTRY_ID_CANADA,
    CUSTOMER_ID_NO_CUSTOMER,
    ROLE_ID_SELF_REGISTERED,
    TIME_ZONE_ID_AMERICA_EDMONTON,
)
from app.dashapp.utils import (
    get_db_options,
)
from app.email_stuff import send_email
from app.models.models import User
from shared.utils.string_utils import generate_random_string


def get_signals():
    """Signals for registration form"""
    return html.Div(
        [
            # Hidden signal value, for running callback on page load
            html.Div(id="hidden_signal_register", style={"display": "none"}),
            dcc.Location(id="location_registration", refresh=True),
            # dcc.Interval(
            #     id="registration_interval",
            #     interval=1_000,
            #     disabled=False,
            #     n_intervals=0,
            #     # max_intervals=61,
            # ),
        ]
    )


def get_modal():
    """Get a modal for displaying messages"""
    return dbc.Modal(
        id="register_modal",
        is_open=False,
        size="lg",
        centered=True,  # Vertically centered
        children=[
            dbc.ModalHeader(id="register_modal_header"),
            dbc.ModalBody(id="register_modal_body"),
        ],
    )


def get_registration_form(has_app_context: bool = True):
    """Registration form"""
    if has_app_context:
        login_url = url_for("dash.login")
        contact_url = url_for("dash.contact")
    else:
        login_url = "/login/"
        contact_url = "/contact/"

    return html.Div(
        [
            dbc.Row(
                dbc.Col(
                    xs=12,
                    children=[
                        html.H1(
                            "Register for an IJACK account",
                            className="text-center mt-4 mb-2",
                            style={
                                "font-weight": "bold",
                                "font-size": "2em",
                            },
                        ),
                        html.H3(
                            "Control and monitor your units.",
                            className="text-center mb-4",
                        ),
                    ],
                )
            ),
            dbc.Row(
                dbc.Col(
                    xs=12,
                    sm=10,
                    md=8,
                    lg=6,
                    xl=5,
                    class_name="mx-auto",
                    children=dbc.Form(
                        [
                            # CSRF Token as a hidden field (value updated in callback)
                            dcc.Input(
                                type="hidden", id="register_csrf_token", value=None
                            ),
                            html.Div(
                                [
                                    dbc.Label(
                                        "Email",
                                        html_for="register_email_input",
                                        class_name="mb-1",
                                    ),
                                    dbc.Input(
                                        type="register_email_input",
                                        id="register_email_input",
                                        valid=None,
                                        placeholder="Enter your email",
                                        required=True,
                                        persistence=True,
                                    ),
                                    dcc.Store(id="register_email_store"),
                                    dbc.FormText(
                                        "We'll never share your email",
                                        color="secondary",
                                        id="register_email_form_text",
                                        class_name="mt-1",
                                    ),
                                ],
                                className="mb-3",
                            ),
                            dbc.Label(
                                "First Name", html_for="first-name", class_name="mb-1"
                            ),
                            dbc.Input(
                                type="text",
                                id="register_first_name",
                                placeholder="Enter your first name",
                                required=True,
                                persistence=True,
                                class_name="mb-3",
                            ),
                            dbc.Label(
                                "Last Name", html_for="last-name", class_name="mb-1"
                            ),
                            dbc.Input(
                                type="text",
                                id="register_last_name",
                                placeholder="Enter your last name",
                                required=True,
                                persistence=True,
                                class_name="mb-3",
                            ),
                            dbc.Label(
                                "Company",
                                html_for="register_company",
                                class_name="mb-1",
                            ),
                            dbc.Input(
                                type="text",
                                id="register_company",
                                required=True,
                                persistence=True,
                                placeholder="Enter your company name (optional)",
                                class_name="mb-3",
                            ),
                            dbc.Label(
                                "Job Title", html_for="job-title", class_name="mb-1"
                            ),
                            dbc.Input(
                                type="text",
                                id="register_job_title",
                                required=False,
                                persistence=True,
                                placeholder="Enter your job title (optional)",
                                class_name="mb-3",
                            ),
                            dbc.Label(
                                "City", html_for="register_city", class_name="mb-1"
                            ),
                            dbc.Input(
                                type="text",
                                id="register_city",
                                required=False,
                                persistence=True,
                                placeholder="Enter your city (optional)",
                                class_name="mb-3",
                            ),
                            dbc.Label("Country", html_for="country", class_name="mb-1"),
                            dcc.Dropdown(
                                id="register_country_id",
                                value=COUNTRY_ID_CANADA,
                                persistence=True,
                                className="mb-3",
                            ),
                            html.Div(
                                [
                                    dbc.Label(
                                        "Phone Number",
                                        html_for="register_phone",
                                        class_name="mb-1",
                                    ),
                                    dbc.Input(
                                        type="tel",
                                        id="register_phone",
                                        valid=None,
                                        required=True,  # Not required for IJACK people
                                        persistence=True,
                                        placeholder="Enter your phone number",
                                    ),
                                    dcc.Store(id="register_phone_store"),
                                    dbc.FormText(
                                        "We'll never share your phone number",
                                        id="register_phone_form_text",
                                        color="secondary",
                                        class_name="mt-1",
                                    ),
                                ],
                                className="mb-3",
                            ),
                            dbc.Label(
                                "Time Zone", html_for="time-zone", class_name="mb-1"
                            ),
                            dcc.Dropdown(
                                id="register_time_zone_id",
                                value=TIME_ZONE_ID_AMERICA_EDMONTON,
                                persistence=True,
                                className="mb-3",
                            ),
                            dbc.Checklist(
                                options=[
                                    {
                                        "label": "Service update emails",
                                        "value": "service",
                                    },
                                    {"label": "RCOM update emails", "value": "rcom"},
                                    {"label": "New product emails", "value": "product"},
                                    {"label": "Marketing emails", "value": "marketing"},
                                    # {
                                    #     "label": "Don't receive ANY IJACK emails",
                                    #     "value": "unsubscribe",
                                    # },
                                ],
                                value=["service", "rcom", "product", "marketing"],
                                id="register_email_preferences_checklist",
                                inline=False,
                                # persistence=True,
                            ),
                            dbc.Checkbox(
                                label="Don't receive ANY IJACK emails",
                                id="register_unsubscribe_all_checkbox",
                                value=False,
                                # persistence=True,
                                class_name="mt-0 mb-3",
                            ),
                            html.Div(
                                [
                                    dbc.Button(
                                        "Validate",
                                        id="register_validate_btn",
                                        disabled=False,
                                        color="dark",
                                        class_name="mb-1",
                                    ),
                                    dbc.FormText(
                                        color="secondary",
                                        id="register_validate_form_text",
                                        className="mt-1",
                                    ),
                                ],
                                className="mt-2",
                            ),
                            html.Div(
                                dbc.Button(
                                    "Register",
                                    id="register_submit_registration_button",
                                    style={"display": "none"},
                                    disabled=False,
                                    color="dark",
                                ),
                                className="mt-2",
                            ),
                        ],
                    ),
                )
            ),
            dbc.Row(
                justify="center",
                class_name="mt-4",
                children=dbc.Col(
                    xs=12,
                    children=[
                        html.P(
                            [
                                "Already have an account? ",
                                html.A(
                                    "Log in here.",
                                    href=login_url,
                                    style={"color": "blue"},
                                ),
                            ],
                            className="text-center",
                        ),
                        html.P(
                            [
                                "Need help? Already an IJACK customer? ",
                                html.A(
                                    "Contact us.",
                                    href=contact_url,
                                    style={"color": "blue"},
                                ),
                            ],
                            className="text-center",
                        ),
                    ],
                ),
            ),
        ],
    )


def registration_layout(has_app_context: bool = True):
    """Layout for registration form"""

    # The actual layout starts here
    layout = dbc.Container(
        [
            # Hidden signal value, for running callback on page load
            get_signals(),
            get_modal(),
            get_registration_form(has_app_context=has_app_context),
        ]
    )

    return layout


@callback(
    Output("register_email_preferences_checklist", "value"),
    Input("register_unsubscribe_all_checkbox", "value"),
    prevent_initial_call=True,
)
def if_unsubscribe_all_clicked(unsubscribe_all: bool):
    """If the "Unsubscribe from all emails" box is checked, then disable all other boxes"""
    if unsubscribe_all is True:
        return []
    raise PreventUpdate()


@callback(
    Output("register_csrf_token", "value"),
    Input("hidden_signal_register", "children"),
)
def update_csrf_token(hidden_signal_register):
    """Update the CSRF token"""
    return generate_csrf()


@callback(
    Output("register_country_id", "options"),
    Input("hidden_signal_register", "children"),
)
def update_country_options(hidden_signal_register):
    """Get the country options from the database"""
    options: list = get_db_options(
        columns=["country_name"],
        table="countries",
        schema="public",
    )

    return options


@callback(
    Output("register_time_zone_id", "options"),
    Input("hidden_signal_register", "children"),
)
def update_time_zone_options(hidden_signal_register):
    """Get the time zone options from the database"""
    options: list = get_db_options(
        columns=["time_zone"],
        table="time_zones",
        schema="public",
    )
    if not options:
        raise PreventUpdate()

    return options


@callback(
    Output("register_email_form_text", "children"),
    Output("register_email_form_text", "color"),
    Output("register_email_input", "valid"),
    Output("register_email_input", "invalid"),
    Output("register_email_store", "data"),
    Input("register_email_input", "n_blur"),
    Input("register_validate_btn", "n_clicks"),
    # Input("registration_interval", "n_intervals"),
    State("register_email_input", "value"),
    prevent_initial_call=True,
)
def validate_email_value(
    email_n_blur,
    validate_button_n_clicks,
    # registration_interval_n_intervals,
    email_value,
):
    """Validate the email address"""
    if (
        not email_n_blur and not validate_button_n_clicks
        # and not registration_interval_n_intervals
    ):
        raise PreventUpdate()

    def return_vars(
        email_form_text_children: str = "",
        email_form_text_color: str = "danger",
        email_valid: bool = False,
        email_invalid: bool = True,
        email_store: str = None,
    ):
        """Default return values"""
        return (
            email_form_text_children,
            email_form_text_color,
            email_valid,
            email_invalid,
            email_store,
        )

    try:
        validate_email(form=None, field=email_value, must_be_unique=True)
    except Exception as err:
        return return_vars(
            email_form_text_children=str(err),
            email_form_text_color="danger",
            email_valid=False,
            email_invalid=True,
        )

    return return_vars(
        email_form_text_children="",
        email_form_text_color="success",
        email_valid=True,
        email_invalid=False,
        email_store=str(email_value).lower().strip(),
    )


@callback(
    Output("register_phone", "value"),
    Output("register_phone_store", "data"),
    Output("register_phone_form_text", "children"),
    Output("register_phone_form_text", "color"),
    Output("register_phone", "valid"),
    Output("register_phone", "invalid"),
    Output("register_phone", "required"),
    Input("register_phone", "n_blur"),
    Input("register_validate_btn", "n_clicks"),
    # Input("registration_interval", "n_intervals"),
    State("register_phone", "value"),
    prevent_initial_call=True,
)
def validate_phone_value(
    phone_n_blur,
    validate_button_n_clicks,
    # registration_interval_n_intervals,
    phone_value,
):
    """Validate the phone number"""
    if (
        not phone_n_blur and not validate_button_n_clicks
        # and not registration_interval_n_intervals
    ):
        raise PreventUpdate()

    def return_vars(
        phone_value: str = no_update,
        phone_store: str = no_update,
        phone_form_text_children: str = no_update,
        phone_form_text_color: str = "danger",
        phone_valid: bool = False,
        phone_invalid: bool = True,
    ):
        """Default return values"""
        phone_required = (
            False
            if user_is_ijack_employee(user_id=getattr(current_user, "id", None))
            else True
        )
        return (
            phone_value,
            phone_store,
            phone_form_text_children,
            phone_form_text_color,
            phone_valid,
            phone_invalid,
            phone_required,
        )

    try:
        phone_parsed = validate_phone(form=None, field=phone_value, must_be_unique=True)
        phone_international = phonenumbers.format_number(
            phone_parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
        )
        phone_e164 = phonenumbers.format_number(
            phone_parsed, phonenumbers.PhoneNumberFormat.E164
        )
    except Exception as err:
        return return_vars(
            phone_value=no_update,
            phone_store=None,
            phone_form_text_children=str(err),
            phone_form_text_color="danger",
            phone_valid=False,
            phone_invalid=True,
        )

    return return_vars(
        phone_value=phone_international,
        phone_store=phone_e164,
        phone_form_text_children="",
        phone_form_text_color="success",
        phone_valid=True,
        phone_invalid=False,
    )


@callback(
    Output("register_submit_registration_button", "style"),
    Output("register_validate_btn", "color"),
    Output("register_validate_form_text", "children"),
    Input("register_validate_btn", "n_clicks"),
    State("register_email_input", "valid"),
    State("register_phone", "valid"),
    State("register_phone_store", "data"),
    prevent_initial_call=True,
)
def enable_register_button(
    validate_button_n_clicks,
    email_valid: bool,
    phone_valid: bool,
    phone_store_data: str,
):
    """Enable the register button if the email and phone are valid"""
    if not validate_button_n_clicks:
        raise PreventUpdate()

    def return_vars(
        register_button_style: dict = {"display": "none"},
        validate_button_color: str = "dark",
        validate_form_text_children: str = "",
    ):
        """Default return values"""
        return (
            register_button_style,
            validate_button_color,
            validate_form_text_children,
        )

    if email_valid and (
        phone_valid
        or (
            phone_store_data is None
            and user_is_ijack_employee(user_id=getattr(current_user, "id", None))
        )
    ):
        # return {}, "secondary"
        return return_vars(
            register_button_style={},
            validate_button_color="secondary",
            validate_form_text_children=dbc.Alert(
                "Form looks good", color="success", className="mt-2"
            ),
        )

    # return {"display": "none"}, "dark"
    return return_vars(
        register_button_style={"display": "none"},
        validate_button_color="dark",
        validate_form_text_children=dbc.Alert(
            "Please review the errors in the form fields.",
            color="danger",
            className="mt-2",
        ),
    )


@callback(
    # To redirect to the login page
    # Output("url", "href"),
    Output("register_submit_registration_button", "disabled"),
    Output("register_modal", "is_open", allow_duplicate=True),
    Output("register_modal_body", "children", allow_duplicate=True),
    Output("register_validate_btn", "n_clicks"),
    # Input("registration_form_output", "children"),
    Input("register_submit_registration_button", "n_clicks"),
    State("register_email_store", "data"),
    State("register_phone_store", "data"),
    State("register_csrf_token", "value"),
    # State("password", "value"),
    # State("confirm_password", "value"),
    State("register_first_name", "value"),
    State("register_last_name", "value"),
    State("register_company", "value"),
    State("register_job_title", "value"),
    State("register_city", "value"),
    State("register_country_id", "value"),
    State("register_country_id", "options"),
    State("register_time_zone_id", "value"),
    State("register_time_zone_id", "options"),
    State("register_email_preferences_checklist", "value"),
    State("register_unsubscribe_all_checkbox", "value"),
    prevent_initial_call=True,
)
def register_button_clicked(
    # registration_form_output_children,
    register_button_n_clicks,
    email_store_value,
    phone_store_data,
    csrf_token_value,
    # password_value,
    # confirm_password_value,
    first_name_value,
    last_name_value,
    company_value,
    job_title_value,
    city_value,
    country_id_value,
    country_id_options,
    time_zone_id_value,
    time_zone_id_options,
    email_preferences_checklist_value,
    unsubscribe_all_checkbox_value,
):
    if not register_button_n_clicks:
        raise PreventUpdate()

    def return_vars(
        register_button_disabled: bool = True,
        register_modal_is_open: bool = True,
        register_modal_body_children: list = [],
        validate_button_n_clicks: int = no_update,
    ):
        """Default return values"""
        return (
            register_button_disabled,
            register_modal_is_open,
            register_modal_body_children,
            validate_button_n_clicks,
        )

    errors = []

    # Create an instance of the RegistrationForm
    # form = request.form
    random_password: str = generate_random_string(16)
    # password_hash = generate_password_hash(random_password)
    # form = RegistrationForm(
    # this is only used for validation. never submitted by a CSRF request
    form = RegistrationFormVoluntary(
        email=str(email_store_value).lower().strip(),
        # password=password_value,
        # confirm_password=confirm_password_value,
        first_name=str(first_name_value).strip().title(),
        last_name=str(last_name_value).strip().title(),
        country_id=country_id_value,
        phone=phone_store_data,
        time_zone_id=time_zone_id_value,
        csrf_token=csrf_token_value,
        password=random_password,
        confirm_password=random_password,
        company=str(company_value).strip().title(),
        job_title=str(job_title_value).strip().title(),
        city=str(city_value).strip().title(),
        # Email marketing campaigns
        eml_unsubscribe_all=unsubscribe_all_checkbox_value,
        eml_marketing="marketing" in email_preferences_checklist_value,
        eml_new_products="product" in email_preferences_checklist_value,
        eml_service="service" in email_preferences_checklist_value,
        eml_rcom="rcom" in email_preferences_checklist_value,
    )

    form.country_id.choices = [(x["value"], x["label"]) for x in country_id_options]

    # The country defaults to Canada, so only load the Canadian time zones at first.
    # JavaScript will load other time zones if the country changes.
    form.time_zone_id.choices = [(x["value"], x["label"]) for x in time_zone_id_options]

    # Validate form using Flask-WTF validators
    if not form.validate():
        for field, error_messages in form.errors.items():
            for error in error_messages:
                errors.append(f"{field}: {error}")
        return return_vars(
            register_button_disabled=False,
            register_modal_is_open=True,
            register_modal_body_children=dbc.Alert(
                [html.P(error) for error in errors], color="danger"
            ),
            validate_button_n_clicks=1,
        )

    user = User.query.filter(func.lower(User.email) == form.email.data).first()

    if user is not None:
        return return_vars(
            register_button_disabled=False,
            register_modal_is_open=True,
            register_modal_body_children=dbc.Alert(
                "An account with that email address is already registered. Please log in instead."
            ),
            validate_button_n_clicks=1,
        )

    try:
        user = User(
            is_active=True,
            email=form.email.data,
            password=random_password,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            company=form.company.data,
            city=form.city.data,
            country_id=form.country_id.data,
            time_zone_id=form.time_zone_id.data,
            phone=phone_store_data,
            # Email marketing campaigns
            eml_unsubscribe_all=form.eml_unsubscribe_all.data,
            eml_marketing=form.eml_marketing.data,
            eml_new_products=form.eml_new_products.data,
            eml_service=form.eml_service.data,
            eml_rcom=form.eml_rcom.data,
            # The following are specified without an option
            is_confirmed=False,
            customer_id=CUSTOMER_ID_NO_CUSTOMER,
            # Don't allow roles of 1 or 4 since those are IJACK roles
            roles_rel=[db.session.get(Role, ROLE_ID_SELF_REGISTERED)],
            date_created=str(date.today()),
        )

        # add user to the database
        db.session.add(user)
        db.session.commit()
    except Exception as err:
        return return_vars(
            register_button_disabled=False,
            register_modal_is_open=True,
            register_modal_body_children=dbc.Alert(
                [
                    html.P(
                        [
                            "An error occurred while registering. Please try again or ",
                            html.A(
                                "contact IJACK",
                                href=url_for("dash.contact"),
                                style={
                                    "color": "white",
                                    "text-decoration": "underline",
                                    "font-weight": "bold",
                                },
                            ),
                        ]
                    ),
                    html.P(f"Technical details: {err}"),
                ],
                color="danger",
            ),
            validate_button_n_clicks=1,
        )

    # Send an email to IJACK to notify them of the new registration
    html_body = f"""<p>New user registration: {form.email.data}. Please review and potentially assign the user a role and customer.</p>
    <p>Name: {form.first_name.data} {form.last_name.data}</p>
    <p>Company: {form.company.data}</p>
    <p><a href="mailto:{form.email.data}">{form.email.data}</a></p>
    <p><a href="tel:{form.phone.data}">{form.phone.data}</a></p>
    <p>Location: {form.city.data}</p>
    <p><a href="https://myijack.com/admin/users/?search={form.email.data}">View user in the admin panel</a></p>
    <p>Date created: {date.today()}</p>"""
    send_email(
        subject="New user registration",
        sender="RCOM Website <<EMAIL>>",
        to_emails=ADMIN_EMAILS,
        text_body=f"""New user registration: {form.email.data}. The user is currently inactive. Please review and approve the registration.\n\nhttps://myijack.com/admin/users/?search={form.email.data}""",
        html_body=html_body,
    )

    return return_vars(
        register_button_disabled=True,
        register_modal_is_open=True,
        register_modal_body_children=[
            dbc.Alert(
                html.P(
                    [
                        "You have successfully registered. An IJACK representative will review your registration and contact you shortly. Feel free to ",
                        html.A(
                            "contact us",
                            href=url_for("dash.contact"),
                        ),
                        " if you have any questions.",
                    ]
                ),
                color="success",
            ),
            # html.Div(
            #     html.A(
            #         "Sign in",
            #         href=url_for("dash.login"),
            #         className="btn btn-primary",
            #     ),
            #     style={"text-align": "center"},
            # ),
        ],
    )
