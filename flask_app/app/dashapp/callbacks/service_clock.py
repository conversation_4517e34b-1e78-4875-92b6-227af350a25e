import json
import os
from datetime import date, datetime, timedelta, timezone
from typing import List, <PERSON><PERSON>
from urllib import parse

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import pandas as pd
import plotly.graph_objects as go
import pytz
from dash import (
    Input,
    Output,
    State,
    callback,
    clientside_callback,
    dcc,
    html,
    no_update,
)
from dash.dependencies import ClientsideFunction
from dash.exceptions import PreventUpdate
from flask import current_app, flash, url_for
from flask_login import current_user, logout_user
from flask_sqlalchemy.model import Model
from shared.models.models import ServiceClock, TimeZone
from shared.models.models_bom import Warehouse

from app import db, is_active
from app.config import (
    DASH_URL_SERVICE_CLOCK,
    UNIT_TYPE_ID_BOOST,
    UNIT_TYPE_ID_DGAS,
    UNIT_TYPE_ID_EGAS,
    UNIT_TYPE_ID_UNO,
    UNIT_TYPE_ID_UNOGAS,
    UNIT_TYPE_ID_VRU,
    UNIT_TYPE_ID_XFER,
)
from app.dashapp.layout_utils import get_ag_grid
from app.dashapp.metrics import BOOTSTRAP_BLUE_500, BOOTSTRAP_RED_500
from app.dashapp.utils import (
    discrete_background_color_bins,
    get_all_structures,
    get_datetime_from_parts,
    get_db_options,
    get_id_triggered,
)
from app.email_stuff import send_email
from app.models.models import Structure
from shared.utils.geographic_utils import calc_distance
from shared.utils.datetime_utils import utcnow_naive

CLASS_NAME_DISPLAY_NONE = "d-none"
MODAL_TITLE_ERROR_STYLE = {"background-color": BOOTSTRAP_RED_500, "color": "white"}


# @cache_memoize_if_prod(timeout=5)
def get_last_clock_in_record(user_id: int) -> ServiceClock | None:
    """Get the record for the last time the user clocked in, if available"""

    last_clock_in = (
        db.session.query(ServiceClock)
        .filter(
            ServiceClock.user_id == user_id,
            # User must not have clocked out already
            ServiceClock.timestamp_utc_out == None,  # noqa: E711
        )
        .order_by(ServiceClock.timestamp_utc_in.desc())
        .first()
    )
    return last_clock_in
    # if hasattr(last_clock_in, "timestamp_utc_in"):
    #     return last_clock_in.timestamp_utc_in

    # return None


def get_user_service_clock_log(
    user_id: int, since_dt: datetime | None = None
) -> List[ServiceClock] | None:
    """Get the user's service clock log"""

    since_dt = since_dt or utcnow_naive() - timedelta(days=30)

    return (
        db.session.query(ServiceClock)
        .filter(
            ServiceClock.user_id == user_id,
            ServiceClock.timestamp_utc_in >= since_dt,
        )
        .order_by(ServiceClock.timestamp_utc_in.desc())
        .all()
    )


def get_modal():
    """Modal for service clock"""
    return html.Div(
        [
            dbc.Modal(
                [
                    dbc.ModalHeader("Error", id="service_clock_modal_header"),
                    dbc.ModalBody(id="service_clock_modal_body"),
                    dbc.ModalFooter(
                        [
                            dbc.Button(
                                "Confirm",
                                id="service_clock_modal_confirm_btn",
                                className="ml-auto me-2",
                                disabled=True,
                                color="danger",
                                # Redirect after submitting form
                                external_link=True,
                                # href=f"/{DASH_URL_RCOM}/",
                                href=f"/{DASH_URL_SERVICE_CLOCK}/",
                            ),
                            # html.A(
                            #     html.Button("Refresh Page"), href="relative_pathname"
                            # ),
                            dbc.Button(
                                "Cancel",
                                id="service_clock_modal_cancel_btn",
                                # className="ml-auto",
                            ),
                        ]
                    ),
                ],
                id="service_clock_modal",
                size="lg",
                centered=True,
                is_open=False,
            ),
        ]
    )


def service_clock_layout_children() -> list:
    """Service clock layout"""

    return [
        html.Div(id="hidden_signal_sc", style={"display": "none"}),
        dcc.Location(id="location_service_clock", refresh=True),
        dcc.Store(id="user_gps_position_string_store", storage_type="memory"),
        dcc.Store(id="user_gps_position_dict_store", storage_type="memory", data={}),
        # So we don't send too many emails to Richie and Catherine
        dcc.Store(
            id="timestamp_sent_email_no_location_permissions", storage_type="local"
        ),
        # Are we editing an existing service clock entry?
        dcc.Store(storage_type="memory", id="service_clock_edit_id"),
        dcc.Store(id="service_clock_error_msg", storage_type="session"),
        dbc.Row(
            # There's already margin on the top of the page, so we don't need as much here
            class_name="mt-1",
            children=dbc.Col(
                [
                    # dbc.Label("User", class_name="mb-1"),
                    # dbc.Input(id="service_clock_user_name", disabled=True),
                    html.Header(
                        id="service_clock_header",
                        className="h4 mb-0",
                        children="Service Clock",
                    ),
                    # dbc.Collapse(
                    #     id="service_clock_msg_collapse",
                    #     class_name="mt-2",
                    #     is_open=False,
                    #     # Make it stand out a bit more
                    #     style={
                    #         # Glowing shadow in IJACK-green
                    #         "box-shadow": "0 0 10px #C1D72E",
                    #         # Match the outline of the Bootstrap card border
                    #         "border-radius": "0.25rem",
                    #     },
                    # ),
                ]
            ),
        ),
        dbc.Row(
            # # No gutters between the columns,
            # # since the second set of columns has more columns inside it
            # class_name="g-0",
            children=[
                dbc.Col(
                    class_name="mt-3",
                    width="auto",
                    children=[
                        # dropdown with list of all unit types, for filtering the list of units
                        dbc.Label(
                            "Unit or Warehouse?",
                            class_name="mb-1",
                        ),
                        dbc.RadioItems(
                            id="service_clock_unit_warehouse_radio",
                            options=[
                                {"label": "Unit", "value": "unit"},
                                {"label": "Warehouse", "value": "warehouse"},
                            ],
                            value="unit",
                            inline=True,
                            # This is the only input that might be useful to store
                            persistence=True,
                        ),
                    ],
                ),
                # Second column, whose columns we want to keep together
                dbc.Col(
                    width="auto",
                    children=[
                        dbc.Row(
                            [
                                dbc.Col(
                                    class_name="mt-3",
                                    width="auto",
                                    children=[
                                        dbc.Label(
                                            "Billable Work?",
                                            class_name="mb-1",
                                        ),
                                        dbc.Checkbox(
                                            id="service_clock_is_billable_checkbox",
                                            value=True,
                                            # inline=True,
                                            persistence=True,
                                        ),
                                    ],
                                ),
                                dbc.Col(
                                    class_name="mt-3",
                                    width="auto",
                                    children=[
                                        dbc.Label(
                                            "Travel Time?",
                                            class_name="mb-1",
                                        ),
                                        dbc.Checkbox(
                                            id="service_clock_is_travel_checkbox",
                                            value=False,
                                            # inline=True,
                                            persistence=True,
                                        ),
                                    ],
                                ),
                            ],
                        ),
                    ],
                ),
            ],
        ),
        dbc.Row(
            dbc.Col(
                [
                    # dropdown with list of all unit types, for filtering the list of units
                    dbc.Label(
                        "Select a warehouse",
                        class_name="mb-1",
                    ),
                    # For storing and obfuscating the list of units
                    dcc.Store(id="service_clock_warehouses_select_store"),
                    dcc.Dropdown(
                        id="service_clock_warehouses_select",
                        options=[
                            {
                                "label": "No Warehouse Selected",
                                "value": "0",
                            }
                        ],
                        value="0",
                        disabled=False,
                        # Don't store this since we want to reset it when the page is reloaded
                        persistence=False,
                        # placeholder="e.g. 20xxxx or 19xxxx",
                    ),
                ],
            ),
            class_name="mt-3",
            id="service_clock_warehouses_select_row",
            style={"display": "none"},
        ),
        html.Div(
            id="service_clock_unit_select_div",
            children=[
                dbc.Row(
                    [
                        dbc.Col(
                            width="auto",
                            children=[
                                dbc.Label(
                                    "Select a customer to filter the list of units",
                                    class_name="mb-1 mt-3",
                                ),
                                dcc.Dropdown(
                                    id="service_clock_customer_select",
                                    options=[
                                        {
                                            "label": "No customer selected",
                                            "value": "0",
                                        }
                                    ],
                                    value="0",
                                    disabled=False,
                                    # Don't store this since we want to reset it when the page is reloaded
                                    persistence=False,
                                ),
                                dbc.FormText(
                                    "",
                                    id="service_clock_customer_select_ft",
                                    color="danger",
                                ),
                            ],
                        ),
                        dbc.Col(
                            width="auto",
                            children=[
                                # dropdown with list of all unit types, for filtering the list of units
                                dbc.Label(
                                    "Select a unit type to filter the list of units",
                                    class_name="mb-1 mt-3",
                                ),
                                dcc.Dropdown(
                                    id="service_clock_unit_type_select",
                                    options=[
                                        {
                                            "label": "No unit type selected",
                                            "value": "0",
                                        }
                                    ],
                                    value="0",
                                    disabled=False,
                                    # Don't store this since we want to reset it when the page is reloaded
                                    persistence=False,
                                ),
                                dbc.FormText(
                                    "",
                                    id="service_clock_unit_type_select_ft",
                                    color="danger",
                                ),
                            ],
                        ),
                    ],
                ),
                dbc.Row(
                    class_name="mt-3",
                    children=dbc.Col(
                        [
                            dbc.Label("Unit", class_name="mb-1"),
                            # For storing the list of units, since we might change the filters
                            # but we want to store all queried units in case the filters change.
                            dcc.Store(id="service_clock_structures_select_store"),
                            dcc.Dropdown(
                                id="service_clock_structures_select",
                                options=[
                                    {
                                        "label": "No unit selected",
                                        "value": "0",
                                    }
                                ],
                                value="0",
                                disabled=False,
                                # Don't store this since we want to reset it when the page is reloaded
                                persistence=False,
                                placeholder="e.g. 20xxxx or 19xxxx",
                            ),
                        ]
                    ),
                ),
            ],
        ),
        dbc.Row(
            class_name="mt-3",
            children=dbc.Col(
                [
                    dbc.Label("GPS Coordinates (Approximate)", class_name="mb-1"),
                    dbc.Input(id="service_clock_gps_coordinates", disabled=True),
                    # Dash Plotly chart showing GPS coordinates on a map
                    dcc.Graph(
                        id="service_clock_gps_map",
                        # hoverData={"points": [{"customdata": None}]},
                        # style={"height": "300px"},
                        # Disable the ModeBar with the Plotly logo and other buttons
                        config=dict(
                            displayModeBar=False,
                        ),
                    ),
                ]
            ),
        ),
        # Notes row
        dbc.Row(
            # The map must have margin below it, so we don't need as much here
            class_name="mt-1",
            children=dbc.Col(
                [
                    dbc.Label("Notes", class_name="mb-1"),
                    dbc.Textarea(
                        id="service_clock_notes",
                        placeholder="Enter notes here",
                        style={"height": "100px"},
                    ),
                ]
            ),
        ),
        dbc.Row(
            # class_name="mt-3",
            id="service_clock_clock_in_row",
            children=[
                dbc.Col(
                    [
                        dbc.Label("Clock-In Date", class_name="mb-1 mt-3"),
                        # dcc.DatePickerSingle(
                        #     id="service_clock_clock_in_date",
                        #     min_date_allowed=date.today() - timedelta(days=7),
                        #     max_date_allowed=date.today(),
                        #     date=date.today(),
                        #     className="mt-1",
                        # ),
                        dmc.DatePickerInput(
                            id="service_clock_clock_in_date",
                            value=date.today(),
                            # minDate=date.today() - timedelta(days=7),
                            maxDate=date.today(),
                            className="mt-1",
                            disabled=True,
                            # dropdownType="modal", # default "popover"
                            # So the datepicker is in front of the Bootstrap modal
                            popoverProps={"zIndex": 10_000},
                            # modalProps={"zIndex": 10_000},
                            # style={"width": "200px"},
                            # dropdownPosition="bottom-start" fixes bug with Mantine DatePickerInput causing app to lock up!
                            # https://community.plotly.com/t/dash-mantine-datepicker/75251/4
                            # dropdownPosition="bottom-start",
                        ),
                    ],
                    width="auto",
                ),
                dbc.Col(
                    [
                        dbc.Label("Time", class_name="mb-1 mt-3"),
                        dmc.TimeInput(
                            id="service_clock_clock_in_time",
                            # Just a simple HH:MM string for the time input
                            value=datetime.now().strftime("%H:%M"),
                            withSeconds=False,
                            disabled=True,
                            # format="12",
                            className="mt-1",
                            # style={"width": "200px"},
                        ),
                    ],
                    width="auto",
                ),
                dbc.Col(
                    [
                        dbc.Label("Timezone", class_name="mb-1 mt-3"),
                        dcc.Dropdown(
                            id="service_clock_clock_in_time_zone_select",
                            persistence=True,
                            disabled=True,
                            className="mt-1",
                        ),
                    ],
                    # width="auto",
                ),
            ],
        ),
        dbc.Row(
            # class_name="mt-3",
            id="service_clock_clock_out_row",
            style={"display": "none"},
            children=[
                dbc.Col(
                    [
                        dbc.Label("Clock-Out Date", class_name="mb-1 mt-3"),
                        # dcc.DatePickerSingle(
                        #     id="service_clock_clock_out_date",
                        #     min_date_allowed=date.today() - timedelta(days=7),
                        #     max_date_allowed=date.today(),
                        #     date=date.today(),
                        #     className="mt-1",
                        # ),
                        dmc.DatePickerInput(
                            id="service_clock_clock_out_date",
                            value=date.today(),
                            # minDate=date.today() - timedelta(days=7),
                            maxDate=date.today(),
                            disabled=True,
                            className="mt-1",
                            # dropdownType="modal", # default "popover"
                            # So the datepicker is in front of the Bootstrap modal
                            popoverProps={"zIndex": 10_000},
                            # modalProps={"zIndex": 10_000},
                            # style={"width": "200px"},
                            # dropdownPosition="bottom-start" fixes bug with Mantine DatePickerInput causing app to lock up!
                            # https://community.plotly.com/t/dash-mantine-datepicker/75251/4
                            # dropdownPosition="bottom-start",
                        ),
                    ],
                    width="auto",
                ),
                dbc.Col(
                    [
                        dbc.Label("Time", class_name="mb-1 mt-3"),
                        dmc.TimeInput(
                            id="service_clock_clock_out_time",
                            # Just a simple HH:MM string for the time input
                            value=datetime.now().strftime("%H:%M"),
                            withSeconds=False,
                            disabled=True,
                            # format="12",
                            className="mt-1",
                            # style={"width": "200px"},
                        ),
                    ],
                    width="auto",
                ),
                dbc.Col(
                    [
                        dbc.Label("Timezone", class_name="mb-1 mt-3"),
                        dcc.Dropdown(
                            id="service_clock_clock_out_time_zone_select",
                            persistence=True,
                            disabled=True,
                            className="mt-1",
                        ),
                    ],
                    # width="auto",
                ),
            ],
        ),
        dbc.Row(
            class_name="mt-3",
            id="service_clock_total_hours_worked_row",
            style={"display": "none"},
            children=dbc.Col(
                [
                    dbc.Label("Total Hours Worked", class_name="mb-1"),
                    dbc.Input(
                        id="service_clock_total_hours_worked",
                        disabled=True,
                        type="number",
                    ),
                ]
            ),
        ),
        dbc.Row(
            class_name="mt-5",
            id="service_clock_ask_for_permission_row",
            style={"display": "none"},
            children=[
                dbc.Col(
                    [
                        dbc.Label(
                            "Location permissions are not enabled for this browser. You must enable location permissions to clock in or out.",
                            style={"color": "red"},
                        ),
                        dbc.Button(
                            "Ask for GPS Coordinates?",
                            id="service_clock_ask_for_permission_btn",
                        ),
                        dcc.Store(
                            id="service_clock_ask_for_gps_store", storage_type="local"
                        ),
                    ]
                ),
                # dbc.Col(
                #     [
                #         dbc.Button("Remove Location Permissions", id="service_clock_remove_permissions"),
                #         dcc.Store(id="service_clock_remove_permissions_store", storage_type="local"),
                #     ]
                # ),
            ],
        ),
        dbc.Row(
            class_name="mt-3",
            # justify="end",
            children=dbc.Col(
                [
                    # The "Children" on this button either shows "Clock In" or "Clock Out"
                    dbc.Button(
                        [
                            html.I(className="fa-solid fa-arrow-circle-right me-1"),
                            "Submit",
                        ],
                        type="submit",
                        id="service_clock_submit_btn",
                        class_name="float-end",
                        disabled=False,
                    ),
                ],
                # xs=3,
            ),
        ),
        # Little message below the submit button
        dbc.Row(
            class_name="mt-1",
            children=dbc.Col(
                dbc.FormText(
                    id="service_clock_submit_msg",
                    class_name="float-end",
                    color="danger",
                    style={"font-size": "24px"},
                )
            ),
        ),
        get_modal(),
    ]


def service_clock_layout():
    """
    Get the Dash layout for the service clock page.
    """
    return dbc.Container(
        fluid=True,
        children=dbc.Row(
            justify="center",
            children=[
                dbc.Col(
                    service_clock_layout_children(),
                    xs=12,
                    md=10,
                    lg=8,
                    xl=5,
                ),
                dbc.Col(
                    id="service_clock_log_table_col",
                    xs=12,
                    md=10,
                    lg=8,
                    xl=7,
                ),
            ],
        ),
    )


def get_service_clock_column_defs(df: pd.DataFrame):
    """Service clock column definitions"""
    return [
        {
            "headerName": "Edit",
            # "cellRenderer": "editButtonRenderer",  # Custom cell renderer for the edit button
            # "cellRendererParams": {
            #     "onEditClick": "function(params) { onEditButtonClick(params.data); }"
            # },
            # "cellRenderer": "markdown",
            "cellRenderer": "ServiceClockEditLink",
            "width": 80,
            "resizable": False,
            "sortable": False,
            "filter": False,
        },
        {
            "field": "location",
            "headerName": "Location",
            "minWidth": 100,
            "maxWidth": 250,
            # Wrap text in cells
            "wrapText": True,
            "autoHeight": True,
        },
        {
            "field": "total_hours_worked",
            "headerName": "Total Hours",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["total_hours_worked"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "is_travel",
            "headerName": "Is Travel",
            # "editable": True,
            "cellStyle": {
                "styleConditions": [
                    {
                        "condition": "params.value == 'Yes'",
                        "style": {"backgroundColor": "lightYellow"},
                    },
                ]
            },
        },
        {
            "field": "is_billable",
            "headerName": "Is Billable",
            # "editable": True,
            "cellStyle": {
                "styleConditions": [
                    {
                        "condition": "params.value == 'Yes'",
                        "style": {"backgroundColor": "lightGreen"},
                    },
                ]
            },
        },
        {"field": "timestamp_in", "headerName": "Clock In"},
        {"field": "timestamp_out", "headerName": "Clock Out"},
        # {"field": "notes_in", "headerName": "Notes In", "editable": True},
        # {"field": "notes_out", "headerName": "Notes Out", "editable": True},
        {
            "field": "notes",
            "headerName": "Notes",
            # "editable": True,
            "minWidth": 100,
            "maxWidth": 250,
            # Wrap text in cells
            "wrapText": True,
            "autoHeight": True,
        },
        # {"field": "gps_lat_in", "headerName": "GPS Lat In"},
        # {"field": "gps_lon_in", "headerName": "GPS Lon In"},
        # {"field": "gps_lat_out", "headerName": "GPS Lat Out"},
        # {"field": "gps_lon_out", "headerName": "GPS Lon Out"},
    ]


def validate_date(date_str: str) -> Tuple[bool, str]:
    """Validate the date string"""
    if not date_str:
        return False, "Date is missing"

    try:
        _ = pd.to_datetime(date_str)
    except ValueError:
        return False, "Invalid date"

    return True, ""


def validate_time(time_str: str) -> Tuple[bool, str]:
    """Validate the time string"""
    if not time_str:
        return False, "Time is missing"

    try:
        _ = pd.to_datetime(time_str)
    except ValueError:
        return False, "Invalid time"

    return True, ""


# # Callback for whether to display the permissions buttons row
# @callback(
#     Output("service_clock_ask_for_permission_row", "style"),
#     Input("hidden_signal_sc", "children"),
# )
# def service_clock_ask_for_permission_row(hidden_signal_children) -> dict:
#     """Callback for whether to display the permissions buttons row"""

#     if getattr(current_user, "id", None) == USER_ID_SEAN:
#         return {}

#     return {"display": "none"}

# Get location permissions
clientside_callback(
    ClientsideFunction(
        namespace="service_clock_namespace",
        function_name="ask_for_location_permissions",
    ),
    Output("service_clock_ask_for_gps_store", "data"),
    Input("service_clock_ask_for_permission_btn", "n_clicks"),
    prevent_initial_call=True,
)
# # Remove permissions (NOTE: DOESN'T SEEM TO WORK)
# clientside_callback(
#     ClientsideFunction(
#         namespace="service_clock_namespace", function_name="remove_permissions"
#     ),
#     Output("service_clock_remove_permissions_store", "data"),
#     Input("service_clock_remove_permissions", "n_clicks"),
#     prevent_initial_call=True,
# )

# Get the user's latitude and longitude from the browser
clientside_callback(
    ClientsideFunction(
        namespace="service_clock_namespace", function_name="ask_permission_get_gps"
    ),
    Output("user_gps_position_string_store", "data"),
    Input("service_clock_ask_for_gps_store", "data"),
    Input("hidden_signal_sc", "children"),
    prevent_initial_call=False,
)


@callback(
    Output("service_clock_gps_coordinates", "value"),
    Output("user_gps_position_dict_store", "data"),
    Output("timestamp_sent_email_no_location_permissions", "data"),
    Input("user_gps_position_string_store", "data"),
    State("timestamp_sent_email_no_location_permissions", "data"),
)
def display_user_gps_coordinates(
    user_gps_position_string_store_data,
    timestamp_sent_email_no_location_permissions_data,
) -> list:
    """Get the user's latitude and longitude from the browser"""

    def return_vars(
        service_clock_gps_coordinates_value: str,
        user_gps_position_dict_store_data: dict,
        timestamp_sent_email_no_location_permissions_data: dict,
    ):
        """Default return variables"""
        return (
            service_clock_gps_coordinates_value,
            user_gps_position_dict_store_data,
            timestamp_sent_email_no_location_permissions_data,
        )

    final_dict: dict = {
        "latitude": None,
        "longitude": None,
        "timezone_id": None,
    }
    try:
        gps_dict: dict = json.loads(user_gps_position_string_store_data)
        coords = gps_dict.get("coordinates", {})
        latitude = coords.get("latitude", None)
        longitude = coords.get("longitude", None)
        if latitude and longitude:
            final_dict["latitude"] = latitude
            final_dict["longitude"] = longitude

        # Extract timezone information
        timezone_info: dict = gps_dict.get("timezone", {})
        timezone_name = timezone_info.get("timezoneName")

        tz_model: TimeZone = TimeZone.query.filter_by(time_zone=timezone_name).first()
        if tz_model:
            final_dict["timezone_id"] = tz_model.id

        location_string = (
            f"""Lat: {latitude:.4f}. Lon: {longitude:.4f}. TZ: {timezone_name}"""
        )
    except (TypeError, json.JSONDecodeError):
        should_send_email = False
        if isinstance(timestamp_sent_email_no_location_permissions_data, dict):
            user_id = timestamp_sent_email_no_location_permissions_data.get(
                "user_id", None
            )
            timestamp_utc = timestamp_sent_email_no_location_permissions_data.get(
                "timestamp_utc", 0
            )
            if (
                user_id == getattr(current_user, "id", None)
                and (utcnow_naive().timestamp() - timestamp_utc) < 60 * 2
            ):
                should_send_email = False

        if should_send_email:
            if os.getenv("FLASK_CONFIG", "production") in (
                "development",
                "wsl",
                "testing",
            ):
                to_emails = ["<EMAIL>"]
            else:
                to_emails = [
                    "<EMAIL>",
                    # "<EMAIL>",
                    # "<EMAIL>",
                ]

            text_body = f"GPS error!! Has {current_user} enabled location permissions in the browser? \n\nHere's the error message from JavaScript: {user_gps_position_string_store_data}"
            send_email(
                subject="GPS error!!",
                sender="<EMAIL>",
                to_emails=to_emails,
                text_body=text_body,
                html_body=None,
                files_list=None,
                testing=False,
            )
            # Reset the timestamp for the last time an email was sent
            timestamp_sent_email_no_location_permissions_data = {
                "user_id": getattr(current_user, "id", None),
                "timestamp_utc": int(utcnow_naive().timestamp()),
            }

        error_msg = (
            f"Error getting GPS coordinates: {user_gps_position_string_store_data}"
        )
        return return_vars(
            service_clock_gps_coordinates_value=error_msg,
            user_gps_position_dict_store_data=final_dict,
            timestamp_sent_email_no_location_permissions_data=timestamp_sent_email_no_location_permissions_data,
        )

    return return_vars(
        service_clock_gps_coordinates_value=location_string,
        user_gps_position_dict_store_data=final_dict,
        timestamp_sent_email_no_location_permissions_data=no_update,
    )


@callback(
    Output("service_clock_gps_map", "figure"),
    Input("user_gps_position_dict_store", "data"),
    prevent_initial_call=True,
)
def service_clock_gps_map(user_gps_position_dict_store_data) -> go.Figure:
    """Callback to update the GPS location map"""

    if (
        not isinstance(user_gps_position_dict_store_data, dict)
        or not user_gps_position_dict_store_data
    ):
        raise PreventUpdate

    latitude = user_gps_position_dict_store_data.get("latitude", None)
    longitude = user_gps_position_dict_store_data.get("longitude", None)

    if not latitude or not longitude:
        raise PreventUpdate

    return go.Figure(
        data=go.Scattermap(
            lat=[latitude],
            lon=[longitude],
            mode="markers",
            marker=go.scattermap.Marker(size=14),
        ),
        layout=go.Layout(
            margin=dict(l=0, r=0, b=20, t=15),
            height=200,
            # autosize=True,
            # hovermode="closest",
            map=go.layout.Map(
                bearing=0,
                center=go.layout.map.Center(lat=latitude, lon=longitude),
                pitch=0,
                # The greater the zoom, the closer to earth we zoom (2-10 is good)
                zoom=8,
                style="light",
            ),
        ),
    )


@callback(
    # Output("service_clock_log_table", "rowData"),
    Output("service_clock_log_table_col", "children"),
    Input("hidden_signal_sc", "children"),
    prevent_initial_call=False,
)
def service_clock_log_table(hidden_signal_children) -> list:
    """Callback to populate the service clock log table"""

    # Get the user's service clock log
    service_clocks: List[Model] | None = get_user_service_clock_log(
        user_id=getattr(current_user, "id", None)
    )
    if service_clocks is None:
        # We still want to return an empty table
        service_clocks = []

    pytz_wanted = pytz.timezone(current_user.time_zones_rel.time_zone)
    # For the AG Grid table
    rowData = []
    # For the pandas DataFrame, for conditional formatting, which needs numbers
    df_rows = []
    for clock in service_clocks:
        timestamp_in_str: str = ""
        timestamp_out_str: str = ""
        total_hours_worked: float = 0.0
        total_hours_worked_str: str = ""
        if clock.timestamp_utc_in:
            # Convert from UTC to time zone wanted
            timestamp_in = pytz_wanted.fromutc(clock.timestamp_utc_in)
            timestamp_in_str = timestamp_in.strftime("%Y-%m-%d %-H:%M %Z")
        if clock.timestamp_utc_out:
            # Convert from UTC to time zone wanted
            timestamp_out = pytz_wanted.fromutc(clock.timestamp_utc_out)
            timestamp_out_str = timestamp_out.strftime("%Y-%m-%d %-H:%M %Z")
            # Display with one decimal point
            total_hours_worked = clock.total_hours_worked.total_seconds() / 3600
            total_hours_worked_str = f"{total_hours_worked:.2f}"

        if clock.structure_rel:
            location = str(clock.structure_rel)
        elif clock.warehouse_rel:
            location = f"{clock.warehouse_rel} (warehouse)"
        else:
            location = "None"

        rowData.append(
            {
                # Markdown edit crayon with link to row ID
                "edit": clock.id,
                "location": location,
                "total_hours_worked": total_hours_worked_str,
                "is_travel": "Yes" if clock.is_travel else "No",
                "is_billable": "Yes" if clock.is_billable else "No",
                "timestamp_in": timestamp_in_str,
                "timestamp_out": timestamp_out_str,
                # "notes_in": clock.notes_in,
                # "notes_out": clock.notes_out,
                "notes": clock.notes,
                "gps_lat_in": clock.gps_lat_in,
                "gps_lon_in": clock.gps_lon_in,
                "gps_lat_out": clock.gps_lat_out,
                "gps_lon_out": clock.gps_lon_out,
            }
        )
        df_rows.append(
            {
                "total_hours_worked": total_hours_worked,
            }
        )

    div_children = html.Div(
        className="mt-3",
        children=get_ag_grid(
            id="service_clock_log_table",
            rowData=rowData,
            columnSize="autoSize",
            columnDefs=get_service_clock_column_defs(df=pd.DataFrame(df_rows)),
        ),
        # AgGrid(
        #     id="service_clock_log_table",
        #     rowData=rowData,
        #     # Default theme is "ag-theme-quartz"
        #     className="ag-theme-quartz",
        #     dashGridOptions={
        #         # allow the grid to auto-size its height to fit rows
        #         # "domLayout": "autoHeight"
        #     },
        #     # 80vh is 80% of the viewport height
        #     style={"height": "80vh", "width": "100%"},
        #     # autoSize changes the column sizes to fit the column's content
        #     columnSize="autoSize",
        #     columnSizeOptions={
        #         "defaultMinWidth": 100,
        #         # "columnLimits": [{"key": "location", "maxWidth": 150}],
        #     },
        #     columnDefs=get_service_clock_column_defs(df=pd.DataFrame(df_rows)),
        #     defaultColDef={"resizable": True},
        #     getRowStyle={
        #         "styleConditions": [
        #             {
        #                 # Set every 2nd row to have a background color
        #                 "condition": "params.rowIndex % 2 === 0",
        #                 "style": {
        #                     "backgroundColor": "rgba(0, 0, 0, 0.05)",
        #                 },
        #             },
        #         ]
        #     },
        #     # style_cell_conditional=[
        #     #     {"if": {"column_id": "user"}, "textAlign": "left"},
        #     # ],
        #     # style_header={"backgroundColor": "white", "fontWeight": "bold"},
        #     # style_data_conditional=[
        #     #     {
        #     #         "if": {"row_index": "odd"},
        #     #         "backgroundColor": "rgb(248, 248, 248)",
        #     #     },
        #     # ],
        # ),
    )

    return div_children


@callback(
    Output("location_service_clock", "href"),
    Input("hidden_signal_sc", "children"),
    prevent_initial_call=False,
)
def check_is_active(
    hidden_signal_children,
):
    """Check if the user is active"""
    if not is_active():
        flash(
            "You have been logged out since your RCOM account is inactive. Please contact IJACK."
        )
        logout_user()
        return url_for("dash.login")
    raise PreventUpdate()


@callback(
    Output("service_clock_clock_in_time_zone_select", "options"),
    Output("service_clock_clock_out_time_zone_select", "options"),
    Input("hidden_signal_sc", "children"),
)
def populate_time_zone_select(_):
    """Populate the time zone select dropdown"""

    options: list = get_db_options(
        columns=["time_zone"],
        table="time_zones",
        schema="public",
        where="",
        ascending=True,
    )
    if not options:
        raise PreventUpdate()

    # options.insert(0, {"label": "No time zone selected", "value": 0})

    return options, options


@callback(
    Output("service_clock_customer_select", "options"),
    Input("hidden_signal_sc", "children"),
)
def populate_customer_select(_):
    """Populate the customer select dropdown"""

    options: list = get_db_options(
        columns=["customer"],
        table="customers",
        schema="public",
        where="",
        ascending=True,
    )
    if not options:
        raise PreventUpdate()

    options.insert(0, {"label": "No customer selected", "value": 0})

    return options


@callback(
    Output("service_clock_unit_type_select", "options"),
    Input("hidden_signal_sc", "children"),
)
def populate_unit_type_select(_):
    """Populate the unit_type select dropdown"""

    unit_types_allowed: str = ",".join(
        (
            str(UNIT_TYPE_ID_BOOST),
            str(UNIT_TYPE_ID_DGAS),
            str(UNIT_TYPE_ID_EGAS),
            str(UNIT_TYPE_ID_UNO),
            str(UNIT_TYPE_ID_UNOGAS),
            str(UNIT_TYPE_ID_VRU),
            str(UNIT_TYPE_ID_XFER),
        )
    )
    options: list = get_db_options(
        columns=["unit_type"],
        table="unit_types",
        schema="public",
        where=f"where id in ({unit_types_allowed})",
        ascending=False,
    )
    if not options:
        raise PreventUpdate()

    options.insert(0, {"label": "No unit type selected", "value": 0})

    return options


@callback(
    Output("service_clock_structures_select_store", "data"),
    Input("user_gps_position_dict_store", "data"),
    prevent_initial_call=True,
)
def populate_structures_select_store(
    user_gps_position_dict_store_data: dict,
) -> list:
    """Query for all structures only once when the page loads"""

    # JSON-ready list of dicts
    structures: List[dict] = get_all_structures(
        user=current_user, ijack_units_allowed=False
    )
    if not structures:
        raise PreventUpdate()

    df: pd.DataFrame = pd.DataFrame(structures)

    # Calculate the distance from the user's GPS position to each structure
    gps_lat = user_gps_position_dict_store_data.get("latitude", None)
    gps_lon = user_gps_position_dict_store_data.get("longitude", None)
    df["distance"] = df.apply(
        lambda row: calc_distance(
            lat1=gps_lat,
            lon1=gps_lon,
            lat2=row["gps_lat"],
            lon2=row["gps_lon"],
        ),
        axis=1,
    )
    df = df.sort_values(by="distance", ascending=True)
    struct_list = df.to_dict(orient="records")

    return struct_list


@callback(
    Output("service_clock_warehouses_select_store", "data"),
    Input("hidden_signal_sc", "children"),
    Input("user_gps_position_dict_store", "data"),
    prevent_initial_call=False,
)
def populate_warehouses_select_store(
    _,
    user_gps_position_dict_store_data: dict,
) -> list:
    """Query for all warehouses only once when the page loads"""

    # JSON-ready list of dicts
    warehouses: List[dict] = Warehouse.query.all()
    if not warehouses:
        raise PreventUpdate()

    warehouses_list = [
        {
            "id": row.id,
            "name": row.name,
            "description": row.description,
            "gps_lat": row.gps_lat,
            "gps_lon": row.gps_lon,
            "time_zone_id": row.time_zone_id,
        }
        for row in warehouses
    ]
    df: pd.DataFrame = pd.DataFrame(warehouses_list)

    # Calculate the distance from the user's GPS position to each warehouse
    gps_lat = user_gps_position_dict_store_data.get("latitude", None)
    gps_lon = user_gps_position_dict_store_data.get("longitude", None)
    if gps_lat and gps_lon:
        df["distance"] = df.apply(
            lambda row: calc_distance(
                lat1=gps_lat,
                lon1=gps_lon,
                lat2=row["gps_lat"],
                lon2=row["gps_lon"],
            ),
            axis=1,
        )
        df = df.sort_values(by="distance", ascending=True)
    else:
        df["distance"] = 0.0

    warehouses_list = df.to_dict(orient="records")

    return warehouses_list


@callback(
    Output("service_clock_structures_select", "options"),
    Input("service_clock_structures_select", "search_value"),
    Input("service_clock_customer_select", "value"),
    Input("service_clock_unit_type_select", "value"),
    Input("service_clock_structures_select_store", "data"),
    prevent_initial_call=True,
)
def populate_structures_select(
    service_clock_structures_select_search_value,
    service_clock_customer_select_value,
    service_clock_unit_type_select_value,
    service_clock_structures_select_store_data,
):
    """Populate the structures select dropdown"""

    if not service_clock_structures_select_store_data:
        return [{"label": "No units found", "value": 0}]

    id_triggered: str = get_id_triggered()
    if (
        id_triggered == "service_clock_structures_select.search_value"
        and not service_clock_structures_select_search_value
    ):
        # Don't trigger this callback when the search box is cleared
        # (i.e. after a mouse-up event... feels like a Dash bug, but this addresses/fixes it...)
        raise PreventUpdate()

    # # Limit the number of options so people can't see how many units we have
    # unit_limit: int = 5
    # counter: int = 0
    options: list = [{"label": "No unit selected", "value": 0}]
    for structure in service_clock_structures_select_store_data:
        if service_clock_customer_select_value not in (0, "0", None):
            # Filter on customer, if a customer is selected
            if str(structure["customer_id"]) != str(
                service_clock_customer_select_value
            ):
                continue

        if service_clock_unit_type_select_value not in (0, "0", None):
            # Filter on unit type, if a unit type is selected
            if str(structure["unit_type_id"]) != str(
                service_clock_unit_type_select_value
            ):
                continue

        if service_clock_structures_select_search_value:
            # Filter on search value
            search_value_lower = str(
                service_clock_structures_select_search_value
            ).lower()
            if not (
                search_value_lower in str(structure["power_unit_str"]).lower()
                or search_value_lower in str(structure["structure_str"]).lower()
                or search_value_lower in str(structure["customer"]).lower()
                or search_value_lower in str(structure["surface"]).lower()
            ):
                continue

        # # We're going to add an option, so increment the counter
        # counter += 1
        # if counter > unit_limit:
        #     break

        if structure["distance"] is None:
            label: str = f"?? km {structure['unit_type']}"
        else:
            label: str = f"{structure['distance']:,.2f} km {structure['unit_type']}"

        if structure["power_unit_str"]:
            label += f" power unit {structure['power_unit_str']},"
        label += f" structure {structure['structure_str']}"

        if structure["customer"]:
            label += f" ({structure['customer']})"

        options.append(
            {
                "value": structure["id"],
                "label": label,
            }
        )

    return options


@callback(
    Output("service_clock_warehouses_select", "options"),
    Input("service_clock_warehouses_select", "search_value"),
    Input("service_clock_warehouses_select_store", "data"),
    prevent_initial_call=True,
)
def populate_warehouses_select(
    service_clock_warehouses_select_search_value,
    service_clock_warehouses_select_store_data,
):
    """Populate the warehouses select dropdown"""

    if not service_clock_warehouses_select_store_data:
        return [{"label": "No units found", "value": 0}]

    id_triggered: str = get_id_triggered()
    if (
        id_triggered == "service_clock_warehouses_select.search_value"
        and not service_clock_warehouses_select_search_value
    ):
        # Don't trigger this callback when the search box is cleared
        # (i.e. after a mouse-up event... feels like a Dash bug, but this addresses/fixes it...)
        raise PreventUpdate()

    options: list = [{"label": "No unit selected", "value": 0}]
    for warehouse in service_clock_warehouses_select_store_data:
        if service_clock_warehouses_select_search_value:
            # Filter on search value
            search_value_lower = str(
                service_clock_warehouses_select_search_value
            ).lower()
            if not (
                search_value_lower in str(warehouse["name"]).lower()
                or search_value_lower in str(warehouse["description"]).lower()
            ):
                continue

        if warehouse["distance"] is None:
            label: str = f"?? km {warehouse['name']}"
        else:
            label: str = f"{warehouse['distance']:,.2f} km {warehouse['name']}"

        options.append(
            {
                "value": warehouse["id"],
                "label": label,
            }
        )

    return options


@callback(
    Output("service_clock_warehouses_select_row", "style"),
    Output("service_clock_unit_select_div", "style"),
    Input("service_clock_unit_warehouse_radio", "value"),
    prevent_initial_call=False,
)
def unit_or_warehouse_select(
    service_clock_unit_warehouse_radio_value,
) -> Tuple[dict, dict]:
    """Show or hide the unit or warehouse select dropdown"""
    if service_clock_unit_warehouse_radio_value == "unit":
        return {"display": "none"}, {}
    return {}, {"display": "none"}


@callback(
    Output("service_clock_edit_id", "data"),
    # Output("service_clock_msg_collapse", "is_open"),
    # Output("service_clock_msg_collapse", "children"),
    Input("url", "search"),
    Input("url", "pathname"),
)
def are_we_editing_an_existing_service_clock(url_search, url_pathname) -> tuple:
    """If we're editing an existing work order, store its ID"""

    if not url_search:
        raise PreventUpdate

    # return vars
    service_clock_id: int = None
    # service_clock_msg_collapse_is_open: bool = False
    # service_clock_msg_collapse_children = None

    if url_search:
        query_str = parse.urlsplit(url_search).query
        query_dict = parse.parse_qs(query_str)
        service_clock_id = query_dict.get("edit_service_clock_id", None)
        if isinstance(service_clock_id, (list, tuple)):
            service_clock_id = service_clock_id[0]

    # if service_clock_id:
    #     service_clock_msg_collapse_is_open = True
    #     service_clock_msg_collapse_children = dbc.Card(
    #         dbc.CardBody("EDIT MODE"),
    #         class_name="mt-2 mb-2",
    #     )

    return service_clock_id
    # service_clock_msg_collapse_is_open,
    # service_clock_msg_collapse_children,
    # )


@callback(
    Output("service_clock_header", "children"),
    Output("service_clock_clock_in_date", "value"),
    Output("service_clock_clock_in_time", "value"),
    Output("service_clock_clock_in_time_zone_select", "value"),
    Output("service_clock_clock_out_date", "value"),
    Output("service_clock_clock_out_time", "value"),
    Output("service_clock_clock_out_time_zone_select", "value"),
    Output("service_clock_clock_out_row", "style"),
    # Output("service_clock_total_hours_worked", "value", allow_duplicate=True),
    Output("service_clock_total_hours_worked_row", "style"),
    Output("service_clock_submit_btn", "children"),
    Output("service_clock_unit_type_select", "value"),
    Output("service_clock_unit_type_select", "disabled"),
    Output("service_clock_structures_select", "value"),
    Output("service_clock_structures_select", "disabled"),
    Output("service_clock_warehouses_select", "value"),
    Output("service_clock_warehouses_select", "disabled"),
    Output("service_clock_unit_warehouse_radio", "value"),
    Output("service_clock_is_billable_checkbox", "value"),
    Output("service_clock_is_travel_checkbox", "value"),
    Output("service_clock_notes", "value"),
    Input("hidden_signal_sc", "children"),
    Input("service_clock_structures_select_store", "data"),
    Input("service_clock_warehouses_select_store", "data"),
    Input("service_clock_edit_id", "data"),
    Input("user_gps_position_dict_store", "data"),
    # State("service_clock_clock_in_time_zone_select", "value"),
    # State("service_clock_clock_out_time_zone_select", "value"),
    State("service_clock_unit_warehouse_radio", "value"),
    State("service_clock_is_billable_checkbox", "value"),
    State("service_clock_is_travel_checkbox", "value"),
    prevent_initial_call=False,
)
def set_default_values(
    _,
    service_clock_structures_select_store_data,
    service_clock_warehouses_select_store_data,
    service_clock_edit_id,
    user_gps_position_dict_store_data,
    service_clock_unit_warehouse_radio_value,
    service_clock_is_billable_checkbox_value,
    service_clock_is_travel_checkbox_value,
) -> Tuple[str, dict, float, dict, str]:
    """Set the default values for the service clock form fields"""

    def return_vars(
        clocking_in: bool = True,
        is_editing: bool = False,
        is_warehouse: bool = False,
        header: str = "Clock In",
        clock_in_timestamp_utc: datetime = utcnow_naive(),
        clock_out_timestamp_utc: datetime = utcnow_naive(),
        preferred_time_zone_id: int = no_update,
        # clock_in_time: datetime = no_update,
        # clock_in_time_zone: str = no_update,
        clock_out_row_style: dict = {"display": "none"},
        # total_hours_worked: float = no_update,
        total_hours_worked_row_style: dict = {"display": "none"},
        submit_btn_children: str = "Clock In",
        unit_type_select_value: int | None = None,
        unit_type_select_disabled: bool = False,
        structures_select_value: int | None = None,
        structures_select_disabled: bool = False,
        warehouses_select_value: int | None = None,
        warehouses_select_disabled: bool = False,
        unit_warehouse_radio_value: str = no_update,
        is_billable_checkbox_value: bool | None = None,
        is_travel_checkbox_value: bool | None = None,
        service_clock_notes_value: str = "",
    ) -> tuple:
        """Return the variables for the callback"""

        user_full_name = f"{current_user.first_name} {current_user.last_name}"
        time_zone_id: int = None
        if isinstance(user_gps_position_dict_store_data, dict):
            # gps_lat = user_gps_position_dict_store_data.get("latitude", None)
            # gps_lon = user_gps_position_dict_store_data.get("longitude", None)
            time_zone_id = user_gps_position_dict_store_data.get("timezone_id", None)

        if clocking_in and not is_editing:
            if is_warehouse and service_clock_warehouses_select_store_data:
                # Find the closest warehouse
                closest_warehouse = service_clock_warehouses_select_store_data[0]
                warehouses_select_value = closest_warehouse["id"]
                if not time_zone_id:
                    time_zone_id = closest_warehouse["time_zone_id"]
            else:
                # Get the closest unit, since they're clocking in and it's sorted by distance
                try:
                    closest_unit = service_clock_structures_select_store_data[0]
                    structures_select_value = closest_unit["id"]
                    unit_type_select_value = closest_unit["unit_type_id"]
                    if not time_zone_id:
                        time_zone_id = closest_unit["time_zone_id"]
                except Exception:
                    raise PreventUpdate()
        elif not time_zone_id:
            # Time zone from when the user clocked in
            time_zone_id = preferred_time_zone_id

        # Convert from UTC to the preferred time zone for display
        # .replace won't work!
        # https://stackoverflow.com/questions/36156675/behavior-of-pytz-timezones-is-inconsistent
        # https://pythonhosted.org/pytz/
        tz_wanted_model = TimeZone.query.get(time_zone_id)
        if not tz_wanted_model:
            raise PreventUpdate
        else:
            pytz_wanted = tz_wanted_model.to_pytz()

        if getattr(clock_in_timestamp_utc, "tzinfo", None) is None:
            clock_in_utc_aware = pytz.utc.localize(clock_in_timestamp_utc)
        else:
            clock_in_utc_aware = clock_in_timestamp_utc

        if getattr(clock_out_timestamp_utc, "tzinfo", None) is None:
            clock_out_utc_aware = pytz.utc.localize(clock_out_timestamp_utc)
        else:
            clock_out_utc_aware = clock_out_timestamp_utc

        clock_in_timestamp_local = clock_in_utc_aware.astimezone(pytz_wanted)
        clock_out_timestamp_local = clock_out_utc_aware.astimezone(pytz_wanted)
        clock_in_ts_local_no_tz = clock_in_timestamp_local.replace(tzinfo=None)
        clock_out_ts_local_no_tz = clock_out_timestamp_local.replace(tzinfo=None)

        # Convert to a string for the header
        style_wanted = "%-I:%M %p %Z on %Y-%m-%d (%a)"
        clock_in_time_str = clock_in_timestamp_local.strftime(style_wanted)
        if is_editing:
            header = html.Span(
                [
                    html.Strong(html.Em("Editing ")),
                    f"record {service_clock_edit_id} from {clock_in_time_str}",
                ]
            )
        elif clocking_in:
            header = html.Span(
                [f"{user_full_name} is ", html.Strong("not"), " clocked in yet"]
            )
        else:
            header = f"{user_full_name} has been clocked in since {clock_in_time_str}."

        if is_billable_checkbox_value is None:
            # If the user hasn't clocked in yet, use what's already selected
            is_billable_checkbox_value = service_clock_is_billable_checkbox_value
        if is_travel_checkbox_value is None:
            # If the user hasn't clocked in yet, use what's already selected
            is_travel_checkbox_value = service_clock_is_travel_checkbox_value

        return (
            header,
            # clock_in_ts_local_no_tz.date(),
            # clock_in_ts_local_no_tz.time(),
            # clock_in_timestamp_local.date(),
            # clock_in_timestamp_local.time(),
            clock_in_ts_local_no_tz.date(),
            # Just a simple HH:MM string for the time input
            clock_in_ts_local_no_tz.strftime("%H:%M"),
            time_zone_id,
            # clock_out_timestamp_local.date(),
            # clock_out_timestamp_local.time(),
            clock_out_ts_local_no_tz.date(),
            # Just a simple HH:MM string for the time input
            clock_out_ts_local_no_tz.strftime("%H:%M"),
            time_zone_id,
            clock_out_row_style,
            # total_hours_worked,
            total_hours_worked_row_style,
            submit_btn_children,
            unit_type_select_value,
            unit_type_select_disabled,
            structures_select_value,
            structures_select_disabled,
            warehouses_select_value,
            warehouses_select_disabled,
            unit_warehouse_radio_value,
            is_billable_checkbox_value,
            is_travel_checkbox_value,
            service_clock_notes_value,
        )

    is_warehouse = service_clock_unit_warehouse_radio_value == "warehouse"

    if service_clock_edit_id:
        db_record = ServiceClock.query.get(service_clock_edit_id)
        is_editing = True
        submit_btn_children = "Submit Changes"
        # Might be editing a clock in or a clock out record
        clock_out_timestamp_utc = db_record.timestamp_utc_out or datetime.now(
            timezone.utc
        )
    else:
        db_record = get_last_clock_in_record(user_id=getattr(current_user, "id", None))
        is_editing = False
        submit_btn_children = "Clock Out"
        clock_out_timestamp_utc = utcnow_naive()

    if not hasattr(db_record, "timestamp_utc_in"):
        # If can't find a record (i.e. it's None), then clock in
        return return_vars(clocking_in=True, is_warehouse=is_warehouse)

    timestamp_utc_in = db_record.timestamp_utc_in
    if not isinstance(timestamp_utc_in, datetime):
        # Clock in
        return return_vars(clocking_in=True, is_warehouse=is_warehouse)

    clocking_in = False
    # if service_clock_edit_id and isinstance(
    #     db_record.total_hours_worked, timedelta
    # ):
    #     # clocking_in=False
    #     total_hours_worked_delta = db_record.total_hours_worked
    # else:
    #     # We could be editing a clock-in record that doesn't have a clock-out record yet
    #     # Calculate the total hours worked (UTC times)
    #     # clocking_in=True
    #     total_hours_worked_delta = utcnow_aware() - timestamp_utc_in.replace(
    #         tzinfo=timezone.utc
    #     )

    # total_hours_worked = round(total_hours_worked_delta.total_seconds() / 3600, 2)

    if isinstance(db_record.structure_rel, Structure):
        unit_warehouse_str = "unit"
        structure_id = db_record.structure_id
        unit_type_id = db_record.structure_rel.unit_type_id
        warehouse_id = None
    elif isinstance(db_record.warehouse_rel, Warehouse):
        unit_warehouse_str = "warehouse"
        structure_id = None
        unit_type_id = None
        warehouse_id = db_record.warehouse_id
    else:
        unit_warehouse_str = service_clock_unit_warehouse_radio_value
        structure_id = None
        unit_type_id = None
        warehouse_id = None

    # Clock out or edit record
    return return_vars(
        clocking_in=clocking_in,
        is_editing=is_editing,
        is_warehouse=unit_warehouse_str == "warehouse",
        clock_in_timestamp_utc=timestamp_utc_in,
        clock_out_timestamp_utc=clock_out_timestamp_utc,
        preferred_time_zone_id=db_record.time_zone_in_id,
        # clock_in_time=timestamp_local_obj.time(),
        # clock_in_time_zone=pytz_wanted.id,
        clock_out_row_style={},
        # total_hours_worked=total_hours_worked,
        total_hours_worked_row_style={},
        submit_btn_children=submit_btn_children,
        unit_type_select_value=unit_type_id,
        unit_type_select_disabled=False,
        structures_select_value=structure_id,
        structures_select_disabled=False,
        warehouses_select_disabled=False,
        warehouses_select_value=warehouse_id,
        unit_warehouse_radio_value=unit_warehouse_str,
        is_billable_checkbox_value=db_record.is_billable,
        is_travel_checkbox_value=db_record.is_travel,
        service_clock_notes_value=db_record.notes,
    )


@callback(
    Output("service_clock_clock_in_time", "error"),
    Input("service_clock_clock_in_time", "value"),
    prevent_initial_call=True,
)
def validate_clock_in_time(time_str: str) -> str:
    """Validate the clock in time"""
    return validate_time(time_str)[1]


@callback(
    Output("service_clock_clock_out_time", "error"),
    Input("service_clock_clock_out_time", "value"),
    prevent_initial_call=True,
)
def validate_clock_out_time(time_str: str) -> str:
    """Validate the clock in time"""
    return validate_time(time_str)[1]


@callback(
    Output("service_clock_clock_in_date", "error"),
    Input("service_clock_clock_in_date", "value"),
    prevent_initial_call=True,
)
def validate_clock_in_date(date_str: str) -> str:
    """Validate the clock in date"""
    return validate_date(date_str)[1]


@callback(
    Output("service_clock_clock_out_date", "error"),
    Output("service_clock_total_hours_worked", "value"),
    Input("service_clock_clock_out_date", "value"),
    Input("service_clock_clock_in_date", "value"),
    Input("service_clock_clock_out_time", "value"),
    Input("service_clock_clock_in_time", "value"),
    Input("service_clock_clock_out_time_zone_select", "value"),
    Input("service_clock_clock_in_time_zone_select", "value"),
    # So the validation runs when the user clicks the submit button
    Input("service_clock_submit_btn", "n_clicks"),
    prevent_initial_call=True,
)
def validate_clock_out_date(
    service_clock_clock_out_date_value,
    service_clock_clock_in_date_value,
    service_clock_clock_out_time_value,
    service_clock_clock_in_time_value,
    service_clock_clock_out_time_zone_select_value,
    service_clock_clock_in_time_zone_select_value,
    # So the validation runs when the user clicks the submit button
    service_clock_submit_btn_n_clicks,
):
    """Validate the clock out date, using all available inputs"""

    # Also validate the clock in date
    is_good, msg = validate_date(service_clock_clock_in_date_value)
    if not is_good:
        return f"{msg} for clock-in date", 0.0

    is_good, msg = validate_date(service_clock_clock_out_date_value)
    if not is_good:
        return msg

    clock_in_datetime: datetime | None = get_datetime_from_parts(
        date_str=service_clock_clock_in_date_value,
        time_str=service_clock_clock_in_time_value,
        time_zone_id=service_clock_clock_in_time_zone_select_value,
    )
    if not clock_in_datetime:
        return "Clock in date and time must be valid", 0.0
    clock_out_datetime: datetime | None = get_datetime_from_parts(
        date_str=service_clock_clock_out_date_value,
        time_str=service_clock_clock_out_time_value,
        time_zone_id=service_clock_clock_out_time_zone_select_value,
    )
    if not clock_out_datetime:
        return "Clock out date and time must be valid", 0.0

    time_delta = clock_out_datetime - clock_in_datetime
    total_hours_worked = round(time_delta.total_seconds() / 3600, 2)

    if clock_out_datetime < clock_in_datetime:
        return "Clock out time must be after clock in time", total_hours_worked

    return "", total_hours_worked


@callback(
    Output("service_clock_modal_confirm_btn", "disabled"),
    Output("service_clock_modal_header", "children"),
    Output("service_clock_modal_body", "children"),
    Input("service_clock_submit_btn", "n_clicks"),
    Input("service_clock_error_msg", "data"),
    # State("service_clock_clock_in_date", "value"),
    # State("service_clock_clock_in_time", "value"),
    # State("service_clock_clock_in_time_zone_select", "value"),
    # State("service_clock_gps_coordinates", "value"),
    State("service_clock_submit_btn", "children"),
    prevent_initial_call=True,
)
def service_clock_submit_modal(
    submit_btn_n_clicks,
    service_clock_error_msg_data,
    # service_clock_clock_in_date_value,
    # service_clock_clock_in_time_value,
    # service_clock_clock_in_time_zone_select_value,
    # gps_coordinates,
    service_clock_submit_btn_children,
) -> tuple:
    """
    Callback for when the user submits the form.
    Opens the modal and displays a message, and asks for confirmation
    """

    def return_vars(
        service_clock_modal_confirm_btn_disabled: bool,
        service_clock_modal_header: str,
        service_clock_modal_body: str,
    ) -> tuple:
        return (
            service_clock_modal_confirm_btn_disabled,
            service_clock_modal_header,
            service_clock_modal_body,
        )

    id_triggered: str = get_id_triggered()
    if service_clock_error_msg_data and id_triggered == "service_clock_error_msg.data":
        return return_vars(
            service_clock_modal_confirm_btn_disabled=True,
            service_clock_modal_header="Error Updating Database",
            service_clock_modal_body=service_clock_error_msg_data,
        )

    if not current_user.is_authenticated:
        return return_vars(
            service_clock_modal_confirm_btn_disabled=True,
            service_clock_modal_header="Error",
            service_clock_modal_body="You must be logged in to clock in or out",
        )

    if service_clock_submit_btn_children == "Clock In":
        clocking_string = "clocking in"
    elif service_clock_submit_btn_children == "Clock Out":
        clocking_string = "clocking out"
    elif service_clock_submit_btn_children == "Submit Changes":
        clocking_string = "editing an existing record"
    else:
        return return_vars(
            service_clock_modal_confirm_btn_disabled=True,
            service_clock_modal_header="Error",
            service_clock_modal_body="App error: not sure whether to 'Clock In' or 'Clock Out'... Please inform the software developer.",
        )

    # # Convert to a string
    # style_wanted = "%-I:%M %p %Z on %Y-%m-%d (%a)"
    # clock_out_time_str = pd.to_datetime(
    #     service_clock_clock_out_time_value
    # ).strftime(style_wanted)

    user_name = f"{current_user.first_name} {current_user.last_name}"
    modal_body = [
        html.P(f"{user_name} is {clocking_string}."),
        # html.Br(),
        # html.P(f"GPS: {gps_coordinates}."),
        html.Br(),
        html.P("Are you sure?"),
    ]

    return return_vars(
        service_clock_modal_confirm_btn_disabled=False,
        service_clock_modal_header=f"Please Confirm {service_clock_submit_btn_children}",
        service_clock_modal_body=modal_body,
    )


@callback(
    Output("service_clock_modal", "is_open"),
    Output("service_clock_submit_msg", "children"),
    Output("service_clock_submit_msg", "color"),
    Output("service_clock_error_msg", "data"),
    Input("service_clock_submit_btn", "n_clicks"),
    Input("service_clock_modal_cancel_btn", "n_clicks"),
    Input("service_clock_modal_confirm_btn", "n_clicks"),
    State("service_clock_edit_id", "data"),
    State("user_gps_position_dict_store", "data"),
    # State("service_clock_submit_btn", "children"),
    State("service_clock_notes", "value"),
    State("service_clock_structures_select", "value"),
    State("service_clock_warehouses_select", "value"),
    State("service_clock_unit_warehouse_radio", "value"),
    State("service_clock_is_billable_checkbox", "value"),
    State("service_clock_is_travel_checkbox", "value"),
    State("service_clock_clock_in_date", "value"),
    State("service_clock_clock_in_time", "value"),
    State("service_clock_clock_in_time_zone_select", "value"),
    State("service_clock_clock_out_date", "value"),
    State("service_clock_clock_out_time", "value"),
    State("service_clock_clock_out_time_zone_select", "value"),
    State("service_clock_clock_in_date", "error"),
    State("service_clock_clock_in_time", "error"),
    State("service_clock_clock_out_date", "error"),
    State("service_clock_clock_out_time", "error"),
    prevent_initial_call=True,
)
def service_clock_modal_submit_and_close(
    service_clock_submit_btn_n_clicks,
    service_clock_modal_cancel_btn_n_clicks,
    service_clock_modal_confirm_btn_n_clicks,
    service_clock_edit_id,
    user_gps_position_dict_store_data,
    # service_clock_submit_btn_children,
    service_clock_notes_value,
    service_clock_structures_select_value,
    service_clock_warehouses_select_value,
    service_clock_unit_warehouse_radio_value,
    service_clock_is_billable_checkbox_value,
    service_clock_is_travel_checkbox_value,
    service_clock_clock_in_date_value,
    service_clock_clock_in_time_value,
    service_clock_clock_in_time_zone_select_value,
    service_clock_clock_out_date_value,
    service_clock_clock_out_time_value,
    service_clock_clock_out_time_zone_select_value,
    service_clock_clock_in_date_error,
    service_clock_clock_in_time_error,
    service_clock_clock_out_date_error,
    service_clock_clock_out_time_error,
) -> bool:
    """Callback to submit the form to the database and close the modal"""

    def return_vars(
        service_clock_modal_is_open: bool,
        service_clock_submit_msg_children: str = no_update,
        service_clock_submit_msg_color: str = no_update,
        error_message: str = no_update,
    ) -> tuple:
        return (
            service_clock_modal_is_open,
            service_clock_submit_msg_children,
            service_clock_submit_msg_color,
            error_message,
        )

    if any(
        (
            service_clock_clock_in_date_error != "",
            service_clock_clock_in_time_error != "",
            service_clock_clock_out_date_error != "",
            service_clock_clock_out_time_error != "",
        )
    ):
        error_message = "Please fix the errors before submitting the form"
        return return_vars(
            service_clock_modal_is_open=False,
            service_clock_submit_msg_children=error_message,
            service_clock_submit_msg_color="danger",
            error_message=error_message,
        )

    id_triggered: str = get_id_triggered()
    if id_triggered == "service_clock_submit_btn.n_clicks":
        # Open the confirmation modal
        return return_vars(True)

    if (
        id_triggered != "service_clock_modal_confirm_btn.n_clicks"
        or not service_clock_modal_confirm_btn_n_clicks
    ):
        # Ensure we close the modal, and don't insert the data into the database
        return return_vars(False)

    # If the user clicks the "Confirm" button in the modal,
    # submit the form to the database and close the modal.
    try:
        if service_clock_edit_id:
            # The user is editing an existing record
            model = db.session.get(ServiceClock, service_clock_edit_id)
            # We don't want to overwrite the GPS coordinates if the user is editing the record
            gps_lat = model.gps_lat_out
            gps_lon = model.gps_lon_out
            # time_zone_id = model.time_zone_id
        else:
            # Query the database to see if the user has already clocked in or out
            model = get_last_clock_in_record(user_id=getattr(current_user, "id", None))
            gps_lat = user_gps_position_dict_store_data.get("latitude", None)
            gps_lon = user_gps_position_dict_store_data.get("longitude", None)
            # time_zone_id = user_gps_position_dict_store_data.get("timezone_id", None)

        # We don't want to record BOTH a structure and a warehouse, so check which radio button is selected
        if service_clock_unit_warehouse_radio_value == "unit":
            # User is clocking in or out of a structure
            # Don't try to record if the user hasn't selected a structure (i.e. "No unit selected" or "0")
            structure_id = (
                service_clock_structures_select_value
                if service_clock_structures_select_value not in (0, "0", None)
                else None
            )
            warehouse_id = None
        else:
            # User is clocking in or out of a warehouse
            structure_id = None
            warehouse_id = (
                service_clock_warehouses_select_value
                if service_clock_warehouses_select_value not in (0, "0", None)
                else None
            )

        if not any((structure_id, warehouse_id)):
            error_message = "Please select a structure or warehouse"
            return return_vars(
                # Don't open the modal - just show the error message below the submit button
                service_clock_modal_is_open=False,
                service_clock_submit_msg_children=error_message,
                service_clock_submit_msg_color="danger",
                error_message=error_message,
            )

        clock_in_datetime: datetime = get_datetime_from_parts(
            date_str=service_clock_clock_in_date_value,
            time_str=service_clock_clock_in_time_value,
            time_zone_id=service_clock_clock_in_time_zone_select_value,
        )
        clock_out_datetime: datetime = get_datetime_from_parts(
            date_str=service_clock_clock_out_date_value,
            time_str=service_clock_clock_out_time_value,
            time_zone_id=service_clock_clock_out_time_zone_select_value,
        )
        # Convert time_zone-aware datetimes to UTC for the database
        clock_in_datetime_utc = clock_in_datetime.astimezone(pytz.UTC)
        clock_out_datetime_utc = clock_out_datetime.astimezone(pytz.UTC)

        if isinstance(model, ServiceClock):
            # The user has already clocked in, so update the existing record with the clock-out time info
            model.timestamp_utc_out = clock_out_datetime_utc
            model.time_zone_out_id = service_clock_clock_out_time_zone_select_value
            if not service_clock_edit_id:
                # Only update the GPS coordinates if the user is clocking in or out for the first time
                model.gps_lat_out = gps_lat
                model.gps_lon_out = gps_lon
        else:
            # The user has not clocked in, so create a new record with the GPS coordinates at clock-in time
            model = ServiceClock(
                user_id=getattr(current_user, "id", None),
                gps_lat_in=gps_lat,
                gps_lon_in=gps_lon,
            )
            db.session.add(model)

        # The user can still update the clock-in time if desired
        model.timestamp_utc_in = clock_in_datetime_utc
        model.time_zone_in_id = service_clock_clock_in_time_zone_select_value

        model.notes = service_clock_notes_value
        model.structure_id = structure_id
        model.warehouse_id = warehouse_id
        model.is_billable = service_clock_is_billable_checkbox_value
        model.is_travel = service_clock_is_travel_checkbox_value

        db.session.commit()
    except Exception as err:
        db.session.rollback()
        current_app.logger.exception("Error inserting service clock")
        error_message = f"Error inserting service clock data into database: \n{err}"
        return return_vars(
            # Don't open the modal - just show the error message below the submit button
            service_clock_modal_is_open=False,
            service_clock_submit_msg_children=error_message,
            service_clock_submit_msg_color="danger",
            error_message=error_message,
        )

    # Close the modal
    return return_vars(
        service_clock_modal_is_open=False,
        service_clock_submit_msg_children="Success - database updated!",
        service_clock_submit_msg_color="success",
        error_message="",
    )
