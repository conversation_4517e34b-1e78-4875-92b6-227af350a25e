# /app/dashapp/callbacks/work_order.py

import base64
import re
import urllib.parse
from datetime import date, datetime, timezone
from decimal import Decimal
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import dash_bootstrap_components as dbc
import dash_mantine_components as dmc
import numpy as np
import plotly.express as px
from anthropic import Anthropic
from dash import (
    Input,
    Output,
    State,
    callback,
    dash_table,
    dcc,
    html,
    no_update,
)
from dash.dash_table.Format import Format, Group, Scheme, Sign, Symbol
from dash.exceptions import PreventUpdate
from flask import current_app, flash, render_template_string, url_for
from flask_login import current_user, logout_user
from shared.models.models import (
    Base,
    City,
    Country,
    County,
    Customer,
    PowerUnit,
    Province,
    SalesTax,
    ZipCode,
    ZipCodeSalesTax,
)
from shared.models.models_bom import (
    InventoryMovement,
    InventoryReservation,
    ModelType,
    Part,
    Warehouse,
    WarehousePart,
)
from shared.models.models_work_order import (
    ServiceType,
    WorkOrder,
    WorkOrderPart,
    WorkOrderUploadFile,
)
from shared.utils.inventory_legacy_adapter import (
    InventoryError,
    delete_work_order_movements,
    delete_work_order_reservations,
)
from shared.utils.inventory_manager import (
    InventoryManager,
    get_work_order_state,
)
from sqlalchemy import or_, text

from app import db, get_user_role_ids, is_active, is_admin
from app.config import (
    COUNTRY_ID_CANADA,
    COUNTRY_ID_USA,
    CURRENCY_ID_CAD,
    CURRENCY_ID_USD,
    CUSTOMER_ID_IJACK_CORP,
    CUSTOMER_ID_IJACK_INC,
    DASH_URL_WORK_ORDER,
    DASH_URL_WORK_ORDER_CORP,
    PART_ID_PART_0,
    ROLE_ID_APPROVE_WORK_ORDERS,
    ROLE_ID_EDIT_EXISTING_WORK_ORDERS,
    ROLE_ID_IJACK_SALES,
    ROLE_ID_IJACK_SERVICE,
    SALES_TAX_ID_SK,
    SERVICE_TYPE_ID_REPAIR,
    TEMPLATES_FOLDER,
    USER_ID_RICHIE,
    USER_ID_TIM,
    WORK_ORDER_STATUS_ID_WARRANTY,
)
from app.dashapp.layout_utils import get_ag_grid
from app.dashapp.metrics import BOOTSTRAP_RED_500
from app.dashapp.utils import (
    dmc_notification,
    get_db_options,
    get_id_triggered,
    get_users_by_customer_id,
)
from app.databases import run_sql_query
from app.email_stuff import send_email
from app.models.models import Structure, User
from app.utils.complex import TaxRateUpdateResult, ensure_list, update_tax_rate
from app.utils.pdf_generator import generate_work_order_pdf
from app.utils.simple import utcnow_naive

SIGNATURE_HEIGHT = 300
SIGNATURE_WIDTH = 800


def generate_inventory_report(work_order_id: int):
    """
    Generate a comprehensive inventory report for a work order.

    This function provides detailed visibility into inventory operations including
    warehouse quantities, reservations, and movements for the specified work order.
    This helps users verify inventory status and troubleshoot any issues.

    Parameters:
    -----------
    work_order_id : int
        The ID of the work order to generate the report for

    Returns:
    --------
    Dash HTML component containing the formatted inventory report
    """
    try:
        # Query warehouse parts with their current quantities for this work order
        warehouse_parts = (
            db.session.query(WarehousePart, Part, Warehouse)
            .join(Part, WarehousePart.part_id == Part.id)
            .join(Warehouse, WarehousePart.warehouse_id == Warehouse.id)
            .join(WorkOrderPart, WorkOrderPart.part_id == Part.id)
            .filter(WorkOrderPart.work_order_id == work_order_id)
            .all()
        )

        # Query reservations for this work order (using reference_id and reference_type)
        reservations = (
            db.session.query(InventoryReservation, Part, Warehouse)
            .join(Part, InventoryReservation.part_id == Part.id)
            .join(Warehouse, InventoryReservation.warehouse_id == Warehouse.id)
            .filter(
                InventoryReservation.reference_id == work_order_id,
                InventoryReservation.reference_type == "work_order",
            )
            .all()
        )

        # Query movements for this work order (using reference_id and reference_type)
        movements = (
            db.session.query(InventoryMovement, Part)
            .join(Part, InventoryMovement.part_id == Part.id)
            .filter(
                InventoryMovement.reference_id == work_order_id,
                InventoryMovement.reference_type == "work_order",
            )
            .order_by(InventoryMovement.id.desc())
            .all()
        )

        # Build warehouse parts table rows
        warehouse_rows = []
        if warehouse_parts:
            for warehouse_part, part, warehouse in warehouse_parts:
                available = warehouse_part.quantity - warehouse_part.quantity_reserved
                status_style = (
                    {"color": "#28a745"} if available >= 0 else {"color": "#dc3545"}
                )
                status_text = "✓ OK" if available >= 0 else "⚠ NEGATIVE"

                warehouse_rows.append(
                    html.Tr(
                        [
                            html.Td(part.part_num or "N/A"),
                            html.Td(
                                warehouse.name or f"WH-{warehouse_part.warehouse_id}"
                            ),
                            html.Td(
                                str(warehouse_part.quantity),
                                style={"text-align": "right"},
                            ),
                            html.Td(
                                str(warehouse_part.quantity_reserved),
                                style={"text-align": "right"},
                            ),
                            html.Td(str(available), style={"text-align": "right"}),
                            html.Td(
                                status_text,
                                style={**status_style, "text-align": "center"},
                            ),
                        ]
                    )
                )
        else:
            warehouse_rows.append(
                html.Tr(
                    [
                        html.Td(
                            "No warehouse parts found for this work order",
                            colSpan=6,
                            style={"text-align": "center", "color": "#666"},
                        )
                    ]
                )
            )

        # Build reservations table rows
        reservation_rows = []
        if reservations:
            for reservation, part, warehouse in reservations:
                status_style = {}
                if reservation.status == "FULFILLED":
                    status_style = {"color": "#28a745"}
                elif reservation.status == "CONFIRMED":
                    status_style = {"color": "#ffc107"}
                elif reservation.status == "PENDING":
                    status_style = {"color": "#17a2b8"}

                # Check for inconsistency where fulfilled > reserved
                fulfilled_style = {"text-align": "right"}
                if reservation.quantity_fulfilled > reservation.quantity_reserved:
                    fulfilled_style["color"] = "#dc3545"
                    fulfilled_style["font-weight"] = "bold"

                reservation_rows.append(
                    html.Tr(
                        [
                            html.Td(part.part_num or "N/A"),
                            html.Td(warehouse.name or f"WH-{reservation.warehouse_id}"),
                            html.Td(
                                str(reservation.quantity_reserved),
                                style={"text-align": "right"},
                            ),
                            html.Td(
                                str(reservation.quantity_fulfilled),
                                style=fulfilled_style,
                            ),
                            html.Td(
                                reservation.status,
                                style={**status_style, "text-align": "center"},
                            ),
                        ]
                    )
                )
        else:
            reservation_rows.append(
                html.Tr(
                    [
                        html.Td(
                            "No reservations found for this work order",
                            colSpan=5,
                            style={"text-align": "center", "color": "#666"},
                        )
                    ]
                )
            )

        # Build movements table rows
        movement_rows = []
        if movements:
            for movement, part in movements[:10]:  # Limit to recent 10 movements
                movement_type = (
                    movement.movement_type.value
                    if hasattr(movement.movement_type, "value")
                    else str(movement.movement_type)
                )

                # Get warehouse names for from and to warehouses
                from_wh = "-"
                if movement.from_warehouse_id:
                    from_warehouse = db.session.get(
                        Warehouse, movement.from_warehouse_id
                    )
                    from_wh = (
                        from_warehouse.name
                        if from_warehouse and from_warehouse.name
                        else f"WH-{movement.from_warehouse_id}"
                    )

                to_wh = "-"
                if movement.to_warehouse_id:
                    to_warehouse = db.session.get(Warehouse, movement.to_warehouse_id)
                    to_wh = (
                        to_warehouse.name
                        if to_warehouse and to_warehouse.name
                        else f"WH-{movement.to_warehouse_id}"
                    )

                movement_rows.append(
                    html.Tr(
                        [
                            html.Td(part.part_num or "N/A"),
                            html.Td(movement_type),
                            html.Td(
                                str(movement.quantity), style={"text-align": "right"}
                            ),
                            html.Td(from_wh, style={"text-align": "center"}),
                            html.Td(to_wh, style={"text-align": "center"}),
                            html.Td(
                                movement.timestamp_utc_inserted.strftime("%m/%d %H:%M")
                                if hasattr(movement, "timestamp_utc_inserted")
                                and movement.timestamp_utc_inserted
                                else "N/A"
                            ),
                        ]
                    )
                )
        else:
            movement_rows.append(
                html.Tr(
                    [
                        html.Td(
                            "No movements found for this work order",
                            colSpan=6,
                            style={"text-align": "center", "color": "#666"},
                        )
                    ]
                )
            )

        # Create the complete report using Dash components
        report_component = dbc.Card(
            [
                dbc.CardHeader(
                    [
                        html.H5(
                            [
                                "📦 Inventory Report - Work Order ",
                                html.Span(
                                    str(work_order_id), style={"font-weight": "bold"}
                                ),
                            ],
                            className="text-primary mb-0",
                        ),
                        html.Small(
                            "Detailed view of warehouse quantities, reservations, and movements",
                            className="text-muted",
                        ),
                    ]
                ),
                dbc.CardBody(
                    [
                        # Warehouse Parts Row
                        dbc.Row(
                            [
                                dbc.Col(
                                    [
                                        html.H6(
                                            "📦 Warehouse Parts Inventory",
                                            className="text-primary",
                                        ),
                                        html.Div(
                                            [
                                                html.Table(
                                                    [
                                                        html.Thead(
                                                            [
                                                                html.Tr(
                                                                    [
                                                                        html.Th("Part"),
                                                                        html.Th(
                                                                            "Warehouse"
                                                                        ),
                                                                        html.Th(
                                                                            "On Hand",
                                                                            style={
                                                                                "text-align": "right"
                                                                            },
                                                                        ),
                                                                        html.Th(
                                                                            "Reserved",
                                                                            style={
                                                                                "text-align": "right"
                                                                            },
                                                                        ),
                                                                        html.Th(
                                                                            "Available",
                                                                            style={
                                                                                "text-align": "right"
                                                                            },
                                                                        ),
                                                                        html.Th(
                                                                            "Status",
                                                                            style={
                                                                                "text-align": "center"
                                                                            },
                                                                        ),
                                                                    ]
                                                                )
                                                            ],
                                                            className="table-dark",
                                                        ),
                                                        html.Tbody(warehouse_rows),
                                                    ],
                                                    className="table table-sm table-striped",
                                                )
                                            ],
                                            style={
                                                "max-height": "300px",
                                                "overflow-y": "auto",
                                            },
                                        ),
                                    ],
                                    md=12,  # Full width
                                ),
                            ],
                            className="mb-4",  # Add margin bottom for spacing
                        ),
                        # Reservations Row
                        dbc.Row(
                            [
                                dbc.Col(
                                    [
                                        html.H6(
                                            "📋 Inventory Reservations",
                                            className="text-success",
                                        ),
                                        html.Div(
                                            [
                                                html.Table(
                                                    [
                                                        html.Thead(
                                                            [
                                                                html.Tr(
                                                                    [
                                                                        html.Th("Part"),
                                                                        html.Th(
                                                                            "Warehouse"
                                                                        ),
                                                                        html.Th(
                                                                            "Reserved",
                                                                            style={
                                                                                "text-align": "right"
                                                                            },
                                                                        ),
                                                                        html.Th(
                                                                            "Fulfilled",
                                                                            style={
                                                                                "text-align": "right"
                                                                            },
                                                                        ),
                                                                        html.Th(
                                                                            "Status",
                                                                            style={
                                                                                "text-align": "center"
                                                                            },
                                                                        ),
                                                                    ]
                                                                )
                                                            ],
                                                            className="table-dark",
                                                        ),
                                                        html.Tbody(reservation_rows),
                                                    ],
                                                    className="table table-sm table-striped",
                                                )
                                            ],
                                            style={
                                                "max-height": "300px",
                                                "overflow-y": "auto",
                                            },
                                        ),
                                    ],
                                    md=12,  # Full width
                                ),
                            ],
                            className="mb-4",  # Add margin bottom for spacing
                        ),
                        # Movements Row
                        dbc.Row(
                            [
                                dbc.Col(
                                    [
                                        html.H6(
                                            "🚚 Inventory Movements",
                                            className="text-warning",
                                        ),
                                        html.Div(
                                            [
                                                html.Table(
                                                    [
                                                        html.Thead(
                                                            [
                                                                html.Tr(
                                                                    [
                                                                        html.Th("Part"),
                                                                        html.Th("Type"),
                                                                        html.Th(
                                                                            "Qty",
                                                                            style={
                                                                                "text-align": "right"
                                                                            },
                                                                        ),
                                                                        html.Th(
                                                                            "From",
                                                                            style={
                                                                                "text-align": "center"
                                                                            },
                                                                        ),
                                                                        html.Th(
                                                                            "To",
                                                                            style={
                                                                                "text-align": "center"
                                                                            },
                                                                        ),
                                                                        html.Th("Time"),
                                                                    ]
                                                                )
                                                            ],
                                                            className="table-dark",
                                                        ),
                                                        html.Tbody(movement_rows),
                                                    ],
                                                    className="table table-sm table-striped",
                                                )
                                            ],
                                            style={
                                                "max-height": "300px",
                                                "overflow-y": "auto",
                                            },
                                        ),
                                    ],
                                    md=12,  # Full width
                                ),
                            ],
                            className="mb-4",  # Add margin bottom for spacing
                        ),
                        # Inventory Health Guide
                        dbc.Row(
                            [
                                dbc.Col(
                                    [
                                        dbc.Alert(
                                            [
                                                html.H6(
                                                    "🔍 Inventory Health Indicators:",
                                                    className="mb-2",
                                                ),
                                                html.Ul(
                                                    [
                                                        html.Li(
                                                            [
                                                                html.Strong(
                                                                    "Available Inventory: "
                                                                ),
                                                                "Should not be negative unless warehouse allows negative stock",
                                                            ]
                                                        ),
                                                        html.Li(
                                                            [
                                                                html.Strong(
                                                                    "Reservation Status: "
                                                                ),
                                                                "PENDING (blue), CONFIRMED (yellow), FULFILLED (green)",
                                                            ]
                                                        ),
                                                        html.Li(
                                                            [
                                                                html.Strong(
                                                                    "Movement Types: "
                                                                ),
                                                                "ISSUE (parts used), RECEIPT (parts received), TRANSFER (moved between warehouses)",
                                                            ]
                                                        ),
                                                        html.Li(
                                                            [
                                                                html.Strong(
                                                                    "Data Consistency: "
                                                                ),
                                                                "Fulfilled quantities should not exceed reserved quantities",
                                                            ]
                                                        ),
                                                    ],
                                                    className="mb-0",
                                                ),
                                            ],
                                            color="info",
                                            className="mt-3",
                                        )
                                    ],
                                    md=12,
                                )
                            ]
                        ),
                    ]
                ),
            ],
            style={"background-color": "#f8f9fa", "border": "1px solid #dee2e6"},
            className="mt-4",
        )

        return report_component

    except Exception as e:
        # Return error message using Dash components if something goes wrong
        return dbc.Alert(
            [
                html.H4(
                    "❌ Error Generating Inventory Report",
                    className="text-danger mb-2",
                ),
                html.P(f"Error: {str(e)}", className="mb-0"),
            ],
            color="danger",
            className="mt-4",
        )


def decimal_num(decimals, sign=Sign.default):
    """Format a number in a Dash data table, as a number"""
    return Format(
        group=Group.yes,
        precision=decimals,
        scheme=Scheme.fixed,
        sign=sign,
        # This is the only difference from the "money" format type
        symbol=Symbol.no,
    )


def get_parts_table_columns_ag_grid(
    currency_id: int = None, query_db: bool = True
) -> List[dict]:
    """Get the columns for the parts table"""

    price_col_header: str = "Unit Price"
    cost_col_header: str = "Total"
    if str(currency_id) == str(CURRENCY_ID_USD):
        price_col_header = "Unit Cost (USD)"
        cost_col_header = "Total (USD)"
    elif str(currency_id) == str(CURRENCY_ID_CAD):
        price_col_header = "Unit Cost (CAD)"
        cost_col_header = "Total (CAD)"

    if query_db:
        part_num_options = get_db_options(
            columns=["part_num"],
            table="parts",
            values_as_strings=True,
            # Don't allow parts in the dropdown if they're flagged for deletion (i.e. not in the BoM Master anymore)
            where="where flagged_for_deletion = false",
        )
        structure_options = get_db_options(
            columns=["structure_str"], table="structures", values_as_strings=True
        )
        warehouse_options = get_db_options(
            columns=["name"], table="warehouses", values_as_strings=True
        )
        credited_to_options = get_db_options(
            columns=["first_name", "last_name"],
            table="users",
            schema="public",
            where=f"""
inner join public.user_role_rel rl on t1.id = rl.user_id
where customer_id in ({CUSTOMER_ID_IJACK_INC}, {CUSTOMER_ID_IJACK_CORP})
and rl.role_id in ({ROLE_ID_IJACK_SERVICE}, {ROLE_ID_IJACK_SALES})
""",
            values_as_strings=True,
        )
    else:
        part_num_options = []
        structure_options = []
        warehouse_options = []
        credited_to_options = []

    return [
        {
            "field": "delete",
            "headerName": "",
            "cellRenderer": "DeleteButton",
            "lockPosition": "left",
            "filter": False,
            "maxWidth": 35,
        },
        {
            "field": "part_id",
            "headerName": "Part",
            # "headerTooltip": "",
            "initialWidth": 200,
            "maxWidth": 200,
            "editable": True,
            "singleClickEdit": True,
            "cellEditorPopup": True,
            "cellEditor": {"function": "SelectDMC"},
            "cellEditorParams": {
                "options": part_num_options,
                "placeholder": "Select a part",
                "maxDropdownHeight": 280,
                "creatable": False,
                "clearable": True,
                # "shadow": "xl",
            },
            "valueFormatter": {"function": "getDisplayLabel(params)"},
        },
        {
            "field": "description",
            "headerName": "Description",
            # "headerTooltip": "",
            "editable": True,
            "singleClickEdit": True,
            "cellDataType": "text",
            "cellRenderer": "Tooltip",
            "initialWidth": 200,
            "maxWidth": 200,
        },
        {
            "field": "structure_id",
            "headerName": "Structure",
            # "headerTooltip": "",
            # "cellDataType": "text",
            # "cellRenderer": "Tooltip",
            "initialWidth": 200,
            "maxWidth": 200,
            "editable": True,
            "singleClickEdit": True,
            "cellEditorPopup": True,
            "cellEditor": {"function": "SelectDMC"},
            "cellEditorParams": {
                "options": structure_options,
                "placeholder": "Select a structure",
                "maxDropdownHeight": 280,
                "creatable": False,
                "clearable": True,
                # "shadow": "xl",
            },
            "valueFormatter": {"function": "getDisplayLabel(params)"},
        },
        {
            "field": "warehouse_id",
            "headerName": "From Warehouse",
            # "headerTooltip": "",
            # "cellDataType": "text",
            # "cellRenderer": "Tooltip",
            "initialWidth": 200,
            "maxWidth": 200,
            "editable": True,
            "singleClickEdit": True,
            "cellEditorPopup": True,
            "cellEditor": {"function": "SelectDMC"},
            "cellEditorParams": {
                "options": warehouse_options,
                "placeholder": "Select a warehouse",
                "maxDropdownHeight": 280,
                "creatable": False,
                "clearable": True,
                # "shadow": "xl",
            },
            "valueFormatter": {"function": "getDisplayLabel(params)"},
        },
        {
            "field": "warehouse_to_id",
            "headerName": "To Warehouse",
            # "headerTooltip": "",
            # "cellDataType": "text",
            # "cellRenderer": "Tooltip",
            "initialWidth": 200,
            "maxWidth": 200,
            "editable": True,
            "singleClickEdit": True,
            "cellEditorPopup": True,
            "cellEditor": {"function": "SelectDMC"},
            "cellEditorParams": {
                "options": warehouse_options,
                "placeholder": "Select a warehouse",
                "maxDropdownHeight": 280,
                "creatable": False,
                "clearable": True,
                # "shadow": "xl",
            },
            "valueFormatter": {"function": "getDisplayLabel(params)"},
        },
        {
            "field": "quantity",
            "headerName": "Quantity",
            # "headerTooltip": "",
            "editable": True,
            "singleClickEdit": True,
            "initialWidth": 200,
            "maxWidth": 200,
            "cellDataType": "number",
            "cellRenderer": "NumberFormatter2",
            # "valueFormatter": {"function": "d3.format(',.2f')(params.value)"},
        },
        {
            "field": "price",
            "headerName": price_col_header,
            # "headerTooltip": "",
            "editable": True,
            "singleClickEdit": True,
            "initialWidth": 200,
            "maxWidth": 200,
            "cellDataType": "number",
            "cellRenderer": "CurrencyFormatter",
        },
        {
            "field": "cost_before_tax",
            "headerName": cost_col_header,
            "editable": False,
            # "headerTooltip": "",
            "initialWidth": 200,
            "maxWidth": 200,
            "cellDataType": "number",
            "cellRenderer": "CurrencyFormatter",
        },
        {
            "field": "field_tech_id",
            "headerName": "Credited To",
            "headerTooltip": "The IJACK field technician or salesperson who deserves credit for the sale",
            "editable": True,
            "singleClickEdit": True,
            "cellEditorPopup": True,
            "cellEditor": {"function": "SelectDMC"},
            "cellEditorParams": {
                "options": credited_to_options,
                "placeholder": "Select a sales rep",
                "maxDropdownHeight": 280,
                "creatable": False,
                "clearable": True,
                # "shadow": "xl",
            },
            "valueFormatter": {"function": "getDisplayLabel(params)"},
        },
    ]


def modify_columns_for_currency(
    columns: List[dict], currency_id: int = None
) -> List[dict]:
    """Modify the columns for the parts table if the currency is changed"""

    price_col_header: str = "Unit Price"
    cost_col_header: str = "Total"
    if str(currency_id) == str(CURRENCY_ID_USD):
        price_col_header = "Unit Cost (USD)"
        cost_col_header = "Total (USD)"
    elif str(currency_id) == str(CURRENCY_ID_CAD):
        price_col_header = "Unit Cost (CAD)"
        cost_col_header = "Total (CAD)"

    for column in columns:
        if column["field"] == "price":
            column["headerName"] = price_col_header
        elif column["field"] == "cost_before_tax":
            column["headerName"] = cost_col_header

    return columns


def header_and_dropdowns_row(company_id: int = None, is_customer: bool = False):
    """Title row"""
    company_name: str = "IJACK Inc 🍁"
    if company_id == CUSTOMER_ID_IJACK_CORP:
        company_name = "IJACK Corp - USA"

    return dbc.Row(
        dbc.Col(
            [
                html.H1(company_name, className="text-center"),
                html.Ul(
                    id="work_order_links",
                    # Only IJACK employees can see these links
                    style={"display": "none"} if is_customer else {"display": "block"},
                    children=[
                        html.Li(
                            html.A(
                                "View All Work Orders - IJACK Inc 🍁",
                                id="work_order_link_to_work_orders",
                                # Populate the URL in a callback with url_for()
                                href=None,
                                # white, not blue for link
                                # style={"color": "#fff"},
                            ),
                        ),
                        html.Li(
                            html.A(
                                "View All Work Order Quotes - IJACK Inc 🍁",
                                id="work_order_link_to_work_order_quotes",
                                # Populate the URL in a callback with url_for()
                                href=None,
                                # white, not blue for link
                                # style={"color": "#fff"},
                            ),
                        ),
                        html.Li(
                            html.A(
                                "View All Work Orders - IJACK Corp - USA",
                                id="work_order_link_to_work_orders_corp",
                                # Populate the URL in a callback with url_for()
                                href=None,
                                # white, not blue for link
                                # style={"color": "#fff"},
                            ),
                        ),
                        html.Li(
                            html.A(
                                "View All Work Order Quotes - IJACK Corp - USA",
                                id="work_order_link_to_work_order_quotes_corp",
                                # Populate the URL in a callback with url_for()
                                href=None,
                                # white, not blue for link
                                # style={"color": "#fff"},
                            ),
                        ),
                    ],
                ),
                dbc.Collapse(
                    id="work_order_msg_collapse",
                    is_open=False,
                    # Make it stand out a bit more
                    style={
                        # Glowing shadow in IJACK-green
                        "box-shadow": "0 0 10px #C1D72E",
                        # Match the outline of the Bootstrap card border
                        "border-radius": "0.25rem",
                    },
                ),
            ]
        )
    )


def currency_row(company_id: int = None):
    """Currency row"""

    default_currency_id: int = CURRENCY_ID_CAD
    default_country_id: str = COUNTRY_ID_CANADA
    if company_id == CUSTOMER_ID_IJACK_CORP:
        default_currency_id = CURRENCY_ID_USD
        default_country_id = COUNTRY_ID_USA

    return dbc.Row(
        [
            dbc.Col(
                md=6,
                class_name="mt-3",
                # style={"display": "none"},
                children=[
                    dbc.Label("Country"),
                    dcc.Dropdown(
                        id="work_order_country_id",
                        value=default_country_id,
                        persistence=False,
                    ),
                    dbc.FormText(
                        "Country for tax purposes (Canada or USA, typically). This will determine the sales tax rate."
                    ),
                ],
            ),
            dbc.Col(
                md=6,
                class_name="mt-3",
                # style={"display": "none"},
                children=[
                    dbc.Label("Currency"),
                    dcc.Dropdown(
                        id="work_order_currency_id",
                        value=default_currency_id,
                        persistence=False,
                        options=[
                            {
                                "label": "CAD (Canadian Dollars)",
                                "value": CURRENCY_ID_CAD,
                            },
                            {
                                "label": "USD (US Dollars)",
                                "value": CURRENCY_ID_USD,
                            },
                        ],
                    ),
                    dbc.FormText(
                        "Currency of the work order (sometimes INC invoices CORP in USD, if INC wants USD)"
                    ),
                ],
            ),
        ]
    )


def company_customer_row():
    """Work order form row"""
    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Label("Customer"),
                    dcc.Dropdown(
                        id="work_order_customer",
                        # value=CUSTOMER_ID_TEINE,
                        # NOTE: If we persist the customer, it causes a bug where the SECOND time a person creates a work order,
                        # the customer "value" does not trigger the callback to update the
                        # "requested by" and "approved by" dropdowns options, and enable them.
                        persistence=False,
                        # persistence_type="session",
                    ),
                    dbc.FormText("Customer for which service was done"),
                ],
                md=6,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Warranty Service?"),
                    dbc.Switch(
                        id="work_order_is_warranty",
                        value=False,
                        persistence=False,
                        # persistence_type="session",
                    ),
                    dbc.FormText("Is this covered by warranty?"),
                ],
                md=6,
                class_name="mt-3",
            ),
        ],
    )


def service_date_and_type_row():
    """Work order form row"""

    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Label("Service Date"),
                    dmc.DatePickerInput(
                        id="work_order_date_service",
                        value=date.today(),
                        minDate=date(2000, 1, 1),
                        persistence=False,
                        # persistence_type="session",
                        # dropdownType="modal", # default "popover"
                        # So the datepicker is in front of the Bootstrap modal
                        popoverProps={"zIndex": 10_000},
                        # modalProps={"zIndex": 10_000},
                        # dropdownPosition="bottom-start" fixes bug with Mantine DatePickerInput causing app to lock up!
                        # https://community.plotly.com/t/dash-mantine-datepicker/75251/4
                        # dropdownPosition="bottom-start",
                    ),
                    dbc.FormText("Select a date"),
                ],
                md=4,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Service Type"),
                    dcc.Dropdown(
                        id="work_order_service_type",
                        value=SERVICE_TYPE_ID_REPAIR,
                        persistence=False,
                        # persistence_type="session",
                    ),
                    dbc.FormText("Type of service"),
                ],
                md=4,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Location"),
                    dbc.Input(
                        id="work_order_location",
                        # required=True,
                        type="text",
                        persistence=False,
                        # persistence_type="session",
                    ),
                    dbc.FormText("Location of service"),
                ],
                md=4,
                class_name="mt-3",
            ),
        ],
    )


def requested_and_approved_by_row():
    """Work order form row"""

    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Label("Quote/Work Order Requested By"),
                    dcc.Dropdown(
                        id="work_order_requested_by_dropdown",
                        placeholder="Choose a customer, then select a user...",
                        disabled=True,
                    ),
                    dbc.FormText("Who at this company requested this quote?"),
                ],
                md=6,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Company Approval Person"),
                    dcc.Dropdown(
                        id="work_order_company_rep_dropdown",
                        placeholder="Choose a customer, then select a user...",
                        disabled=True,
                    ),
                    dbc.FormText(
                        "Who at this company will be responsible for approving this quote?"
                    ),
                ],
                md=6,
                class_name="mt-3",
            ),
        ],
    )


def field_techs_row():
    """Work order form row"""

    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Label("IJACK Field Techs"),
                    dcc.Dropdown(
                        id="work_order_users",
                        # We'll set the default value to the current user in the callback
                        persistence=False,
                        # persistence_type="session",
                        # Multi-select
                        multi=True,
                    ),
                    html.Div(
                        id="work_order_users_valid",
                        style={"color": "red"},
                        className="my-1",
                    ),
                    dbc.FormText("Sometimes multiple IJACK installers are involved."),
                ],
                md=4,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("IJACK Sales Credit"),
                    dcc.Dropdown(
                        id="work_order_users_sales",
                        # We'll set the default value to the current user in the callback
                        persistence=False,
                        # persistence_type="session",
                        # Multi-select
                        multi=True,
                    ),
                    html.Div(
                        id="work_order_users_sales_valid",
                        style={"color": "red"},
                        className="my-1",
                    ),
                    dbc.FormText("Who gets credit for the sales revenue?"),
                ],
                id="work_order_sales_credit_col",
                md=4,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Service Crew"),
                    dbc.Input(
                        id="work_order_service_crew",
                        # required=True,
                        type="text",
                        persistence=False,
                        # persistence_type="session",
                    ),
                    dbc.FormText(""),
                ],
                md=4,
                class_name="mt-3",
            ),
        ],
    )


def service_required_row():
    """Work order form row"""

    return html.Div(
        [
            dbc.Row(
                [
                    dbc.Col(
                        [
                            dbc.Label("IJACK-Only Notes (Internal Use)"),
                            dbc.Textarea(
                                id="work_order_service_required",
                                # required=True,
                                # type="text",
                                persistence=False,
                                # persistence_type="session",
                                style={"height": "200px"},  # Increased initial height
                            ),
                        ],
                        id="work_order_service_required_col",
                        md=6,
                        class_name="mt-3",
                    ),
                    dbc.Col(
                        [
                            dbc.Label("Invoice Description"),
                            dbc.Button(
                                "Improve with AI",
                                size="sm",
                                color="secondary",
                                class_name="ms-2 mb-1",
                                id="work_order_fix_description_btn",
                                n_clicks=0,
                            ),
                            dbc.Textarea(
                                id="work_order_work_done",
                                persistence=False,
                                # persistence_type="session",
                                style={"height": "200px"},  # Increased initial height
                            ),
                        ],
                        md=6,
                        class_name="mt-3",
                    ),
                ],
            ),
            # Row that's initially hidden, but shown when the "Improve with AI" button is clicked
            dbc.Spinner(
                color="success",
                children=dbc.Collapse(
                    is_open=False,
                    id="work_order_work_done_ai_collapse",
                    children=dbc.Row(
                        children=[
                            dbc.Col(md=6),
                            dbc.Col(
                                [
                                    dbc.Label("AI Description of Work Performed"),
                                    dbc.Button(
                                        "Use Improved Description",
                                        size="sm",
                                        color="secondary",
                                        class_name="ms-2 mb-1",
                                        id="work_order_use_ai_description_btn",
                                        n_clicks=0,
                                    ),
                                    dbc.Textarea(
                                        id="work_order_work_done_ai",
                                        persistence=False,
                                        # persistence_type="session",
                                        style={
                                            "height": "600px"
                                        },  # Increased initial height
                                    ),
                                ],
                                md=6,
                                class_name="mt-3",
                            ),
                        ],
                    ),
                ),
            ),
        ]
    )


def work_order_file_upload_row() -> dbc.Row:
    """Work order picture upload row"""
    return dbc.Row(
        [
            dbc.Col(
                md=12,
                class_name="mt-3",
                children=[
                    dbc.Label("File or Picture Upload"),
                    dcc.Upload(
                        id="work_order_file_upload",
                        children=html.Div(
                            [
                                "Drag and Drop or ",
                                html.A("Select Files"),
                            ],
                            style={
                                "width": "100%",
                                "height": "80px",
                                "lineHeight": "80px",
                                "borderWidth": "1px",
                                "borderStyle": "dashed",
                                "borderRadius": "5px",
                                "textAlign": "center",
                                # "margin": "10px",
                            },
                        ),
                        # Allow multiple files to be uploaded
                        multiple=True,
                    ),
                    html.Div(
                        id="work_order_file_upload_data_div",
                        style={"display": "none"},
                        className="mt-3",
                        children=dash_table.DataTable(
                            id="work_order_file_upload_data_table",
                            columns=[
                                {"name": "File Name", "id": "file_name"},
                                {"name": "Content Type", "id": "file_type"},
                                {"name": "Contents", "id": "file_bytes"},
                            ],
                            hidden_columns=["file_bytes"],
                            # Don't show the "show-hide" columns button above the table
                            css=[{"selector": ".show-hide", "rule": "display: none"}],
                            row_deletable=True,
                            style_table={
                                # "border": "none",
                                "fontFamily": "Open Sans, sans-serif",
                                "fontSize": "1rem",
                                "width": "100%",
                                "overflowX": "auto",  # 'auto' or 'scroll' (both seem to work, and not work at times...)
                            },
                            style_data={
                                # "border": "none",
                                "whiteSpace": "normal",
                                "height": "auto",
                                "fontFamily": "Open Sans, sans-serif",
                                "fontSize": "1rem",
                                "lineHeight": "1.5rem",
                            },
                            style_header={
                                # "border": "none",
                                "backgroundColor": "white",
                                "fontFamily": "Open Sans, sans-serif",
                                "fontSize": "1rem",
                                # "fontWeight": "bold",
                                # Wrap text (whiteSpace: "normal")
                                "whiteSpace": "normal",
                                "textAlign": "left",
                                "lineHeight": "1.5rem",
                            },
                            style_cell={
                                "fontSize": "1rem",
                                "textAlign": "left",
                                # Padding-left is important actually, both between columns and on the left
                                "padding": "0.5rem",
                                "color": "rgb(33, 37, 41)",
                            },
                            style_data_conditional=[
                                {
                                    "if": {"row_index": "even"},
                                    "backgroundColor": "rgba(0, 0, 0, 0.05)",
                                }
                            ],
                        ),
                    ),
                ],
            ),
        ],
    )


def customer_row():
    """Work order form row"""

    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Label("Customer Work Order"),
                    dbc.Input(
                        id="work_order_cust_work_order",
                        # required=True,
                        type="text",
                        persistence=False,
                        # persistence_type="session",
                    ),
                    dbc.FormText(""),
                ],
                md=4,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Authorization for expenditure (AFE)"),
                    dbc.Input(
                        id="work_order_afe",
                        # required=True,
                        type="text",
                        persistence=False,
                        # persistence_type="session",
                    ),
                    dbc.FormText(""),
                ],
                md=4,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Customer PO"),
                    dbc.Input(
                        id="work_order_customer_po",
                        # required=True,
                        type="text",
                        persistence=False,
                        # persistence_type="session",
                    ),
                    dbc.FormText(""),
                ],
                md=4,
                class_name="mt-3",
            ),
        ],
    )


def signature_row():
    """Work order form row with signature pad"""
    # Create a blank white image for the signature background
    # Flexible height and width
    height, width = SIGNATURE_HEIGHT, SIGNATURE_WIDTH
    blank_img = np.ones((height, width, 3), dtype=np.uint8) * 255

    # Create a figure with the blank white background
    fig = px.imshow(blank_img)

    # Configure the figure for signature drawing
    fig.update_layout(
        margin=dict(l=0, r=0, t=0, b=0),  # Remove margins
        height=height,  # Set a fixed height
        # width=width,  # Set a fixed width
        autosize=True,  # Enable autosize for responsive width
        dragmode="drawopenpath",  # Start in drawing mode by default
        # Remove axes and other elements for a clean signature pad
        xaxis=dict(
            showgrid=False,
            zeroline=False,
            showticklabels=False,
            visible=False,
            fixedrange=True,  # Prevent zoom on x-axis
            range=[0, width],
        ),
        yaxis=dict(
            showgrid=False,
            zeroline=False,
            showticklabels=False,
            visible=False,
            fixedrange=True,  # Prevent zoom on y-axis
            range=[height, 0],  # Flip y-axis for natural drawing coordinates
        ),
        plot_bgcolor="white",
    )

    # Configure drawing tools - only show necessary ones for signatures
    config = {
        # "modeBarButtonsToAdd": [
        #     "drawopenpath",  # For drawing the signature
        #     # "eraseshape",  # For correcting mistakes
        # ],
        "modeBarButtonsToRemove": [
            "zoom",
            "pan",
            "select",
            "zoomIn",
            "zoomOut",
            "autoScale",
            "resetScale",
            "toImage",
            "lasso2d",
            "eraseshape",
            "drawopenpath",
        ],
        "displayModeBar": False,  # Hide the mode bar for cleaner appearance
        "displaylogo": False,
        "responsive": True,  # Enable responsive resizing
    }

    return dbc.Row(
        class_name="mt-3",
        children=dbc.Col(
            class_name="mx-0 ms-auto",  # Right alignment
            style={
                "height": "fit-content",
                "display": "block",
            },
            children=[
                # Hidden component to store the signature data in base64 format
                dcc.Store(id="signature_store"),
                dbc.Label(
                    "Signature",
                    class_name="mb-1 text-end w-100",
                    html_for="signature_graph",
                ),
                # Display this editing area if there is no existing signature image in the database
                dbc.Row(
                    class_name="g-0 justify-content-end",
                    children=dbc.Col(
                        id="signature_graph_container",
                        width="auto",
                        children=dcc.Graph(
                            id="signature_graph",
                            figure=fig,
                            config=config,
                            # Small border on the graph so the user knows where to scribble
                            style={
                                "width": "100%",
                                "height": "100%",
                                "border": "1px solid #ccc",
                            },
                        ),
                    ),
                ),
                # Display this if there's an existing signature image in the database
                dbc.Row(
                    class_name="g-0 justify-content-end",
                    children=dbc.Col(
                        html.Img(
                            id="signature_image",
                            style={"display": "none"},
                            className="img-fluid ms-auto",  # img-fluid for responsive image, ms-auto for right alignment
                        ),
                        class_name="d-flex justify-content-end",  # Right-align content
                        width="auto",
                    ),
                ),
                dbc.Row(
                    class_name="g-0 justify-content-end",
                    children=[
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fa-solid fa-pen me-1"),
                                    "Edit",
                                ],
                                id="edit_signature_btn",
                                color="secondary",
                                class_name="mt-2",
                                size="sm",
                            ),
                            width="auto",
                        ),
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fa-solid fa-eraser me-1"),
                                    "Clear",
                                ],
                                id="clear_signature_btn",
                                # style={"display": "none"},
                                color="secondary",
                                class_name="ms-2 mt-2",
                                size="sm",
                            ),
                            width="auto",
                        ),
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fa-solid fa-floppy-disk me-1"),
                                    "Save",
                                ],
                                id="save_signature_btn",
                                style={"display": "none"},
                                color="primary",
                                class_name="ms-2 mt-2",
                                size="sm",
                            ),
                            width="auto",
                        ),
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fa-solid fa-xmark me-1"),
                                    "Cancel",
                                ],
                                id="cancel_signature_btn",
                                style={"display": "none"},
                                color="danger",
                                class_name="ms-2 mt-2",
                                size="sm",
                            ),
                            width="auto",
                        ),
                    ],
                ),
                # Name of person who signed the work order
                dbc.Row(
                    class_name="justify-content-end mt-1",
                    children=dbc.Col(
                        dbc.Input(
                            id="work_order_signature_name",
                            placeholder="Full name",
                            type="text",
                            persistence=False,
                            # persistence_type="session",
                        ),
                        xs=12,
                        md=10,
                        lg=6,
                    ),
                ),
                # Automatic date of signature
                dbc.Row(
                    class_name="justify-content-end mt-1",
                    children=dbc.Col(
                        dcc.DatePickerSingle(
                            id="work_order_signature_date",
                            max_date_allowed=date.today(),
                            initial_visible_month=date.today(),
                            date=date.today(),
                            style={"display": "inline-block"},
                        ),
                        xs=12,
                        md=10,
                        lg=6,
                    ),
                ),
            ],
        ),
    )


@callback(
    Output("work_order_signature_date", "date", allow_duplicate=True),
    Input("work_order_signature_name", "value"),
    prevent_initial_call=True,
)
def update_signature_date(signature_name: str) -> str:
    """
    Update the signature date to today if the signature name is provided.
    This ensures that the signature date is always set to today when a signature is created.
    """
    if signature_name:
        return date.today()
    raise PreventUpdate


@callback(
    Output("signature_graph_container", "style"),
    Output("edit_signature_btn", "style"),
    Output("clear_signature_btn", "style"),
    Output("save_signature_btn", "style"),
    Output("cancel_signature_btn", "style"),
    Output("signature_image", "style"),
    Output("signature_image", "src"),
    Input("signature_store", "data"),
    Input("edit_signature_btn", "n_clicks"),
    Input("cancel_signature_btn", "n_clicks"),
    prevent_initial_call=True,
)
def update_existing_signature(
    signature_store_data,
    edit_signature_btn_n_clicks,
    cancel_signature_btn_n_clicks,
):
    """
    Update the signature image based on the default value or the signature data.
    """

    def return_vars(
        signature_graph_container_style: dict,
        edit_signature_btn_style: dict,
        clear_signature_btn_style: dict,
        save_signature_btn_style: dict,
        cancel_signature_btn_style: dict,
        signature_image_style: dict,
        signature_image_src: str,
    ):
        """Default values"""
        return (
            signature_graph_container_style,
            edit_signature_btn_style,
            clear_signature_btn_style,
            save_signature_btn_style,
            cancel_signature_btn_style,
            signature_image_style,
            signature_image_src,
        )

    id_triggered: str = get_id_triggered()
    if (
        id_triggered in ("signature_store.data", "cancel_signature_btn.n_clicks")
        and signature_store_data
    ):
        # If there's a signature in the store, show the image
        return return_vars(
            signature_graph_container_style={"display": "none"},
            edit_signature_btn_style={"display": "block"},
            clear_signature_btn_style={"display": "block"},
            save_signature_btn_style={"display": "none"},
            cancel_signature_btn_style={"display": "none"},
            signature_image_style={"display": "block"},
            signature_image_src=signature_store_data,
        )

    elif (
        id_triggered == "clear_signature_btn.n_clicks" and cancel_signature_btn_n_clicks
    ):
        # If the clear button is clicked, show the image with no signature
        return return_vars(
            signature_graph_container_style={"display": "none"},
            edit_signature_btn_style={"display": "block"},
            clear_signature_btn_style={"display": "block"},
            save_signature_btn_style={"display": "none"},
            cancel_signature_btn_style={"display": "none"},
            # Show the signature image
            signature_image_style={"display": "block"},
            signature_image_src="",
        )

    elif id_triggered == "edit_signature_btn.n_clicks" and edit_signature_btn_n_clicks:
        # If the edit button is clicked, show the drawing area and the save button
        return return_vars(
            signature_graph_container_style={"display": "block"},
            edit_signature_btn_style={"display": "none"},
            clear_signature_btn_style={"display": "none"},
            save_signature_btn_style={"display": "block"},
            cancel_signature_btn_style={"display": "block"},
            # Hide the signature image
            signature_image_style={"display": "none"},
            signature_image_src=no_update,
        )

    return return_vars(
        signature_graph_container_style={"display": "block"},
        edit_signature_btn_style={"display": "none"},
        clear_signature_btn_style={"display": "none"},
        save_signature_btn_style={"display": "block"},
        cancel_signature_btn_style={"display": "block"},
        # Hide the signature image
        signature_image_style={"display": "none"},
        signature_image_src=no_update,
    )


@callback(
    Output("signature_graph", "figure"),
    Input("edit_signature_btn", "n_clicks"),
    Input("clear_signature_btn", "n_clicks"),
    Input("cancel_signature_btn", "n_clicks"),
    prevent_initial_call=True,
)
def edit_signature(
    edit_signature_btn_n_clicks,
    clear_signature_btn_n_clicks,
    cancel_signature_btn_n_clicks,
):
    """If the user clicks either the edit or clear button, clear the drawing area"""
    if (
        not edit_signature_btn_n_clicks
        and not clear_signature_btn_n_clicks
        and not cancel_signature_btn_n_clicks
    ):
        # If none of the buttons are clicked, do nothing
        raise PreventUpdate

    # Create a new blank figure
    height, width = SIGNATURE_HEIGHT, SIGNATURE_WIDTH
    blank_img = np.ones((height, width, 3), dtype=np.uint8) * 255
    fig = px.imshow(blank_img)

    fig.update_layout(
        margin=dict(l=0, r=0, t=0, b=0),
        height=SIGNATURE_HEIGHT,
        width=800,
        dragmode="drawopenpath",
        xaxis=dict(
            showgrid=False,
            zeroline=False,
            showticklabels=False,
            visible=False,
            fixedrange=True,
            range=[0, width],
        ),
        yaxis=dict(
            showgrid=False,
            zeroline=False,
            showticklabels=False,
            visible=False,
            fixedrange=True,
            range=[height, 0],
        ),
        plot_bgcolor="white",
    )
    return fig


def plotly_shapes_to_svg(shapes, width=SIGNATURE_WIDTH, height=SIGNATURE_HEIGHT) -> str:
    """
    Convert Plotly shapes to an SVG string.
    """
    # Start the SVG
    svg = f'<svg xmlns="http://www.w3.org/2000/svg" width="{width}" height="{height}">'

    # Add a white background rectangle
    svg = f'{svg}<rect width="{width}" height="{height}" fill="white"/>'

    # Process each shape
    for shape in shapes:
        if shape.get("type") == "path":
            path_str = shape.get("path", "")
            stroke_color = shape.get("line", {}).get("color", "black")
            stroke_width = shape.get("line", {}).get("width", 2)

            # Clean and convert the path data
            # Plotly paths use a different format than SVG paths
            svg_path = convert_plotly_path_to_svg(path_str)

            if svg_path:
                svg += f'<path d="{svg_path}" stroke="{stroke_color}" stroke-width="{stroke_width}" fill="none"/>'

    # Close the SVG
    svg = f"{svg}</svg>"

    return svg


def convert_plotly_path_to_svg(plotly_path: str) -> str:
    """
    Convert a Plotly path string to SVG path format.
    Plotly uses a different path format and coordinate system.
    """
    if not plotly_path:
        return ""

    # # Extract points from Plotly path format
    # # Plotly paths often look like "M x,y L x,y L x,y..."
    # points = []
    # print(points)

    # Simple regex to extract points
    matches = re.findall(r"([ML]) ?([\d\.]+),?([\d\.]+)", plotly_path)

    svg_path = ""

    for cmd, x, y in matches:
        # Convert coordinates (flip y-axis if needed)
        x_val = float(x)
        y_val = float(y)

        # Append to SVG path
        if cmd == "M":
            svg_path += f"M{x_val},{y_val} "
        elif cmd == "L":
            svg_path += f"L{x_val},{y_val} "

    return svg_path


def svg_to_base64(svg_content) -> str:
    """
    Convert an SVG string to a base64-encoded data URL.
    """
    # Encode the SVG content to bytes
    svg_bytes = svg_content.encode("utf-8")

    # Encode to base64
    base64_svg = base64.b64encode(svg_bytes).decode("utf-8")

    # Create the complete data URL
    data_url = f"data:image/svg+xml;base64,{base64_svg}"

    return data_url


@callback(
    Output("signature_store", "data", allow_duplicate=True),
    Input("save_signature_btn", "n_clicks"),
    Input("clear_signature_btn", "n_clicks"),
    State("signature_graph", "relayoutData"),
    prevent_initial_call=True,
)
def save_signature(
    save_signature_btn_n_clicks,
    clear_signature_btn_n_clicks,
    signature_graph_relayout_data,
) -> str:
    """Save the signature to the database."""
    signature_to_be_saved: bool = bool(
        save_signature_btn_n_clicks and signature_graph_relayout_data
    )
    if not signature_to_be_saved and not clear_signature_btn_n_clicks:
        # If the save button is not clicked, or if there's no relayout data, do nothing
        raise PreventUpdate

    id_triggered: str = get_id_triggered()
    if id_triggered == "clear_signature_btn.n_clicks":
        # If the clear button is clicked, clear the signature store
        return ""

    # Parse the signature data
    shapes = signature_graph_relayout_data.get("shapes", [])
    if not shapes:
        raise PreventUpdate

    # Convert the shapes to SVG
    svg_content: str = plotly_shapes_to_svg(
        shapes, width=SIGNATURE_WIDTH, height=SIGNATURE_HEIGHT
    )

    # Convert SVG to base64
    base64_svg: str = svg_to_base64(svg_content)

    return base64_svg


def invoice_summary_autofill_row():
    """Work order form row"""

    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Label("Invoice Summary"),
                    dbc.Button(
                        "Auto-fill",
                        size="sm",
                        color="secondary",
                        class_name="ms-2 mb-1",
                        id="work_order_invoice_summary_autofill",
                        n_clicks=0,
                    ),
                    dbc.Textarea(
                        id="work_order_invoice_summary",
                        persistence=False,
                        # persistence_type="session",
                        style={"height": "200px"},
                    ),
                    dbc.FormText(""),
                ],
                md=12,
                class_name="mt-3",
            ),
        ],
    )


def equipment_serviced_row():
    """Work order form row"""

    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Label("Equipment Serviced (Models)"),
                    dcc.Dropdown(
                        id="work_order_model_types",
                        persistence=False,
                        # persistence_type="session",
                        multi=True,
                    ),
                    dbc.FormText(
                        "Sometimes there's more than one model being serviced"
                    ),
                ],
                md=6,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Power Units Involved"),
                    dcc.Dropdown(
                        id="work_order_power_units",
                        persistence=False,
                        # persistence_type="session",
                        multi=True,
                    ),
                    dbc.FormText(
                        "Sometimes multiple power units are involved in a work order"
                    ),
                ],
                md=6,
                class_name="mt-3",
            ),
        ],
    )


def structures_involved_row():
    """Work order form row"""

    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Label("Structures Involved"),
                    # dbc.Select(
                    dcc.Dropdown(
                        id="work_order_structures",
                        persistence=False,
                        # persistence_type="session",
                        multi=True,
                    ),
                    dbc.FormText("Sometimes there is more than one structure involved"),
                ],
                md=6,
                class_name="mt-3",
            ),
            dbc.Col(
                [
                    dbc.Label("Structure Slave Number"),
                    dbc.Input(
                        id="work_order_structure_slave",
                        required=False,
                        persistence=False,
                        # persistence_type="session",
                        type="number",
                    ),
                    # dbc.Select(
                    #     id="work_order_structure_slave",
                    #     required=False,
                    #     persistence=False,
                    # persistence_type="session",
                    # ),
                    dbc.FormText("This must be numeric"),
                ],
                md=6,
                class_name="mt-3",
            ),
        ],
    )


def work_order_parts_rows(has_app_context: bool = False, company_id: int = None):
    """Work order parts table"""

    if company_id == CUSTOMER_ID_IJACK_CORP:
        default_currency_id: int = CURRENCY_ID_USD
    elif company_id == CUSTOMER_ID_IJACK_INC:
        default_currency_id: int = CURRENCY_ID_CAD
    else:
        default_currency_id: int = CURRENCY_ID_CAD

    columns_ag_grid = get_parts_table_columns_ag_grid(
        default_currency_id, query_db=has_app_context
    )

    # The delete button doesn't have a field so use c.get("field", "")
    blank_row_ag = {c.get("field", ""): "" for c in columns_ag_grid}
    # blank_row_ag["quantity"] = ""
    data_ag = [blank_row_ag for _ in range(5)]

    return dbc.Row(
        [
            dbc.Col(
                [
                    dbc.Row(
                        dbc.Col(
                            [
                                dbc.Button(
                                    "Add Part",
                                    id="work_order_add_rows_btn",
                                    n_clicks=0,
                                    color="secondary",
                                    size="sm",
                                    class_name="mb-1 me-1",
                                ),
                                dbc.Button(
                                    "Parts Lookup",
                                    id="work_order_parts_lookup_btn",
                                    n_clicks=0,
                                    color="secondary",
                                    size="sm",
                                    class_name="mb-1",
                                ),
                                # dbc.Button(
                                #     "Get table data (manually, in case it's missing)",
                                #     id="work_order_get_table_data_btn",
                                #     n_clicks=0,
                                #     color="primary",
                                #     size="sm",
                                #     class_name="mb-1 ms-1",
                                # ),
                            ]
                        )
                    ),
                    dbc.Row(
                        dbc.Col(
                            # Make the following inline so the buttons are on the same line
                            dbc.RadioItems(
                                options=[
                                    {"label": "MSRP List Price", "value": "msrp"},
                                    {"label": "Dealer Cost", "value": "dealer"},
                                    {
                                        "label": "IJACK INC to CORP Transfer Cost",
                                        "value": "transfer",
                                    },
                                ],
                                value="msrp",
                                id="work_order_parts_dealer_cost_switch",
                                inline=True,
                                switch=True,
                                class_name="ms-2 mb-1",
                            ),
                        )
                    ),
                    # dbc.Row(
                    #     dbc.Col(
                    #         dbc.Switch(
                    #             id="work_order_transfer_to_warehouse_switch",
                    #             value=False,
                    #             persistence=False,
                    #             label="Transfer to Warehouse",
                    #             class_name="ms-2",
                    #         ),
                    #     )
                    # ),
                    # dbc.Collapse(
                    #     id="work_order_transfer_to_warehouse_collapse",
                    #     is_open=False,
                    #     children=[
                    #         dbc.Label("Warehouse where parts are going"),
                    #         dcc.Dropdown(
                    #             id="work_order_transfer_to_warehouse_dropdown",
                    #             persistence=False,
                    #             # persistence_type="session",
                    #         ),
                    #     ],
                    #     class_name="my-1 mb-3",
                    # ),
                    # dcc.Store("work_order_parts_table_ag_store", data=data_ag),
                    html.Div(
                        className="mb-3",
                        children=get_ag_grid(
                            id="work_order_parts_table_ag",
                            # getRowId="params.data.id",
                            columnDefs=columns_ag_grid,
                            columnSize="sizeToFit",
                            column_filter=False,
                            rowData=data_ag,
                            pagination=False,
                        ),
                    ),
                ]
            )
        ],
        class_name="mt-4",
    )


def get_store_items(company_id: int = None) -> html.Div:
    """Get the dcc.store items for the layout"""
    default_creator_company_id: int = (
        CUSTOMER_ID_IJACK_CORP
        if company_id == CUSTOMER_ID_IJACK_CORP
        else CUSTOMER_ID_IJACK_INC
    )
    return html.Div(
        [
            # To store the work order ID when we're editing an existing work order
            dcc.Store(storage_type="memory", id="edit_work_order_id"),
            # track whether data has been loaded yet, from an existing work order
            dcc.Store(id="work_order_data_initialized", data=False),
            # For storing who created the work order, in case someone edits one of Tim Beals'
            # work orders, and he's got a special description in the parts table.
            dcc.Store(storage_type="memory", id="work_order_creator_id"),
            # For storing the company ID of the creator
            dcc.Store(
                storage_type="memory",
                id="work_order_creator_company_id",
                data=default_creator_company_id,
            ),
            # dcc.Store(storage_type="memory", id="work_order_country_id"),
            # # If we're editing a work order and we change the warehouse quantities,
            # # we need to know which ones to update
            # dcc.Store(
            #     storage_type="memory",
            #     id="prev_work_order_quantities_to_update",
            #     data={},
            # ),
            dcc.Store(
                storage_type="memory",
                id="work_order_customer_users_store",
                data={},
            ),
            # # Track form dirty state for preventing unsaved changes before finalization
            # dcc.Store(
            #     storage_type="memory",
            #     id="work_order_form_dirty_store",
            #     data=False,
            # ),
            # # Toast notification container for finalize warnings
            # html.Div(id="work_order_finalize_warning_toast"),
        ]
    )


def parts_lookup_modal():
    """Modal for looking up parts and descriptions"""

    return dbc.Modal(
        id="parts_lookup_modal",
        centered=True,
        is_open=False,
        scrollable=True,
        size="xl",
        children=[
            dbc.ModalHeader(dbc.ModalTitle("Parts Lookup")),
            dbc.ModalBody(
                [
                    dbc.Row(
                        [
                            dbc.Col(
                                dbc.Input(
                                    type="text",
                                    placeholder="Search",
                                    id="parts_lookup_modal_search_input",
                                    # The Input("value") prop will fire on "enter" or lost focus
                                    debounce=True,
                                ),
                                className="me-3",
                            ),
                            dbc.Col(
                                dbc.Button(
                                    "Search",
                                    id="parts_lookup_modal_search_btn",
                                    n_clicks=0,
                                ),
                                width="auto",
                            ),
                        ],
                        class_name="mb-2 g-0",
                    ),
                    dbc.Row(
                        dbc.Col(
                            children=[
                                dcc.Store(id="parts_list_store", storage_type="memory"),
                                # Custom CSS stylesheet to make the radio items look like Bootstrap buttons
                                html.Link(
                                    rel="stylesheet",
                                    id="work_order_stylesheet",
                                ),
                                dbc.RadioItems(
                                    id="work_order_parts_lookup_radio",
                                    # Special classes so the radio items look like Bootstrap buttons
                                    # input_checked_class_name="btn-primary",
                                    # input_checked_style={"color": "white"},
                                    input_class_name="custom-radio-button",
                                    # input_style={"color": "black"},
                                    # label_checked_class_name = "btn-primary",
                                    # label_checked_style = {"color": "white"},
                                    label_class_name="custom-radio-label",
                                    # label_style = {"color": "black"},
                                    # style={"ps": "0"},
                                    class_name="ps-0",
                                ),
                            ]
                        )
                    ),
                ],
            ),
            dbc.ModalFooter(
                dbc.Button(
                    "Close",
                    id="parts_lookup_modal_close_btn",
                    size="sm",
                    color="secondary",
                    n_clicks=0,
                ),
            ),
        ],
    )


def work_order_delete_confirmation_modal():
    """Modal for confirming work order deletion"""

    return dbc.Modal(
        id="work_order_delete_confirmation_modal",
        centered=True,
        is_open=False,
        size="md",
        children=[
            dbc.ModalHeader(
                dbc.ModalTitle(
                    [
                        html.I(
                            className="fa-solid fa-exclamation-triangle me-2 text-warning"
                        ),
                        "Delete Work Order",
                    ]
                )
            ),
            dbc.ModalBody(
                [
                    html.P(
                        "Are you sure you want to delete this work order?",
                        className="mb-3",
                    ),
                    html.P(
                        "This action cannot be undone.",
                        className="text-muted small mb-0",
                    ),
                ]
            ),
            dbc.ModalFooter(
                [
                    dbc.Button(
                        "Cancel",
                        id="work_order_delete_cancel_btn",
                        color="secondary",
                        size="sm",
                        className="me-2",
                        n_clicks=0,
                    ),
                    dbc.Button(
                        [
                            html.I(className="fa-solid fa-trash me-1"),
                            "Delete Work Order",
                        ],
                        id="work_order_delete_confirm_btn",
                        color="danger",
                        size="sm",
                        n_clicks=0,
                    ),
                ]
            ),
        ],
    )


def work_order_finalize_confirmation_modal():
    """Modal for confirming work order finalization"""

    return dbc.Modal(
        id="work_order_finalize_confirmation_modal",
        centered=True,
        is_open=False,
        size="md",
        children=[
            dbc.ModalHeader(
                dbc.ModalTitle(
                    [
                        html.I(className="fa-solid fa-check-circle me-2 text-success"),
                        "Finalize Work Order",
                    ]
                )
            ),
            dbc.ModalBody(
                [
                    html.P(
                        "Are you sure you want to finalize this work order?",
                        className="mb-3",
                    ),
                    html.P(
                        "Once finalized, the work order will be approved and no further changes can be made.",
                        className="text-muted small mb-0",
                    ),
                ]
            ),
            dbc.ModalFooter(
                [
                    dbc.Button(
                        "Cancel",
                        id="work_order_finalize_cancel_btn",
                        color="secondary",
                        size="sm",
                        className="me-2",
                        n_clicks=0,
                    ),
                    # Store whether the work order has been saved after clicking the finalize button
                    # dcc.Store(id="work_order_finalize_store"),
                    dbc.Button(
                        [
                            html.I(className="fa-solid fa-check me-1"),
                            "Finalize Work Order",
                        ],
                        id="work_order_finalize_confirm_btn",
                        color="success",
                        size="sm",
                        n_clicks=0,
                    ),
                ]
            ),
        ],
    )


def work_order_confirmation_and_redirect_modal(company_id: int = None):
    """After submitting the work order, either view all work orders, or start another one"""

    create_new_btn_href: str = f"/{DASH_URL_WORK_ORDER}/"
    if company_id == CUSTOMER_ID_IJACK_CORP:
        create_new_btn_href = f"/{DASH_URL_WORK_ORDER_CORP}/"

    # body = get_modal_body()
    return dbc.Modal(
        [
            dbc.ModalHeader(dbc.ModalTitle("Success!", id="work_order_modal_title")),
            dbc.ModalBody(
                [
                    html.Div(
                        "Work order created successfully.", id="work_order_modal_body"
                    ),
                    dcc.Store(id="store_work_order_id_to_clone", storage_type="memory"),
                    dbc.Card(
                        dbc.CardBody(
                            [
                                html.Div(
                                    "Auto-create matching USD work order for IJACK Inc 🍁? This new, 2nd work order will use transfer pricing."
                                ),
                                dbc.Button(
                                    "Auto-Create",
                                    id="work_order_modal_clone_for_ijack_inc_btn",
                                    size="sm",
                                    color="primary",
                                    n_clicks=0,
                                    # href=create_new_btn_href,
                                    # target="_blank",
                                    # style={"display": "none"},
                                    disabled=False,
                                    class_name="mb-1",
                                ),
                                dbc.Spinner(
                                    html.Div(id="work_order_modal_clone_success_div"),
                                ),
                            ]
                        ),
                        id="work_order_modal_clone_card",
                        class_name="mt-3",
                        style={"display": "none"},
                    ),
                ]
            ),
            dbc.ModalFooter(
                [
                    html.A(
                        dbc.Button(
                            "View All Work Orders",
                            id="work_order_modal_view_all_btn",
                            size="sm",
                            color="secondary",
                            n_clicks=0,
                        ),
                        id="work_order_modal_view_all_link",
                        href=None,
                        style={"color": "#fff"},  # white, not blue for link
                    ),
                    dbc.Button(
                        # dcc.Link('Create New Work Order', href="/")
                        "Create New Work Order",
                        id="work_order_modal_create_new_btn",
                        size="sm",
                        color="secondary",
                        n_clicks=0,
                        href=create_new_btn_href,
                        target="_blank",
                    ),
                ]
            ),
        ],
        id="work_order_modal",
        centered=True,
        size="xl",  # Make the modal wider for better visibility of sanity check reports
        # # Render a backdrop that doesn't dismiss the modal when clicked
        # # (i.e. user MUST click a button in the footer)
        # backdrop="static",
        is_open=False,
    )


def work_order_totals_rows(company_id: int = None):
    """Totals row, including tax"""

    subtotal_str: str = "Subtotal"
    subtotal_after_discount_str: str = "Subtotal After Discount"
    sales_tax_str: str = "Sales Tax"
    total_str: str = "Total"
    default_sales_tax_id: str = None
    if company_id == CUSTOMER_ID_IJACK_INC:
        subtotal_str = "Subtotal (CAD)"
        subtotal_after_discount_str = "Subtotal After Discount (CAD)"
        sales_tax_str = "Sales Tax (CAD)"
        total_str = "Total (CAD)"
        default_sales_tax_id = str(SALES_TAX_ID_SK)
    elif company_id == CUSTOMER_ID_IJACK_CORP:
        subtotal_str = "Subtotal (USD)"
        subtotal_after_discount_str = "Subtotal After Discount (USD)"
        sales_tax_str = "Sales Tax (USD)"
        total_str = "Total (USD)"

    return html.Form(
        # Ddd this to prevent autofill from Chrome, which is annoying
        autoComplete="off",
        children=dbc.Row(
            [
                dbc.Col(
                    [
                        dbc.Row(
                            [
                                dbc.Label(
                                    subtotal_str,
                                    "work_order_subtotal_label",
                                    xs=12,
                                    sm=5,
                                ),
                                dcc.Store(
                                    storage_type="local", id="work_order_subtotal_store"
                                ),
                                dbc.Col(
                                    dbc.Input(
                                        id="work_order_subtotal",
                                        value=0,
                                        disabled=True,
                                        type="text",
                                        style={
                                            "text-align": "right",
                                        },
                                    ),
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                        ),
                        # Discount row
                        dbc.Row(
                            [
                                dbc.Label("Discount (%)", xs=12, sm=5),
                                dbc.Col(
                                    dbc.Input(
                                        id="work_order_discount_pct",
                                        value=0,
                                        disabled=False,
                                        type="number",
                                        style={
                                            "text-align": "right",
                                        },
                                    ),
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                        ),
                        # Subtotal after discount row
                        dbc.Row(
                            [
                                dbc.Label(
                                    subtotal_after_discount_str,
                                    id="work_order_subtotal_after_discount_label",
                                    xs=12,
                                    sm=5,
                                ),
                                dcc.Store(
                                    storage_type="local",
                                    id="work_order_subtotal_after_discount_store",
                                ),
                                dbc.Col(
                                    dbc.Input(
                                        id="work_order_subtotal_after_discount",
                                        value=0,
                                        disabled=True,
                                        type="text",
                                        style={
                                            "text-align": "right",
                                        },
                                    ),
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                        ),
                        # Add a switch for companies that are tax-exempt
                        dbc.Row(
                            class_name="justify-content-end my-2",
                            children=[
                                dbc.Col(
                                    width="auto",
                                    class_name="text-end",
                                    children=dbc.Label(
                                        "Tax exempt?",
                                        className="mb-0 mt-3 fw-medium",
                                    ),
                                ),
                                dbc.Col(
                                    width="auto",
                                    class_name="text-end",
                                    children=dbc.Switch(
                                        id="work_order_is_tax_exempt",
                                        value=False,
                                        persistence=False,
                                        # persistence_type="session",
                                        class_name="mt-3 float-end",
                                        # size="md",
                                        input_style={"transform": "scale(1.5)"},
                                    ),
                                ),
                            ],
                        ),
                        dbc.Collapse(
                            id="work_order_taxes_div",
                            is_open=True,
                            children=[
                                # Province or state row
                                dbc.Row(
                                    [
                                        dbc.Label(
                                            "Province"
                                            if company_id == CUSTOMER_ID_IJACK_INC
                                            else "State",
                                            # id="work_order_province_state_label",
                                            xs=12,
                                            sm=5,
                                        ),
                                        dbc.Col(
                                            dcc.Dropdown(
                                                id="work_order_province_id",
                                                persistence=False,
                                                # persistence_type="session",
                                                # style={
                                                #     "text-align": "right",
                                                # },
                                            ),
                                            xs=12,
                                            sm=7,
                                        ),
                                    ],
                                    class_name="mt-3",
                                    # style={"display": "none"},
                                    # id="work_order_province_state_row",
                                ),
                                # County row
                                dbc.Row(
                                    [
                                        dbc.Label("County", xs=12, sm=5),
                                        dbc.Col(
                                            dcc.Dropdown(
                                                id="work_order_county_id",
                                            ),
                                            xs=12,
                                            sm=7,
                                        ),
                                    ],
                                    class_name="mt-3",
                                    style={"display": "none"},
                                    id="work_order_county_row",
                                ),
                                # City row
                                dbc.Row(
                                    [
                                        dbc.Label("City", xs=12, sm=5),
                                        dbc.Col(
                                            dcc.Dropdown(
                                                id="work_order_city_id",
                                            ),
                                            xs=12,
                                            sm=7,
                                        ),
                                    ],
                                    class_name="mt-3",
                                    style={"display": "none"},
                                    id="work_order_city_row",
                                ),
                                # Zip code row
                                dbc.Row(
                                    [
                                        dbc.Label("ZIP code", xs=12, sm=5),
                                        dbc.Col(
                                            dcc.Dropdown(
                                                id="work_order_zip_code_id",
                                            ),
                                            xs=12,
                                            sm=7,
                                        ),
                                    ],
                                    class_name="mt-3",
                                    style={"display": "none"},
                                    id="work_order_zip_code_row",
                                ),
                                dbc.Row(
                                    [
                                        dbc.Label("Sales tax select", xs=12, sm=5),
                                        dbc.Col(
                                            dcc.Dropdown(
                                                id="work_order_sales_tax_id",
                                                value=default_sales_tax_id,
                                            ),
                                            xs=12,
                                            sm=7,
                                        ),
                                    ],
                                    class_name="mt-3",
                                    id="work_order_sales_tax_select_row",
                                ),
                            ],
                        ),
                        # Sales tax rate row - combined rate (for US and backward compatibility)
                        dbc.Row(
                            [
                                dbc.Label("Sales tax rate (%)", xs=12, sm=5),
                                dbc.Col(
                                    [
                                        dbc.Input(
                                            id="work_order_sales_tax_rate",
                                            value=0,
                                            disabled=False,
                                            style={
                                                "text-align": "right",
                                            },
                                        ),
                                        dbc.FormText(
                                            id="work_order_sales_tax_rate_text"
                                        ),
                                    ],
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                            id="work_order_sales_tax_rate_row",
                        ),
                        # GST rate row (for Canadian provinces)
                        dbc.Row(
                            [
                                dbc.Label("GST rate (%)", xs=12, sm=5),
                                dbc.Col(
                                    [
                                        dbc.Input(
                                            id="work_order_gst_rate",
                                            value=5,  # Default 5% in Canada
                                            disabled=False,
                                            style={
                                                "text-align": "right",
                                            },
                                        ),
                                        dbc.FormText(
                                            id="work_order_gst_rate_text",
                                            children="Federal GST (Goods and Services Tax)",
                                        ),
                                    ],
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                            id="work_order_gst_rate_row",
                            style={"display": "none"},
                        ),
                        # PST rate row (for Canadian provinces)
                        dbc.Row(
                            [
                                dbc.Label("PST rate (%)", xs=12, sm=5),
                                dbc.Col(
                                    [
                                        dbc.Input(
                                            id="work_order_pst_rate",
                                            value=6,  # Default to SK's PST rate
                                            disabled=False,
                                            style={
                                                "text-align": "right",
                                            },
                                        ),
                                        dbc.FormText(
                                            id="work_order_pst_rate_text",
                                            children="Provincial PST (Provincial Sales Tax)",
                                        ),
                                    ],
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                            id="work_order_pst_rate_row",
                            style={"display": "none"},
                        ),
                        dbc.Row(
                            [
                                dbc.Label(
                                    sales_tax_str,
                                    id="work_order_sales_tax_label",
                                    xs=12,
                                    sm=5,
                                ),
                                dcc.Store(
                                    storage_type="local",
                                    id="work_order_sales_tax_store",
                                ),
                                dbc.Col(
                                    dbc.Input(
                                        id="work_order_sales_tax",
                                        value=0,
                                        disabled=True,
                                        type="text",
                                        style={
                                            "text-align": "right",
                                        },
                                    ),
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                            id="work_order_sales_tax_row",
                        ),
                        # GST amount row (for Canadian provinces)
                        dbc.Row(
                            [
                                dbc.Label(
                                    "GST Amount",
                                    id="work_order_gst_amount_label",
                                    xs=12,
                                    sm=5,
                                ),
                                dcc.Store(
                                    storage_type="local",
                                    id="work_order_gst_amount_store",
                                ),
                                dbc.Col(
                                    dbc.Input(
                                        id="work_order_gst_amount",
                                        value=0,
                                        disabled=True,
                                        type="text",
                                        style={
                                            "text-align": "right",
                                        },
                                    ),
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                            id="work_order_gst_amount_row",
                            style={"display": "none"},
                        ),
                        # PST amount row (for Canadian provinces)
                        dbc.Row(
                            [
                                dbc.Label(
                                    "PST Amount",
                                    id="work_order_pst_amount_label",
                                    xs=12,
                                    sm=5,
                                ),
                                dcc.Store(
                                    storage_type="local",
                                    id="work_order_pst_amount_store",
                                ),
                                dbc.Col(
                                    dbc.Input(
                                        id="work_order_pst_amount",
                                        value=0,
                                        disabled=True,
                                        type="text",
                                        style={
                                            "text-align": "right",
                                        },
                                    ),
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                            id="work_order_pst_amount_row",
                            style={"display": "none"},
                        ),
                        dbc.Row(
                            [
                                dbc.Label(
                                    total_str, id="work_order_total_label", xs=12, sm=5
                                ),
                                dcc.Store(
                                    storage_type="local", id="work_order_total_store"
                                ),
                                dbc.Col(
                                    dbc.Input(
                                        id="work_order_total",
                                        value=0,
                                        disabled=True,
                                        type="text",
                                        style={
                                            "text-align": "right",
                                        },
                                    ),
                                    xs=12,
                                    sm=7,
                                ),
                            ],
                            class_name="mt-3",
                        ),
                        # dbc.Row(
                        #     dbc.Col(table),
                        #     class_name="mt-3",
                        # ),
                        # dbc.Card(
                        #     [
                        #         # dbc.CardHeader("test"),
                        #         dbc.CardBody(
                        #             [
                        #                 dbc.Row(
                        #                     [
                        #                         dbc.Label("Email", width="auto"),
                        #                         dbc.Col(
                        #                             dbc.Input(
                        #                                 type="text",
                        #                                 disabled=True,
                        #                                 id="work_order_subtotal",
                        #                             ),
                        #                             # className="me-3",
                        #                         ),
                        #                     ],
                        #                     class_name="mt-1",
                        #                 ),
                        #                 dbc.Row(
                        #                     [
                        #                         dbc.Label("Email", width="auto"),
                        #                         dbc.Col(
                        #                             dbc.Input(
                        #                                 type="text",
                        #                                 disabled=True,
                        #                                 id="work_order_subtotal2",
                        #                             ),
                        #                             # className="me-3",
                        #                         ),
                        #                     ]
                        #                 ),
                        #             ]
                        #         ),
                        #     ]
                        # ),
                    ],
                    md=10,
                    lg=8,
                    xl=7,
                ),
            ],
            justify="end",
            class_name="mt-3",
        ),
    )


def work_order_is_quote_switch_row() -> dbc.Row:
    """
    Creates a row with a switch component for whether the work order is a quote.
    """
    return dbc.Row(
        class_name="justify-content-end mt-2",
        children=[
            dbc.Col(
                width="auto",
                class_name="text-end",
                children=dbc.Label(
                    "Is this just a quote for the time being?",
                    className="mb-0 fw-medium",
                ),
            ),
            dbc.Col(
                width="auto",
                class_name="text-end",
                children=dbc.Switch(
                    id="work_order_is_quote",
                    disabled=False,
                    value=False,
                    persistence=False,
                    input_style={"transform": "scale(1.5)"},
                    class_name="float-end",
                ),
            ),
        ],
    )


def work_order_approved_switch_row() -> dbc.Row:
    """
    Creates a row with a switch component for work order approval status.
    The switch is right-aligned.
    """
    return dbc.Row(
        class_name="justify-content-end mt-1 mb-3",
        children=[
            dbc.Col(
                width="auto",
                class_name="text-end",
                children=dbc.Label(
                    "Approved for invoice creation?",
                    className="mb-0 fw-medium",
                ),
            ),
            dbc.Col(
                width="auto",
                class_name="text-end",
                children=dbc.Switch(
                    id="work_order_approved",
                    disabled=True,
                    value=False,
                    persistence=False,
                    input_style={"transform": "scale(1.5)"},
                    class_name="float-end",
                ),
            ),
        ],
    )


@callback(
    Output("work_order_is_quote", "disabled", allow_duplicate=True),
    Output("work_order_is_quote", "value", allow_duplicate=True),
    Input("work_order_approved", "value"),
    prevent_initial_call=True,
)
def toggle_work_order_is_quote_switch(is_approved: bool) -> Tuple[bool, bool]:
    """
    Callback to toggle the 'Is this just a quote?' switch based on work order approval status.
    If the work order is approved, the switch is disabled and set to False.
    If the work order is not approved, the switch is enabled and set to False.
    """
    if is_approved:
        return True, False
    return False, no_update


def get_submit_button() -> dbc.Row:
    """Save work order button for form"""
    return html.Div(
        [
            dbc.Row(
                justify="end",  # Right-align the buttons
                class_name="mt-2 g-1",
                children=[
                    # Save button column
                    dbc.Col(
                        [
                            # This gets triggered by the save button, to do some things before submitting
                            dcc.Store(id="work_order_submit_store"),
                            dbc.Button(
                                [
                                    html.I(className="fa-solid fa-floppy-disk me-1"),
                                    "Save",
                                ],
                                type="submit",
                                id="work_order_save_btn",
                                disabled=False,
                                external_link=True,
                            ),
                        ],
                        width="auto",
                    ),
                    # Delete button column
                    dbc.Col(
                        dbc.Button(
                            [
                                html.I(className="fa-solid fa-trash me-1"),
                                "Delete",
                            ],
                            id="work_order_delete_btn",
                            color="danger",  # Red color
                            disabled=False,
                            outline=True,  # Outlined style for better visual hierarchy
                        ),
                        width="auto",
                    ),
                    # # Finalize work order button column (this is the last thing anyone does,
                    # # after which no changes can be made to the work order)
                    # dbc.Col(
                    #     dbc.Button(
                    #         [
                    #             html.I(className="fa-solid fa-check me-1"),
                    #             "Approve and Finalize",
                    #         ],
                    #         id="work_order_finalize_btn",
                    #         color="success",  # Green color
                    #         disabled=True,  # Disabled until work order is loaded
                    #         outline=True,  # Outlined style for better visual hierarchy
                    #     ),
                    #     width="auto",
                    # ),
                ],
            ),
            dbc.Row(
                justify="end",
                class_name="mt-1",
                children=[
                    # Save button form text
                    dbc.Col(
                        dbc.FormText(
                            "",
                            id="work_order_save_btn_form_text",
                            color="secondary",
                            className="text-end",
                        ),
                        width="auto",
                    ),
                    # Delete button form text
                    dbc.Col(
                        dbc.FormText(
                            "",
                            id="work_order_delete_btn_form_text",
                            color="secondary",
                            className="text-end",
                        ),
                        width="auto",
                    ),
                    # # Finalize button form text
                    # dbc.Col(
                    #     dbc.FormText(
                    #         "",
                    #         id="work_order_finalize_btn_form_text",
                    #         color="secondary",
                    #         className="text-end",
                    #     ),
                    #     width="auto",
                    # ),
                ],
            ),
        ]
    )


def get_pdf_dropdown_button() -> dbc.Row:
    """PDF generation dropdown button for work order"""
    return dbc.Row(
        justify="end",  # Right-align the button
        class_name="mt-2",
        children=dbc.Col(
            [
                # PDF generation dropdown button
                dbc.DropdownMenu(
                    label=[html.I(className="fas fa-file-pdf me-1"), "PDF Options"],
                    color="danger",
                    id="work_order_pdf_dropdown",
                    disabled=True,  # Disabled until work order is loaded
                    children=[
                        dbc.DropdownMenuItem(
                            [
                                html.I(className="fas fa-download me-1"),
                                "Download PDF",
                            ],
                            id="work_order_download_pdf_btn",
                            href="#",  # Will be set by callback
                        ),
                        dbc.DropdownMenuItem(
                            [
                                html.I(className="fas fa-envelope me-1"),
                                "Email with PDF",
                            ],
                            id="work_order_email_pdf_btn",
                            href="#",  # Will be set by callback
                        ),
                    ],
                ),
            ],
            width="auto",
        ),
    )


def get_dmc_column_defs(has_app_context: bool = False):
    """Get the column definitions for the ag-grid with dmc.Select options"""
    if not has_app_context:
        options = [
            {"label": "Calgary", "value": "YYC"},
        ]
    else:
        options = [
            {"label": "Calgary", "value": "YYC"},
            {"label": "Seattle", "value": "SEA"},
            {"label": "San Francisco", "value": "SFO", "disabled": True},
        ]

    return [
        {
            "field": "city",
            "cellEditor": {"function": "SelectDMC"},
            "cellEditorParams": {
                "options": options,
                "placeholder": "Select a City",
                "maxDropdownHeight": 280,
                "searchable": True,
                "clearable": True,
            },
            "valueFormatter": {"function": "getDisplayLabel(params)"},
            "cellEditorPopup": True,
            "singleClickEdit": True,
        },
        # First column with a custom button renderer
        {
            "field": "structure_id",  # Field name for the column
            "headerName": "Make Quote",  # Header for the button column
            "headerTooltip": "Make a work order quote for this unit (IJACK-only column)",
            "cellRenderer": "ButtonDBC",  # Use custom button renderer
            "cellRendererParams": {"color": "primary", "label": "Quote"},
            "width": 100,  # Fixed width for button column
            "sortable": False,  # Disable sorting for button column
            "filter": False,  # Disable filtering for button column
            # "pinned": "left",  # Pin button column to the left
        },
    ]


def work_order_layout(
    has_app_context: bool = False, company_id: int = None, is_customer: bool = False
):
    """
    Get the Dash layout for the work order create form.
    If it's just initial layout validation, don't run database queries.
    In Pytest tests, the tables might not be created yet.
    """

    return dbc.Container(
        [
            # Hidden signal value, for running callback on page load
            html.Div(id="hidden_signal_wo", style={"display": "none"}),
            dcc.Location(id="location_work_order", refresh=True),
            # test_dmc_stuff(),
            get_store_items(company_id=company_id),
            # TODO: Put these here at the top temporarily, while troubleshooting the bug that prevents line items from populating...
            # work_order_parts_rows(
            #     has_app_context=has_app_context, company_id=company_id
            # ),
            header_and_dropdowns_row(company_id=company_id, is_customer=is_customer),
            currency_row(company_id=company_id),
            company_customer_row(),
            service_date_and_type_row(),
            # work_order_row_3(),
            # IJACK field techs, Service crew
            field_techs_row(),
            requested_and_approved_by_row(),
            # PO, customer work order, AFE
            customer_row(),
            # Created by
            equipment_serviced_row(),
            # Structure and power unit
            structures_involved_row(),
            service_required_row(),
            work_order_file_upload_row(),
            invoice_summary_autofill_row(),
            work_order_parts_rows(
                has_app_context=has_app_context, company_id=company_id
            ),
            work_order_totals_rows(company_id=company_id),
            signature_row(),
            work_order_is_quote_switch_row(),
            work_order_approved_switch_row(),
            get_submit_button(),
            get_pdf_dropdown_button(),
            work_order_confirmation_and_redirect_modal(company_id=company_id),
            work_order_delete_confirmation_modal(),
            work_order_finalize_confirmation_modal(),
            parts_lookup_modal(),
            email_confirmation_modal(),
            # For the notifications
            html.Div(id="work_order_dmc_notifications"),
        ]
    )


CLASS_NAME_DISPLAY_NONE = "d-none"
MODAL_TITLE_ERROR_STYLE = {"background-color": BOOTSTRAP_RED_500, "color": "white"}


def get_decimal(value) -> Decimal:
    """Strip characters, and convert to a string automatically first"""
    if isinstance(value, Decimal):
        return value
    return Decimal(str(value).replace("$", "").replace(",", ""))


# @cache_memoize_if_prod(60 * 5)
def get_sales_tax_rates_dict(country_id: int | None = None) -> dict:
    """Get a dictionary with sales tax ID as key, and province/rate as label"""
    sql_str: str = """
        select
            t1.id as sales_tax_id,
            provinces.name || ' (' || t1.rate || '%)' as province,
            t1.rate
        from public.sales_taxes t1
        left join public.provinces provinces
            on t1.province_id = provinces.id
        left join public.countries countries
            on provinces.country_id = countries.id
        order by countries.country_code, provinces.name
    """
    bindparams: dict = {}
    if country_id:
        sql_str += " where countries.id = :country_id"
        bindparams["country_id"] = country_id

    sql_text = text(sql_str).bindparams(**bindparams)

    rows, _ = run_sql_query(sql_text, db_name="ijack")
    rates_dict = {
        str(row["sales_tax_id"]): {"province": row["province"], "rate": row["rate"]}
        for row in rows
    }
    return rates_dict


def clone_model(model: Base, **kwargs: dict) -> Base:
    """
    Clone a SQLAlchemy model, except for the many-to-many relationship fields,
    and the foreign relationship fields (e.g. WorkOrderPart)
    """

    if not isinstance(model, Base):
        raise ValueError(f"Expected a SQLAlchemy model object, not '{model}'")

    # Ensure the model's data is loaded before copying.
    model.id
    table = model.__table__
    cols_dict = {key: col for key, col in table.columns.items()}

    # This does NOT include relationship (i.e. *_rel) fields, unfortunately
    # non_pk_columns = [k for k in table.columns.keys() if k not in table.primary_key.columns.keys() and getattr(table, k)]
    non_pk_columns = []
    try:
        for key in table.columns.keys():
            if key in table.primary_key.columns.keys() or key == "id":
                # Skip primary keys
                continue
            col = cols_dict.get(key, None)
            if col is not None and getattr(col, "computed"):
                # Skip "generated" columns
                continue
            non_pk_columns.append(key)
    except Exception:
        current_app.logger.exception(f"Error cloning the model '{model}'!")
        raise

    data = {c: getattr(model, c) for c in non_pk_columns}
    data.update(kwargs)
    clone = model.__class__(**data)

    return clone


def clone_work_order(work_order: WorkOrder, **kwargs: dict) -> int:
    """Clone IJACK Corp work orders to IJACK Inc and return the new work order ID"""

    # if not isinstance(work_order, WorkOrder):
    #     raise ValueError(f"Expected a WorkOrder object, not '{work_order}'")

    # Import config locally to get patched constants in parallel execution
    from app import config

    clone = clone_model(model=work_order, **kwargs)

    db.session.add(clone)

    # Change the customer from the actual US customer to IJACK Corp (who's now buying from IJACK Inc)
    clone.customer_id = config.CUSTOMER_ID_IJACK_CORP
    # Now the company creating the invoice is IJACK Inc 🍁
    clone.creator_company_id = config.CUSTOMER_ID_IJACK_INC
    # Change from USD to CAD
    # clone.currency_id = config.CURRENCY_ID_CAD
    # Olga wants the currency to be USD so CORP pays USD to INC
    clone.currency_id = config.CURRENCY_ID_USD
    # # Change from US state to Saskatchewan
    # clone.province_id = config.PROVINCE_ID_SK

    # Set the country ID explicitly (required field)
    clone.country_id = config.COUNTRY_ID_USA

    # Don't charge sales taxes for inter-company transfers?
    # Sales tax rate is stored as a number between 0 and 100, not between 0 and 1
    sales_tax_rate_new: Decimal = Decimal("0")
    clone.sales_tax_rate = sales_tax_rate_new

    # Many-to-many relationship columns must be done separately
    many_to_many_rel_fields = [
        "users_rel",
        "model_types_rel",
        "structures_rel",
        "power_units_rel",
        "unit_types_rel",
        "services_rel",
        # We'll do this 'work_order_parts_rel' separately below
        # "work_order_parts_rel",
    ]
    for rel_field_name in many_to_many_rel_fields:
        clone_values: list = getattr(clone, rel_field_name, None)
        work_order_values: list = getattr(work_order, rel_field_name, None)
        if work_order_values and not clone_values:
            setattr(clone, rel_field_name, work_order_values)
        # for rel_model_list in getattr(work_order, rel_field_name):
        #     new_list: list = []
        #     for rel_model in rel_model_list:
        #         model_new = rel_model.__class__.get(rel_model.id)
        #         setattr(clone, rel_model, ...)

    # session.flush() communicates a series of operations to the database (insert, update, delete).
    # The database maintains them as pending operations in a transaction.
    # The changes aren't persisted permanently to disk, or visible to other transactions
    # until the database receives a COMMIT for the current transaction (which is what session.commit() does).
    db.session.flush()

    # At this point, the object f has been pushed to the DB,
    # and has been automatically assigned a unique primary key id.
    # Refresh updates given object in the session with its state in the DB
    # (and can also only refresh certain attributes - search for documentation)
    db.session.refresh(clone)

    work_order_id_new = clone.id

    dont_clone_part_nums = (
        "050-0500",
        "050-0501",
        "050-0510",
        "050-0520",
        "050-0530",
        "050-0540",
        "050-0550",
        "050-0560",
        "050-0570",
        "050-0573",
    )
    for wo_part in work_order.work_order_parts_rel:
        if wo_part.part_num in dont_clone_part_nums:
            # Skip these labour and truck cost parts
            continue
        # Recalculate the price based on the CAD price times the transfer price multiplier from the BOM Master spreadsheet
        part_model = db.session.get(Part, wo_part.part_id)
        # Make this in USD. Olga wants the currency to be USD so CORP pays USD to INC.
        # Use the 'get_decimal' function to convert the string to a Decimal,
        # rather than using the calculated field value, since then it'll do the decimals correctly.
        wo_price_for_transfer = get_decimal(part_model.msrp_usd) * get_decimal(
            part_model.transfer_mult_inc_to_corp
        )
        wo_part_clone = clone_model(
            model=wo_part,
            work_order_id=work_order_id_new,
            price=wo_price_for_transfer,
            sales_tax_rate=sales_tax_rate_new,
        )
        clone.work_order_parts_rel.append(wo_part_clone)

    # Recalculate the subtotal, taxes, etc
    clone.subtotal = Decimal("0")
    part = None
    try:
        for part in clone.work_order_parts_rel:
            cost_before_tax_calc = get_decimal(part.price) * get_decimal(part.quantity)
            clone.subtotal += cost_before_tax_calc
    except Exception:
        current_app.logger.exception(f"Problem adding up subtotal for part '{part}'")
        raise

    # Calculate the subtotal after discount
    if clone.discount_pct in (None, 0, Decimal("0")):
        clone.subtotal_after_discount = clone.subtotal
    else:
        # Discount percent is stored as a number between 0 and 100, not between 0 and 1
        clone.subtotal_after_discount = clone.subtotal * (1 - clone.discount_pct / 100)

    # # Calculate sales tax from association proxy via province_id
    # try:
    #     clone.sales_tax_rate = clone.sales_taxes_rel[0].rate / 100
    #     # tax_clone = SalesTax.query.filter_by(province_id=clone.province_id).first()
    #     # tax_rate = tax_clone.rate / 100
    # except Exception:
    #     msg = f"Does the province '{clone.province_rel}' have a sales tax associated with it? clone.sales_taxes_rel = {clone.sales_taxes_rel}"
    #     current_app.logger.exception(msg)
    #     clone.sales_tax_rate = 0

    # Sales tax rate is stored as a number between 0 and 100, not between 0 and 1
    clone.sales_tax = (
        clone.sales_tax_rate / Decimal("100") * clone.subtotal_after_discount
    )
    clone.total = clone.subtotal_after_discount + clone.sales_tax

    db.session.commit()
    return work_order_id_new


def is_submit_btn_disabled(
    work_order_id: int, user_id: int
) -> Tuple[bool, Optional[Any]]:
    """
    If the work order has already been approved by another user,
    disable the submit button and show a message
    """
    is_btn_disabled: bool = False
    message: Any = None
    # Check if the work order has already been approved by another user
    # (i.e. editing existing work order).
    work_order = db.session.get(WorkOrder, work_order_id)
    if work_order and user_id:
        role_ids: list = get_user_role_ids(user_id)
        if ROLE_ID_EDIT_EXISTING_WORK_ORDERS in role_ids:
            # If the user has the role to edit existing work orders, they can edit it
            return is_btn_disabled, message

        is_already_approved = getattr(work_order, "invoice_approval_req", False)
        if is_already_approved:
            approved_by_rel = getattr(work_order, "approved_by_rel", None)
            if hasattr(approved_by_rel, "id"):
                if approved_by_rel.id != user_id:
                    # Disable the submit button
                    is_btn_disabled = True
                    msg = f"This work order has already been approved by {approved_by_rel}, so he/she is the only one who can edit it now."
                    message = dbc.Alert(
                        msg,
                        color="warning",
                        dismissable=True,
                    )

    return is_btn_disabled, message


@callback(
    Output("edit_work_order_id", "data", allow_duplicate=True),
    Output("work_order_data_initialized", "data", allow_duplicate=True),
    Output("work_order_save_btn", "disabled", allow_duplicate=True),
    Output("work_order_save_btn_form_text", "children"),
    Output("work_order_country_id", "value"),
    Output("work_order_currency_id", "value", allow_duplicate=True),
    Output("work_order_msg_collapse", "is_open", allow_duplicate=True),
    Output("work_order_msg_collapse", "children"),
    Input("url", "search"),
    Input("url", "pathname"),
    prevent_initial_call=True,
)
def are_we_editing_an_existing_work_order(url_search, url_pathname) -> tuple:
    """If we're editing an existing work order, store its ID"""

    # return vars
    work_order_id: int = None
    # Default is that we're not editing an existing work order
    work_order_data_initialized: bool = True
    work_order_save_btn_disabled: bool = False
    work_order_save_btn_form_text: Any = None
    country_id: str = COUNTRY_ID_CANADA
    currency_id: int = CURRENCY_ID_CAD
    work_order_msg_collapse_is_open: bool = False
    work_order_msg_collapse_children = None

    if url_search:
        query_str = urllib.parse.urlsplit(url_search).query
        query_dict = urllib.parse.parse_qs(query_str)
        work_order_id = query_dict.get("work_order_id", None)
        if isinstance(work_order_id, (list, tuple)):
            work_order_id = work_order_id[0]

    if url_pathname == f"/{DASH_URL_WORK_ORDER_CORP}/":
        country_id = COUNTRY_ID_USA
        currency_id = CURRENCY_ID_USD

    if work_order_id:
        # If we're editing an existing work order...
        work_order_data_initialized = False
        # Disable the submit button
        work_order_save_btn_disabled, work_order_save_btn_form_text = (
            is_submit_btn_disabled(work_order_id, getattr(current_user, "id", None))
        )

        work_order_msg_collapse_is_open = True
        work_order_msg_collapse_children = dbc.Card(
            dbc.CardBody(f"You are editing work order ID {work_order_id}"),
            class_name="mt-3 mb-2",
        )

    return (
        work_order_id,
        work_order_data_initialized,
        work_order_save_btn_disabled,
        work_order_save_btn_form_text,
        country_id,
        currency_id,
        work_order_msg_collapse_is_open,
        work_order_msg_collapse_children,
    )


@callback(
    Output("location_work_order", "href", allow_duplicate=True),
    Input("hidden_signal_wo", "children"),
    # allow_duplicate requires prevent_initial_call to be True
    prevent_initial_call=True,
)
def check_is_active(
    hidden_signal_children,
):
    """Check if the user is active"""
    if not is_active():
        flash(
            "You have been logged out since your RCOM account is inactive. Please contact IJACK."
        )
        logout_user()
        return url_for("dash.login")

    user_id: int = getattr(current_user, "id", None)
    if not is_admin(user_id=user_id):
        flash(
            "You must be an admin to access this page. Please contact IJACK if you need access."
        )
        return url_for("dash.contact")

    raise PreventUpdate()


@callback(
    Output("work_order_stylesheet", "href"),
    Output("work_order_customer", "options"),
    Output("work_order_service_type", "options"),
    Output("work_order_country_id", "options"),
    Output("work_order_power_units", "options"),
    Output("work_order_model_types", "options"),
    Output("work_order_structures", "options"),
    Output("parts_list_store", "data"),
    # Output("work_order_transfer_to_warehouse_dropdown", "options"),
    Input("hidden_signal_wo", "children"),
)
def get_options_on_page_load(_) -> list:
    """Get the options for the select dropdowns"""

    def return_vars(
        work_order_stylesheet_href: str,
        work_order_customer_options: list,
        work_order_service_type_options: list,
        work_order_country_id_options: list,
        work_order_power_units_options: list,
        work_order_model_types_options: list,
        work_order_structures_options: list,
        parts_list_store_data: list,
        # warehouse_options: list,
    ) -> tuple:
        """Default return vars"""
        return (
            work_order_stylesheet_href,
            work_order_customer_options,
            work_order_service_type_options,
            work_order_country_id_options,
            work_order_power_units_options,
            work_order_model_types_options,
            work_order_structures_options,
            parts_list_store_data,
            # warehouse_options,
        )

    return return_vars(
        work_order_stylesheet_href=url_for(
            "static", filename="src/css/styles_dash_work_order.css"
        ),
        work_order_customer_options=get_db_options(
            columns=["customer"],
            table="customers",
            schema="public",
        ),
        work_order_service_type_options=get_db_options(
            columns=["name"], table="service_types"
        ),
        work_order_country_id_options=get_db_options(
            columns=["country_name"],
            table="countries",
            schema="public",
        ),
        work_order_power_units_options=get_db_options(
            columns=["power_unit"], table="power_units"
        ),
        work_order_model_types_options=get_db_options(
            columns=["model"], table="model_types", schema="public"
        ),
        work_order_structures_options=get_db_options(
            columns=["structure"], table="structures"
        ),
        # This requires the psycopg2 RealDictCursor cursor_factory.
        # Even though as_dict=False, it still returns a list of dicts, using the column names as keys.
        parts_list_store_data=get_db_options(
            columns=["part_num", "description"],
            table="parts",
            items_list=False,
            as_dict=False,
            # Don't allow parts that are flagged for deletion (i.e. not in the BoM Master spreadsheet)
            where="where flagged_for_deletion = false",
        ),
        # warehouse_options=get_db_options(columns=["name"], table="warehouses"),
    )


@callback(
    Output("work_order_users", "options"),
    Output("work_order_users_sales", "options"),
    Output("work_order_users", "value", allow_duplicate=True),
    Output("work_order_users_sales", "value", allow_duplicate=True),
    # Just use any Input to trigger this, other than the hidden_signal_wo
    Input("work_order_service_type", "options"),
    # Need this since there's a duplicate Output
    State("work_order_users", "value"),
    State("work_order_users_sales", "value"),
    prevent_initial_call=True,
)
def get_work_order_users_options(
    _, work_order_users_value, work_order_users_sales_value
) -> list:
    """Get the options for the select dropdowns"""
    ijack_user_options = get_db_options(
        columns=["first_name", "last_name"],
        table="users",
        schema="public",
        where=f"where customer_id in ({CUSTOMER_ID_IJACK_INC}, {CUSTOMER_ID_IJACK_CORP})",
    )
    # Don't update these users if we don't have to, since they'll trigger a refresh of the table data,
    # which may (seems like a bug) overwrite the data already in the table if we're editing an existing work order.
    # See Tim Beals' email to Sean McCarthy from Wed 2025-03-05 5:22 PM.
    new_work_order_users_value = no_update
    new_work_order_users_sales_value = no_update
    current_user_id = getattr(current_user, "id", None)
    if current_user_id:
        if not work_order_users_value:
            # If we're not editing an existing work order, set the current user as the default technician
            new_work_order_users_value = [current_user_id]
        if not work_order_users_sales_value:
            new_work_order_users_sales_value = [current_user_id]

    return (
        ijack_user_options,
        ijack_user_options,
        new_work_order_users_value,
        new_work_order_users_sales_value,
    )


# @callback(
#     Output("work_order_date_service", "value", allow_duplicate=True),
#     Input("hidden_signal_wo", "children"),
#     prevent_initial_call=True,
# )
# def get_work_order_datetime_defaults(_):
#     """Get the default value for the work order date service field."""
#     try:
#         tz_model = db.session.get(TimeZone, current_user.time_zone_id)
#         tz_wanted = pytz.timezone(tz_model.time_zone)
#         time_end_local = datetime.now(tz_wanted)
#         time_start_local = time_end_local - timedelta(hours=3)
#     except Exception:
#         current_app.logger.exception(
#             "Problem getting user's time zone for the work order form"
#         )
#         raise PreventUpdate()

#     return time_start_local.date()


@callback(
    Output("work_order_province_id", "options"),
    Input("work_order_country_id", "value"),
)
def get_work_order_province_dropdown(
    work_order_country_id_value,
) -> list:
    """Get the options for the province dropdown."""
    if work_order_country_id_value:
        where = f"where country_id in (select id from public.countries where id = {work_order_country_id_value})"
    else:
        where = ""

    provinces = get_db_options(
        columns=["name"],
        table="provinces",
        where=where,
    )
    return provinces


@callback(
    Output("work_order_sales_tax_id", "options"),
    # Don't change this one since it might be set to something else
    # Output("work_order_sales_tax_id", "value", allow_duplicate=True),
    Input("hidden_signal_wo", "children"),
    Input("work_order_province_id", "value"),
    prevent_initial_call=True,
)
def get_work_order_sales_tax_dropdown(
    _,
    work_order_province_id_value,
) -> list:
    """
    Get the options sales tax dropdown.
    Note, the sales tax dropdown just looks like a province to the user
    """
    # Don't filter on country code anymore, so INC can create invoices for CORP in USD
    # rates_dict = get_sales_tax_rates_dict(country_id=None)
    # options = [
    #     {"label": dict_["province"], "value": id}
    #     for id, dict_ in rates_dict.items()
    # ]
    filters = []
    if work_order_province_id_value:
        filters.append(SalesTax.province_id == work_order_province_id_value)
    sales_tax_tuples: List[tuple] = (
        SalesTax.query.join(Province, SalesTax.province_id == Province.id)
        .with_entities(Province.name, SalesTax.rate, SalesTax.id)
        .filter(*filters)
        .order_by(Province.name)
        .all()
    )
    options = [
        {
            "label": f"{tuple_[0]} ({tuple_[1]}%)",
            "value": tuple_[2],
        }
        for tuple_ in sales_tax_tuples
    ]

    # Don't change this one since it might be set to something else
    # if len(options) == 1:
    #     # If there's only one option, select it by default
    #     return options, options[0]["value"]

    return options


@callback(
    Output("work_order_county_id", "options"),
    Output("work_order_county_row", "style"),
    Output("work_order_city_row", "style"),
    Output("work_order_zip_code_row", "style"),
    Output("work_order_sales_tax_select_row", "style"),
    Output("work_order_sales_tax_rate_row", "style"),
    Output("work_order_gst_rate_row", "style"),
    Output("work_order_pst_rate_row", "style"),
    Output("work_order_sales_tax_row", "style"),
    Output("work_order_gst_amount_row", "style"),
    Output("work_order_pst_amount_row", "style"),
    Input("hidden_signal_wo", "children"),
    # Input("work_order_sales_tax_id", "value"),
    Input("work_order_province_id", "value"),
    Input("work_order_country_id", "value"),
    prevent_initial_call=False,
)
def get_work_order_county_dropdown(
    _,
    # work_order_sales_tax_id_value,
    work_order_province_id_value,
    work_order_country_id_value,
) -> list:
    """Get the US counties dropdown for Michelle (convenience for her)."""

    def return_vars(
        county_id_options,
        county_row_style,
        city_row_style,
        zip_code_row_style,
        sales_tax_select_row_style,
        sales_tax_rate_row_style,
        gst_rate_row_style,
        pst_rate_row_style,
        sales_tax_row_style,
        gst_amount_row_style,
        pst_amount_row_style,
    ):
        """Return the variables for the callback"""
        return (
            county_id_options,
            county_row_style,
            city_row_style,
            zip_code_row_style,
            sales_tax_select_row_style,
            sales_tax_rate_row_style,
            gst_rate_row_style,
            pst_rate_row_style,
            sales_tax_row_style,
            gst_amount_row_style,
            pst_amount_row_style,
        )

    if work_order_country_id_value == COUNTRY_ID_CANADA:
        # Canadian work orders use GST+PST
        return return_vars(
            county_id_options=no_update,
            county_row_style={"display": "none"},
            city_row_style={"display": "none"},
            zip_code_row_style={"display": "none"},
            sales_tax_select_row_style={},
            sales_tax_rate_row_style={"display": "none"},
            gst_rate_row_style={},
            pst_rate_row_style={},
            sales_tax_row_style={"display": "none"},
            gst_amount_row_style={},
            pst_amount_row_style={},
        )
    elif work_order_country_id_value != COUNTRY_ID_USA:
        # Other non-US work orders use standard combined rate
        return return_vars(
            county_id_options=no_update,
            county_row_style={"display": "none"},
            city_row_style={"display": "none"},
            zip_code_row_style={"display": "none"},
            sales_tax_select_row_style={},
            sales_tax_rate_row_style={},
            gst_rate_row_style={"display": "none"},
            pst_rate_row_style={"display": "none"},
            sales_tax_row_style={},
            gst_amount_row_style={"display": "none"},
            pst_amount_row_style={"display": "none"},
        )

    filters = [
        Country.id == work_order_country_id_value,
    ]
    if work_order_province_id_value:
        filters.append(Province.id == work_order_province_id_value)

    # county_options = get_db_options(columns=["name"], table="counties")
    counties = (
        County.query.join(Province, County.province_id == Province.id)
        # .join(SalesTax, Province.id == SalesTax.province_id)
        .join(Country, Province.country_id == Country.id)
        .filter(*filters)
        .order_by(County.name)
        .all()
    )
    county_options = [
        {"label": f"{county.name} ({county.province_rel.name})", "value": county.id}
        for county in counties
    ]

    # This is a US work order, so show the county, city, and zip code dropdowns
    # and only show combined tax rate (no GST/PST)
    return return_vars(
        county_id_options=county_options,
        county_row_style={},
        city_row_style={},
        zip_code_row_style={},
        sales_tax_select_row_style={"display": "none"},
        sales_tax_rate_row_style={},
        gst_rate_row_style={"display": "none"},
        pst_rate_row_style={"display": "none"},
        sales_tax_row_style={},
        gst_amount_row_style={"display": "none"},
        pst_amount_row_style={"display": "none"},
    )


@callback(
    Output("work_order_city_id", "options"),
    # Input("hidden_signal_wo", "children"),
    Input("work_order_country_id", "value"),
    Input("work_order_province_id", "value"),
    Input("work_order_county_id", "value"),
    prevent_initial_call=True,
)
def get_work_order_city_dropdown(
    work_order_country_id_value,
    work_order_province_id_value,
    work_order_county_id_value,
) -> list:
    """Get the US cities dropdown for Michelle (convenience for her)."""
    if work_order_country_id_value != COUNTRY_ID_USA:
        # Only display this for US work orders
        return []

    filters = []
    if work_order_province_id_value:
        filters.append(City.province_id == work_order_province_id_value)
    if work_order_county_id_value:
        filters.append(City.county_id == work_order_county_id_value)

    # city_options = get_db_options(columns=["city"], table="zip_codes")
    cities = db.session.query(City).filter(*filters).all()
    city_options = [
        {"label": f"{city.name} ({city.province_rel.name})", "value": city.id}
        for city in cities
    ]

    return city_options


@callback(
    Output("work_order_zip_code_id", "options"),
    # Input("hidden_signal_wo", "children"),
    Input("work_order_country_id", "value"),
    Input("work_order_province_id", "value"),
    Input("work_order_county_id", "value"),
    Input("work_order_city_id", "value"),
    prevent_initial_call=True,
)
def get_work_order_zip_code_dropdown(
    work_order_country_id_value,
    work_order_province_id_value,
    work_order_county_id_value,
    work_order_city_id_value,
) -> list:
    """Get the US zip codes dropdown for Michelle (convenience for her)."""
    if work_order_country_id_value != COUNTRY_ID_USA:
        # Only display this for US work orders
        return []

    filters = []
    if work_order_province_id_value:
        filters.append(Province.id == work_order_province_id_value)
    if work_order_county_id_value:
        filters.append(County.id == work_order_county_id_value)
    if work_order_city_id_value:
        filters.append(ZipCode.city_id == work_order_city_id_value)

    # zip_code_options = get_db_options(columns=["zip_code"], table="zip_codes")
    zip_codes = (
        db.session.query(ZipCode)
        .join(City, City.id == ZipCode.city_id)
        .join(County, County.id == City.county_id)
        .join(Province, Province.id == City.province_id)
        .filter(*filters)
        .all()
    )
    if not zip_codes:
        # If there are no zip codes after filtering, remove most of the filters and try again
        filters = [Province.id == work_order_province_id_value]
        zip_codes = (
            db.session.query(ZipCode)
            .join(City, City.id == ZipCode.city_id)
            .join(Province, Province.id == City.province_id)
            .filter(*filters)
            .all()
        )
    zip_code_options = [
        {"label": zip_code.zip_code, "value": zip_code.id} for zip_code in zip_codes
    ]

    return zip_code_options


@callback(
    Output("work_order_link_to_work_orders", "href"),
    Output("work_order_link_to_work_order_quotes", "href"),
    Output("work_order_link_to_work_orders_corp", "href"),
    Output("work_order_link_to_work_order_quotes_corp", "href"),
    Output("work_order_modal_view_all_link", "href"),
    # Input("hidden_signal_wo", "children"),
    Input("work_order_country_id", "value"),
    prevent_initial_call=True,
)
def get_work_order_submit_button_href(work_order_country_id_value):
    """Get the link to the Admin - Work Orders view"""
    url_admin_ijack_inc = "/admin/work_orders"
    url_admin_ijack_inc_quotes = "/admin/work_order_quotes"
    url_admin_ijack_corp = "/admin/work_orders_corp"
    url_admin_ijack_corp_quotes = "/admin/work_order_quotes_corp"

    if work_order_country_id_value == COUNTRY_ID_USA:
        url_modal_work_orders_view_all = url_admin_ijack_corp
    else:
        url_modal_work_orders_view_all = url_admin_ijack_inc

    return (
        url_admin_ijack_inc,
        url_admin_ijack_inc_quotes,
        url_admin_ijack_corp,
        url_admin_ijack_corp_quotes,
        url_modal_work_orders_view_all,
    )


@callback(
    Output("work_order_data_initialized", "data", allow_duplicate=True),
    Output("work_order_parts_table_ag", "rowData", allow_duplicate=True),
    Output("work_order_date_service", "value"),
    # Output("prev_work_order_quantities_to_update", "data"),
    Output("work_order_creator_id", "data"),
    Output("work_order_service_type", "value"),
    Output("work_order_currency_id", "value", allow_duplicate=True),
    Output("work_order_creator_company_id", "data"),
    Output("work_order_customer", "value"),
    Output("work_order_users", "value", allow_duplicate=True),
    Output("work_order_users_sales", "value", allow_duplicate=True),
    Output("work_order_requested_by_dropdown", "value"),
    Output("work_order_company_rep_dropdown", "value", allow_duplicate=True),
    Output("work_order_service_crew", "value"),
    Output("work_order_location", "value"),
    Output("work_order_model_types", "value"),
    Output("work_order_service_required", "value"),
    Output("work_order_is_warranty", "value"),
    Output("work_order_work_done", "value", allow_duplicate=True),
    Output("work_order_customer_po", "value"),
    Output("work_order_cust_work_order", "value"),
    Output("work_order_afe", "value"),
    Output("signature_store", "data", allow_duplicate=True),
    Output("work_order_signature_name", "value"),
    Output("work_order_signature_date", "date", allow_duplicate=True),
    Output("work_order_structures", "value"),
    Output("work_order_structure_slave", "value"),
    Output("work_order_power_units", "value"),
    Output("work_order_discount_pct", "value"),
    Output("work_order_sales_tax_id", "value"),
    Output("work_order_sales_tax_rate", "value", allow_duplicate=True),
    Output("work_order_province_id", "value"),
    Output("work_order_county_id", "value"),
    Output("work_order_city_id", "value"),
    Output("work_order_zip_code_id", "value"),
    # Output("work_order_transfer_to_warehouse_dropdown", "value"),
    # Output("work_order_transfer_to_warehouse_dropdown", "disabled"),
    # Output("work_order_transfer_to_warehouse_switch", "value"),
    # Output("work_order_transfer_to_warehouse_switch", "disabled"),
    Output("work_order_approved", "disabled"),
    Output("work_order_approved", "value"),
    Output("work_order_is_quote", "disabled", allow_duplicate=True),
    Output("work_order_is_quote", "value", allow_duplicate=True),
    # Output("work_order_is_quote_previous_value", "data"),
    Output("work_order_gst_rate", "value", allow_duplicate=True),
    Output("work_order_pst_rate", "value", allow_duplicate=True),
    Output("work_order_is_tax_exempt", "value", allow_duplicate=True),
    # Save and approve buttons disabled if the work order is already finalized
    # Output("work_order_save_btn", "disabled"),
    # Output("work_order_finalize_btn", "disabled", allow_duplicate=True),
    Input("edit_work_order_id", "data"),
    State("work_order_data_initialized", "data"),
    prevent_initial_call=True,
)
def populate_values_for_editing_existing_work_order(
    edit_work_order_id_data, work_order_data_initialized_data
):
    """If we're editing an existing work order, populate the existing values"""
    if not edit_work_order_id_data or work_order_data_initialized_data:
        # If we're not editing an existing work order, or the data is already initialized,
        raise PreventUpdate()

    work_order = db.session.get(WorkOrder, edit_work_order_id_data)
    if not work_order:
        raise PreventUpdate()

    def return_vars(
        work_order_data_initialized_data,
        work_order_parts_table_ag_rowData,
        work_order_date_service_value,
        # prev_work_order_quantities_to_update_data,
        work_order_creator_id_value,
        work_order_service_type_value,
        work_order_currency_id_value,
        work_order_creator_company_id_data,
        work_order_customer_value,
        work_order_users_value,
        work_order_users_sales_value,
        work_order_requested_by_dropdown_value,
        work_order_company_rep_dropdown_value,
        work_order_service_crew_value,
        work_order_location_value,
        work_order_model_types_value,
        work_order_service_required_value,
        work_order_is_warranty_value,
        work_order_work_done_value,
        work_order_customer_po_value,
        work_order_cust_work_order_value,
        work_order_afe_value,
        signature_store_data: str,
        work_order_signature_name: str,
        work_order_signature_date_date,
        work_order_structures_value,
        work_order_structure_slave_value,
        work_order_power_units_value,
        work_order_discount_pct_value,
        work_order_sales_tax_id_value,
        work_order_sales_tax_rate_value,
        work_order_province_id_value,
        work_order_county_id_value,
        work_order_city_id_value,
        work_order_zip_code_id_value,
        # work_order_transfer_to_warehouse_dropdown_value,
        # work_order_transfer_to_warehouse_dropdown_disabled,
        # work_order_transfer_to_warehouse_switch_value,
        # work_order_transfer_to_warehouse_switch_disabled,
        work_order_approved_disabled,
        work_order_approved_value,
        work_order_is_quote_disabled,
        work_order_is_quote_value,
        # work_order_is_quote_previous_value_data,
        work_order_gst_rate_value,
        work_order_pst_rate_value,
        work_order_is_tax_exempt_value,
        # work_order_save_btn_disabled,
        # work_order_finalize_btn_disabled,
    ):
        """Default return values"""
        return (
            work_order_data_initialized_data,
            work_order_parts_table_ag_rowData,
            work_order_date_service_value,
            # prev_work_order_quantities_to_update_data,
            work_order_creator_id_value,
            work_order_service_type_value,
            work_order_currency_id_value,
            work_order_creator_company_id_data,
            work_order_customer_value,
            work_order_users_value,
            work_order_users_sales_value,
            work_order_requested_by_dropdown_value,
            work_order_company_rep_dropdown_value,
            work_order_service_crew_value,
            work_order_location_value,
            work_order_model_types_value,
            work_order_service_required_value,
            work_order_is_warranty_value,
            work_order_work_done_value,
            work_order_customer_po_value,
            work_order_cust_work_order_value,
            work_order_afe_value,
            # This is a Base64 string, which is what we want
            signature_store_data,
            work_order_signature_name,
            work_order_signature_date_date,
            work_order_structures_value,
            work_order_structure_slave_value,
            work_order_power_units_value,
            work_order_discount_pct_value,
            work_order_sales_tax_id_value,
            work_order_sales_tax_rate_value,
            work_order_province_id_value,
            work_order_county_id_value,
            work_order_city_id_value,
            work_order_zip_code_id_value,
            # work_order_transfer_to_warehouse_dropdown_value,
            # work_order_transfer_to_warehouse_dropdown_disabled,
            # work_order_transfer_to_warehouse_switch_value,
            # work_order_transfer_to_warehouse_switch_disabled,
            work_order_approved_disabled,
            work_order_approved_value,
            work_order_is_quote_disabled,
            work_order_is_quote_value,
            # work_order_is_quote_previous_value_data,
            work_order_gst_rate_value,
            work_order_pst_rate_value,
            work_order_is_tax_exempt_value,
            # work_order_save_btn_disabled,
            # work_order_finalize_btn_disabled,
        )

    try:
        sales_tax_id = work_order.sales_taxes_rel[0].id
    except Exception:
        sales_tax_id = None

    # Is the switch for approving the work order disabled?
    user_id: int = getattr(current_user, "id", None)
    role_ids: list = get_user_role_ids(user_id)
    # Disable the switch if the user doesn't have the role to approve work orders,
    # or if the work order is already approved
    work_order_approved_disabled: bool = (
        ROLE_ID_APPROVE_WORK_ORDERS not in role_ids
        # or work_order.invoice_approval_req
    )
    work_order_is_quote_disabled: bool = (
        ROLE_ID_EDIT_EXISTING_WORK_ORDERS not in role_ids
        or work_order.invoice_approval_req
    )

    # When editing an existing work order, disable the switch so the user can't change it,
    # since that complicates things tremendously
    if work_order.warehouse_id:
        pass
    else:
        pass

    # Fill the line item parts table with existing values
    work_order_parts_table_ag_rowData = []
    parts_loaded = [part for part in work_order.work_order_parts_rel]

    # Change this to a dict so we can update the quantities later
    # prev_work_order_quantities_to_update = {}
    for index, part in enumerate(parts_loaded):
        new_row = {
            "delete": "",
            # This is the part_id from the public.parts table.
            # It's displayed as the part_num string, but stored as the part_id
            "part_id": str(part.part_id),
            "description": str(part.description),
            "structure_id": str(part.structure_id) or "",
            "warehouse_id": str(part.warehouse_id) or "",
            "warehouse_to_id": str(part.warehouse_to_id) or "",
            "quantity": part.quantity,
            "price": part.price,
            "cost_before_tax": part.quantity * part.price,
            "field_tech_id": str(part.field_tech_id) or "",
        }
        work_order_parts_table_ag_rowData.insert(index, new_row)

        # # Store the existing warehouse quantities to update later
        # prev_work_order_quantities_to_update[str(part.part_id)] = float(part.quantity)

    return return_vars(
        work_order_data_initialized_data=True,
        work_order_parts_table_ag_rowData=work_order_parts_table_ag_rowData,
        work_order_date_service_value=work_order.date_service,
        # prev_work_order_quantities_to_update_data=prev_work_order_quantities_to_update,
        work_order_creator_id_value=work_order.creator_id,
        work_order_service_type_value=work_order.service_type_id,
        work_order_currency_id_value=work_order.currency_id,
        work_order_creator_company_id_data=work_order.creator_company_id,
        work_order_customer_value=work_order.customer_id,
        work_order_users_value=[user.id for user in work_order.users_rel],
        work_order_users_sales_value=[user.id for user in work_order.users_sales_rel],
        # These have to be strings, since the dropdown options are strings
        work_order_requested_by_dropdown_value=str(work_order.requested_by_id),
        work_order_company_rep_dropdown_value=str(work_order.approval_person_id),
        work_order_service_crew_value=work_order.service_crew,
        work_order_location_value=work_order.location,
        work_order_model_types_value=[
            model_types.id for model_types in work_order.model_types_rel
        ],
        work_order_service_required_value=work_order.service_required,
        work_order_is_warranty_value=work_order.is_warranty,
        work_order_work_done_value=work_order.work_done,
        work_order_customer_po_value=work_order.customer_po,
        work_order_cust_work_order_value=work_order.cust_work_order,
        work_order_afe_value=work_order.afe,
        # This is a Base64 string, which is what we want
        signature_store_data=work_order.signature_svg,
        work_order_signature_name=work_order.signature_name,
        work_order_signature_date_date=work_order.signature_date
        if work_order.signature_date
        else None,
        work_order_structures_value=[
            structure.id for structure in work_order.structures_rel
        ],
        work_order_structure_slave_value=work_order.structure_slave,
        work_order_power_units_value=[
            power_unit.id for power_unit in work_order.power_units_rel
        ],
        work_order_discount_pct_value=work_order.discount_pct,
        work_order_sales_tax_id_value=sales_tax_id,
        work_order_sales_tax_rate_value=Decimal("0")
        if work_order.is_tax_exempt
        else work_order.sales_tax_rate,
        work_order_province_id_value=work_order.province_id,
        work_order_county_id_value=work_order.county_id,
        work_order_city_id_value=work_order.city_id,
        work_order_zip_code_id_value=work_order.zip_code_id,
        # work_order_transfer_to_warehouse_dropdown_value=transfer_to_warehouse_dropdown_value,
        # work_order_transfer_to_warehouse_dropdown_disabled=transfer_to_warehouse_dropdown_disabled,
        # work_order_transfer_to_warehouse_switch_value=transfer_to_warehouse_switch_value,
        # work_order_transfer_to_warehouse_switch_disabled=transfer_to_warehouse_switch_disabled,
        work_order_approved_disabled=work_order_approved_disabled,
        work_order_approved_value=work_order.invoice_approval_req,
        work_order_is_quote_disabled=work_order_is_quote_disabled,
        work_order_is_quote_value=work_order.is_quote,
        # work_order_is_quote_previous_value_data=work_order.is_quote,
        work_order_gst_rate_value=work_order.gst_rate,
        work_order_pst_rate_value=work_order.pst_rate,
        work_order_is_tax_exempt_value=work_order.is_tax_exempt,
        # work_order_save_btn_disabled=work_order.invoice_approval_req,
        # work_order_finalize_btn_disabled=work_order.invoice_approval_req
        # or work_order.is_quote,
    )


def remove_duplicate_part_warehouse_combinations(row_data: list) -> list:
    """
    Remove duplicate part_id and warehouse_id combinations from the work order parts table.

    This prevents inventory management issues where the same part from the same warehouse
    appears multiple times with different warehouse_to_id values.

    Args:
        row_data: List of row dictionaries from the AG Grid

    Returns:
        List with duplicates removed (keeping first occurrence, clearing duplicates)
    """
    seen_combinations = set()
    cleaned_row_data = []

    for row in row_data:
        # It's displayed as the part_num string, but stored as the part_id
        part_id = row.get("part_id", "")
        warehouse_id = row.get("warehouse_id", "")

        # Skip empty rows
        if not part_id or not warehouse_id:
            cleaned_row_data.append(row)
            continue

        # Create a key for this combination
        combination_key = f"{part_id}_{warehouse_id}"

        if combination_key in seen_combinations:
            # This is a duplicate - clear the row but keep it in the table
            # so the user can see what happened and fix it
            cleared_row = {key: "" for key in row.keys()}
            cleared_row["description"] = (
                f"DUPLICATE REMOVED: Part {part_id} from Warehouse {warehouse_id} already exists above"
            )
            cleaned_row_data.append(cleared_row)
            current_app.logger.warning(
                f"Removed duplicate part {part_id} from warehouse {warehouse_id} combination"
            )
        else:
            # This is the first occurrence - keep it
            seen_combinations.add(combination_key)
            cleaned_row_data.append(row)

    return cleaned_row_data


def work_order_parts_table_add_rows(
    id_triggered: str,
    work_order_parts_lookup_radio_value: int,
    work_order_parts_table_ag_rowData: list,
    work_order_parts_table_ag_columnDefs: list,
) -> list:
    """Button, when clicked, adds a row to the parts table"""

    # Not all AG grid columns have 'field' keys, so we have to check for that
    blank_row_ag = {
        c.get("field", ""): "" for c in work_order_parts_table_ag_columnDefs
    }
    # Whether we're here simply to add a blank row, or to add a part from the parts lookup modal,
    # we need to add a new, blank row at the end of the table.
    work_order_parts_table_ag_rowData.append(blank_row_ag)

    if id_triggered == "work_order_parts_lookup_radio.value":
        # If the user selects a part from the parts lookup modal, add it to the table
        if not work_order_parts_lookup_radio_value:
            return work_order_parts_table_ag_rowData

        part_model = db.session.get(Part, work_order_parts_lookup_radio_value)
        if not part_model:
            return work_order_parts_table_ag_rowData

        new_row = {
            "delete": "",
            # It's displayed as the part_num string, but stored as the part_id
            "part_id": str(part_model.id),
            "description": str(part_model.description),
            "structure_id": "",
            "warehouse_id": "",
            "warehouse_to_id": "",
            "quantity": 1,
            "price": "",
            "cost_before_tax": "",
        }

        # Find the first row with a blank part number and replace it
        for i, row in enumerate(work_order_parts_table_ag_rowData):
            if row["part_id"] in ("", None):
                work_order_parts_table_ag_rowData.insert(i, new_row)
                break

    return work_order_parts_table_ag_rowData


@callback(
    Output("work_order_parts_table_ag", "rowData", allow_duplicate=True),
    Input("work_order_parts_table_ag", "cellValueChanged"),
    # This gets updated after an existing work order is edited, and we might need to update the descriptions
    # Input("prev_work_order_quantities_to_update", "data"),
    Input("work_order_parts_dealer_cost_switch", "value"),
    Input("work_order_add_rows_btn", "n_clicks"),
    Input("work_order_parts_lookup_radio", "value"),
    State("work_order_parts_table_ag", "virtualRowData"),
    State("edit_work_order_id", "data"),
    State("work_order_structures", "value"),
    State("work_order_users", "value"),
    State("work_order_parts_table_ag", "columnDefs"),
    State("work_order_data_initialized", "data"),
    State("work_order_currency_id", "value"),
    State("work_order_creator_id", "data"),
    prevent_initial_call=True,
)
def update_work_order_table_data(
    work_order_parts_table_ag_cellValueChanged,
    # prev_work_order_quantities_to_update_data,
    work_order_parts_dealer_cost_switch_value,
    work_order_add_rows_btn_n_clicks,
    work_order_parts_lookup_radio_value,
    work_order_parts_table_ag_rowData,
    edit_work_order_id_data,
    work_order_structures_value,
    work_order_users_value,
    work_order_parts_table_ag_columnDefs,
    work_order_data_initialized_data,
    work_order_currency_id_value,
    work_order_creator_id_data,
):
    """Calculate the extended price in the parts table"""

    if not work_order_data_initialized_data:
        # We're editing an existing work order, but the data hasn't been initialized yet
        raise PreventUpdate()

    # In the parts table, there are both CAD and USD prices available for each part.
    # If the work order is for Canada (IJACK Inc), use the CAD price.
    price_name: str | None = None
    if str(work_order_currency_id_value) == str(CURRENCY_ID_CAD):
        if work_order_parts_dealer_cost_switch_value == "transfer":
            price_name = "ijack_corp_cost_cad"
        elif work_order_parts_dealer_cost_switch_value == "dealer":
            price_name = "dealer_cost_cad"
        elif work_order_parts_dealer_cost_switch_value == "msrp":
            price_name = "msrp_cad"
        else:
            raise Exception(
                f"Unknown value for work_order_parts_dealer_cost_switch_value: '{work_order_parts_dealer_cost_switch_value}'"
            )
    elif str(work_order_currency_id_value) == str(CURRENCY_ID_USD):
        if work_order_parts_dealer_cost_switch_value == "transfer":
            price_name = "ijack_corp_cost_usd"
        elif work_order_parts_dealer_cost_switch_value == "dealer":
            price_name = "dealer_cost_usd"
        elif work_order_parts_dealer_cost_switch_value == "msrp":
            price_name = "msrp_usd"
        else:
            raise Exception(
                f"Unknown value for work_order_parts_dealer_cost_switch_value: '{work_order_parts_dealer_cost_switch_value}'"
            )
    else:
        raise Exception(
            f"Unknown currency ID: '{work_order_currency_id_value}'. Was expecting {CURRENCY_ID_CAD} (Canada) or {CURRENCY_ID_USD} (USA)."
        )

    def return_vars(work_order_parts_table_ag_rowData: list):
        """Default return values"""
        return work_order_parts_table_ag_rowData

    id_triggered: str = get_id_triggered()
    if id_triggered in (
        "work_order_add_rows_btn.n_clicks",
        "work_order_parts_lookup_radio.value",
    ):
        # Add a row to the parts table
        work_order_parts_table_ag_rowData = work_order_parts_table_add_rows(
            id_triggered=id_triggered,
            work_order_parts_lookup_radio_value=work_order_parts_lookup_radio_value,
            work_order_parts_table_ag_rowData=work_order_parts_table_ag_rowData,
            work_order_parts_table_ag_columnDefs=work_order_parts_table_ag_columnDefs,
        )

    # If editing an existing work order, get the official creator ID so we can check if Tim Beals created it
    work_order_creator_id: int | None = None
    if edit_work_order_id_data:
        work_order = db.session.get(WorkOrder, edit_work_order_id_data)
        if work_order:
            work_order_creator_id = work_order.creator_id

    # Get the parts table from the database, for the part descriptions and prices
    parts_table = get_db_options(
        columns=["part_num", "description", price_name],
        table="parts",
        # We want the column names to be the keys in the dict
        items_list=False,
    )
    # Make it quickly searchable by part_id ("id")
    parts_dict_ag = {str(row.get("id", "")): row for row in parts_table}

    if not work_order_parts_table_ag_rowData:
        # If there are no rows in the table, don't update
        raise PreventUpdate()

    try:
        row: dict
        for row in work_order_parts_table_ag_rowData:
            # It's displayed as the part_num string, but stored as the part_id
            part_id: int = row.get("part_id", None)
            if not part_id:
                continue

            part_dict: dict = parts_dict_ag.get(str(part_id), {})

            # Conditions for filling in the description, quantity, price, structure, and warehouse
            description_is_blank: bool = row.get("description", "") in (None, "")
            # Only part_num 0 can be edited manually. All other parts must use database description,
            # unless the user is Tim Beals...
            is_part_zero: bool = (
                str(part_id) == str(PART_ID_PART_0)
                or part_dict.get("part_num", None) == "0"
            )
            # Tim wants to be able to edit any part description, and Dan says this is okay.
            is_tim_beals: bool = (
                # Tim created the original work order
                work_order_creator_id == USER_ID_TIM
                # This second one is not reliable, but it's a backup
                or work_order_creator_id_data == USER_ID_TIM
                # Tim is currently creating or editing the work order
                or getattr(current_user, "id", None) == USER_ID_TIM
            )
            # If both of these are false, then the user can NOT edit the description
            not_part_zero_and_not_tim_beals: bool = (
                not is_part_zero and not is_tim_beals
            )

            if description_is_blank or not_part_zero_and_not_tim_beals:
                # Set the description from the database
                row["description"] = part_dict.get("description", "")

            if row.get("quantity", "") in (None, ""):
                # The user might overwrite this quantity
                row["quantity"] = 1

            if (
                row.get("price", "") in (None, "")
                or id_triggered == "work_order_parts_dealer_cost_switch.value"
            ):
                # The user might overwrite this price
                row["price"] = part_dict.get(price_name, "")

            if (
                row.get("structure_id", "") in (None, "")
                and work_order_structures_value
            ):
                # The user might overwrite this
                row["structure_id"] = str(work_order_structures_value[0])

            row_structure_id: int = row.get("structure_id", None)
            if row.get("warehouse_id", "") in (None, "") and row_structure_id:
                # The user might overwrite this
                # Get the warehouse ID from the structure
                row["warehouse_id"] = str(
                    db.session.query(Structure.warehouse_id)
                    .filter(Structure.id == int(row_structure_id))
                    .scalar()
                )

            # The price could be zero, in which case we'd still want to recalculate
            if row.get("price", "") != "":
                try:
                    quantity = get_decimal(row["quantity"])
                    price = get_decimal(row["price"])
                    row["cost_before_tax"] = quantity * price
                except Exception:
                    current_app.logger.exception("Problem calculating extended price")
                    row["cost_before_tax"] = "N/A"
                    raise PreventUpdate()

            # Tim wants a default field tech here, but Olga kind of wants the user to choose
            if row.get("field_tech_id", "") in (None, ""):
                if isinstance(work_order_users_value, list) and work_order_users_value:
                    row["field_tech_id"] = str(work_order_users_value[0])
                else:
                    row["field_tech_id"] = str(getattr(current_user, "id", ""))

    except Exception:
        current_app.logger.exception("Problem updating the work order table data")
        raise PreventUpdate()

    # # Remove duplicate part_id and warehouse_id combinations
    # # Keep only the first occurrence and clear duplicates
    # work_order_parts_table_ag_rowData = remove_duplicate_part_warehouse_combinations(
    #     work_order_parts_table_ag_rowData
    # )

    return return_vars(
        work_order_parts_table_ag_rowData=work_order_parts_table_ag_rowData
    )


@callback(
    Output("work_order_taxes_div", "is_open"),
    Input("work_order_is_tax_exempt", "value"),
    prevent_initial_call=True,
)
def show_hide_work_order_taxes_div(work_order_is_tax_exempt_value):
    """Show or hide the taxes div based on the tax exempt checkbox"""
    if work_order_is_tax_exempt_value:
        return False
    return True


@callback(
    Output("work_order_dmc_notifications", "children", allow_duplicate=True),
    Output("work_order_customer_users_store", "data"),
    Output("work_order_requested_by_dropdown", "options"),
    Output("work_order_company_rep_dropdown", "options"),
    Output("work_order_requested_by_dropdown", "disabled"),
    Output("work_order_company_rep_dropdown", "disabled"),
    Output("work_order_is_tax_exempt", "value", allow_duplicate=True),
    Input("work_order_customer", "value"),
    State("edit_work_order_id", "data"),
    # The customer might be persisted, which doesn't trigger follow-up callbacks,
    # so don't use prevent_initial_call=True
    # However, we must use prevent_initial_call=True for Output("work_order_is_tax_exempt", "value", allow_duplicate=True)
    prevent_initial_call=True,
)
def get_work_order_requested_by_and_approval_person_options(
    work_order_customer_value, edit_work_order_id_data
):
    """Get the options for the requested by and company rep dropdowns"""

    def return_vars(
        work_order_dmc_notifications_children: list | str = no_update,
        work_order_customer_users_store_data: dict = {},
        work_order_requested_by_dropdown_options: list = [],
        work_order_company_rep_dropdown_options: list = [],
        # If there's a customer selected, these will change to False (i.e. enabled)
        work_order_requested_by_dropdown_disabled: bool = True,
        work_order_company_rep_dropdown_disabled: bool = True,
        work_order_is_tax_exempt_value: bool = False,
    ):
        """Default return values"""
        if edit_work_order_id_data:
            # If we're editing an existing work order, don't update the tax exempt checkbox
            work_order_is_tax_exempt_value = no_update
        return (
            work_order_dmc_notifications_children,
            work_order_customer_users_store_data,
            work_order_requested_by_dropdown_options,
            work_order_company_rep_dropdown_options,
            work_order_requested_by_dropdown_disabled,
            work_order_company_rep_dropdown_disabled,
            work_order_is_tax_exempt_value,
        )

    if not work_order_customer_value:
        # If the customer is not set, the dropdowns should be disabled and empty
        return return_vars(
            work_order_dmc_notifications_children=dmc_notification(
                "Please select a customer so the dropdown menus can be populated."
            )
        )

    customer: Customer | None = db.session.get(Customer, work_order_customer_value)
    if not customer:
        return return_vars(
            work_order_dmc_notifications_children=dmc_notification(
                f"Couldn't find the customer with ID {work_order_customer_value}",
            )
        )

    # This function is cached, so it's not a big deal to call it multiple times
    users_for_customer: list = get_users_by_customer_id(work_order_customer_value)
    users_not_this_customer: list = get_users_by_customer_id(
        work_order_customer_value, all_except_this_customer=True
    )

    options: list = []
    users_dict_store: dict = {}
    for list_ in (users_for_customer, users_not_this_customer):
        for user in list_:
            options.append(
                {
                    "label": f"{user.first_name} {user.last_name} ({user.customer})",
                    "value": str(user.id),
                }
            )
            users_dict_store[str(user.id)] = {
                "name": f"{user.first_name} {user.last_name}",
                "phone": user.phone,
                "email": user.email,
            }

    return return_vars(
        work_order_customer_users_store_data=users_dict_store,
        work_order_requested_by_dropdown_options=options,
        work_order_company_rep_dropdown_options=options,
        work_order_requested_by_dropdown_disabled=False,
        work_order_company_rep_dropdown_disabled=False,
        work_order_is_tax_exempt_value=customer.is_tax_exempt,
    )


@callback(
    # This person is responsible for approving the quote on behalf of the customer
    Output("work_order_company_rep_dropdown", "value", allow_duplicate=True),
    Input("work_order_requested_by_dropdown", "value"),
    State("work_order_company_rep_dropdown", "value"),
    prevent_initial_call=True,
)
def fill_work_order_approval_person_dropdown(
    work_order_requested_by_dropdown_value,
    work_order_company_rep_dropdown_value,
    # work_order_customer_users_store_data,
):
    """Get the company contact email and requested by values"""
    if (
        not work_order_requested_by_dropdown_value
        # Don't update it if it's already set
        or work_order_company_rep_dropdown_value
    ):
        raise PreventUpdate()

    def return_vars(work_order_company_rep_dropdown_value: str = no_update):
        """Default return values"""
        return work_order_company_rep_dropdown_value

    return return_vars(
        work_order_company_rep_dropdown_value=work_order_requested_by_dropdown_value
    )


@callback(
    Output("work_order_sales_tax_rate", "value", allow_duplicate=True),
    Output("work_order_gst_rate", "value", allow_duplicate=True),
    Output("work_order_pst_rate", "value", allow_duplicate=True),
    Output("work_order_sales_tax_rate_text", "children"),
    Output("work_order_dmc_notifications", "children", allow_duplicate=True),
    # For Canadian work orders, we don't need to update the sales tax rates
    Input("work_order_sales_tax_id", "value"),
    # For US work orders, we need to update the sales tax rates
    Input("work_order_zip_code_id", "value"),
    Input("work_order_city_id", "value"),
    Input("work_order_county_id", "value"),
    Input("work_order_province_id", "value"),
    Input("work_order_is_tax_exempt", "value"),
    State("work_order_country_id", "value"),
    prevent_initial_call=True,
)
def get_work_order_sales_tax_rate(
    work_order_sales_tax_id_value,
    work_order_zip_code_id_value,
    work_order_city_id_value,
    work_order_county_id_value,
    work_order_province_id_value,
    work_order_is_tax_exempt_value,
    work_order_country_id_value,
) -> Decimal:
    """Get the sales tax rate for the work order"""

    def return_vars(
        work_order_sales_tax_rate_value,
        work_order_gst_rate_value,
        work_order_pst_rate_value,
        work_order_sales_tax_rate_text,
        work_order_dmc_notifications_children,
    ):
        """Default return values"""
        return (
            work_order_sales_tax_rate_value,
            work_order_gst_rate_value,
            work_order_pst_rate_value,
            work_order_sales_tax_rate_text,
            work_order_dmc_notifications_children,
        )

    notification = []
    id_triggered: str = get_id_triggered()
    if (
        work_order_is_tax_exempt_value
        or id_triggered == "work_order_is_tax_exempt.value"
    ):
        work_order_sales_tax_rate_text = (
            "Tax exempt" if work_order_is_tax_exempt_value else ""
        )
        return return_vars(
            work_order_sales_tax_rate_value=Decimal("0"),
            work_order_gst_rate_value=Decimal("0"),
            work_order_pst_rate_value=Decimal("0"),
            work_order_sales_tax_rate_text=work_order_sales_tax_rate_text,
            work_order_dmc_notifications_children=notification,
        )

    if (
        work_order_country_id_value == COUNTRY_ID_CANADA
        and id_triggered == "work_order_sales_tax_id.value"
        and work_order_sales_tax_id_value
        and work_order_country_id_value
    ):
        # This is for non-US work orders, which are much simpler than US work orders
        sales_tax_provincial = getattr(
            db.session.get(SalesTax, work_order_sales_tax_id_value),
            "rate",
            Decimal("0"),
        )
        sales_tax_federal = getattr(
            db.session.get(Country, work_order_country_id_value),
            "sales_tax_rate",
            Decimal("0"),
        )
        return return_vars(
            work_order_sales_tax_rate_value=sales_tax_provincial + sales_tax_federal,
            work_order_gst_rate_value=sales_tax_federal,
            work_order_pst_rate_value=sales_tax_provincial,
            work_order_sales_tax_rate_text=f"GST {sales_tax_federal}% + PST {sales_tax_provincial}%",
            work_order_dmc_notifications_children=notification,
        )

    elif work_order_country_id_value == COUNTRY_ID_USA and id_triggered in (
        "work_order_zip_code_id.value",
        "work_order_city_id.value",
        "work_order_county_id.value",
        "work_order_province_id.value",
    ):
        try:
            # Update the US sales tax rate based on the zip code
            zip_code_obj = db.session.get(ZipCode, work_order_zip_code_id_value)
            zip_code: str = zip_code_obj.zip_code if zip_code_obj else ""
            result_dict: TaxRateUpdateResult = update_tax_rate(zip_code=zip_code)
            success: bool = result_dict.get("success", False)
            message: str = result_dict.get("message", "")
            error: str = result_dict.get("error", "")
            if error:
                message: str = f"{message} {error}. We try to update the tax rates weekly so they're up-to-date, but this time it didn't work. Let your RCOM admin know if the sales tax rate looks wrong for this state/county/city/zip code."
            notification = dmc.Notification(
                title="Sales Tax Rate Update",
                message=message,
                color="green" if success else "red",
                loading=False,
                action="show",
                # Checkmark symbol 2713 or X symbol 2717
                icon="\u2713" if success else "\u2717",
                # icon=DashIconify(icon="akar-icons:circle-check"),
                # position="bottom-right",
                autoClose=10_000,  # 10 seconds
            )

            # Get the sales tax rate for the zip code
            filters = {}
            if work_order_zip_code_id_value:
                filters["zip_code_id"] = work_order_zip_code_id_value
            if work_order_city_id_value:
                filters["city_id"] = work_order_city_id_value
            if work_order_county_id_value:
                filters["county_id"] = work_order_county_id_value
            if work_order_province_id_value:
                filters["state_id"] = work_order_province_id_value
            zip_code_sales_taxes: list = (
                db.session.query(ZipCodeSalesTax).filter_by(**filters).all()
            )

            if len(zip_code_sales_taxes) < 1:
                return return_vars(
                    work_order_sales_tax_rate_value=Decimal("0"),
                    work_order_gst_rate_value=Decimal("0"),
                    work_order_pst_rate_value=Decimal("0"),
                    work_order_sales_tax_rate_text="Couldn't find the sales tax rate, so defaulting to 0%",
                    work_order_dmc_notifications_children=notification,
                )

            if len(zip_code_sales_taxes) > 1:
                msg = "Found more than one sales tax rate, so defaulting to 0%."
                if not work_order_zip_code_id_value:
                    msg += " Please select a zip code."
                if not work_order_city_id_value:
                    msg += " Please select a city."
                if not work_order_county_id_value:
                    msg += " Please select a county."
                if not work_order_province_id_value:
                    msg += " Please select a state."
                return return_vars(
                    work_order_sales_tax_rate_value=Decimal("0"),
                    work_order_gst_rate_value=Decimal("0"),
                    work_order_pst_rate_value=Decimal("0"),
                    work_order_sales_tax_rate_text=msg,
                    work_order_dmc_notifications_children=notification,
                )

            # There's just one zip code sales tax rate, which is what we want
            zip_code_sales_tax = zip_code_sales_taxes[0]
            # These are stored as decimals between 0 and 1, and we need them as percentages
            work_order_sales_tax_rate_text = f"{zip_code_sales_tax.state_county_other * 100:.2f}% = state rate {zip_code_sales_tax.state_rate * 100:.2f}% + county rate {zip_code_sales_tax.county_rate * 100:.2f}% + 'other' rate {zip_code_sales_tax.special_rate * 100:.2f}%"
            return return_vars(
                work_order_sales_tax_rate_value=zip_code_sales_tax.state_county_other
                * 100,
                work_order_gst_rate_value=Decimal("0"),
                work_order_pst_rate_value=Decimal("0"),
                work_order_sales_tax_rate_text=work_order_sales_tax_rate_text,
                work_order_dmc_notifications_children=notification,
            )
        except Exception as err:
            notification = dmc.Notification(
                title="Sales Tax Rate Update",
                message=f"Error updating the sales tax rate: {err}",
                color="red",
                loading=False,
                action="show",
                # X symbol 2717
                icon="\u2717",
                autoClose=10_000,  # 10 seconds
            )

    raise PreventUpdate()


@callback(
    Output("work_order_modal_clone_success_div", "children"),
    Output("work_order_modal_clone_for_ijack_inc_btn", "disabled"),
    Input("work_order_modal_clone_for_ijack_inc_btn", "n_clicks"),
    State("store_work_order_id_to_clone", "data"),
    prevent_initial_call=True,
)
def modal_clone_work_order_clicked(
    work_order_modal_clone_for_ijack_inc_btn_n_clicks,
    store_work_order_id_to_clone_data_data,
):
    """When the user decides to clone the USD work order for IJACK Inc in CAD, do the following"""
    # Duplicate the work order so IJACK Corp can buy the stuff from IJACK Inc 🍁
    if not work_order_modal_clone_for_ijack_inc_btn_n_clicks:
        raise PreventUpdate()

    work_order_model = db.session.get(WorkOrder, store_work_order_id_to_clone_data_data)
    if not work_order_model:
        return (
            "For some reason, couldn't find the work order to clone! Operation failure!",
            False,
        )

    clone_work_order(work_order=work_order_model)

    return (
        f"Successfully cloned work order ID '{store_work_order_id_to_clone_data_data}' ✅",
        True,
    )


@callback(
    Output("work_order_modal", "is_open", allow_duplicate=True),
    Output("location_work_order", "href", allow_duplicate=True),
    Input("work_order_modal_create_new_btn", "n_clicks"),
    State("work_order_country_id", "value"),
    prevent_initial_call=True,
)
def work_order_modal_close(
    work_order_modal_create_new_btn_n_clicks, work_order_country_id_value
):
    """Close the success confirmation modal and redirect back to the work orders page"""
    if work_order_country_id_value == COUNTRY_ID_USA:
        # If the work order is for the USA, redirect to the work orders page
        return False, f"/{DASH_URL_WORK_ORDER_CORP}/"
    return False, f"/{DASH_URL_WORK_ORDER}/"


@callback(
    Output("parts_lookup_modal", "is_open"),
    Input("work_order_parts_lookup_btn", "n_clicks"),
    Input("parts_lookup_modal_close_btn", "n_clicks"),
    prevent_initial_call=True,
)
def work_order_parts_lookup_modal_is_open(
    work_order_parts_lookup_btn, parts_lookup_modal_close_btn
):
    """Open or close the parts lookup modal"""
    id_triggered: str = get_id_triggered()
    if id_triggered == "work_order_parts_lookup_btn.n_clicks":
        return True
    if id_triggered == "parts_lookup_modal_close_btn.n_clicks":
        return False

    raise PreventUpdate()


@callback(
    Output("work_order_parts_lookup_radio", "options"),
    Input("parts_list_store", "data"),
    Input("parts_lookup_modal_search_input", "value"),
    Input("parts_lookup_modal_search_btn", "n_clicks"),
)
def get_parts_lookup_options(
    parts_list_store_data,
    parts_lookup_modal_search_input_value,
    parts_lookup_modal_search_btn_n_clicks,
):
    """Get the parts lookup modal contents on page load"""

    if not parts_list_store_data:
        return [{"label": "ERROR: No parts found. Please try again.", "value": None}]

    # Filter the stored parts list, if there's search text
    if parts_lookup_modal_search_input_value:
        new_list = []
        search_str = str(parts_lookup_modal_search_input_value).lower()
        for row in parts_list_store_data:
            if (
                search_str in str(row["part_num"]).lower()
                or search_str in str(row["description"]).lower()
            ):
                new_list.append(row)
        parts_list_store_data = new_list

    if not parts_list_store_data:
        return [{"label": "ERROR: No parts found. Please try again.", "value": None}]

    # Create the radio options list
    radio_options: list = []
    for row in parts_list_store_data:
        if row["part_num"]:
            radio_options.append(
                {
                    "label": f"{row['part_num']} - {row['description']}",
                    "value": row["id"],
                }
            )
        elif row["description"]:
            radio_options.append(
                {
                    "label": f"No part number. Description: {row['description']}",
                    "value": row["id"],
                }
            )
        else:
            radio_options.append(
                {"label": "No part number or description", "value": row["id"]}
            )

    # return table, radio_options
    return radio_options


@callback(
    Output("work_order_file_upload_data_table", "data"),
    Output("work_order_file_upload_data_div", "style"),
    Input("work_order_file_upload", "contents"),
    Input("edit_work_order_id", "data"),
    State("work_order_file_upload", "filename"),
    State("work_order_file_upload_data_table", "data"),
    prevent_initial_call=True,
)
def display_upload_files(
    list_of_contents,
    edit_work_order_id_data,
    list_of_names,
    data,
):
    """Upload a file/picture"""

    id_triggered: str = get_id_triggered()
    if id_triggered == "edit_work_order_id.data":
        if not edit_work_order_id_data:
            raise PreventUpdate()
        work_order = db.session.get(WorkOrder, edit_work_order_id_data)
        if not work_order:
            raise PreventUpdate()
        # Get the WorkOrderUploadFile models that are related to this work order
        files_loaded = [file for file in work_order.work_order_upload_files_rel]
        data = [
            {
                "file_name": file.file_name,
                "file_type": file.file_type,
                "file_bytes": base64.b64encode(file.file_bytes).decode("utf-8"),
            }
            for file in files_loaded
        ]

    else:
        if not list_of_contents:
            raise PreventUpdate()

        data = data or []
        for contents, name in zip(list_of_contents, list_of_names):
            content_type, content_string = contents.split(",")
            data.append(
                {
                    "file_name": name,
                    "file_type": content_type,
                    "file_bytes": content_string,
                }
            )

    if data:
        style = {}
    else:
        style = {"display": "none"}

    return data, style


@callback(
    Output("work_order_invoice_summary", "value"),
    Output("work_order_submit_store", "data"),
    Input("work_order_invoice_summary_autofill", "n_clicks"),
    Input("work_order_save_btn", "n_clicks"),
    Input("edit_work_order_id", "data"),
    State("work_order_invoice_summary", "value"),
    State("work_order_model_types", "value"),
    State("work_order_power_units", "value"),
    State("work_order_structures", "value"),
    State("work_order_service_type", "value"),
    State("work_order_cust_work_order", "value"),
    State("work_order_afe", "value"),
    State("work_order_customer_po", "value"),
    State("work_order_location", "value"),
    State("work_order_company_rep_dropdown", "value"),
    State("work_order_requested_by_dropdown", "value"),
    State("work_order_work_done", "value"),
    prevent_initial_call=True,
)
def invoice_summary_autofill(
    work_order_invoice_summary_autofill_n_clicks,
    work_order_save_btn_n_clicks,
    edit_work_order_id_data,
    work_order_invoice_summary_value,
    work_order_model_types_value,
    work_order_power_units_value,
    work_order_structures_value,
    work_order_service_type_value,
    work_order_cust_work_order_value,
    work_order_afe_value,
    work_order_customer_po_value,
    work_order_location_value,
    work_order_company_rep_dropdown_value,
    work_order_requested_by_dropdown_value,
    work_order_work_done_value,
):
    """Automatically fill in the invoice summary"""

    id_triggered: str = get_id_triggered()
    if id_triggered not in (
        "work_order_invoice_summary_autofill.n_clicks",
        "work_order_save_btn.n_clicks",
        "edit_work_order_id.data",
    ):
        raise PreventUpdate()

    if id_triggered == "work_order_save_btn.n_clicks":
        # Only if the user has submitted the form, do we trigger the create/edit callback
        work_order_submit_store_data = True
        if work_order_invoice_summary_value:
            # The form submit button was pressed, and the summary value is already fill out,
            # so don't redo it
            return no_update, work_order_submit_store_data
    else:
        work_order_submit_store_data = no_update

    if id_triggered == "edit_work_order_id.data":
        if not edit_work_order_id_data:
            raise PreventUpdate()
        work_order = db.session.get(WorkOrder, edit_work_order_id_data)
        if work_order:
            return work_order.invoice_summary, work_order_submit_store_data
        return "", work_order_submit_store_data

    service_type = ServiceType.query.where(
        ServiceType.id.in_(ensure_list(work_order_service_type_value))
    ).first()

    model_types = ModelType.query.where(
        ModelType.id.in_(ensure_list(work_order_model_types_value))
    ).all()

    power_units = PowerUnit.query.where(
        PowerUnit.id.in_(ensure_list(work_order_power_units_value))
    ).all()

    structures = Structure.query.where(
        Structure.id.in_(ensure_list(work_order_structures_value))
    ).all()

    new_summary = ""
    if work_order_location_value:
        new_summary += f"{str(work_order_location_value).strip()}\n"
    if work_order_company_rep_dropdown_value:
        company_approval_person = db.session.get(
            User, work_order_company_rep_dropdown_value
        )
        if company_approval_person:
            new_summary += f"{company_approval_person.first_name} {company_approval_person.last_name}\n"
    elif work_order_requested_by_dropdown_value:
        requested_by_person = db.session.get(
            User, work_order_requested_by_dropdown_value
        )
        if requested_by_person:
            new_summary += (
                f"{requested_by_person.first_name} {requested_by_person.last_name}\n"
            )
    if work_order_cust_work_order_value:
        new_summary += f"WO {str(work_order_cust_work_order_value).strip()}\n"
    if work_order_afe_value:
        new_summary += f"AFE {str(work_order_afe_value).strip()}\n"
    if work_order_customer_po_value:
        new_summary += f"PO {str(work_order_customer_po_value).strip()}\n"
    if service_type:
        new_summary += str(service_type.name).strip()
    if model_types or power_units or structures:
        # new_summary += ": Equipment serviced"
        new_summary += ":"
        if model_types:
            new_summary += f" {', '.join([str(x.model).strip() for x in model_types])}."
        if power_units or structures:
            new_summary += " SN"
            if power_units:
                # if len(power_units) > 1:
                #     new_summary += f" Power units "
                # else:
                #     new_summary += f" Power unit "
                new_summary += f" {', '.join([str(x.power_unit).replace('.0', '').strip() for x in power_units])}."
            if structures:
                # if len(structures) > 1:
                #     new_summary += f" Structures "
                # else:
                #     new_summary += f" Structure "
                new_summary += f" {', '.join([str(x.structure).replace('.0', '').strip() for x in structures])}."
        new_summary += "\n"
    if work_order_work_done_value:
        new_summary += (
            f"\n\nWork performed: {str(work_order_work_done_value).strip()}\n"
        )

    if id_triggered == "work_order_invoice_summary_autofill.n_clicks":
        # If the user is just auto-populating the "invoice summary", don't trigger the "submit" modal
        return f"{new_summary}\n\n", work_order_submit_store_data

    return f"{new_summary}\n\n", work_order_submit_store_data


@callback(
    Output("work_order_sales_tax_rate", "valid"),
    Output("work_order_sales_tax_rate", "invalid"),
    Input("work_order_sales_tax_rate", "value"),
    prevent_initial_call=True,
)
def work_order_sales_tax_rate_valid(value):
    """Validate that the sales tax rate is a number between 0 and 100"""
    if value is None:
        return True, False
    try:
        value = float(value)
        if value < 0 or value > 100:
            return False, True
        return True, False
    except ValueError:
        return False, True


@callback(
    Output("work_order_discount_pct", "valid"),
    Output("work_order_discount_pct", "invalid"),
    Input("work_order_discount_pct", "value"),
    prevent_initial_call=True,
)
def work_order_discount_pct_valid(value):
    """Validate that the discount percent is a number between 0 and 100"""
    if value is None:
        return True, False
    try:
        value = float(value)
        if value < 0 or value > 100:
            return False, True
        return True, False
    except ValueError:
        return False, True


@callback(
    Output("work_order_users_valid", "children"),
    Input("work_order_users", "value"),
    prevent_initial_call=True,
)
def work_order_users_value_valid(value):
    """Validate that the users are selected"""
    if isinstance(value, list) and value:
        return ""
    return "Please select at least one IJACK field technician"


@callback(
    Output("work_order_users_sales_valid", "children"),
    Input("work_order_users_sales", "value"),
    prevent_initial_call=True,
)
def work_order_users_sales_value_valid(value):
    """Validate that the users who get sales credit are selected"""
    if isinstance(value, list) and value:
        return ""
    return "Please select at least one IJACK person to get credit for the sales revenue"


# @callback(
#     Output("work_order_transfer_to_warehouse_collapse", "is_open"),
#     Input("work_order_transfer_to_warehouse_switch", "value"),
#     prevent_initial_call=True,
# )
# def work_order_transfer_to_warehouse_collapse(
#     work_order_transfer_to_warehouse_switch_value,
# ):
#     """If the switch is on, open the collapse"""
#     return work_order_transfer_to_warehouse_switch_value


@callback(
    Output("work_order_subtotal_store", "data"),
    Output("work_order_subtotal", "value"),
    Output("work_order_subtotal_after_discount_store", "data"),
    Output("work_order_subtotal_after_discount", "value"),
    Output("work_order_sales_tax_store", "data"),
    Output("work_order_sales_tax", "value"),
    Output("work_order_gst_amount_store", "data"),
    Output("work_order_gst_amount", "value"),
    Output("work_order_pst_amount_store", "data"),
    Output("work_order_pst_amount", "value"),
    Output("work_order_total_store", "data"),
    Output("work_order_total", "value"),
    # Input("work_order_parts_table", "data"),
    Input("work_order_parts_table_ag", "virtualRowData"),
    # Input("work_order_sales_tax_id", "value"),
    Input("work_order_sales_tax_rate", "value"),
    Input("work_order_gst_rate", "value"),
    Input("work_order_pst_rate", "value"),
    Input("work_order_discount_pct", "value"),
    Input("work_order_country_id", "value"),
    prevent_initial_call=True,
)
def update_subtotals(
    # work_order_parts_table_data,
    work_order_parts_table_ag_rowData,
    # work_order_sales_tax_id_value,
    work_order_sales_tax_rate_value,
    work_order_gst_rate_value,
    work_order_pst_rate_value,
    work_order_discount_pct_value,
    work_order_country_id_value,
):
    """Calculate the subtotal, sales tax, and total for the work order"""
    work_order_subtotal_value = Decimal("0")
    row: dict

    if not work_order_parts_table_ag_rowData:
        # If there are no rows in the table, don't update
        raise PreventUpdate()

    # for row in work_order_parts_table_data:
    for row in work_order_parts_table_ag_rowData:
        cost_before_tax = row.get("cost_before_tax", None)
        if isinstance(cost_before_tax, (float, int, Decimal)):
            work_order_subtotal_value += get_decimal(row["cost_before_tax"])

    # Calculate the subtotal after discount.
    # Discount is stored as a number between 0 and 100, not between 0 and 1
    if work_order_discount_pct_value in (None, 0):
        work_order_subtotal_after_discount_store_data = work_order_subtotal_value
    else:
        work_order_subtotal_after_discount_store_data = work_order_subtotal_value * (
            1 - Decimal(str(work_order_discount_pct_value)) / 100
        )

    # Initialize tax values
    work_order_sales_tax_value = Decimal("0")
    work_order_gst_amount_value = Decimal("0")
    work_order_pst_amount_value = Decimal("0")

    # Calculate sales tax based on country
    if work_order_country_id_value == COUNTRY_ID_CANADA:
        # For Canadian work orders, use GST and PST separately
        if work_order_gst_rate_value not in (None, "", 0):
            work_order_gst_amount_value = (
                get_decimal(work_order_gst_rate_value)
                / Decimal("100")
                * work_order_subtotal_after_discount_store_data
            )

        if work_order_pst_rate_value not in (None, "", 0):
            work_order_pst_amount_value = (
                get_decimal(work_order_pst_rate_value)
                / Decimal("100")
                * work_order_subtotal_after_discount_store_data
            )

        # Combined tax is the sum of GST and PST
        work_order_sales_tax_value = (
            work_order_gst_amount_value + work_order_pst_amount_value
        )
    else:
        # For non-Canadian work orders, use combined sales tax rate
        if work_order_sales_tax_rate_value not in (None, "", 0):
            # Sales tax rate is stored as a number between 0 and 100, not between 0 and 1
            work_order_sales_tax_value = (
                get_decimal(work_order_sales_tax_rate_value)
                / Decimal("100")
                * work_order_subtotal_after_discount_store_data
            )

    # Calculate total
    work_order_total_value = (
        work_order_subtotal_after_discount_store_data + work_order_sales_tax_value
    )

    return (
        work_order_subtotal_value,
        f"${work_order_subtotal_value:,.2f}",
        work_order_subtotal_after_discount_store_data,
        f"${work_order_subtotal_after_discount_store_data:,.2f}",
        work_order_sales_tax_value,
        f"${work_order_sales_tax_value:,.2f}",
        work_order_gst_amount_value,
        f"${work_order_gst_amount_value:,.2f}",
        work_order_pst_amount_value,
        f"${work_order_pst_amount_value:,.2f}",
        work_order_total_value,
        f"${work_order_total_value:,.2f}",
    )


@callback(
    Output("work_order_parts_table_ag", "columnDefs"),
    Output("work_order_subtotal_label", "children"),
    Output("work_order_subtotal_after_discount_label", "children"),
    Output("work_order_sales_tax_label", "children"),
    Output("work_order_total_label", "children"),
    Input("work_order_currency_id", "value"),
    State("work_order_parts_table_ag", "columnDefs"),
    prevent_initial_call=True,
)
def get_things_that_depend_on_currency_id(
    work_order_currency_id_value, work_order_parts_table_ag_columnDefs
):
    """Get the things that depend on the currency ID, like the column names for the parts table"""

    columns_ag: list = modify_columns_for_currency(
        work_order_parts_table_ag_columnDefs, work_order_currency_id_value
    )

    if str(work_order_currency_id_value) == str(CURRENCY_ID_CAD):
        str_in_parentheses = "(CAD)"
    elif str(work_order_currency_id_value) == str(CURRENCY_ID_USD):
        str_in_parentheses = "(USD)"
    else:
        str_in_parentheses = ""

    work_order_subtotal_label: str = f"Subtotal {str_in_parentheses}"
    work_order_subtotal_after_discount_label: str = (
        f"Subtotal After Discount {str_in_parentheses}"
    )
    work_order_sales_tax_label: str = f"Sales Tax {str_in_parentheses}"
    work_order_total_label: str = f"Total {str_in_parentheses}"

    return (
        columns_ag,
        work_order_subtotal_label,
        work_order_subtotal_after_discount_label,
        work_order_sales_tax_label,
        work_order_total_label,
    )


@callback(
    Output("work_order_pdf_dropdown", "disabled"),
    Output("work_order_download_pdf_btn", "href"),
    Input("edit_work_order_id", "data"),
    Input("work_order_approved", "value"),
    State("work_order_customer", "value"),
    # State("work_order_form_dirty_store", "data"),
    prevent_initial_call=True,
)
def update_pdf_dropdown_state(
    edit_work_order_id_data,
    work_order_approved_value,
    work_order_customer_value,
    # work_order_form_dirty_store_data,
):
    """Enable/disable PDF dropdown and set its links based on work order state"""

    def return_vars(
        work_order_pdf_dropdown_disabled: bool = True,
        work_order_download_pdf_btn_href: str = "#",
    ):
        """The default return tuple"""
        return work_order_pdf_dropdown_disabled, work_order_download_pdf_btn_href

    # For a saved, existing work order, PDFs can be created/downloaded
    if edit_work_order_id_data:
        # and not work_order_form_dirty_store_data:
        # Don't allow the PDF invoice to be created/downloaded if the work order is not approved??
        # work_order_pdf_dropdown_disabled = False if work_order_approved_value else True
        work_order_pdf_dropdown_disabled = False
        # Enable dropdown and set hrefs
        # download_url = f"/work-order/{edit_work_order_id_data}/pdf"
        # email_url = f"/work-order/{edit_work_order_id_data}/email-pdf"
        download_url = url_for(
            "auth.make_work_order_pdf",
            work_order_id=edit_work_order_id_data,
            _external=True,
        )
        return return_vars(
            work_order_pdf_dropdown_disabled=work_order_pdf_dropdown_disabled,
            work_order_download_pdf_btn_href=download_url,
        )

    # If no work order ID is available, disable the dropdown and set hrefs to "#"
    return return_vars(
        work_order_pdf_dropdown_disabled=True,
        work_order_download_pdf_btn_href="#",
    )


def get_ai_description(description: str) -> str:
    """Get an AI-generated description of work performed"""
    if len(description) < 30:
        return "Please enter a longer description of work performed, so the AI can summarize and improve it."

    client = Anthropic()
    message = client.messages.create(
        model="claude-3-7-sonnet-20250219",
        max_tokens=1000,
        temperature=1,
        system="""You are a professional service writer for an oil and gas field service company. Improve the spelling, grammar, and clarity of the field technician's description below while maintaining approximately the same length. Use professional language but preserve the technical details and keep the description concise. Write in third person, past tense.""",
        messages=[
            {
                "role": "user",
                "content": [{"type": "text", "text": description}],
            }
        ],
    )
    return message.content[0].text


@callback(
    Output("work_order_work_done_ai_collapse", "is_open"),
    Output("work_order_work_done_ai", "value"),
    Output("work_order_work_done", "value", allow_duplicate=True),
    Input("work_order_fix_description_btn", "n_clicks"),
    Input("work_order_use_ai_description_btn", "n_clicks"),
    State("work_order_work_done_ai", "value"),
    State("work_order_work_done", "value"),
    prevent_initial_call=True,
)
def work_order_fix_description(
    work_order_fix_description_btn_n_clicks,
    work_order_use_ai_description_btn_n_clicks,
    work_order_work_done_ai_value,
    work_order_work_done_value,
):
    """Auto-fill the 'Work Done' field with AI-generated text"""
    if (
        not work_order_fix_description_btn_n_clicks
        and not work_order_use_ai_description_btn_n_clicks
    ):
        raise PreventUpdate()

    def return_vars(
        work_order_work_done_ai_collapse_is_open: bool = False,
        work_order_work_done_ai_value: str = "",
        work_order_work_done_value: str = "",
    ):
        """The default return tuple"""
        return (
            work_order_work_done_ai_collapse_is_open,
            work_order_work_done_ai_value,
            work_order_work_done_value,
        )

    id_triggered: str = get_id_triggered()

    if id_triggered == "work_order_use_ai_description_btn.n_clicks":
        return return_vars(
            # Hide the AI-generated text
            work_order_work_done_ai_collapse_is_open=False,
            # Clear this one
            work_order_work_done_ai_value="",
            # Set the value to the AI-generated text
            work_order_work_done_value=work_order_work_done_ai_value,
        )

    if id_triggered == "work_order_fix_description_btn.n_clicks":
        # Get the AI-generated text
        ai_description: str = get_ai_description(description=work_order_work_done_value)
        return return_vars(
            work_order_work_done_ai_collapse_is_open=True,
            work_order_work_done_ai_value=ai_description,
            work_order_work_done_value=no_update,
        )

    raise PreventUpdate()


def email_confirmation_modal():
    """Modal dialog for confirming email details before sending"""
    return dbc.Modal(
        [
            dbc.ModalHeader(dbc.ModalTitle("Send Work Order PDF by Email")),
            dbc.ModalBody(
                [
                    # Store work_order_id for the callback
                    dcc.Store(id="email_work_order_id_store", storage_type="memory"),
                    # From field (disabled, always current user)
                    dbc.Row(
                        [
                            dbc.Label("From:", width=2),
                            dbc.Col(
                                dbc.Input(
                                    id="email_from_input",
                                    type="text",
                                    disabled=True,
                                ),
                                width=10,
                            ),
                        ],
                        className="mb-3",
                    ),
                    # To field (dropdown, multiple)
                    dbc.Row(
                        [
                            dbc.Label("To:", width=2),
                            dbc.Col(
                                dcc.Dropdown(
                                    id="email_to_dropdown",
                                    multi=True,
                                    placeholder="Select recipients...",
                                ),
                                width=10,
                            ),
                        ],
                        className="mb-3",
                    ),
                    # CC field (dropdown, multiple)
                    dbc.Row(
                        [
                            dbc.Label("CC:", width=2),
                            dbc.Col(
                                dcc.Dropdown(
                                    id="email_cc_dropdown",
                                    multi=True,
                                    placeholder="Select CC recipients...",
                                ),
                                width=10,
                            ),
                        ],
                        className="mb-3",
                    ),
                    # Subject field
                    dbc.Row(
                        [
                            dbc.Label("Subject:", width=2),
                            dbc.Col(
                                dbc.Input(
                                    id="email_subject_input",
                                    type="text",
                                    placeholder="Email subject",
                                ),
                                width=10,
                            ),
                        ],
                        className="mb-3",
                    ),
                    # Body field (textarea for more space)
                    dbc.Row(
                        [
                            dbc.Label("Message:", width=2),
                            dbc.Col(
                                dbc.Textarea(
                                    id="email_body_textarea",
                                    placeholder="Email body",
                                    style={"height": "200px"},
                                ),
                                width=10,
                            ),
                        ],
                        className="mb-3",
                    ),
                    # Attachment info (disabled, just for info)
                    dbc.Row(
                        [
                            dbc.Label("Attachment:", width=2),
                            dbc.Col(
                                dbc.Input(
                                    id="email_attachment_input",
                                    type="text",
                                    disabled=True,
                                    placeholder="Work order PDF will be attached automatically",
                                ),
                                width=10,
                            ),
                        ],
                        className="mb-3",
                    ),
                    # Status display for errors/success
                    dbc.Row(
                        dbc.Col(
                            html.Div(
                                id="email_status_message", className="text-danger"
                            ),
                            width=12,
                        ),
                        className="mb-3",
                    ),
                ]
            ),
            dbc.ModalFooter(
                [
                    dbc.Row(
                        dbc.Col(
                            dbc.Button(
                                "Cancel",
                                id="email_cancel_button",
                                className="float-end",
                                color="secondary",
                                n_clicks=0,
                            ),
                            width=12,
                            class_name="d-flex justify-content-end",
                        ),
                        class_name="w-100 mb-1",
                    ),
                    dbc.Row(
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fas fa-paper-plane me-1"),
                                    "Send email from server (with PDF)",
                                ],
                                id="email_send_button",
                                className="float-end",
                                color="primary",
                                n_clicks=0,
                            ),
                            width=12,
                            class_name="d-flex justify-content-end",
                        ),
                        class_name="w-100 mb-1",
                    ),
                    dbc.Row(
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fas fa-paper-plane me-1"),
                                    "Send email from  your email app (attach your own PDF)",
                                ],
                                id="email_send_outlook_button",
                                className="float-end",
                                href="mailto:",
                                color="primary",
                                n_clicks=0,
                            ),
                            width=12,
                            class_name="d-flex justify-content-end",
                        ),
                        class_name="w-100 mb-1",
                    ),
                ],
                class_name="d-flex flex-column align-items-stretch",  # Override the default ModalFooter layout
            ),
        ],
        id="email_confirmation_modal",
        size="lg",
        is_open=False,
    )


def send_work_order_pdf_email(
    work_order_id: int,
    to_emails: List[str],
    subject: str,
    body: str,
    cc_emails: Optional[List[str]] = None,
) -> bool:
    """
    Generate a PDF for a work order and email it

    Args:
        work_order_id: ID of the work order
        to_email: Email to send to
        subject: Email subject line
        body: Email body text
        cc_emails: Optional list of CC recipients

    Returns:
        bool: True if successful, False otherwise
    """

    try:
        # Get work order
        work_order = db.session.get(WorkOrder, work_order_id)
        if not work_order:
            current_app.logger.error(f"Work order not found: {work_order_id}")
            return False

        # Get customer name
        customer_name: str = "Customer"
        if work_order.customers_rel:
            customer_name = work_order.customers_rel.customer

        # Create filename
        doc_type: str = "Quote" if work_order.is_quote else "Invoice"
        date_str: str = date.today().strftime("%Y-%m-%d")
        if work_order.date_service:
            date_str = work_order.date_service.strftime("%Y-%m-%d")

        body = (
            body
            or f"""Dear {customer_name},

Please find attached the IJACK {doc_type.lower()} #{work_order_id}.

If you have any questions or require any clarification, please don't hesitate to contact us.

Best regards,
IJACK Team
"""
        )
        # Generate PDF
        pdf_buffer = generate_work_order_pdf(work_order_id)
        # Reset file pointer to beginning
        pdf_buffer.seek(0)
        # Create filename
        filename = f"IJACK_{doc_type}_{customer_name.replace(' ', '_')}_{date_str}_{work_order_id}.pdf"

        # Read HTML template
        path_html: Path = TEMPLATES_FOLDER.joinpath("email/email_work_order_quote.html")
        with open(path_html, "r", encoding="utf-8") as f:
            # Render HTML template with work order data
            html_body: str = render_template_string(
                f.read(),
                work_order=work_order,
                customer_name=customer_name,
            )

        # Send email with attachment
        send_email(
            sender=f"{current_user.first_name} {current_user.last_name} <{current_user.email}>",
            to_emails=to_emails,
            cc_emails=cc_emails,
            subject=subject,
            text_body=body,
            html_body=html_body,
            files_list=[("attachment", (filename, pdf_buffer.read()))],
            send_all_together=True,
        )

        # Log successful email
        current_app.logger.info(
            f"Successfully sent work order PDF email for work order {work_order_id} to {to_emails}"
        )
        return True

    except Exception:
        current_app.logger.exception("Error sending work order PDF email")
        return False


# Callback to open the email modal and populate it with data
@callback(
    Output("email_confirmation_modal", "is_open"),
    Output("email_work_order_id_store", "data"),
    Output("email_from_input", "value"),
    Output("email_to_dropdown", "options"),
    Output("email_to_dropdown", "value"),
    Output("email_cc_dropdown", "options"),
    Output("email_cc_dropdown", "value"),
    Output("email_subject_input", "value"),
    Output("email_body_textarea", "value"),
    Output("email_attachment_input", "value"),
    Output("email_status_message", "children", allow_duplicate=True),
    Input("work_order_email_pdf_btn", "n_clicks"),
    State("edit_work_order_id", "data"),
    prevent_initial_call=True,
)
def open_email_modal(n_clicks, work_order_id):
    """Opens the email confirmation modal and populates it with recipient suggestions"""
    if not n_clicks or not work_order_id:
        raise PreventUpdate

    def return_vars(
        email_confirmation_modal_is_open: bool = False,
        email_work_order_id_store_data: str = None,
        email_from_input_value: str = "",
        email_to_dropdown_options: list = [],
        email_to_dropdown_value: list = [],
        email_cc_dropdown_options: list = [],
        email_cc_dropdown_value: list = [],
        email_subject_input_value: str = "",
        email_body_textarea_value: str = "",
        email_attachment_input_value: str = "",
        email_status_message_children: str = "",
    ):
        """The default return tuple"""
        return (
            email_confirmation_modal_is_open,
            email_work_order_id_store_data,
            email_from_input_value,
            email_to_dropdown_options,
            email_to_dropdown_value,
            email_cc_dropdown_options,
            email_cc_dropdown_value,
            email_subject_input_value,
            email_body_textarea_value,
            email_attachment_input_value,
            email_status_message_children,
        )

    try:
        # Get the work order from the database
        work_order = db.session.get(WorkOrder, work_order_id)
        if not work_order:
            # Could use a notification here instead, but for simplicity returning no_update
            raise PreventUpdate

        # Get customer name
        customer_name = "Customer"
        if work_order.customers_rel:
            customer_name = work_order.customers_rel.customer

        # Determine document type
        doc_type = "Quote" if work_order.is_quote else "Work Order"

        # Prepare email recipients
        to_emails = []
        cc_emails = []
        all_email_options = []  # For both dropdowns

        # Helper function to add an email to options list
        def add_email_option(email, name=None, relation=None):
            if not email:
                return

            # Create a label with name if available
            label = email
            if name:
                label = f"{name} <{email}>"
            if relation:
                label = f"{label} - {relation}"

            option = {"label": label, "value": email}
            if option not in all_email_options:
                all_email_options.append(option)

        # Who requested the work order (usually the customer)
        if work_order.requested_by_rel:
            requester = work_order.requested_by_rel
            if requester.email and requester.email not in to_emails:
                to_emails.append(requester.email)
                add_email_option(
                    requester.email,
                    f"{requester.first_name} {requester.last_name}",
                    "Requested by",
                )

        # Add the person whose signature is on the work order (usually the customer)
        if work_order.approval_person_rel:
            approver = work_order.approval_person_rel
            if approver.email and approver.email not in to_emails:
                to_emails.append(approver.email)
                add_email_option(
                    approver.email,
                    f"{approver.first_name} {approver.last_name}",
                    "Approver",
                )

        # Person who approves that this invoice gets sent out (usually Richie)
        if work_order.approved_by_rel:
            invoice_approver = work_order.approved_by_rel
            if (
                invoice_approver.email
                and invoice_approver.email not in to_emails
                and invoice_approver.email not in cc_emails
            ):
                cc_emails.append(invoice_approver.email)
                add_email_option(
                    invoice_approver.email,
                    f"{invoice_approver.first_name} {invoice_approver.last_name}",
                    "Invoice approver",
                )

        # Add the creator of the work order to CC
        if work_order.creator_rel:
            creator = work_order.creator_rel
            if (
                creator.email
                and creator.email not in to_emails
                and creator.email not in cc_emails
            ):
                cc_emails.append(creator.email)
                add_email_option(
                    creator.email,
                    f"{creator.first_name} {creator.last_name}",
                    "Creator",
                )

        # Add IJACK sales credit people to CC
        if work_order.users_sales_rel:
            for user in work_order.users_sales_rel:
                if (
                    user.email
                    and user.email not in to_emails
                    and user.email not in cc_emails
                ):
                    cc_emails.append(user.email)
                    add_email_option(
                        user.email, f"{user.first_name} {user.last_name}", "Sales"
                    )

        # Add IJACK field techs to CC
        if work_order.users_rel:
            for user in work_order.users_rel:
                if (
                    user.email
                    and user.email not in to_emails
                    and user.email not in cc_emails
                ):
                    cc_emails.append(user.email)
                    add_email_option(
                        user.email, f"{user.first_name} {user.last_name}", "Field Tech"
                    )

        # Add all other emails for both IJACK employees, and the customer
        users: list = (
            db.session.query(
                User.first_name,
                User.last_name,
                User.email,
            )
            .filter(
                or_(
                    User.customer_id == work_order.customer_id,
                    User.customer_id == CUSTOMER_ID_IJACK_INC,
                )
            )
            .all()
        )
        all_other_emails = [
            {
                "label": f"{user.first_name} {user.last_name} <{user.email}>",
                "value": user.email,
            }
            for user in users
            if user.email not in to_emails and user.email not in cc_emails
        ]
        # Add all other emails to the options list
        all_email_options.extend(all_other_emails)

        # Set the from email (current user)
        from_email = ""
        if current_user and hasattr(current_user, "email"):
            from_email = current_user.email
            if hasattr(current_user, "first_name") and hasattr(
                current_user, "last_name"
            ):
                from_email = f"{current_user.first_name} {current_user.last_name} <{current_user.email}>"

        # Prepare subject
        subject = f"IJACK {doc_type} #{work_order_id} for {customer_name}"

        # Create a professional email body
        body = f"""Dear {customer_name},

Please find attached the IJACK {doc_type.lower()} #{work_order_id}.

"""

        # Add additional information if available
        if work_order.date_service:
            service_date = work_order.date_service.strftime("%B %d, %Y")
            body += f"Service date: {service_date}\n"

        if work_order.location:
            body += f"Location: {work_order.location}\n"

        body += """
If you have any questions or require any clarification, please don't hesitate to contact us.

Best regards,
"""

        # Add current user's name if available
        if (
            current_user
            and hasattr(current_user, "first_name")
            and hasattr(current_user, "last_name")
        ):
            body += f"{current_user.first_name} {current_user.last_name}\n"

        body += "IJACK Team"

        # Create attachment info text
        doc_type_lower = "quote" if work_order.is_quote else "invoice"
        attachment_info = f"IJACK_{doc_type_lower}_{customer_name.replace(' ', '_')}"
        if work_order.date_service:
            date_str = work_order.date_service.strftime("%Y-%m-%d")
            attachment_info += f"_{date_str}"
        attachment_info += f"_{work_order_id}.pdf"
        # Return all the values to populate the modal
        return return_vars(
            email_confirmation_modal_is_open=True,
            email_work_order_id_store_data=work_order_id,
            email_from_input_value=from_email,
            email_to_dropdown_options=all_email_options,
            email_to_dropdown_value=to_emails,
            email_cc_dropdown_options=all_email_options,
            email_cc_dropdown_value=cc_emails,
            email_subject_input_value=subject,
            email_body_textarea_value=body,
            email_attachment_input_value=attachment_info,
        )

    except Exception:
        current_app.logger.exception("Error preparing email modal")
        raise PreventUpdate


# # =====================================
# # Form dirty tracking and finalize control callbacks
# # =====================================


# @callback(
#     Output("work_order_form_dirty_store", "data"),
#     Output("work_order_finalize_btn", "disabled", allow_duplicate=True),
#     Output("work_order_finalize_btn_form_text", "children", allow_duplicate=True),
#     Input("work_order_date_service", "value"),
#     Input("work_order_service_type", "value"),
#     Input("work_order_customer", "value"),
#     Input("work_order_users", "value"),
#     Input("work_order_users_sales", "value"),
#     Input("work_order_requested_by_dropdown", "value"),
#     Input("work_order_company_rep_dropdown", "value"),
#     Input("work_order_service_crew", "value"),
#     Input("work_order_location", "value"),
#     Input("work_order_model_types", "value"),
#     Input("work_order_service_required", "value"),
#     Input("work_order_work_done", "value"),
#     Input("work_order_customer_po", "value"),
#     Input("work_order_cust_work_order", "value"),
#     Input("work_order_afe", "value"),
#     Input("work_order_invoice_summary", "value"),
#     Input("work_order_structures", "value"),
#     Input("work_order_power_units", "value"),
#     Input("work_order_parts_table_ag", "virtualRowData"),
#     Input("work_order_discount_pct", "value"),
#     Input("work_order_is_warranty", "value"),
# #     Input("work_order_transfer_to_warehouse_switch", "value"),
# #     Input("work_order_submit_store", "data"),  # Reset dirty state when saved
#     State("work_order_approved", "value"),
#     State("edit_work_order_id", "data"),
#     prevent_initial_call=True,
# )
# def track_form_changes_and_finalize_state(
#     date_service,
#     service_type,
#     customer,
#     users,
#     users_sales,
#     requested_by,
#     company_rep,
#     service_crew,
#     location,
#     model_types,
#     service_required,
#     work_done,
#     customer_po,
#     cust_work_order,
#     afe,
#     invoice_summary,
#     structures,
#     power_units,
#     parts_table_data,
#     discount_pct,
#     is_warranty,
# #     transfer_switch,
# #     submit_store_data,
#     work_order_approved_value,
#     edit_work_order_id_data,
# ):
#     """
#     Track form changes to determine if there are unsaved modifications.
#     Control the finalize button disabled state and tooltip text.
#     """
#     id_triggered: str = get_id_triggered()

#     def return_vars(
#         form_dirty: bool = False,
#         finalize_disabled: bool = True,
#         finalize_tooltip: str = "",
#     ):
#         """Return callback outputs"""
#         return form_dirty, finalize_disabled, finalize_tooltip

#     # If the work order was just saved, reset dirty state
#     if id_triggered == "work_order_submit_store.data" and submit_store_data:
#         return return_vars(
#             form_dirty=False,
#             finalize_disabled=not work_order_approved_value
#             if edit_work_order_id_data
#             else True,
#             finalize_tooltip="Work order has been saved"
#             if edit_work_order_id_data
#             else "Please save the work order first",
#         )

#     # If any form field changed, mark as dirty
#     form_is_dirty = True  # Any input change means form is dirty

#     # Determine finalize button state
#     if not edit_work_order_id_data:
#         # New work order - must save first
#         finalize_disabled = True
#         finalize_tooltip = "Please save the work order first"
#     elif work_order_approved_value:
#         # Already approved
#         finalize_disabled = True
#         finalize_tooltip = "Work order is already approved and finalized"
#     elif form_is_dirty:
#         # Has unsaved changes
#         finalize_disabled = True
#         finalize_tooltip = "Please save changes before finalizing"
#     else:
#         # Can be finalized
#         finalize_disabled = False
#         finalize_tooltip = "Ready to finalize"

#     return return_vars(
#         form_dirty=form_is_dirty,
#         finalize_disabled=finalize_disabled,
#         finalize_tooltip=finalize_tooltip,
#     )


# @callback(
#     Output("work_order_finalize_warning_toast", "children"),
#     Input("work_order_finalize_btn", "n_clicks"),
#     State("work_order_form_dirty_store", "data"),
#     prevent_initial_call=True,
# )
# def show_unsaved_changes_warning(finalize_btn_clicks, form_dirty_state):
#     """
#     Show a toast warning when user tries to finalize with unsaved changes.
#     This provides immediate feedback before the modal logic runs.
#     """
#     if not finalize_btn_clicks:
#         raise PreventUpdate

#     if form_dirty_state:
#         return dmc_notification(
#             "You have unsaved changes. Please save the work order before finalizing.",
#             "Cannot Finalize",
#             color="orange",
#             position="top-center",
#             autoClose=5000,
#         )

#     return no_update


# =====================================
# End of form dirty tracking callbacks
# =====================================


# Callback to close the modal when Cancel is clicked
@callback(
    Output("email_confirmation_modal", "is_open", allow_duplicate=True),
    Input("email_cancel_button", "n_clicks"),
    prevent_initial_call=True,
)
def close_email_modal(n_clicks):
    if n_clicks:
        return False
    raise PreventUpdate


@callback(
    Output("email_send_outlook_button", "href"),
    Input("email_to_dropdown", "value"),
    Input("email_cc_dropdown", "value"),
    Input("email_subject_input", "value"),
    Input("email_body_textarea", "value"),
    prevent_initial_call=True,
)
def update_outlook_email_link(to_emails, cc_emails, subject, body):
    """Update the Outlook email link with the subject, body, to, and CC"""
    # Replace /n in body with %0A for line breaks in the email
    body = body.replace("\n", "%0A")
    # Make a link with subject, body, to, and CC
    send_with_outlook_href: str = f"mailto:?subject={subject}&body={body}&to={','.join(to_emails)}&cc={','.join(cc_emails)}"
    return send_with_outlook_href


# Callback to handle the email sending process
@callback(
    Output("email_status_message", "children", allow_duplicate=True),
    Output("email_send_button", "disabled"),
    Output("email_confirmation_modal", "is_open", allow_duplicate=True),
    Output("work_order_dmc_notifications", "children", allow_duplicate=True),
    Input("email_send_button", "n_clicks"),
    Input("email_send_outlook_button", "n_clicks"),
    State("email_work_order_id_store", "data"),
    State("email_to_dropdown", "value"),
    State("email_cc_dropdown", "value"),
    State("email_subject_input", "value"),
    State("email_body_textarea", "value"),
    prevent_initial_call=True,
)
def send_email_callback(
    email_send_button_n_clicks,
    email_send_outlook_button_n_clicks,
    work_order_id,
    to_emails,
    cc_emails,
    subject,
    body,
):
    """Sends the email with the work order PDF attached"""
    if not email_send_button_n_clicks and not email_send_outlook_button_n_clicks:
        raise PreventUpdate

    def return_vars(
        email_status_message_children: str = "",
        email_send_button_disabled: bool = False,
        email_confirmation_modal_is_open: bool = False,
        work_order_dmc_notifications_children: str = no_update,
    ):
        """The default return tuple"""
        return (
            email_status_message_children,
            email_send_button_disabled,
            email_confirmation_modal_is_open,
            work_order_dmc_notifications_children,
        )

    if not work_order_id:
        return return_vars(
            email_status_message_children="Work order ID not found",
            email_send_button_disabled=False,
            email_confirmation_modal_is_open=no_update,
        )

    if not to_emails or len(to_emails) == 0:
        return return_vars(
            email_status_message_children="Please select at least one recipient",
            email_send_button_disabled=False,
            email_confirmation_modal_is_open=no_update,
        )

    if not subject:
        return return_vars(
            email_status_message_children="Please enter a subject",
            email_send_button_disabled=False,
            email_confirmation_modal_is_open=no_update,
        )

    if not body:
        return return_vars(
            email_status_message_children="Please enter a message body",
            email_send_button_disabled=False,
            email_confirmation_modal_is_open=no_update,
        )

    try:
        id_triggered: str = get_id_triggered()
        if id_triggered == "email_send_outlook_button.n_clicks":
            return return_vars(
                email_status_message_children="Email will be sent from your email app",
                email_send_button_disabled=False,
                email_confirmation_modal_is_open=False,
                # Show a notification instead of message inside modal,
                # since we're closing the modal
                work_order_dmc_notifications_children=dmc_notification(
                    "Email will be sent from your email app",
                    "Success!",
                    color="green",
                    position="bottom-center",
                    autoClose=10_000,
                ),
            )

        # Send the email using the provided function
        success: bool = send_work_order_pdf_email(
            work_order_id=work_order_id,
            to_emails=to_emails,
            cc_emails=cc_emails if cc_emails else None,
            subject=subject,
            body=body,
        )

        if success:
            # Close the modal
            return return_vars(
                email_status_message_children="Email sent successfully",
                email_send_button_disabled=True,
                email_confirmation_modal_is_open=False,
                # Show a notification instead of message inside modal,
                # since we're closing the modal
                work_order_dmc_notifications_children=dmc_notification(
                    "Email sent successfully",
                    "Success!",
                    color="green",
                    position="bottom-center",
                    autoClose=10_000,
                ),
            )
        else:
            return return_vars(
                email_status_message_children="Failed to send email. Please check the logs for details.",
                email_send_button_disabled=False,
                email_confirmation_modal_is_open=no_update,
            )

    except Exception as e:
        error_msg: str = f"Error sending email: {str(e)}"
        current_app.logger.error(error_msg)
        return return_vars(
            email_status_message_children=error_msg,
            email_send_button_disabled=False,
            email_confirmation_modal_is_open=no_update,
        )


@callback(
    Output("work_order_modal", "is_open", allow_duplicate=True),
    Output("work_order_modal_title", "children"),
    Output("work_order_modal_title", "style"),
    Output("work_order_modal_body", "children"),
    Output("work_order_modal_view_all_btn", "class_name"),
    Output("work_order_modal_create_new_btn", "children"),
    Output("store_work_order_id_to_clone", "data"),
    Output("work_order_modal_clone_card", "style"),
    Output("edit_work_order_id", "data", allow_duplicate=True),
    # Output("work_order_approved", "disabled", allow_duplicate=True),
    # Output("work_order_is_quote", "disabled", allow_duplicate=True),
    # Output("work_order_finalize_btn", "disabled", allow_duplicate=True),
    Input("work_order_submit_store", "data"),
    # Form values
    State("work_order_approved", "value"),
    State("work_order_date_service", "value"),
    State("work_order_service_type", "value"),
    State("work_order_currency_id", "value"),
    State("work_order_creator_company_id", "data"),
    State("work_order_customer", "value"),
    State("work_order_users", "value"),
    State("work_order_users_sales", "value"),
    State("work_order_requested_by_dropdown", "value"),
    State("work_order_service_crew", "value"),
    State("work_order_company_rep_dropdown", "value"),
    State("work_order_location", "value"),
    State("work_order_model_types", "value"),
    State("work_order_service_required", "value"),
    State("work_order_is_warranty", "value"),
    State("work_order_work_done", "value"),
    State("work_order_customer_po", "value"),
    State("work_order_cust_work_order", "value"),
    State("work_order_afe", "value"),
    State("work_order_invoice_summary", "value"),
    State("work_order_structures", "value"),
    State("work_order_structure_slave", "value"),
    State("work_order_power_units", "value"),
    State("work_order_province_id", "value"),
    State("work_order_zip_code_id", "value"),
    State("work_order_sales_tax_rate", "value"),
    State("work_order_gst_rate", "value"),
    State("work_order_pst_rate", "value"),
    State("work_order_county_id", "value"),
    State("work_order_city_id", "value"),
    State("work_order_subtotal_store", "data"),
    State("work_order_discount_pct", "value"),
    State("work_order_subtotal_after_discount_store", "data"),
    State("work_order_sales_tax_store", "data"),
    State("work_order_gst_amount_store", "data"),
    State("work_order_pst_amount_store", "data"),
    State("work_order_total_store", "data"),
    State("work_order_parts_table_ag", "virtualRowData"),
    State("edit_work_order_id", "data"),
    State("work_order_file_upload_data_table", "data"),
    # State("work_order_transfer_to_warehouse_switch", "value"),
    # State("work_order_transfer_to_warehouse_dropdown", "value"),
    State("signature_store", "data"),
    State("work_order_signature_name", "value"),
    State("work_order_signature_date", "date"),
    State("work_order_country_id", "value"),
    State("work_order_is_quote", "value"),
    State("work_order_is_tax_exempt", "value"),
    prevent_initial_call=True,
)
def work_order_form_submit(
    work_order_submit_store_data,
    work_order_approved_value,
    work_order_date_service_value,
    work_order_service_type_value,
    work_order_currency_id_value,
    work_order_creator_company_id_data,
    work_order_customer_value,
    work_order_users_value,
    work_order_users_sales_value,
    work_order_requested_by_dropdown_value,
    work_order_service_crew_value,
    work_order_company_rep_dropdown_value,
    work_order_location_value,
    work_order_model_types_value,
    work_order_service_required_value,
    work_order_is_warranty_value,
    work_order_work_done_value,
    work_order_customer_po_value,
    work_order_cust_work_order_value,
    work_order_afe_value,
    work_order_invoice_summary_value,
    work_order_structures_value,
    work_order_structure_slave_value,
    work_order_power_units_value,
    work_order_province_id_value,
    work_order_zip_code_id_value,
    work_order_sales_tax_rate_value,
    work_order_gst_rate_value,
    work_order_pst_rate_value,
    work_order_county_id_value,
    work_order_city_id_value,
    work_order_subtotal_store_data,
    work_order_discount_pct_value,
    work_order_subtotal_after_discount_store_data,
    work_order_sales_tax_store_data,
    work_order_gst_amount_store_data,
    work_order_pst_amount_store_data,
    work_order_total_store_data,
    work_order_parts_table_ag_rowData,
    edit_work_order_id_data,
    # Upload files
    work_order_file_upload_data_table_data,
    # # Transfer to warehouse
    # work_order_transfer_to_warehouse_switch_value,
    # work_order_transfer_to_warehouse_dropdown_value,
    # Signature
    signature_store_data,
    work_order_signature_name_value,
    work_order_signature_date_date,
    work_order_country_id_value,
    work_order_is_quote_value,
    work_order_is_tax_exempt_value,
):
    """Process the submitted work order form"""

    if not work_order_submit_store_data:
        raise PreventUpdate()

    def return_vars(
        modal_open: bool = True,
        title: str = "Success!",
        work_order_modal_title_style: dict = {},
        body: str = "Work order created.",
        work_order_modal_view_all_btn_class_name: str = "",
        work_order_modal_create_new_btn_children: str = "Create New Work Order",
        store_work_order_id_to_clone_data: Optional[str] = None,
        work_order_modal_clone_card_style: Optional[str] = no_update,
        edit_work_order_id_data: Optional[str] = no_update,
        # work_order_approved_disabled: Optional[bool] = no_update,
        # work_order_is_quote_disabled: Optional[bool] = no_update,
        # work_order_finalize_btn_disabled: Optional[bool] = no_update,
    ) -> tuple:
        """The default return tuple"""
        return (
            modal_open,
            title,
            work_order_modal_title_style,
            body,
            work_order_modal_view_all_btn_class_name,
            work_order_modal_create_new_btn_children,
            store_work_order_id_to_clone_data,
            work_order_modal_clone_card_style,
            edit_work_order_id_data,
            # work_order_approved_disabled,
            # work_order_is_quote_disabled,
            # work_order_finalize_btn_disabled,
        )

    def error_try_again(body: str, rollback: bool = False) -> tuple:
        """Return an error message and don't close the modal"""
        if rollback:
            # Database transactions may have failed, so rollback
            db.session.rollback()
        return return_vars(
            modal_open=True,
            title="ERROR: Try Again!!",
            work_order_modal_title_style=MODAL_TITLE_ERROR_STYLE,
            body=body,
            # don't show the first button
            work_order_modal_view_all_btn_class_name=CLASS_NAME_DISPLAY_NONE,
            work_order_modal_create_new_btn_children="Close and try again",
        )

    try:
        users = []
        if not work_order_users_value:
            return error_try_again("Please select at least one IJACK field technician.")
        else:
            users = User.query.where(
                User.id.in_(ensure_list(work_order_users_value))
            ).all()

        users_sales = []
        if not work_order_users_sales_value:
            return error_try_again(
                "Please select at least one IJACK person to get credit for the sales revenue."
            )
        else:
            users_sales = User.query.where(
                User.id.in_(ensure_list(work_order_users_sales_value))
            ).all()

        if (
            work_order_country_id_value == COUNTRY_ID_USA
            and not work_order_is_tax_exempt_value
            and not work_order_zip_code_id_value
        ):
            return error_try_again(
                "Please select a ZIP code for accounting/invoicing purposes."
            )

        if not work_order_company_rep_dropdown_value:
            # This person is responsible for approving the quote on behalf of the customer
            return error_try_again(
                "Please select the company approval person, who will be responsible for approving this quote on behalf of the customer."
            )

        # if work_order_transfer_to_warehouse_switch_value:
        #     if not work_order_transfer_to_warehouse_dropdown_value:
        #         return error_try_again(
        #             "You chose 'Transfer to Warehouse' but no warehouse was selected."
        #         )

        if signature_store_data and not work_order_signature_name_value:
            return error_try_again(
                "Please enter your name in the signature field. This is required to sign the work order."
            )

        structures = []
        if work_order_structures_value:
            structures = Structure.query.where(
                Structure.id.in_(ensure_list(work_order_structures_value))
            ).all()

        power_units = []
        if work_order_power_units_value:
            power_units = PowerUnit.query.where(
                PowerUnit.id.in_(ensure_list(work_order_power_units_value))
            ).all()

        # unit_types = [db.session.get(UnitType, id) for id in work_order_unit_type_value]
        model_types = []
        if work_order_model_types_value:
            model_types = ModelType.query.where(
                ModelType.id.in_(ensure_list(work_order_model_types_value))
            ).all()

        work_order = None
        if edit_work_order_id_data:
            # Editing an existing work order
            work_order = db.session.get(WorkOrder, edit_work_order_id_data)
        if work_order:
            # Update the existing work order's timestamp
            work_order.timestamp_utc_updated = utcnow_naive()
        else:
            work_order = WorkOrder(
                # Person who filled out the work order form
                creator_id=getattr(current_user, "id", None),
                timestamp_utc_inserted=utcnow_naive(),
                timestamp_utc_updated=utcnow_naive(),
                # Approved for invoice creation yet? (typically Richie approves it)
                # invoice_approval_req=False,
                approved_by_id=USER_ID_RICHIE,
            )
            db.session.add(work_order)  # add the new work order

            # New work order, so don't use the existing status ID
            work_order.status_id = (
                WORK_ORDER_STATUS_ID_WARRANTY if work_order_is_warranty_value else None
            )

        work_order.date_service = work_order_date_service_value  # required
        work_order.service_type_id = work_order_service_type_value  # required
        work_order.currency_id = work_order_currency_id_value  # required
        work_order.creator_company_id = work_order_creator_company_id_data  # required
        work_order.country_id = work_order_country_id_value  # required
        work_order.customer_id = work_order_customer_value  # required
        # IJACK field technicians
        work_order.users_rel = users
        # IJACK sales people
        work_order.users_sales_rel = users_sales
        work_order.requested_by_id = work_order_requested_by_dropdown_value
        work_order.approval_person_id = work_order_company_rep_dropdown_value
        work_order.service_crew = work_order_service_crew_value
        work_order.location = work_order_location_value
        work_order.model_types_rel = model_types
        work_order.service_required = work_order_service_required_value
        work_order.is_warranty = work_order_is_warranty_value  # required
        work_order.work_done = work_order_work_done_value
        work_order.customer_po = work_order_customer_po_value
        work_order.cust_work_order = work_order_cust_work_order_value
        work_order.afe = work_order_afe_value
        work_order.invoice_summary = work_order_invoice_summary_value
        work_order.structures_rel = structures
        work_order.structure_slave = work_order_structure_slave_value
        work_order.power_units_rel = power_units
        work_order.subtotal = work_order_subtotal_store_data
        # Discount percent is stored as a number between 0 and 100, not between 0 and 1
        work_order.discount_pct = work_order_discount_pct_value
        work_order.subtotal_after_discount = (
            work_order_subtotal_after_discount_store_data
        )
        work_order.is_tax_exempt = work_order_is_tax_exempt_value
        # The province select dropdown is for selecting the sales tax ID
        work_order.province_id = work_order_province_id_value
        work_order.county_id = work_order_county_id_value
        work_order.city_id = work_order_city_id_value
        work_order.zip_code_id = work_order_zip_code_id_value

        # Handle Canadian tax rates (GST and PST) when appropriate
        if work_order_country_id_value == COUNTRY_ID_CANADA:
            # For Canadian provinces, we'll set GST and PST rates for Canadian provinces
            work_order.gst_rate = get_decimal(work_order_gst_rate_value)
            work_order.pst_rate = get_decimal(work_order_pst_rate_value)
            work_order.sales_tax_rate = work_order.gst_rate + work_order.pst_rate
        else:
            # For US work orders, just use the combined rate
            work_order.gst_rate = None
            work_order.pst_rate = None
            # Sales tax rate is stored as a number between 0 and 100, not between 0 and 1
            work_order.sales_tax_rate = work_order_sales_tax_rate_value

        work_order.sales_tax = work_order_sales_tax_store_data
        work_order.gst_amount = work_order_gst_amount_store_data
        work_order.pst_amount = work_order_pst_amount_store_data
        work_order.total = work_order_total_store_data

        # Capture old inventory state for transition detection BEFORE setting new values
        old_is_quote = None
        old_invoice_approval_req = None
        if edit_work_order_id_data:
            # For existing work orders, capture the current state BEFORE we modify it
            old_is_quote = getattr(work_order, "is_quote", None)
            old_invoice_approval_req = getattr(work_order, "invoice_approval_req", None)
            current_app.logger.info(
                f"Editing existing work order {work_order.id}: old_is_quote={old_is_quote}, "
                f"old_invoice_approval_req={old_invoice_approval_req}"
            )
        else:
            # For new work orders, old state should be None
            current_app.logger.info(
                "Creating new work order: old_is_quote=None, old_invoice_approval_req=None"
            )

        # Remove any existing parts, or uploaded files, before we re-add them below
        work_order.work_order_parts_rel = []
        work_order.work_order_upload_files_rel = []
        # This is a Base64 string, which is what we want
        work_order.signature_svg = signature_store_data
        work_order.signature_name = work_order_signature_name_value

        # Set signature timestamp when signature data exists
        if signature_store_data and work_order_signature_date_date:
            try:
                # Parse the signature date and convert to datetime with current time
                work_order.signature_date = datetime.strptime(
                    work_order_signature_date_date, "%Y-%m-%d"
                )
            except (ValueError, TypeError):
                # If date parsing fails, use current timestamp
                work_order.signature_date = date.today()
        elif signature_store_data:
            # If there's signature data but no date, use current timestamp
            work_order.signature_date = date.today()
        else:
            # No signature data, clear the timestamp
            work_order.signature_date = None

        # Set the new values for state transition
        work_order.is_quote = work_order_is_quote_value
        work_order.invoice_approval_req = work_order_approved_value

        # Log the transition for debugging
        current_app.logger.info(
            f"Work order {getattr(work_order, 'id', 'NEW')} state transition: "
            f"is_quote {old_is_quote} -> {work_order_is_quote_value}, "
            f"invoice_approval_req {old_invoice_approval_req} -> {work_order_approved_value}"
        )

        # if (
        #     work_order_transfer_to_warehouse_switch_value
        #     and work_order_transfer_to_warehouse_dropdown_value
        # ):
        #     work_order.warehouse_id = work_order_transfer_to_warehouse_dropdown_value

        # session.flush() communicates a series of operations to the database (insert, update, delete).
        # The database maintains them as pending operations in a transaction.
        # The changes aren't persisted permanently to disk, or visible to other transactions
        # until the database receives a COMMIT for the current transaction (which is what session.commit() does).
        db.session.flush()

        # At this point, the object f has been pushed to the DB,
        # and has been automatically assigned a unique primary key id.
        # Refresh updates given object in the session with its state in the DB
        # (and can also only refresh certain attributes - search for documentation)
        db.session.refresh(work_order)

        i: int
        row: dict
        if not work_order_parts_table_ag_rowData:
            # No parts table data provided, so return an error
            return error_try_again(
                body="Please add at least one part to the work order parts table."
            )

        for i, row in enumerate(work_order_parts_table_ag_rowData):
            if all([not val for _, val in row.items()]):
                # Blank row
                continue

            # Define required fields and their error messages
            required_fields = {
                # It's displayed as the part_num string, but stored as the part_id
                "part_id": "part number",
                "description": "description",
                "structure_id": "structure",
                "warehouse_id": "warehouse",
                "field_tech_id": "field tech",
                # NOTE: We're not checking for quantity and price here
                # because they're checked separately below
                "quantity": "quantity",
                "price": "price",
            }

            # Check each required field
            for field, name in required_fields.items():
                # Check for None or empty string, but not for zero. Zero is fine sometimes for quantity and price.
                if row.get(field, None) in (None, ""):
                    return error_try_again(
                        body=f"A {name} must be included on parts table line item {i + 1}.",
                        rollback=True,
                    )

            # We're going to remove these parts from the warehouse
            # NOTE: It's displayed as the part_num string, but stored as the part_id!
            part_id = row.get("part_id", None)
            warehouse_id = row.get("warehouse_id", None)
            warehouse_to_id = row.get("warehouse_to_id", None)
            warehouse_to_id = (
                None if str(warehouse_to_id).lower() == "none" else warehouse_to_id
            )
            quantity: float = float(row.get("quantity", 1.0))
            description = row.get("description", None)
            structure_id = row.get("structure_id", None)
            field_tech_id = row.get("field_tech_id", None)
            price = row.get("price", None)

            # Log warehouse_to_id value for debugging
            current_app.logger.info(
                f"Creating WorkOrderPart for row {i + 1}: "
                f"part_id={part_id}, warehouse_id={warehouse_id}, "
                f"warehouse_to_id raw value from row={row.get('warehouse_to_id', None)}, "
                f"warehouse_to_id after processing={warehouse_to_id}, "
                f"quantity={quantity}"
            )

            part = WorkOrderPart(
                work_order_id=work_order.id,
                part_id=part_id,
                description=description,
                structure_id=structure_id or None,
                warehouse_id=warehouse_id or None,
                warehouse_to_id=warehouse_to_id or None,
                field_tech_id=field_tech_id,
                quantity=quantity,
                price=price,
                # sales_tax_rate is for Michelle
                # Sales tax rate is stored as a number between 0 and 100, not between 0 and 1
                sales_tax_rate=work_order_sales_tax_rate_value,
                timestamp_utc_inserted=utcnow_naive(),
            )
            work_order.work_order_parts_rel.append(part)

        if work_order_file_upload_data_table_data:
            # # files list for email attachments
            # files_list = []
            file_dict: dict
            for file_dict in work_order_file_upload_data_table_data:
                # Change contents to BYTEA for database
                file_bytes = base64.b64decode(file_dict["file_bytes"])
                file_model = WorkOrderUploadFile(
                    timestamp_utc_inserted=datetime.now(timezone.utc).replace(
                        tzinfo=None
                    ),
                    work_order_id=work_order.id,
                    file_name=file_dict["file_name"],
                    file_type=file_dict["file_type"],
                    file_bytes=file_bytes,
                )
                work_order.work_order_upload_files_rel.append(file_model)

                # # Add to files list for email attachments
                # files_list.append(("attachment", (file_dict["file_name"], file_bytes)))

        # Handle inventory operations using the new clean inventory manager
        try:
            # Get user ID
            user_id = getattr(current_user, "id", None)
            if not user_id:
                raise ValueError("No user ID available for inventory operations")

            # Initialize inventory manager
            inventory_manager = InventoryManager(db.session)

            # Determine old and new states
            old_state = None
            if old_is_quote is not None:
                # Create a simple object to determine old state
                class OldWorkOrder:
                    def __init__(self, is_quote, invoice_approval_req):
                        self.is_quote = is_quote
                        self.invoice_approval_req = invoice_approval_req

                old_wo = OldWorkOrder(old_is_quote, old_invoice_approval_req)
                old_state = get_work_order_state(old_wo)

            # Update work order with new values for state determination
            work_order.is_quote = work_order_is_quote_value
            work_order.invoice_approval_req = work_order_approved_value
            new_state = get_work_order_state(work_order)

            # Handle inventory state change
            inventory_result = inventory_manager.handle_state_change(
                work_order=work_order,
                old_state=old_state,
                new_state=new_state,
                user_id=user_id,
            )

            # Check result
            if not inventory_result.success:
                error_msg = f"Inventory operations failed for work order {work_order.id}: {inventory_result.errors}"
                current_app.logger.error(error_msg)
                raise Exception(error_msg)

            # Log success and any warnings
            current_app.logger.info(
                f"Inventory operations completed for WO {work_order.id}: {inventory_result.message}"
            )

            for warning in inventory_result.warnings:
                current_app.logger.warning(
                    f"Inventory warning for WO {work_order.id}: {warning}"
                )

        except Exception as inventory_err:
            current_app.logger.exception(
                f"Error in inventory operations for work order {work_order.id}"
            )
            # Re-raise to trigger the main exception handler
            raise inventory_err

    except Exception as err:
        current_app.logger.exception("Problem creating work order record")
        db.session.rollback()
        return return_vars(
            modal_open=True,
            title="ERROR",
            work_order_modal_title_style=MODAL_TITLE_ERROR_STYLE,
            body=f"Error saving work order to database. Here's the error: \n\n{err}",
            # don't show the first button
            work_order_modal_view_all_btn_class_name=CLASS_NAME_DISPLAY_NONE,
            work_order_modal_create_new_btn_children="Close and try again",
        )
    else:
        db.session.commit()
        current_app.logger.info(
            f"Work order {work_order.id} created/updated successfully by user {current_user.id}"
        )

    # Generate the inventory report for transparency and troubleshooting
    inventory_report = generate_inventory_report(work_order.id)

    # Open the modal
    if edit_work_order_id_data:
        return return_vars(
            modal_open=True,
            title="Success!",
            body=[html.P("Work order edited"), inventory_report],
            work_order_modal_create_new_btn_children="Create New Work Order",
            edit_work_order_id_data=str(work_order.id),
            # # If the work order is approved, disable the approval and quote buttons
            # work_order_approved_disabled=work_order.invoice_approval_req,
            # work_order_is_quote_disabled=work_order.invoice_approval_req,
            # work_order_finalize_btn_disabled=False,
            # work_order_finalize_store_data=work_order_finalize_store_data,
        )

    modal_body = [html.P("Work order created"), inventory_report]
    store_work_order_id_to_clone_data: str = None
    work_order_modal_clone_card_style = no_update
    if work_order_creator_company_id_data == CUSTOMER_ID_IJACK_CORP:
        # Duplicate the work order so IJACK Corp can buy the stuff from IJACK Inc 🍁
        store_work_order_id_to_clone_data = str(work_order.id)
        work_order_modal_clone_card_style = {}

    return return_vars(
        modal_open=True,
        title="Success!",
        body=modal_body,
        work_order_modal_create_new_btn_children="Create New Work Order",
        store_work_order_id_to_clone_data=store_work_order_id_to_clone_data,
        work_order_modal_clone_card_style=work_order_modal_clone_card_style,
        edit_work_order_id_data=str(work_order.id),
        # # If the work order is approved, disable the approval and quote buttons
        # work_order_approved_disabled=work_order.invoice_approval_req,
        # work_order_is_quote_disabled=work_order.invoice_approval_req,
        # work_order_finalize_btn_disabled=False,
        # work_order_finalize_store_data=work_order_finalize_store_data,
    )


# @callback(
#     Output("work_order_finalize_confirmation_modal", "is_open", allow_duplicate=True),
#     Output("work_order_dmc_notifications", "children", allow_duplicate=True),
#     Input("work_order_finalize_btn", "n_clicks"),
#     Input("work_order_finalize_cancel_btn", "n_clicks"),
#     Input("work_order_finalize_confirm_btn", "n_clicks"),
#     State("work_order_form_dirty_store", "data"),
#     State("work_order_approved", "value"),
#     prevent_initial_call=True,
# )
# def open_finalize_confirmation_modal_with_save_check(
#     finalize_clicks,
#     cancel_clicks,
#     confirm_clicks,
#     form_dirty_state,
#     work_order_approved_value,
# ):
#     """
#     Open the finalize confirmation modal when finalize button is clicked,
#     but only if there are no unsaved changes. Otherwise show warning.
#     """
#     id_triggered: str = get_id_triggered()

#     def return_vars(modal_open: bool = False, notification_children=no_update):
#         """Return callback outputs"""
#         return modal_open, notification_children

#     if id_triggered == "work_order_finalize_btn.n_clicks":
#         # Check for unsaved changes before opening modal
#         if form_dirty_state:
#             return return_vars(
#                 modal_open=False,
#                 notification_children=dmc_notification(
#                     "Please save your changes before finalizing the work order.",
#                     "Unsaved Changes",
#                     color="orange",
#                     position="top-center",
#                     autoClose=5000,
#                 ),
#             )

#         # Check if already approved
#         if work_order_approved_value:
#             return return_vars(
#                 modal_open=False,
#                 notification_children=dmc_notification(
#                     "This work order has already been approved and finalized.",
#                     "Already Finalized",
#                     color="blue",
#                     position="top-center",
#                     autoClose=3000,
#                 ),
#             )

#         # Open the confirmation modal - no unsaved changes
#         return return_vars(modal_open=True)

#     elif id_triggered in (
#         "work_order_finalize_cancel_btn.n_clicks",
#         "work_order_finalize_confirm_btn.n_clicks",
#     ):
#         # Close the modal
#         return return_vars(modal_open=False)

#     return return_vars(modal_open=False)


# @callback(
#     Output("work_order_dmc_notifications", "children", allow_duplicate=True),
#     Output("work_order_approved", "value", allow_duplicate=True),
#     Output("work_order_finalize_btn", "disabled", allow_duplicate=True),
#     Output("work_order_save_btn", "disabled", allow_duplicate=True),
#     Input("work_order_finalize_confirm_btn", "n_clicks"),
#     # State("work_order_finalize_store", "data"),
#     State("edit_work_order_id", "data"),
#     prevent_initial_call=True,
# )
# def handle_work_order_finalization(
#     work_order_finalize_confirm_btn_n_clicks,
#     # work_order_finalize_store_data,
#     edit_work_order_id_data,
# ):
#     """
#     Handle the actual work order finalization when the confirmation button is clicked.
#     Sets invoice_approval_req to True and approved_by to current user.
#     """
#     if not work_order_finalize_confirm_btn_n_clicks:
#         raise PreventUpdate

#     id_triggered: str = get_id_triggered()
#     if id_triggered != "work_order_finalize_confirm_btn.n_clicks":
#         raise PreventUpdate

#     def return_vars(
#         work_order_dmc_notifications_children: dmc.Notification = no_update,
#         work_order_approved_value: bool = no_update,
#         work_order_finalize_btn_disabled: bool = no_update,
#         work_order_save_btn_disabled: bool = no_update,
#     ) -> tuple:
#         """Return the output variables"""
#         return (
#             work_order_dmc_notifications_children,
#             work_order_approved_value,
#             work_order_finalize_btn_disabled,
#             work_order_save_btn_disabled,
#         )

#     # Get the work order
#     work_order = db.session.get(WorkOrder, edit_work_order_id_data)
#     if not work_order:
#         return return_vars(
#             work_order_dmc_notifications_children=dmc_notification(
#                 "Work order not found",
#                 "Error",
#                 color="red",
#                 position="bottom-center",
#                 autoClose=10_000,
#             )
#         )

#     try:
#         # Set the work order as approved/finalized
#         work_order.invoice_approval_req = True
#         work_order.approved_by_id = current_user.id

#         try:
#             # fulfillment_result: Dict[str, Any] = fulfill_work_order(
#             #     db_session=db.session,
#             #     work_order=work_order,
#             #     user_id=getattr(current_user, "id", None),
#             #     partial_fulfillment=False,
#             #     commit=False,  # We'll commit later
#             # )
#             fulfillment_result: Dict[str, Any] = create_work_order_movements(
#                 db_session=db.session,
#                 work_order=work_order,
#                 user_id=getattr(current_user, "id", None),
#                 commit=False,  # We'll commit later
#             )
#             if fulfillment_result.get("success"):
#                 current_app.logger.info(
#                     f"Successfully fulfilled work order {work_order.id} with {len(fulfillment_result.get('fulfilled_parts', []))} parts"
#                 )
#             else:
#                 current_app.logger.error(
#                     f"Failed to fulfill work order {work_order.id}: "
#                     f"{fulfillment_result.get('errors', [])}"
#                 )
#                 raise InventoryError(
#                     f"Failed to fulfill work order {work_order.id}: "
#                     f"{fulfillment_result.get('errors', [])}"
#                 )

#         except Exception as approval_inventory_err:
#             msg = f"Failed to handle approval status change inventory for work order {work_order.id}: {approval_inventory_err}"
#             current_app.logger.error(msg)
#             raise InventoryError(msg)

#         # Commit the changes
#         db.session.commit()

#         current_app.logger.info(
#             f"Work order {work_order.id} finalized by user {current_user.id}"
#         )

#         return return_vars(
#             work_order_dmc_notifications_children=dmc_notification(
#                 "Work order finalized successfully",
#                 "Success!",
#                 color="green",
#                 position="bottom-center",
#                 autoClose=10_000,
#             ),
#             work_order_approved_value=True,
#             work_order_finalize_btn_disabled=True,
#             work_order_save_btn_disabled=True,
#         )

#     except Exception as e:
#         db.session.rollback()
#         current_app.logger.exception(
#             f"Error finalizing work order {edit_work_order_id_data}"
#         )

#         return return_vars(
#             work_order_dmc_notifications_children=dmc_notification(
#                 f"Failed to finalize work order: {str(e)}",
#                 "Error",
#                 color="red",
#                 position="bottom-center",
#                 autoClose=10_000,
#             )
#         )


@callback(
    Output("work_order_delete_confirmation_modal", "is_open"),
    Input("work_order_delete_btn", "n_clicks"),
    Input("work_order_delete_cancel_btn", "n_clicks"),
    Input("work_order_delete_confirm_btn", "n_clicks"),
    prevent_initial_call=True,
)
def open_delete_confirmation_modal(delete_clicks, cancel_clicks, confirm_clicks):
    """
    Open the delete confirmation modal when delete button is clicked,
    or close it when cancel is clicked.
    """
    id_triggered: str = get_id_triggered()

    if id_triggered == "work_order_delete_btn.n_clicks":
        # Open the confirmation modal
        return True
    elif id_triggered in (
        "work_order_delete_cancel_btn.n_clicks",
        "work_order_delete_confirm_btn.n_clicks",
    ):
        # Close the modal
        return False

    return False


@callback(
    Output("work_order_dmc_notifications", "children", allow_duplicate=True),
    Output("location_work_order", "href", allow_duplicate=True),
    Input("work_order_delete_confirm_btn", "n_clicks"),
    State("edit_work_order_id", "data"),
    State("work_order_country_id", "value"),
    prevent_initial_call=True,
)
def handle_work_order_deletion(
    confirm_clicks, edit_work_order_id_data, work_order_country_id_value
):
    """
    Handle the actual work order deletion when the confirmation button is clicked.
    This callback is left empty for now as requested.
    """
    if not edit_work_order_id_data:
        raise PreventUpdate

    id_triggered: str = get_id_triggered()
    if id_triggered != "work_order_delete_confirm_btn.n_clicks":
        raise PreventUpdate

    def return_vars(
        work_order_dmc_notifications_children: dmc.Notification = no_update,
        location_work_order_href: str = no_update,
    ) -> tuple:
        """Return the output variables"""
        return (
            work_order_dmc_notifications_children,
            location_work_order_href,
        )

    # Work order deletion logic
    work_order = db.session.get(WorkOrder, edit_work_order_id_data)
    if not work_order:
        raise PreventUpdate

    if work_order_country_id_value == COUNTRY_ID_USA:
        work_orders_url = "/admin/work_orders_corp"
    else:
        work_orders_url = "/admin/work_orders"

    # Initialize the result dictionaries
    result_reservations: Dict[str, Any] = {}
    result_movements: Dict[str, Any] = {}

    try:
        # result_reservations = cancel_work_order_reservations(
        #     db_session=db.session,
        #     work_order=work_order,
        #     user_id=current_user.id,
        #     cancel_reason="Work order deleted",
        #     commit=False,  # We'll commit later
        # )
        result_reservations = delete_work_order_reservations(
            db_session=db.session,
            work_order=work_order,
            user_id=current_user.id,
            commit=False,  # We'll commit later
        )
    except InventoryError as e:
        current_app.logger.error(
            f"Failed to cancel work order reservations for {work_order.id}: {e}"
        )
        db.session.rollback()
        return return_vars(
            work_order_dmc_notifications_children=dmc_notification(
                f"Failed to cancel work order reservations: {str(e)}",
                "Error",
                color="red",
                position="bottom-center",
                autoClose=10_000,
            ),
        )

    try:
        # result_movements = cancel_work_order_movements(
        #     db_session=db.session,
        #     work_order=work_order,
        #     user_id=current_user.id,
        #     cancel_reason="Work order deleted",
        #     commit=False,  # We'll commit later
        # )
        result_movements = delete_work_order_movements(
            db_session=db.session,
            work_order=work_order,
            user_id=current_user.id,
            commit=False,  # We'll commit later
        )
    except InventoryError as e:
        current_app.logger.error(
            f"Failed to cancel work order movements for {work_order.id}: {e}"
        )
        db.session.rollback()
        return return_vars(
            work_order_dmc_notifications_children=dmc_notification(
                f"Failed to cancel work order movements: {str(e)}",
                "Error",
                color="red",
                position="bottom-center",
                autoClose=10_000,
            ),
        )

    if not result_reservations.get("success", False):
        db.session.rollback()
        return return_vars(
            work_order_dmc_notifications_children=dmc_notification(
                result_reservations.get(
                    "message", "Failed to cancel work order inventory reservations"
                ),
                "Error",
                color="red",
                position="bottom-center",
                autoClose=10_000,
            ),
            location_work_order_href=work_orders_url,
        )

    if not result_movements.get("success", False):
        db.session.rollback()
        return return_vars(
            work_order_dmc_notifications_children=dmc_notification(
                result_movements.get(
                    "message", "Failed to cancel work order inventory movements"
                ),
                "Error",
                color="red",
                position="bottom-center",
                autoClose=10_000,
            ),
            location_work_order_href=work_orders_url,
        )

    # Delete the work order
    db.session.delete(work_order)
    db.session.commit()
    return return_vars(
        work_order_dmc_notifications_children=dmc_notification(
            "Work order deleted successfully",
            "Success!",
            color="green",
            position="bottom-center",
            autoClose=10_000,
        ),
        location_work_order_href=work_orders_url,
    )
