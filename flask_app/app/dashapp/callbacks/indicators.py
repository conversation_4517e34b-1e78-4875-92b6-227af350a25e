import re
from collections import OrderedDict
from datetime import date, datetime
from random import randint

import dash_bootstrap_components as dbc
import phonenumbers
from dash import Input, Output, State, callback, html, no_update
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import ReleaseNote, StructureVw
from sqlalchemy import func
from sqlalchemy.sql import text

from app import db, user_is_demo_customer
from app.config import (
    SOFTWARE_VERSIONS_WITH_BUGS,
    TAB_CARDS,
    TAB_CHARTS,
    TAB_CONTROL,
    TAB_HEALTH,
    TAB_INDICATORS,
    TAB_INVENTORY,
    TAB_LOG,
    TAB_PERFORMANCE,
    UNIT_TYPE_ID_UNOGAS,
    UNIT_TYPES_IDS_XFER_EGAS,
    USER_ID_SEAN,
)
from app.dashapp.callbacks.control_utils import (
    PermissionsAbilities,
    get_card_body_permissions_abilities,
)
from app.dashapp.layout_utils import get_list_group_item
from app.dashapp.metrics import names_uno_unogas, names_xfer_egas
from app.dashapp.utils import (
    get_iot_device_shadow,
    get_or_update_gps,
    get_structure_obj,
    get_unit_type,
    log_function_caller,
    seconds_since_last_any_msg,
    update_shadow,
)
from app.databases import run_sql_query
from shared.utils.datetime_utils import FriendlyTime, utcnow_naive


def get_indicators_div(is_ijack_user: bool = False):
    """Get the indicators div"""
    return dbc.Row(
        id="indicators_div",
        justify="center",
        class_name="mt-3",
        # Don't hide this initially, since it's the default tab.
        # This way we don't need to run the callback on startup, to make this card visible
        # style={"display": "none"},
        children=dbc.Col(
            sm=12,
            xxl=10,
            children=[
                dbc.Row(
                    dbc.Col(
                        dbc.Card(
                            [
                                dbc.CardHeader(
                                    dbc.Spinner(
                                        "Messages",
                                        id="ind_messages_card_title",
                                        color="success",
                                    ),
                                ),
                                dbc.CardBody(
                                    [
                                        html.Div(id="indicators_messages_div"),
                                        dbc.Button(
                                            [
                                                html.I(
                                                    className="fa-solid fa-refresh me-1"
                                                ),
                                                "Refresh Data",
                                            ],
                                            id="indicators_msg_refresh_btn",
                                            color="primary",
                                        ),
                                    ]
                                ),
                            ],
                        )
                    )
                ),
                dbc.Row(
                    [
                        dbc.Col(
                            [
                                # Active alert indicators
                                dbc.Row(
                                    id="ind_alarms_row",
                                    style={"display": "none"},
                                    children=dbc.Col(
                                        dbc.Card(
                                            class_name="mt-3",
                                            children=[
                                                dbc.CardHeader(
                                                    "Active Alerts",
                                                ),
                                                dbc.CardBody(
                                                    [
                                                        dbc.ListGroup(
                                                            id="ind_alarms_list_group"
                                                        ),
                                                        html.Div(
                                                            style={}
                                                            if is_ijack_user
                                                            else {"display": "none"},
                                                            className="mt-2",
                                                            children=dbc.Button(
                                                                "Clear All Alerts",
                                                                id="btn_clear_all_alerts",
                                                                color="primary",
                                                                size="sm",
                                                            ),
                                                        ),
                                                    ]
                                                ),
                                            ],
                                        ),
                                    ),
                                ),
                                # Regular indicators that are always there
                                dbc.Row(
                                    dbc.Col(
                                        dbc.Card(
                                            [
                                                dbc.CardHeader(
                                                    dbc.Spinner(
                                                        "Unit Status",
                                                        id="ind_warn_card_title",
                                                        color="success",
                                                    ),
                                                ),
                                                dbc.CardBody(
                                                    [
                                                        dbc.ListGroup(
                                                            [
                                                                get_list_group_item(
                                                                    "ind_warn_time_lbl",
                                                                    "ind_warn_time_badge",
                                                                    tooltip="Seconds since last data point received from unit",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_time_metric_lbl",
                                                                    "ind_warn_time_metric_badge",
                                                                    label="Time since metric (IJACK-only)",
                                                                    tooltip="Which metric is the time since last data point received from unit?",
                                                                    parent_id="ind_warn_time_metric_div",
                                                                    style={
                                                                        "display": "none"
                                                                    },
                                                                    badge_color="secondary",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_connected_lbl",
                                                                    "ind_warn_connected_badge",
                                                                    tooltip="Does the unit have an active connection to AWS IoT?",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_ion_lbl",
                                                                    "ind_warn_ion_badge",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_hyd_lbl",
                                                                    "ind_warn_hyd_badge",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_mtr_lbl",
                                                                    "ind_warn_mtr_badge",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_clr_lbl",
                                                                    "ind_warn_clr_badge",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_htr_lbl",
                                                                    "ind_warn_htr_badge",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_aux_lbl",
                                                                    "ind_warn_aux_badge",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_prs_lbl",
                                                                    "ind_warn_prs_badge",
                                                                    parent_id="ind_warn_prs_parent",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_sbf_lbl",
                                                                    "ind_warn_sbf_badge",
                                                                ),
                                                                get_list_group_item(
                                                                    "ind_warn_stboxf_lbl",
                                                                    "ind_warn_stboxf_badge",
                                                                    parent_id="ind_warn_stboxf_parent",
                                                                    style={
                                                                        "display": "none"
                                                                    },
                                                                ),
                                                            ]
                                                        ),
                                                    ]
                                                ),
                                            ],
                                            class_name="mt-3",
                                        ),
                                    )
                                ),
                                # # IJACK-only indicators for new stuff we're trying out
                                # dbc.Row(
                                #     dbc.Col(
                                #         dbc.Card(
                                #             [
                                #                 dbc.CardHeader(
                                #                     "IJACK Only! Customers can't see this  :)",
                                #                 ),
                                #                 dbc.CardBody(
                                #                     [
                                #                         dbc.ListGroup([]),
                                #                     ]
                                #                 ),
                                #             ],
                                #             class_name="mt-3",
                                #         ),
                                #     ),
                                #     id="ijack_only_indicators_row",
                                #     style={"display": "none"},
                                # ),
                            ],
                            sm=12,
                            md=5,
                        ),
                        dbc.Col(
                            [
                                dbc.Card(
                                    [
                                        dbc.CardHeader(
                                            dbc.Spinner(
                                                [
                                                    html.Div(
                                                        "Other Information",
                                                        id="ind_other_card_title",
                                                    ),
                                                    dbc.Button(
                                                        [
                                                            html.I(
                                                                className="fa-solid fa-edit me-1"
                                                            ),
                                                            "Edit Unit",
                                                        ],
                                                        id="unit_edit_btn",
                                                        color="primary",
                                                        outline=True,
                                                        class_name="float-end lh-1",
                                                        size="sm",
                                                        style={"display": "none"},
                                                        external_link=True,
                                                    ),
                                                ],
                                                color="success",
                                            )
                                        ),
                                        dbc.CardBody(
                                            [
                                                dbc.ListGroup(
                                                    [
                                                        get_list_group_item(
                                                            "ind_othr_model_lbl",
                                                            "ind_othr_model_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_pu_type_lbl",
                                                            "ind_othr_pu_type_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_has_slave_lbl",
                                                            "ind_othr_has_slave_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_startup_lbl",
                                                            "ind_othr_startup_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_op_hours_lbl",
                                                            "ind_othr_op_hours_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_surface_lbl",
                                                            "ind_othr_surface_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_gps_lbl",
                                                            "ind_othr_gps_badge",
                                                            8,
                                                            parent_id="ind_othr_gps_parent",
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_warehouse_lbl",
                                                            "ind_othr_warehouse_badge",
                                                            8,
                                                            parent_id="ind_othr_warehouse_parent",
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_pu_lbl",
                                                            "ind_othr_pu_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_struct_lbl",
                                                            "ind_othr_struct_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_aws_lbl",
                                                            "ind_othr_aws_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_gateway_type_lbl",
                                                            "ind_othr_gateway_type_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_wl_lbl",
                                                            "ind_othr_wl_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_sig_lbl",
                                                            "ind_othr_sig_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_imei_lbl",
                                                            "ind_othr_imei_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_apn_lbl",
                                                            "ind_othr_apn_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_phone_lbl",
                                                            "ind_othr_phone_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_swv_lbl",
                                                            "ind_othr_swv_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_swvp_lbl",
                                                            "ind_othr_swvp_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_restarts_lbl",
                                                            "ind_othr_restarts_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_rest_mins_lbl",
                                                            "ind_othr_rest_mins_badge",
                                                            8,
                                                        ),
                                                        get_list_group_item(
                                                            "ind_othr_suction_range_lbl",
                                                            "ind_othr_suction_range_badge",
                                                            8,
                                                        ),
                                                    ]
                                                )
                                            ]
                                        ),
                                    ],
                                    style={"margin-top": "1rem"},
                                ),
                            ],
                            sm=12,
                            md=7,
                        ),
                    ]
                ),
                #     ],
                #     color="success",
                #     type='grow'
                # ),
            ],
        ),
    )


def get_ind_charts_cards_tabs(is_ijack_user: bool):
    """More tabs for the indicators/charts/control/log, etc"""
    tabs_list: list = [
        dbc.Tab(
            label="Indicators",
            tab_id=TAB_INDICATORS,
        ),
        dbc.Tab(
            label="Charts",
            tab_id=TAB_CHARTS,
        ),
        dbc.Tab(
            label="Cards",
            id="tab_cards_id",  # need this to set the label to "Surface Card" or "Compression Card"
            tab_id=TAB_CARDS,
        ),
        # dbc.Tab(
        #     label="Diagnostic",
        #     id="tab_diag_cards_id",
        #     tab_id=TAB_DIAG_CARDS,
        #     # style={"display": "none"},
        #     # class_name="d-none",
        #     tab_style={"display": "none"},
        # ),
        dbc.Tab(
            label="Performance",
            tab_id=TAB_PERFORMANCE,
        ),
        # This tab is only inserted if the user is an IJACK user, for the time being
        # dbc.Tab(
        #     label="Health",
        #     tab_id=TAB_HEALTH,
        # ),
        dbc.Tab(
            label="Inventory",
            id="tab_inventory_id",
            tab_id=TAB_INVENTORY,
            tab_style={"display": "none"},
        ),
        dbc.Tab(
            label="Control",
            tab_id=TAB_CONTROL,
        ),
        dbc.Tab(
            label="Log",
            tab_id=TAB_LOG,
        ),
    ]

    if is_ijack_user:
        tabs_list.insert(
            5,
            dbc.Tab(
                label="Health",
                tab_id=TAB_HEALTH,
            ),
        )

    return dbc.Row(
        class_name="mb-2",
        children=dbc.Col(
            dbc.Tabs(
                tabs_list,
                id="tabs_for_ind_charts_control_log",
                # active_tab=TAB_INDICATORS,  # default tab
                className="justify-content-center",
                # style=dict(color="#717174"),
                persistence=True,
                persistence_type="local",
            ),
        ),
    )


def get_unit_status_indicators(
    shadow: dict, metrics_dict: dict, is_xfer_type: bool
) -> dict:
    """Make a dictionary of unit status indicators from the AWS IoT device shadow"""

    # Store all indicators in a dictionary so we can add them all to a list by iterating through the dictionary
    indicators_dict_col1 = OrderedDict()

    # Last reported (any metric)
    seconds_ago, key_latest = seconds_since_last_any_msg(shadow)

    friendly_time = FriendlyTime(seconds=seconds_ago)
    mins_ago: float = friendly_time.mins
    color_time_since: str = friendly_time.color_time_since
    msg: str = friendly_time.elapsed_time

    # Generic 'time_since' warning that applies to all others
    time_since_warning = False if mins_ago < 16 else True
    time_since_warning_color = "warning"

    metric = "time"
    # is_good = True if mins_ago < 5 else False
    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = "No RCOM" if shadow == {} else msg
    indicators_dict_col1[metric]["color"] = color_time_since
    indicators_dict_col1[metric]["label"] = "Time since last reported"

    # Which metric was used for the time since last reported?
    metric = "time_metric"
    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = key_latest

    reported = shadow.get("state", {}).get("reported", {})
    swv = reported.get("SWV", None)

    metric = "connected"
    value = reported.get("connected", None)
    is_good = True if value == 1 and mins_ago < 15 else False
    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = "Online" if is_good else "Offline"
    indicators_dict_col1[metric]["color"] = "success" if is_good else "danger"
    indicators_dict_col1[metric]["label"] = (
        "Connected to AWS IoT" if is_good else "NOT connected to AWS IoT"
    )

    # Grab WARN1 (motor problem alert) and WARN2 (hydraulic problem alert)
    # since we'll use them for several indicators, or the colours of indicators
    warn1 = reported.get(metrics_dict["warn1"].upper(), None)
    warn2 = reported.get(metrics_dict["warn2"].upper(), None)

    # IJACK operating normally
    metric = "ion"
    value = reported.get(metrics_dict[metric].upper(), None)
    if value is None or str(swv) == "312":
        # substitute HYD_EGAS for ION_EGAS, only for units that have PLC software v312
        value = reported.get(metrics_dict["hyd"].upper(), None)
    if value == 1:
        msg = "Good"
        color = "success"
        label = "IJACK operating normally"
    elif value == 0:
        if warn1 == 1:
            msg = "Warning"
            color = "danger"
            label = "IJACK not operating normally"
        elif warn2 == 1:
            msg = "Warning"
            color = "warning"
            label = "IJACK not operating normally"
        else:
            msg = "Ready"
            color = "secondary"
            label = "IJACK not operating normally"
    else:
        msg = "Unknown"
        color = "warning"
        label = "Not sure if IJACK operating normally"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    indicators_dict_col1[metric]["label"] = label

    # Hydraulics on
    metric = "hyd"
    value = reported.get(metrics_dict[metric].upper(), None)
    if value == 1:
        msg = "Stroking"
        color = "success"
    elif value == 0:
        if warn2 == 1:  # warning state == 1
            msg = "Off - shutdown active"
            color = "warning"
        else:
            msg = "Off - waiting to start"
            color = "secondary"
    else:
        msg = "Unknown"
        color = "warning"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    # indicators_dict_col1[metric]['label'] = 'Hydraulics on' if is_good else 'Hydraulics off'
    indicators_dict_col1[metric]["label"] = "Hydraulics"

    # Motor on
    metric = "mtr"
    value = reported.get(metrics_dict[metric].upper(), None)
    if value == 1:
        msg = "On"
        color = "success"
    elif value == 0:
        if warn1 == 1:  # warning state == 1
            msg = "Off - shutdown active"
            color = "danger"
        else:
            msg = "Off - waiting to start"
            color = "secondary"
    else:
        msg = "Unknown"
        color = "warning"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    indicators_dict_col1[metric]["label"] = "Motor"

    # Cooler on
    metric = "clr"
    value = reported.get(metrics_dict[metric].upper(), None)
    hydr_temp = reported.get(metrics_dict["ht"].upper(), None)
    if hydr_temp is not None:
        hydr_temp = round(hydr_temp)
        fahr = round(hydr_temp * 1.8 + 32)
        temp_info = f"{hydr_temp}{chr(176)}C ({fahr}{chr(176)}F)"
    else:
        fahr = None
        temp_info = ""

    if value == 1:
        if hydr_temp is not None and hydr_temp > -100 and hydr_temp < 500:
            msg = f"On: {temp_info}"
        else:
            msg = "On"
        color = "success"
    elif value == 0:
        if hydr_temp is not None and hydr_temp > -100 and hydr_temp < 500:
            msg = f"Off: {temp_info}"
        else:
            msg = "Off"
        color = "secondary"
    else:
        msg = "Unknown"
        color = "warning"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    indicators_dict_col1[metric]["label"] = "Cooler fan"

    # Heater on
    metric = "htr"
    value = reported.get(metrics_dict[metric].upper(), None)
    if value == 1:
        msg = "On"
        color = "success"
    elif value == 0:
        msg = "Off"
        color = "secondary"
    else:
        msg = "Unknown"
        color = "warning"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    indicators_dict_col1[metric]["label"] = "Heater"

    # Auxiliary on
    metric = "aux"
    value = reported.get(metrics_dict[metric].upper(), None)
    if value == 1:
        msg = "On"
        color = "success"
    elif value == 0:
        msg = "Off"
        color = "secondary"
    else:
        msg = "Unknown"
        color = "warning"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    indicators_dict_col1[metric]["label"] = "Auxiliary"

    # Presco pressure switch
    metric = "prs"
    value = reported.get(metrics_dict[metric].upper(), None)
    if value == 1:
        msg = "Shutdown active"
        color = "warning"
    elif value == 0:
        msg = "Okay"
        color = "secondary"
    else:
        msg = "Unknown"
        color = "warning"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    indicators_dict_col1[metric]["label"] = "Pressure switch"

    # Containment enviro switch (stuffing box float switch)
    metric = "sbf"
    value = reported.get(metrics_dict[metric].upper(), None)
    if value == 1:
        msg = "Shutdown active"
        color = "warning"
    elif value == 0:
        msg = "Okay"
        color = "secondary"
    else:
        msg = "Unknown"
        color = "warning"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    indicators_dict_col1[metric]["label"] = (
        "Containment float switch" if is_xfer_type else "Stuffing box float switch"
    )

    # Slave unit containment float switch (not always visible)
    metric = "stboxf"
    value = reported.get(metrics_dict[metric].upper(), None)
    if value == 1:
        msg = "Switch activated"
        color = "warning"
    elif value == 0:
        msg = "Okay"
        color = "secondary"
    else:
        msg = "Unknown"
        color = "warning"

    if time_since_warning:
        color = time_since_warning_color

    indicators_dict_col1[metric] = {}
    indicators_dict_col1[metric]["msg"] = msg
    indicators_dict_col1[metric]["color"] = color
    indicators_dict_col1[metric]["label"] = "Containment float switch - unit 2"

    return indicators_dict_col1


def get_other_gateway_info(
    shadow: dict, structure_obj: StructureVw, is_online: bool
) -> dict:
    """Make a dictionary of unit status indicators from the AWS IoT device shadow"""

    # Store all indicators in a dictionary so we can add them all to a list by iterating through the dictionary
    other_indicators_dict = OrderedDict()
    reported = shadow.get("state", {}).get("reported", {})

    # Identify slave units (e.g. EGAS on an UNOGAS type)
    is_unogas = structure_obj.unit_type_id == UNIT_TYPE_ID_UNOGAS

    # Model
    metric = "model"
    value = str(structure_obj.model)
    if is_unogas:
        value = f"{value}, {structure_obj.model_slave}"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "IJACK model"

    # Model
    metric = "pu_type"
    value = str(structure_obj.power_unit_type)
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "IJACK power unit type"

    # Has slave unit
    metric = "has_slave"
    if is_unogas:
        value = True
    else:
        value = True if reported.get("HAS_SLAVE", None) else False
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["label"] = "Two units sharing one power unit?"
    other_indicators_dict[metric]["msg"] = "Yes" if value else "No"
    other_indicators_dict[metric]["color"] = "success" if value else "secondary"
    other_indicators_dict[metric]["value"] = value

    # land location
    metric = "surface"
    surface_str: str = str(structure_obj.surface) or "None"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = (
        surface_str if surface_str else "Not Reported"
    )
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Surface location"

    # GPS location
    metric = "gps"
    lat, lon = get_or_update_gps(
        structure_obj, reason=f"User {current_user} is on the Indicators tab"
    )
    # Most reliable Google Maps link
    google_maps_api_url = f"https://www.google.com/maps/search/?api=1&query={lat},{lon}"
    if lat is None or lon is None:
        msg = None
    else:
        msg = f"{float(lat):0.4f}/{float(lon):0.4f}"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = msg if msg else "Not Reported"
    other_indicators_dict[metric]["color"] = "success" if msg else "secondary"
    other_indicators_dict[metric]["label"] = "Google Maps GPS"
    other_indicators_dict[metric]["href"] = google_maps_api_url if msg else "#"

    # Warehouse
    metric = "warehouse"
    value = str(structure_obj.warehouse_name)
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Warehouse location"

    # Startup (formerly installation) date
    metric = "startup"
    startup_date = structure_obj.structure_install_date
    startup_date_str = startup_date.strftime("%Y-%m-%d")
    if is_unogas:
        startup_date = f"{startup_date_str}, {structure_obj.slave_install_date}"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = (
        startup_date_str if startup_date_str else "Not Reported"
    )
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Startup date"

    # Operating hours, converted to months or years
    metric = "op_hours"
    op_hours = getattr(structure_obj, "op_hours", None)
    try:
        if not isinstance(op_hours, (float, int)) or op_hours <= 0:
            # If no operating hours, show the months since the startup date
            if isinstance(startup_date, date):
                # Convert the date to a datetime object
                startup_date = datetime.combine(startup_date, datetime.min.time())
            elif isinstance(startup_date, str):
                # If the date is a string, convert it to a datetime object
                try:
                    startup_date = datetime.strptime(startup_date, "%Y-%m-%d")
                except ValueError:
                    # Unconverted data remains?
                    startup_date = datetime.strptime(startup_date, "%Y-%m-%d %H:%M:%S")
            if isinstance(startup_date, datetime):
                # Calculate the hours since the startup date
                op_hours = (utcnow_naive() - startup_date).days * 24
        if isinstance(op_hours, (float, int)) and op_hours > 0:
            if op_hours > 744:
                # Convert from hours to months
                op_hours_converted = round(op_hours / (24 * 30.4375), 1)
                op_hours_str = f"{op_hours_converted} months"
            else:
                # Show days
                op_hours_converted = round(op_hours / 24, 1)
                op_hours_str = f"{op_hours_converted} days"
        else:
            op_hours_str = "Not Reported"
    except Exception:
        op_hours_str = "Not Reported"
        current_app.logger.exception(
            "Error calculating operating hours on Indicators tab for unit %s",
            structure_obj.power_unit_str,
        )

    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = op_hours_str
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Operating hours"

    # SERIAL_NUMBER
    metric = "pu"
    value = str(reported.get("SERIAL_NUMBER", ""))
    value_db = str(structure_obj.power_unit).replace(".0", "")
    if value != value_db:
        value = f"DB '{value_db}'. CAN bus '{value}'"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Power unit serial number"

    # STRUCTURE
    metric = "struct"
    value = str(structure_obj.structure).replace(".0", "")
    if is_unogas:
        structure_slave = str(structure_obj.structure_slave).replace(".0", "")
        value = f"{value}, {structure_slave}"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Structure serial number"

    # AWS_THING
    metric = "aws"
    aws_thing = reported.get("AWS_THING", None)
    if aws_thing is None:
        aws_thing = str(structure_obj.aws_thing)
    if getattr(current_user, "id", None) == USER_ID_SEAN:
        url = f"https://us-west-2.console.aws.amazon.com/iot/home?region=us-west-2#/thing/{aws_thing}/namedShadow/Classic%20Shadow"
    else:
        url = no_update
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = aws_thing if aws_thing else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "AWS 'thing name'"
    other_indicators_dict[metric]["href"] = url

    # gateway_type
    metric = "gateway_type"
    value = reported.get("gateway_type", "Not Reported")
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Gateway type"

    # Well license
    metric = "wl"
    value = reported.get("WELL_LICENSE", None)
    if value is None:
        value = str(structure_obj.well_license)
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Well license"

    # Cellular signal strength
    metric = "sig"
    value = reported.get("SIGNAL", None)
    # Replace any whitespace character (\s), percentage (%),
    # newline (\n), or carriage return (\r) with an empty string
    try:
        float_value = float(re.sub(r"[\s%\n\r]", "", str(value)))
        value = f"{float_value:.0f}%"
    except Exception:
        # float_value = 0.0
        value = "Not Reported"
        color = "secondary"
    else:
        if float_value > 40:
            color = "success"
        elif float_value > 20:
            color = "warning"
            value = f"{value} (weak)"
        elif float_value > 0:
            color = "danger"
            value = f"{value} (very weak)"
        else:
            color = "secondary"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = color
    other_indicators_dict[metric]["label"] = "Cellular signal strength"

    # IMEI
    metric = "imei"
    value = reported.get("IMEI", "Not Reported")
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "IMEI"

    # APN
    metric = "apn"
    # First place to check is the AWS IoT shadow, as reported by the gateway
    value = reported.get("APN", None)
    if value is None:
        # This is from the power_units table, and gets downloaded to the gateways via C__APN name in AWS IoT shadow
        value = structure_obj.apn
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Access Point Name (APN)"

    # phone number
    metric = "phone"
    # This is a 12 digit number, but we only want the last 10 digits after the country code
    number = str(reported.get("PHONE_NUMBER", ""))
    if number:
        try:
            phone_parsed = phonenumbers.parse(number, "US")
            # Validate the phone number and raise an exception if it's not valid
            if not phonenumbers.is_valid_number(phone_parsed):
                value = "Not Reported"
            value = phonenumbers.format_number(
                phone_parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL
            )
        except Exception:
            value = "Not Reported"
    else:
        value = "Not Reported"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "SIM card phone number"

    # software version
    metric = "swv"
    value = reported.get("SWV", None)
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value if value else "Not Reported"
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Software version (PLC)"

    # software version Python
    metric = "swvp"
    swv_python: str = reported.get("SWV_PYTHON", "Not Reported")
    try:
        swv_python_float: float = float(swv_python)
    except Exception:
        swv_python_float = 0.0
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = swv_python
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Software version (gateway)"
    # We need to set this href to #, since it's not working without it.
    # If there's no valid destination for the link, it might not render the link text as a clickable link.
    # This can lead to the appearance of the link text being invisible,
    # even though the HTML element is still there.
    other_indicators_dict[metric]["href"] = "#"  # no link to release notes update page
    # if is_online:
    # Sometimes the app is offline but the 'mqtt_jobs.py' updating software is still working
    abilities_dict: PermissionsAbilities = get_card_body_permissions_abilities(
        shadow, structure_obj, swv_plc=999
    )
    has_abilities: bool = abilities_dict.get("has_abilities", False)
    if has_abilities:
        # No sense trying to update the gateway software version if the unit is offline,
        # or the user doesn't have permissions
        try:
            latest_canpy_version: float = db.session.query(
                func.max(ReleaseNote.version)
            ).scalar()
            if latest_canpy_version > swv_python_float:
                other_indicators_dict[metric]["color"] = "warning"
                other_indicators_dict[metric]["msg"] = f"{swv_python} (update)"
                # if os.getenv("FLASK_CONFIG", "development") in (
                #     "development",
                #     "wsl",
                #     "testing",
                # ):
                #     # We're just testing this currently
                power_unit_str: str = str(structure_obj.power_unit_str)
                other_indicators_dict[metric]["href"] = (
                    f"/release-notes/?gateway={aws_thing}&current_version={swv_python}&surface={surface_str}&power_unit={power_unit_str}"
                )
        except Exception as err:
            current_app.logger.error(f"Error comparing CANpy versions: {err}")

    if swv_python_float in SOFTWARE_VERSIONS_WITH_BUGS:
        # So we can easily see which units have the buggy software, and update them
        other_indicators_dict[metric]["color"] = "danger"
        other_indicators_dict[metric]["msg"] = f"{swv_python} (must update)"

    metric = "restarts"
    value = reported.get("RESTART_ATTEMPTS", "Not Reported")
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Restart attempts, if unit stops"

    metric = "rest_mins"
    value = reported.get("RESTART_DELAY", "Not Reported")
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Minutes between restart attempts"

    metric = "suction_range"
    value = float(reported.get("SUCTION_RANGE", 0.0))
    if value == 0.0:
        value = "0"
    elif value == 1.0:
        value = "1"
    elif value == 2.0:
        value = "VRU"
    elif value == 3.0:
        value = "3"
    elif value == 4.0:
        value = "Vessel Level Mode"
    other_indicators_dict[metric] = {}
    other_indicators_dict[metric]["msg"] = value
    other_indicators_dict[metric]["color"] = "secondary"
    other_indicators_dict[metric]["label"] = "Mode"

    return other_indicators_dict


# def get_group_items_list(dict_, width_badge=4, badge_on_right=False):
#     """Return a list of list_group items"""
#     list_ = []
#     for key in dict_.keys():
#         msg = dict_[key]['msg']
#         color = dict_[key]['color']
#         label = dict_[key]['label']

#         list_of_cols = [
#             dbc.Col([
#                 dbc.Badge(msg, pill=False, color=color, class_name="float-end"),
#             ], width=width_badge),
#             dbc.Col([
#                 html.Div(label, style=dict(color="#717174")),
#             ], width=(12-width_badge)),
#         ]

#         list_of_cols2 = list_of_cols
#         if badge_on_right:
#             list_of_cols2 = [list_of_cols[1], list_of_cols[0]]

#         list_.append(
#             dbc.ListGroupItem([
#                 dbc.Row(list_of_cols2)
#             ])
#         )

#     return list_


def get_alerts_from_shadow(shadow: dict, unit_type_lower: str) -> list:
    # Find active alerts in device shadow
    active_alerts = []
    reported = shadow.get("state", {}).get("reported", {})

    # unit_type_lower does NOT include "unogas".
    # UNOGAS units are either "egas" or "xfer" at this point
    if unit_type_lower in ("egas", "xfer"):
        # EGAS/XFER alerts
        starts_with = "AE"
    else:
        # UNO alerts
        starts_with = "AU"

    for key, value in reported.items():
        if key.startswith(starts_with) and value == 1:
            active_alerts.append(key)

    return active_alerts


def get_alerts_style_and_list_group_items(shadow: dict, unit_type_lower: str) -> tuple:
    """Get any active alerts from the shadow"""

    null_return = {"display": "none"}, None

    active_alerts: list = get_alerts_from_shadow(shadow, unit_type_lower)

    # Don't run the query below, if no active alerts
    if len(active_alerts) == 0:
        return null_return

    # Get the descriptions for each active alert.
    # Use "text()" to properly escape the % in AE%
    sql = text(
        """
        select
            abbrev,
            item as label,
            color
        from public.map_abbrev_item
        where
            char_length(abbrev) = 5
            and (
                abbrev like 'AE%'
                or abbrev like 'AU%'
            )
            --AE005 = IJACK operating normally
            --AE051 = IJACK 2 operating normally
            and abbrev not in ('AE005', 'AE051')
    """
    )
    rows, _ = run_sql_query(sql, db_name="ijack")
    abbrevs_dict = {d["abbrev"]: d for d in rows}

    # Make the HTML
    list_group_items = []
    for metric in active_alerts:
        # Get label and color for each alert
        alert_dict = abbrevs_dict.get(metric, {})
        label = alert_dict.get("label", None)
        if label is None:
            continue
        color = alert_dict.get("color", "secondary")

        item = get_list_group_item(
            label_id=f"ind_lbl_{metric}",
            label=label,
            badge_id=f"ind_badge_{metric}",
            badge_children="Active",
            badge_color=color,
        )
        list_group_items.append(item)

    if list_group_items == []:
        return null_return

    return {}, list_group_items


# def find_latest_canpy_software_for_install() -> str:
#     """Find the latest CANpy software for installation"""
#     version = db.session.query(func.max(ReleaseNote.version)).scalar()
#     # version = ReleaseNote.query(func.max(ReleaseNote.version)).scalar()

#     s3 = boto3.client('s3')
#     bucket_name: str = "ijack-canpy-deployment-stable"

#     try:
#         response = s3.list_objects_v2(Bucket=bucket_name)
#         while True:
#             # Process the objects in the current page
#             if 'Contents' in response:
#                 for obj in response['Contents']:
#                     if 'canpy_folder_v' in obj['Key']:
#                         # Get the version number from the folder name
#                         version: str = obj['Key'].split('canpy_folder_v')[-1]
#                         version = version.replace('.tar.gz', '')
#                         return version

#             # Check if there are more pages to retrieve
#             if response['IsTruncated']:
#                 continuation_token = response['NextContinuationToken']
#                 response = s3.list_objects_v2(Bucket=bucket_name, ContinuationToken=continuation_token)
#             else:
#                 break

#     except Exception as e:
#         print("An error occurred:", str(e))

#     return None


@callback(
    Output("ind_messages_card_title", "children"),
    Output("indicators_messages_div", "children"),
    Output("ind_warn_card_title", "children"),
    Output("ind_other_card_title", "children"),
    # 11 warning labels
    Output("ind_warn_time_lbl", "children"),
    Output("ind_warn_connected_lbl", "children"),
    Output("ind_warn_ion_lbl", "children"),
    Output("ind_warn_hyd_lbl", "children"),
    Output("ind_warn_mtr_lbl", "children"),
    Output("ind_warn_clr_lbl", "children"),
    Output("ind_warn_htr_lbl", "children"),
    Output("ind_warn_aux_lbl", "children"),
    Output("ind_warn_prs_lbl", "children"),
    Output("ind_warn_sbf_lbl", "children"),
    Output("ind_warn_stboxf_lbl", "children"),
    # 12 warning badge children
    Output("ind_warn_time_badge", "children"),
    Output("ind_warn_connected_badge", "children"),
    Output("ind_warn_time_metric_badge", "children"),
    Output("ind_warn_ion_badge", "children"),
    Output("ind_warn_hyd_badge", "children"),
    Output("ind_warn_mtr_badge", "children"),
    Output("ind_warn_clr_badge", "children"),
    Output("ind_warn_htr_badge", "children"),
    Output("ind_warn_aux_badge", "children"),
    Output("ind_warn_prs_badge", "children"),
    Output("ind_warn_sbf_badge", "children"),
    Output("ind_warn_stboxf_badge", "children"),
    # 11 warning badge colors
    Output("ind_warn_time_badge", "color"),
    Output("ind_warn_connected_badge", "color"),
    Output("ind_warn_ion_badge", "color"),
    Output("ind_warn_hyd_badge", "color"),
    Output("ind_warn_mtr_badge", "color"),
    Output("ind_warn_clr_badge", "color"),
    Output("ind_warn_htr_badge", "color"),
    Output("ind_warn_aux_badge", "color"),
    Output("ind_warn_prs_badge", "color"),
    Output("ind_warn_sbf_badge", "color"),
    Output("ind_warn_stboxf_badge", "color"),
    # 3 styles
    Output("ind_othr_gps_parent", "style"),
    Output("ind_warn_stboxf_parent", "style"),
    Output("ind_warn_prs_parent", "style"),
    # 19 indicator labels' children
    Output("ind_othr_model_lbl", "children"),
    Output("ind_othr_pu_type_lbl", "children"),
    Output("ind_othr_has_slave_lbl", "children"),
    Output("ind_othr_startup_lbl", "children"),
    Output("ind_othr_op_hours_lbl", "children"),
    Output("ind_othr_surface_lbl", "children"),
    Output("ind_othr_gps_lbl", "children"),
    Output("ind_othr_warehouse_lbl", "children"),
    Output("ind_othr_pu_lbl", "children"),
    Output("ind_othr_struct_lbl", "children"),
    Output("ind_othr_aws_lbl", "children"),
    Output("ind_othr_gateway_type_lbl", "children"),
    Output("ind_othr_wl_lbl", "children"),
    Output("ind_othr_sig_lbl", "children"),
    Output("ind_othr_imei_lbl", "children"),
    Output("ind_othr_apn_lbl", "children"),
    Output("ind_othr_phone_lbl", "children"),
    Output("ind_othr_swv_lbl", "children"),
    Output("ind_othr_swvp_lbl", "children"),
    Output("ind_othr_restarts_lbl", "children"),
    Output("ind_othr_rest_mins_lbl", "children"),
    Output("ind_othr_suction_range_lbl", "children"),
    # 19 indicator badges' children
    Output("ind_othr_model_badge", "children"),
    Output("ind_othr_pu_type_badge", "children"),
    Output("ind_othr_has_slave_badge", "children"),
    Output("ind_othr_startup_badge", "children"),
    Output("ind_othr_op_hours_badge", "children"),
    Output("ind_othr_surface_badge", "children"),
    Output("ind_othr_gps_badge", "children"),
    Output("ind_othr_warehouse_badge", "children"),
    Output("ind_othr_pu_badge", "children"),
    Output("ind_othr_struct_badge", "children"),
    Output("ind_othr_aws_badge", "children"),
    Output("ind_othr_gateway_type_badge", "children"),
    Output("ind_othr_wl_badge", "children"),
    Output("ind_othr_sig_badge", "children"),
    Output("ind_othr_imei_badge", "children"),
    Output("ind_othr_apn_badge", "children"),
    Output("ind_othr_phone_badge", "children"),
    Output("ind_othr_swv_badge", "children"),
    Output("ind_othr_swvp_badge", "children"),
    Output("ind_othr_restarts_badge", "children"),
    Output("ind_othr_rest_mins_badge", "children"),
    Output("ind_othr_suction_range_badge", "children"),
    # 3 indicator badge hrefs
    Output("ind_othr_gps_badge", "href"),
    Output("ind_othr_aws_badge", "href"),
    Output("ind_othr_swvp_badge", "href"),
    # 1 indicator badge's target for opening a new tab/window
    Output("ind_othr_gps_badge", "target"),
    Output("ind_othr_aws_badge", "target"),
    Output("ind_othr_swvp_badge", "target"),
    # # Display the VPN access?
    # Output('vpn_startup_col', 'style'),
    # 19 indicator badges' colors
    Output("ind_othr_model_badge", "color"),
    Output("ind_othr_pu_type_badge", "color"),
    Output("ind_othr_has_slave_badge", "color"),
    Output("ind_othr_startup_badge", "color"),
    Output("ind_othr_op_hours_badge", "color"),
    Output("ind_othr_surface_badge", "color"),
    Output("ind_othr_gps_badge", "color"),
    Output("ind_othr_warehouse_badge", "color"),
    Output("ind_othr_pu_badge", "color"),
    Output("ind_othr_struct_badge", "color"),
    Output("ind_othr_aws_badge", "color"),
    Output("ind_othr_gateway_type_badge", "color"),
    Output("ind_othr_wl_badge", "color"),
    Output("ind_othr_sig_badge", "color"),
    Output("ind_othr_imei_badge", "color"),
    Output("ind_othr_apn_badge", "color"),
    Output("ind_othr_phone_badge", "color"),
    Output("ind_othr_swv_badge", "color"),
    Output("ind_othr_swvp_badge", "color"),
    Output("ind_othr_restarts_badge", "color"),
    Output("ind_othr_rest_mins_badge", "color"),
    Output("ind_othr_suction_range_badge", "color"),
    # 2 alarm outputs
    Output("ind_alarms_row", "style"),
    Output("ind_alarms_list_group", "children"),
    Output("record_visit_rcom_indicators", "data"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("indicators_msg_refresh_btn", "n_clicks"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def indicators_tab(
    store_structure_id_data,
    active_tab,
    n_clicks,
    tab_uno_egas,
    store_unit_type_id_data,
):
    """Display status indicators for the unit"""
    log_function_caller()

    if store_structure_id_data is None or active_tab != TAB_INDICATORS:
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()
    aws_thing = structure_obj.aws_thing
    power_unit_str = structure_obj.power_unit_str
    customer_first_word = structure_obj.customer.split()[0]
    has_rcom: bool = bool(structure_obj.aws_thing)

    # Request a data refresh from the gateway
    d = {"state": {"desired": {"AWS_REFRESH": randint(1, 65535)}}}
    try:
        update_shadow(d, aws_thing)
    except Exception:
        current_app.logger.exception(
            f"Error updating shadow for {aws_thing} on Indicators tab"
        )

    # Get the AWS IoT device shadow (latest indicators)
    # The above update_shadow() only gets the metrics it updates
    shadow = get_iot_device_shadow(aws_thing)

    # Get a mapping dictionary of metrics by unit type (e.g. UNO = 'hpu' and EGAS = 'hpe')
    is_xfer_type = store_unit_type_id_data in UNIT_TYPES_IDS_XFER_EGAS
    metrics_dict = names_xfer_egas if is_xfer_type else names_uno_unogas

    # Get a dictionary of unit status indicators from the AWS IoT device shadow
    unit_type_lower, _ = get_unit_type(tab_uno_egas, store_unit_type_id_data)
    d1 = get_unit_status_indicators(shadow, metrics_dict, is_xfer_type)
    is_online: bool = d1["connected"]["msg"] == "Online"
    d2 = get_other_gateway_info(shadow, structure_obj, is_online)

    # list_group_items_list_col1 = get_group_items_list(d1, width_badge=3, badge_on_right=True)
    # list_group_items_list_col2 = get_group_items_list(d2, width_badge=4, badge_on_right=True)

    # structure = str(structure_obj.structure).replace(".0", "")
    # power_unit = str(structure_obj.power_unit).replace(".0", "")
    customer_unit_info = f"{customer_first_word} {str(structure_obj.unit_type).upper()} {power_unit_str} at {structure_obj.surface}"

    if user_is_demo_customer(user_id=getattr(current_user, "id", None)):
        ind_messages_card_title = "Messages"
        ind_warn_card_title = "Unit Status"
        ind_other_card_title = "Other Information"
    else:
        ind_messages_card_title = f"Messages for {customer_unit_info}"
        ind_warn_card_title = f"Unit Status for {customer_unit_info}"
        ind_other_card_title = f"Other Information for {customer_unit_info}"

    if has_rcom:
        indicators_message = html.P(
            "The latest indicators data has been requested from the unit. Click refresh to see it."
        )
    else:
        indicators_message = html.P(
            "The unit doesn't have RCOM, so there's no real-time unit status information"
        )

    # Display any alerts that are available
    ind_alarms_row_style, ind_alarms_list_group_children = (
        get_alerts_style_and_list_group_items(shadow, unit_type_lower)
    )

    # Hide certain indicators, especially if there's a slave unit present
    gps_style = {}
    has_slave = d2["has_slave"]["value"]
    # Only show the "stuffing box float switch" indicator if there's a slave unit
    ind_warn_stboxf_parent_style = {} if has_slave else {"display": "none"}
    # If the unit has a slave, there's no PRESCO switch
    ind_warn_prs_parent_style = {"display": "none"} if has_slave else {}

    # # Display the VPN start/stop card if user is Sean
    # vpn_startup_col_style = {'display': 'none'}
    # if getattr(current_user, "id", None) == 1:
    #     vpn_startup_col_style = {}

    record_visit_rcom = "rcom_indicators"

    def return_vars(
        ind_messages_card_title_children,
        indicators_messages_div_children,
        ind_warn_card_title_children,
        ind_other_card_title_children,
        # 11 warning labels
        ind_warn_time_lbl_children,
        ind_warn_connected_lbl_children,
        ind_warn_ion_lbl_children,
        ind_warn_hyd_lbl_children,
        ind_warn_mtr_lbl_children,
        ind_warn_clr_lbl_children,
        ind_warn_htr_lbl_children,
        ind_warn_aux_lbl_children,
        ind_warn_prs_lbl_children,
        ind_warn_sbf_lbl_children,
        ind_warn_stboxf_lbl_children,
        # 12 warning badge children
        ind_warn_time_badge_children,
        ind_warn_connected_badge_children,
        ind_warn_time_metric_badge_children,
        ind_warn_ion_badge_children,
        ind_warn_hyd_badge_children,
        ind_warn_mtr_badge_children,
        ind_warn_clr_badge_children,
        ind_warn_htr_badge_children,
        ind_warn_aux_badge_children,
        ind_warn_prs_badge_children,
        ind_warn_sbf_badge_children,
        ind_warn_stboxf_badge_children,
        # 11 warning badge colors
        ind_warn_time_badge_color,
        ind_warn_connected_badge_color,
        ind_warn_ion_badge_color,
        ind_warn_hyd_badge_color,
        ind_warn_mtr_badge_color,
        ind_warn_clr_badge_color,
        ind_warn_htr_badge_color,
        ind_warn_aux_badge_color,
        ind_warn_prs_badge_color,
        ind_warn_sbf_badge_color,
        ind_warn_stboxf_badge_color,
        # 3 styles
        gps_style,
        ind_warn_stboxf_parent_style,
        ind_warn_prs_parent_style,
        # 19 indicator labels' children
        ind_othr_model_lbl_children,
        ind_othr_pu_type_lbl_children,
        ind_othr_has_slave_lbl_children,
        ind_othr_startup_lbl_children,
        ind_othr_op_hours_lbl_children,
        ind_othr_surface_lbl_children,
        ind_othr_gps_lbl_children,
        ind_othr_warehouse_lbl_children,
        ind_othr_pu_lbl_children,
        ind_othr_struct_lbl_children,
        ind_othr_aws_lbl_children,
        ind_othr_gateway_type_lbl_children,
        ind_othr_wl_lbl_children,
        ind_othr_sig_lbl_children,
        ind_othr_imei_lbl_children,
        ind_othr_apn_lbl_children,
        ind_othr_phone_lbl_children,
        ind_othr_swv_lbl_children,
        ind_othr_swvp_lbl_children,
        ind_othr_restarts_lbl_children,
        ind_othr_rest_mins_lbl_children,
        ind_othr_suction_range_lbl_children,
        # 19 indicator badges' children
        ind_othr_model_badge_children,
        ind_othr_pu_type_badge_children,
        ind_othr_has_slave_badge_children,
        ind_othr_startup_badge_children,
        ind_othr_op_hours_badge_children,
        ind_othr_surface_badge_children,
        ind_othr_gps_badge_children,
        ind_othr_warehouse_badge_children,
        ind_othr_pu_badge_children,
        ind_othr_struct_badge_children,
        ind_othr_aws_badge_children,
        ind_othr_gateway_type_badge_children,
        ind_othr_wl_badge_children,
        ind_othr_sig_badge_children,
        ind_othr_imei_badge_children,
        ind_othr_apn_badge_children,
        ind_othr_phone_badge_children,
        ind_othr_swv_badge_children,
        ind_othr_swvp_badge_children,
        ind_othr_restarts_badge_children,
        ind_othr_rest_mins_badge_children,
        ind_othr_suction_range_badge_children,
        # 3 indicator badge hrefs
        ind_othr_gps_badge_href,
        ind_othr_aws_badge_href,
        ind_othr_swvp_badge_href,
        # 1 indicator badge's target for opening a new tab/window
        ind_othr_gps_badge_target,
        ind_othr_aws_badge_target,
        ind_othr_swvp_badge_target,
        # # Display the VPN access?
        # vpn_startup_col_style,
        # 19 indicator badges' colors
        ind_othr_model_badge_color,
        ind_othr_pu_type_badge_color,
        ind_othr_has_slave_badge_color,
        ind_othr_startup_badge_color,
        ind_othr_op_hours_badge_color,
        ind_othr_surface_badge_color,
        ind_othr_gps_badge_color,
        ind_othr_warehouse_badge_color,
        ind_othr_pu_badge_color,
        ind_othr_struct_badge_color,
        ind_othr_aws_badge_color,
        ind_othr_gateway_type_badge_color,
        ind_othr_wl_badge_color,
        ind_othr_sig_badge_color,
        ind_othr_imei_badge_color,
        ind_othr_apn_badge_color,
        ind_othr_phone_badge_color,
        ind_othr_swv_badge_color,
        ind_othr_swvp_badge_color,
        ind_othr_restarts_badge_color,
        ind_othr_rest_mins_badge_color,
        ind_othr_suction_range_badge_color,
        # 2 alarm outputs
        ind_alarms_row_style,
        ind_alarms_list_group_children,
        record_visit_rcom_indicators_data,
    ):
        """Default return values"""
        return (
            ind_messages_card_title_children,
            indicators_messages_div_children,
            ind_warn_card_title_children,
            ind_other_card_title_children,
            ind_warn_time_lbl_children,
            ind_warn_connected_lbl_children,
            ind_warn_ion_lbl_children,
            ind_warn_hyd_lbl_children,
            ind_warn_mtr_lbl_children,
            ind_warn_clr_lbl_children,
            ind_warn_htr_lbl_children,
            ind_warn_aux_lbl_children,
            ind_warn_prs_lbl_children,
            ind_warn_sbf_lbl_children,
            ind_warn_stboxf_lbl_children,
            ind_warn_time_badge_children,
            ind_warn_connected_badge_children,
            ind_warn_time_metric_badge_children,
            ind_warn_ion_badge_children,
            ind_warn_hyd_badge_children,
            ind_warn_mtr_badge_children,
            ind_warn_clr_badge_children,
            ind_warn_htr_badge_children,
            ind_warn_aux_badge_children,
            ind_warn_prs_badge_children,
            ind_warn_sbf_badge_children,
            ind_warn_stboxf_badge_children,
            ind_warn_time_badge_color,
            ind_warn_connected_badge_color,
            ind_warn_ion_badge_color,
            ind_warn_hyd_badge_color,
            ind_warn_mtr_badge_color,
            ind_warn_clr_badge_color,
            ind_warn_htr_badge_color,
            ind_warn_aux_badge_color,
            ind_warn_prs_badge_color,
            ind_warn_sbf_badge_color,
            ind_warn_stboxf_badge_color,
            gps_style,
            ind_warn_stboxf_parent_style,
            ind_warn_prs_parent_style,
            ind_othr_model_lbl_children,
            ind_othr_pu_type_lbl_children,
            ind_othr_has_slave_lbl_children,
            ind_othr_startup_lbl_children,
            ind_othr_op_hours_lbl_children,
            ind_othr_surface_lbl_children,
            ind_othr_gps_lbl_children,
            ind_othr_warehouse_lbl_children,
            ind_othr_pu_lbl_children,
            ind_othr_struct_lbl_children,
            ind_othr_aws_lbl_children,
            ind_othr_gateway_type_lbl_children,
            ind_othr_wl_lbl_children,
            ind_othr_sig_lbl_children,
            ind_othr_imei_lbl_children,
            ind_othr_apn_lbl_children,
            ind_othr_phone_lbl_children,
            ind_othr_swv_lbl_children,
            ind_othr_swvp_lbl_children,
            ind_othr_restarts_lbl_children,
            ind_othr_rest_mins_lbl_children,
            ind_othr_suction_range_lbl_children,
            ind_othr_model_badge_children,
            ind_othr_pu_type_badge_children,
            ind_othr_has_slave_badge_children,
            ind_othr_startup_badge_children,
            ind_othr_op_hours_badge_children,
            ind_othr_surface_badge_children,
            ind_othr_gps_badge_children,
            ind_othr_warehouse_badge_children,
            ind_othr_pu_badge_children,
            ind_othr_struct_badge_children,
            ind_othr_aws_badge_children,
            ind_othr_gateway_type_badge_children,
            ind_othr_wl_badge_children,
            ind_othr_sig_badge_children,
            ind_othr_imei_badge_children,
            ind_othr_apn_badge_children,
            ind_othr_phone_badge_children,
            ind_othr_swv_badge_children,
            ind_othr_swvp_badge_children,
            ind_othr_restarts_badge_children,
            ind_othr_rest_mins_badge_children,
            ind_othr_suction_range_badge_children,
            ind_othr_gps_badge_href,
            ind_othr_aws_badge_href,
            ind_othr_swvp_badge_href,
            ind_othr_gps_badge_target,
            ind_othr_aws_badge_target,
            ind_othr_swvp_badge_target,
            ind_othr_model_badge_color,
            ind_othr_pu_type_badge_color,
            ind_othr_has_slave_badge_color,
            ind_othr_startup_badge_color,
            ind_othr_op_hours_badge_color,
            ind_othr_surface_badge_color,
            ind_othr_gps_badge_color,
            ind_othr_warehouse_badge_color,
            ind_othr_pu_badge_color,
            ind_othr_struct_badge_color,
            ind_othr_aws_badge_color,
            ind_othr_gateway_type_badge_color,
            ind_othr_wl_badge_color,
            ind_othr_sig_badge_color,
            ind_othr_imei_badge_color,
            ind_othr_apn_badge_color,
            ind_othr_phone_badge_color,
            ind_othr_swv_badge_color,
            ind_othr_swvp_badge_color,
            ind_othr_restarts_badge_color,
            ind_othr_rest_mins_badge_color,
            ind_othr_suction_range_badge_color,
            ind_alarms_row_style,
            ind_alarms_list_group_children,
            record_visit_rcom_indicators_data,
        )

    return return_vars(
        ind_messages_card_title_children=ind_messages_card_title,
        indicators_messages_div_children=indicators_message,
        ind_warn_card_title_children=ind_warn_card_title,
        ind_other_card_title_children=ind_other_card_title,
        # Warning labels
        ind_warn_time_lbl_children=d1["time"]["label"],
        ind_warn_connected_lbl_children=d1["connected"]["label"],
        ind_warn_ion_lbl_children=d1["ion"]["label"],
        ind_warn_hyd_lbl_children=d1["hyd"]["label"],
        ind_warn_mtr_lbl_children=d1["mtr"]["label"],
        ind_warn_clr_lbl_children=d1["clr"]["label"],
        ind_warn_htr_lbl_children=d1["htr"]["label"],
        ind_warn_aux_lbl_children=d1["aux"]["label"],
        ind_warn_prs_lbl_children=d1["prs"]["label"],
        ind_warn_sbf_lbl_children=d1["sbf"]["label"],
        ind_warn_stboxf_lbl_children=d1["stboxf"]["label"],
        # Warning badge children
        ind_warn_time_badge_children=d1["time"]["msg"],
        ind_warn_connected_badge_children=d1["connected"]["msg"],
        ind_warn_time_metric_badge_children=d1["time_metric"]["msg"],
        ind_warn_ion_badge_children=d1["ion"]["msg"],
        ind_warn_hyd_badge_children=d1["hyd"]["msg"],
        ind_warn_mtr_badge_children=d1["mtr"]["msg"],
        ind_warn_clr_badge_children=d1["clr"]["msg"],
        ind_warn_htr_badge_children=d1["htr"]["msg"],
        ind_warn_aux_badge_children=d1["aux"]["msg"],
        ind_warn_prs_badge_children=d1["prs"]["msg"],
        ind_warn_sbf_badge_children=d1["sbf"]["msg"],
        ind_warn_stboxf_badge_children=d1["stboxf"]["msg"],
        # Warning badge colors
        ind_warn_time_badge_color=d1["time"]["color"],
        ind_warn_connected_badge_color=d1["connected"]["color"],
        ind_warn_ion_badge_color=d1["ion"]["color"],
        ind_warn_hyd_badge_color=d1["hyd"]["color"],
        ind_warn_mtr_badge_color=d1["mtr"]["color"],
        ind_warn_clr_badge_color=d1["clr"]["color"],
        ind_warn_htr_badge_color=d1["htr"]["color"],
        ind_warn_aux_badge_color=d1["aux"]["color"],
        ind_warn_prs_badge_color=d1["prs"]["color"],
        ind_warn_sbf_badge_color=d1["sbf"]["color"],
        ind_warn_stboxf_badge_color=d1["stboxf"]["color"],
        # Styles
        gps_style=gps_style,
        ind_warn_stboxf_parent_style=ind_warn_stboxf_parent_style,
        ind_warn_prs_parent_style=ind_warn_prs_parent_style,
        # Other labels
        ind_othr_model_lbl_children=d2["model"]["label"],
        ind_othr_pu_type_lbl_children=d2["pu_type"]["label"],
        ind_othr_has_slave_lbl_children=d2["has_slave"]["label"],
        ind_othr_startup_lbl_children=d2["startup"]["label"],
        ind_othr_op_hours_lbl_children=d2["op_hours"]["label"],
        ind_othr_surface_lbl_children=d2["surface"]["label"],
        ind_othr_gps_lbl_children=d2["gps"]["label"],
        ind_othr_warehouse_lbl_children=d2["warehouse"]["label"],
        ind_othr_pu_lbl_children=d2["pu"]["label"],
        ind_othr_struct_lbl_children=d2["struct"]["label"],
        ind_othr_aws_lbl_children=d2["aws"]["label"],
        ind_othr_gateway_type_lbl_children=d2["gateway_type"]["label"],
        ind_othr_wl_lbl_children=d2["wl"]["label"],
        ind_othr_sig_lbl_children=d2["sig"]["label"],
        ind_othr_imei_lbl_children=d2["imei"]["label"],
        ind_othr_apn_lbl_children=d2["apn"]["label"],
        ind_othr_phone_lbl_children=d2["phone"]["label"],
        ind_othr_swv_lbl_children=d2["swv"]["label"],
        ind_othr_swvp_lbl_children=d2["swvp"]["label"],
        ind_othr_restarts_lbl_children=d2["restarts"]["label"],
        ind_othr_rest_mins_lbl_children=d2["rest_mins"]["label"],
        ind_othr_suction_range_lbl_children=d2["suction_range"]["label"],
        # Other badges
        ind_othr_model_badge_children=d2["model"]["msg"],
        ind_othr_pu_type_badge_children=d2["pu_type"]["msg"],
        ind_othr_has_slave_badge_children=d2["has_slave"]["msg"],
        ind_othr_startup_badge_children=d2["startup"]["msg"],
        ind_othr_op_hours_badge_children=d2["op_hours"]["msg"],
        ind_othr_surface_badge_children=d2["surface"]["msg"],
        ind_othr_gps_badge_children=d2["gps"]["msg"],
        ind_othr_warehouse_badge_children=d2["warehouse"]["msg"],
        ind_othr_pu_badge_children=d2["pu"]["msg"],
        ind_othr_struct_badge_children=d2["struct"]["msg"],
        ind_othr_aws_badge_children=d2["aws"]["msg"],
        ind_othr_gateway_type_badge_children=d2["gateway_type"]["msg"],
        ind_othr_wl_badge_children=d2["wl"]["msg"],
        ind_othr_sig_badge_children=d2["sig"]["msg"],
        ind_othr_imei_badge_children=d2["imei"]["msg"],
        ind_othr_apn_badge_children=d2["apn"]["msg"],
        ind_othr_phone_badge_children=d2["phone"]["msg"],
        ind_othr_swv_badge_children=d2["swv"]["msg"],
        ind_othr_swvp_badge_children=d2["swvp"]["msg"],
        ind_othr_restarts_badge_children=d2["restarts"]["msg"],
        ind_othr_rest_mins_badge_children=d2["rest_mins"]["msg"],
        ind_othr_suction_range_badge_children=d2["suction_range"]["msg"],
        # Other badge hrefs
        ind_othr_gps_badge_href=d2["gps"]["href"],
        ind_othr_aws_badge_href=d2["aws"]["href"],
        ind_othr_swvp_badge_href=d2["swvp"]["href"],
        # Other badge target
        ind_othr_gps_badge_target="_blank",
        ind_othr_aws_badge_target="_blank",
        ind_othr_swvp_badge_target="_blank",
        # # VPN access
        # vpn_startup_col_style=vpn_startup_col_style,
        # Other badge colors
        ind_othr_model_badge_color=d2["model"]["color"],
        ind_othr_pu_type_badge_color=d2["pu_type"]["color"],
        ind_othr_has_slave_badge_color=d2["has_slave"]["color"],
        ind_othr_startup_badge_color=d2["startup"]["color"],
        ind_othr_op_hours_badge_color=d2["op_hours"]["color"],
        ind_othr_surface_badge_color=d2["surface"]["color"],
        ind_othr_gps_badge_color=d2["gps"]["color"],
        ind_othr_warehouse_badge_color=d2["warehouse"]["color"],
        ind_othr_pu_badge_color=d2["pu"]["color"],
        ind_othr_struct_badge_color=d2["struct"]["color"],
        ind_othr_aws_badge_color=d2["aws"]["color"],
        ind_othr_gateway_type_badge_color=d2["gateway_type"]["color"],
        ind_othr_wl_badge_color=d2["wl"]["color"],
        ind_othr_sig_badge_color=d2["sig"]["color"],
        ind_othr_imei_badge_color=d2["imei"]["color"],
        ind_othr_apn_badge_color=d2["apn"]["color"],
        ind_othr_phone_badge_color=d2["phone"]["color"],
        ind_othr_swv_badge_color=d2["swv"]["color"],
        ind_othr_swvp_badge_color=d2["swvp"]["color"],
        ind_othr_restarts_badge_color=d2["restarts"]["color"],
        ind_othr_rest_mins_badge_color=d2["rest_mins"]["color"],
        ind_othr_suction_range_badge_color=d2["suction_range"]["color"],
        # Alarms
        ind_alarms_row_style=ind_alarms_row_style,
        ind_alarms_list_group_children=ind_alarms_list_group_children,
        record_visit_rcom_indicators_data=record_visit_rcom,
    )


@callback(
    Output("indicators_msg_refresh_btn", "n_clicks"),
    Input("btn_clear_all_alerts", "n_clicks"),
    State("indicators_msg_refresh_btn", "n_clicks"),
    State("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def clear_all_active_alerts_in_aws_iot_shadow(
    btn_clear_all_alerts_n_clicks,
    indicators_msg_refresh_btn_n_clicks,
    store_structure_id_data,
    store_unit_type_id_data,
):
    """Clear all active alerts in AWS IoT device shadow"""
    log_function_caller()

    if not btn_clear_all_alerts_n_clicks:
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()
    aws_thing = structure_obj.aws_thing

    # Get the AWS IoT device shadow (latest indicators)
    # The above update_shadow() only gets the metrics it updates
    shadow = get_iot_device_shadow(aws_thing)

    # Get any alerts that are available
    active_alerts_xfer: list = get_alerts_from_shadow(shadow, unit_type_lower="xfer")
    active_alerts_uno: list = get_alerts_from_shadow(shadow, unit_type_lower="uno")
    active_alerts_all: list = active_alerts_xfer + active_alerts_uno

    # Clear all active alerts in AWS IoT device shadow
    updated_shadow: dict = {
        "state": {"reported": {metric: None for metric in active_alerts_all}}
    }
    update_shadow(updated_shadow, aws_thing)

    if isinstance(indicators_msg_refresh_btn_n_clicks, int):
        return indicators_msg_refresh_btn_n_clicks + 1
    return 1
