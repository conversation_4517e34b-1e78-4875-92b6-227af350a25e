import dash_bootstrap_components as dbc
import pandas as pd
from dash import Input, Output, State, callback, dash_table, html
from dash.exceptions import PreventUpdate
from flask_login import current_user
from markupsafe import Markup
from shared.models.models import (
    Customer,
    StructureVw,
    work_order_power_unit_rel,
    work_order_structure_rel,
)
from shared.models.models_work_order import Maintenance, Service, WorkOrder
from sqlalchemy import or_

from app import db, get_user_cust_ids, user_is_demo_customer, user_is_ijack_employee
from app.config import (
    SERVICE_TYPE_ID_NEW_INSTALLATION,
    SERVICE_TYPE_ID_PARTS,
    SERVICE_TYPE_ID_PREV_MAINT,
    SERVICE_TYPE_ID_REPAIR,
)
from app.dashapp.utils import (
    get_structure_obj,
    get_unit_type,
    get_work_order_link,
    log_function_caller,
)

STYLE_ALIGN_TEXT_CENTER = {"width": "100%", "text-align": "center"}


def get_maintenance_row():
    """Row with maintenance for the unit"""
    return dbc.Row(
        style={"display": "none"},
        id="maintenance_row",
        justify="center",
        class_name="mt-4",
        children=[
            dbc.Col(
                xs=12,
                xxl=10,
                children=[
                    dbc.Card(
                        [
                            dbc.CardHeader(
                                dbc.Spinner(
                                    [
                                        dbc.Row(
                                            [
                                                dbc.Col(
                                                    "Maintenance for this unit",
                                                    id="maintenance_card_header",
                                                    width="auto",
                                                ),
                                                dbc.Col(
                                                    dbc.Row(
                                                        [
                                                            dbc.Col(
                                                                dbc.Button(
                                                                    [
                                                                        html.I(
                                                                            className="fa-solid fa-refresh me-1"
                                                                        ),
                                                                        "REFRESH",
                                                                    ],
                                                                    id="maintenance_refresh_btn",
                                                                    n_clicks=0,
                                                                    color="primary",
                                                                    outline=True,
                                                                    size="sm",
                                                                    class_name="mb-1 lh-1",
                                                                    # style={"display": "none"},
                                                                    # external_link=True,
                                                                ),
                                                                width="auto",
                                                            ),
                                                        ],
                                                        # class_name="g-2 justify-content-end",
                                                        class_name="g-2",
                                                        justify="end",
                                                    ),
                                                    width="auto",
                                                ),
                                            ],
                                            # class_name="justify-content-between",
                                            justify="between",
                                        ),
                                    ],
                                    color="success",
                                ),
                            ),
                            dbc.CardBody(
                                dbc.Spinner(
                                    html.Div(id="maintenance_card_body"),
                                    color="success",
                                    # type='grow'
                                ),
                                style={"margin-x": "0.25rem"},
                            ),
                        ],
                    ),
                ],
            ),
        ],
    )


# Can't memoize this since it'll change if the user pauses his/her alerts
# @cache_memoize_if_prod(timeout=60)
def get_maintenance(power_unit_id: int, structure_id: int) -> pd.DataFrame:
    """Get the maintenance for each structure by querying alerts and remote control tables"""

    maint_rows = Maintenance.query.filter_by(structure_id=structure_id).all()
    service_rows = Service.query.filter_by(structure_id=structure_id).all()

    work_order_or_filters = [work_order_structure_rel.c.structure_id == structure_id]
    if power_unit_id:
        # DGAS types don't have power_unit_id
        work_order_or_filters.append(
            work_order_power_unit_rel.c.power_unit_id == power_unit_id
        )

    work_order_rows = (
        WorkOrder.query.join(
            work_order_structure_rel,
            WorkOrder.id == work_order_structure_rel.c.work_order_id,
            isouter=True,
        )
        .join(
            work_order_power_unit_rel,
            WorkOrder.id == work_order_power_unit_rel.c.work_order_id,
            isouter=True,
        )
        .filter(
            or_(
                *work_order_or_filters,
            ),
            WorkOrder.service_type_id.in_(
                [
                    SERVICE_TYPE_ID_NEW_INSTALLATION,
                    SERVICE_TYPE_ID_REPAIR,
                    SERVICE_TYPE_ID_PARTS,
                    SERVICE_TYPE_ID_PREV_MAINT,
                ]
            ),
        )
        .order_by(WorkOrder.date_service.desc())
        .all()
    )

    maint_rows_list = [
        {
            "timestamp_utc": row.timestamp_utc.replace(
                hour=0, minute=0, second=0, microsecond=0
            ),
            "maintenance_type": f"{row.maintenance_type_rel.name} (Customer Reset)",
            "id": Markup(
                f"<a href='/admin/maintenance/details/?id={row.id}'>Maintenance Done {row.id}</a>"
            ),
            "creator_company_id": "",
            # Markdown presentation column for description
            "description": f"<p>{row.description}</p>",
            "files_uploaded": "",
        }
        for row in maint_rows
    ]

    service_requests_list = []
    for row in service_rows:
        # service_request_url = url_for("service_requests.export", id=row.id)
        resolved_str = "(Resolved)" if row.is_resolved else "(Unresolved)"
        maintenance_type = f"Service Request for {getattr(row.service_type_rel, 'name', 'Unknown')} {resolved_str}"
        service_requests_list.append(
            {
                "timestamp_utc": row.timestamp_utc.replace(
                    hour=0, minute=0, second=0, microsecond=0
                ),
                "maintenance_type": maintenance_type,
                "id": Markup(
                    f"<a href='/admin/service_requests/details/?id={row.id}'>Service Request {row.id}</a>"
                ),
                "creator_company_id": getattr(
                    db.session.get(Customer, getattr(row, "user_id", None)), "id", ""
                ),
                # Markdown presentation column for description
                "description": f"<p>{row.description}</p>",
                "files_uploaded": "",
            }
        )

    work_order_rows_list = []
    for row in work_order_rows:
        work_order_link: str | Markup = get_work_order_link(row)
        work_order_rows_list.append(
            {
                "timestamp_utc": row.date_service,
                "maintenance_type": row.service_type_rel.name,
                "id": work_order_link,
                "creator_company_id": row.creator_company_id,
                # Markdown presentation column for description
                "description": Markup(
                    f"""<span style='font-weight: bold;'>Work Done (Internal)</span><br>{row.work_done}<br><br>
            <span style='font-weight: bold;'>Service Required</span><br>{row.service_required}"""
                ),
                "files_uploaded": Markup(
                    "<br>".join(
                        f'{index + 1} - <a href="/download-workorderuploadfile/{model.id}">{model.file_name}</a>'
                        for index, model in enumerate(row.work_order_upload_files_rel)
                    )
                ),
            }
        )

    rows = maint_rows_list + service_requests_list + work_order_rows_list

    if len(rows) == 0:
        return pd.DataFrame()

    df = pd.DataFrame(rows, columns=rows[0].keys())
    # Sort by timestamp
    df["timestamp_utc"] = pd.to_datetime(df["timestamp_utc"])
    df = df.sort_values(["timestamp_utc"], ascending=False)
    # Convert timestamp to string
    df["timestamp_utc"] = df["timestamp_utc"].dt.strftime("%Y-%m-%d")

    return df


@callback(
    Output("maintenance_row", "style"),
    Output("maintenance_card_header", "children"),
    Output("maintenance_card_body", "children"),
    Input("store_structure_id", "data"),
    Input("pause_alerts_btn_label", "children"),
    Input("maintenance_refresh_btn", "n_clicks"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def update_maintenance(
    store_structure_id_data,
    pause_alerts_btn_label_children,
    maintenance_refresh_btn_n_clicks,
    tab_uno_egas,
    store_unit_type_id,
):
    """Maintenance records data table"""
    log_function_caller()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    unit_type_lower, _ = get_unit_type(tab_uno_egas, store_unit_type_id)

    # Identify the demo user for which to make fake locations
    user_cust_ids: tuple = get_user_cust_ids(user_id=user_id)
    if user_is_demo_customer(user_id=user_id, user_cust_ids=user_cust_ids):
        title = "Maintenance Records for Unit"
    else:
        customer_first_word = structure_obj.customer.split()[0]
        customer_unit_info = f"{customer_first_word} {unit_type_lower.upper()} {structure_obj.power_unit_str} at {structure_obj.surface}"
        title = f"Maintenance Records for {customer_unit_info}"

    def return_variables(
        maintenance_row_style: dict = {},
        maintenance_card_header: str = "",
        maintenance_card_body=html.H3("No maintenance to Display"),
    ):
        return (
            maintenance_row_style,
            maintenance_card_header,
            maintenance_card_body,
        )

    df = get_maintenance(structure_obj.power_unit_id, structure_obj.id)

    # Check if there are any remaining maintenance records
    if len(df.index) == 0:
        return return_variables({}, title, html.H3("No Maintenance to Display"))

    data_table_cols = [
        {"id": "timestamp_utc", "name": "Date", "type": "text"},
        {"id": "maintenance_type", "name": "Maintenance Type", "type": "text"},
    ]
    if user_is_ijack_employee(user_id=user_id, user_cust_ids=user_cust_ids):
        # Include a link to the work order
        data_table_cols.append({"id": "id", "name": "Link", "presentation": "markdown"})
        # df["id"] = df.apply(get_work_order_link, axis=1)

        # Another HTML column for the description
        data_table_cols.append(
            {
                "id": "description",
                "name": "Description (IJACK-Only)",
                "presentation": "markdown",
            }
        )
        data_table_cols.append(
            {
                "id": "files_uploaded",
                "name": "Files Uploaded (IJACK-Only)",
                "presentation": "markdown",
            }
        )

    df = df.sort_values(["timestamp_utc"], ascending=False)
    df_data: list = df.to_dict("records")
    # tooltip_data: list = [
    #     {"item": {"value": row["description"], "type": "markdown"}}
    #     for row in df_data
    # ]
    card_body = dash_table.DataTable(
        data=df_data,
        sort_action="native",
        editable=False,
        columns=data_table_cols,
        # tooltip_data=tooltip_data,
        # tooltip_delay=0,  # appear immediately
        # tooltip_duration=None,  # None = tooltips remain visible while hovering over them
        # page_action="native",  # pagination
        # page_size=20,  # num rows to display
        style_table={
            "fontFamily": "Open Sans, sans-serif",
            "fontSize": "1rem",
            "width": "100%",
            # Scrollable for small viewports
            "overflowX": "auto",  # 'auto' or 'scroll' (both seem to work, and not work at times...)
            # 'margin': "0.5rem"
        },
        style_header={
            "backgroundColor": "white",
            "fontFamily": "Open Sans, sans-serif",
            "fontSize": "1rem",
            "fontWeight": "bold",
            # Wrap text (whiteSpace: "normal")
            "whiteSpace": "normal",
            "textAlign": "left",
            "lineHeight": "1.5rem",
        },
        style_data={
            "whiteSpace": "normal",
            "height": "auto",
            "fontFamily": "Open Sans, sans-serif",
            "fontSize": "1rem",
            "lineHeight": "1.5rem",
        },
        style_cell={
            "fontSize": "1rem",
            "textAlign": "left",
            # Padding-left is important actually, both between columns and on the left
            "padding": "0.5rem",
            "color": "rgb(33, 37, 41)",
        },
        style_data_conditional=[
            {"if": {"row_index": "even"}, "backgroundColor": "rgba(0, 0, 0, 0.05)"}
        ],
        # No vertical grid lines
        # style_as_list_view=True,
        # Allow HTML in markdown where "presentation": "markdown"
        markdown_options={"html": True},
    )

    return return_variables({}, title, card_body)
