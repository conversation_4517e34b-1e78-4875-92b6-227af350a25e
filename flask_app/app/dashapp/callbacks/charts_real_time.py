import time
from datetime import datetime, timedelta, timezone

import dash_bootstrap_components as dbc
import pandas as pd
import plotly.graph_objects as go
import pytz
from dash import Input, Output, State, callback, dcc, html, no_update
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import (
    Gw,
    PowerUnit,
    StructureVw,
)
from sqlalchemy import select

from app import db, user_is_demo_customer
from app.config import (
    ACTIVE_TAB_RT_CHARTS,
    GATEWAY_TYPE_ID_AXIOMTEK,
    GATEWAY_TYPE_ID_COMPULAB,
    TAB_CHARTS,
    UNIT_TYPE_ID_UNO,
    UNIT_TYPE_ID_UNOGAS,
    unit_type_upper_dict,
)
from app.dashapp.callbacks.charts_utils import get_time_series_dataframe
from app.dashapp.layout_utils import get_card_header_with_spinner
from app.dashapp.metrics import (
    bootstrap_colours,
    defaults_xfer_egas,
    get_labels,
    names_xfer_egas,
)
from app.dashapp.utils import (
    get_id_triggered,
    get_structure_obj,
    log_function_caller,
    update_shadow,
)
from app.models.models import Structure
from app.utils.simple import FriendlyTime, convert_date_string_to_datetime, utcnow_naive


def get_rt_range_selector_buttons():
    """Get the custom range selector buttons for the real-time chart"""
    cn = "me-1 mb-1 py-0 px-1"
    st = {"font-size": "0.8em"}
    cl = "secondary"
    return html.Div(
        className="player text-start",
        id="main_graph_rt_rangeselector_buttons",
        style={"min-width": "400px"},
        children=[
            # The first button is offset-1 so it appears above the chart in the right spot
            dbc.Button(
                "1m",
                id="rt_rng_btn_1",
                color=cl,
                class_name=cn + " offset-1",
                style=st,
            ),
            dbc.Button("2m", id="rt_rng_btn_2", color=cl, class_name=cn, style=st),
            dbc.Button("5m", id="rt_rng_btn_3", color=cl, class_name=cn, style=st),
            dbc.Button("10m", id="rt_rng_btn_4", color=cl, class_name=cn, style=st),
            dbc.Button("20m", id="rt_rng_btn_5", color=cl, class_name=cn, style=st),
            dbc.Button("1h", id="rt_rng_btn_6", color=cl, class_name=cn, style=st),
            dbc.Button("2h", id="rt_rng_btn_7", color=cl, class_name=cn, style=st),
        ],
    )


def get_main_rt_chart_row() -> dbc.Row:
    """Get the layout for the real-time chart"""
    return html.Div(
        id="charts_div_rt",
        style={"display": "none"},
        children=[
            dbc.Row(
                class_name="mb-2",
                children=[
                    dbc.Col(
                        # width="auto",
                        children=dbc.Card(
                            dbc.CardBody(
                                [
                                    dbc.Switch(
                                        id="rt_data_manual_refresh_switch",
                                        label="Manual refresh mode (disable automatic chart refresh)",
                                        label_style=dict(display="block"),
                                        label_class_name="mb-0",
                                        value=False,
                                        persistence=True,
                                        persistence_type="local",
                                        style={"display": "inline-block"},
                                    ),
                                    dbc.Spinner(
                                        dbc.FormText(
                                            id="rt_data_manual_refresh_form_text",
                                            color="success",
                                        ),
                                        color="success",
                                    ),
                                ],
                            )
                        ),
                    ),
                ],
            ),
            dbc.Row(
                dbc.Col(
                    dbc.Card(
                        id="rt_chart_card",
                        class_name="mb-3",
                        children=[
                            get_card_header_with_spinner(
                                "main_graph_card_title_rt",
                                # Don't use a spinner for the RT chart
                                want_spinner=False,
                                refresh_btn_id="chart_rt_refresh_btn",
                            ),
                            dbc.CardBody(
                                [
                                    get_rt_range_selector_buttons(),
                                    dbc.Spinner(
                                        color="success",
                                        delay_show=1000,
                                        show_initially=False,
                                        # A place to put a message, such as "no data available for this unit"
                                        children=html.Div(id="main_graph_rt_msg"),
                                    ),
                                    dcc.Graph(
                                        id="main_graph_rt",
                                        # Disable the ModeBar with the Plotly logo and other buttons
                                        config=dict(
                                            displayModeBar=False,
                                        ),
                                        style={"display": "none"},
                                    ),
                                    html.Div(
                                        html.A(
                                            "Download CSV",
                                            id="rt_chart_download_csv",
                                            href="#",
                                            className="btn btn-outline-secondary btn-sm mt-2",
                                        ),
                                        id="rt_chart_download_csv_div",
                                    ),
                                ],
                            ),
                        ],
                    ),
                ),
            ),
        ],
    )


@callback(
    Output("rt_hist_request_datetime", "value"),
    Input("store_structure_id", "data"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("tabs_for_normal_or_rt_charts", "active_tab"),
    Input("rt_hist_request_datetime_store", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=False,
)
def update_rt_hist_request_datetime(
    store_structure_id_data,
    active_tab_1,
    active_tab_2,
    rt_hist_request_datetime_store_value,
    store_unit_type_id,
) -> str:
    """Update the real-time chart's request datetime input field"""
    log_function_caller()

    # Early validation - group all the validation logic together
    if (
        store_structure_id_data is None
        or active_tab_1 != TAB_CHARTS
        or active_tab_2 != ACTIVE_TAB_RT_CHARTS
    ):
        raise PreventUpdate()

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    # Calculate the datetime value to return
    if rt_hist_request_datetime_store_value:
        # Use the stored value, formatted as time only
        rt_hist_request_datetime_value = pd.to_datetime(
            rt_hist_request_datetime_store_value
        ).strftime("%H:%M")
    else:
        # Default to current time minus 15 minutes in the structure's timezone
        tz_wanted = pytz.timezone(structure_obj.time_zone)
        rt_hist_request_datetime_value = (
            datetime.now(tz_wanted) - timedelta(minutes=15)
        ).strftime("%H:%M")

    return rt_hist_request_datetime_value


@callback(
    Output("main_graph_rt", "figure"),
    Output("main_graph_rt", "style"),
    Output("main_graph_rt_msg", "children"),
    Output("main_graph_card_title_rt", "children"),
    Output("rt_data_x_mins_form_text", "children"),
    Output("rt_data_x_mins_form_text", "color"),
    Output("rt_data_manual_refresh_form_text", "children"),
    Output("rt_data_manual_refresh_form_text", "color"),
    Output("record_visit_rcom_charts_real_time", "data"),
    Output("rt_chart_download_csv", "href"),
    Output("rt_chart_download_csv_div", "style"),
    Input("items_to_chart", "value"),
    Input("rt_charts_refresh_interval", "n_intervals"),
    Input("chart_rt_refresh_btn", "n_clicks"),
    Input("store_rt_rangeselector", "data"),
    Input("tabs_for_unogas_uno_egas", "active_tab"),
    State("store_structure_id", "data"),
    State("store_tabs_for_ind_charts_control_log", "data"),
    State("tabs_for_normal_or_rt_charts", "active_tab"),
    State("store_rt_countdown", "data"),
    State("category_items_options", "value"),
    State("store_unit_type_id", "data"),
    State("rt_data_manual_refresh_switch", "value"),
    prevent_initial_call=True,
)
def update_real_time_graph(
    cols_chosen,
    rt_charts_refresh_interval_n_intervals,
    chart_refresh_btn_n_clicks,
    store_rt_rangeselector_data,
    tab_uno_egas,
    store_structure_id_data,
    active_tab_1,
    active_tab_2,
    data,
    category_items_options,
    store_unit_type_id,
    rt_data_manual_refresh_switch_value,
):
    """Real time (fast) time series chart"""
    log_function_caller()

    if (
        store_structure_id_data is None
        or cols_chosen is None
        or active_tab_1 != TAB_CHARTS
        or active_tab_2 != ACTIVE_TAB_RT_CHARTS
    ):
        raise PreventUpdate()

    # Default to UTC if no time zone is set
    tz_wanted = timezone.utc

    def return_vars(
        msg: str = "No data to display",
        main_graph_rt_figure: go.Figure = go.Figure(),
        main_graph_rt_style: dict = {"display": "none"},
        main_graph_card_title_rt: str = no_update,
        rt_data_x_mins_form_text: str = no_update,
        rt_data_x_mins_form_text_color: str = no_update,
        rt_data_manual_refresh_form_text: str = "",
        rt_data_manual_refresh_form_text_color: str = no_update,
        record_visit_rcom: str = "rcom_charts_real_time",
        rt_chart_download_csv_href: str = "#",
        rt_chart_download_csv_div_style: dict = {"display": "none"},
    ):
        """Default return variables"""
        if msg:
            main_graph_rt_msg = html.H3(msg, className="offset-1")
        else:
            main_graph_rt_msg = None

        return (
            main_graph_rt_figure,
            main_graph_rt_style,
            main_graph_rt_msg,
            main_graph_card_title_rt,
            rt_data_x_mins_form_text,
            rt_data_x_mins_form_text_color,
            rt_data_manual_refresh_form_text,
            rt_data_manual_refresh_form_text_color,
            record_visit_rcom,
            rt_chart_download_csv_href,
            rt_chart_download_csv_div_style,
        )

    # Get some stored data from memory
    if data is None:
        minutes_requested = 5
        seconds_until = 60 * 5
    else:
        minutes_requested = data.get("minutes_requested", 0)
        ts_stop = data.get("ts_stop_rt_data", 0)
        seconds_until = max(0, ts_stop - round(time.time()))

    # Ensure this page doesn't keep refreshing forever if the user just leaves this tab open
    id_triggered: str = get_id_triggered()
    if id_triggered == "rt_charts_refresh_interval.n_intervals":
        if rt_data_manual_refresh_switch_value is True:
            # We're in manual-refresh mode, so don't refresh automatically
            raise PreventUpdate()
        if rt_charts_refresh_interval_n_intervals > 100:
            if data is None or seconds_until == 0:
                # raise PreventUpdate()
                return return_vars(
                    msg="Please refresh the page to see more real time data",
                    main_graph_rt_figure=no_update,
                    main_graph_rt_style={},
                    main_graph_card_title_rt=no_update,
                    rt_data_x_mins_form_text=no_update,
                    rt_data_manual_refresh_form_text="",
                    rt_data_manual_refresh_form_text_color="success",
                )

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    customer_first_word = structure_obj.customer.split()[0]
    unit_type_upper = unit_type_upper_dict.get(store_unit_type_id, "")
    # More informative title
    customer_unit_info = f"{customer_first_word} {unit_type_upper} {structure_obj.power_unit_str} at {structure_obj.surface}"
    title = f"Real Time Data for {customer_unit_info}"
    has_rcom: bool = bool(structure_obj.aws_thing)
    is_vessel_level_mode: bool = structure_obj.suction_range == 4

    if store_unit_type_id in (UNIT_TYPE_ID_UNO, UNIT_TYPE_ID_UNOGAS):
        label = "UNO and UNOGAS units cannot yet generate real time data, only XFER and EGAS."
        label += " Please use the normal charts instead, while IJACK works to add real time data to UNO and UNOGAS units as well."
    else:
        friendly_time = FriendlyTime(seconds=seconds_until)
        label = f"Real time data requested for {minutes_requested} minutes, ending in {friendly_time.elapsed_time} at {friendly_time.datetime_future}."

    # Convert from UTC to the customer's local time
    try:
        tz_wanted = pytz.timezone(structure_obj.time_zone)
    except pytz.exceptions.UnknownTimeZoneError:
        current_app.logger.exception(f"Unknown time_zone: '{structure_obj.time_zone}'")
        return return_vars(
            msg="Please set the time zone under 'Account/Admin/Units/Unit Information' to see chart data",
            main_graph_card_title_rt=title,
            rt_data_x_mins_form_text=label,
        )

    # Identify the demo user for which to make fake locations
    if user_is_demo_customer(user_id=user_id):
        msg = (
            "Real time data is disabled in demo mode. Please contact the administrator."
        )
        return return_vars(
            msg=msg,
            main_graph_card_title_rt=f"Real Time Data for {customer_first_word} {unit_type_upper}",
            rt_data_x_mins_form_text=msg,
            rt_data_x_mins_form_text_color="danger",
            rt_data_manual_refresh_form_text=msg,
            rt_data_manual_refresh_form_text_color="danger",
        )

    if structure_obj.gateway_type_id not in (
        GATEWAY_TYPE_ID_AXIOMTEK,
        GATEWAY_TYPE_ID_COMPULAB,
    ):
        msg = "Real-time data only available from Axiomtek and Compulab gateway types"
        return return_vars(
            msg=msg,
            main_graph_card_title_rt=f"Real Time Data for {customer_first_word} {unit_type_upper}",
            rt_data_x_mins_form_text=msg,
            rt_data_x_mins_form_text_color="danger",
            rt_data_manual_refresh_form_text=msg,
            rt_data_manual_refresh_form_text_color="danger",
        )

    if not has_rcom:
        return return_vars(
            msg="Unit doesn't have RCOM",
            main_graph_card_title_rt=title,
            rt_data_x_mins_form_text=label,
        )

    # Find the unit_type-specific column name in the appropriate dictionary
    if store_unit_type_id in (UNIT_TYPE_ID_UNO, UNIT_TYPE_ID_UNOGAS):
        return return_vars(
            main_graph_card_title_rt=title, rt_data_x_mins_form_text=label
        )
        # cols_chosen_db_fields = [names_uno_unogas[x] for x in cols_chosen if x in names_uno_unogas.keys()]
        # if len(cols_chosen_db_fields) == 0:
        #     cols_chosen = defaults_uno_unogas # not unit-type-specific
        #     cols_chosen_db_fields = [names_uno_unogas[x] for x in defaults_uno_unogas] # unit-type-specific
    else:
        cols_chosen_db_fields = [
            names_xfer_egas[x] for x in cols_chosen if x in names_xfer_egas.keys()
        ]
        if len(cols_chosen_db_fields) == 0:
            cols_chosen = defaults_xfer_egas  # not unit-type-specific
            cols_chosen_db_fields = [
                names_xfer_egas[x] for x in defaults_xfer_egas
            ]  # unit-type-specific

    use_kpa = "kpa_not_psi" in category_items_options
    use_oz_per_inch2_for_suction = "oz_per_inch2_for_suction" in category_items_options
    use_cf = "cf_not_m3" in category_items_options
    use_barrels = "barrels_not_m3" in category_items_options
    use_fahrenheit = "fahrenheit_not_celsius" in category_items_options

    # Get the saved RT start time, based on the rangeselector button last clicked
    if not isinstance(store_rt_rangeselector_data, dict):
        minutes_wanted = 2
    else:
        minutes_wanted = store_rt_rangeselector_data.get("minutes_wanted", 2)
    x_axis_start_utc = datetime.now(timezone.utc) - timedelta(minutes=minutes_wanted)
    rt_start_str = x_axis_start_utc.strftime("%Y-%m-%d %H:%M:%S")

    dff = get_time_series_dataframe(
        power_unit_str=structure_obj.power_unit_str,
        cols_chosen=cols_chosen_db_fields,
        is_rt=True,
        use_kpa=use_kpa,
        use_oz_per_inch2_for_suction=use_oz_per_inch2_for_suction,
        use_cf=use_cf,
        use_barrels=use_barrels,
        use_fahrenheit=use_fahrenheit,
        rt_start_str=rt_start_str,
    )
    if dff is None:
        return return_vars(
            main_graph_card_title_rt=title, rt_data_x_mins_form_text=label
        )
    # Can't get the length of a NoneType, so do this after the None check
    n_records: int = len(dff)

    dff["timestamp_utc"] = pd.to_datetime(dff["timestamp_utc"])
    dff["timestamp_local"] = (
        dff["timestamp_utc"].dt.tz_localize(pytz.utc).dt.tz_convert(tz_wanted)
    )

    # Find where the x-axis starts and ends
    dt_format_str = "%Y-%m-%d %H:%M:%S"
    x_axis_start_local = (
        x_axis_start_utc.astimezone(pytz.utc)
        .astimezone(tz_wanted)
        .strftime(dt_format_str)
    )
    x_axis_end_local = dff["timestamp_local"].max().strftime(dt_format_str)

    # Create figure
    fig = go.Figure()
    item_labels_colors = get_labels(
        use_kpa,
        use_oz_per_inch2_for_suction,
        use_cf,
        use_barrels,
        use_fahrenheit,
        is_vessel_level_mode,
    )

    for col_not_specific, col_specific in zip(cols_chosen, cols_chosen_db_fields):
        if col_specific in dff.columns:
            fig.add_trace(
                # go.Scattergl(
                go.Scatter(
                    x=list(dff["timestamp_local"]),
                    y=list(dff[col_specific]),
                    name=item_labels_colors[col_not_specific]["label"],
                    line=dict(
                        color=item_labels_colors[col_not_specific]["color"],
                        # shape='spline',
                        shape="linear",  # ['linear', 'hv', 'vh', 'hvh', 'vhv']
                        # smoothing=.2
                    ),
                    # visible="legendonly",
                    hoverlabel={"namelength": -1},
                )
            )

    # Add range slider
    fig.update_layout(
        colorway=bootstrap_colours,
        xaxis=dict(
            range=[x_axis_start_local, x_axis_end_local],
            type="date",
            gridcolor="rgb(238,238,238)",
        ),
        yaxis=dict(
            gridcolor="rgb(238,238,238)",
        ),
        hovermode="closest",
        height=400,  # 500 is a bit too big on a smartphone
        legend=dict(
            font=dict(color="#717174"),
            orientation="h",  # Looks much better horizontal than vertical
            y=-0.2,
        ),
        font={
            "family": "Segoe UI",
            "color": "#717174",
        },
        # Added more margin on the left side to fix the cutoff True/False labels on the booleans
        margin=dict(l=40, r=25, b=0, t=0, pad=0),
        plot_bgcolor="white",
    )

    record_visit_rcom = "rcom_charts_real_time"

    rt_data_manual_refresh_form_text: str = f"Displaying {n_records} data points from {x_axis_start_local} to {x_axis_end_local}..."
    rt_data_manual_refresh_form_text_color: str = "success"
    if n_records > 1000:
        rt_data_manual_refresh_form_text += " This is a lot of data points! Please consider reducing the time range or the number of items to chart."
        rt_data_manual_refresh_form_text_color = "danger"

    download_csv_href = f"/rcom/download_csv_chart_rt?pu={structure_obj.power_unit_str}"
    download_csv_href += f"&ut={store_unit_type_id}&start={rt_start_str}"
    download_csv_href += f"&use_kpa={use_kpa}&tzw={tz_wanted}"
    download_csv_href += f"&use_cf={use_cf}&use_barrels={use_barrels}&is_vessel_level_mode={is_vessel_level_mode}"
    if not user_is_demo_customer(user_id=user_id):
        # If it's not the demo user, add the location parameter
        download_csv_href += f"&location={structure_obj.location}"

    return return_vars(
        msg=None,
        main_graph_rt_figure=fig,
        main_graph_rt_style={},
        main_graph_card_title_rt=title,
        rt_data_x_mins_form_text=label,
        rt_data_x_mins_form_text_color="success",
        rt_data_manual_refresh_form_text=rt_data_manual_refresh_form_text,
        rt_data_manual_refresh_form_text_color=rt_data_manual_refresh_form_text_color,
        record_visit_rcom=record_visit_rcom,
        rt_chart_download_csv_href=download_csv_href,
        rt_chart_download_csv_div_style={},
    )


@callback(
    Output("store_rt_rangeselector", "data"),
    # Change the colour of the button if clicked
    Output("rt_rng_btn_1", "active"),
    Output("rt_rng_btn_2", "active"),
    Output("rt_rng_btn_3", "active"),
    Output("rt_rng_btn_4", "active"),
    Output("rt_rng_btn_5", "active"),
    Output("rt_rng_btn_6", "active"),
    Output("rt_rng_btn_7", "active"),
    Input("rt_rng_btn_1", "n_clicks"),
    Input("rt_rng_btn_2", "n_clicks"),
    Input("rt_rng_btn_3", "n_clicks"),
    Input("rt_rng_btn_4", "n_clicks"),
    Input("rt_rng_btn_5", "n_clicks"),
    Input("rt_rng_btn_6", "n_clicks"),
    Input("rt_rng_btn_7", "n_clicks"),
    prevent_initial_call=True,
)
def store_rt_rangeselector_value(_1, _2, _3, _4, _5, _6, _7):
    """Store which real time chart rangeselector button has been clicked"""
    log_function_caller()

    # Find which id in the inputs has been triggered
    id_triggered: str = get_id_triggered()
    if id_triggered is None:
        raise PreventUpdate()

    data = {}
    # Button "active" colour states
    active_state_btn_1 = False
    active_state_btn_2 = False
    active_state_btn_3 = False
    active_state_btn_4 = False
    active_state_btn_5 = False
    active_state_btn_6 = False
    active_state_btn_7 = False

    # Only use days, hours, and minutes
    if "rt_rng_btn_1" in id_triggered:
        minutes_wanted = 1
        active_state_btn_1 = True
    elif "rt_rng_btn_2" in id_triggered:
        minutes_wanted = 2
        active_state_btn_2 = True
    elif "rt_rng_btn_3" in id_triggered:
        minutes_wanted = 5
        active_state_btn_3 = True
    elif "rt_rng_btn_4" in id_triggered:
        minutes_wanted = 10
        active_state_btn_4 = True
    elif "rt_rng_btn_5" in id_triggered:
        minutes_wanted = 20
        active_state_btn_5 = True
    elif "rt_rng_btn_6" in id_triggered:
        minutes_wanted = 60
        active_state_btn_6 = True
    elif "rt_rng_btn_7" in id_triggered:
        minutes_wanted = 120
        active_state_btn_7 = True
    else:
        raise PreventUpdate()

    data = {
        "minutes_wanted": minutes_wanted,
    }
    return (
        data,
        # Change the colour of the button if clicked
        active_state_btn_1,
        active_state_btn_2,
        active_state_btn_3,
        active_state_btn_4,
        active_state_btn_5,
        active_state_btn_6,
        active_state_btn_7,
    )


@callback(
    Output("store_rt_countdown", "data"),
    Input("btn_rt_data_x_mins", "n_clicks"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("tabs_for_normal_or_rt_charts", "active_tab"),
    Input("store_structure_id", "data"),
    State("rt_data_x_mins_input_id", "value"),
    State("store_rt_countdown", "data"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def request_real_time_data(
    n_clicks,
    active_tab_1,
    active_tab_2,
    store_structure_id_data,
    minutes_requested,
    data,
    store_unit_type_id,
):
    """
    On button click, send a command to the gateway's
    AWS IoT device shadow to generate real time data
    """
    log_function_caller()
    if (
        store_structure_id_data is None
        or active_tab_1 != TAB_CHARTS
        or active_tab_2 != ACTIVE_TAB_RT_CHARTS
    ):
        raise PreventUpdate()

    if minutes_requested is None:
        # For convenience, this can be 2 minutes,
        # even if there's no value in the input box
        # and the user clicks the "Submit" button
        minutes_requested = 1

    # When will the real time data end?
    ts_stop = round(time.time()) + (60 * minutes_requested)

    if data is None:
        data = {
            "ts_stop_rt_data": ts_stop,
            "minutes_requested": minutes_requested,
        }
    else:
        data["ts_stop_rt_data"] = ts_stop
        data["minutes_requested"] = minutes_requested

    # Request a data refresh from the gateway
    try:
        encoded_value = f"{ts_stop}_{int(minutes_requested)}"
    except Exception:
        raise PreventUpdate()

    d = {"state": {"desired": {"AWS_REAL_TIME_DATA_MINS_WANTED": encoded_value}}}

    # Update the AWS IoT device shadow, using the unit's "AWS_THING" ID
    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    if store_unit_type_id in (UNIT_TYPE_ID_UNO, UNIT_TYPE_ID_UNOGAS):
        # Ensure it stops right now since UNO and UNOGAS don't have real time data yet
        data = {
            "ts_stop_rt_data": round(time.time()),
            "minutes_requested": 0,
        }
    else:
        try:
            update_shadow(d, structure_obj.aws_thing)
        except Exception:
            raise PreventUpdate()

    return data


@callback(
    Output("rt_charts_refresh_interval", "disabled", allow_duplicate=True),
    Output("chart_rt_refresh_btn", "color"),
    Output("chart_rt_refresh_btn", "style"),
    Input("rt_data_manual_refresh_switch", "value"),
    prevent_initial_call=True,
)
def rt_data_manual_refresh_switch_style(rt_data_manual_refresh_switch_value):
    """
    This is for disabling the automatic refresh interval,
    and setting the style of the real time chart refresh button.
    """
    log_function_caller()

    if rt_data_manual_refresh_switch_value is True:
        # Highlight the button so the user is aware that it's in manual refresh mode
        return True, "primary", {"box-shadow": "0 0 5px yellow"}

    return False, "secondary", {}


@callback(
    Output("rt_data_manual_refresh_switch", "value"),
    Output("rt_data_get_hist_form_text", "children"),
    Output("rt_data_get_hist_form_text", "color"),
    Input("btn_rt_data_get_hist", "n_clicks"),
    Input("btn_rt_data_x_mins", "n_clicks"),
    State("store_structure_id", "data"),
    prevent_initial_call=True,
)
def rt_data_manual_refresh_switch_value(
    btn_rt_data_get_hist_n_clicks,
    btn_rt_data_x_mins_n_clicks,
    store_structure_id_data,
):
    """
    Set the manual refresh switch to True or False based on which button was clicked
    """
    log_function_caller()

    def return_vars(
        rt_data_manual_refresh_switch_value: bool = False,
        rt_data_get_hist_form_text: str = no_update,
        rt_data_get_hist_form_text_color: str = "secondary",
    ):
        """Default return variables"""
        return (
            rt_data_manual_refresh_switch_value,
            rt_data_get_hist_form_text,
            rt_data_get_hist_form_text_color,
        )

    # Get the gateway_type_id since only Axiomteks can request historical data
    gateway_type_id: int = db.session.execute(
        select(Gw.gateway_type_id)
        .join(PowerUnit)
        .join(Structure)
        .where(Structure.id == store_structure_id_data)
    ).scalar()
    if gateway_type_id not in (GATEWAY_TYPE_ID_AXIOMTEK, GATEWAY_TYPE_ID_COMPULAB):
        return return_vars(
            rt_data_manual_refresh_switch_value=False,
            rt_data_get_hist_form_text="Historical data only available from Axiomtek and Compulab gateways",
            rt_data_get_hist_form_text_color="danger",
        )

    id_triggered: str = get_id_triggered()
    if (
        id_triggered == "btn_rt_data_get_hist.n_clicks"
        and btn_rt_data_get_hist_n_clicks
    ):
        return return_vars(
            rt_data_manual_refresh_switch_value=True,
            rt_data_get_hist_form_text="Historical data requested... Please wait and click the 'Refresh' button.",
            rt_data_get_hist_form_text_color="success",
        )
    elif id_triggered == "btn_rt_data_x_mins.n_clicks" and btn_rt_data_x_mins_n_clicks:
        return return_vars(
            rt_data_manual_refresh_switch_value=False,
            rt_data_get_hist_form_text="",
            rt_data_get_hist_form_text_color=no_update,
        )

    raise PreventUpdate()


@callback(
    Output("modal_message_header", "children", allow_duplicate=True),
    Output("modal_message_body", "children", allow_duplicate=True),
    Output("modal_message", "is_open", allow_duplicate=True),
    Output("modal_message", "backdrop", allow_duplicate=True),
    Output("rt_hist_request_datetime_store", "data"),
    Input("btn_rt_data_get_hist", "n_clicks"),
    # Input("btn_request_hist_memory_data_diag", "n_clicks"),
    State("store_chart_category", "data"),
    State("store_tabs_for_ind_charts_control_log", "data"),
    State("store_structure_id", "data"),
    State("store_unit_type_id", "data"),
    State("rt_hist_request_datetime", "value"),
    prevent_initial_call=True,
)
def request_hist_memory_data_from_gateways(
    btn_rt_data_get_hist_n_clicks,
    # btn_request_hist_memory_data_diag_n_clicks,
    store_chart_category_data,
    store_tabs_for_ind_charts_control_log_data,
    store_structure_id_data,
    store_unit_type_id_data,
    rt_hist_request_datetime_value,
):
    """
    This is for automatically requesting saved (but not sent)
    diagnostic and real time data from the gateways
    """
    log_function_caller()

    def return_variables(
        modal_message_header_children=None,
        modal_message_body_children=None,
        modal_message_is_open=False,
        modal_message_backdrop=True,
        rt_hist_request_datetime_store_value: str = no_update,
    ):
        """Default return variables"""
        return (
            modal_message_header_children,
            modal_message_body_children,
            modal_message_is_open,
            modal_message_backdrop,
            rt_hist_request_datetime_store_value,
        )

    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id_data, user_id, store_unit_type_id_data
    )
    if structure_obj is None:
        raise PreventUpdate()
    aws_thing: str = structure_obj.aws_thing

    if structure_obj.gateway_type_id not in (
        GATEWAY_TYPE_ID_AXIOMTEK,
        GATEWAY_TYPE_ID_COMPULAB,
    ):
        return return_variables(
            modal_message_header_children="Gateway Type",
            modal_message_body_children="Real-time data only available from Axiomtek and Compulab gateway types",
            modal_message_is_open=True,
            modal_message_backdrop=True,
        )

    if user_is_demo_customer(user_id=getattr(current_user, "id", None)):
        return return_variables(
            modal_message_header_children="Demo Mode",
            modal_message_body_children="This feature is disabled in demo mode. Please contact the administrator.",
            modal_message_is_open=True,
            rt_hist_request_datetime_store_value=no_update,
        )

    # Request real time data from unit
    aws_iot_desired_metric = "REAL_TIME"
    action = "Real time data requested"

    default_datetime = utcnow_naive() - timedelta(minutes=15)
    datetime_wanted_utc, _ = convert_date_string_to_datetime(
        local_datetime_str=rt_hist_request_datetime_value,
        local_timezone_str=structure_obj.time_zone,
        default_datetime=default_datetime,
        tz_wanted="UTC",
    )

    timestamp_wanted: float = datetime_wanted_utc.timestamp()
    # It'll only send new data if the timestamp is newer, so don't worry about sending this over and over
    random_string = f"{time.time()}_{timestamp_wanted}"
    new_shadow = {
        "state": {"desired": {aws_iot_desired_metric: random_string}},
    }
    update_shadow(new_shadow, aws_thing)
    return return_variables(
        modal_message_header_children="Requested Data from Unit",
        modal_message_body_children=f"{action} from gateway. Please wait and refresh chart",
        modal_message_is_open=True,
        modal_message_backdrop=True,
        rt_hist_request_datetime_store_value=rt_hist_request_datetime_value,
    )
