from datetime import date, datetime, timedelta

import dash_bootstrap_components as dbc
import numpy as np
import pandas as pd
from dash import Input, Output, callback, html
from dash.exceptions import PreventUpdate
from flask_login import current_user
from pandas import DataFrame
from shared.models.models import (
    Customer,
    CustSubGroup,
    TimeSeries,
    TimeSeriesAgg,
    UnitType,
)
from shared.models.models_bom import Warehouse
from sqlalchemy import func, text

from app import db, user_is_demo_customer, user_is_ijack_employee
from app.config import (
    CUST_SUB_GROUP_ID_ALL_GROUPS,
    CUST_SUB_GROUP_ID_ALL_OTHERS,
    CUSTOMER_ID_ALL_CUSTOMERS,
    TAB_PERFORMANCE,
    UNIT_TYPE_ID_ALL_TYPES,
    WAREHOUSE_ID_ALL_WAREHOUSES,
)
from app.dashapp.layout_utils import get_ag_grid
from app.dashapp.metrics import BOOTSTRAP_BLUE_500
from app.dashapp.utils import (
    discrete_background_color_bins,
    get_structures_df_by_filter,
    log_function_caller,
)
from app.databases import get_sqla_query_string, run_sql_query


def get_performance_div():
    """Get the performance div"""
    # Get the current month
    current_month = date.today().replace(day=1)

    # Create a list of months for the past year
    months_list = [
        (current_month - timedelta(days=30 * i)).replace(day=1)
        for i in range(11, -1, -1)
    ]

    # Format the months as strings (optional)
    month_options = [
        {"label": month.strftime("%Y %b"), "value": month} for month in months_list
    ]

    return dbc.Card(
        class_name="mt-3",
        id="performance_div",
        style={"display": "none"},
        children=[
            dbc.CardHeader(
                dbc.Row(
                    [
                        dbc.Col(
                            dbc.Spinner(
                                "Performance",
                                id="performance_card_title",
                                color="success",
                            ),
                            width="auto",
                        ),
                        dbc.Col(
                            dbc.Button(
                                [
                                    html.I(className="fa-solid fa-refresh me-1"),
                                    "Refresh",
                                ],
                                id="performance_refresh_btn",
                                color="primary",
                                outline=True,
                                size="sm",
                                # line-height 1
                                class_name="lh-1",
                            ),
                            width="auto",
                        ),
                    ],
                    justify="between",
                )
            ),
            dbc.CardBody(
                [
                    dbc.Row(
                        dbc.Col(
                            dbc.ListGroup(
                                dbc.ListGroupItem(
                                    [
                                        dbc.Label(
                                            "Months",
                                        ),
                                        dbc.Checklist(
                                            options=month_options,
                                            value=[str(current_month)],
                                            id="performance_months_checklist",
                                            inline=True,
                                            # switch=True,
                                            className="mb-2",
                                        ),
                                    ]
                                )
                            ),
                        )
                    ),
                    dbc.Row(
                        dbc.Col(
                            class_name="my-3",
                            children=[
                                html.Div(
                                    className="mb-3",
                                    children=dbc.Button(
                                        "Download CSV",
                                        id="performance_download_csv",
                                        color="secondary",
                                        outline=True,
                                        size="sm",
                                    ),
                                ),
                                dbc.Spinner(
                                    color="success",
                                    children=get_ag_grid(
                                        id="performance_ag_grid",
                                        # 'sizeToFit' = reduce column widths to fit the width of the container
                                        # columnSize="sizeToFit",
                                        # columnSize="responsiveSizeToFit",
                                        paginationPageSize=5,
                                    ),
                                ),
                            ],
                        ),
                    ),
                ]
            ),
        ],
    )


# Memoize the function, so it only runs once per hour
# @cache_memoize_if_prod(timeout=60 * 60 * 1)
def get_time_series_agg(power_units: list, months: list) -> DataFrame:
    """Get all units for the customer_id"""

    # Get aggregated time series data for the power units
    time_series_query = (
        db.session.query(TimeSeriesAgg)
        .filter(TimeSeriesAgg.power_unit.in_(power_units))
        .filter(TimeSeriesAgg.month_date.in_(months))
    )
    # get SQL query from SQLAlchemy
    sql: str = get_sqla_query_string(time_series_query, log_level=None)
    result, columns = run_sql_query(text(sql), db_name="timescale", as_dict=True)
    time_series_agg_df = pd.DataFrame(result, columns=columns)

    return time_series_agg_df


def get_current_pressures(power_units: list) -> DataFrame:
    """Get current pressure data from TimeSeries for the most recent readings within past 1 hour"""

    # Calculate 1 hour ago from now in UTC
    one_hour_ago = datetime.utcnow() - timedelta(hours=1)

    # Subquery to get the maximum timestamp for each power unit within the past hour
    max_timestamp_subquery = (
        db.session.query(
            TimeSeries.power_unit,
            func.max(TimeSeries.timestamp_utc).label("max_timestamp"),
        )
        .filter(TimeSeries.power_unit.in_(power_units))
        .filter(TimeSeries.timestamp_utc >= one_hour_ago)
        .group_by(TimeSeries.power_unit)
        .subquery()
    )

    # Main query to get current pressure readings
    current_pressure_query = db.session.query(
        TimeSeries.power_unit,
        TimeSeries.cgp.label("current_suction_pressure"),
        TimeSeries.dgp.label("current_discharge_pressure"),
        TimeSeries.timestamp_utc,
    ).join(
        max_timestamp_subquery,
        (TimeSeries.power_unit == max_timestamp_subquery.c.power_unit)
        & (TimeSeries.timestamp_utc == max_timestamp_subquery.c.max_timestamp),
    )

    # Get SQL query from SQLAlchemy
    sql: str = get_sqla_query_string(current_pressure_query, log_level=None)
    result, columns = run_sql_query(text(sql), db_name="timescale", as_dict=True)
    current_pressures_df = pd.DataFrame(result, columns=columns)

    return current_pressures_df


def get_performance_df(
    customer_id: int,
    warehouse_id: int,
    cust_sub_group_id: int,
    unit_type_id: int,
    months: list,
) -> DataFrame:
    """Get the performance DataFrame for the Dash table or CSV download"""

    power_units_df: DataFrame = get_structures_df_by_filter(
        customer_id=customer_id,
        warehouse_id=warehouse_id,
        cust_sub_group_id=cust_sub_group_id,
        unit_type_id=unit_type_id,
    )
    power_unit_strs: list = power_units_df.power_unit_str.unique().tolist()

    performance_df: DataFrame = get_time_series_agg(
        power_units=power_unit_strs, months=months
    )

    # Get current pressure data
    current_pressures_df: DataFrame = get_current_pressures(power_units=power_unit_strs)

    # Merge the performance data with power units data
    df = pd.merge(
        left=performance_df,
        right=power_units_df,
        how="left",
        left_on="power_unit",
        right_on="power_unit_str",
    )

    # Merge with current pressure data
    df = pd.merge(
        left=df,
        right=current_pressures_df,
        how="left",
        left_on="power_unit_str",
        right_on="power_unit",
        suffixes=("", "_current"),
    )
    if user_is_demo_customer(user_id=getattr(current_user, "id", None)):
        # For demo customers, don't show the location
        df["downhole"] = ""
        df["surface"] = "01-01-01-01W1"
        df["location"] = "01-01-01-01W1"
        df["gps_lat"] = 50.16
        df["gps_lon"] = -101.668

    # Combine power unit and surface columns so we can pin them on the left side of the table
    df["pinned_col"] = df["power_unit_str"] + " - " + df["surface"]

    # Averages divided by maximums
    df["hp_usage"] = np.where(
        df["hp_limit"] == 0,
        0,
        df["hp_avg"] / df["hp_limit"] * 100,
    )
    df["discharge_usage"] = np.where(
        df["mgp_avg"] == 0,
        0,
        df["dgp_avg"] / df["mgp_avg"] * 100,
    )
    df["discharge_temp_usage"] = np.where(
        df["agf_dis_temp_max_avg"] == 0,
        0,
        df["agf_dis_temp_avg"] / df["agf_dis_temp_max_avg"] * 100,
    )

    # If dtp_max_avg is None, then set it to max_delta_p,
    # from the model_types table (manually entered)
    df["dtp_max_avg"] = np.where(
        df["dtp_max_avg"].isnull() & ~df["max_delta_p"].isnull(),
        df["max_delta_p"],
        df["dtp_max_avg"],
    )
    df["dtp_usage"] = np.where(
        df["dtp_max_avg"] == 0,
        0,
        df["dtp_avg"] / df["dtp_max_avg"] * 100,
    )
    # Make dtp_usage numeric so it can be rounded
    df["dtp_usage"] = pd.to_numeric(df["dtp_usage"], errors="coerce")

    # Handle current pressure data - fill NaN values for offline units
    df["current_suction_pressure"] = pd.to_numeric(
        df["current_suction_pressure"], errors="coerce"
    )
    df["current_discharge_pressure"] = pd.to_numeric(
        df["current_discharge_pressure"], errors="coerce"
    )

    # Handle monthly average pressure data
    df["cgp_avg"] = pd.to_numeric(df["cgp_avg"], errors="coerce")
    df["dgp_avg"] = pd.to_numeric(df["dgp_avg"], errors="coerce")

    # Round numeric values to 1 decimal point for pressure readings, 0 for usage percentages
    pressure_columns = [
        "current_suction_pressure",
        "current_discharge_pressure",
        "cgp_avg",
        "dgp_avg",
    ]
    df[pressure_columns] = df[pressure_columns].round(1)

    # Round other numeric values to 0 decimal points (REAL in Postgres is 4 bytes np.float64)
    non_pressure_cols = [
        col
        for col in df.select_dtypes(include=[np.number]).columns
        if col not in pressure_columns
    ]
    df[non_pressure_cols] = df[non_pressure_cols].round(0)

    # Format timestamp as month
    df["month_date"] = pd.to_datetime(
        df["month_date"], format="%Y-%m-%d %H:%M:%S"
    ).dt.strftime("%Y-%m")

    # Format as string. If the value is NaN, then make it an empty string
    df["sample_size"] = df["sample_size"].transform(
        lambda x: np.where(np.isnan(x), "", f"{x:,.0f}")
    )

    # Sort by customer, then by unit type
    df = df.sort_values(by=["customer", "unit_type", "model", "month_date"])

    return df


def get_performance_columns(df: pd.DataFrame) -> list:
    """Get the columns for the performance table"""

    columns = [
        {
            "field": "pinned_col",
            "headerName": "Location",
            "headerTooltip": "The power unit and surface location of the unit",
            "pinned": "left",
            "cellDataType": "text",
            # "cellRenderer": "Tooltip",
            "initialWidth": 200,
            "maxWidth": 200,
        },
        {
            "field": "unit_type",
            "headerName": "Unit Type",
            "headerTooltip": "The high-level IJACK type",
            "cellDataType": "text",
            # "headerTooltip": "The name of the warehouse that has the parts",
            # "cellDataType": "text",
            # "sortable": True,
            # "resizable": True,
            # "filter": True,
        },
        {
            "field": "model",
            "headerName": "Model",
            "headerTooltip": "The specific model of the unit",
            "cellDataType": "text",
            # # Don't include this column in the overall resizing
            # "suppressSizeToFit": True,
            # "width": 300,
            # "minWidth": 300,
        },
        {
            "field": "cust_sub_group",
            "headerName": "Sub Group",
            "headerTooltip": "The customer-defined sub-group",
            "cellDataType": "text",
        },
        # {
        #     "field": "surface",
        #     "headerName": "Surface",
        #     "headerTooltip": "The surface location of the unit",
        #     "cellDataType": "text",
        # },
        # {
        #     "field": "power_unit_str",
        #     "headerName": "Power Unit",
        #     "headerTooltip": "The unique identifier for the power unit",
        #     "cellDataType": "text",
        # },
        {
            "field": "power_unit_type",
            "headerName": "Power Unit Type",
            "headerTooltip": "The specific type of power unit",
            "cellDataType": "text",
            # # Don't include this column in the overall resizing
            # "suppressSizeToFit": True,
            # "width": 300,
            # "minWidth": 300,
        },
        {
            "field": "month_date",
            "headerName": "Month",
            "headerTooltip": "The month of the data",
            "cellDataType": "text",
        },
        {
            "field": "sample_size",
            "headerName": "Sample Size",
            "headerTooltip": "The number of data samples in the month",
            "cellDataType": "text",
        },
        {
            "field": "stroke_speed_avg",
            "headerName": "Stroke Speed % of Max",
            "headerTooltip": "The average stroke speed as a percentage of the maximum",
            "cellDataType": "number",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["stroke_speed_avg"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "hp_usage",
            "headerName": "Horsepower % of Max",
            "headerTooltip": "The average horsepower as a percentage of the maximum",
            "cellDataType": "number",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["hp_usage"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "discharge_usage",
            "headerName": "Discharge % of Max",
            "headerTooltip": "The average discharge pressure as a percentage of the maximum",
            "cellDataType": "number",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["discharge_usage"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "discharge_temp_usage",
            "headerName": "Discharge Temp % of Max",
            "headerTooltip": "The average discharge temperature as a percentage of the maximum",
            "cellDataType": "number",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["discharge_temp_usage"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "dtp_usage",
            "headerName": "Delta P % of Max",
            "headerTooltip": "The average delta pressure (discharge minus suction) as a percentage of the maximum",
            "cellDataType": "number",
            "cellStyle": {
                "styleConditions": discrete_background_color_bins(
                    df=df,
                    n_bins=9,
                    columns=["dtp_usage"],
                    reverse=False,
                    color_start=BOOTSTRAP_BLUE_500,
                    is_ag_grid=True,
                )
            },
        },
        {
            "field": "cgp_avg",
            "headerName": "Avg Monthly Suction Pressure",
            "headerTooltip": "Average monthly suction pressure in PSI",
            "cellDataType": "number",
        },
        {
            "field": "dgp_avg",
            "headerName": "Avg Monthly Discharge Pressure",
            "headerTooltip": "Average monthly discharge pressure in PSI",
            "cellDataType": "number",
        },
        {
            "field": "current_suction_pressure",
            "headerName": "Current Suction Pressure",
            "headerTooltip": "Most recent suction pressure reading (within past hour) in PSI",
            "cellDataType": "number",
        },
        {
            "field": "current_discharge_pressure",
            "headerName": "Current Discharge Pressure",
            "headerTooltip": "Most recent discharge pressure reading (within past hour) in PSI",
            "cellDataType": "number",
        },
    ]
    if user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
        # Insert at the start of the dictionary
        columns.insert(
            0, {"field": "customer", "headerName": "Customer", "cellDataType": "text"}
        )

    return columns


@callback(
    Output("performance_ag_grid", "exportDataAsCsv"),
    Input("performance_download_csv", "n_clicks"),
    prevent_initial_call=True,
)
def export_data_as_csv(n_clicks):
    """Export the data as CSV"""
    if n_clicks:
        return True
    return False


@callback(
    Output("performance_ag_grid", "rowData"),
    Output("performance_ag_grid", "columnDefs"),
    Output("performance_card_title", "children"),
    Input("store_tabs_for_ind_charts_control_log", "data"),
    Input("performance_refresh_btn", "n_clicks"),
    Input("performance_months_checklist", "value"),
    Input("customer_radio", "value"),
    Input("warehouse_radio", "value"),
    Input("cust_sub_groups_radio", "value"),
    Input("unit_type_ids_radio", "value"),
    # Input("store_url_search", "data"),
    # State("use_url_search", "data"),
    # Ensure this callback always fires on page load (from url.search prop updating).
    # The only time we don't want it to fire is if the map tab is active.
    prevent_initial_call=True,
)
def make_performance_div(
    active_tab,
    performance_refresh_btn_n_clicks,
    performance_months_checklist_value,
    customer_radio_value,
    warehouse_radio_value,
    cust_sub_groups_radio_value,
    unit_type_ids_radio_value,
    # store_url_search_data,
    # use_url_search_data,
):
    """Make filter for customers (the first filter)"""
    log_function_caller()

    if (
        active_tab != TAB_PERFORMANCE
        or customer_radio_value is None
        or warehouse_radio_value is None
        or cust_sub_groups_radio_value is None
        or unit_type_ids_radio_value is None
    ):
        raise PreventUpdate()

    def return_vars(
        performance_ag_grid_RowData: list,
        performance_ag_grid_columnDefs: list,
        performance_card_title_children: str,
    ) -> tuple:
        """Default return vars"""
        return (
            performance_ag_grid_RowData,
            performance_ag_grid_columnDefs,
            performance_card_title_children,
        )

    df: pd.DataFrame = get_performance_df(
        customer_id=customer_radio_value,
        warehouse_id=warehouse_radio_value,
        cust_sub_group_id=cust_sub_groups_radio_value,
        unit_type_id=unit_type_ids_radio_value,
        months=performance_months_checklist_value,
    )
    rowData = df.to_dict("records")
    columnDefs = get_performance_columns(df)

    if customer_radio_value == CUSTOMER_ID_ALL_CUSTOMERS:
        customer_str = "All Customers"
    else:
        customer = db.session.get(Customer, customer_radio_value)
        if hasattr(customer, "customer"):
            customer_str = customer.customer
        else:
            customer_str = ""

    if user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
        # Only IJACK employees can filter on warehouse
        if warehouse_radio_value == WAREHOUSE_ID_ALL_WAREHOUSES:
            warehouse_str = "All Warehouses, "
        else:
            warehouse = db.session.get(Warehouse, warehouse_radio_value)
            warehouse_str = (
                f"{getattr(warehouse, 'name', 'No Warehouse Name, ')} Warehouse, "
            )
    else:
        warehouse_str = ""

    if cust_sub_groups_radio_value == CUST_SUB_GROUP_ID_ALL_GROUPS:
        cust_sub_group_str = "All Groups"
    elif cust_sub_groups_radio_value == CUST_SUB_GROUP_ID_ALL_OTHERS:
        cust_sub_group_str = "All Others Group"
    else:
        cust_sub_group = db.session.get(CustSubGroup, cust_sub_groups_radio_value)
        cust_sub_group_str = f"{getattr(cust_sub_group, 'name', 'No Group Name')} Group"

    if unit_type_ids_radio_value == UNIT_TYPE_ID_ALL_TYPES:
        unit_type_str = "All Unit Types"
    else:
        unit_type = db.session.get(UnitType, unit_type_ids_radio_value)
        unit_type_str = (
            f"{getattr(unit_type, 'unit_type', 'No unit type found - ')} Types"
        )

    performance_card_title_children = f"Performance Data for {customer_str}, {warehouse_str}{cust_sub_group_str}, {unit_type_str}"

    return return_vars(
        performance_ag_grid_RowData=rowData,
        performance_ag_grid_columnDefs=columnDefs,
        performance_card_title_children=performance_card_title_children,
    )
