import os
from datetime import datetime

import dash_bootstrap_components as dbc
import pytz
from dash import Input, Output, State, callback, dash_table, html
from dash.exceptions import PreventUpdate
from flask import current_app
from flask_login import current_user
from shared.models.models import StructureVw

from app import is_admin, user_is_ijack_employee
from app.config import TAB_CARDS, UNIT_TYPE_ID_EGAS, UNIT_TYPE_ID_XFER
from app.dashapp.utils import (
    get_card_data,
    get_structure_obj,
    get_unit_type,
    log_function_caller,
    query_cluster_id_description_solution,
    scale_and_predict,
)


def get_artificial_intelligence_div() -> dbc.Row:
    """Return the card for the artificial intelligence messages"""
    return dbc.Row(
        dbc.Col(
            dbc.Collapse(
                [
                    dbc.Card(
                        [
                            dbc.CardHeader(
                                "Artificial Intelligence",
                            ),
                            dbc.CardBody(
                                [
                                    dbc.Collapse(
                                        id="card_messages_warning_collapse",
                                        class_name="mt-2 mb-4 p-2",
                                        is_open=False,
                                        # Make it stand out a bit more
                                        style={
                                            # Glowing shadow in IJACK-green
                                            "box-shadow": "0 0 10px #C1D72E",
                                            # Match the outline of the Bootstrap card border
                                            "border-radius": "0.25rem",
                                        },
                                    ),
                                    dbc.Spinner(
                                        id="card_messages",
                                        color="success",
                                        # type='grow'
                                    ),
                                ]
                            ),
                        ]
                    ),
                ],
                id="card_messages_collapse",
                is_open=False,
            ),
            class_name="mt-3",
        ),
    )


def chart_card_ad_hoc(df, timestamp_utc, structure_obj, surface_or_compression):
    """For ad hoc charting of the image the ML estimator sees, for troubleshooting"""
    import numpy as np
    import plotly.graph_objects as go

    # It's important to make a copy so we don't modify the main df on which we're doing ML
    df = df[df["timestamp_utc"] == timestamp_utc].copy()

    if surface_or_compression == "compression":
        # The ML models were trained on downstroke being positive, but charted on negative
        # Pylint won't like this, but this needs to be an ==, not "is True"
        condition = df["is_up"] == False  # noqa: E712
        df["load"] = np.where(condition, df["load"] * -1, df["load"])

    label_hour = str(timestamp_utc)
    print(f"label_hour: {label_hour}")

    fig = go.Figure(
        layout=go.Layout(
            # title=f'compression Card for Cluster {label_hour}',
            # xaxis_title='Position',
            # yaxis_title='Load',
            paper_bgcolor="black",
            plot_bgcolor="black",
            autosize=True,
            # autosize=False,
            # height=224*10, # At first, resolution is 10X bigger
            # width=224*10, # At first, resolution is 10X bigger
            showlegend=False,
            xaxis=dict(
                # range=[0, 1],
                showgrid=False,
                ticks="",
                showticklabels=False,
                zeroline=False,
                visible=False,
            ),
            yaxis=dict(
                # range=[0, 1],
                showgrid=False,
                ticks="",
                showticklabels=False,
                zeroline=False,
                visible=False,
            ),
        )
    )
    # Plot both the upstroke and the downstroke on the same chart,
    # and fill between the lines to make a black and white image (black background, white card)
    for is_up, ud_group in df.groupby("is_up"):
        print(f"is_up: {is_up}")
        fig.add_trace(
            go.Scatter(
                mode="lines",
                x=ud_group["position"],
                y=ud_group["load"],
                name="up" if is_up else "down",
                # Fill white colour between the upstroke and the downstroke
                fill="tonexty" if is_up else "none",
                fillcolor="white",
                line=dict(
                    # color='firebrick' if is_up == 'u' else 'royalblue',
                    color="white",
                    width=4,
                ),
                hoverlabel={"namelength": -1},
            )
        )
    # Show the image in the browser
    fig.show()

    # Scale the image down from 2240x2240 to 224*224 pixels and save it as a .png file
    filename = f"{structure_obj.customer}_{str(structure_obj.power_unit_str).replace('.0', '')}_{label_hour}.png"
    print(f"filename: {os.getcwd()}/{filename}")
    fig.write_image(filename, scale=0.1)

    return None


@callback(
    Output("card_messages", "children"),
    Output("card_messages_collapse", "is_open"),
    Output("card_messages_warning_collapse", "is_open"),
    Output("card_messages_warning_collapse", "children"),
    # Use this as the only input, so it waits until the card chart is loaded
    Input("surface_graph_card_title", "children"),
    # State("surface_graph_date_range", "end_date"),
    State("surface_graph_date_range", "date"),
    State("store_structure_id", "data"),
    State("store_tabs_for_ind_charts_control_log", "data"),
    State("tabs_for_unogas_uno_egas", "active_tab"),
    State("store_unit_type_id", "data"),
    prevent_initial_call=True,
)
def card_machine_learning(
    _,
    # end_date,
    start_date,
    store_structure_id,
    active_tab,
    tab_uno_egas,
    store_unit_type_id,
):
    """
    Once the surface card chart is finished loading,
    do the machine learning to classify patterns
    """
    log_function_caller()

    def return_vars(
        power_unit_str: str | None = None,
        structure_obj: StructureVw | None = None,
        card_messages_children: str = "",
        card_messages_collapse_is_open: bool = False,
        card_messages_warning_collapse_is_open: bool = False,
        card_messages_warning_collapse_children: str = "",
    ):
        """Return values. Do something different if card messages are disabled."""

        if getattr(structure_obj, "website_card_msg", True) is False:
            if not user_is_ijack_employee(user_id=getattr(current_user, "id", None)):
                # If we've disabled card messages, don't show them to non-IJACK employees
                card_messages_children = ""
                card_messages_collapse_is_open = False
            else:
                card_messages_warning_collapse_is_open = True
                # Make a link to the admin page to re-enable them, for IJACK employees only
                if power_unit_str is None:
                    admin_link = "/admin/power_units/"
                    unit_str = "this unit"
                else:
                    admin_link: str = f"/admin/power_units/?flt2_power_unit_serial_in_list={power_unit_str}"
                    unit_str = f"power unit '{power_unit_str}'"
                card_messages_warning_collapse_children = [
                    html.P(
                        html.Strong(
                            f"Artificial intelligence messages are disabled for {unit_str}. Customers don't see anything here for this unit."
                        )
                    ),
                ]
                if is_admin():
                    card_messages_warning_collapse_children.append(
                        html.P(
                            [
                                html.A("Click here", href=admin_link),
                                " to re-enable AI messages so customers can see them too.",
                            ]
                        )
                    )
        return (
            card_messages_children,
            card_messages_collapse_is_open,
            card_messages_warning_collapse_is_open,
            card_messages_warning_collapse_children,
        )

    power_unit_str: str = None
    structure_obj: StructureVw | None = None
    if store_structure_id is None or active_tab != TAB_CARDS:
        # or None in hour_selected or None in cols_chosen:
        return return_vars(power_unit_str=power_unit_str, structure_obj=structure_obj)
    user_id: int = getattr(current_user, "id", None)
    structure_obj: StructureVw | None = get_structure_obj(
        store_structure_id, user_id, store_unit_type_id
    )
    if structure_obj is None:
        raise PreventUpdate()

    power_unit_str = structure_obj.power_unit_str
    unit_type_lower, unit_type_id = get_unit_type(tab_uno_egas, store_unit_type_id)
    try:
        tz_wanted = pytz.timezone(structure_obj.time_zone)
    except Exception:
        current_app.logger.exception(f"Unknown time_zone: '{structure_obj.time_zone}'")
        return return_vars(power_unit_str=power_unit_str, structure_obj=structure_obj)

    dt_format = "%Y-%m-%d"
    dt_format_db = "%Y-%m-%d %H:%M"
    # Create a date object
    start_date_obj_local_naive = datetime.strptime(start_date, dt_format)
    # end_date_obj_local_naive = datetime.strptime(end_date, dt_format)
    # Ensure the end date includes all minutes of that day for the DB query
    end_date_obj_local_naive = start_date_obj_local_naive.replace(
        hour=23, minute=59, second=59, microsecond=59
    )
    # Convert to UTC for the database query
    start_date_obj_utc = tz_wanted.localize(start_date_obj_local_naive).astimezone(
        pytz.utc
    )
    end_date_obj_utc = tz_wanted.localize(end_date_obj_local_naive).astimezone(pytz.utc)
    # Create a string for the database query
    start_date_utc_str = start_date_obj_utc.strftime(dt_format_db)
    end_date_utc_str = end_date_obj_utc.strftime(dt_format_db)

    if unit_type_id in (UNIT_TYPE_ID_EGAS, UNIT_TYPE_ID_XFER):
        surface_or_compression = "compression"
    else:
        surface_or_compression = "surface"
        # need to re-train all the UNOs to 210 features instead of 214
        # return return_vars(power_unit_str=power_unit_str, structure_obj=structure_obj)

    # Don't make the downstroke negative if it's a compression card,
    # since it was trained on positive data and only charted as negative afterward
    df = get_card_data(
        start_date_utc_str, end_date_utc_str, surface_or_compression, power_unit_str
    )

    # The Cinco EGAS units didn't have any data, so no times
    if df is None or len(df) == 0:
        return return_vars(power_unit_str=power_unit_str, structure_obj=structure_obj)

    map_df = query_cluster_id_description_solution(surface_or_compression)
    df2 = scale_and_predict(
        df, map_df, structure_obj, surface_or_compression, power_unit_str
    )
    if df2 is None:
        current_app.logger.error(
            f"ERROR! df2 for power_unit {unit_type_lower.upper()} '{power_unit_str}' is None..."
        )
        return return_vars(power_unit_str=power_unit_str, structure_obj=structure_obj)

    # # # the groupby object we'll be working on
    # # g1 = df.groupby('power_unit_str')

    # # Doesn't seem to work...
    # # df['position'] = g1[['position']].transform(lambda x: scale_pos.fit_transform(x))
    # # df['load'] = g1[['load']].transform(lambda x: scaler_load.fit_transform(x))

    # # Import the pre-trained load scalers
    # ml_path = Path(__file__).absolute().parent.parent.joinpath("ml")
    # if unit_type_lower in ("egas", "xfer"):
    #     scaler_file = ml_path.joinpath(
    #         f"scaler_load_egas_v{current_app.config['ML_VERSION_EGAS']}.gz"
    #     )
    # else:
    #     scaler_file = ml_path.joinpath(
    #         f"scaler_load_uno_v{current_app.config['ML_VERSION_UNO']}.gz"
    #     )

    # # New units might not have custom scalers, and can use default ones instead
    # # power_unit_name_no_colons = str(power_unit_str).replace(':', '.')
    # is_scaler_already_fit = True
    # try:
    #     scaler_load = joblib.load(scaler_file)
    # except Exception:
    #     is_scaler_already_fit = False
    #     scaler_load = MinMaxScaler(feature_range=(0, 1), copy=True)
    #     # if unit_type_lower in ("egas", "xfer"):
    #     #     scaler_load = MinMaxScaler(feature_range=(0, 1), copy=True)
    #     # else:
    #     #     scaler_load = StandardScaler(copy=True, with_mean=True, with_std=True)

    # current_app.logger.debug("Scaling load for machine learning classification...")
    # # this needs a 2D array (i.e. DataFrame, not Series)
    # if is_scaler_already_fit:
    #     assert check_is_fitted(scaler_load) == None, "scaler_load is not fitted!"
    #     assert scaler_load.data_max_[0] != 1., \
    #         "WARNING: No scaling will be done since the fitted parameter max is 1.!"
    #     df["load"] = scaler_load.transform(df[["load"]])
    # else:
    #     # Must fit a new scaler, and then transform the features
    #     df["load"] = scaler_load.fit_transform(df[["load"]])

    # # # For ad hoc charting of the card, for troubleshooting
    # # if os.getenv("FLASK_CONFIG", "production") in ("development", "wsl"):
    # #     chart_card_ad_hoc(df, datetime(2021, 3, 25, 9), structure_obj, surface_or_compression)

    # # for n, g in df.groupby('power_unit_str'):
    # # count the strokes as well?
    # # df.loc[g.index, 'stroke_count'] = ((g['up_down'].shift(1) == 'u') & (g['up_down'] == 'd')).cumsum() + 1
    # # df.loc[g.index, 'load'] = scaler_load.transform(g[['load']])

    # # if unit_type_lower not in ('egas', 'xfer'):
    # #     # Only UNO x-axis data were scaled
    # #     # (totally unnecessary mistake in clustering step)
    # #     # What matters is the number of x-axis features for the matrix, not their values
    # #     try:
    # #         scale_pos = joblib.load(scaler_path.joinpath(f'scaler_position_{power_unit_name_no_colons}.gz'))
    # #     except Exception:
    # #         scale_pos = MinMaxScaler(feature_range=(0, 1), copy=True)
    # #     df.loc[g.index, 'position'] = scale_pos.transform(g[['position']])
    # #     df['position'] = scale_pos.transform(df['position'])

    # # Pivot from long to wide and fill in missing values.
    # df = card_data_pivot_and_fill(df, power_unit_str)
    # _, cols = df.shape
    # if cols != 214:
    #     # Check if we can fix the UNO units, which are missing 4.0 and 216.0
    #     # on both the upstroke and the downstroke
    #     if cols == 210:
    #         for true_false in (True, False):
    #             if (true_false, 6.0) in df.columns and (
    #                 true_false,
    #                 4.0,
    #             ) not in df.columns:
    #                 # Copy position 6 to position 4 (easy, practical fix)
    #                 df[true_false, 4.0] = df[true_false, 6.0]
    #             if (true_false, 214.0) in df.columns and (
    #                 true_false,
    #                 216.0,
    #             ) not in df.columns:
    #                 # Copy position 214 to position 216 (easy, practical fix)
    #                 df[true_false, 216.0] = df[true_false, 214.0]

    #     # Check again after the above tried to fix things
    #     if df.shape[1] != 214:
    #         current_app.logger.error(
    #             f"df.shape needs to be (x, 214) and it's {df.shape}. Exiting function"
    #         )
    #         return return_vars(power_unit_str=power_unit_str, structure_obj=structure_obj)

    # # Put power_unit_str and timestamp_utc back to columns
    # df = df.reset_index()

    # # Remove future hours (which are there so the surface card chart has 24 hours for each frame)
    # df = df[df["timestamp_utc"] <= utcnow_naive()]

    # # Convert to local time
    # df["timestamp_local"] = (
    #     df["timestamp_utc"]
    #     .dt.tz_localize(pytz.utc)
    #     .dt.tz_convert(structure_obj.time_zone)
    # )

    # df = df.drop(columns="timestamp_utc")

    # df = df.set_index(["power_unit_str", "timestamp_local"])

    # # # Set the index like the machine learning model expects it
    # # df = df.index.set_names(['power_unit_str', 'date_hour_utc'])

    # # Aggregate to daily average to get only one classification label per day?
    # # (we'll just classify and report each day's average card for now)
    # # df = df.groupby([df.index.get_level_values(0), pd.Grouper(freq='D', level=1)]).mean()
    # # current_app.logger.debug(f"\ndf daily average: \n{df}")
    # X = df.values
    # # print(X)

    # # Load the PCA (principal components analysis) pre-trained model,
    # # and the SVC (support vector machines classifier) pre-trained model
    # # transformer = None  # PCA, etc
    # if unit_type_lower in ("egas", "xfer"):
    #     # pca = joblib.load(ml_path.joinpath('pca_egas.gz'))
    #     # estimator = joblib.load(ml_path.joinpath('svc_trained_model_egas.gz'))
    #     estimator_path = ml_path.joinpath(
    #         f"2_k_means_egas_v{current_app.config['ML_VERSION_EGAS']}.gz"
    #     )
    #     estimator = joblib.load(estimator_path)
    # else:
    #     # transformer = joblib.load(
    #     #     ml_path.joinpath(f"pca_v{current_app.config['ML_VERSION_UNO']}.gz")
    #     # )
    #     estimator_path = ml_path.joinpath(
    #         f"2_k_means_uno_v{current_app.config['ML_VERSION_UNO']}.gz"
    #     )
    #     estimator = joblib.load(estimator_path)

    # # # Transform with PCA and then predict the labels using the SVC model
    # # if transformer is None:
    # #     X_transformed = X
    # # else:
    # #     try:
    # #         X_transformed = transformer.transform(X)
    # #     except ValueError:
    # #         current_app.logger.exception(
    # #             "Problem with PCA transformation! Displaying nothing."
    # #         )
    # #         return return_vars(power_unit_str=power_unit_str, structure_obj=structure_obj)

    # pred = estimator.predict(X)
    # # current_app.logger.debug(f"\nPredicted labels/cluster IDs: \n{pred}")

    # # if unit_type_lower in ("egas", "xfer"):
    # # DataFrame of the predicted labels (no X-data)
    # df2 = pd.DataFrame(pred, index=df.index, columns=["cluster_id"]).reset_index()
    # map_df = query_cluster_id_description_solution(surface_or_compression)
    # # Merge the tables
    # df2 = df2.merge(map_df, on="cluster_id").sort_values(
    #     "timestamp_local", ascending=False
    # )

    # Keep only the "alertable" hours
    # Pylint won't like this, but this needs to be an ==, not "is True"
    df2 = df2[df2["send_alert"] == True]  # noqa: E712
    # else:
    #     # DataFrame of the predicted labels (no X-data)
    #     df2 = pd.DataFrame(
    #         pred, index=df.index, columns=["pattern_id"]
    #     ).reset_index()
    #     # 0 and 10 = normal operation
    #     df2 = df2[~df2["pattern_id"].isin(0, 10)]
    #     # Get a DataFrame that maps the predicted pattern_id's with info about that pattern
    #     map_df = query_pattern_map(surface_or_compression)
    #     # Merge the tables
    #     df2 = df2.merge(map_df, on="pattern_id").sort_values(
    #         "timestamp_local", ascending=False
    #     )

    df2["timestamp_local_str"] = df2["timestamp_local"].dt.strftime("%b %-d, hour %H")
    df2 = df2[["timestamp_local_str", "description", "explanation", "solution"]]
    df2.columns = [
        "Date",
        "Pattern Identified",
        "Explanation",
        "Potential Solution",
    ]

    current_app.logger.debug("\ncluster_pattern_map: \n%s", map_df)
    # current_app.logger.debug(f"\df2: \n{df2}")

    # Bootstrap version of table
    # table = dbc.Table.from_dataframe(
    #     df2,
    #     striped=True,
    #     bordered=True,
    #     hover=True,
    #     style={
    #         'color': '#717174',
    #         'margin-top': "1rem"
    #     }
    # ),
    if len(df2.index) == 0:
        return return_vars(
            power_unit_str=power_unit_str,
            structure_obj=structure_obj,
            card_messages_children=html.P("No problems identified"),
            card_messages_collapse_is_open=True,
        )

    table = (
        dash_table.DataTable(
            columns=[{"name": i, "id": i} for i in df2.columns],
            data=df2.to_dict("records"),
            page_action="native",  # pagination
            page_size=20,  # num rows to display
            style_header={
                "backgroundColor": "rgb(230, 230, 230)",
                "fontWeight": "bold",
            },
            style_table={
                "width": "100%",
                "overflowX": "auto",  # 'auto' or 'scroll' (both seem to work, and not work at times...)
                # 'margin': "0.5rem"
            },
            # style_data={
            #     # whiteSpace and height serve to wrap the text on small screens
            #     'whiteSpace': 'normal',
            #     # 'height': 'auto'
            # },
            style_cell={
                "textAlign": "left",
                # Padding-left is important actually, both between columns and on the left
                "padding-left": "1rem",
                # 'margin': "0.5rem",
                "color": "#717174",
                # 'height': 'auto',
                # 'whiteSpace': 'normal',
                # 'minWidth': '0px',
                # 'maxWidth': '180px',
                # 'overflow': 'hidden',
                # 'textOverflow': 'ellipsis',
            },
            style_as_list_view=True,
            style_data_conditional=[
                {
                    "if": {"row_index": "odd"},
                    "backgroundColor": "rgb(248, 248, 248)",
                }
            ],
            # content_style='grow',
            # css=[{
            #     'selector': '.dash-cell div.dash-cell-value',
            #     'rule': 'display: inline; white-space: inherit; overflow: inherit; text-overflow: inherit;'
            # }],
            # css=[
            #     {
            #         'selector': 'table',
            #         'rule': 'width: 100%;'
            #     }
            # ],
        ),
    )

    # card = dbc.Card(
    #     [
    #         dbc.CardHeader("Artificial Intelligence", style={'color': '#717174'}),
    #         dbc.CardBody(
    #             table
    #         )
    #     ],
    #     style={'margin-top': "1rem"},
    # )

    return return_vars(
        power_unit_str=power_unit_str,
        structure_obj=structure_obj,
        card_messages_children=table,
        card_messages_collapse_is_open=True,
    )
