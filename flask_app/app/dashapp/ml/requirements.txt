# Python 3.7.6 libraries required to use the saved machine learning models:
joblib==0.16.0
scikit-learn==0.23.1
scipy==1.5.0
Keras==2.3.1

absl-py==0.9.0
alabaster==0.7.12
aplus==0.11.0
argh==0.26.2
astor==0.8.0
astroid==2.3.3
astropy==4.0.1.post1
atomicwrites @ file:///home/<USER>/feedstock_root/build_artifacts/atomicwrites_1588182545583/work
attrs==19.3.0
autopep8==1.4.3
Babel==2.8.0
backcall @ file:///home/<USER>/feedstock_root/build_artifacts/backcall_1592338393461/work
bcrypt==3.1.7
bleach @ file:///home/<USER>/feedstock_root/build_artifacts/bleach_1588608214987/work
blinker==1.4
bokeh @ file:///D:/bld/bokeh_1592869751982/work
boto3==1.9.66
botocore==1.12.189
bqplot @ file:///home/<USER>/feedstock_root/build_artifacts/bqplot_1592851124979/work
branca==0.3.1
brotlipy==0.7.0
cachetools==4.1.0
certifi==2020.6.20
cffi==1.14.0
chardet==3.0.4
click==7.1.2
cloudpickle @ file:///home/<USER>/feedstock_root/build_artifacts/cloudpickle_1593623337153/work
colorama==0.4.3
cryptography==2.9.2
cycler==0.10.0
cytoolz==0.10.1
dask @ file:///home/<USER>/feedstock_root/build_artifacts/dask-core_1593753420471/work
decorator==4.4.2
defusedxml==0.6.0
diff-match-patch==20181111
distributed @ file:///D:/bld/distributed_1593754412232/work
docutils==0.16
entrypoints==0.3
et-xmlfile==1.0.1
flake8==3.7.9
fsspec @ file:///home/<USER>/feedstock_root/build_artifacts/fsspec_1589989738418/work
future==0.18.2
gast==0.2.2
google-auth==1.14.1
google-auth-oauthlib==0.4.1
google-pasta==0.2.0
grpcio==1.27.2
h5py==2.10.0
HeapDict==1.0.1
idna @ file:///tmp/build/80754af9/idna_1593446292537/work
imagecodecs @ file:///D:/bld/imagecodecs_1593295625443/work
imageio==2.8.0
imagesize==1.2.0
importlib-metadata @ file:///D:/bld/importlib-metadata_1593211612489/work
intervaltree==3.0.2
ipydatawidgets @ file:///home/<USER>/feedstock_root/build_artifacts/ipydatawidgets_1592927540203/work
ipykernel @ file:///D:/bld/ipykernel_1590023017759/work/dist/ipykernel-5.3.0-py3-none-any.whl
ipyleaflet @ file:///home/<USER>/feedstock_root/build_artifacts/ipyleaflet_1593588654538/work
ipympl==0.5.6
ipython @ file:///D:/bld/ipython_1593235680304/work
ipython-genutils==0.2.0
ipyvolume @ file:///home/<USER>/feedstock_root/build_artifacts/ipyvolume_1591796094004/work
ipyvue @ file:///home/<USER>/feedstock_root/build_artifacts/ipyvue_1593006296303/work
ipyvuetify @ file:///home/<USER>/feedstock_root/build_artifacts/ipyvuetify_1588673302711/work
ipywebrtc==0.5.0
ipywidgets==7.5.1
isort==4.3.21
jdcal==1.4.1
jedi==0.15.2
Jinja2==2.11.2
jmespath @ file:///home/<USER>/feedstock_root/build_artifacts/jmespath_1589369830981/work

jsonschema==3.2.0
jupyter-client @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_client_1593562235049/work
jupyter-core==4.6.3

Keras-Applications==1.0.8
Keras-Preprocessing==1.1.0
keyring @ file:///D:/bld/keyring_1588434024659/work
kiwisolver==1.2.0
lazy-object-proxy @ file:///D:/bld/lazy-object-proxy_1591367919629/work
llvmlite==0.33.0
locket==0.2.0
Markdown==3.1.1
MarkupSafe==1.1.1
matplotlib==3.2.1
mccabe==0.6.1
mistune==0.8.4
mkl-service==2.3.0
msgpack==1.0.0
nbconvert==5.6.1
nbformat==5.0.6
nest-asyncio @ file:///home/<USER>/feedstock_root/build_artifacts/nest-asyncio_1588514211290/work
networkx==2.4
notebook @ file:///D:/bld/notebook_1588887315004/work
numba==0.50.1
numpy @ file:///D:/bld/numpy_1591485433275/work
numpydoc @ file:///home/<USER>/feedstock_root/build_artifacts/numpydoc_1593656006205/work
oauthlib==3.1.0
olefile==0.46
openpyxl @ file:///home/<USER>/feedstock_root/build_artifacts/openpyxl_1593690790606/work
opt-einsum==3.1.0
packaging @ file:///home/<USER>/feedstock_root/build_artifacts/packaging_1589925210001/work
pandas @ file:///D:/bld/pandas_1592422178599/work
pandocfilters==1.4.2
paramiko==2.7.1
parso==0.5.2
partd==1.1.0
pathtools==0.1.2
patsy==0.5.1
pexpect==4.8.0
pickleshare==0.7.5
Pillow @ file:///D:/bld/pillow_1593694005242/work
plotly @ file:///home/<USER>/feedstock_root/build_artifacts/plotly_1593202648564/work
pluggy @ file:///D:/bld/pluggy_1592827687923/work
progressbar2 @ file:///home/<USER>/feedstock_root/build_artifacts/progressbar2_1593145458914/work
prometheus-client @ file:///home/<USER>/feedstock_root/build_artifacts/prometheus_client_1590412252446/work
prompt-toolkit @ file:///home/<USER>/feedstock_root/build_artifacts/prompt-toolkit_1592500439797/work
protobuf==3.12.3
psutil==5.7.0
psycopg2==2.8.5
pyarrow==0.17.1
pyasn1==0.4.8
pyasn1-modules==0.2.7
pycodestyle==2.5.0
pycparser==2.20
pydocstyle==5.0.2
pyflakes==2.1.1
Pygments==2.6.1
PyJWT==1.7.1
pylint==2.4.4
PyNaCl==1.3.0
pyOpenSSL==19.1.0
pyparsing==2.4.7
PyQt5==5.12.3
PyQt5-sip==4.19.18
PyQtChart==5.12
PyQtWebEngine==5.12.1
pyreadline==2.1
pyrsistent==0.16.0
PySocks==1.7.1
python-dateutil==2.8.1
python-jsonrpc-server @ file:///home/<USER>/feedstock_root/build_artifacts/python-jsonrpc-server_1588783985962/work
python-language-server==0.31.10
python-utils==2.4.0
pythreejs==2.2.0
pytz==2020.1
PyWavelets==1.1.1
pywin32==227
pywin32-ctypes==0.2.0
pywinpty==0.5.7
PyYAML==5.3.1
pyzmq==19.0.1
QDarkStyle==2.8.1
QtAwesome @ file:///home/<USER>/feedstock_root/build_artifacts/qtawesome_1588715536397/work
qtconsole @ file:///home/<USER>/feedstock_root/build_artifacts/qtconsole_1592845750760/work
QtPy==1.9.0
requests @ file:///tmp/build/80754af9/requests_1592841827918/work
requests-oauthlib==1.3.0
retrying==1.3.3
rope @ file:///home/<USER>/feedstock_root/build_artifacts/rope_1588950285934/work
rsa==4.0
Rtree==0.9.4
s3fs==0.2.2
s3transfer==0.1.13
scikit-image==0.17.2

seaborn @ file:///home/<USER>/feedstock_root/build_artifacts/seaborn-base_1591878760859/work
Send2Trash==1.5.0
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1590081179328/work
snowballstemmer==2.0.0
sortedcontainers @ file:///home/<USER>/feedstock_root/build_artifacts/sortedcontainers_1591999956871/work
Sphinx @ file:///home/<USER>/feedstock_root/build_artifacts/sphinx_1592165595094/work
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==1.0.3
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.4
spyder @ file:///D:/bld/spyder_1588993919517/work
spyder-kernels @ file:///D:/bld/spyder-kernels_1588785911039/work
statsmodels @ file:///D:/bld/statsmodels_1591963449919/work
tabulate==0.8.7
tblib==1.6.0
tensorboard==2.2.1
tensorboard-plugin-wit==1.6.0
tensorflow==2.1.0
tensorflow-estimator @ file:///home/<USER>/feedstock_root/build_artifacts/tensorflow-estimator_1592819788686/work/tensorflow_estimator-2.2.0-py2.py3-none-any.whl
termcolor==1.1.0
terminado==0.8.3
testpath==0.4.4
threadpoolctl @ file:///tmp/tmp79xdzxkt/threadpoolctl-2.1.0-py3-none-any.whl
tifffile @ file:///home/<USER>/feedstock_root/build_artifacts/tifffile_1593913740982/work
toolz==0.10.0
tornado==6.0.4
traitlets==4.3.3
traittypes==0.2.1
typing-extensions @ file:///home/<USER>/feedstock_root/build_artifacts/typing_extensions_1588470653596/work
ujson==1.35
urllib3==1.25.9
vaex-arrow @ file:///home/<USER>/feedstock_root/build_artifacts/vaex-arrow_1591300356629/work
vaex-astro @ file:///home/<USER>/feedstock_root/build_artifacts/vaex-astro_1590441909348/work
vaex-core @ file:///D:/bld/vaex-core_1591799849835/work
vaex-hdf5 @ file:///home/<USER>/feedstock_root/build_artifacts/vaex-hdf5_1590441393702/work
vaex-jupyter @ file:///home/<USER>/feedstock_root/build_artifacts/vaex-jupyter_1591988653787/work
vaex-ml @ file:///home/<USER>/feedstock_root/build_artifacts/vaex-ml_1590442846357/work
vaex-server @ file:///home/<USER>/feedstock_root/build_artifacts/vaex-server_1591300223654/work
vaex-viz @ file:///home/<USER>/feedstock_root/build_artifacts/vaex-viz_1590441948645/work
watchdog @ file:///D:/bld/watchdog_1593120742469/work
wcwidth @ file:///home/<USER>/feedstock_root/build_artifacts/wcwidth_1592931742287/work
webencodings==0.5.1
Werkzeug==0.14.1
widgetsnbextension==3.5.1
win-inet-pton==1.1.0
wincertstore==0.2
wrapt==1.12.1
xarray==0.15.1
xlrd==1.2.0
yapf==0.29.0
zict==2.0.0
zipp==3.1.0
