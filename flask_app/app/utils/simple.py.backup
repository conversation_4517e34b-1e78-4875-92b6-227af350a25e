import os
import random
import string
import subprocess
from datetime import datetime, timedelta, timezone
from functools import wraps
from math import atan2, cos, radians, sin, sqrt
from pathlib import Path
from typing import List, Tuple

import jwt
import pandas as pd
import pytz
from bs4 import BeautifulSoup as bs
from flask import Flask, current_app, redirect, request, url_for
from flask import typing as ft
from flask.signals import request_finished
from flask.wrappers import Response
from flask_login import current_user
from itsdangerous import URLSafeTimedSerializer
from werkzeug.exceptions import HTTPException

# NOTE the below datetime functions are adapted from this <PERSON> blog post:
# https://blog.miguelgrinberg.com/post/it-s-time-for-a-change-datetime-utcnow-is-now-deprecated


def utcnow_aware() -> datetime:
    """Get the current time in UTC, with timezone info attached"""
    return datetime.now(timezone.utc)


def utcnow_naive() -> datetime:
    """Get the current time in UTC, without timezone info"""
    return utcnow_aware().replace(tzinfo=None)


def utcfromtimestamp_aware(timestamp: float) -> datetime:
    """Convert a timestamp to a UTC datetime object with timezone info attached"""
    return datetime.fromtimestamp(timestamp, timezone.utc)


def utcfromtimestamp_naive(timestamp: float) -> datetime:
    """Convert a timestamp to a UTC datetime object without timezone info"""
    return utcfromtimestamp_aware(timestamp).replace(tzinfo=None)


class FriendlyTime:
    """A class to represent a friendly time since an event."""

    def __init__(self, seconds: float):
        """Initialize the FriendlyTime object with the time since the event."""
        self.seconds = max(0.0, float(seconds))
        self.mins = round(self.seconds / 60, 1)
        self.hours = round(self.mins / 60, 1)
        self.days = round(self.hours / 24, 1)

    @property
    def elapsed_time(self) -> str:
        """Return a friendly string representation of the time since the event."""
        if self.seconds < 60:
            return f"{self.seconds} secs"
        elif self.mins < 60:
            return f"{self.mins} mins"
        elif self.hours < 24:
            return f"{self.hours} hours"
        else:
            return f"{self.days} days"

    @property
    def color_time_since(self) -> str:
        """Return the color for the time since the event."""
        if self.seconds < 60:
            return "success"
        elif self.mins < 16:
            return "warning"
        else:
            return "danger"

    @property
    def datetime_future(self) -> str:
        """Return the future datetime as a string."""
        return (utcnow_aware() + timedelta(seconds=self.seconds)).strftime(
            "%Y-%m-%d %H:%M:%S %Z"
        )


class Flask2(Flask):
    """A subclass of Flask that adds a custom finalize_request method."""

    # Dictionary to hold custom functions
    functions: dict = {}

    def finalize_request(
        self,
        rv: ft.ResponseReturnValue | HTTPException,
        from_error_handler: bool = False,
    ) -> Response:
        """Given the return value from a view function this finalizes
        the request by converting it into a response and invoking the
        postprocessing functions.  This is invoked for both normal
        request dispatching as well as error handlers.

        Because this means that it might be called as a result of a
        failure a special safe mode is available which can be enabled
        with the `from_error_handler` flag.  If enabled, failures in
        response processing will be logged and otherwise ignored.

        :internal:
        """
        # Find out which view function is handling the request
        if not rv:
            raise ValueError(
                f"View function '{request.url_rule.endpoint}' did not return a response"
            )

        response = self.make_response(rv)
        try:
            response = self.process_response(response)
            request_finished.send(
                self, _async_wrapper=self.ensure_sync, response=response
            )
        except Exception as error:
            print(error)
            if not from_error_handler:
                raise
            self.logger.exception(
                "Request finalizing failed with an error while handling an error"
            )
        return response


def convert_date_string_to_datetime(
    local_datetime_str: str,
    local_timezone_str: str,
    default_datetime: datetime = None,
    tz_wanted: str = "UTC",
) -> Tuple[datetime, str]:
    """Convert a local datetime string to UTC using pytz, the datetime module, and a local timezone string."""

    # If the default datetime is not provided, use the current UTC datetime
    default_datetime = default_datetime or utcnow_naive()
    default_return_tuple = (
        default_datetime,
        default_datetime.strftime("%Y-%m-%d %H:%M"),
    )

    if not local_datetime_str:
        current_app.logger.error("ERROR: No local datetime string provided")
        return default_return_tuple

    if not local_timezone_str:
        current_app.logger.error("ERROR: No local timezone string provided")
        return default_return_tuple

    # Parse the local datetime string to a datetime object
    try:
        local_datetime = pd.to_datetime(local_datetime_str)
    except Exception:
        current_app.logger.error(
            f"ERROR: Unable to parse the local datetime string with pd.to_datetime('{local_datetime_str}')"
        )
        return default_return_tuple

    # Get the local timezone
    try:
        local_timezone = pytz.timezone(local_timezone_str)
    except pytz.exceptions.UnknownTimeZoneError:
        current_app.logger.error(f"ERROR: Unknown timezone '{local_timezone_str}'")
        return default_return_tuple

    try:
        tz_wanted = pytz.timezone(tz_wanted)
    except pytz.exceptions.UnknownTimeZoneError:
        current_app.logger.error(f"ERROR: Unknown timezone '{tz_wanted}'")
        return default_return_tuple

    # Localize the datetime object to the local timezone
    local_datetime = local_timezone.localize(local_datetime)

    # Convert the localized datetime to UTC
    datetime_utc = local_datetime.astimezone(tz_wanted)

    datetime_format = "%Y-%m-%d %H:%M"
    datetime_wanted_str = datetime_utc.strftime(datetime_format)

    return datetime_utc, datetime_wanted_str


def create_jwt_encoded_token(expires_in: timedelta = None, **kwargs) -> str:
    """
    Create a jwt-encoded token with an expiry key and keyword arguments (kwargs)
    """

    # Create the dictionary contents to be encoded
    contents = {
        # "user_id": self.id,
    }
    contents.update(kwargs)

    if isinstance(expires_in, timedelta):
        contents["exp"] = utcnow_naive() + expires_in

    # Create the encoded token
    token_encoded = jwt.encode(
        contents,
        os.getenv("TWILIO_AUTH_TOKEN"),
        algorithm="HS256",
    )
    try:
        token_decoded = token_encoded.decode("utf-8")
    except AttributeError:
        # Error on FATBOXs with newer package versions.
        # 'str' object has no attribute 'decode'.
        # jwt.encode must return something
        # like 'eyJ0...' instead of b'eyJ0...' in the newer versions.
        token_decoded = token_encoded

    return token_decoded


def decode_jwt_token(token) -> dict:
    """Given a jwt-encoded token, return its decoded contents"""
    try:
        token_decoded = jwt.decode(
            token, os.getenv("TWILIO_AUTH_TOKEN"), algorithms=["HS256"]
        )
    except jwt.exceptions.ExpiredSignatureError:
        current_app.logger.error("ERROR: JSON web token has expired!")
        return {
            "error": "JSON web token has expired!",
        }
    except Exception as err:
        current_app.logger.exception("ERROR decoding JWT JSON web token!")
        return {"error": f"ERROR decoding JWT JSON web token: {err}"}

    return token_decoded


def generate_confirmation_token(email: str) -> str:
    """
    Use the URLSafeTimedSerializer to generate a token using the email address
    obtained during user registration. The actual email is encoded in the token.
    The token is valid for 3600 seconds (1 hour) and is salted with the
    SECRET_KEY and SECURITY_PASSWORD_SALT from the Flask app configuration.
    """
    serializer = URLSafeTimedSerializer(os.getenv("SECRET_KEY"))
    return serializer.dumps(email, salt=os.getenv("SECURITY_PASSWORD_SALT"))


def generate_confirmation_code() -> int:
    """
    Generate a 6-digit confirmation code.
    """
    return random.randint(100000, 999999)


def confirm_token(token: str, expiration: int = 3600) -> str | bool:
    """
    Confirm the token generated by the generate_confirmation_token method.
    If the token is valid, return the email address. If the token is invalid,
    return False.
    """
    serializer = URLSafeTimedSerializer(os.getenv("SECRET_KEY"))
    try:
        email: str = serializer.loads(
            token,
            salt=os.getenv("SECURITY_PASSWORD_SALT"),
            max_age=expiration,
        )
    except Exception:
        return False
    return email


def get_unsubscribe_email_lines(
    email: str,
    power_unit_str: str = None,
    all_alerts: bool = True,
    unit: bool = False,
    report_email_hourly: bool = False,
    report_email_derates: bool = False,
    op_hours_100: bool = False,
    op_hours_1000: bool = False,
    service: bool = False,
):
    """
    From the cloud/Docker-based Alerts 'helpers.py' file:
    Add lines to the message for the user to unsubscribe
    """

    if os.getenv("FLASK_CONFIG") in ("development", "wsl"):
        website = "http://app.localhost:5000"
    else:
        website = "https://myijack.com"

    unsub_lines = f"Sent to: {email}<br>"
    if unit:
        unsub_unit_token = create_jwt_encoded_token(
            expires_in=None,
            email=email,
            unsubscribe_type="unit_alerts",
            power_unit_str=power_unit_str,
            on_or_off="off",
            message=None,
        )
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/update_email_preferences/{unsub_unit_token}">Unsubscribe from this unit</a><br>'

    if report_email_hourly:
        unsub_report_email_hourly_token = create_jwt_encoded_token(
            expires_in=None,
            email=email,
            unsubscribe_type="report_email_hourly",
            on_or_off="off",
            message=None,
        )
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/admin/hourly_update_report/">Manage these "hourly update" emails in the "Admin" site</a><br>'
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/update_email_preferences/{unsub_report_email_hourly_token}">Unsubscribe from this report</a><br>'

    if report_email_derates:
        unsub_report_email_derates_token = create_jwt_encoded_token(
            expires_in=None,
            email=email,
            unsubscribe_type="report_email_derates",
            on_or_off="off",
            message=None,
        )
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/admin/derates_report/">Manage these "derates" emails in the "Admin" site</a><br>'
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/update_email_preferences/{unsub_report_email_derates_token}">Unsubscribe from this report</a><br>'

    if op_hours_100:
        unsub_op_hours_100_token = create_jwt_encoded_token(
            expires_in=None,
            email=email,
            unsubscribe_type="op_hours_100",
            on_or_off="off",
            message=None,
        )
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/admin/op_hours_report/">Manage your op-hours emails in the "Admin" site</a><br>'
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/update_email_preferences/{unsub_op_hours_100_token}">Unsubscribe from 100 op-hours emails</a><br>'

    if op_hours_1000:
        unsub_op_hours_1000_token = create_jwt_encoded_token(
            expires_in=None,
            email=email,
            unsubscribe_type="op_hours_1000",
            on_or_off="off",
            message=None,
        )
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/admin/op_hours_report/">Manage your op-hours emails in the "Admin" site</a><br>'
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/update_email_preferences/{unsub_op_hours_1000_token}">Unsubscribe from 1000 op-hours emails</a><br>'

    if service:
        unsub_service_token = create_jwt_encoded_token(
            expires_in=None,
            email=email,
            unsubscribe_type="service",
            on_or_off="off",
            message=None,
        )
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/update_email_preferences/{unsub_service_token}">Unsubscribe from service emails</a><br>'

    if all_alerts:
        unsub_all_token = create_jwt_encoded_token(
            expires_in=None,
            email=email,
            unsubscribe_type="all_units",
            on_or_off="off",
            message=None,
        )
        unsub_lines += f'<a style="color: #c1d72e;" href="{website}/update_email_preferences/{unsub_all_token}">Unsubscribe from all emails</a><br>'

    return unsub_lines


def geodesic(
    lat1_dec: float, lon1_dec: float, lat2_dec: float, lon2_dec: float
) -> float:
    """
    Calculate the distance between two GPS coordinates.
    The incoing lat and lon values are in decimal format.
    They need to be converted to radians before using the Haversine formula.
    The radians function needs positive values, so we use the abs() function.
    https://stackoverflow.com/a/19412565/3385948
    """
    lat1 = radians(abs(lat1_dec))
    lon1 = radians(abs(lon1_dec))
    lat2 = radians(abs(lat2_dec))
    lon2 = radians(abs(lon2_dec))

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    # Radius of Earth in kilometers = 6371.0
    distance = 6371.0 * c

    return distance


def calc_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    Calculate the distance between two GPS coordinates
    https://stackoverflow.com/a/43211266/3385948
    """
    try:
        km = geodesic(lat1, lon1, lat2, lon2)
    except (ValueError, TypeError):
        km = 2000.0

    return km


def check_confirmed(func):
    """
    Decorator to check if the current user has a confirmed email address.
    Works very similar to the @login_required decorator.
    """

    @wraps(func)
    def decorated_function(*args, **kwargs):
        # is_confirmed: bool = getattr(current_user, "is_confirmed", False)
        # date_created: date = getattr(current_user, "date_created", date(2021, 9, 1))
        if not getattr(current_user, "is_confirmed", False):
            # and date_created >= date(2024, 8, 12):
            return redirect(url_for("auth.email_not_confirmed"))
        return func(*args, **kwargs)

    return decorated_function


def generate_random_string(
    length: int = 8,
    characters: List[str] = [
        string.ascii_letters,
        string.ascii_uppercase,
        string.digits,
        # string.punctuation,
        r"[]()-— -{};:=<>_+^#$@!%*?&",
    ],
) -> str:
    """
    Generate a random string of letters and digits
    """
    # Generate the random string
    counter: int = 0
    random_string: str = ""
    for _ in range(length):
        random_string += random.choice(characters[counter])
        counter += 1
        if counter == len(characters):
            counter = 0

    return random_string


def subprocess_popen_stream(
    command: str, raise_error: bool = True, log_to_file: Path | None = None
) -> None:
    """Run a shell command. If the command fails, raise an exception"""

    rc = None
    stdout = None
    stderr = None
    try:
        current_app.logger.info(f"Running command: {command}")
        # process = subprocess.Popen(command, stdout=subprocess.PIPE, shell=True)
        # for c in iter(lambda: process.stdout.read(1), b""):
        #     sys.stdout.buffer.write(c)
        #     current_app.logger.info(c)
        process = subprocess.Popen(
            command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True
        )

        # Read stdout line by line
        with open(log_to_file, "w", encoding="utf-8") as file_:
            for line in iter(process.stdout.readline, b""):
                text = line.strip().decode("utf-8")
                current_app.logger.info(text)
                if isinstance(log_to_file, Path):
                    file_.write(f"{text}\n")

        # get the return code, stdout and stderr
        rc = process.poll()
        stdout, stderr = process.communicate()
        current_app.logger.info(f"rc: {rc}")
        current_app.logger.info(f"stdout: {stdout.decode('utf-8') if stdout else None}")
        current_app.logger.info(f"stderr: {stderr.decode('utf-8') if stderr else None}")
    except subprocess.CalledProcessError:
        if raise_error:
            raise

    if rc != 0:
        current_app.logger.info(
            f"Command had a non-zero exit status: {rc}. Command: {command}. Stdout: {stdout}. Stderr: {stderr}"
        )
        if raise_error:
            raise subprocess.CalledProcessError(rc, command)
        # sys.exit(rc)

    return None


def save_html_response_as_file(response, filename: str = "test.html") -> None:
    """
    Save the HTML as a file, so it can be viewed in a browser.
    This is useful for debugging.
    """
    html = bs(response.data, features="lxml").prettify()
    with open(filename, "w", encoding="utf-8") as file:
        file.write(str(html))
        # file.write(str(response.data))
    return None
