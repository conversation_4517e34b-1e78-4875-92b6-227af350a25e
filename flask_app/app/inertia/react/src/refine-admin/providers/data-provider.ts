import { DataProvider, BaseRecord, GetListResponse, GetOneResponse, CreateResponse, UpdateResponse, DeleteOneResponse } from "@refinedev/core";
import { fetchClient } from "@/api/web-api";

export interface FastAPIListResponse<T = BaseRecord> {
  result: {
    data: T[];
    total: number;
    page: number;
    per_page: number;
    pages: number;
  };
}

export interface FastAPISingleResponse<T = BaseRecord> {
  result: T;
}

interface DataProviderOptions {
  apiUrl: string;
}

// Resource metadata extracted from Flask Admin
export interface ResourceFieldMetadata {
  key: string;
  title: string;
  dataIndex: string;
  type: string;
  searchable?: boolean;
  sortable?: boolean;
  required?: boolean;
}

export interface ResourceMetadata {
  name: string;
  label: string;
  category: string;
  listFields?: ResourceFieldMetadata[];
  formFields?: ResourceFieldMetadata[];
  permissions?: {
    create?: boolean;
    edit?: boolean;
    delete?: boolean;
  };
}

// FastAPI admin router creates endpoints directly at /admin/{resource_name}
// No category-based routing needed - keeps it DRY and simple

export const createDataProvider = ({ apiUrl }: DataProviderOptions): DataProvider => {
  // Use the same working API client pattern as service dashboard
  // No axios instance needed - $api handles everything with proper typing

  // Helper function to get resource URL path (matching Flask admin API)
  const getResourcePath = (resourceName: string, meta?: any): string => {
    // Use existing Flask admin API: /api/v1/admin/{resource}
    // This maintains authentication and uses existing proven endpoints
    return `/api/v1/admin/${resourceName}`;
  };

  // Enhanced error handling
  const handleApiError = (error: any, operation: string, resource: string) => {
    console.error(`Data provider ${operation} error for resource ${resource}:`, error);
    
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      throw new Error(`${operation} failed (${status}): ${data?.detail || data?.message || 'Unknown error'}`);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error(`${operation} failed: No response from server`);
    } else {
      // Something else happened
      throw new Error(`${operation} failed: ${error.message}`);
    }
  };

  // Transform data for API submission (handle datetime, select fields, etc.)
  const transformDataForApi = (data: Record<string, any>, meta?: any): Record<string, any> => {
    if (!meta?.formFields && !meta?.listFields) {
      return data; // No field metadata available, return as-is
    }

    const transformed = { ...data };
    const fields = meta?.formFields || meta?.listFields || [];

    fields.forEach((field: ResourceFieldMetadata) => {
      const value = transformed[field.dataIndex] || transformed[field.key];
      
      if (value === undefined || value === null) {
        return; // Skip undefined/null values
      }

      // Handle different field types
      switch (field.type) {
        case 'datetime':
          // Ensure datetime is in ISO format for FastAPI
          if (value instanceof Date) {
            transformed[field.dataIndex] = value.toISOString();
          } else if (typeof value === 'string' && value) {
            // Try to parse as date and convert to ISO
            const parsed = new Date(value);
            if (!isNaN(parsed.getTime())) {
              transformed[field.dataIndex] = parsed.toISOString();
            }
          }
          break;
        
        case 'boolean':
          // Ensure boolean values are properly typed
          if (typeof value === 'string') {
            transformed[field.dataIndex] = value === 'true' || value === '1';
          }
          break;
        
        case 'number':
          // Ensure numeric values are properly typed
          if (typeof value === 'string' && value !== '') {
            const parsed = parseFloat(value);
            if (!isNaN(parsed)) {
              transformed[field.dataIndex] = parsed;
            }
          }
          break;
        
        case 'select':
          // Handle select relationships - may need special handling based on backend
          // For now, pass through as-is but could be enhanced for relationship handling
          break;
        
        default:
          // For text, email, textarea, etc., pass through as string
          if (typeof value !== 'string') {
            transformed[field.dataIndex] = String(value);
          }
      }
    });

    return transformed;
  };

  // Transform Refine filters to FastAPI format
  const transformFilters = (filters?: any[]): Record<string, string> => {
    if (!filters || filters.length === 0) return {};

    const transformed: Record<string, string> = {};
    
    filters.forEach((filter) => {
      const { field, operator, value } = filter;
      
      // Map Refine operators to FastAPI filter format
      switch (operator) {
        case "eq":
          transformed[`filter_${field}`] = value;
          break;
        case "ne":
          transformed[`filter_${field}__ne`] = value;
          break;
        case "lt":
          transformed[`filter_${field}__lt`] = value;
          break;
        case "lte":
          transformed[`filter_${field}__lte`] = value;
          break;
        case "gt":
          transformed[`filter_${field}__gt`] = value;
          break;
        case "gte":
          transformed[`filter_${field}__gte`] = value;
          break;
        case "contains":
          transformed[`filter_${field}__contains`] = value;
          break;
        case "in":
          transformed[`filter_${field}__in`] = value.join(",");
          break;
        case "nin":
          transformed[`filter_${field}__nin`] = value.join(",");
          break;
        case "null":
          transformed[`filter_${field}__null`] = "true";
          break;
        case "nnull":
          transformed[`filter_${field}__null`] = "false";
          break;
        default:
          transformed[`filter_${field}`] = value;
      }
    });

    return transformed;
  };

  // Transform Refine sorters to FastAPI format
  const transformSorters = (sorters?: any[]): { sort?: string; order?: string } => {
    if (!sorters || sorters.length === 0) return {};

    const { field, order } = sorters[0]; // FastAPI backend supports single sort
    return {
      sort: field,
      order: order === "asc" ? "asc" : "desc",
    };
  };

  return {
    getList: async ({ resource, pagination, filters, sorters, meta }) => {
      try {
        const { current = 1, pageSize = 10 } = pagination ?? {};
        
        const params = {
          page: current.toString(),
          per_page: pageSize.toString(),
          ...transformFilters(filters),
          ...transformSorters(sorters),
          ...(meta?.search && { search: meta.search }),
        };

        const resourcePath = getResourcePath(resource, meta);
        
        // Use fetchClient directly for async calls (not hooks)
        const response = await fetchClient.GET(resourcePath as any, { 
          params: { query: params } 
        });
        
        if (response.error) {
          throw new Error(`API Error: ${response.error}`);
        }
        
        const apiData = response.data as any;
        
        return {
          data: apiData.result?.data || [],
          total: apiData.result?.total || 0,
        };
      } catch (error) {
        handleApiError(error, 'getList', resource);
        throw error;
      }
    },

    getOne: async ({ resource, id, meta }) => {
      try {
        const resourcePath = getResourcePath(resource, meta);
        
        const response = await fetchClient.GET(`${resourcePath}/{id}` as any, { 
          params: { path: { id: id.toString() } } 
        });
        
        if (response.error) {
          throw new Error(`API Error: ${response.error}`);
        }
        
        const apiData = response.data as any;
        
        return {
          data: apiData.result,
        };
      } catch (error) {
        handleApiError(error, 'getOne', resource);
        throw error;
      }
    },

    create: async ({ resource, variables, meta }) => {
      try {
        const transformedVariables = transformDataForApi(variables, meta);
        const resourcePath = getResourcePath(resource, meta);
        
        const response = await fetchClient.POST(resourcePath as any, { 
          body: transformedVariables 
        });
        
        if (response.error) {
          throw new Error(`API Error: ${response.error}`);
        }
        
        const apiData = response.data as any;
        
        return {
          data: apiData.result,
        };
      } catch (error) {
        handleApiError(error, 'create', resource);
        throw error;
      }
    },

    update: async ({ resource, id, variables, meta }) => {
      try {
        const transformedVariables = transformDataForApi(variables, meta);
        const resourcePath = getResourcePath(resource, meta);
        
        const response = await fetchClient.PUT(`${resourcePath}/{id}` as any, { 
          params: { path: { id: id.toString() } },
          body: transformedVariables 
        });
        
        if (response.error) {
          throw new Error(`API Error: ${response.error}`);
        }
        
        const apiData = response.data as any;
        
        return {
          data: apiData.result,
        };
      } catch (error) {
        handleApiError(error, 'update', resource);
        throw error;
      }
    },

    deleteOne: async ({ resource, id, meta }) => {
      try {
        const resourcePath = getResourcePath(resource, meta);
        
        const response = await fetchClient.DELETE(`${resourcePath}/{id}` as any, { 
          params: { path: { id: id.toString() } } 
        });
        
        if (response.error) {
          throw new Error(`API Error: ${response.error}`);
        }
        
        return {
          data: { id } as any,
        };
      } catch (error) {
        handleApiError(error, 'deleteOne', resource);
        throw error;
      }
    },

    getMany: async ({ resource, ids, meta }) => {
      try {
        const params = {
          ids: ids.join(","),
        };
        
        const resourcePath = getResourcePath(resource, meta);
        
        const response = await fetchClient.GET(`${resourcePath}/batch` as any, { 
          params: { query: params } 
        });
        
        if (response.error) {
          throw new Error(`API Error: ${response.error}`);
        }
        
        const apiData = response.data as any;
        
        return {
          data: apiData.result?.data || [],
        };
      } catch (error) {
        handleApiError(error, 'getMany', resource);
        throw error;
      }
    },

    createMany: async ({ resource, variables, meta }) => {
      try {
        const resourcePath = getResourcePath(resource, meta);
        const promises = variables.map(async (item) => {
          const transformedItem = transformDataForApi(item, meta);
          const response = await fetchClient.POST(resourcePath as any, { body: transformedItem });
          if (response.error) {
            throw new Error(`API Error: ${response.error}`);
          }
          return response.data as any;
        });
        
        const responses = await Promise.all(promises);
        
        return {
          data: responses.map((response) => response.result),
        };
      } catch (error) {
        handleApiError(error, 'createMany', resource);
        throw error;
      }
    },

    deleteMany: async ({ resource, ids, meta }) => {
      try {
        const resourcePath = getResourcePath(resource, meta);
        const promises = ids.map(async (id) => {
          const response = await fetchClient.DELETE(`${resourcePath}/{id}` as any, { 
            params: { path: { id: id.toString() } } 
          });
          if (response.error) {
            throw new Error(`API Error: ${response.error}`);
          }
          return response;
        });
        
        await Promise.all(promises);
        
        return {
          data: ids.map((id) => ({ id })) as any[],
        };
      } catch (error) {
        handleApiError(error, 'deleteMany', resource);
        throw error;
      }
    },

    updateMany: async ({ resource, ids, variables, meta }) => {
      try {
        const resourcePath = getResourcePath(resource, meta);
        const transformedVariables = transformDataForApi(variables, meta);
        
        const promises = ids.map(async (id) => {
          const response = await fetchClient.PUT(`${resourcePath}/{id}` as any, { 
            params: { path: { id: id.toString() } },
            body: transformedVariables 
          });
          if (response.error) {
            throw new Error(`API Error: ${response.error}`);
          }
          return response.data as any;
        });
        
        const responses = await Promise.all(promises);
        
        return {
          data: responses.map((response) => response.result),
        };
      } catch (error) {
        handleApiError(error, 'updateMany', resource);
        throw error;
      }
    },

    getApiUrl: () => apiUrl,

    custom: async ({ url, method, filters, sorters, payload, query, headers, meta }) => {
      try {
        const params = {
          ...query,
          ...transformFilters(filters),
          ...transformSorters(sorters),
        };

        const transformedPayload = payload ? transformDataForApi(payload, meta) : payload;

        // Use fetchClient for custom requests
        let response;
        switch (method?.toLowerCase()) {
          case 'get':
            response = await fetchClient.GET(url as any, { params: { query: params } });
            break;
          case 'post':
            response = await fetchClient.POST(url as any, { body: transformedPayload, params: { query: params } });
            break;
          case 'put':
            response = await fetchClient.PUT(url as any, { body: transformedPayload, params: { query: params } });
            break;
          case 'delete':
            response = await fetchClient.DELETE(url as any, { params: { query: params } });
            break;
          default:
            throw new Error(`Unsupported method: ${method}`);
        }

        if (response.error) {
          throw new Error(`API Error: ${response.error}`);
        }

        return {
          data: response.data,
        };
      } catch (error) {
        handleApiError(error, 'custom', 'custom-endpoint');
        throw error;
      }
    },
  };
};