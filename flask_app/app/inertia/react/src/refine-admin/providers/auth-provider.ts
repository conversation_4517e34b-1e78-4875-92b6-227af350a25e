import { AuthBindings } from "@refinedev/core";

export interface User {
  id: number;
  name: string;
  email: string;
  roles: number[];
  role_names: string[];
}

interface AuthProviderOptions {
  user: User | null;
  loginUrl?: string;
  logoutUrl?: string;
}

export const createAuthProvider = ({ user, loginUrl = "/login", logoutUrl = "/logout" }: AuthProviderOptions): AuthBindings => {
  return {
    login: async ({ email, password, remember, providerName }) => {
      // Since we're using session-based auth from Flask,
      // redirect to Flask login page
      window.location.href = loginUrl;
      return {
        success: false,
        error: {
          message: "Redirecting to login page",
          name: "<PERSON><PERSON> Required",
        },
      };
    },

    logout: async () => {
      // Redirect to Flask logout endpoint
      window.location.href = logoutUrl;
      return {
        success: true,
        redirectTo: "/",
      };
    },

    check: async () => {
      // Check if user is logged in based on the user prop
      if (user) {
        return {
          authenticated: true,
        };
      }

      return {
        authenticated: false,
        error: {
          message: "Authentication required",
          name: "Unauthorized",
        },
        logout: true,
        redirectTo: loginUrl,
      };
    },

    getPermissions: async () => {
      if (!user) return null;
      
      // Return user roles as permissions
      return user.role_names || [];
    },

    getIdentity: async () => {
      if (!user) return null;

      return {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=C1D72E&color=0c1316`,
      };
    },

    onError: async (error) => {
      // Handle 401 errors by redirecting to login
      if (error?.response?.status === 401) {
        return {
          logout: true,
          redirectTo: loginUrl,
          error,
        };
      }

      return { error };
    },
  };
};