import { AccessControlProvider } from "@refinedev/core";
import type { User } from "./auth-provider";

interface AccessControlOptions {
  user: User | null;
}

export interface ResourceAccess {
  allowedRoles?: number[];
  rejectedRoles?: number[];
}

// This will be populated from the resource registry
export const resourceAccessMap: Record<string, ResourceAccess> = {};

export const createAccessControlProvider = ({ user }: AccessControlOptions): AccessControlProvider => {
  const checkAccess = (resourceAccess?: ResourceAccess): boolean => {
    if (!user || !resourceAccess) return true; // Default allow if no restrictions

    const { allowedRoles, rejectedRoles } = resourceAccess;
    const userRoles = user.roles || [];

    // Check rejected roles first
    if (rejectedRoles && rejectedRoles.length > 0) {
      const isRejected = userRoles.some(role => rejectedRoles.includes(role));
      if (isRejected) return false;
    }

    // Check allowed roles
    if (allowedRoles && allowedRoles.length > 0) {
      const isAllowed = userRoles.some(role => allowedRoles.includes(role));
      return isAllowed;
    }

    // If no specific allowed roles, default to allow
    return true;
  };

  return {
    can: async ({ resource, action, params }) => {
      // Special case for admin users (assuming role 1 is admin)
      if (user?.roles?.includes(1)) {
        return { can: true };
      }

      // Get resource access config
      const resourceAccess = resourceAccessMap[resource];
      
      // Check access based on roles
      const canAccess = checkAccess(resourceAccess);

      // Additional action-based restrictions can be added here
      // For example, some roles might be able to read but not write
      if (!canAccess) {
        return {
          can: false,
          reason: "You don't have permission to access this resource",
        };
      }

      return { can: true };
    },
  };
};