import { ResourceProps } from "@refinedev/core";

export interface FieldConfig {
  key: string;
  label: string;
  type: "text" | "number" | "boolean" | "date" | "datetime" | "email" | "url" | "select" | "reference" | "json" | "image";
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
  required?: boolean;
  options?: { label: string; value: any }[];
  referenceResource?: string;
  referenceLabel?: string;
  defaultValue?: any;
  validation?: any; // Zod schema or validation rules
  hidden?: boolean;
  readOnly?: boolean;
  format?: string; // For date/number formatting
  placeholder?: string;
  helpText?: string;
}

export interface ResourceConfig extends Partial<ResourceProps> {
  name: string;
  label: string;
  icon?: React.ReactNode;
  fields: FieldConfig[];
  searchableFields?: string[];
  filterableFields?: string[];
  defaultSort?: { field: string; order: "asc" | "desc" };
  allowedRoles?: number[];
  rejectedRoles?: number[];
  readOnly?: boolean;
  canCreate?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  canShow?: boolean;
  category?: string;
  relations?: {
    [key: string]: {
      resource: string;
      field: string;
      label: string;
    };
  };
}

export interface ResourceCategory {
  name: string;
  label: string;
  icon?: React.ReactNode;
  order: number;
}

export const resourceCategories: ResourceCategory[] = [
  { name: "customers", label: "Customers", order: 1 },
  { name: "units", label: "Units", order: 2 },
  { name: "bom", label: "BoM", order: 3 },
  { name: "build", label: "Build", order: 4 },
  { name: "service", label: "Service", order: 5 },
  { name: "inventory", label: "Inventory", order: 6 },
  { name: "alerts", label: "Alerts", order: 7 },
  { name: "reports", label: "Reports", order: 8 },
  { name: "machine_learning", label: "Machine Learning", order: 9 },
  { name: "other", label: "Other", order: 10 },
];

// Helper function to create a resource config
export const createResourceConfig = (config: ResourceConfig): ResourceConfig => {
  return {
    ...config,
    canCreate: config.canCreate ?? !config.readOnly,
    canEdit: config.canEdit ?? !config.readOnly,
    canDelete: config.canDelete ?? !config.readOnly,
    canShow: config.canShow ?? true,
  };
};