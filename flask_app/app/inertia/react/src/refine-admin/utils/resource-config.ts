/**
 * Resource configuration utilities for Flask Admin to Refine conversion
 * Converts Flask Admin extracted resources to Refine-compatible configurations
 */

import { ResourceProps } from "@refinedev/core";
import { ResourceFieldMetadata } from "../providers/data-provider";
import { 
  mapResourceFields, 
  getDefaultSortField, 
  getSearchableFields,
  getSortableFields,
  RefineFieldConfig 
} from "./field-mappers";

// Enhanced resource configuration interface
export interface EnhancedResourceConfig {
  name: string;
  label: string;
  category: string;
  
  // URLs for different operations
  list: string;
  create: string;
  edit: string;
  show: string;
  
  // Field configurations
  listFields: RefineFieldConfig[];
  formFields: RefineFieldConfig[];
  searchableFields: string[];
  sortableFields: string[];
  
  // Default behaviors
  defaultSort?: { field: string; order: 'asc' | 'desc' };
  
  // Permissions
  permissions: {
    create: boolean;
    edit: boolean;
    delete: boolean;
    show?: boolean;
  };
  
  // UI configuration
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canShow: boolean;
  
  // Original Flask Admin metadata
  originalMeta?: any;
}

/**
 * Convert Flask Admin resource to enhanced Refine configuration
 */
export const convertFlaskAdminResource = (flaskResource: ResourceProps): EnhancedResourceConfig => {
  const meta = flaskResource.meta || {};
  const listFields = meta.listFields as ResourceFieldMetadata[] || [];
  const formFields = meta.formFields as ResourceFieldMetadata[] || [];
  const permissions = meta.permissions || {};
  
  // Convert field configurations
  const refineListFields = mapResourceFields(listFields, 'list');
  const refineFormFields = mapResourceFields(formFields, 'form');
  
  // Determine default sort
  const defaultSort = getDefaultSortField(listFields);
  
  return {
    name: flaskResource.name,
    label: meta.label || flaskResource.name,
    category: meta.category || 'Other',
    
    // URLs (should match FastAPI structure)
    list: flaskResource.list || `/api/v1/admin/${flaskResource.name}`,
    create: flaskResource.create || `/api/v1/admin/${flaskResource.name}`,
    edit: flaskResource.edit || `/api/v1/admin/${flaskResource.name}`,
    show: flaskResource.show || `/api/v1/admin/${flaskResource.name}`,
    
    // Field configurations
    listFields: refineListFields,
    formFields: refineFormFields,
    searchableFields: getSearchableFields(listFields),
    sortableFields: getSortableFields(listFields),
    
    // Default behaviors
    defaultSort,
    
    // Permissions
    permissions: {
      create: permissions.create !== false,
      edit: permissions.edit !== false,
      delete: permissions.delete !== false,
      show: true, // Always allow show unless explicitly disabled
    },
    
    // UI configuration
    canCreate: permissions.create !== false,
    canEdit: permissions.edit !== false,
    canDelete: permissions.delete !== false,
    canShow: true,
    
    // Original metadata for reference
    originalMeta: meta,
  };
};

/**
 * Convert all Flask Admin resources to enhanced configurations
 */
export const convertAllFlaskAdminResources = (flaskResources: ResourceProps[]): EnhancedResourceConfig[] => {
  return flaskResources.map(convertFlaskAdminResource);
};

/**
 * Get resource configuration by name
 */
export const getResourceConfig = (
  resources: EnhancedResourceConfig[], 
  resourceName: string
): EnhancedResourceConfig | undefined => {
  return resources.find(r => r.name === resourceName);
};

/**
 * Group resources by category
 */
export const groupResourcesByCategory = (resources: EnhancedResourceConfig[]): Record<string, EnhancedResourceConfig[]> => {
  return resources.reduce((groups, resource) => {
    const category = resource.category || 'Other';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(resource);
    return groups;
  }, {} as Record<string, EnhancedResourceConfig[]>);
};

/**
 * Get visible list fields (not hidden)
 */
export const getVisibleListFields = (resource: EnhancedResourceConfig): RefineFieldConfig[] => {
  return resource.listFields.filter(field => !field.hidden);
};

/**
 * Get required form fields
 */
export const getRequiredFormFields = (resource: EnhancedResourceConfig): RefineFieldConfig[] => {
  return resource.formFields.filter(field => field.required);
};

/**
 * Get field by key/dataIndex
 */
export const getFieldConfig = (
  resource: EnhancedResourceConfig, 
  fieldKey: string,
  fieldType: 'list' | 'form' = 'list'
): RefineFieldConfig | undefined => {
  const fields = fieldType === 'list' ? resource.listFields : resource.formFields;
  return fields.find(field => field.key === fieldKey || field.dataIndex === fieldKey);
};

/**
 * Check if resource has specific capability
 */
export const hasCapability = (
  resource: EnhancedResourceConfig, 
  capability: 'create' | 'edit' | 'delete' | 'show'
): boolean => {
  switch (capability) {
    case 'create':
      return resource.canCreate && resource.permissions.create;
    case 'edit':
      return resource.canEdit && resource.permissions.edit;
    case 'delete':
      return resource.canDelete && resource.permissions.delete;
    case 'show':
      return resource.canShow && (resource.permissions.show !== false);
    default:
      return false;
  }
};

/**
 * Get category icon for UI
 */
export const getCategoryIcon = (categoryName: string): string => {
  const iconMap: Record<string, string> = {
    'Alerts': '🚨',
    'BoM': '🔧',
    'Build': '🏗️',
    'Customers': '👥',
    'Inventory': '📦',
    'Machine Learning': '🤖',
    'Other': '⚙️',
    'Reports': '📊',
    'Service': '🛠️',
    'Units': '📟',
  };
  return iconMap[categoryName] || '📁';
};