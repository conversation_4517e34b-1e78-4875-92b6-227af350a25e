/**
 * Field mapping utilities for Flask Admin to Refine conversion
 * Maps extracted Flask Admin field configurations to Refine-compatible field definitions
 */

import { ResourceFieldMetadata } from "../providers/data-provider";

// Unified field configuration for Refine components
export interface RefineFieldConfig {
  key: string;
  label: string;
  dataIndex: string;
  type: string;
  sortable?: boolean;
  searchable?: boolean;
  required?: boolean;
  hidden?: boolean;
  width?: number;
  render?: string; // Custom render function name
  options?: Array<{ label: string; value: any }>;
  format?: string; // For numbers, dates, etc.
  placeholder?: string;
  description?: string;
}

/**
 * Convert Flask Admin field metadata to Refine field configuration
 */
export const mapFlaskAdminFieldToRefine = (field: ResourceFieldMetadata): RefineFieldConfig => {
  const baseConfig: RefineFieldConfig = {
    key: field.key,
    label: field.title,
    dataIndex: field.dataIndex,
    type: mapFieldType(field.type),
    sortable: field.sortable,
    searchable: field.searchable,
    required: field.required,
  };

  // Handle special field types and add type-specific configurations
  switch (field.type) {
    case 'datetime':
      return {
        ...baseConfig,
        type: 'datetime',
        format: 'MMM dd, yyyy HH:mm',
      };

    case 'date':
      return {
        ...baseConfig,
        type: 'date',
        format: 'MMM dd, yyyy',
      };

    case 'email':
      return {
        ...baseConfig,
        type: 'email',
        placeholder: 'Enter email address',
      };

    case 'textarea':
      return {
        ...baseConfig,
        type: 'textarea',
        width: 200, // Make textarea columns wider
      };

    case 'number':
      return {
        ...baseConfig,
        type: 'number',
        format: isIdField(field.key) ? 'id' : 'number',
      };

    case 'boolean':
      return {
        ...baseConfig,
        type: 'boolean',
        width: 80, // Boolean columns can be narrower
      };

    case 'select':
      return {
        ...baseConfig,
        type: 'select',
        options: [], // Will be populated by relationship data
      };

    default:
      return baseConfig;
  }
};

/**
 * Map Flask Admin field types to standardized Refine types
 */
export const mapFieldType = (flaskType: string): string => {
  const typeMapping: Record<string, string> = {
    'text': 'text',
    'textarea': 'textarea',
    'email': 'email',
    'number': 'number',
    'boolean': 'boolean',
    'datetime': 'datetime',
    'date': 'date',
    'select': 'select',
    'reference': 'reference',
    'json': 'json',
    'url': 'url',
  };

  return typeMapping[flaskType] || 'text';
};

/**
 * Check if a field is an ID field
 */
export const isIdField = (fieldKey: string): boolean => {
  return fieldKey === 'id' || fieldKey.endsWith('_id') || fieldKey.endsWith('Id');
};

/**
 * Check if a field is a relationship field
 */
export const isRelationshipField = (field: ResourceFieldMetadata): boolean => {
  return field.type === 'select' || 
         field.key.endsWith('_rel') || 
         field.key.endsWith('Rel') ||
         field.dataIndex.includes('.');
};

/**
 * Extract relationship information from field
 */
export const extractRelationshipInfo = (field: ResourceFieldMetadata): { 
  resourceName: string; 
  displayField: string;
} | null => {
  // Handle nested field access like "customer_rel.customer"
  if (field.dataIndex.includes('.')) {
    const [relationField, displayField] = field.dataIndex.split('.');
    const resourceName = relationField.replace(/_rel$/, '').replace(/Rel$/, '');
    return { resourceName, displayField };
  }

  // Handle direct relationship fields
  if (field.key.endsWith('_rel') || field.key.endsWith('Rel')) {
    const resourceName = field.key.replace(/_rel$/, '').replace(/Rel$/, '');
    return { resourceName, displayField: 'name' }; // Default display field
  }

  return null;
};

/**
 * Convert list of Flask Admin fields to Refine field configurations
 */
export const mapResourceFields = (
  fields: ResourceFieldMetadata[], 
  fieldType: 'list' | 'form' = 'list'
): RefineFieldConfig[] => {
  return fields
    .map(mapFlaskAdminFieldToRefine)
    .map(field => {
      // Apply field type-specific modifications
      if (fieldType === 'form') {
        // Form-specific modifications
        if (field.type === 'textarea') {
          field.placeholder = `Enter ${field.label.toLowerCase()}`;
        }
        if (field.required) {
          field.label = `${field.label} *`;
        }
      } else {
        // List-specific modifications
        if (field.type === 'textarea') {
          field.width = 150; // Limit textarea width in lists
        }
        if (isIdField(field.key)) {
          field.width = 80; // ID columns can be narrow
        }
      }
      
      return field;
    })
    .filter(field => {
      // Hide certain fields in lists by default
      if (fieldType === 'list') {
        // Hide very long text fields in list view
        if (field.type === 'textarea' && !field.searchable) {
          field.hidden = true;
        }
        // Hide JSON fields in list view
        if (field.type === 'json') {
          field.hidden = true;
        }
      }
      
      return true; // Keep all fields, just mark some as hidden
    });
};

/**
 * Get searchable fields for a resource
 */
export const getSearchableFields = (fields: ResourceFieldMetadata[]): string[] => {
  return fields
    .filter(field => field.searchable === true)
    .map(field => field.dataIndex);
};

/**
 * Get sortable fields for a resource  
 */
export const getSortableFields = (fields: ResourceFieldMetadata[]): string[] => {
  return fields
    .filter(field => field.sortable === true)
    .map(field => field.dataIndex);
};

/**
 * Get default sort field for a resource
 */
export const getDefaultSortField = (fields: ResourceFieldMetadata[]): { field: string; order: 'asc' | 'desc' } | null => {
  // Try to find a commonly used sort field
  const sortPreference = ['timestamp_utc_inserted', 'date_created', 'created_at', 'id'];
  
  for (const preferredField of sortPreference) {
    const field = fields.find(f => 
      f.dataIndex === preferredField && f.sortable === true
    );
    if (field) {
      return { 
        field: field.dataIndex, 
        order: preferredField === 'id' ? 'asc' : 'desc' 
      };
    }
  }
  
  // Fallback to first sortable field
  const firstSortable = fields.find(f => f.sortable === true);
  if (firstSortable) {
    return { field: firstSortable.dataIndex, order: 'asc' };
  }
  
  return null;
};