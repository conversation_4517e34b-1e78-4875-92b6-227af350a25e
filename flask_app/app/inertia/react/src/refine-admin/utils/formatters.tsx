import React from "react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle } from "lucide-react";
import { RefineFieldConfig } from "./field-mappers";

export const formatCellValue = (value: any, field: RefineFieldConfig): React.ReactNode => {
  if (value === null || value === undefined) {
    return <span className="text-muted-foreground">-</span>;
  }

  switch (field.type) {
    case "boolean":
      return value ? (
        <CheckCircle className="h-4 w-4 text-green-500" />
      ) : (
        <XCircle className="h-4 w-4 text-gray-400" />
      );

    case "date":
      try {
        return format(new Date(value), "MMM dd, yyyy");
      } catch {
        return value;
      }

    case "datetime":
      try {
        return format(new Date(value), "MMM dd, yyyy HH:mm");
      } catch {
        return value;
      }

    case "number":
      if (field.format === "currency") {
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(value);
      }
      return value.toLocaleString();

    case "email":
      return (
        <a href={`mailto:${value}`} className="text-blue-600 hover:underline">
          {value}
        </a>
      );

    case "url":
      return (
        <a
          href={value}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:underline"
        >
          {value}
        </a>
      );

    case "select":
      const option = field.options?.find((opt) => opt.value === value);
      return option ? (
        <Badge variant="outline">{option.label}</Badge>
      ) : (
        <Badge variant="secondary">{String(value)}</Badge>
      );

    case "reference":
      return <span className="font-medium">#{value}</span>;

    case "textarea":
      // Truncate long text in list views
      const text = String(value);
      if (text.length > 100) {
        return (
          <span title={text}>
            {text.substring(0, 100)}...
          </span>
        );
      }
      return text;

    case "json":
      return (
        <code className="text-xs bg-gray-100 px-1 py-0.5 rounded max-w-xs overflow-hidden">
          {JSON.stringify(value, null, 2).substring(0, 50)}...
        </code>
      );

    default:
      // Handle long text values
      const stringValue = String(value);
      if (stringValue.length > 50) {
        return (
          <span title={stringValue}>
            {stringValue.substring(0, 50)}...
          </span>
        );
      }
      return stringValue;
  }
};