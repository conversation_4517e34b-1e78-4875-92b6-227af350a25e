import React from "react";
import { useTable, useResource, CanAccess } from "@refinedev/core";
import { usePara<PERSON>, Link } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Plus,
  Search,
  MoreHorizontal,
  Eye,
  Edit,
  Trash,
  ArrowUpDown,
  Filter,
} from "lucide-react";
import { allResources } from "../resources";
import { formatCellValue } from "../utils/formatters";
import { 
  convertFlaskAdminResource, 
  getVisibleListFields, 
  hasCapability 
} from "../utils/resource-config";
import { cn } from "@/lib/utils";

export const ResourceList: React.FC = () => {
  const params = useParams<{ resource: string }>();
  const resourceName = params.resource!;
  const { resource } = useResource(resourceName);
  
  // Find and convert resource config
  const rawResourceConfig = allResources.find((r) => r.name === resourceName);
  
  if (!rawResourceConfig) {
    return <div>Resource not found</div>;
  }

  // Convert Flask Admin resource to enhanced config
  const resourceConfig = convertFlaskAdminResource(rawResourceConfig);

  const {
    tableQuery,
    current,
    setCurrent,
    pageSize,
    setPageSize,
    pageCount,
    sorters,
    setSorters,
    filters,
    setFilters,
  } = useTable({
    resource: resourceName,
    sorters: {
      initial: resourceConfig.defaultSort ? [resourceConfig.defaultSort] : [],
    },
    meta: {
      // Pass resource metadata to data provider
      category: resourceConfig.category,
      listFields: resourceConfig.originalMeta?.listFields,
      formFields: resourceConfig.originalMeta?.formFields,
    },
  });

  const { data, isLoading, isError } = tableQuery;
  const records = data?.data || [];

  // Get visible fields (not hidden)
  const visibleFields = getVisibleListFields(resourceConfig);

  const handleSort = (fieldKey: string) => {
    const field = visibleFields.find(f => f.key === fieldKey || f.dataIndex === fieldKey);
    if (!field?.sortable) return; // Don't sort if field is not sortable
    
    const sortField = field.dataIndex; // Use dataIndex for sorting
    const currentSort = sorters?.find((s) => s.field === sortField);
    const newOrder = !currentSort ? "asc" : currentSort.order === "asc" ? "desc" : null;

    if (newOrder) {
      setSorters([{ field: sortField, order: newOrder }]);
    } else {
      setSorters([]);
    }
  };

  const getSortIcon = (fieldKey: string) => {
    const field = visibleFields.find(f => f.key === fieldKey || f.dataIndex === fieldKey);
    if (!field?.sortable) return null;
    
    const sortField = field.dataIndex;
    const currentSort = sorters?.find((s) => s.field === sortField);
    if (!currentSort) return <ArrowUpDown className="ml-2 h-4 w-4" />;
    return (
      <ArrowUpDown
        className={cn(
          "ml-2 h-4 w-4",
          currentSort.order === "desc" && "rotate-180"
        )}
      />
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {resourceConfig.label}
          </h1>
          <p className="text-muted-foreground">
            Manage {resourceConfig.label.toLowerCase()} records
          </p>
        </div>
        
        <CanAccess resource={resourceName} action="create">
          {hasCapability(resourceConfig, 'create') && (
            <Button asChild>
              <Link to={`/admin3/resources/${resourceName}/create`}>
                <Plus className="mr-2 h-4 w-4" />
                Create New
              </Link>
            </Button>
          )}
        </CanAccess>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <Input
                placeholder={`Search ${resourceConfig.label.toLowerCase()}...`}
                className="pl-10"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox />
                  </TableHead>
                  {visibleFields.map((field) => (
                    <TableHead
                      key={field.key}
                      className={field.sortable ? "cursor-pointer" : ""}
                      onClick={() => field.sortable && handleSort(field.key)}
                      style={{ width: field.width }}
                    >
                      <div className="flex items-center">
                        {field.label}
                        {field.sortable && getSortIcon(field.key)}
                      </div>
                    </TableHead>
                  ))}
                  <TableHead className="w-20">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell
                      colSpan={visibleFields.length + 2}
                      className="text-center"
                    >
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : isError ? (
                  <TableRow>
                    <TableCell
                      colSpan={visibleFields.length + 2}
                      className="text-center text-red-500"
                    >
                      Error loading data
                    </TableCell>
                  </TableRow>
                ) : records.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={visibleFields.length + 2}
                      className="text-center"
                    >
                      No records found
                    </TableCell>
                  </TableRow>
                ) : (
                  records.map((record: any) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <Checkbox />
                      </TableCell>
                      {visibleFields.map((field) => (
                        <TableCell key={field.key}>
                          {formatCellValue(
                            // Support nested field access like "customer_rel.customer"
                            field.dataIndex.includes('.') 
                              ? field.dataIndex.split('.').reduce((obj, key) => obj?.[key], record)
                              : record[field.dataIndex],
                            field
                          )}
                        </TableCell>
                      ))}
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <CanAccess resource={resourceName} action="show">
                              {hasCapability(resourceConfig, 'show') && (
                                <DropdownMenuItem asChild>
                                  <Link to={`/admin3/resources/${resourceName}/show/${record.id}`}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </Link>
                                </DropdownMenuItem>
                              )}
                            </CanAccess>
                            <CanAccess resource={resourceName} action="edit">
                              {hasCapability(resourceConfig, 'edit') && (
                                <DropdownMenuItem asChild>
                                  <Link to={`/admin3/resources/${resourceName}/edit/${record.id}`}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </Link>
                                </DropdownMenuItem>
                              )}
                            </CanAccess>
                            <CanAccess resource={resourceName} action="delete">
                              {hasCapability(resourceConfig, 'delete') && (
                                <DropdownMenuItem className="text-red-600">
                                  <Trash className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              )}
                            </CanAccess>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pageCount > 1 && (
            <div className="flex items-center justify-between px-6 py-4 border-t">
              <div className="text-sm text-muted-foreground">
                Showing {(current - 1) * pageSize + 1} to{" "}
                {Math.min(current * pageSize, data?.total || 0)} of{" "}
                {data?.total || 0} results
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrent(current - 1)}
                  disabled={current === 1}
                >
                  Previous
                </Button>
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, pageCount) }, (_, i) => {
                    const page = i + 1;
                    return (
                      <Button
                        key={page}
                        variant={current === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrent(page)}
                      >
                        {page}
                      </Button>
                    );
                  })}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrent(current + 1)}
                  disabled={current === pageCount}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};