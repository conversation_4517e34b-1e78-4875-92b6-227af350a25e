import React from "react";
import { useList } from "@refinedev/core";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Users, Package, AlertCircle, TrendingUp, DollarSign, Activity } from "lucide-react";
import { cn } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  description,
  icon,
  trend,
  className,
}) => (
  <Card className={cn("", className)}>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      {trend && (
        <div className="flex items-center pt-1">
          <TrendingUp
            className={cn(
              "mr-1 h-4 w-4",
              trend.isPositive ? "text-green-500" : "rotate-180 text-red-500"
            )}
          />
          <span
            className={cn(
              "text-xs font-medium",
              trend.isPositive ? "text-green-500" : "text-red-500"
            )}
          >
            {trend.value}%
          </span>
          <span className="text-xs text-muted-foreground ml-1">from last month</span>
        </div>
      )}
    </CardContent>
  </Card>
);

export const DashboardPage: React.FC = () => {
  // Fetch data for statistics
  const { data: usersData } = useList({
    resource: "users",
    pagination: { current: 1, pageSize: 1 },
  });

  const { data: structuresData } = useList({
    resource: "structures",
    pagination: { current: 1, pageSize: 1 },
  });

  const { data: alertsData } = useList({
    resource: "alerts",
    pagination: { current: 1, pageSize: 1 },
    filters: [{ field: "is_active", operator: "eq", value: true }],
  });

  const { data: workOrdersData } = useList({
    resource: "work_orders_inc",
    pagination: { current: 1, pageSize: 1 },
  });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to IJACK Admin Panel
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Users"
          value={usersData?.total || 0}
          description="Active platform users"
          icon={<Users className="h-4 w-4 text-muted-foreground" />}
          trend={{ value: 12.5, isPositive: true }}
        />
        <StatCard
          title="Active Units"
          value={structuresData?.total || 0}
          description="Deployed structures"
          icon={<Package className="h-4 w-4 text-muted-foreground" />}
          trend={{ value: 8.2, isPositive: true }}
        />
        <StatCard
          title="Active Alerts"
          value={alertsData?.total || 0}
          description="Requiring attention"
          icon={<AlertCircle className="h-4 w-4 text-muted-foreground" />}
          trend={{ value: 3.1, isPositive: false }}
        />
        <StatCard
          title="Work Orders"
          value={workOrdersData?.total || 0}
          description="In progress"
          icon={<Activity className="h-4 w-4 text-muted-foreground" />}
          trend={{ value: 5.7, isPositive: true }}
        />
      </div>

      {/* Tabs Section */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest system events and user actions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Activity feed would go here */}
                  <p className="text-sm text-muted-foreground">
                    Activity feed coming soon...
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common administrative tasks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {/* Quick action buttons would go here */}
                  <p className="text-sm text-muted-foreground">
                    Quick actions coming soon...
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>
                Performance metrics and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Analytics dashboard coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle>Reports</CardTitle>
              <CardDescription>
                Generate and view system reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Reports section coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};