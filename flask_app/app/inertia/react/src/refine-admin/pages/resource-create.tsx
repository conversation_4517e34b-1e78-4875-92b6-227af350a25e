import React from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useCreate } from "@refinedev/core";
import { useForm } from "@refinedev/react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { allResources } from "../resources";

export const ResourceCreate: React.FC = () => {
  const params = useParams<{ resource: string }>();
  const navigate = useNavigate();
  const resourceName = params.resource!;
  
  const resourceConfig = allResources.find((r) => r.name === resourceName);
  
  if (!resourceConfig) {
    return <div>Resource not found</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Create {resourceConfig.label}
          </h1>
          <p className="text-muted-foreground">
            Add a new {resourceConfig.label.toLowerCase()} record
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>New {resourceConfig.label}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Form fields will be dynamically generated based on resource configuration.
          </p>
          <div className="mt-6 flex gap-2">
            <Button onClick={() => navigate(-1)} variant="outline">
              Cancel
            </Button>
            <Button>Save</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};