import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useOne, useUpdate } from "@refinedev/core";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { allResources } from "../resources";

export const ResourceEdit: React.FC = () => {
  const params = useParams<{ resource: string; id: string }>();
  const navigate = useNavigate();
  const resourceName = params.resource!;
  const recordId = params.id!;
  
  const resourceConfig = allResources.find((r) => r.name === resourceName);
  
  if (!resourceConfig) {
    return <div>Resource not found</div>;
  }

  const { data, isLoading } = useOne({
    resource: resourceName,
    id: recordId,
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Edit {resourceConfig.label}
          </h1>
          <p className="text-muted-foreground">
            Update {resourceConfig.label.toLowerCase()} #{recordId}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Edit {resourceConfig.label}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Form fields will be dynamically generated based on resource configuration.
          </p>
          <div className="mt-6 flex gap-2">
            <Button onClick={() => navigate(-1)} variant="outline">
              Cancel
            </Button>
            <Button>Save Changes</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};