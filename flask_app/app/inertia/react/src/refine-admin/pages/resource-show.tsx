import React from "react";
import { use<PERSON>ara<PERSON>, useNavigate, <PERSON> } from "react-router-dom";
import { useOne, useResource, CanAccess } from "@refinedev/core";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, ArrowLeft } from "lucide-react";
import { allResources } from "../resources";
import { formatCellValue } from "../utils/formatters";

export const ResourceShow: React.FC = () => {
  const params = useParams<{ resource: string; id: string }>();
  const navigate = useNavigate();
  const resourceName = params.resource!;
  const recordId = params.id!;
  
  const resourceConfig = allResources.find((r) => r.name === resourceName);
  
  if (!resourceConfig) {
    return <div>Resource not found</div>;
  }

  const { data, isLoading } = useOne({
    resource: resourceName,
    id: recordId,
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const record = data?.data;
  const visibleFields = resourceConfig.fields.filter((field) => !field.hidden);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => navigate(-1)}
            variant="ghost"
            size="sm"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {resourceConfig.label} Details
            </h1>
            <p className="text-muted-foreground">
              Viewing {resourceConfig.label.toLowerCase()} #{recordId}
            </p>
          </div>
        </div>
        
        <CanAccess resource={resourceName} action="edit">
          {resourceConfig.canEdit !== false && (
            <Button asChild>
              <Link to={`/admin3/resources/${resourceName}/edit/${recordId}`}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </Button>
          )}
        </CanAccess>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Record Information</CardTitle>
        </CardHeader>
        <CardContent>
          <dl className="divide-y divide-gray-100">
            {visibleFields.map((field) => (
              <div key={field.key} className="py-4 sm:grid sm:grid-cols-3 sm:gap-4">
                <dt className="text-sm font-medium text-gray-900">
                  {field.label}
                </dt>
                <dd className="mt-1 text-sm text-gray-700 sm:col-span-2 sm:mt-0">
                  {formatCellValue(record?.[field.key], field)}
                </dd>
              </div>
            ))}
          </dl>
        </CardContent>
      </Card>
    </div>
  );
};