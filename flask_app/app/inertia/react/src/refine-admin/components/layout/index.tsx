import React from "react";
import { LayoutWrapper } from "@refinedev/core";
import { Sidebar } from "./sidebar";
import { Header } from "./header";

export const Layout: React.FC<React.PropsWithChildren> = ({ children }) => {
  return (
    <LayoutWrapper>
      <div className="flex h-screen overflow-hidden bg-gray-50">
        {/* Sidebar - Hidden on mobile */}
        <div className="hidden lg:block">
          <Sidebar />
        </div>

        {/* Main Content */}
        <div className="flex flex-1 flex-col overflow-hidden">
          <Header />
          
          {/* Page Content */}
          <main className="flex-1 overflow-auto p-6">
            <div className="mx-auto max-w-7xl">
              {children}
            </div>
          </main>
        </div>
      </div>
    </LayoutWrapper>
  );
};