import React from "react";
import { useMenu, useLogout, useGetIdentity, CanAccess } from "@refinedev/core";
import { NavLink } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Users,
  Cpu,
  Package,
  Wrench,
  Bell,
  FileText,
  Settings2,
  Warehouse,
  Brain,
  MoreHorizontal,
  ChevronDown,
  LogOut,
  LayoutDashboard,
} from "lucide-react";
import { resourcesByCategory } from "../../resources";

const categoryIcons: Record<string, React.ReactNode> = {
  customers: <Users className="h-4 w-4" />,
  units: <Cpu className="h-4 w-4" />,
  bom: <Package className="h-4 w-4" />,
  build: <Wrench className="h-4 w-4" />,
  alerts: <Bell className="h-4 w-4" />,
  reports: <FileText className="h-4 w-4" />,
  service: <Settings2 className="h-4 w-4" />,
  inventory: <Warehouse className="h-4 w-4" />,
  machine_learning: <Brain className="h-4 w-4" />,
  other: <MoreHorizontal className="h-4 w-4" />,
};

interface SidebarProps {
  className?: string;
}

export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const { mutate: logout } = useLogout();
  const { data: identity } = useGetIdentity<{
    id: number;
    name: string;
    email: string;
    avatar: string;
  }>();
  const [openCategories, setOpenCategories] = React.useState<Record<string, boolean>>({});

  const toggleCategory = (categoryName: string) => {
    setOpenCategories((prev) => ({
      ...prev,
      [categoryName]: !prev[categoryName],
    }));
  };

  return (
    <div className={cn("flex h-full w-64 flex-col border-r bg-white", className)}>
      {/* Logo/Brand */}
      <div className="flex h-16 items-center border-b px-6">
        <img
          src="/static/img/icons/ijack-logo-189x86.png"
          alt="IJACK Technologies"
          className="h-8 w-auto"
        />
      </div>

      {/* User Info */}
      {identity && (
        <div className="border-b p-4">
          <div className="flex items-center space-x-3">
            <img
              src={identity.avatar}
              alt={identity.name}
              className="h-10 w-10 rounded-full"
            />
            <div className="flex-1 overflow-hidden">
              <p className="truncate text-sm font-medium">{identity.name}</p>
              <p className="truncate text-xs text-gray-500">{identity.email}</p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <div className="space-y-1">
          {/* Dashboard Link */}
          <NavLink
            to="/admin3"
            end
            className={({ isActive }) =>
              cn(
                "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                isActive
                  ? "bg-ijack-green text-white"
                  : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
              )
            }
          >
            <LayoutDashboard className="h-4 w-4" />
            Dashboard
          </NavLink>

          {/* Resource Categories */}
          {resourcesByCategory.map((category) => {
            if (category.resources.length === 0) return null;

            const isOpen = openCategories[category.name] ?? false;

            return (
              <Collapsible
                key={category.name}
                open={isOpen}
                onOpenChange={() => toggleCategory(category.name)}
              >
                <CollapsibleTrigger className="flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                  <div className="flex items-center gap-3">
                    {categoryIcons[category.name]}
                    <span>{category.label}</span>
                  </div>
                  <ChevronDown
                    className={cn(
                      "h-4 w-4 transition-transform",
                      isOpen && "rotate-180"
                    )}
                  />
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <div className="ml-7 space-y-1 py-1">
                    {category.resources.map((resource) => (
                      <CanAccess
                        key={resource.name}
                        resource={resource.name}
                        action="list"
                      >
                        <NavLink
                          to={`/resources/${resource.name}`}
                          className={({ isActive }) =>
                            cn(
                              "block rounded-md px-3 py-1.5 text-sm transition-colors",
                              isActive
                                ? "bg-ijack-green/10 text-ijack-green font-medium"
                                : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                            )
                          }
                        >
                          {resource.label}
                        </NavLink>
                      </CanAccess>
                    ))}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            );
          })}
        </div>
      </ScrollArea>

      {/* Logout Button */}
      <div className="border-t p-4">
        <Button
          variant="outline"
          className="w-full justify-start"
          onClick={() => logout()}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </Button>
      </div>
    </div>
  );
};