import React from "react";
import { Refine, Authenticated } from "@refinedev/core";
import routerB<PERSON><PERSON>, {
  CatchAllNavigate,
  DocumentTitleHandler,
  NavigateToResource,
  UnsavedChangesNotifier,
} from "@refinedev/react-router-v6";
import { <PERSON>rowserRouter, Outlet, Route, Routes } from "react-router-dom";
import { Toaster } from "@/components/ui/sonner";

import { createDataProvider } from "./providers/data-provider";
import { createAuthProvider, User } from "./providers/auth-provider";
import { createAccessControlProvider } from "./providers/access-control";
import { allResources } from "./resources";
import { Layout } from "./components/layout";
import { DashboardPage } from "./pages/dashboard";
import { ResourceList } from "./pages/resource-list";
import { ResourceCreate } from "./pages/resource-create";
import { ResourceEdit } from "./pages/resource-edit";
import { ResourceShow } from "./pages/resource-show";

interface RefineAdminAppProps {
  user: User | null;
  apiBaseUrl: string;
}

// Custom notification provider using sonner
const notificationProvider = {
  open: ({ type, message, description }: any) => {
    if (type === "success") {
      // @ts-ignore - sonner toast is available globally
      window.toast?.success(message, { description });
    } else if (type === "error") {
      // @ts-ignore
      window.toast?.error(message, { description });
    } else {
      // @ts-ignore
      window.toast?.(message, { description });
    }
  },
  close: () => {},
};

export const RefineAdminApp: React.FC<RefineAdminAppProps> = ({ user, apiBaseUrl }) => {
  const dataProvider = React.useMemo(
    () => createDataProvider({ apiUrl: apiBaseUrl }),
    [apiBaseUrl]
  );

  const authProvider = React.useMemo(
    () => createAuthProvider({ user }),
    [user]
  );

  const accessControlProvider = React.useMemo(
    () => createAccessControlProvider({ user }),
    [user]
  );

  return (
    <BrowserRouter basename="/admin3">
      <Refine
          dataProvider={dataProvider}
          authProvider={authProvider}
          accessControlProvider={accessControlProvider}
          routerProvider={routerBindings}
          notificationProvider={notificationProvider}
          resources={allResources.map((resource) => ({
            name: resource.name,
            list: "/resources/:resource",
            create: "/resources/:resource/create",
            edit: "/resources/:resource/edit/:id",
            show: "/resources/:resource/show/:id",
            meta: {
              label: resource.meta?.label || resource.name,
              canDelete: resource.meta?.canDelete !== false,
              ...resource.meta,
            },
          }))}
          options={{
            syncWithLocation: true,
            warnWhenUnsavedChanges: true,
            useNewQueryKeys: true,
            projectId: "ijack-admin",
          }}
        >
          <Routes>
            <Route
              element={
                <Authenticated
                  key="authenticated-routes"
                  fallback={<CatchAllNavigate to="/login" />}
                >
                  <Layout>
                    <Outlet />
                  </Layout>
                </Authenticated>
              }
            >
              <Route index element={<DashboardPage />} />
              
              {/* Dynamic resource routes */}
              <Route path="/resources/:resource">
                <Route index element={<ResourceList />} />
                <Route path="create" element={<ResourceCreate />} />
                <Route path="edit/:id" element={<ResourceEdit />} />
                <Route path="show/:id" element={<ResourceShow />} />
              </Route>

              <Route path="*" element={<ErrorComponent />} />
            </Route>
          </Routes>

          <UnsavedChangesNotifier />
          <DocumentTitleHandler />
        </Refine>
        <Toaster richColors position="top-right" />
    </BrowserRouter>
  );
};

// Simple error component
const ErrorComponent: React.FC = () => (
  <div className="flex h-full items-center justify-center">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900">404</h1>
      <p className="mt-2 text-gray-600">Page not found</p>
    </div>
  </div>
);