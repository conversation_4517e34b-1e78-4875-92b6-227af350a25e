/**
 * Complete Flask Admin to Refine Admin Resource Mapping
 * Auto-generated comprehensive field configurations
 */

import {
  ResourceConfig,
  createResourceConfig,
} from "../types/resources";
import { resourceAccessMap } from "../providers/access-control";

// Helper to register resource with access control
const registerResource = (config: ResourceConfig): ResourceConfig => {
  const resource = createResourceConfig(config);
  resourceAccessMap[resource.name] = {
    allowedRoles: resource.allowedRoles || [1], // Default to admin only
    rejectedRoles: resource.rejectedRoles,
  };
  return resource;
};


export const usersResource = registerResource({
  name: "users",
  label: "Users",
  category: "customers",
  endpoint: "users",
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "email",
        "label": "Email",
        "type": "email",
        "required": true,
        "searchable": true,
        "unique": true
    },
    {
        "key": "first_name",
        "label": "First Name",
        "type": "text",
        "searchable": true
    },
    {
        "key": "last_name",
        "label": "Last Name",
        "type": "text",
        "searchable": true
    },
    {
        "key": "phone",
        "label": "Phone",
        "type": "text",
        "searchable": true,
        "validators": [
            "phone"
        ]
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "editable": true,
        "default": true
    },
    {
        "key": "customer_id",
        "label": "Customer",
        "type": "reference",
        "filterable": true,
        "referenceResource": "customers"
    },
    {
        "key": "sms_stop_all",
        "label": "SMS Stop All",
        "type": "boolean",
        "filterable": true,
        "editable": true
    },
    {
        "key": "wants_sms",
        "label": "Wants SMS",
        "type": "boolean",
        "filterable": true,
        "editable": true
    },
    {
        "key": "wants_email",
        "label": "Wants Email",
        "type": "boolean",
        "filterable": true,
        "editable": true
    },
    {
        "key": "created_at",
        "label": "Created At",
        "type": "datetime",
        "readOnly": true,
        "filterable": true
    },
    {
        "key": "modified_at",
        "label": "Modified At",
        "type": "datetime",
        "readOnly": true
    },
    {
        "key": "last_seen",
        "label": "Last Seen",
        "type": "datetime",
        "readOnly": true
    }
],
  searchableFields: ["first_name", "last_name", "email", "phone"],
  filterableFields: ["is_active", "sms_stop_all", "wants_sms", "wants_email", "customer_id", "created_at"],
  editableFields: ["is_active", "sms_stop_all", "wants_sms", "wants_email"],
  defaultSort: {"field": "created_at", "order": "desc"},
});

export const customersResource = registerResource({
  name: "customers",
  label: "Customers",
  category: "customers",
  endpoint: "customers",
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "customer",
        "label": "Customer Name",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "email",
        "label": "Email",
        "type": "email",
        "searchable": true
    },
    {
        "key": "phone",
        "label": "Phone",
        "type": "text",
        "searchable": true,
        "validators": [
            "phone"
        ]
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    },
    {
        "key": "is_tax_exempt",
        "label": "Tax Exempt",
        "type": "boolean",
        "filterable": true
    },
    {
        "key": "color",
        "label": "Color",
        "type": "text",
        "widget": "color"
    },
    {
        "key": "created_at",
        "label": "Created At",
        "type": "datetime",
        "readOnly": true,
        "filterable": true
    }
],
  searchableFields: ["customer", "email", "phone"],
  filterableFields: ["is_active", "is_tax_exempt", "created_at"],
});

export const applicationsResource = registerResource({
  name: "applications",
  label: "Applications - All Combined",
  category: "customers",
  endpoint: "applications",
  allowedRoles: ["sales"],
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "name",
        "label": "Name",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "email",
        "label": "Email",
        "type": "email",
        "required": true,
        "searchable": true
    },
    {
        "key": "phone",
        "label": "Phone",
        "type": "text",
        "searchable": true,
        "validators": [
            "phone"
        ]
    },
    {
        "key": "company",
        "label": "Company",
        "type": "text",
        "searchable": true
    },
    {
        "key": "product_interest",
        "label": "Product Interest",
        "type": "select",
        "filterable": true,
        "options": [
            {
                "label": "XFER - Multiphase Transfer Pump",
                "value": "XFER"
            },
            {
                "label": "VRU - Vapor Recovery Unit",
                "value": "VRU"
            },
            {
                "label": "EGAS - Electric Gas Compressor",
                "value": "EGAS"
            },
            {
                "label": "DGAS - Direct Gas Compressor",
                "value": "DGAS"
            },
            {
                "label": "UNO - Hydraulic Pump Jack",
                "value": "UNO"
            }
        ]
    },
    {
        "key": "is_contacted",
        "label": "Contacted",
        "type": "boolean",
        "filterable": true
    },
    {
        "key": "created_at",
        "label": "Created At",
        "type": "datetime",
        "readOnly": true,
        "filterable": true
    }
],
  searchableFields: ["name", "email", "phone", "company"],
  filterableFields: ["product_interest", "is_contacted", "created_at"],
});

export const countriesResource = registerResource({
  name: "countries",
  label: "Countries",
  category: "customers",
  endpoint: "countries",
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "country",
        "label": "Country Name",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "country_short",
        "label": "Country Short",
        "type": "text",
        "searchable": true
    },
    {
        "key": "country_code",
        "label": "Country Code",
        "type": "text",
        "searchable": true
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    }
],
  searchableFields: ["country", "country_short", "country_code"],
  filterableFields: ["is_active"],
});

export const provinces_statesResource = registerResource({
  name: "provinces_states",
  label: "Provinces/States",
  category: "customers",
  endpoint: "provinces_states",
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "province",
        "label": "Province Name",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "province_short",
        "label": "Province Short",
        "type": "text",
        "searchable": true
    },
    {
        "key": "province_code",
        "label": "Province Code",
        "type": "text",
        "searchable": true
    },
    {
        "key": "country_id",
        "label": "Country",
        "type": "reference",
        "filterable": true,
        "referenceResource": "countries"
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    }
],
  searchableFields: ["province", "province_short", "province_code"],
  filterableFields: ["country_id", "is_active"],
});

export const structuresResource = registerResource({
  name: "structures",
  label: "Structures",
  category: "units",
  endpoint: "structures",
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "structure",
        "label": "Structure Name",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "structure_short",
        "label": "Structure Short",
        "type": "text",
        "searchable": true
    },
    {
        "key": "model_type_id",
        "label": "Model Type",
        "type": "reference",
        "filterable": true,
        "referenceResource": "structure_types"
    },
    {
        "key": "customer_id",
        "label": "Customer",
        "type": "reference",
        "filterable": true,
        "referenceResource": "customers"
    },
    {
        "key": "surface",
        "label": "Surface",
        "type": "text",
        "filterable": true
    },
    {
        "key": "date_built",
        "label": "Date Built",
        "type": "date",
        "filterable": true
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    }
],
  searchableFields: ["structure", "structure_short"],
  filterableFields: ["model_type_id", "customer_id", "is_active", "surface", "date_built"],
});

export const power_unitsResource = registerResource({
  name: "power_units",
  label: "Power Units",
  category: "units",
  endpoint: "power_units",
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "power_unit",
        "label": "Power Unit Name",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "power_unit_type_id",
        "label": "Power Unit Type",
        "type": "reference",
        "filterable": true,
        "referenceResource": "power_unit_types"
    },
    {
        "key": "serial_number",
        "label": "Serial Number",
        "type": "text",
        "searchable": true
    },
    {
        "key": "structure_id",
        "label": "Structure",
        "type": "reference",
        "filterable": true,
        "referenceResource": "structures"
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    }
],
  searchableFields: ["power_unit", "serial_number"],
  filterableFields: ["power_unit_type_id", "structure_id", "is_active"],
});

export const gatewaysResource = registerResource({
  name: "gateways",
  label: "Gateways",
  category: "units",
  endpoint: "gateways",
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "imei",
        "label": "IMEI",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "structure_id",
        "label": "Structure",
        "type": "reference",
        "filterable": true,
        "referenceResource": "structures"
    },
    {
        "key": "ip_address",
        "label": "IP Address",
        "type": "text",
        "searchable": true
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    },
    {
        "key": "is_connected",
        "label": "Connected",
        "type": "boolean",
        "readOnly": true,
        "filterable": true
    },
    {
        "key": "last_seen_dt",
        "label": "Last Seen",
        "type": "datetime",
        "readOnly": true,
        "filterable": true
    }
],
  searchableFields: ["imei", "ip_address"],
  filterableFields: ["structure_id", "is_active", "is_connected", "last_seen_dt"],
});

export const partsResource = registerResource({
  name: "parts",
  label: "BoM Parts - Every Part that Finished Goods Need",
  category: "bom",
  endpoint: "parts",
  allowedRoles: ["bom_master"],
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "part_number",
        "label": "Part Number",
        "type": "text",
        "required": true,
        "searchable": true,
        "unique": true
    },
    {
        "key": "description",
        "label": "Description",
        "type": "text",
        "searchable": true
    },
    {
        "key": "part_category_id",
        "label": "Part Category",
        "type": "reference",
        "filterable": true,
        "referenceResource": "part_categories"
    },
    {
        "key": "unit_cost_cad",
        "label": "Unit Cost (CAD)",
        "type": "number",
        "format": "currency"
    },
    {
        "key": "msrp_cad",
        "label": "MSRP (CAD)",
        "type": "number",
        "format": "currency"
    },
    {
        "key": "dealer_cost_cad",
        "label": "Dealer Cost (CAD)",
        "type": "number",
        "format": "currency"
    },
    {
        "key": "part_image",
        "label": "Part Image",
        "type": "file",
        "accept": "image/*"
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    }
],
  searchableFields: ["part_number", "description"],
  filterableFields: ["part_category_id", "is_active"],
});

export const structure_typesResource = registerResource({
  name: "structure_types",
  label: "Structure Model Types - Finished Goods Mapping",
  category: "bom",
  endpoint: "structure_types",
  allowedRoles: ["bom_master"],
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "model_type",
        "label": "Model Type",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "model_type_short",
        "label": "Model Type Short",
        "type": "text",
        "searchable": true
    },
    {
        "key": "unit_type_id",
        "label": "Unit Type",
        "type": "reference",
        "filterable": true,
        "referenceResource": "unit_types"
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    }
],
  searchableFields: ["model_type", "model_type_short"],
  filterableFields: ["unit_type_id", "is_active"],
});

export const work_ordersResource = registerResource({
  name: "work_orders",
  label: "Work Orders (IJACK Inc 🍁)",
  category: "service",
  endpoint: "work_orders",
  allowedRoles: ["sales", "service", "hr"],
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "work_order_number",
        "label": "Work Order Number",
        "type": "text",
        "required": true,
        "searchable": true,
        "unique": true
    },
    {
        "key": "structure_id",
        "label": "Structure",
        "type": "reference",
        "filterable": true,
        "referenceResource": "structures"
    },
    {
        "key": "work_order_status_id",
        "label": "Status",
        "type": "reference",
        "filterable": true,
        "referenceResource": "work_order_status"
    },
    {
        "key": "service_type_id",
        "label": "Service Type",
        "type": "reference",
        "filterable": true,
        "referenceResource": "work_order_service_type"
    },
    {
        "key": "description",
        "label": "Description",
        "type": "text",
        "widget": "textarea"
    },
    {
        "key": "resolution_notes",
        "label": "Resolution Notes",
        "type": "text",
        "widget": "textarea"
    },
    {
        "key": "scheduled_dt",
        "label": "Scheduled Date",
        "type": "datetime",
        "filterable": true
    },
    {
        "key": "total_quoted",
        "label": "Total Quoted",
        "type": "number",
        "readOnly": true,
        "format": "currency"
    },
    {
        "key": "total_actual",
        "label": "Total Actual",
        "type": "number",
        "readOnly": true,
        "format": "currency"
    },
    {
        "key": "created_at",
        "label": "Created At",
        "type": "datetime",
        "readOnly": true,
        "filterable": true
    }
],
  searchableFields: ["work_order_number"],
  filterableFields: ["structure_id", "work_order_status_id", "service_type_id", "created_at", "scheduled_dt"],
});

export const service_requestsResource = registerResource({
  name: "service_requests",
  label: "Service Requests",
  category: "service",
  endpoint: "service_requests",
  allowedRoles: ["sales", "service"],
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "customer_id",
        "label": "Customer",
        "type": "reference",
        "filterable": true,
        "referenceResource": "customers"
    },
    {
        "key": "structure_id",
        "label": "Structure",
        "type": "reference",
        "filterable": true,
        "referenceResource": "structures"
    },
    {
        "key": "description",
        "label": "Description",
        "type": "text",
        "searchable": true,
        "widget": "textarea"
    },
    {
        "key": "priority",
        "label": "Priority",
        "type": "select",
        "filterable": true,
        "options": [
            {
                "label": "Low",
                "value": 1
            },
            {
                "label": "Medium",
                "value": 2
            },
            {
                "label": "High",
                "value": 3
            },
            {
                "label": "Critical",
                "value": 4
            }
        ]
    },
    {
        "key": "created_at",
        "label": "Created At",
        "type": "datetime",
        "readOnly": true,
        "filterable": true
    }
],
  searchableFields: ["description"],
  filterableFields: ["customer_id", "structure_id", "priority", "created_at"],
});

export const inventory_warehousesResource = registerResource({
  name: "inventory_warehouses",
  label: "Warehouses",
  category: "inventory",
  endpoint: "inventory_warehouses",
  allowedRoles: ["sales", "service", "bom_master"],
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "warehouse_name",
        "label": "Warehouse Name",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "warehouse_code",
        "label": "Warehouse Code",
        "type": "text",
        "searchable": true,
        "unique": true
    },
    {
        "key": "address",
        "label": "Address",
        "type": "text",
        "widget": "textarea"
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    }
],
  searchableFields: ["warehouse_name", "warehouse_code"],
  filterableFields: ["is_active"],
});

export const inventory_warehouse_partsResource = registerResource({
  name: "inventory_warehouse_parts",
  label: "Warehouse Parts (Current Stock)",
  category: "inventory",
  endpoint: "inventory_warehouse_parts",
  allowedRoles: ["sales", "service", "bom_master"],
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "warehouse_id",
        "label": "Warehouse",
        "type": "reference",
        "filterable": true,
        "referenceResource": "inventory_warehouses"
    },
    {
        "key": "part_id",
        "label": "Part",
        "type": "reference",
        "filterable": true,
        "referenceResource": "parts"
    },
    {
        "key": "quantity_on_hand",
        "label": "On Hand",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "quantity_reserved",
        "label": "Reserved",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "quantity_available",
        "label": "Available",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "quantity_desired",
        "label": "Quantity Desired",
        "type": "number",
        "editable": true
    },
    {
        "key": "last_updated",
        "label": "Last Updated",
        "type": "datetime",
        "readOnly": true
    }
],
  filterableFields: ["warehouse_id", "part_id"],
  editableFields: ["quantity_desired"],
});

export const alertsResource = registerResource({
  name: "alerts",
  label: "Alerts",
  category: "alerts",
  endpoint: "alerts",
  fields: [
    {
        "key": "id",
        "label": "Id",
        "type": "number",
        "readOnly": true
    },
    {
        "key": "alert_name",
        "label": "Alert Name",
        "type": "text",
        "required": true,
        "searchable": true
    },
    {
        "key": "structure_id",
        "label": "Structure",
        "type": "reference",
        "filterable": true,
        "referenceResource": "structures"
    },
    {
        "key": "alert_type",
        "label": "Alert Type",
        "type": "select",
        "filterable": true,
        "options": [
            {
                "label": "High Pressure",
                "value": "high_pressure"
            },
            {
                "label": "Low Pressure",
                "value": "low_pressure"
            },
            {
                "label": "Not Running",
                "value": "not_running"
            },
            {
                "label": "Maintenance Due",
                "value": "maintenance_due"
            }
        ]
    },
    {
        "key": "is_active",
        "label": "Is Active",
        "type": "boolean",
        "filterable": true,
        "default": true
    },
    {
        "key": "created_at",
        "label": "Created At",
        "type": "datetime",
        "readOnly": true,
        "filterable": true
    }
],
  searchableFields: ["alert_name"],
  filterableFields: ["structure_id", "alert_type", "is_active", "created_at"],
});

export const sales_by_person_yearResource = registerResource({
  name: "sales_by_person_year",
  label: "Sales by Person by Year",
  category: "reports",
  endpoint: "sales_by_person_year",
  allowedRoles: ["sales", "service", "hr"],
  readOnly: true,
  canCreate: false,
  canEdit: false,
  canDelete: false,
  fields: [
    {
        "key": "user_id",
        "label": "User",
        "type": "reference",
        "filterable": true,
        "referenceResource": "users"
    },
    {
        "key": "year",
        "label": "Year",
        "type": "number",
        "filterable": true
    },
    {
        "key": "total_sales",
        "label": "Total Sales",
        "type": "number",
        "format": "currency"
    },
    {
        "key": "units_sold",
        "label": "Units Sold",
        "type": "number"
    },
    {
        "key": "avg_sale_amount",
        "label": "Average Sale Amount",
        "type": "number",
        "format": "currency"
    }
],
  filterableFields: ["year", "user_id"],
});

// Export all resources
export const allCompleteResources: ResourceConfig[] = [
  usersResource,
  customersResource,
  applicationsResource,
  countriesResource,
  provinces_statesResource,
  structuresResource,
  power_unitsResource,
  gatewaysResource,
  partsResource,
  structure_typesResource,
  work_ordersResource,
  service_requestsResource,
  inventory_warehousesResource,
  inventory_warehouse_partsResource,
  alertsResource,
  sales_by_person_yearResource
];

// Export categorized resources
export const completeResourcesByCategory = {
  customers: [usersResource, customersResource, applicationsResource, countriesResource, provinces_statesResource],
  units: [structuresResource, power_unitsResource, gatewaysResource],
  bom: [partsResource, structure_typesResource],
  service: [work_ordersResource, service_requestsResource],
  inventory: [inventory_warehousesResource, inventory_warehouse_partsResource],
  alerts: [alertsResource],
  reports: [sales_by_person_yearResource],
};
