// Auto-generated refine admin resources for Reports category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const reportsResources: ResourceProps[] = [
  {
    name: "sales_by_person_year",
    list: "/admin/sales_by_person_year/sales_by_person_year",
    create: "/admin/sales_by_person_year/sales_by_person_year",
    edit: "/admin/sales_by_person_year/sales_by_person_year",
    show: "/admin/sales_by_person_year/sales_by_person_year",
    meta: {
      label: "Sales by Person by Year",
      category: "Reports"
    }
,
    listFields: [
      {
            "key": "serviceYear",
            "title": "Service Year",
            "dataIndex": "service_year",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name ",
            "dataIndex": "name_",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "salesWarranty",
            "title": "Sales Warranty",
            "dataIndex": "sales_warranty",
            "type": "text",
            "sortable": true
      },
      {
            "key": "salesTotal",
            "title": "Sales Total",
            "dataIndex": "sales_total",
            "type": "number",
            "sortable": true
      },
      {
            "key": "salesLabour",
            "title": "Sales Labour",
            "dataIndex": "sales_labour",
            "type": "text",
            "sortable": true
      },
      {
            "key": "salesParts",
            "title": "Sales Parts",
            "dataIndex": "sales_parts",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeSales",
            "title": "Type Sales",
            "dataIndex": "type_sales",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeRentals",
            "title": "Type Rentals",
            "dataIndex": "type_rentals",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeNewInstalls",
            "title": "Type New Installs",
            "dataIndex": "type_new_installs",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeRepairs",
            "title": "Type Repairs",
            "dataIndex": "type_repairs",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeParts",
            "title": "Type Parts",
            "dataIndex": "type_parts",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typePreventMaint",
            "title": "Type Prevent Maint",
            "dataIndex": "type_prevent_maint",
            "type": "text",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "sales_by_person_quarter",
    list: "/admin/sales_by_person_quarter/sales_by_person_quarter",
    create: "/admin/sales_by_person_quarter/sales_by_person_quarter",
    edit: "/admin/sales_by_person_quarter/sales_by_person_quarter",
    show: "/admin/sales_by_person_quarter/sales_by_person_quarter",
    meta: {
      label: "Sales by Person by Quarter",
      category: "Reports"
    }
,
    listFields: [
      {
            "key": "serviceYear",
            "title": "Service Year",
            "dataIndex": "service_year",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "serviceQuarter",
            "title": "Service Quarter",
            "dataIndex": "service_quarter",
            "type": "text",
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name ",
            "dataIndex": "name_",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "salesWarranty",
            "title": "Sales Warranty",
            "dataIndex": "sales_warranty",
            "type": "text",
            "sortable": true
      },
      {
            "key": "salesTotal",
            "title": "Sales Total",
            "dataIndex": "sales_total",
            "type": "number",
            "sortable": true
      },
      {
            "key": "salesLabour",
            "title": "Sales Labour",
            "dataIndex": "sales_labour",
            "type": "text",
            "sortable": true
      },
      {
            "key": "salesParts",
            "title": "Sales Parts",
            "dataIndex": "sales_parts",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeSales",
            "title": "Type Sales",
            "dataIndex": "type_sales",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeRentals",
            "title": "Type Rentals",
            "dataIndex": "type_rentals",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeNewInstalls",
            "title": "Type New Installs",
            "dataIndex": "type_new_installs",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeRepairs",
            "title": "Type Repairs",
            "dataIndex": "type_repairs",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeParts",
            "title": "Type Parts",
            "dataIndex": "type_parts",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typePreventMaint",
            "title": "Type Prevent Maint",
            "dataIndex": "type_prevent_maint",
            "type": "text",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "sales_by_person_month",
    list: "/admin/sales_by_person_month/sales_by_person_month",
    create: "/admin/sales_by_person_month/sales_by_person_month",
    edit: "/admin/sales_by_person_month/sales_by_person_month",
    show: "/admin/sales_by_person_month/sales_by_person_month",
    meta: {
      label: "Sales by Person by Month",
      category: "Reports"
    }
,
    listFields: [
      {
            "key": "serviceYear",
            "title": "Service Year",
            "dataIndex": "service_year",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "serviceMonth",
            "title": "Service Month",
            "dataIndex": "service_month",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name ",
            "dataIndex": "name_",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "salesWarranty",
            "title": "Sales Warranty",
            "dataIndex": "sales_warranty",
            "type": "text",
            "sortable": true
      },
      {
            "key": "salesTotal",
            "title": "Sales Total",
            "dataIndex": "sales_total",
            "type": "number",
            "sortable": true
      },
      {
            "key": "salesLabour",
            "title": "Sales Labour",
            "dataIndex": "sales_labour",
            "type": "text",
            "sortable": true
      },
      {
            "key": "salesParts",
            "title": "Sales Parts",
            "dataIndex": "sales_parts",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeSales",
            "title": "Type Sales",
            "dataIndex": "type_sales",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeRentals",
            "title": "Type Rentals",
            "dataIndex": "type_rentals",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeNewInstalls",
            "title": "Type New Installs",
            "dataIndex": "type_new_installs",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeRepairs",
            "title": "Type Repairs",
            "dataIndex": "type_repairs",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typeParts",
            "title": "Type Parts",
            "dataIndex": "type_parts",
            "type": "text",
            "sortable": true
      },
      {
            "key": "typePreventMaint",
            "title": "Type Prevent Maint",
            "dataIndex": "type_prevent_maint",
            "type": "text",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "hours_billed_monthly_efficiency",
    list: "/admin/hours_billed_monthly_efficiency/hours_billed_monthly_efficiency",
    create: "/admin/hours_billed_monthly_efficiency/hours_billed_monthly_efficiency",
    edit: "/admin/hours_billed_monthly_efficiency/hours_billed_monthly_efficiency",
    show: "/admin/hours_billed_monthly_efficiency/hours_billed_monthly_efficiency",
    meta: {
      label: "Hours Billed (Whole Team) - Monthly Efficiency",
      category: "Reports"
    }
,
    listFields: [
      {
            "key": "serviceYear",
            "title": "Service Year",
            "dataIndex": "service_year",
            "type": "text",
            "searchable": true
      },
      {
            "key": "serviceMonth",
            "title": "Service Month",
            "dataIndex": "service_month",
            "type": "text"
      },
      {
            "key": "billedPerHourWorked",
            "title": "Billed Per Hour Worked",
            "dataIndex": "billed_per_hour_worked",
            "type": "text"
      },
      {
            "key": "quantityHoursBilled",
            "title": "Quantity Hours Billed",
            "dataIndex": "quantity_hours_billed",
            "type": "number"
      },
      {
            "key": "monthlyHoursWorkedClock",
            "title": "Monthly Hours Worked Clock",
            "dataIndex": "monthly_hours_worked_clock",
            "type": "text"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "hours_billed_by_field_tech_monthly_efficiency",
    list: "/admin/hours_billed_by_field_tech_monthly_efficiency/hours_billed_by_field_tech_monthly_efficiency",
    create: "/admin/hours_billed_by_field_tech_monthly_efficiency/hours_billed_by_field_tech_monthly_efficiency",
    edit: "/admin/hours_billed_by_field_tech_monthly_efficiency/hours_billed_by_field_tech_monthly_efficiency",
    show: "/admin/hours_billed_by_field_tech_monthly_efficiency/hours_billed_by_field_tech_monthly_efficiency",
    meta: {
      label: "Hours Billed by Field Tech - Monthly Efficiency",
      category: "Reports"
    }
,
    listFields: [
      {
            "key": "serviceYear",
            "title": "Service Year",
            "dataIndex": "service_year",
            "type": "text"
      },
      {
            "key": "serviceMonth",
            "title": "Service Month",
            "dataIndex": "service_month",
            "type": "text"
      },
      {
            "key": "fullName",
            "title": "Full Name",
            "dataIndex": "full_name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "billedPerHourWorked",
            "title": "Billed Per Hour Worked",
            "dataIndex": "billed_per_hour_worked",
            "type": "text"
      },
      {
            "key": "quantityHoursBilled",
            "title": "Quantity Hours Billed",
            "dataIndex": "quantity_hours_billed",
            "type": "number"
      },
      {
            "key": "monthlyHoursWorkedClock",
            "title": "Monthly Hours Worked Clock",
            "dataIndex": "monthly_hours_worked_clock",
            "type": "text"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "hours_billed_by_field_tech_by_work_order",
    list: "/admin/hours_billed_by_field_tech_by_work_order/hours_billed_by_field_tech_by_work_order",
    create: "/admin/hours_billed_by_field_tech_by_work_order/hours_billed_by_field_tech_by_work_order",
    edit: "/admin/hours_billed_by_field_tech_by_work_order/hours_billed_by_field_tech_by_work_order",
    show: "/admin/hours_billed_by_field_tech_by_work_order/hours_billed_by_field_tech_by_work_order",
    meta: {
      label: "Hours Billed by Field Tech - by Work Order",
      category: "Reports"
    }
,
    listFields: [
      {
            "key": "workOrderId",
            "title": "Work Order Id",
            "dataIndex": "work_order_id",
            "type": "number"
      },
      {
            "key": "serviceYear",
            "title": "Service Year",
            "dataIndex": "service_year",
            "type": "text"
      },
      {
            "key": "serviceMonth",
            "title": "Service Month",
            "dataIndex": "service_month",
            "type": "text"
      },
      {
            "key": "isWarranty",
            "title": "Is Warranty",
            "dataIndex": "is_warranty",
            "type": "boolean"
      },
      {
            "key": "name",
            "title": "Name ",
            "dataIndex": "name_",
            "type": "text",
            "searchable": true
      },
      {
            "key": "dateService",
            "title": "Date Service",
            "dataIndex": "date_service",
            "type": "datetime"
      },
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text",
            "searchable": true
      },
      {
            "key": "monthlyHoursWorkedClock",
            "title": "Monthly Hours Worked Clock",
            "dataIndex": "monthly_hours_worked_clock",
            "type": "text"
      },
      {
            "key": "quantityHoursBilled",
            "title": "Quantity Hours Billed",
            "dataIndex": "quantity_hours_billed",
            "type": "number"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "service_clock_daily",
    list: "/admin/service_clock_daily/service_clock_daily",
    create: "/admin/service_clock_daily/service_clock_daily",
    edit: "/admin/service_clock_daily/service_clock_daily",
    show: "/admin/service_clock_daily/service_clock_daily",
    meta: {
      label: "Service Clock - Daily Hours",
      category: "Reports"
    }
,
    listFields: [
      {
            "key": "year",
            "title": "Year ",
            "dataIndex": "year_",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "month",
            "title": "Month ",
            "dataIndex": "month_",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "day",
            "title": "Day ",
            "dataIndex": "day_",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name ",
            "dataIndex": "name_",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "timeRecords",
            "title": "Time Records",
            "dataIndex": "time_records",
            "type": "datetime"
      },
      {
            "key": "daysWorked",
            "title": "Days Worked",
            "dataIndex": "days_worked",
            "type": "text",
            "sortable": true
      },
      {
            "key": "hoursWorked",
            "title": "Hours Worked",
            "dataIndex": "hours_worked",
            "type": "text",
            "sortable": true
      },
      {
            "key": "totalHours",
            "title": "Total Hours",
            "dataIndex": "total_hours",
            "type": "number",
            "sortable": true
      },
      {
            "key": "serviceHours",
            "title": "Service Hours",
            "dataIndex": "service_hours",
            "type": "text",
            "sortable": true
      },
      {
            "key": "travelHours",
            "title": "Travel Hours",
            "dataIndex": "travel_hours",
            "type": "text",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "service_clock_monthly",
    list: "/admin/service_clock_monthly/service_clock_monthly",
    create: "/admin/service_clock_monthly/service_clock_monthly",
    edit: "/admin/service_clock_monthly/service_clock_monthly",
    show: "/admin/service_clock_monthly/service_clock_monthly",
    meta: {
      label: "Service Clock - Monthly Hours",
      category: "Reports"
    }

  },
  {
    name: "service_clock_yearly",
    list: "/admin/service_clock_yearly/service_clock_yearly",
    create: "/admin/service_clock_yearly/service_clock_yearly",
    edit: "/admin/service_clock_yearly/service_clock_yearly",
    show: "/admin/service_clock_yearly/service_clock_yearly",
    meta: {
      label: "Service Clock - Yearly Hours",
      category: "Reports"
    }

  },
];

export default reportsResources;
