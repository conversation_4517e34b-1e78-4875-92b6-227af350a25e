// Auto-generated refine admin resources for Customers category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const customersResources: ResourceProps[] = [
  {
    name: "applications",
    list: "/admin/applications/applications",
    create: "/admin/applications/applications",
    edit: "/admin/applications/applications",
    show: "/admin/applications/applications",
    meta: {
      label: "Applications - All Combined",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "application_types_rel.name",
            "title": "Application Types Rel.Name",
            "dataIndex": "application_types_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "customer_rel.customer",
            "title": "Customer Rel.Customer",
            "dataIndex": "customer_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "companyName",
            "title": "Company Name",
            "dataIndex": "company_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "street",
            "title": "Street",
            "dataIndex": "street",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "countries_rel.country_name",
            "title": "Countries Rel.Country Name",
            "dataIndex": "countries_rel.country_name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "user_rel.email",
            "title": "User Rel.Email",
            "dataIndex": "user_rel.email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactName",
            "title": "Contact Name",
            "dataIndex": "contact_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactPhone",
            "title": "Contact Phone",
            "dataIndex": "contact_phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactEmail",
            "title": "Contact Email",
            "dataIndex": "contact_email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "installLocation",
            "title": "Install Location",
            "dataIndex": "install_location",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactName",
            "title": "Field Contact Name",
            "dataIndex": "field_contact_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactPhone",
            "title": "Field Contact Phone",
            "dataIndex": "field_contact_phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactEmail",
            "title": "Field Contact Email",
            "dataIndex": "field_contact_email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "projectObjective",
            "title": "Project Objective",
            "dataIndex": "project_objective",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "currentProcessEquipment",
            "title": "Current Process Equipment",
            "dataIndex": "current_process_equipment",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "currentProcessIssues",
            "title": "Current Process Issues",
            "dataIndex": "current_process_issues",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "whenNeedEquipment",
            "title": "When Need Equipment",
            "dataIndex": "when_need_equipment",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "emulsionComingFrom",
            "title": "Emulsion Coming From",
            "dataIndex": "emulsion_coming_from",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "emulsionDischargingInto",
            "title": "Emulsion Discharging Into",
            "dataIndex": "emulsion_discharging_into",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourComingOffStorage",
            "title": "Vapour Coming Off Storage",
            "dataIndex": "vapour_coming_off_storage",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourComingOffVrTanks",
            "title": "Vapour Coming Off Vr Tanks",
            "dataIndex": "vapour_coming_off_vr_tanks",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourComingOffOther",
            "title": "Vapour Coming Off Other",
            "dataIndex": "vapour_coming_off_other",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourDischargingInto",
            "title": "Vapour Discharging Into",
            "dataIndex": "vapour_discharging_into",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "tankPressureRating",
            "title": "Tank Pressure Rating",
            "dataIndex": "tank_pressure_rating",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "tankPressureDesired",
            "title": "Tank Pressure Desired",
            "dataIndex": "tank_pressure_desired",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "casingCount",
            "title": "Casing Count",
            "dataIndex": "casing_count",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "dischargingIntoEmulsion1OrGasLine2",
            "title": "Discharging Into Emulsion1 Or Gas Line2",
            "dataIndex": "discharging_into_emulsion1_or_gas_line2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "flowlinePressureOnTest",
            "title": "Flowline Pressure On Test",
            "dataIndex": "flowline_pressure_on_test",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "artificialLiftSystem",
            "title": "Artificial Lift System",
            "dataIndex": "artificial_lift_system",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "separatorsInstalled",
            "title": "Separators Installed",
            "dataIndex": "separators_installed",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "compressorB4Separators",
            "title": "Compressor B4 Separators",
            "dataIndex": "compressor_b4_separators",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "wellPumpedOffStatus",
            "title": "Well Pumped Off Status",
            "dataIndex": "well_pumped_off_status",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpFillagePct",
            "title": "Pump Fillage Pct",
            "dataIndex": "pump_fillage_pct",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "formationPressure",
            "title": "Formation Pressure",
            "dataIndex": "formation_pressure",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpMake",
            "title": "Pump Make",
            "dataIndex": "pump_make",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpModel",
            "title": "Pump Model",
            "dataIndex": "pump_model",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpSpeedSpm",
            "title": "Pump Speed Spm",
            "dataIndex": "pump_speed_spm",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpStrokeLength",
            "title": "Pump Stroke Length",
            "dataIndex": "pump_stroke_length",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpRodLoad",
            "title": "Pump Rod Load",
            "dataIndex": "pump_rod_load",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpLongStroke",
            "title": "Pump Long Stroke",
            "dataIndex": "pump_long_stroke",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpSetOnCement1Piles2",
            "title": "Pump Set On Cement1 Piles2",
            "dataIndex": "pump_set_on_cement1_piles2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpBaseHeight",
            "title": "Pump Base Height",
            "dataIndex": "pump_base_height",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpNumRails",
            "title": "Pump Num Rails",
            "dataIndex": "pump_num_rails",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpNumTiedowns",
            "title": "Pump Num Tiedowns",
            "dataIndex": "pump_num_tiedowns",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "usePpm1Percent2",
            "title": "Use Ppm1 Percent2",
            "dataIndex": "use_ppm1_percent2",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "h2s",
            "title": "H2S",
            "dataIndex": "h2s",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "co2",
            "title": "Co2",
            "dataIndex": "co2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "salinity",
            "title": "Salinity",
            "dataIndex": "salinity",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "usePsi1Kpa2Ozsqinch3",
            "title": "Use Psi1 Kpa2 Ozsqinch3",
            "dataIndex": "use_psi1_kpa2_ozsqinch3",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletPressureCurrent",
            "title": "Inlet Pressure Current",
            "dataIndex": "inlet_pressure_current",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletPressureDesired",
            "title": "Inlet Pressure Desired",
            "dataIndex": "inlet_pressure_desired",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "dischargePressure",
            "title": "Discharge Pressure",
            "dataIndex": "discharge_pressure",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "useE3m3d1Mcfd2",
            "title": "Use E3M3D1 Mcfd2",
            "dataIndex": "use_e3m3d1_mcfd2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "expectedGasVolume",
            "title": "Expected Gas Volume",
            "dataIndex": "expected_gas_volume",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "expectedWaterVolume",
            "title": "Expected Water Volume",
            "dataIndex": "expected_water_volume",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "expectedOilVolume",
            "title": "Expected Oil Volume",
            "dataIndex": "expected_oil_volume",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "useCelsius1Fahrenheit2",
            "title": "Use Celsius1 Fahrenheit2",
            "dataIndex": "use_celsius1_fahrenheit2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletTemp",
            "title": "Inlet Temp",
            "dataIndex": "inlet_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "maxDischargeFlowlineTemp",
            "title": "Max Discharge Flowline Temp",
            "dataIndex": "max_discharge_flowline_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "maxAmbientTemp",
            "title": "Max Ambient Temp",
            "dataIndex": "max_ambient_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "sand",
            "title": "Sand",
            "dataIndex": "sand",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fracSand",
            "title": "Frac Sand",
            "dataIndex": "frac_sand",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "parafin",
            "title": "Parafin",
            "dataIndex": "parafin",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "otherSolids",
            "title": "Other Solids",
            "dataIndex": "other_solids",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pipelineDiameterInletInches",
            "title": "Pipeline Diameter Inlet Inches",
            "dataIndex": "pipeline_diameter_inlet_inches",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pipelineDiameterDischargeInches",
            "title": "Pipeline Diameter Discharge Inches",
            "dataIndex": "pipeline_diameter_discharge_inches",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerAvailable",
            "title": "Power Available",
            "dataIndex": "power_available",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerVoltage",
            "title": "Power Voltage",
            "dataIndex": "power_voltage",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerPhase",
            "title": "Power Phase",
            "dataIndex": "power_phase",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerAmps",
            "title": "Power Amps",
            "dataIndex": "power_amps",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerGrid",
            "title": "Power Grid",
            "dataIndex": "power_grid",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerGenerator",
            "title": "Power Generator",
            "dataIndex": "power_generator",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelDiesel",
            "title": "Fuel Diesel",
            "dataIndex": "fuel_diesel",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelNaturalGas",
            "title": "Fuel Natural Gas",
            "dataIndex": "fuel_natural_gas",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelPropane",
            "title": "Fuel Propane",
            "dataIndex": "fuel_propane",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "cellSignalGood",
            "title": "Cell Signal Good",
            "dataIndex": "cell_signal_good",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "specialMetalsNeeded",
            "title": "Special Metals Needed",
            "dataIndex": "special_metals_needed",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "specialRequirements",
            "title": "Special Requirements",
            "dataIndex": "special_requirements",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "application_upload_files_rel.file_name",
            "title": "Application Upload Files Rel.File Name",
            "dataIndex": "application_upload_files_rel.file_name",
            "type": "number"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "applications_xfer",
    list: "/admin/applications_xfer/applications_xfer",
    create: "/admin/applications_xfer/applications_xfer",
    edit: "/admin/applications_xfer/applications_xfer",
    show: "/admin/applications_xfer/applications_xfer",
    meta: {
      label: "Applications - XFER",
      category: "Customers"
    }

  },
  {
    name: "applications_vru",
    list: "/admin/applications_vru/applications_vru",
    create: "/admin/applications_vru/applications_vru",
    edit: "/admin/applications_vru/applications_vru",
    show: "/admin/applications_vru/applications_vru",
    meta: {
      label: "Applications - VRU",
      category: "Customers"
    }

  },
  {
    name: "applications_egas",
    list: "/admin/applications_egas/applications_egas",
    create: "/admin/applications_egas/applications_egas",
    edit: "/admin/applications_egas/applications_egas",
    show: "/admin/applications_egas/applications_egas",
    meta: {
      label: "Applications - EGAS",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "application_types_rel.name",
            "title": "Application Types Rel.Name",
            "dataIndex": "application_types_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "customer_rel.customer",
            "title": "Customer Rel.Customer",
            "dataIndex": "customer_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "companyName",
            "title": "Company Name",
            "dataIndex": "company_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "street",
            "title": "Street",
            "dataIndex": "street",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "countries_rel.country_name",
            "title": "Countries Rel.Country Name",
            "dataIndex": "countries_rel.country_name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "user_rel.email",
            "title": "User Rel.Email",
            "dataIndex": "user_rel.email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactName",
            "title": "Contact Name",
            "dataIndex": "contact_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactPhone",
            "title": "Contact Phone",
            "dataIndex": "contact_phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactEmail",
            "title": "Contact Email",
            "dataIndex": "contact_email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "installLocation",
            "title": "Install Location",
            "dataIndex": "install_location",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactName",
            "title": "Field Contact Name",
            "dataIndex": "field_contact_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactPhone",
            "title": "Field Contact Phone",
            "dataIndex": "field_contact_phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactEmail",
            "title": "Field Contact Email",
            "dataIndex": "field_contact_email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "projectObjective",
            "title": "Project Objective",
            "dataIndex": "project_objective",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "currentProcessEquipment",
            "title": "Current Process Equipment",
            "dataIndex": "current_process_equipment",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "currentProcessIssues",
            "title": "Current Process Issues",
            "dataIndex": "current_process_issues",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "whenNeedEquipment",
            "title": "When Need Equipment",
            "dataIndex": "when_need_equipment",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "emulsionComingFrom",
            "title": "Emulsion Coming From",
            "dataIndex": "emulsion_coming_from",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "emulsionDischargingInto",
            "title": "Emulsion Discharging Into",
            "dataIndex": "emulsion_discharging_into",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourComingOffStorage",
            "title": "Vapour Coming Off Storage",
            "dataIndex": "vapour_coming_off_storage",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourComingOffVrTanks",
            "title": "Vapour Coming Off Vr Tanks",
            "dataIndex": "vapour_coming_off_vr_tanks",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourComingOffOther",
            "title": "Vapour Coming Off Other",
            "dataIndex": "vapour_coming_off_other",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourDischargingInto",
            "title": "Vapour Discharging Into",
            "dataIndex": "vapour_discharging_into",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "tankPressureRating",
            "title": "Tank Pressure Rating",
            "dataIndex": "tank_pressure_rating",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "tankPressureDesired",
            "title": "Tank Pressure Desired",
            "dataIndex": "tank_pressure_desired",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "casingCount",
            "title": "Casing Count",
            "dataIndex": "casing_count",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "dischargingIntoEmulsion1OrGasLine2",
            "title": "Discharging Into Emulsion1 Or Gas Line2",
            "dataIndex": "discharging_into_emulsion1_or_gas_line2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "flowlinePressureOnTest",
            "title": "Flowline Pressure On Test",
            "dataIndex": "flowline_pressure_on_test",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "artificialLiftSystem",
            "title": "Artificial Lift System",
            "dataIndex": "artificial_lift_system",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "separatorsInstalled",
            "title": "Separators Installed",
            "dataIndex": "separators_installed",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "compressorB4Separators",
            "title": "Compressor B4 Separators",
            "dataIndex": "compressor_b4_separators",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "wellPumpedOffStatus",
            "title": "Well Pumped Off Status",
            "dataIndex": "well_pumped_off_status",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpFillagePct",
            "title": "Pump Fillage Pct",
            "dataIndex": "pump_fillage_pct",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "formationPressure",
            "title": "Formation Pressure",
            "dataIndex": "formation_pressure",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpMake",
            "title": "Pump Make",
            "dataIndex": "pump_make",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpModel",
            "title": "Pump Model",
            "dataIndex": "pump_model",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpSpeedSpm",
            "title": "Pump Speed Spm",
            "dataIndex": "pump_speed_spm",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpStrokeLength",
            "title": "Pump Stroke Length",
            "dataIndex": "pump_stroke_length",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpRodLoad",
            "title": "Pump Rod Load",
            "dataIndex": "pump_rod_load",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpLongStroke",
            "title": "Pump Long Stroke",
            "dataIndex": "pump_long_stroke",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpSetOnCement1Piles2",
            "title": "Pump Set On Cement1 Piles2",
            "dataIndex": "pump_set_on_cement1_piles2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpBaseHeight",
            "title": "Pump Base Height",
            "dataIndex": "pump_base_height",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpNumRails",
            "title": "Pump Num Rails",
            "dataIndex": "pump_num_rails",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpNumTiedowns",
            "title": "Pump Num Tiedowns",
            "dataIndex": "pump_num_tiedowns",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "usePpm1Percent2",
            "title": "Use Ppm1 Percent2",
            "dataIndex": "use_ppm1_percent2",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "h2s",
            "title": "H2S",
            "dataIndex": "h2s",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "co2",
            "title": "Co2",
            "dataIndex": "co2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "salinity",
            "title": "Salinity",
            "dataIndex": "salinity",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "usePsi1Kpa2Ozsqinch3",
            "title": "Use Psi1 Kpa2 Ozsqinch3",
            "dataIndex": "use_psi1_kpa2_ozsqinch3",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletPressureCurrent",
            "title": "Inlet Pressure Current",
            "dataIndex": "inlet_pressure_current",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletPressureDesired",
            "title": "Inlet Pressure Desired",
            "dataIndex": "inlet_pressure_desired",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "dischargePressure",
            "title": "Discharge Pressure",
            "dataIndex": "discharge_pressure",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "useE3m3d1Mcfd2",
            "title": "Use E3M3D1 Mcfd2",
            "dataIndex": "use_e3m3d1_mcfd2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "expectedGasVolume",
            "title": "Expected Gas Volume",
            "dataIndex": "expected_gas_volume",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "useCelsius1Fahrenheit2",
            "title": "Use Celsius1 Fahrenheit2",
            "dataIndex": "use_celsius1_fahrenheit2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletTemp",
            "title": "Inlet Temp",
            "dataIndex": "inlet_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "maxDischargeFlowlineTemp",
            "title": "Max Discharge Flowline Temp",
            "dataIndex": "max_discharge_flowline_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "maxAmbientTemp",
            "title": "Max Ambient Temp",
            "dataIndex": "max_ambient_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "otherSolids",
            "title": "Other Solids",
            "dataIndex": "other_solids",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerAvailable",
            "title": "Power Available",
            "dataIndex": "power_available",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerVoltage",
            "title": "Power Voltage",
            "dataIndex": "power_voltage",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerPhase",
            "title": "Power Phase",
            "dataIndex": "power_phase",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerAmps",
            "title": "Power Amps",
            "dataIndex": "power_amps",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerGrid",
            "title": "Power Grid",
            "dataIndex": "power_grid",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerGenerator",
            "title": "Power Generator",
            "dataIndex": "power_generator",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelDiesel",
            "title": "Fuel Diesel",
            "dataIndex": "fuel_diesel",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelNaturalGas",
            "title": "Fuel Natural Gas",
            "dataIndex": "fuel_natural_gas",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelPropane",
            "title": "Fuel Propane",
            "dataIndex": "fuel_propane",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "cellSignalGood",
            "title": "Cell Signal Good",
            "dataIndex": "cell_signal_good",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "specialRequirements",
            "title": "Special Requirements",
            "dataIndex": "special_requirements",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
]
  },
  {
    name: "applications_dgas",
    list: "/admin/applications_dgas/applications_dgas",
    create: "/admin/applications_dgas/applications_dgas",
    edit: "/admin/applications_dgas/applications_dgas",
    show: "/admin/applications_dgas/applications_dgas",
    meta: {
      label: "Applications - DGAS",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "application_types_rel.name",
            "title": "Application Types Rel.Name",
            "dataIndex": "application_types_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "customer_rel.customer",
            "title": "Customer Rel.Customer",
            "dataIndex": "customer_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "companyName",
            "title": "Company Name",
            "dataIndex": "company_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "street",
            "title": "Street",
            "dataIndex": "street",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "countries_rel.country_name",
            "title": "Countries Rel.Country Name",
            "dataIndex": "countries_rel.country_name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "user_rel.email",
            "title": "User Rel.Email",
            "dataIndex": "user_rel.email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactName",
            "title": "Contact Name",
            "dataIndex": "contact_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactPhone",
            "title": "Contact Phone",
            "dataIndex": "contact_phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "contactEmail",
            "title": "Contact Email",
            "dataIndex": "contact_email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "installLocation",
            "title": "Install Location",
            "dataIndex": "install_location",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactName",
            "title": "Field Contact Name",
            "dataIndex": "field_contact_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactPhone",
            "title": "Field Contact Phone",
            "dataIndex": "field_contact_phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fieldContactEmail",
            "title": "Field Contact Email",
            "dataIndex": "field_contact_email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "projectObjective",
            "title": "Project Objective",
            "dataIndex": "project_objective",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "currentProcessEquipment",
            "title": "Current Process Equipment",
            "dataIndex": "current_process_equipment",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "currentProcessIssues",
            "title": "Current Process Issues",
            "dataIndex": "current_process_issues",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "whenNeedEquipment",
            "title": "When Need Equipment",
            "dataIndex": "when_need_equipment",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "vapourComingOffStorage",
            "title": "Vapour Coming Off Storage",
            "dataIndex": "vapour_coming_off_storage",
            "type": "text"
      },
      {
            "key": "vapourComingOffVrTanks",
            "title": "Vapour Coming Off Vr Tanks",
            "dataIndex": "vapour_coming_off_vr_tanks",
            "type": "text"
      },
      {
            "key": "vapourComingOffOther",
            "title": "Vapour Coming Off Other",
            "dataIndex": "vapour_coming_off_other",
            "type": "text"
      },
      {
            "key": "vapourDischargingInto",
            "title": "Vapour Discharging Into",
            "dataIndex": "vapour_discharging_into",
            "type": "text"
      },
      {
            "key": "tankPressureRating",
            "title": "Tank Pressure Rating",
            "dataIndex": "tank_pressure_rating",
            "type": "number"
      },
      {
            "key": "tankPressureDesired",
            "title": "Tank Pressure Desired",
            "dataIndex": "tank_pressure_desired",
            "type": "number"
      },
      {
            "key": "casingCount",
            "title": "Casing Count",
            "dataIndex": "casing_count",
            "type": "number"
      },
      {
            "key": "dischargingIntoEmulsion1OrGasLine2",
            "title": "Discharging Into Emulsion1 Or Gas Line2",
            "dataIndex": "discharging_into_emulsion1_or_gas_line2",
            "type": "text"
      },
      {
            "key": "flowlinePressureOnTest",
            "title": "Flowline Pressure On Test",
            "dataIndex": "flowline_pressure_on_test",
            "type": "number"
      },
      {
            "key": "artificialLiftSystem",
            "title": "Artificial Lift System",
            "dataIndex": "artificial_lift_system",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "separatorsInstalled",
            "title": "Separators Installed",
            "dataIndex": "separators_installed",
            "type": "text"
      },
      {
            "key": "compressorB4Separators",
            "title": "Compressor B4 Separators",
            "dataIndex": "compressor_b4_separators",
            "type": "text"
      },
      {
            "key": "wellPumpedOffStatus",
            "title": "Well Pumped Off Status",
            "dataIndex": "well_pumped_off_status",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpFillagePct",
            "title": "Pump Fillage Pct",
            "dataIndex": "pump_fillage_pct",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "formationPressure",
            "title": "Formation Pressure",
            "dataIndex": "formation_pressure",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpMake",
            "title": "Pump Make",
            "dataIndex": "pump_make",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpModel",
            "title": "Pump Model",
            "dataIndex": "pump_model",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpSpeedSpm",
            "title": "Pump Speed Spm",
            "dataIndex": "pump_speed_spm",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpStrokeLength",
            "title": "Pump Stroke Length",
            "dataIndex": "pump_stroke_length",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpRodLoad",
            "title": "Pump Rod Load",
            "dataIndex": "pump_rod_load",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpLongStroke",
            "title": "Pump Long Stroke",
            "dataIndex": "pump_long_stroke",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpSetOnCement1Piles2",
            "title": "Pump Set On Cement1 Piles2",
            "dataIndex": "pump_set_on_cement1_piles2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpBaseHeight",
            "title": "Pump Base Height",
            "dataIndex": "pump_base_height",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpNumRails",
            "title": "Pump Num Rails",
            "dataIndex": "pump_num_rails",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "pumpNumTiedowns",
            "title": "Pump Num Tiedowns",
            "dataIndex": "pump_num_tiedowns",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "usePsi1Kpa2Ozsqinch3",
            "title": "Use Psi1 Kpa2 Ozsqinch3",
            "dataIndex": "use_psi1_kpa2_ozsqinch3",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletPressureCurrent",
            "title": "Inlet Pressure Current",
            "dataIndex": "inlet_pressure_current",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletPressureDesired",
            "title": "Inlet Pressure Desired",
            "dataIndex": "inlet_pressure_desired",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "dischargePressure",
            "title": "Discharge Pressure",
            "dataIndex": "discharge_pressure",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "useE3m3d1Mcfd2",
            "title": "Use E3M3D1 Mcfd2",
            "dataIndex": "use_e3m3d1_mcfd2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "expectedGasVolume",
            "title": "Expected Gas Volume",
            "dataIndex": "expected_gas_volume",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "useCelsius1Fahrenheit2",
            "title": "Use Celsius1 Fahrenheit2",
            "dataIndex": "use_celsius1_fahrenheit2",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "inletTemp",
            "title": "Inlet Temp",
            "dataIndex": "inlet_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "maxDischargeFlowlineTemp",
            "title": "Max Discharge Flowline Temp",
            "dataIndex": "max_discharge_flowline_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "maxAmbientTemp",
            "title": "Max Ambient Temp",
            "dataIndex": "max_ambient_temp",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "otherSolids",
            "title": "Other Solids",
            "dataIndex": "other_solids",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerAvailable",
            "title": "Power Available",
            "dataIndex": "power_available",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerVoltage",
            "title": "Power Voltage",
            "dataIndex": "power_voltage",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerPhase",
            "title": "Power Phase",
            "dataIndex": "power_phase",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerAmps",
            "title": "Power Amps",
            "dataIndex": "power_amps",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerGrid",
            "title": "Power Grid",
            "dataIndex": "power_grid",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerGenerator",
            "title": "Power Generator",
            "dataIndex": "power_generator",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelDiesel",
            "title": "Fuel Diesel",
            "dataIndex": "fuel_diesel",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelNaturalGas",
            "title": "Fuel Natural Gas",
            "dataIndex": "fuel_natural_gas",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "fuelPropane",
            "title": "Fuel Propane",
            "dataIndex": "fuel_propane",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "cellSignalGood",
            "title": "Cell Signal Good",
            "dataIndex": "cell_signal_good",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "specialRequirements",
            "title": "Special Requirements",
            "dataIndex": "special_requirements",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
]
  },
  {
    name: "career_applications",
    list: "/admin/career_applications/career_applications",
    create: "/admin/career_applications/career_applications",
    edit: "/admin/career_applications/career_applications",
    show: "/admin/career_applications/career_applications",
    meta: {
      label: "Career Applications",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "timestampInsertedUtc",
            "title": "Timestamp Inserted Utc",
            "dataIndex": "timestamp_inserted_utc",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "jobType",
            "title": "Job Type",
            "dataIndex": "job_type",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "message",
            "title": "Message",
            "dataIndex": "message",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "careerFilesRel",
            "title": "Career Files Rel",
            "dataIndex": "career_files_rel",
            "type": "select"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "timestampInsertedUtc",
            "title": "Timestamp Inserted Utc",
            "dataIndex": "timestamp_inserted_utc",
            "type": "datetime",
            "required": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "jobType",
            "title": "Job Type",
            "dataIndex": "job_type",
            "type": "text",
            "required": true
      },
      {
            "key": "message",
            "title": "Message",
            "dataIndex": "message",
            "type": "textarea",
            "required": true
      },
      {
            "key": "careerFilesRel",
            "title": "Career Files Rel",
            "dataIndex": "career_files_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "api_tokens",
    list: "/admin/api_tokens/api_tokens",
    create: "/admin/api_tokens/api_tokens",
    edit: "/admin/api_tokens/api_tokens",
    show: "/admin/api_tokens/api_tokens",
    meta: {
      label: "API Tokens",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      },
      {
            "key": "expires",
            "title": "Expires",
            "dataIndex": "expires",
            "type": "text",
            "sortable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "sortable": true
      },
      {
            "key": "token",
            "title": "Token",
            "dataIndex": "token",
            "type": "text",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "expires",
            "title": "Expires",
            "dataIndex": "expires",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "roles",
    list: "/admin/roles/roles",
    create: "/admin/roles/roles",
    edit: "/admin/roles/roles",
    show: "/admin/roles/roles",
    meta: {
      label: "Roles",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
]
  },
  {
    name: "users_roles",
    list: "/admin/users_roles/users_roles",
    create: "/admin/users_roles/users_roles",
    edit: "/admin/users_roles/users_roles",
    show: "/admin/users_roles/users_roles",
    meta: {
      label: "Users' Roles",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text"
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text"
      },
      {
            "key": "email",
            "title": "Email",
            "dataIndex": "email",
            "type": "email"
      },
      {
            "key": "jobTitle",
            "title": "Job Title",
            "dataIndex": "job_title",
            "type": "text"
      },
      {
            "key": "roles",
            "title": "Roles",
            "dataIndex": "roles",
            "type": "text"
      },
      {
            "key": "units",
            "title": "Units",
            "dataIndex": "units",
            "type": "text"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "customers",
    list: "/admin/customers/customers",
    create: "/admin/customers/customers",
    edit: "/admin/customers/customers",
    show: "/admin/customers/customers",
    meta: {
      label: "Customers",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "country_rel.country_name",
            "title": "Country Rel.Country Name",
            "dataIndex": "country_rel.country_name",
            "type": "number",
            "sortable": true
      },
      {
            "key": "formal",
            "title": "Formal",
            "dataIndex": "formal",
            "type": "text",
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "country",
            "title": "Country",
            "dataIndex": "country",
            "type": "number",
            "sortable": true
      },
      {
            "key": "mqttTopic",
            "title": "Mqtt Topic",
            "dataIndex": "mqtt_topic",
            "type": "text",
            "sortable": true
      },
      {
            "key": "cust_sub_groups_rel.name",
            "title": "Cust Sub Groups Rel.Name",
            "dataIndex": "cust_sub_groups_rel.name",
            "type": "select",
            "sortable": true
      },
      {
            "key": "isTaxExempt",
            "title": "Is Tax Exempt",
            "dataIndex": "is_tax_exempt",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "unit",
            "title": "Unit",
            "dataIndex": "unit",
            "type": "text",
            "sortable": true
      },
      {
            "key": "street",
            "title": "Street",
            "dataIndex": "street",
            "type": "text",
            "sortable": true
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text",
            "sortable": true
      },
      {
            "key": "state",
            "title": "State",
            "dataIndex": "state",
            "type": "text",
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "sortable": true
      },
      {
            "key": "postal",
            "title": "Postal",
            "dataIndex": "postal",
            "type": "text",
            "sortable": true
      },
      {
            "key": "accountingContactRel",
            "title": "Accounting Contact Rel",
            "dataIndex": "accounting_contact_rel",
            "type": "number"
      },
      {
            "key": "gstHstNumber",
            "title": "Gst Hst Number",
            "dataIndex": "gst_hst_number",
            "type": "number",
            "sortable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text",
            "required": true
      },
      {
            "key": "formal",
            "title": "Formal",
            "dataIndex": "formal",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "country",
            "title": "Country",
            "dataIndex": "country",
            "type": "number",
            "required": true
      },
      {
            "key": "countryRel",
            "title": "Country Rel",
            "dataIndex": "country_rel",
            "type": "number",
            "required": true
      },
      {
            "key": "mqttTopic",
            "title": "Mqtt Topic",
            "dataIndex": "mqtt_topic",
            "type": "text",
            "required": true
      },
      {
            "key": "custSubGroupsRel",
            "title": "Cust Sub Groups Rel",
            "dataIndex": "cust_sub_groups_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "isTaxExempt",
            "title": "Is Tax Exempt",
            "dataIndex": "is_tax_exempt",
            "type": "boolean",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "unit",
            "title": "Unit",
            "dataIndex": "unit",
            "type": "text",
            "required": true
      },
      {
            "key": "street",
            "title": "Street",
            "dataIndex": "street",
            "type": "text",
            "required": true
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text",
            "required": true
      },
      {
            "key": "state",
            "title": "State",
            "dataIndex": "state",
            "type": "text",
            "required": true
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "postal",
            "title": "Postal",
            "dataIndex": "postal",
            "type": "text",
            "required": true
      },
      {
            "key": "accountingContactRel",
            "title": "Accounting Contact Rel",
            "dataIndex": "accounting_contact_rel",
            "type": "number",
            "required": true
      },
      {
            "key": "gstHstNumber",
            "title": "Gst Hst Number",
            "dataIndex": "gst_hst_number",
            "type": "number",
            "required": true
      }
]
  },
  {
    name: "cust_sub_groups",
    list: "/admin/cust_sub_groups/cust_sub_groups",
    create: "/admin/cust_sub_groups/cust_sub_groups",
    edit: "/admin/cust_sub_groups/cust_sub_groups",
    show: "/admin/cust_sub_groups/cust_sub_groups",
    meta: {
      label: "Customer Sub-Groups",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "abbrev",
            "title": "Abbrev",
            "dataIndex": "abbrev",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "customers_rel.customer",
            "title": "Customers Rel.Customer",
            "dataIndex": "customers_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "structures_rel.structure",
            "title": "Structures Rel.Structure",
            "dataIndex": "structures_rel.structure",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "abbrev",
            "title": "Abbrev",
            "dataIndex": "abbrev",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "countries",
    list: "/admin/countries/countries",
    create: "/admin/countries/countries",
    edit: "/admin/countries/countries",
    show: "/admin/countries/countries",
    meta: {
      label: "Countries",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "countryCode",
            "title": "Country Code",
            "dataIndex": "country_code",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "countryName",
            "title": "Country Name",
            "dataIndex": "country_name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "salesTaxRate",
            "title": "Sales Tax Rate",
            "dataIndex": "sales_tax_rate",
            "type": "text",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "countryCode",
            "title": "Country Code",
            "dataIndex": "country_code",
            "type": "number",
            "required": true
      },
      {
            "key": "countryName",
            "title": "Country Name",
            "dataIndex": "country_name",
            "type": "number",
            "required": true
      },
      {
            "key": "salesTaxRate",
            "title": "Sales Tax Rate",
            "dataIndex": "sales_tax_rate",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "provinces_states",
    list: "/admin/provinces_states/provinces_states",
    create: "/admin/provinces_states/provinces_states",
    edit: "/admin/provinces_states/provinces_states",
    show: "/admin/provinces_states/provinces_states",
    meta: {
      label: "Provinces/States",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "abbrev",
            "title": "Abbrev",
            "dataIndex": "abbrev",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "country_rel.country_name",
            "title": "Country Rel.Country Name",
            "dataIndex": "country_rel.country_name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "abbrev",
            "title": "Abbrev",
            "dataIndex": "abbrev",
            "type": "text",
            "required": true
      },
      {
            "key": "countryRel",
            "title": "Country Rel",
            "dataIndex": "country_rel",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "counties",
    list: "/admin/counties/counties",
    create: "/admin/counties/counties",
    edit: "/admin/counties/counties",
    show: "/admin/counties/counties",
    meta: {
      label: "Counties",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "countyCode",
            "title": "County Code",
            "dataIndex": "county_code",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "countyCode",
            "title": "County Code",
            "dataIndex": "county_code",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "cities",
    list: "/admin/cities/cities",
    create: "/admin/cities/cities",
    edit: "/admin/cities/cities",
    show: "/admin/cities/cities",
    meta: {
      label: "Cities",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "county_rel.name",
            "title": "County Rel.Name",
            "dataIndex": "county_rel.name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "countyRel",
            "title": "County Rel",
            "dataIndex": "county_rel",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "zip_code_sales_tax",
    list: "/admin/zip_code_sales_tax/zip_code_sales_tax",
    create: "/admin/zip_code_sales_tax/zip_code_sales_tax",
    edit: "/admin/zip_code_sales_tax/zip_code_sales_tax",
    show: "/admin/zip_code_sales_tax/zip_code_sales_tax",
    meta: {
      label: "Sales Tax Rates by Zip Code",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "zip_code_rel.zip_code",
            "title": "Zip Code Rel.Zip Code",
            "dataIndex": "zip_code_rel.zip_code",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "county_rel.name",
            "title": "County Rel.Name",
            "dataIndex": "county_rel.name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "city_rel.name",
            "title": "City Rel.Name",
            "dataIndex": "city_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "statePlusCounty",
            "title": "State Plus County",
            "dataIndex": "state_plus_county",
            "type": "number",
            "sortable": true
      },
      {
            "key": "combinedRateEst",
            "title": "Combined Rate Est",
            "dataIndex": "combined_rate_est",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "stateRate",
            "title": "State Rate",
            "dataIndex": "state_rate",
            "type": "text",
            "sortable": true
      },
      {
            "key": "countyRate",
            "title": "County Rate",
            "dataIndex": "county_rate",
            "type": "number",
            "sortable": true
      },
      {
            "key": "cityRate",
            "title": "City Rate",
            "dataIndex": "city_rate",
            "type": "text",
            "sortable": true
      },
      {
            "key": "specialRate",
            "title": "Special Rate",
            "dataIndex": "special_rate",
            "type": "text",
            "sortable": true
      },
      {
            "key": "riskLevel",
            "title": "Risk Level",
            "dataIndex": "risk_level",
            "type": "text",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "zipCodeRel",
            "title": "Zip Code Rel",
            "dataIndex": "zip_code_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "countyRel",
            "title": "County Rel",
            "dataIndex": "county_rel",
            "type": "number",
            "required": true
      },
      {
            "key": "cityRel",
            "title": "City Rel",
            "dataIndex": "city_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "statePlusCounty",
            "title": "State Plus County",
            "dataIndex": "state_plus_county",
            "type": "number",
            "required": true
      },
      {
            "key": "combinedRateEst",
            "title": "Combined Rate Est",
            "dataIndex": "combined_rate_est",
            "type": "text",
            "required": true
      },
      {
            "key": "stateRate",
            "title": "State Rate",
            "dataIndex": "state_rate",
            "type": "text",
            "required": true
      },
      {
            "key": "countyRate",
            "title": "County Rate",
            "dataIndex": "county_rate",
            "type": "number",
            "required": true
      },
      {
            "key": "cityRate",
            "title": "City Rate",
            "dataIndex": "city_rate",
            "type": "text",
            "required": true
      },
      {
            "key": "specialRate",
            "title": "Special Rate",
            "dataIndex": "special_rate",
            "type": "text",
            "required": true
      },
      {
            "key": "riskLevel",
            "title": "Risk Level",
            "dataIndex": "risk_level",
            "type": "text",
            "required": true
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "countyRel",
            "title": "County Rel",
            "dataIndex": "county_rel",
            "type": "number",
            "required": true
      },
      {
            "key": "cityRel",
            "title": "City Rel",
            "dataIndex": "city_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "zipCodeRel",
            "title": "Zip Code Rel",
            "dataIndex": "zip_code_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "work_order_sales_tax",
    list: "/admin/work_order_sales_tax/work_order_sales_tax",
    create: "/admin/work_order_sales_tax/work_order_sales_tax",
    edit: "/admin/work_order_sales_tax/work_order_sales_tax",
    show: "/admin/work_order_sales_tax/work_order_sales_tax",
    meta: {
      label: "Sales Tax Rates by Province",
      category: "Customers"
    }
,
    listFields: [
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "rate",
            "title": "Rate",
            "dataIndex": "rate",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "rate",
            "title": "Rate",
            "dataIndex": "rate",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "users",
    list: "/admin/users/users",
    create: "/admin/users/users",
    edit: "/admin/users/users",
    show: "/admin/users/users",
    meta: {
      label: "Users",
      category: "Customers"
    }
,
    permissions: {
      "create": true,
      "delete": true
}
  },
];

export default customersResources;
