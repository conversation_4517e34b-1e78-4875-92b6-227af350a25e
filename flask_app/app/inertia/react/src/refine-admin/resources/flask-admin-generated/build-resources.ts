// Auto-generated refine admin resources for Build category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const buildResources: ResourceProps[] = [
  {
    name: "pump_top_options",
    list: "/admin/pump_top_options/pump_top_options",
    create: "/admin/pump_top_options/pump_top_options",
    edit: "/admin/pump_top_options/pump_top_options",
    show: "/admin/pump_top_options/pump_top_options",
    meta: {
      label: "Pump Top Options",
      category: "Build"
    }

  },
  {
    name: "power_unit_options",
    list: "/admin/power_unit_options/power_unit_options",
    create: "/admin/power_unit_options/power_unit_options",
    edit: "/admin/power_unit_options/power_unit_options",
    show: "/admin/power_unit_options/power_unit_options",
    meta: {
      label: "Power Unit Options",
      category: "Build"
    }

  },
  {
    name: "power_unit_speeds",
    list: "/admin/power_unit_speeds/power_unit_speeds",
    create: "/admin/power_unit_speeds/power_unit_speeds",
    edit: "/admin/power_unit_speeds/power_unit_speeds",
    show: "/admin/power_unit_speeds/power_unit_speeds",
    meta: {
      label: "Power Unit Speeds",
      category: "Build"
    }

  },
  {
    name: "power_unit_power",
    list: "/admin/power_unit_power/power_unit_power",
    create: "/admin/power_unit_power/power_unit_power",
    edit: "/admin/power_unit_power/power_unit_power",
    show: "/admin/power_unit_power/power_unit_power",
    meta: {
      label: "Power Unit Power",
      category: "Build"
    }

  },
  {
    name: "power_unit_voltage",
    list: "/admin/power_unit_voltage/power_unit_voltage",
    create: "/admin/power_unit_voltage/power_unit_voltage",
    edit: "/admin/power_unit_voltage/power_unit_voltage",
    show: "/admin/power_unit_voltage/power_unit_voltage",
    meta: {
      label: "Power Unit Voltage",
      category: "Build"
    }

  },
];

export default buildResources;
