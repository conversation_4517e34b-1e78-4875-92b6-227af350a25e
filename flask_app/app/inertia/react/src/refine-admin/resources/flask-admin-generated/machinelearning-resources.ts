// Auto-generated refine admin resources for Machine Learning category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const machinelearningResources: ResourceProps[] = [
  {
    name: "compression_images",
    list: "/admin/compression_images/compression_images",
    create: "/admin/compression_images/compression_images",
    edit: "/admin/compression_images/compression_images",
    show: "/admin/compression_images/compression_images",
    meta: {
      label: "Compression Card Image Classification",
      category: "Machine Learning"
    }
,
    listFields: [
      {
            "key": "image",
            "title": "Image",
            "dataIndex": "image",
            "type": "text",
            "sortable": true
      },
      {
            "key": "description_rel.description",
            "title": "Description Rel.Description",
            "dataIndex": "description_rel.description",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "descriptionRel",
            "title": "Description Rel",
            "dataIndex": "description_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "patternId",
            "title": "Pattern Id",
            "dataIndex": "pattern_id",
            "type": "number",
            "sortable": true
      },
      {
            "key": "numInCluster",
            "title": "Num In Cluster",
            "dataIndex": "num_in_cluster",
            "type": "text",
            "sortable": true
      },
      {
            "key": "description_rel.send_alert",
            "title": "Description Rel.Send Alert",
            "dataIndex": "description_rel.send_alert",
            "type": "select",
            "sortable": true
      },
      {
            "key": "description_rel.solution",
            "title": "Description Rel.Solution",
            "dataIndex": "description_rel.solution",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description_rel.explanation",
            "title": "Description Rel.Explanation",
            "dataIndex": "description_rel.explanation",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "mlVersion",
            "title": "Ml Version",
            "dataIndex": "ml_version",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "clusterId",
            "title": "Cluster Id",
            "dataIndex": "cluster_id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    formFields: [
      {
            "key": "image",
            "title": "Image",
            "dataIndex": "image",
            "type": "text",
            "required": true
      },
      {
            "key": "mlVersion",
            "title": "Ml Version",
            "dataIndex": "ml_version",
            "type": "text",
            "required": true
      },
      {
            "key": "clusterId",
            "title": "Cluster Id",
            "dataIndex": "cluster_id",
            "type": "number",
            "required": true
      },
      {
            "key": "descriptionRel",
            "title": "Description Rel",
            "dataIndex": "description_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "solution",
            "title": "Solution",
            "dataIndex": "solution",
            "type": "text",
            "required": true
      },
      {
            "key": "explanation",
            "title": "Explanation",
            "dataIndex": "explanation",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "surface_images",
    list: "/admin/surface_images/surface_images",
    create: "/admin/surface_images/surface_images",
    edit: "/admin/surface_images/surface_images",
    show: "/admin/surface_images/surface_images",
    meta: {
      label: "Surface Card Image Classification",
      category: "Machine Learning"
    }

  },
  {
    name: "compression_patterns",
    list: "/admin/compression_patterns/compression_patterns",
    create: "/admin/compression_patterns/compression_patterns",
    edit: "/admin/compression_patterns/compression_patterns",
    show: "/admin/compression_patterns/compression_patterns",
    meta: {
      label: "Compression Card Patterns",
      category: "Machine Learning"
    }
,
    listFields: [
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "sendAlert",
            "title": "Send Alert",
            "dataIndex": "send_alert",
            "type": "text"
      },
      {
            "key": "solution",
            "title": "Solution",
            "dataIndex": "solution",
            "type": "text",
            "searchable": true
      },
      {
            "key": "explanation",
            "title": "Explanation",
            "dataIndex": "explanation",
            "type": "text",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "sendAlert",
            "title": "Send Alert",
            "dataIndex": "send_alert",
            "type": "text",
            "required": true
      },
      {
            "key": "solution",
            "title": "Solution",
            "dataIndex": "solution",
            "type": "text",
            "required": true
      },
      {
            "key": "explanation",
            "title": "Explanation",
            "dataIndex": "explanation",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "surface_patterns",
    list: "/admin/surface_patterns/surface_patterns",
    create: "/admin/surface_patterns/surface_patterns",
    edit: "/admin/surface_patterns/surface_patterns",
    show: "/admin/surface_patterns/surface_patterns",
    meta: {
      label: "Surface Card Patterns",
      category: "Machine Learning"
    }
,
    listFields: [
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "sendAlert",
            "title": "Send Alert",
            "dataIndex": "send_alert",
            "type": "text"
      },
      {
            "key": "solution",
            "title": "Solution",
            "dataIndex": "solution",
            "type": "text",
            "searchable": true
      },
      {
            "key": "explanation",
            "title": "Explanation",
            "dataIndex": "explanation",
            "type": "text",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "sendAlert",
            "title": "Send Alert",
            "dataIndex": "send_alert",
            "type": "text",
            "required": true
      },
      {
            "key": "solution",
            "title": "Solution",
            "dataIndex": "solution",
            "type": "text",
            "required": true
      },
      {
            "key": "explanation",
            "title": "Explanation",
            "dataIndex": "explanation",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
];

export default machinelearningResources;
