// Auto-generated refine admin resources for BoM category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const bomResources: ResourceProps[] = [
  {
    name: "unit_types",
    list: "/admin/unit_types/unit_types",
    create: "/admin/unit_types/unit_types",
    edit: "/admin/unit_types/unit_types",
    show: "/admin/unit_types/unit_types",
    meta: {
      label: "Unit Types - Very High Level for RCOM/Alerts",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "unitType",
            "title": "Unit Type",
            "dataIndex": "unit_type",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "models_rel.model",
            "title": "Models Rel.Model",
            "dataIndex": "models_rel.model",
            "type": "select"
      },
      {
            "key": "structures_rel.structure",
            "title": "Structures Rel.Structure",
            "dataIndex": "structures_rel.structure",
            "type": "select"
      },
      {
            "key": "canShowToCustomers",
            "title": "Can Show To Customers",
            "dataIndex": "can_show_to_customers",
            "type": "boolean"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "unitType",
            "title": "Unit Type",
            "dataIndex": "unit_type",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "canShowToCustomers",
            "title": "Can Show To Customers",
            "dataIndex": "can_show_to_customers",
            "type": "boolean",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "structure_types",
    list: "/admin/structure_types/structure_types",
    create: "/admin/structure_types/structure_types",
    edit: "/admin/structure_types/structure_types",
    show: "/admin/structure_types/structure_types",
    meta: {
      label: "Structure Model Types - Finished Goods Mapping",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select"
      },
      {
            "key": "model",
            "title": "Model",
            "dataIndex": "model",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "maxDeltaP",
            "title": "Max Delta P",
            "dataIndex": "max_delta_p",
            "type": "text"
      },
      {
            "key": "color",
            "title": "Color",
            "dataIndex": "color",
            "type": "text"
      },
      {
            "key": "bom_structure_rel.name",
            "title": "Bom Structure Rel.Name",
            "dataIndex": "bom_structure_rel.name",
            "type": "select"
      },
      {
            "key": "bom_pump_top_rel.name",
            "title": "Bom Pump Top Rel.Name",
            "dataIndex": "bom_pump_top_rel.name",
            "type": "select"
      },
      {
            "key": "bom_dgas_rel.name",
            "title": "Bom Dgas Rel.Name",
            "dataIndex": "bom_dgas_rel.name",
            "type": "select"
      },
      {
            "key": "parts_rel.part_num",
            "title": "Parts Rel.Part Num",
            "dataIndex": "parts_rel.part_num",
            "type": "select"
      },
      {
            "key": "pm_seal_kits_rel.part_num",
            "title": "Pm Seal Kits Rel.Part Num",
            "dataIndex": "pm_seal_kits_rel.part_num",
            "type": "select"
      },
      {
            "key": "unit_types_rel.unit_type",
            "title": "Unit Types Rel.Unit Type",
            "dataIndex": "unit_types_rel.unit_type",
            "type": "select"
      },
      {
            "key": "structures_rel.structure",
            "title": "Structures Rel.Structure",
            "dataIndex": "structures_rel.structure",
            "type": "select"
      },
      {
            "key": "canShowToCustomers",
            "title": "Can Show To Customers",
            "dataIndex": "can_show_to_customers",
            "type": "boolean"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "model",
            "title": "Model",
            "dataIndex": "model",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "maxDeltaP",
            "title": "Max Delta P",
            "dataIndex": "max_delta_p",
            "type": "text",
            "required": true
      },
      {
            "key": "color",
            "title": "Color",
            "dataIndex": "color",
            "type": "text",
            "required": true
      },
      {
            "key": "bomStructureRel",
            "title": "Bom Structure Rel",
            "dataIndex": "bom_structure_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "bomPumpTopRel",
            "title": "Bom Pump Top Rel",
            "dataIndex": "bom_pump_top_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "bomDgasRel",
            "title": "Bom Dgas Rel",
            "dataIndex": "bom_dgas_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "partsRel",
            "title": "Parts Rel",
            "dataIndex": "parts_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "pmSealKitsRel",
            "title": "Pm Seal Kits Rel",
            "dataIndex": "pm_seal_kits_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "unitTypesRel",
            "title": "Unit Types Rel",
            "dataIndex": "unit_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "canShowToCustomers",
            "title": "Can Show To Customers",
            "dataIndex": "can_show_to_customers",
            "type": "boolean",
            "required": true
      }
]
  },
  {
    name: "power_unit_types",
    list: "/admin/power_unit_types/power_unit_types",
    create: "/admin/power_unit_types/power_unit_types",
    edit: "/admin/power_unit_types/power_unit_types",
    show: "/admin/power_unit_types/power_unit_types",
    meta: {
      label: "Power Unit Model Types - Finished Goods Mapping",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "bom_base_powerunit_rel.name",
            "title": "Bom Base Powerunit Rel.Name",
            "dataIndex": "bom_base_powerunit_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "bom_powerunit_rel.name",
            "title": "Bom Powerunit Rel.Name",
            "dataIndex": "bom_powerunit_rel.name",
            "type": "select"
      },
      {
            "key": "parts_rel.part_num",
            "title": "Parts Rel.Part Num",
            "dataIndex": "parts_rel.part_num",
            "type": "select"
      },
      {
            "key": "filters_rel.part_num",
            "title": "Filters Rel.Part Num",
            "dataIndex": "filters_rel.part_num",
            "type": "select"
      },
      {
            "key": "power_units_rel.power_unit_str",
            "title": "Power Units Rel.Power Unit Str",
            "dataIndex": "power_units_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "bomBasePowerunitRel",
            "title": "Bom Base Powerunit Rel",
            "dataIndex": "bom_base_powerunit_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "bomPowerunitRel",
            "title": "Bom Powerunit Rel",
            "dataIndex": "bom_powerunit_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "partsRel",
            "title": "Parts Rel",
            "dataIndex": "parts_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "filtersRel",
            "title": "Filters Rel",
            "dataIndex": "filters_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "powerUnitsRel",
            "title": "Power Units Rel",
            "dataIndex": "power_units_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "bom_pricing",
    list: "/admin/bom_pricing/bom_pricing",
    create: "/admin/bom_pricing/bom_pricing",
    edit: "/admin/bom_pricing/bom_pricing",
    show: "/admin/bom_pricing/bom_pricing",
    meta: {
      label: "BoM Pricing - Parts Mapping",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "bomPricingRel",
            "title": "Bom Pricing Rel",
            "dataIndex": "bom_pricing_rel",
            "type": "select"
      },
      {
            "key": "bomPricingPartsRel",
            "title": "Bom Pricing Parts Rel",
            "dataIndex": "bom_pricing_parts_rel",
            "type": "select"
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "required": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "bom_pricing_parts",
    list: "/admin/bom_pricing_parts/bom_pricing_parts",
    create: "/admin/bom_pricing_parts/bom_pricing_parts",
    edit: "/admin/bom_pricing_parts/bom_pricing_parts",
    show: "/admin/bom_pricing_parts/bom_pricing_parts",
    meta: {
      label: "BoM Pricing - Parts Quantity",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "finished_good_rel.name",
            "title": "Finished Good Rel.Name",
            "dataIndex": "finished_good_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "part_rel.part_num",
            "title": "Part Rel.Part Num",
            "dataIndex": "part_rel.part_num",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    formFields: [
      {
            "key": "finishedGoodRel",
            "title": "Finished Good Rel",
            "dataIndex": "finished_good_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "bom_base_powerunit",
    list: "/admin/bom_base_powerunit/bom_base_powerunit",
    create: "/admin/bom_base_powerunit/bom_base_powerunit",
    edit: "/admin/bom_base_powerunit/bom_base_powerunit",
    show: "/admin/bom_base_powerunit/bom_base_powerunit",
    meta: {
      label: "BoM Base Powerunit - Parts Mapping",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number"
      },
      {
            "key": "bomBasePowerunitPartRel",
            "title": "Bom Base Powerunit Part Rel",
            "dataIndex": "bom_base_powerunit_part_rel",
            "type": "select"
      },
      {
            "key": "powerUnitTypeRel",
            "title": "Power Unit Type Rel",
            "dataIndex": "power_unit_type_rel",
            "type": "select"
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "required": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "bom_powerunit",
    list: "/admin/bom_powerunit/bom_powerunit",
    create: "/admin/bom_powerunit/bom_powerunit",
    edit: "/admin/bom_powerunit/bom_powerunit",
    show: "/admin/bom_powerunit/bom_powerunit",
    meta: {
      label: "BoM Powerunit - Parts Mapping",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number"
      },
      {
            "key": "bomPowerunitPartRel",
            "title": "Bom Powerunit Part Rel",
            "dataIndex": "bom_powerunit_part_rel",
            "type": "select"
      },
      {
            "key": "powerUnitTypeRel",
            "title": "Power Unit Type Rel",
            "dataIndex": "power_unit_type_rel",
            "type": "select"
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "required": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "bom_structure",
    list: "/admin/bom_structure/bom_structure",
    create: "/admin/bom_structure/bom_structure",
    edit: "/admin/bom_structure/bom_structure",
    show: "/admin/bom_structure/bom_structure",
    meta: {
      label: "BoM Structure - Parts Mapping",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number"
      },
      {
            "key": "bomStructureRel",
            "title": "Bom Structure Rel",
            "dataIndex": "bom_structure_rel",
            "type": "select"
      },
      {
            "key": "modelTypeRel",
            "title": "Model Type Rel",
            "dataIndex": "model_type_rel",
            "type": "select"
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "required": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "bom_pump_top",
    list: "/admin/bom_pump_top/bom_pump_top",
    create: "/admin/bom_pump_top/bom_pump_top",
    edit: "/admin/bom_pump_top/bom_pump_top",
    show: "/admin/bom_pump_top/bom_pump_top",
    meta: {
      label: "BoM Pump Top - Parts Mapping",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number"
      },
      {
            "key": "bomPumpTopPartRel",
            "title": "Bom Pump Top Part Rel",
            "dataIndex": "bom_pump_top_part_rel",
            "type": "select"
      },
      {
            "key": "modelTypeRel",
            "title": "Model Type Rel",
            "dataIndex": "model_type_rel",
            "type": "select"
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "required": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "bom_dgas",
    list: "/admin/bom_dgas/bom_dgas",
    create: "/admin/bom_dgas/bom_dgas",
    edit: "/admin/bom_dgas/bom_dgas",
    show: "/admin/bom_dgas/bom_dgas",
    meta: {
      label: "BoM DGAS - Parts Mapping",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number"
      },
      {
            "key": "bomDgasPartRel",
            "title": "Bom Dgas Part Rel",
            "dataIndex": "bom_dgas_part_rel",
            "type": "select"
      },
      {
            "key": "modelTypeRel",
            "title": "Model Type Rel",
            "dataIndex": "model_type_rel",
            "type": "select"
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "required": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "parts",
    list: "/admin/parts/parts",
    create: "/admin/parts/parts",
    edit: "/admin/parts/parts",
    show: "/admin/parts/parts",
    meta: {
      label: "BoM Parts - Every Part that Finished Goods Need",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "worksheet",
            "title": "Worksheet",
            "dataIndex": "worksheet",
            "type": "text",
            "searchable": true
      },
      {
            "key": "wsRow",
            "title": "Ws Row",
            "dataIndex": "ws_row",
            "type": "text"
      },
      {
            "key": "partNum",
            "title": "Part Num",
            "dataIndex": "part_num",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "partImage",
            "title": "Part Image",
            "dataIndex": "part_image",
            "type": "text"
      },
      {
            "key": "partImageRel",
            "title": "Part Image Rel",
            "dataIndex": "part_image_rel",
            "type": "select"
      },
      {
            "key": "msrpMultCad",
            "title": "Msrp Mult Cad",
            "dataIndex": "msrp_mult_cad",
            "type": "text"
      },
      {
            "key": "transferMultCadDealer",
            "title": "Transfer Mult Cad Dealer",
            "dataIndex": "transfer_mult_cad_dealer",
            "type": "text"
      },
      {
            "key": "msrpMultUsd",
            "title": "Msrp Mult Usd",
            "dataIndex": "msrp_mult_usd",
            "type": "text"
      },
      {
            "key": "transferMultIncToCorp",
            "title": "Transfer Mult Inc To Corp",
            "dataIndex": "transfer_mult_inc_to_corp",
            "type": "text"
      },
      {
            "key": "transferMultUsdDealer",
            "title": "Transfer Mult Usd Dealer",
            "dataIndex": "transfer_mult_usd_dealer",
            "type": "text"
      },
      {
            "key": "warehouseMult",
            "title": "Warehouse Mult",
            "dataIndex": "warehouse_mult",
            "type": "text"
      },
      {
            "key": "costCad",
            "title": "Cost Cad",
            "dataIndex": "cost_cad",
            "type": "number"
      },
      {
            "key": "msrpCad",
            "title": "Msrp Cad",
            "dataIndex": "msrp_cad",
            "type": "text"
      },
      {
            "key": "dealerCostCad",
            "title": "Dealer Cost Cad",
            "dataIndex": "dealer_cost_cad",
            "type": "number"
      },
      {
            "key": "costUsd",
            "title": "Cost Usd",
            "dataIndex": "cost_usd",
            "type": "number"
      },
      {
            "key": "msrpUsd",
            "title": "Msrp Usd",
            "dataIndex": "msrp_usd",
            "type": "text"
      },
      {
            "key": "dealerCostUsd",
            "title": "Dealer Cost Usd",
            "dataIndex": "dealer_cost_usd",
            "type": "number"
      },
      {
            "key": "isUsd",
            "title": "Is Usd",
            "dataIndex": "is_usd",
            "type": "boolean"
      },
      {
            "key": "cadPerUsd",
            "title": "Cad Per Usd",
            "dataIndex": "cad_per_usd",
            "type": "text"
      },
      {
            "key": "isSoftPart",
            "title": "Is Soft Part",
            "dataIndex": "is_soft_part",
            "type": "boolean"
      },
      {
            "key": "isHardPart",
            "title": "Is Hard Part",
            "dataIndex": "is_hard_part",
            "type": "boolean"
      },
      {
            "key": "weight",
            "title": "Weight",
            "dataIndex": "weight",
            "type": "text"
      },
      {
            "key": "harmonizationCode",
            "title": "Harmonization Code",
            "dataIndex": "harmonization_code",
            "type": "text",
            "searchable": true
      },
      {
            "key": "countryOfOrigin",
            "title": "Country Of Origin",
            "dataIndex": "country_of_origin",
            "type": "number",
            "searchable": true
      },
      {
            "key": "noDelete",
            "title": "No Delete",
            "dataIndex": "no_delete",
            "type": "text"
      },
      {
            "key": "flaggedForDeletion",
            "title": "Flagged For Deletion",
            "dataIndex": "flagged_for_deletion",
            "type": "text"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true
      }
],
    formFields: [
      {
            "key": "partNum",
            "title": "Part Num",
            "dataIndex": "part_num",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "partImage",
            "title": "Part Image",
            "dataIndex": "part_image",
            "type": "text",
            "required": true
      },
      {
            "key": "partImageRel",
            "title": "Part Image Rel",
            "dataIndex": "part_image_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "noDelete",
            "title": "No Delete",
            "dataIndex": "no_delete",
            "type": "text",
            "required": true
      },
      {
            "key": "flaggedForDeletion",
            "title": "Flagged For Deletion",
            "dataIndex": "flagged_for_deletion",
            "type": "text",
            "required": true
      },
      {
            "key": "worksheet",
            "title": "Worksheet",
            "dataIndex": "worksheet",
            "type": "text",
            "required": true
      },
      {
            "key": "wsRow",
            "title": "Ws Row",
            "dataIndex": "ws_row",
            "type": "text",
            "required": true
      },
      {
            "key": "costCad",
            "title": "Cost Cad",
            "dataIndex": "cost_cad",
            "type": "number",
            "required": true
      },
      {
            "key": "costUsd",
            "title": "Cost Usd",
            "dataIndex": "cost_usd",
            "type": "number",
            "required": true
      },
      {
            "key": "msrpMultCad",
            "title": "Msrp Mult Cad",
            "dataIndex": "msrp_mult_cad",
            "type": "text",
            "required": true
      },
      {
            "key": "msrpMultUsd",
            "title": "Msrp Mult Usd",
            "dataIndex": "msrp_mult_usd",
            "type": "text",
            "required": true
      },
      {
            "key": "msrpCad",
            "title": "Msrp Cad",
            "dataIndex": "msrp_cad",
            "type": "text",
            "required": true
      },
      {
            "key": "msrpUsd",
            "title": "Msrp Usd",
            "dataIndex": "msrp_usd",
            "type": "text",
            "required": true
      },
      {
            "key": "transferMultCadDealer",
            "title": "Transfer Mult Cad Dealer",
            "dataIndex": "transfer_mult_cad_dealer",
            "type": "text",
            "required": true
      },
      {
            "key": "transferMultUsdDealer",
            "title": "Transfer Mult Usd Dealer",
            "dataIndex": "transfer_mult_usd_dealer",
            "type": "text",
            "required": true
      },
      {
            "key": "transferMultIncToCorp",
            "title": "Transfer Mult Inc To Corp",
            "dataIndex": "transfer_mult_inc_to_corp",
            "type": "text",
            "required": true
      },
      {
            "key": "warehouseMult",
            "title": "Warehouse Mult",
            "dataIndex": "warehouse_mult",
            "type": "text",
            "required": true
      },
      {
            "key": "dealerCostCad",
            "title": "Dealer Cost Cad",
            "dataIndex": "dealer_cost_cad",
            "type": "number",
            "required": true
      },
      {
            "key": "dealerCostUsd",
            "title": "Dealer Cost Usd",
            "dataIndex": "dealer_cost_usd",
            "type": "number",
            "required": true
      },
      {
            "key": "isUsd",
            "title": "Is Usd",
            "dataIndex": "is_usd",
            "type": "boolean",
            "required": true
      },
      {
            "key": "cadPerUsd",
            "title": "Cad Per Usd",
            "dataIndex": "cad_per_usd",
            "type": "text",
            "required": true
      },
      {
            "key": "isSoftPart",
            "title": "Is Soft Part",
            "dataIndex": "is_soft_part",
            "type": "boolean",
            "required": true
      },
      {
            "key": "isHardPart",
            "title": "Is Hard Part",
            "dataIndex": "is_hard_part",
            "type": "boolean",
            "required": true
      },
      {
            "key": "weight",
            "title": "Weight",
            "dataIndex": "weight",
            "type": "text",
            "required": true
      },
      {
            "key": "harmonizationCode",
            "title": "Harmonization Code",
            "dataIndex": "harmonization_code",
            "type": "text",
            "required": true
      },
      {
            "key": "countryOfOrigin",
            "title": "Country Of Origin",
            "dataIndex": "country_of_origin",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "export": true
}
  },
  {
    name: "filters",
    list: "/admin/filters/filters",
    create: "/admin/filters/filters",
    edit: "/admin/filters/filters",
    show: "/admin/filters/filters",
    meta: {
      label: "Filters",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "partNum",
            "title": "Part Num",
            "dataIndex": "part_num",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
]
  },
  {
    name: "rod_types",
    list: "/admin/rod_types/rod_types",
    create: "/admin/rod_types/rod_types",
    edit: "/admin/rod_types/rod_types",
    show: "/admin/rod_types/rod_types",
    meta: {
      label: "Rod Types",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "barrel_types",
    list: "/admin/barrel_types/barrel_types",
    create: "/admin/barrel_types/barrel_types",
    edit: "/admin/barrel_types/barrel_types",
    show: "/admin/barrel_types/barrel_types",
    meta: {
      label: "Barrel Types",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "shuttle_valve_types",
    list: "/admin/shuttle_valve_types/shuttle_valve_types",
    create: "/admin/shuttle_valve_types/shuttle_valve_types",
    edit: "/admin/shuttle_valve_types/shuttle_valve_types",
    show: "/admin/shuttle_valve_types/shuttle_valve_types",
    meta: {
      label: "Shuttle Valve Types",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "check_valve_types",
    list: "/admin/check_valve_types/check_valve_types",
    create: "/admin/check_valve_types/check_valve_types",
    edit: "/admin/check_valve_types/check_valve_types",
    show: "/admin/check_valve_types/check_valve_types",
    meta: {
      label: "Check Valve Types",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "packing_gland_types",
    list: "/admin/packing_gland_types/packing_gland_types",
    create: "/admin/packing_gland_types/packing_gland_types",
    edit: "/admin/packing_gland_types/packing_gland_types",
    show: "/admin/packing_gland_types/packing_gland_types",
    meta: {
      label: "Packing Gland Types",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "hydraulic_piston_types",
    list: "/admin/hydraulic_piston_types/hydraulic_piston_types",
    create: "/admin/hydraulic_piston_types/hydraulic_piston_types",
    edit: "/admin/hydraulic_piston_types/hydraulic_piston_types",
    show: "/admin/hydraulic_piston_types/hydraulic_piston_types",
    meta: {
      label: "Hydraulic Piston Types",
      category: "BoM"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
];

export default bomResources;
