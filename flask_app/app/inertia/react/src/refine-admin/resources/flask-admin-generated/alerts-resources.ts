// Auto-generated refine admin resources for Alerts category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const alertsResources: ResourceProps[] = [
  {
    name: "alerts",
    list: "/admin/alerts/alerts",
    create: "/admin/alerts/alerts",
    edit: "/admin/alerts/alerts",
    show: "/admin/alerts/alerts",
    meta: {
      label: "Alerts",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerUnitsRel",
            "title": "Power Units Rel",
            "dataIndex": "power_units_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "users_rel.first_name",
            "title": "Users Rel.First Name",
            "dataIndex": "users_rel.first_name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "users_rel.last_name",
            "title": "Users Rel.Last Name",
            "dataIndex": "users_rel.last_name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "customers_rel.customer",
            "title": "Customers Rel.Customer",
            "dataIndex": "customers_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "users_rel.phone",
            "title": "Users Rel.Phone",
            "dataIndex": "users_rel.phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "wantsSms",
            "title": "Wants Sms",
            "dataIndex": "wants_sms",
            "type": "text",
            "sortable": true
      },
      {
            "key": "users_rel.sms_stop_all",
            "title": "Users Rel.Sms Stop All",
            "dataIndex": "users_rel.sms_stop_all",
            "type": "select",
            "sortable": true
      },
      {
            "key": "wantsEmail",
            "title": "Wants Email",
            "dataIndex": "wants_email",
            "type": "email",
            "sortable": true
      },
      {
            "key": "wantsPhone",
            "title": "Wants Phone",
            "dataIndex": "wants_phone",
            "type": "text",
            "sortable": true
      },
      {
            "key": "wantsWhatsapp",
            "title": "Wants Whatsapp",
            "dataIndex": "wants_whatsapp",
            "type": "text",
            "sortable": true
      },
      {
            "key": "users_rel.whatsapp_stop_all",
            "title": "Users Rel.Whatsapp Stop All",
            "dataIndex": "users_rel.whatsapp_stop_all",
            "type": "select",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "firstName",
            "title": "First Name",
            "dataIndex": "first_name",
            "type": "text",
            "required": true
      },
      {
            "key": "lastName",
            "title": "Last Name",
            "dataIndex": "last_name",
            "type": "text",
            "required": true
      },
      {
            "key": "email",
            "title": "Email",
            "dataIndex": "email",
            "type": "email",
            "required": true
      },
      {
            "key": "formPassword",
            "title": "Form Password",
            "dataIndex": "form_password",
            "type": "text",
            "required": true
      },
      {
            "key": "formPasswordVerify",
            "title": "Form Password Verify",
            "dataIndex": "form_password_verify",
            "type": "text",
            "required": true
      },
      {
            "key": "mainCustomerRel",
            "title": "Main Customer Rel",
            "dataIndex": "main_customer_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "jobTitle",
            "title": "Job Title",
            "dataIndex": "job_title",
            "type": "text",
            "required": true
      },
      {
            "key": "isActive",
            "title": "Is Active",
            "dataIndex": "is_active",
            "type": "boolean",
            "required": true
      },
      {
            "key": "dateCreated",
            "title": "Date Created",
            "dataIndex": "date_created",
            "type": "datetime",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "maintenanceStructuresRel",
            "title": "Maintenance Structures Rel",
            "dataIndex": "maintenance_structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "salesStructuresRel",
            "title": "Sales Structures Rel",
            "dataIndex": "sales_structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "custSubGroupsNotifyServiceRequestsRel",
            "title": "Cust Sub Groups Notify Service Requests Rel",
            "dataIndex": "cust_sub_groups_notify_service_requests_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "notifyServiceRequests",
            "title": "Notify Service Requests",
            "dataIndex": "notify_service_requests",
            "type": "text",
            "required": true
      },
      {
            "key": "rolesRel",
            "title": "Roles Rel",
            "dataIndex": "roles_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "phone",
            "title": "Phone",
            "dataIndex": "phone",
            "type": "text",
            "required": true
      },
      {
            "key": "countriesRel",
            "title": "Countries Rel",
            "dataIndex": "countries_rel",
            "type": "number",
            "required": true
      },
      {
            "key": "isUsPhone",
            "title": "Is Us Phone",
            "dataIndex": "is_us_phone",
            "type": "text",
            "required": true
      },
      {
            "key": "timeZonesRel",
            "title": "Time Zones Rel",
            "dataIndex": "time_zones_rel",
            "type": "datetime",
            "required": true
      },
      {
            "key": "userLat",
            "title": "User Lat",
            "dataIndex": "user_lat",
            "type": "text",
            "required": true
      },
      {
            "key": "userLon",
            "title": "User Lon",
            "dataIndex": "user_lon",
            "type": "text",
            "required": true
      },
      {
            "key": "isConfirmed",
            "title": "Is Confirmed",
            "dataIndex": "is_confirmed",
            "type": "boolean",
            "required": true
      },
      {
            "key": "smsStopAll",
            "title": "Sms Stop All",
            "dataIndex": "sms_stop_all",
            "type": "text",
            "required": true
      },
      {
            "key": "smsStoppedAtUtc",
            "title": "Sms Stopped At Utc",
            "dataIndex": "sms_stopped_at_utc",
            "type": "text",
            "required": true
      },
      {
            "key": "emlUnsubscribeAll",
            "title": "Eml Unsubscribe All",
            "dataIndex": "eml_unsubscribe_all",
            "type": "text",
            "required": true
      },
      {
            "key": "emlMarketing",
            "title": "Eml Marketing",
            "dataIndex": "eml_marketing",
            "type": "text",
            "required": true
      },
      {
            "key": "emlNewProducts",
            "title": "Eml New Products",
            "dataIndex": "eml_new_products",
            "type": "text",
            "required": true
      },
      {
            "key": "emlService",
            "title": "Eml Service",
            "dataIndex": "eml_service",
            "type": "text",
            "required": true
      },
      {
            "key": "emlRcom",
            "title": "Eml Rcom",
            "dataIndex": "eml_rcom",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "alerts_sent",
    list: "/admin/alerts_sent/alerts_sent",
    create: "/admin/alerts_sent/alerts_sent",
    edit: "/admin/alerts_sent/alerts_sent",
    show: "/admin/alerts_sent/alerts_sent",
    meta: {
      label: "Alerts Sent",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "powerUnit",
            "title": "Power Unit",
            "dataIndex": "power_unit",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "warningMsg",
            "title": "Warning Msg",
            "dataIndex": "warning_msg",
            "type": "text",
            "searchable": true
      },
      {
            "key": "explanation",
            "title": "Explanation",
            "dataIndex": "explanation",
            "type": "text"
      },
      {
            "key": "program",
            "title": "Program",
            "dataIndex": "program",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "function",
            "title": "Function",
            "dataIndex": "function",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "alertsSentUsersRel",
            "title": "Alerts Sent Users Rel",
            "dataIndex": "alerts_sent_users_rel",
            "type": "select",
            "searchable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "alerts_custom",
    list: "/admin/alerts_custom/alerts_custom",
    create: "/admin/alerts_custom/alerts_custom",
    edit: "/admin/alerts_custom/alerts_custom",
    show: "/admin/alerts_custom/alerts_custom",
    meta: {
      label: "Alerts - Custom",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "monthsRel",
            "title": "Months Rel",
            "dataIndex": "months_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "daysRel",
            "title": "Days Rel",
            "dataIndex": "days_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "hourEndRel",
            "title": "Hour End Rel",
            "dataIndex": "hour_end_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "timeZonesRel",
            "title": "Time Zones Rel",
            "dataIndex": "time_zones_rel",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "wantEmail",
            "title": "Want Email",
            "dataIndex": "want_email",
            "type": "email",
            "sortable": true
      },
      {
            "key": "wantSms",
            "title": "Want Sms",
            "dataIndex": "want_sms",
            "type": "text",
            "sortable": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "subject",
            "title": "Subject",
            "dataIndex": "subject",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "body",
            "title": "Body",
            "dataIndex": "body",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "imagesRel",
            "title": "Images Rel",
            "dataIndex": "images_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "subject",
            "title": "Subject",
            "dataIndex": "subject",
            "type": "text",
            "required": true
      },
      {
            "key": "body",
            "title": "Body",
            "dataIndex": "body",
            "type": "text",
            "required": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "monthsRel",
            "title": "Months Rel",
            "dataIndex": "months_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "daysRel",
            "title": "Days Rel",
            "dataIndex": "days_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "hourEndRel",
            "title": "Hour End Rel",
            "dataIndex": "hour_end_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "timeZonesRel",
            "title": "Time Zones Rel",
            "dataIndex": "time_zones_rel",
            "type": "datetime",
            "required": true
      },
      {
            "key": "wantEmail",
            "title": "Want Email",
            "dataIndex": "want_email",
            "type": "email",
            "required": true
      },
      {
            "key": "wantSms",
            "title": "Want Sms",
            "dataIndex": "want_sms",
            "type": "text",
            "required": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "imagesRel",
            "title": "Images Rel",
            "dataIndex": "images_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "hourly_update_report",
    list: "/admin/hourly_update_report/hourly_update_report",
    create: "/admin/hourly_update_report/hourly_update_report",
    edit: "/admin/hourly_update_report/hourly_update_report",
    show: "/admin/hourly_update_report/hourly_update_report",
    meta: {
      label: "Hourly Update Email",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "customers_rel.customer",
            "title": "Customers Rel.Customer",
            "dataIndex": "customers_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "hoursRel",
            "title": "Hours Rel",
            "dataIndex": "hours_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "daysOfWeekRel",
            "title": "Days Of Week Rel",
            "dataIndex": "days_of_week_rel",
            "type": "select"
      },
      {
            "key": "unitTypesRel",
            "title": "Unit Types Rel",
            "dataIndex": "unit_types_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "warehousesRel",
            "title": "Warehouses Rel",
            "dataIndex": "warehouses_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "maxDistanceKm",
            "title": "Max Distance Km",
            "dataIndex": "max_distance_km",
            "type": "text",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    formFields: [
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text",
            "required": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "hoursRel",
            "title": "Hours Rel",
            "dataIndex": "hours_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "daysOfWeekRel",
            "title": "Days Of Week Rel",
            "dataIndex": "days_of_week_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "unitTypesRel",
            "title": "Unit Types Rel",
            "dataIndex": "unit_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "warehousesRel",
            "title": "Warehouses Rel",
            "dataIndex": "warehouses_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "maxDistanceKm",
            "title": "Max Distance Km",
            "dataIndex": "max_distance_km",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "derates_report",
    list: "/admin/derates_report/derates_report",
    create: "/admin/derates_report/derates_report",
    edit: "/admin/derates_report/derates_report",
    show: "/admin/derates_report/derates_report",
    meta: {
      label: "Derates Email",
      category: "Alerts"
    }

  },
  {
    name: "op_hours_report",
    list: "/admin/op_hours_report/op_hours_report",
    create: "/admin/op_hours_report/op_hours_report",
    edit: "/admin/op_hours_report/op_hours_report",
    show: "/admin/op_hours_report/op_hours_report",
    meta: {
      label: "Operating Hours Email (100, 1000, etc)",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "customers_rel.customer",
            "title": "Customers Rel.Customer",
            "dataIndex": "customers_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "typesRel",
            "title": "Types Rel",
            "dataIndex": "types_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "unitTypesRel",
            "title": "Unit Types Rel",
            "dataIndex": "unit_types_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "warehousesRel",
            "title": "Warehouses Rel",
            "dataIndex": "warehouses_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "maxDistanceKm",
            "title": "Max Distance Km",
            "dataIndex": "max_distance_km",
            "type": "text",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    formFields: [
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text",
            "required": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "typesRel",
            "title": "Types Rel",
            "dataIndex": "types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "unitTypesRel",
            "title": "Unit Types Rel",
            "dataIndex": "unit_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "warehousesRel",
            "title": "Warehouses Rel",
            "dataIndex": "warehouses_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "maxDistanceKm",
            "title": "Max Distance Km",
            "dataIndex": "max_distance_km",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "inventory_report",
    list: "/admin/inventory_report/inventory_report",
    create: "/admin/inventory_report/inventory_report",
    edit: "/admin/inventory_report/inventory_report",
    show: "/admin/inventory_report/inventory_report",
    meta: {
      label: "Inventory Email",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "warehousesRel",
            "title": "Warehouses Rel",
            "dataIndex": "warehouses_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "daysOfWeekRel",
            "title": "Days Of Week Rel",
            "dataIndex": "days_of_week_rel",
            "type": "select"
      },
      {
            "key": "hoursRel",
            "title": "Hours Rel",
            "dataIndex": "hours_rel",
            "type": "select"
      },
      {
            "key": "maxDistanceKm",
            "title": "Max Distance Km",
            "dataIndex": "max_distance_km",
            "type": "text",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    formFields: [
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "warehousesRel",
            "title": "Warehouses Rel",
            "dataIndex": "warehouses_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "daysOfWeekRel",
            "title": "Days Of Week Rel",
            "dataIndex": "days_of_week_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "hoursRel",
            "title": "Hours Rel",
            "dataIndex": "hours_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "maxDistanceKm",
            "title": "Max Distance Km",
            "dataIndex": "max_distance_km",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "alerts_sent_users",
    list: "/admin/alerts_sent_users/alerts_sent_users",
    create: "/admin/alerts_sent_users/alerts_sent_users",
    edit: "/admin/alerts_sent_users/alerts_sent_users",
    show: "/admin/alerts_sent_users/alerts_sent_users",
    meta: {
      label: "Alerts Sent - Users Alerted",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "",
            "title": "",
            "dataIndex": "",
            "type": "text",
            "sortable": true
      },
      {
            "key": "",
            "title": "",
            "dataIndex": "",
            "type": "text",
            "sortable": true
      },
      {
            "key": "",
            "title": "",
            "dataIndex": "",
            "type": "text",
            "sortable": true
      },
      {
            "key": "",
            "title": "",
            "dataIndex": "",
            "type": "text",
            "sortable": true
      },
      {
            "key": "",
            "title": "",
            "dataIndex": "",
            "type": "text",
            "sortable": true
      },
      {
            "key": "",
            "title": "",
            "dataIndex": "",
            "type": "text",
            "sortable": true
      },
      {
            "key": "alertsSentId",
            "title": "Alerts Sent Id",
            "dataIndex": "alerts_sent_id",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select"
      },
      {
            "key": "msgType",
            "title": "Msg Type",
            "dataIndex": "msg_type",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "twilioSid",
            "title": "Twilio Sid",
            "dataIndex": "twilio_sid",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "mailgunId",
            "title": "Mailgun Id",
            "dataIndex": "mailgun_id",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "remote_control",
    list: "/admin/remote_control/remote_control",
    create: "/admin/remote_control/remote_control",
    edit: "/admin/remote_control/remote_control",
    show: "/admin/remote_control/remote_control",
    meta: {
      label: "Remote Control Actions",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "power_unit_rel.power_unit_str",
            "title": "Power Unit Rel.Power Unit Str",
            "dataIndex": "power_unit_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "users_rel.email",
            "title": "Users Rel.Email",
            "dataIndex": "users_rel.email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "action",
            "title": "Action",
            "dataIndex": "action",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "metric",
            "title": "Metric",
            "dataIndex": "metric",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "valueWanted",
            "title": "Value Wanted",
            "dataIndex": "value_wanted",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "alerts_sent_maintenance",
    list: "/admin/alerts_sent_maintenance/alerts_sent_maintenance",
    create: "/admin/alerts_sent_maintenance/alerts_sent_maintenance",
    edit: "/admin/alerts_sent_maintenance/alerts_sent_maintenance",
    show: "/admin/alerts_sent_maintenance/alerts_sent_maintenance",
    meta: {
      label: "Alerts Sent - Maintenance",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      },
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime"
      },
      {
            "key": "alertsSentMaintEmailTypesRel",
            "title": "Alerts Sent Maint Email Types Rel",
            "dataIndex": "alerts_sent_maint_email_types_rel",
            "type": "email"
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "alerts_sent_maintenance_users",
    list: "/admin/alerts_sent_maintenance_users/alerts_sent_maintenance_users",
    create: "/admin/alerts_sent_maintenance_users/alerts_sent_maintenance_users",
    edit: "/admin/alerts_sent_maintenance_users/alerts_sent_maintenance_users",
    show: "/admin/alerts_sent_maintenance_users/alerts_sent_maintenance_users",
    meta: {
      label: "Alerts Sent - Maintenance - Users Alerted",
      category: "Alerts"
    }
,
    listFields: [
      {
            "key": "alertsSentMaintRel",
            "title": "Alerts Sent Maint Rel",
            "dataIndex": "alerts_sent_maint_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "alertsSentMaintRelTimestampUtcProxy",
            "title": "Alerts Sent Maint Rel Timestamp Utc Proxy",
            "dataIndex": "alerts_sent_maint_rel_timestamp_utc_proxy",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "alerts_sent_maint_rel.email_types_rel_name_proxy",
            "title": "Alerts Sent Maint Rel.Email Types Rel Name Proxy",
            "dataIndex": "alerts_sent_maint_rel.email_types_rel_name_proxy",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "alerts_sent_maint_rel.customers_rel_customer_proxy",
            "title": "Alerts Sent Maint Rel.Customers Rel Customer Proxy",
            "dataIndex": "alerts_sent_maint_rel.customers_rel_customer_proxy",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
];

export default alertsResources;
