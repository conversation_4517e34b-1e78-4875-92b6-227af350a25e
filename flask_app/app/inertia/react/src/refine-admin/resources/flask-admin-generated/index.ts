// Auto-generated master refine admin resources
// Generated from complete Flask admin extraction

import { ResourceProps } from "@refinedev/core";

import alertsResources from "./alerts-resources";
import bomResources from "./bom-resources";
import buildResources from "./build-resources";
import customersResources from "./customers-resources";
import inventoryResources from "./inventory-resources";
import machinelearningResources from "./machinelearning-resources";
import otherResources from "./other-resources";
import reportsResources from "./reports-resources";
import serviceResources from "./service-resources";
import unitsResources from "./units-resources";

// All resources combined
export const allFlaskAdminResources: ResourceProps[] = [
  ...alertsResources,
  ...bomResources,
  ...buildResources,
  ...customersResources,
  ...inventoryResources,
  ...machinelearningResources,
  ...otherResources,
  ...reportsResources,
  ...serviceResources,
  ...unitsResources,
];

// Export by category for menu building
export const resourcesByCategory = {
  "Alerts": alertsResources,
  "BoM": bomResources,
  "Build": buildResources,
  "Customers": customersResources,
  "Inventory": inventoryResources,
  "Machine Learning": machinelearningResources,
  "Other": otherResources,
  "Reports": reportsResources,
  "Service": serviceResources,
  "Units": unitsResources,
};

export default allFlaskAdminResources;
