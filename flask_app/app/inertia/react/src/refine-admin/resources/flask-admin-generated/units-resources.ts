// Auto-generated refine admin resources for Units category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const unitsResources: ResourceProps[] = [
  {
    name: "structures",
    list: "/admin/structures/structures",
    create: "/admin/structures/structures",
    edit: "/admin/structures/structures",
    show: "/admin/structures/structures",
    meta: {
      label: "Structures",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "structure",
            "title": "Structure",
            "dataIndex": "structure",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerUnitsRel",
            "title": "Power Units Rel",
            "dataIndex": "power_units_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "power_units_rel.power_unit_str",
            "title": "Power Units Rel.Power Unit Str",
            "dataIndex": "power_units_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "qbSale",
            "title": "Qb Sale",
            "dataIndex": "qb_sale",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "model_types_rel.model",
            "title": "Model Types Rel.Model",
            "dataIndex": "model_types_rel.model",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "unitTypesRel",
            "title": "Unit Types Rel",
            "dataIndex": "unit_types_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "unit_types_rel.unit_type",
            "title": "Unit Types Rel.Unit Type",
            "dataIndex": "unit_types_rel.unit_type",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "power_units_rel.power_unit_type_rel",
            "title": "Power Units Rel.Power Unit Type Rel.Name",
            "dataIndex": "power_units_rel.power_unit_type_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "opMonthsInterval",
            "title": "Op Months Interval",
            "dataIndex": "op_months_interval",
            "type": "text",
            "sortable": true
      },
      {
            "key": "runMfgDate",
            "title": "Run Mfg Date",
            "dataIndex": "run_mfg_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "structureInstallDate",
            "title": "Structure Install Date",
            "dataIndex": "structure_install_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "custSubGroupsRel",
            "title": "Cust Sub Groups Rel",
            "dataIndex": "cust_sub_groups_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "warehouseRel",
            "title": "Warehouse Rel",
            "dataIndex": "warehouse_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "surface",
            "title": "Surface",
            "dataIndex": "surface",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "downhole",
            "title": "Downhole",
            "dataIndex": "downhole",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "timeZonesRel",
            "title": "Time Zones Rel",
            "dataIndex": "time_zones_rel",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "gpsLat",
            "title": "Gps Lat",
            "dataIndex": "gps_lat",
            "type": "text",
            "sortable": true
      },
      {
            "key": "gpsLon",
            "title": "Gps Lon",
            "dataIndex": "gps_lon",
            "type": "text",
            "sortable": true
      },
      {
            "key": "hasRcom",
            "title": "Has Rcom",
            "dataIndex": "has_rcom",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "structureSlaveRel",
            "title": "Structure Slave Rel",
            "dataIndex": "structure_slave_rel",
            "type": "select"
      },
      {
            "key": "slaveInstallDate",
            "title": "Slave Install Date",
            "dataIndex": "slave_install_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "modelTypeSlavesRel",
            "title": "Model Type Slaves Rel",
            "dataIndex": "model_type_slaves_rel",
            "type": "select",
            "searchable": true
      },
      {
            "key": "notes1",
            "title": "Notes 1",
            "dataIndex": "notes_1",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "power_units_rel.notes",
            "title": "Power Units Rel.Notes",
            "dataIndex": "power_units_rel.notes",
            "type": "select",
            "sortable": true
      },
      {
            "key": "onGenset",
            "title": "On Genset",
            "dataIndex": "on_genset",
            "type": "text",
            "sortable": true
      },
      {
            "key": "autoGreaser",
            "title": "Auto Greaser",
            "dataIndex": "auto_greaser",
            "type": "text",
            "sortable": true
      },
      {
            "key": "rodRel",
            "title": "Rod Rel",
            "dataIndex": "rod_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "barrelRel",
            "title": "Barrel Rel",
            "dataIndex": "barrel_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "shuttleValveRel",
            "title": "Shuttle Valve Rel",
            "dataIndex": "shuttle_valve_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "checkValveRel",
            "title": "Check Valve Rel",
            "dataIndex": "check_valve_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "packingGlandRel",
            "title": "Packing Gland Rel",
            "dataIndex": "packing_gland_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "hydPistonTypeRel",
            "title": "Hyd Piston Type Rel",
            "dataIndex": "hyd_piston_type_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "wellLicense",
            "title": "Well License",
            "dataIndex": "well_license",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "dgasPumpjack",
            "title": "Dgas Pumpjack",
            "dataIndex": "dgas_pumpjack",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "gateways_rel.aws_thing",
            "title": "Gateways Rel.Aws Thing",
            "dataIndex": "gateways_rel.aws_thing",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "operatorsUsersRel",
            "title": "Operators Users Rel",
            "dataIndex": "operators_users_rel",
            "type": "select"
      },
      {
            "key": "maintenanceUsersRel",
            "title": "Maintenance Users Rel",
            "dataIndex": "maintenance_users_rel",
            "type": "select"
      },
      {
            "key": "salesUsersRel",
            "title": "Sales Users Rel",
            "dataIndex": "sales_users_rel",
            "type": "select"
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    formFields: [
      {
            "key": "structure",
            "title": "Structure",
            "dataIndex": "structure",
            "type": "text",
            "required": true
      },
      {
            "key": "structureSlaveRel",
            "title": "Structure Slave Rel",
            "dataIndex": "structure_slave_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "powerUnitsRel",
            "title": "Power Units Rel",
            "dataIndex": "power_units_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "operatorsUsersRel",
            "title": "Operators Users Rel",
            "dataIndex": "operators_users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "maintenanceUsersRel",
            "title": "Maintenance Users Rel",
            "dataIndex": "maintenance_users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "salesUsersRel",
            "title": "Sales Users Rel",
            "dataIndex": "sales_users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "onGenset",
            "title": "On Genset",
            "dataIndex": "on_genset",
            "type": "text",
            "required": true
      },
      {
            "key": "autoGreaser",
            "title": "Auto Greaser",
            "dataIndex": "auto_greaser",
            "type": "text",
            "required": true
      },
      {
            "key": "autoGreaserNeedsAlerts",
            "title": "Auto Greaser Needs Alerts",
            "dataIndex": "auto_greaser_needs_alerts",
            "type": "text",
            "required": true
      },
      {
            "key": "opMonthsInterval",
            "title": "Op Months Interval",
            "dataIndex": "op_months_interval",
            "type": "text",
            "required": true
      },
      {
            "key": "qbSale",
            "title": "Qb Sale",
            "dataIndex": "qb_sale",
            "type": "text",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "modelTypeSlavesRel",
            "title": "Model Type Slaves Rel",
            "dataIndex": "model_type_slaves_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "unitTypesRel",
            "title": "Unit Types Rel",
            "dataIndex": "unit_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "runMfgDate",
            "title": "Run Mfg Date",
            "dataIndex": "run_mfg_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "structureInstallDate",
            "title": "Structure Install Date",
            "dataIndex": "structure_install_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "slaveInstallDate",
            "title": "Slave Install Date",
            "dataIndex": "slave_install_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "custSubGroupsRel",
            "title": "Cust Sub Groups Rel",
            "dataIndex": "cust_sub_groups_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "downhole",
            "title": "Downhole",
            "dataIndex": "downhole",
            "type": "text",
            "required": true
      },
      {
            "key": "surface",
            "title": "Surface",
            "dataIndex": "surface",
            "type": "text",
            "required": true
      },
      {
            "key": "timeZonesRel",
            "title": "Time Zones Rel",
            "dataIndex": "time_zones_rel",
            "type": "datetime",
            "required": true
      },
      {
            "key": "gpsLat",
            "title": "Gps Lat",
            "dataIndex": "gps_lat",
            "type": "text",
            "required": true
      },
      {
            "key": "gpsLon",
            "title": "Gps Lon",
            "dataIndex": "gps_lon",
            "type": "text",
            "required": true
      },
      {
            "key": "hasRcom",
            "title": "Has Rcom",
            "dataIndex": "has_rcom",
            "type": "boolean",
            "required": true
      },
      {
            "key": "warehouseRel",
            "title": "Warehouse Rel",
            "dataIndex": "warehouse_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "notes1",
            "title": "Notes 1",
            "dataIndex": "notes_1",
            "type": "textarea",
            "required": true
      },
      {
            "key": "wellLicense",
            "title": "Well License",
            "dataIndex": "well_license",
            "type": "text",
            "required": true
      },
      {
            "key": "afe",
            "title": "Afe",
            "dataIndex": "afe",
            "type": "text",
            "required": true
      },
      {
            "key": "rodRel",
            "title": "Rod Rel",
            "dataIndex": "rod_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "barrelRel",
            "title": "Barrel Rel",
            "dataIndex": "barrel_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "shuttleValveRel",
            "title": "Shuttle Valve Rel",
            "dataIndex": "shuttle_valve_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "packingGlandRel",
            "title": "Packing Gland Rel",
            "dataIndex": "packing_gland_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "hydPistonTypeRel",
            "title": "Hyd Piston Type Rel",
            "dataIndex": "hyd_piston_type_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "checkValveRel",
            "title": "Check Valve Rel",
            "dataIndex": "check_valve_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "dgasPumpjack",
            "title": "Dgas Pumpjack",
            "dataIndex": "dgas_pumpjack",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "structures_by_model",
    list: "/admin/structures_by_model/structures_by_model",
    create: "/admin/structures_by_model/structures_by_model",
    edit: "/admin/structures_by_model/structures_by_model",
    show: "/admin/structures_by_model/structures_by_model",
    meta: {
      label: "Structures Built, by Model",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "model",
            "title": "Model",
            "dataIndex": "model",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "mostRecentInstallDate",
            "title": "Most Recent Install Date",
            "dataIndex": "most_recent_install_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "totalUnits",
            "title": "Total Units",
            "dataIndex": "total_units",
            "type": "number",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "power_units",
    list: "/admin/power_units/power_units",
    create: "/admin/power_units/power_units",
    edit: "/admin/power_units/power_units",
    show: "/admin/power_units/power_units",
    meta: {
      label: "Power Units",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "powerUnit",
            "title": "Power Unit",
            "dataIndex": "power_unit",
            "type": "text",
            "sortable": true
      },
      {
            "key": "power_unit_type_rel.name",
            "title": "Power Unit Type Rel.Name",
            "dataIndex": "power_unit_type_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "runMfgDate",
            "title": "Run Mfg Date",
            "dataIndex": "run_mfg_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "websiteCardMsg",
            "title": "Website Card Msg",
            "dataIndex": "website_card_msg",
            "type": "text",
            "sortable": true
      },
      {
            "key": "structures_rel.notes_1",
            "title": "Structures Rel.Notes 1",
            "dataIndex": "structures_rel.notes_1",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "customers_rel.customer",
            "title": "Customers Rel.Customer",
            "dataIndex": "customers_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "structures_rel.model_types_rel",
            "title": "Structures Rel.Model Types Rel.Model",
            "dataIndex": "structures_rel.model_types_rel.model",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "gatewaysRel",
            "title": "Gateways Rel",
            "dataIndex": "gateways_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "modbusNetworksRel",
            "title": "Modbus Networks Rel",
            "dataIndex": "modbus_networks_rel",
            "type": "select"
      },
      {
            "key": "fixedIpNetworksRel",
            "title": "Fixed Ip Networks Rel",
            "dataIndex": "fixed_ip_networks_rel",
            "type": "select"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "powerUnit",
            "title": "Power Unit",
            "dataIndex": "power_unit",
            "type": "text",
            "required": true
      },
      {
            "key": "powerUnitTypeRel",
            "title": "Power Unit Type Rel",
            "dataIndex": "power_unit_type_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "runMfgDate",
            "title": "Run Mfg Date",
            "dataIndex": "run_mfg_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "websiteCardMsg",
            "title": "Website Card Msg",
            "dataIndex": "website_card_msg",
            "type": "text",
            "required": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "power",
            "title": "Power",
            "dataIndex": "power",
            "type": "text",
            "required": true
      },
      {
            "key": "voltage",
            "title": "Voltage",
            "dataIndex": "voltage",
            "type": "number",
            "required": true
      },
      {
            "key": "modbusNetworksRel",
            "title": "Modbus Networks Rel",
            "dataIndex": "modbus_networks_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "fixedIpNetworksRel",
            "title": "Fixed Ip Networks Rel",
            "dataIndex": "fixed_ip_networks_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "changeDetectSens",
            "title": "Change Detect Sens",
            "dataIndex": "change_detect_sens",
            "type": "text",
            "required": true
      },
      {
            "key": "waitTimeMins",
            "title": "Wait Time Mins",
            "dataIndex": "wait_time_mins",
            "type": "datetime",
            "required": true
      },
      {
            "key": "waitTimeMinsOl",
            "title": "Wait Time Mins Ol",
            "dataIndex": "wait_time_mins_ol",
            "type": "datetime",
            "required": true
      },
      {
            "key": "waitTimeMinsSuction",
            "title": "Wait Time Mins Suction",
            "dataIndex": "wait_time_mins_suction",
            "type": "datetime",
            "required": true
      },
      {
            "key": "suction",
            "title": "Suction",
            "dataIndex": "suction",
            "type": "text",
            "required": true
      },
      {
            "key": "waitTimeMinsDischarge",
            "title": "Wait Time Mins Discharge",
            "dataIndex": "wait_time_mins_discharge",
            "type": "datetime",
            "required": true
      },
      {
            "key": "discharge",
            "title": "Discharge",
            "dataIndex": "discharge",
            "type": "text",
            "required": true
      },
      {
            "key": "waitTimeMinsSpm",
            "title": "Wait Time Mins Spm",
            "dataIndex": "wait_time_mins_spm",
            "type": "datetime",
            "required": true
      },
      {
            "key": "waitTimeMinsStboxf",
            "title": "Wait Time Mins Stboxf",
            "dataIndex": "wait_time_mins_stboxf",
            "type": "datetime",
            "required": true
      },
      {
            "key": "spm",
            "title": "Spm",
            "dataIndex": "spm",
            "type": "text",
            "required": true
      },
      {
            "key": "waitTimeMinsHydTemp",
            "title": "Wait Time Mins Hyd Temp",
            "dataIndex": "wait_time_mins_hyd_temp",
            "type": "datetime",
            "required": true
      },
      {
            "key": "hydTemp",
            "title": "Hyd Temp",
            "dataIndex": "hyd_temp",
            "type": "number",
            "required": true
      },
      {
            "key": "alertsEdge",
            "title": "Alerts Edge",
            "dataIndex": "alerts_edge",
            "type": "text",
            "required": true
      },
      {
            "key": "heartbeatEnabled",
            "title": "Heartbeat Enabled",
            "dataIndex": "heartbeat_enabled",
            "type": "boolean",
            "required": true
      },
      {
            "key": "onlineHbEnabled",
            "title": "Online Hb Enabled",
            "dataIndex": "online_hb_enabled",
            "type": "boolean",
            "required": true
      },
      {
            "key": "waitTimeMinsHydOilLvl",
            "title": "Wait Time Mins Hyd Oil Lvl",
            "dataIndex": "wait_time_mins_hyd_oil_lvl",
            "type": "datetime",
            "required": true
      },
      {
            "key": "waitTimeMinsHydFiltLife",
            "title": "Wait Time Mins Hyd Filt Life",
            "dataIndex": "wait_time_mins_hyd_filt_life",
            "type": "datetime",
            "required": true
      },
      {
            "key": "waitTimeMinsHydOilLife",
            "title": "Wait Time Mins Hyd Oil Life",
            "dataIndex": "wait_time_mins_hyd_oil_life",
            "type": "datetime",
            "required": true
      },
      {
            "key": "hydOilLvlThresh",
            "title": "Hyd Oil Lvl Thresh",
            "dataIndex": "hyd_oil_lvl_thresh",
            "type": "text",
            "required": true
      },
      {
            "key": "hydFiltLifeThresh",
            "title": "Hyd Filt Life Thresh",
            "dataIndex": "hyd_filt_life_thresh",
            "type": "text",
            "required": true
      },
      {
            "key": "hydOilLifeThresh",
            "title": "Hyd Oil Life Thresh",
            "dataIndex": "hyd_oil_life_thresh",
            "type": "text",
            "required": true
      },
      {
            "key": "waitTimeMinsAe011",
            "title": "Wait Time Mins Ae011",
            "dataIndex": "wait_time_mins_ae011",
            "type": "datetime",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "power_unit_modbus_network",
    list: "/admin/power_unit_modbus_network/power_unit_modbus_network",
    create: "/admin/power_unit_modbus_network/power_unit_modbus_network",
    edit: "/admin/power_unit_modbus_network/power_unit_modbus_network",
    show: "/admin/power_unit_modbus_network/power_unit_modbus_network",
    meta: {
      label: "Power Unit Modbus Network",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "power_unit_rel.power_unit_str",
            "title": "Power Unit Rel.Power Unit Str",
            "dataIndex": "power_unit_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "power_unit_rel.customers_rel",
            "title": "Power Unit Rel.Customers Rel",
            "dataIndex": "power_unit_rel.customers_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "ipAddress",
            "title": "Ip Address",
            "dataIndex": "ip_address",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "subnet",
            "title": "Subnet",
            "dataIndex": "subnet",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "gateway",
            "title": "Gateway",
            "dataIndex": "gateway",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "neverDefault",
            "title": "Never Default",
            "dataIndex": "never_default",
            "type": "text",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "powerUnitRel",
            "title": "Power Unit Rel",
            "dataIndex": "power_unit_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "ipAddress",
            "title": "Ip Address",
            "dataIndex": "ip_address",
            "type": "text",
            "required": true
      },
      {
            "key": "subnet",
            "title": "Subnet",
            "dataIndex": "subnet",
            "type": "text",
            "required": true
      },
      {
            "key": "gateway",
            "title": "Gateway",
            "dataIndex": "gateway",
            "type": "text",
            "required": true
      },
      {
            "key": "neverDefault",
            "title": "Never Default",
            "dataIndex": "never_default",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "power_unit_fixed_ip_network",
    list: "/admin/power_unit_fixed_ip_network/power_unit_fixed_ip_network",
    create: "/admin/power_unit_fixed_ip_network/power_unit_fixed_ip_network",
    edit: "/admin/power_unit_fixed_ip_network/power_unit_fixed_ip_network",
    show: "/admin/power_unit_fixed_ip_network/power_unit_fixed_ip_network",
    meta: {
      label: "Power Unit Fixed IP Interface",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "power_unit_rel.power_unit_str",
            "title": "Power Unit Rel.Power Unit Str",
            "dataIndex": "power_unit_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "power_unit_rel.customers_rel",
            "title": "Power Unit Rel.Customers Rel",
            "dataIndex": "power_unit_rel.customers_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "ipAddress",
            "title": "Ip Address",
            "dataIndex": "ip_address",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "subnet",
            "title": "Subnet",
            "dataIndex": "subnet",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "gateway",
            "title": "Gateway",
            "dataIndex": "gateway",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "neverDefault",
            "title": "Never Default",
            "dataIndex": "never_default",
            "type": "text",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "powerUnitRel",
            "title": "Power Unit Rel",
            "dataIndex": "power_unit_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "ipAddress",
            "title": "Ip Address",
            "dataIndex": "ip_address",
            "type": "text",
            "required": true
      },
      {
            "key": "subnet",
            "title": "Subnet",
            "dataIndex": "subnet",
            "type": "text",
            "required": true
      },
      {
            "key": "gateway",
            "title": "Gateway",
            "dataIndex": "gateway",
            "type": "text",
            "required": true
      },
      {
            "key": "neverDefault",
            "title": "Never Default",
            "dataIndex": "never_default",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "gateways",
    list: "/admin/gateways/gateways",
    create: "/admin/gateways/gateways",
    edit: "/admin/gateways/gateways",
    show: "/admin/gateways/gateways",
    meta: {
      label: "Gateways",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "gateway_types_rel.name",
            "title": "Gateway Types Rel.Name",
            "dataIndex": "gateway_types_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "customers_rel.customer",
            "title": "Customers Rel.Customer",
            "dataIndex": "customers_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "cust_sub_groups_rel.name",
            "title": "Cust Sub Groups Rel.Name",
            "dataIndex": "cust_sub_groups_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "gateway",
            "title": "Gateway",
            "dataIndex": "gateway",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "serialGw",
            "title": "Serial Gw",
            "dataIndex": "serial_gw",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "modelGw",
            "title": "Model Gw",
            "dataIndex": "model_gw",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "imei",
            "title": "Imei",
            "dataIndex": "imei",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "power_units_rel.power_unit_str",
            "title": "Power Units Rel.Power Unit Str",
            "dataIndex": "power_units_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "structures_rel.structure",
            "title": "Structures Rel.Structure",
            "dataIndex": "structures_rel.structure",
            "type": "select",
            "sortable": true
      },
      {
            "key": "structures_rel.downhole",
            "title": "Structures Rel.Downhole",
            "dataIndex": "structures_rel.downhole",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "structures_rel.surface",
            "title": "Structures Rel.Surface",
            "dataIndex": "structures_rel.surface",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "testCanBus",
            "title": "Test Can Bus",
            "dataIndex": "test_can_bus",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "testCellular",
            "title": "Test Cellular",
            "dataIndex": "test_cellular",
            "type": "text",
            "sortable": true
      },
      {
            "key": "testCellularUserRel",
            "title": "Test Cellular User Rel",
            "dataIndex": "test_cellular_user_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "readyAndWorking",
            "title": "Ready And Working",
            "dataIndex": "ready_and_working",
            "type": "text",
            "sortable": true
      },
      {
            "key": "locationGw",
            "title": "Location Gw",
            "dataIndex": "location_gw",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "timeZonesRel",
            "title": "Time Zones Rel",
            "dataIndex": "time_zones_rel",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "changeDetectSens",
            "title": "Change Detect Sens",
            "dataIndex": "change_detect_sens",
            "type": "text",
            "sortable": true
      },
      {
            "key": "waitTimeMinsOl",
            "title": "Wait Time Mins Ol",
            "dataIndex": "wait_time_mins_ol",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "gatewayTypesRel",
            "title": "Gateway Types Rel",
            "dataIndex": "gateway_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "required": true
      },
      {
            "key": "gateway",
            "title": "Gateway",
            "dataIndex": "gateway",
            "type": "text",
            "required": true
      },
      {
            "key": "awsThing",
            "title": "Aws Thing",
            "dataIndex": "aws_thing",
            "type": "text",
            "required": true
      },
      {
            "key": "mac",
            "title": "Mac",
            "dataIndex": "mac",
            "type": "text",
            "required": true
      },
      {
            "key": "serialGw",
            "title": "Serial Gw",
            "dataIndex": "serial_gw",
            "type": "text",
            "required": true
      },
      {
            "key": "modelGw",
            "title": "Model Gw",
            "dataIndex": "model_gw",
            "type": "text",
            "required": true
      },
      {
            "key": "imei",
            "title": "Imei",
            "dataIndex": "imei",
            "type": "text",
            "required": true
      },
      {
            "key": "powerUnitsRel",
            "title": "Power Units Rel",
            "dataIndex": "power_units_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "required": true
      },
      {
            "key": "testCanBus",
            "title": "Test Can Bus",
            "dataIndex": "test_can_bus",
            "type": "boolean",
            "required": true
      },
      {
            "key": "testCellular",
            "title": "Test Cellular",
            "dataIndex": "test_cellular",
            "type": "text",
            "required": true
      },
      {
            "key": "testCellularUserRel",
            "title": "Test Cellular User Rel",
            "dataIndex": "test_cellular_user_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "readyAndWorking",
            "title": "Ready And Working",
            "dataIndex": "ready_and_working",
            "type": "text",
            "required": true
      },
      {
            "key": "locationGw",
            "title": "Location Gw",
            "dataIndex": "location_gw",
            "type": "text",
            "required": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "timeZonesRel",
            "title": "Time Zones Rel",
            "dataIndex": "time_zones_rel",
            "type": "datetime",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "gateway_info",
    list: "/admin/gateway_info/gateway_info",
    create: "/admin/gateway_info/gateway_info",
    edit: "/admin/gateway_info/gateway_info",
    show: "/admin/gateway_info/gateway_info",
    meta: {
      label: "Gateway Info",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "power_unit_rel.customers_rel",
            "title": "Power Unit Rel.Customers Rel",
            "dataIndex": "power_unit_rel.customers_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "powerUnitStr",
            "title": "Power Unit Str",
            "dataIndex": "power_unit_str",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "gateways_rel.gateway",
            "title": "Gateways Rel.Gateway",
            "dataIndex": "gateways_rel.gateway",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "awsThing",
            "title": "Aws Thing",
            "dataIndex": "aws_thing",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "timeSinceReported",
            "title": "Time Since Reported",
            "dataIndex": "time_since_reported",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "connected",
            "title": "Connected",
            "dataIndex": "connected",
            "type": "text",
            "sortable": true
      },
      {
            "key": "hyd",
            "title": "Hyd",
            "dataIndex": "hyd",
            "type": "text",
            "sortable": true
      },
      {
            "key": "warn1",
            "title": "Warn1",
            "dataIndex": "warn1",
            "type": "text",
            "sortable": true
      },
      {
            "key": "warn2",
            "title": "Warn2",
            "dataIndex": "warn2",
            "type": "text",
            "sortable": true
      },
      {
            "key": "hours",
            "title": "Hours",
            "dataIndex": "hours",
            "type": "text",
            "sortable": true
      },
      {
            "key": "gateway_types_rel.name",
            "title": "Gateway Types Rel.Name",
            "dataIndex": "gateway_types_rel.name",
            "type": "select",
            "sortable": true
      },
      {
            "key": "swvCanpy",
            "title": "Swv Canpy",
            "dataIndex": "swv_canpy",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "swvPlc",
            "title": "Swv Plc",
            "dataIndex": "swv_plc",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "suctionRange",
            "title": "Suction Range",
            "dataIndex": "suction_range",
            "type": "text",
            "sortable": true
      },
      {
            "key": "hasSlave",
            "title": "Has Slave",
            "dataIndex": "has_slave",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "modemModel",
            "title": "Modem Model",
            "dataIndex": "modem_model",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "modemFirmwareRev",
            "title": "Modem Firmware Rev",
            "dataIndex": "modem_firmware_rev",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "modemDrivers",
            "title": "Modem Drivers",
            "dataIndex": "modem_drivers",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "simOperator",
            "title": "Sim Operator",
            "dataIndex": "sim_operator",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "osName",
            "title": "Os Name",
            "dataIndex": "os_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "osPrettyName",
            "title": "Os Pretty Name",
            "dataIndex": "os_pretty_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "osVersion",
            "title": "Os Version",
            "dataIndex": "os_version",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "osVersionId",
            "title": "Os Version Id",
            "dataIndex": "os_version_id",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "osRelease",
            "title": "Os Release",
            "dataIndex": "os_release",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "osMachine",
            "title": "Os Machine",
            "dataIndex": "os_machine",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "osPlatform",
            "title": "Os Platform",
            "dataIndex": "os_platform",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "osPythonVersion",
            "title": "Os Python Version",
            "dataIndex": "os_python_version",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "driveSizeGb",
            "title": "Drive Size Gb",
            "dataIndex": "drive_size_gb",
            "type": "text",
            "sortable": true
      },
      {
            "key": "driveUsedGb",
            "title": "Drive Used Gb",
            "dataIndex": "drive_used_gb",
            "type": "text",
            "sortable": true
      },
      {
            "key": "memorySizeGb",
            "title": "Memory Size Gb",
            "dataIndex": "memory_size_gb",
            "type": "text",
            "sortable": true
      },
      {
            "key": "memoryUsedGb",
            "title": "Memory Used Gb",
            "dataIndex": "memory_used_gb",
            "type": "text",
            "sortable": true
      },
      {
            "key": "timestampUtcUpdated",
            "title": "Timestamp Utc Updated",
            "dataIndex": "timestamp_utc_updated",
            "type": "datetime",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "gateways_not_connected",
    list: "/admin/gateways_not_connected/gateways_not_connected",
    create: "/admin/gateways_not_connected/gateways_not_connected",
    edit: "/admin/gateways_not_connected/gateways_not_connected",
    show: "/admin/gateways_not_connected/gateways_not_connected",
    meta: {
      label: "Gateways Not Connected Notes",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "gateways_rel.gateway",
            "title": "Gateways Rel.Gateway",
            "dataIndex": "gateways_rel.gateway",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "gateways_rel.gateway_types_rel",
            "title": "Gateways Rel.Gateway Types Rel",
            "dataIndex": "gateways_rel.gateway_types_rel",
            "type": "select",
            "searchable": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "searchable": true
      },
      {
            "key": "surface",
            "title": "Surface",
            "dataIndex": "surface",
            "type": "text"
      },
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "users_rel.first_name",
            "title": "Users Rel.First Name",
            "dataIndex": "users_rel.first_name",
            "type": "select"
      },
      {
            "key": "users_rel.last_name",
            "title": "Users Rel.Last Name",
            "dataIndex": "users_rel.last_name",
            "type": "select"
      },
      {
            "key": "amIWorried",
            "title": "Am I Worried",
            "dataIndex": "am_i_worried",
            "type": "text"
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "testCellularUserRel",
            "title": "Test Cellular User Rel",
            "dataIndex": "test_cellular_user_rel",
            "type": "select"
      },
      {
            "key": "gatewayInfoDaysSinceReported",
            "title": "Gateway Info Days Since Reported",
            "dataIndex": "gateway_info_days_since_reported",
            "type": "text"
      },
      {
            "key": "operatorsContacted",
            "title": "Operators Contacted",
            "dataIndex": "operators_contacted",
            "type": "text"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "release_notes",
    list: "/admin/release_notes/release_notes",
    create: "/admin/release_notes/release_notes",
    edit: "/admin/release_notes/release_notes",
    show: "/admin/release_notes/release_notes",
    meta: {
      label: "Gateway Release Notes (Software)",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "version",
            "title": "Version",
            "dataIndex": "version",
            "type": "text"
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea"
      },
      {
            "key": "isStable",
            "title": "Is Stable",
            "dataIndex": "is_stable",
            "type": "boolean"
      },
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "can_bus_cob_map",
    list: "/admin/can_bus_cob_map/can_bus_cob_map",
    create: "/admin/can_bus_cob_map/can_bus_cob_map",
    edit: "/admin/can_bus_cob_map/can_bus_cob_map",
    show: "/admin/can_bus_cob_map/can_bus_cob_map",
    meta: {
      label: "CAN Bus Cob Map",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "cob",
            "title": "Cob",
            "dataIndex": "cob",
            "type": "text",
            "sortable": true
      },
      {
            "key": "byteStartIndex",
            "title": "Byte Start Index",
            "dataIndex": "byte_start_index",
            "type": "text",
            "sortable": true
      },
      {
            "key": "numBytes",
            "title": "Num Bytes",
            "dataIndex": "num_bytes",
            "type": "text",
            "sortable": true
      },
      {
            "key": "signed",
            "title": "Signed",
            "dataIndex": "signed",
            "type": "text",
            "sortable": true
      },
      {
            "key": "dataType",
            "title": "Data Type",
            "dataIndex": "data_type",
            "type": "text",
            "sortable": true
      },
      {
            "key": "decimals",
            "title": "Decimals",
            "dataIndex": "decimals",
            "type": "text",
            "sortable": true
      },
      {
            "key": "resolution",
            "title": "Resolution",
            "dataIndex": "resolution",
            "type": "text",
            "sortable": true
      },
      {
            "key": "offset",
            "title": "Offset ",
            "dataIndex": "offset_",
            "type": "text",
            "sortable": true
      },
      {
            "key": "bitStartIndex",
            "title": "Bit Start Index",
            "dataIndex": "bit_start_index",
            "type": "text",
            "sortable": true
      },
      {
            "key": "abbrev",
            "title": "Abbrev",
            "dataIndex": "abbrev",
            "type": "text",
            "sortable": true
      },
      {
            "key": "plus1Variable",
            "title": "Plus1 Variable",
            "dataIndex": "plus1_variable",
            "type": "text",
            "sortable": true
      },
      {
            "key": "plus1Program",
            "title": "Plus1 Program",
            "dataIndex": "plus1_program",
            "type": "text",
            "sortable": true
      },
      {
            "key": "rcomName",
            "title": "Rcom Name",
            "dataIndex": "rcom_name",
            "type": "text",
            "sortable": true
      },
      {
            "key": "rcomTab",
            "title": "Rcom Tab",
            "dataIndex": "rcom_tab",
            "type": "text",
            "sortable": true
      },
      {
            "key": "controllerVersion",
            "title": "Controller Version",
            "dataIndex": "controller_version",
            "type": "text",
            "sortable": true
      },
      {
            "key": "machine",
            "title": "Machine",
            "dataIndex": "machine",
            "type": "text",
            "sortable": true
      },
      {
            "key": "item",
            "title": "Item",
            "dataIndex": "item",
            "type": "text",
            "sortable": true
      },
      {
            "key": "units",
            "title": "Units",
            "dataIndex": "units",
            "type": "text",
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "sortable": true
      },
      {
            "key": "controlNum",
            "title": "Control Num",
            "dataIndex": "control_num",
            "type": "text",
            "sortable": true
      },
      {
            "key": "enum",
            "title": "Enum",
            "dataIndex": "enum",
            "type": "text",
            "sortable": true
      },
      {
            "key": "number",
            "title": "Number ",
            "dataIndex": "number_",
            "type": "number",
            "sortable": true
      },
      {
            "key": "interesting",
            "title": "Interesting",
            "dataIndex": "interesting",
            "type": "text",
            "sortable": true
      },
      {
            "key": "modbusHoldingRegistersRel",
            "title": "Modbus Holding Registers Rel",
            "dataIndex": "modbus_holding_registers_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "modbus_holding_registers_rel.address",
            "title": "Modbus Holding Registers Rel.Address",
            "dataIndex": "modbus_holding_registers_rel.address",
            "type": "select"
      },
      {
            "key": "modbus_holding_registers_rel.n_registers",
            "title": "Modbus Holding Registers Rel.N Registers",
            "dataIndex": "modbus_holding_registers_rel.n_registers",
            "type": "select"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "cob",
            "title": "Cob",
            "dataIndex": "cob",
            "type": "text",
            "required": true
      },
      {
            "key": "byteStartIndex",
            "title": "Byte Start Index",
            "dataIndex": "byte_start_index",
            "type": "text",
            "required": true
      },
      {
            "key": "numBytes",
            "title": "Num Bytes",
            "dataIndex": "num_bytes",
            "type": "text",
            "required": true
      },
      {
            "key": "signed",
            "title": "Signed",
            "dataIndex": "signed",
            "type": "text",
            "required": true
      },
      {
            "key": "dataType",
            "title": "Data Type",
            "dataIndex": "data_type",
            "type": "text",
            "required": true
      },
      {
            "key": "decimals",
            "title": "Decimals",
            "dataIndex": "decimals",
            "type": "text",
            "required": true
      },
      {
            "key": "resolution",
            "title": "Resolution",
            "dataIndex": "resolution",
            "type": "text",
            "required": true
      },
      {
            "key": "offset",
            "title": "Offset ",
            "dataIndex": "offset_",
            "type": "text",
            "required": true
      },
      {
            "key": "bitStartIndex",
            "title": "Bit Start Index",
            "dataIndex": "bit_start_index",
            "type": "text",
            "required": true
      },
      {
            "key": "abbrev",
            "title": "Abbrev",
            "dataIndex": "abbrev",
            "type": "text",
            "required": true
      },
      {
            "key": "plus1Variable",
            "title": "Plus1 Variable",
            "dataIndex": "plus1_variable",
            "type": "text",
            "required": true
      },
      {
            "key": "plus1Program",
            "title": "Plus1 Program",
            "dataIndex": "plus1_program",
            "type": "text",
            "required": true
      },
      {
            "key": "rcomName",
            "title": "Rcom Name",
            "dataIndex": "rcom_name",
            "type": "text",
            "required": true
      },
      {
            "key": "rcomTab",
            "title": "Rcom Tab",
            "dataIndex": "rcom_tab",
            "type": "text",
            "required": true
      },
      {
            "key": "controllerVersion",
            "title": "Controller Version",
            "dataIndex": "controller_version",
            "type": "text",
            "required": true
      },
      {
            "key": "machine",
            "title": "Machine",
            "dataIndex": "machine",
            "type": "text",
            "required": true
      },
      {
            "key": "item",
            "title": "Item",
            "dataIndex": "item",
            "type": "text",
            "required": true
      },
      {
            "key": "units",
            "title": "Units",
            "dataIndex": "units",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "controlNum",
            "title": "Control Num",
            "dataIndex": "control_num",
            "type": "text",
            "required": true
      },
      {
            "key": "enum",
            "title": "Enum",
            "dataIndex": "enum",
            "type": "text",
            "required": true
      },
      {
            "key": "number",
            "title": "Number ",
            "dataIndex": "number_",
            "type": "number",
            "required": true
      },
      {
            "key": "interesting",
            "title": "Interesting",
            "dataIndex": "interesting",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "modbus_holding_registers",
    list: "/admin/modbus_holding_registers/modbus_holding_registers",
    create: "/admin/modbus_holding_registers/modbus_holding_registers",
    edit: "/admin/modbus_holding_registers/modbus_holding_registers",
    show: "/admin/modbus_holding_registers/modbus_holding_registers",
    meta: {
      label: "Modbus and Web API Metrics",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "abbrev_rel.abbrev",
            "title": "Abbrev Rel.Abbrev",
            "dataIndex": "abbrev_rel.abbrev",
            "type": "select"
      },
      {
            "key": "abbrev_rel.description",
            "title": "Abbrev Rel.Description",
            "dataIndex": "abbrev_rel.description",
            "type": "select"
      },
      {
            "key": "address",
            "title": "Address",
            "dataIndex": "address",
            "type": "text"
      },
      {
            "key": "nRegisters",
            "title": "N Registers",
            "dataIndex": "n_registers",
            "type": "text"
      },
      {
            "key": "holdingReg40k",
            "title": "Holding Reg 40K",
            "dataIndex": "holding_reg_40k",
            "type": "text"
      },
      {
            "key": "inWebApi",
            "title": "In Web Api",
            "dataIndex": "in_web_api",
            "type": "text"
      },
      {
            "key": "writableModbus",
            "title": "Writable Modbus",
            "dataIndex": "writable_modbus",
            "type": "text"
      },
      {
            "key": "writableWebApi",
            "title": "Writable Web Api",
            "dataIndex": "writable_web_api",
            "type": "text"
      },
      {
            "key": "minVal",
            "title": "Min Val",
            "dataIndex": "min_val",
            "type": "text"
      },
      {
            "key": "maxVal",
            "title": "Max Val",
            "dataIndex": "max_val",
            "type": "text"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "abbrevRel",
            "title": "Abbrev Rel",
            "dataIndex": "abbrev_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "address",
            "title": "Address",
            "dataIndex": "address",
            "type": "text",
            "required": true
      },
      {
            "key": "nRegisters",
            "title": "N Registers",
            "dataIndex": "n_registers",
            "type": "text",
            "required": true
      },
      {
            "key": "inWebApi",
            "title": "In Web Api",
            "dataIndex": "in_web_api",
            "type": "text",
            "required": true
      },
      {
            "key": "writableModbus",
            "title": "Writable Modbus",
            "dataIndex": "writable_modbus",
            "type": "text",
            "required": true
      },
      {
            "key": "writableWebApi",
            "title": "Writable Web Api",
            "dataIndex": "writable_web_api",
            "type": "text",
            "required": true
      },
      {
            "key": "minVal",
            "title": "Min Val",
            "dataIndex": "min_val",
            "type": "text",
            "required": true
      },
      {
            "key": "maxVal",
            "title": "Max Val",
            "dataIndex": "max_val",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "alarm_log_metrics",
    list: "/admin/alarm_log_metrics/alarm_log_metrics",
    create: "/admin/alarm_log_metrics/alarm_log_metrics",
    edit: "/admin/alarm_log_metrics/alarm_log_metrics",
    show: "/admin/alarm_log_metrics/alarm_log_metrics",
    meta: {
      label: "Alarm Log Metrics",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "abbrev",
            "title": "Abbrev",
            "dataIndex": "abbrev",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "index",
            "title": "Index",
            "dataIndex": "index",
            "type": "text",
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "diagnostic_metrics",
    list: "/admin/diagnostic_metrics/diagnostic_metrics",
    create: "/admin/diagnostic_metrics/diagnostic_metrics",
    edit: "/admin/diagnostic_metrics/diagnostic_metrics",
    show: "/admin/diagnostic_metrics/diagnostic_metrics",
    meta: {
      label: "Diagnostic Metrics",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "diagNum",
            "title": "Diag Num",
            "dataIndex": "diag_num",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "decimals",
            "title": "Decimals",
            "dataIndex": "decimals",
            "type": "text",
            "sortable": true
      },
      {
            "key": "units",
            "title": "Units",
            "dataIndex": "units",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "color",
            "title": "Color",
            "dataIndex": "color",
            "type": "text"
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "timestampUtcModified",
            "title": "Timestamp Utc Modified",
            "dataIndex": "timestamp_utc_modified",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "diagnostic_data",
    list: "/admin/diagnostic_data/diagnostic_data",
    create: "/admin/diagnostic_data/diagnostic_data",
    edit: "/admin/diagnostic_data/diagnostic_data",
    show: "/admin/diagnostic_data/diagnostic_data",
    meta: {
      label: "Diagnostic Data",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "powerUnitStr",
            "title": "Power Unit Str",
            "dataIndex": "power_unit_str",
            "type": "text",
            "searchable": true
      },
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcModified",
            "title": "Timestamp Utc Modified",
            "dataIndex": "timestamp_utc_modified",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "isMain",
            "title": "Is Main",
            "dataIndex": "is_main",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "msgType",
            "title": "Msg Type",
            "dataIndex": "msg_type",
            "type": "text",
            "sortable": true
      },
      {
            "key": "diagNum",
            "title": "Diag Num",
            "dataIndex": "diag_num",
            "type": "text",
            "sortable": true
      },
      {
            "key": "value",
            "title": "Value",
            "dataIndex": "value",
            "type": "text",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "sim_cards",
    list: "/admin/sim_cards/sim_cards",
    create: "/admin/sim_cards/sim_cards",
    edit: "/admin/sim_cards/sim_cards",
    show: "/admin/sim_cards/sim_cards",
    meta: {
      label: "SIM Cards",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "simCard",
            "title": "Sim Card",
            "dataIndex": "sim_card",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "simCardNum",
            "title": "Sim Card Num",
            "dataIndex": "sim_card_num",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "customers_rel.customer",
            "title": "Customers Rel.Customer",
            "dataIndex": "customers_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "gateways_rel.gateway",
            "title": "Gateways Rel.Gateway",
            "dataIndex": "gateways_rel.gateway",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "simCardActivated",
            "title": "Sim Card Activated",
            "dataIndex": "sim_card_activated",
            "type": "text",
            "sortable": true
      },
      {
            "key": "simCardPhone",
            "title": "Sim Card Phone",
            "dataIndex": "sim_card_phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "cellProvider",
            "title": "Cell Provider",
            "dataIndex": "cell_provider",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "location",
            "title": "Location",
            "dataIndex": "location",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "simCard",
            "title": "Sim Card",
            "dataIndex": "sim_card",
            "type": "text",
            "required": true
      },
      {
            "key": "simCardNum",
            "title": "Sim Card Num",
            "dataIndex": "sim_card_num",
            "type": "text",
            "required": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "gatewaysRel",
            "title": "Gateways Rel",
            "dataIndex": "gateways_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "simCardActivated",
            "title": "Sim Card Activated",
            "dataIndex": "sim_card_activated",
            "type": "text",
            "required": true
      },
      {
            "key": "simCardPhone",
            "title": "Sim Card Phone",
            "dataIndex": "sim_card_phone",
            "type": "text",
            "required": true
      },
      {
            "key": "cellProvider",
            "title": "Cell Provider",
            "dataIndex": "cell_provider",
            "type": "number",
            "required": true
      },
      {
            "key": "location",
            "title": "Location",
            "dataIndex": "location",
            "type": "text",
            "required": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "calculator",
    list: "/admin/calculator/calculator",
    create: "/admin/calculator/calculator",
    edit: "/admin/calculator/calculator",
    show: "/admin/calculator/calculator",
    meta: {
      label: "Calculator",
      category: "Units"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text"
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea"
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select"
      },
      {
            "key": "model_types_rel.unit_types_rel",
            "title": "Model Types Rel.Unit Types Rel",
            "dataIndex": "model_types_rel.unit_types_rel",
            "type": "select"
      },
      {
            "key": "powerUnitTypeRel",
            "title": "Power Unit Type Rel",
            "dataIndex": "power_unit_type_rel",
            "type": "select"
      },
      {
            "key": "diameter",
            "title": "Diameter",
            "dataIndex": "diameter",
            "type": "number"
      },
      {
            "key": "area",
            "title": "Area",
            "dataIndex": "area",
            "type": "text"
      },
      {
            "key": "stroke",
            "title": "Stroke",
            "dataIndex": "stroke",
            "type": "text"
      },
      {
            "key": "maxSpm",
            "title": "Max Spm",
            "dataIndex": "max_spm",
            "type": "text"
      },
      {
            "key": "maxDeltaP",
            "title": "Max Delta P",
            "dataIndex": "max_delta_p",
            "type": "text"
      },
      {
            "key": "mawp",
            "title": "Mawp",
            "dataIndex": "mawp",
            "type": "text"
      },
      {
            "key": "rodSize",
            "title": "Rod Size",
            "dataIndex": "rod_size",
            "type": "text"
      },
      {
            "key": "motorHp",
            "title": "Motor Hp",
            "dataIndex": "motor_hp",
            "type": "text"
      },
      {
            "key": "hyds",
            "title": "Hyds",
            "dataIndex": "hyds",
            "type": "text"
      },
      {
            "key": "maxLiquidM3pd",
            "title": "Max Liquid M3Pd",
            "dataIndex": "max_liquid_m3pd",
            "type": "number"
      },
      {
            "key": "minLiquidM3pd10pct",
            "title": "Min Liquid M3Pd 10Pct",
            "dataIndex": "min_liquid_m3pd_10pct",
            "type": "number"
      },
      {
            "key": "hydSizeInch",
            "title": "Hyd Size Inch",
            "dataIndex": "hyd_size_inch",
            "type": "text"
      },
      {
            "key": "singleLossCuInch",
            "title": "Single Loss Cu Inch",
            "dataIndex": "single_loss_cu_inch",
            "type": "text"
      },
      {
            "key": "portAreaSqInch",
            "title": "Port Area Sq Inch",
            "dataIndex": "port_area_sq_inch",
            "type": "text"
      },
      {
            "key": "singlePortGasLevel",
            "title": "Single Port Gas Level",
            "dataIndex": "single_port_gas_level",
            "type": "text"
      },
      {
            "key": "chargePumpDispCc",
            "title": "Charge Pump Disp Cc",
            "dataIndex": "charge_pump_disp_cc",
            "type": "text"
      },
      {
            "key": "chargePumpPressPsi",
            "title": "Charge Pump Press Psi",
            "dataIndex": "charge_pump_press_psi",
            "type": "text"
      },
      {
            "key": "frictionMinDeltaP",
            "title": "Friction Min Delta P",
            "dataIndex": "friction_min_delta_p",
            "type": "text"
      },
      {
            "key": "frictionMaxDeltaP",
            "title": "Friction Max Delta P",
            "dataIndex": "friction_max_delta_p",
            "type": "text"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "powerUnitTypeRel",
            "title": "Power Unit Type Rel",
            "dataIndex": "power_unit_type_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "diameter",
            "title": "Diameter",
            "dataIndex": "diameter",
            "type": "number",
            "required": true
      },
      {
            "key": "area",
            "title": "Area",
            "dataIndex": "area",
            "type": "text",
            "required": true
      },
      {
            "key": "stroke",
            "title": "Stroke",
            "dataIndex": "stroke",
            "type": "text",
            "required": true
      },
      {
            "key": "maxSpm",
            "title": "Max Spm",
            "dataIndex": "max_spm",
            "type": "text",
            "required": true
      },
      {
            "key": "maxDeltaP",
            "title": "Max Delta P",
            "dataIndex": "max_delta_p",
            "type": "text",
            "required": true
      },
      {
            "key": "mawp",
            "title": "Mawp",
            "dataIndex": "mawp",
            "type": "text",
            "required": true
      },
      {
            "key": "rodSize",
            "title": "Rod Size",
            "dataIndex": "rod_size",
            "type": "text",
            "required": true
      },
      {
            "key": "motorHp",
            "title": "Motor Hp",
            "dataIndex": "motor_hp",
            "type": "text",
            "required": true
      },
      {
            "key": "hyds",
            "title": "Hyds",
            "dataIndex": "hyds",
            "type": "text",
            "required": true
      },
      {
            "key": "maxLiquidM3pd",
            "title": "Max Liquid M3Pd",
            "dataIndex": "max_liquid_m3pd",
            "type": "number",
            "required": true
      },
      {
            "key": "minLiquidM3pd10pct",
            "title": "Min Liquid M3Pd 10Pct",
            "dataIndex": "min_liquid_m3pd_10pct",
            "type": "number",
            "required": true
      },
      {
            "key": "hydSizeInch",
            "title": "Hyd Size Inch",
            "dataIndex": "hyd_size_inch",
            "type": "text",
            "required": true
      },
      {
            "key": "singleLossCuInch",
            "title": "Single Loss Cu Inch",
            "dataIndex": "single_loss_cu_inch",
            "type": "text",
            "required": true
      },
      {
            "key": "portAreaSqInch",
            "title": "Port Area Sq Inch",
            "dataIndex": "port_area_sq_inch",
            "type": "text",
            "required": true
      },
      {
            "key": "singlePortGasLevel",
            "title": "Single Port Gas Level",
            "dataIndex": "single_port_gas_level",
            "type": "text",
            "required": true
      },
      {
            "key": "chargePumpDispCc",
            "title": "Charge Pump Disp Cc",
            "dataIndex": "charge_pump_disp_cc",
            "type": "text",
            "required": true
      },
      {
            "key": "chargePumpPressPsi",
            "title": "Charge Pump Press Psi",
            "dataIndex": "charge_pump_press_psi",
            "type": "text",
            "required": true
      },
      {
            "key": "frictionMinDeltaP",
            "title": "Friction Min Delta P",
            "dataIndex": "friction_min_delta_p",
            "type": "text",
            "required": true
      },
      {
            "key": "frictionMaxDeltaP",
            "title": "Friction Max Delta P",
            "dataIndex": "friction_max_delta_p",
            "type": "text",
            "required": true
      }
]
  },
  {
    name: "gateway_types",
    list: "/admin/gateway_types/gateway_types",
    create: "/admin/gateway_types/gateway_types",
    edit: "/admin/gateway_types/gateway_types",
    show: "/admin/gateway_types/gateway_types",
    meta: {
      label: "Gateway Types",
      category: "Units"
    }

  },
];

export default unitsResources;
