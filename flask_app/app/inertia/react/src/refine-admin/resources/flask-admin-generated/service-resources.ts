// Auto-generated refine admin resources for Service category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const serviceResources: ResourceProps[] = [
  {
    name: "work_orders",
    list: "/admin/work_orders/work_orders",
    create: "/admin/work_orders/work_orders",
    edit: "/admin/work_orders/work_orders",
    show: "/admin/work_orders/work_orders",
    meta: {
      label: "Work Orders (IJACK Inc 🍁)",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      },
      {
            "key": "creatorRel",
            "title": "Creator Rel",
            "dataIndex": "creator_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select"
      },
      {
            "key": "usersSalesRel",
            "title": "Users Sales Rel",
            "dataIndex": "users_sales_rel",
            "type": "select"
      },
      {
            "key": "creatorCompanyRel",
            "title": "Creator Company Rel",
            "dataIndex": "creator_company_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "dateService",
            "title": "Date Service",
            "dataIndex": "date_service",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "invoiceApprovalReq",
            "title": "Invoice Approval Req",
            "dataIndex": "invoice_approval_req",
            "type": "text",
            "sortable": true
      },
      {
            "key": "dateSentForApproval",
            "title": "Date Sent For Approval",
            "dataIndex": "date_sent_for_approval",
            "type": "datetime"
      },
      {
            "key": "approvedByRel",
            "title": "Approved By Rel",
            "dataIndex": "approved_by_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "isQuote",
            "title": "Is Quote",
            "dataIndex": "is_quote",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "quickbooksNum",
            "title": "Quickbooks Num",
            "dataIndex": "quickbooks_num",
            "type": "text",
            "sortable": true
      },
      {
            "key": "statusRel",
            "title": "Status Rel",
            "dataIndex": "status_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "isWarranty",
            "title": "Is Warranty",
            "dataIndex": "is_warranty",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "serviceTypeRel",
            "title": "Service Type Rel",
            "dataIndex": "service_type_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "customers_rel.customer",
            "title": "Customers Rel.Customer",
            "dataIndex": "customers_rel.customer",
            "type": "select",
            "sortable": true
      },
      {
            "key": "currency_rel.name",
            "title": "Currency Rel.Name",
            "dataIndex": "currency_rel.name",
            "type": "select",
            "sortable": true
      },
      {
            "key": "location",
            "title": "Location",
            "dataIndex": "location",
            "type": "text",
            "sortable": true
      },
      {
            "key": "county_rel.name",
            "title": "County Rel.Name",
            "dataIndex": "county_rel.name",
            "type": "number",
            "sortable": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "powerUnitsRel",
            "title": "Power Units Rel",
            "dataIndex": "power_units_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "total",
            "title": "Total",
            "dataIndex": "total",
            "type": "number",
            "sortable": true
      },
      {
            "key": "serviceRequired",
            "title": "Service Required",
            "dataIndex": "service_required",
            "type": "text",
            "sortable": true
      },
      {
            "key": "workDone",
            "title": "Work Done",
            "dataIndex": "work_done",
            "type": "text",
            "sortable": true
      },
      {
            "key": "approvalPersonRel",
            "title": "Approval Person Rel",
            "dataIndex": "approval_person_rel",
            "type": "select"
      },
      {
            "key": "customerPo",
            "title": "Customer Po",
            "dataIndex": "customer_po",
            "type": "text"
      },
      {
            "key": "serviceCrew",
            "title": "Service Crew",
            "dataIndex": "service_crew",
            "type": "text"
      },
      {
            "key": "custWorkOrder",
            "title": "Cust Work Order",
            "dataIndex": "cust_work_order",
            "type": "text"
      },
      {
            "key": "afe",
            "title": "Afe",
            "dataIndex": "afe",
            "type": "text"
      },
      {
            "key": "serviceHours",
            "title": "Service Hours",
            "dataIndex": "service_hours",
            "type": "text"
      },
      {
            "key": "travelTimeHours",
            "title": "Travel Time Hours",
            "dataIndex": "travel_time_hours",
            "type": "datetime"
      },
      {
            "key": "invoiceSummary",
            "title": "Invoice Summary",
            "dataIndex": "invoice_summary",
            "type": "text"
      },
      {
            "key": "unitTypesRel",
            "title": "Unit Types Rel",
            "dataIndex": "unit_types_rel",
            "type": "select"
      },
      {
            "key": "structureSlave",
            "title": "Structure Slave",
            "dataIndex": "structure_slave",
            "type": "text"
      },
      {
            "key": "workOrderPartsRel",
            "title": "Work Order Parts Rel",
            "dataIndex": "work_order_parts_rel",
            "type": "select"
      },
      {
            "key": "subtotal",
            "title": "Subtotal",
            "dataIndex": "subtotal",
            "type": "number"
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select"
      },
      {
            "key": "salesTaxRate",
            "title": "Sales Tax Rate",
            "dataIndex": "sales_tax_rate",
            "type": "text"
      },
      {
            "key": "salesTax",
            "title": "Sales Tax",
            "dataIndex": "sales_tax",
            "type": "text"
      },
      {
            "key": "currencyRel",
            "title": "Currency Rel",
            "dataIndex": "currency_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "work_order_upload_files_rel.file_name",
            "title": "Work Order Upload Files Rel.File Name",
            "dataIndex": "work_order_upload_files_rel.file_name",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "dateService",
            "title": "Date Service",
            "dataIndex": "date_service",
            "type": "datetime",
            "required": true
      },
      {
            "key": "customersRel",
            "title": "Customers Rel",
            "dataIndex": "customers_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "currencyRel",
            "title": "Currency Rel",
            "dataIndex": "currency_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "creatorCompanyRel",
            "title": "Creator Company Rel",
            "dataIndex": "creator_company_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "location",
            "title": "Location",
            "dataIndex": "location",
            "type": "text",
            "required": true
      },
      {
            "key": "serviceTypeRel",
            "title": "Service Type Rel",
            "dataIndex": "service_type_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "serviceTypeRel",
            "title": "Service Type Rel",
            "dataIndex": "service_type_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "dateSentForApproval",
            "title": "Date Sent For Approval",
            "dataIndex": "date_sent_for_approval",
            "type": "datetime",
            "required": true
      },
      {
            "key": "invoiceApprovalReq",
            "title": "Invoice Approval Req",
            "dataIndex": "invoice_approval_req",
            "type": "text",
            "required": true
      },
      {
            "key": "isQuote",
            "title": "Is Quote",
            "dataIndex": "is_quote",
            "type": "boolean",
            "required": true
      },
      {
            "key": "statusRel",
            "title": "Status Rel",
            "dataIndex": "status_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "quickbooksNum",
            "title": "Quickbooks Num",
            "dataIndex": "quickbooks_num",
            "type": "text",
            "required": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "usersSalesRel",
            "title": "Users Sales Rel",
            "dataIndex": "users_sales_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "serviceCrew",
            "title": "Service Crew",
            "dataIndex": "service_crew",
            "type": "text",
            "required": true
      },
      {
            "key": "requestedByRel",
            "title": "Requested By Rel",
            "dataIndex": "requested_by_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "approvalPersonRel",
            "title": "Approval Person Rel",
            "dataIndex": "approval_person_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "custWorkOrder",
            "title": "Cust Work Order",
            "dataIndex": "cust_work_order",
            "type": "text",
            "required": true
      },
      {
            "key": "afe",
            "title": "Afe",
            "dataIndex": "afe",
            "type": "text",
            "required": true
      },
      {
            "key": "customerPo",
            "title": "Customer Po",
            "dataIndex": "customer_po",
            "type": "text",
            "required": true
      },
      {
            "key": "modelTypesRel",
            "title": "Model Types Rel",
            "dataIndex": "model_types_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "powerUnitsRel",
            "title": "Power Units Rel",
            "dataIndex": "power_units_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "structureSlave",
            "title": "Structure Slave",
            "dataIndex": "structure_slave",
            "type": "text",
            "required": true
      },
      {
            "key": "serviceHours",
            "title": "Service Hours",
            "dataIndex": "service_hours",
            "type": "text",
            "required": true
      },
      {
            "key": "travelTimeHours",
            "title": "Travel Time Hours",
            "dataIndex": "travel_time_hours",
            "type": "datetime",
            "required": true
      },
      {
            "key": "isWarranty",
            "title": "Is Warranty",
            "dataIndex": "is_warranty",
            "type": "boolean",
            "required": true
      },
      {
            "key": "isWarrantyReason",
            "title": "Is Warranty Reason",
            "dataIndex": "is_warranty_reason",
            "type": "boolean",
            "required": true
      },
      {
            "key": "serviceRequired",
            "title": "Service Required",
            "dataIndex": "service_required",
            "type": "text",
            "required": true
      },
      {
            "key": "workDone",
            "title": "Work Done",
            "dataIndex": "work_done",
            "type": "text",
            "required": true
      },
      {
            "key": "invoiceSummary",
            "title": "Invoice Summary",
            "dataIndex": "invoice_summary",
            "type": "text",
            "required": true
      },
      {
            "key": "workOrderPartsRel",
            "title": "Work Order Parts Rel",
            "dataIndex": "work_order_parts_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "currencyRel",
            "title": "Currency Rel",
            "dataIndex": "currency_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "countyRel",
            "title": "County Rel",
            "dataIndex": "county_rel",
            "type": "number",
            "required": true
      },
      {
            "key": "workOrderUploadFilesRel",
            "title": "Work Order Upload Files Rel",
            "dataIndex": "work_order_upload_files_rel",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true,
      "export": true
}
  },
  {
    name: "work_orders_corp",
    list: "/admin/work_orders_corp/work_orders_corp",
    create: "/admin/work_orders_corp/work_orders_corp",
    edit: "/admin/work_orders_corp/work_orders_corp",
    show: "/admin/work_orders_corp/work_orders_corp",
    meta: {
      label: "Work Orders (IJACK Corp - USA)",
      category: "Service"
    }

  },
  {
    name: "work_order_quotes",
    list: "/admin/work_order_quotes/work_order_quotes",
    create: "/admin/work_order_quotes/work_order_quotes",
    edit: "/admin/work_order_quotes/work_order_quotes",
    show: "/admin/work_order_quotes/work_order_quotes",
    meta: {
      label: "Work Order Quotes (IJACK Inc 🍁)",
      category: "Service"
    }

  },
  {
    name: "work_order_quotes_corp",
    list: "/admin/work_order_quotes_corp/work_order_quotes_corp",
    create: "/admin/work_order_quotes_corp/work_order_quotes_corp",
    edit: "/admin/work_order_quotes_corp/work_order_quotes_corp",
    show: "/admin/work_order_quotes_corp/work_order_quotes_corp",
    meta: {
      label: "Work Order Quotes (IJACK Corp - USA)",
      category: "Service"
    }

  },
  {
    name: "work_orders_by_unit",
    list: "/admin/work_orders_by_unit/work_orders_by_unit",
    create: "/admin/work_orders_by_unit/work_orders_by_unit",
    edit: "/admin/work_orders_by_unit/work_orders_by_unit",
    show: "/admin/work_orders_by_unit/work_orders_by_unit",
    meta: {
      label: "Work Orders by Unit",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "workOrderId",
            "title": "Work Order Id",
            "dataIndex": "work_order_id",
            "type": "number",
            "searchable": true
      },
      {
            "key": "dateService",
            "title": "Date Service",
            "dataIndex": "date_service",
            "type": "datetime"
      },
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text",
            "searchable": true
      },
      {
            "key": "model",
            "title": "Model",
            "dataIndex": "model",
            "type": "text",
            "searchable": true
      },
      {
            "key": "unitType",
            "title": "Unit Type",
            "dataIndex": "unit_type",
            "type": "text",
            "searchable": true
      },
      {
            "key": "powerUnit",
            "title": "Power Unit",
            "dataIndex": "power_unit",
            "type": "text"
      },
      {
            "key": "powerUnitType",
            "title": "Power Unit Type",
            "dataIndex": "power_unit_type",
            "type": "text",
            "searchable": true
      },
      {
            "key": "structure",
            "title": "Structure",
            "dataIndex": "structure",
            "type": "text",
            "searchable": true
      },
      {
            "key": "downhole",
            "title": "Downhole",
            "dataIndex": "downhole",
            "type": "text",
            "searchable": true
      },
      {
            "key": "surface",
            "title": "Surface",
            "dataIndex": "surface",
            "type": "text",
            "searchable": true
      },
      {
            "key": "serviceRequired",
            "title": "Service Required",
            "dataIndex": "service_required",
            "type": "text",
            "searchable": true
      },
      {
            "key": "workDone",
            "title": "Work Done",
            "dataIndex": "work_done",
            "type": "text",
            "searchable": true
      },
      {
            "key": "isWarranty",
            "title": "Is Warranty",
            "dataIndex": "is_warranty",
            "type": "boolean"
      },
      {
            "key": "isWarrantyReason",
            "title": "Is Warranty Reason",
            "dataIndex": "is_warranty_reason",
            "type": "boolean",
            "searchable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "work_order_parts",
    list: "/admin/work_order_parts/work_order_parts",
    create: "/admin/work_order_parts/work_order_parts",
    edit: "/admin/work_order_parts/work_order_parts",
    show: "/admin/work_order_parts/work_order_parts",
    meta: {
      label: "Work Order Parts (Line Items)",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "workOrdersRel",
            "title": "Work Orders Rel",
            "dataIndex": "work_orders_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quickbooksNum",
            "title": "Quickbooks Num",
            "dataIndex": "quickbooks_num",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "work_orders_rel.status_rel",
            "title": "Work Orders Rel.Status Rel",
            "dataIndex": "work_orders_rel.status_rel",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "work_orders_rel.status_rel",
            "title": "Work Orders Rel.Status Rel.Name",
            "dataIndex": "work_orders_rel.status_rel.name",
            "type": "select",
            "sortable": true
      },
      {
            "key": "invoiceApprovalReq",
            "title": "Invoice Approval Req",
            "dataIndex": "invoice_approval_req",
            "type": "text",
            "sortable": true
      },
      {
            "key": "approvedByRel",
            "title": "Approved By Rel",
            "dataIndex": "approved_by_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "creatorRel",
            "title": "Creator Rel",
            "dataIndex": "creator_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "fieldTechRel",
            "title": "Field Tech Rel",
            "dataIndex": "field_tech_rel",
            "type": "select"
      },
      {
            "key": "salesPersonRel",
            "title": "Sales Person Rel",
            "dataIndex": "sales_person_rel",
            "type": "select"
      },
      {
            "key": "work_orders_rel.customers_rel",
            "title": "Work Orders Rel.Customers Rel.Customer",
            "dataIndex": "work_orders_rel.customers_rel.customer",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "dateService",
            "title": "Date Service",
            "dataIndex": "date_service",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "location",
            "title": "Location",
            "dataIndex": "location",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "invoiceSummary",
            "title": "Invoice Summary",
            "dataIndex": "invoice_summary",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "work_orders_rel.work_done",
            "title": "Work Orders Rel.Work Done",
            "dataIndex": "work_orders_rel.work_done",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "parts_rel.part_num",
            "title": "Parts Rel.Part Num",
            "dataIndex": "parts_rel.part_num",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "partId",
            "title": "Part Id",
            "dataIndex": "part_id",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "structures_rel.structure_str",
            "title": "Structures Rel.Structure Str",
            "dataIndex": "structures_rel.structure_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "power_units_rel.power_unit_str",
            "title": "Power Units Rel.Power Unit Str",
            "dataIndex": "power_units_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number",
            "sortable": true
      },
      {
            "key": "price",
            "title": "Price",
            "dataIndex": "price",
            "type": "number",
            "sortable": true
      },
      {
            "key": "costBeforeTax",
            "title": "Cost Before Tax",
            "dataIndex": "cost_before_tax",
            "type": "number",
            "sortable": true
      },
      {
            "key": "salesTaxRate",
            "title": "Sales Tax Rate",
            "dataIndex": "sales_tax_rate",
            "type": "text",
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "sortable": true
      },
      {
            "key": "county_rel.name",
            "title": "County Rel.Name",
            "dataIndex": "county_rel.name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "work_orders_rel.currency_rel",
            "title": "Work Orders Rel.Currency Rel.Name",
            "dataIndex": "work_orders_rel.currency_rel.name",
            "type": "select",
            "sortable": true
      },
      {
            "key": "salesTaxPartAmount",
            "title": "Sales Tax Part Amount",
            "dataIndex": "sales_tax_part_amount",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "quickbooksNum",
            "title": "Quickbooks Num",
            "dataIndex": "quickbooks_num",
            "type": "text",
            "required": true
      },
      {
            "key": "work_orders_rel.status_rel",
            "title": "Work Orders Rel.Status Rel",
            "dataIndex": "work_orders_rel.status_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true,
      "export": true
}
  },
  {
    name: "contact_form",
    list: "/admin/contact_form/contact_form",
    create: "/admin/contact_form/contact_form",
    edit: "/admin/contact_form/contact_form",
    show: "/admin/contact_form/contact_form",
    meta: {
      label: "Contact Form Submissions",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "message",
            "title": "Message",
            "dataIndex": "message",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "firstName",
            "title": "First Name",
            "dataIndex": "first_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "lastName",
            "title": "Last Name",
            "dataIndex": "last_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "phone",
            "title": "Phone",
            "dataIndex": "phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "email",
            "title": "Email",
            "dataIndex": "email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "required": true
      },
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "message",
            "title": "Message",
            "dataIndex": "message",
            "type": "textarea",
            "required": true
      },
      {
            "key": "firstName",
            "title": "First Name",
            "dataIndex": "first_name",
            "type": "text",
            "required": true
      },
      {
            "key": "lastName",
            "title": "Last Name",
            "dataIndex": "last_name",
            "type": "text",
            "required": true
      },
      {
            "key": "phone",
            "title": "Phone",
            "dataIndex": "phone",
            "type": "text",
            "required": true
      },
      {
            "key": "email",
            "title": "Email",
            "dataIndex": "email",
            "type": "email",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "service_requests",
    list: "/admin/service_requests/service_requests",
    create: "/admin/service_requests/service_requests",
    edit: "/admin/service_requests/service_requests",
    show: "/admin/service_requests/service_requests",
    meta: {
      label: "Service Requests",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "priority",
            "title": "Priority",
            "dataIndex": "priority",
            "type": "text",
            "sortable": true
      },
      {
            "key": "users_rel.email",
            "title": "Users Rel.Email",
            "dataIndex": "users_rel.email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "isResolved",
            "title": "Is Resolved",
            "dataIndex": "is_resolved",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "resolution",
            "title": "Resolution",
            "dataIndex": "resolution",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "operatorName",
            "title": "Operator Name",
            "dataIndex": "operator_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "operatorPhone",
            "title": "Operator Phone",
            "dataIndex": "operator_phone",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "operatorEmail",
            "title": "Operator Email",
            "dataIndex": "operator_email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "service_type_rel.name",
            "title": "Service Type Rel.Name",
            "dataIndex": "service_type_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "structures_rel.power_unit_str",
            "title": "Structures Rel.Power Unit Str",
            "dataIndex": "structures_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "work_orders_rel.id",
            "title": "Work Orders Rel.Id",
            "dataIndex": "work_orders_rel.id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "required": true
      },
      {
            "key": "priority",
            "title": "Priority",
            "dataIndex": "priority",
            "type": "text",
            "required": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "isResolved",
            "title": "Is Resolved",
            "dataIndex": "is_resolved",
            "type": "boolean",
            "required": true
      },
      {
            "key": "resolution",
            "title": "Resolution",
            "dataIndex": "resolution",
            "type": "text",
            "required": true
      },
      {
            "key": "operatorName",
            "title": "Operator Name",
            "dataIndex": "operator_name",
            "type": "text",
            "required": true
      },
      {
            "key": "operatorPhone",
            "title": "Operator Phone",
            "dataIndex": "operator_phone",
            "type": "text",
            "required": true
      },
      {
            "key": "operatorEmail",
            "title": "Operator Email",
            "dataIndex": "operator_email",
            "type": "email",
            "required": true
      },
      {
            "key": "serviceTypeRel",
            "title": "Service Type Rel",
            "dataIndex": "service_type_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "workOrdersRel",
            "title": "Work Orders Rel",
            "dataIndex": "work_orders_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "service_request_email_recipients",
    list: "/admin/service_request_email_recipients/service_request_email_recipients",
    create: "/admin/service_request_email_recipients/service_request_email_recipients",
    edit: "/admin/service_request_email_recipients/service_request_email_recipients",
    show: "/admin/service_request_email_recipients/service_request_email_recipients",
    meta: {
      label: "Service Request Email Recipients",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "maintenance",
    list: "/admin/maintenance/maintenance",
    create: "/admin/maintenance/maintenance",
    edit: "/admin/maintenance/maintenance",
    show: "/admin/maintenance/maintenance",
    meta: {
      label: "Maintenance Records",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "structures_rel.power_unit_str",
            "title": "Structures Rel.Power Unit Str",
            "dataIndex": "structures_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcInserted",
            "title": "Timestamp Utc Inserted",
            "dataIndex": "timestamp_utc_inserted",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "opHours",
            "title": "Op Hours",
            "dataIndex": "op_hours",
            "type": "text",
            "sortable": true
      },
      {
            "key": "opMonths",
            "title": "Op Months",
            "dataIndex": "op_months",
            "type": "text",
            "sortable": true
      },
      {
            "key": "maintenance_type_rel.name",
            "title": "Maintenance Type Rel.Name",
            "dataIndex": "maintenance_type_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "users_rel.email",
            "title": "Users Rel.Email",
            "dataIndex": "users_rel.email",
            "type": "email",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      }
],
    formFields: [
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "opHours",
            "title": "Op Hours",
            "dataIndex": "op_hours",
            "type": "text",
            "required": true
      },
      {
            "key": "maintenanceTypeRel",
            "title": "Maintenance Type Rel",
            "dataIndex": "maintenance_type_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "maintenance_types",
    list: "/admin/maintenance_types/maintenance_types",
    create: "/admin/maintenance_types/maintenance_types",
    edit: "/admin/maintenance_types/maintenance_types",
    show: "/admin/maintenance_types/maintenance_types",
    meta: {
      label: "Maintenance Types",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "service_clock",
    list: "/admin/service_clock/service_clock",
    create: "/admin/service_clock/service_clock",
    edit: "/admin/service_clock/service_clock",
    show: "/admin/service_clock/service_clock",
    meta: {
      label: "Service Clock",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "employeeRel",
            "title": "Employee Rel",
            "dataIndex": "employee_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "structureRel",
            "title": "Structure Rel",
            "dataIndex": "structure_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "warehouseRel",
            "title": "Warehouse Rel",
            "dataIndex": "warehouse_rel",
            "type": "select"
      },
      {
            "key": "structure_rel.power_unit_str",
            "title": "Structure Rel.Power Unit Str",
            "dataIndex": "structure_rel.power_unit_str",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "structure_rel.surface",
            "title": "Structure Rel.Surface",
            "dataIndex": "structure_rel.surface",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "timestampUtcIn",
            "title": "Timestamp Utc In",
            "dataIndex": "timestamp_utc_in",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timestampUtcOut",
            "title": "Timestamp Utc Out",
            "dataIndex": "timestamp_utc_out",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "timeZoneInRel",
            "title": "Time Zone In Rel",
            "dataIndex": "time_zone_in_rel",
            "type": "datetime"
      },
      {
            "key": "timeZoneOutRel",
            "title": "Time Zone Out Rel",
            "dataIndex": "time_zone_out_rel",
            "type": "datetime"
      },
      {
            "key": "totalHoursWorked",
            "title": "Total Hours Worked",
            "dataIndex": "total_hours_worked",
            "type": "number",
            "sortable": true
      },
      {
            "key": "gpsLatIn",
            "title": "Gps Lat In",
            "dataIndex": "gps_lat_in",
            "type": "text",
            "sortable": true
      },
      {
            "key": "gpsLonIn",
            "title": "Gps Lon In",
            "dataIndex": "gps_lon_in",
            "type": "text",
            "sortable": true
      },
      {
            "key": "gpsLatOut",
            "title": "Gps Lat Out",
            "dataIndex": "gps_lat_out",
            "type": "text",
            "sortable": true
      },
      {
            "key": "gpsLonOut",
            "title": "Gps Lon Out",
            "dataIndex": "gps_lon_out",
            "type": "text",
            "sortable": true
      },
      {
            "key": "notesIn",
            "title": "Notes In",
            "dataIndex": "notes_in",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "notesOut",
            "title": "Notes Out",
            "dataIndex": "notes_out",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "work_order_service_type",
    list: "/admin/work_order_service_type/work_order_service_type",
    create: "/admin/work_order_service_type/work_order_service_type",
    edit: "/admin/work_order_service_type/work_order_service_type",
    show: "/admin/work_order_service_type/work_order_service_type",
    meta: {
      label: "Work Order Service Type",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text"
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "work_order_status",
    list: "/admin/work_order_status/work_order_status",
    create: "/admin/work_order_status/work_order_status",
    edit: "/admin/work_order_status/work_order_status",
    show: "/admin/work_order_status/work_order_status",
    meta: {
      label: "Work Order Status",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text"
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "currencies",
    list: "/admin/currencies/currencies",
    create: "/admin/currencies/currencies",
    edit: "/admin/currencies/currencies",
    show: "/admin/currencies/currencies",
    meta: {
      label: "Currencies",
      category: "Service"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text"
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea"
      },
      {
            "key": "fxRateCadPer",
            "title": "Fx Rate Cad Per",
            "dataIndex": "fx_rate_cad_per",
            "type": "text"
      },
      {
            "key": "country_rel.country_name",
            "title": "Country Rel.Country Name",
            "dataIndex": "country_rel.country_name",
            "type": "number"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "fxRateCadPer",
            "title": "Fx Rate Cad Per",
            "dataIndex": "fx_rate_cad_per",
            "type": "text",
            "required": true
      },
      {
            "key": "countryRel",
            "title": "Country Rel",
            "dataIndex": "country_rel",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
];

export default serviceResources;
