// Auto-generated refine admin resources for Other category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const otherResources: ResourceProps[] = [
  {
    name: "redis_cli",
    list: "/admin/redis_cli/redis_cli",
    create: "/admin/redis_cli/redis_cli",
    edit: "/admin/redis_cli/redis_cli",
    show: "/admin/redis_cli/redis_cli",
    meta: {
      label: "Redis DB Command Line Interface",
      category: "Other"
    }

  },
  {
    name: "meta_data",
    list: "/admin/meta_data/meta_data",
    create: "/admin/meta_data/meta_data",
    edit: "/admin/meta_data/meta_data",
    show: "/admin/meta_data/meta_data",
    meta: {
      label: "Meta Data",
      category: "Other"
    }
,
    listFields: [
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      },
      {
            "key": "idCell",
            "title": "Id Cell",
            "dataIndex": "id_cell",
            "type": "number"
      },
      {
            "key": "element",
            "title": "Element",
            "dataIndex": "element",
            "type": "text"
      },
      {
            "key": "color",
            "title": "Color",
            "dataIndex": "color",
            "type": "text"
      }
],
    formFields: [
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "powerUnitsRel",
            "title": "Power Units Rel",
            "dataIndex": "power_units_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "gatewaysRel",
            "title": "Gateways Rel",
            "dataIndex": "gateways_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "wantsSms",
            "title": "Wants Sms",
            "dataIndex": "wants_sms",
            "type": "text",
            "required": true
      },
      {
            "key": "wantsEmail",
            "title": "Wants Email",
            "dataIndex": "wants_email",
            "type": "email",
            "required": true
      },
      {
            "key": "wantsPhone",
            "title": "Wants Phone",
            "dataIndex": "wants_phone",
            "type": "text",
            "required": true
      },
      {
            "key": "wantsWhatsapp",
            "title": "Wants Whatsapp",
            "dataIndex": "wants_whatsapp",
            "type": "text",
            "required": true
      },
      {
            "key": "wantsShortSms",
            "title": "Wants Short Sms",
            "dataIndex": "wants_short_sms",
            "type": "text",
            "required": true
      },
      {
            "key": "wantsShortEmail",
            "title": "Wants Short Email",
            "dataIndex": "wants_short_email",
            "type": "email",
            "required": true
      },
      {
            "key": "wantsShortPhone",
            "title": "Wants Short Phone",
            "dataIndex": "wants_short_phone",
            "type": "text",
            "required": true
      },
      {
            "key": "heartbeat",
            "title": "Heartbeat",
            "dataIndex": "heartbeat",
            "type": "text",
            "required": true
      },
      {
            "key": "onlineHb",
            "title": "Online Hb",
            "dataIndex": "online_hb",
            "type": "text",
            "required": true
      },
      {
            "key": "spm",
            "title": "Spm",
            "dataIndex": "spm",
            "type": "text",
            "required": true
      },
      {
            "key": "stboxf",
            "title": "Stboxf",
            "dataIndex": "stboxf",
            "type": "text",
            "required": true
      },
      {
            "key": "hydTemp",
            "title": "Hyd Temp",
            "dataIndex": "hyd_temp",
            "type": "number",
            "required": true
      },
      {
            "key": "suction",
            "title": "Suction",
            "dataIndex": "suction",
            "type": "text",
            "required": true
      },
      {
            "key": "discharge",
            "title": "Discharge",
            "dataIndex": "discharge",
            "type": "text",
            "required": true
      },
      {
            "key": "changeSuction",
            "title": "Change Suction",
            "dataIndex": "change_suction",
            "type": "text",
            "required": true
      },
      {
            "key": "changeDgp",
            "title": "Change Dgp",
            "dataIndex": "change_dgp",
            "type": "text",
            "required": true
      },
      {
            "key": "changeHydTemp",
            "title": "Change Hyd Temp",
            "dataIndex": "change_hyd_temp",
            "type": "number",
            "required": true
      },
      {
            "key": "changeHpDelta",
            "title": "Change Hp Delta",
            "dataIndex": "change_hp_delta",
            "type": "text",
            "required": true
      },
      {
            "key": "wantsCardMl",
            "title": "Wants Card Ml",
            "dataIndex": "wants_card_ml",
            "type": "text",
            "required": true
      },
      {
            "key": "hydOilLvl",
            "title": "Hyd Oil Lvl",
            "dataIndex": "hyd_oil_lvl",
            "type": "text",
            "required": true
      },
      {
            "key": "hydFiltLife",
            "title": "Hyd Filt Life",
            "dataIndex": "hyd_filt_life",
            "type": "text",
            "required": true
      },
      {
            "key": "hydOilLife",
            "title": "Hyd Oil Life",
            "dataIndex": "hyd_oil_life",
            "type": "text",
            "required": true
      },
      {
            "key": "chkMtrOvld",
            "title": "Chk Mtr Ovld",
            "dataIndex": "chk_mtr_ovld",
            "type": "text",
            "required": true
      },
      {
            "key": "pwrFail",
            "title": "Pwr Fail",
            "dataIndex": "pwr_fail",
            "type": "text",
            "required": true
      },
      {
            "key": "softStartErr",
            "title": "Soft Start Err",
            "dataIndex": "soft_start_err",
            "type": "text",
            "required": true
      },
      {
            "key": "greyWireErr",
            "title": "Grey Wire Err",
            "dataIndex": "grey_wire_err",
            "type": "text",
            "required": true
      },
      {
            "key": "ae011",
            "title": "Ae011",
            "dataIndex": "ae011",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "hours",
    list: "/admin/hours/hours",
    create: "/admin/hours/hours",
    edit: "/admin/hours/hours",
    show: "/admin/hours/hours",
    meta: {
      label: "Hours in the day",
      category: "Other"
    }
,
    listFields: [
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "hour",
            "title": "Hour",
            "dataIndex": "hour",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "hourEnding",
            "title": "Hour Ending",
            "dataIndex": "hour_ending",
            "type": "text",
            "searchable": true,
            "sortable": true
      }
],
    formFields: [
      {
            "key": "hour",
            "title": "Hour",
            "dataIndex": "hour",
            "type": "text",
            "required": true
      },
      {
            "key": "hourEnding",
            "title": "Hour Ending",
            "dataIndex": "hour_ending",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "error_logs",
    list: "/admin/error_logs/error_logs",
    create: "/admin/error_logs/error_logs",
    edit: "/admin/error_logs/error_logs",
    show: "/admin/error_logs/error_logs",
    meta: {
      label: "Error Logs",
      category: "Other"
    }
,
    listFields: [
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      },
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "userRel",
            "title": "User Rel",
            "dataIndex": "user_rel",
            "type": "select"
      },
      {
            "key": "errorType",
            "title": "Error Type",
            "dataIndex": "error_type",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "errorMessage",
            "title": "Error Message",
            "dataIndex": "error_message",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "requestUrl",
            "title": "Request Url",
            "dataIndex": "request_url",
            "type": "text",
            "searchable": true
      },
      {
            "key": "environment",
            "title": "Environment",
            "dataIndex": "environment",
            "type": "text",
            "sortable": true
      },
      {
            "key": "statusCode",
            "title": "Status Code",
            "dataIndex": "status_code",
            "type": "text",
            "sortable": true
      },
      {
            "key": "resolved",
            "title": "Resolved",
            "dataIndex": "resolved",
            "type": "text",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "resolved",
            "title": "Resolved",
            "dataIndex": "resolved",
            "type": "text",
            "required": true
      },
      {
            "key": "resolutionNotes",
            "title": "Resolution Notes",
            "dataIndex": "resolution_notes",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "export": true
}
  },
  {
    name: "slow_requests",
    list: "/admin/slow_requests/slow_requests",
    create: "/admin/slow_requests/slow_requests",
    edit: "/admin/slow_requests/slow_requests",
    show: "/admin/slow_requests/slow_requests",
    meta: {
      label: "Website Slow Requests",
      category: "Other"
    }
,
    listFields: [
      {
            "key": "fullName",
            "title": "Full Name",
            "dataIndex": "full_name",
            "type": "text"
      },
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text"
      },
      {
            "key": "statusCode",
            "title": "Status Code",
            "dataIndex": "status_code",
            "type": "text"
      },
      {
            "key": "timestampUtcStarted",
            "title": "Timestamp Utc Started",
            "dataIndex": "timestamp_utc_started",
            "type": "datetime"
      },
      {
            "key": "timestampUtcEnded",
            "title": "Timestamp Utc Ended",
            "dataIndex": "timestamp_utc_ended",
            "type": "datetime"
      },
      {
            "key": "elapsed",
            "title": "Elapsed",
            "dataIndex": "elapsed",
            "type": "text"
      },
      {
            "key": "cpuStart",
            "title": "Cpu Start",
            "dataIndex": "cpu_start",
            "type": "text"
      },
      {
            "key": "cpuEnd",
            "title": "Cpu End",
            "dataIndex": "cpu_end",
            "type": "text"
      },
      {
            "key": "endpointName",
            "title": "Endpoint Name",
            "dataIndex": "endpoint_name",
            "type": "text"
      },
      {
            "key": "referrer",
            "title": "Referrer",
            "dataIndex": "referrer",
            "type": "text"
      },
      {
            "key": "method",
            "title": "Method",
            "dataIndex": "method",
            "type": "text"
      },
      {
            "key": "args",
            "title": "Args",
            "dataIndex": "args",
            "type": "text"
      },
      {
            "key": "kwargs",
            "title": "Kwargs",
            "dataIndex": "kwargs",
            "type": "text"
      },
      {
            "key": "queryString",
            "title": "Query String",
            "dataIndex": "query_string",
            "type": "text"
      },
      {
            "key": "form",
            "title": "Form",
            "dataIndex": "form",
            "type": "text"
      },
      {
            "key": "ip",
            "title": "Ip",
            "dataIndex": "ip",
            "type": "text"
      },
      {
            "key": "files",
            "title": "Files",
            "dataIndex": "files",
            "type": "text"
      },
      {
            "key": "path",
            "title": "Path",
            "dataIndex": "path",
            "type": "text"
      },
      {
            "key": "requestArgs",
            "title": "Request Args",
            "dataIndex": "request_args",
            "type": "text"
      },
      {
            "key": "scheme",
            "title": "Scheme",
            "dataIndex": "scheme",
            "type": "text"
      },
      {
            "key": "userAgent",
            "title": "User Agent",
            "dataIndex": "user_agent",
            "type": "text"
      },
      {
            "key": "body",
            "title": "Body",
            "dataIndex": "body",
            "type": "text"
      },
      {
            "key": "headers",
            "title": "Headers",
            "dataIndex": "headers",
            "type": "text"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "website_views",
    list: "/admin/website_views/website_views",
    create: "/admin/website_views/website_views",
    edit: "/admin/website_views/website_views",
    show: "/admin/website_views/website_views",
    meta: {
      label: "Website Views",
      category: "Other"
    }
,
    listFields: [
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime",
            "searchable": true
      },
      {
            "key": "page",
            "title": "Page",
            "dataIndex": "page",
            "type": "text",
            "searchable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select"
      },
      {
            "key": "company",
            "title": "Company",
            "dataIndex": "company",
            "type": "text",
            "searchable": true
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text",
            "searchable": true
      },
      {
            "key": "region",
            "title": "Region",
            "dataIndex": "region",
            "type": "text",
            "searchable": true
      },
      {
            "key": "timezone",
            "title": "Timezone",
            "dataIndex": "timezone",
            "type": "datetime",
            "searchable": true
      },
      {
            "key": "countryCode",
            "title": "Country Code",
            "dataIndex": "country_code",
            "type": "number",
            "searchable": true
      },
      {
            "key": "countryName",
            "title": "Country Name",
            "dataIndex": "country_name",
            "type": "number",
            "searchable": true
      },
      {
            "key": "ip",
            "title": "Ip",
            "dataIndex": "ip",
            "type": "text",
            "searchable": true
      },
      {
            "key": "postal",
            "title": "Postal",
            "dataIndex": "postal",
            "type": "text",
            "searchable": true
      },
      {
            "key": "gpsLat",
            "title": "Gps Lat",
            "dataIndex": "gps_lat",
            "type": "text",
            "searchable": true
      },
      {
            "key": "gpsLon",
            "title": "Gps Lon",
            "dataIndex": "gps_lon",
            "type": "text",
            "searchable": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "website_most_active_users",
    list: "/admin/website_most_active_users/website_most_active_users",
    create: "/admin/website_most_active_users/website_most_active_users",
    edit: "/admin/website_most_active_users/website_most_active_users",
    show: "/admin/website_most_active_users/website_most_active_users",
    meta: {
      label: "Website Most Active Users",
      category: "Other"
    }
,
    listFields: [
      {
            "key": "page",
            "title": "Page",
            "dataIndex": "page",
            "type": "text"
      },
      {
            "key": "count",
            "title": "Count ",
            "dataIndex": "count_",
            "type": "number"
      },
      {
            "key": "customer",
            "title": "Customer",
            "dataIndex": "customer",
            "type": "text"
      },
      {
            "key": "firstName",
            "title": "First Name",
            "dataIndex": "first_name",
            "type": "text"
      },
      {
            "key": "lastName",
            "title": "Last Name",
            "dataIndex": "last_name",
            "type": "text"
      },
      {
            "key": "email",
            "title": "Email",
            "dataIndex": "email",
            "type": "email"
      },
      {
            "key": "phone",
            "title": "Phone",
            "dataIndex": "phone",
            "type": "text"
      },
      {
            "key": "earliestDateInSample",
            "title": "Earliest Date In Sample",
            "dataIndex": "earliest_date_in_sample",
            "type": "datetime"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "website_most_active_users_filterable",
    list: "/admin/website_most_active_users_filterable/website_most_active_users_filterable",
    create: "/admin/website_most_active_users_filterable/website_most_active_users_filterable",
    edit: "/admin/website_most_active_users_filterable/website_most_active_users_filterable",
    show: "/admin/website_most_active_users_filterable/website_most_active_users_filterable",
    meta: {
      label: "Website Most Active Users - filterable",
      category: "Other"
    }
,
    listFields: [
      {
            "key": "timestampUtc",
            "title": "Timestamp Utc",
            "dataIndex": "timestamp_utc",
            "type": "datetime"
      },
      {
            "key": "countRecords",
            "title": "Count Records",
            "dataIndex": "count_records",
            "type": "number"
      },
      {
            "key": "page",
            "title": "Page",
            "dataIndex": "page",
            "type": "text",
            "searchable": true
      },
      {
            "key": "users_rel.customers_rel",
            "title": "Users Rel.Customers Rel",
            "dataIndex": "users_rel.customers_rel",
            "type": "select",
            "searchable": true
      },
      {
            "key": "usersRel",
            "title": "Users Rel",
            "dataIndex": "users_rel",
            "type": "select"
      },
      {
            "key": "users_rel.first_name",
            "title": "Users Rel.First Name",
            "dataIndex": "users_rel.first_name",
            "type": "select",
            "searchable": true
      },
      {
            "key": "users_rel.last_name",
            "title": "Users Rel.Last Name",
            "dataIndex": "users_rel.last_name",
            "type": "select",
            "searchable": true
      },
      {
            "key": "users_rel.email",
            "title": "Users Rel.Email",
            "dataIndex": "users_rel.email",
            "type": "email",
            "searchable": true
      },
      {
            "key": "users_rel.phone",
            "title": "Users Rel.Phone",
            "dataIndex": "users_rel.phone",
            "type": "text",
            "searchable": true
      },
      {
            "key": "company",
            "title": "Company",
            "dataIndex": "company",
            "type": "text"
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text"
      },
      {
            "key": "region",
            "title": "Region",
            "dataIndex": "region",
            "type": "text"
      },
      {
            "key": "timezone",
            "title": "Timezone",
            "dataIndex": "timezone",
            "type": "datetime"
      },
      {
            "key": "countryCode",
            "title": "Country Code",
            "dataIndex": "country_code",
            "type": "number"
      },
      {
            "key": "countryName",
            "title": "Country Name",
            "dataIndex": "country_name",
            "type": "number"
      },
      {
            "key": "ip",
            "title": "Ip",
            "dataIndex": "ip",
            "type": "text"
      },
      {
            "key": "postal",
            "title": "Postal",
            "dataIndex": "postal",
            "type": "text"
      },
      {
            "key": "gpsLat",
            "title": "Gps Lat",
            "dataIndex": "gps_lat",
            "type": "text"
      },
      {
            "key": "gpsLon",
            "title": "Gps Lon",
            "dataIndex": "gps_lon",
            "type": "text"
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
  {
    name: "images",
    list: "/admin/images/images",
    create: "/admin/images/images",
    edit: "/admin/images/images",
    show: "/admin/images/images",
    meta: {
      label: "Images",
      category: "Other"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true
      },
      {
            "key": "image",
            "title": "Image",
            "dataIndex": "image",
            "type": "text"
      },
      {
            "key": "format",
            "title": "Format",
            "dataIndex": "format",
            "type": "text"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "image",
            "title": "Image",
            "dataIndex": "image",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true
}
  },
];

export default otherResources;
