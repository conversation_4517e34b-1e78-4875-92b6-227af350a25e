// Auto-generated refine admin resources for Inventory category
// Generated from Flask admin extraction

import { ResourceProps } from "@refinedev/core";

export const inventoryResources: ResourceProps[] = [
  {
    name: "inventory_warehouses",
    list: "/admin/inventory_warehouses/inventory_warehouses",
    create: "/admin/inventory_warehouses/inventory_warehouses",
    edit: "/admin/inventory_warehouses/inventory_warehouses",
    show: "/admin/inventory_warehouses/inventory_warehouses",
    meta: {
      label: "Warehouses",
      category: "Inventory"
    }
,
    listFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "isActive",
            "title": "Is Active",
            "dataIndex": "is_active",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "allowsNegativeStock",
            "title": "Allows Negative Stock",
            "dataIndex": "allows_negative_stock",
            "type": "text",
            "sortable": true
      },
      {
            "key": "isMainWarehouse",
            "title": "Is Main Warehouse",
            "dataIndex": "is_main_warehouse",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "canShowToCustomers",
            "title": "Can Show To Customers",
            "dataIndex": "can_show_to_customers",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "address",
            "title": "Address",
            "dataIndex": "address",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "zipCode",
            "title": "Zip Code",
            "dataIndex": "zip_code",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "province_rel.name",
            "title": "Province Rel.Name",
            "dataIndex": "province_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "country_rel.country_name",
            "title": "Country Rel.Country Name",
            "dataIndex": "country_rel.country_name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "time_zone_rel.time_zone",
            "title": "Time Zone Rel.Time Zone",
            "dataIndex": "time_zone_rel.time_zone",
            "type": "datetime",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "sortable": true
      },
      {
            "key": "countryRel",
            "title": "Country Rel",
            "dataIndex": "country_rel",
            "type": "number",
            "sortable": true
      },
      {
            "key": "timeZoneRel",
            "title": "Time Zone Rel",
            "dataIndex": "time_zone_rel",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "gpsLat",
            "title": "Gps Lat",
            "dataIndex": "gps_lat",
            "type": "text",
            "sortable": true
      },
      {
            "key": "gpsLon",
            "title": "Gps Lon",
            "dataIndex": "gps_lon",
            "type": "text",
            "sortable": true
      },
      {
            "key": "structures_rel.structure",
            "title": "Structures Rel.Structure",
            "dataIndex": "structures_rel.structure",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number"
      }
],
    formFields: [
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "description",
            "title": "Description",
            "dataIndex": "description",
            "type": "textarea",
            "required": true
      },
      {
            "key": "isActive",
            "title": "Is Active",
            "dataIndex": "is_active",
            "type": "boolean",
            "required": true
      },
      {
            "key": "allowsNegativeStock",
            "title": "Allows Negative Stock",
            "dataIndex": "allows_negative_stock",
            "type": "text",
            "required": true
      },
      {
            "key": "isMainWarehouse",
            "title": "Is Main Warehouse",
            "dataIndex": "is_main_warehouse",
            "type": "boolean",
            "required": true
      },
      {
            "key": "canShowToCustomers",
            "title": "Can Show To Customers",
            "dataIndex": "can_show_to_customers",
            "type": "boolean",
            "required": true
      },
      {
            "key": "address",
            "title": "Address",
            "dataIndex": "address",
            "type": "text",
            "required": true
      },
      {
            "key": "city",
            "title": "City",
            "dataIndex": "city",
            "type": "text",
            "required": true
      },
      {
            "key": "zipCode",
            "title": "Zip Code",
            "dataIndex": "zip_code",
            "type": "text",
            "required": true
      },
      {
            "key": "provinceRel",
            "title": "Province Rel",
            "dataIndex": "province_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "countryRel",
            "title": "Country Rel",
            "dataIndex": "country_rel",
            "type": "number",
            "required": true
      },
      {
            "key": "timeZoneRel",
            "title": "Time Zone Rel",
            "dataIndex": "time_zone_rel",
            "type": "datetime",
            "required": true
      },
      {
            "key": "gpsLat",
            "title": "Gps Lat",
            "dataIndex": "gps_lat",
            "type": "text",
            "required": true
      },
      {
            "key": "gpsLon",
            "title": "Gps Lon",
            "dataIndex": "gps_lon",
            "type": "text",
            "required": true
      },
      {
            "key": "structuresRel",
            "title": "Structures Rel",
            "dataIndex": "structures_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "export": true
}
  },
  {
    name: "warehouse_locations",
    list: "/admin/warehouse_locations/warehouse_locations",
    create: "/admin/warehouse_locations/warehouse_locations",
    edit: "/admin/warehouse_locations/warehouse_locations",
    show: "/admin/warehouse_locations/warehouse_locations",
    meta: {
      label: "Warehouse Locations",
      category: "Inventory"
    }
,
    listFields: [
      {
            "key": "warehouse_rel.name",
            "title": "Warehouse Rel.Name",
            "dataIndex": "warehouse_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "locationCode",
            "title": "Location Code",
            "dataIndex": "location_code",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "locationName",
            "title": "Location Name",
            "dataIndex": "location_name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "locationType",
            "title": "Location Type",
            "dataIndex": "location_type",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "parent_location_rel.location_code",
            "title": "Parent Location Rel.Location Code",
            "dataIndex": "parent_location_rel.location_code",
            "type": "select"
      },
      {
            "key": "isActive",
            "title": "Is Active",
            "dataIndex": "is_active",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "isPickable",
            "title": "Is Pickable",
            "dataIndex": "is_pickable",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "isReceivable",
            "title": "Is Receivable",
            "dataIndex": "is_receivable",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "maxWeightKg",
            "title": "Max Weight Kg",
            "dataIndex": "max_weight_kg",
            "type": "text",
            "sortable": true
      },
      {
            "key": "maxVolumeM3",
            "title": "Max Volume M3",
            "dataIndex": "max_volume_m3",
            "type": "number",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "warehouseRel",
            "title": "Warehouse Rel",
            "dataIndex": "warehouse_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "locationCode",
            "title": "Location Code",
            "dataIndex": "location_code",
            "type": "text",
            "required": true
      },
      {
            "key": "locationName",
            "title": "Location Name",
            "dataIndex": "location_name",
            "type": "text",
            "required": true
      },
      {
            "key": "locationType",
            "title": "Location Type",
            "dataIndex": "location_type",
            "type": "text",
            "required": true
      },
      {
            "key": "parentLocationRel",
            "title": "Parent Location Rel",
            "dataIndex": "parent_location_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "isActive",
            "title": "Is Active",
            "dataIndex": "is_active",
            "type": "boolean",
            "required": true
      },
      {
            "key": "isPickable",
            "title": "Is Pickable",
            "dataIndex": "is_pickable",
            "type": "boolean",
            "required": true
      },
      {
            "key": "isReceivable",
            "title": "Is Receivable",
            "dataIndex": "is_receivable",
            "type": "boolean",
            "required": true
      },
      {
            "key": "maxWeightKg",
            "title": "Max Weight Kg",
            "dataIndex": "max_weight_kg",
            "type": "text",
            "required": true
      },
      {
            "key": "maxVolumeM3",
            "title": "Max Volume M3",
            "dataIndex": "max_volume_m3",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "export": true
}
  },
  {
    name: "inventory_warehouse_parts",
    list: "/admin/inventory_warehouse_parts/inventory_warehouse_parts",
    create: "/admin/inventory_warehouse_parts/inventory_warehouse_parts",
    edit: "/admin/inventory_warehouse_parts/inventory_warehouse_parts",
    show: "/admin/inventory_warehouse_parts/inventory_warehouse_parts",
    meta: {
      label: "Warehouse Parts (Current Stock)",
      category: "Inventory"
    }
,
    listFields: [
      {
            "key": "warehouse_rel.name",
            "title": "Warehouse Rel.Name",
            "dataIndex": "warehouse_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "part_rel.part_num",
            "title": "Part Rel.Part Num",
            "dataIndex": "part_rel.part_num",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "part_rel.description",
            "title": "Part Rel.Description",
            "dataIndex": "part_rel.description",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number",
            "sortable": true
      },
      {
            "key": "quantityReserved",
            "title": "Quantity Reserved",
            "dataIndex": "quantity_reserved",
            "type": "number",
            "sortable": true
      },
      {
            "key": "quantityAvailable",
            "title": "Quantity Available",
            "dataIndex": "quantity_available",
            "type": "number",
            "sortable": true
      },
      {
            "key": "quantityDesired",
            "title": "Quantity Desired",
            "dataIndex": "quantity_desired",
            "type": "number",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "warehouseRel",
            "title": "Warehouse Rel",
            "dataIndex": "warehouse_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number",
            "required": true
      },
      {
            "key": "quantityReserved",
            "title": "Quantity Reserved",
            "dataIndex": "quantity_reserved",
            "type": "number",
            "required": true
      },
      {
            "key": "quantityDesired",
            "title": "Quantity Desired",
            "dataIndex": "quantity_desired",
            "type": "number",
            "required": true
      }
],
    permissions: {
      "export": true
}
  },
  {
    name: "inventory_movements",
    list: "/admin/inventory_movements/inventory_movements",
    create: "/admin/inventory_movements/inventory_movements",
    edit: "/admin/inventory_movements/inventory_movements",
    show: "/admin/inventory_movements/inventory_movements",
    meta: {
      label: "Inventory Movements (Audit Trail)",
      category: "Inventory"
    }
,
    listFields: [
      {
            "key": "movementNumber",
            "title": "Movement Number",
            "dataIndex": "movement_number",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "movementType",
            "title": "Movement Type",
            "dataIndex": "movement_type",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "movementDate",
            "title": "Movement Date",
            "dataIndex": "movement_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "part_rel.part_num",
            "title": "Part Rel.Part Num",
            "dataIndex": "part_rel.part_num",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "part_rel.description",
            "title": "Part Rel.Description",
            "dataIndex": "part_rel.description",
            "type": "select",
            "searchable": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number",
            "sortable": true
      },
      {
            "key": "unitCost",
            "title": "Unit Cost",
            "dataIndex": "unit_cost",
            "type": "number",
            "sortable": true
      },
      {
            "key": "totalCost",
            "title": "Total Cost",
            "dataIndex": "total_cost",
            "type": "number",
            "sortable": true
      },
      {
            "key": "from_warehouse_rel.name",
            "title": "From Warehouse Rel.Name",
            "dataIndex": "from_warehouse_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "to_warehouse_rel.name",
            "title": "To Warehouse Rel.Name",
            "dataIndex": "to_warehouse_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "referenceType",
            "title": "Reference Type",
            "dataIndex": "reference_type",
            "type": "text",
            "sortable": true
      },
      {
            "key": "referenceNumber",
            "title": "Reference Number",
            "dataIndex": "reference_number",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "createdByRel",
            "title": "Created By Rel",
            "dataIndex": "created_by_rel",
            "type": "select"
      },
      {
            "key": "isReversed",
            "title": "Is Reversed",
            "dataIndex": "is_reversed",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "movementNumber",
            "title": "Movement Number",
            "dataIndex": "movement_number",
            "type": "number",
            "required": true
      },
      {
            "key": "movementType",
            "title": "Movement Type",
            "dataIndex": "movement_type",
            "type": "text",
            "required": true
      },
      {
            "key": "movementDate",
            "title": "Movement Date",
            "dataIndex": "movement_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "quantity",
            "title": "Quantity",
            "dataIndex": "quantity",
            "type": "number",
            "required": true
      },
      {
            "key": "unitCost",
            "title": "Unit Cost",
            "dataIndex": "unit_cost",
            "type": "number",
            "required": true
      },
      {
            "key": "totalCost",
            "title": "Total Cost",
            "dataIndex": "total_cost",
            "type": "number",
            "required": true
      },
      {
            "key": "fromWarehouseRel",
            "title": "From Warehouse Rel",
            "dataIndex": "from_warehouse_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "toWarehouseRel",
            "title": "To Warehouse Rel",
            "dataIndex": "to_warehouse_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "referenceType",
            "title": "Reference Type",
            "dataIndex": "reference_type",
            "type": "text",
            "required": true
      },
      {
            "key": "referenceId",
            "title": "Reference Id",
            "dataIndex": "reference_id",
            "type": "number",
            "required": true
      },
      {
            "key": "referenceNumber",
            "title": "Reference Number",
            "dataIndex": "reference_number",
            "type": "number",
            "required": true
      },
      {
            "key": "lotNumber",
            "title": "Lot Number",
            "dataIndex": "lot_number",
            "type": "number",
            "required": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "required": true
      },
      {
            "key": "reasonCode",
            "title": "Reason Code",
            "dataIndex": "reason_code",
            "type": "text",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true,
      "export": true
}
  },
  {
    name: "inventory_reservations",
    list: "/admin/inventory_reservations/inventory_reservations",
    create: "/admin/inventory_reservations/inventory_reservations",
    edit: "/admin/inventory_reservations/inventory_reservations",
    show: "/admin/inventory_reservations/inventory_reservations",
    meta: {
      label: "Inventory Reservations",
      category: "Inventory"
    }
,
    listFields: [
      {
            "key": "reservationNumber",
            "title": "Reservation Number",
            "dataIndex": "reservation_number",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "status",
            "title": "Status",
            "dataIndex": "status",
            "type": "text",
            "sortable": true
      },
      {
            "key": "part_rel.part_num",
            "title": "Part Rel.Part Num",
            "dataIndex": "part_rel.part_num",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "part_rel.description",
            "title": "Part Rel.Description",
            "dataIndex": "part_rel.description",
            "type": "select",
            "searchable": true
      },
      {
            "key": "warehouse_rel.name",
            "title": "Warehouse Rel.Name",
            "dataIndex": "warehouse_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "quantityReserved",
            "title": "Quantity Reserved",
            "dataIndex": "quantity_reserved",
            "type": "number",
            "sortable": true
      },
      {
            "key": "quantityFulfilled",
            "title": "Quantity Fulfilled",
            "dataIndex": "quantity_fulfilled",
            "type": "number",
            "sortable": true
      },
      {
            "key": "quantityRemaining",
            "title": "Quantity Remaining",
            "dataIndex": "quantity_remaining",
            "type": "number",
            "sortable": true
      },
      {
            "key": "reservationDate",
            "title": "Reservation Date",
            "dataIndex": "reservation_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "expiryDate",
            "title": "Expiry Date",
            "dataIndex": "expiry_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "referenceType",
            "title": "Reference Type",
            "dataIndex": "reference_type",
            "type": "text"
      },
      {
            "key": "referenceNumber",
            "title": "Reference Number",
            "dataIndex": "reference_number",
            "type": "number",
            "searchable": true
      },
      {
            "key": "priority",
            "title": "Priority",
            "dataIndex": "priority",
            "type": "text",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "reservationNumber",
            "title": "Reservation Number",
            "dataIndex": "reservation_number",
            "type": "number",
            "required": true
      },
      {
            "key": "status",
            "title": "Status",
            "dataIndex": "status",
            "type": "text",
            "required": true
      },
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "warehouseRel",
            "title": "Warehouse Rel",
            "dataIndex": "warehouse_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "quantityReserved",
            "title": "Quantity Reserved",
            "dataIndex": "quantity_reserved",
            "type": "number",
            "required": true
      },
      {
            "key": "quantityFulfilled",
            "title": "Quantity Fulfilled",
            "dataIndex": "quantity_fulfilled",
            "type": "number",
            "required": true
      },
      {
            "key": "reservationDate",
            "title": "Reservation Date",
            "dataIndex": "reservation_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "expiryDate",
            "title": "Expiry Date",
            "dataIndex": "expiry_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "referenceType",
            "title": "Reference Type",
            "dataIndex": "reference_type",
            "type": "text",
            "required": true
      },
      {
            "key": "referenceId",
            "title": "Reference Id",
            "dataIndex": "reference_id",
            "type": "number",
            "required": true
      },
      {
            "key": "referenceNumber",
            "title": "Reference Number",
            "dataIndex": "reference_number",
            "type": "number",
            "required": true
      },
      {
            "key": "priority",
            "title": "Priority",
            "dataIndex": "priority",
            "type": "text",
            "required": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "edit": true,
      "delete": true,
      "export": true
}
  },
  {
    name: "cycle_counts",
    list: "/admin/cycle_counts/cycle_counts",
    create: "/admin/cycle_counts/cycle_counts",
    edit: "/admin/cycle_counts/cycle_counts",
    show: "/admin/cycle_counts/cycle_counts",
    meta: {
      label: "Cycle Counts",
      category: "Inventory"
    }
,
    listFields: [
      {
            "key": "countNumber",
            "title": "Count Number",
            "dataIndex": "count_number",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "countName",
            "title": "Count Name",
            "dataIndex": "count_name",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "status",
            "title": "Status",
            "dataIndex": "status",
            "type": "text",
            "sortable": true
      },
      {
            "key": "warehouse_rel.name",
            "title": "Warehouse Rel.Name",
            "dataIndex": "warehouse_rel.name",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "countType",
            "title": "Count Type",
            "dataIndex": "count_type",
            "type": "number",
            "sortable": true
      },
      {
            "key": "scheduledDate",
            "title": "Scheduled Date",
            "dataIndex": "scheduled_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "startedDate",
            "title": "Started Date",
            "dataIndex": "started_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "completedDate",
            "title": "Completed Date",
            "dataIndex": "completed_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "totalItems",
            "title": "Total Items",
            "dataIndex": "total_items",
            "type": "number",
            "sortable": true
      },
      {
            "key": "countedItems",
            "title": "Counted Items",
            "dataIndex": "counted_items",
            "type": "number",
            "sortable": true
      },
      {
            "key": "varianceItems",
            "title": "Variance Items",
            "dataIndex": "variance_items",
            "type": "text",
            "sortable": true
      },
      {
            "key": "totalVarianceValue",
            "title": "Total Variance Value",
            "dataIndex": "total_variance_value",
            "type": "number",
            "sortable": true
      },
      {
            "key": "createdByRel",
            "title": "Created By Rel",
            "dataIndex": "created_by_rel",
            "type": "select"
      },
      {
            "key": "assignedToRel",
            "title": "Assigned To Rel",
            "dataIndex": "assigned_to_rel",
            "type": "select"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "countNumber",
            "title": "Count Number",
            "dataIndex": "count_number",
            "type": "number",
            "required": true
      },
      {
            "key": "countName",
            "title": "Count Name",
            "dataIndex": "count_name",
            "type": "number",
            "required": true
      },
      {
            "key": "status",
            "title": "Status",
            "dataIndex": "status",
            "type": "text",
            "required": true
      },
      {
            "key": "warehouseRel",
            "title": "Warehouse Rel",
            "dataIndex": "warehouse_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "countType",
            "title": "Count Type",
            "dataIndex": "count_type",
            "type": "number",
            "required": true
      },
      {
            "key": "scheduledDate",
            "title": "Scheduled Date",
            "dataIndex": "scheduled_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "startedDate",
            "title": "Started Date",
            "dataIndex": "started_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "completedDate",
            "title": "Completed Date",
            "dataIndex": "completed_date",
            "type": "datetime",
            "required": true
      },
      {
            "key": "abcCodes",
            "title": "Abc Codes",
            "dataIndex": "abc_codes",
            "type": "text",
            "required": true
      },
      {
            "key": "assignedToRel",
            "title": "Assigned To Rel",
            "dataIndex": "assigned_to_rel",
            "type": "select",
            "required": true
      }
],
    permissions: {
      "export": true
}
  },
  {
    name: "cycle_count_items",
    list: "/admin/cycle_count_items/cycle_count_items",
    create: "/admin/cycle_count_items/cycle_count_items",
    edit: "/admin/cycle_count_items/cycle_count_items",
    show: "/admin/cycle_count_items/cycle_count_items",
    meta: {
      label: "Cycle Count Items",
      category: "Inventory"
    }
,
    listFields: [
      {
            "key": "cycle_count_rel.count_number",
            "title": "Cycle Count Rel.Count Number",
            "dataIndex": "cycle_count_rel.count_number",
            "type": "number",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "part_rel.part_num",
            "title": "Part Rel.Part Num",
            "dataIndex": "part_rel.part_num",
            "type": "select",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "part_rel.description",
            "title": "Part Rel.Description",
            "dataIndex": "part_rel.description",
            "type": "select",
            "searchable": true
      },
      {
            "key": "systemQuantity",
            "title": "System Quantity",
            "dataIndex": "system_quantity",
            "type": "number",
            "sortable": true
      },
      {
            "key": "countedQuantity",
            "title": "Counted Quantity",
            "dataIndex": "counted_quantity",
            "type": "number",
            "sortable": true
      },
      {
            "key": "varianceQuantity",
            "title": "Variance Quantity",
            "dataIndex": "variance_quantity",
            "type": "number",
            "sortable": true
      },
      {
            "key": "unitCost",
            "title": "Unit Cost",
            "dataIndex": "unit_cost",
            "type": "number",
            "sortable": true
      },
      {
            "key": "varianceValue",
            "title": "Variance Value",
            "dataIndex": "variance_value",
            "type": "text",
            "sortable": true
      },
      {
            "key": "isCounted",
            "title": "Is Counted",
            "dataIndex": "is_counted",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "countDate",
            "title": "Count Date",
            "dataIndex": "count_date",
            "type": "datetime",
            "sortable": true
      },
      {
            "key": "countedByRel",
            "title": "Counted By Rel",
            "dataIndex": "counted_by_rel",
            "type": "number"
      },
      {
            "key": "varianceReason",
            "title": "Variance Reason",
            "dataIndex": "variance_reason",
            "type": "text",
            "searchable": true
      },
      {
            "key": "adjustmentApproved",
            "title": "Adjustment Approved",
            "dataIndex": "adjustment_approved",
            "type": "text"
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "cycleCountRel",
            "title": "Cycle Count Rel",
            "dataIndex": "cycle_count_rel",
            "type": "number",
            "required": true
      },
      {
            "key": "partRel",
            "title": "Part Rel",
            "dataIndex": "part_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "systemQuantity",
            "title": "System Quantity",
            "dataIndex": "system_quantity",
            "type": "number",
            "required": true
      },
      {
            "key": "countedQuantity",
            "title": "Counted Quantity",
            "dataIndex": "counted_quantity",
            "type": "number",
            "required": true
      },
      {
            "key": "unitCost",
            "title": "Unit Cost",
            "dataIndex": "unit_cost",
            "type": "number",
            "required": true
      },
      {
            "key": "varianceReason",
            "title": "Variance Reason",
            "dataIndex": "variance_reason",
            "type": "text",
            "required": true
      },
      {
            "key": "adjustmentApproved",
            "title": "Adjustment Approved",
            "dataIndex": "adjustment_approved",
            "type": "text",
            "required": true
      },
      {
            "key": "notes",
            "title": "Notes",
            "dataIndex": "notes",
            "type": "textarea",
            "required": true
      }
],
    permissions: {
      "create": true,
      "export": true
}
  },
  {
    name: "part_categories",
    list: "/admin/part_categories/part_categories",
    create: "/admin/part_categories/part_categories",
    edit: "/admin/part_categories/part_categories",
    show: "/admin/part_categories/part_categories",
    meta: {
      label: "Part Categories",
      category: "Inventory"
    }
,
    listFields: [
      {
            "key": "code",
            "title": "Code",
            "dataIndex": "code",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "searchable": true,
            "sortable": true
      },
      {
            "key": "parent_category_rel.code",
            "title": "Parent Category Rel.Code",
            "dataIndex": "parent_category_rel.code",
            "type": "select"
      },
      {
            "key": "abcCode",
            "title": "Abc Code",
            "dataIndex": "abc_code",
            "type": "text",
            "sortable": true
      },
      {
            "key": "countFrequencyDays",
            "title": "Count Frequency Days",
            "dataIndex": "count_frequency_days",
            "type": "number",
            "sortable": true
      },
      {
            "key": "isActive",
            "title": "Is Active",
            "dataIndex": "is_active",
            "type": "boolean",
            "sortable": true
      },
      {
            "key": "id",
            "title": "Id",
            "dataIndex": "id",
            "type": "number",
            "sortable": true
      }
],
    formFields: [
      {
            "key": "code",
            "title": "Code",
            "dataIndex": "code",
            "type": "text",
            "required": true
      },
      {
            "key": "name",
            "title": "Name",
            "dataIndex": "name",
            "type": "text",
            "required": true
      },
      {
            "key": "parentCategoryRel",
            "title": "Parent Category Rel",
            "dataIndex": "parent_category_rel",
            "type": "select",
            "required": true
      },
      {
            "key": "abcCode",
            "title": "Abc Code",
            "dataIndex": "abc_code",
            "type": "text",
            "required": true
      },
      {
            "key": "countFrequencyDays",
            "title": "Count Frequency Days",
            "dataIndex": "count_frequency_days",
            "type": "number",
            "required": true
      },
      {
            "key": "isActive",
            "title": "Is Active",
            "dataIndex": "is_active",
            "type": "boolean",
            "required": true
      }
],
    permissions: {
      "export": true
}
  },
];

export default inventoryResources;
