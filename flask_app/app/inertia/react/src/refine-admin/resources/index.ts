// Export comprehensive Flask admin-generated resources
export * from "./flask-admin-generated";

// Import the comprehensive Flask admin resources
import { allFlaskAdminResources, resourcesByCategory as generatedResourcesByCategory } from "./flask-admin-generated";

// Export all resources combined (114 comprehensive Flask admin resources)
export const allResources = allFlaskAdminResources;

// Export categorized resources for menu building
export const resourcesByCategory = Object.entries(generatedResourcesByCategory).map(([categoryName, resources]) => ({
  name: categoryName,
  resources: resources,
  icon: getCategoryIcon(categoryName),
}));

// Helper function to get category icons
function getCategoryIcon(categoryName: string): string {
  const iconMap: Record<string, string> = {
    'Alerts': '🚨',
    'BoM': '🔧',
    'Build': '🏗️',
    'Customers': '👥',
    'Inventory': '📦',
    'Machine Learning': '🤖',
    'Other': '⚙️',
    'Reports': '📊',
    'Service': '🛠️',
    'Units': '📟',
  };
  return iconMap[categoryName] || '📁';
}