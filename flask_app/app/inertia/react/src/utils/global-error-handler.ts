/**
 * Global error handler for React applications
 * 
 * In production: Shows generic error messages and provides option to report to support
 * In development: Shows detailed error information for debugging
 */

import { getApiV1Url } from '@/refine-admin/utils/api-url';

interface ErrorDetails {
  name: string;
  message: string;
  stack?: string;
  componentStack?: string;
}

interface UserInfo {
  id?: number;
  name?: string;
  email?: string;
  roles?: string[];
}

interface ErrorReportRequest {
  error_name: string;
  error_message: string;
  error_stack?: string;
  component_stack?: string;
  user_agent?: string;
  url?: string;
  timestamp?: string;
  user_info?: UserInfo;
  additional_info?: Record<string, any>;
}

interface ErrorReportResponse {
  success: boolean;
  message: string;
  error_id?: string;
}

/**
 * Check if we're running in production environment
 */
function isProduction(): boolean {
  return import.meta.env.PROD || import.meta.env.MODE === 'production';
}

/**
 * Get current user information from global context (if available)
 */
function getCurrentUserInfo(): UserInfo | undefined {
  try {
    // Try to get user info from window object (set by Inertia)
    const userInfo = (window as any).userInfo || (window as any).page?.props?.user;
    if (userInfo) {
      return {
        id: userInfo.id,
        name: userInfo.name || userInfo.full_name,
        email: userInfo.email,
        roles: userInfo.roles || userInfo.role_names,
      };
    }
  } catch (e) {
    console.warn('Could not retrieve user info for error reporting:', e);
  }
  return undefined;
}

/**
 * Send error report to FastAPI backend
 */
async function sendErrorReport(errorDetails: ErrorDetails): Promise<ErrorReportResponse> {
  try {
    const apiUrl = getApiV1Url();
    const errorReportUrl = `${apiUrl}/errors/report-error`;
    
    const payload: ErrorReportRequest = {
      error_name: errorDetails.name,
      error_message: errorDetails.message,
      error_stack: errorDetails.stack,
      component_stack: errorDetails.componentStack,
      user_agent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      user_info: getCurrentUserInfo(),
      additional_info: {
        browser_language: navigator.language,
        screen_resolution: `${screen.width}x${screen.height}`,
        viewport_size: `${window.innerWidth}x${window.innerHeight}`,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
    };

    const response = await fetch(errorReportUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include session cookies
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to send error report:', error);
    throw error;
  }
}

/**
 * Show error modal to user
 */
function showErrorModal(errorDetails: ErrorDetails): void {
  const isProd = isProduction();
  
  // Remove any existing error modal
  const existingModal = document.getElementById('global-error-modal');
  if (existingModal) {
    existingModal.remove();
  }

  // Create modal HTML
  const modalHTML = `
    <div id="global-error-modal" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    ">
      <div style="
        background: white;
        border-radius: 12px;
        padding: 32px;
        max-width: ${isProd ? '500px' : '800px'};
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        position: relative;
      ">
        <!-- Close button -->
        <button id="error-modal-close" style="
          position: absolute;
          top: 16px;
          right: 16px;
          background: none;
          border: none;
          font-size: 24px;
          cursor: pointer;
          color: #666;
          padding: 8px;
          border-radius: 4px;
        " title="Close">×</button>
        
        <!-- Error icon and title -->
        <div style="text-align: center; margin-bottom: 24px;">
          <div style="
            font-size: 48px; 
            color: #f44336; 
            margin-bottom: 16px;
          ">⚠️</div>
          <h2 style="
            margin: 0;
            color: #333;
            font-size: 24px;
            font-weight: 600;
          ">${isProd ? 'Something went wrong' : 'Application Error'}</h2>
        </div>

        <!-- Error message -->
        <div style="margin-bottom: 24px;">
          ${isProd 
            ? `
            <p style="
              color: #666; 
              line-height: 1.6; 
              margin: 0 0 16px 0;
              text-align: center;
            ">
              We apologize for the inconvenience. An unexpected error has occurred. 
              Our support team has been notified and will investigate this issue.
            </p>
            <p style="
              color: #666; 
              line-height: 1.6; 
              margin: 0;
              text-align: center;
            ">
              If you continue to experience problems, please contact IJACK support.
            </p>
            `
            : `
            <div style="margin-bottom: 16px;">
              <strong style="color: #333;">Error:</strong> 
              <span style="color: #f44336; font-family: monospace;">${errorDetails.name}</span>
            </div>
            <div style="margin-bottom: 16px;">
              <strong style="color: #333;">Message:</strong> 
              <span style="color: #666;">${errorDetails.message}</span>
            </div>
            ${errorDetails.stack ? `
            <details style="margin-top: 16px;">
              <summary style="cursor: pointer; color: #333; font-weight: 600;">Stack Trace</summary>
              <pre style="
                background-color: #f5f5f5;
                padding: 12px;
                border-radius: 4px;
                overflow-x: auto;
                font-size: 12px;
                line-height: 1.4;
                margin-top: 8px;
                white-space: pre-wrap;
                color: #333;
              ">${errorDetails.stack}</pre>
            </details>
            ` : ''}
            ${errorDetails.componentStack ? `
            <details style="margin-top: 16px;">
              <summary style="cursor: pointer; color: #333; font-weight: 600;">Component Stack</summary>
              <pre style="
                background-color: #f5f5f5;
                padding: 12px;
                border-radius: 4px;
                overflow-x: auto;
                font-size: 12px;
                line-height: 1.4;
                margin-top: 8px;
                white-space: pre-wrap;
                color: #333;
              ">${errorDetails.componentStack}</pre>
            </details>
            ` : ''}
            `
          }
        </div>

        <!-- Action buttons -->
        <div style="
          display: flex; 
          gap: 12px; 
          justify-content: ${isProd ? 'center' : 'flex-end'};
          margin-top: 32px;
        ">
          ${isProd ? `
          <button id="error-report-btn" style="
            background-color: #2196f3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.2s;
          ">
            📧 Report to Support
          </button>
          ` : ''}
          
          <button id="error-reload-btn" style="
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.2s;
          ">
            🔄 Reload Page
          </button>
        </div>

        <!-- Status message area -->
        <div id="error-status-message" style="
          margin-top: 16px;
          padding: 12px;
          border-radius: 6px;
          text-align: center;
          font-size: 14px;
          display: none;
        "></div>
      </div>
    </div>
  `;

  // Add modal to DOM
  document.body.insertAdjacentHTML('beforeend', modalHTML);
  
  // Prevent body scroll
  document.body.style.overflow = 'hidden';

  // Add event listeners
  const modal = document.getElementById('global-error-modal')!;
  const closeBtn = document.getElementById('error-modal-close')!;
  const reloadBtn = document.getElementById('error-reload-btn')!;
  const reportBtn = document.getElementById('error-report-btn');
  const statusDiv = document.getElementById('error-status-message')!;

  const closeModal = () => {
    modal.remove();
    document.body.style.overflow = '';
  };

  const showStatus = (message: string, isError: boolean = false) => {
    statusDiv.textContent = message;
    statusDiv.style.display = 'block';
    statusDiv.style.backgroundColor = isError ? '#ffebee' : '#e8f5e8';
    statusDiv.style.color = isError ? '#c62828' : '#2e7d32';
    statusDiv.style.border = `1px solid ${isError ? '#ffcdd2' : '#a5d6a7'}`;
  };

  // Close button
  closeBtn.addEventListener('click', closeModal);

  // Reload button
  reloadBtn.addEventListener('click', () => {
    window.location.reload();
  });

  // Report button (production only)
  if (reportBtn && isProd) {
    reportBtn.addEventListener('click', async () => {
      reportBtn.textContent = '📧 Sending...';
      reportBtn.style.backgroundColor = '#666';
      (reportBtn as HTMLButtonElement).disabled = true;

      try {
        const response = await sendErrorReport(errorDetails);
        if (response.success) {
          showStatus(`✅ Error report sent successfully! Reference ID: ${response.error_id || 'N/A'}`);
          reportBtn.textContent = '✅ Sent';
          reportBtn.style.backgroundColor = '#4caf50';
        } else {
          throw new Error(response.message || 'Unknown error');
        }
      } catch (error) {
        console.error('Failed to send error report:', error);
        showStatus('❌ Failed to send error report. Please try again or contact support directly.', true);
        reportBtn.textContent = '📧 Try Again';
        reportBtn.style.backgroundColor = '#f44336';
        (reportBtn as HTMLButtonElement).disabled = false;
      }
    });
  }

  // Close on escape key
  const handleEscape = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      closeModal();
      document.removeEventListener('keydown', handleEscape);
    }
  };
  document.addEventListener('keydown', handleEscape);

  // Close on backdrop click
  modal.addEventListener('click', (event) => {
    if (event.target === modal) {
      closeModal();
    }
  });
}

/**
 * Handle JavaScript errors
 */
function handleError(event: ErrorEvent): void {
  const errorDetails: ErrorDetails = {
    name: event.error?.name || 'Error',
    message: event.error?.message || event.message || 'Unknown error occurred',
    stack: event.error?.stack,
  };

  console.error('Global error handler caught:', errorDetails);
  
  // In production, automatically send error report and show generic modal
  if (isProduction()) {
    sendErrorReport(errorDetails).catch(console.error);
  }
  
  showErrorModal(errorDetails);
}

/**
 * Handle unhandled promise rejections
 */
function handleUnhandledRejection(event: PromiseRejectionEvent): void {
  const error = event.reason;
  
  const errorDetails: ErrorDetails = {
    name: error?.name || 'UnhandledPromiseRejection',
    message: error?.message || String(error) || 'Unhandled promise rejection',
    stack: error?.stack,
  };

  console.error('Global unhandled rejection handler caught:', errorDetails);
  
  // In production, automatically send error report and show generic modal
  if (isProduction()) {
    sendErrorReport(errorDetails).catch(console.error);
  }
  
  showErrorModal(errorDetails);
}

/**
 * Setup global error handling
 */
export function setupGlobalErrorHandler(): void {
  // Handle JavaScript errors
  window.addEventListener('error', handleError);
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', handleUnhandledRejection);
  
  console.log('Global error handler initialized');
}