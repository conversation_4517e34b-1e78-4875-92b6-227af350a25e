import {
  createRouter,
  parseSearchWith,
  stringifySearchWith,
} from "@tanstack/react-router";
import { routeTree as buildAndPriceRouteTree } from "./routeTree.gen.ts";
import { User } from "@/types/user";
import { decodeFromBinary, encodeToBinary } from "@/utils/encode.ts";
import { stringify, parse } from "zipson";

export const createBuildAndPriceRouter = (user: User) =>
  createRouter({
    parseSearch: parseSearchWith((value) => parse(decodeFromBinary(value))),
    stringifySearch: stringifySearchWith((value) =>
      encodeToBinary(stringify(value)),
    ),
    routeTree: buildAndPriceRouteTree,
    context: {
      user,
    },
    basepath: "/build-and-price",
    defaultPreload: "intent",
    scrollRestoration: false,
  });

export type BuildAndPriceRouter = ReturnType<typeof createBuildAndPriceRouter>;
