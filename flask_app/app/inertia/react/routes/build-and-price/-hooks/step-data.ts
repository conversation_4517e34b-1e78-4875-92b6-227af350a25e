import { $api } from "@/api/web-api";
import React from "react";
import { useSearch } from "@tanstack/react-router";

/**
 * Hook that implements conditional data loading pattern for the build and price wizard.
 * Only waits for enabled steps to resolve before indicating the wizard is ready to render.
 *
 * @returns Object containing:
 *   - isReady: boolean indicating if all enabled steps have loaded successfully
 *   - selectedPumpTop: pump top data (if step 1 enabled)
 *   - selectedSiteVoltage: site voltage data (if step 2 enabled)
 *   - selectedPowerUnit: power unit data (if step 2 enabled)
 *   - enabledSteps: object indicating which steps are enabled
 */
const useConditionalWizardData = () => {
  const {
    build: { pump_top_id, site_voltage_id, power_unit_id },
  } = useSearch({ from: "/" });

  // Step enabling logic - synchronous functions that return boolean
  const stepOneEnabled = pump_top_id > 0;
  const stepTwoEnabled =
    stepOneEnabled && site_voltage_id > 0 && power_unit_id > 0;

  // Step 1 data query
  const { data: selectedPumpTop, isSuccess: stepOneSuccess } = $api.useQuery(
    "get",
    "/v1/pricing/pump-top/{part_id}",
    {
      params: { path: { part_id: pump_top_id } },
    },
    {
      enabled: stepOneEnabled,
      select(data) {
        return data.result;
      },
    },
  );

  // Step 2 data queries
  const { data: selectedSiteVoltage, isSuccess: siteVoltageSuccess } =
    $api.useQuery(
      "get",
      "/v1/pricing/voltage/{site_voltage_id}",
      {
        params: { path: { site_voltage_id } },
      },
      {
        enabled: stepTwoEnabled,
        select(data) {
          return data.result;
        },
      },
    );

  const { data: selectedPowerUnit, isSuccess: powerUnitSuccess } =
    $api.useQuery(
      "get",
      "/v1/pricing/power-unit/{power_unit_id}",
      {
        params: { path: { power_unit_id } },
      },
      {
        enabled: stepTwoEnabled,
        select(data) {
          return data.result;
        },
      },
    );

  // Conditional rendering logic: only wait for enabled steps to succeed
  const isReady = React.useMemo(() => {
    const enabledQueries = [];

    if (stepOneEnabled) {
      enabledQueries.push(stepOneSuccess);
    }

    if (stepTwoEnabled) {
      enabledQueries.push(siteVoltageSuccess, powerUnitSuccess);
    }

    // If no steps are enabled, we're ready to render
    if (enabledQueries.length === 0) {
      return true;
    }

    // All enabled queries must be successful
    return enabledQueries.every((success) => success === true);
  }, [
    stepOneEnabled,
    stepTwoEnabled,
    stepOneSuccess,
    siteVoltageSuccess,
    powerUnitSuccess,
  ]);

  return {
    isReady,
    selectedPumpTop,
    selectedSiteVoltage,
    selectedPowerUnit,
    enabledSteps: {
      stepOne: stepOneEnabled,
      stepTwo: stepTwoEnabled,
    },
  };
};

export { useConditionalWizardData };
