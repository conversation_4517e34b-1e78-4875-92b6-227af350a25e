import z from "zod";

export const pumpTopSchema = z.object({
  description: z.string().default(""),
  pump_top_id: z.number().gte(0, "ID must be a positive number").default(0),
  unit_type: z.string().default(""),
  msrp_cad: z
    .string()
    .default("0.00")
    .refine((v) => {
      const n = Number(v);
      return !isNaN(n) && v?.length > 0 && n >= 0;
    }, "MSRP must be a positive number"),
  msrp_usd: z
    .string()
    .default("0.00")
    .refine((v) => {
      const n = Number(v);
      return !isNaN(n) && v?.length > 0 && n >= 0;
    }, "MSRP must be a positive number"),
});

export const buildSchema = z.object({
  pump_top_id: z.number().gte(0, "ID must be a positive number"),
  site_voltage_id: z.number().gte(0, "ID must be a positive number").default(0),
  power_unit_id: z.number().gte(0, "ID must be a positive number").default(0),
});

export const siteVoltageSchema = z.object({
  description: z.string().default(""),
  site_voltage_id: z.number().gte(0, "ID must be a positive number").default(0),
  msrp_cad: z
    .string()
    .default("0.00")
    .refine((v) => {
      const n = Number(v);
      return !isNaN(n) && v?.length > 0 && n >= 0;
    }, "MSRP must be a positive number"),
  msrp_usd: z
    .string()
    .default("0.00")
    .refine((v) => {
      const n = Number(v);
      return !isNaN(n) && v?.length > 0 && n >= 0;
    }, "MSRP must be a positive number"),
});

export const powerUnitSchema = z.object({
  description: z.string().default(""),
  power_unit_id: z.number().gte(0, "ID must be a positive number").default(0),
  msrp_cad: z
    .string()
    .default("0.00")
    .refine((v) => {
      const n = Number(v);
      return !isNaN(n) && v?.length > 0 && n >= 0;
    }, "MSRP must be a positive number"),
  msrp_usd: z
    .string()
    .default("0.00")
    .refine((v) => {
      const n = Number(v);
      return !isNaN(n) && v?.length > 0 && n >= 0;
    }, "MSRP must be a positive number"),
});

export type BuildSchema = z.infer<typeof buildSchema>;
export type PumpTopSchema = z.infer<typeof pumpTopSchema>;
export type SiteVoltageSchema = z.infer<typeof siteVoltageSchema>;
export type PowerUnitSchema = z.infer<typeof powerUnitSchema>;
