import React from "react";
import { createStore, SnapshotFromStore } from "@xstate/store";
import { useSelector } from "@xstate/store/react";
import {
  PowerUnitSchema,
  powerUnitSchema,
  PumpTopSchema,
  pumpTopSchema,
  SiteVoltageSchema,
  siteVoltageSchema,
} from "@routes/build-and-price/-schemas/build";
import { ProgressBuildSchema } from "@routes/build-and-price/-schemas/progress-store";
import { SnapshotFrom } from "xstate";
import { machine } from "./machine";
import { BuildAndPriceWizard } from "./context";

export const store = (
  defaults = { step: 1, build: {} } as ProgressBuildSchema,
) =>
  createStore({
    context: {
      pump_top: pumpTopSchema.parse({}),
      site_voltage: siteVoltageSchema.parse({}),
      power_unit: powerUnitSchema.parse({}), // Assuming power_unit is not defined in the defaults
      step: defaults?.step ?? 1, // Default to step 1 if not provided
    },
    on: {
      nextStep: (context) => ({
        ...context,
        step: Math.min(context.step + 1, 5), // Assuming a maximum of 5 steps
      }),
      previousStep: (context) => ({
        ...context,
        step: Math.max(context.step - 1, 0),
      }),
      jumpToStep: (context, event: { step: number }) => {
        if (
          typeof event.step === "number" &&
          event.step >= 0 &&
          event.step <= 5
        ) {
          return { ...context, step: event.step };
        }
        return context; // No change if the step is invalid
      },
      setPumpTop: (context, event: { pump_top: PumpTopSchema }) => {
        return { ...context, pump_top: event.pump_top };
      },
      setSiteVoltage: (context, event: { site_voltage: SiteVoltageSchema }) => {
        return { ...context, site_voltage: event.site_voltage };
      },
      setPowerUnit: (context, event: { power_unit: PowerUnitSchema }) => {
        return { ...context, power_unit: event.power_unit };
      },
    },
  });

type BuildStore = ReturnType<typeof store>;

export const BuildStoreContext = React.createContext<BuildStore | null>(null);

export const useBuildSelector = <T>(
  selector: (state: SnapshotFromStore<BuildStore>) => T,
  compare?: (a: T | undefined, b: T) => boolean,
) => {
  const store = React.useContext(BuildStoreContext);
  if (!store) {
    throw new Error("Missing ProgressStoreProvider");
  }
  return useSelector(store, selector, compare);
};

export const useBuildStore = () => {
  const store = React.useContext(BuildStoreContext);
  if (!store) {
    throw new Error("Missing ProgressStoreProvider");
  }
  return store;
};

export const useBuildPriceWizardSelector = <T>(
  selector: (snapshot: SnapshotFrom<typeof machine>) => T,
  compare?: (a: T, b: T) => boolean,
) => {
  return BuildAndPriceWizard.useSelector(selector, compare);
};

export const useBuildPriceWizardRef = () => {
  return BuildAndPriceWizard.useActorRef();
};
