import {
  powerUnitSchema,
  PowerUnitSchema,
  pumpTopSchema,
  PumpTopSchema,
  siteVoltageSchema,
  SiteVoltageSchema,
} from "@routes/build-and-price/-schemas/build";
import Decimal from "decimal.js";
import { assign, setup } from "xstate";

type MachineContext = {
  step: number;
  build: {
    productName: string;
    pumpTop: PumpTopSchema;
    siteVoltage: SiteVoltageSchema;
    powerUnit: PowerUnitSchema;
  };
  pricing: {
    pumpTop: PumpTopSchema;
    siteVoltage: SiteVoltageSchema;
    powerUnit: PowerUnitSchema;
    listPrice: string;
  };
};

type MachineEvents =
  | { type: "setProductName"; productName: string }
  | { type: "setPumpTop"; pumpTop: PumpTopSchema }
  | { type: "setSiteVoltage"; siteVoltage: SiteVoltageSchema }
  | { type: "setPowerUnit"; powerUnit: PowerUnitSchema }
  | { type: "canJumpTo"; step: number };

type MachineInput = {
  step?: number;
  build?: {
    pumpTop: PumpTopSchema | undefined;
    siteVoltage: SiteVoltageSchema | undefined;
    powerUnit: PowerUnitSchema | undefined;
  };
};

const stepGuards = {
  pumpTop: () => true, // pumpTop guard - always accessible
  build: ({ context }: { context: MachineContext }) =>
    context.build.pumpTop.pump_top_id !== 0 && context.build.productName !== "", // build guard
  options: ({ context }: { context: MachineContext }) =>
    context.build.powerUnit.power_unit_id !== 0 &&
    context.build.siteVoltage.site_voltage_id !== 0 &&
    context.build.pumpTop.pump_top_id !== 0 &&
    context.build.productName !== "", // options guard
};

export const machine = setup({
  types: {
    context: {} as MachineContext,
    events: {} as MachineEvents,
    input: {} as MachineInput,
  },
  actions: {
    setPumpTop: assign(({ context, event }) => {
      if (event.type === "setPumpTop") {
        return {
          build: {
            ...context.build,
            pumpTop: event.pumpTop,
          },
          pricing: {
            ...context.pricing,
            pumpTop: event.pumpTop,
            listPrice: Decimal(event.pumpTop.msrp_cad)
              .add(context.pricing.siteVoltage.msrp_cad)
              .add(context.pricing.powerUnit.msrp_cad)
              .toFixed(2),
          },
        };
      }
      return {};
    }),
    setSiteVoltage: assign(({ context, event }) => {
      if (event.type === "setSiteVoltage") {
        return {
          build: {
            ...context.build,
            siteVoltage: event.siteVoltage,
          },
          pricing: {
            ...context.pricing,
            siteVoltage: event.siteVoltage,
            listPrice: Decimal(context.pricing.pumpTop.msrp_cad)
              .add(event.siteVoltage.msrp_cad)
              .add(context.pricing.powerUnit.msrp_cad)
              .toFixed(2),
          },
        };
      }
      return {};
    }),
    setPowerUnit: assign(({ context, event }) => {
      if (event.type === "setPowerUnit") {
        return {
          build: {
            ...context.build,
            powerUnit: event.powerUnit,
          },
          pricing: {
            ...context.pricing,
            powerUnit: event.powerUnit,
            listPrice: Decimal(context.pricing.pumpTop.msrp_cad)
              .add(context.pricing.siteVoltage.msrp_cad)
              .add(event.powerUnit.msrp_cad)
              .toFixed(2),
          },
        };
      }
      return {};
    }),
  },
  guards: {
    options: stepGuards.options,
    build: stepGuards.build,
    pumpTop: stepGuards.pumpTop,
    canJumpTo: ({ context, event }) => {
      if (event.type === "canJumpTo") {
        const stepGuards2 = {
          1: stepGuards.pumpTop(),
          2: stepGuards.build({ context }),
          3: stepGuards.options({ context }),
        };
        return stepGuards2[event.step as keyof typeof stepGuards2] || false;
      }
      return false;
    },
  },
}).createMachine({
  id: "buildMachine",
  initial: "Loading",
  context: ({ input }) => ({
    step: input.step ?? 1,
    build: {
      productName: input.build?.pumpTop?.unit_type ?? "",
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
    },
    pricing: {
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
      listPrice: Decimal(input.build?.powerUnit?.msrp_cad ?? 0)
        .add(Decimal(input.build?.siteVoltage?.msrp_cad ?? 0))
        .add(Decimal(input.build?.pumpTop?.msrp_cad ?? 0))
        .toFixed(2),
    },
  }),
  states: {
    Loading: {
      always: [
        { target: "Options", guard: "options" },
        { target: "Build", guard: "build" },
        { target: "PumpTop", guard: "pumpTop" },
      ],
    },
    PumpTop: {
      on: {
        setPumpTop: { actions: "setPumpTop" },
        setSiteVoltage: { actions: "setSiteVoltage" },
        setPowerUnit: { actions: "setPowerUnit" },
        jumpToStep: { actions: "jumpToStep" },
        canJumpTo: { target: "Loading", guard: "canJumpTo" },
      },
    },
    Build: {
      on: {
        setPumpTop: { actions: "setPumpTop" },
        setSiteVoltage: { actions: "setSiteVoltage" },
        setPowerUnit: { actions: "setPowerUnit" },
        jumpToStep: { actions: "jumpToStep" },
        canJumpTo: { target: "Loading", guard: "canJumpTo" },
      },
    },
    Options: {
      on: {
        setPumpTop: { actions: "setPumpTop" },
        setSiteVoltage: { actions: "setSiteVoltage" },
        setPowerUnit: { actions: "setPowerUnit" },
        jumpToStep: { actions: "jumpToStep" },
        canJumpTo: { target: "Loading", guard: "canJumpTo" },
      },
    },
  },
});
