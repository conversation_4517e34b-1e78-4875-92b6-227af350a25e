import {
  createFileRoute,
  useNavigate,
  useSearch,
} from "@tanstack/react-router";
import { Header } from "./-components/header";
import Progress from "./-components/progress";
import { BuildAndPrice } from "./-components/build-price";
import { zodValidator } from "@tanstack/zod-adapter";
import { searchParamsSchema } from "./-schemas/search-params";
import { BuildAndPriceWizard } from "./-store/progress/context";
import { useConditionalWizardData } from "./-hooks/step-data";
import React from "react";
import { useBuildPriceWizardRef } from "./-store/progress/hooks";

export const Route = createFileRoute("/")({
  validateSearch: zodValidator(searchParamsSchema),
  component: RouteComponent,
});

function RouteComponent() {
  const { step, build } = useSearch({ from: "/" });
  const { isReady, selectedPumpTop, selectedSiteVoltage, selectedPowerUnit } =
    useConditionalWizardData();
  return (
    <div className="flex flex-grow flex-col items-center justify-start bg-gray-100 p-2 lg:p-8">
      <div className="flex w-full max-w-7xl flex-col gap-2 lg:gap-8">
        <Header />
        {isReady ? (
          <BuildAndPriceWizard.Provider
            key={JSON.stringify({ step, build })}
            options={{
              input: {
                step,
                build: {
                  pumpTop: selectedPumpTop,
                  siteVoltage: selectedSiteVoltage,
                  powerUnit: selectedPowerUnit,
                },
              },
            }}
          >
            <Wizard isReady={isReady} />
          </BuildAndPriceWizard.Provider>
        ) : (
          <div className="flex flex-col items-center justify-center gap-4">
            Loading...
          </div>
        )}
      </div>
    </div>
  );
}

function Wizard({ isReady }: { isReady: boolean }) {
  const { step } = useSearch({ from: "/" });
  const navigate = useNavigate();
  const wizard = useBuildPriceWizardRef();
  const allowed = wizard.getSnapshot().can({ type: "canJumpTo", step });
  React.useEffect(() => {
    if (isReady && !allowed) {
      navigate({
        to: ".",
        search: (prev) => ({
          ...prev,
          step: step - 1,
        }),
        replace: true,
      });
    }
  }, [isReady, step, allowed, navigate]);
  if (!isReady || !allowed) {
    return null;
  }
  return (
    <>
      <Progress />
      <BuildAndPrice />
    </>
  );
}
