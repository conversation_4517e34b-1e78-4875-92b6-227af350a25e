import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { shallowEqual } from "@xstate/react";
import { Wizard } from "../-state/wizard/context";

function Total() {
  const pumpTop = Wizard.useSelector(
    (state) => state.context.pricing.pumpTop,
    shallowEqual,
  );
  const siteVoltage = Wizard.useSelector(
    (state) => state.context.pricing.siteVoltage,
    shallowEqual,
  );
  const powerUnit = Wizard.useSelector(
    (state) => state.context.pricing.powerUnit,
    shallowEqual,
  );
  const total = Wizard.useSelector((state) => state.context.pricing.listPrice);

  return (
    <div className="flex w-full items-center justify-between">
      <Table>
        <TableHeader>
          {total !== "0.00" && (
            <TableRow>
              <TableHead colSpan={4} className="font-semibold">
                Required:
              </TableHead>
            </TableRow>
          )}
        </TableHeader>
        <TableBody>
          {pumpTop.pump_top_id !== 0 && (
            <TableRow>
              <TableCell>Pump Top</TableCell>
              <TableCell colSpan={2}>{pumpTop.description}</TableCell>
              <TableCell className="text-right">
                <Label.Numeric value={pumpTop.msrp_cad} variant="currency" />
              </TableCell>
            </TableRow>
          )}
          {powerUnit.power_unit_id > 0 && (
            <TableRow>
              <TableCell>Power Unit</TableCell>
              <TableCell>{powerUnit.description}</TableCell>
              <TableCell></TableCell>
              <TableCell className="text-right">
                <Label.Numeric
                  value={powerUnit?.msrp_cad ?? "0.00"}
                  variant="currency"
                />
              </TableCell>
            </TableRow>
          )}
          {siteVoltage.site_voltage_id > 0 && (
            <TableRow>
              <TableCell>Site Voltage</TableCell>
              <TableCell>{siteVoltage.description}</TableCell>
              <TableCell></TableCell>
              <TableCell className="text-right">
                <Label.Numeric
                  value={siteVoltage?.msrp_cad ?? "0.00"}
                  variant="currency"
                />
              </TableCell>
            </TableRow>
          )}
        </TableBody>
        {Number(total) > 0 && (
          <TableFooter>
            <TableRow>
              <TableCell colSpan={3} className="text-right">
                Total Suggested List Price
              </TableCell>
              <TableCell className="text-right">
                <Label.Numeric value={total} variant="currency" />
              </TableCell>
            </TableRow>
            <>
              <TableRow className="bg-white">
                <TableCell colSpan={3} className="text-right">
                  Taxes
                </TableCell>
                <TableCell className="text-right">$0.00</TableCell>
              </TableRow>
              <TableRow className="font-bold">
                <TableCell colSpan={3} className="text-right">
                  Total
                </TableCell>
                <TableCell className="text-right">$0.00</TableCell>
              </TableRow>
            </>
          </TableFooter>
        )}
      </Table>
    </div>
  );
}
export { Total };
