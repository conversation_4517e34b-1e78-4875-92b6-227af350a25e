import { Error } from "@/components/ui/typography";
import { createFormHookContexts } from "@tanstack/react-form";

type SubmitErrorProps = {
  form: ReturnType<ReturnType<typeof createFormHookContexts>["useFormContext"]>;
};

const SubmitError = ({ form }: SubmitErrorProps) => {
  return (
    <form.Subscribe
      selector={(state) => state.errors}
      children={(errors) => {
        if (errors.length !== 0) {
          console.log({ errors });
          const numErrors =
            errors.length === 1
              ? Object.keys(errors.at(0)).length
              : errors.length;
          console.log({ numErrors });
          return (
            <Error className="text-destructive w-full text-center italic">
              Correct the form errors to continue.
            </Error>
          );
        }
        return <div className="pb-4" />;
      }}
    />
  );
};

export { SubmitError };
