import {
  ColumnDef,
  flexRender,
  SortingState,
  getSortedRowModel,
  getCoreRowModel,
  useReactTable,
  Row,
} from "@tanstack/react-table";
import React from "react";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { useFieldContext as usePumpTopFieldContext } from "@routes/build-and-price/-components/1-pump-top/form/form";
import { useFieldContext as useBuildFieldContext } from "@routes/build-and-price/-components/2-build/form/form";

type TableFieldProps<TData, TValue> = {
  columns: ColumnDef<TData, TValue>[];
  defaultColumn?: Partial<ColumnDef<TData, unknown>>;
  data: TData[];
  isSuccess?: boolean;
  isLoading?: boolean;
  isError?: boolean;
  loadingRows?: number;
  id: keyof TData;
  field:
    | ReturnType<typeof usePumpTopFieldContext<number>>
    | ReturnType<typeof useBuildFieldContext<number>>;
  emptyValue: number;
  onRowSelect?: (row: Row<TData>, selected: boolean) => void;
  parseRowId?: (rowId: string) => number;
};

export function TableFieldComponent<TData, TValue>({
  columns,
  defaultColumn = {},
  data,
  isError = false,
  isSuccess = false,
  isLoading = false,
  loadingRows = 1,
  id,
  field,
  emptyValue,
  onRowSelect = () => undefined,
  parseRowId = (rowId) => parseInt(rowId),
}: TableFieldProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const table = useReactTable({
    data,
    columns,
    defaultColumn,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    getRowId: (row) => `${row[id]}`,
    state: {
      sorting,
    },
  });
  if (isLoading) {
    return (
      <div className="grid w-full grid-cols-4 gap-3 px-4">
        <div className="col-span-full row-span-full grid h-7 grid-cols-subgrid">
          <Skeleton className="h-full w-full" />
          <Skeleton className="h-full w-full" />
          <Skeleton className="h-full w-full" />
          <Skeleton className="h-full w-full" />
        </div>
        {[...Array(loadingRows)].map((_, idx) => (
          <Skeleton key={idx} className="col-span-full h-7 w-full" />
        ))}
      </div>
    );
  }
  if (isError) {
    return <div>Error loading data</div>;
  }
  if (isSuccess === false) {
    return null;
  }
  return (
    <div className="container flex max-h-96 w-full flex-1 overflow-y-auto rounded-md border">
      <div className="h-full w-full">
        {/* Header */}
        <div className="sticky top-0 z-10 flex w-full border-b border-gray-200 bg-gray-50">
          {table
            .getHeaderGroups()
            .map((headerGroup) =>
              headerGroup.headers.map((header) => (
                <React.Fragment key={header.id}>
                  {!header.isPlaceholder &&
                    flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
                </React.Fragment>
              )),
            )}
        </div>

        {/* Body */}
        <div className="my-0.5 bg-white">
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <div
                key={row.id}
                className={cn(
                  "data-[state=selected]:outline-ijack-green mx-0.5 flex cursor-pointer flex-row border-gray-200 hover:bg-gray-50 data-[state=false]:border-b data-[state=selected]:bg-gray-50 data-[state=selected]:outline-2",
                )}
                data-state={row.id === `${field.state.value}` && "selected"}
                onClick={() => {
                  // clear all prior selections
                  table
                    .getRowModel()
                    .rows.forEach(
                      (r) => r.getIsSelected() && r.toggleSelected(false),
                    );
                  // select only this row
                  const selected = row.id !== `${field.state.value}`;
                  if (selected) {
                    row.toggleSelected(selected);
                    field.setValue(parseRowId(row.id));
                  } else {
                    row.toggleSelected(selected);
                    field.setValue(emptyValue);
                  }
                  onRowSelect(row, selected);
                }}
              >
                {row.getVisibleCells().map((cell) => (
                  <React.Fragment key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </React.Fragment>
                ))}
              </div>
            ))
          ) : (
            <div className="col-span-3 flex h-24 items-center justify-center text-center text-gray-500">
              No results.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
