export const getSubmitter = (e: React.FormEvent<HTMLFormElement>) => {
  // Get the clicked button that triggered the form submission
  const submitter = (e.nativeEvent as SubmitEvent)
    .submitter as HTMLButtonElement;

  // Get the step index from the data-idx attribute of the progress button
  const stepIndex = submitter?.getAttribute("data-idx");
  const targetStep = stepIndex ? parseInt(stepIndex) + 1 : 1; // Default to step 1 if no data-idx
  return targetStep;
};
