import { ColumnDef, Row } from "@tanstack/react-table";

import { useFieldContext } from "../form/form";
import { TableFieldComponent } from "@routes/build-and-price/-components/form/table-field";

type TableFieldProps<TData, TValue> = {
  columns: ColumnDef<TData, TValue>[];
  defaultColumn?: Partial<ColumnDef<TData, unknown>>;
  data: TData[];
  isSuccess: boolean;
  isLoading: boolean;
  loadingRows?: number;
  id: keyof TData;
  onRowSelect?: (row: Row<TData>, selected: boolean) => void;
};

export function TableField<TData, TValue>({
  columns,
  defaultColumn = {},
  data,
  isLoading = false,
  isSuccess = true,
  loadingRows = 1,
  id,
  onRowSelect = () => undefined,
}: TableFieldProps<TData, TValue>) {
  const field = useFieldContext<number>();
  return (
    <TableFieldComponent
      columns={columns}
      defaultColumn={defaultColumn}
      data={data}
      isSuccess={isSuccess}
      isLoading={isLoading}
      loadingRows={loadingRows}
      id={id}
      onRowSelect={onRowSelect}
      field={field}
      emptyValue={0}
      parseRowId={(id) => parseInt(id)}
    />
  );
}
