import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useSearch } from "@tanstack/react-router";
import { useBuildPriceWizardSelector } from "@routes/build-and-price/-store/progress/hooks";
import { shallowEqual } from "@xstate/store";

function Total() {
  const { step } = useSearch({ from: "/" });
  // const pumpTop = useBuildSelector((state) => state.context.pump_top);
  const pumpTop = useBuildPriceWizardSelector(
    (state) => state.context.pricing.pumpTop,
    shallowEqual,
  );
  return (
    <div className="flex w-full items-center justify-between">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead colSpan={4} className="font-semibold">
              Required:
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {pumpTop !== undefined && (
            <TableRow>
              <TableCell>Pump Top</TableCell>
              <TableCell colSpan={2}>{pumpTop.description}</TableCell>
              <TableCell className="text-right">
                <Label.Numeric value={pumpTop.msrp_cad} variant="currency" />
              </TableCell>
            </TableRow>
          )}
          {step > 1 && (
            <>
              <TableRow>
                <TableCell>Power Unit</TableCell>
                <TableCell>Pending</TableCell>
                <TableCell>PayPal</TableCell>
                <TableCell className="text-right">$0.00</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Barrel</TableCell>
                <TableCell>Overdue</TableCell>
                <TableCell>Bank Transfer</TableCell>
                <TableCell className="text-right">$0.00</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Rods</TableCell>
                <TableCell>Overdue</TableCell>
                <TableCell>Bank Transfer</TableCell>
                <TableCell className="text-right">$0.00</TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Voltage</TableCell>
                <TableCell>Overdue</TableCell>
                <TableCell>Bank Transfer</TableCell>
                <TableCell className="text-right">$0.00</TableCell>
              </TableRow>
            </>
          )}
        </TableBody>
        {step > 2 && (
          <TableFooter>
            <TableRow>
              <TableCell colSpan={3} className="text-right">
                Total Suggested List Price
              </TableCell>
              <TableCell className="text-right">$2,500.00</TableCell>
            </TableRow>
            <TableRow className="bg-white">
              <TableCell colSpan={3} className="text-right">
                Taxes
              </TableCell>
              <TableCell className="text-right">$2,500.00</TableCell>
            </TableRow>
            <TableRow className="font-bold">
              <TableCell colSpan={3} className="text-right">
                Total
              </TableCell>
              <TableCell className="text-right">$2,500.00</TableCell>
            </TableRow>
          </TableFooter>
        )}
      </Table>
    </div>
  );
}

export { Total };
