import {
  <PERSON>,
  CardA<PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { formOpts, withForm } from "./form";
import { H3, H4 } from "@/components/ui/typography";
import { CheckIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { options } from "@routes/build-and-price/-schemas/data";
import useEmblaCarousel from "embla-carousel-react";
import { Button } from "@/components/ui/button";
import React from "react";
import { $api } from "@/api/web-api";
import { useQueryClient } from "@tanstack/react-query";
import {
  useBuildPriceWizardRef,
  useBuildPriceWizardSelector,
} from "@routes/build-and-price/-store/progress/hooks";

const DesktopCard = withForm({
  ...formOpts,
  props: {
    nextRef: { current: null as HTMLElement | null },
  },
  render: function DCard({ form, nextRef }) {
    const queryClient = useQueryClient();
    const wizard = useBuildPriceWizardRef();
    return (
      <div className="hidden w-full gap-y-2 lg:grid lg:grid-cols-3 lg:gap-x-10">
        <form.Subscribe
          selector={(state) => state.fieldMeta.product_name}
          children={(value) => {
            return (
              <div className="col-span-full flex flex-wrap items-center gap-x-4">
                <H3 className="col-span-full pb-2">Select Product</H3>
                {(value?.errors ?? []).length > 0 &&
                  value.errors.map((error, idx) => (
                    <p
                      key={idx}
                      className="text-destructive h-full items-center text-center align-middle"
                    >
                      *{error.message}
                    </p>
                  ))}
              </div>
            );
          }}
        />
        {options.map((option) => (
          <form.Field
            key={option.id}
            listeners={{
              onChange: () => {
                form.setFieldValue("pump_top_id", 0);
              },
            }}
            name="product_name"
            children={(field) => {
              const isSelected = field.state.value === option.id;
              const queryOptions = $api.queryOptions(
                "get",
                "/v1/pricing/pump-top/",
                {
                  params: {
                    query: {
                      unit_type: option.name,
                    },
                  },
                },
              );
              return (
                <Card
                  key={option.id}
                  className={cn(
                    "relative col-span-1 border transition-transform duration-200 ease-in-out",
                    field.state.value === option.id
                      ? "border-ijack-green border-2 lg:scale-110"
                      : "border-gray-200",
                  )}
                >
                  <CardHeader className="flex flex-col items-center">
                    <CardTitle className="flex items-center justify-between">
                      <H4>{option.name}</H4>
                    </CardTitle>
                    <CardDescription>{option.description}</CardDescription>
                    <Button
                      onClick={() => {
                        if (option.id !== field.state.value) {
                          setTimeout(
                            () =>
                              nextRef.current?.scrollIntoView({
                                behavior: "smooth",
                                block: "start",
                              }),
                            300,
                          );
                          field.setValue(option.id);
                          wizard.send({
                            type: "setProductName",
                            productName: option.name,
                          });
                        }
                      }}
                      onMouseEnter={() => {
                        queryClient.prefetchQuery(queryOptions);
                      }}
                      variant={isSelected ? "active" : "default"}
                      className="mx-auto w-full max-w-40 rounded-full"
                    >
                      {isSelected ? (
                        <>
                          <CheckIcon className="stroke-3" />
                          Selected
                        </>
                      ) : (
                        "Select"
                      )}
                    </Button>
                  </CardHeader>
                  <CardContent>{option.promotionalBlurb}</CardContent>
                </Card>
              );
            }}
          />
        ))}
      </div>
    );
  },
});

const MobileCard = withForm({
  ...formOpts,
  props: {
    nextRef: { current: null as HTMLElement | null },
  },
  render: function MCard({ form, nextRef }) {
    const productName = useBuildPriceWizardSelector(
      (state) => state.context.build.productName,
    );
    const startIndex = (() => {
      const idx = options.findIndex((option) => option.id === productName);
      return idx === -1 ? 1 : idx;
    })();
    const [emblaRef, emblaApi] = useEmblaCarousel({
      align: "center",
      startIndex,
    });
    const [carouselIndex, setCarouselIndex] = React.useState(startIndex);
    React.useLayoutEffect(() => {
      const t = emblaApi?.on("select", () => {
        setCarouselIndex(emblaApi?.selectedScrollSnap() ?? 0);
      });
      const y = emblaApi?.on("reInit", () => {
        emblaApi?.scrollTo(carouselIndex);
      });

      return () => {
        t?.clear();
        y?.clear();
      };
    }, [emblaApi, carouselIndex]);
    const queryClient = useQueryClient();
    const wizard = useBuildPriceWizardRef();
    return (
      <div className="flex w-full flex-col items-start lg:hidden">
        <form.Subscribe
          selector={(state) => state.fieldMeta.product_name}
          children={(value) => {
            return (
              <div className="col-span-full flex flex-wrap items-center gap-x-4">
                <H3 className="col-span-full pb-2">Select Product</H3>
                {(value?.errors ?? []).length > 0 &&
                  value.errors.map((error, idx) => (
                    <p
                      key={idx}
                      className="text-destructive items-center text-center align-middle"
                    >
                      *{error.message}
                    </p>
                  ))}
              </div>
            );
          }}
        />
        <div className="w-full">
          <form.Field
            name="product_name"
            listeners={{
              onChange: () => {
                form.setFieldValue("pump_top_id", 0);
              },
            }}
            children={function MCard2(field) {
              return (
                <>
                  <div
                    ref={emblaRef}
                    className="embla fade-mask-edge -mx-4 w-auto overflow-x-clip pb-5 md:-mx-10"
                  >
                    <div className="embla__container flex">
                      {/* Left spacer for centering */}
                      <div className="embla__slide w-1/4 flex-none"></div>

                      {options.map((option, idx) => {
                        const isSelected = field.state.value === option.id;
                        const queryOptions = $api.queryOptions(
                          "get",
                          "/v1/pricing/pump-top/",
                          {
                            params: {
                              query: {
                                unit_type: option.name,
                              },
                            },
                          },
                        );
                        return (
                          <div
                            key={option.id}
                            className="embla__slide -mx-8 w-1/2 flex-none transition-all duration-300 ease-in-out"
                          >
                            <Card
                              onClick={() => {
                                emblaApi?.scrollTo(idx);
                              }}
                              className={cn(
                                "hover:border-ijack-green relative cursor-pointer border drop-shadow-xl transition-all duration-300 ease-in-out",
                                carouselIndex === idx
                                  ? "z-50" // Selected card on top
                                  : (() => {
                                      const distance = Math.abs(
                                        carouselIndex - idx,
                                      );
                                      // Cards decrease in z-index based on distance from selected
                                      switch (distance) {
                                        case 1:
                                          return "z-40 scale-80"; // Adjacent cards
                                        case 2:
                                          return "z-30 scale-80"; // Two positions away
                                        case 3:
                                          return "z-20 scale-80"; // Three positions away
                                        default:
                                          return "z-10 scale-80"; // Further cards
                                      }
                                    })(),
                              )}
                            >
                              <CardHeader className="flex flex-col items-center">
                                <CardTitle className="flex items-center justify-between">
                                  <H4>{option.name}</H4>
                                </CardTitle>
                                <CardDescription>
                                  {option.description}
                                </CardDescription>
                                <Button
                                  onClick={() => {
                                    if (option.id !== field.state.value) {
                                      form.reset();
                                      setTimeout(
                                        () =>
                                          nextRef.current?.scrollIntoView({
                                            behavior: "smooth",
                                            block: "start",
                                          }),
                                        300,
                                      );
                                      field.setValue(option.id);
                                      wizard.send({
                                        type: "setProductName",
                                        productName: option.name,
                                      });
                                    }
                                    emblaApi?.scrollTo(idx);
                                  }}
                                  variant={isSelected ? "active" : "default"}
                                  className="mx-auto w-full max-w-40 rounded-full"
                                  onMouseOutCapture={() => {
                                    queryClient.prefetchQuery(queryOptions);
                                  }}
                                  asChild
                                >
                                  <CardAction>
                                    {isSelected ? (
                                      <>
                                        <CheckIcon className="stroke-3" />
                                        Selected
                                      </>
                                    ) : (
                                      "Select"
                                    )}
                                  </CardAction>
                                </Button>
                              </CardHeader>
                              <CardContent>
                                {option.promotionalBlurb}
                              </CardContent>
                            </Card>
                          </div>
                        );
                      })}

                      {/* Right spacer for centering */}
                      <div className="embla__slide w-1/4 flex-none"></div>
                    </div>
                  </div>
                  <div className="flex justify-center gap-2">
                    {options.map((option, idx) => {
                      return (
                        <button
                          key={option.id}
                          type="button"
                          onClick={() => {
                            emblaApi?.scrollTo(idx);
                          }}
                          className={cn(
                            carouselIndex === idx
                              ? "bg-ijack-black hover:bg-ijack-black/80"
                              : "bg-gray-300 hover:bg-gray-400",
                            "h-2 w-14 rounded-full transition-all duration-300 ease-in-out",
                            // isActive
                            //   ? "bg-ijack-green"
                            //   : "bg-gray-300 hover:bg-gray-400",
                          )}
                          aria-label={`Select ${option.name}`}
                        />
                      );
                    })}
                  </div>
                </>
              );
            }}
          />

          {/* Carousel Indicators */}
        </div>
      </div>
    );
  },
});

export { MobileCard, DesktopCard };
