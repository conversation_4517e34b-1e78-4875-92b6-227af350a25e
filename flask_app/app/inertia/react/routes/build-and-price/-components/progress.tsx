import { CheckIcon } from "lucide-react";
import { Link, useSearch } from "@tanstack/react-router";
import { useBuildPriceWizardRef } from "../-store/progress/hooks";
import { cn } from "@/lib/utils";

const steps = [
  { id: "1", name: "Select Pump Top", href: "#", status: "complete" },
  { id: "2", name: "Build", href: "#", status: "current" },
  { id: "3", name: "Options", href: "#", status: "upcoming" },
  { id: "4", name: "Request Quote", href: "#", status: "upcoming" },
];

export default function Progress() {
  const search = useSearch({ from: "/" });
  const stepState = search.step - 1;
  const wizard = useBuildPriceWizardRef();
  return (
    <>
      <nav
        aria-label="Progress"
        className="flex items-center justify-center md:hidden"
      >
        <p className="text-sm font-medium">
          Step {stepState + 1} of {steps.length}
        </p>
        <ol role="list" className="ml-8 flex items-center space-x-5">
          {steps.map((step, idx) => (
            <li key={step.name}>
              {idx < stepState ? (
                <button
                  type="submit"
                  form={`${idx}`}
                  className="bg-ijack-black block size-2.5 cursor-pointer rounded-full border transition-transform duration-200 ease-in-out hover:scale-125"
                >
                  <span className="sr-only">{step.name}</span>
                </button>
              ) : idx === stepState ? (
                <button
                  type="submit"
                  form={`${idx}`}
                  aria-current="step"
                  className="relative flex cursor-pointer items-center justify-center"
                >
                  <span
                    aria-hidden="true"
                    className="absolute flex size-5 p-px"
                  >
                    <span className="bg-ijack-black size-full rounded-full" />
                  </span>
                  <span
                    aria-hidden="true"
                    className="bg-ijack-green relative block size-2.5 cursor-pointer rounded-full"
                  />
                  <span className="sr-only">{step.name}</span>
                </button>
              ) : (
                <button
                  type="submit"
                  form={`${idx}`}
                  className="block size-2.5 rounded-full bg-gray-200 hover:bg-gray-400"
                >
                  <span className="sr-only">{step.name}</span>
                </button>
              )}
            </li>
          ))}
        </ol>
      </nav>
      <nav className="hidden w-full md:block" aria-label="Progress">
        <ol role="list" className="space-y-4 md:flex md:space-y-0 md:space-x-8">
          {steps.map((step, idx) => {
            const disabled =
              wizard.getSnapshot().can({ type: "canJumpTo", step: idx + 1 }) ===
              false;
            return (
              <li
                key={step.name}
                className="transition-transform duration-200 ease-in-out hover:scale-105 md:flex-1"
              >
                {idx < stepState ? (
                  <button
                    disabled={disabled}
                    data-idx={idx}
                    form={`${stepState + 1}`}
                    type="submit"
                    className="group border-ijack-green flex w-full flex-col border-l-4 py-2 pl-4 text-start md:border-t-4 md:border-l-0 md:pt-4 md:pb-0 md:pl-0"
                  >
                    <span className="text-ijack-black flex justify-between text-sm font-medium">
                      Step {step.id}
                      <CheckIcon />
                    </span>
                    <span className="text-sm font-medium">{step.name}</span>
                  </button>
                ) : idx === stepState ? (
                  <button
                    disabled={disabled}
                    form={`${stepState + 1}`}
                    type="submit"
                    aria-current="step"
                    className="border-ijack-green flex w-full flex-col border-l-4 py-2 pl-4 text-start md:border-t-4 md:border-l-0 md:pt-4 md:pb-0 md:pl-0"
                  >
                    <span className="text-ijack-black text-sm font-medium">
                      Step {step.id}
                    </span>
                    <span className="text-sm font-medium">{step.name}</span>
                  </button>
                ) : (
                  <Link
                    disabled={disabled}
                    to="."
                    search={(prev) => ({ ...prev, step: idx + 1 })}
                    type="button"
                    className={cn(
                      "group flex w-full flex-col border-l-4 py-2 pl-4 text-start md:border-t-4 md:border-l-0 md:pt-4 md:pb-0 md:pl-0",
                      disabled
                        ? "border-gray-200 hover:border-gray-300"
                        : "border-ijack-grey hover:border-gray-400",
                    )}
                  >
                    <span
                      className={cn(
                        "text-sm font-medium",
                        disabled && "text-gray-400",
                      )}
                    >
                      Step {step.id}
                    </span>
                    <span
                      className={cn(
                        "text-sm font-medium",
                        disabled && "text-gray-400",
                      )}
                    >
                      {step.name}
                    </span>
                  </Link>
                )}
              </li>
            );
          })}
        </ol>
      </nav>
    </>
  );
}
