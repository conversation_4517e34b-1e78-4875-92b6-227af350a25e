import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { components } from "@/types/web-api.gen";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowDown, ArrowUp, ArrowUpDown } from "lucide-react";

type VoltagePricing = components["schemas"]["VoltagePricing"];
type PowerUnitPricing = components["schemas"]["PowerUnitPricing"];

export const powerUnitColumns: (
  selectedId: number,
) => ColumnDef<PowerUnitPricing>[] = (selectedId) => [
  {
    id: "power_unit_id",
    accessorKey: "power_unit_id",
    header: () => (
      <span className="hidden min-w-0 flex-none text-left sm:flex"></span>
    ),
    cell: ({ row }) => (
      <span className="hidden min-w-0 flex-none truncate overflow-hidden px-2 py-2 text-sm text-gray-900 sm:flex lg:px-4">
        <Checkbox
          checked={row.getValue("power_unit_id") === selectedId}
          className="mx-auto rounded-full"
          aria-label="Select row"
        />
      </span>
    ),
    enableSorting: false,
    enableResizing: false,
  },
  {
    accessorKey: "description",
    header: ({ column }) => {
      const sortState = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="ml-0 flex flex-grow justify-start lg:ml-12"
          onClick={column.getToggleSortingHandler()}
        >
          Power Unit
          {sortState === "asc" ? (
            <ArrowUp className="ml-2 h-4 w-4" />
          ) : sortState === "desc" ? (
            <ArrowDown className="ml-2 h-4 w-4" />
          ) : (
            <ArrowUpDown className="ml-2 h-4 w-4" />
          )}
        </Button>
      );
    },
    cell: (pumpTop) => {
      return (
        <div className="min-w-0 grow truncate overflow-hidden px-2 py-2 text-sm text-gray-900">
          <span className="truncate text-sm">{`${pumpTop.getValue()}`}</span>
        </div>
      );
    },
    enableResizing: false,
  },
  {
    accessorKey: "msrp_cad",
    header: ({ column }) => {
      const sortState = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="min-w-fit text-left"
          onClick={column.getToggleSortingHandler()}
        >
          MSRP (CAD)
          {sortState === "asc" ? (
            <ArrowUp className="ml-1 h-3 w-3" />
          ) : sortState === "desc" ? (
            <ArrowDown className="ml-1 h-3 w-3" />
          ) : (
            <ArrowUpDown className="ml-1 h-3 w-3" />
          )}
        </Button>
      );
    },
    cell: (msrp) => {
      return (
        <Label.Numeric
          value={isNaN(msrp.getValue() as number) ? 0 : `${msrp.getValue()}`}
          variant="currency"
          className="px-2 py-2 text-sm text-gray-900"
        />
      );
    },
    enableResizing: false,
    sortingFn: "alphanumeric",
  },
];

export const siteVoltageColumns: (
  selectedId: number,
) => ColumnDef<VoltagePricing>[] = (selectedId) => [
  {
    id: "site_voltage_id",
    accessorKey: "site_voltage_id",
    header: () => (
      <span className="hidden min-w-0 flex-none text-left sm:flex"></span>
    ),
    cell: ({ row }) => (
      <span className="hidden min-w-0 flex-none truncate overflow-hidden px-2 py-2 text-sm text-gray-900 sm:flex lg:px-4">
        <Checkbox
          checked={row.getValue("site_voltage_id") === selectedId}
          className="mx-auto rounded-full"
          aria-label="Select row"
        />
      </span>
    ),
    enableSorting: false,
    enableResizing: false,
  },
  {
    accessorKey: "description",
    header: ({ column }) => {
      const sortState = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="ml-0 flex flex-grow justify-start lg:ml-12"
          onClick={column.getToggleSortingHandler()}
        >
          Site Voltage
          {sortState === "asc" ? (
            <ArrowUp className="ml-2 h-4 w-4" />
          ) : sortState === "desc" ? (
            <ArrowDown className="ml-2 h-4 w-4" />
          ) : (
            <ArrowUpDown className="ml-2 h-4 w-4" />
          )}
        </Button>
      );
    },
    cell: (pumpTop) => {
      return (
        <div className="min-w-0 grow truncate overflow-hidden px-2 py-2 text-sm text-gray-900">
          <span className="truncate text-sm">{`${pumpTop.getValue()}`}</span>
        </div>
      );
    },
    enableResizing: false,
  },
  {
    accessorKey: "msrp_cad",
    header: ({ column }) => {
      const sortState = column.getIsSorted();
      return (
        <Button
          variant="ghost"
          className="min-w-fit text-left"
          onClick={column.getToggleSortingHandler()}
        >
          MSRP (CAD)
          {sortState === "asc" ? (
            <ArrowUp className="ml-1 h-3 w-3" />
          ) : sortState === "desc" ? (
            <ArrowDown className="ml-1 h-3 w-3" />
          ) : (
            <ArrowUpDown className="ml-1 h-3 w-3" />
          )}
        </Button>
      );
    },
    cell: (msrp) => {
      return (
        <Label.Numeric
          value={isNaN(msrp.getValue() as number) ? 0 : `${msrp.getValue()}`}
          variant="currency"
          className="px-2 py-2 text-sm text-gray-900"
        />
      );
    },
    enableResizing: false,
    sortingFn: "alphanumeric",
  },
];
