import { <PERSON>rro<PERSON>, H3 } from "@/components/ui/typography";
import { formOpts, useAppForm } from "./form/form";
import { useBuildPriceWizardRef } from "@routes/build-and-price/-store/progress/hooks";
import { Link, useNavigate, useSearch } from "@tanstack/react-router";
import { $api } from "@/api/web-api";
import { powerUnitColumns, siteVoltageColumns } from "./table/columns";
import {
  powerUnitSchema,
  siteVoltageSchema,
} from "@routes/build-and-price/-schemas/build";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, ArrowRight, FileCheck } from "lucide-react";
import { buildFormSchema } from "./form/validation";
import { defaultMeta } from "@routes/build-and-price/-schemas/form-meta";
import { toast } from "sonner";
import { Total } from "../total";
import { getSubmitter } from "../form/submitter";

function BuildForm() {
  const search = useSearch({ from: "/" });
  const navigate = useNavigate();
  const form = useAppForm({
    ...formOpts,
    defaultValues: {
      site_voltage_id: search.build.site_voltage_id,
      power_unit_id: search.build.power_unit_id,
    },
    validators: {
      onSubmit: buildFormSchema,
    },
    onSubmitMeta: defaultMeta,
    onSubmit: ({ value, meta: { step } }) => {
      navigate({
        to: ".",
        search: (prev) => ({
          ...prev,
          step,
          build: {
            ...prev.build,
            site_voltage_id: value.site_voltage_id,
            power_unit_id: value.power_unit_id,
          },
        }),
      });
    },
    onSubmitInvalid: (errors) => {
      const messages: string[] = [];
      (errors.formApi.getAllErrors().form.errors ?? []).forEach((error) => {
        console.log({ error });
        Object.values(error ?? {}).forEach((err) => {
          err.forEach((e) => {
            if (e.message !== undefined) {
              messages.push(e.message);
            }
          });
        });
      });
      messages.forEach((error) => {
        toast.error(error);
      });
    },
  });
  const {
    data: siteVoltage,
    isSuccess: siteVoltageSuccess,
    isLoading: siteVoltageLoading,
  } = $api.useQuery("get", "/v1/pricing/voltage/");
  const {
    data: powerUnit,
    isSuccess: isSuccessPowerUnit,
    isLoading: powerUnitLoading,
  } = $api.useQuery("get", "/v1/pricing/power-unit/", {
    params: {
      query: { unit_type: null },
    },
  });
  const powerUnitField = "power_unit_id";
  const siteVoltageField = "site_voltage_id";
  const wizard = useBuildPriceWizardRef();
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        const step = getSubmitter(e);
        form.handleSubmit({ step });
      }}
      id={"2"}
      className="flex w-full flex-col gap-4"
    >
      <form.AppField
        name={powerUnitField}
        children={(field) => {
          return (
            <>
              <div className="flex w-full flex-wrap items-end justify-start gap-x-4">
                <H3>Select Power Unit</H3>
                {field.state.meta.errors.length > 0 && (
                  <>
                    {field.state.meta.errors.map((error, idx) => (
                      <Error key={idx}>*{error?.message}</Error>
                    ))}
                  </>
                )}
              </div>
              <field.TableField
                data={isSuccessPowerUnit ? powerUnit.result : []}
                isSuccess={isSuccessPowerUnit}
                isLoading={powerUnitLoading}
                loadingRows={7}
                columns={powerUnitColumns(field.state.value)}
                id={powerUnitField}
                onRowSelect={(row, selected) => {
                  wizard.send({
                    type: "setPowerUnit",
                    powerUnit: selected
                      ? row.original
                      : powerUnitSchema.parse({}),
                  });
                }}
              />
            </>
          );
        }}
      />
      <form.AppField
        name={siteVoltageField}
        children={(field) => {
          return (
            <>
              <div className="flex w-full flex-wrap items-end justify-start gap-4">
                <H3>Select Site Voltage</H3>
                {field.state.meta.errors.length > 0 && (
                  <>
                    {field.state.meta.errors.map((error, idx) => (
                      <Error key={idx}>{error?.message}</Error>
                    ))}
                  </>
                )}
              </div>
              <field.TableField
                data={siteVoltageSuccess ? siteVoltage.result : []}
                isSuccess={siteVoltageSuccess}
                isLoading={siteVoltageLoading}
                columns={siteVoltageColumns(field.state.value)}
                loadingRows={4}
                id={siteVoltageField}
                onRowSelect={(row, selected) => {
                  wizard.send({
                    type: "setSiteVoltage",
                    siteVoltage: selected
                      ? row.original
                      : siteVoltageSchema.parse({}),
                  });
                }}
              />
            </>
          );
        }}
      />
      <form.Subscribe
        selector={(state) => state.values}
        children={(values) => {
          return (
            <div className="grid w-full grid-cols-2 items-center gap-4 justify-self-end-safe pt-8">
              <Button
                className="mr-0 ml-auto w-full max-w-md"
                variant={"outline"}
                asChild
              >
                <Link
                  to="."
                  search={(prev) => ({
                    ...prev,
                    step: 1,
                    build: {
                      ...prev.build,
                      site_voltage: values.site_voltage_id,
                      power_unit_id: values.power_unit_id,
                    },
                  })}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  <span className="hidden md:block">Back to Pump Top</span>
                  <span className="block md:hidden">Back</span>
                </Link>
              </Button>
              <Button
                variant={"default"}
                className="mr-auto ml-0 w-full max-w-md"
                onClick={() => {
                  form.handleSubmit({ step: 3 });
                }}
              >
                Options
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          );
        }}
      />
      <form.AppForm>
        <form.SubmitErrorForm />
      </form.AppForm>
      <Separator />
      <Total />
      <form.Subscribe
        selector={(state) => state.values}
        children={() => {
          return (
            <div className="flex items-center justify-center sm:justify-end">
              <Button
                variant={"active"}
                className="flex w-full max-w-2xs items-center justify-center"
                onClick={() => {
                  form.handleSubmit({ step: 5 });
                }}
              >
                <FileCheck className="h-4 w-4" />
                <span>Request Quote</span>
              </Button>
            </div>
          );
        }}
      />
    </form>
  );
}

export { BuildForm };
