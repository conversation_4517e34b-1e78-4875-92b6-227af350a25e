import {
  createFormHook,
  createFormHookContexts,
  formOptions,
} from "@tanstack/react-form";
import { TableField } from "../table/pricing-table";
import { SubmitErrorForm } from "./errors";

export const formOpts = formOptions({
  defaultValues: {
    site_voltage_id: 0,
    power_unit_id: 0,
  },
});

export const { fieldContext, useFieldContext, formContext, useFormContext } =
  createFormHookContexts();

export const { useAppForm, withForm } = createFormHook({
  fieldComponents: { TableField },
  formComponents: { SubmitErrorForm },
  fieldContext,
  formContext,
});
