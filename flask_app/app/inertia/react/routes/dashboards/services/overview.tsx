import { createFileRoute } from "@tanstack/react-router";
import { Link } from "@tanstack/react-router";
import { useState } from "react";
import { CostByModelChart } from "@/components/service-dashboard/cost-by-model-chart";
import { CostTrendsChart } from "@/components/service-dashboard/cost-trends-chart";
import { HighCostUnitsTable } from "@/components/service-dashboard/high-cost-units-table";
import { StructureWorkOrdersTable } from "@/components/service-dashboard/structure-work-orders-table";
import { WorkOrderPartsTable } from "@/components/service-dashboard/work-order-parts-table";
import { DrillDownBreadcrumb } from "@/components/service-dashboard/drill-down-breadcrumb";
import {
  ServiceCostCards,
  TotalServiceCosts,
  AverageServiceCost,
  PartsLaborRatio,
} from "@/components/service-dashboard/service-cost-cards";
import {
  EnhancedKPICards,
  CostEfficiencyCard,
  ServiceQualityCard,
  PartsEfficiencyCard,
  OutlierAlertsCard,
} from "@/components/service-dashboard/enhanced-kpi-cards";
import { TechnicianPerformanceDashboard } from "@/components/service-dashboard/technician-performance-dashboard";
import { CustomerProfitabilityDashboard } from "@/components/service-dashboard/customer-profitability-dashboard";
import { OutlierAlertsTable } from "@/components/service-dashboard/outlier-alerts-table";
import UnitDeepDiveAnalysis from "@/components/service-dashboard/unit-deep-dive-analysis";
import ServiceIntervalOptimization from "@/components/service-dashboard/service-interval-optimization";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
export const Route = createFileRoute("/services/overview")({
  component: ServiceOverview,
});

function ServiceOverview() {
  const [selectedUnitId, setSelectedUnitId] = useState<number | null>(null);
  const [currentView, setCurrentView] = useState<'dashboard' | 'unit-analysis'>('dashboard');

  const handleUnitSelect = (structureId: number) => {
    setSelectedUnitId(structureId);
    setCurrentView('unit-analysis');
  };

  const handleBackToDashboard = () => {
    setSelectedUnitId(null);
    setCurrentView('dashboard');
  };

  if (currentView === 'unit-analysis' && selectedUnitId) {
    return (
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="px-4 lg:px-6">
              <UnitDeepDiveAnalysis 
                structureId={selectedUnitId}
                onBack={handleBackToDashboard}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
            {/* Page Header */}
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">
                  Service Analytics Dashboard
                </h1>
                <p className="mt-1 text-sm text-gray-600">
                  Advanced cost analysis and problem detection (All amounts in
                  CAD)
                </p>
              </div>
              <Link
                to="/"
                className="rounded bg-gray-100 px-4 py-2 text-sm font-medium text-gray-800 hover:bg-gray-200"
              >
                Back to Dashboards
              </Link>
            </div>

            {/* Basic KPI Cards */}
            <div className="mb-6">
              <h2 className="mb-3 text-lg font-semibold">
                Service Cost Overview
              </h2>
              <ServiceCostCards>
                <TotalServiceCosts />
                <AverageServiceCost />
                <PartsLaborRatio />
              </ServiceCostCards>
            </div>

            {/* Enhanced KPI Cards */}
            <div className="mb-8">
              <h2 className="mb-3 text-lg font-semibold">
                Performance Metrics
              </h2>
              <EnhancedKPICards>
                <CostEfficiencyCard />
                <ServiceQualityCard />
                <PartsEfficiencyCard />
                <OutlierAlertsCard />
              </EnhancedKPICards>
            </div>

            {/* Phase 2: Advanced Analytics Tabs */}
            <div className="mb-8">
              <h2 className="mb-3 text-lg font-semibold">
                🔍 Advanced Analytics & Optimization
              </h2>
              <Tabs defaultValue="problem-detection" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="problem-detection">🚨 Problem Detection</TabsTrigger>
                  <TabsTrigger value="performance">👥 Performance Analytics</TabsTrigger>
                  <TabsTrigger value="optimization">🎯 Service Optimization</TabsTrigger>
                  <TabsTrigger value="insights">📊 Predictive Insights</TabsTrigger>
                </TabsList>

                <TabsContent value="problem-detection" className="space-y-6">
                  <div>
                    <h3 className="mb-3 text-md font-medium">Outlier Detection & Early Warnings</h3>
                    <OutlierAlertsTable onUnitSelect={handleUnitSelect} />
                  </div>
                </TabsContent>

                <TabsContent value="performance" className="space-y-6">
                  <div>
                    <h3 className="mb-3 text-md font-medium">Technician & Customer Performance</h3>
                    <div className="space-y-6">
                      <TechnicianPerformanceDashboard />
                      <CustomerProfitabilityDashboard />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="optimization" className="space-y-6">
                  <div>
                    <h3 className="mb-3 text-md font-medium">Service Interval Optimization</h3>
                    <ServiceIntervalOptimization />
                  </div>
                </TabsContent>

                <TabsContent value="insights" className="space-y-6">
                  <div>
                    <h3 className="mb-3 text-md font-medium">Predictive Analytics & Root Cause Analysis</h3>
                    <div className="text-center py-12 text-gray-500">
                      <h4 className="text-lg font-medium mb-2">Coming in Phase 3</h4>
                      <p>Advanced machine learning predictions and automated root cause analysis</p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Service Cost Analysis Charts */}
            <div className="mb-8">
              <h2 className="mb-3 text-lg font-semibold">
                📊 Cost Trends & Analysis
              </h2>
              <div className="mb-6 grid auto-rows-min gap-4 md:grid-cols-2">
                <CostByModelChart />
                <CostTrendsChart />
              </div>
            </div>

            {/* High-Cost Units Table */}
            <div className="mb-8">
              <h2 className="mb-3 text-lg font-semibold">
                💰 High-Cost Unit Analysis
              </h2>
              <HighCostUnitsTable />
            </div>

            {/* Drill-Down Tables with Visual Hierarchy */}
            <div className="mb-8">
              <h2 className="mb-3 text-lg font-semibold">
                🔍 Detailed Analysis
              </h2>
              <div className="space-y-6 pb-20">
                <div className="border-l-4 border-blue-200 pl-4">
                  <StructureWorkOrdersTable />
                </div>
                <div className="border-l-4 border-green-200 pl-8">
                  <WorkOrderPartsTable />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Drill-Down Breadcrumb */}
      <DrillDownBreadcrumb />
    </div>
  );
}
