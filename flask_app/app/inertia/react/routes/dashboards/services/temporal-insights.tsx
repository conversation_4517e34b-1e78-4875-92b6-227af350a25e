import { createFileRoute } from "@tanstack/react-router";
import { Link } from "@tanstack/react-router";
import TemporalInsightsDashboard from "@/components/service-dashboard/temporal-insights-dashboard";
import FilterSection from "@/components/service-dashboard/filter-section";
import { ServiceCostStoreContext, store } from "@/stores/service-costs/hooks";

export const Route = createFileRoute("/services/temporal-insights")({
  component: TemporalInsightsPage,
});

function TemporalInsightsPage() {
  return (
    <ServiceCostStoreContext.Provider value={store}>
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="px-4 lg:px-6">
              {/* Page Header */}
              <div className="mb-6 flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold">
                    Temporal & Operational Insights
                  </h1>
                  <p className="mt-1 text-sm text-gray-600">
                    Advanced seasonal, geographic, and operational analysis
                  </p>
                </div>
                <Link
                  to="/services/overview"
                  className="rounded bg-gray-100 px-4 py-2 text-sm font-medium text-gray-800 hover:bg-gray-200"
                >
                  Back to Overview
                </Link>
              </div>

              {/* Filter Section */}
              <FilterSection showGeographic={true} />

              {/* Temporal Insights Dashboard */}
              <TemporalInsightsDashboard />
            </div>
          </div>
        </div>
      </div>
    </ServiceCostStoreContext.Provider>
  );
}