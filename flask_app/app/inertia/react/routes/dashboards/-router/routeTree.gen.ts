/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './../__root'
import { Route as UnitsImport } from './../units'
import { Route as ServicesImport } from './../services'
import { Route as SalesRouteImport } from './../_sales/route'
import { Route as IndexImport } from './../index'
import { Route as UnitsIndexImport } from './../units/index'
import { Route as ServicesIndexImport } from './../services/index'
import { Route as ServicesTemporalInsightsImport } from './../services/temporal-insights'
import { Route as ServicesOverviewImport } from './../services/overview'
import { Route as SalesSalesIndexImport } from './../_sales/sales.index'
import { Route as SalesSalesPivotAnalysisImport } from './../_sales/sales.pivot-analysis'

// Create/Update Routes

const UnitsRoute = UnitsImport.update({
  id: '/units',
  path: '/units',
  getParentRoute: () => rootRoute,
} as any)

const ServicesRoute = ServicesImport.update({
  id: '/services',
  path: '/services',
  getParentRoute: () => rootRoute,
} as any)

const SalesRouteRoute = SalesRouteImport.update({
  id: '/_sales',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const UnitsIndexRoute = UnitsIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => UnitsRoute,
} as any)

const ServicesIndexRoute = ServicesIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => ServicesRoute,
} as any)

const ServicesTemporalInsightsRoute = ServicesTemporalInsightsImport.update({
  id: '/temporal-insights',
  path: '/temporal-insights',
  getParentRoute: () => ServicesRoute,
} as any)

const ServicesOverviewRoute = ServicesOverviewImport.update({
  id: '/overview',
  path: '/overview',
  getParentRoute: () => ServicesRoute,
} as any)

const SalesSalesIndexRoute = SalesSalesIndexImport.update({
  id: '/sales/',
  path: '/sales/',
  getParentRoute: () => SalesRouteRoute,
} as any)

const SalesSalesPivotAnalysisRoute = SalesSalesPivotAnalysisImport.update({
  id: '/sales/pivot-analysis',
  path: '/sales/pivot-analysis',
  getParentRoute: () => SalesRouteRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_sales': {
      id: '/_sales'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof SalesRouteImport
      parentRoute: typeof rootRoute
    }
    '/services': {
      id: '/services'
      path: '/services'
      fullPath: '/services'
      preLoaderRoute: typeof ServicesImport
      parentRoute: typeof rootRoute
    }
    '/units': {
      id: '/units'
      path: '/units'
      fullPath: '/units'
      preLoaderRoute: typeof UnitsImport
      parentRoute: typeof rootRoute
    }
    '/services/overview': {
      id: '/services/overview'
      path: '/overview'
      fullPath: '/services/overview'
      preLoaderRoute: typeof ServicesOverviewImport
      parentRoute: typeof ServicesImport
    }
    '/services/temporal-insights': {
      id: '/services/temporal-insights'
      path: '/temporal-insights'
      fullPath: '/services/temporal-insights'
      preLoaderRoute: typeof ServicesTemporalInsightsImport
      parentRoute: typeof ServicesImport
    }
    '/services/': {
      id: '/services/'
      path: '/'
      fullPath: '/services/'
      preLoaderRoute: typeof ServicesIndexImport
      parentRoute: typeof ServicesImport
    }
    '/units/': {
      id: '/units/'
      path: '/'
      fullPath: '/units/'
      preLoaderRoute: typeof UnitsIndexImport
      parentRoute: typeof UnitsImport
    }
    '/_sales/sales/pivot-analysis': {
      id: '/_sales/sales/pivot-analysis'
      path: '/sales/pivot-analysis'
      fullPath: '/sales/pivot-analysis'
      preLoaderRoute: typeof SalesSalesPivotAnalysisImport
      parentRoute: typeof SalesRouteImport
    }
    '/_sales/sales/': {
      id: '/_sales/sales/'
      path: '/sales'
      fullPath: '/sales'
      preLoaderRoute: typeof SalesSalesIndexImport
      parentRoute: typeof SalesRouteImport
    }
  }
}

// Create and export the route tree

interface SalesRouteRouteChildren {
  SalesSalesPivotAnalysisRoute: typeof SalesSalesPivotAnalysisRoute
  SalesSalesIndexRoute: typeof SalesSalesIndexRoute
}

const SalesRouteRouteChildren: SalesRouteRouteChildren = {
  SalesSalesPivotAnalysisRoute: SalesSalesPivotAnalysisRoute,
  SalesSalesIndexRoute: SalesSalesIndexRoute,
}

const SalesRouteRouteWithChildren = SalesRouteRoute._addFileChildren(
  SalesRouteRouteChildren,
)

interface ServicesRouteChildren {
  ServicesOverviewRoute: typeof ServicesOverviewRoute
  ServicesTemporalInsightsRoute: typeof ServicesTemporalInsightsRoute
  ServicesIndexRoute: typeof ServicesIndexRoute
}

const ServicesRouteChildren: ServicesRouteChildren = {
  ServicesOverviewRoute: ServicesOverviewRoute,
  ServicesTemporalInsightsRoute: ServicesTemporalInsightsRoute,
  ServicesIndexRoute: ServicesIndexRoute,
}

const ServicesRouteWithChildren = ServicesRoute._addFileChildren(
  ServicesRouteChildren,
)

interface UnitsRouteChildren {
  UnitsIndexRoute: typeof UnitsIndexRoute
}

const UnitsRouteChildren: UnitsRouteChildren = {
  UnitsIndexRoute: UnitsIndexRoute,
}

const UnitsRouteWithChildren = UnitsRoute._addFileChildren(UnitsRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof SalesRouteRouteWithChildren
  '/services': typeof ServicesRouteWithChildren
  '/units': typeof UnitsRouteWithChildren
  '/services/overview': typeof ServicesOverviewRoute
  '/services/temporal-insights': typeof ServicesTemporalInsightsRoute
  '/services/': typeof ServicesIndexRoute
  '/units/': typeof UnitsIndexRoute
  '/sales/pivot-analysis': typeof SalesSalesPivotAnalysisRoute
  '/sales': typeof SalesSalesIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof SalesRouteRouteWithChildren
  '/services/overview': typeof ServicesOverviewRoute
  '/services/temporal-insights': typeof ServicesTemporalInsightsRoute
  '/services': typeof ServicesIndexRoute
  '/units': typeof UnitsIndexRoute
  '/sales/pivot-analysis': typeof SalesSalesPivotAnalysisRoute
  '/sales': typeof SalesSalesIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_sales': typeof SalesRouteRouteWithChildren
  '/services': typeof ServicesRouteWithChildren
  '/units': typeof UnitsRouteWithChildren
  '/services/overview': typeof ServicesOverviewRoute
  '/services/temporal-insights': typeof ServicesTemporalInsightsRoute
  '/services/': typeof ServicesIndexRoute
  '/units/': typeof UnitsIndexRoute
  '/_sales/sales/pivot-analysis': typeof SalesSalesPivotAnalysisRoute
  '/_sales/sales/': typeof SalesSalesIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/services'
    | '/units'
    | '/services/overview'
    | '/services/temporal-insights'
    | '/services/'
    | '/units/'
    | '/sales/pivot-analysis'
    | '/sales'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/services/overview'
    | '/services/temporal-insights'
    | '/services'
    | '/units'
    | '/sales/pivot-analysis'
    | '/sales'
  id:
    | '__root__'
    | '/'
    | '/_sales'
    | '/services'
    | '/units'
    | '/services/overview'
    | '/services/temporal-insights'
    | '/services/'
    | '/units/'
    | '/_sales/sales/pivot-analysis'
    | '/_sales/sales/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  SalesRouteRoute: typeof SalesRouteRouteWithChildren
  ServicesRoute: typeof ServicesRouteWithChildren
  UnitsRoute: typeof UnitsRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  SalesRouteRoute: SalesRouteRouteWithChildren,
  ServicesRoute: ServicesRouteWithChildren,
  UnitsRoute: UnitsRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_sales",
        "/services",
        "/units"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_sales": {
      "filePath": "_sales/route.tsx",
      "children": [
        "/_sales/sales/pivot-analysis",
        "/_sales/sales/"
      ]
    },
    "/services": {
      "filePath": "services.tsx",
      "children": [
        "/services/overview",
        "/services/temporal-insights",
        "/services/"
      ]
    },
    "/units": {
      "filePath": "units.tsx",
      "children": [
        "/units/"
      ]
    },
    "/services/overview": {
      "filePath": "services/overview.tsx",
      "parent": "/services"
    },
    "/services/temporal-insights": {
      "filePath": "services/temporal-insights.tsx",
      "parent": "/services"
    },
    "/services/": {
      "filePath": "services/index.tsx",
      "parent": "/services"
    },
    "/units/": {
      "filePath": "units/index.tsx",
      "parent": "/units"
    },
    "/_sales/sales/pivot-analysis": {
      "filePath": "_sales/sales.pivot-analysis.tsx",
      "parent": "/_sales"
    },
    "/_sales/sales/": {
      "filePath": "_sales/sales.index.tsx",
      "parent": "/_sales"
    }
  }
}
ROUTE_MANIFEST_END */
