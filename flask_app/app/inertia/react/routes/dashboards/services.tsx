import { createFileRoute, Outlet } from "@tanstack/react-router";
import { DashboardSidebar } from "@/components/sales-dashboard/sidebar";
import { SiteNavigation } from "@/components/site-navigation";
import {
  SidebarMenuItem,
  SidebarProvider,
} from "@/components/ui/sidebar";
import { ServiceCostStoreContext, store } from "@/stores/service-costs/hooks";
import {
  CustomerFilter,
  ServiceTypeFilter,
  ModelTypeFilter,
  UnitTypeFilter,
  TechnicianFilter,
  PartCategoryFilter,
  ServiceDateFilter,
  ResetFilters,
} from "@/components/service-dashboard/filters";

export const Route = createFileRoute("/services")({
  component: ServicesLayout,
  loader: ({ context }) => {
    // Set breadcrumbs for all service routes
    context.crumbs = [
      {
        text: "Service",
        url: "/services",
        is_current_path: true,
      },
    ];
  },
});

function ServicesLayout() {
  return (
    <ServiceCostStoreContext.Provider value={store}>
      <div className="relative flex h-full w-full flex-col">
        <SidebarProvider className="flex flex-col">
          <div className="sticky top-1 flex flex-1">
            <div className="sticky top-0 h-screen">
              <DashboardSidebar variant="sidebar">
                <SidebarMenuItem>
                  <CustomerFilter />
                  <ServiceTypeFilter />
                  <ModelTypeFilter />
                  <UnitTypeFilter />
                  <TechnicianFilter />
                  <PartCategoryFilter />
                  <ServiceDateFilter />
                  <ResetFilters />
                </SidebarMenuItem>
              </DashboardSidebar>
            </div>
            <div className="flex flex-1 flex-col gap-4">
              <SiteNavigation
                breadcrumbs={{
                  text: "Dashboards",
                  url: "/",
                  children: [
                    {
                      is_current_path: true,
                      text: "Service",
                      url: "/services",
                      children: [],
                    },
                  ],
                  is_current_path: false,
                }}
              />
              <div className="flex flex-1 flex-col p-4">
                <Outlet />
              </div>
            </div>
          </div>
        </SidebarProvider>
      </div>
    </ServiceCostStoreContext.Provider>
  );
}
