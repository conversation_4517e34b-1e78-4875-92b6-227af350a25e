import { ChevronDown, Slash } from "lucide-react";
import {
  Breadcrumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "./ui/breadcrumb";
import { Link } from "@tanstack/react-router";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
} from "./ui/dropdown-menu";
import { Separator } from "./ui/separator";
import { SidebarTrigger } from "./ui/sidebar";
import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { Breadcrumbs } from "@/types/breadcrumbs";

export function BasicBreadcrumb({
  breadcrumb,
}: {
  breadcrumb: Breadcrumbs | Breadcrumbs[];
}) {
  if (Array.isArray(breadcrumb) && breadcrumb.length > 1) {
    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger className="flex items-center gap-1">
            {breadcrumb.find((item) => item.is_current_path)?.text ?? "..."}
            <ChevronDown />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {breadcrumb.map((item) => (
              <DropdownMenuItem key={item.url} asChild>
                <Link to={item.url}>{item.text}</Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
        {breadcrumb[0].children.length > 0 && (
          <>
            <BreadcrumbSeparator>
              <Slash />
            </BreadcrumbSeparator>
            <BasicBreadcrumb breadcrumb={breadcrumb[0].children} />
          </>
        )}
      </>
    );
  } else {
    breadcrumb = Array.isArray(breadcrumb) ? breadcrumb[0] : breadcrumb;
    return (
      <>
        <BreadcrumbItem>
          <BreadcrumbLink href={breadcrumb.url} asChild>
            <Link to={breadcrumb.url}>{breadcrumb.text}</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        {breadcrumb.children.length > 0 && (
          <>
            <BreadcrumbSeparator>
              <Slash />
            </BreadcrumbSeparator>
            <BasicBreadcrumb breadcrumb={breadcrumb.children} />
          </>
        )}
      </>
    );
  }
}

export function SiteNavigation({ breadcrumbs }: { breadcrumbs: Breadcrumbs }) {
  return (
    <header className="sticky top-0 z-50 flex h-12 shrink-0 items-center gap-2 border-b bg-white transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mx-2 data-[orientation=vertical]:h-4"
        />
        <Breadcrumb>
          <BreadcrumbList>
            <BasicBreadcrumb breadcrumb={breadcrumbs} />
          </BreadcrumbList>
        </Breadcrumb>
      </div>
    </header>
  );
}
