import React, { useState } from 'react';
import { AlertTriangle, Bug, Zap, Globe, Copy, Activity } from 'lucide-react';
import { errorContextCollector } from '../utils/error-context-collector';
import { SimpleErrorTest } from './simple-error-test';

export const TestErrorHandler: React.FC = () => {
  const [errorCount, setErrorCount] = useState(0);
  const [testData, setTestData] = useState<string>('');

  // Simulate some user activity before errors
  const simulateUserActivity = () => {
    // Generate some console logs
    console.log('User activity simulation started');
    console.info('Loading user preferences...');
    console.warn('Cache is getting full');
    
    // Make some API calls
    fetch('/api/user/profile').catch(() => {});
    fetch('/api/settings/preferences').catch(() => {});
    
    // Set some context
    errorContextCollector.setApplicationContext({
      userId: 'test-user-123',
      sessionId: 'session-abc-456',
      featureFlags: { enhancedErrors: true, darkMode: false }
    });
    
    // Simulate form input
    setTestData('Some test input data');
    
    console.log('User activity simulation completed');
  };

  const triggerJavaScriptError = () => {
    setErrorCount(prev => prev + 1);
    simulateUserActivity();
    // This will trigger a JavaScript error
    throw new Error(`Test JavaScript Error #${errorCount + 1}: Something went wrong in the application!`);
  };

  const triggerTypeError = () => {
    setErrorCount(prev => prev + 1);
    // This will trigger a TypeError
    // @ts-ignore
    const obj: any = null;
    obj.nonExistentMethod(); // This will throw "Cannot read property 'nonExistentMethod' of null"
  };

  const triggerPromiseRejection = () => {
    setErrorCount(prev => prev + 1);
    // This will trigger an unhandled promise rejection
    Promise.reject(new Error(`Test Promise Rejection #${errorCount + 1}: Async operation failed!`));
  };

  const triggerNetworkError = async () => {
    setErrorCount(prev => prev + 1);
    // This will trigger a network error (404)
    try {
      await fetch('/api/non-existent-endpoint-' + Date.now());
    } catch (error) {
      console.log('Network error caught:', error);
    }
  };

  const triggerMultipleErrors = () => {
    // Trigger multiple errors in sequence
    setTimeout(() => {
      console.error('Error 1: Database connection failed');
    }, 100);
    
    setTimeout(() => {
      console.error(new Error('Error 2: Authentication token expired'));
    }, 200);
    
    setTimeout(() => {
      Promise.reject('Error 3: API request timeout');
    }, 300);
    
    setTimeout(() => {
      // @ts-ignore
      undefined.toString(); // Error 4: Cannot read property of undefined
    }, 400);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">Enhanced Error Handler Test Suite</h2>
        
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>⚠️ Warning:</strong> These buttons will trigger real errors with enhanced context tracking. 
            In development mode, you'll see detailed diagnostics including user actions, console logs, network requests, and more.
          </p>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-gray-700">Test Setup</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={simulateUserActivity}
              className="flex items-center justify-center space-x-2 p-4 bg-green-50 hover:bg-green-100 text-green-700 rounded-lg transition-colors border border-green-200"
            >
              <Activity className="h-5 w-5" />
              <span className="font-medium">Simulate User Activity</span>
            </button>
            
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
              <input
                type="text"
                value={testData}
                onChange={(e) => setTestData(e.target.value)}
                placeholder="Type something to track input events..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <button
            onClick={triggerJavaScriptError}
            className="flex items-center justify-center space-x-2 p-4 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg transition-colors border border-red-200"
          >
            <Bug className="h-5 w-5" />
            <span className="font-medium">Trigger JavaScript Error</span>
          </button>

          <button
            onClick={triggerTypeError}
            className="flex items-center justify-center space-x-2 p-4 bg-orange-50 hover:bg-orange-100 text-orange-700 rounded-lg transition-colors border border-orange-200"
          >
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">Trigger Type Error</span>
          </button>

          <button
            onClick={triggerPromiseRejection}
            className="flex items-center justify-center space-x-2 p-4 bg-purple-50 hover:bg-purple-100 text-purple-700 rounded-lg transition-colors border border-purple-200"
          >
            <Zap className="h-5 w-5" />
            <span className="font-medium">Trigger Promise Rejection</span>
          </button>

          <button
            onClick={triggerNetworkError}
            className="flex items-center justify-center space-x-2 p-4 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg transition-colors border border-blue-200"
          >
            <Globe className="h-5 w-5" />
            <span className="font-medium">Trigger Network Error</span>
          </button>
        </div>

        <div className="border-t pt-4">
          <button
            onClick={triggerMultipleErrors}
            className="flex items-center justify-center space-x-2 w-full p-4 bg-gradient-to-r from-red-50 to-orange-50 hover:from-red-100 hover:to-orange-100 text-red-700 rounded-lg transition-all border border-red-200 font-medium"
          >
            <Copy className="h-5 w-5" />
            <span>Trigger Multiple Errors (4 different types)</span>
          </button>
        </div>

        <div className="mt-4 text-center text-sm text-gray-600">
          Errors triggered: {errorCount}
        </div>
        
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-2">Direct Test:</h3>
          <SimpleErrorTest />
        </div>

        <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-2">Expected Behavior:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-700 mb-2">Development Mode (Enhanced):</p>
              <ul className="text-gray-600 space-y-1">
                <li>✓ Full error details with tabbed interface</li>
                <li>✓ <strong>User Actions tab:</strong> Click trail & interactions</li>
                <li>✓ <strong>Console tab:</strong> Recent console logs</li>
                <li>✓ <strong>Network tab:</strong> API calls & responses</li>
                <li>✓ <strong>Browser tab:</strong> System diagnostics</li>
                <li>✓ <strong>Storage tab:</strong> LocalStorage/SessionStorage</li>
                <li>✓ Component stack for React errors</li>
                <li>✓ Application context (routes, user ID, etc.)</li>
                <li>✓ Copy all diagnostic data to clipboard</li>
              </ul>
            </div>
            <div>
              <p className="font-medium text-gray-700 mb-2">Production Mode:</p>
              <ul className="text-gray-600 space-y-1">
                <li>✓ Generic error message only</li>
                <li>✓ No technical details visible</li>
                <li>✓ No user data exposed</li>
                <li>✓ Only "Email Support" button</li>
                <li>✓ Basic error info in email</li>
                <li>✗ No copy functionality</li>
                <li>✗ No diagnostic tabs</li>
                <li>✗ No stack traces</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};