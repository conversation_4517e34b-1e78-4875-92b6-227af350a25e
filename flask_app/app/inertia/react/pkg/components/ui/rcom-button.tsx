import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Button } from "./button";
import { faTachometerAltFast } from "@fortawesome/free-solid-svg-icons";

export function RCOM({ showText = true }: { showText?: boolean }) {
  return (
    <Button variant="menu-primary" asChild>
      <a href="/rcom/">
        <FontAwesomeIcon icon={faTachometerAltFast} />
        {showText && "RCOM"}
      </a>
    </Button>
  );
}
