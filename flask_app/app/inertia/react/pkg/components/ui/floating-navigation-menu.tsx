import * as React from "react";
import {
  useFloating,
  autoUpdate,
  offset,
  shift,
  useHover,
  useFocus,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  arrow,
  FloatingArrow,
  autoPlacement,
} from "@floating-ui/react";
import { ChevronDownIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface FloatingNavigationMenuProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export function FloatingNavigationMenu({
  trigger,
  children,
  className,
}: FloatingNavigationMenuProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const arrowRef = React.useRef(null);

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    middleware: [
      offset(8), // 8px offset from trigger
      // flip({
      //   fallbackAxisSideDirection: "start",
      //   fallbackPlacements: ["right-start", "left-start", "top-start"],
      // }),
      shift({ padding: 8 }),
      arrow({
        element: arrowRef,
      }),
      autoPlacement({}),
    ],
    whileElementsMounted: autoUpdate,
  });

  const hover = useHover(context, {
    move: false,
    delay: { open: 100, close: 300 },
  });
  const focus = useFocus(context);
  const dismiss = useDismiss(context);
  const role = useRole(context);

  const { getReferenceProps, getFloatingProps } = useInteractions([
    hover,
    focus,
    dismiss,
    role,
  ]);

  return (
    <>
      <div
        ref={refs.setReference}
        {...getReferenceProps()}
        className="group hover:text-ijack-green relative flex cursor-pointer items-center gap-2 text-sm font-medium text-gray-400 transition-all"
      >
        {trigger}
        <ChevronDownIcon
          className={cn(
            "size-3 transition-transform duration-300",
            isOpen && "rotate-180",
          )}
          aria-hidden="true"
        />
      </div>

      {isOpen && (
        <FloatingPortal>
          <div
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
            className={cn(
              "min-w-56 rounded-md border border-gray-700 bg-gray-800 p-1 text-gray-100 shadow-lg",
              className,
            )}
          >
            <FloatingArrow
              ref={arrowRef}
              context={context}
              className="fill-gray-800"
            />
            {children}
          </div>
        </FloatingPortal>
      )}
    </>
  );
}

interface FloatingNavigationItemProps {
  href: string;
  children: React.ReactNode;
  onClick?: () => void;
}

export function FloatingNavigationItem({
  href,
  children,
  onClick,
}: FloatingNavigationItemProps) {
  return (
    <a
      href={href}
      onClick={onClick}
      className="block rounded-sm px-3 py-2 text-sm text-gray-300 transition-colors hover:bg-gray-700 hover:text-white"
    >
      {children}
    </a>
  );
}
