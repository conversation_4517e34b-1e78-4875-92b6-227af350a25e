/**
 * FloatingMenu Component
 *
 * A reusable floating menu component using Floating UI for precise positioning.
 * Provides dropdown functionality with automatic positioning, keyboard navigation,
 * and accessibility features.
 */
import React, { useRef, ReactNode } from "react";
import { Slot } from "@radix-ui/react-slot";
import { cn } from "@/lib/utils";
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useClick,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
  arrow,
  FloatingArrow,
  Placement,
} from "@floating-ui/react";

interface FloatingMenuProps {
  /** Whether the menu is open */
  isOpen: boolean;
  /** Callback when menu open state changes */
  onOpenChange: (open: boolean) => void;
  /** The trigger element that opens the menu */
  trigger: ReactNode;
  /** The content to display in the floating menu */
  children: ReactNode;
  /** Placement of the menu relative to the trigger */
  placement?: Placement;
  /** Offset distance from the trigger in pixels */
  offsetDistance?: number;
  /** Padding from viewport edges in pixels */
  shiftPadding?: number;
  /** Whether to show an arrow pointing to the trigger */
  showArrow?: boolean;
  /** Additional CSS classes for the menu container */
  className?: string;
  /** Custom styles for the menu container */
  style?: React.CSSProperties;
  /** Whether to render in a portal (outside normal DOM tree) */
  portal?: boolean;
}

export function FloatingMenu({
  isOpen,
  onOpenChange,
  trigger,
  children,
  placement = "bottom-start",
  offsetDistance = 10,
  shiftPadding = 8,
  showArrow = true,
  className = "",
  style = {},
  portal = true,
}: FloatingMenuProps) {
  const arrowRef = useRef(null);

  // Floating UI setup
  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: onOpenChange,
    middleware: [
      offset(offsetDistance), // Spacing from the trigger
      flip(), // Flip to opposite side if no space
      shift({ padding: shiftPadding }), // Keep on screen with padding
      ...(showArrow ? [arrow({ element: arrowRef })] : []), // Conditional arrow
    ],
    whileElementsMounted: autoUpdate, // Auto update position
    placement: placement,
  });

  // Interaction hooks
  const click = useClick(context);
  const dismiss = useDismiss(context);
  const role = useRole(context);

  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    dismiss,
    role,
  ]);

  // Default menu styles
  const defaultMenuStyles = {
    zIndex: 50,
    backgroundColor: "white",
    borderRadius: "0.375rem", // rounded-md
    border: "1px solid #e5e7eb", // border-gray-200
    boxShadow:
      "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", // shadow-lg
    padding: "0.25rem", // p-1
    ...style,
  };

  const menuContent = (
    <div
      ref={refs.setFloating}
      style={{ ...floatingStyles, ...defaultMenuStyles }}
      {...getFloatingProps()}
      className={`floating-menu ${className}`}
    >
      {/* Arrow pointing to the trigger */}
      {showArrow && (
        <FloatingArrow
          ref={arrowRef}
          context={context}
          className="fill-white"
        />
      )}

      {children}
    </div>
  );

  return (
    <>
      {/* Trigger element */}
      <div
        ref={refs.setReference}
        {...getReferenceProps()}
        className="inline-block"
      >
        {trigger}
      </div>

      {/* Floating menu content */}
      {isOpen &&
        (portal ? <FloatingPortal>{menuContent}</FloatingPortal> : menuContent)}
    </>
  );
}

/**
 * FloatingMenuTrigger Component
 *
 * A helper component for creating consistent trigger buttons
 */
interface FloatingMenuTriggerProps {
  /** The content of the trigger button */
  children: ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Whether the menu is currently open (for styling) */
  isOpen?: boolean;
  /** Click handler */
  onClick?: () => void;
}

export function FloatingMenuTrigger({
  children,
  className = "",
  isOpen = false,
  onClick,
}: FloatingMenuTriggerProps) {
  const baseClasses =
    "inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";
  const stateClasses = isOpen
    ? "bg-gray-100 text-gray-900"
    : "text-gray-700 hover:bg-gray-50 hover:text-gray-900";

  return (
    <button
      type="button"
      className={`${baseClasses} ${stateClasses} ${className}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
}

/**
 * FloatingMenuContent Component
 *
 * A helper component for creating consistent menu content containers
 */
interface FloatingMenuContentProps {
  /** The content to display */
  children: ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Minimum width for the content */
  minWidth?: string;
  /** Maximum width for the content */
  maxWidth?: string;
}

export function FloatingMenuContent({
  children,
  className = "",
  minWidth = "auto",
  maxWidth = "24rem", // max-w-sm
}: FloatingMenuContentProps) {
  return (
    <div
      className={`floating-menu-content ${className}`}
      style={{ minWidth, maxWidth }}
    >
      {children}
    </div>
  );
}

/**
 * FloatingMenuItem Component
 *
 * A helper component for creating consistent menu items
 */
interface FloatingMenuItemProps {
  /** The content of the menu item */
  children: ReactNode;
  /** Click handler */
  onClick?: () => void;
  /** Whether the item is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Whether to render as the child element instead of a button wrapper */
  asChild?: boolean;
}

export function FloatingMenuItem({
  children,
  onClick,
  disabled = false,
  className = "",
  asChild = false,
}: FloatingMenuItemProps) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      type={asChild ? undefined : "button"}
      className={cn(
        "block w-full rounded-md px-3 py-2 text-left text-sm text-gray-700 transition-colors",
        disabled
          ? "cursor-not-allowed opacity-50"
          : "cursor-pointer hover:bg-gray-100 hover:text-gray-900",
        className,
      )}
      onClick={disabled ? undefined : onClick}
      disabled={disabled}
      data-slot="floating-menu-item"
    >
      {children}
    </Comp>
  );
}
