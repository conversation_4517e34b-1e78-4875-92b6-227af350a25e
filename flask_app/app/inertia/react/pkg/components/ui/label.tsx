import * as React from "react";
import * as LabelPrimitive from "@radix-ui/react-label";
import { numericFormatter } from "react-number-format";

import { cn } from "@/lib/utils";

const Label = ({
  className,
  ...props
}: React.ComponentProps<typeof LabelPrimitive.Root>) => {
  return (
    <LabelPrimitive.Root
      data-slot="label"
      className={cn(
        "flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",
        className,
      )}
      {...props}
    />
  );
};

export const numericFunctionalityVariants = {
  integer: (value: string) =>
    numericFormatter(value, {
      allowLeadingZeros: false,
      allowNegative: false,
      decimalScale: 0,
      fixedDecimalScale: false,
      decimalSeparator: ".",
      thousandSeparator: ",",
      thousandsGroupStyle: "thousand",
    }),
  dollars: (value: string) =>
    numericFormatter(value, {
      allowLeadingZeros: false,
      allowNegative: true,
      decimalScale: 0,
      fixedDecimalScale: true,
      prefix: "$",
      thousandSeparator: ",",
      thousandsGroupStyle: "thousand",
      decimalSeparator: ".",
    }),
  currency: (value: string) =>
    numericFormatter(value, {
      allowLeadingZeros: false,
      allowNegative: true,
      decimalScale: 2,
      fixedDecimalScale: true,
      prefix: "$",
      thousandSeparator: ",",
      thousandsGroupStyle: "thousand",
      decimalSeparator: ".",
    }),
} as const;

type LabelNumericProps = {
  className?: string;
  value: string | number | null | undefined;
  empty?: string;
  variant: keyof typeof numericFunctionalityVariants;
};

Label.Numeric = ({
  className,
  value,
  empty = "0",
  variant,
  ...props
}: LabelNumericProps) => {
  let formattedValue = value;
  if (value === null || value === undefined) {
    formattedValue = empty;
  } else {
    formattedValue = numericFunctionalityVariants[variant](`${value}`);
  }
  return (
    <span className={cn("leading-none text-inherit", className)} {...props}>
      {formattedValue}
    </span>
  );
};

Label.displayName = LabelPrimitive.Root.displayName;

export { Label };
