import { cn } from "@/lib/utils";

const Skeleton = ({ className, ...props }: React.ComponentProps<"div">) => {
  return (
    <div
      data-slot="skeleton"
      className={cn("bg-primary/10 animate-pulse rounded-md shadow", className)}
      {...props}
    />
  );
};

Skeleton.Select = () => {
  return (
    <div className="space-2 flex gap-2">
      <Skeleton className="h-4 w-4" />
      <Skeleton className="h-4 w-40" />
    </div>
  );
};

export { Skeleton };
