import React, { useState } from "react";
import { ChevronsUpDown, Globe, XIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Muted } from "./typography";
type FilterMenuProps<T, K> = {
  options: T[];
  keyGetter: (item: T) => K;
  labelGetter?: (item: T) => string;
  onChange?: (selectedKeys: K[]) => void;
  selectedKeys: K[];
  setSelectedKeys: (selectedKeys: K[]) => void;
  placeholder?: string;
  allSelectedText?: string;
  noneSelectedText?: string;
  multipleSelectedText?: (selectedKeys: K[]) => string;
  emptyWhenAllSelected?: boolean;
  icon?: React.ReactNode;
  customSearchFunction?: (value: string, search: string) => number;
  nullable?: boolean;
};

export default function FilterMenu<T, K>({
  options = [],
  keyGetter,
  labelGetter = (item) => `${item}`,
  selectedKeys = [],
  setSelectedKeys = () => undefined,
  placeholder = "Search options...",
  allSelectedText = "All options",
  noneSelectedText = "No options selected",
  multipleSelectedText = (selectedKeys) =>
    `${selectedKeys.length} options selected`,
  emptyWhenAllSelected = false,
  icon = <Globe className="h-4 w-4" />,
  customSearchFunction = defaultSearchFunction,
  nullable = false,
}: FilterMenuProps<T, K>) {
  const [open, setOpen] = useState(false);
  const [internalKeys, setInternalKeys] = useState<K[]>(selectedKeys);
  const onOpenChange = (open: boolean) => {
    setOpen(open);
    if (!open) {
      // If the menu is closed and all options are selected, clear the selection
      if (
        emptyWhenAllSelected &&
        selectedKeys.length === options.length + (nullable ? 1 : 0)
      ) {
        setSelectedKeys([]);
      }
    }
  };

  const [searchValue, setSearchValue] = useState("");

  // Format the button text based on selection state
  const getButtonText = () => {
    if (
      selectedKeys.length === options.length + (nullable ? 1 : 0) &&
      options.length !== 0
    ) {
      return allSelectedText;
    } else if (selectedKeys.length === 0) {
      return noneSelectedText;
    } else {
      return multipleSelectedText(selectedKeys);
    }
  };

  // Get selected option objects for displaying badges
  const getSelectedItems = () => {
    return options.filter((item) => selectedKeys.includes(keyGetter(item)));
  };

  const emptySelectedKeysWrapper = (keys: K[]) => {
    if (
      emptyWhenAllSelected &&
      keys.length === options.length + (nullable ? 1 : 0)
    ) {
      setSelectedKeys([]);
    } else {
      setSelectedKeys(keys);
    }
  };

  const onCheck = (key: K, checked: boolean) => {
    if (checked) {
      const keySet = new Set(selectedKeys);
      keySet.add(key);
      if (selectedKeys.length === 0) {
        setSelectedKeys(Array.from(keySet));
        setInternalKeys(Array.from(keySet));
      } else if (
        selectedKeys.length ===
        options.length - 1 + (nullable ? 1 : 0)
      ) {
        const keySet = new Set(selectedKeys);
        keySet.add(key);
        keySet.add(null as K);
        emptySelectedKeysWrapper(Array.from(keySet));
        setInternalKeys(Array.from(keySet));
      } else {
        setSelectedKeys(Array.from(keySet));
        setInternalKeys(Array.from(keySet));
      }
    } else {
      setSelectedKeys(internalKeys.filter((k) => k !== key));
      setInternalKeys(internalKeys.filter((k) => k !== key));
    }
  };

  return (
    <>
      <Popover open={open} modal={false} onOpenChange={onOpenChange}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            <div className="flex items-center gap-2">
              {icon}
              <span>{getButtonText()}</span>
            </div>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0" align="start">
          <Command filter={defaultSearchFunction}>
            <div className="flex items-center border-b px-3">
              <CommandInput
                placeholder={placeholder}
                value={searchValue}
                onValueChange={setSearchValue}
                className="h-9"
              />
            </div>
            <div className="flex items-center justify-between border-b px-1 py-0.5">
              <div className="flex flex-1 items-center gap-2 rounded hover:bg-gray-100">
                <Checkbox
                  id="select-all"
                  className="ml-2"
                  onCheckedChange={(checked) => {
                    const allVisibleOptions = searchValue
                      ? options.filter(
                          (item) =>
                            customSearchFunction(
                              labelGetter(item),
                              searchValue,
                            ) > 0,
                        )
                      : options;
                    if (checked) {
                      const allKeys = allVisibleOptions.map((item) =>
                        keyGetter(item),
                      );
                      if (nullable && searchValue === "") {
                        allKeys.push(null as K);
                      }
                      const keySet = new Set(allKeys.concat(internalKeys));
                      emptySelectedKeysWrapper(Array.from(keySet));
                      setInternalKeys(
                        nullable && searchValue === ""
                          ? [...Array.from(keySet), null as K]
                          : Array.from(keySet),
                      );
                    } else {
                      if (searchValue === "") {
                        setSelectedKeys([]);
                        setInternalKeys([]);
                      } else {
                        const allKeys = allVisibleOptions.map((item) =>
                          keyGetter(item),
                        );
                        const keySet = new Set(
                          internalKeys.filter((k) => !allKeys.includes(k)),
                        );
                        emptySelectedKeysWrapper(Array.from(keySet));
                        setInternalKeys(
                          nullable && searchValue === ""
                            ? [...Array.from(keySet), null as K]
                            : Array.from(keySet),
                        );
                      }
                    }
                  }}
                  checked={(() => {
                    if (searchValue) {
                      const allVisibleOptions = options.filter(
                        (item) =>
                          customSearchFunction(labelGetter(item), searchValue) >
                          0,
                      );
                      // check if internalKeys is a superset of allVisibleOptions
                      return (
                        allVisibleOptions.length > 0 &&
                        allVisibleOptions.every((item) =>
                          internalKeys.includes(keyGetter(item)),
                        )
                      );
                    }
                    return (
                      (emptyWhenAllSelected &&
                        selectedKeys.length === 0 &&
                        internalKeys.length !== 0) ||
                      (selectedKeys.length ===
                        options.length + (nullable ? 1 : 0) &&
                        !emptyWhenAllSelected)
                    );
                  })()}
                />
                <Label
                  htmlFor="select-all"
                  className="flex min-w-20 flex-1 py-2"
                >
                  Select {searchValue ? "Filtered" : "All"}
                </Label>
              </div>
              {internalKeys.length > 0 && (
                <Button
                  variant={"outline"}
                  className="flex h-5 cursor-pointer flex-row gap-0 p-2 py-1"
                  onClick={() => {
                    setSelectedKeys([]);
                    setInternalKeys([]);
                  }}
                >
                  <Muted className="mx-0 flex w-full items-center justify-between gap-0">
                    <XIcon className="ml-1 h-3 w-3" />
                    <span>Clear All</span>
                  </Muted>
                </Button>
              )}
            </div>
            <CommandList>
              <CommandEmpty>No options found.</CommandEmpty>
              <CommandGroup className="max-h-[300px] overflow-auto">
                {nullable && (
                  <CommandItem
                    key={`empty`}
                    value={"Empty"}
                    className="text-muted-foreground flex items-center gap-2 p-2"
                    onSelect={() => {
                      const shouldCheck =
                        (!selectedKeys.includes(null as K) ||
                          internalKeys.length === 0) &&
                        (!internalKeys.includes(null as K) ||
                          internalKeys.length === 0);
                      onCheck(null as K, shouldCheck);
                    }}
                  >
                    <div className="flex flex-1 items-center gap-2">
                      <Checkbox
                        id={`option-empty`}
                        onClick={(e) => e.stopPropagation()}
                        checked={internalKeys.includes(null as K)}
                        onCheckedChange={(checked) => {
                          onCheck(null as K, Boolean(checked));
                        }}
                      />
                      <Label
                        htmlFor={`option-empty`}
                        className="pointer-events-none flex-1 italic"
                      >
                        (Empty)
                      </Label>
                    </div>
                  </CommandItem>
                )}
                {options.map((item) => {
                  const key = keyGetter(item);
                  const label = labelGetter(item);

                  return (
                    <CommandItem
                      key={`${key}`}
                      value={label}
                      onSelect={() => {
                        const shouldCheck =
                          (!selectedKeys.includes(key) ||
                            internalKeys.length === 0) &&
                          (!internalKeys.includes(key) ||
                            internalKeys.length === 0);
                        onCheck(key, shouldCheck);
                      }}
                      className="flex items-center gap-2 p-2"
                    >
                      <div className="flex flex-1 items-center gap-2">
                        <Checkbox
                          id={`option-${key}`}
                          checked={internalKeys.includes(key)}
                          onCheckedChange={(checked) => {
                            onCheck(key, Boolean(checked));
                          }}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <Label
                          htmlFor={`option-${key}`}
                          className="pointer-events-none flex-1"
                        >
                          {label}
                        </Label>
                      </div>
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedKeys.length > 0 && selectedKeys.length < options.length && (
        <div className="mt-1">
          <div className="flex flex-wrap items-center gap-1">
            {getSelectedItems()
              .slice(0, 3)
              .map((item) => {
                const key = keyGetter(item);
                const label = labelGetter(item);

                return (
                  <Badge
                    key={`${key}`}
                    variant="default"
                    className="flex-shrink-0 cursor-pointer text-xs"
                    onClick={() => onCheck(key, false)}
                  >
                    {label}
                    <XIcon className="ml-1 h-3 w-3" />
                  </Badge>
                );
              })}
            {selectedKeys.length > 3 && (
              <Badge
                variant="default"
                onClick={() => {
                  const keysToKeep = getSelectedItems()
                    .slice(0, 3)
                    .map((item) => keyGetter(item));
                  setSelectedKeys(keysToKeep);
                  setInternalKeys(keysToKeep);
                }}
                className="flex-shrink-0 cursor-pointer"
              >
                +{selectedKeys.length - 3} more
                <XIcon className="ml-1 h-3 w-3" />
              </Badge>
            )}
          </div>
        </div>
      )}
    </>
  );
}
/**
 * Custom search function for filtering options in the command menu.
 * This function provides fuzzy matching and prioritizes exact matches.
 *
 * @param value - The option label to search in
 * @param search - The search term entered by the user
 * @returns A score between 0-1, where 1 is a perfect match and 0 is no match
 */
function defaultSearchFunction(value: string, search: string): number {
  // Convert both to lowercase for case-insensitive search
  const normalizedValue = value.toLowerCase().trim();
  const normalizedSearch = search.toLowerCase().trim();

  // Return 1 if search is empty (show all items)
  if (!normalizedSearch) return 1;

  // Exact match gets highest score
  if (normalizedValue === normalizedSearch) return 1;

  // Starts with search term gets high score
  if (normalizedValue.startsWith(normalizedSearch)) return 0.9;

  // Contains search term gets medium score
  if (normalizedValue.includes(normalizedSearch)) return 0.7;

  // Fuzzy matching: check if all characters in search appear in order
  let searchIndex = 0;
  let valueIndex = 0;
  let matchedChars = 0;

  while (
    searchIndex < normalizedSearch.length &&
    valueIndex < normalizedValue.length
  ) {
    if (normalizedSearch[searchIndex] === normalizedValue[valueIndex]) {
      matchedChars++;
      searchIndex++;
    }
    valueIndex++;
  }

  // If all search characters were found in order, give partial score
  if (matchedChars === normalizedSearch.length) {
    const fuzzyScore = matchedChars / normalizedValue.length;
    return Math.max(0.3, fuzzyScore * 0.6); // Minimum 0.3, max 0.6 for fuzzy matches
  }

  // No match found
  return 0;
}
