import * as React from "react";
import { PatternFormat } from "react-number-format";
import { cn } from "@/lib/utils";
import { InputMaskColorer } from "./input";
import { format } from "date-fns";

type DatePickerProps = {
  isGrid?: boolean;
  onSelect?: (value: Date | null) => void;
  initialValue?: string;
  onChange: (value: Date | null) => void;
  onFocus?: () => void;
  hasFocus?: boolean;
  disabled?: boolean;
  value?: Date | null;
  className?: string;
};

function isAllowed({ formattedValue }: { formattedValue: string }) {
  const year = formattedValue.split("/")[0];
  const month = formattedValue.split("/")[1];
  const isMonth = !month?.includes("M");
  const day = formattedValue.split("/")[2];
  const isDay = !day?.includes("D") && day?.replace("0", "") !== "";
  if (isMonth && (Number(month) > 12 || Number(month) < 1)) {
    return false;
  }
  // Create a date object for the first day of the next month
  const firstDayOfNextMonth = new Date(Number(year), Number(month), 1);
  // Subtract one day to get the last day of the month
  firstDayOfNextMonth.setDate(firstDayOfNextMonth.getDate() - 1);
  const maxDayOfMonth = firstDayOfNextMonth.getDate();
  if (isDay && maxDayOfMonth < Number(day) && Number(day) > 0) {
    return false;
  }
  return true;
}
export const DateInput = ({
  value,
  onChange: onFormChange,
  disabled = false,
  isGrid = false,
  onSelect = () => undefined,
  onFocus = () => undefined,
  hasFocus = false,
  className = "",
  initialValue,
}: DatePickerProps) => {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const defaultMonth = ((value, initialValue) => {
    if (initialValue != undefined && initialValue !== "") {
      return new Date(initialValue);
    }
    if (initialValue === "") {
      return new Date();
    }
    if (value != null) {
      return value;
    }
    return new Date();
  })(value, initialValue);
  const [, setSelectedMonth] = React.useState(defaultMonth);
  const [error, setError] = React.useState(false);
  const [open, setOpen] = React.useState(isGrid);
  const onChange = React.useCallback(
    (change: Date | null) => {
      onFormChange(change);
      onSelect(change);
    },
    [onFormChange, onSelect],
  );
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Backspace" && inputRef.current) {
        // if the user backspaces, just clear the input to simplify the allowed values validation.
        // eventually we can add a more complex validation here but would have to break year,month,day into
        // separate inputs.
        onChange(null);
      }
      if (e.key === "Enter" && inputRef.current) {
        if (
          isAllowed({ formattedValue: inputRef.current.value }) &&
          inputRef.current.value
        ) {
          onChange(new Date(inputRef.current.value));
        } else if (inputRef.current.value === "") {
          onChange(null);
        }
      }
    };
    const currentInput = inputRef.current;
    if (currentInput) {
      currentInput.addEventListener("keydown", handleKeyDown);
      if (isGrid) {
        currentInput.focus();
      }
    }
    return () => {
      if (currentInput) {
        currentInput.removeEventListener("keydown", handleKeyDown);
      }
    };
  }, [onChange, isGrid]);
  const patternValue = React.useMemo(() => {
    if (initialValue != null) {
      return initialValue;
    }
    if (value == null || value?.toString() === "Invalid Date") {
      return initialValue ?? "";
    }
    return format(value, "yyyy/MM/dd");
  }, [value, initialValue]);
  // Set the cursor to the end of the input when editing
  React.useLayoutEffect(() => {
    if (inputRef.current && patternValue === inputRef.current?.value) {
      inputRef.current.setSelectionRange(
        patternValue.length,
        patternValue.length,
      );
    }
  }, [patternValue, open]);
  return (
    <PatternFormat
      onFocus={() => {
        onFocus();
      }}
      customInput={InputMaskColorer}
      classNameMask={error ? "text-red-500" : ""}
      getInputRef={inputRef}
      disabled={disabled}
      placeholder="YYYY/MM/DD"
      value={patternValue}
      format="####/##/##"
      mask={["Y", "Y", "Y", "Y", "M", "M", "D", "D"]}
      tabIndex={-1}
      className={cn(
        "border-input inset-0 w-full rounded text-sm outline-none placeholder:align-middle placeholder:text-sm placeholder:font-medium",
        error && "placeholder:text-red-500",
        hasFocus && "ring-ring ring-1",
        className,
      )}
      onBlur={() => {
        const reg = new RegExp(/(\d{4})\/(\d{2})\/(\d{2})/);
        if (
          (reg.test(inputRef.current?.value ?? "") &&
            inputRef.current?.value.split("/")[2] !== "00") ||
          inputRef.current?.value === ""
        ) {
          setError(false);
        } else {
          setError(true);
        }
      }}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          const reg = new RegExp(/(\d{4})\/(\d{2})\/(\d{2})/);
          if (
            reg.test(inputRef.current?.value ?? "") ||
            inputRef.current?.value === ""
          ) {
            setError(false);
            setOpen(false);
            onChange(
              inputRef.current?.value != null
                ? new Date(inputRef.current?.value)
                : null,
            );
          } else {
            setError(true);
            setOpen(false);
          }
        }
      }}
      isAllowed={isAllowed}
      onValueChange={({ formattedValue }) => {
        const yearIsSelected = !formattedValue.split("/").at(0)?.includes("Y");
        const monthIsSelected = !formattedValue.split("/").at(1)?.includes("M");
        const dayIsSelected =
          !formattedValue.split("/").at(2)?.includes("D") &&
          formattedValue.split("/").at(2)?.replace("0", "") !== "";
        const validDate =
          yearIsSelected &&
          monthIsSelected &&
          dayIsSelected &&
          new Date(formattedValue).toString() !== "Invalid Date";
        if (validDate) {
          const year = Number(formattedValue.split("/")[0]);
          const month = Number(formattedValue.split("/")[1]) - 1;
          setSelectedMonth(new Date(year, month));
          setError(false);
          onChange(new Date(formattedValue));
        } else if (yearIsSelected && monthIsSelected && !dayIsSelected) {
          const year = Number(formattedValue.split("/")[0]);
          const month = Number(formattedValue.split("/")[1]) - 1;
          setSelectedMonth(new Date(year, month));
        } else if (yearIsSelected && !monthIsSelected) {
          const year = Number(formattedValue.split("/")[0]);
          setSelectedMonth(new Date(year, new Date().getMonth()));
        } else if (formattedValue === "") {
          onChange(null);
          setError(false);
        } else if (
          !formattedValue.includes("Y") &&
          !formattedValue.includes("M") &&
          !formattedValue.includes("D")
        ) {
          setError(true);
        }
      }}
    ></PatternFormat>
  );
};
