import React from "react";
import { CalendarIcon, ChevronDown } from "lucide-react";

import { DateRange } from "react-day-picker";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { DateInput } from "./date-picker";
import { Tiny } from "./typography";

type PresetRange = {
  label: string;
  value: string;
  range: () => DateRange;
};

type CalendarFilterMenuProps = {
  presets?: PresetRange[];
  range?: { from: Date | null; to: Date | null };
  onChange?: (range: { from: Date | null; to: Date | null }) => void;
  noneSelectedText?: string;
  disabled?: boolean;
};

export default function CalendarFilterMenu({
  presets = [],
  range = { from: null, to: null },
  onChange = () => undefined,
  noneSelectedText = "All Dates",
  disabled = false,
}: CalendarFilterMenuProps) {
  // const getButtonText = () => {
  //   return "Select Date Range";
  // };
  // const [from, setFrom] = React.useState<Date | undefined>(undefined);
  const from = range?.from ?? undefined;
  // const [from, setFrom] = React.useState<Date | undefined>(
  //   range?.from ?? undefined,
  // );
  // const [to, setTo] = React.useState<Date | undefined>(undefined);
  const to = range?.to ?? undefined;
  // const [to, setTo] = React.useState<Date | undefined>(range?.to ?? undefined);
  const setDateRange = (from: Date | undefined, to: Date | undefined) => {
    onChange({
      from: from ?? null,
      to: to ?? null,
    });
    // setTo(to);
    // setFrom(from);
  };
  const [selectedPreset] = React.useState<string | null>();
  const [toFromFocus, setToFromFocus] = React.useState<
    "from" | "to" | undefined
  >();
  const [month, setMonth] = React.useState<Date>(new Date());
  const setToFrom = (
    value: Date | undefined,
    focus: "from" | "to" | undefined,
  ) => {
    if (focus === "from") {
      if (to !== undefined && value !== undefined && value > to) {
        setDateRange(to, value);
      } else {
        setDateRange(value, to);
      }
      setToFromFocus("to");
    } else if (focus === "to") {
      if (from !== undefined && value !== undefined && value < from) {
        setDateRange(value, from);
      } else {
        setDateRange(from, value);
      }
      setToFromFocus(undefined);
    } else if (
      focus === undefined &&
      ((from === undefined && to === undefined) ||
        (from !== undefined && to !== undefined))
    ) {
      setDateRange(value, undefined);
      setToFromFocus("to");
    } else if (focus === undefined && from === undefined && to !== undefined) {
      if (value !== undefined && value > to) {
        setDateRange(to, value);
      } else {
        setDateRange(value, to);
      }
      setToFromFocus("to");
    } else {
      setDateRange(from, value);
      setToFromFocus(undefined);
    }
    if (value !== undefined) {
      setMonth(value);
    }
  };
  return (
    <Popover modal={false}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          disabled={disabled}
          className={cn(
            "flex w-auto min-w-0 justify-between text-left font-normal", // Added min-w-0
            disabled && "opacity-50 cursor-not-allowed"
          )}
        >
          <div className="flex min-w-0 items-center gap-2">
            <CalendarIcon className="h-4 w-4 flex-shrink-0" />{" "}
            <span className="min-w-0 flex-shrink truncate font-medium">
              {getButtonText(from, to, noneSelectedText)}
            </span>
          </div>
          <ChevronDown className="h-4 w-4 flex-shrink-0 opacity-50" />{" "}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <div className="flex">
          {presets.length > 0 && (
            <div className="space-y-4 border-r p-4">
              <div>
                <h4 className="mb-3 text-sm font-medium">Quick Ranges</h4>
                <div className="grid grid-cols-2 gap-2">
                  {presets.map((preset) => (
                    <Button
                      key={preset.value}
                      variant={
                        selectedPreset === preset.value ? "default" : "ghost"
                      }
                      size="sm"
                      className="h-8 justify-start px-3"
                      onClick={() => undefined}
                    >
                      {preset.label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="p-4">
            <div className="flex justify-around">
              <div className="flex w-28 flex-col gap-1">
                <Tiny>From:</Tiny>
                <DateInput
                  onChange={(v) => setToFrom(v ?? undefined, "from")}
                  value={from ?? null}
                  onFocus={() => setToFromFocus("from")}
                  hasFocus={toFromFocus === "from" ? true : false}
                />
              </div>
              <div className="flex w-28 flex-col gap-1">
                <Tiny>To:</Tiny>
                <DateInput
                  onChange={(v) => setToFrom(v ?? undefined, "to")}
                  value={to ?? null}
                  onFocus={() => setToFromFocus("to")}
                  hasFocus={toFromFocus === "to" ? true : false}
                />
              </div>
            </div>
            <Calendar
              mode="range"
              selected={{ from: from, to: to }}
              month={month}
              onMonthChange={setMonth}
              onDayClick={(range) => {
                setToFrom(range, toFromFocus);
              }}
            />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
/**
 * Generate user-friendly button text based on the current date range state.
 *
 * This function handles various scenarios:
 * - No dates selected
 * - Only from date selected
 * - Only to date selected
 * - Both dates selected (complete range)
 * - Same date for both from and to (single day)
 *
 * @returns {string} Formatted text to display on the button
 */
function getButtonText(
  from?: Date,
  to?: Date,
  noneSelectedText?: string,
): string {
  // Helper function to format a single date
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // Helper function to format a date range
  const formatDateRange = (fromDate: Date, toDate: Date): string => {
    const fromFormatted = formatDate(fromDate);
    const toFormatted = formatDate(toDate);

    // If same date, show as single date
    if (fromDate.toDateString() === toDate.toDateString()) {
      return fromFormatted;
    }

    // If same year, abbreviate the from date
    if (fromDate.getFullYear() === toDate.getFullYear()) {
      const fromAbbreviated = fromDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
      return `${fromAbbreviated} - ${toFormatted}`;
    }

    // Different years, show full dates
    return `${fromFormatted} - ${toFormatted}`;
  };

  // Handle different date range states
  if (from && to) {
    // Both dates selected - show range
    return formatDateRange(from, to);
  } else if (from && !to) {
    // Only from date selected
    return `From ${formatDate(from)}`;
  } else if (!from && to) {
    // Only to date selected (unusual case)
    return `Until ${formatDate(to)}`;
  } else {
    // No dates selected
    return noneSelectedText ?? "All Dates";
  }
}
