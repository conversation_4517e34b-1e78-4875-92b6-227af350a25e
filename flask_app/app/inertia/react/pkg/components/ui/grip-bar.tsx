import React, { useRef, useCallback, useEffect } from "react";
import { GrippieIcon } from "@/components/icons/grippie-icon";

interface GripBarProps {
  className?: string;
  children: (resizeRef: React.RefObject<HTMLDivElement>) => React.ReactNode;
  minHeight?: number;
  maxHeight?: number;
}

/**
 * GripBar Component
 *
 * A resizable container with a grip handle that works on both desktop and mobile devices.
 * The grip bar allows users to resize the content area by dragging vertically.
 *
 * Features:
 * - Cross-platform touch and mouse support
 * - iOS Safari compatibility with proper touch-action handling
 * - Visual feedback with hover states using the GrippieIcon
 * - Configurable height constraints
 * - Larger touch target for mobile accessibility
 */
export const GripBar: React.FC<GripBarProps> = ({
  className,
  children,
  minHeight = 200,
  maxHeight = 800,
}) => {
  const resizeRef = useRef<HTMLDivElement>(null);
  const gripRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef(false);
  const startY = useRef(0);
  const startHeight = useRef(0);

  /**
   * Handle the start of a resize operation (mouse or touch)
   * Sets up initial state and prevents default browser behaviors
   */
  const handleStart = useCallback((clientY: number) => {
    if (!resizeRef.current) return;

    isDragging.current = true;
    startY.current = clientY;
    startHeight.current = resizeRef.current.offsetHeight;

    // Prevent text selection and scrolling during drag
    document.body.style.userSelect = "none";
  }, []);

  /**
   * Handle the movement during resize (mouse or touch)
   * Calculates new height based on mouse/touch position and applies constraints
   */
  const handleMove = useCallback(
    (clientY: number) => {
      if (!isDragging.current || !resizeRef.current) return;

      const deltaY = clientY - startY.current;
      const newHeight = Math.min(
        Math.max(startHeight.current + deltaY, minHeight),
        maxHeight,
      );

      resizeRef.current.style.height = `${newHeight}px`;
    },
    [minHeight, maxHeight],
  );

  /**
   * Handle the end of a resize operation
   * Restores original body styles and cleans up state
   */
  const handleEnd = useCallback(() => {
    isDragging.current = false;

    // Restore body styles
    document.body.style.userSelect = "";
  }, []);

  // Mouse event handlers for desktop interaction
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      handleStart(e.clientY);
    },
    [handleStart],
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      e.preventDefault();
      handleMove(e.clientY);
    },
    [handleMove],
  );

  const handleMouseUp = useCallback(
    (e: MouseEvent) => {
      e.preventDefault();
      handleEnd();
    },
    [handleEnd],
  );

  // Touch event handlers for mobile devices
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault();
      e.stopPropagation();

      // Ensure we have at least one touch point
      if (e.touches.length > 0) {
        const touch = e.touches[0];
        handleStart(touch.clientY);
      }
    },
    [handleStart],
  );

  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      e.preventDefault();

      // Ensure we have at least one touch point
      if (e.touches.length > 0) {
        const touch = e.touches[0];
        handleMove(touch.clientY);
      }
    },
    [handleMove],
  );

  const handleTouchEnd = useCallback(
    (e: TouchEvent) => {
      e.preventDefault();
      handleEnd();
    },
    [handleEnd],
  );

  // Set up global event listeners for drag operations
  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging.current) {
        handleMouseMove(e);
      }
    };

    const handleGlobalMouseUp = (e: MouseEvent) => {
      if (isDragging.current) {
        handleMouseUp(e);
      }
    };

    const handleGlobalTouchMove = (e: TouchEvent) => {
      if (isDragging.current) {
        handleTouchMove(e);
      }
    };

    const handleGlobalTouchEnd = (e: TouchEvent) => {
      if (isDragging.current) {
        handleTouchEnd(e);
      }
    };

    // Add event listeners with appropriate options
    document.addEventListener("mousemove", handleGlobalMouseMove);
    document.addEventListener("mouseup", handleGlobalMouseUp);

    // Use passive: false for touch events to allow preventDefault
    document.addEventListener("touchmove", handleGlobalTouchMove, {
      passive: false,
    });
    document.addEventListener("touchend", handleGlobalTouchEnd, {
      passive: false,
    });

    // Cleanup function to remove event listeners
    return () => {
      document.removeEventListener("mousemove", handleGlobalMouseMove);
      document.removeEventListener("mouseup", handleGlobalMouseUp);
      document.removeEventListener("touchmove", handleGlobalTouchMove);
      document.removeEventListener("touchend", handleGlobalTouchEnd);
    };
  }, [handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  return (
    <div className={`relative ${className || ""}`}>
      {/* Resizable content area */}
      {children(resizeRef as React.RefObject<HTMLDivElement>)}

      {/* Grip bar for resizing with enhanced mobile support */}
      <div
        ref={gripRef}
        className="group border-border relative flex h-3 w-full cursor-ns-resize items-center justify-center rounded-b border border-t-0 bg-gray-50 transition-colors duration-150 hover:bg-gray-100 active:bg-gray-200"
        style={{
          // Critical CSS for iOS touch support
          touchAction: "none",
          WebkitTouchCallout: "none",
          WebkitUserSelect: "none",
          userSelect: "none",
          // Ensure the grip bar is always interactive
          pointerEvents: "auto",
        }}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        role="separator"
        aria-label="Resize handle"
        tabIndex={0}
      >
        {/* GrippieIcon - the original grip icon you were using */}
        <GrippieIcon className="pointer-events-none h-full w-full text-gray-500 transition-colors duration-150 group-hover:text-gray-600 group-active:text-gray-700" />

        {/* Larger invisible touch target for mobile accessibility */}
        <div
          className="absolute inset-0 -mb-2 min-h-[44px]"
          style={{
            touchAction: "none",
            // Make this invisible but still interactive
            backgroundColor: "transparent",
          }}
          aria-hidden="true"
        />
      </div>
    </div>
  );
};
