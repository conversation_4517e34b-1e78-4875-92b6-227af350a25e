import React, { HTMLAttributes, RefObject } from "react";
import { cn } from "@/lib/utils";
import { Slot } from "@radix-ui/react-slot";

type TypographyProps = {
  className?: string;
} & React.HTMLAttributes<HTMLElement>;

// copied from shadcn/ui
// https://ui.shadcn.com/docs/components/typography

export function H1({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <h1
      className={cn(
        "scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl",
        className,
      )}
    >
      {children}
    </h1>
  );
}

export function H2({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <h2
      className={cn(
        "scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0",
        className,
      )}
    >
      {children}
    </h2>
  );
}

export function H3({
  className,
  children,
  ...props
}: React.PropsWithChildren<
  TypographyProps & {
    ref?: RefObject<HTMLHeadingElement | null>;
  } & HTMLAttributes<HTMLHeadingElement>
>) {
  return (
    <h3
      {...props}
      className={cn(
        "scroll-m-20 text-2xl font-semibold tracking-tight",
        className,
      )}
    >
      {children}
    </h3>
  );
}

export function H4({
  className,
  children,
}: React.PropsWithChildren<TypographyProps> & {
  className?: string;
}) {
  return (
    <h4
      className={cn(
        "scroll-m-20 text-xl font-semibold tracking-tight",
        className,
      )}
    >
      {children}
    </h4>
  );
}
export function P({
  children,
  className,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <p className={cn("leading-7 [&:not(:first-child)]:mt-6", className)}>
      {children}
    </p>
  );
}

export function Blockquote({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <blockquote className={cn("mt-6 border-l-2 pl-6 italic", className)}>
      {children}
    </blockquote>
  );
}

export function List({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <ul className={cn("[&> my-6 ml-6 list-disc", className)}>{children}</ul>
  );
}

export function InlineCode({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <code
      className={cn(
        "bg-muted relative rounded px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold",
        className,
      )}
    >
      {children}
    </code>
  );
}

export function Lead({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <p className={cn("text-muted-foreground text-xl", className)}>{children}</p>
  );
}

export function Large({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <span className={cn("text-lg font-semibold", className)}>{children}</span>
  );
}

export function Text({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return <p className={cn("text-md", className)}>{children}</p>;
}

export function Small({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <small className={cn("text-sm leading-none font-medium", className)}>
      {children}
    </small>
  );
}

export function Error({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <p
      className={cn(
        "text-destructive text-sm leading-none font-medium italic",
        className,
      )}
    >
      {children}
    </p>
  );
}

export function Tiny({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <small className={cn("text-xs leading-none font-medium", className)}>
      {children}
    </small>
  );
}

export function Muted({
  className,
  children,
}: React.PropsWithChildren<TypographyProps>) {
  return (
    <p className={cn("text-muted-foreground text-sm", className)}>{children}</p>
  );
}

export function Link({
  className,
  asChild = false,
  ...props
}: React.ComponentProps<"a"> & {
  className?: string;
  asChild?: boolean;
}) {
  const Comp = asChild ? Slot : "a";
  return (
    <Comp
      className={cn(
        "text-primary underline-offset-4 hover:underline",
        className,
      )}
      {...props}
    />
  );
}
