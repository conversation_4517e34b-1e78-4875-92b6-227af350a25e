import { useIsFetching, useIsMutating } from "@tanstack/react-query";
import NProgress from "nprogress";
import React from "react";

const NProgressQuery = () => {
  React.useEffect(() => {
    NProgress.configure({
      showSpinner: false, // This hides the spinner
    });
  }, []); // Empty dependency array means this runs once when component mounts
  // Start NProgress when there are any queries or mutations in progress
  const isFetching = useIsFetching();
  const isMutating = useIsMutating();
  React.useEffect(() => {
    if (isFetching || isMutating) {
      NProgress.start();
    } else {
      NProgress.done();
    }
  }, [isFetching, isMutating]);
  return null;
};

export { NProgressQuery };
