import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AlertTriangle, Bug, Mail, ChevronDown, ChevronUp, Layers, MousePointer, Network, Terminal, Monitor, Database } from 'lucide-react';
import { createRoot } from 'react-dom/client';
import { isDevelopment } from '../../utils/environment';
import { getErrorStats } from '../../utils/global-error-handler';
import type { EnhancedErrorContext, UserAction, ConsoleLog, NetworkRequest } from '../../utils/error-context-collector';

export interface ErrorDetails {
  message: string;
  stack: string | undefined;
  timestamp: Date;
  userAgent: string;
  url: string;
  type: 'javascript' | 'react' | 'network' | 'unknown';
  source: string | undefined;
  // Extended fields for collected errors
  id?: string;
  count?: number;
  firstOccurrence?: Date;
  lastOccurrence?: Date;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

// Use enhanced error context or basic error details
export type FullErrorDetails = ErrorDetails | EnhancedErrorContext;

interface ErrorNotificationProps {
  error: FullErrorDetails;
  onClose: () => void;
}

type ErrorTab = 'overview' | 'user-actions' | 'console' | 'network' | 'browser' | 'storage';


const ErrorNotification: React.FC<ErrorNotificationProps> = ({ error, onClose }) => {
  const [copied, setCopied] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const isDevMode = isDevelopment();
  const [activeTab, setActiveTab] = useState<ErrorTab>('overview');
  
  // Check if we have enhanced error context
  const isEnhancedError = (error: FullErrorDetails): error is EnhancedErrorContext => {
    return 'userActions' in error && 'consoleLogs' in error && 'networkRequests' in error;
  };

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const formatErrorForClipboard = () => {
    return `
🐛 APPLICATION ERROR REPORT

Timestamp: ${error.timestamp.toISOString()}
Type: ${error.type.toUpperCase()}
URL: ${error.url}
User Agent: ${error.userAgent}

Error Message:
${error.message}

${error.stack ? `Stack Trace:\n${error.stack}` : ''}

${error.source ? `Source: ${error.source}` : ''}

---
Generated by IJACK Error Reporter
    `.trim();
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(formatErrorForClipboard());
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = formatErrorForClipboard();
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const sendSupportEmail = () => {
    const subject = encodeURIComponent(`Error Report: ${getErrorTypeLabel()}`);
    const body = encodeURIComponent(formatErrorForClipboard());
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    window.open(mailtoUrl, '_blank');
  };

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Wait for animation
  };

  const getErrorIcon = () => {
    switch (error.type) {
      case 'react':
        return <Bug className="h-5 w-5 text-red-500" />;
      case 'network':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
    }
  };

  const getErrorTypeLabel = () => {
    switch (error.type) {
      case 'react':
        return 'React Component Error';
      case 'javascript':
        return 'JavaScript Error';
      case 'network':
        return 'Network Error';
      default:
        return 'Application Error';
    }
  };

  const getSeverityBadge = () => {
    if (!error.severity) return null;
    
    const severityStyles = {
      low: 'bg-blue-100 text-blue-800 border-blue-200',
      medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      high: 'bg-orange-100 text-orange-800 border-orange-200',
      critical: 'bg-red-100 text-red-800 border-red-200'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${severityStyles[error.severity]}`}>
        {error.severity.toUpperCase()}
      </span>
    );
  };

  const getOccurrenceText = () => {
    if (!error.count || error.count <= 1) return null;
    
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
        {error.count}x
      </span>
    );
  };

  // Helper functions for rendering enhanced error sections
  const renderUserActions = (actions: UserAction[]) => {
    if (!actions || actions.length === 0) {
      return <p className="text-sm text-gray-500">No user actions recorded</p>;
    }

    return (
      <div className="space-y-2">
        <p className="text-xs text-gray-600 mb-2">Last {actions.length} user interactions:</p>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {actions.map((action, idx) => (
            <div key={idx} className="flex items-start space-x-2 text-xs p-2 bg-gray-50 rounded">
              <span className="text-gray-500 font-mono text-[10px]">
                {action.timestamp.toLocaleTimeString()}
              </span>
              <span className="font-medium text-gray-700">{action.type}</span>
              <span className="text-gray-600 flex-1 truncate">{action.target}</span>
              {action.value && (
                <span className="text-gray-500 max-w-[150px] truncate">= {action.value}</span>
              )}
              {action.coordinates && (
                <span className="text-gray-400">({action.coordinates.x}, {action.coordinates.y})</span>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderConsoleLogs = (logs: ConsoleLog[]) => {
    if (!logs || logs.length === 0) {
      return <p className="text-sm text-gray-500">No console logs recorded</p>;
    }

    const logColors = {
      log: 'text-gray-700',
      info: 'text-blue-700',
      warn: 'text-yellow-700',
      error: 'text-red-700',
      debug: 'text-purple-700'
    };

    return (
      <div className="space-y-2">
        <p className="text-xs text-gray-600 mb-2">Last {logs.length} console logs before error:</p>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {logs.map((log, idx) => (
            <div key={idx} className={`text-xs p-2 bg-gray-50 rounded font-mono ${logColors[log.level]}`}>
              <div className="flex items-start space-x-2">
                <span className="text-gray-500 text-[10px]">
                  {log.timestamp.toLocaleTimeString()}
                </span>
                <span className="font-medium">[{log.level.toUpperCase()}]</span>
                <span className="flex-1 whitespace-pre-wrap break-all">{log.message}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderNetworkRequests = (requests: NetworkRequest[]) => {
    if (!requests || requests.length === 0) {
      return <p className="text-sm text-gray-500">No network requests recorded</p>;
    }

    return (
      <div className="space-y-2">
        <p className="text-xs text-gray-600 mb-2">Last {requests.length} network requests:</p>
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {requests.map((req, idx) => (
            <div key={idx} className="text-xs p-2 bg-gray-50 rounded">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-gray-500 font-mono text-[10px]">
                    {req.timestamp.toLocaleTimeString()}
                  </span>
                  <span className={`font-medium ${req.method === 'GET' ? 'text-green-600' : req.method === 'POST' ? 'text-blue-600' : 'text-purple-600'}`}>
                    {req.method}
                  </span>
                  <span className={`${req.error ? 'text-red-600' : req.status && req.status >= 400 ? 'text-orange-600' : 'text-gray-700'} truncate max-w-[400px]`}>
                    {req.url}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-gray-500">
                  {req.status && <span className={`font-medium ${req.status >= 400 ? 'text-red-600' : 'text-green-600'}`}>{req.status}</span>}
                  {req.duration && <span>{req.duration}ms</span>}
                  {req.size && <span>{(req.size / 1024).toFixed(1)}KB</span>}
                  {req.error && <span className="text-red-600">Failed</span>}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderBrowserDiagnostics = (diag: EnhancedErrorContext['browserDiagnostics']) => {
    if (!diag) return null;

    return (
      <div className="space-y-3 text-xs">
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gray-50 p-2 rounded">
            <p className="font-medium text-gray-700 mb-1">Display</p>
            <p className="text-gray-600">Viewport: {diag.viewport.width}x{diag.viewport.height}</p>
            <p className="text-gray-600">Screen: {diag.screen.width}x{diag.screen.height}</p>
            <p className="text-gray-600">DPR: {diag.devicePixelRatio}</p>
          </div>
          
          <div className="bg-gray-50 p-2 rounded">
            <p className="font-medium text-gray-700 mb-1">System</p>
            <p className="text-gray-600">Platform: {diag.platform}</p>
            <p className="text-gray-600">Cores: {diag.hardwareConcurrency}</p>
            <p className="text-gray-600">Online: {diag.online ? 'Yes' : 'No'}</p>
          </div>
        </div>

        {diag.memory && (
          <div className="bg-gray-50 p-2 rounded">
            <p className="font-medium text-gray-700 mb-1">Memory</p>
            <p className="text-gray-600">Used: {(diag.memory.usedJSHeapSize / 1048576).toFixed(1)}MB</p>
            <p className="text-gray-600">Total: {(diag.memory.totalJSHeapSize / 1048576).toFixed(1)}MB</p>
            <p className="text-gray-600">Limit: {(diag.memory.jsHeapSizeLimit / 1048576).toFixed(1)}MB</p>
          </div>
        )}

        {diag.connection && (
          <div className="bg-gray-50 p-2 rounded">
            <p className="font-medium text-gray-700 mb-1">Network</p>
            <p className="text-gray-600">Type: {diag.connection.effectiveType}</p>
            <p className="text-gray-600">Downlink: {diag.connection.downlink}Mbps</p>
            <p className="text-gray-600">RTT: {diag.connection.rtt}ms</p>
          </div>
        )}
      </div>
    );
  };

  const renderStorageData = (localStorage?: Record<string, string>, sessionStorage?: Record<string, string>) => {
    const hasLocalStorage = localStorage && Object.keys(localStorage).length > 0;
    const hasSessionStorage = sessionStorage && Object.keys(sessionStorage).length > 0;

    if (!hasLocalStorage && !hasSessionStorage) {
      return <p className="text-sm text-gray-500">No storage data available</p>;
    }

    return (
      <div className="space-y-3">
        {hasLocalStorage && (
          <div>
            <p className="text-xs font-medium text-gray-700 mb-2">Local Storage:</p>
            <div className="bg-gray-50 rounded p-2 max-h-48 overflow-y-auto">
              {Object.entries(localStorage).map(([key, value]) => (
                <div key={key} className="text-xs mb-1">
                  <span className="font-medium text-gray-700">{key}:</span>
                  <span className="text-gray-600 ml-2 break-all">{value}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {hasSessionStorage && (
          <div>
            <p className="text-xs font-medium text-gray-700 mb-2">Session Storage:</p>
            <div className="bg-gray-50 rounded p-2 max-h-48 overflow-y-auto">
              {Object.entries(sessionStorage).map(([key, value]) => (
                <div key={key} className="text-xs mb-1">
                  <span className="font-medium text-gray-700">{key}:</span>
                  <span className="text-gray-600 ml-2 break-all">{value}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={`fixed top-4 right-4 z-[9999] transition-all duration-300 ease-out ${
        isVisible 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-full opacity-0'
      }`}
      style={{ maxWidth: isDevMode && isEnhancedError(error) ? '90vw' : '420px', maxHeight: isDevMode ? '90vh' : 'auto' }}
    >
      <div className="bg-white border border-red-200 rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-50 to-orange-50 border-b border-red-100 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getErrorIcon()}
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="text-sm font-semibold text-gray-900">
                    {getErrorTypeLabel()}
                  </h3>
                  {getSeverityBadge()}
                  {getOccurrenceText()}
                </div>
                <p className="text-xs text-gray-600">
                  {error.count && error.count > 1 && error.firstOccurrence && error.lastOccurrence
                    ? `First: ${error.firstOccurrence.toLocaleString()}, Latest: ${error.lastOccurrence.toLocaleString()}`
                    : error.timestamp.toLocaleString()
                  }
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4" style={{ maxHeight: isDevMode ? 'calc(90vh - 200px)' : 'auto', overflowY: 'auto' }}>
          <div className="space-y-3">
            {/* In production, show minimal error info */}
            {!isDevMode ? (
              <div>
                <p className="text-sm text-gray-900">
                  An error occurred while processing your request. Please contact IJACK support for assistance.
                </p>
              </div>
            ) : (
              <>
                {/* In development, show enhanced error details with tabs if available */}
                {isEnhancedError(error) ? (
                  <>
                    {/* Tab Navigation */}
                    <div className="flex flex-wrap gap-1 border-b border-gray-200 mb-4">
                      <button
                        onClick={() => setActiveTab('overview')}
                        className={`px-3 py-2 text-xs font-medium border-b-2 transition-colors ${
                          activeTab === 'overview' 
                            ? 'border-blue-500 text-blue-600' 
                            : 'border-transparent text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        <div className="flex items-center space-x-1">
                          <Bug className="h-3 w-3" />
                          <span>Overview</span>
                        </div>
                      </button>
                      <button
                        onClick={() => setActiveTab('user-actions')}
                        className={`px-3 py-2 text-xs font-medium border-b-2 transition-colors ${
                          activeTab === 'user-actions' 
                            ? 'border-blue-500 text-blue-600' 
                            : 'border-transparent text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        <div className="flex items-center space-x-1">
                          <MousePointer className="h-3 w-3" />
                          <span>User Actions ({error.userActions.length})</span>
                        </div>
                      </button>
                      <button
                        onClick={() => setActiveTab('console')}
                        className={`px-3 py-2 text-xs font-medium border-b-2 transition-colors ${
                          activeTab === 'console' 
                            ? 'border-blue-500 text-blue-600' 
                            : 'border-transparent text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        <div className="flex items-center space-x-1">
                          <Terminal className="h-3 w-3" />
                          <span>Console ({error.consoleLogs.length})</span>
                        </div>
                      </button>
                      <button
                        onClick={() => setActiveTab('network')}
                        className={`px-3 py-2 text-xs font-medium border-b-2 transition-colors ${
                          activeTab === 'network' 
                            ? 'border-blue-500 text-blue-600' 
                            : 'border-transparent text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        <div className="flex items-center space-x-1">
                          <Network className="h-3 w-3" />
                          <span>Network ({error.networkRequests.length})</span>
                        </div>
                      </button>
                      <button
                        onClick={() => setActiveTab('browser')}
                        className={`px-3 py-2 text-xs font-medium border-b-2 transition-colors ${
                          activeTab === 'browser' 
                            ? 'border-blue-500 text-blue-600' 
                            : 'border-transparent text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        <div className="flex items-center space-x-1">
                          <Monitor className="h-3 w-3" />
                          <span>Browser</span>
                        </div>
                      </button>
                      <button
                        onClick={() => setActiveTab('storage')}
                        className={`px-3 py-2 text-xs font-medium border-b-2 transition-colors ${
                          activeTab === 'storage' 
                            ? 'border-blue-500 text-blue-600' 
                            : 'border-transparent text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        <div className="flex items-center space-x-1">
                          <Database className="h-3 w-3" />
                          <span>Storage</span>
                        </div>
                      </button>
                    </div>

                    {/* Tab Content */}
                    <div className="min-h-[200px]">
                      {activeTab === 'overview' && (
                        <div className="space-y-3">
                          <div>
                            <p className="text-xs font-medium text-gray-700 mb-1">Error Message:</p>
                            <p className="text-sm text-gray-900 bg-gray-50 rounded p-2 font-mono text-wrap break-words">
                              {error.message}
                            </p>
                          </div>
                          {error.source && (
                            <div>
                              <p className="text-xs font-medium text-gray-700 mb-1">Source:</p>
                              <p className="text-xs text-gray-600 bg-gray-50 rounded p-2 font-mono">
                                {error.source}
                              </p>
                            </div>
                          )}
                          {error.stack && (
                            <div>
                              <button
                                onClick={() => setShowDetails(!showDetails)}
                                className="flex items-center space-x-1 text-xs font-medium text-gray-700 hover:text-gray-900 transition-colors"
                              >
                                {showDetails ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                                <span>Stack Trace</span>
                              </button>
                              {showDetails && (
                                <div className="mt-2 bg-gray-50 rounded p-2 max-h-40 overflow-y-auto">
                                  <pre className="text-xs text-gray-700 font-mono whitespace-pre-wrap">
                                    {error.stack}
                                  </pre>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                      {activeTab === 'user-actions' && renderUserActions(error.userActions)}
                      {activeTab === 'console' && renderConsoleLogs(error.consoleLogs)}
                      {activeTab === 'network' && renderNetworkRequests(error.networkRequests)}
                      {activeTab === 'browser' && renderBrowserDiagnostics(error.browserDiagnostics)}
                      {activeTab === 'storage' && renderStorageData(error.localStorage, error.sessionStorage)}
                    </div>
                  </>
                ) : (
                  // Basic error display for non-enhanced errors
                  <>
                    <div>
                      <p className="text-xs font-medium text-gray-700 mb-1">Error Message:</p>
                      <p className="text-sm text-gray-900 bg-gray-50 rounded p-2 font-mono text-wrap break-words">
                        {error.message}
                      </p>
                    </div>
                    {error.source && (
                      <div>
                        <p className="text-xs font-medium text-gray-700 mb-1">Source:</p>
                        <p className="text-xs text-gray-600 bg-gray-50 rounded p-2 font-mono">
                          {error.source}
                        </p>
                      </div>
                    )}
                    {error.stack && (
                      <div>
                        <button
                          onClick={() => setShowDetails(!showDetails)}
                          className="flex items-center space-x-1 text-xs font-medium text-gray-700 hover:text-gray-900 transition-colors"
                        >
                          {showDetails ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                          <span>Stack Trace</span>
                        </button>
                        {showDetails && (
                          <div className="mt-2 bg-gray-50 rounded p-2 max-h-40 overflow-y-auto">
                            <pre className="text-xs text-gray-700 font-mono whitespace-pre-wrap">
                              {error.stack}
                            </pre>
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}

                <div className="bg-yellow-50 border border-yellow-200 rounded p-2">
                  <p className="text-xs text-yellow-800">
                    <strong>Development Mode:</strong> {isEnhancedError(error) ? 'Enhanced error tracking active. Click tabs above for detailed diagnostics.' : 'Basic error details shown. Enhanced tracking is initializing...'}
                  </p>
                </div>
              </>
            )}

            {/* Bulk Error Actions */}
            {(() => {
              const stats = getErrorStats();
              if (stats.totalErrors > 1) {
                return (
                  <div className="bg-blue-50 border border-blue-200 rounded p-3">
                    <div className="flex items-center space-x-2 mb-2">
                      <Layers className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">
                        Multiple Errors Detected
                      </span>
                    </div>
                    <p className="text-xs text-blue-700 mb-2">
                      {stats.totalErrors} total errors ({stats.uniqueErrors} unique types)
                    </p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(stats.allErrorsReport);
                          setCopied(true);
                          setTimeout(() => setCopied(false), 2000);
                        }}
                        className="flex items-center space-x-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                      >
                        <Copy className="h-3 w-3" />
                        <span>Copy All</span>
                      </button>
                      {!isDevMode && (
                        <button
                          onClick={() => {
                            const subject = encodeURIComponent(`Multiple Error Report: ${stats.totalErrors} errors`);
                            const body = encodeURIComponent(stats.allErrorsReport);
                            const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
                            window.open(mailtoUrl, '_blank');
                          }}
                          className="flex items-center space-x-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                        >
                          <Mail className="h-3 w-3" />
                          <span>Email All</span>
                        </button>
                      )}
                    </div>
                  </div>
                );
              }
              return null;
            })()}

            <div className="pt-2 border-t border-gray-100 space-y-2">
              {isDevMode ? (
                <button
                  onClick={copyToClipboard}
                  className={`flex items-center space-x-2 w-full px-3 py-2 rounded-md text-sm font-medium transition-all ${
                    copied
                      ? 'bg-green-100 text-green-700 border border-green-200'
                      : 'bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100'
                  }`}
                >
                  {copied ? (
                    <>
                      <CheckCheck className="h-4 w-4" />
                      <span>Copied to clipboard!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      <span>Copy full error details</span>
                    </>
                  )}
                </button>
              ) : (
                <div className="space-y-2">
                  <button
                    onClick={sendSupportEmail}
                    className="flex items-center space-x-2 w-full px-3 py-2 rounded-md text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 transition-all"
                  >
                    <Mail className="h-4 w-4" />
                    <span>Email IJACK Support</span>
                  </button>
                  <button
                    onClick={copyToClipboard}
                    className={`flex items-center space-x-2 w-full px-3 py-2 rounded-md text-sm font-medium transition-all ${
                      copied
                        ? 'bg-green-100 text-green-700 border border-green-200'
                        : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {copied ? (
                      <>
                        <CheckCheck className="h-4 w-4" />
                        <span>Copied!</span>
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4" />
                        <span>Copy error report</span>
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 border-t border-gray-100 px-4 py-3">
          <p className="text-xs text-gray-500 text-center">
            {isDevMode 
              ? 'Full error details are available for debugging in development mode'
              : 'Share this error report with IJACK support for faster troubleshooting'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

// Error notification manager
class ErrorNotificationManager {
  private container: HTMLDivElement | null = null;
  private root: any = null;
  private notifications: Set<string> = new Set();

  private createContainer() {
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'error-notifications';
      this.container.style.position = 'fixed';
      this.container.style.top = '0';
      this.container.style.right = '0';
      this.container.style.zIndex = '9999';
      this.container.style.pointerEvents = 'none';
      document.body.appendChild(this.container);
      
      this.root = createRoot(this.container);
    }
  }

  show(error: ErrorDetails) {
    // Prevent duplicate notifications for the same error
    const errorKey = `${error.message}-${error.timestamp.getTime()}`;
    if (this.notifications.has(errorKey)) {
      return;
    }
    this.notifications.add(errorKey);

    this.createContainer();

    const handleClose = () => {
      this.notifications.delete(errorKey);
      if (this.notifications.size === 0 && this.container) {
        this.root?.unmount();
        document.body.removeChild(this.container);
        this.container = null;
        this.root = null;
      }
    };

    // Enable pointer events for the notification
    if (this.container) {
      this.container.style.pointerEvents = 'auto';
    }

    this.root?.render(
      <ErrorNotification 
        error={error} 
        onClose={handleClose} 
      />
    );

    // Auto-dismiss after 30 seconds
    setTimeout(() => {
      if (this.notifications.has(errorKey)) {
        handleClose();
      }
    }, 30000);
  }

  clear() {
    this.notifications.clear();
    if (this.container) {
      this.root?.unmount();
      document.body.removeChild(this.container);
      this.container = null;
      this.root = null;
    }
  }
}

export const errorNotificationManager = new ErrorNotificationManager();

export default ErrorNotification;