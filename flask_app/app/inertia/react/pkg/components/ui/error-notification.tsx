import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AlertTriangle, Bug, Mail, ChevronDown, ChevronUp } from 'lucide-react';
import { createRoot } from 'react-dom/client';
import { isDevelopment } from '../../utils/environment';

export interface ErrorDetails {
  message: string;
  stack: string | undefined;
  timestamp: Date;
  userAgent: string;
  url: string;
  type: 'javascript' | 'react' | 'network' | 'unknown';
  source: string | undefined;
}

interface ErrorNotificationProps {
  error: ErrorDetails;
  onClose: () => void;
}

const ErrorNotification: React.FC<ErrorNotificationProps> = ({ error, onClose }) => {
  const [copied, setCopied] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const isDevMode = isDevelopment();

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  const formatErrorForClipboard = () => {
    return `
🐛 APPLICATION ERROR REPORT

Timestamp: ${error.timestamp.toISOString()}
Type: ${error.type.toUpperCase()}
URL: ${error.url}
User Agent: ${error.userAgent}

Error Message:
${error.message}

${error.stack ? `Stack Trace:\n${error.stack}` : ''}

${error.source ? `Source: ${error.source}` : ''}

---
Generated by IJACK Error Reporter
    `.trim();
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(formatErrorForClipboard());
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = formatErrorForClipboard();
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const sendSupportEmail = () => {
    const subject = encodeURIComponent(`Error Report: ${getErrorTypeLabel()}`);
    const body = encodeURIComponent(formatErrorForClipboard());
    const mailtoUrl = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    window.open(mailtoUrl, '_blank');
  };

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Wait for animation
  };

  const getErrorIcon = () => {
    switch (error.type) {
      case 'react':
        return <Bug className="h-5 w-5 text-red-500" />;
      case 'network':
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
    }
  };

  const getErrorTypeLabel = () => {
    switch (error.type) {
      case 'react':
        return 'React Component Error';
      case 'javascript':
        return 'JavaScript Error';
      case 'network':
        return 'Network Error';
      default:
        return 'Application Error';
    }
  };

  return (
    <div
      className={`fixed top-4 right-4 z-[9999] transition-all duration-300 ease-out ${
        isVisible 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-full opacity-0'
      }`}
      style={{ maxWidth: '420px' }}
    >
      <div className="bg-white border border-red-200 rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-red-50 to-orange-50 border-b border-red-100 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getErrorIcon()}
              <div>
                <h3 className="text-sm font-semibold text-gray-900">
                  {getErrorTypeLabel()}
                </h3>
                <p className="text-xs text-gray-600">
                  {error.timestamp.toLocaleString()}
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="space-y-3">
            <div>
              <p className="text-xs font-medium text-gray-700 mb-1">Error Message:</p>
              <p className="text-sm text-gray-900 bg-gray-50 rounded p-2 font-mono text-wrap break-words">
                {error.message}
              </p>
            </div>

            {isDevMode && (
              <>
                {error.source && (
                  <div>
                    <p className="text-xs font-medium text-gray-700 mb-1">Source:</p>
                    <p className="text-xs text-gray-600 bg-gray-50 rounded p-2 font-mono">
                      {error.source}
                    </p>
                  </div>
                )}

                {error.stack && (
                  <div>
                    <button
                      onClick={() => setShowDetails(!showDetails)}
                      className="flex items-center space-x-1 text-xs font-medium text-gray-700 hover:text-gray-900 transition-colors"
                    >
                      {showDetails ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
                      <span>Stack Trace</span>
                    </button>
                    {showDetails && (
                      <div className="mt-2 bg-gray-50 rounded p-2 max-h-40 overflow-y-auto">
                        <pre className="text-xs text-gray-700 font-mono whitespace-pre-wrap">
                          {error.stack}
                        </pre>
                      </div>
                    )}
                  </div>
                )}

                <div className="bg-yellow-50 border border-yellow-200 rounded p-2">
                  <p className="text-xs text-yellow-800">
                    <strong>Development Mode:</strong> Full error details are shown above. In production, users will see a simplified error message with support contact options.
                  </p>
                </div>
              </>
            )}

            <div className="pt-2 border-t border-gray-100 space-y-2">
              {isDevMode ? (
                <button
                  onClick={copyToClipboard}
                  className={`flex items-center space-x-2 w-full px-3 py-2 rounded-md text-sm font-medium transition-all ${
                    copied
                      ? 'bg-green-100 text-green-700 border border-green-200'
                      : 'bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100'
                  }`}
                >
                  {copied ? (
                    <>
                      <CheckCheck className="h-4 w-4" />
                      <span>Copied to clipboard!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      <span>Copy full error details</span>
                    </>
                  )}
                </button>
              ) : (
                <div className="space-y-2">
                  <button
                    onClick={sendSupportEmail}
                    className="flex items-center space-x-2 w-full px-3 py-2 rounded-md text-sm font-medium bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 transition-all"
                  >
                    <Mail className="h-4 w-4" />
                    <span>Email IJACK Support</span>
                  </button>
                  <button
                    onClick={copyToClipboard}
                    className={`flex items-center space-x-2 w-full px-3 py-2 rounded-md text-sm font-medium transition-all ${
                      copied
                        ? 'bg-green-100 text-green-700 border border-green-200'
                        : 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {copied ? (
                      <>
                        <CheckCheck className="h-4 w-4" />
                        <span>Copied!</span>
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4" />
                        <span>Copy error report</span>
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 border-t border-gray-100 px-4 py-3">
          <p className="text-xs text-gray-500 text-center">
            {isDevMode 
              ? 'Full error details are available for debugging in development mode'
              : 'Share this error report with IJACK support for faster troubleshooting'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

// Error notification manager
class ErrorNotificationManager {
  private container: HTMLDivElement | null = null;
  private root: any = null;
  private notifications: Set<string> = new Set();

  private createContainer() {
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'error-notifications';
      this.container.style.position = 'fixed';
      this.container.style.top = '0';
      this.container.style.right = '0';
      this.container.style.zIndex = '9999';
      this.container.style.pointerEvents = 'none';
      document.body.appendChild(this.container);
      
      this.root = createRoot(this.container);
    }
  }

  show(error: ErrorDetails) {
    // Prevent duplicate notifications for the same error
    const errorKey = `${error.message}-${error.timestamp.getTime()}`;
    if (this.notifications.has(errorKey)) {
      return;
    }
    this.notifications.add(errorKey);

    this.createContainer();

    const handleClose = () => {
      this.notifications.delete(errorKey);
      if (this.notifications.size === 0 && this.container) {
        this.root?.unmount();
        document.body.removeChild(this.container);
        this.container = null;
        this.root = null;
      }
    };

    // Enable pointer events for the notification
    if (this.container) {
      this.container.style.pointerEvents = 'auto';
    }

    this.root?.render(
      <ErrorNotification 
        error={error} 
        onClose={handleClose} 
      />
    );

    // Auto-dismiss after 30 seconds
    setTimeout(() => {
      if (this.notifications.has(errorKey)) {
        handleClose();
      }
    }, 30000);
  }

  clear() {
    this.notifications.clear();
    if (this.container) {
      this.root?.unmount();
      document.body.removeChild(this.container);
      this.container = null;
      this.root = null;
    }
  }
}

export const errorNotificationManager = new ErrorNotificationManager();

export default ErrorNotification;