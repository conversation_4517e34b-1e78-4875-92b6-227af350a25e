import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
        "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
        className,
      )}
      {...props}
    />
  );
}

const InputMaskColorer = ({
  className,
  classNameMask = "",
  type,
  ref,
  ...props
}: React.ComponentProps<"input"> & { classNameMask?: string }) => {
  return (
    <div className="group relative flex h-full w-full justify-center">
      <span
        className={cn(
          "pointer-events-none absolute inset-0 z-10 flex h-full items-center px-2 align-middle text-sm font-medium outline-none",
          className,
        )}
      >
        {`${props?.value ?? ""}`.split("").map((char, i) => {
          if (["Y", "M", "D"].includes(char)) {
            return (
              <span key={i} className={cn("text-gray-400", classNameMask)}>
                {char}
              </span>
            );
          } else {
            return char;
          }
        })}
      </span>
      <Input
        type={type}
        className={cn(
          "pointer-events-auto h-full w-full rounded border px-2 py-2 pr-0 text-sm font-medium text-transparent caret-black outline-none selection:z-50 selection:bg-black/20 selection:text-transparent hover:outline-none",
          className,
        )}
        autoComplete="off"
        ref={ref}
        {...props}
        tabIndex={0}
      />
    </div>
  );
};

export { Input, InputMaskColorer };
