import { cn } from "@/lib/utils";

export const G<PERSON>pieIcon = ({ className }: { className?: string }) => (
  <svg
    viewBox="0 0 210 50"
    className={cn("icon-fill-default", className)}
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="10" height="10" x="20" y="10" fill="currentColor" />
    <rect width="10" height="10" x="60" y="10" fill="currentColor" />
    <rect width="10" height="10" x="100" y="10" fill="currentColor" />
    <rect width="10" height="10" x="140" y="10" fill="currentColor" />
    <rect width="10" height="10" x="180" y="10" fill="currentColor" />
    <rect width="10" height="10" x="40" y="30" fill="currentColor" />
    <rect width="10" height="10" x="80" y="30" fill="currentColor" />
    <rect width="10" height="10" x="120" y="30" fill="currentColor" />
    <rect width="10" height="10" x="160" y="30" fill="currentColor" />
  </svg>
);
