import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

// Define the structure for chart data
interface ChartDataItem {
  year: number;
  [unitType: string]: number;
}

export function StructuresByYearAndUnitTypeChart() {
  const { data, isLoading, error } = $api.useQuery(
    "get",
    "/v1/structure/by-year-by-unit-type",
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Structures by Unit Type</CardTitle>
        </CardHeader>
        <CardContent className="h-[400px]">
          <Skeleton className="h-full w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Structures by Unit Type</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[400px] items-center justify-center">
            <p className="text-muted-foreground text-sm">
              Error loading data. Please try again.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get unique years and unit types
  const years = [...new Set(data.result.map((item) => item.year))].sort();
  const unitTypes = [...new Set(data.result.map((item) => item.unit_type))];

  // Organize data for stacked bar chart by year
  const chartData: ChartDataItem[] = years.map((year) => {
    const yearData: ChartDataItem = { year };
    unitTypes.forEach((unitType) => {
      const unitTypeData = data.result.find(
        (item) => item.year === year && item.unit_type === unitType,
      );
      yearData[unitType] = unitTypeData ? unitTypeData.total_count : 0;
    });
    return yearData;
  });

  // Array of colors for the chart
  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884D8",
    "#82CA9D",
    "#FF6B6B",
    "#6C757D",
    "#A4A4A4",
    "#FFC107",
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Structures by Unit Type Over Time</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 60,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis
                label={{
                  value: "Number of Structures",
                  angle: -90,
                  position: "insideLeft",
                }}
                width={80}
              />
              <Tooltip
                formatter={(value) => Number(value)}
                labelFormatter={(value) => `Year: ${value}`}
              />
              <Legend />
              {unitTypes.map((unitType, index) => (
                <Bar
                  key={unitType}
                  dataKey={unitType}
                  stackId="a"
                  fill={COLORS[index % COLORS.length]}
                  name={unitType}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
