import React from 'react';
import { errorContextCollector } from '../utils/error-context-collector';
import { errorNotificationManager } from './ui/error-notification';
import { errorCollector } from '../utils/error-collector';

export const SimpleErrorTest: React.FC = () => {
  const testEnhancedError = () => {
    // Create a basic error
    const basicError = {
      message: 'Test error with enhanced context',
      stack: new Error().stack,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      type: 'javascript' as const,
      source: 'test-button',
    };

    // Collect enhanced context
    const enhancedError = errorContextCollector.collectEnhancedContext(basicError);
    
    console.log('Test - Enhanced error:', enhancedError);
    console.log('Test - Has userActions:', 'userActions' in enhancedError);
    console.log('Test - userActions:', enhancedError.userActions);
    
    // Add to collector and show
    const collectedError = errorCollector.addError(enhancedError);
    console.log('Test - Collected error:', collectedError);
    
    errorNotificationManager.show(collectedError);
  };

  return (
    <div className="p-4">
      <button
        onClick={testEnhancedError}
        className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
      >
        Test Enhanced Error
      </button>
    </div>
  );
};