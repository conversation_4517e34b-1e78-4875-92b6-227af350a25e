import { useMenuSelector, useMenuStore } from "@/stores/menu/hooks";
import { MenuStoreProvider } from "@/stores/menu/store";
import React from "react";
import { Logo } from "../icons/ijack-logo";
import { RCOM } from "../ui/rcom-button";
import { <PERSON>rollArea, ScrollBar } from "../ui/scroll-area";
import {
  LeftHeaderDefault,
  MobileMenuDefault,
  RightHeaderDefault,
} from "./header-defaults";

function MobileMenuContext({ children }: React.PropsWithChildren) {
  return <MenuStoreProvider>{children}</MenuStoreProvider>;
}

function MobileMenuButton() {
  const open = useMenuSelector((state) => state.context.mobileMenuOpen);
  const menuStore = useMenuStore();
  return (
    <div className="ml-auto flex items-center justify-center md:hidden">
      <button
        type="button"
        className="hover:text-ijack-green text-ijack-grey"
        onClick={() => menuStore.send({ type: "toggleMobileMenu" })}
      >
        <span className="sr-only">Open main menu</span>
        <svg
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          {open ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          )}
        </svg>
      </button>
    </div>
  );
}

function MobileMenuContent({ children }: React.PropsWithChildren) {
  const isOpen = useMenuSelector((state) => state.context.mobileMenuOpen);
  return (
    <div
      className={`${isOpen ? "flex" : "hidden"} w-full justify-self-end py-2 md:hidden`}
    >
      <div className="flex w-full flex-col space-y-1 pt-2 pb-3">{children}</div>
    </div>
  );
}

function LeftMenuContent({ children }: React.PropsWithChildren) {
  return (
    <div className="hidden min-w-0 flex-1 items-center md:flex">
      <ScrollArea className="w-full whitespace-nowrap">
        {children}
        <ScrollBar
          orientation="horizontal"
          className="h-1.5 flex-shrink-0 border border-gray-500"
          thumbClassName="bg-ijack-grey"
          forceMount
        />
      </ScrollArea>
    </div>
  );
}
function RightMenuContent({ children }: React.PropsWithChildren) {
  return (
    <div className="-ml-4 hidden items-center gap-2 space-y-1 justify-self-end md:flex">
      {children}
    </div>
  );
}

type HeaderProps = {
  leftHeaderContent?: React.ReactNode;
  rightHeaderContent?: React.ReactNode;
  mobileHeaderContent?: React.ReactNode;
};

function Header({
  leftHeaderContent = <LeftHeaderDefault />,
  rightHeaderContent = <RightHeaderDefault />,
  mobileHeaderContent = <MobileMenuDefault />,
}: HeaderProps) {
  return (
    <header className="w-full border-b border-gray-800 bg-[#111111]">
      <div className="mx-auto w-full max-w-[1320px] px-4">
        <MobileMenuContext>
          <div className="mx-auto flex h-16 flex-row items-center justify-start gap-4">
            <Logo />
            <LeftMenuContent>{leftHeaderContent}</LeftMenuContent>
            <RightMenuContent>{rightHeaderContent}</RightMenuContent>
            <MobileMenuButton />
            <div>
              <RCOM />
            </div>
          </div>
          <MobileMenuContent>{mobileHeaderContent}</MobileMenuContent>
        </MobileMenuContext>
      </div>
    </header>
  );
}

export { Header };
