import React from "react";
import { But<PERSON> } from "../ui/button";
import { Text } from "../ui/typography";
import { Separator } from "@radix-ui/react-separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { usePage } from "@inertiajs/react";
import { User } from "@/types/user";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";
import { cn } from "@/lib/utils";

const products = [
  {
    label: "Configure",
    items: [
      {
        label: "Build and price",
        href: "/build-and-price",
      },
    ],
  },
  {
    label: "Multiphase Pump",
    items: [
      {
        label: "XFER - dual-acting reciprocating tri-phase pump",
        href: "/xfer",
      },
    ],
  },
  {
    label: "Vapor Recovery",
    items: [
      {
        label: "VRU - eliminate emissions",
        href: "/vru",
      },
    ],
  },
  {
    label: "Gas Compression",
    items: [
      {
        label: "EGAS - hydraulic casing wet gas compressor",
        href: "/egas",
      },
      { label: "DGAS - beam-mounted bolt-on gas compressor", href: "/dgas" },
    ],
  },
  {
    label: "High Pressure Injection Pump",
    items: [
      {
        label: "BOOST - high pressure injection pump",
        href: "/boost",
      },
    ],
  },
  {
    label: "Artificial Lift",
    items: [
      { label: "UNO - hydraulic pump jack", href: "/uno" },
      { label: "UNOGAS - hydraulic pump jack with EGAS", href: "/unogas" },
    ],
  },
  {
    label: "IJACK Web App",
    items: [{ label: "RCOM - remote control and monitoring", href: "/rcom" }],
  },
  {
    label: "Patents",
    items: [{ label: "IJACK Patents", href: "/patents" }],
  },
];

const headerUrls = [
  { label: "Apply", href: "/apply" },
  { label: "Media", href: "/media" },
  { label: "Docs", href: "/docs" },
  { label: "Contact", href: "/contact" },
  { label: "Careers", href: "/careers" },
];

function ProductMenu() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="menu-link" size="menu-link">
          Products
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-fit" align="start">
        {products.map((product, index, arr) => (
          <React.Fragment key={product.label}>
            <DropdownMenuLabel asChild>
              <Text className="font-extralight text-gray-500 uppercase">
                {product.label}
              </Text>
            </DropdownMenuLabel>
            <DropdownMenuGroup>
              {product.items.map((item) => (
                <DropdownMenuItem key={item.label} asChild>
                  <Button
                    variant="ghost"
                    className="w-full cursor-pointer justify-start py-0 text-base font-normal hover:outline-0 focus-visible:ring-0"
                    asChild
                  >
                    <a href={item.href}>{item.label}</a>
                  </Button>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
            {arr.length - 1 !== index && (
              <Separator className="my-1 h-px bg-gray-300" />
            )}
          </React.Fragment>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function LeftHeaderDefault() {
  return (
    <>
      <ProductMenu />
      {headerUrls.map((item) => (
        <Button
          key={item.label}
          variant="menu-link"
          size="menu-link"
          className="hidden md:inline-flex"
          asChild
        >
          <a href={item.href}>{item.label}</a>
        </Button>
      ))}
    </>
  );
}

function RightHeaderDefault({ className }: { className?: string }) {
  const page = usePage();
  const user = page.props?.user as User;
  return (
    <>
      {(user?.roles ?? [])?.includes("IJACK Admin") && (
        <Button
          variant="menu-link"
          size="menu-link"
          className={cn("m-0 hidden md:flex", className)}
          asChild
        >
          <a href="/admin">Admin</a>
        </Button>
      )}
      {user?.id == null ? (
        <>
          <Button
            variant="menu-link"
            size="menu-link"
            className={cn("hidden md:inline-flex", className)}
            asChild
          >
            <a href="/register">Register</a>
          </Button>
          <Button
            variant="menu-link"
            size="menu-link"
            className={cn("hidden md:inline-flex", className)}
            asChild
          >
            <a href="/login">Login</a>
          </Button>
        </>
      ) : (
        <Button
          variant="menu-link"
          size="menu-link"
          className={cn("hidden md:inline-flex", className)}
          asChild
        >
          <a href="/logout">Logout</a>
        </Button>
      )}
    </>
  );
}

function MobileMenuDefault() {
  return (
    <div className="mx-auto flex h-fit w-full max-w-xl flex-col items-center gap-2">
      <Accordion type="multiple" className="w-full">
        <AccordionItem value="products" className="border-gray-700">
          <Button
            variant="menu-link"
            size="menu-link"
            className="w-full justify-start hover:no-underline"
            asChild
          >
            <AccordionTrigger>Products</AccordionTrigger>
          </Button>
          <AccordionContent className="px-1 pb-2 sm:px-2">
            <div className="space-y-1">
              {products.map((product, index, array) => (
                <div key={product.label} className="w-full px-2">
                  <Text className="text-ijack-grey font-extralight uppercase">
                    {product.label}
                  </Text>
                  {product.items.map((item) => (
                    <Button
                      key={item.label}
                      variant="ghost"
                      tabIndex={0}
                      className="hover:text-ijack-green hover:text-ijack-green h-auto min-h-0 w-full justify-start rounded-md py-2 pl-6 text-left text-base font-light whitespace-normal text-gray-300 hover:bg-transparent"
                      asChild
                    >
                      <a
                        href={item.href}
                        className="text-left leading-relaxed whitespace-normal"
                      >
                        {item.label}
                      </a>
                    </Button>
                  ))}
                  {index < array.length - 1 && (
                    <Separator className="bg-ijack-grey my-1 h-px" />
                  )}
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      {headerUrls.map((item) => (
        <Button
          key={item.label}
          variant="menu-link"
          size="menu-link"
          className="w-full justify-start"
          asChild
        >
          <a href={item.href}>{item.label}</a>
        </Button>
      ))}
      <RightHeaderDefault className="flex w-full items-start justify-start" />
    </div>
  );
}
export { LeftHeaderDefault, RightHeaderDefault, MobileMenuDefault };
