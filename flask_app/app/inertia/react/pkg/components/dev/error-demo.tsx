import React, { useState } from 'react';
import { <PERSON><PERSON>, Zap, <PERSON>ert<PERSON>riangle, Wifi } from 'lucide-react';
import { isDevelopment } from '../../utils/environment';

// Demo component for testing error handling (development only)
export const ErrorDemo: React.FC = () => {
  const [count, setCount] = useState(0);

  // Component that will throw on render
  const BrokenComponent = () => {
    if (count > 5) {
      throw new Error('Component crashed! Count exceeded maximum limit.');
    }
    return <span>Count: {count}</span>;
  };

  const triggerJavaScriptError = () => {
    // @ts-expect-error - Intentional error for testing
    window.nonExistentFunction();
  };

  const triggerPromiseRejection = () => {
    Promise.reject(new Error('Unhandled promise rejection test'));
  };

  const triggerNetworkError = async () => {
    try {
      await fetch('/api/non-existent-endpoint');
    } catch {
      // This will be caught by the global error handler
    }
  };

  const triggerConsoleError = () => {
    console.error(new Error('Manual console error test'));
  };

  if (!isDevelopment()) {
    return null; // Don't show in production
  }

  return (
    <div className="fixed bottom-4 left-4 p-4 bg-gray-900 text-white rounded-lg shadow-lg max-w-xs">
      <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
        <Bug className="h-4 w-4" />
        Error Demo (Dev Only)
      </h3>
      
      <div className="space-y-2 text-xs">
        <div>
          <BrokenComponent />
          <button
            onClick={() => setCount(c => c + 1)}
            className="ml-2 px-2 py-1 bg-red-600 hover:bg-red-700 rounded text-xs"
          >
            +1 (Crash at 6)
          </button>
        </div>

        <button
          onClick={triggerJavaScriptError}
          className="w-full flex items-center gap-2 px-2 py-1 bg-orange-600 hover:bg-orange-700 rounded"
        >
          <Zap className="h-3 w-3" />
          JS Error
        </button>

        <button
          onClick={triggerPromiseRejection}
          className="w-full flex items-center gap-2 px-2 py-1 bg-yellow-600 hover:bg-yellow-700 rounded"
        >
          <AlertTriangle className="h-3 w-3" />
          Promise Rejection
        </button>

        <button
          onClick={triggerNetworkError}
          className="w-full flex items-center gap-2 px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded"
        >
          <Wifi className="h-3 w-3" />
          Network Error
        </button>

        <button
          onClick={triggerConsoleError}
          className="w-full flex items-center gap-2 px-2 py-1 bg-purple-600 hover:bg-purple-700 rounded"
        >
          <Bug className="h-3 w-3" />
          Console Error
        </button>
      </div>
    </div>
  );
};

export default ErrorDemo;