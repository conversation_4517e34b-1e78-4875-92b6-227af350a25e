import React from 'react';
import { <PERSON>ug, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>if<PERSON>, Zap } from 'lucide-react';

/**
 * Enhanced Error Handler Test Component
 * 
 * This component provides buttons to trigger different types of errors
 * to test the enhanced error collection and bulk reporting functionality.
 */
const EnhancedErrorTest: React.FC = () => {
  const triggerJavaScriptError = () => {
    // @ts-ignore - Intentionally accessing undefined to trigger error
    const result = window.undefinedObject.someProperty;
    console.log(result);
  };

  const triggerTypeError = () => {
    // @ts-ignore - Intentionally calling undefined as function
    const func = undefined;
    // @ts-ignore - Intentionally calling undefined as function for testing
    func();
  };

  const triggerPromiseRejection = () => {
    Promise.reject(new Error('Test promise rejection error'));
  };

  const triggerNetworkError = async () => {
    try {
      await fetch('/api/non-existent-endpoint-test');
    } catch (error) {
      console.error('Network test error:', error);
    }
  };

  const triggerConsoleError = () => {
    console.error(new Error('Test console error message'));
  };

  const triggerMultipleErrors = () => {
    // Trigger several errors in quick succession to test aggregation
    setTimeout(() => triggerJavaScriptError(), 100);
    setTimeout(() => triggerTypeError(), 200);
    setTimeout(() => triggerPromiseRejection(), 300);
    setTimeout(() => triggerConsoleError(), 400);
    setTimeout(() => triggerNetworkError(), 500);
  };

  const triggerSimilarErrors = () => {
    // Trigger the same error multiple times to test deduplication and counting
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        console.error(new Error('Repeated error for testing deduplication'));
      }, i * 100);
    }
  };

  const triggerDifferentSeverities = () => {
    // Trigger errors that should have different severity levels
    setTimeout(() => {
      // High severity - unhandled reference error
      // @ts-ignore
      window.criticalUndefinedFunction();
    }, 100);

    setTimeout(() => {
      // Medium severity - console error
      console.error('Medium severity test error');
    }, 200);

    setTimeout(() => {
      // Network error - varies by status code
      fetch('/api/test-500-error').catch(() => {});
    }, 300);
  };

  const clearAllErrors = () => {
    window.location.reload();
  };

  return (
    <div className="p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">
          Enhanced Error Handler Test
        </h2>
        <p className="text-sm text-gray-600">
          Use these buttons to test the enhanced error collection and bulk reporting features.
          Each button will trigger different types of errors to verify the system works correctly.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Individual Error Types */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Individual Error Types</h3>
          
          <button
            onClick={triggerJavaScriptError}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-red-50 text-red-700 border border-red-200 rounded hover:bg-red-100 transition-colors"
          >
            <Bug className="h-4 w-4" />
            <span>JavaScript Error</span>
          </button>

          <button
            onClick={triggerTypeError}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-red-50 text-red-700 border border-red-200 rounded hover:bg-red-100 transition-colors"
          >
            <AlertTriangle className="h-4 w-4" />
            <span>Type Error</span>
          </button>

          <button
            onClick={triggerPromiseRejection}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-orange-50 text-orange-700 border border-orange-200 rounded hover:bg-orange-100 transition-colors"
          >
            <Zap className="h-4 w-4" />
            <span>Promise Rejection</span>
          </button>

          <button
            onClick={triggerNetworkError}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-yellow-50 text-yellow-700 border border-yellow-200 rounded hover:bg-yellow-100 transition-colors"
          >
            <Wifi className="h-4 w-4" />
            <span>Network Error</span>
          </button>

          <button
            onClick={triggerConsoleError}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-purple-50 text-purple-700 border border-purple-200 rounded hover:bg-purple-100 transition-colors"
          >
            <Bug className="h-4 w-4" />
            <span>Console Error</span>
          </button>
        </div>

        {/* Bulk Testing */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Bulk Testing</h3>
          
          <button
            onClick={triggerMultipleErrors}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-blue-50 text-blue-700 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
          >
            <AlertTriangle className="h-4 w-4" />
            <span>Trigger Multiple Different Errors</span>
          </button>

          <button
            onClick={triggerSimilarErrors}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-indigo-50 text-indigo-700 border border-indigo-200 rounded hover:bg-indigo-100 transition-colors"
          >
            <Bug className="h-4 w-4" />
            <span>Trigger Similar Errors (3x)</span>
          </button>

          <button
            onClick={triggerDifferentSeverities}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-green-50 text-green-700 border border-green-200 rounded hover:bg-green-100 transition-colors"
          >
            <Zap className="h-4 w-4" />
            <span>Test Different Severities</span>
          </button>

          <button
            onClick={clearAllErrors}
            className="flex items-center space-x-2 w-full px-3 py-2 text-sm bg-gray-50 text-gray-700 border border-gray-200 rounded hover:bg-gray-100 transition-colors"
          >
            <span>🔄</span>
            <span>Clear All (Reload Page)</span>
          </button>
        </div>
      </div>

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
        <h4 className="text-sm font-medium text-blue-800 mb-2">What to Test:</h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Single errors should show with severity badges and occurrence counts</li>
          <li>• Multiple errors should show "Multiple Errors Detected" section</li>
          <li>• Similar errors should be deduplicated with occurrence counts</li>
          <li>• "Copy All" should include all collected errors in the clipboard</li>
          <li>• "Email All" should work in production mode with all errors</li>
          <li>• Errors should be sorted by severity (critical → high → medium → low)</li>
        </ul>
      </div>
    </div>
  );
};

export default EnhancedErrorTest;