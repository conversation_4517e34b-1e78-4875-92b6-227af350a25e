import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import {
  <PERSON>C<PERSON>,
  Bar,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { Skeleton } from "@/components/ui/skeleton";
import { $api } from "@/api/web-api";

// Define the data structure returned by the API
interface SalesByModelData {
  model: string;
  year: number;
  total_sales: number;
}

// Define the structure for chart data
interface ChartDataItem {
  year: number;
  [modelName: string]: number;
}

export function SalesByYearAndModelChart() {
  const {
    data: apiData,
    isLoading,
    error,
  } = $api.useQuery("get", "/v1/work-order/sales-by-year-and-model");

  // TypeScript type assertion to properly handle the API response
  const data = apiData as unknown as { result: SalesByModelData[] };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales by Model</CardTitle>
        </CardHeader>
        <CardContent className="h-[400px]">
          <Skeleton className="h-full w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales by Model</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[400px] items-center justify-center">
            <p className="text-muted-foreground text-sm">
              Error loading data. Please try again.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get unique years to organize data
  const years = [...new Set(data.result.map((item) => item.year))].sort();

  // Get all models sorted by total sales
  const models = [...new Set(data.result.map((item) => item.model))];
  const modelSales = models.map((model) => ({
    model,
    totalSales: data.result
      .filter((item) => item.model === model)
      .reduce((sum, item) => sum + item.total_sales, 0),
  }));

  const sortedModels = modelSales
    .sort((a, b) => b.totalSales - a.totalSales)
    .map((item) => item.model);

  // Organize data for stacked bar chart
  const chartData: ChartDataItem[] = years.map((year) => {
    const yearData: ChartDataItem = { year };
    sortedModels.forEach((model) => {
      const modelData = data.result.find(
        (item) => item.year === year && item.model === model,
      );
      yearData[model] = modelData ? modelData.total_sales : 0;
    });
    return yearData;
  });

  // Array of colors for the chart - expanded to handle more models
  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884D8",
    "#82CA9D",
    "#FF6B6B",
    "#6C757D",
    "#A4A4A4",
    "#FFC107",
    "#3F51B5",
    "#FF5722",
    "#607D8B",
    "#9C27B0",
    "#2196F3",
    "#4CAF50",
    "#CDDC39",
    "#795548",
    "#9E9E9E",
    "#F44336",
  ];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Sales by Model</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 40,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis
                tickFormatter={(value) => formatCurrency(value)}
                width={80}
              />
              <Tooltip
                formatter={(value) => formatCurrency(Number(value))}
                labelFormatter={(value) => `Year: ${value}`}
              />
              {sortedModels.map((model, index) => (
                <Bar
                  key={model}
                  dataKey={model}
                  stackId="a"
                  fill={COLORS[index % COLORS.length]}
                  name={model}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
