import {
  Bar,
  Composed<PERSON>hart,
  CartesianGrid,
  XAxis,
  Line,
  YAxis,
  Label,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label as LabelComponent } from "@/components/ui/label";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { $api } from "@/api/web-api";
import { useSalesSelector } from "@/stores/sales/hooks";
import { zDate, zIsoDate } from "@/lib/zod-utils";
import { MinusIcon } from "lucide-react";
import { Payload } from "recharts/types/component/DefaultLegendContent";
export const description = "A multiple bar chart";

function RunningTotalIcon() {
  return <MinusIcon className="stroke-black" />;
}

/**
 * Props interface for the CurrencyLabelFormatter component
 * Matches the first two parameters of the Recharts Formatter type
 */
interface CurrencyLabelFormatterProps {
  value: number | string;
  label: string;
  payload: Payload;
}

/**
 * Currency formatter component for chart legends and tooltips
 *
 * Takes a numeric value and entry data from Recharts and formats it as currency
 * with appropriate scale indicators (K, M, B)
 *
 * @param value - The numeric value to format (from chart data)
 * @param entry - Chart entry metadata including color, type, dataKey, etc.
 * @returns Formatted React element with currency display
 */
function CurrencyLabelFormatter({
  value,
  label,
  payload,
}: CurrencyLabelFormatterProps) {
  return (
    <div className="border-input flex w-full items-center gap-1 border-t pt-1">
      {payload.color && (
        <span
          className="inline-block h-3 w-3 rounded-sm"
          style={{ backgroundColor: payload.color }}
        />
      )}
      <LabelComponent.Numeric value={value} variant="dollars" />
      <span className="text-muted-foreground w-auto flex-grow text-end text-sm">
        {label}
      </span>
    </div>
  );
}

const formatCurrency = (value: number): string => {
  if (value >= 1000000000) {
    return `$${(value / 1000000000).toFixed(1)}B`;
  } else if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(0)}K`;
  }
  return `$${value.toLocaleString()}`;
};
const modelChartConfig = {
  egas: {
    label: "EGAS",
    color: "var(--chart-1)",
  },
  dgas: {
    label: "DGAS",
    color: "var(--chart-2)",
  },
  vru: {
    label: "VRU",
    color: "var(--chart-3)",
  },
  xfer: {
    label: "XFER",
    color: "var(--chart-4)",
  },
  boost: {
    label: "Boost",
    color: "var(--chart-5)",
  },
  unogas: {
    label: "UNOGAS",
    color: "var(--chart-6)",
  },
  uno: {
    label: "UNO",
    color: "var(--chart-7)",
  },
  total: {
    label: "Running Total",
    icon: RunningTotalIcon,
    color: "#000",
  },
} satisfies ChartConfig;

const chartConfig = {
  sale: {
    label: "Sales",
    color: "var(--chart-1)",
  },
  repair: {
    label: "Repairs",
    color: "var(--chart-2)",
  },
  rental: {
    label: "Rentals",
    color: "var(--chart-3)",
  },
  parts: {
    label: "Parts",
    color: "var(--chart-4)",
  },
  new_installation: {
    label: "Installs",
    color: "var(--chart-5)",
  },
  preventative_maintenance: {
    label: "Preventative Maintenance",
    color: "var(--chart-6)",
  },
  customer: {
    label: "Customer",
    color: "var(--chart-7)",
  },
  total: {
    label: "Running Total",
    icon: RunningTotalIcon,
    color: "#000",
  },
} satisfies ChartConfig;

function dateTickMonthFormatter(value: string | number): string {
  const date = zDate.parse(value);
  if (date) {
    return date.toLocaleDateString("en-US", {
      month: "short",
    });
  }
  return "";
}
function dateTickYearFormatter(value: string | number): string {
  const date = zDate.parse(value);
  if (date && date.getMonth() === 0) {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
    });
  }
  return "";
}
function dateLegendFormatter(value: string | number): string {
  const date = zDate.parse(value);
  if (date) {
    return date.toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    });
  }
  return "";
}
export function MonthlyUnitTypeChart() {
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector(
    (state) => state.context.service_types,
  );
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);
  const models = useSalesSelector((state) => state.context.model_types);
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/sales-analytics/monthly-model-type-revenue",
    {
      body: {
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        customers: customers,
        service_types: service_types,
        models: models,
      },
    },
  );
  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Unit Type Revenue</CardTitle>
        <CardDescription>
          Revenue breakdown by model type with running total
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading && <div>Loading...</div>}
        {isError && <div>Error loading chart data</div>}
        {isSuccess && (
          <ChartContainer
            className="aspect-auto h-96 w-full"
            config={modelChartConfig}
          >
            <ComposedChart
              data={data.result}
              margin={{
                top: 30, // Increased top margin for Y-axis label
                right: 0, // Increased right margin for right Y-axis and label
                left: 0, // Increased left margin for left Y-axis label
              }}
            >
              <ChartLegend
                className="flex-wrap"
                content={
                  <ChartLegendContent
                    filterFunc={(item) => {
                      // for the item get all data keys and check if the datakeys is not 0
                      const key = item.dataKey as keyof typeof modelChartConfig;
                      const v = data.result.reduce((acc, d) => {
                        if (d[key] !== 0) {
                          acc = acc + (d[key] ?? 0);
                        }
                        return acc;
                      }, 0);
                      if (v !== 0) {
                        return true;
                      }
                      return false;
                    }}
                  />
                }
              />
              <CartesianGrid vertical={true} strokeDasharray="3 3" />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    filterFunc={(item) => item.value !== 0}
                    className="w-[250px]"
                    nameKey="views"
                    formatter={(value, name, item) => {
                      return (
                        <CurrencyLabelFormatter
                          value={Number(value)}
                          label={`${name}`}
                          payload={item as unknown as Payload}
                        />
                      );
                    }}
                    labelFormatter={dateLegendFormatter}
                  />
                }
              />

              {/* X-axis for months and years */}
              <XAxis
                dataKey="month"
                tickLine={false}
                tickFormatter={dateTickMonthFormatter}
                tickMargin={10}
                axisLine={true}
              />
              <XAxis
                xAxisId={"year"}
                dataKey="month"
                tickLine={false}
                tickFormatter={dateTickYearFormatter}
                axisLine={false}
              />

              {/* Left Y-axis for service type bars - LINEAR scale with formatting */}
              <YAxis
                yAxisId="service"
                orientation="left"
                tickFormatter={formatCurrency}
                tickLine={false}
                axisLine={false}
                domain={[0, "dataMax"]} // Start from 0 for proper visual comparison
              >
                <Label
                  value="Unit"
                  position="top"
                  offset={20} // Add some spacing from the top
                  className="font-bold text-black"
                />
              </YAxis>

              {/* Right Y-axis for running total line - LINEAR scale */}
              <YAxis
                yAxisId="total"
                orientation="right"
                tickFormatter={formatCurrency}
                tickLine={false}
                axisLine={false}
                domain={[
                  0,
                  data.result.reduce(
                    (max, item) => Math.max(max, item.total),
                    0,
                  ),
                ]} // Dynamic max based on data
              >
                <Label
                  value="Total"
                  position="top"
                  offset={20} // Add some spacing from the top
                  className="font-bold text-black"
                />
              </YAxis>

              <Bar
                dataKey="egas"
                stackId={"service"}
                yAxisId="service"
                name="EGAS"
                fill="var(--color-egas)"
              />

              <Bar
                stackId={"service"}
                dataKey="dgas"
                yAxisId="service"
                name="DGAS"
                fill="var(--color-dgas)"
              />

              <Bar
                stackId={"service"}
                dataKey="vru"
                yAxisId="service"
                name="VRU"
                fill="var(--color-vru)"
              />

              <Bar
                stackId={"service"}
                dataKey="xfer"
                yAxisId="service"
                name="XFER"
                fill="var(--color-xfer)"
              />
              <Bar
                dataKey="boost"
                stackId={"service"}
                yAxisId="service"
                name="Boost"
                fill="var(--color-boost)"
              />
              <Bar
                dataKey="unogas"
                yAxisId="service"
                stackId={"service"}
                fill="var(--color-unogas)"
                name="UNOGAS"
              />
              <Bar
                dataKey="uno"
                stackId={"service"}
                yAxisId="service"
                name="UNO"
                fill="var(--color-uno)"
              />

              {/* Running total line */}
              <Line
                type="monotone"
                strokeWidth={2}
                dot={false}
                dataKey="total"
                yAxisId="total"
                stroke="var(--color-total)"
                name="Running Total"
              />
            </ComposedChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}

export function MonthlyServiceTypeChart() {
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector(
    (state) => state.context.service_types,
  );
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);
  const models = useSalesSelector((state) => state.context.model_types);
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/sales-analytics/monthly-service-type-chart",
    {
      body: {
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        customers: customers,
        service_types: service_types,
        models: models,
      },
    },
  );
  return (
    <Card>
      <CardHeader>
        <CardTitle>Monthly Service Type Revenue</CardTitle>
        <CardDescription>
          Revenue breakdown by service type with running total
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading && <div>Loading...</div>}
        {isError && <div>Error loading chart data</div>}
        {isSuccess && (
          <ChartContainer
            className="aspect-auto h-96 w-full"
            config={chartConfig}
          >
            <ComposedChart
              data={data.result}
              margin={{
                top: 30, // Increased top margin for Y-axis label
                right: 10, // Increased right margin for right Y-axis and label
                left: 10, // Increased left margin for left Y-axis label
              }}
            >
              <ChartLegend
                className="flex-wrap"
                content={
                  <ChartLegendContent
                    filterFunc={(item) => {
                      // for the item get all data keys and check if the datakeys is not 0
                      const key = item.dataKey as keyof typeof chartConfig;
                      const v = data.result.reduce((acc, d) => {
                        if (d[key] !== 0) {
                          acc = acc + (d[key] ?? 0);
                        }
                        return acc;
                      }, 0);
                      if (v !== 0) {
                        return true;
                      }
                      return false;
                    }}
                  />
                }
              />
              <CartesianGrid vertical={true} strokeDasharray="3 3" />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    filterFunc={(item) => item.value !== 0}
                    className="w-[250px]"
                    nameKey="views"
                    formatter={(value, name, item) => {
                      return (
                        <CurrencyLabelFormatter
                          value={Number(value)}
                          label={`${name}`}
                          payload={item as unknown as Payload}
                        />
                      );
                    }}
                    labelFormatter={dateLegendFormatter}
                  />
                }
              />

              {/* X-axis for months and years */}
              <XAxis
                dataKey="month"
                tickLine={false}
                tickFormatter={dateTickMonthFormatter}
                tickMargin={10}
                axisLine={true}
              />
              <XAxis
                xAxisId={"year"}
                dataKey="month"
                tickLine={false}
                tickFormatter={dateTickYearFormatter}
                axisLine={false}
              />

              {/* Left Y-axis for service type bars - LINEAR scale with formatting */}
              <YAxis
                yAxisId="service"
                orientation="left"
                tickFormatter={formatCurrency}
                tickLine={false}
                axisLine={false}
                domain={[0, "dataMax"]} // Start from 0 for proper visual comparison
              >
                <Label
                  value="Service"
                  position="top"
                  offset={20} // Add some spacing from the top
                  className="font-bold text-black"
                />
              </YAxis>

              {/* Right Y-axis for running total line - LINEAR scale */}
              <YAxis
                yAxisId="total"
                orientation="right"
                tickFormatter={formatCurrency}
                tickLine={false}
                axisLine={false}
                domain={[
                  0,
                  data.result.reduce(
                    (max, item) => Math.max(max, item.total),
                    0,
                  ),
                ]} // Dynamic max based on data
              >
                <Label
                  value="Total"
                  position="top"
                  offset={20} // Add some spacing from the top
                  className="font-bold text-black"
                />
              </YAxis>

              <Bar
                dataKey="customer"
                stackId={"service"}
                yAxisId="service"
                name="Customer"
                fill="var(--color-customer)"
              />

              <Bar
                stackId={"service"}
                dataKey="new_installation"
                yAxisId="service"
                name="New Installation"
                fill="var(--color-new_installation)"
              />

              <Bar
                stackId={"service"}
                dataKey="parts"
                yAxisId="service"
                name="Parts"
                fill="var(--color-parts)"
              />

              <Bar
                stackId={"service"}
                dataKey="preventative_maintenance"
                yAxisId="service"
                name="Preventative Maintenance"
                fill="var(--color-preventative_maintenance)"
              />
              <Bar
                dataKey="rental"
                stackId={"service"}
                yAxisId="service"
                name="Rental"
                fill="var(--color-rental)"
              />
              <Bar
                dataKey="repair"
                yAxisId="service"
                stackId={"service"}
                fill="var(--color-repair)"
                name="Repair"
              />
              <Bar
                dataKey="sale"
                stackId={"service"}
                yAxisId="service"
                name="Sale"
                fill="var(--color-sale)"
              />

              {/* Running total line */}
              <Line
                type="monotone"
                strokeWidth={2}
                dot={false}
                dataKey="total"
                yAxisId="total"
                stroke="var(--color-total)"
                name="Running Total"
              />
            </ComposedChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
