import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
} from "@/components/ui/sidebar";
import React from "react";

type SidebarProps = React.ComponentProps<"div"> & {
  side?: "left" | "right";
  variant?: "sidebar" | "floating" | "inset";
  collapsible?: "offcanvas" | "icon" | "none";
};

export function DashboardSidebar({
  children,
  ...props
}: React.PropsWithChildren<SidebarProps>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarContent>
        <SidebarGroupContent className="flex flex-col gap-2 py-2">
          <SidebarMenu>
            <SidebarGroup>
              <SidebarMenu className="gap-0">{children}</SidebarMenu>
              <SidebarGroupContent className="max-h-44 overflow-auto"></SidebarGroupContent>
            </SidebarGroup>
          </SidebarMenu>
          <SidebarMenu></SidebarMenu>
        </SidebarGroupContent>
        <SidebarGroup />
      </SidebarContent>
      <SidebarFooter />
    </Sidebar>
  );
}
