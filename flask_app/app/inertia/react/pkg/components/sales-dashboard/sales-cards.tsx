import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import { UserIcon, CalendarIcon, ShoppingBag } from "lucide-react";
import React from "react";
import { Label } from "../ui/label";
import { H3, Muted } from "../ui/typography";
import { Skeleton } from "../ui/skeleton";
import { useSalesSelector } from "@/stores/sales/hooks";
import { zIsoDate } from "@/lib/zod-utils";

const SalesCards = ({ children }: React.PropsWithChildren) => {
  return <div className="grid grid-cols-3 gap-0 lg:gap-4">{children}</div>;
};

const CardLoader = () => {
  return (
    <div className="flex flex-col space-y-1 md:space-y-2">
      <Skeleton className="h-4 w-1/2 md:h-6" />
      <Skeleton className="h-3 w-1/3 md:h-4" />
    </div>
  );
};

const TopCustomer = () => {
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector(
    (state) => state.context.service_types,
  );
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);
  const models = useSalesSelector((state) => state.context.model_types);
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/sales-analytics/top-customer",
    {
      body: {
        customers,
        models,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        service_types,
      },
    },
  );
  return (
    <Card className="gap-2 rounded-r-none p-2 md:gap-6 md:p-4 lg:rounded-xl lg:p-6">
      <CardHeader className="flex h-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="text-sm font-medium md:text-sm">
          Top Customer
        </CardTitle>
        <UserIcon
          strokeWidth={2}
          className="hidden h-4 w-4 min-w-5 text-black md:flex md:h-5 md:w-5"
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              {data.result?.customer ?? "None"}
            </H3>
            <Muted className="text-sm md:text-sm">
              <Label.Numeric
                variant="dollars"
                value={data.result?.total_cost ?? "0.00"}
              />
            </Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

const TopYear = () => {
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector(
    (state) => state.context.service_types,
  );
  const models = useSalesSelector((state) => state.context.model_types);
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/sales-analytics/top-year",
    {
      body: {
        customers,
        models,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        service_types,
      },
    },
  );
  return (
    <Card className="gap-2 rounded-none p-2 md:gap-4 md:p-4 lg:rounded-xl lg:p-6">
      <CardHeader className="flex h-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="min-w-0 text-sm font-medium md:text-sm">
          Top Year
        </CardTitle>
        <CalendarIcon
          strokeWidth={2}
          className="hidden h-4 w-4 min-w-5 text-black md:flex md:h-5 md:w-5"
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              {data.result?.year ?? "None"}
            </H3>
            <Muted className="text-sm md:text-sm">
              <Label.Numeric
                variant="dollars"
                value={data.result?.total_cost ?? "0"}
              />
            </Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

const AverageOrderValue = () => {
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector(
    (state) => state.context.service_types,
  );
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);
  const models = useSalesSelector((state) => state.context.model_types);
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/sales-analytics/average-order-value",
    {
      body: {
        customers,
        models,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        service_types,
      },
    },
  );
  return (
    <Card className="gap-2 rounded-l-none p-2 md:gap-6 md:p-4 lg:rounded-xl lg:p-6">
      <CardHeader className="flex h-full w-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="min-w-0 text-sm font-medium md:text-sm">
          Average Order Value
        </CardTitle>
        <ShoppingBag
          strokeWidth={2}
          className="hidden h-4 w-4 min-w-5 text-black md:flex md:h-5 md:w-5"
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              <Label.Numeric variant="dollars" value={data.result ?? "0"} />
            </H3>
            <Muted className="text-sm md:text-sm">Per Order</Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

export { SalesCards, TopCustomer, TopYear, AverageOrderValue };
