import React from "react";

import type { <PERSON><PERSON>ef, GridReadyEvent, IDatasource } from "ag-grid-community";
import { fetchClient } from "@/api/web-api";
import { subDays } from "date-fns";
import nProgress from "nprogress";
import {
  InfiniteRowModelModule,
  ModuleRegistry,
  ValidationModule,
  CustomFilterModule,
  CellStyleModule,
  DateFilterModule,
} from "ag-grid-community";
import { SetFilter } from "@/components/ag-grid/filters";
import { AgGridReact, CustomCellRendererProps } from "ag-grid-react";
import { useSalesSelector } from "@/stores/sales/hooks";
import { GripBar } from "@/components/ui/grip-bar";
import { toast } from "sonner";
import {
  ExternalLinkRenderer,
  NumericRenderer,
} from "../ag-grid/cell-renderers";
import { EditIcon } from "lucide-react";

ModuleRegistry.registerModules([
  InfiniteRowModelModule,
  CustomFilterModule,
  DateFilterModule,
  CellStyleModule,
  ...(process.env.NODE_ENV !== "production" ? [ValidationModule] : []),
]);

const WorkOrdersGrid = () => {
  const customers = useSalesSelector((state) => state.context.customers);
  const service_types = useSalesSelector(
    (state) => state.context.service_types,
  );
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);
  const model_types = useSalesSelector((state) => state.context.model_types);
  // Column Definitions: Defines & controls grid columns.
  const [colDefs] = React.useState<ColDef[]>([
    {
      field: "id",
      headerName: "Work Order",
      minWidth: 100,
      width: 115,
      flex: 0,
      cellStyle: { padding: 0, border: 0 },
      cellRenderer: ExternalLinkRenderer,
      cellRendererParams: (params: CustomCellRendererProps) => ({
        value: params.value,
        link: `/work-order-create${params.data?.creator_company_id == 3 ? "-ijack-corp" : ""}/?work_order_id=${params.value}`,
        icon: <EditIcon />,
        variant: "link",
        className:
          "h-full w-full bg-transparent hover:bg-transparent rounded-none border-0",
      }),
    },
    {
      field: "order_total",
      headerName: "Order Total",
      minWidth: 150,
      cellRenderer: NumericRenderer,
      cellRendererParams: {
        variant: "dollars",
      },
    },
    {
      field: "customer",
      headerName: "Customer",
      minWidth: 150,
      filter: SetFilter,
      filterParams: {
        field: "customer",
        dataType: "number",
      },
    },
    {
      field: "service_type",
      headerName: "Service Type",
      minWidth: 150,
      filter: SetFilter,
      filterParams: {
        field: "service_type",
        dataType: "number",
      },
    },
    {
      field: "date_service",
      headerName: "Service Date",
      minWidth: 150,
      filter: "agDateColumnFilter",
    },
    {
      field: "equipment_serviced",
      headerName: "Equipment Serviced",
      minWidth: 200,
      filter: SetFilter,
      filterParams: {
        field: "equipment_serviced",
        dataType: "number",
      },
    },
    { field: "invoice_summary", headerName: "Invoice Summary", minWidth: 200 },
    {
      field: "service_required",
      headerName: "Service Required",
      minWidth: 200,
    },
    { field: "work_done", headerName: "Work Done", minWidth: 200 },
  ]);

  const defaultColDef: ColDef = {
    flex: 1,
    hide: false,
    suppressHeaderFilterButton: true,
  };

  const onGridReady = React.useCallback((params: GridReadyEvent) => {
    const dataSource: IDatasource = {
      rowCount: 0,
      getRows: async ({
        startRow,
        endRow,
        filterModel,
        sortModel,
        successCallback,
      }) => {
        try {
          nProgress.start();
          const data = await fetchClient.POST(
            "/v1/sales-analytics/work-order-grid",
            {
              body: {
                startRow,
                endRow,
                filterModel,
                sortModel,
              },
            },
          );
          nProgress.done();
          successCallback(data.data?.rowData ?? [], data.data?.rowCount ?? 0);
        } catch (error) {
          nProgress.done();
          toast.error("Failed to load work orders grid.");
          console.error({ error });
        }
      },
    };
    params.api.setGridOption("datasource", dataSource);
  }, []);
  const gridRef = React.useRef<AgGridReact>(null);
  React.useEffect(() => {
    // Sync xstate filter state with ag-grid filters
    const customer =
      customers?.length > 0
        ? {
            filterType: "set",
            values: (customers?.length ?? 0 > 0) ? customers : [],
          }
        : null;
    const service_type =
      service_types.length > 0
        ? {
            filterType: "set",
            values: service_types,
          }
        : null;
    const equipment_serviced =
      model_types.length > 0
        ? {
            filterType: "set",
            values: model_types,
          }
        : null;
    const date_service =
      from && to
        ? {
            type: "inRange",
            filterType: "date",
            dateFrom: from ? new Date(from).toISOString() : null,
            dateTo: to ? new Date(to).toISOString() : null,
          }
        : from
          ? {
              type: "greaterThan",
              filterType: "date",
              dateFrom: from ? new Date(subDays(from, 1)).toISOString() : null,
              dateTo: null,
            }
          : to
            ? {
                type: "lessThan",
                filterType: "date",
                dateFrom: to ? new Date(to).toISOString() : null,
                dateTo: null,
              }
            : null;
    if (customers.length >= 0 && !!gridRef.current?.api) {
      gridRef.current?.api.setFilterModel({
        customer,
        service_type,
        equipment_serviced,
        date_service,
      });
    }
  }, [customers, from, to, model_types, service_types]);
  return (
    <div className="flex h-fit max-h-full flex-col">
      <GripBar maxHeight={window.innerHeight - 100} className="border-border">
        {(resizeRef) => (
          <div
            ref={resizeRef}
            className="ag-theme-ijack h-[350px] max-h-full min-h-[350px] w-full overflow-auto select-all"
          >
            <AgGridReact
              columnDefs={colDefs}
              defaultColDef={defaultColDef}
              rowBuffer={20}
              rowModelType={"infinite"}
              enableCellTextSelection
              cacheBlockSize={150}
              cacheOverflowSize={2}
              maxConcurrentDatasourceRequests={3}
              infiniteInitialRowCount={1000}
              onGridReady={onGridReady}
              ref={gridRef}
            />
          </div>
        )}
      </GripBar>
    </div>
  );
};

export { WorkOrdersGrid };
