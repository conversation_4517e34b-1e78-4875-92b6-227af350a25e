import { $api } from "@/api/web-api";

interface SalesSummaryData {
  total_sales: number;
  total_count: number;
  average_sale: number;
  customer_count: number;
  top_customer: string;
  top_customer_sales: number;
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export function SalesSummary() {
  const { data, isLoading, error } = $api.useQuery(
    "get",
    "/v1/work-order/sales-summary",
  );

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="flex h-24 animate-pulse items-center justify-center rounded-lg bg-white p-4 shadow"
          >
            <div className="h-12 w-full rounded bg-gray-200"></div>
          </div>
        ))}
      </div>
    );
  }

  if (error || !data || !data.result) {
    return (
      <div className="rounded-lg bg-white p-4 shadow">
        <div className="p-6 text-center text-sm text-red-500">
          {error
            ? "Error loading sales summary. Please try again later."
            : "No sales data available."}
        </div>
      </div>
    );
  }

  const summary: SalesSummaryData = data.result;

  const cards = [
    {
      title: "Total Sales",
      value: formatCurrency(summary.total_sales),
      icon: "💰",
      color: "bg-blue-50 text-blue-700",
    },
    {
      title: "Total Orders",
      value: summary.total_count.toLocaleString(),
      icon: "📦",
      color: "bg-green-50 text-green-700",
    },
    {
      title: "Average Sale",
      value: formatCurrency(summary.average_sale),
      icon: "📊",
      color: "bg-purple-50 text-purple-700",
    },
    {
      title: "Customer Count",
      value: summary.customer_count.toLocaleString(),
      icon: "👥",
      color: "bg-yellow-50 text-yellow-700",
    },
    {
      title: "Top Customer",
      value: summary.top_customer,
      subValue: formatCurrency(summary.top_customer_sales),
      icon: "🏆",
      color: "bg-red-50 text-red-700",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-3">
      {cards.map((card, index) => (
        <div
          key={index}
          className={`rounded-lg ${card.color} flex flex-col p-4 shadow`}
        >
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">{card.title}</h3>
            <span className="text-2xl">{card.icon}</span>
          </div>
          <div className="mt-2 truncate text-2xl font-bold">{card.value}</div>
          {card.subValue && (
            <div className="text-sm opacity-80">Sales: {card.subValue}</div>
          )}
        </div>
      ))}
    </div>
  );
}
