import { CogIcon, Users, WrenchIcon } from "lucide-react";
import FilterMenu from "../ui/filter-menu";
import { $api } from "@/api/web-api";
import { useSalesSelector, useSalesStore } from "@/stores/sales/hooks";
import { Small } from "@/components/ui/typography";
import { Skeleton } from "../ui/skeleton";
import CalendarFilterMenu from "../ui/calendar-filter-menu";

function CustomerFilter() {
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "get",
    "/v1/customer/",
  );
  const storekeys = useSalesSelector((state) => state.context.customers);
  const store = useSalesStore();
  const setKeys = (keys: number[]) => {
    store.send({ type: "applyFilter", customers: keys });
  };
  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Customers:</Small>
      {isSuccess ? (
        <FilterMenu
          options={data.result}
          keyGetter={(item) => item.id}
          labelGetter={(item) => item.customer}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search customers..."
          allSelectedText="All customers"
          noneSelectedText="All customers"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} customers selected`
          }
          emptyWhenAllSelected={true}
          icon={<Users strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        "Error loading customers"
      ) : null}
    </div>
  );
}
function ServiceTypeFilter() {
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "get",
    "/v1/work-order/service-type",
  );
  const storekeys = useSalesSelector((state) => state.context.service_types);
  const store = useSalesStore();
  const setKeys = (keys: number[]) => {
    store.send({ type: "applyFilter", service_types: keys });
  };
  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Service Types:</Small>
      {isSuccess ? (
        <FilterMenu
          options={data.result}
          keyGetter={(item) => item.id}
          labelGetter={(item) => item.name}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search service types..."
          allSelectedText="All service types"
          noneSelectedText="All service types"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} service types selected`
          }
          emptyWhenAllSelected={true}
          icon={<WrenchIcon strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        "Error loading service types"
      ) : null}
    </div>
  );
}

function ModelTypeFilter() {
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "get",
    "/v1/structure/model",
  );
  const storekeys = useSalesSelector((state) => state.context.model_types);
  const store = useSalesStore();
  const setKeys = (keys: (number | null)[]) => {
    store.send({ type: "applyFilter", model_types: keys });
  };
  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Equipment Serviced:</Small>
      {isSuccess ? (
        <FilterMenu
          options={data.result}
          keyGetter={(item) => item.id}
          labelGetter={(item) => item.model}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search model types..."
          nullable
          allSelectedText="All model types"
          noneSelectedText="All model types"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} model types selected`
          }
          emptyWhenAllSelected={true}
          icon={<CogIcon strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        "Error loading service types"
      ) : null}
    </div>
  );
}

function ServiceDateFilter() {
  const from = useSalesSelector((state) => state.context.service_date_from);
  const to = useSalesSelector((state) => state.context.service_date_to);
  const store = useSalesStore();
  const setServiceDates = (dateRange: {
    from: Date | null;
    to: Date | null;
  }) => {
    store.send({
      type: "applyFilter",
      service_date_from: dateRange.from,
      service_date_to: dateRange.to,
    });
  };
  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Service Dates:</Small>
      <CalendarFilterMenu
        onChange={(range) => setServiceDates(range)}
        range={{ from, to }}
        noneSelectedText="All service dates"
      />
    </div>
  );
}

function ResetFilters() {
  const store = useSalesStore();
  const clearFilters = () => {
    store.send({ type: "clearFilters" });
  };
  return (
    <button
      className="flex items-center gap-2 rounded-md border border-gray-300 bg-white px-2 py-1 text-sm text-gray-700 hover:bg-gray-50"
      onClick={clearFilters}
    >
      Reset Filters
    </button>
  );
}

export {
  CustomerFilter,
  ModelTypeFilter,
  ServiceTypeFilter,
  ServiceDateFilter,
  ResetFilters,
};
