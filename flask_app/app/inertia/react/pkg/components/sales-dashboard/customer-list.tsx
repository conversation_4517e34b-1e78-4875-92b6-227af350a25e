import { Checkbox } from "@/components/ui/checkbox";
import { SidebarMenuItem } from "@/components/ui/sidebar";
import { $api } from "@/api/web-api";
import { Skeleton } from "@/components/ui/skeleton";

function CustomerList() {
  const { isSuccess, isLoading, data } = $api.useQuery("get", "/v1/customer/");
  return (
    <SidebarMenuItem className="flex flex-col gap-1 border p-1">
      {isSuccess ? (
        data.result.map((customer) => (
          <div className="flex items-center space-x-2">
            <Checkbox id={`${customer.id}`} />
            <label
              htmlFor={`${customer.id}`}
              className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {customer.customer}
            </label>
          </div>
        ))
      ) : isLoading ? (
        <div className="flex flex-col gap-2 py-1">
          {Array.from({ length: 10 }, () => (
            <Skeleton.Select />
          ))}
        </div>
      ) : null}
    </SidebarMenuItem>
  );
}

export { CustomerList };
