import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

export function SalesByYearAndProductChart() {
  const { data, isLoading, error } = $api.useQuery(
    "get",
    "/v1/work-order/sales-by-year-and-product",
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales by Product Category</CardTitle>
        </CardHeader>
        <CardContent className="h-[400px]">
          <Skeleton className="h-full w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales by Product Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[400px] items-center justify-center">
            <p className="text-muted-foreground text-sm">
              Error loading data. Please try again.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get unique years and top product categories
  const years = [...new Set(data.result.map((item) => item.year))].sort();

  // Get the top 5 product categories by total sales
  const productCategories = [
    ...new Set(data.result.map((item) => item.part_num_group)),
  ];
  const productCategorySales = productCategories.map((category) => ({
    category,
    totalSales: data.result
      .filter((item) => item.part_num_group === category)
      .reduce((sum, item) => sum + item.total_sales, 0),
  }));

  const topCategories = productCategorySales
    .sort((a, b) => b.totalSales - a.totalSales)
    .slice(0, 5)
    .map((item) => item.category);

  // Organize data for stacked bar chart
  const chartData = years.map((year) => {
    const yearData: Record<string, number | string> = { year };
    topCategories.forEach((category) => {
      const categoryData = data.result.find(
        (item) => item.year === year && item.part_num_group === category,
      );
      yearData[category] = categoryData ? categoryData.total_sales : 0;
    });
    return yearData;
  });

  // Array of colors for the chart
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Sales by Product Category</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 40,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis
                tickFormatter={(value) => formatCurrency(value)}
                width={80}
              />
              <Tooltip
                formatter={(value) => formatCurrency(Number(value))}
                labelFormatter={(value) => `Year: ${value}`}
              />
              <Legend />
              {topCategories.map((category, index) => (
                <Bar
                  key={category}
                  dataKey={category}
                  stackId="a"
                  fill={COLORS[index % COLORS.length]}
                  name={category}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
