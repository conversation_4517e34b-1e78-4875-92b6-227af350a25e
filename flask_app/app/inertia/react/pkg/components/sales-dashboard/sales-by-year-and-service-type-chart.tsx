import { $api } from "@/api/web-api";
import {
  <PERSON>,
  <PERSON><PERSON>,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

// Define the data structure returned by the API
interface SalesByServiceTypeData {
  service_type: string;
  year: number;
  total_sales: number;
}

// Define the structure for chart data
interface ChartDataItem {
  year: number;
  [serviceType: string]: number;
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export function SalesByYearAndServiceTypeChart() {
  // Fetch sales by service type data from the API using the $api client
  // Use the service type by year endpoint which includes year information
  const {
    data: apiData,
    isLoading,
    error,
  } = $api.useQuery("get", "/v1/work-order/sales-by-year-and-service-type");

  // TypeScript type assertion to properly handle the API response
  const data = apiData as unknown as { result: SalesByServiceTypeData[] };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales by Service Type</CardTitle>
        </CardHeader>
        <CardContent className="h-[400px]">
          <Skeleton className="h-full w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error || !data || !data.result || data.result.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales by Service Type</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[400px] items-center justify-center">
            <p className="text-muted-foreground text-sm">
              {error
                ? "Error loading chart data. Please try again later."
                : "No sales data available for charting."}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get unique years to organize data
  const years = [
    ...new Set(
      data.result.map((item) => item.year || new Date().getFullYear()),
    ),
  ].sort();

  // Take only the top 8 service types for better visualization
  const topServiceTypes = [
    ...new Set(data.result.map((item) => item.service_type)),
  ].slice(0, 8);

  // Organize data for stacked bar chart
  const chartData: ChartDataItem[] = years.map((year) => {
    const yearData: ChartDataItem = { year };
    topServiceTypes.forEach((serviceType) => {
      const serviceData = data.result.find(
        (item) =>
          (item.year || new Date().getFullYear()) === year &&
          item.service_type === serviceType,
      );
      yearData[serviceType] = serviceData ? serviceData.total_sales : 0;
    });
    return yearData;
  });

  // Array of colors for the chart
  const COLORS = [
    "#8b5cf6", // violet-500 (original color)
    "#3b82f6", // blue-500
    "#10b981", // emerald-500
    "#f59e0b", // amber-500
    "#ef4444", // red-500
    "#6366f1", // indigo-500
    "#ec4899", // pink-500
    "#64748b", // slate-500
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Sales by Service Type</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="mb-4 text-sm text-gray-500">
          Revenue distribution across different service types by year.
        </p>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 40,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis
                tickFormatter={(value) => formatCurrency(value)}
                width={80}
              />
              <Tooltip
                formatter={(value) => formatCurrency(Number(value))}
                labelFormatter={(value) => `Year: ${value}`}
              />
              <Legend />
              {topServiceTypes.map((serviceType, index) => (
                <Bar
                  key={serviceType}
                  dataKey={serviceType}
                  stackId="a"
                  fill={COLORS[index % COLORS.length]}
                  name={serviceType}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
