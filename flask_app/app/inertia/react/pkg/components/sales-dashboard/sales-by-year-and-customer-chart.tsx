import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { Skeleton } from "@/components/ui/skeleton";

export function SalesByYearAndCustomerChart() {
  const { data, isLoading, error } = $api.useQuery(
    "get",
    "/v1/work-order/sales-by-year-and-customer",
  );

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales by Customer</CardTitle>
        </CardHeader>
        <CardContent className="h-[400px]">
          <Skeleton className="h-full w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Sales by Customer</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[400px] items-center justify-center">
            <p className="text-muted-foreground text-sm">
              Error loading data. Please try again.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get unique years and all customers
  const years = [...new Set(data.result.map((item) => item.year))].sort();

  // Get all customers sorted by total sales
  const customers = [...new Set(data.result.map((item) => item.customer))];
  const customerSales = customers.map((customer) => ({
    customer,
    totalSales: data.result
      .filter((item) => item.customer === customer)
      .reduce((sum, item) => sum + item.total_sales, 0),
  }));

  const sortedCustomers = customerSales
    .sort((a, b) => b.totalSales - a.totalSales)
    .map((item) => item.customer);

  // Organize data for bar chart
  const chartData = years.map((year) => {
    const yearData: Record<string, number | string> = { year };
    sortedCustomers.forEach((customer) => {
      const customerData = data.result.find(
        (item) => item.year === year && item.customer === customer,
      );
      yearData[customer] = customerData ? customerData.total_sales : 0;
    });
    return yearData;
  });

  // Array of colors for the chart - expanded to handle more customers
  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884D8",
    "#82CA9D",
    "#FF6B6B",
    "#6C757D",
    "#A4A4A4",
    "#FFC107",
    "#3F51B5",
    "#FF5722",
    "#607D8B",
    "#9C27B0",
    "#2196F3",
    "#4CAF50",
    "#CDDC39",
    "#795548",
    "#9E9E9E",
    "#F44336",
  ];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Sales by Customer</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 60,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis
                tickFormatter={(value) => formatCurrency(value)}
                width={80}
              />
              <Tooltip
                formatter={(value) => formatCurrency(Number(value))}
                labelFormatter={(value) => `Year: ${value}`}
              />
              {sortedCustomers.map((customer, index) => (
                <Bar
                  key={customer}
                  dataKey={customer}
                  stackId="a"
                  fill={COLORS[index % COLORS.length]}
                  name={customer}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
