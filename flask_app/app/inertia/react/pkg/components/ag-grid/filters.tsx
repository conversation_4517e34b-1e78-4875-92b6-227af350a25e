import { use<PERSON><PERSON>back, useEffect, useState, useMemo } from "react";

import type {
  IAfterGuiAttachedParams,
  IDoesFilterPassParams,
} from "ag-grid-community";
import type { CustomFilterProps } from "ag-grid-react";
import { useGridFilter } from "ag-grid-react";

/**
 * SetFilterModel interface defining the structure of the filter model
 */
interface SetFilterModel {
  filterType: "set";
  values: (string | number)[];
}

/**
 * SetFilterProps extending CustomFilterProps with additional configuration
 */
interface SetFilterProps extends CustomFilterProps<SetFilterModel> {
  /** Field name being filtered */
  field?: string;
  /** Data type of the field values */
  dataType?: "string" | "number" | "auto";
  /** Maximum number of values to show before adding search */
  maxDisplayItems?: number;
  /** Pre-defined values to show in the filter */
  values?: (string | number)[];
  /** Function to fetch values dynamically */
  valuesFunction?: () => Promise<(string | number)[]>;
}

/**
 * Custom Set Filter Component
 *
 * Recreates the AG Grid Enterprise set filter functionality for filtering
 * by multiple selected values. Supports both string and numeric data types.
 */
function SetFilter({
  model,
  onModelChange,
  field = "value",
  dataType = "auto",
  values,
  valuesFunction,
}: SetFilterProps) {
  const [closeFilter, setCloseFilter] = useState<(() => void) | undefined>();
  const [unappliedModel, setUnappliedModel] = useState<SetFilterModel | null>(
    model || null,
  );
  const [searchText, setSearchText] = useState("");
  const [availableValues, setAvailableValues] = useState<(string | number)[]>(
    [],
  );
  const [isLoading, setIsLoading] = useState(true);

  /**
   * Load all unique values from the grid data
   * Extracts unique values from the current grid data for the specified field
   */
  const loadAvailableValues = useCallback(async () => {
    setIsLoading(true);
    try {
      let rawValues: (string | number)[] = [];

      // Priority order: valuesFunction > values prop > default sample data
      if (valuesFunction) {
        // Use provided async function to fetch values
        rawValues = await valuesFunction();
      } else if (values && values.length > 0) {
        // Use provided values array
        rawValues = values;
      } else {
        // Fallback to empty array
        rawValues = [];
      }

      // Process values based on data type
      const processedValues = rawValues.map((value) => {
        if (dataType === "number") {
          // Force conversion to number
          const numValue = Number(value);
          return isNaN(numValue) ? value : numValue;
        } else if (dataType === "string") {
          // Force conversion to string
          return String(value);
        } else {
          // Auto-detect: if it's a valid number, convert it
          const numValue = Number(value);
          if (!isNaN(numValue) && String(numValue) === String(value)) {
            return numValue;
          }
          return value;
        }
      });

      // Remove duplicates and sort
      const uniqueValues = Array.from(new Set(processedValues));
      uniqueValues.sort((a, b) => {
        // Sort numbers before strings, then alphabetically/numerically
        const aIsNum = typeof a === "number";
        const bIsNum = typeof b === "number";

        if (aIsNum && !bIsNum) return -1;
        if (!aIsNum && bIsNum) return 1;

        if (aIsNum && bIsNum) return a - b;
        return String(a).localeCompare(String(b));
      });

      // Add blank option if there might be null values
      const valuesWithBlank = [...uniqueValues, "(Blank)"];

      setAvailableValues(valuesWithBlank);
    } catch (error) {
      console.error("Error loading filter values:", error);
      setAvailableValues([]);
    } finally {
      setIsLoading(false);
    }
  }, [dataType, field, values, valuesFunction]);

  /**
   * Filter logic - checks if the cell value is in the selected values
   */
  const doesFilterPass = useCallback(
    (params: IDoesFilterPassParams) => {
      if (!unappliedModel || !unappliedModel.values.length) {
        return true; // No filter active, show all
      }

      const cellValue = params.data[field];

      // Handle null/undefined values
      if (cellValue == null) {
        return unappliedModel.values.includes("(Blank)");
      }

      // Convert to string for comparison to handle both strings and numbers
      const stringValue = String(cellValue);
      const numericValue = Number(cellValue);

      // Check if value exists in selected values (handle both string and numeric comparison)
      return unappliedModel.values.some((selectedValue) => {
        if (typeof selectedValue === "number" && !isNaN(numericValue)) {
          return selectedValue === numericValue;
        }
        return String(selectedValue) === stringValue;
      });
    },
    [unappliedModel, field],
  );

  /**
   * Called when the filter popup is attached to the DOM
   */
  const afterGuiAttached = useCallback(
    ({ hidePopup }: IAfterGuiAttachedParams) => {
      setCloseFilter(() => hidePopup);
      // Load available values when filter opens
      loadAvailableValues();
    },
    [loadAvailableValues],
  );

  // Register filter handlers with the grid
  useGridFilter({
    doesFilterPass,
    afterGuiAttached,
  });

  /**
   * Sync unapplied model with the actual model when it changes
   */
  useEffect(() => {
    setUnappliedModel(model || null);
  }, [model]);

  /**
   * Filter available values based on search text
   */
  const filteredValues = useMemo(() => {
    if (!searchText) return availableValues;

    return availableValues.filter((value) =>
      String(value).toLowerCase().includes(searchText.toLowerCase()),
    );
  }, [availableValues, searchText]);

  /**
   * Handle individual checkbox change
   */
  const handleValueToggle = useCallback((value: string | number) => {
    setUnappliedModel((prev) => {
      const currentValues = prev?.values || [];
      const isSelected = currentValues.some((v) => String(v) === String(value));

      let newValues: (string | number)[];
      if (isSelected) {
        // Remove value
        newValues = currentValues.filter((v) => String(v) !== String(value));
      } else {
        // Add value
        newValues = [...currentValues, value];
      }

      return {
        filterType: "set",
        values: newValues,
      };
    });
  }, []);

  /**
   * Handle select all / clear all
   */
  const handleSelectAll = useCallback(
    (selectAll: boolean) => {
      setUnappliedModel({
        filterType: "set",
        values: selectAll ? [...filteredValues] : [],
      });
    },
    [filteredValues],
  );

  /**
   * Apply the filter and close popup
   */
  const handleApply = useCallback(() => {
    const modelToApply = unappliedModel?.values.length ? unappliedModel : null;
    onModelChange(modelToApply);
    if (closeFilter) {
      closeFilter();
    }
  }, [unappliedModel, onModelChange, closeFilter]);

  /**
   * Reset filter to show all values
   */
  const handleClear = useCallback(() => {
    setUnappliedModel(null);
    onModelChange(null);
    if (closeFilter) {
      closeFilter();
    }
  }, [onModelChange, closeFilter]);

  /**
   * Check if all filtered values are selected
   */
  const allSelected = useMemo(() => {
    if (!unappliedModel?.values?.length) return false;
    return filteredValues.every((value) =>
      unappliedModel.values.some(
        (selected) => String(selected) === String(value),
      ),
    );
  }, [unappliedModel, filteredValues]);

  /**
   * Check if some (but not all) filtered values are selected
   */
  const someSelected = useMemo(() => {
    if (!unappliedModel?.values.length) return false;
    return (
      filteredValues.some((value) =>
        unappliedModel.values.some(
          (selected) => String(selected) === String(value),
        ),
      ) && !allSelected
    );
  }, [unappliedModel, filteredValues, allSelected]);

  return (
    <div className="set-filter" style={{ width: "250px", padding: "8px" }}>
      {/* Header */}
      <div
        style={{ marginBottom: "8px", fontWeight: "bold", fontSize: "12px" }}
      >
        Select Values
      </div>

      {/* Search Box */}
      {availableValues.length > 10 && (
        <div style={{ marginBottom: "8px" }}>
          <input
            type="text"
            placeholder="Search..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{
              width: "100%",
              padding: "4px 8px",
              border: "1px solid #ccc",
              borderRadius: "3px",
              fontSize: "12px",
            }}
          />
        </div>
      )}

      {/* Select All / Clear All */}
      <div style={{ marginBottom: "8px", fontSize: "12px" }}>
        <label
          style={{ display: "flex", alignItems: "center", cursor: "pointer" }}
        >
          <input
            type="checkbox"
            checked={allSelected}
            ref={(el) => {
              if (el) el.indeterminate = someSelected;
            }}
            onChange={(e) => handleSelectAll(e.target.checked)}
            style={{ marginRight: "6px" }}
          />
          Select All
        </label>
      </div>

      {/* Values List */}
      <div
        style={{
          maxHeight: "200px",
          overflowY: "auto",
          border: "1px solid #ddd",
          borderRadius: "3px",
          padding: "4px",
          marginBottom: "8px",
        }}
      >
        {isLoading ? (
          <div
            style={{
              padding: "8px",
              textAlign: "center",
              fontSize: "12px",
              color: "#666",
            }}
          >
            Loading...
          </div>
        ) : filteredValues.length === 0 ? (
          <div
            style={{
              padding: "8px",
              textAlign: "center",
              fontSize: "12px",
              color: "#666",
            }}
          >
            No values found
          </div>
        ) : (
          filteredValues.map((value) => {
            const isSelected =
              unappliedModel?.values.some((v) => String(v) === String(value)) ||
              false;
            return (
              <label
                key={String(value)}
                style={{
                  display: "flex",
                  alignItems: "center",
                  padding: "2px 4px",
                  cursor: "pointer",
                  fontSize: "12px",
                  borderRadius: "2px",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#f0f0f0";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                }}
              >
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => handleValueToggle(value)}
                  style={{ marginRight: "6px" }}
                />
                <span style={{ overflow: "hidden", textOverflow: "ellipsis" }}>
                  {String(value)}
                </span>
              </label>
            );
          })
        )}
      </div>

      {/* Action Buttons */}
      <div style={{ display: "flex", gap: "4px", justifyContent: "flex-end" }}>
        <button
          onClick={handleClear}
          style={{
            padding: "4px 8px",
            fontSize: "11px",
            border: "1px solid #ccc",
            borderRadius: "3px",
            backgroundColor: "#f8f8f8",
            cursor: "pointer",
          }}
        >
          Clear
        </button>
        <button
          onClick={handleApply}
          style={{
            padding: "4px 8px",
            fontSize: "11px",
            border: "1px solid #007acc",
            borderRadius: "3px",
            backgroundColor: "#007acc",
            color: "white",
            cursor: "pointer",
          }}
        >
          Apply
        </button>
      </div>
    </div>
  );
}

export { SetFilter };
