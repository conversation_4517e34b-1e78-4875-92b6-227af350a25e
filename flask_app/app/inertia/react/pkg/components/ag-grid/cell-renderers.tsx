import type { CustomCellRendererProps } from "ag-grid-react";
import { Label } from "../ui/label";
import { numericFunctionalityVariants } from "../ui/label";
import { Button, buttonVariants } from "../ui/button";
import { VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

function NumericRenderer({
  value,
  variant = "integer",
}: CustomCellRendererProps & {
  variant?: keyof typeof numericFunctionalityVariants;
}) {
  return value !== undefined ? (
    <Label.Numeric value={value} variant={variant} />
  ) : null;
}

function ExternalLinkRenderer({
  value,
  link,
  icon,
  className,
  variant = "default",
}: CustomCellRendererProps & {
  link: string;
  icon?: React.ReactNode;
  className?: string;
} & VariantProps<typeof buttonVariants>) {
  return value !== undefined ? (
    <Button
      variant={variant}
      asChild
      className={cn("h-full w-full", className)}
    >
      <a href={link}>
        {icon && <span className="mr-2">{icon}</span>}
        {value}
      </a>
    </Button>
  ) : null;
}

export { NumericRenderer, ExternalLinkRenderer };
