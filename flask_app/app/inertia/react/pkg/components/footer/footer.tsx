import { usePage } from "@inertiajs/react";
import { Logo } from "../icons/ijack-logo";
import { Separator } from "../ui/separator";
import { H4, Large, Link, Text, Tiny } from "../ui/typography";
import { User } from "@/types/user";
import {
  faFacebookSquare,
  faTwitterSquare,
  faLinkedin,
  faInstagramSquare,
} from "@fortawesome/free-brands-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { RCOM } from "../ui/rcom-button";

function FooterLogo() {
  return (
    <div className="flex flex-col items-center gap-4 sm:items-start">
      <Logo />
      <Large className="mt-0 font-light uppercase">
        Changing the landscape
      </Large>
    </div>
  );
}

function FooterLink({
  href,
  children,
}: {
  href: string;
  children: React.ReactNode;
}) {
  return (
    <Link
      href={href}
      className="text-ijack-green hover:text-ijack-green/80 w-fit pl-1 font-extralight transition-colors"
    >
      {children}
    </Link>
  );
}

function FooterContact() {
  return (
    <div className="flex flex-col gap-0">
      <Text>
        Operations:
        <FooterLink href="tel:+1844MYIJACK">(844) MYIJACK</FooterLink>
      </Text>
      <Text>
        Service:
        <FooterLink href="tel:+3064345858">(*************</FooterLink>
      </Text>
      <Text>
        US Sales:
        <FooterLink href="tel:+14323314227">(*************</FooterLink>
      </Text>
      <Text>
        CA Sales:
        <FooterLink href="tel:+14038084870">(*************</FooterLink>
      </Text>
      <Text>
        Email:
        <FooterLink href="mailto:<EMAIL>"><EMAIL></FooterLink>
      </Text>
    </div>
  );
}

function FooterProducts() {
  return (
    <>
      <H4 className="font-normal uppercase">Products</H4>
      <Separator className="bg-ijack-grey my-1" />
      <FooterLink href="/xfer">XFER</FooterLink>
      <FooterLink href="/vru">VRU</FooterLink>
      <FooterLink href="/egas">EGAS</FooterLink>
      <FooterLink href="/dgas">DGAS</FooterLink>
      <FooterLink href="/boost">BOOST</FooterLink>
      <FooterLink href="/uno">UNO</FooterLink>
      <FooterLink href="/unogas">UNOGAS</FooterLink>
    </>
  );
}

function FooterSite() {
  const page = usePage<{ user: User }>();
  const isLoggedIn = page.props?.user?.id != null;
  return (
    <>
      <H4 className="font-normal uppercase">Site</H4>
      <Separator className="bg-ijack-grey my-1" />
      <FooterLink href="/">HOME</FooterLink>
      <FooterLink href="/media/">MEDIA</FooterLink>
      <FooterLink href="/contact/">CONTACT</FooterLink>
      <FooterLink href="/rcom-info/">RCOM INFO</FooterLink>
      <FooterLink href="/healthcheck/">SITE HEALTH</FooterLink>
      {isLoggedIn ? (
        <FooterLink href="/logout/">LOGOUT</FooterLink>
      ) : (
        <>
          <FooterLink href="/login/">LOGIN</FooterLink>
          <FooterLink href="/register/">REGISTER</FooterLink>
        </>
      )}
    </>
  );
}

function FooterSocials() {
  return (
    <div className="mx-auto flex w-full flex-col gap-0">
      <Tiny className="text-center font-extralight sm:text-left">
        ©{new Date().getFullYear()} IJACK™ Technologies Inc.
      </Tiny>
      <div className="flex flex-row items-center justify-center gap-3 pt-2 sm:justify-start">
        <Link
          href="https://twitter.com/IJACK_TechInc"
          className="text-ijack-grey hover:text-ijack-green cursor-pointer"
        >
          <FontAwesomeIcon size="2x" icon={faTwitterSquare} />
        </Link>
        <Link
          href="https://www.facebook.com/IJackTechnologiesInc/"
          className="text-ijack-grey hover:text-ijack-green cursor-pointer"
        >
          <FontAwesomeIcon size="2x" icon={faFacebookSquare} />
        </Link>
        <Link
          href="https://www.linkedin.com/company/6410477"
          className="text-ijack-grey hover:text-ijack-green cursor-pointer"
        >
          <FontAwesomeIcon size="2x" icon={faLinkedin} />
        </Link>
        <Link
          className="text-ijack-grey hover:text-ijack-green cursor-pointer"
          href="https://www.instagram.com/ijacktechnologies/"
        >
          <FontAwesomeIcon size="2x" icon={faInstagramSquare} />
        </Link>
      </div>
    </div>
  );
}

function Footer() {
  return (
    <footer className="text-ijack-grey bg-ijack-black mx-auto flex w-full flex-col gap-5 border-b border-gray-800 p-8 py-12">
      <div className="mx-auto flex w-full max-w-[1320px] flex-row items-center justify-between gap-8 sm:items-start">
        <div className="mx-auto flex w-auto flex-col items-center gap-4 sm:ml-0 sm:items-start">
          <FooterLogo />
          <FooterContact />
        </div>
        <div className="hidden max-w-1/4 flex-1 flex-col gap-0.5 p-2 sm:flex">
          <FooterProducts />
        </div>
        <div className="hidden max-w-1/4 flex-1 flex-col gap-0.5 p-2 sm:flex">
          <FooterSite />
        </div>
      </div>
      <div className="mx-auto flex w-full max-w-[1320px] flex-col items-center justify-between gap-8 sm:flex-row sm:items-start">
        <FooterSocials />
        <RCOM />
      </div>
    </footer>
  );
}

export { Footer };
