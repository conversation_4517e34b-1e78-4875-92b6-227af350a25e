import {
  useState,
  useRef,
  useEffect,
  useLayoutEffect,
  useCallback,
  useMemo,
} from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTachometerAltFast } from "@fortawesome/free-solid-svg-icons";
import { ChevronDownIcon, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { usePage } from "@inertiajs/react";
import { User } from "@/types/user";
import { FloatingMenu, FloatingMenuItem } from "./ui/floating-menu";
import { NavigationMenu, NavigationMenuList } from "./ui/navigation-menu";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "./ui/tabs";
import { Separator } from "./ui/separator";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "./ui/accordion";

// Role permission functions extracted from dropdown components
const canView = () => true;
const isSales = (roles: string[]) => roles.includes("IJACK Sales");
const isHR = (roles: string[]) => roles.includes("Human Resources Team");
const isBoMAccess = (roles: string[]) => roles.includes("BoM Master Tables");
const isIT = (roles: string[]) =>
  roles.includes("IJACK Software Development (IT)");
const isMachineLearning = (roles: string[]) =>
  roles.includes("IJACK Machine Learning");
const isSalesOrServiceOrHR = (roles: string[]) =>
  roles.some((role) =>
    ["IJACK Sales", "IJACK Service Team", "Human Resources Team"].includes(
      role,
    ),
  );
const isSalesOrITDev = (roles: string[]) =>
  roles.some((role) =>
    ["IJACK Sales", "IJACK Software Development (IT)"].includes(role),
  );
const isSalesOrHROrService = (roles: string[]) =>
  roles.some((role) =>
    ["IJACK Sales", "Human Resources Team", "IJACK Service Team"].includes(
      role,
    ),
  );
const isSalesOrBoMOrService = (roles: string[]) =>
  roles.some((role) =>
    ["IJACK Sales", "BoM Master Tables", "IJACK Service Team"].includes(role),
  );

// Navigation items converted to navItems format
const navItems = [
  {
    name: "Customers",
    navItems: [
      { url: "/admin/users", text: "Users", canView },
      { url: "/admin/users/new", text: "Register User", canView },
      {
        url: "/admin/applications",
        text: "Applications - All Combined",
        canView: isSales,
      },
      {
        url: "/admin/applications_xfer",
        text: "Applications - XFER",
        canView: isSales,
      },
      {
        url: "/admin/applications_vru",
        text: "Applications - VRU",
        canView: isSales,
      },
      {
        url: "/admin/applications_egas",
        text: "Applications - EGAS",
        canView: isSales,
      },
      {
        url: "/admin/applications_dgas",
        text: "Applications - DGAS",
        canView: isSales,
      },
      {
        url: "/admin/career_applications",
        text: "Career Applications",
        canView: isHR,
      },
      { url: "/admin/api_tokens", text: "API Tokens", canView },
      { url: "/admin/roles", text: "Roles", canView: isHR },
      { url: "/admin/users_roles", text: "Users' Roles", canView },
      { url: "/admin/customers", text: "Customers", canView },
      { url: "/admin/cust_sub_groups", text: "Customer Sub-Groups", canView },
      { url: "/admin/countries", text: "Countries", canView },
      { url: "/admin/provinces_states", text: "Provinces/States", canView },
      { url: "/admin/counties", text: "Counties", canView },
      { url: "/admin/cities", text: "Cities", canView },
      {
        url: "/admin/zip_code_sales_tax",
        text: "Sales Tax Rates by Zip Code",
        canView,
      },
      {
        url: "/admin/work_order_sales_tax",
        text: "Sales Tax Rates by Province",
        canView,
      },
    ],
    canView,
  },
  {
    name: "Units",
    navItems: [
      { url: "/admin/structures", text: "Structures", canView },
      {
        url: "/admin/structures_by_model",
        text: "Structures Built, by Model",
        canView,
      },
      { url: "/admin/power_units", text: "Power Units", canView },
      {
        url: "/admin/power_unit_modbus_network",
        text: "Power Unit Modbus Network",
        canView,
      },
      {
        url: "/admin/power_unit_fixed_ip_network",
        text: "Power Unit Fixed IP Interface",
        canView,
      },
      { url: "/admin/gateways", text: "Gateways", canView },
      { url: "/admin/gateway_info", text: "Gateway Info", canView },
      {
        url: "/admin/gateways_not_connected",
        text: "Gateways Not Connected Notes",
        canView,
      },
      {
        url: "/admin/release_notes",
        text: "Gateway Release Notes (Software)",
        canView,
      },
      { url: "/admin/can_bus_cob_map", text: "CAN Bus Cob Map", canView },
      {
        url: "/admin/modbus_holding_registers",
        text: "Modbus and Web API Metrics",
        canView,
      },
      { url: "/admin/alarm_log_metrics", text: "Alarm Log Metrics", canView },
      { url: "/admin/diagnostic_metrics", text: "Diagnostic Metrics", canView },
      { url: "/admin/diagnostic_data", text: "Diagnostic Data", canView },
      { url: "/admin/sim_cards", text: "SIM Cards", canView },
      { url: "/admin/calculator", text: "Calculator", canView },
      { url: "/admin/gateway_types", text: "Gateway Types", canView },
    ],
    canView,
  },
  {
    name: "BoM",
    navItems: [
      {
        url: "/admin/unit_types",
        text: "Unit Types - Very High Level for RCOM/Alerts",
        canView,
      },
      {
        url: "/admin/structure_types",
        text: "Structure Model Types - Finished Goods Mapping",
        canView,
      },
      {
        url: "/admin/power_unit_types",
        text: "Power Unit Model Types - Finished Goods Mapping",
        canView,
      },
      {
        url: "/admin/bom_pricing",
        text: "BoM Pricing - Parts Mapping",
        canView,
      },
      {
        url: "/admin/bom_pricing_parts",
        text: "BoM Pricing - Parts Quantity",
        canView,
      },
      {
        url: "/admin/bom_base_powerunit",
        text: "BoM Base Powerunit - Parts Mapping",
        canView,
      },
      {
        url: "/admin/bom_powerunit",
        text: "BoM Powerunit - Parts Mapping",
        canView,
      },
      {
        url: "/admin/bom_structure",
        text: "BoM Structure - Parts Mapping",
        canView,
      },
      {
        url: "/admin/bom_pump_top",
        text: "BoM Pump Top - Parts Mapping",
        canView,
      },
      { url: "/admin/bom_dgas", text: "BoM DGAS - Parts Mapping", canView },
      {
        url: "/admin/parts",
        text: "BoM Parts - Every Part that Finished Goods Need",
        canView,
      },
      { url: "/admin/filters", text: "Filters", canView },
      { url: "/admin/rod_types", text: "Rod Types", canView },
      { url: "/admin/barrel_types", text: "Barrel Types", canView },
      {
        url: "/admin/shuttle_valve_types",
        text: "Shuttle Valve Types",
        canView,
      },
      { url: "/admin/check_valve_types", text: "Check Valve Types", canView },
      {
        url: "/admin/packing_gland_types",
        text: "Packing Gland Types",
        canView,
      },
      {
        url: "/admin/hydraulic_piston_types",
        text: "Hydraulic Piston Types",
        canView,
      },
    ],
    canView: isBoMAccess,
  },

  {
    name: "Build",
    navItems: [
      {
        url: "/admin/pump_top_options",
        text: "Pump Top Options",
        canView: isBoMAccess,
      },
      {
        url: "/admin/power_unit_options",
        text: "Power Unit Options",
        canView: isBoMAccess,
      },
      {
        url: "/admin/power_unit_speeds",
        text: "Power Unit Speeds",
        canView: isBoMAccess,
      },
      {
        url: "/admin/power_unit_power",
        text: "Power Unit Power",
        canView: isBoMAccess,
      },
      {
        url: "/admin/power_unit_voltage",
        text: "Power Unit Voltage",
        canView: isBoMAccess,
      },
    ],
    canView: isBoMAccess,
  },

  {
    name: "Other",
    navItems: [
      {
        url: "/admin/redis_cli",
        text: "Redis DB Command Line Interface",
        canView,
      },
      {
        url: "/admin/email_marketing",
        text: "Email Marketing",
        canView: (roles: string[]) =>
          !roles.includes("IJACK Software Development (IT)"),
      },
      {
        url: "/admin/meta_data",
        text: "Meta Data",
        canView: (roles: string[]) =>
          !roles.includes("IJACK Software Development (IT)"),
      },
      {
        url: "/admin/hours",
        text: "Hours in the Day",
        canView: (roles: string[]) =>
          !roles.includes("IJACK Software Development (IT)"),
      },
      {
        url: "/admin/error_logs",
        text: "Error Logs",
        canView: (roles: string[]) =>
          !roles.includes("IJACK Software Development (IT)"),
      },
      { url: "/admin/slow_requests", text: "Website Slow Requests", canView },
      {
        url: "/profiler",
        text: "Website Profiler Page",
        canView: (roles: string[]) =>
          !roles.includes("IJACK Software Development (IT)"),
      },
      {
        url: "/simulate_hard/",
        text: "Simulate Long Max-CPU Request",
        canView: (roles: string[]) =>
          !roles.includes("IJACK Software Development (IT)"),
      },
      { url: "/admin/website_views", text: "Website Views", canView },
      {
        url: "/admin/website_most_active_users",
        text: "Website Most Active Users",
        canView,
      },
      {
        url: "/admin/website_most_active_users_filterable",
        text: "Website Most Active Users - Filterable",
        canView,
      },
      { url: "/admin/images", text: "Images", canView },
    ],
    canView: isIT,
  },
  {
    name: "Machine Learning",
    navItems: [
      {
        url: "/admin/compression_images",
        text: "Compression Card Image Classification",
        canView,
      },
      {
        url: "/admin/surface_images",
        text: "Surface Card Image Classification",
        canView,
      },
      {
        url: "/admin/compression_patterns",
        text: "Compression Card Patterns",
        canView,
      },
      {
        url: "/admin/surface_patterns",
        text: "Surface Card Patterns",
        canView,
      },
    ],
    canView: isMachineLearning,
  },
  {
    name: "Alerts",
    navItems: [
      { url: "/admin/alerts", text: "Alerts", canView },
      { url: "/admin/alerts_sent", text: "Alerts Sent", canView },
      { url: "/admin/alerts_custom", text: "Alerts - Custom", canView },
      {
        url: "/admin/hourly_update_report",
        text: "Hourly Update Email",
        canView,
      },
      { url: "/admin/derates_report", text: "Derates Email", canView },
      {
        url: "/admin/op_hours_report",
        text: "Operating Hours Email (100, 1000, etc)",
        canView,
      },
      { url: "/admin/inventory_report", text: "Inventory Email", canView },
      {
        url: "/admin/alerts_sent_users",
        text: "Alerts Sent - Users Alerted",
        canView,
      },
      { url: "/admin/remote_control", text: "Remote Control Actions", canView },
      {
        url: "/admin/alerts_sent_maintenance",
        text: "Alerts Sent - Maintenance",
        canView,
      },
      {
        url: "/admin/alerts_sent_maintenance_users",
        text: "Alerts Sent - Maintenance - Users Alerted",
        canView,
      },
    ],
    canView,
  },
  {
    name: "Reports",
    navItems: [
      {
        url: "/admin/sales_by_person_year",
        text: "Sales by Person by Year",
        canView,
      },
      {
        url: "/admin/sales_by_person_quarter",
        text: "Sales by Person by Quarter",
        canView,
      },
      {
        url: "/admin/sales_by_person_month",
        text: "Sales by Person by Month",
        canView,
      },
      {
        url: "/admin/hours_billed_monthly_efficiency",
        text: "Hours Billed (Whole Team) - Monthly Efficiency",
        canView,
      },
      {
        url: "/admin/hours_billed_by_field_tech_monthly_efficiency",
        text: "Hours Billed by Field Tech - Monthly Efficiency",
        canView,
      },
      {
        url: "/admin/hours_billed_by_field_tech_by_work_order",
        text: "Hours Billed by Field Tech - by Work Order",
        canView,
      },
    ],
    canView: isSalesOrServiceOrHR,
  },
  {
    name: "Service",
    navItems: [
      {
        url: "/admin/work_orders",
        text: "Work Orders (IJACK Inc 🍁)",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/work_orders_corp",
        text: "Work Orders (IJACK Corp - USA)",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/work_order_quotes",
        text: "Work Order Quotes (IJACK Inc 🍁)",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/work_order_quotes_corp",
        text: "Work Order Quotes (IJACK Corp - USA)",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/work-order-create",
        text: "Create Work Order (IJACK Inc 🍁)",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/work-order-create-ijack-corp",
        text: "Create Work Order (IJACK Corp - USA)",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/work_orders_by_unit",
        text: "Work Orders by Unit",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/work_order_parts",
        text: "Work Order Parts (Line Items)",
        canView: isSalesOrHROrService,
      },
      { url: "/admin/contact_form", text: "Contact Form Submissions", canView },
      {
        url: "/admin/service_requests",
        text: "Service Requests",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/service_request_email_recipients",
        text: "Service Request Email Recipients",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/maintenance",
        text: "Maintenance Records",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/maintenance_types",
        text: "Maintenance Types",
        canView: isSalesOrHROrService,
      },
      {
        url: "/admin/warehouses",
        text: "Warehouses",
        canView: isSalesOrBoMOrService,
      },
      {
        url: "/admin/warehouse_parts",
        text: "Warehouse Parts (Inventory)",
        canView: isSalesOrBoMOrService,
      },
      {
        url: "/admin/work_order_service_type",
        text: "Work Order Service Type",
        canView: isSalesOrBoMOrService,
      },
      {
        url: "/admin/work_order_status",
        text: "Work Order Status",
        canView: isSalesOrBoMOrService,
      },
      { url: "/admin/currencies", text: "Currencies", canView },
    ],
    canView: isSalesOrHROrService,
  },
  {
    name: "Dashboard",
    navItems: [
      { url: "/dashboards/sales", text: "Sales", canView: isSalesOrITDev },
      {
        url: "/dashboards/services/overview",
        text: "Service",
        canView: isSalesOrITDev,
      },
    ],
    canView: isSalesOrITDev,
  },
];

export function SiteHeader() {
  const [isOpen, setIsOpen] = useState(false);
  const [visibleItems, setVisibleItems] = useState<typeof navItems>([]);
  const [overflowItems, setOverflowItems] = useState<typeof navItems>([]);
  const [isMoreDropdownOpen, setIsMoreDropdownOpen] = useState(false);
  const [openNavMenuIndex, setOpenNavMenuIndex] = useState<number | null>(null);

  const { props } = usePage();
  const user = props?.user as User;

  const navRef = useRef<HTMLDivElement>(null);
  const rightSectionRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLAnchorElement>(null);
  const itemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const moreButtonRef = useRef<HTMLButtonElement>(null);
  const measurementRef = useRef<HTMLDivElement>(null);

  // Filter navItems based on user roles - memoize to prevent infinite loops
  const filteredNavItems = useMemo(
    () =>
      navItems.filter((item) =>
        user?.roles ? item.canView(user.roles) : false,
      ),
    [user?.roles],
  );

  const calculateVisibleItems = useCallback(() => {
    if (
      !navRef.current ||
      !rightSectionRef.current ||
      !logoRef.current ||
      !measurementRef.current
    ) {
      return false;
    }

    // Get container dimensions
    const containerWidth = navRef.current.offsetWidth;
    const rightSectionWidth = rightSectionRef.current.offsetWidth;
    const logoWidth = logoRef.current.offsetWidth;
    const padding = 32; // Left and right padding
    const spacing = 4; // Space between items

    // Calculate available width for navigation items
    const availableWidth =
      containerWidth - logoWidth - rightSectionWidth - padding;

    // Get actual widths of each item from measurement elements
    const itemWidths: number[] = [];
    let allItemsMeasured = true;

    for (let i = 0; i < filteredNavItems.length; i++) {
      const ref = itemRefs.current[i];
      if (ref && ref.offsetWidth > 0) {
        itemWidths[i] = ref.offsetWidth + spacing; // Add spacing between items
      } else {
        allItemsMeasured = false;
        break;
      }
    }

    // If not all items are measured yet, return false to retry
    if (!allItemsMeasured) {
      return false;
    }

    // Get "More" button width (with fallback)
    const moreButtonWidth = moreButtonRef.current?.offsetWidth || 80;

    // Calculate which items can fit
    const visibleIndices: number[] = [];
    let usedWidth = 0;

    for (let i = 0; i < filteredNavItems.length; i++) {
      const remainingItems = filteredNavItems.length - i - 1;
      const needsMoreButton = remainingItems > 0;

      // If we need the "More" button, reserve space for it
      const requiredWidth =
        usedWidth + itemWidths[i] + (needsMoreButton ? moreButtonWidth : 0);

      if (requiredWidth <= availableWidth) {
        visibleIndices.push(i);
        usedWidth += itemWidths[i];
      } else {
        break;
      }
    }

    // Ensure at least one item is visible (fallback)
    if (visibleIndices.length === 0 && filteredNavItems.length > 0) {
      visibleIndices.push(0);
    }

    // Update state with visible and overflow items
    const visible = visibleIndices.map((i) => filteredNavItems[i]);
    const overflow = filteredNavItems.filter(
      (_, i) => !visibleIndices.includes(i),
    );

    setVisibleItems(visible);
    setOverflowItems(overflow);
    return true;
  }, [filteredNavItems]);

  // Initial calculation using useLayoutEffect to prevent flicker
  useLayoutEffect(() => {
    const performCalculation = () => {
      const success = calculateVisibleItems();
      if (!success) {
        // If measurement failed, retry on next frame
        requestAnimationFrame(performCalculation);
      }
    };

    // Small delay to ensure DOM is ready, but still before paint
    const timer = setTimeout(performCalculation, 0);
    return () => clearTimeout(timer);
  }, [calculateVisibleItems]);

  // Recalculate on window resize using regular useEffect with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleResize = () => {
      // Clear the previous timeout
      clearTimeout(timeoutId);

      // Set a new timeout to debounce the resize events
      timeoutId = setTimeout(() => {
        calculateVisibleItems();
      }, 100);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
      clearTimeout(timeoutId);
    };
  }, [calculateVisibleItems]);

  const renderNavItem = (
    item: {
      name: string;
      navItems: {
        url: string;
        text: string;
        canView: (roles: string[]) => boolean;
      }[];
      canView: (roles: string[]) => boolean;
    },
    index: number,
    isForMeasurement = false,
  ) => {
    // Filter the navItems based on user roles
    const filteredSubItems = item.navItems.filter((subItem) =>
      user?.roles ? subItem.canView(user.roles) : false,
    );

    return (
      <div
        key={`nav-item-${index}-${isForMeasurement ? "measure" : "display"}`}
        ref={
          isForMeasurement
            ? (el) => {
                itemRefs.current[index] = el;
              }
            : undefined
        }
        className="flex-shrink-0"
      >
        <FloatingMenu
          isOpen={!isForMeasurement && openNavMenuIndex === index}
          onOpenChange={(open) => {
            if (!isForMeasurement) {
              setOpenNavMenuIndex(open ? index : null);
            }
          }}
          placement="bottom-start"
          offsetDistance={10}
          showArrow={true}
          trigger={
            <Button variant="menu-link">
              {item.name}
              <ChevronDownIcon className="h-4 w-4" />
            </Button>
          }
        >
          <div className="flex w-auto flex-col py-1">
            {filteredSubItems.map((subItem) => (
              <FloatingMenuItem key={subItem.url} asChild>
                <a href={subItem.url} onClick={() => setOpenNavMenuIndex(null)}>
                  {subItem.text}
                </a>
              </FloatingMenuItem>
            ))}
          </div>
        </FloatingMenu>
      </div>
    );
  };
  return (
    <header className="w-full border-b border-gray-800 bg-[#111111]">
      <div className="w-full px-4">
        <div className="flex h-16 w-full items-center gap-4" ref={navRef}>
          {/* Logo */}
          <a href="/" className="flex-shrink-0" ref={logoRef}>
            <img
              src="/static/img/icons/ijack-logo-189x86.png"
              alt="IJACK Technologies Inc."
              className="object-fit mt-1 h-10 w-[87px] min-w-[87px]"
            />
          </a>

          {/* Desktop navigation */}
          <nav className="hidden flex-1 items-center space-x-0 md:flex">
            <NavigationMenu viewport={false} className="z-50">
              <NavigationMenuList>
                {user?.roles?.includes("IJACK Admin") && (
                  <>
                    {visibleItems.map((item, index) =>
                      renderNavItem(item, index, false),
                    )}

                    {/* More Dropdown for Overflow Items - Using FloatingMenu Component */}
                    {overflowItems.length > 0 && (
                      <FloatingMenu
                        isOpen={isMoreDropdownOpen}
                        onOpenChange={setIsMoreDropdownOpen}
                        placement="bottom-start"
                        offsetDistance={10}
                        showArrow={true}
                        className="w-auto"
                        trigger={
                          <Button variant="menu-link">
                            <MoreHorizontal className="h-4 w-4" />
                            More
                            <ChevronDownIcon className="h-4 w-4" />
                          </Button>
                        }
                      >
                        <Tabs defaultValue={overflowItems[0]?.name}>
                          <TabsList>
                            {overflowItems.map((item) => (
                              <TabsTrigger
                                key={item.name}
                                value={item.name}
                                className="px-4 text-sm"
                              >
                                {item.name}
                              </TabsTrigger>
                            ))}
                          </TabsList>
                          <Separator className="bg-gray-200" />
                          {overflowItems.map((item) => {
                            const filteredSubItems = item.navItems.filter(
                              (subItem) =>
                                user?.roles
                                  ? subItem.canView(user.roles)
                                  : false,
                            );
                            return (
                              <TabsContent
                                key={item.name}
                                value={item.name}
                                className="mt-0 space-y-1"
                              >
                                {filteredSubItems.map((subItem) => (
                                  <a
                                    key={subItem.url}
                                    href={subItem.url}
                                    className="block rounded-md px-3 py-2 text-sm text-nowrap text-gray-700 transition-colors hover:bg-gray-100 hover:text-gray-900"
                                    onClick={() => setIsMoreDropdownOpen(false)}
                                  >
                                    {subItem.text}
                                  </a>
                                ))}
                              </TabsContent>
                            );
                          })}
                        </Tabs>
                      </FloatingMenu>
                    )}
                  </>
                )}
              </NavigationMenuList>
            </NavigationMenu>
          </nav>

          {/* Right side navigation */}
          <div
            className="ml-auto flex flex-shrink-0 items-center pr-4"
            ref={rightSectionRef}
          >
            {/* Desktop Admin/Logout links */}
            <div className="mr-4 hidden items-center md:flex">
              <Button variant="menu-link" asChild>
                <a href="/logout">Logout</a>
              </Button>
            </div>

            {/* RCOM button - always visible */}
            <Button variant="menu-primary" asChild>
              <a href="/rcom/">
                <FontAwesomeIcon icon={faTachometerAltFast} />
                RCOM
              </a>
            </Button>

            {/* Mobile menu button */}
            <div className="ml-4 flex md:hidden">
              <button
                type="button"
                className="text-gray-300 hover:text-white"
                onClick={() => setIsOpen(!isOpen)}
              >
                <span className="sr-only">Open main menu</span>
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  {isOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </button>
            </div>
          </div>

          {/* Hidden measurement elements - positioned off-screen but still in DOM */}
          <div
            ref={measurementRef}
            className="pointer-events-none absolute -top-96 left-0 whitespace-nowrap opacity-0"
            aria-hidden="true"
          >
            <div className="flex items-center space-x-0">
              <NavigationMenu viewport={false} className="z-50">
                <NavigationMenuList>
                  {user?.roles?.includes("IJACK Admin") &&
                    filteredNavItems.map((item, index) =>
                      renderNavItem(item, index, true),
                    )}
                  {/* Also measure the More button */}
                </NavigationMenuList>
              </NavigationMenu>
              <Button ref={moreButtonRef} variant="menu-link">
                <MoreHorizontal className="h-4 w-4 fill-white text-white" />
                <span className="ml-1">More</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile menu, show/hide based on menu state */}
        <div
          className={`${isOpen ? "flex" : "hidden"} w-full justify-self-end py-2 md:hidden`}
        >
          <div className="w-full space-y-1 pt-2 pb-3">
            {/* Navigation items for mobile using accordion */}
            {user?.roles?.includes("IJACK Admin") &&
              filteredNavItems.length > 0 && (
                <Accordion type="multiple" className="w-full">
                  {filteredNavItems.map((item) => {
                    const filteredSubItems = item.navItems.filter((subItem) =>
                      user?.roles ? subItem.canView(user.roles) : false,
                    );

                    // Only render accordion item if there are visible sub-items
                    if (filteredSubItems.length === 0) return null;

                    return (
                      <AccordionItem
                        key={item.name}
                        value={item.name}
                        className="border-gray-700"
                      >
                        <AccordionTrigger className="px-3 py-2 text-sm font-medium tracking-wider text-gray-400 uppercase hover:text-gray-300 hover:no-underline">
                          {item.name}
                        </AccordionTrigger>
                        <AccordionContent className="pb-2">
                          <div className="space-y-1">
                            {filteredSubItems.map((subItem) => (
                              <a
                                key={subItem.url}
                                href={subItem.url}
                                className="mx-3 block rounded-md px-6 py-2 text-sm text-nowrap text-gray-300 hover:bg-gray-700 hover:text-white"
                                onClick={() => setIsOpen(false)} // Close menu when link is clicked
                              >
                                {subItem.text}
                              </a>
                            ))}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    );
                  })}
                </Accordion>
              )}

            {/* Static menu items */}
            <div className="mt-4 border-t border-gray-700 pt-4">
              <a
                href="/logout"
                className="block rounded-md px-3 py-2 text-gray-300 hover:bg-gray-700 hover:text-white"
                onClick={() => setIsOpen(false)}
              >
                Logout
              </a>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
