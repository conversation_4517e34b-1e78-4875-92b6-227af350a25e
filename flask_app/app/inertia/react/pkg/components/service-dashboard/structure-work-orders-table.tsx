import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import {
  useServiceCostSelector,
  useServiceCostStore,
} from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { AgGridReact } from "ag-grid-react";
import { ColDef, ValueFormatterParams } from "ag-grid-community";
import { themeQuartz } from "ag-grid-community";
import { Button } from "../ui/button";
import { X } from "lucide-react";

const formatCurrency = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === "") return "";
  return new Intl.NumberFormat("en-CA", {
    style: "currency",
    currency: "CAD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(Number(params.value));
};

const formatDate = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === "") return "";
  return new Date(params.value).toLocaleDateString("en-CA");
};

const formatNumber = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === "") return "";
  return Number(params.value).toFixed(1);
};

const getStructureTitle = (structureData: any, structureLoading: boolean, selectedStructureId: number | null) => {
  if (structureLoading) {
    return "Structure Work Orders (Loading...)";
  }
  
  if (!structureData?.result) {
    return `Structure Work Orders (ID: ${selectedStructureId})`;
  }
  
  const structure = structureData.result;
  let title = `Structure Work Orders (${structure.structure_str || selectedStructureId})`;
  
  if (structure.power_unit_str) {
    title += ` - ${structure.power_unit_str}`;
  }
  
  return title;
};

interface StructureWorkOrdersTableProps {
  structureId?: number; // Optional prop to override store value
}

const StructureWorkOrdersTable = ({ structureId: propStructureId }: StructureWorkOrdersTableProps = {}) => {
  const storeStructureId = useServiceCostSelector(
    (state) => state.context.selectedStructureId,
  );
  // Use prop if provided, otherwise use store value
  const selectedStructureId = propStructureId ?? storeStructureId;
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector(
    (state) => state.context.service_types,
  );
  const from = useServiceCostSelector(
    (state) => state.context.service_date_from,
  );
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );
  const store = useServiceCostStore();

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/structure-work-orders",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        structure_id: selectedStructureId,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
    {
      enabled: selectedStructureId !== null,
    },
  );

  // Fetch structure details for the title
  const { data: structureData, isLoading: structureLoading } = $api.useQuery(
    "get",
    "/v1/structure/{structure_id}",
    {
      params: {
        path: { structure_id: selectedStructureId?.toString() || "" },
      },
    },
    {
      enabled: selectedStructureId !== null,
    },
  );

  const columnDefs: ColDef[] = [
    {
      field: "work_order_id",
      headerName: "Work Order",
      headerTooltip: "Unique work order identifier. Click to view detailed parts breakdown and costs.",
      pinned: "left",
      width: 130,
      cellStyle: { fontWeight: "bold" },
      cellRenderer: (params: any) => {
        return (
          <div
            className="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
            onClick={() =>
              store.send({ type: "selectWorkOrder", workOrderId: params.value })
            }
          >
            WO-{params.value}
          </div>
        );
      },
    },
    {
      field: "date_service",
      headerName: "Service Date",
      headerTooltip: "Date when the service work was performed. Most recent services appear first.",
      valueFormatter: formatDate,
      width: 120,
      sort: "desc",
    },
    {
      field: "service_type",
      headerName: "Service Type",
      headerTooltip: "Category of service performed (e.g., Preventive Maintenance, Emergency Repair, Installation)",
      width: 150,
    },
    {
      field: "technician_name",
      headerName: "Technicians",
      headerTooltip: "Names of technicians assigned to this work order. Multiple technicians may work on the same job.",
      width: 180,
      wrapText: true,
      autoHeight: true,
      cellRenderer: (params: any) => {
        const technicians = params.value;
        if (technicians === 'Unassigned' || !technicians) {
          return <span className="text-gray-500 italic">Unassigned</span>;
        }
        return <span className="text-sm">{technicians}</span>;
      }
    },
    {
      field: "labor_hours",
      headerName: "Labor Hours",
      headerTooltip: "Total hours of labor charged for this service (from 070-series parts)",
      valueFormatter: formatNumber,
      width: 100,
      type: "numericColumn",
    },
    {
      field: "labor_cost",
      headerName: "Labor Cost",
      headerTooltip: "Cost of labor for this service visit (converted to CAD). Based on time and technician rates.",
      valueFormatter: formatCurrency,
      width: 120,
      type: "numericColumn",
    },
    {
      field: "parts_cost",
      headerName: "Parts Cost",
      headerTooltip: "Cost of replacement parts and materials used (converted to CAD). Does not include labor.",
      valueFormatter: formatCurrency,
      width: 120,
      type: "numericColumn",
    },
    {
      field: "total_cost",
      headerName: "Total Cost",
      headerTooltip: "Total service cost including both labor and parts (converted to CAD)",
      valueFormatter: formatCurrency,
      width: 120,
      type: "numericColumn",
      cellStyle: { fontWeight: "bold" },
    },
    {
      field: "status",
      headerName: "Status",
      headerTooltip: "Current work order status. Completed work orders are included in cost calculations.",
      width: 100,
      cellRenderer: (params: any) => {
        const status = params.value?.toLowerCase();
        const statusClass =
          status === "completed"
            ? "text-green-600"
            : status === "in progress"
              ? "text-blue-600"
              : "text-gray-600";
        return <span className={statusClass}>{params.value}</span>;
      },
    },
  ];

  const defaultColDef = {
    sortable: true,
    filter: true,
    resizable: true,
  };

  if (selectedStructureId === null) {
    return null; // Don't render if no structure is selected
  }

  return (
    <Card className="col-span-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">
            {getStructureTitle(structureData, structureLoading, selectedStructureId)}
          </CardTitle>
          {/* Only show close button if using store value (not prop) */}
          {!propStructureId && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => store.send({ type: "clearStructureSelection" })}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : isError ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">Error loading work orders</p>
          </div>
        ) : !data?.result || data.result.length === 0 ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">
              No work orders found for this structure
            </p>
          </div>
        ) : (
          <div className="h-[400px] w-full">
            <AgGridReact
              theme={themeQuartz}
              rowModelType="clientSide"
              rowData={data.result}
              columnDefs={columnDefs}
              defaultColDef={defaultColDef}
              pagination={true}
              paginationPageSize={10}
              paginationPageSizeSelector={[10, 20, 50]}
              domLayout="normal"
              suppressMenuHide={true}
              enableCellTextSelection={true}
              animateRows={true}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { StructureWorkOrdersTable };
