import React, { useState } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  AlertTriangleIcon,
  ShieldIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  TargetIcon,
  ClockIcon,
  BarChart3Icon,
  EyeIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from 'lucide-react';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Scatter<PERSON>hart, Scatter } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";

const CompositeRiskAnalysis: React.FC = () => {
  const [selectedRiskType, setSelectedRiskType] = useState<'all' | 'cost' | 'failure' | 'operational' | 'financial'>('all');
  const [viewMode, setViewMode] = useState<'overview' | 'units' | 'trends'>('overview');

  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  // Fetch composite risk scores
  const { data: riskData, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/composite-risk-scores",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    }
  );

  const getRiskColor = (score: number | string) => {
    const numScore = typeof score === 'string' ? 
      score === 'critical' ? 90 :
      score === 'high' ? 70 :
      score === 'medium' ? 50 : 30 : score;
    
    if (numScore >= 80) return 'text-red-600 bg-red-50 border-red-200';
    if (numScore >= 60) return 'text-orange-600 bg-orange-50 border-orange-200';
    if (numScore >= 40) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-green-600 bg-green-50 border-green-200';
  };

  const getRiskIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'critical': return <XCircleIcon className="h-4 w-4 text-red-600" />;
      case 'high': return <AlertTriangleIcon className="h-4 w-4 text-orange-600" />;
      case 'medium': return <AlertCircleIcon className="h-4 w-4 text-yellow-600" />;
      case 'low': return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      default: return <TargetIcon className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend.toLowerCase()) {
      case 'increasing': return <TrendingUpIcon className="h-4 w-4 text-red-600" />;
      case 'decreasing': return <TrendingDownIcon className="h-4 w-4 text-green-600" />;
      default: return <BarChart3Icon className="h-4 w-4 text-gray-600" />;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Skeleton className="h-80 w-full" />
          <Skeleton className="h-80 w-full" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">Error loading risk analysis</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const risks = riskData?.result || [];

  if (risks.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500 space-y-2">
          <ShieldIcon className="h-12 w-12 mx-auto text-green-600" />
          <div className="text-lg font-medium">No Risk Data Available</div>
          <div className="text-sm">All units appear to be operating within normal parameters</div>
        </div>
      </Card>
    );
  }

  // Filter risks based on selected type
  const filteredRisks = selectedRiskType === 'all' ? risks : risks.filter(risk => {
    switch (selectedRiskType) {
      case 'cost': return risk.cost_risk_score >= 60;
      case 'failure': return risk.failure_risk_score >= 60;
      case 'operational': return risk.operational_risk_score >= 60;
      case 'financial': return risk.financial_risk_score >= 60;
      default: return true;
    }
  });

  // Calculate summary statistics
  const riskDistribution = [
    { name: 'Low Risk', value: risks.filter(r => r.risk_category === 'low').length, color: '#10B981' },
    { name: 'Medium Risk', value: risks.filter(r => r.risk_category === 'medium').length, color: '#F59E0B' },
    { name: 'High Risk', value: risks.filter(r => r.risk_category === 'high').length, color: '#EF4444' },
    { name: 'Critical Risk', value: risks.filter(r => r.risk_category === 'critical').length, color: '#DC2626' }
  ];

  const averageRiskScore = risks.reduce((sum, risk) => sum + risk.composite_risk_score, 0) / risks.length;
  const criticalUnits = risks.filter(r => r.risk_category === 'critical').length;
  const highRiskUnits = risks.filter(r => r.risk_category === 'high' || r.risk_category === 'critical').length;

  // Prepare scatter plot data for risk components
  const scatterData = risks.map(risk => ({
    x: risk.cost_risk_score,
    y: risk.failure_risk_score,
    z: risk.composite_risk_score,
    name: risk.structure_number || `Unit ${risk.structure_id}`,
    category: risk.risk_category
  }));

  // Risk score distribution data
  const scoreRanges = [
    { range: '0-20', count: risks.filter(r => r.composite_risk_score < 20).length },
    { range: '20-40', count: risks.filter(r => r.composite_risk_score >= 20 && r.composite_risk_score < 40).length },
    { range: '40-60', count: risks.filter(r => r.composite_risk_score >= 40 && r.composite_risk_score < 60).length },
    { range: '60-80', count: risks.filter(r => r.composite_risk_score >= 60 && r.composite_risk_score < 80).length },
    { range: '80-100', count: risks.filter(r => r.composite_risk_score >= 80).length }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <ShieldIcon className="h-6 w-6 text-purple-600" />
            Composite Risk Analysis
          </h2>
          <p className="text-gray-600">
            Unified risk assessment combining multiple predictive factors
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={viewMode === 'overview' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('overview')}
          >
            Overview
          </Button>
          <Button
            variant={viewMode === 'units' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('units')}
          >
            Units
          </Button>
          <Button
            variant={viewMode === 'trends' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('trends')}
          >
            Trends
          </Button>
        </div>
      </div>

      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Total Units Analyzed</div>
              <div className="text-2xl font-bold text-blue-600">{risks.length}</div>
              <div className="text-xs text-gray-500">Risk assessed</div>
            </div>
            <TargetIcon className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Average Risk Score</div>
              <div className={`text-2xl font-bold ${getRiskColor(averageRiskScore).split(' ')[0]}`}>
                {averageRiskScore.toFixed(0)}
              </div>
              <div className="text-xs text-gray-500">Out of 100</div>
            </div>
            <BarChart3Icon className="h-8 w-8 text-purple-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Critical Risk Units</div>
              <div className="text-2xl font-bold text-red-600">{criticalUnits}</div>
              <div className="text-xs text-gray-500">
                {((criticalUnits / risks.length) * 100).toFixed(1)}% of total
              </div>
            </div>
            <XCircleIcon className="h-8 w-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">High+ Risk Units</div>
              <div className="text-2xl font-bold text-orange-600">{highRiskUnits}</div>
              <div className="text-xs text-gray-500">
                {((highRiskUnits / risks.length) * 100).toFixed(1)}% of total
              </div>
            </div>
            <AlertTriangleIcon className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Risk Type Filter */}
      <div className="flex gap-2 flex-wrap">
        <span className="text-sm font-medium self-center">Filter by risk type:</span>
        <Button
          variant={selectedRiskType === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedRiskType('all')}
        >
          All Risk ({risks.length})
        </Button>
        <Button
          variant={selectedRiskType === 'cost' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedRiskType('cost')}
        >
          Cost Risk ({risks.filter(r => r.cost_risk_score >= 60).length})
        </Button>
        <Button
          variant={selectedRiskType === 'failure' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedRiskType('failure')}
        >
          Failure Risk ({risks.filter(r => r.failure_risk_score >= 60).length})
        </Button>
        <Button
          variant={selectedRiskType === 'operational' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedRiskType('operational')}
        >
          Operational ({risks.filter(r => r.operational_risk_score >= 60).length})
        </Button>
        <Button
          variant={selectedRiskType === 'financial' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSelectedRiskType('financial')}
        >
          Financial ({risks.filter(r => r.financial_risk_score >= 60).length})
        </Button>
      </div>

      {viewMode === 'overview' && (
        <>
          {/* Risk Distribution & Score Histogram */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Risk Distribution Pie Chart */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Risk Category Distribution</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={riskDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                    >
                      {riskDistribution.map((entry, index) => (
                        <Cell key={index} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="flex justify-center gap-4 mt-4">
                {riskDistribution.map((entry, index) => (
                  <div key={index} className="flex items-center gap-1">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: entry.color }}
                    />
                    <span className="text-xs">{entry.name}: {entry.value}</span>
                  </div>
                ))}
              </div>
            </Card>

            {/* Score Distribution Histogram */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Risk Score Distribution</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={scoreRanges}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="range" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value) => [value, 'Units']}
                      labelFormatter={(label) => `Risk Score: ${label}`}
                    />
                    <Bar 
                      dataKey="count" 
                      fill="#8B5CF6" 
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </div>

          {/* Risk Component Analysis */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Cost vs Failure Risk Correlation</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart data={scatterData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    type="number" 
                    dataKey="x" 
                    name="Cost Risk Score" 
                    domain={[0, 100]}
                  />
                  <YAxis 
                    type="number" 
                    dataKey="y" 
                    name="Failure Risk Score"
                    domain={[0, 100]}
                  />
                  <Tooltip 
                    formatter={(value, name) => [`${Number(value).toFixed(0)}`, name]}
                    labelFormatter={(_, payload) => {
                      const point = payload?.[0]?.payload;
                      return point ? `${point.name} (Overall: ${point.z.toFixed(0)})` : 'Unit';
                    }}
                  />
                  <Scatter dataKey="y" fill="#8884d8" />
                </ScatterChart>
              </ResponsiveContainer>
            </div>
            <div className="text-sm text-gray-600 mt-2">
              Units in the top-right quadrant (high cost + high failure risk) require immediate attention.
            </div>
          </Card>
        </>
      )}

      {viewMode === 'units' && (
        <>
          {/* Critical & High Risk Units */}
          <div className="space-y-4">
            {filteredRisks
              .filter(risk => risk.risk_category === 'critical' || risk.risk_category === 'high')
              .sort((a, b) => b.composite_risk_score - a.composite_risk_score)
              .slice(0, 10)
              .map((risk, index) => (
              <Card key={index} className="p-4">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getRiskIcon(risk.risk_category)}
                        <h4 className="font-semibold text-lg">
                          {risk.structure_number || `Unit ${risk.structure_id}`}
                        </h4>
                        <Badge className={getRiskColor(risk.composite_risk_score)}>
                          Score: {risk.composite_risk_score.toFixed(0)}
                        </Badge>
                        <Badge className={getRiskColor(risk.risk_category)}>
                          {risk.risk_category}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600 mb-3">
                        {risk.customer_name} • {risk.model_name}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-1 text-sm text-gray-500">
                        {getTrendIcon(risk.risk_trend)}
                        <span>Trend: {risk.risk_trend}</span>
                      </div>
                    </div>
                  </div>

                  {/* Risk Component Scores */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Cost Risk</div>
                      <div className={`text-xl font-bold ${getRiskColor(risk.cost_risk_score).split(' ')[0]}`}>
                        {risk.cost_risk_score.toFixed(0)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Failure Risk</div>
                      <div className={`text-xl font-bold ${getRiskColor(risk.failure_risk_score).split(' ')[0]}`}>
                        {risk.failure_risk_score.toFixed(0)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Operational Risk</div>
                      <div className={`text-xl font-bold ${getRiskColor(risk.operational_risk_score).split(' ')[0]}`}>
                        {risk.operational_risk_score.toFixed(0)}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Financial Risk</div>
                      <div className={`text-xl font-bold ${getRiskColor(risk.financial_risk_score).split(' ')[0]}`}>
                        {risk.financial_risk_score.toFixed(0)}
                      </div>
                    </div>
                  </div>

                  {/* Risk Factors */}
                  {risk.primary_risk_factors.length > 0 && (
                    <div>
                      <div className="text-sm font-medium mb-2">Primary Risk Factors:</div>
                      <div className="flex flex-wrap gap-1">
                        {risk.primary_risk_factors.map((factor, factorIndex) => (
                          <Badge key={factorIndex} variant="destructive" className="text-xs">
                            {factor}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Immediate Actions */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium mb-2">Immediate Actions:</div>
                      <ul className="text-sm list-disc list-inside space-y-1">
                        {risk.immediate_actions.slice(0, 3).map((action, actionIndex) => (
                          <li key={actionIndex}>{action}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <div className="text-sm font-medium mb-2">Monitoring:</div>
                      <div className="space-y-1">
                        {risk.monitoring_metrics.slice(0, 3).map((metric, metricIndex) => (
                          <div key={metricIndex} className="text-sm flex items-center gap-2">
                            <EyeIcon className="h-3 w-3 text-blue-600" />
                            <span>{metric}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Risk Forecasts */}
                  <div className="bg-gray-50 p-3 rounded">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">30-day forecast:</span>
                        <span className={`font-medium ${getRiskColor(risk.risk_forecast_30_days).split(' ')[0]}`}>
                          {risk.risk_forecast_30_days.toFixed(0)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">90-day forecast:</span>
                        <span className={`font-medium ${getRiskColor(risk.risk_forecast_90_days).split(' ')[0]}`}>
                          {risk.risk_forecast_90_days.toFixed(0)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </>
      )}

      {viewMode === 'trends' && (
        <>
          {/* Risk Trend Analysis */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Risk Trend Summary</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center gap-2">
                    <TrendingUpIcon className="h-5 w-5 text-red-600" />
                    <span className="font-medium">Increasing Risk</span>
                  </div>
                  <div className="text-xl font-bold text-red-600">
                    {risks.filter(r => r.risk_trend === 'increasing').length}
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center gap-2">
                    <BarChart3Icon className="h-5 w-5 text-gray-600" />
                    <span className="font-medium">Stable Risk</span>
                  </div>
                  <div className="text-xl font-bold text-gray-600">
                    {risks.filter(r => r.risk_trend === 'stable').length}
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center gap-2">
                    <TrendingDownIcon className="h-5 w-5 text-green-600" />
                    <span className="font-medium">Decreasing Risk</span>
                  </div>
                  <div className="text-xl font-bold text-green-600">
                    {risks.filter(r => r.risk_trend === 'decreasing').length}
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Risk Escalation Thresholds</h3>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-sm text-gray-500">Average Escalation Threshold</div>
                  <div className="text-3xl font-bold text-purple-600">
                    {(risks.reduce((sum, r) => sum + r.escalation_threshold, 0) / risks.length).toFixed(0)}
                  </div>
                </div>
                <div className="border-t pt-4">
                  <div className="text-sm text-gray-600">
                    <strong>{risks.filter(r => r.composite_risk_score >= r.escalation_threshold).length}</strong> units 
                    currently exceed their escalation thresholds and require immediate attention.
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* 30-day vs 90-day Forecast Comparison */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Risk Forecast Analysis</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart data={risks.map(r => ({
                  x: r.risk_forecast_30_days,
                  y: r.risk_forecast_90_days,
                  name: r.structure_number || `Unit ${r.structure_id}`,
                  current: r.composite_risk_score
                }))}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    type="number" 
                    dataKey="x" 
                    name="30-day Forecast" 
                    domain={[0, 100]}
                  />
                  <YAxis 
                    type="number" 
                    dataKey="y" 
                    name="90-day Forecast"
                    domain={[0, 100]}
                  />
                  <Tooltip 
                    formatter={(value, name) => [`${Number(value).toFixed(0)}`, name]}
                    labelFormatter={(_, payload) => {
                      const point = payload?.[0]?.payload;
                      return point ? `${point.name} (Current: ${point.current.toFixed(0)})` : 'Unit';
                    }}
                  />
                  <Scatter dataKey="y" fill="#A855F7" />
                </ScatterChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </>
      )}

      {/* Action Items Summary */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <ClockIcon className="h-5 w-5 text-orange-600" />
          Priority Action Items
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium mb-3 text-red-600">Immediate (24-48 hours)</h4>
            <div className="space-y-2">
              {risks.filter(r => r.risk_category === 'critical').slice(0, 3).map((risk, index) => (
                <div key={index} className="text-sm p-2 border-l-4 border-red-500 bg-red-50">
                  <div className="font-medium">{risk.structure_number || `Unit ${risk.structure_id}`}</div>
                  <div className="text-gray-600">{risk.immediate_actions[0]}</div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3 text-orange-600">Short-term (1-2 weeks)</h4>
            <div className="space-y-2">
              {risks.filter(r => r.risk_category === 'high').slice(0, 3).map((risk, index) => (
                <div key={index} className="text-sm p-2 border-l-4 border-orange-500 bg-orange-50">
                  <div className="font-medium">{risk.structure_number || `Unit ${risk.structure_id}`}</div>
                  <div className="text-gray-600">{risk.immediate_actions[0]}</div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3 text-yellow-600">Medium-term (1 month)</h4>
            <div className="space-y-2">
              {risks.filter(r => r.risk_category === 'medium').slice(0, 3).map((risk, index) => (
                <div key={index} className="text-sm p-2 border-l-4 border-yellow-500 bg-yellow-50">
                  <div className="font-medium">{risk.structure_number || `Unit ${risk.structure_id}`}</div>
                  <div className="text-gray-600">{risk.immediate_actions[0]}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CompositeRiskAnalysis;