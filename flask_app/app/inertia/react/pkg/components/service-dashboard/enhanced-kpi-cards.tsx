import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  DollarSign,
  Wrench
} from "lucide-react";
import React from "react";
import { Label } from "../ui/label";
import { H3, Muted } from "../ui/typography";
import { Skeleton } from "../ui/skeleton";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";

const CardLoader = () => {
  return (
    <div className="flex flex-col space-y-1 md:space-y-2">
      <Skeleton className="h-4 w-1/2 md:h-6" />
      <Skeleton className="h-3 w-1/3 md:h-4" />
    </div>
  );
};

const EnhancedKPICards = ({ children }: React.PropsWithChildren) => {
  return <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">{children}</div>;
};

const CostEfficiencyCard = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/cost-efficiency-metrics",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  const efficiency = data?.result?.billable_hours_ratio || 0;
  const isEfficient = efficiency >= 0.80; // 80% efficiency threshold

  return (
    <Card className="gap-2 p-2 md:gap-6 md:p-4 lg:p-6">
      <CardHeader className="flex h-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="text-sm font-medium md:text-sm">
          Labor Efficiency
        </CardTitle>
        <Clock
          strokeWidth={2}
          className={`hidden h-4 w-4 min-w-5 md:flex md:h-5 md:w-5 ${
            isEfficient ? "text-green-600" : "text-orange-600"
          }`}
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              {(efficiency * 100).toFixed(1)}%
            </H3>
            <Muted className="flex items-center gap-1 text-sm md:text-sm">
              {isEfficient ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <AlertTriangle className="h-3 w-3 text-orange-500" />
              )}
              Billable Hours Ratio
            </Muted>
            <Muted className="text-xs text-gray-500">
              <Label.Numeric
                variant="dollars"
                value={data.result?.cost_per_billable_hour ?? "0"}
              />
              /hour
            </Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

const ServiceQualityCard = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/service-quality-metrics",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  const repeatRate = data?.result?.repeat_service_rate || 0;
  const isGoodQuality = repeatRate <= 15; // Less than 15% repeat rate is good

  return (
    <Card className="gap-2 p-2 md:gap-6 md:p-4 lg:p-6">
      <CardHeader className="flex h-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="text-sm font-medium md:text-sm">
          Service Quality
        </CardTitle>
        <Wrench
          strokeWidth={2}
          className={`hidden h-4 w-4 min-w-5 md:flex md:h-5 md:w-5 ${
            isGoodQuality ? "text-green-600" : "text-red-600"
          }`}
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              {repeatRate.toFixed(1)}%
            </H3>
            <Muted className="flex items-center gap-1 text-sm md:text-sm">
              {isGoodQuality ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <AlertTriangle className="h-3 w-3 text-red-500" />
              )}
              Repeat Service Rate
            </Muted>
            <Muted className="text-xs text-gray-500">
              {data.result?.avg_days_between_services.toFixed(0)} days avg interval
            </Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

const PartsEfficiencyCard = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/cost-efficiency-metrics",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  const markupRatio = data?.result?.parts_markup_ratio || 1.0;
  const emergencyRate = data?.result?.emergency_service_rate || 0;
  const isEfficient = markupRatio <= 2.5 && emergencyRate <= 0.20; // Reasonable markup and low emergency rate

  return (
    <Card className="gap-2 p-2 md:gap-6 md:p-4 lg:p-6">
      <CardHeader className="flex h-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="text-sm font-medium md:text-sm">
          Parts Efficiency
        </CardTitle>
        <DollarSign
          strokeWidth={2}
          className={`hidden h-4 w-4 min-w-5 md:flex md:h-5 md:w-5 ${
            isEfficient ? "text-green-600" : "text-orange-600"
          }`}
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              {markupRatio.toFixed(1)}x
            </H3>
            <Muted className="flex items-center gap-1 text-sm md:text-sm">
              {isEfficient ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <AlertTriangle className="h-3 w-3 text-orange-500" />
              )}
              Average Markup
            </Muted>
            <Muted className="text-xs text-gray-500">
              {(emergencyRate * 100).toFixed(1)}% emergency services
            </Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

const OutlierAlertsCard = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/basic-outlier-detection",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  const outliers = data?.result || [];
  const criticalOutliers = outliers.filter(o => o.severity === "critical").length;
  const highOutliers = outliers.filter(o => o.severity === "high").length;
  const totalOutliers = outliers.length;

  const getSeverityColor = () => {
    if (criticalOutliers > 0) return "text-red-600";
    if (highOutliers > 0) return "text-orange-600";
    if (totalOutliers > 0) return "text-yellow-600";
    return "text-green-600";
  };

  const getSeverityIcon = () => {
    if (criticalOutliers > 0) return <XCircle className="h-3 w-3 text-red-500" />;
    if (highOutliers > 0) return <AlertTriangle className="h-3 w-3 text-orange-500" />;
    if (totalOutliers > 0) return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
    return <CheckCircle className="h-3 w-3 text-green-500" />;
  };

  return (
    <Card className="gap-2 p-2 md:gap-6 md:p-4 lg:p-6">
      <CardHeader className="flex h-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="text-sm font-medium md:text-sm">
          Problem Units
        </CardTitle>
        <AlertTriangle
          strokeWidth={2}
          className={`hidden h-4 w-4 min-w-5 md:flex md:h-5 md:w-5 ${getSeverityColor()}`}
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              {totalOutliers}
            </H3>
            <Muted className="flex items-center gap-1 text-sm md:text-sm">
              {getSeverityIcon()}
              Units Detected
            </Muted>
            <Muted className="text-xs text-gray-500">
              {criticalOutliers} critical, {highOutliers} high priority
            </Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

export { 
  EnhancedKPICards, 
  CostEfficiencyCard, 
  ServiceQualityCard, 
  PartsEfficiencyCard, 
  OutlierAlertsCard 
};