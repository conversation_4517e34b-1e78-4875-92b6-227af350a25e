import React, { useState } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '../ui/tabs';
import { 
  TrendingUpIcon, 
  TrendingDownIcon, 
  AlertTriangleIcon, 
  CheckCircleIcon,
  DollarSignIcon,
  ClockIcon,
  BarChartIcon
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ScatterChart, Scatter } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";

interface ServiceIntervalData {
  structure_id: number;
  structure_number: string | null;
  customer_name: string | null;
  model_name: string | null;
  
  current_avg_interval: number;
  current_cost_per_service: string;
  current_failure_rate: number;
  
  recommended_interval: number;
  confidence_level: number;
  potential_cost_savings: string;
  risk_assessment: string;
  
  rationale: string;
  monitoring_metrics: string[];
}

const ServiceIntervalOptimization: React.FC = () => {
  const [selectedUnit, setSelectedUnit] = useState<ServiceIntervalData | null>(null);
  const [minSavings, setMinSavings] = useState<number>(1000);
  const [sortBy, setSortBy] = useState<'savings' | 'confidence' | 'interval'>('savings');

  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/service-interval-optimization",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatDays = (days: number) => {
    if (days < 30) return `${Math.round(days)} days`;
    if (days < 365) return `${Math.round(days / 30)} months`;
    return `${Math.round(days / 365)} years`;
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'low': return <CheckCircleIcon className="h-4 w-4" />;
      case 'medium': return <AlertTriangleIcon className="h-4 w-4" />;
      case 'high': return <AlertTriangleIcon className="h-4 w-4" />;
      default: return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getIntervalChange = (current: number, recommended: number) => {
    const change = ((recommended - current) / current) * 100;
    return {
      percentage: Math.abs(change),
      direction: change > 0 ? 'increase' : 'decrease',
      icon: change > 0 ? <TrendingUpIcon className="h-4 w-4" /> : <TrendingDownIcon className="h-4 w-4" />
    };
  };

  const optimizationData = data?.result || [];
  const filteredData = optimizationData.filter(item => parseFloat(item.potential_cost_savings) >= minSavings);
  
  const sortedData = [...filteredData].sort((a, b) => {
    switch (sortBy) {
      case 'savings':
        return parseFloat(b.potential_cost_savings) - parseFloat(a.potential_cost_savings);
      case 'confidence':
        return b.confidence_level - a.confidence_level;
      case 'interval':
        return Math.abs(b.recommended_interval - b.current_avg_interval) - 
               Math.abs(a.recommended_interval - a.current_avg_interval);
      default:
        return 0;
    }
  });

  const totalPotentialSavings = filteredData.reduce((sum, item) => sum + parseFloat(item.potential_cost_savings), 0);
  const averageConfidence = filteredData.length > 0 
    ? filteredData.reduce((sum, item) => sum + item.confidence_level, 0) / filteredData.length 
    : 0;

  const chartData = sortedData.slice(0, 10).map(item => ({
    name: item.structure_number || `Unit ${item.structure_id}`,
    current: item.current_avg_interval,
    recommended: item.recommended_interval,
    savings: item.potential_cost_savings,
    confidence: item.confidence_level * 100
  }));

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-4/5" />
        <Skeleton className="h-4 w-3/5" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">Error loading optimization data</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Service Interval Optimization</h2>
          <p className="text-gray-600">
            Optimize service intervals to reduce costs while maintaining reliability
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Total Potential Savings</div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(totalPotentialSavings)}
              </div>
            </div>
            <DollarSignIcon className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Optimization Opportunities</div>
              <div className="text-2xl font-bold">{filteredData.length}</div>
            </div>
            <BarChartIcon className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Average Confidence</div>
              <div className="text-2xl font-bold">{(averageConfidence * 100).toFixed(0)}%</div>
            </div>
            <CheckCircleIcon className="h-8 w-8 text-purple-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">High Confidence Units</div>
              <div className="text-2xl font-bold">
                {filteredData.filter(item => item.confidence_level > 0.8).length}
              </div>
            </div>
            <AlertTriangleIcon className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Controls */}
      <Card className="p-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Min. Savings:</label>
            <Input
              type="number"
              value={minSavings}
              onChange={(e) => setMinSavings(Number(e.target.value))}
              className="w-32"
              placeholder="1000"
            />
          </div>
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Sort by:</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'savings' | 'confidence' | 'interval')}
              className="px-3 py-1 border rounded"
            >
              <option value="savings">Potential Savings</option>
              <option value="confidence">Confidence Level</option>
              <option value="interval">Interval Change</option>
            </select>
          </div>
        </div>
      </Card>

      <Tabs defaultValue="list" className="w-full">
        <TabsList>
          <TabsTrigger value="list">Optimization List</TabsTrigger>
          <TabsTrigger value="charts">Visual Analysis</TabsTrigger>
          <TabsTrigger value="details">Unit Details</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <div className="p-6">
              {optimizationData.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <h4 className="text-lg font-medium mb-2">No Optimization Opportunities Found</h4>
                  <p>All units appear to have appropriate service intervals based on current data.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {sortedData.map((item) => {
                    const intervalChange = getIntervalChange(item.current_avg_interval, item.recommended_interval);
                    
                    return (
                      <div 
                        key={item.structure_id} 
                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                          selectedUnit?.structure_id === item.structure_id 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedUnit(item)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h4 className="font-semibold">
                                {item.structure_number || `Unit ${item.structure_id}`}
                              </h4>
                              <Badge variant="outline">{item.model_name}</Badge>
                              <Badge className={getRiskColor(item.risk_assessment)}>
                                {getRiskIcon(item.risk_assessment)}
                                <span className="ml-1">{item.risk_assessment} risk</span>
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-600 mb-2">
                              {item.customer_name}
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <span className="text-gray-500">Current Interval:</span>
                                <div className="font-medium">{formatDays(item.current_avg_interval)}</div>
                              </div>
                              <div>
                                <span className="text-gray-500">Recommended:</span>
                                <div className="font-medium flex items-center gap-1">
                                  {intervalChange.icon}
                                  {formatDays(item.recommended_interval)}
                                </div>
                              </div>
                              <div>
                                <span className="text-gray-500">Potential Savings:</span>
                                <div className="font-medium text-green-600">
                                  {formatCurrency(parseFloat(item.potential_cost_savings))}
                                </div>
                              </div>
                              <div>
                                <span className="text-gray-500">Confidence:</span>
                                <div className="font-medium">
                                  {(item.confidence_level * 100).toFixed(0)}%
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="charts" className="space-y-6">
          {chartData.length > 0 ? (
            <>
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Interval Comparison (Top 10 Units)</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis tickFormatter={(value) => `${value}d`} />
                      <Tooltip 
                        formatter={(value, name) => [
                          name === 'current' || name === 'recommended' ? `${value} days` : value,
                          name === 'current' ? 'Current Interval' : 'Recommended Interval'
                        ]}
                      />
                      <Bar dataKey="current" fill="#8884d8" name="current" />
                      <Bar dataKey="recommended" fill="#82ca9d" name="recommended" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </Card>

              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Savings vs Confidence Analysis</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <ScatterChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="confidence" 
                        tickFormatter={(value) => `${value}%`}
                        domain={[0, 100]}
                      />
                      <YAxis 
                        dataKey="savings"
                        tickFormatter={(value) => `$${value.toLocaleString()}`}
                      />
                      <Tooltip 
                        formatter={(value, name) => [
                          name === 'confidence' ? `${value}%` : formatCurrency(Number(value)),
                          name === 'confidence' ? 'Confidence' : 'Potential Savings'
                        ]}
                      />
                      <Scatter dataKey="savings" fill="#8884d8" />
                    </ScatterChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </>
          ) : (
            <Card className="p-6">
              <div className="text-center text-gray-500">
                No data available for charts. Adjust filters to see optimization opportunities.
              </div>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="details">
          {selectedUnit ? (
            <Card className="p-6">
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-2">
                    {selectedUnit.structure_number || `Unit ${selectedUnit.structure_id}`}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>{selectedUnit.customer_name}</span>
                    <span>•</span>
                    <span>{selectedUnit.model_name}</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Current Performance</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Service Interval:</span>
                        <span className="font-medium">
                          {formatDays(selectedUnit.current_avg_interval)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Cost per Service:</span>
                        <span className="font-medium">
                          {formatCurrency(parseFloat(selectedUnit.current_cost_per_service))}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Failure Rate:</span>
                        <span className="font-medium">
                          {(selectedUnit.current_failure_rate * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3">Optimization</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Recommended Interval:</span>
                        <span className="font-medium">
                          {formatDays(selectedUnit.recommended_interval)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Potential Savings:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(parseFloat(selectedUnit.potential_cost_savings))}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Confidence Level:</span>
                        <span className="font-medium">
                          {(selectedUnit.confidence_level * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-3">Risk Assessment</h4>
                    <div className="flex items-center gap-2 mb-3">
                      <Badge className={getRiskColor(selectedUnit.risk_assessment)}>
                        {getRiskIcon(selectedUnit.risk_assessment)}
                        <span className="ml-1">{selectedUnit.risk_assessment} risk</span>
                      </Badge>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">Rationale</h4>
                  <p className="text-gray-700">{selectedUnit.rationale}</p>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">Monitoring Metrics</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedUnit.monitoring_metrics.map((metric, index) => (
                      <Badge key={index} variant="outline">
                        {metric}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          ) : (
            <Card className="p-6">
              <div className="text-center text-gray-500">
                Select a unit from the list to view detailed optimization analysis
              </div>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ServiceIntervalOptimization;