import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import {
  useServiceCostSelector,
  useServiceCostStore,
} from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { AgGridReact } from "ag-grid-react";
import { ColDef, ValueFormatterParams } from "ag-grid-community";
import { themeQuartz } from "ag-grid-community";
import { Button } from "../ui/button";
import { X } from "lucide-react";

const formatCurrency = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === "") return "";
  return new Intl.NumberFormat("en-CA", {
    style: "currency",
    currency: "CAD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(Number(params.value));
};

const formatNumber = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === "") return "";
  return Number(params.value).toFixed(2);
};

const WorkOrderPartsTable = () => {
  const selectedWorkOrderId = useServiceCostSelector(
    (state) => state.context.selectedWorkOrderId,
  );
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const store = useServiceCostStore();

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/work-order-parts",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        work_order_id: selectedWorkOrderId,
        include_ijack,
        include_sales_parts,
      },
    },
    {
      enabled: selectedWorkOrderId !== null,
    },
  );

  const columnDefs: ColDef[] = [
    {
      field: "part_name",
      headerName: "Part Number",
      headerTooltip: "IJack part number or SKU. Used to identify specific components, materials, or labor charges.",
      pinned: "left",
      width: 150,
      cellStyle: { fontWeight: "bold" },
    },
    {
      field: "description",
      headerName: "Description",
      headerTooltip: "Detailed description of the part, material, or service performed. Provides context for the line item.",
      width: 250,
      wrapText: true,
      autoHeight: true,
    },
    {
      field: "category",
      headerName: "Category",
      headerTooltip: "Part category: 070 (Service) = labor charges, 060 (PM Parts) = maintenance parts, 050 (Sales) = sold equipment",
      width: 140,
      cellRenderer: (params: any) => {
        const category = params.value;
        const categoryClass = 
          category === '070 (Service)' ? 'text-blue-600 font-semibold bg-blue-50 px-2 py-1 rounded' :
          category === '060 (PM Parts)' ? 'text-orange-600 font-semibold bg-orange-50 px-2 py-1 rounded' :
          category === '050 (Sales)' ? 'text-green-600 font-semibold bg-green-50 px-2 py-1 rounded' :
          'text-gray-600 px-2 py-1';
        return <span className={categoryClass}>{category}</span>;
      }
    },
    {
      field: "quantity",
      headerName: "Qty",
      headerTooltip: "Quantity used or hours of labor. For service (070) items, this represents labor hours worked.",
      valueFormatter: formatNumber,
      width: 80,
      type: "numericColumn",
      cellStyle: { textAlign: "center" },
    },
    {
      field: "price",
      headerName: "Unit Cost",
      headerTooltip: "Cost per unit or hourly rate (converted to CAD). For labor, this is the technician's hourly billing rate.",
      valueFormatter: formatCurrency,
      width: 110,
      type: "numericColumn",
    },
    {
      field: "total_cost",
      headerName: "Total Cost",
      headerTooltip: "Total line item cost (quantity × unit cost, converted to CAD). Highest costs appear first by default.",
      valueFormatter: formatCurrency,
      width: 120,
      type: "numericColumn",
      cellStyle: { fontWeight: "bold" },
      sort: "desc",
    },
    {
      field: "warehouse",
      headerName: "Warehouse",
      headerTooltip: "Source warehouse location where the part was pulled from or service category designation.",
      width: 120,
    },
  ];

  const defaultColDef = {
    sortable: true,
    filter: true,
    resizable: true,
  };

  if (selectedWorkOrderId === null) {
    return null; // Don't render if no work order is selected
  }

  return (
    <Card className="col-span-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">
            Work Order Parts (WO-{selectedWorkOrderId})
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => store.send({ type: "clearWorkOrderSelection" })}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : isError ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">Error loading parts data</p>
          </div>
        ) : !data?.result || data.result.length === 0 ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">
              No parts found for this work order
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="h-[400px] w-full">
              <AgGridReact
                theme={themeQuartz}
                rowModelType="clientSide"
                rowData={data.result}
                columnDefs={columnDefs}
                defaultColDef={defaultColDef}
                pagination={true}
                paginationPageSize={15}
                paginationPageSizeSelector={[15, 25, 50]}
                domLayout="normal"
                suppressMenuHide={true}
                enableCellTextSelection={true}
                animateRows={true}
              />
            </div>

            {/* Enhanced Summary Card with Labor vs Parts Breakdown */}
            <div className="rounded-lg border bg-gray-50 p-4">
              <div className="grid grid-cols-2 gap-4 md:grid-cols-5">
                <div className="text-center">
                  <div className="text-sm text-gray-600">Total Items</div>
                  <div className="text-lg font-semibold">
                    {data.result.length}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-blue-600 font-medium">Labor Hours</div>
                  <div className="text-lg font-semibold text-blue-700">
                    {data.result
                      .filter(part => part.category === '070 (Service)')
                      .reduce((sum, part) => sum + Number(part.quantity || 0), 0)
                      .toFixed(1)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-blue-600 font-medium">Labor Cost</div>
                  <div className="text-lg font-semibold text-blue-700">
                    {formatCurrency({
                      value: data.result
                        .filter(part => part.category === '070 (Service)')
                        .reduce((sum, part) => sum + Number(part.total_cost || 0), 0),
                      data: {},
                      node: {} as any,
                      colDef: {} as any,
                      column: {} as any,
                      api: {} as any,
                      context: {},
                    })}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-orange-600 font-medium">Parts Cost</div>
                  <div className="text-lg font-semibold text-orange-700">
                    {formatCurrency({
                      value: data.result
                        .filter(part => part.category !== '070 (Service)')
                        .reduce((sum, part) => sum + Number(part.total_cost || 0), 0),
                      data: {},
                      node: {} as any,
                      colDef: {} as any,
                      column: {} as any,
                      api: {} as any,
                      context: {},
                    })}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-600 font-medium">Total Cost</div>
                  <div className="text-xl font-bold text-gray-800">
                    {formatCurrency({
                      value: data.result.reduce(
                        (sum, part) => sum + Number(part.total_cost || 0),
                        0,
                      ),
                      data: {},
                      node: {} as any,
                      colDef: {} as any,
                      column: {} as any,
                      api: {} as any,
                      context: {},
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { WorkOrderPartsTable };
