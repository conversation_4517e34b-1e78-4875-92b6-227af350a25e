import React from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { 
  CalendarIcon,
  SunIcon,
  CloudIcon,
  SnowflakeIcon,
  LeafIcon,
  ThermometerIcon,
  TrendingUpIcon,
  TrendingDownIcon
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";

const SeasonalInsights: React.FC = () => {
  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  // Fetch seasonal analysis
  const { data: seasonalData, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/seasonal-analysis",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getSeasonIcon = (season: string) => {
    switch (season.toLowerCase()) {
      case 'winter': return <SnowflakeIcon className="h-5 w-5 text-blue-600" />;
      case 'spring': return <LeafIcon className="h-5 w-5 text-green-600" />;
      case 'summer': return <SunIcon className="h-5 w-5 text-orange-600" />;
      case 'fall': return <CloudIcon className="h-5 w-5 text-amber-600" />;
      default: return <CalendarIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getSeasonColor = (season: string) => {
    switch (season.toLowerCase()) {
      case 'winter': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'spring': return 'text-green-600 bg-green-50 border-green-200';
      case 'summer': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'fall': return 'text-amber-600 bg-amber-50 border-amber-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getMonthColor = (variance: number) => {
    if (variance > 15) return '#EF4444'; // Red for high cost months
    if (variance > 5) return '#F59E0B';  // Orange for moderate
    if (variance < -15) return '#10B981'; // Green for low cost months
    if (variance < -5) return '#34D399';  // Light green
    return '#6B7280'; // Gray for average
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-80 w-full" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">Error loading seasonal analysis</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const seasonal = seasonalData?.result;

  if (!seasonal) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          No seasonal data available. Ensure sufficient historical data exists for analysis.
        </div>
      </Card>
    );
  }

  // Prepare chart data
  const monthlyChartData = seasonal.monthly_patterns.map(pattern => ({
    month: pattern.month_name,
    monthShort: pattern.month_name.substring(0, 3),
    cost: parseFloat(pattern.average_cost),
    variance: pattern.cost_variance,
    failures: pattern.failure_rate,
    services: pattern.service_count
  }));


  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <CalendarIcon className="h-6 w-6 text-blue-600" />
            Seasonal Cost Analysis
          </h2>
          <p className="text-gray-600">
            Identify seasonal patterns and optimize for cost variations
          </p>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <div className="text-sm text-gray-500 mb-1">Seasonal Cost Variation</div>
            <div className="text-3xl font-bold text-blue-600">
              {(seasonal.seasonal_cost_variation * 100).toFixed(1)}%
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Coefficient of variation across seasons
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-center">
            <div className="text-sm text-gray-500 mb-1">Highest Cost Season</div>
            <div className="flex items-center justify-center gap-2 my-2">
              {getSeasonIcon(seasonal.highest_cost_season)}
              <Badge className={getSeasonColor(seasonal.highest_cost_season)}>
                {seasonal.highest_cost_season}
              </Badge>
            </div>
            <div className="text-xs text-gray-500">
              Plan for increased costs during this period
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-center">
            <div className="text-sm text-gray-500 mb-1">Weather Impact Score</div>
            <div className="text-3xl font-bold text-orange-600">
              {(seasonal.weather_impact_score * 100).toFixed(0)}%
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Estimated climate influence on costs
            </div>
          </div>
        </Card>
      </div>

      {/* Monthly Pattern Chart */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Monthly Cost Patterns</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={monthlyChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="monthShort" />
              <YAxis yAxisId="cost" orientation="left" />
              <YAxis yAxisId="rate" orientation="right" />
              <Tooltip 
                formatter={(value, name) => [
                  name === 'cost' ? formatCurrency(Number(value)) : `${Number(value).toFixed(1)}%`,
                  name === 'cost' ? 'Average Cost' : 'Failure Rate'
                ]}
                labelFormatter={(label) => {
                  const month = monthlyChartData.find(m => m.monthShort === label);
                  return month ? month.month : label;
                }}
              />
              <Line 
                yAxisId="cost"
                type="monotone" 
                dataKey="cost" 
                stroke="#3B82F6" 
                strokeWidth={3}
                name="cost"
                dot={{ r: 4 }}
              />
              <Line 
                yAxisId="rate"
                type="monotone" 
                dataKey="failures" 
                stroke="#EF4444" 
                strokeWidth={2}
                strokeDasharray="5 5"
                name="failures"
                dot={{ r: 3 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        <div className="flex justify-center gap-6 mt-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-0.5 bg-blue-600"></div>
            <span>Average Cost (CAD)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-0.5 bg-red-600 border-dashed"></div>
            <span>Failure Rate (%)</span>
          </div>
        </div>
      </Card>

      {/* Cost Variance Heatmap */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Monthly Cost Variance from Average</h3>
        <div className="grid grid-cols-6 md:grid-cols-12 gap-2">
          {monthlyChartData.map((month, index) => (
            <div 
              key={index}
              className="text-center p-2 rounded border"
              style={{ 
                backgroundColor: getMonthColor(month.variance) + '20',
                borderColor: getMonthColor(month.variance) + '40'
              }}
            >
              <div className="text-xs font-medium">{month.monthShort}</div>
              <div 
                className="text-xs font-bold mt-1"
                style={{ color: getMonthColor(month.variance) }}
              >
                {month.variance > 0 ? '+' : ''}{month.variance.toFixed(1)}%
              </div>
            </div>
          ))}
        </div>
        <div className="flex justify-center gap-4 mt-4 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded bg-red-500"></div>
            <span>High Cost (+15%)</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded bg-orange-500"></div>
            <span>Above Avg (+5%)</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded bg-gray-500"></div>
            <span>Average (±5%)</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded bg-green-500"></div>
            <span>Below Avg (-5%)</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 rounded bg-emerald-500"></div>
            <span>Low Cost (-15%)</span>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Peak Failure Months */}
        {seasonal.peak_failure_months.length > 0 && (
          <Card className="p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <TrendingUpIcon className="h-5 w-5 text-red-600" />
              Peak Failure Months
            </h3>
            <div className="space-y-2">
              {seasonal.peak_failure_months.map((month, index) => {
                const monthData = seasonal.monthly_patterns.find(p => p.month_name === month);
                return (
                  <div key={index} className="flex items-center justify-between">
                    <span className="font-medium">{month}</span>
                    <div className="text-right">
                      <div className="text-sm text-red-600 font-bold">
                        {monthData?.failure_rate.toFixed(1)}%
                      </div>
                      <div className="text-xs text-gray-500">
                        {monthData?.service_count} services
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        )}

        {/* Climate Risk Factors */}
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <ThermometerIcon className="h-5 w-5 text-orange-600" />
            Climate Risk Factors
          </h3>
          <div className="space-y-2">
            {seasonal.climate_risk_factors.map((factor, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                <span className="text-sm">{factor}</span>
              </div>
            ))}
          </div>
        </Card>

        {/* Next Seasonal Peak */}
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <CalendarIcon className="h-5 w-5 text-purple-600" />
            Next Seasonal Peak
          </h3>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {formatDate(seasonal.next_seasonal_peak)}
            </div>
            <div className="text-sm text-gray-600">
              Prepare inventory and resources before this date
            </div>
          </div>
        </Card>

        {/* Preparation Actions */}
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <TrendingDownIcon className="h-5 w-5 text-green-600" />
            Recommended Actions
          </h3>
          <div className="space-y-2">
            {seasonal.recommended_prep_actions.map((action, index) => (
              <div key={index} className="flex items-start gap-2">
                <div className="w-2 h-2 rounded-full bg-green-500 mt-2"></div>
                <span className="text-sm">{action}</span>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Detailed Monthly Breakdown */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Monthly Breakdown</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">Month</th>
                <th className="text-right p-2">Avg Cost</th>
                <th className="text-right p-2">Variance</th>
                <th className="text-right p-2">Services</th>
                <th className="text-right p-2">Failure Rate</th>
              </tr>
            </thead>
            <tbody>
              {seasonal.monthly_patterns.map((pattern, index) => (
                <tr key={index} className="border-b hover:bg-gray-50">
                  <td className="p-2 font-medium">{pattern.month_name}</td>
                  <td className="p-2 text-right">
                    {formatCurrency(parseFloat(pattern.average_cost))}
                  </td>
                  <td className="p-2 text-right">
                    <span className={`font-medium ${
                      pattern.cost_variance > 10 ? 'text-red-600' :
                      pattern.cost_variance > 0 ? 'text-orange-600' :
                      pattern.cost_variance < -10 ? 'text-green-600' : 'text-gray-600'
                    }`}>
                      {pattern.cost_variance > 0 ? '+' : ''}{pattern.cost_variance.toFixed(1)}%
                    </span>
                  </td>
                  <td className="p-2 text-right">{pattern.service_count}</td>
                  <td className="p-2 text-right">
                    <span className={`font-medium ${
                      pattern.failure_rate > 20 ? 'text-red-600' :
                      pattern.failure_rate > 10 ? 'text-orange-600' : 'text-green-600'
                    }`}>
                      {pattern.failure_rate.toFixed(1)}%
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default SeasonalInsights;