import React, { useState } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  AlertTriangleIcon, 
  CheckCircleIcon,
  BrainIcon,
  TargetIcon,
  LightbulbIcon,
  CalendarIcon,
  DollarSignIcon
} from 'lucide-react';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";

const PredictiveAnalyticsDashboard: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'30' | '90' | '365'>('90');

  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  // Fetch predictive cost modeling data
  const { data: costPredictions, isLoading: costLoading, isError: costError } = $api.useQuery(
    "post",
    "/v1/service-analytics/predictive-cost-modeling",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  // Fetch failure predictions
  const { data: failurePredictions, isLoading: failureLoading, isError: failureError } = $api.useQuery(
    "post",
    "/v1/service-analytics/failure-prediction",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  // Fetch advanced root cause analysis
  const { data: rootCauseAnalysis, isLoading: rootCauseLoading, isError: rootCauseError } = $api.useQuery(
    "post",
    "/v1/service-analytics/advanced-root-cause-analysis",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  // Fetch intelligent recommendations
  const { data: recommendations, isLoading: recommendationsLoading, isError: recommendationsError } = $api.useQuery(
    "post",
    "/v1/service-analytics/intelligent-recommendations",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getRiskIcon = (risk: string) => {
    switch (risk.toLowerCase()) {
      case 'critical': 
      case 'high': return <AlertTriangleIcon className="h-4 w-4" />;
      case 'medium': return <AlertTriangleIcon className="h-4 w-4" />;
      case 'low': return <CheckCircleIcon className="h-4 w-4" />;
      default: return <AlertTriangleIcon className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'urgent': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (costLoading || failureLoading || rootCauseLoading || recommendationsLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-4/5" />
        <Skeleton className="h-64 w-full" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (costError || failureError || rootCauseError || recommendationsError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">Error loading predictive analytics</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const costModels = costPredictions?.result || [];
  const failures = failurePredictions?.result || [];
  const rootCauses = rootCauseAnalysis?.result || [];
  const recs = recommendations?.result || [];

  // Calculate summary statistics
  const totalPredictedCosts = costModels.reduce((sum, model) => sum + parseFloat(model.annual_cost_prediction), 0);
  const highRiskUnits = failures.filter(f => f.overall_failure_risk === 'critical' || f.overall_failure_risk === 'high').length;
  const totalSavingsOpportunity = recs.reduce((sum, rec) => sum + parseFloat(rec.expected_cost_savings), 0);
  const imminentFailures = failures.filter(f => f.imminent_failure_probability > 0.6).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BrainIcon className="h-6 w-6 text-purple-600" />
            Predictive Analytics & AI Insights
          </h2>
          <p className="text-gray-600">
            Advanced machine learning predictions and automated recommendations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Prediction Horizon:</label>
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value as '30' | '90' | '365')}
            className="px-3 py-1 border rounded"
          >
            <option value="30">30 Days</option>
            <option value="90">90 Days</option>
            <option value="365">1 Year</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Predicted Annual Costs</div>
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(totalPredictedCosts)}
              </div>
            </div>
            <DollarSignIcon className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">High Risk Units</div>
              <div className="text-2xl font-bold text-red-600">{highRiskUnits}</div>
            </div>
            <AlertTriangleIcon className="h-8 w-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Savings Opportunity</div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(totalSavingsOpportunity)}
              </div>
            </div>
            <TargetIcon className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Imminent Failures</div>
              <div className="text-2xl font-bold text-orange-600">{imminentFailures}</div>
            </div>
            <CalendarIcon className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      <Tabs defaultValue="cost-predictions" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="cost-predictions">💰 Cost Predictions</TabsTrigger>
          <TabsTrigger value="failure-analysis">⚠️ Failure Predictions</TabsTrigger>
          <TabsTrigger value="root-cause">🔍 Root Cause AI</TabsTrigger>
          <TabsTrigger value="recommendations">💡 AI Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="cost-predictions" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Predictive Cost Modeling</h3>
            {costModels.length === 0 ? (
              <Card className="p-6">
                <div className="text-center text-gray-500">
                  No cost predictions available. Ensure sufficient historical data exists for analysis.
                </div>
              </Card>
            ) : (
              <div className="space-y-4">
                {costModels.slice(0, 10).map((model) => (
                  <Card key={model.structure_id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-semibold">
                            {model.structure_number || `Unit ${model.structure_id}`}
                          </h4>
                          <Badge variant="outline">{model.model_name}</Badge>
                          <Badge className={model.cost_volatility_score > 0.3 ? getRiskColor('high') : getRiskColor('low')}>
                            {model.cost_volatility_score > 0.3 ? 'High Volatility' : 'Stable'}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600 mb-3">
                          {model.customer_name}
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Current Monthly:</span>
                            <div className="font-medium">{formatCurrency(parseFloat(model.current_monthly_cost))}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Predicted Annual:</span>
                            <div className="font-medium text-blue-600">
                              {formatCurrency(parseFloat(model.annual_cost_prediction))}
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-500">Next Service:</span>
                            <div className="font-medium">
                              {formatCurrency(parseFloat(model.predicted_next_service_cost))}
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-500">Confidence:</span>
                            <div className="font-medium">
                              {(model.prediction_confidence * 100).toFixed(0)}%
                            </div>
                          </div>
                        </div>
                        {model.risk_factors.length > 0 && (
                          <div className="mt-3">
                            <div className="text-sm text-gray-500 mb-1">Risk Factors:</div>
                            <div className="flex flex-wrap gap-1">
                              {model.risk_factors.map((factor, index) => (
                                <Badge key={index} variant="destructive" className="text-xs">
                                  {factor}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => console.log('View details for:', model)}
                      >
                        View Details
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="failure-analysis" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Failure Prediction Analysis</h3>
            {failures.length === 0 ? (
              <Card className="p-6">
                <div className="text-center text-gray-500">
                  No failure predictions available. All units appear to be operating within normal parameters.
                </div>
              </Card>
            ) : (
              <div className="space-y-4">
                {failures.map((failure) => (
                  <Card key={failure.structure_id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-semibold">
                            {failure.structure_number || `Unit ${failure.structure_id}`}
                          </h4>
                          <Badge variant="outline">{failure.model_name}</Badge>
                          <Badge className={getRiskColor(failure.overall_failure_risk)}>
                            {getRiskIcon(failure.overall_failure_risk)}
                            <span className="ml-1">{failure.overall_failure_risk} risk</span>
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600 mb-3">
                          {failure.customer_name}
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                          <div>
                            <span className="text-gray-500">Failure Probability:</span>
                            <div className="font-medium text-red-600">
                              {(failure.imminent_failure_probability * 100).toFixed(0)}%
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-500">High-Risk Parts:</span>
                            <div className="font-medium">{failure.high_risk_parts.length}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Optimal Interval:</span>
                            <div className="font-medium">
                              {Math.round(failure.optimal_service_interval)} days
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-500">Priority Score:</span>
                            <div className="font-medium">
                              {(failure.priority_score * 100).toFixed(0)}%
                            </div>
                          </div>
                        </div>
                        {failure.recommended_actions.length > 0 && (
                          <div className="mb-3">
                            <div className="text-sm text-gray-500 mb-1">Recommendations:</div>
                            <ul className="text-sm list-disc list-inside">
                              {failure.recommended_actions.map((action, index) => (
                                <li key={index}>{action}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {failure.predicted_failures.length > 0 && (
                          <div>
                            <div className="text-sm text-gray-500 mb-1">Predicted Failures:</div>
                            <div className="space-y-1">
                              {failure.predicted_failures.slice(0, 3).map((pred, index) => (
                                <div key={index} className="text-sm flex justify-between">
                                  <span>{String(pred.part_name)}</span>
                                  <span className="text-red-600">
                                    {String(pred.days_until_failure)} days
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="root-cause" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Advanced Root Cause Analysis</h3>
            {rootCauses.length === 0 ? (
              <Card className="p-6">
                <div className="text-center text-gray-500">
                  No root cause analysis available. This requires outlier detection to identify problem units.
                </div>
              </Card>
            ) : (
              <div className="space-y-4">
                {rootCauses.map((analysis) => (
                  <Card key={analysis.structure_id} className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h4 className="font-semibold">
                              {analysis.structure_number || `Unit ${analysis.structure_id}`}
                            </h4>
                            <Badge variant="outline">{analysis.model_name}</Badge>
                            <Badge className={getRiskColor(analysis.problem_severity)}>
                              {analysis.problem_severity} severity
                            </Badge>
                          </div>
                          <div className="text-sm text-gray-600 mb-3">
                            {analysis.customer_name} • {analysis.problem_category}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500">Total Cost Impact</div>
                          <div className="text-lg font-bold text-red-600">
                            {formatCurrency(parseFloat(analysis.total_cost_impact))}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-3 rounded">
                        <div className="text-sm font-medium mb-1">Primary Root Cause:</div>
                        <div className="text-sm">{analysis.primary_root_cause}</div>
                      </div>

                      {analysis.contributing_factors.length > 0 && (
                        <div>
                          <div className="text-sm font-medium mb-2">Contributing Factors:</div>
                          <div className="space-y-1">
                            {analysis.contributing_factors.slice(0, 3).map((factor, index) => (
                              <div key={index} className="flex justify-between text-sm">
                                <span>{String(factor.factor)}</span>
                                <span className="font-medium">
                                  {(Number(factor.impact_score) * 100).toFixed(0)}% impact
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm font-medium mb-2">Immediate Actions:</div>
                          <ul className="text-sm list-disc list-inside space-y-1">
                            {analysis.immediate_fixes.slice(0, 2).map((fix, index) => (
                              <li key={index}>{String(fix.action)}</li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <div className="text-sm font-medium mb-2">Expected Savings:</div>
                          <div className="text-lg font-bold text-green-600">
                            {formatCurrency(parseFloat(analysis.expected_cost_reduction))}
                          </div>
                          <div className="text-sm text-gray-500">
                            {(analysis.solution_success_probability * 100).toFixed(0)}% confidence
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">AI-Generated Recommendations</h3>
            {recs.length === 0 ? (
              <Card className="p-6">
                <div className="text-center text-gray-500">
                  No recommendations available. This may indicate optimal performance or insufficient data for analysis.
                </div>
              </Card>
            ) : (
              <div className="space-y-4">
                {recs.map((rec) => (
                  <Card key={rec.recommendation_id} className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <LightbulbIcon className="h-5 w-5 text-yellow-600" />
                            <h4 className="font-semibold">{rec.title}</h4>
                            <Badge className={getPriorityColor(rec.priority)}>
                              {rec.priority} priority
                            </Badge>
                            <Badge variant="outline">{rec.recommendation_type}</Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500">Expected Savings</div>
                          <div className="text-lg font-bold text-green-600">
                            {formatCurrency(parseFloat(rec.expected_cost_savings))}
                          </div>
                        </div>
                      </div>

                      <div className="bg-blue-50 p-3 rounded">
                        <div className="text-sm font-medium mb-1">Rationale:</div>
                        <div className="text-sm">{rec.rationale}</div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <div className="text-sm font-medium mb-1">Implementation Cost:</div>
                          <div className="font-bold">{formatCurrency(parseFloat(rec.estimated_cost))}</div>
                          <div className="text-sm text-gray-500">
                            {rec.estimated_effort_hours.toFixed(0)} hours effort
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium mb-1">Payback Period:</div>
                          <div className="font-bold">{rec.payback_period_months.toFixed(1)} months</div>
                          <div className="text-sm text-gray-500">
                            {(rec.expected_failure_reduction * 100).toFixed(0)}% failure reduction
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium mb-1">Confidence:</div>
                          <div className="font-bold">{(rec.confidence_score * 100).toFixed(0)}%</div>
                          <div className="text-sm text-gray-500">
                            {rec.implementation_risk} implementation risk
                          </div>
                        </div>
                      </div>

                      <div>
                        <div className="text-sm font-medium mb-2">Implementation Steps:</div>
                        <ol className="text-sm list-decimal list-inside space-y-1">
                          {rec.implementation_steps.slice(0, 3).map((step, index) => (
                            <li key={index}>{step}</li>
                          ))}
                          {rec.implementation_steps.length > 3 && (
                            <li className="text-gray-500">
                              ... and {rec.implementation_steps.length - 3} more steps
                            </li>
                          )}
                        </ol>
                      </div>

                      {rec.expires_date && (
                        <div className="text-sm text-orange-600">
                          ⏰ Recommendation expires: {formatDate(rec.expires_date)}
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PredictiveAnalyticsDashboard;