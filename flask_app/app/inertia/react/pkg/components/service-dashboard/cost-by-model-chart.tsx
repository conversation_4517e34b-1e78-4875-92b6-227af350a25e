import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Label as LabelComponent } from "@/components/ui/label";
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid } from "recharts";
import { Payload } from "recharts/types/component/DefaultLegendContent";

interface CurrencyLabelFormatterProps {
  value: number | string;
  label: string;
  payload: Payload;
}

function CurrencyLabelFormatter({
  value,
  label,
  payload,
}: CurrencyLabelFormatterProps) {
  return (
    <div className="border-input flex w-full items-center gap-1 border-t pt-1">
      {payload.color && (
        <span
          className="inline-block h-3 w-3 rounded-sm"
          style={{ backgroundColor: payload.color }}
        />
      )}
      <LabelComponent.Numeric value={value} variant="dollars" />
      <span className="text-muted-foreground w-auto flex-grow text-end text-sm">
        {label}
      </span>
    </div>
  );
}

const formatCurrency = (value: number): string => {
  if (value >= 1000000000) {
    return `$${(value / 1000000000).toFixed(1)}B`;
  } else if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(0)}K`;
  }
  return `$${value.toLocaleString()}`;
};

const chartConfig = {
  sales: {
    label: "Sales Parts (050)",
    color: "var(--chart-1)",
  },
  pm: {
    label: "PM Parts (060)",
    color: "var(--chart-2)",
  },
  labor: {
    label: "Labor (070)",
    color: "var(--chart-3)",
  },
  other: {
    label: "Other Parts",
    color: "var(--chart-4)",
  },
} satisfies ChartConfig;

const CostByModelChart = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector(
    (state) => state.context.service_types,
  );
  const from = useServiceCostSelector(
    (state) => state.context.service_date_from,
  );
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector(
    (state) => state.context.unit_types,
  );
  const technicians = useServiceCostSelector(
    (state) => state.context.technicians,
  );
  const part_categories = useServiceCostSelector(
    (state) => state.context.part_categories,
  );
  const include_ijack = useServiceCostSelector(
    (state) => state.context.include_ijack,
  );
  const include_sales_parts = useServiceCostSelector(
    (state) => state.context.include_sales_parts,
  );
  const selected_years = useServiceCostSelector(
    (state) => state.context.selected_years,
  );
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/cost-by-model",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  const chartData =
    data?.result?.map((item) => ({
      name: item.model_name,
      // Stack components (average cost per unit for each category)
      sales: Number(item.sales_cost) / (item.unit_count || 1),
      pm: Number(item.pm_cost) / (item.unit_count || 1),
      labor: Number(item.labor_cost) / (item.unit_count || 1),
      other: Number(item.other_cost) / (item.unit_count || 1),
      // Metadata for tooltip
      unitCount: item.unit_count,
      workOrders: item.work_order_count,
      avgCostPerUnit: Number(item.average_cost_per_unit),
    })) || [];

  return (
    <Card className="col-span-1">
      <CardHeader className="pb-0">
        <CardTitle>Average Service Cost per Unit by Model by Year</CardTitle>
        <CardDescription>
          Yearly average service costs per unit broken down by part categories
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : isError ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">Error loading chart data</p>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">No data available</p>
          </div>
        ) : (
          <ChartContainer
            className="aspect-auto h-100 w-full"
            config={chartConfig}
          >
            <BarChart
              data={chartData}
              margin={{
                top: 0,
                bottom: 0,
                right: 10,
                left: 10,
              }}
            >
              <ChartLegend
                className="mt-6 flex-wrap pb-0"
                content={
                  <ChartLegendContent
                    filterFunc={(item) => {
                      const key = item.dataKey as keyof typeof chartConfig;
                      const total = chartData.reduce((acc, d) => {
                        return acc + (d[key] || 0);
                      }, 0);
                      return total !== 0;
                    }}
                  />
                }
              />
              <CartesianGrid vertical={true} strokeDasharray="3 3" />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    filterFunc={(item) => Number(item.value) !== 0}
                    className="w-[300px]"
                    formatter={(value, name, item) => {
                      return (
                        <CurrencyLabelFormatter
                          value={Number(value)}
                          label={`${name}`}
                          payload={item as unknown as Payload}
                        />
                      );
                    }}
                    labelFormatter={(label) => {
                      const data = chartData.find((d) => d.name === label);
                      return (
                        <div>
                          <p className="font-medium">{`Model: ${label}`}</p>
                          {data && (
                            <div className="text-sm text-gray-600">
                              <p>{`Units: ${data.unitCount} | Work Orders: ${data.workOrders}`}</p>
                              <p className="font-semibold">{`Avg Yearly Cost/Unit: ${formatCurrency(data.avgCostPerUnit)}`}</p>
                            </div>
                          )}
                        </div>
                      );
                    }}
                  />
                }
              />
              <XAxis
                dataKey="name"
                angle={-45}
                textAnchor="end"
                height={80}
                tickLine={false}
                axisLine={true}
                tickMargin={10}
              />
              <YAxis
                tickFormatter={formatCurrency}
                tickLine={false}
                axisLine={false}
                domain={[0, "dataMax"]}
              />
              <Bar
                dataKey="sales"
                stackId="cost"
                fill="var(--color-sales)"
                name="Sales Parts (050)"
              />
              <Bar
                dataKey="pm"
                stackId="cost"
                fill="var(--color-pm)"
                name="PM Parts (060)"
              />
              <Bar
                dataKey="labor"
                stackId="cost"
                fill="var(--color-labor)"
                name="Labor (070)"
              />
              <Bar
                dataKey="other"
                stackId="cost"
                fill="var(--color-other)"
                name="Other Parts"
              />
            </BarChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
};

export { CostByModelChart };
