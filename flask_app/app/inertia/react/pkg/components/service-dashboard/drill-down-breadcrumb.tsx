import { useServiceCostSelector, useServiceCostStore } from "@/stores/service-costs/hooks";
import { ChevronRight, Home, Building2, FileText } from "lucide-react";
import { Button } from "../ui/button";

const DrillDownBreadcrumb = () => {
  const selectedStructureId = useServiceCostSelector((state) => state.context.selectedStructureId);
  const selectedWorkOrderId = useServiceCostSelector((state) => state.context.selectedWorkOrderId);
  const store = useServiceCostStore();

  // Don't show breadcrumb if nothing is selected
  if (!selectedStructureId && !selectedWorkOrderId) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 border-t bg-white shadow-lg">
      <div className="mx-auto max-w-7xl px-4 py-3 lg:px-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm">
            {/* Home/Overview */}
            <div className="flex items-center space-x-1">
              <Home className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">Service Cost Overview</span>
            </div>

            {/* Structure Level */}
            {selectedStructureId && (
              <>
                <ChevronRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center space-x-1">
                  <Building2 className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-blue-700">
                    Structure {selectedStructureId}
                  </span>
                </div>
              </>
            )}

            {/* Work Order Level */}
            {selectedWorkOrderId && (
              <>
                <ChevronRight className="h-4 w-4 text-gray-400" />
                <div className="flex items-center space-x-1">
                  <FileText className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-700">
                    Work Order {selectedWorkOrderId}
                  </span>
                </div>
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {selectedWorkOrderId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => store.send({ type: 'clearWorkOrderSelection' })}
                className="h-7 px-3 text-xs text-gray-500 hover:text-gray-700"
              >
                Clear Work Order
              </Button>
            )}
            {selectedStructureId && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => store.send({ type: 'clearStructureSelection' })}
                className="h-7 px-3 text-xs"
              >
                Clear All
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export { DrillDownBreadcrumb };