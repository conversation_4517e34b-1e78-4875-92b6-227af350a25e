import React from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../ui/tabs';
import { 
  CalendarIcon,
  TrendingUpIcon,
  MapPinIcon,
  ShieldIcon,
  AlertTriangleIcon,
  BarChart3Icon,
  SunIcon
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { EnhancedGeographicAnalysis } from './enhanced-geographic-analysis';

const TemporalInsightsDashboard: React.FC = () => {

  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  const baseQuery = {
    customers,
    models,
    unit_types,
    service_dates: {
      from: zIsoDate.nullable().parse(from),
      to: zIsoDate.nullable().parse(to),
    },
    selected_years,
    service_types,
    technicians,
    part_categories,
    include_ijack,
    include_sales_parts,
        include_unmapped,
  };

  // Fetch temporal insights summary
  const { data: temporalSummary, isLoading: summaryLoading, isError: summaryError } = $api.useQuery(
    "post",
    "/v1/service-analytics/temporal-insights-summary",
    { body: baseQuery }
  );

  // Fetch seasonal analysis
  const { data: seasonalData, isLoading: seasonalLoading, isError: seasonalError } = $api.useQuery(
    "post",
    "/v1/service-analytics/seasonal-analysis",
    { body: baseQuery }
  );

  // Fetch warranty analysis
  const { data: warrantyData, isLoading: warrantyLoading, isError: warrantyError } = $api.useQuery(
    "post",
    "/v1/service-analytics/warranty-analysis",
    { body: baseQuery }
  );

  // Fetch geographic analysis
  const { data: _geographicData, isLoading: geographicLoading, isError: geographicError } = $api.useQuery(
    "post",
    "/v1/service-analytics/geographic-analysis",
    { body: baseQuery }
  );

  // Fetch service urgency analysis
  const { data: urgencyData, isLoading: urgencyLoading, isError: urgencyError } = $api.useQuery(
    "post",
    "/v1/service-analytics/service-urgency-analysis",
    { body: baseQuery }
  );

  // Fetch composite risk scores
  const { data: riskData, isLoading: riskLoading, isError: riskError } = $api.useQuery(
    "post",
    "/v1/service-analytics/composite-risk-scores",
    { body: baseQuery }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const getRiskColor = (risk: string | number) => {
    const score = typeof risk === 'string' ? 
      risk.toLowerCase() === 'critical' ? 90 :
      risk.toLowerCase() === 'high' ? 70 :
      risk.toLowerCase() === 'medium' ? 50 : 30 : risk;
    
    if (score >= 80) return 'text-red-600 bg-red-50';
    if (score >= 60) return 'text-orange-600 bg-orange-50';
    if (score >= 40) return 'text-yellow-600 bg-yellow-50';
    return 'text-green-600 bg-green-50';
  };

  const getSeasonColor = (season: string) => {
    switch (season.toLowerCase()) {
      case 'winter': return 'text-blue-600 bg-blue-50';
      case 'spring': return 'text-green-600 bg-green-50';
      case 'summer': return 'text-orange-600 bg-orange-50';
      case 'fall': return 'text-amber-600 bg-amber-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const isLoading = summaryLoading || seasonalLoading || warrantyLoading || geographicLoading || urgencyLoading || riskLoading;
  const hasError = summaryError || seasonalError || warrantyError || geographicError || urgencyError || riskError;

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">Error loading temporal insights</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const summary = temporalSummary?.result;
  const seasonal = seasonalData?.result;
  const warranty = warrantyData?.result;
  // const _geographic = geographicData?.result || [];
  const urgency = urgencyData?.result;
  const risks = riskData?.result || [];

  // Prepare chart data
  const seasonalChartData = seasonal?.monthly_patterns?.map(pattern => ({
    month: pattern.month_name,
    cost: parseFloat(pattern.average_cost),
    variance: pattern.cost_variance,
    failures: pattern.failure_rate
  })) || [];

  const riskDistribution = [
    { name: 'Low Risk', value: risks.filter(r => r.risk_category === 'low').length, color: '#10B981' },
    { name: 'Medium Risk', value: risks.filter(r => r.risk_category === 'medium').length, color: '#F59E0B' },
    { name: 'High Risk', value: risks.filter(r => r.risk_category === 'high').length, color: '#EF4444' },
    { name: 'Critical Risk', value: risks.filter(r => r.risk_category === 'critical').length, color: '#DC2626' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3Icon className="h-6 w-6 text-blue-600" />
            Temporal & Operational Insights
          </h2>
          <p className="text-gray-600">
            Advanced temporal patterns and operational efficiency analysis
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-500">Seasonal Impact</div>
                <div className="text-2xl font-bold text-blue-600">
                  {formatCurrency(parseFloat(summary.seasonal_cost_impact))}
                </div>
                <div className="text-xs text-gray-500">Annual variation</div>
              </div>
              <SunIcon className="h-8 w-8 text-blue-600" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-500">Warranty Opportunity</div>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(parseFloat(summary.warranty_cost_opportunity))}
                </div>
                <div className="text-xs text-gray-500">Optimization potential</div>
              </div>
              <ShieldIcon className="h-8 w-8 text-green-600" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-500">Emergency Reduction</div>
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(parseFloat(summary.emergency_reduction_potential))}
                </div>
                <div className="text-xs text-gray-500">Prevention savings</div>
              </div>
              <AlertTriangleIcon className="h-8 w-8 text-orange-600" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-500">Geographic Savings</div>
                <div className="text-2xl font-bold text-purple-600">
                  {formatCurrency(parseFloat(summary.geographic_optimization_savings))}
                </div>
                <div className="text-xs text-gray-500">Route optimization</div>
              </div>
              <MapPinIcon className="h-8 w-8 text-purple-600" />
            </div>
          </Card>
        </div>
      )}

      {/* Efficiency Scores */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4">
            <div className="text-center">
              <div className="text-sm text-gray-500 mb-2">Temporal Efficiency</div>
              <div className={`text-3xl font-bold ${summary.temporal_efficiency_score >= 80 ? 'text-green-600' : 
                summary.temporal_efficiency_score >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                {summary.temporal_efficiency_score.toFixed(0)}%
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="h-2 rounded-full bg-blue-600" 
                  style={{ width: `${summary.temporal_efficiency_score}%` }}
                />
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-center">
              <div className="text-sm text-gray-500 mb-2">Operational Efficiency</div>
              <div className={`text-3xl font-bold ${summary.operational_efficiency_score >= 80 ? 'text-green-600' : 
                summary.operational_efficiency_score >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                {summary.operational_efficiency_score.toFixed(0)}%
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="h-2 rounded-full bg-green-600" 
                  style={{ width: `${summary.operational_efficiency_score}%` }}
                />
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-center">
              <div className="text-sm text-gray-500 mb-2">Cost Optimization</div>
              <div className={`text-3xl font-bold ${summary.cost_optimization_score >= 80 ? 'text-green-600' : 
                summary.cost_optimization_score >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                {summary.cost_optimization_score.toFixed(0)}%
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="h-2 rounded-full bg-purple-600" 
                  style={{ width: `${summary.cost_optimization_score}%` }}
                />
              </div>
            </div>
          </Card>
        </div>
      )}

      <Tabs defaultValue="seasonal" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="seasonal">🌤️ Seasonal</TabsTrigger>
          <TabsTrigger value="warranty">🛡️ Warranty</TabsTrigger>
          <TabsTrigger value="geographic">🗺️ Geographic</TabsTrigger>
          <TabsTrigger value="urgency">⚡ Urgency</TabsTrigger>
          <TabsTrigger value="risk">⚠️ Risk</TabsTrigger>
        </TabsList>

        <TabsContent value="seasonal" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Seasonal Analysis</h3>
            {seasonal ? (
              <div className="space-y-6">
                {/* Seasonal Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Cost Variation</div>
                      <div className="text-2xl font-bold">
                        {(seasonal.seasonal_cost_variation * 100).toFixed(1)}%
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Highest Cost Season</div>
                      <Badge className={getSeasonColor(seasonal.highest_cost_season)}>
                        {seasonal.highest_cost_season}
                      </Badge>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Weather Impact</div>
                      <div className="text-2xl font-bold text-blue-600">
                        {(seasonal.weather_impact_score * 100).toFixed(0)}%
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Monthly Pattern Chart */}
                <Card className="p-6">
                  <h4 className="text-lg font-semibold mb-4">Monthly Cost Patterns</h4>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={seasonalChartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip 
                          formatter={(value, name) => [
                            name === 'cost' ? formatCurrency(Number(value)) : `${Number(value).toFixed(1)}%`,
                            name === 'cost' ? 'Average Cost' : name === 'variance' ? 'Cost Variance' : 'Failure Rate'
                          ]}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="cost" 
                          stroke="#3B82F6" 
                          strokeWidth={2}
                          name="cost"
                        />
                        <Line 
                          type="monotone" 
                          dataKey="failures" 
                          stroke="#EF4444" 
                          strokeWidth={2}
                          name="failures"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </Card>

                {/* Recommendations */}
                <Card className="p-4">
                  <h4 className="text-lg font-semibold mb-3">Seasonal Recommendations</h4>
                  <div className="space-y-2">
                    {seasonal.recommended_prep_actions.map((action, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <CalendarIcon className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">{action}</span>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            ) : (
              <Card className="p-6">
                <div className="text-center text-gray-500">
                  No seasonal data available
                </div>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="warranty" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Warranty Analysis</h3>
            {warranty ? (
              <div className="space-y-6">
                {/* Warranty Summary */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Warranty Costs</div>
                      <div className="text-xl font-bold text-red-600">
                        {formatCurrency(parseFloat(warranty.warranty_costs))}
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Non-Warranty</div>
                      <div className="text-xl font-bold text-green-600">
                        {formatCurrency(parseFloat(warranty.non_warranty_costs))}
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Warranty %</div>
                      <div className="text-xl font-bold">
                        {warranty.warranty_percentage.toFixed(1)}%
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Efficiency Ratio</div>
                      <div className={`text-xl font-bold ${warranty.warranty_efficiency_ratio > 1.2 ? 'text-red-600' : 'text-green-600'}`}>
                        {warranty.warranty_efficiency_ratio.toFixed(2)}x
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Warranty Trend & High-Risk Models */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="p-4">
                    <h4 className="text-lg font-semibold mb-3">Warranty Trend</h4>
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUpIcon className={`h-5 w-5 ${
                        warranty.warranty_trend === 'increasing' ? 'text-red-600' :
                        warranty.warranty_trend === 'decreasing' ? 'text-green-600' : 'text-gray-600'
                      }`} />
                      <span className="font-medium">{warranty.warranty_trend}</span>
                    </div>
                    <div className="space-y-2">
                      {warranty.warranty_cost_drivers.map((driver, index) => (
                        <div key={index} className="text-sm text-gray-600">
                          • {driver}
                        </div>
                      ))}
                    </div>
                  </Card>

                  {warranty.high_warranty_models.length > 0 && (
                    <Card className="p-4">
                      <h4 className="text-lg font-semibold mb-3">High Warranty Models</h4>
                      <div className="space-y-2">
                        {warranty.high_warranty_models.slice(0, 5).map((model, index) => (
                          <div key={index} className="flex justify-between items-center">
                            <span className="text-sm font-medium">{String(model.model_name)}</span>
                            <div className="text-right">
                              <div className="text-sm text-red-600 font-bold">
                                {Number(model.warranty_rate).toFixed(1)}%
                              </div>
                              <div className="text-xs text-gray-500">
                                {formatCurrency(Number(model.warranty_costs))}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </Card>
                  )}
                </div>
              </div>
            ) : (
              <Card className="p-6">
                <div className="text-center text-gray-500">
                  No warranty data available
                </div>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="geographic" className="space-y-6">
          {/* Use Enhanced Geographic Analysis component instead of basic geographic data */}
          <EnhancedGeographicAnalysis />
        </TabsContent>

        <TabsContent value="urgency" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Service Urgency Analysis</h3>
            {urgency ? (
              <div className="space-y-6">
                {/* Urgency Summary */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Emergency Services</div>
                      <div className="text-2xl font-bold text-red-600">{urgency.emergency_services}</div>
                      <div className="text-xs text-gray-500">{urgency.emergency_percentage.toFixed(1)}% of total</div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Planned Services</div>
                      <div className="text-2xl font-bold text-green-600">{urgency.planned_services}</div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Cost Premium</div>
                      <div className="text-2xl font-bold text-orange-600">
                        {(urgency.emergency_cost_premium * 100).toFixed(0)}%
                      </div>
                    </div>
                  </Card>
                  <Card className="p-4">
                    <div className="text-center">
                      <div className="text-sm text-gray-500">Potential Savings</div>
                      <div className="text-2xl font-bold text-green-600">
                        {formatCurrency(parseFloat(urgency.potential_savings))}
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Response Times & Prevention */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="p-4">
                    <h4 className="text-lg font-semibold mb-3">Response Times</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Emergency Response:</span>
                        <span className="font-medium">{urgency.emergency_response_time.toFixed(1)} days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Planned Scheduling:</span>
                        <span className="font-medium">{urgency.planned_scheduling_efficiency.toFixed(1)} days</span>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-4">
                    <h4 className="text-lg font-semibold mb-3">Prevention Opportunity</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Preventable Emergencies:</span>
                        <span className="font-medium text-orange-600">{urgency.preventable_emergencies}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Prevention Savings:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(parseFloat(urgency.potential_savings))}
                        </span>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>
            ) : (
              <Card className="p-6">
                <div className="text-center text-gray-500">
                  No urgency data available
                </div>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="risk" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">Composite Risk Analysis</h3>
            {risks.length > 0 ? (
              <div className="space-y-6">
                {/* Risk Distribution */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="p-6">
                    <h4 className="text-lg font-semibold mb-4">Risk Distribution</h4>
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={riskDistribution}
                            cx="50%"
                            cy="50%"
                            innerRadius={40}
                            outerRadius={80}
                            dataKey="value"
                          >
                            {riskDistribution.map((entry, index) => (
                              <Cell key={index} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="flex justify-center gap-4 mt-4">
                      {riskDistribution.map((entry, index) => (
                        <div key={index} className="flex items-center gap-1">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: entry.color }}
                          />
                          <span className="text-xs">{entry.name}: {entry.value}</span>
                        </div>
                      ))}
                    </div>
                  </Card>

                  <Card className="p-6">
                    <h4 className="text-lg font-semibold mb-4">Top Risk Units</h4>
                    <div className="space-y-3">
                      {risks.slice(0, 5).map((risk, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">
                              {risk.structure_number || `Unit ${risk.structure_id}`}
                            </div>
                            <div className="text-sm text-gray-500">{risk.model_name}</div>
                          </div>
                          <div className="text-right">
                            <Badge className={getRiskColor(risk.composite_risk_score)}>
                              {risk.composite_risk_score.toFixed(0)}
                            </Badge>
                            <div className="text-xs text-gray-500">{risk.risk_category}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </div>

                {/* Critical Risk Units */}
                {risks.filter(r => r.risk_category === 'critical' || r.risk_category === 'high').length > 0 && (
                  <Card className="p-4">
                    <h4 className="text-lg font-semibold mb-4">Critical & High Risk Units</h4>
                    <div className="space-y-4">
                      {risks.filter(r => r.risk_category === 'critical' || r.risk_category === 'high').slice(0, 3).map((risk, index) => (
                        <div key={index} className="border rounded p-3">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h5 className="font-semibold">
                                {risk.structure_number || `Unit ${risk.structure_id}`}
                              </h5>
                              <div className="text-sm text-gray-600">{risk.customer_name} • {risk.model_name}</div>
                            </div>
                            <Badge className={getRiskColor(risk.composite_risk_score)}>
                              {risk.risk_category}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm mb-2">
                            <div>
                              <span className="text-gray-500">Cost Risk:</span>
                              <div className="font-medium">{risk.cost_risk_score.toFixed(0)}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">Failure Risk:</span>
                              <div className="font-medium">{risk.failure_risk_score.toFixed(0)}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">Operational:</span>
                              <div className="font-medium">{risk.operational_risk_score.toFixed(0)}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">Financial:</span>
                              <div className="font-medium">{risk.financial_risk_score.toFixed(0)}</div>
                            </div>
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-500">Immediate Actions:</span>
                            <ul className="list-disc list-inside mt-1">
                              {risk.immediate_actions.slice(0, 2).map((action, actionIndex) => (
                                <li key={actionIndex}>{action}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                )}
              </div>
            ) : (
              <Card className="p-6">
                <div className="text-center text-gray-500">
                  No risk data available
                </div>
              </Card>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TemporalInsightsDashboard;