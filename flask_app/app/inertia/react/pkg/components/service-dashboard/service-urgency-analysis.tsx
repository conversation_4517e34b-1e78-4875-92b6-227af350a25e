import React, { useState } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  AlertTriangleIcon,
  ClockIcon,
  CheckCircleIcon,
  TrendingDownIcon,
  CalendarIcon,
  DollarSignIcon,
  ZapIcon,
  ShieldIcon,
  TimerIcon,
  XCircleIcon
} from 'lucide-react';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";

const ServiceUrgencyAnalysis: React.FC = () => {
  const [selectedView, setSelectedView] = useState<'overview' | 'prevention' | 'efficiency'>('overview');

  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  // Fetch service urgency analysis
  const { data: urgencyData, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/service-urgency-analysis",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };


  const getEfficiencyRating = (responseTime: number) => {
    if (responseTime <= 1) return { label: 'Excellent', color: 'text-green-600' };
    if (responseTime <= 2) return { label: 'Good', color: 'text-blue-600' };
    if (responseTime <= 3) return { label: 'Fair', color: 'text-yellow-600' };
    return { label: 'Poor', color: 'text-red-600' };
  };

  const getPremiumSeverity = (premium: number) => {
    if (premium > 0.5) return { label: 'Critical', color: 'text-red-600', icon: AlertTriangleIcon };
    if (premium > 0.3) return { label: 'High', color: 'text-orange-600', icon: AlertTriangleIcon };
    if (premium > 0.15) return { label: 'Medium', color: 'text-yellow-600', icon: ClockIcon };
    return { label: 'Low', color: 'text-green-600', icon: CheckCircleIcon };
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Skeleton className="h-80 w-full" />
          <Skeleton className="h-80 w-full" />
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">Error loading service urgency analysis</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const urgency = urgencyData?.result;

  if (!urgency) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          No urgency data available. Ensure sufficient service history exists for analysis.
        </div>
      </Card>
    );
  }

  // Prepare chart data
  const serviceDistributionData = [
    { 
      name: 'Emergency', 
      value: urgency.emergency_services, 
      cost: parseFloat(urgency.emergency_total_cost),
      color: '#EF4444' 
    },
    { 
      name: 'Planned', 
      value: urgency.planned_services, 
      cost: parseFloat(urgency.planned_total_cost),
      color: '#10B981' 
    }
  ];

  const costComparisonData = [
    {
      type: 'Emergency',
      services: urgency.emergency_services,
      totalCost: parseFloat(urgency.emergency_total_cost),
      avgCost: urgency.emergency_services > 0 ? parseFloat(urgency.emergency_total_cost) / urgency.emergency_services : 0,
      responseTime: urgency.emergency_response_time
    },
    {
      type: 'Planned',
      services: urgency.planned_services,
      totalCost: parseFloat(urgency.planned_total_cost),
      avgCost: urgency.planned_services > 0 ? parseFloat(urgency.planned_total_cost) / urgency.planned_services : 0,
      responseTime: urgency.planned_scheduling_efficiency
    }
  ];

  const totalServices = urgency.emergency_services + urgency.planned_services;
  const emergencyAvgCost = urgency.emergency_services > 0 ? parseFloat(urgency.emergency_total_cost) / urgency.emergency_services : 0;
  const plannedAvgCost = urgency.planned_services > 0 ? parseFloat(urgency.planned_total_cost) / urgency.planned_services : 0;

  const premiumInfo = getPremiumSeverity(urgency.emergency_cost_premium);
  const emergencyEfficiency = getEfficiencyRating(urgency.emergency_response_time);
  const plannedEfficiency = getEfficiencyRating(urgency.planned_scheduling_efficiency);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <ZapIcon className="h-6 w-6 text-orange-600" />
            Service Urgency Analysis
          </h2>
          <p className="text-gray-600">
            Analyze emergency vs planned service patterns and cost impacts
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={selectedView === 'overview' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('overview')}
          >
            Overview
          </Button>
          <Button
            variant={selectedView === 'prevention' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('prevention')}
          >
            Prevention
          </Button>
          <Button
            variant={selectedView === 'efficiency' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedView('efficiency')}
          >
            Efficiency
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Emergency Services</div>
              <div className="text-2xl font-bold text-red-600">{urgency.emergency_services}</div>
              <div className="text-xs text-gray-500">
                {urgency.emergency_percentage.toFixed(1)}% of total
              </div>
            </div>
            <AlertTriangleIcon className="h-8 w-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Planned Services</div>
              <div className="text-2xl font-bold text-green-600">{urgency.planned_services}</div>
              <div className="text-xs text-gray-500">
                {(100 - urgency.emergency_percentage).toFixed(1)}% of total
              </div>
            </div>
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Cost Premium</div>
              <div className={`text-2xl font-bold ${premiumInfo.color}`}>
                {(urgency.emergency_cost_premium * 100).toFixed(0)}%
              </div>
              <div className="text-xs text-gray-500">Emergency vs planned</div>
            </div>
            <premiumInfo.icon className={`h-8 w-8 ${premiumInfo.color.replace('text-', 'text-')}`} />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Potential Savings</div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(parseFloat(urgency.potential_savings))}
              </div>
              <div className="text-xs text-gray-500">Prevention opportunity</div>
            </div>
            <DollarSignIcon className="h-8 w-8 text-green-600" />
          </div>
        </Card>
      </div>

      {selectedView === 'overview' && (
        <>
          {/* Service Distribution & Cost Impact */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Service Distribution Pie Chart */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Service Distribution</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={serviceDistributionData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                    >
                      {serviceDistributionData.map((entry, index) => (
                        <Cell key={index} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      formatter={(value, name) => [value, name === 'value' ? 'Services' : name]}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="text-center text-sm text-gray-600 mt-2">
                Total: {totalServices} services
              </div>
            </Card>

            {/* Cost Comparison Chart */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Cost Comparison</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={costComparisonData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis yAxisId="cost" orientation="left" />
                    <YAxis yAxisId="count" orientation="right" />
                    <Tooltip 
                      formatter={(value, name) => [
                        String(name).includes('Cost') ? formatCurrency(Number(value)) : value,
                        name === 'totalCost' ? 'Total Cost' :
                        name === 'avgCost' ? 'Average Cost' : 'Services'
                      ]}
                    />
                    <Bar 
                      yAxisId="cost"
                      dataKey="avgCost" 
                      fill="#3B82F6" 
                      name="avgCost"
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar 
                      yAxisId="count"
                      dataKey="services" 
                      fill="#10B981" 
                      name="services"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </div>

          {/* Detailed Comparison */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Emergency vs Planned Comparison</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Emergency Services */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <XCircleIcon className="h-5 w-5 text-red-600" />
                  <h4 className="font-semibold text-red-600">Emergency Services</h4>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Total Services:</span>
                    <span className="font-medium">{urgency.emergency_services}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Total Cost:</span>
                    <span className="font-medium text-red-600">
                      {formatCurrency(parseFloat(urgency.emergency_total_cost))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Average Cost:</span>
                    <span className="font-medium">{formatCurrency(emergencyAvgCost)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Response Time:</span>
                    <span className={`font-medium ${emergencyEfficiency.color}`}>
                      {urgency.emergency_response_time.toFixed(1)} days ({emergencyEfficiency.label})
                    </span>
                  </div>
                </div>
              </div>

              {/* Planned Services */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  <h4 className="font-semibold text-green-600">Planned Services</h4>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Total Services:</span>
                    <span className="font-medium">{urgency.planned_services}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Total Cost:</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(parseFloat(urgency.planned_total_cost))}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Average Cost:</span>
                    <span className="font-medium">{formatCurrency(plannedAvgCost)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Scheduling Efficiency:</span>
                    <span className={`font-medium ${plannedEfficiency.color}`}>
                      {urgency.planned_scheduling_efficiency.toFixed(1)} days ({plannedEfficiency.label})
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </>
      )}

      {selectedView === 'prevention' && (
        <>
          {/* Prevention Opportunities */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="p-6">
              <div className="text-center">
                <ShieldIcon className="h-12 w-12 text-green-600 mx-auto mb-3" />
                <div className="text-sm text-gray-500 mb-1">Preventable Emergencies</div>
                <div className="text-3xl font-bold text-orange-600">{urgency.preventable_emergencies}</div>
                <div className="text-xs text-gray-500 mt-1">
                  {urgency.emergency_services > 0 ? 
                    (urgency.preventable_emergencies / urgency.emergency_services * 100).toFixed(0) : 0}% of emergencies
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="text-center">
                <DollarSignIcon className="h-12 w-12 text-green-600 mx-auto mb-3" />
                <div className="text-sm text-gray-500 mb-1">Prevention Savings</div>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(parseFloat(urgency.potential_savings))}
                </div>
                <div className="text-xs text-gray-500 mt-1">Annual opportunity</div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="text-center">
                <TrendingDownIcon className="h-12 w-12 text-blue-600 mx-auto mb-3" />
                <div className="text-sm text-gray-500 mb-1">ROI Impact</div>
                <div className="text-2xl font-bold text-blue-600">
                  {parseFloat(urgency.potential_savings) > 0 ? 
                    ((parseFloat(urgency.potential_savings) / (parseFloat(urgency.emergency_total_cost) + parseFloat(urgency.planned_total_cost))) * 100).toFixed(1) : 0}%
                </div>
                <div className="text-xs text-gray-500 mt-1">Of total service costs</div>
              </div>
            </Card>
          </div>

          {/* Prevention Strategies */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <ShieldIcon className="h-5 w-5 text-green-600" />
              Prevention Strategies
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-3">Immediate Actions</h4>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-red-500 mt-2"></div>
                    <div>
                      <div className="font-medium text-sm">High-Risk Unit Monitoring</div>
                      <div className="text-xs text-gray-600">
                        Identify units with frequent emergencies and implement enhanced monitoring
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-orange-500 mt-2"></div>
                    <div>
                      <div className="font-medium text-sm">Predictive Maintenance</div>
                      <div className="text-xs text-gray-600">
                        Use historical patterns to schedule preventive services before failures
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-blue-500 mt-2"></div>
                    <div>
                      <div className="font-medium text-sm">Inventory Optimization</div>
                      <div className="text-xs text-gray-600">
                        Pre-position critical parts to reduce emergency procurement delays
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-3">Long-term Solutions</h4>
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-green-500 mt-2"></div>
                    <div>
                      <div className="font-medium text-sm">Service Interval Optimization</div>
                      <div className="text-xs text-gray-600">
                        Adjust maintenance schedules based on actual failure patterns
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-purple-500 mt-2"></div>
                    <div>
                      <div className="font-medium text-sm">Remote Monitoring</div>
                      <div className="text-xs text-gray-600">
                        Implement IoT sensors for real-time equipment health monitoring
                      </div>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 rounded-full bg-teal-500 mt-2"></div>
                    <div>
                      <div className="font-medium text-sm">Technician Training</div>
                      <div className="text-xs text-gray-600">
                        Enhance diagnostic skills to catch issues during routine maintenance
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Implementation Timeline */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Implementation Roadmap</h3>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="text-sm font-medium w-20">Month 1-3</div>
                <div className="flex-1 bg-red-100 rounded-full h-2">
                  <div className="bg-red-500 h-2 rounded-full w-1/4"></div>
                </div>
                <div className="text-sm text-gray-600">Implement high-risk unit monitoring</div>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-sm font-medium w-20">Month 4-6</div>
                <div className="flex-1 bg-orange-100 rounded-full h-2">
                  <div className="bg-orange-500 h-2 rounded-full w-2/4"></div>
                </div>
                <div className="text-sm text-gray-600">Deploy predictive maintenance protocols</div>
              </div>
              <div className="flex items-center gap-4">
                <div className="text-sm font-medium w-20">Month 7-12</div>
                <div className="flex-1 bg-green-100 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full w-3/4"></div>
                </div>
                <div className="text-sm text-gray-600">Full remote monitoring and optimization</div>
              </div>
            </div>
          </Card>
        </>
      )}

      {selectedView === 'efficiency' && (
        <>
          {/* Response Time Analysis */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <TimerIcon className="h-5 w-5 text-blue-600" />
                Response Time Efficiency
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 border rounded">
                  <div>
                    <div className="font-medium">Emergency Response</div>
                    <div className="text-sm text-gray-600">Average time to service</div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold">{urgency.emergency_response_time.toFixed(1)} days</div>
                    <Badge className={`${emergencyEfficiency.color} bg-opacity-10`}>
                      {emergencyEfficiency.label}
                    </Badge>
                  </div>
                </div>
                <div className="flex justify-between items-center p-3 border rounded">
                  <div>
                    <div className="font-medium">Planned Scheduling</div>
                    <div className="text-sm text-gray-600">Average scheduling lead time</div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold">{urgency.planned_scheduling_efficiency.toFixed(1)} days</div>
                    <Badge className={`${plannedEfficiency.color} bg-opacity-10`}>
                      {plannedEfficiency.label}
                    </Badge>
                  </div>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <DollarSignIcon className="h-5 w-5 text-green-600" />
                Cost Efficiency Analysis
              </h3>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-sm text-gray-500">Emergency Cost Premium</div>
                  <div className={`text-3xl font-bold ${premiumInfo.color}`}>
                    +{(urgency.emergency_cost_premium * 100).toFixed(0)}%
                  </div>
                  <Badge className={`${premiumInfo.color} bg-opacity-10 mt-2`}>
                    {premiumInfo.label} Impact
                  </Badge>
                </div>
                <div className="border-t pt-4">
                  <div className="text-sm text-gray-600 text-center">
                    Emergency services cost {formatCurrency(emergencyAvgCost - plannedAvgCost)} more on average
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Efficiency Recommendations */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Efficiency Improvement Opportunities</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <ClockIcon className="h-4 w-4 text-orange-600" />
                  Response Time
                </h4>
                <div className="space-y-2">
                  {urgency.emergency_response_time > 2 && (
                    <div className="text-sm">
                      • Improve emergency response protocols to achieve &lt;24hr response
                    </div>
                  )}
                  <div className="text-sm">
                    • Implement GPS tracking for faster technician dispatch
                  </div>
                  <div className="text-sm">
                    • Pre-position emergency kits in high-risk areas
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <DollarSignIcon className="h-4 w-4 text-green-600" />
                  Cost Optimization
                </h4>
                <div className="space-y-2">
                  {urgency.emergency_cost_premium > 0.3 && (
                    <div className="text-sm">
                      • Standardize emergency service procedures to reduce premium
                    </div>
                  )}
                  <div className="text-sm">
                    • Negotiate emergency labor rates with contractors
                  </div>
                  <div className="text-sm">
                    • Optimize parts inventory to reduce emergency procurement
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-blue-600" />
                  Scheduling
                </h4>
                <div className="space-y-2">
                  <div className="text-sm">
                    • Implement automated scheduling for routine maintenance
                  </div>
                  <div className="text-sm">
                    • Use predictive analytics to optimize service timing
                  </div>
                  <div className="text-sm">
                    • Balance workload to minimize emergency conflicts
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Performance Targets */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Performance Targets & Benchmarks</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">≤15%</div>
                <div className="text-sm text-gray-600">Emergency Service Rate</div>
                <div className={`text-xs mt-1 ${urgency.emergency_percentage <= 15 ? 'text-green-600' : 'text-red-600'}`}>
                  Current: {urgency.emergency_percentage.toFixed(1)}%
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">≤1.2x</div>
                <div className="text-sm text-gray-600">Cost Premium Target</div>
                <div className={`text-xs mt-1 ${urgency.emergency_cost_premium <= 1.2 ? 'text-green-600' : 'text-red-600'}`}>
                  Current: {(1 + urgency.emergency_cost_premium).toFixed(1)}x
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">≤24h</div>
                <div className="text-sm text-gray-600">Emergency Response</div>
                <div className={`text-xs mt-1 ${urgency.emergency_response_time <= 1 ? 'text-green-600' : 'text-red-600'}`}>
                  Current: {urgency.emergency_response_time.toFixed(1)} days
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">5-7d</div>
                <div className="text-sm text-gray-600">Planned Scheduling</div>
                <div className={`text-xs mt-1 ${urgency.planned_scheduling_efficiency >= 5 && urgency.planned_scheduling_efficiency <= 7 ? 'text-green-600' : 'text-yellow-600'}`}>
                  Current: {urgency.planned_scheduling_efficiency.toFixed(1)} days
                </div>
              </div>
            </div>
          </Card>
        </>
      )}
    </div>
  );
};

export default ServiceUrgencyAnalysis;