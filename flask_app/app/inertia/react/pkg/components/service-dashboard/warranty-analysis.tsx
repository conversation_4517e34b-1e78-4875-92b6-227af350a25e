import React, { useState } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  ShieldIcon,
  ShieldCheckIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  AlertTriangleIcon,
  ClockIcon,
  DollarSignIcon,
  BarChart3Icon,
  CheckCircleIcon,
  XCircleIcon
} from 'lucide-react';
import { XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";

const WarrantyAnalysis: React.FC = () => {
  const [selectedMetric, setSelectedMetric] = useState<'costs' | 'frequency' | 'efficiency'>('costs');

  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  // Fetch warranty analysis
  const { data: warrantyData, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/warranty-analysis",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend.toLowerCase()) {
      case 'increasing': return <TrendingUpIcon className="h-4 w-4 text-red-600" />;
      case 'decreasing': return <TrendingDownIcon className="h-4 w-4 text-green-600" />;
      default: return <BarChart3Icon className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend.toLowerCase()) {
      case 'increasing': return 'text-red-600 bg-red-50';
      case 'decreasing': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getEfficiencyColor = (ratio: number) => {
    if (ratio > 1.3) return 'text-red-600 bg-red-50';
    if (ratio > 1.1) return 'text-orange-600 bg-orange-50';
    if (ratio < 0.9) return 'text-green-600 bg-green-50';
    return 'text-gray-600 bg-gray-50';
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Skeleton className="h-80 w-full" />
          <Skeleton className="h-80 w-full" />
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">Error loading warranty analysis</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const warranty = warrantyData?.result;

  if (!warranty) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          No warranty data available. Ensure sufficient historical data exists for analysis.
        </div>
      </Card>
    );
  }

  // Prepare chart data
  const costComparisonData = [
    {
      type: 'Warranty',
      cost: parseFloat(warranty.warranty_costs),
      avgCost: parseFloat(warranty.warranty_avg_cost_per_service),
      color: '#EF4444'
    },
    {
      type: 'Non-Warranty',
      cost: parseFloat(warranty.non_warranty_costs),
      avgCost: parseFloat(warranty.non_warranty_avg_cost_per_service),
      color: '#10B981'
    }
  ];

  const pieChartData = [
    { name: 'Warranty', value: parseFloat(warranty.warranty_costs), color: '#EF4444' },
    { name: 'Non-Warranty', value: parseFloat(warranty.non_warranty_costs), color: '#10B981' }
  ];

  const ageFrequencyData = warranty.warranty_frequency_by_age.map(item => ({
    age: `${item.equipment_age}y`,
    frequency: item.warranty_frequency,
    services: item.total_services
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <ShieldIcon className="h-6 w-6 text-blue-600" />
            Warranty Analysis
          </h2>
          <p className="text-gray-600">
            Analyze warranty vs non-warranty service costs and efficiency
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={selectedMetric === 'costs' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMetric('costs')}
          >
            Costs
          </Button>
          <Button
            variant={selectedMetric === 'frequency' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMetric('frequency')}
          >
            Frequency
          </Button>
          <Button
            variant={selectedMetric === 'efficiency' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedMetric('efficiency')}
          >
            Efficiency
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Warranty Costs</div>
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(parseFloat(warranty.warranty_costs))}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {warranty.warranty_percentage.toFixed(1)}% of total
              </div>
            </div>
            <XCircleIcon className="h-8 w-8 text-red-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Non-Warranty Costs</div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(parseFloat(warranty.non_warranty_costs))}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {(100 - warranty.warranty_percentage).toFixed(1)}% of total
              </div>
            </div>
            <CheckCircleIcon className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Warranty Avg/Service</div>
              <div className="text-xl font-bold">
                {formatCurrency(parseFloat(warranty.warranty_avg_cost_per_service))}
              </div>
            </div>
            <DollarSignIcon className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Efficiency Ratio</div>
              <div className={`text-xl font-bold ${
                warranty.warranty_efficiency_ratio > 1.2 ? 'text-red-600' :
                warranty.warranty_efficiency_ratio < 0.9 ? 'text-green-600' : 'text-gray-600'
              }`}>
                {warranty.warranty_efficiency_ratio.toFixed(2)}x
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {warranty.warranty_efficiency_ratio > 1 ? 'Higher than' : 'Lower than'} non-warranty
              </div>
            </div>
            <BarChart3Icon className="h-8 w-8 text-purple-600" />
          </div>
        </Card>
      </div>

      {/* Warranty Trend & Cost Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Cost Distribution Pie Chart */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Cost Distribution</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                >
                  {pieChartData.map((entry, index) => (
                    <Cell key={index} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>

        {/* Warranty Trend */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            {getTrendIcon(warranty.warranty_trend)}
            Warranty Trend
          </h3>
          <div className="space-y-4">
            <div className="text-center">
              <Badge className={getTrendColor(warranty.warranty_trend)}>
                {warranty.warranty_trend.charAt(0).toUpperCase() + warranty.warranty_trend.slice(1)}
              </Badge>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Cost Drivers:</h4>
              <div className="space-y-2">
                {warranty.warranty_cost_drivers.map((driver, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <AlertTriangleIcon className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{driver}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Cost Comparison Chart */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Warranty vs Non-Warranty Comparison</h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={costComparisonData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="type" />
              <YAxis yAxisId="total" orientation="left" />
              <YAxis yAxisId="avg" orientation="right" />
              <Tooltip 
                formatter={(value, name) => [
                  formatCurrency(Number(value)),
                  name === 'cost' ? 'Total Cost' : 'Average Cost per Service'
                ]}
              />
              <Bar 
                yAxisId="total"
                dataKey="cost" 
                fill="#3B82F6" 
                name="cost"
                radius={[4, 4, 0, 0]}
              />
              <Bar 
                yAxisId="avg"
                dataKey="avgCost" 
                fill="#10B981" 
                name="avgCost"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Age-Based Analysis & High Warranty Models */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Warranty Frequency by Equipment Age */}
        {ageFrequencyData.length > 0 && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Warranty Frequency by Equipment Age</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={ageFrequencyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="age" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'frequency' ? `${Number(value).toFixed(1)}%` : value,
                      name === 'frequency' ? 'Warranty Frequency' : 'Total Services'
                    ]}
                  />
                  <Bar 
                    dataKey="frequency" 
                    fill="#F59E0B" 
                    name="frequency"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>
        )}

        {/* High Warranty Models */}
        {warranty.high_warranty_models.length > 0 && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">High Warranty Models</h3>
            <div className="space-y-3">
              {warranty.high_warranty_models.slice(0, 8).map((model, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded">
                  <div>
                    <div className="font-medium">{String(model.model_name)}</div>
                    <div className="text-sm text-gray-500">
                      {String(model.total_services)} total services
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-red-600">
                      {Number(model.warranty_rate).toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatCurrency(Number(model.warranty_costs))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}
      </div>

      {/* Efficiency Analysis */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Efficiency Analysis</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-sm text-gray-500 mb-2">Cost Efficiency</div>
            <div className={`text-3xl font-bold ${getEfficiencyColor(warranty.warranty_efficiency_ratio).split(' ')[0]}`}>
              {warranty.warranty_efficiency_ratio > 1 ? 
                `+${((warranty.warranty_efficiency_ratio - 1) * 100).toFixed(0)}%` :
                `-${((1 - warranty.warranty_efficiency_ratio) * 100).toFixed(0)}%`
              }
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {warranty.warranty_efficiency_ratio > 1 ? 'Higher' : 'Lower'} than non-warranty
            </div>
          </div>

          <div className="text-center">
            <div className="text-sm text-gray-500 mb-2">Warranty Proportion</div>
            <div className={`text-3xl font-bold ${
              warranty.warranty_percentage > 25 ? 'text-red-600' :
              warranty.warranty_percentage > 15 ? 'text-orange-600' :
              warranty.warranty_percentage > 10 ? 'text-yellow-600' : 'text-green-600'
            }`}>
              {warranty.warranty_percentage.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Of total service costs
            </div>
          </div>

          <div className="text-center">
            <div className="text-sm text-gray-500 mb-2">Quality Score</div>
            <div className={`text-3xl font-bold ${
              warranty.warranty_percentage > 20 ? 'text-red-600' :
              warranty.warranty_percentage > 15 ? 'text-orange-600' :
              warranty.warranty_percentage > 10 ? 'text-yellow-600' : 'text-green-600'
            }`}>
              {warranty.warranty_percentage > 20 ? 'Poor' :
               warranty.warranty_percentage > 15 ? 'Fair' :
               warranty.warranty_percentage > 10 ? 'Good' : 'Excellent'}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Based on warranty rate
            </div>
          </div>
        </div>
      </Card>

      {/* Recommendations */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <ShieldCheckIcon className="h-5 w-5 text-green-600" />
          Warranty Optimization Recommendations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-3">Quality Improvements</h4>
            <div className="space-y-2">
              {warranty.warranty_percentage > 20 && (
                <div className="flex items-start gap-2">
                  <AlertTriangleIcon className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">High warranty rate indicates quality issues - review manufacturing or installation processes</span>
                </div>
              )}
              {warranty.warranty_efficiency_ratio > 1.3 && (
                <div className="flex items-start gap-2">
                  <AlertTriangleIcon className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">Warranty work is significantly more expensive - standardize warranty procedures</span>
                </div>
              )}
              {warranty.high_warranty_models.length > 0 && (
                <div className="flex items-start gap-2">
                  <ClockIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">Focus on high-warranty models: {warranty.high_warranty_models.slice(0, 2).map(m => m.model_name).join(', ')}</span>
                </div>
              )}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Cost Optimization</h4>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <DollarSignIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm">Potential annual savings: {formatCurrency(parseFloat(warranty.warranty_costs) * 0.15)}</span>
              </div>
              {warranty.warranty_trend === 'increasing' && (
                <div className="flex items-start gap-2">
                  <TrendingUpIcon className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">Investigate increasing warranty trend - implement preventive measures</span>
                </div>
              )}
              <div className="flex items-start gap-2">
                <BarChart3Icon className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm">Track warranty resolution time to improve efficiency</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default WarrantyAnalysis;