import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Label as LabelComponent } from "@/components/ui/label";
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate, zDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { LineChart, Line, XAxis, YAxis, CartesianGrid } from "recharts";
import { Payload } from "recharts/types/component/DefaultLegendContent";

interface CurrencyLabelFormatterProps {
  value: number | string;
  label: string;
  payload: Payload;
}

function CurrencyLabelFormatter({
  value,
  label,
  payload,
}: CurrencyLabelFormatterProps) {
  return (
    <div className="border-input flex w-full items-center gap-1 border-t pt-1">
      {payload.color && (
        <span
          className="inline-block h-3 w-3 rounded-sm"
          style={{ backgroundColor: payload.color }}
        />
      )}
      <LabelComponent.Numeric value={value} variant="dollars" />
      <span className="text-muted-foreground w-auto flex-grow text-end text-sm">
        {label}
      </span>
    </div>
  );
}

const formatCurrency = (value: number): string => {
  if (value >= 1000000000) {
    return `$${(value / 1000000000).toFixed(1)}B`;
  } else if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `$${(value / 1000).toFixed(0)}K`;
  }
  return `$${value.toLocaleString()}`;
};

function dateTickMonthFormatter(value: string | number): string {
  const date = zDate.parse(value);
  if (date) {
    return date.toLocaleDateString("en-US", {
      month: "short",
    });
  }
  return "";
}

function dateLegendFormatter(value: string | number): string {
  const date = zDate.parse(value);
  if (date) {
    return date.toLocaleDateString("en-US", {
      month: "long",
      year: "numeric",
    });
  }
  return "";
}

const chartConfig = {
  totalCost: {
    label: "Total Cost",
    color: "var(--chart-1)",
  },
  laborCost: {
    label: "Labor Cost",
    color: "var(--chart-2)",
  },
  partsCost: {
    label: "Parts Cost",
    color: "var(--chart-3)",
  },
} satisfies ChartConfig;

const CostTrendsChart = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector(
    (state) => state.context.service_types,
  );
  const from = useServiceCostSelector(
    (state) => state.context.service_date_from,
  );
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector(
    (state) => state.context.technicians,
  );
  const part_categories = useServiceCostSelector(
    (state) => state.context.part_categories,
  );
  const include_ijack = useServiceCostSelector(
    (state) => state.context.include_ijack,
  );
  const include_sales_parts = useServiceCostSelector(
    (state) => state.context.include_sales_parts,
  );
  const selected_years = useServiceCostSelector(
    (state) => state.context.selected_years,
  );

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/cost-trends",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
      },
    },
  );

  const chartData =
    data?.result?.map((item) => ({
      month: item.month,
      totalCost: Number(item.total_cost),
      laborCost: Number(item.labor_cost),
      partsCost: Number(item.parts_cost),
      orderCount: item.work_order_count,
    })) || [];

  return (
    <Card className="col-span-1">
      <CardHeader className="pb-2">
        <CardTitle>Monthly Cost Trends</CardTitle>
        <CardDescription>
          Monthly service cost trends showing total, labor, and parts costs
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : isError ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">Error loading chart data</p>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">No data available</p>
          </div>
        ) : (
          <ChartContainer
            className="aspect-auto h-64 w-full"
            config={chartConfig}
          >
            <LineChart
              data={chartData}
              margin={{
                top: 0,
                right: 10,
                left: 10,
                bottom: 0,
              }}
            >
              <ChartLegend
                className="flex-wrap pb-0"
                content={
                  <ChartLegendContent
                    className="pt-1"
                    filterFunc={(item) => {
                      const key = item.dataKey as keyof typeof chartConfig;
                      const total = chartData.reduce((acc, d) => {
                        return acc + (d[key] || 0);
                      }, 0);
                      return total !== 0;
                    }}
                  />
                }
              />
              <CartesianGrid vertical={true} strokeDasharray="3 3" />
              <ChartTooltip
                content={
                  <ChartTooltipContent
                    filterFunc={(item) => Number(item.value) !== 0}
                    className="w-[250px]"
                    formatter={(value, name, item) => {
                      return (
                        <CurrencyLabelFormatter
                          value={Number(value)}
                          label={`${name}`}
                          payload={item as unknown as Payload}
                        />
                      );
                    }}
                    labelFormatter={dateLegendFormatter}
                  />
                }
              />
              <XAxis
                dataKey="month"
                tickLine={false}
                tickFormatter={dateTickMonthFormatter}
                tickMargin={10}
                axisLine={true}
              />
              <YAxis
                tickFormatter={formatCurrency}
                tickLine={false}
                axisLine={false}
                domain={[0, "dataMax"]}
              />
              <Line
                type="monotone"
                dataKey="totalCost"
                stroke="var(--color-totalCost)"
                strokeWidth={2}
                dot={false}
                name="Total Cost"
              />
              <Line
                type="monotone"
                dataKey="laborCost"
                stroke="var(--color-laborCost)"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={false}
                name="Labor Cost"
              />
              <Line
                type="monotone"
                dataKey="partsCost"
                stroke="var(--color-partsCost)"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={false}
                name="Parts Cost"
              />
            </LineChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
};

export { CostTrendsChart };
