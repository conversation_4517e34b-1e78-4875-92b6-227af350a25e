import React, { useState } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  MapPinIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  RouteIcon,
  ThermometerIcon,
  DollarSignIcon,
  BarChart3Icon,
  NavigationIcon,
  CarIcon
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ScatterChart, Scatter } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";

const GeographicAnalysis: React.FC = () => {
  const [sortBy, setSortBy] = useState<'cost' | 'services' | 'efficiency'>('cost');
  const [viewMode, setViewMode] = useState<'list' | 'chart'>('chart');

  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );

  // Fetch geographic analysis
  const { data: geographicData, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/geographic-analysis",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };


  const getCostVarianceColor = (ratio: number) => {
    if (ratio > 1.2) return 'text-red-600 bg-red-50';
    if (ratio > 1.1) return 'text-orange-600 bg-orange-50';
    if (ratio < 0.9) return 'text-green-600 bg-green-50';
    return 'text-gray-600 bg-gray-50';
  };

  const getClimateIcon = (zone: string) => {
    if (!zone) return <ThermometerIcon className="h-4 w-4 text-gray-600" />;
    
    const lowerZone = zone.toLowerCase();
    if (lowerZone.includes('cold')) return <ThermometerIcon className="h-4 w-4 text-blue-600" />;
    if (lowerZone.includes('hot') || lowerZone.includes('arid')) return <ThermometerIcon className="h-4 w-4 text-red-600" />;
    if (lowerZone.includes('moderate')) return <ThermometerIcon className="h-4 w-4 text-green-600" />;
    return <ThermometerIcon className="h-4 w-4 text-gray-600" />;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-80 w-full" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">Error loading geographic analysis</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const geographic = geographicData?.result || [];

  if (geographic.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          No geographic data available. Ensure sufficient regional service data exists for analysis.
        </div>
      </Card>
    );
  }

  // Calculate summary statistics
  const totalServices = geographic.reduce((sum, region) => sum + region.total_services, 0);
  const avgCostAcrossRegions = geographic.reduce((sum, region) => 
    sum + parseFloat(region.average_cost_per_service), 0
  ) / geographic.length;

  // Sort data based on selected criteria
  const sortedRegions = [...geographic].sort((a, b) => {
    switch (sortBy) {
      case 'cost':
        return parseFloat(b.average_cost_per_service) - parseFloat(a.average_cost_per_service);
      case 'services':
        return b.total_services - a.total_services;
      case 'efficiency':
        return b.travel_efficiency_score - a.travel_efficiency_score;
      default:
        return 0;
    }
  });

  // Prepare chart data
  const chartData = sortedRegions.slice(0, 12).map(region => ({
    region: region.region_name.length > 10 ? 
      region.region_name.substring(0, 10) + '...' : 
      region.region_name,
    fullName: region.region_name,
    cost: parseFloat(region.average_cost_per_service),
    services: region.total_services,
    efficiency: region.travel_efficiency_score,
    variance: region.cost_vs_national_average,
    density: region.service_density
  }));

  // Efficiency vs Cost scatter data
  const scatterData = geographic.map(region => ({
    x: region.travel_efficiency_score,
    y: parseFloat(region.average_cost_per_service),
    name: region.region_name,
    services: region.total_services
  }));

  // Group by country
  const countrySummary = geographic.reduce((acc, region) => {
    const country = region.country_name;
    if (!acc[country]) {
      acc[country] = {
        regions: 0,
        totalServices: 0,
        avgCost: 0,
        avgEfficiency: 0
      };
    }
    acc[country].regions += 1;
    acc[country].totalServices += region.total_services;
    acc[country].avgCost += parseFloat(region.average_cost_per_service);
    acc[country].avgEfficiency += region.travel_efficiency_score;
    return acc;
  }, {} as Record<string, any>);

  // Calculate averages
  Object.keys(countrySummary).forEach(country => {
    const data = countrySummary[country];
    data.avgCost = data.avgCost / data.regions;
    data.avgEfficiency = data.avgEfficiency / data.regions;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <MapPinIcon className="h-6 w-6 text-blue-600" />
            Geographic Cost Analysis
          </h2>
          <p className="text-gray-600">
            Analyze regional cost variations and travel efficiency
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={viewMode === 'chart' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('chart')}
          >
            Chart View
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            List View
          </Button>
        </div>
      </div>

      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Total Regions</div>
              <div className="text-2xl font-bold text-blue-600">{geographic.length}</div>
              <div className="text-xs text-gray-500">{Object.keys(countrySummary).length} countries</div>
            </div>
            <MapPinIcon className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Total Services</div>
              <div className="text-2xl font-bold text-green-600">{totalServices}</div>
              <div className="text-xs text-gray-500">Across all regions</div>
            </div>
            <BarChart3Icon className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Avg Cost/Region</div>
              <div className="text-2xl font-bold text-purple-600">
                {formatCurrency(avgCostAcrossRegions)}
              </div>
              <div className="text-xs text-gray-500">Regional average</div>
            </div>
            <DollarSignIcon className="h-8 w-8 text-purple-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Cost Range</div>
              <div className="text-xl font-bold text-orange-600">
                {((Math.max(...geographic.map(r => parseFloat(r.average_cost_per_service))) / 
                   Math.min(...geographic.map(r => parseFloat(r.average_cost_per_service))) - 1) * 100).toFixed(0)}%
              </div>
              <div className="text-xs text-gray-500">High vs low region</div>
            </div>
            <TrendingUpIcon className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Sort Controls */}
      <div className="flex gap-2">
        <span className="text-sm font-medium self-center">Sort by:</span>
        <Button
          variant={sortBy === 'cost' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSortBy('cost')}
        >
          Cost
        </Button>
        <Button
          variant={sortBy === 'services' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSortBy('services')}
        >
          Service Volume
        </Button>
        <Button
          variant={sortBy === 'efficiency' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSortBy('efficiency')}
        >
          Efficiency
        </Button>
      </div>

      {viewMode === 'chart' ? (
        <>
          {/* Regional Cost Chart */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Regional Cost Comparison</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="region" 
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'cost' ? formatCurrency(Number(value)) : value,
                      name === 'cost' ? 'Average Cost' : 
                      name === 'services' ? 'Total Services' : 'Efficiency Score'
                    ]}
                    labelFormatter={(label) => {
                      const region = chartData.find(r => r.region === label);
                      return region ? region.fullName : label;
                    }}
                  />
                  <Bar 
                    dataKey="cost" 
                    fill="#3B82F6" 
                    name="cost"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>

          {/* Efficiency vs Cost Scatter Plot */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Travel Efficiency vs Service Cost</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart data={scatterData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    type="number" 
                    dataKey="x" 
                    name="Travel Efficiency" 
                    unit="%" 
                    domain={[0, 100]}
                  />
                  <YAxis 
                    type="number" 
                    dataKey="y" 
                    name="Average Cost"
                  />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'y' ? formatCurrency(Number(value)) : `${Number(value).toFixed(1)}%`,
                      name === 'y' ? 'Average Cost' : 'Travel Efficiency'
                    ]}
                    labelFormatter={(_, payload) => {
                      return payload?.[0]?.payload?.name || 'Region';
                    }}
                  />
                  <Scatter dataKey="y" fill="#8884d8" />
                </ScatterChart>
              </ResponsiveContainer>
            </div>
            <div className="text-sm text-gray-600 mt-2">
              Higher efficiency (right) and lower cost (bottom) is optimal. Bubble size represents service volume.
            </div>
          </Card>
        </>
      ) : null}

      {/* Regional Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sortedRegions.slice(0, viewMode === 'list' ? 20 : 9).map((region, index) => (
          <Card key={index} className="p-4">
            <div className="space-y-3">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-semibold flex items-center gap-2">
                    <MapPinIcon className="h-4 w-4 text-blue-600" />
                    {region.region_name}
                  </h4>
                  <div className="text-sm text-gray-600">{region.country_name}</div>
                </div>
                <Badge className={getCostVarianceColor(region.cost_vs_national_average)}>
                  {region.cost_vs_national_average > 1.1 ? 'Above Avg' :
                   region.cost_vs_national_average < 0.9 ? 'Below Avg' : 'Average'}
                </Badge>
              </div>

              {/* Metrics Grid */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-500">Avg Cost:</span>
                  <div className="font-medium">
                    {formatCurrency(parseFloat(region.average_cost_per_service))}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Services:</span>
                  <div className="font-medium">{region.total_services}</div>
                </div>
                <div>
                  <span className="text-gray-500">vs National:</span>
                  <div className={`font-medium ${
                    region.cost_vs_national_average > 1.1 ? 'text-red-600' :
                    region.cost_vs_national_average < 0.9 ? 'text-green-600' : 'text-gray-600'
                  }`}>
                    {(region.cost_vs_national_average * 100).toFixed(0)}%
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Efficiency:</span>
                  <div className={`font-medium ${
                    region.travel_efficiency_score >= 80 ? 'text-green-600' :
                    region.travel_efficiency_score >= 60 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {region.travel_efficiency_score.toFixed(0)}%
                  </div>
                </div>
              </div>

              {/* Service Density */}
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500">Service Density:</span>
                <span className="font-medium">{region.service_density.toFixed(1)} per unit</span>
              </div>

              {/* Climate Zone */}
              {region.climate_zone && (
                <div className="flex items-center gap-2">
                  {getClimateIcon(region.climate_zone)}
                  <Badge variant="outline" className="text-xs">
                    {region.climate_zone}
                  </Badge>
                </div>
              )}

              {/* Cost Drivers */}
              {region.cost_drivers.length > 0 && (
                <div>
                  <div className="text-xs text-gray-500 mb-1">Key Issues:</div>
                  <div className="space-y-1">
                    {region.cost_drivers.slice(0, 2).map((driver, driverIndex) => (
                      <div key={driverIndex} className="text-xs text-gray-700">
                        • {driver}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Efficiency Indicator */}
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${
                    region.travel_efficiency_score >= 80 ? 'bg-green-600' :
                    region.travel_efficiency_score >= 60 ? 'bg-yellow-600' :
                    region.travel_efficiency_score >= 40 ? 'bg-orange-600' : 'bg-red-600'
                  }`}
                  style={{ width: `${region.travel_efficiency_score}%` }}
                />
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Country Summary */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Country-Level Summary</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2">Country</th>
                <th className="text-right p-2">Regions</th>
                <th className="text-right p-2">Total Services</th>
                <th className="text-right p-2">Avg Cost</th>
                <th className="text-right p-2">Avg Efficiency</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(countrySummary)
                .sort(([,a], [,b]) => b.totalServices - a.totalServices)
                .map(([country, data]) => (
                <tr key={country} className="border-b hover:bg-gray-50">
                  <td className="p-2 font-medium">{country}</td>
                  <td className="p-2 text-right">{data.regions}</td>
                  <td className="p-2 text-right">{data.totalServices}</td>
                  <td className="p-2 text-right">{formatCurrency(data.avgCost)}</td>
                  <td className="p-2 text-right">
                    <span className={`font-medium ${
                      data.avgEfficiency >= 80 ? 'text-green-600' :
                      data.avgEfficiency >= 60 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {data.avgEfficiency.toFixed(0)}%
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Optimization Recommendations */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <RouteIcon className="h-5 w-5 text-green-600" />
          Geographic Optimization Opportunities
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-3">Cost Reduction Opportunities</h4>
            <div className="space-y-2">
              {sortedRegions.filter(r => r.cost_vs_national_average > 1.2).slice(0, 3).map((region, index) => (
                <div key={index} className="flex items-start gap-2">
                  <TrendingDownIcon className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">
                    <strong>{region.region_name}</strong>: {((region.cost_vs_national_average - 1) * 100).toFixed(0)}% above average
                  </span>
                </div>
              ))}
              <div className="flex items-start gap-2">
                <DollarSignIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Potential savings: {formatCurrency(
                    sortedRegions
                      .filter(r => r.cost_vs_national_average > 1.2)
                      .reduce((sum, r) => sum + (parseFloat(r.average_cost_per_service) - avgCostAcrossRegions) * r.total_services, 0)
                  )}
                </span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Route Efficiency Improvements</h4>
            <div className="space-y-2">
              {sortedRegions.filter(r => r.travel_efficiency_score < 60).slice(0, 3).map((region, index) => (
                <div key={index} className="flex items-start gap-2">
                  <NavigationIcon className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">
                    <strong>{region.region_name}</strong>: {region.travel_efficiency_score.toFixed(0)}% efficiency
                  </span>
                </div>
              ))}
              <div className="flex items-start gap-2">
                <CarIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Optimize technician routing and service scheduling in low-efficiency regions
                </span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default GeographicAnalysis;