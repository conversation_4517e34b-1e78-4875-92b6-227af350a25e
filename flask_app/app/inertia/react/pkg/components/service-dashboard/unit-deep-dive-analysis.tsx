import React from 'react';
import { Card } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { ArrowLeftIcon, AlertTriangleIcon, CheckCircleIcon } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";



interface UnitDeepDiveAnalysisProps {
  structureId: number;
  onBack: () => void;
  filters?: Record<string, unknown>; // Keep for backwards compatibility but not used
}

const UnitDeepDiveAnalysis: React.FC<UnitDeepDiveAnalysisProps> = ({
  structureId,
  onBack,
}) => {
  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/unit-deep-dive-analysis",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        structure_id: structureId, // Add the specific structure_id to the filters
      },
    },
    {
      enabled: structureId !== null && structureId !== undefined,
    }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const formatDays = (days: number) => {
    if (days < 30) return `${Math.round(days)} days`;
    if (days < 365) return `${Math.round(days / 30)} months`;
    return `${Math.round(days / 365)} years`;
  };

  const getBenchmarkColor = (ratio: number) => {
    if (ratio <= 0.8) return 'text-green-600';
    if (ratio <= 1.2) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getBenchmarkIcon = (ratio: number) => {
    if (ratio <= 0.8) return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
    if (ratio <= 1.2) return <AlertTriangleIcon className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangleIcon className="h-4 w-4 text-red-600" />;
  };


  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <Button onClick={onBack} variant="outline" size="sm">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
        <Skeleton className="h-64 w-full" />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }

  if (isError || !data?.result) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 text-lg mb-4">
          Error loading unit analysis data
        </div>
        <p className="text-gray-500 text-sm mb-4">
          Please try refreshing the page or contact support if the issue persists.
        </p>
        <Button onClick={onBack} variant="outline">
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      </div>
    );
  }

  const unitData = data.result;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button onClick={onBack} variant="outline" size="sm">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              Unit Analysis: {unitData.structure_number || `Structure ${unitData.structure_id}`}
            </h1>
            <p className="text-gray-600">
              {unitData.customer_name} • {unitData.model_name} • {unitData.power_unit}
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-500">Analysis Period</div>
          <div className="font-medium">
            {new Date(unitData.analysis_period_start).toLocaleDateString()} - {new Date(unitData.analysis_period_end).toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-sm text-gray-500">Total Lifetime Cost</div>
          <div className="text-2xl font-bold">{formatCurrency(parseFloat(String(unitData.total_lifetime_cost)))}</div>
          <div className="text-sm text-gray-600">
            {formatCurrency(parseFloat(String(unitData.cost_per_month)))}/month average
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-gray-500">Total Services</div>
          <div className="text-2xl font-bold">{unitData.total_services}</div>
          <div className="text-sm text-gray-600">
            {unitData.service_patterns.service_frequency_per_month.toFixed(1)} per month
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-gray-500">Parts vs Labor Ratio</div>
          <div className="text-2xl font-bold">{unitData.parts_vs_labor_ratio.toFixed(1)}:1</div>
          <div className="text-sm text-gray-600">
            {unitData.parts_vs_labor_ratio > 2 ? 'Parts-heavy' : 'Labor-heavy'}
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-gray-500">Service Interval</div>
          <div className="text-2xl font-bold">
            {formatDays(unitData.service_patterns.avg_service_interval)}
          </div>
          <div className="text-sm text-gray-600">
            {unitData.service_patterns.repeat_service_rate.toFixed(1)}% repeat rate
          </div>
        </Card>
      </div>

      {/* Cost Trend Analysis */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Cost Trend Analysis</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={unitData.cost_trend_data || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis tickFormatter={(value) => `$${value.toLocaleString()}`} />
              <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Cost']} />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke="#8884d8" 
                strokeWidth={2}
                dot={{ r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cost Drivers */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Top Cost Drivers</h3>
          <div className="space-y-3">
            {unitData.top_cost_drivers.map((driver, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <div className="font-medium">{driver.driver_name}</div>
                  <div className="text-sm text-gray-500">
                    {driver.service_count} services • {driver.driver_type}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold">{formatCurrency(parseFloat(String(driver.total_cost)))}</div>
                  <div className="text-sm text-gray-500">
                    {driver.percentage_of_total.toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Service Patterns */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Service Patterns</h3>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Average Interval</span>
              <span className="font-medium">
                {formatDays(unitData.service_patterns.avg_service_interval)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Monthly Frequency</span>
              <span className="font-medium">
                {unitData.service_patterns.service_frequency_per_month.toFixed(1)} services
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Repeat Service Rate</span>
              <span className="font-medium">
                {unitData.service_patterns.repeat_service_rate.toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Seasonal Pattern</span>
              <Badge variant={unitData.service_patterns.seasonal_pattern_detected ? "destructive" : "default"}>
                {unitData.service_patterns.seasonal_pattern_detected ? 'Detected' : 'None'}
              </Badge>
            </div>
            <div>
              <span className="text-gray-600">Common Services</span>
              <div className="mt-1 flex flex-wrap gap-1">
                {(unitData.service_patterns.most_common_service_types || []).map((type, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {String(type)}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Failure Analysis */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Failure Analysis</h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-3">Common Failure Parts</h4>
            <div className="space-y-2">
              {unitData.failure_analysis.common_failure_parts.map((part, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm">{String(part.part_name)}</span>
                  <div className="text-right">
                    <div className="text-sm font-medium">{String(part.failure_count)}x</div>
                    <div className="text-xs text-gray-500">
                      {formatCurrency(parseFloat(String(part.cost_impact)))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h4 className="font-medium mb-3">Failure Metrics</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">MTBF</span>
                <span className="font-medium">
                  {unitData.failure_analysis.mtbf_days ? formatDays(unitData.failure_analysis.mtbf_days) : 'N/A'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Warranty Failures</span>
                <span className="font-medium">
                  {unitData.failure_analysis.warranty_failure_rate.toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="mt-4">
              <h4 className="font-medium mb-2">Failure Modes</h4>
              <div className="flex flex-wrap gap-1">
                {(unitData.failure_analysis.failure_modes || []).map((mode, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {String(mode)}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Benchmark Comparison */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Performance Benchmarking</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-sm text-gray-500 mb-2">vs Model Type Average</div>
            <div className={`text-2xl font-bold flex items-center justify-center gap-2 ${getBenchmarkColor(unitData.benchmark_comparison.vs_model_type_average)}`}>
              {getBenchmarkIcon(unitData.benchmark_comparison.vs_model_type_average)}
              {unitData.benchmark_comparison.vs_model_type_average.toFixed(1)}x
            </div>
            <div className="text-sm text-gray-600">
              Rank #{unitData.benchmark_comparison.model_type_rank}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 mb-2">vs Customer Fleet</div>
            <div className={`text-2xl font-bold flex items-center justify-center gap-2 ${getBenchmarkColor(unitData.benchmark_comparison.vs_customer_fleet_average)}`}>
              {getBenchmarkIcon(unitData.benchmark_comparison.vs_customer_fleet_average)}
              {unitData.benchmark_comparison.vs_customer_fleet_average.toFixed(1)}x
            </div>
            <div className="text-sm text-gray-600">
              Rank #{unitData.benchmark_comparison.customer_fleet_rank}
            </div>
          </div>
          <div className="text-center">
            <div className="text-sm text-gray-500 mb-2">vs Top Performers</div>
            <div className={`text-2xl font-bold flex items-center justify-center gap-2 ${getBenchmarkColor(unitData.benchmark_comparison.vs_top_performers)}`}>
              {getBenchmarkIcon(unitData.benchmark_comparison.vs_top_performers)}
              {unitData.benchmark_comparison.vs_top_performers.toFixed(1)}x
            </div>
            <div className="text-sm text-gray-600">
              Industry comparison
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default UnitDeepDiveAnalysis;