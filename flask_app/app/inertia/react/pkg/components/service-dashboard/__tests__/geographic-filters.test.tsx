import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ServiceCostStoreContext, store } from '@/stores/service-costs/hooks';
import {
  CountryFilter,
  RegionFilter,
  GeographicAdvancedFilters,
  GeographicFilterSection,
} from '../geographic-filters';
import { $api } from '@/api/web-api';

// Mock the API
vi.mock('@/api/web-api', () => ({
  $api: {
    useQuery: vi.fn(),
  },
}));

// Test utilities
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ServiceCostStoreContext.Provider value={store}>
        {children}
      </ServiceCostStoreContext.Provider>
    </QueryClientProvider>
  );
};

describe('CountryFilter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state', () => {
    (vi.mocked($api.useQuery) as any).mockReturnValue({
      data: undefined,
      isLoading: true,
      isSuccess: false,
      isError: false,
    } as any);

    render(<CountryFilter />, { wrapper: createWrapper() });

    expect(screen.getByText('Countries:')).toBeInTheDocument();
    expect(screen.getByTestId('skeleton')).toBeInTheDocument();
  });

  it('renders country options from geographic analysis', async () => {
    const mockData = {
      result: {
        regions: [
          {
            country_name: 'Canada',
            total_services: 150,
          },
          {
            country_name: 'United States',
            total_services: 85,
          },
          {
            country_name: 'Canada',
            total_services: 50,
          },
        ],
      },
    };

    (vi.mocked($api.useQuery) as any).mockReturnValue({
      data: mockData,
      isLoading: false,
      isSuccess: true,
      isError: false,
    } as any);

    render(<CountryFilter />, { wrapper: createWrapper() });

    // Open filter menu
    const button = screen.getByRole('button', { name: /all countries/i });
    await userEvent.click(button);

    // Check that countries are displayed with service counts
    await waitFor(() => {
      expect(screen.getByText('Canada (200 services)')).toBeInTheDocument();
      expect(screen.getByText('United States (85 services)')).toBeInTheDocument();
    });
  });

  it('updates store when countries are selected', async () => {
    const mockData = {
      result: {
        regions: [
          { country_name: 'Canada', total_services: 100 },
        ],
      },
    };

    (vi.mocked($api.useQuery) as any).mockReturnValue({
      data: mockData,
      isLoading: false,
      isSuccess: true,
      isError: false,
    } as any);

    render(<CountryFilter />, { wrapper: createWrapper() });

    const button = screen.getByRole('button', { name: /all countries/i });
    await userEvent.click(button);

    // Select Canada
    const canadaOption = await screen.findByText('Canada (100 services)');
    await userEvent.click(canadaOption);

    // Check that store was updated
    const state = store.getSnapshot();
    expect(state.context.country_codes).toContain('CA');
  });

  it('handles error state', () => {
    (vi.mocked($api.useQuery) as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isSuccess: false,
      isError: true,
    } as any);

    render(<CountryFilter />, { wrapper: createWrapper() });

    expect(screen.getByText('Error loading countries')).toBeInTheDocument();
  });
});

describe('RegionFilter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('filters regions based on selected countries', async () => {
    const mockData = {
      result: {
        regions: [
          {
            region_name: 'Alberta',
            country_name: 'Canada',
            total_services: 100,
          },
          {
            region_name: 'Texas',
            country_name: 'United States',
            total_services: 50,
          },
          {
            region_name: 'British Columbia',
            country_name: 'Canada',
            total_services: 75,
          },
        ],
      },
    };

    (vi.mocked($api.useQuery) as any).mockReturnValue({
      data: mockData,
      isLoading: false,
      isSuccess: true,
      isError: false,
    } as any);

    // Set country filter to Canada
    store.send({ type: 'applyFilter', country_codes: ['CA'] });

    render(<RegionFilter />, { wrapper: createWrapper() });

    const button = screen.getByRole('button', { name: /all regions/i });
    await userEvent.click(button);

    // Should only show Canadian regions
    await waitFor(() => {
      expect(screen.getByText('Alberta, Canada (100)')).toBeInTheDocument();
      expect(screen.getByText('British Columbia, Canada (75)')).toBeInTheDocument();
      expect(screen.queryByText(/Texas/)).not.toBeInTheDocument();
    });
  });

  it('shows message when no regions match country filter', () => {
    (vi.mocked($api.useQuery) as any).mockReturnValue({
      data: { result: { regions: [] } },
      isLoading: false,
      isSuccess: true,
      isError: false,
    } as any);

    // Set country filter
    store.send({ type: 'applyFilter', country_codes: ['XX'] });

    render(<RegionFilter />, { wrapper: createWrapper() });

    expect(screen.getByText('No regions in selected countries')).toBeInTheDocument();
  });
});

describe('GeographicAdvancedFilters', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    store.send({ type: 'clearFilters' });
  });

  it('renders all advanced filter controls', () => {
    render(<GeographicAdvancedFilters />, { wrapper: createWrapper() });

    expect(screen.getByText('Advanced Geographic Filters:')).toBeInTheDocument();
    expect(screen.getByLabelText('Include unmapped regions')).toBeInTheDocument();
    expect(screen.getByLabelText('Minimum services in region')).toBeInTheDocument();
    expect(screen.getByLabelText('Max distance from city (km)')).toBeInTheDocument();
  });

  it('updates include_unmapped when checkbox is toggled', async () => {
    render(<GeographicAdvancedFilters />, { wrapper: createWrapper() });

    const checkbox = screen.getByLabelText('Include unmapped regions');
    expect(checkbox).toBeChecked(); // Default is true

    await userEvent.click(checkbox);

    const state = store.getSnapshot();
    expect(state.context.include_unmapped).toBe(false);
  });

  it('debounces numeric input for min services', async () => {
    vi.useFakeTimers();
    
    render(<GeographicAdvancedFilters />, { wrapper: createWrapper() });

    const input = screen.getByLabelText('Minimum services in region');
    await userEvent.type(input, '10');

    // Store should not be updated immediately
    let state = store.getSnapshot();
    expect(state.context.min_services).toBeNull();

    // Fast forward past debounce delay
    vi.advanceTimersByTime(600);

    // Now store should be updated
    state = store.getSnapshot();
    expect(state.context.min_services).toBe(10);

    vi.useRealTimers();
  });

  it('handles invalid numeric input gracefully', async () => {
    render(<GeographicAdvancedFilters />, { wrapper: createWrapper() });

    const input = screen.getByLabelText('Max distance from city (km)');
    await userEvent.type(input, 'abc');

    // Should not crash and should not update store with invalid value
    const state = store.getSnapshot();
    expect(state.context.max_distance_from_city).toBeNull();
  });
});

describe('GeographicFilterSection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with advanced filters hidden by default', () => {
    render(<GeographicFilterSection />, { wrapper: createWrapper() });

    expect(screen.getByText('Geographic Filters')).toBeInTheDocument();
    expect(screen.getByText('Show Advanced')).toBeInTheDocument();
    expect(screen.queryByText('Advanced Geographic Filters:')).not.toBeInTheDocument();
  });

  it('toggles advanced filters visibility', async () => {
    render(<GeographicFilterSection />, { wrapper: createWrapper() });

    const toggleButton = screen.getByText('Show Advanced');
    await userEvent.click(toggleButton);

    expect(screen.getByText('Hide Advanced')).toBeInTheDocument();
    expect(screen.getByText('Advanced Geographic Filters:')).toBeInTheDocument();
  });

  it('renders both country and region filters', () => {
    (vi.mocked($api.useQuery) as any).mockReturnValue({
      data: { result: { regions: [] } },
      isLoading: false,
      isSuccess: true,
      isError: false,
    } as any);

    render(<GeographicFilterSection />, { wrapper: createWrapper() });

    expect(screen.getByText('Countries:')).toBeInTheDocument();
    expect(screen.getByText('Regions/Provinces:')).toBeInTheDocument();
  });

  it('respects showGeographic prop from parent', () => {
    render(<GeographicFilterSection />, { wrapper: createWrapper() });

    // Advanced section should be initially hidden
    expect(screen.queryByText('Advanced Geographic Filters:')).not.toBeInTheDocument();
  });
});

describe('Geographic Filters Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    store.send({ type: 'clearFilters' });
  });

  it('coordinates between country and region filters', async () => {
    const mockData = {
      result: {
        regions: [
          {
            region_name: 'Alberta',
            country_name: 'Canada',
            total_services: 100,
          },
          {
            region_name: 'Texas',
            country_name: 'United States',
            total_services: 50,
          },
        ],
      },
    };

    (vi.mocked($api.useQuery) as any).mockReturnValue({
      data: mockData,
      isLoading: false,
      isSuccess: true,
      isError: false,
    } as any);

    render(<GeographicFilterSection />, { wrapper: createWrapper() });

    // Select Canada in country filter
    const countryButton = screen.getAllByRole('button', { name: /all countries/i })[0];
    await userEvent.click(countryButton);
    
    const canadaOption = await screen.findByText('Canada (100 services)');
    await userEvent.click(canadaOption);

    // Open region filter
    const regionButton = screen.getAllByRole('button', { name: /all regions/i })[0];
    await userEvent.click(regionButton);

    // Should only show Canadian regions
    await waitFor(() => {
      expect(screen.getByText('Alberta, Canada (100)')).toBeInTheDocument();
      expect(screen.queryByText(/Texas/)).not.toBeInTheDocument();
    });
  });

  it('properly resets all geographic filters', async () => {
    // Set some filters
    store.send({
      type: 'applyFilter',
      country_codes: ['CA'],
      region_names: ['Alberta'],
      min_services: 10,
      max_distance_from_city: 100,
      include_unmapped: false,
    });

    // Verify filters are set
    let state = store.getSnapshot();
    expect(state.context.country_codes).toEqual(['CA']);
    expect(state.context.min_services).toBe(10);

    // Clear filters
    store.send({ type: 'clearFilters' });

    // Verify all geographic filters are reset
    state = store.getSnapshot();
    expect(state.context.country_codes).toEqual([]);
    expect(state.context.region_names).toEqual([]);
    expect(state.context.min_services).toBeNull();
    expect(state.context.max_distance_from_city).toBeNull();
    expect(state.context.include_unmapped).toBe(true);
  });
});