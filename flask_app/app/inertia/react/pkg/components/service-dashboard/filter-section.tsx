import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, FilterIcon } from 'lucide-react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import {
  CustomerFilter,
  ModelTypeFilter,
  UnitTypeFilter,
  ServiceTypeFilter,
  TechnicianFilter,
  PartCategoryFilter,
  ServiceDateFilter,
  ResetFilters,
} from './filters';
import { GeographicFilterSection } from './geographic-filters';

interface FilterSectionProps {
  showGeographic?: boolean;
}

export const FilterSection: React.FC<FilterSectionProps> = ({ showGeographic = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showGeographicFilters, setShowGeographicFilters] = useState(showGeographic);

  return (
    <Card className="mb-6 p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <FilterIcon className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold">Filters</h3>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowGeographicFilters(!showGeographicFilters)}
            className="text-sm"
          >
            {showGeographicFilters ? 'Hide' : 'Show'} Geographic
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <>
                <ChevronUpIcon className="h-4 w-4 mr-1" />
                Hide
              </>
            ) : (
              <>
                <ChevronDownIcon className="h-4 w-4 mr-1" />
                Show
              </>
            )}
          </Button>
          <ResetFilters />
        </div>
      </div>

      {isExpanded && (
        <>
          {/* Standard Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-4">
            <CustomerFilter />
            <ServiceTypeFilter />
            <ModelTypeFilter />
            <UnitTypeFilter />
            <TechnicianFilter />
            <PartCategoryFilter />
            <ServiceDateFilter />
          </div>

          {/* Geographic Filters */}
          {showGeographicFilters && (
            <div className="mt-4 pt-4 border-t">
              <GeographicFilterSection />
            </div>
          )}
        </>
      )}
    </Card>
  );
};

export default FilterSection;