import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { AgGridReact } from 'ag-grid-react';
import { ColDef, ValueFormatterParams } from 'ag-grid-community';
import { themeQuartz } from 'ag-grid-community';
import { Badge } from "../ui/badge";

const formatCurrency = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(Number(params.value));
};

const formatPercentage = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return `${(Number(params.value) * 100).toFixed(1)}%`;
};

const formatNumber = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return Number(params.value).toFixed(1);
};

const TechnicianPerformanceDashboard = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/technician-efficiency-metrics",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
      },
    },
  );

  const columnDefs: ColDef[] = [
    {
      field: 'technician_name',
      headerName: 'Technician',
      headerTooltip: 'Field technician name. Performance metrics are calculated based on completed work orders.',
      pinned: 'left',
      width: 180,
      cellStyle: { fontWeight: 'bold' },
    },
    {
      field: 'efficiency_rank',
      headerName: 'Rank',
      headerTooltip: 'Overall efficiency ranking among all technicians. Based on labor efficiency ratio (lower rank = better performance).',
      width: 80,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        const rank = params.value;
        const badgeVariant = rank <= 3 ? 'default' : rank <= 6 ? 'secondary' : 'outline';
        return <Badge variant={badgeVariant}>#{rank}</Badge>;
      }
    },
    {
      field: 'labor_efficiency_ratio',
      headerName: 'Labor Efficiency',
      headerTooltip: 'Efficiency of labor hours vs expected time. ≥85% = Excellent, 75-84% = Good, <75% = Needs Improvement.',
      valueFormatter: formatPercentage,
      width: 140,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        const value = params.value;
        const percentage = (value * 100).toFixed(1);
        const color = value >= 0.85 ? 'text-green-600' : value >= 0.75 ? 'text-orange-600' : 'text-red-600';
        return <span className={color}>{percentage}%</span>;
      }
    },
    {
      field: 'cost_per_service',
      headerName: 'Cost/Service',
      headerTooltip: 'Average cost per service call including labor and materials (CAD). Lower values indicate more cost-effective work.',
      valueFormatter: formatCurrency,
      width: 120,
      type: 'numericColumn',
      sort: 'asc'
    },
    {
      field: 'avg_service_duration',
      headerName: 'Avg Duration',
      headerTooltip: 'Average time spent per service call in hours. Shorter durations may indicate faster problem resolution.',
      valueFormatter: formatNumber,
      width: 120,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        return `${params.value?.toFixed(1) || '0'} hrs`;
      }
    },
    {
      field: 'services_per_day',
      headerName: 'Services/Day',
      headerTooltip: 'Average number of service calls completed per working day. Higher values indicate productivity.',
      valueFormatter: formatNumber,
      width: 120,
      type: 'numericColumn'
    },
    {
      field: 'repeat_service_rate',
      headerName: 'Repeat Rate',
      headerTooltip: 'Percentage of services requiring return visits within 30 days. ≤10% = Excellent, 10-20% = Acceptable, >20% = High.',
      valueFormatter: formatPercentage,
      width: 120,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        const value = params.value;
        const percentage = (value * 100).toFixed(1);
        const color = value <= 0.10 ? 'text-green-600' : value <= 0.20 ? 'text-orange-600' : 'text-red-600';
        return <span className={color}>{percentage}%</span>;
      }
    },
    {
      field: 'total_services',
      headerName: '# Services',
      headerTooltip: 'Total number of completed service work orders for this technician during the selected period.',
      width: 100,
      type: 'numericColumn'
    },
    {
      field: 'total_billable_hours',
      headerName: 'Total Hours',
      headerTooltip: 'Total billable labor hours recorded for this technician across all service calls.',
      valueFormatter: formatNumber,
      width: 110,
      type: 'numericColumn'
    },
    {
      field: 'vs_team_average',
      headerName: 'vs Team Avg',
      headerTooltip: 'Cost performance vs team average. Negative values = below average cost (better), positive = above average cost.',
      width: 120,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        const value = params.value;
        const percentage = ((value - 1) * 100).toFixed(0);
        const isGood = value < 1; // Lower cost is better
        const color = isGood ? 'text-green-600' : 'text-red-600';
        const prefix = value >= 1 ? '+' : '';
        return <span className={color}>{prefix}{percentage}%</span>;
      }
    },
  ];

  const defaultColDef = {
    sortable: true,
    filter: true,
    resizable: true,
  };

  return (
    <Card className="col-span-full">
      <CardHeader className="pb-2">
        <CardTitle>Technician Performance Analysis</CardTitle>
        <CardDescription>
          Performance metrics and efficiency rankings for field technicians
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : isError ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">Error loading technician data</p>
          </div>
        ) : !data?.result || data.result.length === 0 ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">No technician performance data found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
              <div className="rounded-lg border bg-blue-50 p-4 text-center">
                <div className="text-sm text-blue-600 font-medium">Total Technicians</div>
                <div className="text-2xl font-bold text-blue-700">{data.result.length}</div>
              </div>
              <div className="rounded-lg border bg-green-50 p-4 text-center">
                <div className="text-sm text-green-600 font-medium">Top Performer</div>
                <div className="text-lg font-bold text-green-700">{data.result[0]?.technician_name || 'N/A'}</div>
              </div>
              <div className="rounded-lg border bg-orange-50 p-4 text-center">
                <div className="text-sm text-orange-600 font-medium">Avg Efficiency</div>
                <div className="text-2xl font-bold text-orange-700">
                  {data.result.length > 0 
                    ? (data.result.reduce((sum, t) => sum + t.labor_efficiency_ratio, 0) / data.result.length * 100).toFixed(1)
                    : 0}%
                </div>
              </div>
              <div className="rounded-lg border bg-purple-50 p-4 text-center">
                <div className="text-sm text-purple-600 font-medium">Total Services</div>
                <div className="text-2xl font-bold text-purple-700">
                  {data.result.reduce((sum, t) => sum + t.total_services, 0).toLocaleString()}
                </div>
              </div>
            </div>

            {/* Performance Table */}
            <div className="h-[500px] w-full">
              <AgGridReact
                theme={themeQuartz}
                rowModelType="clientSide"
                rowData={data.result}
                columnDefs={columnDefs}
                defaultColDef={defaultColDef}
                pagination={true}
                paginationPageSize={20}
                paginationPageSizeSelector={[10, 20, 50]}
                domLayout="normal"
                suppressMenuHide={true}
                enableCellTextSelection={true}
                animateRows={true}
              />
            </div>

            {/* Performance Legend */}
            <div className="rounded-lg border bg-gray-50 p-4">
              <h4 className="font-medium mb-2">Performance Indicators</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Labor Efficiency:</span>
                  <div className="mt-1">
                    <span className="text-green-600">≥85%</span> Excellent | 
                    <span className="text-orange-600 ml-1">75-84%</span> Good | 
                    <span className="text-red-600 ml-1">&lt;75%</span> Needs Improvement
                  </div>
                </div>
                <div>
                  <span className="font-medium">Repeat Rate:</span>
                  <div className="mt-1">
                    <span className="text-green-600">≤10%</span> Excellent | 
                    <span className="text-orange-600 ml-1">10-20%</span> Acceptable | 
                    <span className="text-red-600 ml-1">&gt;20%</span> High
                  </div>
                </div>
                <div>
                  <span className="font-medium">vs Team Avg:</span>
                  <div className="mt-1">
                    <span className="text-green-600">Negative</span> Better than average | 
                    <span className="text-red-600 ml-1">Positive</span> Above average cost
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { TechnicianPerformanceDashboard };