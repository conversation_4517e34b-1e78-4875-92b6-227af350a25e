import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import { useServiceCostSelector, useServiceCostStore } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { AgGridReact } from 'ag-grid-react';
import { ColDef, ValueFormatterParams } from 'ag-grid-community';
import { themeQuartz } from 'ag-grid-community';
import { Badge } from "../ui/badge";
import { AlertTriangle, XCircle, AlertCircle } from "lucide-react";

const formatCurrency = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(Number(params.value));
};

const formatNumber = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return Number(params.value).toFixed(1);
};

interface OutlierAlertsTableProps {
  onUnitSelect?: (structureId: number) => void;
}

const OutlierAlertsTable = ({ onUnitSelect }: OutlierAlertsTableProps) => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );
  const store = useServiceCostStore();

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/basic-outlier-detection",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );

  const columnDefs: ColDef[] = [
    {
      field: 'severity',
      headerName: 'Priority',
      headerTooltip: 'Alert severity level based on statistical deviation. Critical = >3σ, High = >2.5σ, Medium = >2σ',
      width: 130,
      cellRenderer: (params: any) => {
        const severity = params.value as string;
        const severityConfig = {
          critical: { variant: 'destructive', icon: XCircle, color: 'text-red-600' },
          high: { variant: 'secondary', icon: AlertTriangle, color: 'text-orange-600' },
          medium: { variant: 'outline', icon: AlertCircle, color: 'text-yellow-600' },
          low: { variant: 'outline', icon: AlertCircle, color: 'text-blue-600' }
        } as const;
        const config = severityConfig[severity as keyof typeof severityConfig] || { variant: 'outline', icon: AlertCircle, color: 'text-gray-600' };
        
        const Icon = config.icon;
        return (
          <Badge variant={config.variant as any} className="flex items-center gap-1">
            <Icon className="h-3 w-3" />
            {severity.toUpperCase()}
          </Badge>
        );
      }
    },
    {
      field: 'structure_number',
      headerName: 'Structure',
      headerTooltip: 'Equipment structure number. Click to view detailed work order history and identify root causes.',
      width: 120,
      cellStyle: { fontWeight: 'bold' },
      cellRenderer: (params: any) => {
        return (
          <div 
            className="cursor-pointer text-blue-600 hover:text-blue-800 hover:underline"
            onClick={() => {
              store.send({ type: 'selectStructure', structureId: params.data.structure_id });
              if (onUnitSelect) {
                onUnitSelect(params.data.structure_id);
              }
            }}
            title="Click to view detailed unit analysis"
          >
            {params.value || `ID: ${params.data.structure_id}`}
          </div>
        );
      }
    },
    {
      field: 'power_unit_str',
      headerName: 'Power Unit',
      headerTooltip: 'Power unit identifier for this equipment.',
      width: 120,
    },
    {
      field: 'customer_name',
      headerName: 'Customer',
      headerTooltip: 'Customer who owns this problematic equipment. May indicate operational or environmental factors.',
      width: 150,
    },
    {
      field: 'model_name',
      headerName: 'Model',
      headerTooltip: 'Equipment model type. Some models may be more prone to certain issues than others.',
      width: 120,
    },
    {
      field: 'outlier_type',
      headerName: 'Issue Type',
      headerTooltip: 'Type of problem detected: High Cost = expensive to service, High Freq = serviced too often',
      width: 120,
      cellRenderer: (params: any) => {
        const type = params.value as string;
        const typeConfig = {
          cost: { label: 'High Cost', color: 'text-red-600 bg-red-50' },
          frequency: { label: 'High Freq', color: 'text-orange-600 bg-orange-50' },
          efficiency: { label: 'Low Efficiency', color: 'text-yellow-600 bg-yellow-50' }
        } as const;
        const config = typeConfig[type as keyof typeof typeConfig] || { label: type, color: 'text-gray-600 bg-gray-50' };
        
        return (
          <span className={`px-2 py-1 rounded text-xs font-medium ${config.color}`}>
            {config.label}
          </span>
        );
      }
    },
    {
      field: 'z_score',
      headerName: 'Z-Score',
      headerTooltip: 'Statistical deviation from average (σ = standard deviations). Higher values = more extreme outliers.',
      width: 110,
      type: 'numericColumn',
      valueFormatter: formatNumber,
      cellRenderer: (params: any) => {
        const value = Math.abs(params.value);
        const color = value >= 3 ? 'text-red-600' : value >= 2.5 ? 'text-orange-600' : 'text-yellow-600';
        return <span className={color}>{value.toFixed(1)}σ</span>;
      }
    },
    {
      field: 'current_cost_per_service',
      headerName: 'Current Cost',
      headerTooltip: 'This unit\'s average cost per service visit. Compare with benchmark to see overage.',
      valueFormatter: formatCurrency,
      width: 130,
      type: 'numericColumn'
    },
    {
      field: 'benchmark_cost_per_service',
      headerName: 'Benchmark',
      headerTooltip: 'Average cost per service across all similar equipment. Used as baseline for comparison.',
      valueFormatter: formatCurrency,
      width: 120,
      type: 'numericColumn'
    },
    {
      field: 'current_service_frequency',
      headerName: 'Frequency',
      headerTooltip: 'How often this unit requires service (services per month). Higher frequency may indicate problems.',
      width: 100,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        return `${params.value?.toFixed(1) || '0'}/mo`;
      }
    },
    {
      field: 'annual_excess_cost',
      headerName: 'Annual Impact',
      headerTooltip: 'Estimated additional cost per year if current patterns continue. Helps prioritize which units to address first.',
      valueFormatter: formatCurrency,
      width: 130,
      type: 'numericColumn',
      sort: 'desc'
    },
    {
      field: 'recommendation',
      headerName: 'Recommendation',
      headerTooltip: 'Suggested action based on the type and severity of the outlier pattern detected.',
      width: 300,
      wrapText: true,
      autoHeight: true,
      cellStyle: { fontSize: '12px', lineHeight: '1.3' }
    },
  ];

  const defaultColDef = {
    sortable: true,
    filter: true,
    resizable: true,
  };

  // Calculate summary stats
  const summaryStats = data?.result ? {
    totalOutliers: data.result.length,
    criticalOutliers: data.result.filter(o => o.severity === 'critical').length,
    highOutliers: data.result.filter(o => o.severity === 'high').length,
    totalAnnualImpact: data.result.reduce((sum, o) => sum + Number(o.annual_excess_cost), 0),
    costOutliers: data.result.filter(o => o.outlier_type === 'cost').length,
    frequencyOutliers: data.result.filter(o => o.outlier_type === 'frequency').length,
  } : null;

  return (
    <Card className="col-span-full">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-600" />
          Problem Unit Detection
        </CardTitle>
        <CardDescription>
          Statistical outlier analysis identifying units requiring attention
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : isError ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">Error loading outlier detection data</p>
          </div>
        ) : !data?.result || data.result.length === 0 ? (
          <div className="flex h-64 items-center justify-center">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-green-500 mx-auto mb-2" />
              <p className="text-lg font-medium text-green-600">No Problem Units Detected</p>
              <p className="text-sm text-gray-500">All units are performing within normal parameters</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Alert Summary */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-6">
              <div className="rounded-lg border bg-red-50 p-4 text-center">
                <div className="text-sm text-red-600 font-medium">Critical</div>
                <div className="text-2xl font-bold text-red-700">{summaryStats?.criticalOutliers || 0}</div>
              </div>
              <div className="rounded-lg border bg-orange-50 p-4 text-center">
                <div className="text-sm text-orange-600 font-medium">High Priority</div>
                <div className="text-2xl font-bold text-orange-700">{summaryStats?.highOutliers || 0}</div>
              </div>
              <div className="rounded-lg border bg-blue-50 p-4 text-center">
                <div className="text-sm text-blue-600 font-medium">Total Issues</div>
                <div className="text-2xl font-bold text-blue-700">{summaryStats?.totalOutliers || 0}</div>
              </div>
              <div className="rounded-lg border bg-purple-50 p-4 text-center">
                <div className="text-sm text-purple-600 font-medium">Cost Issues</div>
                <div className="text-2xl font-bold text-purple-700">{summaryStats?.costOutliers || 0}</div>
              </div>
              <div className="rounded-lg border bg-teal-50 p-4 text-center">
                <div className="text-sm text-teal-600 font-medium">Frequency Issues</div>
                <div className="text-2xl font-bold text-teal-700">{summaryStats?.frequencyOutliers || 0}</div>
              </div>
              <div className="rounded-lg border bg-green-50 p-4 text-center">
                <div className="text-sm text-green-600 font-medium">Annual Impact</div>
                <div className="text-lg font-bold text-green-700">
                  {new Intl.NumberFormat('en-CA', { 
                    style: 'currency', 
                    currency: 'CAD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(summaryStats?.totalAnnualImpact || 0)}
                </div>
              </div>
            </div>

            {/* Outlier Detection Table */}
            <div className="h-[500px] w-full">
              <AgGridReact
                theme={themeQuartz}
                rowModelType="clientSide"
                rowData={data.result}
                columnDefs={columnDefs}
                defaultColDef={defaultColDef}
                pagination={true}
                paginationPageSize={15}
                paginationPageSizeSelector={[10, 15, 25]}
                domLayout="normal"
                suppressMenuHide={true}
                enableCellTextSelection={true}
                animateRows={true}
              />
            </div>

            {/* Analysis Legend */}
            <div className="rounded-lg border bg-gray-50 p-4">
              <h4 className="font-medium mb-2">Analysis Legend</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Z-Score:</span>
                  <div className="mt-1">
                    Measures how many standard deviations a unit is from the average. 
                    Higher values indicate more extreme outliers.
                  </div>
                </div>
                <div>
                  <span className="font-medium">Issue Types:</span>
                  <div className="mt-1">
                    <span className="text-red-600">Cost:</span> Unusually expensive to service |
                    <span className="text-orange-600 ml-1">Frequency:</span> Serviced too often
                  </div>
                </div>
                <div>
                  <span className="font-medium">Annual Impact:</span>
                  <div className="mt-1">
                    Estimated additional cost per year if current patterns continue
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { OutlierAlertsTable };