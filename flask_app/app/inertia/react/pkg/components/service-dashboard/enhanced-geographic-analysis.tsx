import React, { useState } from 'react';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { 
  MapPinIcon,
  TrendingUpIcon,
  GlobeIcon,
  BarChart3Icon,
  NavigationIcon,
  CarIcon,
  DatabaseIcon,
  MapIcon,
  TargetIcon,
  AlertCircleIcon
} from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Scatter<PERSON>hart, Scatter, PieChart, Pie, Cell } from 'recharts';
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import type { components } from "@/types/web-api.gen";

type GeographicAnalysis = components["schemas"]["GeographicAnalysis"];

const EnhancedGeographicAnalysis: React.FC = () => {
  const [sortBy, setSortBy] = useState<'cost' | 'services' | 'coverage'>('services');
  const [viewMode, setViewMode] = useState<'chart' | 'list' | 'map'>('chart');

  // Use the same filter pattern as other components
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  // Geographic filters
  const country_codes = useServiceCostSelector((state) => state.context.country_codes);
  const region_names = useServiceCostSelector((state) => state.context.region_names);
  const min_services = useServiceCostSelector((state) => state.context.min_services);
  const max_distance_from_city = useServiceCostSelector((state) => state.context.max_distance_from_city);
  const include_unmapped = useServiceCostSelector((state) => state.context.include_unmapped);

  // Fetch enhanced geographic analysis
  const { data: response, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/geographic/analysis",
    {
      body: {
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        customers,
        models,
        unit_types,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        // Geographic filters
        country_codes,
        region_names,
        min_services,
        max_distance_from_city,
        include_unmapped,
      } as any,
    }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD',
    }).format(amount);
  };

  const getClusteringMethodIcon = (method: string) => {
    switch (method) {
      case 'database':
        return <DatabaseIcon className="h-4 w-4 text-green-600" />;
      case 'major_city':
        return <MapIcon className="h-4 w-4 text-blue-600" />;
      case 'coordinate':
        return <TargetIcon className="h-4 w-4 text-orange-600" />;
      default:
        return <MapPinIcon className="h-4 w-4 text-gray-600" />;
    }
  };

  const getClusteringMethodLabel = (method: string) => {
    switch (method) {
      case 'database':
        return 'Province/State';
      case 'major_city':
        return 'City Region';
      case 'coordinate':
        return 'Coordinate Cluster';
      default:
        return 'Unknown';
    }
  };

  const getCoverageColor = (coverage: number) => {
    if (coverage >= 90) return 'text-green-600 bg-green-50';
    if (coverage >= 70) return 'text-blue-600 bg-blue-50';
    if (coverage >= 50) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
        <Skeleton className="h-80 w-full" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircleIcon className="h-12 w-12 text-red-500 mb-4" />
        <div className="text-red-600 text-lg mb-4">Error loading geographic analysis</div>
        <p className="text-gray-500 text-sm">Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const analysis = response?.result as GeographicAnalysis;
  const regions = analysis?.regions || [];
  const summary = analysis?.summary;

  if (!analysis || regions.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-gray-500">
          No geographic data available. Ensure sufficient regional service data exists for analysis.
        </div>
      </Card>
    );
  }

  // Sort data based on selected criteria
  const sortedRegions = [...regions].sort((a, b) => {
    switch (sortBy) {
      case 'cost':
        return b.avg_cost_per_service - a.avg_cost_per_service;
      case 'services':
        return b.total_services - a.total_services;
      case 'coverage':
        return b.geocoding_coverage - a.geocoding_coverage;
      default:
        return 0;
    }
  });

  // Prepare chart data
  const chartData = sortedRegions.slice(0, 12).map(region => ({
    region: region.region_name.length > 15 ? 
      region.region_name.substring(0, 15) + '...' : 
      region.region_name,
    fullName: region.region_name,
    cost: region.avg_cost_per_service,
    services: region.total_services,
    coverage: region.geocoding_coverage,
    structures: region.unique_structures,
    method: region.clustering_method
  }));

  // Clustering method distribution
  const clusteringMethods = regions.reduce((acc, region) => {
    const method = getClusteringMethodLabel(region.clustering_method);
    acc[method] = (acc[method] || 0) + region.unique_structures;
    return acc;
  }, {} as Record<string, number>);

  const pieData = Object.entries(clusteringMethods).map(([name, value]) => ({
    name,
    value,
    percentage: (value / summary.total_structures * 100).toFixed(1)
  }));

  const COLORS = ['#10b981', '#3b82f6', '#f59e0b', '#ef4444'];

  // Coverage vs Cost scatter data
  const scatterData = regions.map(region => ({
    x: region.geocoding_coverage,
    y: region.avg_cost_per_service,
    name: region.region_name,
    services: region.total_services,
    method: region.clustering_method
  }));

  // Group by country
  const countrySummary = regions.reduce((acc, region) => {
    const country = region.country_name;
    if (!acc[country]) {
      acc[country] = {
        regions: 0,
        totalServices: 0,
        totalCost: 0,
        structures: 0
      };
    }
    acc[country].regions += 1;
    acc[country].totalServices += region.total_services;
    acc[country].totalCost += region.avg_cost_per_service * region.total_services;
    acc[country].structures += region.unique_structures;
    return acc;
  }, {} as Record<string, any>);

  // Calculate averages
  Object.keys(countrySummary).forEach(country => {
    const data = countrySummary[country];
    data.avgCost = data.totalCost / data.totalServices;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <GlobeIcon className="h-6 w-6 text-blue-600" />
            Enhanced Geographic Analysis
          </h2>
          <p className="text-gray-600">
            Hybrid geocoding with {analysis.analysis_method.replace('_', ' ')} analysis
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={viewMode === 'chart' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('chart')}
          >
            Charts
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            Details
          </Button>
          <Button
            variant={viewMode === 'map' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('map')}
          >
            Map View
          </Button>
        </div>
      </div>

      {/* Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Total Regions</div>
              <div className="text-2xl font-bold text-blue-600">{summary.total_regions}</div>
              <div className="text-xs text-gray-500">{summary.dominant_country || 'Multiple countries'}</div>
            </div>
            <MapPinIcon className="h-8 w-8 text-blue-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Total Structures</div>
              <div className="text-2xl font-bold text-green-600">{summary.total_structures}</div>
              <div className="text-xs text-gray-500">{summary.unmapped_structures} unmapped</div>
            </div>
            <BarChart3Icon className="h-8 w-8 text-green-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Geocoding Coverage</div>
              <div className="text-2xl font-bold text-purple-600">
                {summary.avg_geocoding_coverage.toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Province/country data</div>
            </div>
            <DatabaseIcon className="h-8 w-8 text-purple-600" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-500">Dominant Region</div>
              <div className="text-lg font-bold text-orange-600 truncate">
                {summary.dominant_region || 'N/A'}
              </div>
              <div className="text-xs text-gray-500">Most services</div>
            </div>
            <TrendingUpIcon className="h-8 w-8 text-orange-600" />
          </div>
        </Card>
      </div>

      {/* Sort Controls */}
      <div className="flex gap-2">
        <span className="text-sm font-medium self-center">Sort by:</span>
        <Button
          variant={sortBy === 'services' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSortBy('services')}
        >
          Service Volume
        </Button>
        <Button
          variant={sortBy === 'cost' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSortBy('cost')}
        >
          Average Cost
        </Button>
        <Button
          variant={sortBy === 'coverage' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setSortBy('coverage')}
        >
          Data Coverage
        </Button>
      </div>

      {viewMode === 'chart' && (
        <>
          {/* Regional Service Volume Chart */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Service Volume by Region</h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="region" 
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis />
                  <Tooltip 
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        const data = payload[0].payload;
                        return (
                          <div className="bg-white p-3 border rounded shadow-lg">
                            <p className="font-semibold">{data.fullName}</p>
                            <p className="text-sm">Services: {data.services}</p>
                            <p className="text-sm">Avg Cost: {formatCurrency(data.cost)}</p>
                            <p className="text-sm">Coverage: {data.coverage.toFixed(1)}%</p>
                            <p className="text-sm">Method: {getClusteringMethodLabel(data.method)}</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Bar 
                    dataKey="services" 
                    fill="#3B82F6" 
                    name="Services"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Geocoding Coverage Distribution */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Data Source Distribution</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name}: ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieData.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4 space-y-2">
                <div className="flex items-center gap-2">
                  <DatabaseIcon className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Province/State: Database lookup</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapIcon className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">City Region: Major city proximity</span>
                </div>
                <div className="flex items-center gap-2">
                  <TargetIcon className="h-4 w-4 text-orange-600" />
                  <span className="text-sm">Coordinate Cluster: GPS grouping</span>
                </div>
              </div>
            </Card>

            {/* Coverage vs Cost Scatter */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Data Coverage vs Service Cost</h3>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <ScatterChart>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      type="number" 
                      dataKey="x" 
                      name="Coverage" 
                      unit="%" 
                      domain={[0, 100]}
                      label={{ value: 'Geocoding Coverage (%)', position: 'insideBottom', offset: -5 }}
                    />
                    <YAxis 
                      type="number" 
                      dataKey="y" 
                      name="Average Cost"
                      label={{ value: 'Average Cost (CAD)', angle: -90, position: 'insideLeft' }}
                    />
                    <Tooltip 
                      formatter={(value, name) => [
                        name === 'y' ? formatCurrency(Number(value)) : `${Number(value).toFixed(1)}%`,
                        name === 'y' ? 'Average Cost' : 'Coverage'
                      ]}
                      labelFormatter={(_, payload) => {
                        return payload?.[0]?.payload?.name || 'Region';
                      }}
                    />
                    <Scatter 
                      data={scatterData} 
                      fill="#8884d8"
                      fillOpacity={0.6}
                    />
                  </ScatterChart>
                </ResponsiveContainer>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Higher coverage indicates better province/country data quality
              </p>
            </Card>
          </div>
        </>
      )}

      {viewMode === 'list' && (
        <>
          {/* Regional Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sortedRegions.slice(0, 18).map((region, index) => (
              <Card key={index} className="p-4">
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-semibold flex items-center gap-2">
                        {getClusteringMethodIcon(region.clustering_method)}
                        <span className="truncate">{region.region_name}</span>
                      </h4>
                      <div className="text-sm text-gray-600">{region.country_name}</div>
                    </div>
                    <Badge className={getCoverageColor(region.geocoding_coverage)}>
                      {region.geocoding_coverage.toFixed(0)}%
                    </Badge>
                  </div>

                  {/* Metrics Grid */}
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-gray-500">Avg Cost:</span>
                      <div className="font-medium">
                        {formatCurrency(region.avg_cost_per_service)}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">Services:</span>
                      <div className="font-medium">{region.total_services}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Structures:</span>
                      <div className="font-medium">{region.unique_structures}</div>
                    </div>
                    <div>
                      <span className="text-gray-500">Frequency:</span>
                      <div className="font-medium">
                        {region.service_frequency?.toFixed(1) || 'N/A'}/unit
                      </div>
                    </div>
                  </div>

                  {/* Clustering Method Badge */}
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {getClusteringMethodLabel(region.clustering_method)}
                    </Badge>
                    {region.distance_from_major_city && (
                      <span className="text-xs text-gray-500">
                        {region.distance_from_major_city.toFixed(0)}km from city
                      </span>
                    )}
                  </div>

                  {/* Coordinates */}
                  <div className="text-xs text-gray-500">
                    📍 {region.coordinates.lat.toFixed(3)}°, {region.coordinates.lon.toFixed(3)}°
                  </div>

                  {/* Coverage Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        region.geocoding_coverage >= 90 ? 'bg-green-600' :
                        region.geocoding_coverage >= 70 ? 'bg-blue-600' :
                        region.geocoding_coverage >= 50 ? 'bg-yellow-600' : 'bg-red-600'
                      }`}
                      style={{ width: `${region.geocoding_coverage}%` }}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Country Summary Table */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Country-Level Summary</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Country</th>
                    <th className="text-right p-2">Regions</th>
                    <th className="text-right p-2">Total Services</th>
                    <th className="text-right p-2">Structures</th>
                    <th className="text-right p-2">Avg Cost</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(countrySummary)
                    .sort(([,a], [,b]) => b.totalServices - a.totalServices)
                    .map(([country, data]) => (
                    <tr key={country} className="border-b hover:bg-gray-50">
                      <td className="p-2 font-medium">{country}</td>
                      <td className="p-2 text-right">{data.regions}</td>
                      <td className="p-2 text-right">{data.totalServices}</td>
                      <td className="p-2 text-right">{data.structures}</td>
                      <td className="p-2 text-right">{formatCurrency(data.avgCost)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </>
      )}

      {viewMode === 'map' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Geographic Distribution</h3>
          <div className="h-96 bg-gray-100 rounded flex items-center justify-center">
            <div className="text-center">
              <MapIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Interactive map visualization coming soon</p>
              <p className="text-sm text-gray-500 mt-2">
                Will display service locations with heat map overlay
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Data Quality Insights */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <AlertCircleIcon className="h-5 w-5 text-yellow-600" />
          Data Quality & Recommendations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-3">Geocoding Coverage Status</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm">Structures with province data:</span>
                <span className="font-medium">{summary.total_structures - summary.unmapped_structures}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Unmapped structures:</span>
                <span className="font-medium text-orange-600">{summary.unmapped_structures}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Overall coverage:</span>
                <span className="font-medium">{summary.avg_geocoding_coverage.toFixed(1)}%</span>
              </div>
              {analysis.cache_hit_rate && (
                <div className="flex items-center justify-between">
                  <span className="text-sm">Cache efficiency:</span>
                  <span className="font-medium text-green-600">{analysis.cache_hit_rate.toFixed(1)}%</span>
                </div>
              )}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-3">Improvement Actions</h4>
            <div className="space-y-2">
              {summary.unmapped_structures > 0 && (
                <div className="flex items-start gap-2">
                  <NavigationIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">
                    Run background geocoding job to map {summary.unmapped_structures} structures
                  </span>
                </div>
              )}
              <div className="flex items-start gap-2">
                <DatabaseIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Maintain province/country data for new structures at entry
                </span>
              </div>
              <div className="flex items-start gap-2">
                <CarIcon className="h-4 w-4 text-purple-500 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Use geographic insights for route optimization
                </span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EnhancedGeographicAnalysis;
export { EnhancedGeographicAnalysis };