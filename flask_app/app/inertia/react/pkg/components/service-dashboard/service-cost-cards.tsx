import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import { DollarSign, Wrench, TrendingUp, TrendingDown } from "lucide-react";
import React from "react";
import { Label } from "../ui/label";
import { H3, Mu<PERSON> } from "../ui/typography";
import { Skeleton } from "../ui/skeleton";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import HelpTooltip from "../help-system/HelpTooltip";

const ServiceCostCards = ({ children }: React.PropsWithChildren) => {
  return <div className="grid grid-cols-1 gap-4 md:grid-cols-3">{children}</div>;
};

const CardLoader = () => {
  return (
    <div className="flex flex-col space-y-1 md:space-y-2">
      <Skeleton className="h-4 w-1/2 md:h-6" />
      <Skeleton className="h-3 w-1/3 md:h-4" />
    </div>
  );
};

const TotalServiceCosts = ({ compact = false }: { compact?: boolean }) => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector(
    (state) => state.context.service_types,
  );
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/cost-overview",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );
  
  const costTrend = data?.result?.cost_trend_percentage || 0;
  const isIncreasing = costTrend > 0;
  
  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        <DollarSign className="h-4 w-4 text-blue-600" />
        <div>
          <div className="text-sm font-medium text-gray-600">Total</div>
          {isSuccess ? (
            <div className="text-lg font-bold text-blue-600">
              <Label.Numeric
                variant="dollars"
                value={data.result?.total_costs ?? "0.00"}
              />
            </div>
          ) : isLoading ? (
            <Skeleton className="h-5 w-16" />
          ) : (
            <div className="text-sm text-red-500">Error</div>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card className="gap-2 p-2 md:gap-6 md:p-4 lg:p-6">
      <CardHeader className="flex h-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="text-sm font-medium md:text-sm flex items-center gap-2">
          Total Service Costs
          <HelpTooltip 
            content="Aggregate service expenses for the selected period including labor, parts, and overhead costs. Trend indicator shows percentage change vs previous period."
            sectionId="service-cost-cards"
            size="md"
          />
        </CardTitle>
        <DollarSign
          strokeWidth={2}
          className="hidden h-4 w-4 min-w-5 text-black md:flex md:h-5 md:w-5"
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              <Label.Numeric
                variant="dollars"
                value={data.result?.total_costs ?? "0.00"}
              />
            </H3>
            <Muted className="flex items-center gap-1 text-sm md:text-sm">
              {isIncreasing ? (
                <TrendingUp className="h-3 w-3 text-red-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-green-500" />
              )}
              {Math.abs(costTrend).toFixed(1)}% {data.result?.period_comparison}
            </Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

const AverageServiceCost = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector(
    (state) => state.context.service_types,
  );
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/cost-overview",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );
  
  return (
    <Card className="gap-2 p-2 md:gap-4 md:p-4 lg:p-6">
      <CardHeader className="flex h-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="min-w-0 text-sm font-medium md:text-sm flex items-center gap-2">
          Average Cost per Order
          <HelpTooltip 
            content="Cost efficiency per work order. Calculated as total service costs divided by number of service orders. Compare against historical performance and industry benchmarks."
            sectionId="service-cost-cards"
            size="md"
          />
        </CardTitle>
        <Wrench
          strokeWidth={2}
          className="hidden h-4 w-4 min-w-5 text-black md:flex md:h-5 md:w-5"
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              <Label.Numeric
                variant="dollars"
                value={data.result?.average_cost_per_order ?? "0"}
              />
            </H3>
            <Muted className="text-sm md:text-sm">
              {data.result?.work_order_count ?? 0} orders
            </Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

const PartsLaborRatio = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector(
    (state) => state.context.service_types,
  );
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const include_unmapped = useServiceCostSelector(
    (state) => state.context.include_unmapped,
  );
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/cost-overview",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
        include_unmapped,
      },
    },
  );
  
  const partsPercentage = data?.result && Number(data.result.total_costs) > 0 
    ? (Number(data.result.parts_costs) / Number(data.result.total_costs) * 100)
    : 50;
  
  return (
    <Card className="gap-2 p-2 md:gap-6 md:p-4 lg:p-6">
      <CardHeader className="flex h-full w-full flex-row items-start justify-between space-y-0 p-0 pb-0">
        <CardTitle className="min-w-0 text-sm font-medium md:text-sm flex items-center gap-2">
          Parts vs Labor
          <HelpTooltip 
            content="Distribution of costs between parts and labor. Optimal range is typically 40-60% parts, 40-60% labor. Sudden shifts may indicate inefficient parts usage or labor issues."
            sectionId="service-cost-cards"
            size="md"
          />
        </CardTitle>
        <DollarSign
          strokeWidth={2}
          className="hidden h-4 w-4 min-w-5 text-black md:flex md:h-5 md:w-5"
        />
      </CardHeader>
      <CardContent className="p-0 pt-1">
        {isSuccess ? (
          <>
            <H3 className="text-xl md:text-2xl">
              {partsPercentage.toFixed(0)}% / {(100 - partsPercentage).toFixed(0)}%
            </H3>
            <Muted className="text-sm md:text-sm">Parts / Labor</Muted>
          </>
        ) : isLoading ? (
          <CardLoader />
        ) : isError ? (
          <>
            <div className="text-xl font-bold md:text-2xl">Error</div>
          </>
        ) : null}
      </CardContent>
    </Card>
  );
};

export { ServiceCostCards, TotalServiceCosts, AverageServiceCost, PartsLaborRatio };