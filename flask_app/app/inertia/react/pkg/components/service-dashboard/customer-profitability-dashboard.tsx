import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { $api } from "@/api/web-api";
import { useServiceCostSelector } from "@/stores/service-costs/hooks";
import { zIsoDate } from "@/lib/zod-utils";
import { Skeleton } from "../ui/skeleton";
import { AgGridReact } from 'ag-grid-react';
import { ColDef, ValueFormatterParams } from 'ag-grid-community';
import { themeQuartz } from 'ag-grid-community';
import { Badge } from "../ui/badge";
import { TrendingUp, TrendingDown } from "lucide-react";

const formatCurrency = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currency: 'CAD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(Number(params.value));
};

const formatPercentage = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return `${Number(params.value).toFixed(1)}%`;
};

const formatNumber = (params: ValueFormatterParams) => {
  if (params.value == null || params.value === '') return '';
  return Number(params.value).toFixed(1);
};

const CustomerProfitabilityDashboard = () => {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);

  const { data, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/service-analytics/customer-profitability-metrics",
    {
      body: {
        customers,
        models,
        unit_types,
        service_dates: {
          from: zIsoDate.nullable().parse(from),
          to: zIsoDate.nullable().parse(to),
        },
        selected_years,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
      },
    },
  );

  const columnDefs: ColDef[] = [
    {
      field: 'customer_name',
      headerName: 'Customer',
      headerTooltip: 'Customer name - the company or organization receiving service',
      pinned: 'left',
      width: 200,
      cellStyle: { fontWeight: 'bold' },
    },
    {
      field: 'profitability_rank',
      headerName: 'Rank',
      headerTooltip: 'Profitability ranking based on gross margin (revenue minus costs). Lower rank = more profitable.',
      width: 80,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        const rank = params.value;
        const badgeVariant = rank <= 3 ? 'default' : rank <= 6 ? 'secondary' : 'outline';
        return <Badge variant={badgeVariant}>#{rank}</Badge>;
      }
    },
    {
      field: 'gross_margin',
      headerName: 'Gross Margin',
      headerTooltip: 'Total revenue minus total service costs. Shows absolute profit generated from this customer.',
      valueFormatter: formatCurrency,
      width: 140,
      type: 'numericColumn',
      sort: 'desc',
      cellRenderer: (params: any) => {
        const value = Number(params.value);
        const color = value >= 0 ? 'text-green-600' : 'text-red-600';
        const icon = value >= 0 ? <TrendingUp className="h-3 w-3 inline mr-1" /> : <TrendingDown className="h-3 w-3 inline mr-1" />;
        return (
          <span className={color}>
            {icon}
            {formatCurrency(params)}
          </span>
        );
      }
    },
    {
      field: 'margin_percentage',
      headerName: 'Margin %',
      headerTooltip: 'Profit margin as percentage of revenue. Higher percentage indicates better profitability.',
      valueFormatter: formatPercentage,
      width: 100,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        const value = params.value;
        const color = value >= 30 ? 'text-green-600' : value >= 15 ? 'text-orange-600' : 'text-red-600';
        return <span className={color}>{formatPercentage(params)}</span>;
      }
    },
    {
      field: 'total_revenue',
      headerName: 'Revenue',
      headerTooltip: 'Total revenue generated from service work orders for this customer (converted to CAD)',
      valueFormatter: formatCurrency,
      width: 120,
      type: 'numericColumn'
    },
    {
      field: 'total_service_costs',
      headerName: 'Service Costs',
      headerTooltip: 'Total costs of parts and labor for servicing this customer\'s equipment (converted to CAD)',
      valueFormatter: formatCurrency,
      width: 130,
      type: 'numericColumn'
    },
    {
      field: 'avg_cost_per_unit',
      headerName: 'Cost/Unit',
      headerTooltip: 'Average service cost per unit of equipment. Useful for comparing service intensity across customers.',
      valueFormatter: formatCurrency,
      width: 120,
      type: 'numericColumn'
    },
    {
      field: 'service_frequency_vs_benchmark',
      headerName: 'Frequency vs Benchmark',
      headerTooltip: 'Service frequency compared to industry benchmark (3-month average). >100% means more frequent than normal.',
      width: 180,
      type: 'numericColumn',
      cellRenderer: (params: any) => {
        const value = params.value;
        const percentage = ((value - 1) * 100).toFixed(0);
        const color = value > 1.5 ? 'text-red-600' : value > 1.2 ? 'text-orange-600' : 'text-green-600';
        const prefix = value >= 1 ? '+' : '';
        return <span className={color}>{prefix}{percentage}%</span>;
      }
    },
    {
      field: 'total_units',
      headerName: '# Units',
      headerTooltip: 'Total number of unique equipment units serviced for this customer',
      width: 90,
      type: 'numericColumn'
    },
    {
      field: 'total_services',
      headerName: '# Services',
      headerTooltip: 'Total number of service work orders completed for this customer',
      width: 100,
      type: 'numericColumn'
    },
    {
      field: 'avg_services_per_unit',
      headerName: 'Services/Unit',
      headerTooltip: 'Average number of services performed per unit of equipment. Higher values may indicate reliability issues.',
      valueFormatter: formatNumber,
      width: 120,
      type: 'numericColumn'
    },
  ];

  const defaultColDef = {
    sortable: true,
    filter: true,
    resizable: true,
  };

  // Calculate summary statistics
  const summaryStats = data?.result ? {
    totalCustomers: data.result.length,
    totalRevenue: data.result.reduce((sum, c) => sum + Number(c.total_revenue), 0),
    totalCosts: data.result.reduce((sum, c) => sum + Number(c.total_service_costs), 0),
    avgMargin: data.result.reduce((sum, c) => sum + c.margin_percentage, 0) / data.result.length,
    profitableCustomers: data.result.filter(c => c.margin_percentage > 0).length,
  } : null;

  return (
    <Card className="col-span-full">
      <CardHeader className="pb-2">
        <CardTitle>Customer Profitability Analysis</CardTitle>
        <CardDescription>
          Revenue, costs, and profitability metrics by customer
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2">
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
            <Skeleton className="h-64 w-full" />
          </div>
        ) : isError ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">Error loading customer profitability data</p>
          </div>
        ) : !data?.result || data.result.length === 0 ? (
          <div className="flex h-64 items-center justify-center">
            <p className="text-sm text-gray-500">No customer profitability data found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
              <div className="rounded-lg border bg-blue-50 p-4 text-center">
                <div className="text-sm text-blue-600 font-medium">Total Customers</div>
                <div className="text-2xl font-bold text-blue-700">{summaryStats?.totalCustomers || 0}</div>
              </div>
              <div className="rounded-lg border bg-green-50 p-4 text-center">
                <div className="text-sm text-green-600 font-medium">Total Revenue</div>
                <div className="text-lg font-bold text-green-700">
                  {new Intl.NumberFormat('en-CA', { 
                    style: 'currency', 
                    currency: 'CAD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(summaryStats?.totalRevenue || 0)}
                </div>
              </div>
              <div className="rounded-lg border bg-orange-50 p-4 text-center">
                <div className="text-sm text-orange-600 font-medium">Total Costs</div>
                <div className="text-lg font-bold text-orange-700">
                  {new Intl.NumberFormat('en-CA', { 
                    style: 'currency', 
                    currency: 'CAD',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  }).format(summaryStats?.totalCosts || 0)}
                </div>
              </div>
              <div className="rounded-lg border bg-purple-50 p-4 text-center">
                <div className="text-sm text-purple-600 font-medium">Avg Margin</div>
                <div className="text-2xl font-bold text-purple-700">
                  {(summaryStats?.avgMargin || 0).toFixed(1)}%
                </div>
              </div>
              <div className="rounded-lg border bg-teal-50 p-4 text-center">
                <div className="text-sm text-teal-600 font-medium">Profitable</div>
                <div className="text-2xl font-bold text-teal-700">
                  {summaryStats?.profitableCustomers || 0}/{summaryStats?.totalCustomers || 0}
                </div>
              </div>
            </div>

            {/* Profitability Table */}
            <div className="h-[500px] w-full">
              <AgGridReact
                theme={themeQuartz}
                rowModelType="clientSide"
                rowData={data.result}
                columnDefs={columnDefs}
                defaultColDef={defaultColDef}
                pagination={true}
                paginationPageSize={20}
                paginationPageSizeSelector={[10, 20, 50]}
                domLayout="normal"
                suppressMenuHide={true}
                enableCellTextSelection={true}
                animateRows={true}
              />
            </div>

            {/* Profitability Legend */}
            <div className="rounded-lg border bg-gray-50 p-4">
              <h4 className="font-medium mb-2">Profitability Indicators</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Margin %:</span>
                  <div className="mt-1">
                    <span className="text-green-600">≥30%</span> Excellent | 
                    <span className="text-orange-600 ml-1">15-30%</span> Good | 
                    <span className="text-red-600 ml-1">&lt;15%</span> Low
                  </div>
                </div>
                <div>
                  <span className="font-medium">Service Frequency:</span>
                  <div className="mt-1">
                    <span className="text-green-600">≤120%</span> Normal | 
                    <span className="text-orange-600 ml-1">120-150%</span> High | 
                    <span className="text-red-600 ml-1">&gt;150%</span> Excessive
                  </div>
                </div>
                <div>
                  <span className="font-medium">Ranking:</span>
                  <div className="mt-1">
                    Ranked by gross margin (revenue minus service costs)
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { CustomerProfitabilityDashboard };