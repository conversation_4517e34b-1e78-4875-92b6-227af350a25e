import { GlobeIcon, MapPinIcon, NavigationIcon, FilterIcon } from "lucide-react";
import FilterMenu from "../ui/filter-menu";
import { $api } from "@/api/web-api";
import { useServiceCostSelector, useServiceCostStore } from "@/stores/service-costs/hooks";
import { Small } from "@/components/ui/typography";
import { Skeleton } from "../ui/skeleton";
import { Checkbox } from "../ui/checkbox";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { useEffect, useState, useMemo } from "react";

function CountryFilter() {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  
  const storekeys = useServiceCostSelector((state) => state.context.country_codes);
  const store = useServiceCostStore();

  // Get available countries from geographic analysis
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/geographic/analysis",
    {
      body: {
        service_dates: {
          from: from?.toISOString() || null,
          to: to?.toISOString() || null,
        },
        selected_years,
        customers,
        models,
        unit_types,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
      } as any,
    }
  );

  // Extract unique countries from the geographic analysis
  const countryOptions = useMemo(() => {
    if (!data?.result?.regions) return [];
    
    const countriesMap = new Map<string, { code: string; name: string; services: number }>();
    
    data.result.regions.forEach((region) => {
      const country = region.country_name;
      if (country && country !== 'Unknown') {
        // Simple country code mapping (could be enhanced with a proper mapping)
        const code = country === 'Canada' ? 'CA' : 
                    country === 'United States' ? 'US' : 
                    country.substring(0, 2).toUpperCase();
        
        if (countriesMap.has(code)) {
          const existing = countriesMap.get(code)!;
          existing.services += region.total_services;
        } else {
          countriesMap.set(code, {
            code,
            name: country,
            services: region.total_services
          });
        }
      }
    });
    
    return Array.from(countriesMap.values())
      .sort((a, b) => b.services - a.services);
  }, [data]);

  const setKeys = (keys: string[]) => {
    store.send({ type: "applyFilter", country_codes: keys });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Countries:</Small>
      {isSuccess && countryOptions.length > 0 ? (
        <FilterMenu
          options={countryOptions}
          keyGetter={(item) => item.code}
          labelGetter={(item) => `${item.name} (${item.services} services)`}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search countries..."
          allSelectedText="All countries"
          noneSelectedText="All countries"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} countries selected`
          }
          emptyWhenAllSelected={true}
          icon={<GlobeIcon strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        <div className="text-sm text-gray-500">Error loading countries</div>
      ) : (
        <div className="text-sm text-gray-500">No country data available</div>
      )}
    </div>
  );
}

function RegionFilter() {
  const customers = useServiceCostSelector((state) => state.context.customers);
  const service_types = useServiceCostSelector((state) => state.context.service_types);
  const models = useServiceCostSelector((state) => state.context.model_types);
  const unit_types = useServiceCostSelector((state) => state.context.unit_types);
  const technicians = useServiceCostSelector((state) => state.context.technicians);
  const part_categories = useServiceCostSelector((state) => state.context.part_categories);
  const include_ijack = useServiceCostSelector((state) => state.context.include_ijack);
  const include_sales_parts = useServiceCostSelector((state) => state.context.include_sales_parts);
  const from = useServiceCostSelector((state) => state.context.service_date_from);
  const to = useServiceCostSelector((state) => state.context.service_date_to);
  const selected_years = useServiceCostSelector((state) => state.context.selected_years);
  const country_codes = useServiceCostSelector((state) => state.context.country_codes);
  
  const storekeys = useServiceCostSelector((state) => state.context.region_names);
  const store = useServiceCostStore();

  // Get available regions from geographic analysis
  const { data, isSuccess, isLoading, isError } = $api.useQuery(
    "post",
    "/v1/geographic/analysis",
    {
      body: {
        service_dates: {
          from: from?.toISOString() || null,
          to: to?.toISOString() || null,
        },
        selected_years,
        customers,
        models,
        unit_types,
        service_types,
        technicians,
        part_categories,
        include_ijack,
        include_sales_parts,
      } as any,
    }
  );

  // Filter regions based on selected countries
  const regionOptions = useMemo(() => {
    if (!data?.result?.regions) return [];
    
    let regions = data.result.regions;
    
    // Filter by country if countries are selected
    if (country_codes.length > 0) {
      const countryNames = country_codes.map(code => 
        code === 'CA' ? 'Canada' : 
        code === 'US' ? 'United States' : 
        code
      );
      regions = regions.filter((r) => countryNames.includes(r.country_name));
    }
    
    return regions.map((r) => ({
      name: r.region_name,
      country: r.country_name,
      services: r.total_services,
      method: r.clustering_method
    })).sort((a, b) => b.services - a.services);
  }, [data, country_codes]);

  const setKeys = (keys: string[]) => {
    store.send({ type: "applyFilter", region_names: keys });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-1 px-1 py-2">
      <Small>Regions/Provinces:</Small>
      {isSuccess && regionOptions.length > 0 ? (
        <FilterMenu
          options={regionOptions}
          keyGetter={(item) => item.name}
          labelGetter={(item) => `${item.name}, ${item.country} (${item.services})`}
          selectedKeys={storekeys}
          setSelectedKeys={setKeys}
          placeholder="Search regions..."
          allSelectedText="All regions"
          noneSelectedText="All regions"
          multipleSelectedText={(selectedKeys) =>
            `${selectedKeys.length} regions selected`
          }
          emptyWhenAllSelected={true}
          icon={<MapPinIcon strokeWidth={2} className="h-4 w-4" />}
        />
      ) : isLoading ? (
        <Skeleton className="h-8 w-full bg-gray-200" />
      ) : isError ? (
        <div className="text-sm text-gray-500">Error loading regions</div>
      ) : regionOptions.length === 0 && country_codes.length > 0 ? (
        <div className="text-sm text-gray-500">No regions in selected countries</div>
      ) : (
        <div className="text-sm text-gray-500">Select countries first</div>
      )}
    </div>
  );
}

function GeographicAdvancedFilters() {
  const min_services = useServiceCostSelector((state) => state.context.min_services);
  const max_distance_from_city = useServiceCostSelector((state) => state.context.max_distance_from_city);
  const include_unmapped = useServiceCostSelector((state) => state.context.include_unmapped);
  const store = useServiceCostStore();
  
  const [localMinServices, setLocalMinServices] = useState(min_services?.toString() || '');
  const [localMaxDistance, setLocalMaxDistance] = useState(max_distance_from_city?.toString() || '');
  
  // Debounce numeric inputs
  useEffect(() => {
    const timer = setTimeout(() => {
      const minServicesNum = localMinServices ? parseInt(localMinServices) : null;
      if (!isNaN(minServicesNum!) || !localMinServices) {
        store.send({ type: "applyFilter", min_services: minServicesNum });
      }
    }, 500);
    return () => clearTimeout(timer);
  }, [localMinServices, store]);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      const maxDistanceNum = localMaxDistance ? parseFloat(localMaxDistance) : null;
      if (!isNaN(maxDistanceNum!) || !localMaxDistance) {
        store.send({ type: "applyFilter", max_distance_from_city: maxDistanceNum });
      }
    }, 500);
    return () => clearTimeout(timer);
  }, [localMaxDistance, store]);

  const handleUnmappedToggle = (checked: boolean) => {
    store.send({ type: "applyFilter", include_unmapped: checked });
  };

  return (
    <div className="items-left flex flex-col justify-between gap-3 px-1 py-2">
      <Small>Advanced Geographic Filters:</Small>
      
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="include-unmapped"
            checked={include_unmapped}
            onCheckedChange={handleUnmappedToggle}
          />
          <label
            htmlFor="include-unmapped"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Include unmapped regions
          </label>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="min-services" className="text-xs">
            Minimum services in region
          </Label>
          <Input
            id="min-services"
            type="number"
            min="0"
            placeholder="e.g., 10"
            value={localMinServices}
            onChange={(e) => setLocalMinServices(e.target.value)}
            className="h-8 text-sm"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="max-distance" className="text-xs">
            Max distance from city (km)
          </Label>
          <Input
            id="max-distance"
            type="number"
            min="0"
            step="0.1"
            placeholder="e.g., 100"
            value={localMaxDistance}
            onChange={(e) => setLocalMaxDistance(e.target.value)}
            className="h-8 text-sm"
          />
        </div>
      </div>
    </div>
  );
}

function GeographicFilterSection() {
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  return (
    <div className="border rounded-lg p-3 space-y-2">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold flex items-center gap-2">
          <NavigationIcon className="h-4 w-4" />
          Geographic Filters
        </h3>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
        >
          <FilterIcon className="h-3 w-3" />
          {showAdvanced ? 'Hide' : 'Show'} Advanced
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
        <CountryFilter />
        <RegionFilter />
      </div>
      
      {showAdvanced && (
        <div className="pt-2 border-t">
          <GeographicAdvancedFilters />
        </div>
      )}
    </div>
  );
}

export {
  CountryFilter,
  RegionFilter,
  GeographicAdvancedFilters,
  GeographicFilterSection,
};