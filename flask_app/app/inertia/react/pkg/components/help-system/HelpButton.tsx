import React from 'react';
import { Button } from '../ui/button';
import { HelpCircleIcon, BookOpenIcon } from 'lucide-react';
import { useHelp } from './HelpProvider';

interface HelpButtonProps {
  variant?: 'icon' | 'text' | 'floating';
  size?: 'sm' | 'md' | 'lg';
  sectionId?: string;
  className?: string;
  tooltip?: string;
}

const HelpButton: React.FC<HelpButtonProps> = ({
  variant = 'icon',
  size = 'md',
  sectionId,
  className = '',
  tooltip = 'Open help'
}) => {
  const { openHelp } = useHelp();

  const handleClick = () => {
    openHelp(sectionId);
  };

  if (variant === 'floating') {
    return (
      <Button
        onClick={handleClick}
        className={`fixed bottom-6 right-6 z-40 shadow-lg hover:shadow-xl transition-all duration-200 ${className}`}
        size="lg"
        title={tooltip}
      >
        <HelpCircleIcon className="h-5 w-5 mr-2" />
        Help
      </Button>
    );
  }

  if (variant === 'text') {
    return (
      <Button
        onClick={handleClick}
        variant="outline"
        size={size === 'md' ? 'default' : size}
        className={`flex items-center gap-2 ${className}`}
        title={tooltip}
      >
        <BookOpenIcon className="h-4 w-4" />
        Help & Guides
      </Button>
    );
  }

  // Default icon variant
  return (
    <Button
      onClick={handleClick}
      variant="ghost"
      size={size === 'md' ? 'default' : size}
      className={`p-2 ${className}`}
      title={tooltip}
    >
      <HelpCircleIcon className="h-4 w-4" />
    </Button>
  );
};

export default HelpButton;