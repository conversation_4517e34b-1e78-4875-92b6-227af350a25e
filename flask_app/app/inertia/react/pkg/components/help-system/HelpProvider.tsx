import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface HelpSection {
  id: string;
  title: string;
  content: string;
  componentContext?: string;
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  keywords: string[];
  lastUpdated: Date;
  version: string;
}

interface HelpCategory {
  id: string;
  title: string;
  description: string;
  sections: HelpSection[];
  order: number;
}

interface HelpContextType {
  isHelpOpen: boolean;
  currentSection: string | null;
  searchQuery: string;
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  
  // Actions
  openHelp: (sectionId?: string) => void;
  closeHelp: () => void;
  navigateToSection: (sectionId: string) => void;
  setSearchQuery: (query: string) => void;
  setUserLevel: (level: 'beginner' | 'intermediate' | 'advanced') => void;
  
  // Content
  categories: HelpCategory[];
  getCurrentSection: () => HelpSection | null;
  searchSections: (query: string) => HelpSection[];
}

const HelpContext = createContext<HelpContextType | undefined>(undefined);

export const useHelp = () => {
  const context = useContext(HelpContext);
  if (context === undefined) {
    throw new Error('useHelp must be used within a HelpProvider');
  }
  return context;
};

interface HelpProviderProps {
  children: ReactNode;
}

export const HelpProvider: React.FC<HelpProviderProps> = ({ children }) => {
  const [isHelpOpen, setIsHelpOpen] = useState(false);
  const [currentSection, setCurrentSection] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [userLevel, setUserLevel] = useState<'beginner' | 'intermediate' | 'advanced'>('intermediate');

  // Help content structure (in a real app, this would come from an API or CMS)
  const categories: HelpCategory[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Learn the basics of the Service Analytics Dashboard',
      order: 1,
      sections: [
        {
          id: 'dashboard-overview',
          title: 'Dashboard Overview',
          content: `# Dashboard Overview

The Service Analytics Dashboard provides comprehensive insights into your service operations. It consists of several main sections:

## Core Analytics
- **Service Cost Cards**: High-level financial metrics
- **Enhanced KPI Cards**: Operational efficiency metrics
- **Performance Analytics**: Detailed performance analysis

## Advanced Analytics
- **Problem Detection**: Automated outlier identification
- **Technician Performance**: Workforce optimization
- **Customer Profitability**: Customer relationship analysis
- **Predictive Insights**: AI-powered forecasting

## Operational Intelligence
- **Service Optimization**: Interval and efficiency optimization
- **Temporal Insights**: Seasonal and geographic analysis`,
          componentContext: 'overview',
          userLevel: 'beginner',
          keywords: ['overview', 'introduction', 'basics', 'getting started'],
          lastUpdated: new Date(),
          version: '1.0'
        },
        {
          id: 'navigation-guide',
          title: 'Navigation Guide',
          content: `# Navigation Guide

## Main Navigation
- **Tabs**: Switch between different analysis types
- **Filters**: Refine data using the left sidebar
- **Breadcrumbs**: Track your location in the dashboard

## Using Filters
1. **Date Range**: Select time periods for analysis
2. **Customer Filter**: Focus on specific customers
3. **Equipment Filter**: Analyze specific equipment types
4. **Reset**: Clear all filters to return to default view

## Quick Actions
- **F1**: Open help system
- **Ctrl+?**: Search help content
- **Esc**: Close open modals`,
          componentContext: 'navigation',
          userLevel: 'beginner',
          keywords: ['navigation', 'filters', 'tabs', 'controls'],
          lastUpdated: new Date(),
          version: '1.0'
        }
      ]
    },
    {
      id: 'core-analytics',
      title: 'Core Analytics',
      description: 'Understand the fundamental analytics components',
      order: 2,
      sections: [
        {
          id: 'service-cost-cards',
          title: 'Service Cost Cards',
          content: `# Service Cost Cards

These cards provide a high-level financial overview of your service operations.

## Total Service Costs
- **Purpose**: Shows aggregate service expenses
- **Calculation**: Sum of all service-related costs (labor + parts + overhead)
- **Trend**: Percentage change vs previous period
- **Good Performance**: Stable or declining costs with maintained quality

## Average Cost per Service
- **Purpose**: Measures cost efficiency per work order
- **Calculation**: Total service costs ÷ Number of service orders
- **Benchmark**: Compare against historical performance
- **Optimization**: Lower is generally better, but consider service quality

## Parts vs Labor Ratio
- **Purpose**: Shows cost distribution between parts and labor
- **Calculation**: (Parts costs ÷ Total costs) × 100
- **Optimal Range**: Typically 40-60% parts, 40-60% labor
- **Red Flags**: Sudden shifts may indicate inefficiency`,
          componentContext: 'service-cost-cards',
          userLevel: 'beginner',
          keywords: ['costs', 'financial', 'overview', 'total costs', 'average cost', 'parts labor ratio'],
          lastUpdated: new Date(),
          version: '1.0'
        },
        {
          id: 'enhanced-kpis',
          title: 'Enhanced KPI Cards',
          content: `# Enhanced KPI Cards

Advanced metrics measuring operational efficiency and service quality.

## Labor Efficiency
- **Metric**: Billable hours ÷ Total hours × 100
- **Thresholds**:
  - 🟢 Excellent: ≥80%
  - 🟡 Good: 75-79%
  - 🔴 Needs Improvement: <75%
- **Improvement**: Schedule optimization, travel reduction, training

## Service Quality
- **Metric**: Repeat services within 30 days ÷ Total services × 100
- **Thresholds**:
  - 🟢 Excellent: ≤10%
  - 🟡 Acceptable: 10-20%
  - 🔴 High: >20%
- **Root Causes**: Inadequate repairs, parts quality, training

## Parts Efficiency
- **Metrics**: Markup ratio and emergency service rate
- **Optimal**: Markup ≤2.5x, Emergency rate ≤20%
- **Optimization**: Inventory management, supplier negotiations

## Outlier Alerts
- **Purpose**: Equipment requiring immediate attention
- **Calculation**: Z-scores (standard deviations from normal)
- **Alert Levels**: Critical (>3σ), High (2.5-3σ), Medium (2-2.5σ)`,
          componentContext: 'enhanced-kpi-cards',
          userLevel: 'intermediate',
          keywords: ['kpi', 'efficiency', 'quality', 'labor', 'parts', 'outliers'],
          lastUpdated: new Date(),
          version: '1.0'
        }
      ]
    },
    {
      id: 'advanced-analytics',
      title: 'Advanced Analytics',
      description: 'Deep dive into predictive and optimization analytics',
      order: 3,
      sections: [
        {
          id: 'predictive-models',
          title: 'Predictive Analytics',
          content: `# Predictive Analytics

AI-powered insights for proactive decision-making and cost optimization.

## Cost Predictions
- **Purpose**: Forecast future service costs for budget planning
- **Method**: Machine learning models analyze historical patterns
- **Accuracy**: Confidence intervals provided with predictions
- **Use Cases**: Budget planning, variance analysis, strategic planning

## Failure Prediction
- **Purpose**: Anticipate equipment failures before they occur
- **Components**:
  - Failure probability within prediction horizon
  - Most likely failure modes
  - Optimal service interval recommendations
  - Cost impact of failures
- **Actions**: Schedule preventive maintenance, prepare parts

## Root Cause Analysis
- **Purpose**: Identify underlying causes of service problems
- **Process**: Pattern recognition → Statistical analysis → Recommendations
- **Outputs**: Significant contributing factors and corrective actions
- **Implementation**: Address design issues, operational problems, quality issues

## AI Recommendations
- **Types**: Cost reduction, efficiency improvements, quality enhancements
- **Analysis**: ROI calculations and implementation priorities
- **Implementation**: Priority ranking with expected returns`,
          componentContext: 'predictive-analytics',
          userLevel: 'advanced',
          keywords: ['predictive', 'ai', 'forecasting', 'machine learning', 'failure prediction'],
          lastUpdated: new Date(),
          version: '1.0'
        }
      ]
    },
    {
      id: 'operational-intelligence',
      title: 'Operational Intelligence',
      description: 'Temporal insights and operational optimization',
      order: 4,
      sections: [
        {
          id: 'seasonal-analysis',
          title: 'Seasonal Analysis',
          content: `# Seasonal Analysis

Identify and prepare for seasonal cost variations and patterns.

## Monthly Cost Patterns
- **Visualization**: Line chart showing annual cost trends
- **Calculation**: Average costs by month across historical data
- **Variance**: Percentage deviation from annual average
- **Peak Identification**: Months with highest service costs

## Climate Risk Factors
- **Weather Impact**: Temperature, precipitation, seasonal conditions
- **Equipment Stress**: Climate-sensitive equipment identification
- **Preparation**: Preventive actions for high-risk periods

## Seasonal Planning
1. **Budget Allocation**: Resource planning for high-cost seasons
2. **Inventory Management**: Pre-position parts for peak periods
3. **Staffing**: Adjust workforce for seasonal demands
4. **Preventive Maintenance**: Schedule before peak seasons

## Key Metrics
- **Seasonal Variation Coefficient**: Measures volatility
- **Weather Impact Score**: Climate influence on costs
- **Peak Period Identification**: Critical planning periods`,
          componentContext: 'seasonal-analysis',
          userLevel: 'intermediate',
          keywords: ['seasonal', 'weather', 'climate', 'monthly patterns', 'peaks'],
          lastUpdated: new Date(),
          version: '1.0'
        },
        {
          id: 'geographic-analysis',
          title: 'Geographic Analysis',
          content: `# Geographic Analysis

Optimize service delivery across different regions and locations.

## Regional Performance Metrics
- **Cost Variations**: Service costs by geographic area
- **National Average**: Benchmark for cost performance
- **Travel Efficiency**: Geographic impact on service delivery
- **Service Density**: Services per geographic unit

## Optimization Opportunities
1. **High-Cost Regions**: Investigate cost drivers
2. **Low-Efficiency Areas**: Improve routing and scheduling
3. **Market Opportunities**: Identify expansion regions
4. **Resource Allocation**: Optimize technician placement

## Country-Level Analysis
- **Multi-Country Operations**: Comprehensive country comparison
- **Currency Considerations**: Normalized cost analysis
- **Regional Strategies**: Country-specific optimization

## Implementation
- **Route Optimization**: Improve travel efficiency
- **Regional Specialization**: Technician expertise by area
- **Local Partnerships**: Strategic regional relationships`,
          componentContext: 'geographic-analysis',
          userLevel: 'intermediate',
          keywords: ['geographic', 'regional', 'travel', 'efficiency', 'location'],
          lastUpdated: new Date(),
          version: '1.0'
        }
      ]
    },
    {
      id: 'troubleshooting',
      title: 'Troubleshooting',
      description: 'Common issues and solutions',
      order: 5,
      sections: [
        {
          id: 'common-issues',
          title: 'Common Issues',
          content: `# Troubleshooting Common Issues

## "No data available" Message
**Cause**: Filter settings too restrictive or no data for selected parameters
**Solutions**:
1. Check date range includes periods with service activity
2. Verify customer and equipment filters aren't too narrow
3. Ensure data exists for selected parameters
4. Try resetting filters to default settings

## Unusual Metrics (Too High/Low)
**Cause**: Filter settings or data quality issues
**Solutions**:
1. Verify filter settings match intended analysis
2. Check currency conversion settings
3. Review include/exclude IJACK services setting
4. Validate comparison periods for trend calculations

## Predictive Analytics Not Loading
**Cause**: Insufficient historical data
**Requirements**:
- Minimum 6 months of service history recommended
- Equipment must have regular service activity
- Selected filters must include adequate data volume

## Performance Issues
**Cause**: Large datasets impacting load times
**Solutions**:
1. Narrow date ranges for initial analysis
2. Use specific customer or equipment filters
3. Analyze smaller data segments
4. Consider upgrading browser or clearing cache`,
          componentContext: 'troubleshooting',
          userLevel: 'beginner',
          keywords: ['troubleshooting', 'issues', 'problems', 'errors', 'performance'],
          lastUpdated: new Date(),
          version: '1.0'
        }
      ]
    }
  ];

  const openHelp = useCallback((sectionId?: string) => {
    setIsHelpOpen(true);
    if (sectionId) {
      setCurrentSection(sectionId);
    }
  }, []);

  const closeHelp = useCallback(() => {
    setIsHelpOpen(false);
    setCurrentSection(null);
    setSearchQuery('');
  }, []);

  const navigateToSection = useCallback((sectionId: string) => {
    setCurrentSection(sectionId);
    setSearchQuery('');
  }, []);

  const getCurrentSection = useCallback((): HelpSection | null => {
    if (!currentSection) return null;
    
    for (const category of categories) {
      const section = category.sections.find(s => s.id === currentSection);
      if (section) return section;
    }
    return null;
  }, [currentSection, categories]);

  const searchSections = useCallback((query: string): HelpSection[] => {
    if (!query.trim()) return [];
    
    const results: HelpSection[] = [];
    const searchTerm = query.toLowerCase();
    
    for (const category of categories) {
      for (const section of category.sections) {
        // Search in title, keywords, and content
        const matchesTitle = section.title.toLowerCase().includes(searchTerm);
        const matchesKeywords = section.keywords.some(keyword => 
          keyword.toLowerCase().includes(searchTerm)
        );
        const matchesContent = section.content.toLowerCase().includes(searchTerm);
        
        if (matchesTitle || matchesKeywords || matchesContent) {
          results.push(section);
        }
      }
    }
    
    // Sort results by relevance (title matches first, then keywords, then content)
    return results.sort((a, b) => {
      const aTitle = a.title.toLowerCase().includes(searchTerm) ? 3 : 0;
      const aKeyword = a.keywords.some(k => k.toLowerCase().includes(searchTerm)) ? 2 : 0;
      const aContent = a.content.toLowerCase().includes(searchTerm) ? 1 : 0;
      
      const bTitle = b.title.toLowerCase().includes(searchTerm) ? 3 : 0;
      const bKeyword = b.keywords.some(k => k.toLowerCase().includes(searchTerm)) ? 2 : 0;
      const bContent = b.content.toLowerCase().includes(searchTerm) ? 1 : 0;
      
      return (bTitle + bKeyword + bContent) - (aTitle + aKeyword + aContent);
    });
  }, [categories]);

  const value: HelpContextType = {
    isHelpOpen,
    currentSection,
    searchQuery,
    userLevel,
    openHelp,
    closeHelp,
    navigateToSection,
    setSearchQuery,
    setUserLevel,
    categories,
    getCurrentSection,
    searchSections
  };

  return (
    <HelpContext.Provider value={value}>
      {children}
    </HelpContext.Provider>
  );
};