import React, { useEffect, useState } from 'react';
import { useH<PERSON>p } from './HelpProvider';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { 
  XIcon, 
  SearchIcon, 
  BookOpenIcon, 
  ChevronRightIcon,
  ArrowLeftIcon,
  ClockIcon
} from 'lucide-react';

// Simple markdown renderer for basic formatting
const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
  const renderContent = (text: string) => {
    // Convert markdown to HTML-like JSX
    let rendered = text
      // Headers
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      // Bold
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Code
      .replace(/`(.*?)`/g, '<code>$1</code>')
      // Lists
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      // Line breaks
      .replace(/\n\n/g, '<br><br>')
      .replace(/\n/g, '<br>');

    // Wrap consecutive list items
    rendered = rendered.replace(/(<li>.*?<\/li>)(<br>)?(<li>.*?<\/li>)/g, '<ul>$1$3</ul>');
    
    return { __html: rendered };
  };

  return (
    <div 
      className="prose prose-sm max-w-none"
      dangerouslySetInnerHTML={renderContent(content)}
      style={{
        // Custom prose styling
        '--prose-headings': 'rgb(17 24 39)',
        '--prose-body': 'rgb(55 65 81)',
        '--prose-bold': 'rgb(17 24 39)',
        '--prose-code': 'rgb(239 68 68)',
        '--prose-code-bg': 'rgb(254 242 242)',
      } as React.CSSProperties}
    />
  );
};

const HelpModal: React.FC = () => {
  const {
    isHelpOpen,
    closeHelp,
    currentSection,
    searchQuery,
    setSearchQuery,
    navigateToSection,
    categories,
    getCurrentSection,
    searchSections,
    userLevel,
    setUserLevel
  } = useHelp();

  const [viewMode, setViewMode] = useState<'browse' | 'search' | 'section'>('browse');
  const [searchResults, setSearchResults] = useState<any[]>([]);

  // Handle search
  useEffect(() => {
    if (searchQuery.trim()) {
      const results = searchSections(searchQuery);
      setSearchResults(results);
      setViewMode('search');
    } else {
      setSearchResults([]);
      setViewMode(currentSection ? 'section' : 'browse');
    }
  }, [searchQuery, searchSections, currentSection]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isHelpOpen) {
        closeHelp();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isHelpOpen, closeHelp]);

  if (!isHelpOpen) return null;

  const currentSectionData = getCurrentSection();

  const handleSectionClick = (sectionId: string) => {
    navigateToSection(sectionId);
    setViewMode('section');
  };

  const handleBackToBrowse = () => {
    setSearchQuery('');
    setViewMode('browse');
  };

  const getUserLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-blue-100 text-blue-800';
      case 'advanced': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
        onClick={closeHelp}
      />
      
      {/* Modal */}
      <Card className="relative w-full max-w-6xl h-full max-h-[90vh] mx-4 flex flex-col bg-white shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <BookOpenIcon className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold">Service Dashboard Help</h2>
              <p className="text-sm text-gray-600">Comprehensive user guide and documentation</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            {/* User Level Selector */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Level:</span>
              <select
                value={userLevel}
                onChange={(e) => setUserLevel(e.target.value as any)}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
            
            <Button variant="ghost" size="sm" onClick={closeHelp}>
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar Navigation */}
          <div className="w-80 border-r bg-gray-50 flex flex-col">
            {/* Search */}
            <div className="p-4 border-b">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search help content..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-y-auto p-4">
              {viewMode === 'browse' && (
                <div className="space-y-4">
                  {categories
                    .sort((a, b) => a.order - b.order)
                    .map((category) => (
                    <div key={category.id}>
                      <h3 className="font-medium text-gray-900 mb-2">{category.title}</h3>
                      <div className="space-y-1 ml-2">
                        {category.sections
                          .filter(section => userLevel === 'advanced' || 
                                           section.userLevel === userLevel || 
                                           section.userLevel === 'beginner')
                          .map((section) => (
                          <button
                            key={section.id}
                            onClick={() => handleSectionClick(section.id)}
                            className="w-full text-left text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 p-2 rounded flex items-center justify-between group"
                          >
                            <span>{section.title}</span>
                            <div className="flex items-center gap-1">
                              <Badge className={`text-xs ${getUserLevelColor(section.userLevel)}`}>
                                {section.userLevel}
                              </Badge>
                              <ChevronRightIcon className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {viewMode === 'search' && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray-900">Search Results</h3>
                    <span className="text-xs text-gray-500">{searchResults.length} found</span>
                  </div>
                  {searchResults.map((section) => (
                    <button
                      key={section.id}
                      onClick={() => handleSectionClick(section.id)}
                      className="w-full text-left p-3 rounded border hover:bg-blue-50 hover:border-blue-200"
                    >
                      <div className="font-medium text-sm">{section.title}</div>
                      <div className="text-xs text-gray-600 mt-1">
                        {section.keywords.slice(0, 3).join(', ')}
                      </div>
                      <Badge className={`text-xs mt-2 ${getUserLevelColor(section.userLevel)}`}>
                        {section.userLevel}
                      </Badge>
                    </button>
                  ))}
                  {searchResults.length === 0 && searchQuery && (
                    <div className="text-center text-gray-500 py-8">
                      <SearchIcon className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No results found for "{searchQuery}"</p>
                      <p className="text-xs mt-1">Try different keywords or browse categories</p>
                    </div>
                  )}
                </div>
              )}

              {viewMode === 'section' && currentSectionData && (
                <div className="space-y-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBackToBrowse}
                    className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
                  >
                    <ArrowLeftIcon className="h-4 w-4" />
                    Back to Browse
                  </Button>
                  
                  <div className="p-3 bg-blue-50 rounded">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-medium text-blue-900">{currentSectionData.title}</h4>
                      <Badge className={getUserLevelColor(currentSectionData.userLevel)}>
                        {currentSectionData.userLevel}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-3 text-xs text-blue-700">
                      <div className="flex items-center gap-1">
                        <ClockIcon className="h-3 w-3" />
                        Updated {currentSectionData.lastUpdated.toLocaleDateString()}
                      </div>
                      <div>v{currentSectionData.version}</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {viewMode === 'browse' && (
              <div className="p-8 flex-1 overflow-y-auto">
                <div className="max-w-4xl">
                  <div className="text-center mb-8">
                    <BookOpenIcon className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      Welcome to Service Dashboard Help
                    </h3>
                    <p className="text-gray-600 max-w-2xl mx-auto">
                      Get the most out of your Service Analytics Dashboard with comprehensive guides, 
                      tutorials, and best practices. Select a topic from the sidebar to get started.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {categories.slice(0, 4).map((category) => (
                      <Card key={category.id} className="p-6 hover:shadow-lg transition-shadow">
                        <h4 className="font-semibold text-lg mb-2">{category.title}</h4>
                        <p className="text-gray-600 text-sm mb-4">{category.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">
                            {category.sections.length} articles
                          </span>
                          <Button variant="outline" size="sm">
                            Explore →
                          </Button>
                        </div>
                      </Card>
                    ))}
                  </div>

                  <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                    <h4 className="font-semibold mb-2">Quick Tips</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• Use the search bar to quickly find specific topics</li>
                      <li>• Adjust your experience level in the top-right corner</li>
                      <li>• Press <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">F1</kbd> anytime to open help</li>
                      <li>• Look for contextual help icons throughout the dashboard</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {viewMode === 'section' && currentSectionData && (
              <div className="p-8 flex-1 overflow-y-auto">
                <div className="max-w-4xl">
                  <MarkdownRenderer content={currentSectionData.content} />
                </div>
              </div>
            )}

            {viewMode === 'search' && (
              <div className="p-8 flex-1 overflow-y-auto">
                <div className="max-w-4xl">
                  <h3 className="text-xl font-semibold mb-4">
                    Search Results for "{searchQuery}"
                  </h3>
                  {searchResults.length > 0 ? (
                    <div className="space-y-6">
                      {searchResults.map((section) => (
                        <Card key={section.id} className="p-6">
                          <div className="flex items-start justify-between mb-3">
                            <h4 className="font-semibold text-lg">{section.title}</h4>
                            <Badge className={getUserLevelColor(section.userLevel)}>
                              {section.userLevel}
                            </Badge>
                          </div>
                          <div className="text-gray-600 text-sm mb-4">
                            {section.content.substring(0, 200)}...
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="flex flex-wrap gap-1">
                              {section.keywords.slice(0, 4).map((keyword: string) => (
                                <Badge key={keyword} variant="outline" className="text-xs">
                                  {keyword}
                                </Badge>
                              ))}
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleSectionClick(section.id)}
                            >
                              Read More
                            </Button>
                          </div>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <SearchIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                      <h4 className="text-lg font-medium text-gray-900 mb-2">No results found</h4>
                      <p className="text-gray-600">
                        Try adjusting your search terms or browse the categories
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default HelpModal;