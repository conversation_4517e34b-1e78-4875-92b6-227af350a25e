import React, { ReactNode } from 'react';
import { HelpCircleIcon } from 'lucide-react';
import { useHelp } from './HelpProvider';

interface HelpTooltipProps {
  content: string;
  sectionId?: string;
  children?: ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  size?: 'sm' | 'md' | 'lg';
}

const HelpTooltip: React.FC<HelpTooltipProps> = ({
  content,
  sectionId,
  children,
  position = 'top',
  size = 'sm'
}) => {
  const { openHelp } = useHelp();
  
  const handleHelpClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (sectionId) {
      openHelp(sectionId);
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom':
        return 'bottom-full mb-2 left-1/2 transform -translate-x-1/2';
      case 'left':
        return 'right-full mr-2 top-1/2 transform -translate-y-1/2';
      case 'right':
        return 'left-full ml-2 top-1/2 transform -translate-y-1/2';
      default: // top
        return 'top-full mt-2 left-1/2 transform -translate-x-1/2';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'lg':
        return 'max-w-sm p-4 text-sm';
      case 'md':
        return 'max-w-xs p-3 text-sm';
      default: // sm
        return 'max-w-xs p-2 text-xs';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'lg':
        return 'h-5 w-5';
      case 'md':
        return 'h-4 w-4';
      default: // sm
        return 'h-3 w-3';
    }
  };

  return (
    <div className="relative inline-flex items-center group">
      {children || (
        <button
          onClick={handleHelpClick}
          className="text-gray-400 hover:text-blue-600 transition-colors"
          title="Click for detailed help"
        >
          <HelpCircleIcon className={getIconSize()} />
        </button>
      )}
      
      {/* Tooltip */}
      <div
        className={`
          absolute z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible
          transition-all duration-200 pointer-events-none
          bg-gray-900 text-white rounded-lg shadow-lg
          ${getPositionClasses()}
          ${getSizeClasses()}
        `}
      >
        <div className="relative">
          {content}
          {sectionId && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <button
                onClick={handleHelpClick}
                className="text-blue-300 hover:text-blue-200 text-xs underline pointer-events-auto"
              >
                Learn more →
              </button>
            </div>
          )}
          
          {/* Arrow */}
          <div
            className={`
              absolute w-2 h-2 bg-gray-900 transform rotate-45
              ${position === 'top' ? '-bottom-1 left-1/2 -translate-x-1/2' : ''}
              ${position === 'bottom' ? '-top-1 left-1/2 -translate-x-1/2' : ''}
              ${position === 'left' ? '-right-1 top-1/2 -translate-y-1/2' : ''}
              ${position === 'right' ? '-left-1 top-1/2 -translate-y-1/2' : ''}
            `}
          />
        </div>
      </div>
    </div>
  );
};

export default HelpTooltip;