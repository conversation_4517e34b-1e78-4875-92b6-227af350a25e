import React from "react";
import { createStore, SnapshotFromStore } from "@xstate/store";
import { useSelector } from "@xstate/store/react";

export const store = createStore({
  context: { mobileMenuOpen: false },
  on: {
    toggleMobileMenu: (context) => ({
      ...context,
      mobileMenuOpen: !context.mobileMenuOpen,
    }),
  },
});

type MenuStore = typeof store;

export const MenuStoreContext = React.createContext<MenuStore | null>(null);

export const useMenuSelector = <T>(selector: (state: SnapshotFromStore<MenuStore>) => T) => {
  const store = React.useContext(MenuStoreContext)
  if (!store) {
    throw new Error('Missing MenuStoreProvider')
  }
  return useSelector(store, selector)
}

export const useMenuStore = () => {
  const store = React.useContext(MenuStoreContext)
  if (!store) {
    throw new Error('Missing MenuStoreProvider')
  }
  return store
}