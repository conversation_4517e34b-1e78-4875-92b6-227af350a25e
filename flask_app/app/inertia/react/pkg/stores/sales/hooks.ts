import React from "react";
import { createStore, SnapshotFromStore } from "@xstate/store";
import { useSelector } from "@xstate/store/react";

export const store = createStore({
  context: { customers: [] as number[], service_types: [] as number[], service_date_from: null as Date|null, service_date_to: null as Date|null, model_types: [] as (number|null)[] },
  on: {
    applyFilter: (context, event: { customers?: number[], service_types?: number[], service_date_from?: Date|null, service_date_to?: Date|null, model_types?: (number|null)[] }) => ({
      ...context,
      customers: event?.customers ?? context.customers,
      service_types: event?.service_types ?? context.service_types,
      service_date_from: event?.service_date_from === undefined ? context.service_date_from : event.service_date_from,
      service_date_to: event?.service_date_to === undefined ? context.service_date_to : event.service_date_to,
      model_types: event?.model_types ?? context.model_types,
    }),
    clearFilters: (context) => ({
      ...context,
      customers: [],
      service_types: [],
      service_date_from: null,
      service_date_to: null,
      model_types: [],
    }),
  },
});

type SalesStore = typeof store;

export const SalesStoreContext = React.createContext<SalesStore | null>(null);

export const useSalesSelector = <T>(selector: (state: SnapshotFromStore<SalesStore>) => T) => {
  const store = React.useContext(SalesStoreContext)
  if (!store) {
    throw new Error('Missing SalesStoreProvider')
  }
  return useSelector(store, selector)
}

export const useSalesStore = () => {
  const store = React.useContext(SalesStoreContext)
  if (!store) {
    throw new Error('Missing SalesStoreProvider')
  }
  return store
}