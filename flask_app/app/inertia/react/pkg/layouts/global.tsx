import { SiteHeader } from "@/components/site-header";
import { NProgressQuery } from "@/components/ui/nprogress";
import {
  QueryClient,
  QueryCache,
  QueryClientProvider,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import React from "react";
import { toast, Toaster } from "sonner";
import { ErrorBoundary } from "@/components/error-boundary";
import {
  errorNotificationManager,
  ErrorDetails,
} from "@/components/ui/error-notification";

const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => {
      console.log({ error });

      // Show error notification for query errors
      const errorDetails: ErrorDetails = {
        message: `Query Error: ${error instanceof Error ? error.message : "Unknown query error"}`,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        type: "network",
        source: "Query Cache",
      };

      errorNotificationManager.show(errorDetails);

      // Also show toast for immediate feedback
      toast.error(
        `Something went wrong: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    },
  }),
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      refetchOnWindowFocus: false,
    },
  },
});

export default function Global({ children }: React.PropsWithChildren) {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <Toaster expand richColors />
        <NProgressQuery />
        <SiteHeader />
        {children}
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ErrorBoundary>
  );
}
