import { Header } from "@/components/header/header";
import { Footer } from "@/components/footer/footer";
import { NProgressQuery } from "@/components/ui/nprogress";
import {
  QueryClient,
  QueryCache,
  QueryClientProvider,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import React from "react";
import { toast, Toaster } from "sonner";

const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => {
      console.log({ error });
      toast.error(`Something went wrong: ${error instanceof Error ? error.message : 'Unknown error'}`);
    },
  }),
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      refetchOnWindowFocus: false,
    },
  },
});
function Main({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <Toaster expand richColors />
      <NProgressQuery />
      <div className="flex min-h-screen flex-col py-0">
        <Header />
        {children}
        <Footer />
      </div>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
export { Main };
