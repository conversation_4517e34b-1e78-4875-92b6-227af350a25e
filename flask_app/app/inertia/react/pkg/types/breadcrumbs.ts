import z from "zod";

const breadcrumbSchema = z.object({
  text: z.string(),
  url: z.string(),
  is_current_path: z.boolean(),
});

export const breadcrumbsSchema = z.union([
  z.object({}),
  breadcrumbSchema.extend({
    children: z.lazy(() => breadcrumbSchema.array()),
  }),
]);

export type Breadcrumb = z.infer<typeof breadcrumbSchema>;

export type Breadcrumbs =
  | (z.infer<typeof breadcrumbSchema> & {
      children: Breadcrumbs[];
    })
  | Record<string, never>;
