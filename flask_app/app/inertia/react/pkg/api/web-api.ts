import createFetchClient, { Middleware } from "openapi-fetch";
import createClient from "openapi-react-query";
import type { paths } from "@/types/web-api.gen.d.ts"; // generated by openapi-typescript

const middleware: Middleware = {
  onResponse({ response }) {
    if (!response.ok) {
      // Will produce error messages like "https://example.org/api/v1/example: 404 Not Found".
      throw new Error(`${response.url}: ${response.status} ${response.statusText}`)
    }
  }
}

export const fetchClient = createFetchClient<paths>({
  baseUrl: import.meta.env.VITE_WEB_API_URL,
  credentials: "include"
});

fetchClient.use(middleware)

export const $api = createClient(fetchClient);