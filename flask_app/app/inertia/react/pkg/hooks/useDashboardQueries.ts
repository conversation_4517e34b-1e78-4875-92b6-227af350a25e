// app/inertia/react/src/hooks/useDashboardQueries.ts

/**
 * ========================================================
 * DASHBOARD DATA FETCHING MODULE
 * ========================================================
 *
 * This file is responsible for fetching data from the server for our sales dashboard.
 * Think of it as a messenger that goes to the Python backend, gets the data,
 * and delivers it to our React components.
 *
 * It contains:
 * 1. Type definitions (these are like blueprints that describe our data)
 * 2. Functions that fetch data from the server
 * 3. React hooks that components can use to easily access this data
 *
 * HOW THIS FITS INTO THE APPLICATION:
 * - The Python backend (sales.py) prepares and exposes the data
 * - This file (useDashboardQueries.ts) fetches that data
 * - The React component (Sales.tsx) uses this file to display the data
 */

// We import the "useQuery" function from a library called "react-query"
// This library helps us fetch, cache, and update data in React applications
import { useQuery } from "@tanstack/react-query";

// ========================================================
// TYPE DEFINITIONS
// ========================================================
// These are TypeScript "interfaces" - they describe the shape of our data objects
// Think of them like contracts that say "this data will have these specific properties"

/**
 * This defines what our sales data looks like.
 * The question marks (?) mean those fields are optional - they might not always be present.
 */
export interface SalesData {
  customer?: string; // Who bought the product
  year?: number; // When they bought it
  currency?: string; // What currency was used (USD, CAD, etc.)
  month_year?: string; // Formatted as "YYYY-MM" like "2023-01"
  part_num_group?: string; // Product category or group
  model?: string; // Which model was sold
  unit_type?: string; // Type of unit sold
  total_sales: number; // How much money was made (this is required, no question mark)
}

/**
 * This is the main data structure that holds ALL of our dashboard data.
 * It contains multiple arrays of data used for different charts and visualizations.
 */
export interface DashboardData {
  // Various ways of breaking down our sales data
  sales_by_customer: SalesData[];
  sales_by_year: SalesData[];
  sales_by_currency: SalesData[];
  sales_by_month: SalesData[];
  sales_by_product: SalesData[];
  sales_by_model: SalesData[];
  sales_by_unit_type: SalesData[];

  // More complex data for multi-dimensional charts
  sales_by_year_and_product: SalesData[];
  sales_by_year_and_customer: SalesData[];

  // Available filter options and analysis summary.
  // This describes all the filter options available on the dashboard.
  // These are the ways users can narrow down what data they want to see.
  filters: {
    customers: string[]; // List of all customer names
    years: number[]; // List of years with sales data
    currencies: string[]; // Available currencies
    service_types: string[]; // Types of services offered
    is_warranty: boolean | null; // Is this covered by warranty? (true/false/null)
    is_soft_part: boolean | null; // Is this a soft part? (true/false/null)
    creator_companies: string[]; // Companies that created the orders
  };

  // This describes the analysis summary data shown at the top of the dashboard.
  analysis: {
    top_customer: string; // Name of the customer who spent the most
    top_customer_sales: number; // How much that top customer spent
    top_year: number | string; // Year with highest sales
    top_year_sales: number; // How much was sold in that top year
    avg_order_value: number; // Average size of an order
  };
}

/**
 * This describes the filter parameters we can send to the server.
 * These are the actual selected filters, not just the options.
 *
 * The question marks (?) mean these are optional.
 * The "null" means we can explicitly say "no filter for this" (different from undefined).
 */
export interface FilterParams {
  customer?: string[] | null; // Which customers to include
  year?: number[] | null; // Which years to include
  currency?: string | null; // Which currency to filter by
  is_warranty?: boolean | null; // Filter by warranty status
  is_soft_part?: boolean | null; // Filter by part type
  service_type?: string[] | null; // Which service types to include
  creator_company?: string | null; // Which company created the orders
}

/**
 * These are settings for the pivot table visualization.
 * They control how the data is organized and calculated.
 */
export interface PivotDimensions {
  rows: string; // What to show in the rows (e.g., "customer")
  cols: string; // What to show in the columns (e.g., "year")
  values: string; // What value to calculate (e.g., "total_sales")
  aggregation: string; // How to calculate it (e.g., "sum", "average")
}

/**
 * This describes the data structure returned for pivot tables.
 */
export interface PivotData {
  // The actual data points as an array of records
  data: Record<string, string | number | boolean | null>[];

  // Layout settings for the chart
  layout: {
    title: {
      text: string;
      font?: { size?: number; color?: string };
    };
    xaxis: {
      title: { text: string; font?: { size?: number } };
    };
    yaxis: {
      title: { text: string; font?: { size?: number } };
    };
    barmode: string;
  };

  // Optional error message if something went wrong
  error?: string;
}

// ========================================================
// DATA FETCHING FUNCTIONS
// ========================================================

/**
 * This function fetches the dashboard data from the server.
 *
 * @param filterParams - Which filters to apply to the data
 * @returns A promise that will eventually contain the dashboard data
 *
 * Think of this like sending a custom order form to a restaurant:
 * - We specify exactly what we want (the filters)
 * - The kitchen (server) prepares it
 * - The waiter (this function) brings back exactly what we ordered
 */
const fetchDashboardData = async (
  filterParams: FilterParams,
): Promise<DashboardData> => {
  // Start with the basic URL
  let url = "/api/dashboard-data/";

  // Create an object to hold our URL parameters (the stuff after the ? in the URL)
  const params = new URLSearchParams();

  // Add customer filters if any are selected
  if (filterParams.customer && filterParams.customer.length > 0) {
    // For each selected customer, add a "customer=XYZ" parameter
    // This allows for multiple selections (like customer=ABC&customer=XYZ)
    filterParams.customer.forEach((customer) =>
      params.append("customer", customer),
    );
  }

  // Add year filters if any are selected
  if (filterParams.year && filterParams.year.length > 0) {
    // Convert each year to a string and add as a parameter
    filterParams.year.forEach((year) => params.append("year", year.toString()));
  }

  // Add currency filters if any are selected
  if (filterParams.currency && filterParams.currency.length > 0) {
    params.append("currency", filterParams.currency.toString());
  }

  // Add service type filters if any are selected
  if (filterParams.service_type && filterParams.service_type.length > 0) {
    filterParams.service_type.forEach((type) =>
      params.append("service_type", type),
    );
  }

  // Add a special filter for the creator company
  // This is a bit different because it's a single string, not an array
  if (filterParams.creator_company && filterParams.creator_company.length > 0) {
    // Only add this filter if it's not empty
    params.append("creator_company", filterParams.creator_company.toString());
  }

  // Add boolean filters (these can be true, false, or not specified)

  // For warranty status
  if (
    filterParams.is_warranty !== null &&
    filterParams.is_warranty !== undefined
  ) {
    params.append("is_warranty", filterParams.is_warranty.toString());
  }

  // For soft part status
  if (
    filterParams.is_soft_part !== null &&
    filterParams.is_soft_part !== undefined
  ) {
    params.append("is_soft_part", filterParams.is_soft_part.toString());
  }

  // If we have any parameters, add them to the URL with a ?
  if (params.toString()) {
    url += `?${params.toString()}`;
  }

  // Now we actually fetch the data from the server
  const response = await fetch(url);

  // If something went wrong, throw an error
  if (!response.ok) {
    throw new Error(`Failed to fetch dashboard data: ${response.statusText}`);
  }

  // Parse the JSON response
  const data = await response.json();

  // The actual data is nested inside a property called "initial_data"
  return data.initial_data;
};

/**
 * This function fetches pivot table data from the server.
 * Pivot tables let us analyze data from different angles.
 *
 * @param pivotDimensions - How to organize the pivot table
 * @param filterParams - Which filters to apply
 * @returns A promise that will eventually contain the pivot data
 */
const fetchPivotData = async (
  pivotDimensions: PivotDimensions,
  filterParams: FilterParams,
): Promise<PivotData> => {
  // Extract the pivot table configuration
  const { rows, cols, values, aggregation } = pivotDimensions;

  // Start building the URL with the pivot settings
  let url = `/api/sales-pivot-data/?rows=${rows}&cols=${cols}&values=${values}&aggregation=${aggregation}`;

  // Add customer filters if any are selected
  if (filterParams.customer && filterParams.customer.length > 0) {
    // For each customer, add it to the URL
    // encodeURIComponent makes sure special characters in names don't break the URL
    filterParams.customer.forEach((customer) => {
      url += `&customer=${encodeURIComponent(customer)}`;
    });
  }

  // Add year filters if any are selected
  if (filterParams.year && filterParams.year.length > 0) {
    filterParams.year.forEach((year) => {
      url += `&year=${year}`;
    });
  }

  // Add currency filters if any are selected
  if (filterParams.currency && filterParams.currency.length > 0) {
    // There's only one currency allowed, not multiple
    url += `&currency=${encodeURIComponent(filterParams.currency)}`;
  }

  // Add service type filters if any are selected
  if (filterParams.service_type && filterParams.service_type.length > 0) {
    filterParams.service_type.forEach((type) => {
      url += `&service_type=${encodeURIComponent(type)}`;
    });
  }

  // Add boolean filters
  if (
    filterParams.is_warranty !== null &&
    filterParams.is_warranty !== undefined
  ) {
    url += `&is_warranty=${filterParams.is_warranty}`;
  }

  if (
    filterParams.is_soft_part !== null &&
    filterParams.is_soft_part !== undefined
  ) {
    url += `&is_soft_part=${filterParams.is_soft_part}`;
  }

  // Use a try/catch block to handle any errors that might occur
  try {
    // Fetch the data from the server
    const response = await fetch(url);

    // Check if the response is OK
    if (!response.ok) {
      // If not, get the error message and throw an error
      const errorText = await response.text();
      throw new Error(`Failed to fetch pivot data: ${errorText}`);
    }

    // Parse the JSON response
    const data = await response.json();

    // Check if there's an error in the response itself
    if (data.error) {
      throw new Error(data.error);
    }

    // If everything is OK, return the data
    return data;
  } catch (error) {
    // If anything went wrong, create a fallback data object with the error message
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Return an error-formatted response that the UI can still display
    return {
      data: [],
      layout: {
        title: {
          text: `Error: ${errorMessage}`,
          font: { size: 18, color: "red" },
        },
        xaxis: { title: { text: "" } },
        yaxis: { title: { text: "" } },
        barmode: "group",
      },
      error: errorMessage,
    };
  }
};

// ========================================================
// REACT HOOKS
// ========================================================
// These are special functions that React components can use to easily get data

/**
 * This is a "custom hook" for fetching dashboard data.
 * Hooks in React are special functions that let components use features like state and effects.
 *
 * This hook makes it super easy for components to:
 * 1. Request data with specific filters
 * 2. Know when the data is loading
 * 3. Handle any errors
 * 4. Automatically refresh when filters change
 *
 * @param filterParams - Which filters to apply to the data
 * @returns An object with the data, loading state, and any errors
 */
export const useDashboardData = (filterParams: FilterParams) => {
  return useQuery({
    // This "queryKey" uniquely identifies this data request
    // When filterParams changes, React Query knows to fetch fresh data
    queryKey: ["dashboardData", filterParams],

    // This is the function that will actually fetch the data
    queryFn: () => fetchDashboardData(filterParams),

    // Only retry once if the fetch fails
    retry: 1,
  });
};

/**
 * This is another custom hook, but for pivot table data.
 * It works similar to the useDashboardData hook above.
 *
 * @param pivotDimensions - How to organize the pivot table
 * @param filterParams - Which filters to apply
 * @returns An object with the pivot data, loading state, and any errors
 */
export const usePivotData = (
  pivotDimensions: PivotDimensions,
  filterParams: FilterParams,
) => {
  return useQuery({
    // The unique identifier for this query
    queryKey: ["pivotData", pivotDimensions, filterParams],

    // The function to fetch the data
    queryFn: () => fetchPivotData(pivotDimensions, filterParams),

    // Only fetch when dimensions are provided (not on initial render if empty)
    enabled: !!pivotDimensions,

    // Only retry once if the fetch fails
    retry: 1,
  });
};
