/**
 * Utility functions for determining the application environment
 */

/**
 * Check if the application is running in development mode
 *
 * This uses Vite's import.meta.env.MODE environment variable which is set at build time
 *
 * @returns {boolean} True if running in development mode, false otherwise
 */
export const isDevelopment = (): boolean => {
  return import.meta.env.MODE === "development";
};

/**
 * Check if the application is running in production mode
 *
 * This uses Vite's import.meta.env.MODE environment variable which is set at build time
 *
 * @returns {boolean} True if running in production mode, false otherwise
 */
export const isProduction = (): boolean => {
  return import.meta.env.MODE === "production";
};

/**
 * Get the current environment mode (development, production, etc.)
 *
 * @returns {string} The current environment mode
 */
export const getEnvironmentMode = (): string => {
  return import.meta.env.MODE;
};
