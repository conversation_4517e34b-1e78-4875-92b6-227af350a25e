import { ErrorDetails } from '../components/ui/error-notification';

export interface CollectedError extends ErrorDetails {
  id: string;
  count: number;
  firstOccurrence: Date;
  lastOccurrence: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface ErrorSummary {
  totalErrors: number;
  uniqueErrors: number;
  timeRange: {
    start: Date;
    end: Date;
  };
  errors: CollectedError[];
}

class ErrorCollector {
  private errors: Map<string, CollectedError> = new Map();
  private maxErrors = 50; // Prevent memory bloat
  private maxAge = 5 * 60 * 1000; // 5 minutes

  /**
   * Generate a unique key for error deduplication
   */
  private generateErrorKey(error: ErrorDetails): string {
    // Group errors by message and source for smart deduplication
    const key = `${error.type}:${error.message}:${error.source || 'unknown'}`;
    return key;
  }

  /**
   * Determine error severity based on type and content
   */
  private determineSeverity(error: ErrorDetails): CollectedError['severity'] {
    if (error.type === 'network') {
      if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
        return 'critical';
      }
      if (error.message.includes('401') || error.message.includes('403')) {
        return 'high';
      }
      return 'medium';
    }

    if (error.type === 'javascript' || error.type === 'react') {
      if (error.message.toLowerCase().includes('unhandled') || 
          error.message.toLowerCase().includes('cannot read') ||
          error.message.toLowerCase().includes('typeerror')) {
        return 'high';
      }
      return 'medium';
    }

    return 'low';
  }

  /**
   * Add an error to the collection
   */
  addError(error: any): any {
    this.cleanupOldErrors();

    const key = this.generateErrorKey(error);
    const existingError = this.errors.get(key);

    if (existingError) {
      // Update existing error
      existingError.count++;
      existingError.lastOccurrence = error.timestamp;
      // Use the latest error details but keep the original ID and stats
      // Preserve all properties including enhanced context
      const updatedError: any = {
        ...error,
        id: existingError.id,
        count: existingError.count,
        firstOccurrence: existingError.firstOccurrence,
        lastOccurrence: error.timestamp,
        severity: this.determineSeverity(error)
      };
      this.errors.set(key, updatedError as CollectedError);
      return updatedError;
    } else {
      // Create new error entry
      // Preserve all properties including enhanced context
      const collectedError: any = {
        ...error,
        id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        count: 1,
        firstOccurrence: error.timestamp,
        lastOccurrence: error.timestamp,
        severity: this.determineSeverity(error)
      };
      
      this.errors.set(key, collectedError as CollectedError);
      
      // Trim if we have too many errors
      if (this.errors.size > this.maxErrors) {
        const oldestKey = Array.from(this.errors.keys())[0];
        this.errors.delete(oldestKey);
      }
      
      return collectedError;
    }
  }

  /**
   * Remove errors older than maxAge
   */
  private cleanupOldErrors(): void {
    const cutoff = new Date(Date.now() - this.maxAge);
    
    for (const [key, error] of this.errors.entries()) {
      if (error.lastOccurrence < cutoff) {
        this.errors.delete(key);
      }
    }
  }

  /**
   * Get all collected errors sorted by severity and recency
   */
  getAllErrors(): CollectedError[] {
    const errors = Array.from(this.errors.values());
    
    // Sort by severity (critical first) then by last occurrence (newest first)
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    
    return errors.sort((a, b) => {
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
      if (severityDiff !== 0) return severityDiff;
      
      return b.lastOccurrence.getTime() - a.lastOccurrence.getTime();
    });
  }

  /**
   * Get error summary statistics
   */
  getSummary(): ErrorSummary {
    const errors = this.getAllErrors();
    
    if (errors.length === 0) {
      const now = new Date();
      return {
        totalErrors: 0,
        uniqueErrors: 0,
        timeRange: { start: now, end: now },
        errors: []
      };
    }

    const totalErrors = errors.reduce((sum, error) => sum + error.count, 0);
    const timestamps = errors.flatMap(e => [e.firstOccurrence, e.lastOccurrence]);
    
    return {
      totalErrors,
      uniqueErrors: errors.length,
      timeRange: {
        start: new Date(Math.min(...timestamps.map(t => t.getTime()))),
        end: new Date(Math.max(...timestamps.map(t => t.getTime())))
      },
      errors
    };
  }

  /**
   * Format all errors for clipboard/email
   */
  formatAllErrors(): string {
    const summary = this.getSummary();
    
    if (summary.totalErrors === 0) {
      return 'No errors to report.';
    }

    const header = `
🐛 BULK ERROR REPORT

Summary:
- Total Errors: ${summary.totalErrors}
- Unique Error Types: ${summary.uniqueErrors}
- Time Range: ${summary.timeRange.start.toISOString()} to ${summary.timeRange.end.toISOString()}
- User Agent: ${navigator.userAgent}
- Current URL: ${window.location.href}

================================================================================
DETAILED ERROR LIST
================================================================================
`.trim();

    const errorDetails = summary.errors.map((error, index) => {
      const occurrenceText = error.count > 1 
        ? `(${error.count} occurrences: ${error.firstOccurrence.toISOString()} to ${error.lastOccurrence.toISOString()})`
        : `(${error.firstOccurrence.toISOString()})`;

      return `
${index + 1}. ${error.severity.toUpperCase()} - ${error.type.toUpperCase()} ERROR ${occurrenceText}

Message: ${error.message}
Source: ${error.source || 'Unknown'}
URL: ${error.url}

${error.stack ? `Stack Trace:\n${error.stack}\n` : ''}
${'─'.repeat(80)}`;
    }).join('\n');

    const footer = `

---
Generated by IJACK Enhanced Error Reporter
Report ID: ${Date.now().toString(36).toUpperCase()}
    `;

    return header + errorDetails + footer;
  }

  /**
   * Clear all collected errors
   */
  clear(): void {
    this.errors.clear();
  }

  /**
   * Get count of errors
   */
  getErrorCount(): number {
    return this.getSummary().totalErrors;
  }

  /**
   * Get count of unique errors
   */
  getUniqueErrorCount(): number {
    return this.errors.size;
  }
}

export const errorCollector = new ErrorCollector();