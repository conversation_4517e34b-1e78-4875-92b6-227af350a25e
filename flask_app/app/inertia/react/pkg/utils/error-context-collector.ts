import { ErrorDetails } from '../components/ui/error-notification';

export interface UserAction {
  type: 'click' | 'input' | 'scroll' | 'navigation' | 'form_submit' | 'keypress';
  target: string;
  value?: string;
  timestamp: Date;
  coordinates?: { x: number; y: number };
  key?: string;
}

export interface ConsoleLog {
  level: 'log' | 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: Date;
  args: any[];
}

export interface NetworkRequest {
  url: string;
  method: string;
  status?: number;
  duration?: number;
  timestamp: Date;
  error?: string;
  size?: number;
  type: 'fetch' | 'xhr';
}

export interface BrowserDiagnostics {
  viewport: { width: number; height: number };
  screen: { width: number; height: number };
  memory?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
  connection?: {
    effectiveType: string;
    downlink: number;
    rtt: number;
  };
  devicePixelRatio: number;
  colorDepth: number;
  online: boolean;
  language: string;
  languages: string[];
  platform: string;
  cookieEnabled: boolean;
  doNotTrack: string | null;
  hardwareConcurrency: number;
  maxTouchPoints: number;
}

export interface PerformanceMetrics {
  navigationTiming?: {
    loadTime: number;
    domContentLoaded: number;
    firstPaint?: number;
    firstContentfulPaint?: number;
  };
  memory?: {
    usedPercent: number;
    totalMB: number;
  };
  fps?: number;
}

export interface ApplicationContext {
  currentRoute: string;
  previousRoute?: string;
  sessionId?: string;
  userId?: string;
  featureFlags?: Record<string, boolean>;
  buildVersion?: string;
  deploymentEnv?: string;
}

export interface EnhancedErrorContext extends ErrorDetails {
  // User interaction history
  userActions: UserAction[];
  
  // Console logs before error
  consoleLogs: ConsoleLog[];
  
  // Network requests around error time
  networkRequests: NetworkRequest[];
  
  // Browser and device info
  browserDiagnostics: BrowserDiagnostics;
  
  // Performance metrics
  performanceMetrics: PerformanceMetrics;
  
  // Application state
  applicationContext: ApplicationContext;
  
  // React component stack (for React errors)
  componentStack?: string;
  
  // Props/state snapshot (for React component errors)
  componentContext?: {
    props?: Record<string, any>;
    state?: Record<string, any>;
  };
  
  // Related errors (errors that happened close in time)
  relatedErrors?: Array<{
    message: string;
    timestamp: Date;
    type: string;
  }>;
  
  // DOM context (relevant DOM elements)
  domContext?: {
    activeElement?: string;
    parentHierarchy?: string[];
    nearbyElements?: string[];
  };
  
  // Local storage snapshot
  localStorage?: Record<string, string>;
  
  // Session storage snapshot
  sessionStorage?: Record<string, string>;
}

class ErrorContextCollector {
  private userActions: UserAction[] = [];
  private consoleLogs: ConsoleLog[] = [];
  private networkRequests: NetworkRequest[] = [];
  private maxUserActions = 20;
  private maxConsoleLogs = 50;
  private maxNetworkRequests = 30;
  private isInitialized = false;
  private originalConsole: Record<string, Function> = {};
  private applicationContext: ApplicationContext = {
    currentRoute: typeof window !== 'undefined' ? window.location.pathname : '/',
  };

  constructor() {
    if (typeof window !== 'undefined') {
      console.log('ErrorContextCollector: Initializing...');
      this.initialize();
    }
  }

  private initialize() {
    if (this.isInitialized) return;
    this.isInitialized = true;

    console.log('ErrorContextCollector: Setting up tracking...');

    // Track user actions
    this.setupUserActionTracking();
    
    // Intercept console logs
    this.setupConsoleInterception();
    
    // Track network requests
    this.setupNetworkTracking();
    
    // Track navigation
    this.setupNavigationTracking();
    
    console.log('ErrorContextCollector: Setup complete');
  }

  private setupUserActionTracking() {
    // Track clicks
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      this.addUserAction({
        type: 'click',
        target: this.getElementDescription(target),
        timestamp: new Date(),
        coordinates: { x: e.clientX, y: e.clientY }
      });
    }, true);

    // Track form inputs
    document.addEventListener('input', (e) => {
      const target = e.target as HTMLInputElement;
      this.addUserAction({
        type: 'input',
        target: this.getElementDescription(target),
        value: target.type === 'password' ? '[REDACTED]' : target.value?.substring(0, 50),
        timestamp: new Date()
      });
    }, true);

    // Track form submissions
    document.addEventListener('submit', (e) => {
      const target = e.target as HTMLFormElement;
      this.addUserAction({
        type: 'form_submit',
        target: this.getElementDescription(target),
        timestamp: new Date()
      });
    }, true);

    // Track key presses (only special keys)
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === 'Escape' || e.key === 'Tab' || e.ctrlKey || e.metaKey) {
        this.addUserAction({
          type: 'keypress',
          target: this.getElementDescription(e.target as HTMLElement),
          key: e.key,
          timestamp: new Date()
        });
      }
    }, true);

    // Track scroll (debounced)
    let scrollTimeout: number;
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = window.setTimeout(() => {
        this.addUserAction({
          type: 'scroll',
          target: 'window',
          value: `${window.scrollY}px`,
          timestamp: new Date()
        });
      }, 500);
    }, true);
  }

  private setupConsoleInterception() {
    const methods: Array<keyof Console> = ['log', 'info', 'warn', 'error', 'debug'];
    
    methods.forEach(method => {
      this.originalConsole[method] = console[method];
      
      (console as any)[method] = (...args: any[]) => {
        // Call original method
        this.originalConsole[method].apply(console, args);
        
        // Store log
        this.addConsoleLog({
          level: method as ConsoleLog['level'],
          message: args.map(arg => this.stringifyArg(arg)).join(' '),
          timestamp: new Date(),
          args: args.slice(0, 3) // Limit stored args
        });
      };
    });
  }

  private setupNetworkTracking() {
    // Track fetch requests
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const url = typeof args[0] === 'string' ? args[0] : 
                   args[0] instanceof Request ? args[0].url : 
                   args[0] instanceof URL ? args[0].href : 'unknown';
      
      const method = args[1]?.method || 
                     (args[0] instanceof Request ? args[0].method : 'GET');
      
      const startTime = Date.now();
      const request: NetworkRequest = {
        url,
        method,
        timestamp: new Date(),
        type: 'fetch'
      };

      try {
        const response = await originalFetch(...args);
        request.status = response.status;
        request.duration = Date.now() - startTime;
        
        // Estimate size from content-length header
        const contentLength = response.headers.get('content-length');
        if (contentLength) {
          request.size = parseInt(contentLength, 10);
        }
        
        this.addNetworkRequest(request);
        return response;
      } catch (error) {
        request.error = error instanceof Error ? error.message : 'Network error';
        request.duration = Date.now() - startTime;
        this.addNetworkRequest(request);
        throw error;
      }
    };

    // Track XMLHttpRequest
    const XHROpen = XMLHttpRequest.prototype.open;
    const XHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method: string, url: string, ...args: any[]) {
      (this as any)._method = method;
      (this as any)._url = url;
      return XHROpen.apply(this, [method, url, ...args] as any);
    };
    
    XMLHttpRequest.prototype.send = function(...args: any[]) {
      const startTime = Date.now();
      const request: NetworkRequest = {
        url: (this as any)._url || 'unknown',
        method: (this as any)._method || 'GET',
        timestamp: new Date(),
        type: 'xhr'
      };
      
      this.addEventListener('load', () => {
        request.status = this.status;
        request.duration = Date.now() - startTime;
        errorContextCollector.addNetworkRequest(request);
      });
      
      this.addEventListener('error', () => {
        request.error = 'Network error';
        request.duration = Date.now() - startTime;
        errorContextCollector.addNetworkRequest(request);
      });
      
      return XHRSend.apply(this, args as any);
    };
  }

  private setupNavigationTracking() {
    // Track route changes
    const pushState = history.pushState;
    const replaceState = history.replaceState;
    
    history.pushState = (...args) => {
      this.applicationContext.previousRoute = this.applicationContext.currentRoute;
      this.applicationContext.currentRoute = window.location.pathname;
      
      this.addUserAction({
        type: 'navigation',
        target: args[2] as string,
        timestamp: new Date()
      });
      
      return pushState.apply(history, args);
    };
    
    history.replaceState = (...args) => {
      this.applicationContext.previousRoute = this.applicationContext.currentRoute;
      this.applicationContext.currentRoute = window.location.pathname;
      
      return replaceState.apply(history, args);
    };
    
    window.addEventListener('popstate', () => {
      this.applicationContext.previousRoute = this.applicationContext.currentRoute;
      this.applicationContext.currentRoute = window.location.pathname;
      
      this.addUserAction({
        type: 'navigation',
        target: window.location.pathname,
        timestamp: new Date()
      });
    });
  }

  private getElementDescription(element: HTMLElement | null): string {
    if (!element) return 'unknown';
    
    const tag = element.tagName.toLowerCase();
    const id = element.id ? `#${element.id}` : '';
    const classes = element.className ? `.${element.className.split(' ').filter(c => c).join('.')}` : '';
    const text = element.textContent ? element.textContent.substring(0, 30).trim() : '';
    const ariaLabel = element.getAttribute('aria-label');
    
    let description = `<${tag}${id}${classes}>`;
    
    if (ariaLabel) {
      description += ` [${ariaLabel}]`;
    } else if (text && tag !== 'script' && tag !== 'style') {
      description += ` "${text}${element.textContent!.length > 30 ? '...' : ''}"`;
    }
    
    return description;
  }

  private stringifyArg(arg: any): string {
    if (arg === null) return 'null';
    if (arg === undefined) return 'undefined';
    if (typeof arg === 'string') return arg;
    if (typeof arg === 'number' || typeof arg === 'boolean') return String(arg);
    if (arg instanceof Error) return `${arg.name}: ${arg.message}`;
    if (typeof arg === 'object') {
      try {
        return JSON.stringify(arg, null, 2).substring(0, 200);
      } catch {
        return '[Object]';
      }
    }
    return String(arg);
  }

  private addUserAction(action: UserAction) {
    this.userActions.push(action);
    if (this.userActions.length > this.maxUserActions) {
      this.userActions.shift();
    }
  }

  private addConsoleLog(log: ConsoleLog) {
    this.consoleLogs.push(log);
    if (this.consoleLogs.length > this.maxConsoleLogs) {
      this.consoleLogs.shift();
    }
  }

  private addNetworkRequest(request: NetworkRequest) {
    this.networkRequests.push(request);
    if (this.networkRequests.length > this.maxNetworkRequests) {
      this.networkRequests.shift();
    }
  }

  public collectBrowserDiagnostics(): BrowserDiagnostics {
    const nav = navigator as any;
    
    return {
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: screen.width,
        height: screen.height
      },
      ...((performance as any).memory ? {
        memory: {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
          jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
        }
      } : {}),
      ...(nav.connection ? {
        connection: {
          effectiveType: nav.connection.effectiveType,
          downlink: nav.connection.downlink,
          rtt: nav.connection.rtt
        }
      } : {}),
      devicePixelRatio: window.devicePixelRatio,
      colorDepth: screen.colorDepth,
      online: navigator.onLine,
      language: navigator.language,
      languages: [...navigator.languages],
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack,
      hardwareConcurrency: navigator.hardwareConcurrency,
      maxTouchPoints: navigator.maxTouchPoints
    };
  }

  public collectPerformanceMetrics(): PerformanceMetrics {
    const metrics: PerformanceMetrics = {};
    
    // Navigation timing
    if (performance.timing) {
      const timing = performance.timing;
      metrics.navigationTiming = {
        loadTime: timing.loadEventEnd - timing.navigationStart,
        domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart
      };
      
      // Paint timing
      const paintEntries = performance.getEntriesByType('paint');
      paintEntries.forEach(entry => {
        if (entry.name === 'first-paint') {
          metrics.navigationTiming!.firstPaint = entry.startTime;
        } else if (entry.name === 'first-contentful-paint') {
          metrics.navigationTiming!.firstContentfulPaint = entry.startTime;
        }
      });
    }
    
    // Memory usage
    if ((performance as any).memory) {
      const memory = (performance as any).memory;
      metrics.memory = {
        usedPercent: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
        totalMB: memory.totalJSHeapSize / 1048576
      };
    }
    
    return metrics;
  }

  public collectDOMContext(errorElement?: HTMLElement): { activeElement?: string; parentHierarchy?: string[]; nearbyElements?: string[]; } {
    const activeElement = document.activeElement;
    const context: { activeElement?: string; parentHierarchy?: string[]; nearbyElements?: string[]; } = {};
    
    if (activeElement) {
      context.activeElement = this.getElementDescription(activeElement as HTMLElement);
    }
    
    // Get parent hierarchy if error element is provided
    if (errorElement) {
      const hierarchy: string[] = [];
      let current: HTMLElement | null = errorElement;
      
      while (current && hierarchy.length < 5) {
        hierarchy.push(this.getElementDescription(current));
        current = current.parentElement;
      }
      
      context.parentHierarchy = hierarchy;
    }
    
    return context;
  }

  public collectStorageData(): { localStorage?: Record<string, string>; sessionStorage?: Record<string, string> } {
    const data: { localStorage?: Record<string, string>; sessionStorage?: Record<string, string> } = {};
    
    try {
      // Collect localStorage (limit to 10KB)
      const localData: Record<string, string> = {};
      let localSize = 0;
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          const value = localStorage.getItem(key);
          if (value) {
            const size = key.length + value.length;
            if (localSize + size < 10240) { // 10KB limit
              localData[key] = value.length > 200 ? value.substring(0, 200) + '...' : value;
              localSize += size;
            }
          }
        }
      }
      data.localStorage = localData;
      
      // Collect sessionStorage (limit to 10KB)
      const sessionData: Record<string, string> = {};
      let sessionSize = 0;
      
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key) {
          const value = sessionStorage.getItem(key);
          if (value) {
            const size = key.length + value.length;
            if (sessionSize + size < 10240) { // 10KB limit
              sessionData[key] = value.length > 200 ? value.substring(0, 200) + '...' : value;
              sessionSize += size;
            }
          }
        }
      }
      data.sessionStorage = sessionData;
    } catch (error) {
      // Storage access might be blocked
      console.warn('Failed to collect storage data:', error);
    }
    
    return data;
  }

  public setApplicationContext(context: Partial<ApplicationContext>) {
    this.applicationContext = { ...this.applicationContext, ...context };
  }

  public collectEnhancedContext(baseError: ErrorDetails): EnhancedErrorContext {
    const storageData = this.collectStorageData();
    
    return {
      ...baseError,
      userActions: [...this.userActions],
      consoleLogs: [...this.consoleLogs],
      networkRequests: [...this.networkRequests],
      browserDiagnostics: this.collectBrowserDiagnostics(),
      performanceMetrics: this.collectPerformanceMetrics(),
      applicationContext: { ...this.applicationContext },
      domContext: this.collectDOMContext(),
      ...storageData
    };
  }

  public clear() {
    this.userActions = [];
    this.consoleLogs = [];
    this.networkRequests = [];
  }
}

export const errorContextCollector = new ErrorContextCollector();