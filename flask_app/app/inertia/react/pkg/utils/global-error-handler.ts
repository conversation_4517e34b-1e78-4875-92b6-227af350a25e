import { errorNotificationManager, ErrorDetails } from '../components/ui/error-notification';
import { errorCollector } from './error-collector';
import { errorContextCollector } from './error-context-collector';

// Global error handler for unhandled JavaScript errors
export function setupGlobalErrorHandler() {
  // Handle unhandled errors
  window.addEventListener('error', (event) => {
    const error = event.error || new Error(event.message);
    
    const errorDetails: ErrorDetails = {
      message: error.message || event.message || 'Unknown error occurred',
      stack: error.stack,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      type: 'javascript',
      source: event.filename ? `${event.filename}:${event.lineno}:${event.colno}` : undefined,
    };

    // Collect enhanced context
    const enhancedError = errorContextCollector.collectEnhancedContext(errorDetails);
    
    // Add to error collector and show notification
    const collectedError = errorCollector.addError(enhancedError);
    errorNotificationManager.show(collectedError);

    // Log to console for debugging
    console.group('🐛 Global JavaScript Error');
    console.error('Message:', errorDetails.message);
    console.error('Source:', errorDetails.source);
    console.error('Stack:', errorDetails.stack);
    console.groupEnd();
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason;
    
    let message = 'Unhandled promise rejection';
    let stack: string | undefined;
    
    if (error instanceof Error) {
      message = error.message;
      stack = error.stack;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error && typeof error === 'object') {
      message = error.message || JSON.stringify(error);
      stack = error.stack;
    }

    const errorDetails: ErrorDetails = {
      message: `Promise Rejection: ${message}`,
      stack,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      type: 'javascript',
      source: 'Promise rejection',
    };

    // Collect enhanced context
    const enhancedError = errorContextCollector.collectEnhancedContext(errorDetails);
    
    // Add to error collector and show notification
    const collectedError = errorCollector.addError(enhancedError);
    errorNotificationManager.show(collectedError);

    // Log to console for debugging
    console.group('🐛 Unhandled Promise Rejection');
    console.error('Reason:', event.reason);
    console.error('Promise:', event.promise);
    console.groupEnd();

    // Prevent the default handling (which would log to console)
    event.preventDefault();
  });

  // Override console.error to catch manual errors
  const originalConsoleError = console.error;
  console.error = (...args: any[]) => {
    // Call original console.error
    originalConsoleError.apply(console, args);

    // Check if this looks like an error we should show a notification for
    const firstArg = args[0];
    if (firstArg instanceof Error || (typeof firstArg === 'string' && firstArg.toLowerCase().includes('error'))) {
      const message = firstArg instanceof Error ? firstArg.message : String(firstArg);
      const stack = firstArg instanceof Error ? firstArg.stack : undefined;

      const errorDetails: ErrorDetails = {
        message: `Console Error: ${message}`,
        stack,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        type: 'javascript',
        source: 'console.error',
      };

      // Only show notification for errors that seem significant
      if (!isIgnorableError(message)) {
        const collectedError = errorCollector.addError(errorDetails);
        errorNotificationManager.show(collectedError);
      }
    }
  };

  // Network error detection (for fetch failures)
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);
      
      // Show notification for HTTP errors
      if (!response.ok && response.status >= 400) {
        const url = typeof args[0] === 'string' ? args[0] : (args[0] instanceof Request ? args[0].url : args[0] instanceof URL ? args[0].href : 'Unknown URL');
        
        const errorDetails: ErrorDetails = {
          message: `HTTP ${response.status}: ${response.statusText}`,
          stack: undefined,
          timestamp: new Date(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          type: 'network',
          source: url,
        };

        // Only show for 500+ errors or important 400 errors
        if (response.status >= 500 || isImportantClientError(response.status)) {
          const collectedError = errorCollector.addError(errorDetails);
          errorNotificationManager.show(collectedError);
        }
      }
      
      return response;
    } catch (error) {
      const url = typeof args[0] === 'string' ? args[0] : (args[0] instanceof Request ? args[0].url : args[0] instanceof URL ? args[0].href : 'Unknown URL');
      
      const errorDetails: ErrorDetails = {
        message: `Network Error: ${error instanceof Error ? error.message : 'Request failed'}`,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        type: 'network',
        source: url,
      };

      const collectedError = errorCollector.addError(errorDetails);
      errorNotificationManager.show(collectedError);
      throw error;
    }
  };

  console.log('🛡️ Global error handler initialized');
}

// Helper function to determine if an error should be ignored
function isIgnorableError(message: string): boolean {
  const ignorablePatterns = [
    // Common browser extension errors
    'extension',
    'chrome-extension',
    'moz-extension',
    // React DevTools
    'react-devtools',
    // Common warnings that aren't critical
    'warning:',
    'deprecated',
    // Third-party script errors that we can't control
    'script error',
    'non-error promise rejection',
  ];

  const lowerMessage = message.toLowerCase();
  return ignorablePatterns.some(pattern => lowerMessage.includes(pattern));
}

// Helper function to determine if a client error is important enough to show
function isImportantClientError(status: number): boolean {
  // Show notifications for these client errors
  const importantClientErrors = [
    401, // Unauthorized
    403, // Forbidden
    404, // Not Found (for API calls)
    422, // Unprocessable Entity
  ];

  return importantClientErrors.includes(status);
}

// Get error statistics for UI
export function getErrorStats() {
  return {
    totalErrors: errorCollector.getErrorCount(),
    uniqueErrors: errorCollector.getUniqueErrorCount(),
    summary: errorCollector.getSummary(),
    allErrorsReport: errorCollector.formatAllErrors()
  };
}

// Cleanup function
export function cleanupGlobalErrorHandler() {
  errorNotificationManager.clear();
  errorCollector.clear();
}