from datetime import date
from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, ConfigDict
from shared.models.models import Currency, Customer
from shared.models.models_work_order import WorkOrderPartsJoined
from sqlalchemy import case, select

from app import db


class SqlModel(BaseModel):
    model_config = ConfigDict(from_attributes=True)


class CustomerSchema(SqlModel):
    id: int
    customer: str
    description: Optional[str]
    city: Optional[str]
    postal: Optional[str]


def get_customers():
    query = select(
        Customer.id,
        Customer.customer,
        Customer.description,
        Customer.city,
        Customer.postal,
    )
    results = db.session.execute(query)
    customers = [
        CustomerSchema.model_validate(customer).model_dump() for customer in results
    ]
    return customers


class YearSchema(SqlModel):
    year: int


def get_service_years():
    query = select(
        WorkOrderPartsJoined.year,
    ).group_by(WorkOrderPartsJoined.year)
    results = db.session.execute(query)
    years = [YearSchema.model_validate(year).model_dump() for year in results]
    return years


class CurrencySchema(SqlModel):
    id: int
    name: str


def get_currencies():
    query = select(
        Currency.id,
        Currency.name,
    )
    results = db.session.execute(query)
    currencies = [
        CurrencySchema.model_validate(currency).model_dump() for currency in results
    ]
    return currencies


class SalesSchema(SqlModel):
    work_order_part_id: Optional[int]
    date_service: Optional[date]
    work_order_id: Optional[int]
    structure: Optional[str]
    location: Optional[str]
    model: Optional[str]
    unit_type: Optional[str]
    cost: Optional[Decimal]
    total_sales: Optional[Decimal]
    margin: Optional[Decimal]
    cost_adj: Optional[Decimal]
    margin_adj: Optional[Decimal]


def get_sales():
    calculations = select(
        WorkOrderPartsJoined.work_order_part_id,
        WorkOrderPartsJoined.work_order_id,
        WorkOrderPartsJoined.date_service,
        WorkOrderPartsJoined.structure,
        WorkOrderPartsJoined.location,
        WorkOrderPartsJoined.model,
        WorkOrderPartsJoined.unit_type,
        case(
            (WorkOrderPartsJoined.is_void is True, 0),
            else_=WorkOrderPartsJoined.quantity
            * case(
                (
                    WorkOrderPartsJoined.is_usd_work_order is True,
                    WorkOrderPartsJoined.cost_usd,
                ),
                else_=WorkOrderPartsJoined.cost_cad,
            ),
        ).label("cost"),
        case(
            (WorkOrderPartsJoined.is_void is True, 0),
            else_=WorkOrderPartsJoined.cost_before_tax,
        ).label("total_sales"),
    ).cte("calculations")
    adjusted_calcs = select(
        calculations,
        (calculations.c.total_sales - calculations.c.cost).label("margin"),
        (calculations.c.cost == calculations.c.total_sales).label(
            "same_cost_and_revenue"
        ),
    ).cte("adjusted_calculations")
    query = select(
        adjusted_calcs,
        case(
            (adjusted_calcs.c.same_cost_and_revenue, adjusted_calcs.c.cost / 2.0),
            else_=adjusted_calcs.c.cost,
        ).label("cost_adj"),
        (
            adjusted_calcs.c.total_sales
            - case(
                (adjusted_calcs.c.same_cost_and_revenue, adjusted_calcs.c.cost / 2.0),
                else_=adjusted_calcs.c.cost,
            )
        ).label("margin_adj"),
    )
    results = db.session.execute(query)
    sales = [SalesSchema.model_validate(sale).model_dump() for sale in results]
    return sales
