# app/inertia/views.py
"""
This module contains the Inertia views for the application.
It is used to integrate Inertia.js with Flask and Flask-Login.
It provides a test endpoint to verify the integration.
"""

from flask import Blueprint, abort, redirect
from flask_login import current_user, login_required
from inertia_flask import inertia

from app import config, posthog
from app.utils.complex import must_have_role_ids
from app.utils.simple import check_confirmed

# Required configuration keys

# Create a Blueprint
inertia_bp = Blueprint(
    "bp", __name__, template_folder="inertia_templates", static_folder="react/dist"
)


@inertia_bp.route("/build-and-price")
@inertia_bp.route("/build-and-price/")
@inertia_bp.route("/build-and-price/<path:path>")
@inertia("build-and-price")
def build_and_price(path=None):
    if not current_user.is_authenticated:
        return {
            "user": {
                "id": None,
                "name": None,
                "email": None,
                "roles": [],
            }
        }
    if posthog.feature_enabled("build-and-price-release", f"{current_user.id}"):
        return {
            "user": {
                "id": current_user.id,
                "name": current_user.full_name,
                "email": current_user.email,
                "roles": [role.name for role in current_user.roles_rel],
            }
        }
    else:
        return redirect("/")


@inertia_bp.route("/dashboards")
@inertia_bp.route("/dashboards/")
@inertia_bp.route("/dashboards/<path:dashboard>")
@login_required
@check_confirmed
@must_have_role_ids(
    [
        config.ROLE_ID_IJACK_SALES,
        config.ROLE_ID_IJACK_SOFTWARE_DEV,
        config.ROLE_ID_IJACK_SERVICE,
    ]
)
@inertia("dashboards")
def dashboard_route(dashboard=None):
    # Path-specific permission checking
    user_role_ids = [role.id for role in current_user.roles_rel]

    if dashboard and dashboard.startswith("sales"):
        # Sales dashboard: only SALES + SOFTWARE_DEV roles
        allowed_roles = [config.ROLE_ID_IJACK_SALES, config.ROLE_ID_IJACK_SOFTWARE_DEV]
        if not any(role_id in user_role_ids for role_id in allowed_roles):
            abort(403)  # Forbidden: SERVICE role cannot access sales dashboard

    # Service dashboard allows all three roles (SERVICE, SALES, SOFTWARE_DEV)
    # No additional checking needed as decorator already validates

    return {
        "user": {
            "id": current_user.id,
            "name": current_user.full_name,
            "email": current_user.email,
            "roles": [role.name for role in current_user.roles_rel],
        }
    }


@inertia_bp.route("/admin3")
@inertia_bp.route("/admin3/")
@inertia_bp.route("/admin3/<path:path>")
@login_required
@check_confirmed
@must_have_role_ids([config.ROLE_ID_IJACK_ADMIN])
@inertia("admin3")
def admin3_route(path=None):
    """Refine-based admin panel route"""
    return {
        "user": {
            "id": current_user.id,
            "name": current_user.full_name,
            "email": current_user.email,
            "roles": [role.id for role in current_user.roles_rel],
            "role_names": [role.name for role in current_user.roles_rel],
        },
        "api_base_url": "/api/v1",  # Use Flask proxy route
        "title": "IJACK Admin Panel",
    }


# @inertia_bp.route(
#     "/api/v1/<path:path>", methods=["GET", "POST", "PUT", "DELETE", "PATCH"]
# )
# @login_required
# @check_confirmed
# @must_have_role_ids([config.ROLE_ID_IJACK_ADMIN])
# def admin_api_proxy(path):
#     """Proxy admin API requests to FastAPI backend with authentication"""

#     # Forward the request to FastAPI
#     fastapi_url = f"http://localhost:8000/v1/{path}"

#     # Copy request data
#     data = None
#     if request.method in ["POST", "PUT", "PATCH"]:
#         if request.is_json:
#             data = request.get_json()
#         else:
#             data = request.form.to_dict()

#     # Copy query parameters
#     params = request.args.to_dict()

#     # Copy headers, but add session info for FastAPI authentication
#     headers = {
#         "Content-Type": "application/json",
#         "Accept": "application/json",
#     }

#     # Add session cookie for FastAPI authentication
#     cookies = {}
#     session_cookie_name = f"{os.getenv('ENVIRONMENT', 'development')}_session"
#     if session_cookie_name in request.cookies:
#         cookies[session_cookie_name] = request.cookies[session_cookie_name]

#     try:
#         # Make the request to FastAPI
#         response = requests.request(
#             method=request.method,
#             url=fastapi_url,
#             json=data if request.is_json and data else None,
#             data=data if not request.is_json and data else None,
#             params=params,
#             headers=headers,
#             cookies=cookies,
#             timeout=30,
#         )

#         # Return the response
#         return jsonify(
#             response.json() if response.content else {}
#         ), response.status_code

#     except requests.exceptions.RequestException as e:
#         return jsonify({"error": str(e)}), 500
