import csv
import logging
import os
import re
from decimal import Decimal
from logging.config import fileConfig

from alembic import context
from alembic import op as a_op
from alembic.autogenerate import comparators, renderers, rewriter
from alembic.operations import MigrateOperation, Operations, ops
from flask import current_app
from shared.models.base import metadata
from shared.models.models import *  # noqa: F401, F403
from shared.models.models_work_order import *  # noqa: F401, F403
from sqlalchemy import MetaData, text
from sqlalchemy.types import Date, DateTime

from app.models.models import *  # noqa: F401, F403

USE_TWOPHASE = False

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
fileConfig(config.config_file_name)
logger = logging.getLogger("alembic.env")


def get_engine(bind_key=None):
    try:
        # this works with Flask-SQLAlchemy<3 and Alchemical
        return current_app.extensions["migrate"].db.get_engine(bind=bind_key)
    except (TypeError, AttributeError):
        # this works with Flask-SQLAlchemy>=3
        return current_app.extensions["migrate"].db.engines.get(bind_key)


def get_engine_url(bind_key=None):
    try:
        return (
            get_engine(bind_key)
            .url.render_as_string(hide_password=False)
            .replace("%", "%%")
        )
    except AttributeError:
        return str(get_engine(bind_key).url).replace("%", "%%")


# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
config.set_main_option("sqlalchemy.url", get_engine_url())
bind_names = []
if current_app.config.get("SQLALCHEMY_BINDS") is not None:
    bind_names = list(current_app.config["SQLALCHEMY_BINDS"].keys())
else:
    get_bind_names = getattr(current_app.extensions["migrate"].db, "bind_names", None)
    if get_bind_names:
        bind_names = get_bind_names()
bind_names = list(filter(lambda x: x != "timescale", bind_names))
for bind in bind_names:
    context.config.set_section_option(
        bind, "sqlalchemy.url", get_engine_url(bind_key=bind)
    )
target_db = current_app.extensions["migrate"].db

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def get_metadata(bind):
    """Return the metadata for a bind."""
    if bind is None or bind == "":
        return None
    return metadata
    if bind == "":
        bind = None
    if hasattr(target_db, "metadatas"):
        return target_db.metadatas[bind]

    # legacy, less flexible implementation
    m = MetaData()
    for t in target_db.metadata.tables.values():
        if t.info.get("bind_key") == bind:
            t.tometadata(m)
    return m


@Operations.register_operation("update_view")
class UpdateViewOp(MigrateOperation):
    """Create or Drop a View."""

    def __init__(self, view_name, new_script, old_script):
        self.view_name = view_name
        self.new_script = new_script
        self.old_script = old_script

    @classmethod
    def update_view(cls, operations, view_name, new_script, old_script):
        """Issue a "DROP VIEW" instruction."""

        op = UpdateViewOp(view_name, new_script, old_script)
        return operations.invoke(op)

    def reverse(self):
        # only needed to support autogenerate
        return UpdateViewOp(self.view_name, self.old_script, self.new_script)


@Operations.implementation_for(UpdateViewOp)
def update_view(operations, operation):
    # if script is supplied in the operation we should downgrade the view to the previous script
    if len(operation.new_script.strip()) > 0:
        operations.execute(
            text(
                "CREATE OR REPLACE VIEW %s AS %s"
                % (operation.view_name, operation.new_script)
            )
        )
    else:
        operations.execute(text("DROP VIEW %s" % operation.view_name))


@Operations.register_operation("insert_data")
class InsertDataOp(MigrateOperation):
    """Insert data operations for a table"""

    def __init__(self, table, schema, db_name):
        self.table = table
        self.schema = schema
        self.db_name = db_name

    @classmethod
    def insert_data(cls, operations, table, schema, db_name):
        """Issue a "SQLLDR" instruction."""

        op = InsertDataOp(table, schema, db_name)
        return operations.invoke(op)

    def reverse(self):
        # only needed to support autogenerate
        return InsertDataOp("", "", "")


@Operations.implementation_for(InsertDataOp)
def insert_data(operations, operation):
    if operations.impl.as_sql:
        return
    filename = f"migrations/data_loaders/{f'{operation.db_name.lower()}.' if operation.db_name is not None else ''}{operation.schema.lower()}.{operation.table.lower()}.csv"
    if os.path.isfile(filename):
        if not operations.impl.as_sql:
            print(
                f"EXECUTING: INSERT INTO {str.upper(operation.schema)}.{str.upper(operation.table)} ... VALUES ...",
                end=" ",
            )
        insert_records = []
        with open(filename, mode="r", newline="") as file:
            reader = csv.DictReader(file)
            for row in reader:
                new_row = {}
                for k, v in row.items():
                    key = re.sub(r"[^\x00-\x7F]+", "", k) if k is not None else ""
                    value = v
                    column = (
                        target_db.metadatas[operation.db_name]
                        .tables[f"{operation.schema}.{operation.table}"]
                        .columns.get(key.lower(), None)
                    )
                    if column is not None:
                        if hasattr(column.type, "python_type"):
                            match str(column.type.python_type):
                                case "<class 'int'>":
                                    new_row[key] = int(value) if value != "" else None
                                case "<class 'float'>":
                                    new_row[key] = float(value) if value != "" else None
                                case "<class 'str'>":
                                    new_row[key] = str(value) if value != "" else None
                                case "<class 'uuid.UUID'>":
                                    new_row[key] = str(value) if value != "" else None
                                case "<class 'decimal.Decimal'>":
                                    new_row[key] = (
                                        Decimal(value) if value != "" else None
                                    )
                                case "<class 'bool'>":
                                    new_row[key] = bool(value) if value != "" else None
                                case _:
                                    new_row[key] = str(value) if value != "" else None
                        elif hasattr(column.type, "impl"):
                            if type(column.type.impl) is type(DateTime()) or type(
                                column.type
                            ) is type(Date()):
                                new_row[key] = (
                                    a_op.inline_literal(str(value))
                                    if value != ""
                                    else None
                                )
                        elif type(column.type) is type(DateTime()) or type(
                            column.type
                        ) is type(Date()):
                            new_row[key] = (
                                a_op.inline_literal(str(value)) if value != "" else None
                            )
                lower_new_row = {ky.lower(): vl for ky, vl in new_row.items()}
                insert_records.append(lower_new_row)
        a_op.bulk_insert(
            target_db.metadatas[operation.db_name].tables[
                f"{operation.schema}.{operation.table}"
            ],
            insert_records,
            multiinsert=(not operations.impl.as_sql),
        )
        if not operations.impl.as_sql:
            print("DONE.")


@renderers.dispatch_for(UpdateViewOp)
def render_update_view(autogen_context, op):
    return "op.update_view(%r, %r, %r)" % (op.view_name, op.new_script, op.old_script)


@renderers.dispatch_for(InsertDataOp)
def render_insert_data(autogen_context, op):
    if not op.table:
        return []
    return "op.insert_data(%r, %r, %r)" % (op.table, op.schema, op.db_name)


@comparators.dispatch_for("schema")
def compare_schema_objects(autogen_context, upgrade_ops, schemas):
    all_conn_views = set()

    def formatScript(script):
        return re.sub(" +", " ", script.replace("\n", " ").replace("\t", " ").strip())

    print(autogen_context.dialect.name)
    if autogen_context.dialect.name == "postgresql":
        all_conn_views.update(
            [
                (row)
                for row in map(
                    lambda x: (x[0], formatScript(x[1])),
                    autogen_context.connection.execute(
                        text(
                            "SELECT lower(viewname) AS view_name, definition AS text_vc FROM pg_views WHERE schemaname NOT IN ('pg_catalog', 'information_schema')"
                        )
                    ).all(),
                )
            ]
        )

    metadata_views = []
    for view in filter(
        lambda x: x.info.get("view", False),
        list(autogen_context.metadata.tables.values()),
    ):
        metadata_views.append(
            (view.name.lower(), formatScript(view.info.get("script", "")))
        )

    for view in metadata_views:
        if view in set(metadata_views) - all_conn_views:
            db_view = next(filter(lambda x: x[0] == view[0], all_conn_views), ("", ""))[
                1
            ]
            upgrade_ops.ops.append(UpdateViewOp(view[0], view[1], db_view))

    # directives
    for view in all_conn_views - set(metadata_views):
        meta_view_name = next(
            filter(lambda x: x[0] == view[0], metadata_views), ("", "")
        )[0]
        if meta_view_name == "":
            upgrade_ops.ops.append(UpdateViewOp(view[0], "", view[1]))
    for x in filter(
        lambda x: isinstance(x, ops.CreateTableOp) and not x.info.get("view", False),
        # and os.path.isfile(f'data_loaders/{x.table_name.lower()}/{x.table_name.lower()}.csv'),
        list(upgrade_ops.ops),
    ):
        upgrade_ops.ops.append(
            InsertDataOp(
                x.table_name,
                x.schema,
                autogen_context.metadata.info.get("bind_key"),
            )
        )


# OVERWRITE THE CREATE TABLE DIRECTIVE IN ALEMBIC
writer = rewriter.Rewriter()


@writer.rewrites(ops.CreateTableOp)
def create_table(context, revision, op):
    # op_new = Operations(context)
    if op.info.get("view", False) and op.info.get("script", False):
        return
    if op.info.get("view", False):
        return
    #  insert a data load operation after the op if a data file exists
    # return [op, InsertDataOp(op.table_name)]
    return op


@writer.rewrites(ops.DropTableOp)
def drop_table(context, revision, op):
    if not op.info.get("view", False):
        #  save the table data before the table drop operation
        #  return [ops.SaveData(op.name.lower(), revision), op]
        return op


def run_migrations_offline():
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    # for the --sql use case, run migrations for each URL into
    # individual files.
    engines = {}
    for name in bind_names:
        engines[name] = rec = {}
        rec["url"] = context.config.get_section_option(name, "sqlalchemy.url")

    for name, rec in engines.items():
        logger.info("Migrating database %s" % (name or "<default>"))
        file_ = "%s.sql" % name
        logger.info("Writing output to %s" % file_)
        with open(file_, "w") as buffer:
            context.configure(
                url=rec["url"],
                output_buffer=buffer,
                target_metadata=metadata,
                literal_binds=True,
            )
            with context.begin_transaction():
                context.run_migrations(engine_name=name)


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """

    # this callback is used to prevent an auto-migration from being generated
    # when there are no changes to the schema
    # reference: http://alembic.zzzcomputing.com/en/latest/cookbook.html
    def process_revision_directives(context, revision, directives):
        if getattr(config.cmd_opts, "autogenerate", False):

            def valid_ops(ops):
                # Method to filter out operations from an ops list
                def is_valid_op(op):
                    # If we generate a create table operation that is a view ignore it
                    if op.__class__.__name__ == "CreateTableOp":
                        return not op.info.get("view", False)
                    # If we generate a drop table operation that is a view ignore it
                    if op.__class__.__name__ == "DropTableOp":
                        return not op.info.get("view", False)
                    # All other operations not listed above must be valid
                    return True

                return list(filter(is_valid_op, ops))

            # Set script as a list of operation directives that alembic must run
            script = directives[0]

            # Iterate through scripts to determine if they are valid operations.
            for idx, ops in enumerate(script.upgrade_ops_list):
                script.upgrade_ops_list[idx].ops = valid_ops(ops.ops)
            for idx, ops in enumerate(script.downgrade_ops_list):
                script.downgrade_ops_list[idx].ops = valid_ops(ops.ops)
            # script.upgrade_ops.ops = valid_ops(script.upgrade_ops.ops)
            # script.downgrade_ops.ops = valid_ops(script.downgrade_ops.ops)
            total_length = sum(
                len(getattr(item, "ops")) for item in script.upgrade_ops_list
            )
            if total_length == 0:
                empty = True
                for upgrade_ops in script.upgrade_ops_list:
                    if not upgrade_ops.is_empty():
                        empty = False
                if empty:
                    directives[:] = []
                    logger.info("No changes in schema detected.")

    conf_args = current_app.extensions["migrate"].configure_args
    if conf_args.get("process_revision_directives") is None:
        conf_args["process_revision_directives"] = process_revision_directives

    # for the direct-to-DB use case, start a transaction on all
    # engines, then run all migrations, then commit all transactions.
    engines = {}
    for name in bind_names:
        engines[name] = rec = {}
        rec["engine"] = get_engine(bind_key=name)

    for name, rec in engines.items():
        engine = rec["engine"]
        rec["connection"] = conn = engine.connect()

        if USE_TWOPHASE:
            rec["transaction"] = conn.begin_twophase()
        else:
            rec["transaction"] = conn.begin()

    try:
        for name, rec in engines.items():
            logger.info("Migrating database %s" % (name or "<default>"))
            context.configure(
                connection=rec["connection"],
                upgrade_token="%s_upgrades" % name,
                downgrade_token="%s_downgrades" % name,
                target_metadata=metadata,
                **conf_args,
            )
            context.run_migrations(engine_name=name)

        if USE_TWOPHASE:
            for rec in engines.values():
                rec["transaction"].prepare()

        for rec in engines.values():
            rec["transaction"].commit()
    except:  # noqa: E722
        for rec in engines.values():
            rec["transaction"].rollback()
        raise
    finally:
        for rec in engines.values():
            rec["connection"].close()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
