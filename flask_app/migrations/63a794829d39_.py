"""empty message

Revision ID: 63a794829d39
Revises:
Create Date: 2025-06-10 20:41:24.116898

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "63a794829d39"
down_revision = None
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_ijack():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "alarm_log",
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("timestamp_local", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("power_unit", sa.VARCHAR(), nullable=False),
        sa.Column("gateway", sa.VARCHAR(), nullable=True),
        sa.Column("abbrev", sa.VARCHAR(), nullable=False),
        sa.Column("value", sa.VARCHAR(), nullable=False),
        sa.Column("index", sa.SMALLINT(), nullable=False),
        sa.PrimaryKeyConstraint("power_unit", "abbrev", "value"),
        schema="public",
    )
    op.create_table(
        "alarm_log_metrics",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("abbrev", sa.VARCHAR(), nullable=False),
        sa.Column("index", sa.SMALLINT(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=True),
        sa.Column("description", sa.VARCHAR(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "alerts_sent",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("gateway", sa.VARCHAR(), nullable=True),
        sa.Column("power_unit", sa.VARCHAR(), nullable=False),
        sa.Column("good1_bad0", sa.SMALLINT(), nullable=True),
        sa.Column("warning_msg", sa.TEXT(), nullable=True),
        sa.Column("explanation", sa.TEXT(), nullable=True),
        sa.Column("program", sa.VARCHAR(), nullable=True),
        sa.Column("function", sa.VARCHAR(), nullable=True),
        sa.Column("dev_test_prd", sa.VARCHAR(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "alerts_sent_maint_email_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.VARCHAR(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "alerts_sent_other",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_sent", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("alert_type", sa.VARCHAR(), nullable=False),
        sa.Column("power_unit_str", sa.VARCHAR(), nullable=True),
        sa.Column("aws_thing", sa.VARCHAR(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "alerts_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "barrels",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "bom_base_powerunit",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "bom_dgas",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "bom_powerunit",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "bom_pricing",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "bom_pump_top",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "bom_structure",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "calorie_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "check_valves",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "compression_patterns",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=False),
        sa.Column("send_alert", sa.BOOLEAN(), nullable=False),
        sa.Column("solution", sa.TEXT(), nullable=True),
        sa.Column("explanation", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("description"),
        schema="public",
    )
    op.create_table(
        "countries",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("country_code", sa.String(length=2), nullable=False),
        sa.Column("country_name", sa.String(length=45), nullable=False),
        sa.Column("sales_tax_rate", sa.NUMERIC(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("country_code"),
        sa.UniqueConstraint("country_name"),
        schema="public",
    )
    op.create_table(
        "days",
        sa.Column("day", sa.SMALLINT(), nullable=False),
        sa.PrimaryKeyConstraint("day"),
        schema="public",
    )
    op.create_table(
        "days_of_week",
        sa.Column("id", sa.SMALLINT(), nullable=False),
        sa.Column("name", sa.VARCHAR(length=3), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "diagnostic",
        sa.Column("power_unit_str", sa.VARCHAR(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_modified", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("is_main", sa.BOOLEAN(), nullable=False),
        sa.Column("msg_type", sa.SMALLINT(), nullable=False),
        sa.Column("diag_num", sa.SMALLINT(), nullable=False),
        sa.Column("value", sa.NUMERIC(), nullable=False),
        sa.PrimaryKeyConstraint(
            "power_unit_str", "timestamp_utc", "is_main", "msg_type", "diag_num"
        ),
        schema="public",
    )
    op.create_table(
        "diagnostic_metrics",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_modified", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("diag_num", sa.SMALLINT(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("decimals", sa.SMALLINT(), nullable=False),
        sa.Column("units", sa.VARCHAR(), nullable=True),
        sa.Column("color", sa.VARCHAR(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "gateway_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "gw_cell_networks",
        sa.Column("id", sa.SMALLINT(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.VARCHAR(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "hours",
        sa.Column("hour", sa.SMALLINT(), nullable=False),
        sa.Column("hour_ending", sa.SMALLINT(), nullable=True),
        sa.Column(
            "id",
            sa.SMALLINT(),
            sa.Computed(
                "hour",
            ),
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("hour"),
        sa.UniqueConstraint("hour_ending"),
        schema="public",
    )
    op.create_table(
        "hyd_piston_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "images",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.String(length=60), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("format", sa.String(length=10), nullable=True),
        sa.Column("image", postgresql.BYTEA(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "inventory_sources",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.VARCHAR(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "maintenance_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "map_abbrev_item",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("cob", sa.VARCHAR(), nullable=False),
        sa.Column("byte_start_index", sa.SMALLINT(), nullable=True),
        sa.Column("bit_start_index", sa.SMALLINT(), nullable=True),
        sa.Column("abbrev", sa.VARCHAR(), nullable=True),
        sa.Column("plus1_variable", sa.VARCHAR(), nullable=True),
        sa.Column("plus1_program", sa.VARCHAR(), nullable=True),
        sa.Column("rcom_name", sa.VARCHAR(), nullable=True),
        sa.Column("rcom_tab", sa.VARCHAR(), nullable=True),
        sa.Column("machine", sa.VARCHAR(), nullable=True),
        sa.Column("item", sa.VARCHAR(), nullable=True),
        sa.Column("units", sa.VARCHAR(), nullable=True),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("num_bytes", sa.SMALLINT(), nullable=True),
        sa.Column("data_type", sa.VARCHAR(), nullable=True),
        sa.Column("decimals", sa.SMALLINT(), nullable=False),
        sa.Column("signed", sa.BOOLEAN(), nullable=False),
        sa.Column("resolution", sa.VARCHAR(), nullable=True),
        sa.Column("offset_", sa.VARCHAR(), nullable=True),
        sa.Column("number_", sa.VARCHAR(), nullable=True),
        sa.Column("interesting", sa.VARCHAR(), nullable=True),
        sa.Column("controller_version", sa.VARCHAR(), nullable=True),
        sa.Column("color", sa.VARCHAR(), nullable=True),
        sa.Column("control_num", sa.INTEGER(), nullable=True),
        sa.Column("enum", sa.INTEGER(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "abbrev",
            "cob",
            "byte_start_index",
            "bit_start_index",
            name="map_abbrev_item_unique_cob_byte_bit_abbrev",
        ),
        schema="public",
    )
    op.create_table(
        "meta_data",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("id_cell", sa.VARCHAR(), nullable=False),
        sa.Column("element", sa.VARCHAR(), nullable=False),
        sa.Column("color", sa.VARCHAR(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "months",
        sa.Column("month", sa.SMALLINT(), nullable=False),
        sa.PrimaryKeyConstraint("month"),
        schema="public",
    )
    op.create_table(
        "packing_glands",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "part_filters",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("part_num", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("part_num"),
        schema="public",
    )
    op.create_table(
        "parts",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("part_num", sa.VARCHAR(), nullable=False),
        sa.Column(
            "part_name",
            sa.VARCHAR(),
            sa.Computed(
                "regexp_replace(part_num, 'r\\d*$'::text, ''::text)",
            ),
            nullable=False,
        ),
        sa.Column(
            "part_rev",
            sa.DOUBLE_PRECISION(),
            sa.Computed(
                "case when substring(part_num FROM 'r(\\d+)'::text)::double precision is null then 0::double precision else substring(part_num FROM 'r(\\d+)'::text)::double precision end",
            ),
            nullable=False,
        ),
        sa.Column("description", sa.VARCHAR(), nullable=True),
        sa.Column("part_image", postgresql.BYTEA(), nullable=True),
        sa.Column("no_delete", sa.BOOLEAN(), nullable=True),
        sa.Column("flagged_for_deletion", sa.BOOLEAN(), nullable=False),
        sa.Column("worksheet", sa.VARCHAR(), nullable=True),
        sa.Column("ws_row", sa.SMALLINT(), nullable=True),
        sa.Column("cost_cad", sa.NUMERIC(), nullable=False),
        sa.Column("cost_usd", sa.NUMERIC(), nullable=False),
        sa.Column("msrp_mult_cad", sa.REAL(), nullable=False),
        sa.Column("msrp_mult_usd", sa.REAL(), nullable=False),
        sa.Column("msrp_cad", sa.NUMERIC(), nullable=False),
        sa.Column("msrp_usd", sa.NUMERIC(), nullable=False),
        sa.Column("transfer_mult_cad_dealer", sa.REAL(), nullable=False),
        sa.Column("transfer_mult_usd_dealer", sa.REAL(), nullable=False),
        sa.Column("transfer_mult_inc_to_corp", sa.REAL(), nullable=False),
        sa.Column("warehouse_mult", sa.REAL(), nullable=False),
        sa.Column("dealer_cost_cad", sa.NUMERIC(), nullable=False),
        sa.Column("dealer_cost_usd", sa.NUMERIC(), nullable=False),
        sa.Column("ijack_corp_cost", sa.NUMERIC(), nullable=False),
        sa.Column(
            "ijack_corp_cost_usd",
            sa.NUMERIC(),
            sa.Computed(
                "msrp_usd * transfer_mult_inc_to_corp",
            ),
            nullable=False,
        ),
        sa.Column(
            "ijack_corp_cost_cad",
            sa.NUMERIC(),
            sa.Computed(
                "msrp_cad * transfer_mult_inc_to_corp",
            ),
            nullable=False,
        ),
        sa.Column("is_usd", sa.BOOLEAN(), nullable=False),
        sa.Column("cad_per_usd", sa.REAL(), nullable=True),
        sa.Column("is_soft_part", sa.BOOLEAN(), nullable=False),
        sa.Column("is_hard_part", sa.BOOLEAN(), nullable=False),
        sa.Column("harmonization_code", sa.VARCHAR(), nullable=True),
        sa.Column("country_of_origin", sa.VARCHAR(), nullable=True),
        sa.Column("weight", sa.VARCHAR(), nullable=True),
        sa.Column(
            "mass",
            sa.REAL(),
            sa.Computed(
                "CASE WHEN ((weight)::text ~ '^[0-9]+\\.?[0-9]*$'::text) THEN (weight)::real ELSE NULL::real END",
            ),
            nullable=True,
        ),
        sa.Column("lead_time", sa.REAL(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("part_num"),
        schema="public",
    )
    op.create_table(
        "release_notes",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("version", sa.DOUBLE_PRECISION(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=False),
        sa.Column("is_stable", sa.BOOLEAN(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("version"),
        schema="public",
    )
    op.create_table(
        "report_email_op_hours_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "rods",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "roles",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.String(length=60), nullable=False),
        sa.Column("description", sa.String(length=200), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "service_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.VARCHAR(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "shuttle_valves",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "surface_patterns",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=False),
        sa.Column("send_alert", sa.BOOLEAN(), nullable=False),
        sa.Column("solution", sa.TEXT(), nullable=True),
        sa.Column("explanation", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("description"),
        schema="public",
    )
    op.create_table(
        "time_series",
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("power_unit", sa.VARCHAR(), nullable=False),
        sa.Column("gateway", sa.VARCHAR(), nullable=True),
        sa.Column("spm", sa.REAL(), nullable=True),
        sa.Column("spm_egas", sa.REAL(), nullable=True),
        sa.Column("cgp", sa.REAL(), nullable=True),
        sa.Column("dgp", sa.REAL(), nullable=True),
        sa.Column("dtp", sa.REAL(), nullable=True),
        sa.Column("hpu", sa.REAL(), nullable=True),
        sa.Column("hpe", sa.REAL(), nullable=True),
        sa.Column("ht", sa.REAL(), nullable=True),
        sa.Column("ht_egas", sa.REAL(), nullable=True),
        sa.Column("agft", sa.REAL(), nullable=True),
        sa.Column("mgp", sa.REAL(), nullable=True),
        sa.Column("mgp_sl", sa.REAL(), nullable=True),
        sa.Column("agfm", sa.REAL(), nullable=True),
        sa.Column("agfn", sa.REAL(), nullable=True),
        sa.Column("e3m3_d", sa.REAL(), nullable=True),
        sa.Column("m3pd", sa.REAL(), nullable=True),
        sa.Column("hyd", sa.BOOLEAN(), nullable=True),
        sa.Column("hyd_egas", sa.BOOLEAN(), nullable=True),
        sa.Column("warn1", sa.BOOLEAN(), nullable=True),
        sa.Column("warn1_egas", sa.BOOLEAN(), nullable=True),
        sa.Column("warn2", sa.BOOLEAN(), nullable=True),
        sa.Column("warn2_egas", sa.BOOLEAN(), nullable=True),
        sa.Column("mtr", sa.BOOLEAN(), nullable=True),
        sa.Column("mtr_egas", sa.BOOLEAN(), nullable=True),
        sa.Column("clr", sa.BOOLEAN(), nullable=True),
        sa.Column("clr_egas", sa.BOOLEAN(), nullable=True),
        sa.Column("htr", sa.BOOLEAN(), nullable=True),
        sa.Column("htr_egas", sa.BOOLEAN(), nullable=True),
        sa.Column("aux_egas", sa.BOOLEAN(), nullable=True),
        sa.Column("prs", sa.BOOLEAN(), nullable=True),
        sa.Column("sbf", sa.BOOLEAN(), nullable=True),
        sa.Column("ngp", sa.REAL(), nullable=True),
        sa.Column("oh", sa.SMALLINT(), nullable=True),
        sa.Column("sh", sa.SMALLINT(), nullable=True),
        sa.Column("signal", sa.SMALLINT(), nullable=True),
        sa.PrimaryKeyConstraint("timestamp_utc", "power_unit"),
        schema="public",
    )
    op.create_table(
        "time_series_agg",
        sa.Column("power_unit", sa.VARCHAR(), nullable=False),
        sa.Column("month_date", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_modified", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("sample_size", sa.INTEGER(), nullable=True),
        sa.Column("stroke_speed_avg", sa.REAL(), nullable=True),
        sa.Column("hp_limit", sa.REAL(), nullable=True),
        sa.Column("hp_avg", sa.REAL(), nullable=True),
        sa.Column("mgp_avg", sa.REAL(), nullable=True),
        sa.Column("dgp_avg", sa.REAL(), nullable=True),
        sa.Column("agf_dis_temp_max_avg", sa.REAL(), nullable=True),
        sa.Column("agf_dis_temp_avg", sa.REAL(), nullable=True),
        sa.Column("dtp_avg", sa.REAL(), nullable=True),
        sa.Column("dtp_max_avg", sa.REAL(), nullable=True),
        sa.PrimaryKeyConstraint("power_unit", "month_date"),
        schema="public",
    )
    op.create_table(
        "time_series_diagnostic",
        sa.Column("power_unit_str", sa.VARCHAR(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_modified", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("diag_num", sa.SMALLINT(), nullable=False),
        sa.Column("is_main", sa.BOOLEAN(), nullable=False),
        sa.Column("value", sa.NUMERIC(), nullable=False),
        sa.PrimaryKeyConstraint(
            "power_unit_str", "timestamp_utc", "diag_num", "is_main"
        ),
        schema="public",
    )
    op.create_table(
        "time_series_rt",
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("power_unit", sa.VARCHAR(), nullable=False),
        sa.Column("metric", sa.VARCHAR(), nullable=False),
        sa.Column("value", sa.REAL(), nullable=True),
        sa.PrimaryKeyConstraint("timestamp_utc", "power_unit", "metric"),
        schema="public",
    )
    op.create_table(
        "unit_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("unit_type", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("can_show_to_customers", sa.BOOLEAN(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("unit_type"),
        schema="public",
    )
    op.create_table(
        "work_order_status",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "application_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", sa.DATE(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("unit_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["unit_type_id"],
            ["public.unit_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "bom_base_powerunit_part_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_base_powerunit.id"],
        ),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "bom_dgas_part_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_dgas.id"],
        ),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "bom_powerunit_part_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_powerunit.id"],
        ),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "bom_pricing_part_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"], ["public.bom_pricing.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["part_id"], ["public.parts.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "bom_pump_top_part_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_pump_top.id"],
        ),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "bom_structure_part_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_structure.id"],
        ),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "compression_images",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("cluster_id", sa.INTEGER(), nullable=False),
        sa.Column("ml_version", sa.INTEGER(), nullable=False),
        sa.Column("image", postgresql.BYTEA(), nullable=False),
        sa.Column("num_in_cluster", sa.INTEGER(), nullable=True),
        sa.Column("pattern_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["pattern_id"],
            ["public.compression_patterns.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "cluster_id",
            "ml_version",
            name="compression_images_cluster_id_ml_version_key",
        ),
        schema="public",
    )
    op.create_table(
        "currencies",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("fx_rate_cad_per", sa.NUMERIC(), nullable=False),
        sa.Column("country_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["country_id"],
            ["public.countries.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "modbus_holding_registers",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("abbrev_id", sa.INTEGER(), nullable=False),
        sa.Column("address", sa.SMALLINT(), nullable=False),
        sa.Column("n_registers", sa.SMALLINT(), nullable=False),
        sa.Column(
            "holding_reg_40k",
            sa.SMALLINT(),
            sa.Computed(
                "address + 40001",
            ),
            nullable=False,
        ),
        sa.Column("in_web_api", sa.BOOLEAN(), nullable=False),
        sa.Column("writable_modbus", sa.BOOLEAN(), nullable=False),
        sa.Column("writable_web_api", sa.BOOLEAN(), nullable=False),
        sa.Column("min_val", sa.REAL(), nullable=False),
        sa.Column("max_val", sa.REAL(), nullable=False),
        sa.ForeignKeyConstraint(
            ["abbrev_id"],
            ["public.map_abbrev_item.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("address"),
        sa.UniqueConstraint("address", name="modbus_holding_registers_address_key"),
        schema="public",
    )
    op.create_table(
        "model_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=True),
        sa.Column("part_num", sa.VARCHAR(), nullable=True),
        sa.Column(
            "part_name",
            sa.VARCHAR(),
            sa.Computed(
                "regexp_replace(part_num, 'r\\d*$'::text, ''::text)",
            ),
            nullable=True,
        ),
        sa.Column(
            "part_rev",
            sa.DOUBLE_PRECISION(),
            sa.Computed(
                "case when substring(part_num FROM 'r(\\d+)'::text)::double precision is null then 0::double precision else substring(part_num FROM 'r(\\d+)'::text)::double precision end",
            ),
            nullable=True,
        ),
        sa.Column("model", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("can_show_to_customers", sa.BOOLEAN(), nullable=False),
        sa.Column("color", sa.VARCHAR(), nullable=True),
        sa.Column("max_delta_p", sa.INTEGER(), nullable=True),
        sa.Column("unit_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.ForeignKeyConstraint(
            ["unit_type_id"],
            ["public.unit_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("model"),
        sa.UniqueConstraint("part_num"),
        schema="public",
    )
    op.create_table(
        "model_types_options",
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("part_id"),
        schema="public",
    )
    op.create_table(
        "power_unit_types",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=True),
        sa.Column("part_num", sa.VARCHAR(), nullable=True),
        sa.Column(
            "part_name",
            sa.VARCHAR(),
            sa.Computed(
                "regexp_replace(part_num, 'r\\d*$'::text, ''::text)",
            ),
            nullable=True,
        ),
        sa.Column(
            "part_rev",
            sa.DOUBLE_PRECISION(),
            sa.Computed(
                "case when substring(part_num FROM 'r(\\d+)'::text)::double precision is null then 0::double precision else substring(part_num FROM 'r(\\d+)'::text)::double precision end",
            ),
            nullable=True,
        ),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        sa.UniqueConstraint("part_num"),
        schema="public",
    )
    op.create_table(
        "power_unit_types_options",
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("part_id"),
        schema="public",
    )
    op.create_table(
        "power_unit_types_power",
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("part_id"),
        schema="public",
    )
    op.create_table(
        "power_unit_types_speeds",
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("part_id"),
        schema="public",
    )
    op.create_table(
        "power_unit_types_voltage",
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("part_id"),
        schema="public",
    )
    op.create_table(
        "provinces",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("abbrev", sa.String(length=2), nullable=False),
        sa.Column("country_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["country_id"],
            ["public.countries.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("abbrev"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "surface_images",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("cluster_id", sa.INTEGER(), nullable=False),
        sa.Column("ml_version", sa.INTEGER(), nullable=False),
        sa.Column("image", postgresql.BYTEA(), nullable=False),
        sa.Column("num_in_cluster", sa.INTEGER(), nullable=True),
        sa.Column("pattern_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["pattern_id"],
            ["public.surface_patterns.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "cluster_id", "ml_version", name="surface_images_cluster_id_ml_version_key"
        ),
        schema="public",
    )
    op.create_table(
        "time_zones",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("time_zone", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.VARCHAR(), nullable=True),
        sa.Column("can_show_to_customers", sa.BOOLEAN(), nullable=False),
        sa.Column("country_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["country_id"],
            ["public.countries.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("time_zone"),
        schema="public",
    )
    op.create_table(
        "bom_base_powerunit_power_unit_type_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("power_unit_type_id", sa.INTEGER(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_base_powerunit.id"],
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_type_id"],
            ["public.power_unit_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "power_unit_type_id"),
        schema="public",
    )
    op.create_table(
        "bom_dgas_model_type_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_dgas.id"],
        ),
        sa.ForeignKeyConstraint(
            ["model_type_id"],
            ["public.model_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "model_type_id"),
        schema="public",
    )
    op.create_table(
        "bom_powerunit_power_unit_type_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("power_unit_type_id", sa.INTEGER(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_powerunit.id"],
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_type_id"],
            ["public.power_unit_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("power_unit_type_id", "finished_good_id"),
        schema="public",
    )
    op.create_table(
        "bom_pricing_model_type_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_pricing.id"],
        ),
        sa.ForeignKeyConstraint(
            ["model_type_id"], ["public.model_types.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "model_type_id"),
        schema="public",
    )
    op.create_table(
        "bom_pump_top_model_type_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_pump_top.id"],
        ),
        sa.ForeignKeyConstraint(
            ["model_type_id"],
            ["public.model_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "model_type_id"),
        schema="public",
    )
    op.create_table(
        "bom_structure_model_type_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.Column("finished_good_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["finished_good_id"],
            ["public.bom_structure.id"],
        ),
        sa.ForeignKeyConstraint(
            ["model_type_id"],
            ["public.model_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("finished_good_id", "model_type_id"),
        schema="public",
    )
    op.create_table(
        "calculators",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("model_type_id", sa.INTEGER(), nullable=True),
        sa.Column("power_unit_type_id", sa.INTEGER(), nullable=True),
        sa.Column("diameter", sa.FLOAT(), nullable=True),
        sa.Column("area", sa.FLOAT(), nullable=True),
        sa.Column("stroke", sa.FLOAT(), nullable=True),
        sa.Column("max_spm", sa.FLOAT(), nullable=True),
        sa.Column("max_delta_p", sa.FLOAT(), nullable=True),
        sa.Column("mawp", sa.FLOAT(), nullable=True),
        sa.Column("rod_size", sa.FLOAT(), nullable=True),
        sa.Column("motor_hp", sa.FLOAT(), nullable=True),
        sa.Column("hyds", sa.FLOAT(), nullable=True),
        sa.Column(
            "max_liquid_m3pd",
            sa.FLOAT(),
            sa.Computed(
                "floor(area * stroke * max_spm * 24 / 1017 / 100) * 100",
            ),
            nullable=True,
        ),
        sa.Column(
            "min_liquid_m3pd_10pct",
            sa.FLOAT(),
            sa.Computed(
                "floor(area * stroke * max_spm * 24 / 1017 / 100) * 100 * 0.1",
            ),
            nullable=True,
        ),
        sa.Column("hyd_size_inch", sa.FLOAT(), nullable=True),
        sa.Column("single_loss_cu_inch", sa.FLOAT(), nullable=True),
        sa.Column("port_area_sq_inch", sa.FLOAT(), nullable=True),
        sa.Column(
            "single_port_gas_level",
            sa.FLOAT(),
            sa.Computed(
                "((pi() / 4 * diameter * diameter) - port_area_sq_inch) / (pi() / 4 * diameter * diameter)",
            ),
            nullable=True,
        ),
        sa.Column("charge_pump_disp_cc", sa.FLOAT(), nullable=True),
        sa.Column("charge_pump_press_psi", sa.FLOAT(), nullable=True),
        sa.Column("friction_min_delta_p", sa.FLOAT(), nullable=True),
        sa.Column("friction_max_delta_p", sa.FLOAT(), nullable=True),
        sa.ForeignKeyConstraint(
            ["model_type_id"],
            ["public.model_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_type_id"],
            ["public.power_unit_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "counties",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("county_code", sa.VARCHAR(), nullable=True),
        sa.Column("province_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["province_id"],
            ["public.provinces.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "province_id"),
        schema="public",
    )
    op.create_table(
        "customers",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("customer", sa.String(length=60), nullable=True),
        sa.Column("formal", sa.VARCHAR(), nullable=True),
        sa.Column("description", sa.String(length=200), nullable=True),
        sa.Column("is_tax_exempt", sa.BOOLEAN(), nullable=False),
        sa.Column("gst_hst_number", sa.VARCHAR(length=15), nullable=True),
        sa.Column("mqtt_topic", sa.String(length=10), nullable=True),
        sa.Column("unit", sa.VARCHAR(), nullable=True),
        sa.Column("street", sa.VARCHAR(), nullable=True),
        sa.Column("city", sa.VARCHAR(), nullable=True),
        sa.Column("postal", sa.VARCHAR(), nullable=True),
        sa.Column("country_id", sa.INTEGER(), nullable=False),
        sa.Column("province_id", sa.INTEGER(), nullable=True),
        sa.Column("accounting_contact_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["accounting_contact_id"],
            ["public.users.id"],
            name="customers_accounting_contact_id_fkey",
            initially="DEFERRED",
            deferrable=True,
            use_alter=True,
        ),
        sa.ForeignKeyConstraint(
            ["country_id"],
            ["public.countries.id"],
        ),
        sa.ForeignKeyConstraint(
            ["province_id"],
            ["public.provinces.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("customer"),
        sa.UniqueConstraint("formal"),
        schema="public",
    )
    op.create_table(
        "model_types_parts_pm_seal_kits_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["model_type_id"],
            ["public.model_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("model_type_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "model_types_parts_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["model_type_id"],
            ["public.model_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("model_type_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "power_unit_types_filters_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("power_unit_type_id", sa.INTEGER(), nullable=False),
        sa.Column("part_filter_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["part_filter_id"], ["public.part_filters.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_type_id"], ["public.power_unit_types.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("power_unit_type_id", "part_filter_id"),
        schema="public",
    )
    op.create_table(
        "power_unit_types_parts_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("power_unit_type_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_type_id"],
            ["public.power_unit_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("power_unit_type_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "power_units",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("power_unit", sa.DOUBLE_PRECISION(), nullable=False),
        sa.Column(
            "power_unit_str",
            sa.TEXT(),
            sa.Computed(
                "CAST(power_unit as VARCHAR(10))",
            ),
            nullable=False,
        ),
        sa.Column("power_unit_type_id", sa.INTEGER(), nullable=True),
        sa.Column("run_mfg_date", sa.DATE(), nullable=True),
        sa.Column("power", sa.VARCHAR(), nullable=True),
        sa.Column("voltage", sa.INTEGER(), nullable=True),
        sa.Column("notes", sa.TEXT(), nullable=True),
        sa.Column("website_card_msg", sa.BOOLEAN(), nullable=False),
        sa.Column("apn", sa.VARCHAR(), nullable=True),
        sa.Column("heartbeat_enabled", sa.BOOLEAN(), nullable=True),
        sa.Column("online_hb_enabled", sa.BOOLEAN(), nullable=True),
        sa.Column("alerts_edge", sa.BOOLEAN(), nullable=False),
        sa.Column("change_detect_sens", sa.NUMERIC(), nullable=True),
        sa.Column("suction", sa.INTEGER(), nullable=False),
        sa.Column("discharge", sa.INTEGER(), nullable=False),
        sa.Column("spm", sa.INTEGER(), nullable=False),
        sa.Column("hyd_temp", sa.INTEGER(), nullable=False),
        sa.Column("stboxf", sa.SMALLINT(), nullable=False),
        sa.Column("hyd_oil_lvl_thresh", sa.SMALLINT(), nullable=False),
        sa.Column("hyd_filt_life_thresh", sa.SMALLINT(), nullable=False),
        sa.Column("hyd_oil_life_thresh", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_ol", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_spm", sa.SMALLINT(), nullable=True),
        sa.Column("wait_time_mins_suction", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_discharge", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_hyd_temp", sa.INTEGER(), nullable=False),
        sa.Column("wait_time_mins_stboxf", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_hyd_oil_lvl", sa.INTEGER(), nullable=False),
        sa.Column("wait_time_mins_hyd_filt_life", sa.INTEGER(), nullable=False),
        sa.Column("wait_time_mins_hyd_oil_life", sa.INTEGER(), nullable=False),
        sa.Column("wait_time_mins_chk_mtr_ovld", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_pwr_fail", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_soft_start_err", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_grey_wire_err", sa.SMALLINT(), nullable=False),
        sa.Column("wait_time_mins_ae011", sa.SMALLINT(), nullable=False),
        sa.ForeignKeyConstraint(
            ["power_unit_type_id"],
            ["public.power_unit_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("power_unit"),
        schema="public",
    )
    op.create_table(
        "sales_taxes",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("province_id", sa.INTEGER(), nullable=False),
        sa.Column("rate", sa.NUMERIC(), nullable=False),
        sa.ForeignKeyConstraint(
            ["province_id"],
            ["public.provinces.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "users",
        sa.Column("is_active", sa.BOOLEAN(), nullable=False),
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("date_created", sa.DATE(), nullable=False),
        sa.Column("email", sa.String(length=60), nullable=False),
        sa.Column("first_name", sa.String(length=60), nullable=False),
        sa.Column("last_name", sa.String(length=60), nullable=False),
        sa.Column(
            "full_name",
            sa.VARCHAR(),
            sa.Computed(
                "first_name || ' ' || last_name",
            ),
            nullable=True,
        ),
        sa.Column("password_hash", sa.VARCHAR(), nullable=False),
        sa.Column("uuid", sa.UUID(), nullable=False),
        sa.Column("is_superuser", sa.BOOLEAN(), nullable=True),
        sa.Column("user_lat", sa.DOUBLE_PRECISION(), nullable=False),
        sa.Column("user_lon", sa.DOUBLE_PRECISION(), nullable=False),
        sa.Column("company", sa.String(length=60), nullable=True),
        sa.Column("job_title", sa.VARCHAR(), nullable=True),
        sa.Column("city", sa.String(length=60), nullable=True),
        sa.Column("country_id", sa.INTEGER(), nullable=False),
        sa.Column("customer_id", sa.INTEGER(), nullable=False),
        sa.Column("phone", sa.String(length=18), nullable=True),
        sa.Column("is_us_phone", sa.BOOLEAN(), nullable=False),
        sa.Column("time_zone_id", sa.INTEGER(), nullable=False),
        sa.Column("is_confirmed", sa.BOOLEAN(), nullable=False),
        sa.Column("confirmed_at", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("sms_stop_all", sa.BOOLEAN(), nullable=False),
        sa.Column("sms_confirmed_at_utc", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("sms_stopped_at_utc", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("whatsapp_stop_all", sa.BOOLEAN(), nullable=False),
        sa.Column("whatsapp_confirmed_at_utc", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("whatsapp_stopped_at_utc", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("alerts_paused_until_utc", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("current_login_at", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("prev_login_at", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("current_login_ip", sa.VARCHAR(), nullable=True),
        sa.Column("last_login_ip", sa.VARCHAR(), nullable=True),
        sa.Column("login_count", sa.INTEGER(), nullable=True),
        sa.Column("last_seen_at", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("last_error_at", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("eml_unsubscribe_all", sa.BOOLEAN(), nullable=True),
        sa.Column("eml_marketing", sa.BOOLEAN(), nullable=True),
        sa.Column("eml_new_products", sa.BOOLEAN(), nullable=True),
        sa.Column("eml_service", sa.BOOLEAN(), nullable=True),
        sa.Column("eml_rcom", sa.BOOLEAN(), nullable=True),
        sa.Column(
            "eml_unsubscribe_all_stopped_at_utc", postgresql.TIMESTAMP(), nullable=True
        ),
        sa.Column(
            "eml_marketing_stopped_at_utc", postgresql.TIMESTAMP(), nullable=True
        ),
        sa.Column(
            "eml_new_products_stopped_at_utc", postgresql.TIMESTAMP(), nullable=True
        ),
        sa.Column("eml_service_stopped_at_utc", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("eml_rcom_stopped_at_utc", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("notify_service_requests", sa.BOOLEAN(), nullable=True),
        sa.ForeignKeyConstraint(
            ["country_id"],
            ["public.countries.id"],
        ),
        sa.ForeignKeyConstraint(
            ["customer_id"],
            ["public.customers.id"],
            name="users_fk_customer_id",
            initially="DEFERRED",
            deferrable=True,
            use_alter=True,
        ),
        sa.ForeignKeyConstraint(
            ["time_zone_id"],
            ["public.time_zones.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email"),
        sa.UniqueConstraint("phone"),
        sa.UniqueConstraint("uuid"),
        schema="public",
    )
    op.create_table(
        "warehouses",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("can_show_to_customers", sa.BOOLEAN(), nullable=False),
        sa.Column("is_active", sa.BOOLEAN(), nullable=False),
        sa.Column("gps_lat", sa.REAL(), nullable=True),
        sa.Column("gps_lon", sa.REAL(), nullable=True),
        sa.Column("address", sa.TEXT(), nullable=True),
        sa.Column("city", sa.VARCHAR(), nullable=True),
        sa.Column("zip_code", sa.VARCHAR(), nullable=True),
        sa.Column("province_id", sa.INTEGER(), nullable=True),
        sa.Column("country_id", sa.INTEGER(), nullable=True),
        sa.Column("time_zone_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["country_id"],
            ["public.countries.id"],
        ),
        sa.ForeignKeyConstraint(
            ["province_id"],
            ["public.provinces.id"],
        ),
        sa.ForeignKeyConstraint(
            ["time_zone_id"],
            ["public.time_zones.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "alerts",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=False),
        sa.Column("heartbeat", sa.BOOLEAN(), nullable=False),
        sa.Column("online_hb", sa.BOOLEAN(), nullable=False),
        sa.Column("warn1", sa.BOOLEAN(), nullable=False),
        sa.Column("warn2", sa.BOOLEAN(), nullable=False),
        sa.Column("suction", sa.BOOLEAN(), nullable=False),
        sa.Column("discharge", sa.BOOLEAN(), nullable=False),
        sa.Column("mtr", sa.BOOLEAN(), nullable=False),
        sa.Column("spm", sa.BOOLEAN(), nullable=False),
        sa.Column("stboxf", sa.BOOLEAN(), nullable=False),
        sa.Column("hyd_temp", sa.BOOLEAN(), nullable=False),
        sa.Column("wants_card_ml", sa.BOOLEAN(), nullable=False),
        sa.Column("change_suction", sa.BOOLEAN(), nullable=False),
        sa.Column("change_hyd_temp", sa.BOOLEAN(), nullable=False),
        sa.Column("change_dgp", sa.BOOLEAN(), nullable=False),
        sa.Column("change_hp_delta", sa.BOOLEAN(), nullable=False),
        sa.Column("hyd_oil_lvl", sa.BOOLEAN(), nullable=False),
        sa.Column("hyd_filt_life", sa.BOOLEAN(), nullable=False),
        sa.Column("hyd_oil_life", sa.BOOLEAN(), nullable=False),
        sa.Column("chk_mtr_ovld", sa.BOOLEAN(), nullable=False),
        sa.Column("pwr_fail", sa.BOOLEAN(), nullable=False),
        sa.Column("soft_start_err", sa.BOOLEAN(), nullable=False),
        sa.Column("grey_wire_err", sa.BOOLEAN(), nullable=False),
        sa.Column("ae011", sa.BOOLEAN(), nullable=False),
        sa.Column("wants_sms", sa.BOOLEAN(), nullable=False),
        sa.Column("wants_email", sa.BOOLEAN(), nullable=False),
        sa.Column("wants_phone", sa.BOOLEAN(), nullable=False),
        sa.Column("wants_short_sms", sa.BOOLEAN(), nullable=False),
        sa.Column("wants_short_email", sa.BOOLEAN(), nullable=False),
        sa.Column("wants_short_phone", sa.BOOLEAN(), nullable=False),
        sa.Column("wants_whatsapp", sa.BOOLEAN(), nullable=False),
        sa.ForeignKeyConstraint(
            ["power_unit_id"], ["public.power_units.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "power_unit_id"),
        schema="public",
    )
    op.create_table(
        "alerts_custom",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("subject", sa.TEXT(), nullable=False),
        sa.Column("body", sa.TEXT(), nullable=False),
        sa.Column("want_sms", sa.BOOLEAN(), nullable=True),
        sa.Column("want_email", sa.BOOLEAN(), nullable=True),
        sa.Column("customer_id", sa.INTEGER(), nullable=False),
        sa.Column("hour_ending", sa.SMALLINT(), nullable=False),
        sa.Column("time_zone_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["customer_id"],
            ["public.customers.id"],
        ),
        sa.ForeignKeyConstraint(
            ["hour_ending"],
            ["public.hours.hour_ending"],
        ),
        sa.ForeignKeyConstraint(
            ["time_zone_id"],
            ["public.time_zones.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "alerts_sent_maint",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("email_type_id", sa.INTEGER(), nullable=False),
        sa.Column("dev_test_prd", sa.VARCHAR(), nullable=True),
        sa.Column("customer_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["customer_id"],
            ["public.customers.id"],
        ),
        sa.ForeignKeyConstraint(
            ["email_type_id"],
            ["public.alerts_sent_maint_email_types.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "alerts_sent_users",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("dev_test_prd", sa.VARCHAR(), nullable=False),
        sa.Column("msg_type", sa.VARCHAR(), nullable=True),
        sa.Column("twilio_sid", sa.VARCHAR(), nullable=True),
        sa.Column("twilio_delivery_status", sa.VARCHAR(), nullable=True),
        sa.Column("mailgun_id", sa.VARCHAR(), nullable=True),
        sa.Column("mailgun_delivery_status", sa.VARCHAR(), nullable=True),
        sa.Column("alerts_sent_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["alerts_sent_id"],
            ["public.alerts_sent.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "alerts_sent_wait_okay",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_updated_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("wait_okay", sa.BOOLEAN(), nullable=False),
        sa.Column("is_prod", sa.BOOLEAN(), nullable=False),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=False),
        sa.Column("alert_type_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["alert_type_id"],
            ["public.alerts_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_id"],
            ["public.power_units.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("power_unit_id", "alert_type_id", "is_prod"),
        schema="public",
    )
    op.create_table(
        "applications",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", sa.DATE(), nullable=False),
        sa.Column("application_type_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=True),
        sa.Column("customer_id", sa.INTEGER(), nullable=True),
        sa.Column("company_name", sa.VARCHAR(), nullable=False),
        sa.Column("street", sa.TEXT(), nullable=True),
        sa.Column("city", sa.VARCHAR(), nullable=True),
        sa.Column("province_id", sa.INTEGER(), nullable=True),
        sa.Column("country_id", sa.INTEGER(), nullable=True),
        sa.Column("contact_name", sa.VARCHAR(), nullable=False),
        sa.Column("contact_phone", sa.String(length=15), nullable=False),
        sa.Column("contact_email", sa.VARCHAR(), nullable=False),
        sa.Column("install_location", sa.VARCHAR(), nullable=True),
        sa.Column("field_contact_name", sa.VARCHAR(), nullable=True),
        sa.Column("field_contact_phone", sa.String(length=15), nullable=True),
        sa.Column("field_contact_email", sa.VARCHAR(), nullable=True),
        sa.Column("project_objective", sa.TEXT(), nullable=True),
        sa.Column("current_process_equipment", sa.TEXT(), nullable=True),
        sa.Column("current_process_issues", sa.TEXT(), nullable=True),
        sa.Column("when_need_equipment", sa.TEXT(), nullable=True),
        sa.Column("emulsion_coming_from", sa.TEXT(), nullable=True),
        sa.Column("emulsion_discharging_into", sa.TEXT(), nullable=True),
        sa.Column("vapour_coming_off_storage", sa.BOOLEAN(), nullable=False),
        sa.Column("vapour_coming_off_vr_tanks", sa.BOOLEAN(), nullable=False),
        sa.Column("vapour_coming_off_other", sa.TEXT(), nullable=True),
        sa.Column("vapour_discharging_into", sa.TEXT(), nullable=True),
        sa.Column("tank_pressure_rating", sa.NUMERIC(), nullable=True),
        sa.Column("tank_pressure_desired", sa.NUMERIC(), nullable=True),
        sa.Column("casing_count", sa.INTEGER(), nullable=True),
        sa.Column(
            "discharging_into_emulsion1_or_gas_line2", sa.SMALLINT(), nullable=True
        ),
        sa.Column("flowline_pressure_on_test", sa.NUMERIC(), nullable=True),
        sa.Column("artificial_lift_system", sa.TEXT(), nullable=True),
        sa.Column("separators_installed", sa.BOOLEAN(), nullable=False),
        sa.Column("compressor_b4_separators", sa.BOOLEAN(), nullable=False),
        sa.Column("well_pumped_off_status", sa.SMALLINT(), nullable=True),
        sa.Column("pump_fillage_pct", sa.NUMERIC(), nullable=True),
        sa.Column("formation_pressure", sa.NUMERIC(), nullable=True),
        sa.Column("pump_make", sa.VARCHAR(), nullable=True),
        sa.Column("pump_model", sa.VARCHAR(), nullable=True),
        sa.Column("pump_speed_spm", sa.NUMERIC(), nullable=True),
        sa.Column("pump_stroke_length", sa.NUMERIC(), nullable=True),
        sa.Column("pump_rod_load", sa.NUMERIC(), nullable=True),
        sa.Column("pump_long_stroke", sa.BOOLEAN(), nullable=True),
        sa.Column("pump_set_on_cement1_piles2", sa.SMALLINT(), nullable=True),
        sa.Column("pump_base_height", sa.NUMERIC(), nullable=True),
        sa.Column("pump_num_rails", sa.SMALLINT(), nullable=True),
        sa.Column("pump_num_tiedowns", sa.SMALLINT(), nullable=True),
        sa.Column("use_ppm1_percent2", sa.SMALLINT(), nullable=False),
        sa.Column("h2s", sa.NUMERIC(), nullable=False),
        sa.Column("co2", sa.NUMERIC(), nullable=False),
        sa.Column("salinity", sa.NUMERIC(), nullable=False),
        sa.Column("use_psi1_kpa2_ozsqinch3", sa.SMALLINT(), nullable=False),
        sa.Column("inlet_pressure_current", sa.NUMERIC(), nullable=False),
        sa.Column("inlet_pressure_desired", sa.NUMERIC(), nullable=False),
        sa.Column("discharge_pressure", sa.NUMERIC(), nullable=False),
        sa.Column("use_e3m3d1_mcfd2", sa.SMALLINT(), nullable=False),
        sa.Column("expected_gas_volume", sa.NUMERIC(), nullable=False),
        sa.Column("expected_water_volume", sa.NUMERIC(), nullable=False),
        sa.Column("expected_oil_volume", sa.NUMERIC(), nullable=False),
        sa.Column("use_celsius1_fahrenheit2", sa.SMALLINT(), nullable=False),
        sa.Column("inlet_temp", sa.NUMERIC(), nullable=False),
        sa.Column("max_discharge_flowline_temp", sa.NUMERIC(), nullable=False),
        sa.Column("max_ambient_temp", sa.NUMERIC(), nullable=False),
        sa.Column("sand", sa.Boolean(), nullable=False),
        sa.Column("frac_sand", sa.Boolean(), nullable=False),
        sa.Column("parafin", sa.Boolean(), nullable=False),
        sa.Column("other_solids", sa.TEXT(), nullable=True),
        sa.Column("pipeline_diameter_inlet_inches", sa.NUMERIC(), nullable=False),
        sa.Column("pipeline_diameter_discharge_inches", sa.NUMERIC(), nullable=False),
        sa.Column("power_available", sa.Boolean(), nullable=False),
        sa.Column("power_voltage", sa.NUMERIC(), nullable=False),
        sa.Column("power_phase", sa.NUMERIC(), nullable=False),
        sa.Column("power_amps", sa.NUMERIC(), nullable=False),
        sa.Column("power_grid", sa.Boolean(), nullable=False),
        sa.Column("power_generator", sa.Boolean(), nullable=False),
        sa.Column("fuel_diesel", sa.Boolean(), nullable=False),
        sa.Column("fuel_natural_gas", sa.Boolean(), nullable=False),
        sa.Column("fuel_propane", sa.Boolean(), nullable=False),
        sa.Column("cell_signal_good", sa.Boolean(), nullable=False),
        sa.Column("special_metals_needed", sa.Boolean(), nullable=False),
        sa.Column("special_requirements", sa.TEXT(), nullable=True),
        sa.ForeignKeyConstraint(
            ["application_type_id"],
            ["public.application_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["country_id"],
            ["public.countries.id"],
        ),
        sa.ForeignKeyConstraint(
            ["customer_id"],
            ["public.customers.id"],
        ),
        sa.ForeignKeyConstraint(
            ["province_id"],
            ["public.provinces.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["public.users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "calories",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("calories", sa.NUMERIC(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("calorie_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["calorie_type_id"],
            ["public.calorie_types.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "career_applications",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("job_type", sa.VARCHAR(), nullable=False),
        sa.Column("message", sa.TEXT(), nullable=True),
        sa.Column("timestamp_inserted_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "career_files",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("pdf", postgresql.BYTEA(), nullable=False),
        sa.Column("timestamp_inserted_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "cities",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=False),
        sa.Column("province_id", sa.INTEGER(), nullable=False),
        sa.Column("county_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["county_id"],
            ["public.counties.id"],
        ),
        sa.ForeignKeyConstraint(
            ["province_id"],
            ["public.provinces.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("county_id", "name", name="cities_county_id_name_key"),
        schema="public",
    )
    with op.batch_alter_table("cities", schema=None) as batch_op:
        batch_op.create_index(
            "idx_city_province_county", ["province_id", "county_id"], unique=False
        )

    op.create_table(
        "contact_form",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=True),
        sa.Column("first_name", sa.VARCHAR(), nullable=True),
        sa.Column("last_name", sa.VARCHAR(), nullable=True),
        sa.Column("email", sa.VARCHAR(), nullable=True),
        sa.Column("phone", sa.VARCHAR(), nullable=True),
        sa.Column("message", sa.TEXT(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["public.users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "cust_sub_groups",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.String(length=60), nullable=False),
        sa.Column("abbrev", sa.String(length=30), nullable=False),
        sa.Column("description", sa.String(length=200), nullable=True),
        sa.Column("customer_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["customer_id"],
            ["public.customers.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
        schema="public",
    )
    op.create_table(
        "error_logs",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=True),
        sa.Column("error_type", sa.String(length=255), nullable=True),
        sa.Column("error_message", sa.TEXT(), nullable=True),
        sa.Column("error_traceback", sa.TEXT(), nullable=True),
        sa.Column("request_method", sa.String(length=10), nullable=True),
        sa.Column("request_url", sa.String(length=2048), nullable=True),
        sa.Column(
            "request_params", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
        sa.Column(
            "request_body", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
        sa.Column(
            "request_headers", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
        sa.Column("user_agent", sa.String(length=512), nullable=True),
        sa.Column("client_ip", sa.String(length=45), nullable=True),
        sa.Column("app_version", sa.String(length=50), nullable=True),
        sa.Column("environment", sa.String(length=20), nullable=True),
        sa.Column("status_code", sa.INTEGER(), nullable=True),
        sa.Column("resolved", sa.BOOLEAN(), nullable=False),
        sa.Column("resolution_notes", sa.TEXT(), nullable=True),
        sa.Column("resolved_by_user_id", sa.INTEGER(), nullable=True),
        sa.Column("resolved_at_utc", postgresql.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(
            ["resolved_by_user_id"], ["public.users.id"], ondelete="SET NULL"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="SET NULL"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "flask_dance_oauth",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("provider", sa.String(length=50), nullable=False),
        sa.Column("provider_user_id", sa.String(length=256), nullable=False),
        sa.Column("created_at", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("token", postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["public.users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("provider_user_id"),
        schema="public",
    )
    op.create_table(
        "power_units_fixed_ip_networks",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("ip_address", sa.VARCHAR(), nullable=False),
        sa.Column("subnet", sa.VARCHAR(length=2), nullable=False),
        sa.Column("gateway", sa.VARCHAR(), nullable=True),
        sa.Column("never_default", sa.BOOLEAN(), nullable=False),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["power_unit_id"], ["public.power_units.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "power_units_modbus_networks",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("ip_address", sa.VARCHAR(), nullable=False),
        sa.Column("subnet", sa.VARCHAR(length=2), nullable=False),
        sa.Column("gateway", sa.VARCHAR(), nullable=True),
        sa.Column("never_default", sa.BOOLEAN(), nullable=False),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["power_unit_id"], ["public.power_units.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "profiler_measurements",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_started", postgresql.TIMESTAMP(), nullable=True),
        sa.Column("timestamp_utc_ended", postgresql.TIMESTAMP(), nullable=True),
        sa.Column(
            "elapsed_seconds",
            sa.NUMERIC(),
            sa.Computed(
                "EXTRACT(EPOCH FROM timestamp_utc_ended - timestamp_utc_started)",
            ),
            nullable=True,
        ),
        sa.Column("started_at", sa.NUMERIC(), nullable=True),
        sa.Column("ended_at", sa.NUMERIC(), nullable=True),
        sa.Column("elapsed", sa.NUMERIC(), nullable=True),
        sa.Column("cpu_start", sa.INTEGER(), nullable=True),
        sa.Column("cpu_end", sa.INTEGER(), nullable=True),
        sa.Column("endpoint_name", sa.VARCHAR(), nullable=True),
        sa.Column("endpoint", sa.VARCHAR(), nullable=True),
        sa.Column("url", sa.VARCHAR(), nullable=True),
        sa.Column("args", sa.TEXT(), nullable=True),
        sa.Column("kwargs", sa.TEXT(), nullable=True),
        sa.Column("request_args", sa.TEXT(), nullable=True),
        sa.Column("form", sa.TEXT(), nullable=True),
        sa.Column("body", sa.TEXT(), nullable=True),
        sa.Column("headers", sa.TEXT(), nullable=True),
        sa.Column("ip", sa.VARCHAR(), nullable=True),
        sa.Column("method", sa.VARCHAR(), nullable=True),
        sa.Column("status_code", sa.SMALLINT(), nullable=True),
        sa.Column("user_agent", sa.VARCHAR(), nullable=True),
        sa.Column("path", sa.VARCHAR(), nullable=True),
        sa.Column("referrer", sa.VARCHAR(), nullable=True),
        sa.Column("query_string", sa.VARCHAR(), nullable=True),
        sa.Column("scheme", sa.VARCHAR(), nullable=True),
        sa.Column("files", sa.TEXT(), nullable=True),
        sa.Column("user_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "remote_control",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("aws_thing", sa.VARCHAR(), nullable=True),
        sa.Column("dev_test_prd", sa.VARCHAR(), nullable=False),
        sa.Column("power_unit", sa.VARCHAR(), nullable=False),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("metric", sa.VARCHAR(), nullable=True),
        sa.Column("value_wanted", sa.VARCHAR(), nullable=True),
        sa.Column("action", sa.VARCHAR(), nullable=True),
        sa.ForeignKeyConstraint(
            ["power_unit_id"],
            ["public.power_units.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "report_email_derates",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("max_distance_km", sa.INTEGER(), nullable=False),
        sa.Column("derate_threshold", sa.SMALLINT(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "report_email_hourly",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("max_distance_km", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "report_email_inventory",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("max_distance_km", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "report_email_op_hours",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("max_distance_km", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "service_emailees",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["public.users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id"),
        schema="public",
    )
    op.create_table(
        "structures",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("structure", sa.DOUBLE_PRECISION(), nullable=False),
        sa.Column(
            "structure_str",
            sa.VARCHAR(length=10),
            sa.Computed(
                "CAST(structure as VARCHAR(10))",
            ),
            nullable=True,
        ),
        sa.Column("structure_slave_id", sa.INTEGER(), nullable=True),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=True),
        sa.Column("on_genset", sa.BOOLEAN(), nullable=False),
        sa.Column("auto_greaser", sa.BOOLEAN(), nullable=True),
        sa.Column("auto_greaser_needs_alerts", sa.BOOLEAN(), nullable=True),
        sa.Column("model_type_id", sa.INTEGER(), nullable=True),
        sa.Column("model_type_id_slave", sa.INTEGER(), nullable=True),
        sa.Column("unit_type_id", sa.INTEGER(), nullable=True),
        sa.Column("well_license", sa.VARCHAR(), nullable=True),
        sa.Column("qb_sale", sa.VARCHAR(), nullable=True),
        sa.Column("qb_install", sa.VARCHAR(), nullable=True),
        sa.Column("afe", sa.VARCHAR(), nullable=True),
        sa.Column("dgas_pumpjack", sa.VARCHAR(), nullable=True),
        sa.Column("run_mfg_date", sa.DATE(), nullable=True),
        sa.Column("structure_install_date", sa.DATE(), nullable=True),
        sa.Column("slave_install_date", sa.DATE(), nullable=True),
        sa.Column("op_months_interval", sa.REAL(), nullable=False),
        sa.Column("has_rcom", sa.BOOLEAN(), nullable=False),
        sa.Column("status", sa.VARCHAR(), nullable=True),
        sa.Column("area", sa.VARCHAR(), nullable=True),
        sa.Column("downhole", sa.VARCHAR(), nullable=True),
        sa.Column("surface", sa.VARCHAR(), nullable=True),
        sa.Column(
            "location",
            sa.VARCHAR(),
            sa.Computed(
                "CASE WHEN downhole IS NULL OR downhole = '' THEN surface ELSE (downhole || ' @ ') || surface END",
            ),
            nullable=True,
        ),
        sa.Column("gps_lat", sa.REAL(), nullable=True),
        sa.Column("gps_lon", sa.REAL(), nullable=True),
        sa.Column("casing_sensor", sa.BOOLEAN(), nullable=True),
        sa.Column("notes_1", sa.TEXT(), nullable=True),
        sa.Column("notes_2", sa.TEXT(), nullable=True),
        sa.Column("tundra_name", sa.VARCHAR(), nullable=True),
        sa.Column("rod_id", sa.INTEGER(), nullable=True),
        sa.Column("barrel_id", sa.INTEGER(), nullable=True),
        sa.Column("shuttle_valve_id", sa.INTEGER(), nullable=True),
        sa.Column("check_valve_id", sa.INTEGER(), nullable=True),
        sa.Column("packing_gland_id", sa.INTEGER(), nullable=True),
        sa.Column("hyd_piston_type_id", sa.INTEGER(), nullable=True),
        sa.Column("time_zone_id", sa.SMALLINT(), nullable=True),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["barrel_id"],
            ["public.barrels.id"],
        ),
        sa.ForeignKeyConstraint(
            ["check_valve_id"],
            ["public.check_valves.id"],
        ),
        sa.ForeignKeyConstraint(
            ["hyd_piston_type_id"],
            ["public.hyd_piston_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["model_type_id"],
            ["public.model_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["model_type_id_slave"],
            ["public.model_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["packing_gland_id"],
            ["public.packing_glands.id"],
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_id"],
            ["public.power_units.id"],
        ),
        sa.ForeignKeyConstraint(
            ["rod_id"],
            ["public.rods.id"],
        ),
        sa.ForeignKeyConstraint(
            ["shuttle_valve_id"],
            ["public.shuttle_valves.id"],
        ),
        sa.ForeignKeyConstraint(
            ["structure_slave_id"],
            ["public.structures.id"],
        ),
        sa.ForeignKeyConstraint(
            ["time_zone_id"],
            ["public.time_zones.id"],
        ),
        sa.ForeignKeyConstraint(
            ["unit_type_id"],
            ["public.unit_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["warehouse_id"],
            ["public.warehouses.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("structure"),
        sa.UniqueConstraint("structure_slave_id"),
        schema="public",
    )
    op.create_table(
        "user_api_tokens",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("name", sa.VARCHAR(), nullable=True),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("expires", sa.DATE(), nullable=False),
        sa.Column("token", sa.VARCHAR(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "user_authentication_challenges",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("challenge", postgresql.BYTEA(), nullable=False),
        sa.Column("challenge_sent_at_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("challenge_expiry_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.CheckConstraint(
            "challenge_expiry_utc > challenge_sent_at_utc",
            name="challenge_expiry_utc_gt_challenge_sent_at_utc",
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id"),
        schema="public",
    )
    op.create_table(
        "user_chart_preferences",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("chart_type", sa.VARCHAR(length=20), nullable=False),
        sa.Column("chart_category", sa.VARCHAR(length=50), nullable=False),
        sa.Column(
            "selected_metrics", postgresql.JSON(astext_type=sa.Text()), nullable=False
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "user_chart_toggles",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("use_kpa", sa.Boolean(), nullable=False),
        sa.Column("use_cf", sa.Boolean(), nullable=False),
        sa.Column("use_barrels", sa.Boolean(), nullable=False),
        sa.Column("use_fahrenheit", sa.Boolean(), nullable=False),
        sa.Column("use_oz_per_inch2_for_suction", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id"),
        schema="public",
    )
    op.create_table(
        "user_customer_rel",
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("customer_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["customer_id"], ["public.customers.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id", "customer_id"),
        schema="public",
    )
    op.create_table(
        "user_registration_challenges",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("challenge", postgresql.BYTEA(), nullable=False),
        sa.Column("challenge_sent_at_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("challenge_expiry_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.CheckConstraint(
            "challenge_expiry_utc > challenge_sent_at_utc",
            name="challenge_expiry_utc_gt_challenge_sent_at_utc",
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id"),
        schema="public",
    )
    op.create_table(
        "user_role_rel",
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("role_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["role_id"], ["public.roles.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id", "role_id"),
        schema="public",
    )
    op.create_table(
        "user_verification_codes",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("code", sa.INTEGER(), nullable=False),
        sa.Column("code_sent_at_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("code_expiry_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.CheckConstraint(
            "code_expiry_utc > code_sent_at_utc",
            name="code_expiry_utc_gt_code_sent_at_utc",
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", name="user_id_code_unique_constraint"),
        schema="public",
    )
    op.create_table(
        "user_webauthn_credentials",
        sa.Column("credential_id", postgresql.BYTEA(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("credential_public_key", postgresql.BYTEA(), nullable=False),
        sa.Column("sign_count", sa.INTEGER(), nullable=False),
        sa.Column("transports", postgresql.ARRAY(sa.VARCHAR()), nullable=True),
        sa.Column("rp_id", sa.VARCHAR(), nullable=True),
        sa.Column("user_handle", sa.VARCHAR(), nullable=True),
        sa.Column("timestamp_inserted_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("credential_id"),
        sa.UniqueConstraint(
            "user_id", "rp_id", name="user_id_credential_id_unique_constraint"
        ),
        schema="public",
    )
    op.create_table(
        "warehouses_parts_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=False),
        sa.Column("quantity", sa.NUMERIC(), nullable=False),
        sa.Column("quantity_desired", sa.NUMERIC(), nullable=True),
        sa.ForeignKeyConstraint(["part_id"], ["public.parts.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["warehouse_id"], ["public.warehouses.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("warehouse_id", "part_id"),
        schema="public",
    )
    op.create_table(
        "website_views",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("page", sa.VARCHAR(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=True),
        sa.Column("count_records", sa.INTEGER(), nullable=True),
        sa.Column("city", sa.VARCHAR(), nullable=True),
        sa.Column("region", sa.VARCHAR(), nullable=True),
        sa.Column("timezone", sa.VARCHAR(), nullable=True),
        sa.Column("company", sa.VARCHAR(), nullable=True),
        sa.Column("country_code", sa.VARCHAR(), nullable=True),
        sa.Column("country_name", sa.VARCHAR(), nullable=True),
        sa.Column("ip", sa.VARCHAR(), nullable=True),
        sa.Column("postal", sa.VARCHAR(), nullable=True),
        sa.Column("gps_lat", sa.REAL(), nullable=True),
        sa.Column("gps_lon", sa.REAL(), nullable=True),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "alerts_custom_days_rel",
        sa.Column("alerts_custom_id", sa.INTEGER(), nullable=False),
        sa.Column("day", sa.SMALLINT(), nullable=False),
        sa.ForeignKeyConstraint(
            ["alerts_custom_id"], ["public.alerts_custom.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["day"],
            ["public.days.day"],
        ),
        sa.PrimaryKeyConstraint("alerts_custom_id", "day"),
        schema="public",
    )
    op.create_table(
        "alerts_custom_images_rel",
        sa.Column("alerts_custom_id", sa.INTEGER(), nullable=False),
        sa.Column("image_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["alerts_custom_id"], ["public.alerts_custom.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["image_id"], ["public.images.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("alerts_custom_id", "image_id"),
        schema="public",
    )
    op.create_table(
        "alerts_custom_months_rel",
        sa.Column("alerts_custom_id", sa.INTEGER(), nullable=False),
        sa.Column("month", sa.SMALLINT(), nullable=False),
        sa.ForeignKeyConstraint(
            ["alerts_custom_id"], ["public.alerts_custom.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["month"],
            ["public.months.month"],
        ),
        sa.PrimaryKeyConstraint("alerts_custom_id", "month"),
        schema="public",
    )
    op.create_table(
        "alerts_custom_structure_rel",
        sa.Column("alerts_custom_id", sa.INTEGER(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["alerts_custom_id"], ["public.alerts_custom.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("alerts_custom_id", "structure_id"),
        schema="public",
    )
    op.create_table(
        "alerts_custom_user_rel",
        sa.Column("alerts_custom_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["alerts_custom_id"], ["public.alerts_custom.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("alerts_custom_id", "user_id"),
        schema="public",
    )
    op.create_table(
        "alerts_sent_maint_users",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("dev_test_prd", sa.VARCHAR(), nullable=True),
        sa.Column("alerts_sent_maint_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["alerts_sent_maint_id"],
            ["public.alerts_sent_maint.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "application_upload_files",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("application_id", sa.INTEGER(), nullable=False),
        sa.Column("file_name", sa.VARCHAR(), nullable=False),
        sa.Column("file_type", sa.VARCHAR(), nullable=False),
        sa.Column("file_bytes", postgresql.BYTEA(), nullable=False),
        sa.ForeignKeyConstraint(
            ["application_id"],
            ["public.applications.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "career_applications_files_rel",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("career_application_id", sa.INTEGER(), nullable=False),
        sa.Column("career_file_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["career_application_id"],
            ["public.career_applications.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["career_file_id"], ["public.career_files.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("career_application_id", "career_file_id"),
        schema="public",
    )
    op.create_table(
        "gw",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", sa.DATE(), nullable=False),
        sa.Column("gateway", sa.VARCHAR(), nullable=False),
        sa.Column("aws_thing", sa.VARCHAR(), nullable=False),
        sa.Column("mac", sa.VARCHAR(), nullable=True),
        sa.Column("serial_gw", sa.VARCHAR(), nullable=True),
        sa.Column("model_gw", sa.VARCHAR(), nullable=True),
        sa.Column("imei", sa.VARCHAR(), nullable=True),
        sa.Column("notes", sa.TEXT(), nullable=True),
        sa.Column("gateway_type_id", sa.INTEGER(), nullable=True),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=True),
        sa.Column("structure_id", sa.INTEGER(), nullable=True),
        sa.Column("test_cellular", sa.BOOLEAN(), nullable=False),
        sa.Column("test_can_bus", sa.BOOLEAN(), nullable=False),
        sa.Column("ready_and_working", sa.BOOLEAN(), nullable=False),
        sa.Column("location_gw", sa.VARCHAR(), nullable=True),
        sa.ForeignKeyConstraint(
            ["gateway_type_id"],
            ["public.gateway_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_id"],
            ["public.power_units.id"],
        ),
        sa.ForeignKeyConstraint(
            ["structure_id"],
            ["public.structures.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("aws_thing"),
        sa.UniqueConstraint("gateway"),
        sa.UniqueConstraint("imei"),
        schema="public",
    )
    op.create_table(
        "maintenance",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("description", sa.TEXT(), nullable=True),
        sa.Column("op_hours", sa.REAL(), nullable=True),
        sa.Column(
            "op_months",
            sa.REAL(),
            sa.Computed(
                "op_hours / 24",
            ),
            nullable=True,
        ),
        sa.Column("maintenance_type_id", sa.INTEGER(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=True),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=True),
        sa.Column("user_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["maintenance_type_id"], ["public.maintenance_types.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["power_unit_id"],
            ["public.power_units.id"],
        ),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["public.users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "report_email_derates_days_of_week_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("day_of_week_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["day_of_week_id"], ["public.days_of_week.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_derates.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "day_of_week_id"),
        schema="public",
    )
    op.create_table(
        "report_email_derates_hours_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("hour", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["hour"],
            ["public.hours.hour"],
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_derates.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "hour"),
        schema="public",
    )
    op.create_table(
        "report_email_derates_model_types_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["model_type_id"], ["public.model_types.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_derates.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "model_type_id"),
        schema="public",
    )
    op.create_table(
        "report_email_derates_unit_types_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("unit_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_derates.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["unit_type_id"], ["public.unit_types.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "unit_type_id"),
        schema="public",
    )
    op.create_table(
        "report_email_derates_warehouses_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_derates.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["warehouse_id"], ["public.warehouses.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "warehouse_id"),
        schema="public",
    )
    op.create_table(
        "report_email_hourly_days_of_week_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("day_of_week_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["day_of_week_id"], ["public.days_of_week.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_hourly.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "day_of_week_id"),
        schema="public",
    )
    op.create_table(
        "report_email_hourly_hours_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("hour", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["hour"],
            ["public.hours.hour"],
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_hourly.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "hour"),
        schema="public",
    )
    op.create_table(
        "report_email_hourly_model_types_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["model_type_id"], ["public.model_types.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_hourly.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "model_type_id"),
        schema="public",
    )
    op.create_table(
        "report_email_hourly_unit_types_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("unit_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_hourly.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["unit_type_id"], ["public.unit_types.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "unit_type_id"),
        schema="public",
    )
    op.create_table(
        "report_email_hourly_warehouses_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_hourly.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["warehouse_id"], ["public.warehouses.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "warehouse_id"),
        schema="public",
    )
    op.create_table(
        "report_email_inventory_days_of_week_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("day_of_week_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["day_of_week_id"], ["public.days_of_week.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_inventory.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "day_of_week_id"),
        schema="public",
    )
    op.create_table(
        "report_email_inventory_hours_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("hour", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["hour"],
            ["public.hours.hour"],
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_inventory.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "hour"),
        schema="public",
    )
    op.create_table(
        "report_email_inventory_warehouses_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_inventory.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["warehouse_id"], ["public.warehouses.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "warehouse_id"),
        schema="public",
    )
    op.create_table(
        "report_email_op_hours_model_types_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["model_type_id"], ["public.model_types.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_op_hours.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "model_type_id"),
        schema="public",
    )
    op.create_table(
        "report_email_op_hours_types_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("report_email_op_hours_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_email_op_hours_type_id"],
            ["public.report_email_op_hours_types.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_op_hours.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "report_email_op_hours_type_id"),
        schema="public",
    )
    op.create_table(
        "report_email_op_hours_unit_types_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("unit_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_op_hours.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["unit_type_id"], ["public.unit_types.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "unit_type_id"),
        schema="public",
    )
    op.create_table(
        "report_email_op_hours_warehouses_rel",
        sa.Column("report_id", sa.INTEGER(), nullable=False),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["report_id"], ["public.report_email_op_hours.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["warehouse_id"], ["public.warehouses.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("report_id", "warehouse_id"),
        schema="public",
    )
    op.create_table(
        "service",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("priority", sa.INTEGER(), nullable=True),
        sa.Column("description", sa.VARCHAR(), nullable=False),
        sa.Column("is_resolved", sa.BOOLEAN(), nullable=False),
        sa.Column("resolution", sa.TEXT(), nullable=True),
        sa.Column("user_id", sa.INTEGER(), nullable=True),
        sa.Column("operator_name", sa.VARCHAR(), nullable=True),
        sa.Column("operator_phone", sa.String(length=15), nullable=True),
        sa.Column("operator_email", sa.VARCHAR(), nullable=True),
        sa.Column("service_type_id", sa.INTEGER(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=True),
        sa.ForeignKeyConstraint(
            ["service_type_id"], ["public.service_types.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["public.users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "service_clock",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("is_billable", sa.BOOLEAN(), nullable=False),
        sa.Column("is_travel", sa.BOOLEAN(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=True),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=True),
        sa.Column("timestamp_utc_in", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_out", postgresql.TIMESTAMP(), nullable=True),
        sa.Column(
            "total_hours_worked",
            postgresql.INTERVAL(),
            sa.Computed(
                "timestamp_utc_out - timestamp_utc_in",
            ),
            nullable=True,
        ),
        sa.Column("time_zone_in_id", sa.INTEGER(), nullable=False),
        sa.Column("time_zone_out_id", sa.INTEGER(), nullable=False),
        sa.Column("gps_lat_in", sa.DOUBLE_PRECISION(), nullable=True),
        sa.Column("gps_lat_out", sa.DOUBLE_PRECISION(), nullable=True),
        sa.Column("gps_lon_in", sa.DOUBLE_PRECISION(), nullable=True),
        sa.Column("gps_lon_out", sa.DOUBLE_PRECISION(), nullable=True),
        sa.Column("notes", sa.TEXT(), nullable=True),
        sa.CheckConstraint(
            "((structure_id IS NULL AND warehouse_id IS NOT NULL) OR (structure_id IS NOT NULL AND warehouse_id IS NULL) OR (structure_id IS NOT NULL AND warehouse_id IS NOT NULL))"
        ),
        sa.ForeignKeyConstraint(
            ["structure_id"],
            ["public.structures.id"],
        ),
        sa.ForeignKeyConstraint(
            ["time_zone_in_id"],
            ["public.time_zones.id"],
        ),
        sa.ForeignKeyConstraint(
            ["time_zone_out_id"],
            ["public.time_zones.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["warehouse_id"],
            ["public.warehouses.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "structure_cust_sub_group_rel",
        sa.Column("structure_id", sa.INTEGER(), nullable=False),
        sa.Column("cust_sub_group_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["cust_sub_group_id"], ["public.cust_sub_groups.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("structure_id", "cust_sub_group_id"),
        schema="public",
    )
    op.create_table(
        "structure_customer_rel",
        sa.Column("structure_id", sa.INTEGER(), nullable=False),
        sa.Column("customer_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["customer_id"], ["public.customers.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("structure_id", "customer_id"),
        schema="public",
    )
    op.create_table(
        "user_cust_sub_group_notify_service_requests_rel",
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("cust_sub_group_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["cust_sub_group_id"], ["public.cust_sub_groups.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id", "cust_sub_group_id"),
        schema="public",
    )
    op.create_table(
        "user_structure_maintenance_rel",
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id", "structure_id"),
        schema="public",
    )
    op.create_table(
        "user_structure_remote_control_rel",
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id", "structure_id"),
        schema="public",
    )
    op.create_table(
        "user_structure_sales_rel",
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id", "structure_id"),
        schema="public",
    )
    op.create_table(
        "zip_codes",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("zip_code", sa.VARCHAR(length=10), nullable=False),
        sa.Column("city_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["city_id"],
            ["public.cities.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("city_id", "zip_code", name="uq_zip_code_city"),
        schema="public",
    )
    op.create_table(
        "gw_info",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("gateway_id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column(
            "timestamp_utc_last_reported", postgresql.TIMESTAMP(), nullable=False
        ),
        sa.Column("aws_thing", sa.VARCHAR(), nullable=False),
        sa.Column("power_unit_str", sa.VARCHAR(), nullable=True),
        sa.Column("os_name", sa.VARCHAR(), nullable=True),
        sa.Column("os_pretty_name", sa.VARCHAR(), nullable=True),
        sa.Column("os_version", sa.VARCHAR(), nullable=True),
        sa.Column("os_version_id", sa.VARCHAR(), nullable=True),
        sa.Column("os_release", sa.VARCHAR(), nullable=True),
        sa.Column("os_machine", sa.VARCHAR(), nullable=True),
        sa.Column("os_platform", sa.VARCHAR(), nullable=True),
        sa.Column("os_python_version", sa.VARCHAR(), nullable=True),
        sa.Column("modem_model", sa.VARCHAR(), nullable=True),
        sa.Column("modem_firmware_rev", sa.VARCHAR(), nullable=True),
        sa.Column("modem_drivers", sa.VARCHAR(), nullable=True),
        sa.Column("sim_operator", sa.VARCHAR(), nullable=True),
        sa.Column("swv_canpy", sa.VARCHAR(), nullable=True),
        sa.Column("swv_plc", sa.VARCHAR(), nullable=True),
        sa.Column("time_since_reported", sa.VARCHAR(), nullable=True),
        sa.Column("days_since_reported", sa.REAL(), nullable=True),
        sa.Column("connected", sa.BOOLEAN(), nullable=True),
        sa.Column("hyd", sa.SMALLINT(), nullable=True),
        sa.Column("warn1", sa.SMALLINT(), nullable=True),
        sa.Column("warn2", sa.SMALLINT(), nullable=True),
        sa.Column("hours", sa.REAL(), nullable=True),
        sa.Column("suction", sa.REAL(), nullable=True),
        sa.Column("discharge", sa.REAL(), nullable=True),
        sa.Column("spm", sa.REAL(), nullable=True),
        sa.Column("drive_size_gb", sa.REAL(), nullable=True),
        sa.Column("drive_used_gb", sa.REAL(), nullable=True),
        sa.Column("memory_size_gb", sa.REAL(), nullable=True),
        sa.Column("memory_used_gb", sa.REAL(), nullable=True),
        sa.Column("suction_range", sa.SMALLINT(), nullable=True),
        sa.Column("has_slave", sa.BOOLEAN(), nullable=True),
        sa.ForeignKeyConstraint(
            ["gateway_id"],
            ["public.gw.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("gateway_id"),
        schema="public",
    )
    op.create_table(
        "gw_not_connected_dont_worry",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("gateway_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("am_i_worried", sa.BOOLEAN(), nullable=False),
        sa.Column("notes", sa.TEXT(), nullable=True),
        sa.Column("operators_contacted", postgresql.TIMESTAMP(), nullable=True),
        sa.ForeignKeyConstraint(["gateway_id"], ["public.gw.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "gw_tested_cellular",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("gateway_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.Column("network_id", sa.SMALLINT(), nullable=True),
        sa.ForeignKeyConstraint(["gateway_id"], ["public.gw.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["network_id"], ["public.gw_cell_networks.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "mqtt_messages",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("topic", sa.VARCHAR(), nullable=False),
        sa.Column("message", sa.TEXT(), nullable=False),
        sa.Column("is_send", sa.BOOLEAN(), nullable=False),
        sa.Column("rc", sa.SMALLINT(), nullable=False),
        sa.Column("rc_message", sa.VARCHAR(), nullable=False),
        sa.Column("gateway_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["gateway_id"], ["public.gw.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "sim_cards",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("sim_card", sa.VARCHAR(), nullable=True),
        sa.Column("sim_card_num", sa.NUMERIC(), nullable=True),
        sa.Column("gateway_id", sa.INTEGER(), nullable=True),
        sa.Column("customer_id", sa.INTEGER(), nullable=True),
        sa.Column("sim_card_activated", sa.BOOLEAN(), nullable=True),
        sa.Column("sim_card_phone", sa.VARCHAR(), nullable=True),
        sa.Column("cell_provider", sa.VARCHAR(), nullable=True),
        sa.Column("notes", sa.TEXT(), nullable=True),
        sa.Column("location", sa.VARCHAR(), nullable=True),
        sa.ForeignKeyConstraint(
            ["customer_id"],
            ["public.customers.id"],
        ),
        sa.ForeignKeyConstraint(
            ["gateway_id"],
            ["public.gw.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("sim_card"),
        schema="public",
    )
    op.create_table(
        "work_orders",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("quickbooks_num", sa.VARCHAR(), nullable=True),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("date_service", sa.DATE(), nullable=False),
        sa.Column("is_quote", sa.BOOLEAN(), nullable=False),
        sa.Column("is_tax_exempt", sa.BOOLEAN(), nullable=False),
        sa.Column("country_id", sa.INTEGER(), nullable=False),
        sa.Column(
            "date_due",
            sa.DATE(),
            sa.Computed(
                "date_service",
            ),
            nullable=True,
        ),
        sa.Column("terms", sa.VARCHAR(), nullable=True),
        sa.Column("service_type_id", sa.INTEGER(), nullable=False),
        sa.Column("is_paid", sa.BOOLEAN(), nullable=False),
        sa.Column("notes", sa.TEXT(), nullable=True),
        sa.Column("inventory_source_id", sa.INTEGER(), nullable=False),
        sa.Column("customer_id", sa.INTEGER(), nullable=False),
        sa.Column("creator_id", sa.INTEGER(), nullable=False),
        sa.Column("creator_company_id", sa.INTEGER(), nullable=False),
        sa.Column("approved_by_id", sa.INTEGER(), nullable=False),
        sa.Column("date_sent_for_approval", sa.DATE(), nullable=True),
        sa.Column("invoice_approval_req", sa.BOOLEAN(), nullable=False),
        sa.Column("status_id", sa.INTEGER(), nullable=True),
        sa.Column("currency_id", sa.INTEGER(), nullable=False),
        sa.Column("requested_by_id", sa.INTEGER(), nullable=True),
        sa.Column("approval_person_id", sa.INTEGER(), nullable=True),
        sa.Column("service_crew", sa.VARCHAR(), nullable=True),
        sa.Column("location", sa.VARCHAR(), nullable=True),
        sa.Column("has_rcom", sa.BOOLEAN(), nullable=False),
        sa.Column("service_required", sa.TEXT(), nullable=True),
        sa.Column("service_resolution", sa.TEXT(), nullable=True),
        sa.Column("work_done", sa.TEXT(), nullable=True),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=True),
        sa.Column("is_warranty", sa.BOOLEAN(), nullable=False),
        sa.Column("is_warranty_reason", sa.TEXT(), nullable=True),
        sa.Column("picker_truck", sa.BOOLEAN(), nullable=False),
        sa.Column("crew_truck", sa.BOOLEAN(), nullable=False),
        sa.Column("man_lift", sa.BOOLEAN(), nullable=False),
        sa.Column("trailer", sa.BOOLEAN(), nullable=False),
        sa.Column("customer_po", sa.VARCHAR(), nullable=True),
        sa.Column("cust_work_order", sa.VARCHAR(), nullable=True),
        sa.Column("afe", sa.VARCHAR(), nullable=True),
        sa.Column("invoice_summary", sa.TEXT(), nullable=True),
        sa.Column("safe_work_permit_num", sa.VARCHAR(), nullable=True),
        sa.Column("signature_svg", sa.TEXT(), nullable=True),
        sa.Column("signature_name", sa.VARCHAR(), nullable=True),
        sa.Column("signature_date", sa.DATE(), nullable=True),
        sa.Column("structure_slave", sa.INTEGER(), nullable=True),
        sa.Column("province_id", sa.INTEGER(), nullable=True),
        sa.Column("county_id", sa.INTEGER(), nullable=True),
        sa.Column("city_id", sa.INTEGER(), nullable=True),
        sa.Column("zip_code_id", sa.INTEGER(), nullable=True),
        sa.Column("sales_tax_rate", sa.NUMERIC(precision=15, scale=2), nullable=False),
        sa.Column("gst_rate", sa.NUMERIC(precision=15, scale=2), nullable=True),
        sa.Column("pst_rate", sa.NUMERIC(precision=15, scale=2), nullable=True),
        sa.Column("subtotal", sa.NUMERIC(precision=15, scale=2), nullable=False),
        sa.Column("discount_pct", sa.NUMERIC(precision=15, scale=2), nullable=False),
        sa.Column(
            "subtotal_after_discount", sa.NUMERIC(precision=15, scale=2), nullable=True
        ),
        sa.Column("sales_tax", sa.NUMERIC(precision=15, scale=2), nullable=False),
        sa.Column("gst_amount", sa.NUMERIC(precision=15, scale=2), nullable=True),
        sa.Column("pst_amount", sa.NUMERIC(precision=15, scale=2), nullable=True),
        sa.Column("total", sa.NUMERIC(precision=15, scale=2), nullable=False),
        sa.ForeignKeyConstraint(
            ["approval_person_id"],
            ["public.users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["approved_by_id"], ["public.users.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["city_id"],
            ["public.cities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["country_id"],
            ["public.countries.id"],
        ),
        sa.ForeignKeyConstraint(
            ["county_id"],
            ["public.counties.id"],
        ),
        sa.ForeignKeyConstraint(
            ["creator_company_id"],
            ["public.customers.id"],
        ),
        sa.ForeignKeyConstraint(
            ["creator_id"],
            ["public.users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["currency_id"], ["public.currencies.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["customer_id"],
            ["public.customers.id"],
        ),
        sa.ForeignKeyConstraint(
            ["inventory_source_id"],
            ["public.inventory_sources.id"],
        ),
        sa.ForeignKeyConstraint(
            ["province_id"],
            ["public.provinces.id"],
        ),
        sa.ForeignKeyConstraint(
            ["requested_by_id"],
            ["public.users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["service_type_id"],
            ["public.service_types.id"],
        ),
        sa.ForeignKeyConstraint(
            ["status_id"],
            ["public.work_order_status.id"],
        ),
        sa.ForeignKeyConstraint(
            ["warehouse_id"],
            ["public.warehouses.id"],
        ),
        sa.ForeignKeyConstraint(
            ["zip_code_id"],
            ["public.zip_codes.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "zip_code_sales_tax",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_updated", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("zip_code_id", sa.INTEGER(), nullable=False),
        sa.Column("state_id", sa.INTEGER(), nullable=False),
        sa.Column("county_id", sa.INTEGER(), nullable=False),
        sa.Column("city_id", sa.INTEGER(), nullable=False),
        sa.Column("tax_region_name", sa.VARCHAR(), nullable=True),
        sa.Column("state_rate", sa.NUMERIC(), nullable=True),
        sa.Column("county_rate", sa.NUMERIC(), nullable=True),
        sa.Column("city_rate", sa.NUMERIC(), nullable=True),
        sa.Column("special_rate", sa.NUMERIC(), nullable=True),
        sa.Column("risk_level", sa.SMALLINT(), nullable=True),
        sa.Column(
            "state_plus_county",
            sa.NUMERIC(),
            sa.Computed(
                "state_rate + county_rate",
            ),
            nullable=True,
        ),
        sa.Column(
            "state_county_other",
            sa.NUMERIC(),
            sa.Computed(
                "state_rate + county_rate + special_rate",
            ),
            nullable=True,
        ),
        sa.Column(
            "combined_rate_est",
            sa.NUMERIC(),
            sa.Computed(
                "state_rate + county_rate + city_rate + special_rate",
            ),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["city_id"],
            ["public.cities.id"],
        ),
        sa.ForeignKeyConstraint(
            ["county_id"],
            ["public.counties.id"],
        ),
        sa.ForeignKeyConstraint(
            ["state_id"],
            ["public.provinces.id"],
        ),
        sa.ForeignKeyConstraint(
            ["zip_code_id"],
            ["public.zip_codes.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "zip_code_id",
            "state_id",
            "county_id",
            "city_id",
            name="zip_code_sales_tax_state_id_zip_code_id_county_id_city_id_key",
        ),
        schema="public",
    )
    op.create_table(
        "work_order_model_type_rel",
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("model_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["model_type_id"], ["public.model_types.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["work_order_id"], ["public.work_orders.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("work_order_id", "model_type_id"),
        schema="public",
    )
    op.create_table(
        "work_order_power_unit_rel",
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("power_unit_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["power_unit_id"], ["public.power_units.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["work_order_id"], ["public.work_orders.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("work_order_id", "power_unit_id"),
        schema="public",
    )
    op.create_table(
        "work_order_service_rel",
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("service_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["service_id"], ["public.service.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["work_order_id"], ["public.work_orders.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("work_order_id", "service_id"),
        schema="public",
    )
    op.create_table(
        "work_order_structure_rel",
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["structure_id"], ["public.structures.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["work_order_id"], ["public.work_orders.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("work_order_id", "structure_id"),
        schema="public",
    )
    op.create_table(
        "work_order_unit_type_rel",
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("unit_type_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(
            ["unit_type_id"], ["public.unit_types.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["work_order_id"], ["public.work_orders.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("work_order_id", "unit_type_id"),
        schema="public",
    )
    op.create_table(
        "work_order_upload_files",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", postgresql.TIMESTAMP(), nullable=False),
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("file_name", sa.VARCHAR(), nullable=False),
        sa.Column("file_type", sa.VARCHAR(), nullable=False),
        sa.Column("file_bytes", postgresql.BYTEA(), nullable=False),
        sa.ForeignKeyConstraint(
            ["work_order_id"],
            ["public.work_orders.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.create_table(
        "work_order_user_rel",
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["work_order_id"], ["public.work_orders.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("work_order_id", "user_id"),
        schema="public",
    )
    op.create_table(
        "work_order_user_sales_rel",
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("user_id", sa.INTEGER(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["public.users.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(
            ["work_order_id"], ["public.work_orders.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("work_order_id", "user_id"),
        schema="public",
    )
    op.create_table(
        "work_orders_parts",
        sa.Column("id", sa.INTEGER(), nullable=False),
        sa.Column("timestamp_utc_inserted", sa.DATE(), nullable=False),
        sa.Column("work_order_id", sa.INTEGER(), nullable=False),
        sa.Column("part_id", sa.INTEGER(), nullable=True),
        sa.Column("description", sa.TEXT(), nullable=False),
        sa.Column("structure_id", sa.INTEGER(), nullable=True),
        sa.Column("warehouse_id", sa.INTEGER(), nullable=True),
        sa.Column("field_tech_id", sa.INTEGER(), nullable=True),
        sa.Column("quantity", sa.NUMERIC(precision=15, scale=2), nullable=False),
        sa.Column("price", sa.NUMERIC(precision=15, scale=2), nullable=False),
        sa.Column(
            "cost_before_tax",
            sa.NUMERIC(precision=15, scale=2),
            sa.Computed(
                "quantity * price",
            ),
            nullable=True,
        ),
        sa.Column("sales_tax_rate", sa.NUMERIC(precision=15, scale=2), nullable=False),
        sa.Column(
            "sales_tax_part_amount",
            sa.NUMERIC(precision=15, scale=2),
            sa.Computed(
                "sales_tax_rate/100 * quantity * price",
            ),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["field_tech_id"],
            ["public.users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["part_id"],
            ["public.parts.id"],
        ),
        sa.ForeignKeyConstraint(
            ["structure_id"],
            ["public.structures.id"],
        ),
        sa.ForeignKeyConstraint(
            ["warehouse_id"],
            ["public.warehouses.id"],
        ),
        sa.ForeignKeyConstraint(
            ["work_order_id"], ["public.work_orders.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        schema="public",
    )
    op.update_view(
        "vw_work_order_parts_joined",
        "SELECT t1.id AS work_order_part_id, date_part('year'::text, t2.date_service) AS year, date_part('month'::text, t2.date_service) AS month, t2.date_service, t1.work_order_id, str.structure_str AS structure, str.location, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, pu.power_unit_str AS power_unit, gw.aws_thing AS gateway, t2.timestamp_utc_inserted, t1.part_id, t3.part_num, t3.worksheet AS bom_worksheet, t3.ws_row AS worksheet_row, t3.is_usd AS is_part_usd, t3.cad_per_usd, t3.is_soft_part, t3.msrp_cad, t3.msrp_usd, t3.dealer_cost_cad, t3.dealer_cost_usd, t3.ijack_corp_cost, t3.msrp_mult_cad, t3.msrp_mult_usd, t3.transfer_mult_cad_dealer, t3.transfer_mult_usd_dealer, t3.transfer_mult_inc_to_corp, CASE WHEN t3.cost_cad IS NULL THEN t1.price::double precision / t3.msrp_mult_cad ELSE t3.cost_cad::double precision END AS cost_cad, CASE WHEN t3.cost_usd IS NULL THEN t1.price::double precision / t3.msrp_mult_usd ELSE t3.cost_usd::double precision END AS cost_usd, t1.description, t1.price, t1.quantity, t1.cost_before_tax, t1.sales_tax_rate, t1.sales_tax_part_amount AS sales_tax, t1.field_tech_id, (ft.first_name::text || ' '::text) || ft.last_name::text AS credited_to, t2.requested_by_id, t2.approval_person_id, t2.service_crew, t2.invoice_approval_req, t2.location AS wo_location, t2.has_rcom, t2.service_required, t2.service_resolution, t2.is_warranty, t2.is_warranty_reason, t2.picker_truck, t2.crew_truck, t2.man_lift, t2.trailer, t2.work_done, t2.customer_po, t2.cust_work_order, t2.afe, t2.invoice_summary, t4.customer, (t5.first_name::text || ' '::text) || t5.last_name::text AS creator, t10.name AS service_type, t11.name AS inventory_source, t2.creator_company_id, t6.customer AS creator_company, t2.country_id, countries.country_name AS country, t2.currency_id, t12.name AS currency, t13.name AS province, t16.name AS county, cities.name AS city, zip_codes.zip_code, t2.subtotal AS work_order_subtotal, t2.sales_tax AS sales_tax_total, t2.total AS work_order_total, t2.discount_pct, t2.subtotal_after_discount, t2.structure_slave, t14.name AS status, t2.is_paid, t2.notes, t2.safe_work_permit_num, t2.quickbooks_num, (t15.first_name::text || ' '::text) || t15.last_name::text AS approved_by, t2.date_due, t2.terms, t2.date_sent_for_approval, CASE WHEN t3.part_num ~~ '%050-%'::text THEN '050'::text WHEN t3.part_num ~~ '%060-%'::text THEN '060'::text WHEN t3.part_num ~~ '%070-%'::text THEN '070'::text WHEN t3.part_num = '0'::text THEN '0'::text ELSE 'Other'::text END AS part_num_group, CASE WHEN lower(str.status) ~~ '%void%'::text THEN true ELSE false END AS is_void, CASE WHEN t2.currency_id = 1 THEN true ELSE false END AS is_usd_work_order FROM work_orders_parts t1 LEFT JOIN structures str ON str.id = t1.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN power_units pu ON pu.id = str.power_unit_id LEFT JOIN gw gw ON gw.power_unit_id = pu.id LEFT JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN parts t3 ON t3.id = t1.part_id LEFT JOIN public.customers t4 ON t4.id = t2.customer_id LEFT JOIN public.customers t6 ON t6.id = t2.creator_company_id LEFT JOIN public.users t5 ON t5.id = t2.creator_id LEFT JOIN public.users ft ON ft.id = t1.field_tech_id LEFT JOIN service_types t10 ON t10.id = t2.service_type_id LEFT JOIN inventory_sources t11 ON t11.id = t2.inventory_source_id LEFT JOIN currencies t12 ON t12.id = t2.currency_id LEFT JOIN public.countries countries ON countries.id = t2.country_id LEFT JOIN provinces t13 ON t13.id = t2.province_id LEFT JOIN cities cities ON cities.id = t2.city_id LEFT JOIN zip_codes ON zip_codes.id = t2.zip_code_id LEFT JOIN work_order_status t14 ON t2.status_id = t14.id LEFT JOIN public.users t15 ON t15.id = t2.approved_by_id LEFT JOIN counties t16 ON t16.id = t2.county_id WHERE t2.is_quote = false ORDER BY t2.id DESC, t1.id",
        "",
    )
    op.update_view(
        "vw_work_orders_by_unit",
        "SELECT DISTINCT ON (wo.id, pu.id, str.id) row_number() OVER () AS id, wo.id AS work_order_id, wo.date_service, cust.customer, wo.service_required, wo.work_done, wo.is_warranty, wo.is_warranty_reason, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, put.name AS power_unit_type, str.id AS structure_id, str.structure, str.downhole, str.surface, mt.model, ut.unit_type FROM work_orders wo LEFT JOIN work_order_power_unit_rel t2 ON t2.work_order_id = wo.id LEFT JOIN power_units pu ON pu.id = t2.power_unit_id LEFT JOIN work_order_structure_rel t4 ON t4.work_order_id = wo.id LEFT JOIN structures str ON str.id = t4.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN public.power_unit_types put ON put.id = pu.power_unit_type_id LEFT JOIN work_order_user_rel wour ON wour.work_order_id = wo.id LEFT JOIN public.users users ON users.id = wour.user_id LEFT JOIN public.customers cust ON wo.customer_id = cust.id",
        "",
    )
    op.update_view(
        "vw_sales_by_person_year",
        "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)",
        "",
    )
    op.update_view(
        "vw_sales_by_person_quarter",
        "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('quarter'::text, t2.date_service) AS service_quarter, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('quarter'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)",
        "",
    )
    op.update_view(
        "vw_sales_by_person_month",
        "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)",
        "",
    )
    op.update_view(
        "vw_gw_structure_rel",
        "SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id WHERE t3.id IS NOT NULL ORDER BY t1.id",
        "",
    )
    op.update_view(
        "vw_gw_customer_rel",
        "SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id WHERE t5.id IS NOT NULL ORDER BY t1.id",
        "",
    )
    op.update_view(
        "vw_gw_cust_sub_group_rel",
        "SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t6.id AS cust_sub_groups FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id left join public.structure_cust_sub_group_rel csr ON csr.structure_id = t3.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = csr.cust_sub_group_id WHERE t6.id IS NOT NULL ORDER BY t1.id",
        "",
    )
    op.update_view(
        "vw_users_roles",
        "SELECT id, id AS user_id, name, email, customer, job_title, roles, string_agg(unit, ', '::text) AS units FROM ( SELECT a2.customer, a2.id, a2.name, a2.email, a2.job_title, a2.roles, CASE WHEN t7.power_unit_str IS NULL AND t6.surface IS NULL THEN ''::text ELSE concat(t7.power_unit_str, ' (', t6.surface, ')') END AS unit FROM ( SELECT a1.customer, a1.id, a1.name, a1.email, a1.job_title, string_agg(a1.role_name::text, ', '::text) AS roles FROM ( SELECT t2.id, t3.customer, concat(t2.first_name, ' ', t2.last_name) AS name, t2.email, t2.job_title, t4.name AS role_name FROM public.users t2 LEFT JOIN public.user_role_rel t1 ON t1.user_id = t2.id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id LEFT JOIN public.roles t4 ON t4.id = t1.role_id ORDER BY t3.customer, t2.id, t1.role_id) a1 GROUP BY a1.customer, a1.name, a1.email, a1.job_title, a1.id ORDER BY a1.customer, a1.name) a2 LEFT JOIN public.user_structure_remote_control_rel t5 ON t5.user_id = a2.id LEFT JOIN structures t6 ON t6.id = t5.structure_id LEFT JOIN power_units t7 ON t7.id = t6.power_unit_id) a3 GROUP BY customer, id, name, email, job_title, roles ORDER BY customer, id, name, email, job_title, roles",
        "",
    )
    op.update_view(
        "gateways",
        "SELECT DISTINCT ON (gw.id, cust.customer) gw.id AS gateway_id, gw.gateway, gw.aws_thing, gw.mac, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, str.id AS structure_id, str.structure, str.structure_slave_id, str_sl.structure AS structure_slave, gw.ready_and_working, pu.apn, pu.alerts_edge, pu.change_detect_sens, tz.id AS time_zone_id, tz.time_zone, pu.wait_time_mins, pu.wait_time_mins_ol, pu.wait_time_mins_suction, pu.wait_time_mins_discharge, pu.wait_time_mins_spm, pu.wait_time_mins_stboxf, pu.wait_time_mins_hyd_temp, pu.hyd_oil_lvl_thresh, pu.hyd_filt_life_thresh, pu.hyd_oil_life_thresh, pu.wait_time_mins_hyd_oil_lvl, pu.wait_time_mins_hyd_filt_life, pu.wait_time_mins_hyd_oil_life, pu.wait_time_mins_chk_mtr_ovld, pu.wait_time_mins_pwr_fail, pu.wait_time_mins_soft_start_err, pu.wait_time_mins_grey_wire_err, pu.wait_time_mins_ae011, pu.heartbeat_enabled, pu.online_hb_enabled, pu.suction, pu.discharge, pu.spm, pu.stboxf, pu.hyd_temp, str.gps_lat, str.gps_lon, str.downhole, str.surface, str.location, str.well_license, cust.id AS customer_id, cust.customer, cust_sub.id AS cust_sub_group_id, cust_sub.name AS cust_sub_group, cust_sub.abbrev AS cust_sub_group_abbrev, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, mt.unit_type_id AS model_unit_type_id, ut.unit_type AS model_unit_type, str.model_type_id_slave, mt_sl.model AS model_slave, mt_sl.unit_type_id AS model_unit_type_id_slave, ut_sl.unit_type AS model_unit_type_slave, sim.sim_card, (string_to_array(cust.mqtt_topic::text, ' '::text))[1] AS mqtt_topic, str.status, CASE WHEN alerts.power_unit_id IS NULL THEN false ELSE true END AS alerts FROM gw gw LEFT JOIN power_units pu ON gw.power_unit_id = pu.id LEFT JOIN structures str ON pu.id = str.power_unit_id LEFT JOIN structures str_sl ON str.structure_slave_id = str_sl.id LEFT JOIN public.structure_customer_rel str_cust_rel ON str_cust_rel.structure_id = str.id LEFT JOIN public.customers cust ON str_cust_rel.customer_id = cust.id LEFT JOIN ( SELECT DISTINCT alerts_1.power_unit_id FROM alerts alerts_1) alerts ON pu.id = alerts.power_unit_id LEFT JOIN public.time_zones tz ON str.time_zone_id = tz.id LEFT JOIN sim_cards sim ON gw.id = sim.gateway_id LEFT JOIN public.model_types mt ON str.model_type_id = mt.id LEFT JOIN public.unit_types ut ON str.unit_type_id = ut.id LEFT JOIN public.model_types mt_sl ON str_sl.model_type_id_slave = mt_sl.id LEFT JOIN public.unit_types ut_sl ON str_sl.unit_type_id = ut_sl.id LEFT JOIN public.structure_cust_sub_group_rel csr ON csr.structure_id = str.id LEFT JOIN public.cust_sub_groups cust_sub ON cust_sub.id = csr.cust_sub_group_id ORDER BY gw.id, cust.customer",
        "",
    )
    op.update_view(
        "vw_structures_by_model",
        "SELECT row_number() OVER () AS id, t2.model, max(t1.structure_install_date) AS most_recent_install_date, sum( CASE WHEN t1.model_type_id IS NOT NULL THEN 1 ELSE 0 END) AS total_units FROM structures t1 LEFT JOIN public.model_types t2 ON t1.model_type_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id WHERE (t2.model <> ALL (ARRAY['SHOP'::text, 'TEST'::text])) AND t4.customer_id IS DISTINCT FROM 21 GROUP BY t2.model ORDER BY t2.model",
        "",
    )
    op.update_view(
        "vw_website_most_active_users",
        "SELECT row_number() OVER () AS id, t1.page, count(*) AS count_, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone, min(t1.timestamp_utc) AS earliest_date_in_sample, t2.customer_id FROM public.website_views t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.user_id IS NOT NULL AND t1.page::text !~~ '%media%'::text AND t1.page::text !~~ '%protected%'::text AND (t2.customer_id <> ALL (ARRAY[1, 21])) GROUP BY t2.first_name, t2.last_name, t2.email, t2.phone, t3.customer, t2.customer_id, t1.page ORDER BY t1.page, (count(*)) DESC, t3.customer, t2.first_name, t2.last_name",
        "",
    )
    op.update_view(
        "vw_profiler",
        "SELECT t1.id, (t2.first_name::text || ' '::text) || t2.last_name::text AS full_name, t3.customer, t1.status_code, t1.timestamp_utc_started, t1.timestamp_utc_ended, t1.elapsed, t1.cpu_start, t1.cpu_end, t1.endpoint_name, t1.referrer, t1.method, t1.args, t1.kwargs, t1.query_string, t1.form, t1.ip, t1.files, t1.path, t1.request_args, t1.scheme, t1.user_agent, t1.body, t1.headers FROM public.profiler_measurements t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.elapsed > 3::numeric ORDER BY t1.timestamp_utc_ended DESC",
        "",
    )
    op.update_view(
        "vw_gw_not_connected_dont_worry_latest",
        "SELECT t2.gateway_id, t2.days_since_reported, t2.timestamp_utc_last_reported, t1.timestamp_utc AS timestamp_utc_worried, t1.am_i_worried, t1.why_worried, t1.operators_contacted FROM gw_info t2 LEFT JOIN ( SELECT a1.id, a1.row_num, a1.gateway_id, a1.am_i_worried, a1.timestamp_utc, a1.why_worried, a1.operators_contacted FROM ( SELECT gw_not_connected_dont_worry.id, ( SELECT row_number() OVER (PARTITION BY gw_not_connected_dont_worry.gateway_id ORDER BY gw_not_connected_dont_worry.timestamp_utc DESC) AS row_number) AS row_num, gw_not_connected_dont_worry.gateway_id, gw_not_connected_dont_worry.am_i_worried, gw_not_connected_dont_worry.timestamp_utc, gw_not_connected_dont_worry.notes AS why_worried, gw_not_connected_dont_worry.operators_contacted FROM gw_not_connected_dont_worry) a1 WHERE a1.row_num < 2 ORDER BY a1.gateway_id) t1 ON t1.gateway_id = t2.gateway_id WHERE t2.timestamp_utc_last_reported < t1.timestamp_utc ORDER BY t2.gateway_id, t1.timestamp_utc DESC",
        "",
    )
    op.update_view(
        "vw_structures_joined",
        "SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure",
        "",
    )
    op.update_view(
        "vw_structures_joined_filtered",
        "SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure",
        "",
    )
    op.update_view(
        "vw_structures_all",
        "SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure",
        "",
    )
    op.update_view(
        "vw_structures_all_filtered",
        "SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure",
        "",
    )
    op.update_view(
        "vw_hours_billed_by_field_tech_by_work_order",
        "WITH work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id AS user_id, sum(t1_1.quantity) AS quantity_billed, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id WHERE (t1_1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2_1.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_month, t1_1.user_id, (t2_1.first_name::text || ' '::text) || t2_1.last_name::text AS name_, sum(EXTRACT(epoch FROM t1_1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1_1 JOIN public.users t2_1 ON t2_1.id = t1_1.user_id WHERE t1_1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), t1_1.user_id, t2_1.first_name, t2_1.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, t1_1.user_id, t2_1.first_name, t2_1.last_name ) SELECT t2.id AS work_order_id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, t2.is_warranty, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, t1.user_id, t2.date_service, cust.customer, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed, sum(t1.warranty_cad) AS sales_warranty, sum(t1.cost_before_tax_cad) AS sales_total, sum( CASE WHEN t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t1.part_id <> ALL (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers cust ON cust.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t2.id, t2.date_service, t2.is_warranty, cust.customer, t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, t2.is_warranty, ((t3.first_name::text || ' '::text) || t3.last_name::text), t2.date_service DESC",
        "",
    )
    op.update_view(
        "vw_hours_billed_by_field_tech_monthly_efficiency",
        "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id AS user_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, t1.user_id, t2.first_name, t2.last_name ) SELECT row_number() OVER (ORDER BY service_year, service_month, full_name, user_id) AS id, service_year, service_month, full_name, user_id, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS full_name, t1.user_id, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC, full_name",
        "",
    )
    op.update_view(
        "vw_hours_billed_monthly_efficiency",
        "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC ) SELECT row_number() OVER (ORDER BY service_year, service_month) AS id, service_year, service_month, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC",
        "",
    )
    op.update_view(
        "vw_service_clock_hours_daily",
        "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.day_, a1.name_) AS id, a1.year_, a1.month_, a1.day_, a1.user_id, a1.name_, a1.days_worked, a1.time_records, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS day_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked, string_agg(concat(to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_in)), 'MM-DD HH24:MI'::text), ' - ', to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)), 'MM-DD HH24:MI'::text)), ', '::text ORDER BY t1.timestamp_utc_out) AS time_records FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, date_part('day'::text, work_orders.date_service) AS day_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service)), (date_part('day'::text, work_orders.date_service)), (work_orders.date_service::timestamp without time zone)) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.day_ = a2.day_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.day_, a1.user_id",
        "",
    )
    op.update_view(
        "vw_service_clock_hours_monthly",
        "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.name_) AS id, a1.year_, a1.month_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.user_id",
        "",
    )
    op.update_view(
        "vw_service_clock_hours_yearly",
        "SELECT row_number() OVER (ORDER BY a1.year_, a1.name_) AS id, a1.year_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.user_id",
        "",
    )
    op.insert_data("alarm_log", "public", None)
    op.insert_data("alarm_log_metrics", "public", None)
    op.insert_data("alerts_sent", "public", None)
    op.insert_data("alerts_sent_maint_email_types", "public", None)
    op.insert_data("alerts_sent_other", "public", None)
    op.insert_data("alerts_types", "public", None)
    op.insert_data("barrels", "public", None)
    op.insert_data("bom_base_powerunit", "public", None)
    op.insert_data("bom_dgas", "public", None)
    op.insert_data("bom_powerunit", "public", None)
    op.insert_data("bom_pricing", "public", None)
    op.insert_data("bom_pump_top", "public", None)
    op.insert_data("bom_structure", "public", None)
    op.insert_data("calorie_types", "public", None)
    op.insert_data("check_valves", "public", None)
    op.insert_data("compression_patterns", "public", None)
    op.insert_data("countries", "public", None)
    op.insert_data("days", "public", None)
    op.insert_data("days_of_week", "public", None)
    op.insert_data("diagnostic", "public", None)
    op.insert_data("diagnostic_metrics", "public", None)
    op.insert_data("gateway_types", "public", None)
    op.insert_data("gw_cell_networks", "public", None)
    op.insert_data("hours", "public", None)
    op.insert_data("hyd_piston_types", "public", None)
    op.insert_data("images", "public", None)
    op.insert_data("inventory_sources", "public", None)
    op.insert_data("maintenance_types", "public", None)
    op.insert_data("map_abbrev_item", "public", None)
    op.insert_data("meta_data", "public", None)
    op.insert_data("months", "public", None)
    op.insert_data("packing_glands", "public", None)
    op.insert_data("part_filters", "public", None)
    op.insert_data("parts", "public", None)
    op.insert_data("release_notes", "public", None)
    op.insert_data("report_email_op_hours_types", "public", None)
    op.insert_data("rods", "public", None)
    op.insert_data("roles", "public", None)
    op.insert_data("service_types", "public", None)
    op.insert_data("shuttle_valves", "public", None)
    op.insert_data("surface_patterns", "public", None)
    op.insert_data("time_series", "public", None)
    op.insert_data("time_series_agg", "public", None)
    op.insert_data("time_series_diagnostic", "public", None)
    op.insert_data("time_series_rt", "public", None)
    op.insert_data("unit_types", "public", None)
    op.insert_data("work_order_status", "public", None)
    op.insert_data("application_types", "public", None)
    op.insert_data("bom_base_powerunit_part_rel", "public", None)
    op.insert_data("bom_dgas_part_rel", "public", None)
    op.insert_data("bom_powerunit_part_rel", "public", None)
    op.insert_data("bom_pricing_part_rel", "public", None)
    op.insert_data("bom_pump_top_part_rel", "public", None)
    op.insert_data("bom_structure_part_rel", "public", None)
    op.insert_data("compression_images", "public", None)
    op.insert_data("currencies", "public", None)
    op.insert_data("modbus_holding_registers", "public", None)
    op.insert_data("model_types", "public", None)
    op.insert_data("model_types_options", "public", None)
    op.insert_data("power_unit_types", "public", None)
    op.insert_data("power_unit_types_options", "public", None)
    op.insert_data("power_unit_types_power", "public", None)
    op.insert_data("power_unit_types_speeds", "public", None)
    op.insert_data("power_unit_types_voltage", "public", None)
    op.insert_data("provinces", "public", None)
    op.insert_data("surface_images", "public", None)
    op.insert_data("time_zones", "public", None)
    op.insert_data("bom_base_powerunit_power_unit_type_rel", "public", None)
    op.insert_data("bom_dgas_model_type_rel", "public", None)
    op.insert_data("bom_powerunit_power_unit_type_rel", "public", None)
    op.insert_data("bom_pricing_model_type_rel", "public", None)
    op.insert_data("bom_pump_top_model_type_rel", "public", None)
    op.insert_data("bom_structure_model_type_rel", "public", None)
    op.insert_data("calculators", "public", None)
    op.insert_data("counties", "public", None)
    op.insert_data("customers", "public", None)
    op.insert_data("model_types_parts_pm_seal_kits_rel", "public", None)
    op.insert_data("model_types_parts_rel", "public", None)
    op.insert_data("power_unit_types_filters_rel", "public", None)
    op.insert_data("power_unit_types_parts_rel", "public", None)
    op.insert_data("power_units", "public", None)
    op.insert_data("sales_taxes", "public", None)
    op.insert_data("users", "public", None)
    op.insert_data("warehouses", "public", None)
    op.insert_data("alerts", "public", None)
    op.insert_data("alerts_custom", "public", None)
    op.insert_data("alerts_sent_maint", "public", None)
    op.insert_data("alerts_sent_users", "public", None)
    op.insert_data("alerts_sent_wait_okay", "public", None)
    op.insert_data("applications", "public", None)
    op.insert_data("calories", "public", None)
    op.insert_data("career_applications", "public", None)
    op.insert_data("career_files", "public", None)
    op.insert_data("cities", "public", None)
    op.insert_data("contact_form", "public", None)
    op.insert_data("cust_sub_groups", "public", None)
    op.insert_data("error_logs", "public", None)
    op.insert_data("flask_dance_oauth", "public", None)
    op.insert_data("power_units_fixed_ip_networks", "public", None)
    op.insert_data("power_units_modbus_networks", "public", None)
    op.insert_data("profiler_measurements", "public", None)
    op.insert_data("remote_control", "public", None)
    op.insert_data("report_email_derates", "public", None)
    op.insert_data("report_email_hourly", "public", None)
    op.insert_data("report_email_inventory", "public", None)
    op.insert_data("report_email_op_hours", "public", None)
    op.insert_data("service_emailees", "public", None)
    op.insert_data("structures", "public", None)
    op.insert_data("user_api_tokens", "public", None)
    op.insert_data("user_authentication_challenges", "public", None)
    op.insert_data("user_chart_preferences", "public", None)
    op.insert_data("user_chart_toggles", "public", None)
    op.insert_data("user_customer_rel", "public", None)
    op.insert_data("user_registration_challenges", "public", None)
    op.insert_data("user_role_rel", "public", None)
    op.insert_data("user_verification_codes", "public", None)
    op.insert_data("user_webauthn_credentials", "public", None)
    op.insert_data("warehouses_parts_rel", "public", None)
    op.insert_data("website_views", "public", None)
    op.insert_data("alerts_custom_days_rel", "public", None)
    op.insert_data("alerts_custom_images_rel", "public", None)
    op.insert_data("alerts_custom_months_rel", "public", None)
    op.insert_data("alerts_custom_structure_rel", "public", None)
    op.insert_data("alerts_custom_user_rel", "public", None)
    op.insert_data("alerts_sent_maint_users", "public", None)
    op.insert_data("application_upload_files", "public", None)
    op.insert_data("career_applications_files_rel", "public", None)
    op.insert_data("gw", "public", None)
    op.insert_data("maintenance", "public", None)
    op.insert_data("report_email_derates_days_of_week_rel", "public", None)
    op.insert_data("report_email_derates_hours_rel", "public", None)
    op.insert_data("report_email_derates_model_types_rel", "public", None)
    op.insert_data("report_email_derates_unit_types_rel", "public", None)
    op.insert_data("report_email_derates_warehouses_rel", "public", None)
    op.insert_data("report_email_hourly_days_of_week_rel", "public", None)
    op.insert_data("report_email_hourly_hours_rel", "public", None)
    op.insert_data("report_email_hourly_model_types_rel", "public", None)
    op.insert_data("report_email_hourly_unit_types_rel", "public", None)
    op.insert_data("report_email_hourly_warehouses_rel", "public", None)
    op.insert_data("report_email_inventory_days_of_week_rel", "public", None)
    op.insert_data("report_email_inventory_hours_rel", "public", None)
    op.insert_data("report_email_inventory_warehouses_rel", "public", None)
    op.insert_data("report_email_op_hours_model_types_rel", "public", None)
    op.insert_data("report_email_op_hours_types_rel", "public", None)
    op.insert_data("report_email_op_hours_unit_types_rel", "public", None)
    op.insert_data("report_email_op_hours_warehouses_rel", "public", None)
    op.insert_data("service", "public", None)
    op.insert_data("service_clock", "public", None)
    op.insert_data("structure_cust_sub_group_rel", "public", None)
    op.insert_data("structure_customer_rel", "public", None)
    op.insert_data("user_cust_sub_group_notify_service_requests_rel", "public", None)
    op.insert_data("user_structure_maintenance_rel", "public", None)
    op.insert_data("user_structure_remote_control_rel", "public", None)
    op.insert_data("user_structure_sales_rel", "public", None)
    op.insert_data("zip_codes", "public", None)
    op.insert_data("gw_info", "public", None)
    op.insert_data("gw_not_connected_dont_worry", "public", None)
    op.insert_data("gw_tested_cellular", "public", None)
    op.insert_data("mqtt_messages", "public", None)
    op.insert_data("sim_cards", "public", None)
    op.insert_data("work_orders", "public", None)
    op.insert_data("zip_code_sales_tax", "public", None)
    op.insert_data("work_order_model_type_rel", "public", None)
    op.insert_data("work_order_power_unit_rel", "public", None)
    op.insert_data("work_order_service_rel", "public", None)
    op.insert_data("work_order_structure_rel", "public", None)
    op.insert_data("work_order_unit_type_rel", "public", None)
    op.insert_data("work_order_upload_files", "public", None)
    op.insert_data("work_order_user_rel", "public", None)
    op.insert_data("work_order_user_sales_rel", "public", None)
    op.insert_data("work_orders_parts", "public", None)
    # ### end Alembic commands ###


def downgrade_ijack():
    # ### commands auto generated by Alembic - please adjust! ###
    op.update_view(
        "vw_service_clock_hours_yearly",
        "",
        "SELECT row_number() OVER (ORDER BY a1.year_, a1.name_) AS id, a1.year_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.user_id",
    )
    op.update_view(
        "vw_service_clock_hours_monthly",
        "",
        "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.name_) AS id, a1.year_, a1.month_, a1.user_id, a1.name_, a1.days_worked, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service))) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.user_id",
    )
    op.update_view(
        "vw_service_clock_hours_daily",
        "",
        "SELECT row_number() OVER (ORDER BY a1.year_, a1.month_, a1.day_, a1.name_) AS id, a1.year_, a1.month_, a1.day_, a1.user_id, a1.name_, a1.days_worked, a1.time_records, a1.hours_worked, a2.total_hours, a2.service_hours, a2.travel_hours FROM ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS year_, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS month_, date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS day_, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, date_part('epoch'::text, sum(t1.total_hours_worked)) / 3600::double precision AS hours_worked, date_part('epoch'::text, sum(t1.total_hours_worked)) / 86400::double precision AS days_worked, string_agg(concat(to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_in)), 'MM-DD HH24:MI'::text), ' - ', to_char(timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)), 'MM-DD HH24:MI'::text)), ', '::text ORDER BY t1.timestamp_utc_out) AS time_records FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_trunc('year'::text, t1.timestamp_utc_out)), (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('day'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name) a1 LEFT JOIN ( SELECT work_orders.user_id, date_part('year'::text, work_orders.date_service) AS year_, date_part('month'::text, work_orders.date_service) AS month_, date_part('day'::text, work_orders.date_service) AS day_, sum(work_orders.travel_hours) AS travel_hours, sum(work_orders.service_hours) AS service_hours, sum(work_orders.total_hours) AS total_hours FROM ( SELECT t1.date_service, CASE WHEN t2.user_id IS NULL THEN t1.creator_id ELSE t2.user_id END AS user_id, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS travel_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS service_hours, CASE WHEN t3.n_users IS NULL THEN 1::bigint ELSE t3.n_users END::numeric AS total_hours FROM work_orders t1 LEFT JOIN work_order_user_rel t2 ON t1.id = t2.work_order_id LEFT JOIN ( SELECT work_order_user_rel.work_order_id, count(DISTINCT work_order_user_rel.user_id) AS n_users FROM work_order_user_rel GROUP BY work_order_user_rel.work_order_id) t3 ON t1.id = t3.work_order_id) work_orders GROUP BY work_orders.user_id, (date_part('year'::text, work_orders.date_service)), (date_part('month'::text, work_orders.date_service)), (date_part('day'::text, work_orders.date_service)), (work_orders.date_service::timestamp without time zone)) a2 ON a1.year_ = a2.year_ AND a1.month_ = a2.month_ AND a1.day_ = a2.day_ AND a1.user_id = a2.user_id ORDER BY a1.year_, a1.month_, a1.day_, a1.user_id",
    )
    op.update_view(
        "vw_hours_billed_monthly_efficiency",
        "",
        "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC ) SELECT row_number() OVER (ORDER BY service_year, service_month) AS id, service_year, service_month, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC",
    )
    op.update_view(
        "vw_hours_billed_by_field_tech_monthly_efficiency",
        "",
        "WITH work_orders_parts_cad AS ( SELECT t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id AS user_id, sum(t1.quantity) AS quantity_billed, sum( CASE WHEN t2.is_warranty IS TRUE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2.is_warranty IS FALSE THEN 0::numeric ELSE t1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN currencies fx ON fx.id = t2.currency_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1.work_order_id, t1.part_id, t2.date_service, t2.service_type_id, t1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out))) AS service_month, t1.user_id, (t2.first_name::text || ' '::text) || t2.last_name::text AS name_, sum(EXTRACT(epoch FROM t1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1 JOIN public.users t2 ON t2.id = t1.user_id WHERE t1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))), t1.user_id, t2.first_name, t2.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1.timestamp_utc_out)))) DESC, t1.user_id, t2.first_name, t2.last_name ) SELECT row_number() OVER (ORDER BY service_year, service_month, full_name, user_id) AS id, service_year, service_month, full_name, user_id, CASE WHEN monthly_hours_worked_clock = 0::numeric THEN 0::numeric ELSE quantity_hours_billed / monthly_hours_worked_clock END AS billed_per_hour_worked, quantity_hours_billed, monthly_hours_worked_clock FROM ( SELECT date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS full_name, t1.user_id, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers t7 ON t7.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND t2.customer_id <> 1 GROUP BY t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service))) a1 ORDER BY service_year DESC, service_month DESC, full_name",
    )
    op.update_view(
        "vw_hours_billed_by_field_tech_by_work_order",
        "",
        "WITH work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id AS user_id, sum(t1_1.quantity) AS quantity_billed, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id WHERE (t1_1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2_1.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, t2_1.is_warranty, t1_1.field_tech_id ), service_clock_hours_by_year_month_user_id AS ( SELECT date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_year, date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out))) AS service_month, t1_1.user_id, (t2_1.first_name::text || ' '::text) || t2_1.last_name::text AS name_, sum(EXTRACT(epoch FROM t1_1.total_hours_worked) / 3600::numeric) AS hours_worked_clock FROM service_clock t1_1 JOIN public.users t2_1 ON t2_1.id = t1_1.user_id WHERE t1_1.total_hours_worked IS NOT NULL GROUP BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))), t1_1.user_id, t2_1.first_name, t2_1.last_name ORDER BY (date_part('year'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, (date_part('month'::text, timezone('CST'::text, timezone('UTC'::text, t1_1.timestamp_utc_out)))) DESC, t1_1.user_id, t2_1.first_name, t2_1.last_name ) SELECT t2.id AS work_order_id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, t2.is_warranty, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, t1.user_id, t2.date_service, cust.customer, avg(sc.hours_worked_clock) AS monthly_hours_worked_clock, sum(t1.quantity_billed) AS quantity_hours_billed, sum(t1.warranty_cad) AS sales_warranty, sum(t1.cost_before_tax_cad) AS sales_total, sum( CASE WHEN t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t1.part_id <> ALL (ARRAY[5865, 874, 881, 52644, 880, 879, 878]) THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id JOIN public.customers cust ON cust.id = t2.customer_id JOIN public.users t3 ON t3.id = t1.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id LEFT JOIN service_clock_hours_by_year_month_user_id sc ON sc.service_year = date_part('year'::text, t2.date_service) AND sc.service_month = date_part('month'::text, t2.date_service) AND sc.user_id = t1.user_id WHERE (t1.part_id = ANY (ARRAY[5865, 874, 881, 52644, 880, 879, 878])) AND (t2.customer_id <> ALL (ARRAY[1, 3])) GROUP BY t2.id, t2.date_service, t2.is_warranty, cust.customer, t1.user_id, ((t3.first_name::text || ' '::text) || t3.last_name::text), (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, t2.is_warranty, ((t3.first_name::text || ' '::text) || t3.last_name::text), t2.date_service DESC",
    )
    op.update_view(
        "vw_structures_all_filtered",
        "",
        "SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure",
    )
    op.update_view(
        "vw_structures_all",
        "",
        "SELECT t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure",
    )
    op.update_view(
        "vw_structures_joined_filtered",
        "",
        "SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id WHERE t4.customer_id IS DISTINCT FROM 21 AND t1.structure_install_date IS NOT NULL AND t10.model IS NOT NULL AND (t1.model_type_id <> ALL (ARRAY[63, 41, 23, 37, 38])) AND t1.surface IS NOT NULL AND t4.customer_id IS NOT NULL AND (t4.customer_id <> ALL (ARRAY[1, 2, 3])) AND (t1.unit_type_id = 5 OR t1.unit_type_id <> 5 AND t1.time_zone_id IS NOT NULL) ORDER BY t1.structure",
    )
    op.update_view(
        "vw_structures_joined",
        "",
        "SELECT DISTINCT ON (t1.structure, t4.customer_id) t1.id, t1.id AS structure_id, t1.structure, t1.structure_str, t1.structure_slave_id, t01.structure AS structure_slave, t1.power_unit_id, t2.power_unit, t2.power_unit_str, t2.power_unit_type_id, t2.website_card_msg, t13.name AS power_unit_type, t3.id AS gateway_id, t3.gateway, t3.aws_thing, t3.gateway_type_id, t1.has_rcom, t1.qb_sale, t1.unit_type_id, t9.unit_type, t1.model_type_id, t10.model, t10.max_delta_p, t10.unit_type_id AS model_unit_type_id, t100.unit_type AS model_unit_type, t1.model_type_id_slave, t11.model AS model_slave, t11.unit_type_id AS model_unit_type_id_slave, t111.unit_type AS model_unit_type_slave, t4.customer_id, t5.customer, CASE WHEN t61.cust_sub_group_id IS NULL THEN 0 ELSE t61.cust_sub_group_id END AS cust_sub_group_id, t6.name AS cust_sub_group, t1.run_mfg_date, t1.structure_install_date, t1.slave_install_date, t1.downhole, t1.surface, t1.location, t1.gps_lat, t1.gps_lon, t1.notes_1, t1.well_license, t1.time_zone_id, t12.time_zone, t2.apn, t14.am_i_worried, t14.days_since_reported, t14.operators_contacted, t1.op_months_interval, t15.hours AS op_hours, t15.suction_range, t15.has_slave, t15.swv_plc, t15.swv_canpy, t1.warehouse_id, t16.name AS warehouse_name FROM structures t1 LEFT JOIN structures t01 ON t01.id = t1.structure_slave_id LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN gw t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id LEFT JOIN public.structure_cust_sub_group_rel t61 ON t61.structure_id = t1.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = t61.cust_sub_group_id LEFT JOIN public.user_structure_remote_control_rel t7 ON t7.structure_id = t1.id LEFT JOIN public.users t8 ON t8.id = t7.user_id LEFT JOIN public.unit_types t9 ON t1.unit_type_id = t9.id LEFT JOIN public.model_types t10 ON t1.model_type_id = t10.id LEFT JOIN public.unit_types t100 ON t100.id = t10.unit_type_id LEFT JOIN public.model_types t11 ON t1.model_type_id_slave = t11.id LEFT JOIN public.unit_types t111 ON t111.id = t11.unit_type_id LEFT JOIN public.time_zones t12 ON t1.time_zone_id = t12.id LEFT JOIN public.power_unit_types t13 ON t13.id = t2.power_unit_type_id LEFT JOIN vw_gw_not_connected_dont_worry_latest t14 ON t14.gateway_id = t3.id LEFT JOIN gw_info t15 ON t15.gateway_id = t3.id LEFT JOIN warehouses t16 ON t16.id = t1.warehouse_id ORDER BY t1.structure",
    )
    op.update_view(
        "vw_gw_not_connected_dont_worry_latest",
        "",
        "SELECT t2.gateway_id, t2.days_since_reported, t2.timestamp_utc_last_reported, t1.timestamp_utc AS timestamp_utc_worried, t1.am_i_worried, t1.why_worried, t1.operators_contacted FROM gw_info t2 LEFT JOIN ( SELECT a1.id, a1.row_num, a1.gateway_id, a1.am_i_worried, a1.timestamp_utc, a1.why_worried, a1.operators_contacted FROM ( SELECT gw_not_connected_dont_worry.id, ( SELECT row_number() OVER (PARTITION BY gw_not_connected_dont_worry.gateway_id ORDER BY gw_not_connected_dont_worry.timestamp_utc DESC) AS row_number) AS row_num, gw_not_connected_dont_worry.gateway_id, gw_not_connected_dont_worry.am_i_worried, gw_not_connected_dont_worry.timestamp_utc, gw_not_connected_dont_worry.notes AS why_worried, gw_not_connected_dont_worry.operators_contacted FROM gw_not_connected_dont_worry) a1 WHERE a1.row_num < 2 ORDER BY a1.gateway_id) t1 ON t1.gateway_id = t2.gateway_id WHERE t2.timestamp_utc_last_reported < t1.timestamp_utc ORDER BY t2.gateway_id, t1.timestamp_utc DESC",
    )
    op.update_view(
        "vw_profiler",
        "",
        "SELECT t1.id, (t2.first_name::text || ' '::text) || t2.last_name::text AS full_name, t3.customer, t1.status_code, t1.timestamp_utc_started, t1.timestamp_utc_ended, t1.elapsed, t1.cpu_start, t1.cpu_end, t1.endpoint_name, t1.referrer, t1.method, t1.args, t1.kwargs, t1.query_string, t1.form, t1.ip, t1.files, t1.path, t1.request_args, t1.scheme, t1.user_agent, t1.body, t1.headers FROM public.profiler_measurements t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.elapsed > 3::numeric ORDER BY t1.timestamp_utc_ended DESC",
    )
    op.update_view(
        "vw_website_most_active_users",
        "",
        "SELECT row_number() OVER () AS id, t1.page, count(*) AS count_, t3.customer, t2.first_name, t2.last_name, t2.email, t2.phone, min(t1.timestamp_utc) AS earliest_date_in_sample, t2.customer_id FROM public.website_views t1 LEFT JOIN public.users t2 ON t2.id = t1.user_id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id WHERE t1.user_id IS NOT NULL AND t1.page::text !~~ '%media%'::text AND t1.page::text !~~ '%protected%'::text AND (t2.customer_id <> ALL (ARRAY[1, 21])) GROUP BY t2.first_name, t2.last_name, t2.email, t2.phone, t3.customer, t2.customer_id, t1.page ORDER BY t1.page, (count(*)) DESC, t3.customer, t2.first_name, t2.last_name",
    )
    op.update_view(
        "vw_structures_by_model",
        "",
        "SELECT row_number() OVER () AS id, t2.model, max(t1.structure_install_date) AS most_recent_install_date, sum( CASE WHEN t1.model_type_id IS NOT NULL THEN 1 ELSE 0 END) AS total_units FROM structures t1 LEFT JOIN public.model_types t2 ON t1.model_type_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t1.id WHERE (t2.model <> ALL (ARRAY['SHOP'::text, 'TEST'::text])) AND t4.customer_id IS DISTINCT FROM 21 GROUP BY t2.model ORDER BY t2.model",
    )
    op.update_view(
        "gateways",
        "",
        "SELECT DISTINCT ON (gw.id, cust.customer) gw.id AS gateway_id, gw.gateway, gw.aws_thing, gw.mac, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, str.id AS structure_id, str.structure, str.structure_slave_id, str_sl.structure AS structure_slave, gw.ready_and_working, pu.apn, pu.alerts_edge, pu.change_detect_sens, tz.id AS time_zone_id, tz.time_zone, pu.wait_time_mins, pu.wait_time_mins_ol, pu.wait_time_mins_suction, pu.wait_time_mins_discharge, pu.wait_time_mins_spm, pu.wait_time_mins_stboxf, pu.wait_time_mins_hyd_temp, pu.hyd_oil_lvl_thresh, pu.hyd_filt_life_thresh, pu.hyd_oil_life_thresh, pu.wait_time_mins_hyd_oil_lvl, pu.wait_time_mins_hyd_filt_life, pu.wait_time_mins_hyd_oil_life, pu.wait_time_mins_chk_mtr_ovld, pu.wait_time_mins_pwr_fail, pu.wait_time_mins_soft_start_err, pu.wait_time_mins_grey_wire_err, pu.wait_time_mins_ae011, pu.heartbeat_enabled, pu.online_hb_enabled, pu.suction, pu.discharge, pu.spm, pu.stboxf, pu.hyd_temp, str.gps_lat, str.gps_lon, str.downhole, str.surface, str.location, str.well_license, cust.id AS customer_id, cust.customer, cust_sub.id AS cust_sub_group_id, cust_sub.name AS cust_sub_group, cust_sub.abbrev AS cust_sub_group_abbrev, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, mt.unit_type_id AS model_unit_type_id, ut.unit_type AS model_unit_type, str.model_type_id_slave, mt_sl.model AS model_slave, mt_sl.unit_type_id AS model_unit_type_id_slave, ut_sl.unit_type AS model_unit_type_slave, sim.sim_card, (string_to_array(cust.mqtt_topic::text, ' '::text))[1] AS mqtt_topic, str.status, CASE WHEN alerts.power_unit_id IS NULL THEN false ELSE true END AS alerts FROM gw gw LEFT JOIN power_units pu ON gw.power_unit_id = pu.id LEFT JOIN structures str ON pu.id = str.power_unit_id LEFT JOIN structures str_sl ON str.structure_slave_id = str_sl.id LEFT JOIN public.structure_customer_rel str_cust_rel ON str_cust_rel.structure_id = str.id LEFT JOIN public.customers cust ON str_cust_rel.customer_id = cust.id LEFT JOIN ( SELECT DISTINCT alerts_1.power_unit_id FROM alerts alerts_1) alerts ON pu.id = alerts.power_unit_id LEFT JOIN public.time_zones tz ON str.time_zone_id = tz.id LEFT JOIN sim_cards sim ON gw.id = sim.gateway_id LEFT JOIN public.model_types mt ON str.model_type_id = mt.id LEFT JOIN public.unit_types ut ON str.unit_type_id = ut.id LEFT JOIN public.model_types mt_sl ON str_sl.model_type_id_slave = mt_sl.id LEFT JOIN public.unit_types ut_sl ON str_sl.unit_type_id = ut_sl.id LEFT JOIN public.structure_cust_sub_group_rel csr ON csr.structure_id = str.id LEFT JOIN public.cust_sub_groups cust_sub ON cust_sub.id = csr.cust_sub_group_id ORDER BY gw.id, cust.customer",
    )
    op.update_view(
        "vw_users_roles",
        "",
        "SELECT id, id AS user_id, name, email, customer, job_title, roles, string_agg(unit, ', '::text) AS units FROM ( SELECT a2.customer, a2.id, a2.name, a2.email, a2.job_title, a2.roles, CASE WHEN t7.power_unit_str IS NULL AND t6.surface IS NULL THEN ''::text ELSE concat(t7.power_unit_str, ' (', t6.surface, ')') END AS unit FROM ( SELECT a1.customer, a1.id, a1.name, a1.email, a1.job_title, string_agg(a1.role_name::text, ', '::text) AS roles FROM ( SELECT t2.id, t3.customer, concat(t2.first_name, ' ', t2.last_name) AS name, t2.email, t2.job_title, t4.name AS role_name FROM public.users t2 LEFT JOIN public.user_role_rel t1 ON t1.user_id = t2.id LEFT JOIN public.customers t3 ON t3.id = t2.customer_id LEFT JOIN public.roles t4 ON t4.id = t1.role_id ORDER BY t3.customer, t2.id, t1.role_id) a1 GROUP BY a1.customer, a1.name, a1.email, a1.job_title, a1.id ORDER BY a1.customer, a1.name) a2 LEFT JOIN public.user_structure_remote_control_rel t5 ON t5.user_id = a2.id LEFT JOIN structures t6 ON t6.id = t5.structure_id LEFT JOIN power_units t7 ON t7.id = t6.power_unit_id) a3 GROUP BY customer, id, name, email, job_title, roles ORDER BY customer, id, name, email, job_title, roles",
    )
    op.update_view(
        "vw_gw_cust_sub_group_rel",
        "",
        "SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t6.id AS cust_sub_groups FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id left join public.structure_cust_sub_group_rel csr ON csr.structure_id = t3.id LEFT JOIN public.cust_sub_groups t6 ON t6.id = csr.cust_sub_group_id WHERE t6.id IS NOT NULL ORDER BY t1.id",
    )
    op.update_view(
        "vw_gw_customer_rel",
        "",
        "SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures, t5.id AS customers FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id LEFT JOIN public.structure_customer_rel t4 ON t4.structure_id = t3.id LEFT JOIN public.customers t5 ON t5.id = t4.customer_id WHERE t5.id IS NOT NULL ORDER BY t1.id",
    )
    op.update_view(
        "vw_gw_structure_rel",
        "",
        "SELECT DISTINCT t1.id AS gw, t2.id AS power_units, t3.id AS structures FROM gw t1 LEFT JOIN power_units t2 ON t2.id = t1.power_unit_id LEFT JOIN structures t3 ON t3.power_unit_id = t2.id WHERE t3.id IS NOT NULL ORDER BY t1.id",
    )
    op.update_view(
        "vw_sales_by_person_month",
        "",
        "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('month'::text, t2.date_service) AS service_month, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('month'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('month'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)",
    )
    op.update_view(
        "vw_sales_by_person_quarter",
        "",
        "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, date_part('quarter'::text, t2.date_service) AS service_quarter, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), (date_part('quarter'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (date_part('quarter'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)",
    )
    op.update_view(
        "vw_sales_by_person_year",
        "",
        "WITH user_count_per_work_order AS ( SELECT t.work_order_id, CASE WHEN t.user_count = 0 THEN 1::bigint ELSE t.user_count END AS user_count FROM ( SELECT t2_1.id AS work_order_id, count(t31_1.user_id) AS user_count FROM work_orders t2_1 LEFT JOIN work_order_user_sales_rel t31_1 ON t31_1.work_order_id = t2_1.id GROUP BY t2_1.id) t ), work_orders_parts_cad AS ( SELECT t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id, sum( CASE WHEN t2_1.is_warranty IS TRUE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS cost_before_tax_cad, sum( CASE WHEN t2_1.is_warranty IS FALSE THEN 0::numeric ELSE t1_1.cost_before_tax * fx.fx_rate_cad_per END) AS warranty_cad FROM work_orders_parts t1_1 JOIN work_orders t2_1 ON t2_1.id = t1_1.work_order_id JOIN currencies fx ON fx.id = t2_1.currency_id GROUP BY t1_1.work_order_id, t1_1.part_id, t2_1.date_service, t2_1.service_type_id ) SELECT row_number() OVER (ORDER BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text)) AS id, date_part('year'::text, t2.date_service) AS service_year, (t3.first_name::text || ' '::text) || t3.last_name::text AS name_, sum(t1.warranty_cad / uc.user_count::numeric) AS sales_warranty, sum(t1.cost_before_tax_cad / uc.user_count::numeric) AS sales_total, sum( CASE WHEN t4.part_num = ANY (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_labour, sum( CASE WHEN t4.part_num <> ALL (ARRAY['050-0500'::text, '050-0501'::text, '050-0510'::text, '050-0520'::text, '050-0530'::text, '050-0570'::text]) THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS sales_parts, sum( CASE WHEN t5.id = 1 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_new_installs, sum( CASE WHEN t5.id = 4 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_sales, sum( CASE WHEN t5.id = 2 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_repairs, sum( CASE WHEN t5.id = 3 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_parts, sum( CASE WHEN t5.id = 5 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_prevent_maint, sum( CASE WHEN t5.id = 7 THEN t1.cost_before_tax_cad / uc.user_count::numeric ELSE 0::numeric END) AS type_rentals FROM work_orders_parts_cad t1 JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN work_order_user_sales_rel t31 ON t31.work_order_id = t2.id LEFT JOIN public.users t3 ON t3.id = t31.user_id JOIN parts t4 ON t4.id = t1.part_id JOIN service_types t5 ON t5.id = t2.service_type_id JOIN user_count_per_work_order uc ON uc.work_order_id = t2.id WHERE t2.customer_id <> ALL (ARRAY[1, 3]) GROUP BY (date_part('year'::text, t2.date_service)), ((t3.first_name::text || ' '::text) || t3.last_name::text) ORDER BY (date_part('year'::text, t2.date_service)) DESC, (sum(t1.cost_before_tax_cad)) DESC, ((t3.first_name::text || ' '::text) || t3.last_name::text)",
    )
    op.update_view(
        "vw_work_orders_by_unit",
        "",
        "SELECT DISTINCT ON (wo.id, pu.id, str.id) row_number() OVER () AS id, wo.id AS work_order_id, wo.date_service, cust.customer, wo.service_required, wo.work_done, wo.is_warranty, wo.is_warranty_reason, pu.id AS power_unit_id, pu.power_unit, pu.power_unit_str, put.name AS power_unit_type, str.id AS structure_id, str.structure, str.downhole, str.surface, mt.model, ut.unit_type FROM work_orders wo LEFT JOIN work_order_power_unit_rel t2 ON t2.work_order_id = wo.id LEFT JOIN power_units pu ON pu.id = t2.power_unit_id LEFT JOIN work_order_structure_rel t4 ON t4.work_order_id = wo.id LEFT JOIN structures str ON str.id = t4.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN public.power_unit_types put ON put.id = pu.power_unit_type_id LEFT JOIN work_order_user_rel wour ON wour.work_order_id = wo.id LEFT JOIN public.users users ON users.id = wour.user_id LEFT JOIN public.customers cust ON wo.customer_id = cust.id",
    )
    op.update_view(
        "vw_work_order_parts_joined",
        "",
        "SELECT t1.id AS work_order_part_id, date_part('year'::text, t2.date_service) AS year, date_part('month'::text, t2.date_service) AS month, t2.date_service, t1.work_order_id, str.structure_str AS structure, str.location, str.model_type_id, mt.model, mt.unit_type_id, ut.unit_type, pu.power_unit_str AS power_unit, gw.aws_thing AS gateway, t2.timestamp_utc_inserted, t1.part_id, t3.part_num, t3.worksheet AS bom_worksheet, t3.ws_row AS worksheet_row, t3.is_usd AS is_part_usd, t3.cad_per_usd, t3.is_soft_part, t3.msrp_cad, t3.msrp_usd, t3.dealer_cost_cad, t3.dealer_cost_usd, t3.ijack_corp_cost, t3.msrp_mult_cad, t3.msrp_mult_usd, t3.transfer_mult_cad_dealer, t3.transfer_mult_usd_dealer, t3.transfer_mult_inc_to_corp, CASE WHEN t3.cost_cad IS NULL THEN t1.price::double precision / t3.msrp_mult_cad ELSE t3.cost_cad::double precision END AS cost_cad, CASE WHEN t3.cost_usd IS NULL THEN t1.price::double precision / t3.msrp_mult_usd ELSE t3.cost_usd::double precision END AS cost_usd, t1.description, t1.price, t1.quantity, t1.cost_before_tax, t1.sales_tax_rate, t1.sales_tax_part_amount AS sales_tax, t1.field_tech_id, (ft.first_name::text || ' '::text) || ft.last_name::text AS credited_to, t2.requested_by_id, t2.approval_person_id, t2.service_crew, t2.invoice_approval_req, t2.location AS wo_location, t2.has_rcom, t2.service_required, t2.service_resolution, t2.is_warranty, t2.is_warranty_reason, t2.picker_truck, t2.crew_truck, t2.man_lift, t2.trailer, t2.work_done, t2.customer_po, t2.cust_work_order, t2.afe, t2.invoice_summary, t4.customer, (t5.first_name::text || ' '::text) || t5.last_name::text AS creator, t10.name AS service_type, t11.name AS inventory_source, t2.creator_company_id, t6.customer AS creator_company, t2.country_id, countries.country_name AS country, t2.currency_id, t12.name AS currency, t13.name AS province, t16.name AS county, cities.name AS city, zip_codes.zip_code, t2.subtotal AS work_order_subtotal, t2.sales_tax AS sales_tax_total, t2.total AS work_order_total, t2.discount_pct, t2.subtotal_after_discount, t2.structure_slave, t14.name AS status, t2.is_paid, t2.notes, t2.safe_work_permit_num, t2.quickbooks_num, (t15.first_name::text || ' '::text) || t15.last_name::text AS approved_by, t2.date_due, t2.terms, t2.date_sent_for_approval, CASE WHEN t3.part_num ~~ '%050-%'::text THEN '050'::text WHEN t3.part_num ~~ '%060-%'::text THEN '060'::text WHEN t3.part_num ~~ '%070-%'::text THEN '070'::text WHEN t3.part_num = '0'::text THEN '0'::text ELSE 'Other'::text END AS part_num_group, CASE WHEN lower(str.status) ~~ '%void%'::text THEN true ELSE false END AS is_void, CASE WHEN t2.currency_id = 1 THEN true ELSE false END AS is_usd_work_order FROM work_orders_parts t1 LEFT JOIN structures str ON str.id = t1.structure_id LEFT JOIN public.model_types mt ON mt.id = str.model_type_id LEFT JOIN public.unit_types ut ON ut.id = mt.unit_type_id LEFT JOIN power_units pu ON pu.id = str.power_unit_id LEFT JOIN gw gw ON gw.power_unit_id = pu.id LEFT JOIN work_orders t2 ON t2.id = t1.work_order_id LEFT JOIN parts t3 ON t3.id = t1.part_id LEFT JOIN public.customers t4 ON t4.id = t2.customer_id LEFT JOIN public.customers t6 ON t6.id = t2.creator_company_id LEFT JOIN public.users t5 ON t5.id = t2.creator_id LEFT JOIN public.users ft ON ft.id = t1.field_tech_id LEFT JOIN service_types t10 ON t10.id = t2.service_type_id LEFT JOIN inventory_sources t11 ON t11.id = t2.inventory_source_id LEFT JOIN currencies t12 ON t12.id = t2.currency_id LEFT JOIN public.countries countries ON countries.id = t2.country_id LEFT JOIN provinces t13 ON t13.id = t2.province_id LEFT JOIN cities cities ON cities.id = t2.city_id LEFT JOIN zip_codes ON zip_codes.id = t2.zip_code_id LEFT JOIN work_order_status t14 ON t2.status_id = t14.id LEFT JOIN public.users t15 ON t15.id = t2.approved_by_id LEFT JOIN counties t16 ON t16.id = t2.county_id WHERE t2.is_quote = false ORDER BY t2.id DESC, t1.id",
    )
    op.drop_table("work_orders_parts", schema="public")
    op.drop_table("work_order_user_sales_rel", schema="public")
    op.drop_table("work_order_user_rel", schema="public")
    op.drop_table("work_order_upload_files", schema="public")
    op.drop_table("work_order_unit_type_rel", schema="public")
    op.drop_table("work_order_structure_rel", schema="public")
    op.drop_table("work_order_service_rel", schema="public")
    op.drop_table("work_order_power_unit_rel", schema="public")
    op.drop_table("work_order_model_type_rel", schema="public")
    op.drop_table("zip_code_sales_tax", schema="public")
    op.drop_table("work_orders", schema="public")
    op.drop_table("sim_cards", schema="public")
    op.drop_table("mqtt_messages", schema="public")
    op.drop_table("gw_tested_cellular", schema="public")
    op.drop_table("gw_not_connected_dont_worry", schema="public")
    op.drop_table("gw_info", schema="public")
    op.drop_table("zip_codes", schema="public")
    op.drop_table("user_structure_sales_rel", schema="public")
    op.drop_table("user_structure_remote_control_rel", schema="public")
    op.drop_table("user_structure_maintenance_rel", schema="public")
    op.drop_table("user_cust_sub_group_notify_service_requests_rel", schema="public")
    op.drop_table("structure_customer_rel", schema="public")
    op.drop_table("structure_cust_sub_group_rel", schema="public")
    op.drop_table("service_clock", schema="public")
    op.drop_table("service", schema="public")
    op.drop_table("report_email_op_hours_warehouses_rel", schema="public")
    op.drop_table("report_email_op_hours_unit_types_rel", schema="public")
    op.drop_table("report_email_op_hours_types_rel", schema="public")
    op.drop_table("report_email_op_hours_model_types_rel", schema="public")
    op.drop_table("report_email_inventory_warehouses_rel", schema="public")
    op.drop_table("report_email_inventory_hours_rel", schema="public")
    op.drop_table("report_email_inventory_days_of_week_rel", schema="public")
    op.drop_table("report_email_hourly_warehouses_rel", schema="public")
    op.drop_table("report_email_hourly_unit_types_rel", schema="public")
    op.drop_table("report_email_hourly_model_types_rel", schema="public")
    op.drop_table("report_email_hourly_hours_rel", schema="public")
    op.drop_table("report_email_hourly_days_of_week_rel", schema="public")
    op.drop_table("report_email_derates_warehouses_rel", schema="public")
    op.drop_table("report_email_derates_unit_types_rel", schema="public")
    op.drop_table("report_email_derates_model_types_rel", schema="public")
    op.drop_table("report_email_derates_hours_rel", schema="public")
    op.drop_table("report_email_derates_days_of_week_rel", schema="public")
    op.drop_table("maintenance", schema="public")
    op.drop_table("gw", schema="public")
    op.drop_table("career_applications_files_rel", schema="public")
    op.drop_table("application_upload_files", schema="public")
    op.drop_table("alerts_sent_maint_users", schema="public")
    op.drop_table("alerts_custom_user_rel", schema="public")
    op.drop_table("alerts_custom_structure_rel", schema="public")
    op.drop_table("alerts_custom_months_rel", schema="public")
    op.drop_table("alerts_custom_images_rel", schema="public")
    op.drop_table("alerts_custom_days_rel", schema="public")
    op.drop_table("website_views", schema="public")
    op.drop_table("warehouses_parts_rel", schema="public")
    op.drop_table("user_webauthn_credentials", schema="public")
    op.drop_table("user_verification_codes", schema="public")
    op.drop_table("user_role_rel", schema="public")
    op.drop_table("user_registration_challenges", schema="public")
    op.drop_table("user_customer_rel", schema="public")
    op.drop_table("user_chart_toggles", schema="public")
    op.drop_table("user_chart_preferences", schema="public")
    op.drop_table("user_authentication_challenges", schema="public")
    op.drop_table("user_api_tokens", schema="public")
    op.drop_table("structures", schema="public")
    op.drop_table("service_emailees", schema="public")
    op.drop_table("report_email_op_hours", schema="public")
    op.drop_table("report_email_inventory", schema="public")
    op.drop_table("report_email_hourly", schema="public")
    op.drop_table("report_email_derates", schema="public")
    op.drop_table("remote_control", schema="public")
    op.drop_table("profiler_measurements", schema="public")
    op.drop_table("power_units_modbus_networks", schema="public")
    op.drop_table("power_units_fixed_ip_networks", schema="public")
    op.drop_table("flask_dance_oauth", schema="public")
    op.drop_table("error_logs", schema="public")
    op.drop_table("cust_sub_groups", schema="public")
    op.drop_table("contact_form", schema="public")
    with op.batch_alter_table("cities", schema=None) as batch_op:
        batch_op.drop_index("idx_city_province_county")

    op.drop_table("cities", schema="public")
    op.drop_table("career_files", schema="public")
    op.drop_table("career_applications", schema="public")
    op.drop_table("calories", schema="public")
    op.drop_table("applications", schema="public")
    op.drop_table("alerts_sent_wait_okay", schema="public")
    op.drop_table("alerts_sent_users", schema="public")
    op.drop_table("alerts_sent_maint", schema="public")
    op.drop_table("alerts_custom", schema="public")
    op.drop_table("alerts", schema="public")
    op.drop_table("warehouses", schema="public")
    op.drop_table("users", schema="public")
    op.drop_table("sales_taxes", schema="public")
    op.drop_table("power_units", schema="public")
    op.drop_table("power_unit_types_parts_rel", schema="public")
    op.drop_table("power_unit_types_filters_rel", schema="public")
    op.drop_table("model_types_parts_rel", schema="public")
    op.drop_table("model_types_parts_pm_seal_kits_rel", schema="public")
    op.drop_table("customers", schema="public")
    op.drop_table("counties", schema="public")
    op.drop_table("calculators", schema="public")
    op.drop_table("bom_structure_model_type_rel", schema="public")
    op.drop_table("bom_pump_top_model_type_rel", schema="public")
    op.drop_table("bom_pricing_model_type_rel", schema="public")
    op.drop_table("bom_powerunit_power_unit_type_rel", schema="public")
    op.drop_table("bom_dgas_model_type_rel", schema="public")
    op.drop_table("bom_base_powerunit_power_unit_type_rel", schema="public")
    op.drop_table("time_zones", schema="public")
    op.drop_table("surface_images", schema="public")
    op.drop_table("provinces", schema="public")
    op.drop_table("power_unit_types_voltage", schema="public")
    op.drop_table("power_unit_types_speeds", schema="public")
    op.drop_table("power_unit_types_power", schema="public")
    op.drop_table("power_unit_types_options", schema="public")
    op.drop_table("power_unit_types", schema="public")
    op.drop_table("model_types_options", schema="public")
    op.drop_table("model_types", schema="public")
    op.drop_table("modbus_holding_registers", schema="public")
    op.drop_table("currencies", schema="public")
    op.drop_table("compression_images", schema="public")
    op.drop_table("bom_structure_part_rel", schema="public")
    op.drop_table("bom_pump_top_part_rel", schema="public")
    op.drop_table("bom_pricing_part_rel", schema="public")
    op.drop_table("bom_powerunit_part_rel", schema="public")
    op.drop_table("bom_dgas_part_rel", schema="public")
    op.drop_table("bom_base_powerunit_part_rel", schema="public")
    op.drop_table("application_types", schema="public")
    op.drop_table("work_order_status", schema="public")
    op.drop_table("unit_types", schema="public")
    op.drop_table("time_series_rt", schema="public")
    op.drop_table("time_series_diagnostic", schema="public")
    op.drop_table("time_series_agg", schema="public")
    op.drop_table("time_series", schema="public")
    op.drop_table("surface_patterns", schema="public")
    op.drop_table("shuttle_valves", schema="public")
    op.drop_table("service_types", schema="public")
    op.drop_table("roles", schema="public")
    op.drop_table("rods", schema="public")
    op.drop_table("report_email_op_hours_types", schema="public")
    op.drop_table("release_notes", schema="public")
    op.drop_table("parts", schema="public")
    op.drop_table("part_filters", schema="public")
    op.drop_table("packing_glands", schema="public")
    op.drop_table("months", schema="public")
    op.drop_table("meta_data", schema="public")
    op.drop_table("map_abbrev_item", schema="public")
    op.drop_table("maintenance_types", schema="public")
    op.drop_table("inventory_sources", schema="public")
    op.drop_table("images", schema="public")
    op.drop_table("hyd_piston_types", schema="public")
    op.drop_table("hours", schema="public")
    op.drop_table("gw_cell_networks", schema="public")
    op.drop_table("gateway_types", schema="public")
    op.drop_table("diagnostic_metrics", schema="public")
    op.drop_table("diagnostic", schema="public")
    op.drop_table("days_of_week", schema="public")
    op.drop_table("days", schema="public")
    op.drop_table("countries", schema="public")
    op.drop_table("compression_patterns", schema="public")
    op.drop_table("check_valves", schema="public")
    op.drop_table("calorie_types", schema="public")
    op.drop_table("bom_structure", schema="public")
    op.drop_table("bom_pump_top", schema="public")
    op.drop_table("bom_pricing", schema="public")
    op.drop_table("bom_powerunit", schema="public")
    op.drop_table("bom_dgas", schema="public")
    op.drop_table("bom_base_powerunit", schema="public")
    op.drop_table("barrels", schema="public")
    op.drop_table("alerts_types", schema="public")
    op.drop_table("alerts_sent_other", schema="public")
    op.drop_table("alerts_sent_maint_email_types", schema="public")
    op.drop_table("alerts_sent", schema="public")
    op.drop_table("alarm_log_metrics", schema="public")
    op.drop_table("alarm_log", schema="public")
    # ### end Alembic commands ###


def upgrade_timescale():
    pass


def downgrade_timescale():
    pass
