{
  "python.defaultInterpreterPath": ".venv/bin/python",
  "python.autoComplete.extraPaths": [
    "./app",
    ".venv/lib/python3.12/site-packages",
  ],
  "python.terminal.activateEnvironment": true,
  // Point to the flask_app pyproject.toml
  "ruff.configuration": "./pyproject.toml",
  // Configure pytest to use the flask_app tests directory
  "python.testing.pytestEnabled": true,
  "python.testing.unittestEnabled": false,
  "python.testing.pytestArgs": [
    "-n=auto",
    "--dist=worksteal",
    "--tb=short"
  ],
  "[javascript,typescript,typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2,
    "editor.formatOnSave": true,
  },
  "editor.formatOnSave": true,
  "editor.wordWrap": "on",
  "ruff.organizeImports": true,
  "ruff.trace.server": "messages",
  "ruff.nativeServer": true,
  // Remove global ruff.configuration and use folder-specific settings
  // "ruff.configuration": "${workspaceFolder}/pyproject.toml",
  // Use folder-specific settings for different file types
  "[python]": {
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.tabSize": 4,
    "editor.formatOnSave": true,
    "editor.formatOnPaste": false,
    "editor.codeActionsOnSave": {
      "source.fixAll": "explicit",
      "source.organizeImports": "explicit"
    }
  },
  "search.exclude": {
    "**/dist": true,
    "**/node_modules": true,
    "**/.pnpm-store": true,
    "**/bower_components": true,
    "**/*.code-search": true
  }
}