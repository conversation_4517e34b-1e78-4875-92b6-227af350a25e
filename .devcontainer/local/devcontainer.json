// For format details, see https://aka.ms/vscode-remote/devcontainer.json or the definition README at
// https://github.com/microsoft/vscode-dev-containers/tree/master/containers/python-3-miniconda
{
  "name": "Local RCOM Dev Container",
  "containerUser": "root",
  "dockerComposeFile": [
    "../../docker-compose.github.base.yml",
    "../../docker-compose.dev.yml",
    "../../docker-compose.local.yml"
  ],
  "service": "rcom",
  "runServices": [
    "rcom",
    "postgres",
    "redis",
    "pgbouncer-rds",
    "pgbouncer-ts",
    "timescale-test",
    "pgbouncer-test",
    "traefik"
  ],
  "features": {
    "ghcr.io/devcontainers/features/nix:1": {},
    "ghcr.io/schlich/devcontainer-features/starship:0": {}
  },
  "postAttachCommand": "corepack enable && echo y | pnpm || true",
  "postStartCommand": ".devcontainer/aws/post-start.sh",
  "mounts": [
    // Forward SSH credentials
    "type=bind,source=${localEnv:HOME}${localEnv:USERPROFILE}/.ssh,target=/root/.ssh,readonly",
    "source=${localEnv:HOME}${localEnv:USERPROFILE}/.gitconfig,target=/root/.gitconfig,type=bind",
    // .venv mounts
    "source=flask_venv,target=/project/flask_app/.venv,type=volume",
    "source=fast_venv,target=/project/fast_api/.venv,type=volume",
    "source=packages_venv,target=/project/packages/.venv,type=volume",
    // node_modules mounts
    "source=flask_node_modules,target=/project/flask_app/node_modules,type=volume",
    "source=react_node_modules,target=/project/flask_app/app/inertia/react/node_modules,type=volume"
  ],
  "workspaceFolder": "/project",
  "customizations": {
    "vscode": {
      "settings": {
        "remote.extensionKind": {
          "ms-azuretools.vscode-docker": "workspace"
        },
        "git.enableSmartCommit": true,
        "git.terminalAuthentication": false, // Prevents prompts
        "git.autofetch": true,
        "terminal.integrated.shell.linux": "/bin/bash",
        "autoOpenWorkspace.enableAutoOpenAlwaysFirst": true // Auto open the devcontainer in the rcom.code-workspace
      },
      "extensions": [
        "ms-python.python",
        "GitHub.copilot",
        "ms-azuretools.vscode-docker",
        "dbaeumer.vscode-eslint",
        "ms-toolsai.jupyter",
        "ms-python.vscode-pylance",
        "charliermarsh.ruff",
        "oderwat.indent-rainbow",
        "tamasfe.even-better-toml",
        "esbenp.prettier-vscode",
        "ms-toolsai.datawrangler",
        "adpyke.vscode-sql-formatter",
        "redhat.vscode-yaml",
        "eamodio.gitlens",
        "donjayamanne.githistory",
        "foxundermoon.shell-format",
        "monosans.djlint",
        "johnpapa.pwa-tools",
        "samuelcolvin.jinjahtml",
        "ms-python.debugpy",
        "bradlc.vscode-tailwindcss",
        "YoavBls.pretty-ts-errors",
        "usernamehw.errorlens",
        "zoma.vscode-auto-open-workspace"
      ]
    }
  }
}